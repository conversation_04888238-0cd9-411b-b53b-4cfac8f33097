module.exports = {
  prefix: "tw-",
  important: true,
  corePlugins: {
    preflight: false,
  },
  content: [
    "./resources/views/frontend/**/*.{php,html,js}",
    "./resources/assets/js/**/pages/*.vue",
    "./resources/assets/js/**/components/*.vue",
  ],
  plugins: [],
  theme: {
    colors: {
      primary: 'rgb(var(--color-primary) / <alpha-value>)',
      secondary: 'rgb(var(--color-secondary) / <alpha-value>)',
    },
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      'xl0.5': '1100px',
      xl: '1280px',
      '2xl': '1536px',
      desktop: '1050px',
    },
    fontSize: {
      badge: '9px',
      '0.25xs': '10px',
      xs: '12px',
      '0.5sm': '13px',
      sm: '14px',
      '2sm': '15px',
      base: '16px',
      lg: '18px',
      '0.5xl': '19px',
      xl: '20px',
      '1.5xl': '22px',
      '2xl': '24px',
      '2.5xl': '26px',
      '2.75xl': '28px',
      '3xl': '30px',
      '3.5xl': '32px',
      '4xl': '36px',
      '4.5xl': '40px',
      '5xl': '48px',
      '5.4xl': '53px',
      '6xl': '60px',
      '7xl': '72px',
    },
    spacing: {
      unset: 'unset',
      px: '1px',
      0: '0',
      0.125: '0.5px',
      0.25: '1.5px',
      0.5: '2px',
      0.625: '2.5px',
      0.75: '3px',
      1: '4px',
      1.25: '5px',
      1.5: '6px',
      1.75: '7px',
      2: '8px',
      2.25: '9px',
      2.5: '10px',
      2.75: '11px',
      3: '12px',
      3.25: '13px',
      3.5: '14px',
      3.75: '15px',
      4: '16px',
      4.25: '17px',
      4.5: '18px',
      5: '20px',
      5.5: '22px',
      5.75: '23px',
      6: '24px',
      6.25: '25px',
      6.5: '26px',
      7: '28px',
      7.5: '30px',
      8: '32px',
      8.5: '34px',
      8.75: '35px',
      9: '36px',
      9.5: '38px',
      10: '40px',
      11: '44px',
      11.25: '45px',
      11.5: '46px',
      12: '48px',
      12.5: '50px',
      13: '52px',
      13.5: '54px',
      14: '56px',
      14.5: '58px',
      14.75: '59px',
      15: '60px',
      15.5: '62px',
      16: '64px',
      17: '68px',
      17.5: '70px',
      18: '72px',
      18.5: '74px',
      19: '76px',
      20: '80px',
      21: '84px',
      22: '88px',
      22.5: '90px',
      23: '92px',
      23.75: '95px',
      24: '96px',
      25: '100px',
      26: '104px',
      26.5: '104px',
      27.5: '110px',
      28: '112px',
      28.75: '115px',
      29: '116px',
      30: '120px',
      30.75: '123px',
      31: '124px',
      31.25: '125px',
      32: '128px',
      33: '132px',
      34: '136px',
      35: '140px',
      36: '144px',
      36.5: '146px',
      37: '148px',
      40: '160px',
      44: '176px',
      45: '180px',
      46: '184px',
      48: '192px',
      50: '200px',
      52: '208px',
      55: '220px',
      56: '224px',
      57: '228px',
      59: '236px',
      60: '240px',
      64: '256px',
      66: '264px',
      67.5: '270px',
      68.25: '273px',
      68.5: '274px',
      70: '280px',
      71.5: '286px',
      72: '288px',
      74: '296px',
      75: '300px',
      77.5: '310px',
      79.25: '317px',
      80: '320px',
      85: '340px',
      91.25: '365px',
      96: '384px',
      100: '400px',
      112.5: '450px',
      125: '500px',
      150: '600px',
    },
    lineHeight: {
      none: 1,
      1: '4px',
      1.5: '6px',
      1.75: '7px',
      2: '8px',
      2.25: '9px',
      2.5: '10px',
      3: '12px',
      3.5: '14px',
      3.75: '15px',
      4: '16px',
      4.25: '17px',
      4.5: '18px',
      4.75: '19px',
      5: '20px',
      5.5: '22px',
      6: '24px',
      6.5: '26px',
      7: '28px',
      7.5: '30px',
      8: '32px',
      9: '36px',
      10: '40px',
      12: '48px',
      13: '52px',
    },
    extend: {
      minWidth: {
        unset: 'unset',
        3.5: '14px',
        4: '16px',
        5: '20px',
        6: '24px',
        7: '28px',
        8: '32px',
        18: '72px',
        20: '80px',
        25: '100px',
        28.75: '115px',
        30: '120px',
        32.5: '130px',
        34: '136px',
        35: '140px',
        37.5: '150px',
        40: '160px',
        44: '176px',
        45: '180px',
        50: '200px',
        55: '220px',
        56: '224px',
        64: '256px',
        67.5: '270px',
        75: '300px',
        100: '400px',
        125: '500px',
        175: '700px',
        240: '960px',
      },
      stroke: {
        unset: 'unset',
      },
      fill: {
        unset: 'unset',
      },
      strokeWidth: {
        0.1: '0.1',
        0.5: '0.5',
        1.2: '1.2',
        1.5: '1.5',
      },
      borderWidth: {
        0.5: '0.5px',
        1.5: '1.5px',
        5: '5px',
      },
      maxWidth: {
        12.5: '50px',
        15: '60px',
        18: '72px',
        37.5: '150px',
        40: '160px',
        50: '200px',
        64: '256px',
        75: '300px',
        78: '312px',
        105: '420px',
        125: '500px',
        175: '700px',
        945: '945px',
        800: '800px',
        840: '840px',
        1050: '1050px',
      },
      minHeight: {
        10: '40px',
        5: '5px',
        8: '32px',
        12: '48px',
        14: '56px',
        18.5: '74px',
        23.5: '94px',
        37.5: '150px',
        48: '192px',
        150: '600px',
        187.5: '750px',
      },
      maxHeight: {
        30: '120px',
        37.5: '150px',
        74: '296px',
        114: '456px',
        150: '600px',
        70: '280px',
      },
    },
  },
}
