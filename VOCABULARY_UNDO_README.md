# Vocabulary Flashcard Undo Functionality

## Tổng quan

Chức năng undo cho vocabulary flashcard đã được triển khai để cho phép người dùng hoàn tác các thao tác vuốt thẻ. V<PERSON> hệ thống vocabulary re-render lại danh sách flashcard sau mỗi lần vuốt, chúng ta không thể sử dụng `stackedCardsInstance.undo()` mà phải tạo một hệ thống undo riêng.

## Cách hoạt động

### 1. L<PERSON>u trữ trạng thái (State Management)

- **undoHistory**: <PERSON><PERSON>ng lưu trữ các trạng thái trước khi thực hiện swipe
- **maxUndoSteps**: Giới hạn số lần undo (mặc định: 5)
- Mỗi state bao gồm:
  - `dataFlashCard`: Danh sách thẻ hiện tại
  - `swipeCardList`: <PERSON><PERSON> sách thẻ đã vuốt
  - `arrCardLearned`: <PERSON>h sách thẻ đã học
  - `countSwipeCard`: <PERSON>ố lượng thẻ đã vuốt
  - `currentCard`: Thẻ hiện tại
  - `timestamp`: Thời gian lưu state

### 2. Lưu trạng thái trước khi swipe

```javascript
saveUndoState() {
  const state = {
    dataFlashCard: JSON.parse(JSON.stringify(this.dataFlashCard)),
    swipeCardList: JSON.parse(JSON.stringify(this.swipeCardList)),
    arrCardLearned: JSON.parse(JSON.stringify(this.arrCardLearned)),
    countSwipeCard: this.countSwipeCard,
    currentCard: this.currentCard ? JSON.parse(JSON.stringify(this.currentCard)) : null,
    timestamp: Date.now()
  };

  this.undoHistory.push(state);

  // Giới hạn số lượng undo steps
  if (this.undoHistory.length > this.maxUndoSteps) {
    this.undoHistory.shift();
  }
}
```

### 3. Thực hiện undo

```javascript
undo() {
  if (this.undoHistory.length === 0) return;

  // Lấy trạng thái cuối cùng từ history
  const lastState = this.undoHistory.pop();
  
  // Khôi phục trạng thái
  this.dataFlashCard = lastState.dataFlashCard;
  this.swipeCardList = lastState.swipeCardList;
  this.arrCardLearned = lastState.arrCardLearned;
  this.countSwipeCard = lastState.countSwipeCard;
  this.currentCard = lastState.currentCard;

  // Khởi tạo lại stacked cards
  this.$nextTick(() => {
    if (this.isJapanese) {
      let cards = document.querySelectorAll('.card-inner');
      cards.forEach(card => {
        card.classList.remove('flip');
      });
    }
    this.initStackedCards();
  });
}
```

## Các trường hợp được xử lý

### 1. User chưa mua khóa học
- Lưu state trước khi swipe
- Chỉ cho phép undo trong giới hạn thẻ miễn phí

### 2. Thẻ đặc biệt (id = 0)
- Xử lý thẻ quảng cáo/break
- Không lưu state cho thẻ này

### 3. Thẻ take_break
- Thẻ nghỉ giải lao xuất hiện sau mỗi 5 thẻ
- Không lưu state cho thẻ này

### 4. Thẻ kiểm tra (is_test)
- Lưu state trước khi xử lý thẻ kiểm tra
- Cho phép undo thẻ kiểm tra

## UI/UX

### Nút Undo
- **Enabled**: Khi có undo history và thỏa mãn điều kiện khác
- **Disabled**: Khi không có undo history hoặc vi phạm điều kiện
- **Styling**: Màu trắng khi enabled, màu xám khi disabled

### Điều kiện disable nút undo:
```javascript
:disabled="!canUndo() || (currentCard && (currentCard.is_test || (swipeCardList.length > 0 && swipeCardList[swipeCardList.length - 1].is_test))) || (!categoryData.hasCourseOwner && countSwipeCard === limitCardFree)"
```

## Reset và Cleanup

### Khi bắt đầu học mới:
```javascript
async startStudy() {
  this.undoHistory = []; // Reset undo history
  // ... other logic
}
```

### Khi reset progress:
```javascript
async confirmResetProgress() {
  this.undoHistory = []; // Reset undo history
  // ... other reset logic
}
```

## Testing

Đã tạo file test `test_vocabulary_undo.html` để kiểm tra:

1. **Undo History Creation**: Kiểm tra việc tạo history sau swipe
2. **Undo Functionality**: Kiểm tra khôi phục trạng thái
3. **Undo Limit**: Kiểm tra giới hạn số lần undo
4. **canUndo Method**: Kiểm tra method kiểm tra có thể undo
5. **Multiple Swipes and Undo**: Kiểm tra nhiều swipe và undo

## Lưu ý quan trọng

1. **Deep Clone**: Sử dụng `JSON.parse(JSON.stringify())` để tạo deep copy của objects
2. **Memory Management**: Giới hạn số lượng undo steps để tránh memory leak
3. **State Consistency**: Đảm bảo tất cả state liên quan được khôi phục đồng bộ
4. **UI Sync**: Sử dụng `$nextTick()` để đảm bảo DOM được cập nhật trước khi init stacked cards
5. **Japanese Cards**: Reset flip state cho thẻ tiếng Nhật khi undo

## Cách sử dụng

1. Người dùng vuốt thẻ (trái/phải)
2. Hệ thống tự động lưu state trước khi swipe
3. Người dùng click nút "Undo" để hoàn tác
4. Hệ thống khôi phục trạng thái trước đó và re-render

## Troubleshooting

### Nếu undo không hoạt động:
1. Kiểm tra `undoHistory.length > 0`
2. Kiểm tra điều kiện disable của nút undo
3. Kiểm tra console logs để debug state restoration

### Nếu UI không cập nhật đúng:
1. Đảm bảo `$nextTick()` được sử dụng
2. Kiểm tra `initStackedCards()` được gọi
3. Kiểm tra flip state của thẻ tiếng Nhật
