<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Vocabulary Undo Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .state-display {
            background-color: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Vocabulary Undo Functionality</h1>
    
    <div class="test-section">
        <h2>Undo System Test</h2>
        <p>This test simulates the vocabulary flashcard undo functionality.</p>
        
        <div id="test-results"></div>
        
        <button class="btn-primary" onclick="runTests()">Run All Tests</button>
        <button class="btn-secondary" onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h2>Manual Test Simulation</h2>
        <p>Simulate the vocabulary flashcard behavior:</p>
        
        <div class="state-display" id="current-state">
Initial State:
dataFlashCard: [card1, card2, card3]
swipeCardList: []
undoHistory: []
        </div>
        
        <button class="btn-primary" onclick="simulateSwipe('left')">Swipe Left</button>
        <button class="btn-primary" onclick="simulateSwipe('right')">Swipe Right</button>
        <button class="btn-success" onclick="simulateUndo()">Undo</button>
        <button class="btn-secondary" onclick="resetSimulation()">Reset</button>
    </div>

    <script>
        // Simulate the vocabulary flashcard undo system
        class VocabularyUndoSimulator {
            constructor() {
                this.reset();
            }

            reset() {
                this.dataFlashCard = [
                    { id: 1, word: 'card1', box: 0 },
                    { id: 2, word: 'card2', box: 0 },
                    { id: 3, word: 'card3', box: 0 }
                ];
                this.swipeCardList = [];
                this.undoHistory = [];
                this.maxUndoSteps = 5;
                this.updateDisplay();
            }

            saveUndoState() {
                const state = {
                    dataFlashCard: JSON.parse(JSON.stringify(this.dataFlashCard)),
                    swipeCardList: JSON.parse(JSON.stringify(this.swipeCardList)),
                    timestamp: Date.now()
                };

                this.undoHistory.push(state);

                if (this.undoHistory.length > this.maxUndoSteps) {
                    this.undoHistory.shift();
                }
            }

            swipeCard(type) {
                if (this.dataFlashCard.length === 0) return false;

                // Save state before swipe
                this.saveUndoState();

                let currentCard = this.dataFlashCard[0];
                currentCard.type_swipe = type;

                if (type === 'left') {
                    currentCard.box = currentCard.box || 0; // Keep same box
                } else {
                    currentCard.box = (currentCard.box || 0) + 1; // Increase box
                }

                // Remove from dataFlashCard and add to swipeCardList
                this.swipeCardList.push(currentCard);
                this.dataFlashCard.shift();

                this.updateDisplay();
                return true;
            }

            undo() {
                if (this.undoHistory.length === 0) return false;

                const lastState = this.undoHistory.pop();
                this.dataFlashCard = lastState.dataFlashCard;
                this.swipeCardList = lastState.swipeCardList;

                this.updateDisplay();
                return true;
            }

            canUndo() {
                return this.undoHistory.length > 0;
            }

            updateDisplay() {
                const stateElement = document.getElementById('current-state');
                stateElement.textContent = `Current State:
dataFlashCard: [${this.dataFlashCard.map(c => c.word).join(', ')}]
swipeCardList: [${this.swipeCardList.map(c => `${c.word}(${c.type_swipe})`).join(', ')}]
undoHistory: ${this.undoHistory.length} states
canUndo: ${this.canUndo()}`;
            }
        }

        const simulator = new VocabularyUndoSimulator();

        function simulateSwipe(direction) {
            const success = simulator.swipeCard(direction);
            if (!success) {
                alert('No more cards to swipe!');
            }
        }

        function simulateUndo() {
            const success = simulator.undo();
            if (!success) {
                alert('No undo history available!');
            }
        }

        function resetSimulation() {
            simulator.reset();
        }

        function runTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '';

            const tests = [
                testUndoHistoryCreation,
                testUndoFunctionality,
                testUndoLimit,
                testCanUndoMethod,
                testMultipleSwipesAndUndo
            ];

            tests.forEach(test => {
                try {
                    const result = test();
                    displayTestResult(result.name, result.passed, result.message);
                } catch (error) {
                    displayTestResult(test.name, false, `Error: ${error.message}`);
                }
            });
        }

        function testUndoHistoryCreation() {
            const testSim = new VocabularyUndoSimulator();
            testSim.swipeCard('left');
            
            return {
                name: 'Undo History Creation',
                passed: testSim.undoHistory.length === 1,
                message: testSim.undoHistory.length === 1 ? 
                    'Undo history correctly created after swipe' : 
                    `Expected 1 history entry, got ${testSim.undoHistory.length}`
            };
        }

        function testUndoFunctionality() {
            const testSim = new VocabularyUndoSimulator();
            const initialCards = testSim.dataFlashCard.length;
            
            testSim.swipeCard('right');
            const afterSwipe = testSim.dataFlashCard.length;
            
            testSim.undo();
            const afterUndo = testSim.dataFlashCard.length;
            
            return {
                name: 'Undo Functionality',
                passed: initialCards === afterUndo && afterSwipe === initialCards - 1,
                message: initialCards === afterUndo ? 
                    'Undo correctly restored previous state' : 
                    `Initial: ${initialCards}, After undo: ${afterUndo}`
            };
        }

        function testUndoLimit() {
            const testSim = new VocabularyUndoSimulator();
            
            // Perform more swipes than the limit
            for (let i = 0; i < 7; i++) {
                if (testSim.dataFlashCard.length > 0) {
                    testSim.swipeCard('left');
                }
            }
            
            return {
                name: 'Undo Limit',
                passed: testSim.undoHistory.length <= testSim.maxUndoSteps,
                message: testSim.undoHistory.length <= testSim.maxUndoSteps ? 
                    `Undo history correctly limited to ${testSim.maxUndoSteps}` : 
                    `History exceeded limit: ${testSim.undoHistory.length}`
            };
        }

        function testCanUndoMethod() {
            const testSim = new VocabularyUndoSimulator();
            const initialCanUndo = testSim.canUndo();
            
            testSim.swipeCard('left');
            const afterSwipeCanUndo = testSim.canUndo();
            
            testSim.undo();
            const afterUndoCanUndo = testSim.canUndo();
            
            return {
                name: 'canUndo Method',
                passed: !initialCanUndo && afterSwipeCanUndo && !afterUndoCanUndo,
                message: (!initialCanUndo && afterSwipeCanUndo && !afterUndoCanUndo) ? 
                    'canUndo method works correctly' : 
                    `Initial: ${initialCanUndo}, After swipe: ${afterSwipeCanUndo}, After undo: ${afterUndoCanUndo}`
            };
        }

        function testMultipleSwipesAndUndo() {
            const testSim = new VocabularyUndoSimulator();
            
            testSim.swipeCard('left');
            testSim.swipeCard('right');
            
            const beforeUndo = {
                dataCards: testSim.dataFlashCard.length,
                swipedCards: testSim.swipeCardList.length
            };
            
            testSim.undo(); // Should undo the right swipe
            
            const afterFirstUndo = {
                dataCards: testSim.dataFlashCard.length,
                swipedCards: testSim.swipeCardList.length
            };
            
            return {
                name: 'Multiple Swipes and Undo',
                passed: afterFirstUndo.dataCards === beforeUndo.dataCards + 1 && 
                        afterFirstUndo.swipedCards === beforeUndo.swipedCards - 1,
                message: (afterFirstUndo.dataCards === beforeUndo.dataCards + 1) ? 
                    'Multiple swipes and undo work correctly' : 
                    `Before undo: ${beforeUndo.dataCards}/${beforeUndo.swipedCards}, After: ${afterFirstUndo.dataCards}/${afterFirstUndo.swipedCards}`
            };
        }

        function displayTestResult(name, passed, message) {
            const results = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `<strong>${name}:</strong> ${passed ? 'PASS' : 'FAIL'} - ${message}`;
            results.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Initialize display
        simulator.updateDisplay();
    </script>
</body>
</html>
