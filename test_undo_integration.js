/**
 * Integration test script for vocabulary flashcard undo functionality
 * This script can be run in browser console to test the undo feature
 */

// Test helper functions
function logTest(testName, result, details = '') {
    const status = result ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName}${details ? ': ' + details : ''}`);
}

function waitForNextTick() {
    return new Promise(resolve => {
        if (typeof Vue !== 'undefined' && Vue.nextTick) {
            Vue.nextTick(resolve);
        } else {
            setTimeout(resolve, 0);
        }
    });
}

// Main test function
async function testVocabularyUndo() {
    console.log('🧪 Starting Vocabulary Undo Integration Tests...\n');
    
    // Find the FlashCard component instance
    const flashCardElement = document.querySelector('[data-component="FlashCard"]') || 
                            document.querySelector('.vocabulary-flashcard') ||
                            document.querySelector('#stacked-cards-block')?.closest('[data-v-]');
    
    if (!flashCardElement) {
        console.error('❌ FlashCard component not found. Make sure you are on the vocabulary flashcard page.');
        return;
    }
    
    // Get Vue component instance
    let flashCardComponent = null;
    
    // Try different methods to get Vue instance
    if (flashCardElement.__vue__) {
        flashCardComponent = flashCardElement.__vue__;
    } else if (flashCardElement._vnode && flashCardElement._vnode.componentInstance) {
        flashCardComponent = flashCardElement._vnode.componentInstance;
    } else {
        // Try to find parent component
        let parent = flashCardElement.parentElement;
        while (parent && !flashCardComponent) {
            if (parent.__vue__) {
                flashCardComponent = parent.__vue__;
                break;
            }
            parent = parent.parentElement;
        }
    }
    
    if (!flashCardComponent) {
        console.error('❌ Could not access FlashCard Vue component instance.');
        return;
    }
    
    console.log('✅ Found FlashCard component instance');
    
    // Test 1: Check if undo methods exist
    const hasUndoMethods = typeof flashCardComponent.saveUndoState === 'function' &&
                          typeof flashCardComponent.undo === 'function' &&
                          typeof flashCardComponent.canUndo === 'function';
    
    logTest('Undo methods exist', hasUndoMethods);
    
    if (!hasUndoMethods) {
        console.error('❌ Undo methods not found. Make sure the undo functionality is implemented.');
        return;
    }
    
    // Test 2: Check initial state
    const initialUndoHistory = flashCardComponent.undoHistory ? flashCardComponent.undoHistory.length : 0;
    const initialCanUndo = flashCardComponent.canUndo();
    
    logTest('Initial undo state', initialUndoHistory === 0 && !initialCanUndo, 
           `History: ${initialUndoHistory}, canUndo: ${initialCanUndo}`);
    
    // Test 3: Check if data structures exist
    const hasDataStructures = Array.isArray(flashCardComponent.dataFlashCard) &&
                              Array.isArray(flashCardComponent.swipeCardList) &&
                              Array.isArray(flashCardComponent.undoHistory);
    
    logTest('Data structures exist', hasDataStructures);
    
    // Test 4: Test saveUndoState method
    const initialDataLength = flashCardComponent.dataFlashCard.length;
    const initialSwipeLength = flashCardComponent.swipeCardList.length;
    
    try {
        flashCardComponent.saveUndoState();
        const afterSaveHistory = flashCardComponent.undoHistory.length;
        const afterSaveCanUndo = flashCardComponent.canUndo();
        
        logTest('saveUndoState method', afterSaveHistory === 1 && afterSaveCanUndo,
               `History after save: ${afterSaveHistory}, canUndo: ${afterSaveCanUndo}`);
    } catch (error) {
        logTest('saveUndoState method', false, `Error: ${error.message}`);
    }
    
    // Test 5: Test undo method
    try {
        const beforeUndoHistory = flashCardComponent.undoHistory.length;
        flashCardComponent.undo();
        
        await waitForNextTick();
        
        const afterUndoHistory = flashCardComponent.undoHistory.length;
        const afterUndoCanUndo = flashCardComponent.canUndo();
        
        logTest('undo method', afterUndoHistory === beforeUndoHistory - 1,
               `History before: ${beforeUndoHistory}, after: ${afterUndoHistory}`);
    } catch (error) {
        logTest('undo method', false, `Error: ${error.message}`);
    }
    
    // Test 6: Test undo limit
    try {
        const maxUndoSteps = flashCardComponent.maxUndoSteps || 5;
        
        // Clear history first
        flashCardComponent.undoHistory = [];
        
        // Add more states than the limit
        for (let i = 0; i < maxUndoSteps + 3; i++) {
            flashCardComponent.saveUndoState();
        }
        
        const finalHistoryLength = flashCardComponent.undoHistory.length;
        logTest('Undo limit enforcement', finalHistoryLength <= maxUndoSteps,
               `Final history length: ${finalHistoryLength}, max: ${maxUndoSteps}`);
    } catch (error) {
        logTest('Undo limit enforcement', false, `Error: ${error.message}`);
    }
    
    // Test 7: Test UI button state
    const undoButton = document.querySelector('button[onclick*="undo"], button[click*="undo"], .undo-button');
    if (undoButton) {
        const isDisabled = undoButton.disabled || undoButton.classList.contains('disabled');
        const shouldBeDisabled = !flashCardComponent.canUndo();
        
        logTest('Undo button state sync', isDisabled === shouldBeDisabled,
               `Button disabled: ${isDisabled}, should be: ${shouldBeDisabled}`);
    } else {
        logTest('Undo button found', false, 'Undo button not found in DOM');
    }
    
    // Test 8: Test with actual swipe simulation (if possible)
    if (typeof flashCardComponent.swipeCard === 'function' && 
        flashCardComponent.dataFlashCard.length > 0) {
        
        try {
            const beforeSwipeData = flashCardComponent.dataFlashCard.length;
            const beforeSwipeSwipe = flashCardComponent.swipeCardList.length;
            
            // Simulate a swipe
            flashCardComponent.swipeCard('right');
            
            await waitForNextTick();
            
            const afterSwipeData = flashCardComponent.dataFlashCard.length;
            const afterSwipeSwipe = flashCardComponent.swipeCardList.length;
            const hasUndoHistory = flashCardComponent.undoHistory.length > 0;
            
            logTest('Swipe creates undo history', hasUndoHistory,
                   `Data cards: ${beforeSwipeData}→${afterSwipeData}, Swiped: ${beforeSwipeSwipe}→${afterSwipeSwipe}`);
            
            // Test undo after swipe
            if (hasUndoHistory) {
                flashCardComponent.undo();
                await waitForNextTick();
                
                const afterUndoData = flashCardComponent.dataFlashCard.length;
                const afterUndoSwipe = flashCardComponent.swipeCardList.length;
                
                logTest('Undo after swipe', 
                       afterUndoData === beforeSwipeData && afterUndoSwipe === beforeSwipeSwipe,
                       `Restored - Data: ${afterUndoData}, Swiped: ${afterUndoSwipe}`);
            }
        } catch (error) {
            logTest('Swipe simulation', false, `Error: ${error.message}`);
        }
    }
    
    console.log('\n🏁 Vocabulary Undo Integration Tests Completed!');
    
    // Clean up - reset undo history
    if (flashCardComponent.undoHistory) {
        flashCardComponent.undoHistory = [];
    }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
    console.log('🚀 Vocabulary Undo Test Script Loaded!');
    console.log('Run testVocabularyUndo() to start testing.');
    
    // Make function globally available
    window.testVocabularyUndo = testVocabularyUndo;
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testVocabularyUndo };
}
