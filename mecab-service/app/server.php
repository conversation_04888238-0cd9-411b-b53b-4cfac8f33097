<?php
header("Content-Type: application/json");

try {
    // Lấy input từ request (dạng JSON)
    $request = json_decode(file_get_contents("php://input"), true);
    $text = $request['text'] ?? '';

    if (empty($text)) {
        die(json_encode(["error" => "Text is empty before escapeshellarg"]));
    }

    if (!mb_check_encoding($text, "UTF-8")) {
        die("Encoding không phải UTF-8");
    }

    $text = mb_convert_encoding($text, "UTF-8", "auto");

    $escaped_text = escapeshellcmd($text);

    if (empty($escaped_text)) {
        die(json_encode(["error" => "Escaped text is empty"]));
    }

    $command_kana = "echo $escaped_text | mecab";

    $output = shell_exec($command_kana);

    $lines = explode("\n", trim($output));
    $hiraganaText = "";
    $katakanaText = "";
    $furiganaHtml = "";

    foreach ($lines as $line) {
        if ($line == "EOS") continue;
        $parts = explode("\t", $line);
        if (count($parts) > 1) {
            $features = explode(",", $parts[1]);
            $original = $parts[0]; // Từ gốc
            $katakana = $features[7] ?? $original; // Cột 7 là cách đọc Katakana
            $hiragana = mb_convert_kana($katakana, "c", "UTF-8"); // Chuyển Katakana sang Hiragana

            $hiraganaText .= $hiragana;
            $katakanaText .= $katakana;

            // Nếu có Kanji, hiển thị Furigana
            if (preg_match('/[\x{4E00}-\x{9FAF}]/u', $original)) {
                $furiganaHtml .= "<ruby>$original<rt>$hiragana</rt></ruby>";
            } else {
                $furiganaHtml .= $original;
            }
        }
    }

    // Trả về kết quả JSON
    echo json_encode([
        "original" => $text,
        "hiragana" => $hiraganaText,
        "katakana" => $katakanaText,
        "furigana_html" => $furiganaHtml
    ]);
} catch (\Exception $e) {
    echo json_encode(["error" => $e->getMessage()]);
}
