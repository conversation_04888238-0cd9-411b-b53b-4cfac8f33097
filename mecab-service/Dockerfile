FROM ubuntu:20.04

# Đặt múi giờ mặc định để bỏ qua Geographic area
ENV DEBIAN_FRONTEND=noninteractive TZ=Asia/Tokyo

ENV LANG=ja_JP.UTF-8
ENV LC_ALL=ja_JP.UTF-8

# Cập nhật package và cài đặt Apache, PHP, MeCab mà không cần nhập tay
RUN apt-get update && apt-get install -y \
    apache2 \
    libapache2-mod-php \
    php-cli \
    php-mbstring \
    mecab \
    libmecab-dev \
    mecab-ipadic-utf8 \
    locales \
    && rm -rf /var/lib/apt/lists/*

# Thiết lập locale UTF-8
RUN locale-gen ja_JP.UTF-8 && \
    update-locale LANG=ja_JP.UTF-8

# Đặt thư mục gốc cho Apache
WORKDIR /var/www/html

# Copy mã nguồn vào container
COPY app /var/www/html/

# Mở cổng 80
EXPOSE 80

# Chỉnh cấu hình Apache để trỏ mặc định vào server.php
RUN echo "DirectoryIndex server.php" > /etc/apache2/conf-available/custom-index.conf && \
    a2enconf custom-index

# Khởi động Apache
CMD ["apachectl", "-D", "FOREGROUND"]
