<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Frontend\AccountController;
use App\Http\Controllers\Frontend\AdTestController;

// Controller for API
use App\Http\Controllers\Frontend\APIController;
use App\Http\Controllers\Frontend\BirthdayController;
use App\Http\Controllers\Frontend\BlogController;
use App\Http\Controllers\Frontend\BookController;
use App\Http\Controllers\Frontend\CalendarController;
use App\Http\Controllers\Frontend\CDBHController;
use App\Http\Controllers\Frontend\ChatController;
use App\Http\Controllers\Frontend\CommunityController;
use App\Http\Controllers\Frontend\CourseController;
use App\Http\Controllers\Frontend\FirebaseController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\JLPTController;
use App\Http\Controllers\Frontend\LessonController;
use App\Http\Controllers\Frontend\MailController;
use App\Http\Controllers\Frontend\PageController;
use App\Http\Controllers\Frontend\PaymentController;
use App\Http\Controllers\Frontend\TeacherController;
use App\Http\Controllers\Frontend\TestOnlineController;
use App\Http\Controllers\Frontend\VideoController;
use App\Http\Controllers\Frontend\VocabularyController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/*
|--------------------------------------------------------------------------
| Web Routes Frontend
|--------------------------------------------------------------------------
 */

// Đặt ngôn ngữ mặc định trong hệ thống là tiếng việt
App::setLocale('vi');

// Lấy ra cookie ngôn ngữ
if (!is_null(Cookie::get('_lang'))) {
    $value = Cookie::get('_lang');
    if ($value == 'vi') {
        App::setLocale('vi');
    } else if ($value == 'en') {
        App::setLocale('en');
    }
}

// Trang chủ
Route::get('/', [HomeController::class, 'getHomePage'])->name('home')->middleware('check.survey');
Route::get('qr-code', function () {
    return QrCode::size(500)->generate('https://dungmori.com/qr-scan');
});
Route::get('qr-code-app', function () {
    return QrCode::size(500)->generate('https://dungmori.com/tai-app');
});
Route::get('/tai-app', function () {
    return view('frontend.coupon.download');
});
// Link app
Route::get('qr-scan', [APIController::class, 'qrScan'])->name('frontend.qr_scan');
//Route::get('get-voucher/{token}', '[APIController::class, 'getVoucher'])->name('frontend.get_voucher');
Route::get('get-voucher/{token}', function (Request $request) {
    if (!$request->hasValidSignature()) {
        abort(404);
    }
    return App::call('App\Http\Controllers\Frontend\APIController@getVoucher', ['token' => $request->token]);
})->name('frontend.get_voucher');
Route::get('xss', [APIController::class, 'testxss']);

Route::post('coupon/submit', [APIController::class, 'qrSubmit'])->name('frontend.coupon.submit');
Route::get('coupon/exist', function () {
    return view('frontend.coupon.exist');
})->name('frontend.coupon.exist');
Route::get('coupon/thank-you', function () {
    return view('frontend.coupon.thankyou');
})->name('frontend.coupon.thankyou');
Route::get('reset-password', function (Request $request) {
    if (!$request->hasValidSignature()) {
        abort(404);
    }
    return App::call('App\Http\Controllers\Frontend\APIController@resetPassword', ['token' => $request->token]);
})->name('frontend.reset-password');
Route::get('/TaiAppDungmoriAndroid', function () {
    return redirect('https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp');
});
Route::get('/TaiAppDungmoriIos', function () {
    return redirect('https://apps.apple.com/us/app/id1486123836');
});

Route::post('/my-courses', [CourseController::class, 'getMyCourses'])->name('my_course');
Route::get('/bang-gia', [CourseController::class, 'getCoursesPage'])->name('bang_gia')->middleware('check.survey');
Route::get('/firebase', [FirebaseController::class, 'index'])->name('firebase.index');
Route::get('/review', [HomeController::class, 'getFeedback'])->name('review.index');
Route::post('/checkpoint/save', [LessonController::class, 'saveCheckpointResult']);
Route::get('/checkpoint/{lid}', [LessonController::class, 'getCheckpoint']);

Route::group(['prefix' => 'sach'], function () {
    Route::get('', [BookController::class, 'getBooksPage'])->name('frontend.book');
    Route::get('/am-thanh', [BookController::class, 'getAudioBook'])->name('frontend.book.audio_book');
    Route::get('/am-thanh/{url}', [BookController::class, 'getAudio'])->name('frontend.book.audio');
    Route::get('/chi-tiet/{url}', [BookController::class, 'getDetailPage'])->name('frontend.book.detail');
    Route::get('/flashcard', [BookController::class, 'getFlashcardBook'])->name('frontend.book.flashcard_book');
    Route::get('/flashcard/{url}', [BookController::class, 'getFlashcards'])->name('frontend.book.flashcards');
    Route::get('/flashcard/{url}/{id}', [BookController::class, 'getFlashcard'])->name('frontend.book.flashcard');
    //    Route::get('/flashcard/', [BookController::class, 'getFlashcard'])->name('frontend.book.flashcard');
});
// Nhóm điều khiển dành cho khóa học
Route::group(['prefix' => 'khoa-hoc', 'middleware' => 'check.survey'], function () {
    Route::post('/upsert-lesson-progress', [LessonController::class, 'upsertLessonProgress'])->middleware('auth:web');
    Route::get('/', [CourseController::class, 'getCoursesPage']);
    Route::get('/combo/{id}', [CourseController::class, 'getComboPage']);
    Route::get('/{url}', [CourseController::class, 'getCourseDetailPage'])->name('frontend.course.detail');
});

Route::get('/khoa-hoc/{url}/{id}-{lesson_url}', [LessonController::class, 'getLesson'])->name('frontend.lesson.detail')->middleware('check.survey');
Route::get('/khoa-hoc/{courseSlug}/group/{groupId}', [LessonController::class, 'getGroupLesson'])->name('frontend.lesson.group');
Route::get('/khoa-hoc/{courseSlug}/lesson/{lessonId}-{lessonSlug}', [LessonController::class, 'getLessonNew'])->name('frontend.lesson.lesson_basic_new');
Route::get('/khoa-hoc/list-course/{idLesson?}', [LessonController::class, 'getListLesson']);
Route::get('/khoa-hoc/list-course/history-result/{id?}', [LessonController::class, 'getHistoryResultLesson']);
Route::post('/khoa-hoc/list-course/save-result-exercise-user', [LessonController::class, 'saveResultExerciseUser']);
Route::post('khoa-hoc/set-achievement', [LessonController::class, 'setAchievement']);
Route::post('/save-timer', [LessonController::class, 'saveTimer'])->name('frontend.lesson.save_timer');
Route::post('/khoa-hoc/gui-ket-qua', [LessonController::class, 'sendTestResult']);
Route::post('/lesson/submit-exam-ld', [LessonController::class, 'submitExamResult']);
Route::post('/lesson/load-result-history', [LessonController::class, 'loadResults']);
Route::post('/khoa-hoc/change-roadmap', [CourseController::class, 'changeRoadmap']);
Route::get('/prg/get-progress-sqlite', [CourseController::class, 'getProgress']);
Route::post('/khoa-hoc/add-code-lesson-to-user', [CourseController::class, 'addCodeLessonToUser'])->middleware('auth');
Route::get('/get-lesson-percent/{id}', [LessonController::class, 'getLessonPercent'])->middleware('auth');
Route::post('/khoa-hoc/save-rating', [CourseController::class, 'saveRating']);

//phần học của học viên cao đẳng bắc hà
Route::get('/cdbh/{url}/{id}/learn', [LessonController::class, 'getLesson'])->name('frontend.lesson.cdbh');
Route::get('/cdbh/auth', [LoginController::class, 'thirdPartyAuthCheck']);
Route::get('/cdbh/register', [LoginController::class, 'thirdPartyRegister']);
Route::get('/cdbh/change-info', [LoginController::class, 'thirdPartyChangePassword']);
Route::get('/cdbh/change-password', [LoginController::class, 'thirdPartyUserChangePassword']);

Route::group(['prefix' => 'cdbh'], function () {
    Route::post('/comments/comments-load-first', [CDBHController::class, 'getFirstComments']);
    Route::post('/comments/comments-load-more', [CDBHController::class, 'getMoreComments']);
    Route::post('/comments/add-new-comment', [CDBHController::class, 'addNewComment']);
    Route::post('/comments/delete-comment', [CDBHController::class, 'deleteComment']);
    Route::post('/comments/add-new-reply', [CDBHController::class, 'addNewReply']);
    Route::post('/comments/delete-reply', [CDBHController::class, 'deleteReply']);
    Route::post('/comments/edit-comment', [CDBHController::class, 'editComment']);
    Route::post('/comments/like-comment', [CDBHController::class, 'likeComment']);
    Route::get('/get-progress', [CDBHController::class, 'getBhProgress']);
    Route::post('/api/reset-user-progress', [CDBHController::class, 'resetUserProgress']);
    Route::post('/api/active-course', [CDBHController::class, 'activeCourse']);
});

Route::get('/tuyensinhcaodang', [JLPTController::class, 'getRoomBacha']);

// Nhóm điều khiển dành cho giáo viên
Route::group(['prefix' => 'giao-vien'], function () {
    Route::get('/', [TeacherController::class, 'getTeacherPage'])->name('teacher.index');
    Route::get('/{url}', [TeacherController::class, 'getTeacherDetail'])->name('teacher.detail');
});

// Nhóm điều khiển dành cho bài viết
Route::group(['prefix' => 'bai-viet', 'middleware' => 'check.survey'], function () {
    Route::get('/', [BlogController::class, 'getBlogPage'])->name('blog.index');
    Route::get('/{id}-{url}', [BlogController::class, 'getBlogDetail'])->name('blog.detail');
    Route::get('/cm/{ct}', [BlogController::class, 'getBlogPageByCategory'])->name('blog.category');
    Route::get('/load-more/{minId}', [BlogController::class, 'loadMoreBlog'])->name('blog.more');
});

Route::get('ve-dungmori', function () {
    return view('frontend.page.about');
})->middleware('check.survey');

// Nhóm điều khiển dành cho trang hỗ trợ
Route::group(['prefix' => 'ho-tro', 'middleware' => 'check.survey'], function () {
    Route::get('/', [PageController::class, 'getSupportPage']);
    Route::get('/{url}', [PageController::class, 'getSupportDetailPage']);
});
// Trang tuyển dụng
Route::get('tuyen-dung', [PageController::class, 'getRecruitmentPage'])->name('recruitment');

// Các trang bài viết đơn lẻ, trang thông tin
Route::get('/trang/{url}', [PageController::class, 'getInfoPage']);

// Nhóm điều khiển dành cho trang thanh toán
Route::group(['prefix' => 'payment'], function () {
    Route::get('/', [PaymentController::class, 'getPaymentPage']);
    Route::post('/change-customer-info', [PaymentController::class, 'changeCustomerInfo']);
    Route::post('/create-invoice', [PaymentController::class, 'createNewInvoice']);
    Route::post('/create-guest-invoice', [PaymentController::class, 'createNewGuestInvoice']);
    Route::post('/send-email-success', [PaymentController::class, 'sendEmailPaymentSuccess']);
    Route::post('/check-coupon', [PaymentController::class, 'checkCoupon']);
    Route::post('/guest-check', [PaymentController::class, 'guestCheck']);
});

// Đường dẫn kiểm tra đơn hàng
Route::get('/checkout/{uuid}', [PaymentController::class, 'checkoutPage']);

//api cho bên jvb
Route::get('/checkout/json/{uuid}', [PaymentController::class, 'getJsonInvoice']);

// Trang cá nhân sau khi người dùng đăng nhập
Route::group(['prefix' => 'account', 'middleware' => 'check.survey'], function () {

    Route::get('/', [AccountController::class, 'getAccountProfilePage'])->name('account');
    Route::get('/notifications', [AccountController::class, 'getAccountNotificationPage']);
    //thông báo hiể thị trên popup header
    Route::get('/announces', [AccountController::class, 'getUserAnnoucer']);
    Route::get('/update-announce', [AccountController::class, 'updateReadedAnnouce']);

    Route::group(['prefix' => 'courses'], function () {
        Route::get('/', [AccountController::class, 'getAccountCoursesPage']);
        Route::get('/journey', [AccountController::class, 'getAccountJourneyPage']);
        Route::get('/test', [AccountController::class, 'getAccountTestPage']);
    });

    Route::get('/billing', [AccountController::class, 'getAccountBillingPage']);
    Route::get('/active', [AccountController::class, 'getAccountActivePage']);
    Route::get('/logout', [AccountController::class, 'getAccountLogout']);
    Route::get('/score', [AccountController::class, 'getAccountScore']);
    Route::get('/score/detail/{groupId}', [AccountController::class, 'getAccountScoreDetail']);
    Route::get('/achievement', [AccountController::class, 'getAchievement']);
    Route::get('/run-achievement', [AccountController::class, 'handleAchievement']);
    Route::get('/reward', [AccountController::class, 'getReward'])->name('frontend.user.account.rewards');
    Route::post('/buy-reward', [AccountController::class, 'buyReward']);
    Route::post('/active-reward', [AccountController::class, 'activeReward']);
    Route::get('/current-streak', [AccountController::class, 'getStreakStatus']);
    Route::post('/claim-achievements', [AccountController::class, 'claimAchievements']);

    Route::post('/change-email', [AccountController::class, 'changeEmail']);
    Route::post('/change-info', [AccountController::class, 'changeAccountInfo']);
    Route::post('/change-password', [AccountController::class, 'changePassword']);
    Route::post('/change-avatar', [AccountController::class, 'changeAvatarPicture']);

    Route::post('/get-test-result-info', [AccountController::class, 'getTestResultInfo']);
    Route::delete('/delete-test-result', [AccountController::class, 'deleteTestResult']);

    Route::post('/active-course', [AccountController::class, 'activeCourse']);
    Route::post('/update-additional-info', [AccountController::class, 'updateAdditionalInfo']);
    Route::get('/get-captcha', [AccountController::class, 'getCaptcha'])->name('get-captcha-img');
    Route::get('/refresh-captcha', [AccountController::class, 'refreshCaptcha'])->name('refresh-captcha-img');
})->middleware('auth:web');

// Nhóm
Route::group(['prefix' => 'thi-thu'], function () {

    Route::get('/', [JLPTController::class, 'getRoom'])->name('thi-thu.index');
    Route::get('/ranking', [JLPTController::class, 'getRanking'])->name('thi-thu.ranking');
    Route::post('/search-ranking', [JLPTController::class, 'searchRanking']);
    Route::get('/history', [JLPTController::class, 'getHistory'])->name('thi-thu.history');

    //vẽ ảnh từ kết quả
    Route::get('/gen-img/{id}', [JLPTController::class, 'genResultImg']);
});
// Nhóm
Route::group(['prefix' => 'test-online', 'middleware' => 'check.survey'], function () {
    Route::get('/', [TestOnlineController::class, 'index'])->name('test-online.index');
    Route::get('/get-schedule', [TestOnlineController::class, 'getScheduleList'])->name('test-online.schedule-list');
    Route::get('/get-groups', [TestOnlineController::class, 'getGroupList'])->name('test-online.group-list');
    Route::get('/get-ranking', [TestOnlineController::class, 'getRanking'])->name('test-online.group-ranking');
    Route::get('/get-history', [TestOnlineController::class, 'getHistory'])->name('test-online.group-history');
    Route::post('/submit', [TestOnlineController::class, 'submit'])->name('test-online.submit');
    Route::get('/{token}', [TestOnlineController::class, 'getExam'])->name('test-online.get-exam');
})->middleware('auth:web');
// Nhóm
Route::group(['prefix' => 'dong-ho-bao-thuc-mua-thi', 'middleware' => 'check.survey'], function () {
    Route::get('/', [AdTestController::class, 'index'])->name('ad-test-fe.index');
    Route::get('/get-schedule', [AdTestController::class, 'getScheduleList'])->name('ad-test-fe.schedule-list');
    Route::get('/get-groups', [AdTestController::class, 'getGroupList'])->name('ad-test-fe.group-list');
    Route::get('/get-exams', [AdTestController::class, 'getExamList'])->name('ad-test-fe.exam-list');
    Route::get('/get-ranking', [AdTestController::class, 'getRanking'])->name('ad-test-fe.group-ranking');
    Route::get('/get-history', [AdTestController::class, 'getHistory'])->name('ad-test-fe.group-history');
    Route::post('/submit', [AdTestController::class, 'submit'])->name('ad-test-fe.submit');
    Route::get('/{token}', [AdTestController::class, 'getExam'])->name('ad-test-fe.get-exam');
});

// Test tính năng share fb
Route::group(['prefix' => 'test-share-fb'], function () {
    Route::get('/', function () {
        return view('frontend.home.test');
    });
});

// Test tính năng share fb
Route::group(['prefix' => 'get-jlpt-gift'], function () {
    Route::post('/', [APIController::class, 'jlptGift']);
});

// Test tính năng share fb
//  Route::group(['prefix' => 'nhan-qua-jlpt'], function () {
//      Route::get('/', function () {
//          return view('frontend.home.jlpt_gift');
//      });
//      Route::get('/gen-img/{id}', [APIController::class, 'renderImgGift'])->name('achievement.genImg');
//  //    Route::get('/gen-img/{id}', [APIController::class, 'genAchievementImg'])->name('achievement.genImg');
//      Route::get('/{id}', [APIController::class, 'showAchievementResult'])->name('achievement.showResult');
//  });

// Các API phục vụ global var
Route::group(['prefix' => 'api'], function () {

    Route::get('/refresh-cache', [HomeController::class, 'refreshCache']);

    //Nhóm điều api cho jlpt trên app
    Route::group(['prefix' => 'jlpt'], function () {
        Route::get('/init-ranking', 'Api\AppJLPT@initRanking');
        Route::get('/search-ranking', 'Api\AppJLPT@searchRanking');
        Route::get('/my-history/{id}', 'Api\AppJLPT@getHistory');
    });

    Route::get('/dang-ky-vip', [HomeController::class, 'dangKyKhoaVip']);

    Route::post('/user/tracking', [APIController::class, 'trackingUser']);
    Route::get('/user/tracking-online', [APIController::class, 'trackingUserOnline']);

    Route::get('/notifications/count-unread', [AccountController::class, 'countUnreadNotification']);
    Route::get('/notifications/mark-as-readed', [AccountController::class, 'markAsReadedNotification']);
    Route::get('/notifications/mark-all-readed', [AccountController::class, 'markAllReadedNotification']);

    Route::post('/comments/comments-load-first', [APIController::class, 'getFirstComments']);
    Route::post('/comments/comments-load-more', [APIController::class, 'getMoreComments']);
    Route::post('/comments/add-new-comment', [APIController::class, 'addNewComment']);
    Route::post('/comments/delete-comment', [APIController::class, 'deleteComment']);
    Route::post('/comments/add-new-reply', [APIController::class, 'addNewReply']);
    Route::post('/comments/delete-reply', [APIController::class, 'deleteReply']);
    Route::post('/comments/edit-comment', [APIController::class, 'editComment']);
    Route::post('/comments/like-comment', [APIController::class, 'likeComment']);
    Route::post('/profile/get-profile-by-id', [APIController::class, 'getProfileById']);

    // Comment new course
    Route::group(['prefix' => 'comment'], function () {
        Route::get('/list', [LessonController::class, 'getComments']);
        Route::get('/replies', [LessonController::class, 'getReplies']);
        Route::get('/total-unread-replies', [LessonController::class, 'getTotalUnreadReplies']);
        Route::post('/update-read-reply', [LessonController::class, 'updateReadReply']);
    });

    Route::post('/feedback-like-cmt', [APIController::class, 'feedbackLikeComment']);

    Route::post('/progress/get-user-progress', [APIController::class, 'getUserProgress']);

    Route::post('/flashcard/comments-load-first', [APIController::class, 'getFirstFCComments']);
    Route::post('/flashcard/comments-load-more', [APIController::class, 'getMoreFCComments']);

    Route::post('/flashcard/get-first-comments', [\App\Http\Controllers\Frontend\FlashCardController::class, 'getFirstComments']);
    Route::post('/flashcard/get-result', [\App\Http\Controllers\Frontend\FlashCardController::class, 'getResultByUser']);
    Route::post('/flashcard/save-result', [\App\Http\Controllers\Frontend\FlashCardController::class, 'saveResult']);
    Route::post('/flashcard/get-comment-by-id', [\App\Http\Controllers\Frontend\FlashCardController::class, 'getCommentById']);
    Route::post('/flashcard/like-comment', [\App\Http\Controllers\Frontend\FlashCardController::class, 'likeComment']);

    Route::get('/gen-video-name', [APIController::class, 'genVideoName']);
    Route::get('/user', [AccountController::class, 'getInfo']);

    // api flashcards
    Route::post('/flashcards/option', [APIController::class, 'flashcardSettings']);
    Route::post('/flashcards', [APIController::class, 'flashcards']);
    Route::post('/flashcards/learn', [APIController::class, 'flashcardLearn']);
    Route::post('/fetch-table-id', [APIController::class, 'fetchTableIds']);

    // api lưu tiến độ flashcard
    Route::post('/lesson/progress/save', [APIController::class, 'saveProgress']); // Lưu tiến độ bài học
    Route::get('/lesson/push-notification/{location}', [APIController::class, 'pushNotiRender']);
    Route::get('/lesson/get-exam-lesson/{lessonId}', [LessonController::class, 'getExamLesson']);
    Route::get('/lesson/start-exam/{lessonId}', [LessonController::class, 'startExam']);
    Route::post('/lesson/select-answer/{lessonId}', [LessonController::class, 'selectAnswer']);
    Route::post('/lesson/submit-exam/{lessonId}', [LessonController::class, 'submitExam']);
    Route::post('/lesson/next-stage/{lessonId}', [LessonController::class, 'nextStage']);
    Route::post('/lesson/save-passed-time/{resultId}/{time}/{stage}', [LessonController::class, 'savePassedTime']);
    Route::post('/lesson/save-mp3-time/{resultId}', [LessonController::class, 'saveMp3Time']);

    // api liên quan đến quiz
    Route::post('/quiz/question-by-task', [APIController::class, 'getQuestionByTask']);
    Route::post('/quiz/questions', [APIController::class, 'getQuestions']);
    Route::post('/quiz/check-answer-quiz', [APIController::class, 'checkAnswerQuiz']);
    Route::post('/quiz/check-answer-gap-fill', [APIController::class, 'checkAnswerGapFill']);
    Route::post('/quiz/save-result', [APIController::class, 'saveQuizResult']);
    Route::post('/quiz/get-results', [APIController::class, 'getResults']);
    Route::post('/quiz/delete-result', [APIController::class, 'deleteResult']);

    Route::post('/chat/get-conversations', [ChatController::class, 'getConversations']);
    Route::post('/chat/get-focus-conversation', [ChatController::class, 'getFocusConversation']);
    Route::post('/chat/get-messages', [ChatController::class, 'getMessages']);
    Route::post('/chat/load-more-messages', [ChatController::class, 'loadMoreMessages']);
    Route::post('/chat/load-focus-messages', [ChatController::class, 'getFocusMessages']);
    Route::post('/chat/load-next-messages', [ChatController::class, 'getNextMessages']);
    Route::post('/chat/upload-img', [ChatController::class, 'uploadImg']);
    Route::post('/chat/upload-file', [ChatController::class, 'uploadFile']);
    Route::post('/chat/mark-read-conversation', [ChatController::class, 'markReadConversation']);
    Route::post('/chat/check-unread-message', [ChatController::class, 'checkUnreadMesage']);
    Route::post('/chat/update-read-msg', [ChatController::class, 'updateReadMsg']);
    Route::post('/chat/save-group-name', [ChatController::class, 'saveGroupName']);
    Route::post('/chat/change-group-avatar', [ChatController::class, 'saveGroupAvatar']);
    Route::post('/chat/search-msg', [ChatController::class, 'searchMsg']);
    Route::post('/chat/search-member', [ChatController::class, 'searchMember']);
    Route::post('/chat/search-member-chat', [ChatController::class, 'searchMemberChat']);
    Route::post('/chat/add-member-to-group-chat', [ChatController::class, 'addMemberToGroupChat']);
    Route::post('/chat/remove-member-to-group-chat', [ChatController::class, 'removeMemberFromGroupChat']);
    Route::post('/chat/init-chat-personal', [ChatController::class, 'initChatPersonal']);
    Route::post('/chat/get-conversation-list-in-group', [ChatController::class, 'getConversationListInGroup']);
    Route::post('/chat/switch-conversation-group', [ChatController::class, 'switchConversationGroup']);
    Route::post('/chat/mark-as-unread', [ChatController::class, 'markAsUnread']);
    Route::get('/chat/get-member-histories', [ChatController::class, 'getMemberHistories']);
    Route::post('/chat/toggle-joinable/{id}', [ChatController::class, 'toggleJoinable']);
    Route::post('/chat/search-admin', [ChatController::class, 'searchAdmin']);
    Route::post('/chat/login', [ChatController::class, 'loginAdmin']);
    Route::post('/chat/read-all', [ChatController::class, 'readAll']);

    Route::post('/interactive-video/questions', [APIController::class, 'getInteractiveVideoQuestions']);

    Route::post('/submit-sharing-url', [APIController::class, 'submitSharingUrl']);

    Route::get('/get-vendor-progress', [APIController::class, 'getVendorProgress']);

    Route::post('/api-sv/upload-file-api', [APIController::class, 'uploadFileApi']);
    Route::post('/api-sv/upload-image-api', [APIController::class, 'uploadImageApi']);
    Route::post('/api-sv/upload-images-api', [APIController::class, 'uploadImagesApi']);
    Route::post('/api-sv/upload-audios-api', [APIController::class, 'uploadAudiosApi']);
    Route::post('/api-sv/upload-achievement-image-api', [APIController::class, 'uploadAchievementImageApi']);
});

//Authenticate
Auth::routes();

//Auth by social
Route::get('oauth/{provider}', [LoginController::class, 'redirectToProvider']);
Route::get('oauth/{provider}/callback', [LoginController::class, 'handleProviderCallback']);
Route::post('oauth/{provider}/callback', [LoginController::class, 'handleProviderCallback']);

// Register for support in home page
Route::post('/register-for-support', [HomeController::class, 'registerForSupport']);
Route::get('/dang-ky/xac-nhan/{token}', [MailController::class, 'getVerifyPage']); // Xác nhận email đăng ký
Route::get('/gui-mail-xac-thuc-email', [MailController::class, 'sendVerifyMail']); // Gửi mail xác nhận
Route::post('/gui-y-kien', [MailController::class, 'sendMail']); // Gửi mail đóng góp ý kiến tại trang hỗ trợ

Route::get('/clone-group/{g1}/{g2}/{nc}', [HomeController::class, 'cloneGroup']);

Route::group(['prefix' => 'community', 'middleware' => 'check.survey'], function () {
    Route::post('/exam/get-calendar', [CommunityController::class, 'getCalendar']);
    Route::post('/exam/get-current-date-exams', [CommunityController::class, 'getCurrentDateExam']);
    Route::post('/add-exam-to-group', [CommunityController::class, 'addExam']);
    Route::post('/update-schedule', [CommunityController::class, 'updateSchedule']);
    Route::post('/delete-schedule', [CommunityController::class, 'deleteSchedule']);
});

// Các API phục vụ global var
Route::group(['prefix' => 'groups', 'middleware' => 'check.survey'], function () {
    Route::get('/', [CommunityController::class, 'getPublicGroup'])->name('community.groups');
    Route::get('/detail/{id}-preview', [CommunityController::class, 'getDetailPost'])->name('community.detail');
    Route::get('/{uid}-{slug}', [CommunityController::class, 'getPrivateGroup'])->name('community.group');
    Route::get('/me', [CommunityController::class, 'getProfile'])->name('community.me');
    Route::get('/tiktok/{id}', [CommunityController::class, 'embedTiktok'])->name('community.tiktok');
});

Route::get('video/get-link', [VideoController::class, 'getLink']);
Route::get('video.m3u8', [VideoController::class, 'getVideo']);

//api gia hạn khoá học
Route::get('/course/extend-info', [CourseController::class, 'getExtensionCourse']);
Route::view('ket-qua-thi', 'frontend.jlpt.offline_result')->name('offline_result');
Route::get('ket-qua-thi/list', [APIController::class, 'getOfflineResult']);
Route::get('process', [APIController::class, 'process']);
Route::post('upload-file', [APIController::class, 'uploadFile'])->middleware('auth:web');
Route::post('upload-image', [APIController::class, 'uploadImage'])->middleware('auth:web');
Route::post('upload-images', [APIController::class, 'uploadImages'])->middleware('auth:web');

Route::group([
    'prefix' => 'survey'
], function () {
    Route::get('close-popup', [\App\Http\Controllers\Frontend\SurveyController::class, 'hidePopup']);
    Route::get('detail', [\App\Http\Controllers\Frontend\SurveyController::class, 'detail']);
    Route::post('save', [\App\Http\Controllers\Frontend\SurveyController::class, 'saveSurvey'])->name('survey.save');
});


Route::get('create-img/{id}', [APIController::class, 'renderImgGift']);


Route::group(['prefix' => 'vocabulary'], function () {
    Route::get('/', [VocabularyController::class, 'index']);
});

Route::group([
    'prefix' => 'user',
    'middleware' => 'auth:web',
    'as' => 'user.'
], function () {
    Route::post('update-info', [AccountController::class, 'updateInfo'])->name('update-info');
});

Route::get('chuc-mung-sinh-nhat', [BirthdayController::class, 'index'])->name('birthday.index');
Route::get('loi-chuc/{id}', [BirthdayController::class, 'wishPage'])->name('birthday.wishPage');
Route::get('anh-loi-chuc/{id}', [BirthdayController::class, 'genWishImage'])->name('birthday.genWishImage');

// Route::group(['prefix' => 'chuc-mung-sinh-nhat', 'middleware' => 'auth:web'], function () {
//     Route::post('', [BirthdayController::class, 'store'])->name('birthday.store');
// });

Route::get('reset-course-duration', function (Request $request) {
    if (!$request['user_id'] || !$request['course_id'] || !$request['time_reset']) {
        return response()->json([
            'code' => 400,
            'msg' => 'Missing params'
        ]);
    }
    if ($request['is_del'] == 1) {
        \App\Http\ModelsFrontend\CourseOwner::query()->where('owner_id', $request['user_id'])->where('course_id', $request['course_id'])->delete();
    }
    $courseOwner = \App\Http\ModelsFrontend\CourseOwner::query()->where('owner_id', $request['user_id'])->where('course_id', $request['course_id'])->first();
    if (!$courseOwner) {
        return response()->json([
            'code' => 400,
            'msg' => 'Not found'
        ]);
    }
    $courseOwner->watch_expired_day = \Carbon\Carbon::parse($request['time_reset'])->format('Y-m-d H:i:s');
    $courseOwner->save();
    return response()->json([
        'code' => 200,
        'msg' => 'OK'
    ]);
});;

Route::group(['prefix' => 'calendar'], function () {
    Route::get('', [CalendarController::class, 'index'])->name('calendar.index');
});