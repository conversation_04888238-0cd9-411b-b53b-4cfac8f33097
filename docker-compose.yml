# Source: https://github.com/aschmelyun/docker-compose-laravel

networks:
  laravel:
services:
  site:
    build:
      context: .
      dockerfile: docker/nginx.dockerfile
      args:
        - NGINXUSER=${NGINXUSER:-www-data}
        - NGINXGROUP=${NGINXGROUP:-www-data}
    container_name: nginx
    ports:
      - 8000:8000
    volumes:
      - ./:/var/www/html:delegated
    depends_on:
      - php
      - redis
    networks:
      - laravel
  php:
    build:
      context: .
      dockerfile: docker/php.dockerfile
      args:
        - PHPUSER=${PHPUSER:-www-data}
        - PHPGROUP=${PHPGROUP:-www-data}
    container_name: php
    volumes:
      - ./:/var/www/html:delegated
    depends_on:
      - mysql
      - mysql_jlpt
      - mongo
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - laravel
  redis:
    image: redis:7.0.14-alpine
    container_name: redis
    restart: unless-stopped
    ports:
      - 6380:6379
    networks:
      - laravel
  mysql:
    image: mysql:latest
    container_name: mysql
    restart: unless-stopped
    ports:
      - 3328:3306
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - data_mysql:/var/lib/mysql
    networks:
      - laravel
  mysql_school:
    image: mysql:latest
    container_name: mysql_school
    restart: unless-stopped
    ports:
      - 3330:3306
    environment:
      MYSQL_DATABASE: ${DB_DATABASE_THIRD}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD_THIRD}
      MYSQL_USER: ${DB_USERNAME_THIRD}
      MYSQL_PASSWORD: ${DB_PASSWORD_THIRD}
    volumes:
      - data_mysql_school:/var/lib/mysql
    networks:
      - laravel
  mysql_jlpt:
    image: mysql:latest
    container_name: mysql_jlpt
    restart: unless-stopped
    ports:
      - 3329:3306
    environment:
      MYSQL_DATABASE: ${DB_DATABASE_SECOND}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD_SECOND}
      MYSQL_USER: ${DB_USERNAME_SECOND}
      MYSQL_PASSWORD: ${DB_PASSWORD_SECOND}
    volumes:
      - data_mysql_jlpt:/var/lib/mysql
    networks:
      - laravel
  mongo:
    image: mongo:7.0.14-jammy
    container_name: mongo
    restart: unless-stopped
    networks:
      - laravel

volumes:
  data_mysql:
  data_mysql_jlpt:
  data_mysql_school:
