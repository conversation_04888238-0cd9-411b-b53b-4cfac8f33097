Vue.component("course-part-panel",{template:"#course-parts-panel-template",props:["courses","stages"],data:function(){return{url:window.location.origin}},mounted:function(){console.log("courses --\x3e ",this.courses),console.log("parts --\x3e ",this.parts)},methods:{}}),function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}function n(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function r(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(Object(o),!0).forEach((function(t){s(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function s(t,o,n){return(o=function(t){var o=function(t,o){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,o||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"==e(o)?o:o+""}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n,t}Vue.component("lesson-category-panel",{template:"#lesson-categories-panel-template",props:["course","stage","stages","permission"],watch:{stage:function(e){this.getCategoriesByStage(this.course.id,e),this.formData.stage=e},course:function(e){this.formData.course_id=e.id,this.getCategoriesByStage(e,this.stage)}},data:function(){return{url:window.location.origin,categories:[],selectedCategory:{},loading:!1,showModal:!1,draggable:!1,stages:this.stages,formData:{id:void 0,title:"",type:1,icon:"",course_id:0,stage:this.stage,status:1,iconImg:null}}},mounted:function(){},methods:{onDragEnd:function(){var e=this,t=e.categories.map((function(e){return e.id})),o={course_id:e.course.id,stage:e.stage,ids:t};$.post(e.url+"/backend/lesson-categories/apply-sorting",o,(function(t){200===t.code&&(e.categories=t.data.map((function(e){return e})))}))},changeStatus:function(e,t){var o=this,n={id:e.id,status:t};$.post(o.url+"/backend/lesson-categories/change-status",n,(function(e){200===e.code&&(o.categories=o.categories.map((function(t){return t.id===e.data.id&&(t.status=e.data.status),t})))}))},changeFormIcon:function(e){this.formData.iconImg=e.target.files[0]},saveForm:function(){var e=this,t=new FormData;t.append("id",e.formData.id),t.append("title",e.formData.title),t.append("type",e.formData.type),t.append("icon",e.formData.icon),t.append("course_id",e.formData.course_id),t.append("stage",e.formData.stage),t.append("status",e.formData.status),t.append("iconImg",e.formData.iconImg),e.formData.id?e.updateCategory(t):e.addCategory(t)},editCategory:function(e){var t=this;t.formData=r(r({},t.formData),{},{id:e.id,title:e.title,type:e.type,icon:e.icon,course_id:e.course_id,stage:e.stage,status:e.status}),t.showModal=!0},addCategory:function(e){var t=this;$.ajax({url:t.url+"/backend/lesson-categories/add-category",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200===e.code?(e.data.stage===t.stage&&t.categories.push(e.data),t.closeModal()):alert(e.msg)}})},updateCategory:function(e){var t=this;$.ajax({url:t.url+"/backend/lesson-categories/update-category",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200===e.code?(t.categories=t.categories.map((function(t){return t.id===e.data.id&&(t=e.data),t})),t.closeModal()):alert(e.msg)}})},deleteCategory:function(e){var t=this;if(window.confirm("Xác nhận xoá danh mục cùng với toàn bộ bài học trong danh mục?")){var o={course_id:e.course_id,id:e.id};$.post(t.url+"/backend/lesson-categories/delete-category",o,(function(o){200===o.code&&(t.categories=t.categories.filter((function(t){return t.id!==e.id})),t.selectedCategory.id===e.id&&(t.categories.length>0?t.selectCategory(t.categories[0]):t.selectUncategorized()))}))}},setFormStatus:function(e){this.formData.status=parseInt(e.target.value)},closeModal:function(){var e=this;e.formData=r(r({},e.formData),{},{id:void 0,title:"",type:1,icon:"",course_id:e.course.id,status:1,iconImg:null}),e.showModal=!1},getCategoriesByStage:function(e,o){var n=this,r={courseId:e,stage:o};n.loading=!0,$.post(n.url+"/backend/lesson-categories",r,(function(e){200===e.code&&(n.categories=t(e.data),n.selectedCategory=n.categories[0]||{},n.$emit("update:category",n.categories[0]||{}),n.loading=!1)}))},selectCategory:function(e){this.selectedCategory=e,this.$emit("update:category",e)},selectUncategorized:function(){var e=this;e.selectedCategory={course_id:e.course.id,icon:"",id:0,stage:e.stage.id,status:1,title:"Chưa được phân loại",type:1},e.$emit("update:category",e.selectedCategory)}}})}(),function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function o(e){for(var o=1;o<arguments.length;o++){var r=null!=arguments[o]?arguments[o]:{};o%2?t(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function n(t,o,n){return(o=function(t){var o=function(t,o){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,o||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"==e(o)?o:o+""}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n,t}function r(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?s(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}Vue.component("lesson-group-panel",{template:"#lesson-groups-panel-template",props:["category","permission"],data:function(){return{url:window.location.origin,groups:[],lessons:[],choseLessons:[],choseType:void 0,choseRule:void 0,groupIdToChange:void 0,categories:[],showingGroup:{},loading:!1,showModal:!1,showLessonsModal:!1,draggable:!1,playbackRate:[.75,1,1.25,1.5,2],formData:{id:void 0,course_id:0,name:"",description:"",component_count:"",skill:"",lesson_category_id:0,show:0,is_specialezed:0}}},watch:{category:function(e){this.getGroupByCategory(e),this.formData.lesson_category_id=e.id}},mounted:function(){},methods:{printLessonIcon:function(e){var t="";switch(e){case"docs":default:t="docb.png";break;case"video":t="videob.png";break;case"test":t="quizb.png";break;case"flashcard":t="fcb.png"}return t},updateLesson:function(e,t,o){var n=this,r={id:t.id,field:o,value:e.target.value};$.post(n.url+"/backend/lesson-groups/update-lesson",r,(function(e){200===e.code?(n.lessons=n.lessons.map((function(t){return t.id===e.data.id&&(t[o]=e.data[o]),t})),alert("Thành công")):alert("Thất bại")}))},changeLessonSpeed:function(e,t){var o=this,n={id:t.id,speed:e.target.value};$.post(o.url+"/backend/lesson-groups/change-lesson-speed",n,(function(e){200===e.code?o.lessons=o.lessons.map((function(t){return t.id===e.data.id&&(t.default_speed=e.data.default_speed),t})):alert("Thất bại")}))},changeLessonType:function(){var e=this;if(e.choseType&&e.choseLessons.length>0){var t={ids:r(e.choseLessons),choseType:e.choseType};$.post(e.url+"/backend/lesson/change-lesson-type",t,(function(t){200===t.code?(e.choseLessons=[],e.choseType=void 0,e.lessons=e.lessons.map((function(e,o){return e[o].type=t.data[o].type,e})),alert("Chuyển thành công")):alert("Chuyển thất bại")}))}},addLessonRule:function(){var e=this;if(e.choseRule&&e.choseLessons.length>0){var t={ids:r(e.choseLessons),choseRule:e.choseRule};$.post(e.url+"/backend/lesson-groups/lesson-rule/attach",t,(function(t){200===t.code?(t.data,t.data.forEach((function(t){var o=e.lessons.findIndex((function(e){return e.id===t.id}));-1===o?e.lessons.push(t):Vue.set(e.lessons,o,t)}))):alert("Chuyển thất bại")}))}},changeGroupId:function(){var e=this;if(e.groupIdToChange&&e.choseLessons.length>0){var t={ids:r(e.choseLessons),groupIdToChange:e.groupIdToChange};$.post(e.url+"/backend/lesson/change-group",t,(function(t){200===t.code?(e.lessons=e.lessons.filter((function(t){return!e.choseLessons.includes(t.id)})),e.choseLessons=[],alert("Chuyển thành công")):alert("Chuyển thất bại")}))}},checkAllLesson:function(e){var t=this,o=e.target.checked;t.choseLessons=o?t.lessons.map((function(e){return e.id})):[],console.log("array --\x3e ",t.choseLessons)},checkOneLesson:function(e,t){var o=this;e.target.checked?o.choseLessons.push(t):o.choseLessons=o.choseLessons.filter((function(e){return e!==t})),console.log("array --\x3e ",o.choseLessons)},changeLessonRulePoint:function(e){var t=window.prompt("Nhập số điểm");$.post(this.url+"/backend/lesson-groups/rule/".concat(e.pivot.id,"/point"),{point:t},(function(o){200===o.code?e.pivot.point=t:alert(o.msg)}))},detachRule:function(e,t,o){var n={lesson_id:e.id,rule_id:t};$.post(this.url+"/backend/lesson-groups/lesson-rule/detach",n,(function(t){200===t.code?e.experience_rules.splice(o,1):alert(t.msg)}))},toggleHideLessonTitle:function(e){var t=this,o={id:e.id};$.post(t.url+"/backend/lesson/toggle-secret",o,(function(e){200===e.code?t.lessons=t.lessons.map((function(t){return t.id===e.data.id&&(t.is_secret=e.data.is_secret),t})):alert(e.msg)}))},onChangeCheckbox:function(e,t){this.formData[t]=e.target.checked?1:0},onDragLessonEnd:function(){var e=this,t=e.lessons.map((function(e){return e.id})),o={group_id:e.showingGroup.id,ids:t};$.post(e.url+"/backend/lesson-groups/apply-sorting-lesson",o,(function(t){200===t.code&&(e.lessons=t.data.map((function(e){return e})))}))},closeLessonsModal:function(){var e=this;e.lessons=[],e.showLessonsModal=!1,e.groupIdToChange=void 0,e.choseLessons=[]},showLessons:function(e){var t=this,o={id:e.id};t.showingGroup=e,$.post(t.url+"/backend/lesson-groups/get-lessons",o,(function(e){200===e.code&&(t.lessons=r(e.data),t.showLessonsModal=!0)}))},onDragEnd:function(){var e=this,t=e.groups.map((function(e){return e.id})),o={course_id:e.category.course_id,lesson_category_id:e.category.id,ids:t};$.post(e.url+"/backend/lesson-groups/apply-sorting",o,(function(t){200==t.code&&(e.groups=t.data.map((function(e){return e})))}))},changeLessonStatus:function(e,t){var o=this,n={id:e.id,show:t};$.post(o.url+"/backend/lesson-groups/change-lesson-status",n,(function(e){200===e.code&&(o.lessons=o.lessons.map((function(t){return t.id===e.data.id&&(t.show=e.data.show),t})))}))},changeStatus:function(e,t){var o=this,n={id:e.id,show:t};$.post(o.url+"/backend/lesson-groups/change-status",n,(function(e){200===e.code&&(o.groups=o.groups.map((function(t){return t.id===e.data.id&&(t.show=e.data.show),t})))}))},saveForm:function(){var e=this,t=new FormData;t.append("id",e.formData.id),t.append("name",e.formData.name),t.append("description",e.formData.description),t.append("skill",e.formData.skill),t.append("component_count",e.formData.component_count),t.append("course_id",e.category.course_id),t.append("lesson_category_id",e.formData.lesson_category_id),t.append("show",e.formData.show),e.formData.id?e.updateGroup(t):e.addGroup(t)},editGroup:function(e){console.log("group",e);var t=this;t.formData=o(o({},t.formData),{},{id:e.id,name:e.name,description:e.description,skill:e.skill,component_count:e.component_count,lesson_category_id:e.lesson_category_id,show:e.show}),t.showModal=!0},addGroup:function(e){var t=this;$.ajax({url:t.url+"/backend/lesson-groups/add-group",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200===e.code?(e.data.lesson_category_id===t.category.id&&t.groups.push(e.data),t.closeModal()):alert(e.msg)}})},updateGroup:function(e){var t=this;$.ajax({url:t.url+"/backend/lesson-groups/update-group",type:"POST",data:e,processData:!1,contentType:!1,success:function(e){200===e.code?(e.data.lesson_category_id!==t.category.id?t.groups=_.remove(t.groups,(function(t){return t.id!==e.data.id})):t.groups=t.groups.map((function(t){return t.id===e.data.id&&(t=o({},e.data)),t})),t.closeModal()):alert(e.msg)}})},deleteGroup:function(e){var t=this;if(window.confirm("Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?")){var o={id:e.id};$.post(t.url+"/backend/lesson-groups/delete-group",o,(function(o){200===o.code&&(t.groups=t.groups.filter((function(t){return t.id!==e.id})))}))}},setFormStatus:function(e){this.formData.show=parseInt(e.target.value)},closeModal:function(){var e=this;e.formData=o(o({},e.formData),{},{id:void 0,course_id:0,name:"",description:"",skill:"",component_count:"",show:0}),e.showModal=!1},getGroupByCategory:function(e){var t=this;t.loading=!0;var o={course_id:e.course_id,id:e.id};$.post(t.url+"/backend/lesson-groups",o,(function(e){200===e.code&&(t.groups=r(e.data.groups),t.categories=r(e.data.categories),t.loading=!1)}))}}})}(),function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function o(t,o,n){return(o=function(t){var o=function(t,o){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,o||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"==e(o)?o:o+""}(o))in t?Object.defineProperty(t,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[o]=n,t}new Vue({el:"#course-stages__screen",data:function(){return{url:window.location.origin,admin:{},permission:0,courses:[],stages:[],stage_names:[],selectedCourse:{},selectedStage:1,selectedCategory:{},loading:!1,formData:{id:void 0,title:"",key:"",course_id:"",status:1}}},methods:{getStageName:function(e){if(0!==this.stage_names.length)return this.stage_names.filter((function(t){return t.stage===e})).length>0?this.stage_names.filter((function(t){return t.stage===e}))[0].stage_name:null},getReleaseDate:function(e){if(0!==this.stage_names.length)return this.stage_names.filter((function(t){return t.stage===e})).length>0?this.stage_names.filter((function(t){return t.stage===e}))[0].release_date:null},categorySelected:function(e){this.selectedCategory=e},selectCourse:function(e,n){e.target.checked&&(this.selectedCourse=function(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},n))},checkAdmin:function(e){return 1===vm.admin.permission||36===vm.admin.id||4===vm.admin.id||69===vm.admin.id},addStage:function(){this.stages.push(this.stages.length+1)},setMaxStage:function(e){this.stages=[];for(var t=1;t<=e;t++)this.stages.push(t)},removeLastStage:function(){var e=this;confirm("Bạn có chắc chắn muốn xóa chặng cuối chứ? Dữ liệu không thể khôi phục sau khi xóa!")&&(e.stages.pop(),$.post(e.url+"/backend/delete-last-stage",{course_id:e.selectedCourse.id},(function(e){console.log(e)})))},getMaxStage:function(){var e=this;if(e.selectedCourse){var t=e.selectedCourse.id;$.get(e.url+"/backend/get-max-stage?courseId="+t,(function(t){e.setMaxStage(t.max_stage),e.stage_names=t.stage_list}))}},changeStageName:function(e,t){var o=$("#stage_".concat(t)).val();0!=this.stage_names.filter((function(e){return e.stage===t})).length?$.post(this.url+"/backend/update-stage-name",{course_id:e,stage:t,stage_name:o},(function(e){alert("Cập nhật tên chặng thành công!")})):alert("Bạn phải tạo chặng trước khi cập nhật tên chặng!")},changeReleaseDate:function(e,t){var o=$("#release_date_".concat(t)).val();if(o)if(10===o.length){var n=o.split("/");3===n.length&&2===n[0].length&&2===n[1].length&&4===n[2].length?n[0]>31||n[1]>12||n[2]>2025?alert("Ngày ra mắt phải có định dạng dd/mm/yyyy!"):$.post(this.url+"/backend/update-release-date",{course_id:e,stage:t,release_date:o},(function(e){alert("Cập nhật ngày ra mắt thành công!")})):alert("Ngày ra mắt phải có định dạng dd/mm/yyyy!")}else alert("Ngày ra mắt phải có định dạng dd/mm/yyyy!");else alert("Bạn phải nhập ngày ra mắt!")}},watch:{selectedCourse:function(){this.getMaxStage()}},mounted:function(){var e=this;e.admin=admin,e.permission=1===e.admin.permission||36===e.admin.id||4===e.admin.id||69===e.admin.id,e.courses=_.orderBy(courses,"name").filter((function(e){if(["N1","N2","N3","N4","N5","EJU - Toán","Sơ cấp N4","Sơ cấp N5","Luyện đề N4","Luyện đề N5","JLPT N1","JLPT N2","JLPT N3","Chữ Hán N5"].includes(e.name))return e})),e.selectedCourse=e.courses[0],e.getMaxStage()}})}();