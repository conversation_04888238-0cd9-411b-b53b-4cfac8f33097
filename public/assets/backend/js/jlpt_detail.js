/*! For license information please see jlpt_detail.js.LICENSE.txt */
!function(){var t={3191:function(t,e,n){"use strict";var r=n(31928);function o(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var e;this.promise=new Promise((function(t){e=t}));var n=this;t((function(t){n.reason||(n.reason=new r(t),e(n.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var t;return{token:new o((function(e){t=e})),cancel:t}},t.exports=o},5449:function(t){"use strict";t.exports=function(t,e,n,r,o){return t.config=e,n&&(t.code=n),t.request=r,t.response=o,t}},7018:function(t,e,n){"use strict";var r=n(9516);t.exports=function(t,e){r.forEach(t,(function(n,r){r!==e&&r.toUpperCase()===e.toUpperCase()&&(t[e]=n,delete t[r])}))}},7522:function(t,e,n){"use strict";var r=n(47763);t.exports=function(t,e,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?e(r("Request failed with status code "+n.status,n.config,null,n.request,n)):t(n)}},9516:function(t,e,n){"use strict";var r=n(69012),o=n(87206),i=Object.prototype.toString;function s(t){return"[object Array]"===i.call(t)}function a(t){return null!==t&&"object"==typeof t}function u(t){return"[object Function]"===i.call(t)}function c(t,e){if(null!=t)if("object"==typeof t||s(t)||(t=[t]),s(t))for(var n=0,r=t.length;n<r;n++)e.call(null,t[n],n,t);else for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.call(null,t[o],o,t)}t.exports={isArray:s,isArrayBuffer:function(t){return"[object ArrayBuffer]"===i.call(t)},isBuffer:o,isFormData:function(t){return"undefined"!=typeof FormData&&t instanceof FormData},isArrayBufferView:function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&t.buffer instanceof ArrayBuffer},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isObject:a,isUndefined:function(t){return void 0===t},isDate:function(t){return"[object Date]"===i.call(t)},isFile:function(t){return"[object File]"===i.call(t)},isBlob:function(t){return"[object Blob]"===i.call(t)},isFunction:u,isStream:function(t){return a(t)&&u(t.pipe)},isURLSearchParams:function(t){return"undefined"!=typeof URLSearchParams&&t instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:c,merge:function t(){var e={};function n(n,r){"object"==typeof e[r]&&"object"==typeof n?e[r]=t(e[r],n):e[r]=n}for(var r=0,o=arguments.length;r<o;r++)c(arguments[r],n);return e},extend:function(t,e,n){return c(e,(function(e,o){t[o]=n&&"function"==typeof e?r(e,n):e})),t},trim:function(t){return t.replace(/^\s*/,"").replace(/\s*$/,"")}}},17980:function(t){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},18015:function(t,e,n){"use strict";var r=n(9516),o=n(69012),i=n(35155),s=n(96987);function a(t){var e=new i(t),n=o(i.prototype.request,e);return r.extend(n,i.prototype,e),r.extend(n,e),n}var u=a(s);u.Axios=i,u.create=function(t){return a(r.merge(s,t))},u.Cancel=n(31928),u.CancelToken=n(3191),u.isCancel=n(93864),u.all=function(t){return Promise.all(t)},u.spread=n(17980),t.exports=u,t.exports.default=u},29137:function(t){"use strict";t.exports=function(t){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)}},31928:function(t){"use strict";function e(t){this.message=t}e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,t.exports=e},33948:function(t,e,n){"use strict";var r=n(9516);t.exports=r.isStandardBrowserEnv()?{write:function(t,e,n,o,i,s){var a=[];a.push(t+"="+encodeURIComponent(e)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(o)&&a.push("path="+o),r.isString(i)&&a.push("domain="+i),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(t){var e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(t){this.write(t,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},35155:function(t,e,n){"use strict";var r=n(96987),o=n(9516),i=n(83471),s=n(64490),a=n(29137),u=n(84680);function c(t){this.defaults=t,this.interceptors={request:new i,response:new i}}c.prototype.request=function(t){"string"==typeof t&&(t=o.merge({url:arguments[0]},arguments[1])),(t=o.merge(r,this.defaults,{method:"get"},t)).method=t.method.toLowerCase(),t.baseURL&&!a(t.url)&&(t.url=u(t.baseURL,t.url));var e=[s,void 0],n=Promise.resolve(t);for(this.interceptors.request.forEach((function(t){e.unshift(t.fulfilled,t.rejected)})),this.interceptors.response.forEach((function(t){e.push(t.fulfilled,t.rejected)}));e.length;)n=n.then(e.shift(),e.shift());return n},o.forEach(["delete","get","head","options"],(function(t){c.prototype[t]=function(e,n){return this.request(o.merge(n||{},{method:t,url:e}))}})),o.forEach(["post","put","patch"],(function(t){c.prototype[t]=function(e,n,r){return this.request(o.merge(r||{},{method:t,url:e,data:n}))}})),t.exports=c},35592:function(t,e,n){"use strict";var r=n(9516),o=n(7522),i=n(79106),s=n(62012),a=n(64202),u=n(47763),c="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n(42537);t.exports=function(t){return new Promise((function(e,l){var f=t.data,h=t.headers;r.isFormData(f)&&delete h["Content-Type"];var p=new XMLHttpRequest,d="onreadystatechange",m=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in p||a(t.url)||(p=new window.XDomainRequest,d="onload",m=!0,p.onprogress=function(){},p.ontimeout=function(){}),t.auth){var g=t.auth.username||"",y=t.auth.password||"";h.Authorization="Basic "+c(g+":"+y)}if(p.open(t.method.toUpperCase(),i(t.url,t.params,t.paramsSerializer),!0),p.timeout=t.timeout,p[d]=function(){if(p&&(4===p.readyState||m)&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in p?s(p.getAllResponseHeaders()):null,r={data:t.responseType&&"text"!==t.responseType?p.response:p.responseText,status:1223===p.status?204:p.status,statusText:1223===p.status?"No Content":p.statusText,headers:n,config:t,request:p};o(e,l,r),p=null}},p.onerror=function(){l(u("Network Error",t,null,p)),p=null},p.ontimeout=function(){l(u("timeout of "+t.timeout+"ms exceeded",t,"ECONNABORTED",p)),p=null},r.isStandardBrowserEnv()){var v=n(33948),w=(t.withCredentials||a(t.url))&&t.xsrfCookieName?v.read(t.xsrfCookieName):void 0;w&&(h[t.xsrfHeaderName]=w)}if("setRequestHeader"in p&&r.forEach(h,(function(t,e){void 0===f&&"content-type"===e.toLowerCase()?delete h[e]:p.setRequestHeader(e,t)})),t.withCredentials&&(p.withCredentials=!0),t.responseType)try{p.responseType=t.responseType}catch(e){if("json"!==t.responseType)throw e}"function"==typeof t.onDownloadProgress&&p.addEventListener("progress",t.onDownloadProgress),"function"==typeof t.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",t.onUploadProgress),t.cancelToken&&t.cancelToken.promise.then((function(t){p&&(p.abort(),l(t),p=null)})),void 0===f&&(f=null),p.send(f)}))}},42537:function(t){"use strict";function e(){this.message="String contains an invalid character"}e.prototype=new Error,e.prototype.code=5,e.prototype.name="InvalidCharacterError",t.exports=function(t){for(var n,r,o=String(t),i="",s=0,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";o.charAt(0|s)||(a="=",s%1);i+=a.charAt(63&n>>8-s%1*8)){if((r=o.charCodeAt(s+=3/4))>255)throw new e;n=n<<8|r}return i}},47763:function(t,e,n){"use strict";var r=n(5449);t.exports=function(t,e,n,o,i){var s=new Error(t);return r(s,e,n,o,i)}},62012:function(t,e,n){"use strict";var r=n(9516);t.exports=function(t){var e,n,o,i={};return t?(r.forEach(t.split("\n"),(function(t){o=t.indexOf(":"),e=r.trim(t.substr(0,o)).toLowerCase(),n=r.trim(t.substr(o+1)),e&&(i[e]=i[e]?i[e]+", "+n:n)})),i):i}},64202:function(t,e,n){"use strict";var r=n(9516);t.exports=r.isStandardBrowserEnv()?function(){var t,e=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(t){var r=t;return e&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return t=o(window.location.href),function(e){var n=r.isString(e)?o(e):e;return n.protocol===t.protocol&&n.host===t.host}}():function(){return!0}},64490:function(t,e,n){"use strict";var r=n(9516),o=n(82881),i=n(93864),s=n(96987);function a(t){t.cancelToken&&t.cancelToken.throwIfRequested()}t.exports=function(t){return a(t),t.headers=t.headers||{},t.data=o(t.data,t.headers,t.transformRequest),t.headers=r.merge(t.headers.common||{},t.headers[t.method]||{},t.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),(t.adapter||s.adapter)(t).then((function(e){return a(t),e.data=o(e.data,e.headers,t.transformResponse),e}),(function(e){return i(e)||(a(t),e&&e.response&&(e.response.data=o(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)}))}},65606:function(t){var e,n,r=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function s(t){if(e===setTimeout)return setTimeout(t,0);if((e===o||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(n){try{return e.call(null,t,0)}catch(n){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:o}catch(t){e=o}try{n="function"==typeof clearTimeout?clearTimeout:i}catch(t){n=i}}();var a,u=[],c=!1,l=-1;function f(){c&&a&&(c=!1,a.length?u=a.concat(u):l=-1,u.length&&h())}function h(){if(!c){var t=s(f);c=!0;for(var e=u.length;e;){for(a=u,u=[];++l<e;)a&&a[l].run();l=-1,e=u.length}a=null,c=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===i||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{return n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(t)}}function p(t,e){this.fun=t,this.array=e}function d(){}r.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];u.push(new p(t,e)),1!==u.length||c||s(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=d,r.addListener=d,r.once=d,r.off=d,r.removeListener=d,r.removeAllListeners=d,r.emit=d,r.prependListener=d,r.prependOnceListener=d,r.listeners=function(t){return[]},r.binding=function(t){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(t){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},69012:function(t){"use strict";t.exports=function(t,e){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return t.apply(e,n)}}},72505:function(t,e,n){t.exports=n(18015)},79106:function(t,e,n){"use strict";var r=n(9516);function o(t){return encodeURIComponent(t).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(t,e,n){if(!e)return t;var i;if(n)i=n(e);else if(r.isURLSearchParams(e))i=e.toString();else{var s=[];r.forEach(e,(function(t,e){null!=t&&(r.isArray(t)&&(e+="[]"),r.isArray(t)||(t=[t]),r.forEach(t,(function(t){r.isDate(t)?t=t.toISOString():r.isObject(t)&&(t=JSON.stringify(t)),s.push(o(e)+"="+o(t))})))})),i=s.join("&")}return i&&(t+=(-1===t.indexOf("?")?"?":"&")+i),t}},82881:function(t,e,n){"use strict";var r=n(9516);t.exports=function(t,e,n){return r.forEach(n,(function(n){t=n(t,e)})),t}},83471:function(t,e,n){"use strict";var r=n(9516);function o(){this.handlers=[]}o.prototype.use=function(t,e){return this.handlers.push({fulfilled:t,rejected:e}),this.handlers.length-1},o.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},o.prototype.forEach=function(t){r.forEach(this.handlers,(function(e){null!==e&&t(e)}))},t.exports=o},84680:function(t){"use strict";t.exports=function(t,e){return e?t.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):t}},87206:function(t){function e(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t){return null!=t&&(e(t)||function(t){return"function"==typeof t.readFloatLE&&"function"==typeof t.slice&&e(t.slice(0,0))}(t)||!!t._isBuffer)}},93864:function(t){"use strict";t.exports=function(t){return!(!t||!t.__CANCEL__)}},96987:function(t,e,n){"use strict";var r=n(65606),o=n(9516),i=n(7018),s={"Content-Type":"application/x-www-form-urlencoded"};function a(t,e){!o.isUndefined(t)&&o.isUndefined(t["Content-Type"])&&(t["Content-Type"]=e)}var u,c={adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==r)&&(u=n(35592)),u),transformRequest:[function(t,e){return i(e,"Content-Type"),o.isFormData(t)||o.isArrayBuffer(t)||o.isBuffer(t)||o.isStream(t)||o.isFile(t)||o.isBlob(t)?t:o.isArrayBufferView(t)?t.buffer:o.isURLSearchParams(t)?(a(e,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):o.isObject(t)?(a(e,"application/json;charset=utf-8"),JSON.stringify(t)):t}],transformResponse:[function(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(t){}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(t){return t>=200&&t<300}};c.headers={common:{Accept:"application/json, text/plain, */*"}},o.forEach(["delete","get","head"],(function(t){c.headers[t]={}})),o.forEach(["post","put","patch"],(function(t){c.headers[t]=o.merge(s)})),t.exports=c}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";var t=n(72505),e=n.n(t),r=n(65606);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(){i=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,s=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function f(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(t){f=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var o=e&&e.prototype instanceof w?e:w,i=Object.create(o.prototype),a=new q(r||[]);return s(i,"_invoke",{value:O(t,n,a)}),i}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var d="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function w(){}function x(){}function b(){}var k={};f(k,u,(function(){return this}));var T=Object.getPrototypeOf,E=T&&T(T(A([])));E&&E!==n&&r.call(E,u)&&(k=E);var L=b.prototype=w.prototype=Object.create(k);function C(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function _(t,e){function n(i,s,a,u){var c=p(t[i],t,s);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"==o(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return n("throw",t,a,u)}))}u(c.arg)}var i;s(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function O(e,n,r){var o=d;return function(i,s){if(o===g)throw Error("Generator is already running");if(o===y){if("throw"===i)throw s;return{value:t,done:!0}}for(r.method=i,r.arg=s;;){var a=r.delegate;if(a){var u=j(a,r);if(u){if(u===v)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var c=p(e,n,r);if("normal"===c.type){if(o=r.done?y:m,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=y,r.method="throw",r.arg=c.arg)}}}function j(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,j(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var i=p(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,v;var s=i.arg;return s?s.done?(n[e.resultName]=s.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function q(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function A(e){if(e||""===e){var n=e[u];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,s=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return s.next=s}}throw new TypeError(o(e)+" is not iterable")}return x.prototype=b,s(L,"constructor",{value:b,configurable:!0}),s(b,"constructor",{value:x,configurable:!0}),x.displayName=f(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,f(t,l,"GeneratorFunction")),t.prototype=Object.create(L),t},e.awrap=function(t){return{__await:t}},C(_.prototype),f(_.prototype,c,(function(){return this})),e.AsyncIterator=_,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var s=new _(h(t,n,r,o),i);return e.isGeneratorFunction(n)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},C(L),f(L,l,"Generator"),f(L,u,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=A,q.prototype={constructor:q,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return a.type="throw",a.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],a=s.completion;if("root"===s.tryLoc)return o("end");if(s.tryLoc<=this.prev){var u=r.call(s,"catchLoc"),c=r.call(s,"finallyLoc");if(u&&c){if(this.prev<s.catchLoc)return o(s.catchLoc,!0);if(this.prev<s.finallyLoc)return o(s.finallyLoc)}else if(u){if(this.prev<s.catchLoc)return o(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return o(s.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=t,s.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),N(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;N(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function s(t,e,n,r,o,i,s){try{var a=t[i](s),u=a.value}catch(t){return void n(t)}a.done?e(u):Promise.resolve(u).then(r,o)}function a(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function a(t){s(i,r,o,a,u,"next",t)}function u(t){s(i,r,o,a,u,"throw",t)}a(void 0)}))}}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach((function(e){l(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function l(t,e,n){return(e=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}Vue.filter("lessonType",(function(t){switch(+t){case 1:return"Từ vựng - Chữ Hán - Ngữ pháp";case 2:return"Đọc hiểu";case 3:return"Nghe hiểu";default:return"Không xác định"}})),Vue.filter("questionSkill",(function(t){switch(+t){case 1:return"Từ vựng";case 2:return"Chữ Hán";case 3:return"Ngữ pháp";case 4:return"Đọc hiểu";case 5:return"Nghe hiểu";default:return"Không skill"}}));var f=e().create({baseURL:"/backend",headers:{"Content-Type":"application/json"},withCredentials:!0});f.interceptors.response.use((function(t){return t}),(function(t){var e=(t.response||{}).status;return 401===e&&(window.location.href="".concat(r.env.APP_URL,"/backend/dang-nhap")),Promise.reject(t)}));var h=new Vue({el:"#exam-detail-lesson",data:{exam:{},types:{1:"Từ vựng - Chữ hán - Ngữ pháp",2:"Đọc hiểu",3:"Nghe hiểu"},N1Types:{1:"Từ vựng - Chữ hán - Ngữ pháp",2:"Đọc hiểu",3:"Nghe hiểu"},active:0,lesson:{questions:[],exams:[]},points:{1:"",2:"",3:""},question:{position:"",point:""},splitTime:!1,timeSplit:[30,30,30],answers:[],answersCount:2,picked:"",minimum_point:19,questionUrl:"",is_content:!1,isMp3SrcValid:!1,mp3Name:"",cloneModal:!1,cloneTargetLessonId:void 0,questionToCloneId:{},showedLessons:[],showExplain:!1,skill:"",skillTypes:{1:"Từ vựng",2:"Chữ Hán",3:"Ngữ pháp",4:"Đọc hiểu",5:"Nghe hiểu"}},computed:{lessons:function(){return this.exam.lessons},mp3Lesson:function(){return _.find(this.exam.lessons,(function(t){return 3==t.type}))},level:function(){return this.exam.course}},watch:{showedLessons:function(t,e){t.length>0&&e.length>0&&this.toggleLesson(t)},splitTime:function(t){this.timeSplit=t?this.exam.time_split?this.exam.time_split:[30,30,30]:null,t||this.saveLessonTime()},"exam.time_split":function(t){this.timeSplit=t,this.splitTime=!!t}},mounted:function(){var t=this;h=this,$.get(window.location.href+"/lessons",(function(t){h.exam=t;var e=[];h.exam.lessons.forEach((function(t){1==t.pivot.status&&e.push(t.id)})),h.showedLessons=e,h.exam.lessons.forEach((function(t){1==t.type&&h.getLesson(t),3==t.type&&(h.mp3Name=t.mp3,setTimeout((function(){document.getElementById("audioSource").setAttribute("src","https://mp3-v2.dungmori.com/"+t.mp3)}),300))}))}));CKEDITOR.replace("question",{filebrowserBrowseUrl:"/backend/ckfinder/browser",filebrowserUploadUrl:"/backend/ckfinder/connector?command=QuickUpload&type=Files",extraPlugins:"maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",allowedContent:!0}),CKEDITOR.replace("explain",{filebrowserBrowseUrl:"/backend/ckfinder/browser",filebrowserUploadUrl:"/backend/ckfinder/connector?command=QuickUpload&type=Files",extraPlugins:"maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",allowedContent:!0});$("#addQuestion").on("hide.bs.modal",(function(){t.resetAddQuestionForm()})),$(document).on("click",".reloadCKeditor",(function(){$(this).ckeditor((function(){}),{on:{blur:function(){this.destroy()}},extraPlugins:"furigana,colorbutton"})})),$.fn.modal&&($.fn.modal.Constructor.prototype.enforceFocus=function(){var t=this;$(document).on("focusin.modal",(function(e){t.$element[0]===e.target||t.$element.has(e.target).length||$(e.target.parentNode).hasClass("cke_dialog_ui_input_select")||$(e.target.parentNode).hasClass("cke_dialog_ui_input_textarea")||$(e.target.parentNode).hasClass("cke_dialog_ui_input_text")||t.$element.focus()}))})},methods:{fixFurigana:function(){$.fn.modal&&($.fn.modal.Constructor.prototype.enforceFocus=function(){var t=this;$(document).on("focusin.modal",(function(e){t.$element[0]===e.target||t.$element.has(e.target).length||$(e.target.parentNode).hasClass("cke_dialog_ui_input_select")||$(e.target.parentNode).hasClass("cke_dialog_ui_input_textarea")||$(e.target.parentNode).hasClass("cke_dialog_ui_input_text")||t.$element.focus()}))})},getLessonName:function(t,e){switch(e.type){case 1:if(["N1","N2","N3"].includes(e.course))switch(+t.type){case 1:return"Từ vựng - Chữ Hán - Ngữ pháp";case 2:return"Đọc hiểu";case 3:return"Nghe hiểu";default:return"Không xác định"}else switch(+t.type){case 1:return"Từ vựng - Chữ Hán - Ngữ pháp - Đọc hiểu";case 2:return"--";case 3:return"Nghe hiểu";default:return"Không xác định"}case 2:switch(+t.type){case 1:return"Từ vựng - Chữ Hán";case 2:return"Ngữ pháp - Đọc hiểu";case 3:return"Nghe hiểu";default:return"Không xác định"}}},setLessonTime:function(t,e){t<1&&(t=1),this.$set(this.timeSplit,e,Number(t))},saveLessonTime:function(){var t=this;$.post(window.location.origin+"/backend/thi-thu/exam/set-lesson-time",{time_split:this.timeSplit,exam_id:this.exam.id},(function(e){200==e.code&&(t.$set(t.exam,"time_split",e.data.time_split),toastr.success("Thay đổi thành công!!"))}))},getLesson:function(t){var e=this,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=$.get(window.location.origin+"/backend/thi-thu/"+this.exam.id+"/lesson/"+t.id+"/detail");return!0===n&&r.done((function(t){e.lesson=t})),r},toggleLesson:function(t){$.post(window.location.origin+"/backend/thi-thu/toggle-lesson",{exam_id:this.exam.id,lessons:t},(function(t){window.location.reload()}))},changeTab:function(t,e){this.active=e,this.getLesson(t)},addAnswer:function(){this.answers.push({content:""})},deleteAnswer:function(){1===this.answers.pop().is_true&&(this.picked="")},addQuestion:function(){for(var t=this,e=this.answers.length,n=0;n<e;n++)this.answers[n].position=n+1,this.picked!==n?this.answers[n].is_true=0:this.answers[n].is_true=1;var r={content:_.replace(CKEDITOR.instances.question.getData(),'<style type="text/css">\x3c!--td {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}--\x3e</style>',""),explain:_.replace(CKEDITOR.instances.explain.getData(),'<style type="text/css">\x3c!--td {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}--\x3e</style>',""),position:this.question.position,picked:this.picked,is_content:this.is_content?1:0,skill:this.skill,level:this.level};this.is_content||(r.answers=this.answers,r.point=this.question.point),$.post(this.questionUrl,r).done((function(){t.getLesson(t.lesson,!1).done((function(e){t.lesson=e,t.exam.lessons[t.lesson.type-1]=c(c({},t.exam.lessons[t.lesson.type-1]),{},{exams:t.lesson.exams,questions:t.lesson.questions}),$("#addQuestion").modal("hide"),t.resetAddQuestionForm()}))})).fail((function(){t.notifyError("Bạn chưa nhập đủ thông tin!")}))},updateText:function(t,e){this.answers[e].content=t.target.innerHTML},updateAnswerPosition:function(t){this.picked===t.oldIndex?this.picked=t.newIndex:this.picked===t.newIndex?t.oldIndex<this.picked?this.picked-=1:this.picked+=1:t.newIndex>this.picked&&t.oldIndex<this.picked?this.picked-=1:t.newIndex<this.picked&&t.oldIndex>this.picked&&(this.picked+=1),this.setAnswersContent()},updateQuestionsPosition:function(){for(var t=this,e=[],n=this.lesson.questions.length,r=0;r<n;r++)e.push({id:this.lesson.questions[r].id,position:r+1});$.ajax({url:window.location.origin+"/backend/thi-thu/lesson/"+this.lesson.id+"/questions/sort",type:"POST",data:JSON.stringify(e),contentType:"application/json"}).done((function(){t.getLesson(t.lesson)}))},calculatePoint:function(t,e){return this.points[e]=t.reduce((function(t,e){return t+e.point}),0),this.points[e]},countQuestions:function(t){return t.filter((function(t){return!t.is_content})).length},resetAddQuestionForm:function(){CKEDITOR.instances.question.setData(""),CKEDITOR.instances.explain.setData(""),this.question.point="",this.question.position="",this.answers=[],this.picked="",this.is_content=!1,this.skill="",this.level=""},setUrlAddQuestion:function(){this.questionUrl=window.location.origin+"/backend/thi-thu/lesson/"+this.lesson.id+"/question";for(var t=0;t<this.answersCount;t++)this.answers.push({content:""})},setUrlEditQuestion:function(t){var e=this;this.question=JSON.parse(JSON.stringify(t)),console.log(t),CKEDITOR.instances.question.setData(this.question.content),CKEDITOR.instances.explain.setData(this.question.explain),this.is_content=t.is_content,this.skill=t.skill,this.level=t.level,this.answers=this.question.answers,this.answers.forEach((function(t,n){1===t.is_true&&(e.picked=n)})),setTimeout((function(){e.setAnswersContent()}),0),this.questionUrl=window.location.origin+"/backend/thi-thu/lesson/questions/"+this.question.id+"/edit",$("#addQuestion").modal("show"),this.fixFurigana()},setAnswersContent:function(){$(".reloadCKeditor").each((function(t){$(this).html(h.answers[t].content)}))},deleteQuestion:function(t){var e=this;bootbox.confirm({message:"Bạn có chắc chắn muốn xoá không?",callback:function(n){n&&$.ajax({url:window.location.origin+"/backend/thi-thu/lesson/questions/"+t.id+"/delete",type:"delete",success:function(){e.lesson.questions=e.lesson.questions.filter((function(e){return e.id!==t.id})),e.updateQuestionsPosition()}})}})},cloneThisQuestion:function(t){this.cloneModal=!0,this.questionToCloneId=t.id},cloneThisQuestionToLesson:function(){var t=this,e={targetLessonId:t.cloneTargetLessonId};$.post(window.location.origin+"/backend/thi-thu/lesson/questions/"+t.questionToCloneId+"/clone",e,(function(e){200===e.code?(toastr.success("Thay đổi thành công!!"),t.closeCloneModal()):toastr.error(e.msg)}))},closeCloneModal:function(){var t=this;t.questionToCloneId=void 0,t.cloneTargetLessonId=void 0,t.cloneModal=!1},updateMp3:function(){var t=this;h=this,this.mp3Lesson&&$.ajax({url:window.location.origin+"/backend/thi-thu/lesson/"+this.mp3Lesson.id+"/mp3",type:"patch",data:{mp3:this.mp3Lesson.mp3},success:function(){document.getElementById("audioSource").setAttribute("src","https://mp3-v2.dungmori.com/"+h.mp3Lesson.mp3),h.mp3Name=h.mp3Lesson.mp3,$("#linkMp3").modal("hide")}}).fail((function(e){return t.notifyError(Object.values(e.responseJSON)[0][0])}))},checkMp3Src:function(){var t=this;if(this.mp3Lesson){var e=new Audio;e.src="https://mp3-v2.dungmori.com/"+this.mp3Lesson.mp3,e.muted=!0,e.play().then((function(){t.isMp3SrcValid=!0,e.pause()})).catch((function(){t.isMp3SrcValid=!1,e.pause(),t.notifyError("Link mp3 không chính xác!")}))}},notifyError:function(t){$.toast({heading:"Lỗi",text:t,showHideTransition:"fade",icon:"error",position:"bottom-right"})},chooseFileAudio:function(){document.getElementById("audio_file").click()},handleFileAudioChange:function(t){var n=this;return a(i().mark((function r(){var o,s,a,u;return i().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!(o=t.target.files[0])){r.next=13;break}return(s=new FormData).append("file_upload",o),r.next=6,f.get("/video/api/get-token").then((function(t){return t.data}));case 6:return a=r.sent,s.append("token",a),r.next=10,e().post("/api/admin/upload-audio",s,{withCredentials:!1,baseURL:videoBaseURL,headers:{"Content-Type":"multipart/form-data"}}).then((function(t){return t.data.data.file_path})).catch((function(t){return console.error(t)}));case 10:u=r.sent,n.mp3Lesson.mp3=u,n.updateMp3();case 13:case"end":return r.stop()}}),r)})))()},importTest:function(t){return a(i().mark((function e(){var n,r,o;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.target.files[0]){e.next=4;break}return alert("Vui lòng chọn một tệp để tải lên."),e.abrupt("return");case 4:return(r=new FormData).append("file",n),r.append("_token",$('meta[name="csrf-token"]').attr("content")),e.prev=7,e.next=10,f.post("/thi-thu/exam/import-test",r);case 10:if(o=e.sent,console.log("Response:",o),200==o.status){e.next=14;break}throw new Error("HTTP error! status: ".concat(o.status));case 14:alert("Tải lên thành công! Xin hãy chờ một lúc rồi kiểm tra lại"),e.next=20;break;case 17:e.prev=17,e.t0=e.catch(7),console.error("Error:",e.t0);case 20:case"end":return e.stop()}}),e,null,[[7,17]])})))()}}})}()}();