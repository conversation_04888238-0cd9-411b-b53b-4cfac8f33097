/*! For license information please see lesson-component.js.LICENSE.txt */
!function(){var e={};function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function a(){"use strict";a=function(){return n};var e,n={},o=Object.prototype,i=o.hasOwnProperty,s=Object.defineProperty||function(e,t,a){e[t]=a.value},l="function"==typeof Symbol?Symbol:{},r=l.iterator||"@@iterator",d=l.asyncIterator||"@@asyncIterator",c=l.toStringTag||"@@toStringTag";function p(e,t,a){return Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,a){return e[t]=a}}function u(e,t,a,n){var o=t&&t.prototype instanceof $?t:$,i=Object.create(o.prototype),l=new K(n||[]);return s(i,"_invoke",{value:T(e,a,l)}),i}function _(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}n.wrap=u;var m="suspendedStart",f="suspendedYield",v="executing",h="completed",g={};function $(){}function k(){}function b(){}var y={};p(y,r,(function(){return this}));var w=Object.getPrototypeOf,D=w&&w(w(F([])));D&&D!==o&&i.call(D,r)&&(y=D);var x=b.prototype=$.prototype=Object.create(y);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,a){function n(o,s,l,r){var d=_(e[o],e,s);if("throw"!==d.type){var c=d.arg,p=c.value;return p&&"object"==t(p)&&i.call(p,"__await")?a.resolve(p.__await).then((function(e){n("next",e,l,r)}),(function(e){n("throw",e,l,r)})):a.resolve(p).then((function(e){c.value=e,l(c)}),(function(e){return n("throw",e,l,r)}))}r(d.arg)}var o;s(this,"_invoke",{value:function(e,t){function i(){return new a((function(a,o){n(e,t,a,o)}))}return o=o?o.then(i,i):i()}})}function T(t,a,n){var o=m;return function(i,s){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===i)throw s;return{value:e,done:!0}}for(n.method=i,n.arg=s;;){var l=n.delegate;if(l){var r=E(l,n);if(r){if(r===g)continue;return r}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var d=_(t,a,n);if("normal"===d.type){if(o=n.done?h:f,d.arg===g)continue;return{value:d.arg,done:n.done}}"throw"===d.type&&(o=h,n.method="throw",n.arg=d.arg)}}}function E(t,a){var n=a.method,o=t.iterator[n];if(o===e)return a.delegate=null,"throw"===n&&t.iterator.return&&(a.method="return",a.arg=e,E(t,a),"throw"===a.method)||"return"!==n&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=_(o,t.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,g;var s=i.arg;return s?s.done?(a[t.resultName]=s.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,g):s:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function K(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function F(a){if(a||""===a){var n=a[r];if(n)return n.call(a);if("function"==typeof a.next)return a;if(!isNaN(a.length)){var o=-1,s=function t(){for(;++o<a.length;)if(i.call(a,o))return t.value=a[o],t.done=!1,t;return t.value=e,t.done=!0,t};return s.next=s}}throw new TypeError(t(a)+" is not iterable")}return k.prototype=b,s(x,"constructor",{value:b,configurable:!0}),s(b,"constructor",{value:k,configurable:!0}),k.displayName=p(b,c,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,p(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},n.awrap=function(e){return{__await:e}},C(j.prototype),p(j.prototype,d,(function(){return this})),n.AsyncIterator=j,n.async=function(e,t,a,o,i){void 0===i&&(i=Promise);var s=new j(u(e,t,a,o),i);return n.isGeneratorFunction(t)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},C(x),p(x,c,"Generator"),p(x,r,(function(){return this})),p(x,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),a=[];for(var n in t)a.push(n);return a.reverse(),function e(){for(;a.length;){var n=a.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=F,K.prototype={constructor:K,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var a in this)"t"===a.charAt(0)&&i.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function n(n,o){return l.type="throw",l.arg=t,a.next=n,o&&(a.method="next",a.arg=e),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],l=s.completion;if("root"===s.tryLoc)return n("end");if(s.tryLoc<=this.prev){var r=i.call(s,"catchLoc"),d=i.call(s,"finallyLoc");if(r&&d){if(this.prev<s.catchLoc)return n(s.catchLoc,!0);if(this.prev<s.finallyLoc)return n(s.finallyLoc)}else if(r){if(this.prev<s.catchLoc)return n(s.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return n(s.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),O(a),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var n=a.completion;if("throw"===n.type){var o=n.arg;O(a)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,a,n){return this.delegate={iterator:F(t),resultName:a,nextLoc:n},"next"===this.method&&(this.arg=e),g}},n}function n(e,t,a,n,o,i,s){try{var l=e[i](s),r=l.value}catch(e){return void a(e)}l.done?t(r):Promise.resolve(r).then(n,o)}function o(e){return function(){var t=this,a=arguments;return new Promise((function(o,i){var s=e.apply(t,a);function l(e){n(s,o,i,l,r,"next",e)}function r(e){n(s,o,i,l,r,"throw",e)}l(void 0)}))}}function i(e,a,n){return(a=function(e){var a=function(e,a){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,a||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==t(a)?a:a+""}(a))in e?Object.defineProperty(e,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[a]=n,e}e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var s={filebrowserBrowseUrl:"/backend/ckfinder/browser",filebrowserUploadUrl:"/backend/ckfinder/connector?command=QuickUpload&type=Files",extraPlugins:"stylescombo,maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",fontSize_sizes:"8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;64/64px;72/72px;",allowedContent:{$1:{elements:CKEDITOR.dtd,attributes:!0,styles:!0,classes:!0}},disallowedContent:"script;style; *[on*]",stylesSet:[{name:"Overline Text",element:"span",attributes:{style:"text-decoration: overline;"}}]};$(document).ready((function(){$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),$("#lesson_id").val()>0?$("#content_tab").prop("disabled",!1):$("#content_tab").prop("disabled",!0),$(document).on("click",".question_item_header_add_content",(function(){add_conjunction("new")})),$(document).on("click",".btn_sentence_jumble_item_add_content",(function(){add_sentence_jumble("new")})),$(document).on("click",".btn_sentence_jumble_item_add_content_false",(function(){add_item_sentence_jumble_false("new")})),$(document).on("click",".btn_sentence_jumble_item_del_content",(function(){E>2&&($(".item_component_sentence_jumble[data-number=".concat(E,"]")).remove(),T=T.filter((function(e){return e!=="lesson[value][".concat(E,"]")})),E--)})),$(document).on("click",".btn_sentence_jumble_item_del_content_false",(function(){$(".item_component_sentence_jumble_false[data-number=".concat(I,"]")).remove(),T=T.filter((function(e){return e!=="lesson[item_false][".concat(I,"]")})),I--})),$(document).on("click",".btn_word_pair_matching_item_add_content",(function(){add_word_pair_matching("new")})),$(document).on("click",".btn_word_pair_matching_item_del_content",(function(){if(K>2){$(".word_pair_matching_item[data-number=".concat($(this).data("number"),"]")).remove(),K--;var e="lesson[value][".concat($(this).data("number"),"][left]"),t="lesson[value][".concat($(this).data("number"),"][right]");CKEDITOR.instances[e].destroy(!0),CKEDITOR.instances[t].destroy(!0),O=O.filter((function(a){return a!==e&&a!==t}))}})),$(document).on("click",".question_item_header_del_content",(function(){D>0&&(console.log("CKEDITOR.instances[lesson[item][".concat(D,"][value]: "),CKEDITOR.instances["lesson[item][".concat(D,"][value]")]),CKEDITOR.instances["lesson[item][".concat(D,"][value]")].destroy(!0),$("#result"+D).remove(),D--,C.splice(D,1))}))}));var l=CKEDITOR.replace("task_choice",s),r=CKEDITOR.replace("suggest",s),d=CKEDITOR.replace("explain",s),c=CKEDITOR.replace("task_content",s),p=CKEDITOR.replace("lesson_suggest_conjunction",s),u=CKEDITOR.replace("lesson_explain_conjunction",s),_=CKEDITOR.replace("lesson_suggest_sentence_jumble",s),m=CKEDITOR.replace("lesson_explain_sentence_jumble",s),f=CKEDITOR.replace("lesson_question_sentence_jumble",s),v=CKEDITOR.replace("lesson_explain_word_pair_matching",s),h=CKEDITOR.replace("lesson_question_speaking",s),g=CKEDITOR.replace("name_html",s);lesson&&lesson.name_html&&g.setData(lesson.name_html),CKFinder.setupCKEditor(l),CKFinder.setupCKEditor(r),CKFinder.setupCKEditor(d),CKFinder.setupCKEditor(c),CKFinder.setupCKEditor(p),CKFinder.setupCKEditor(u),CKFinder.setupCKEditor(_),CKFinder.setupCKEditor(m),CKFinder.setupCKEditor(f),CKFinder.setupCKEditor(v),CKFinder.setupCKEditor(h);var k=1,b=1,y=1,w=1,D=0,x=[],C=[],j=[],T=[],E=2,I=0,O=[],K=0;sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"),$("#task-type").val(lesson_task.length>0?lesson_task[lesson_task.length-1].type:1),e.g.selectGroupByCourse=function(e){var t=$("#course").val();$("#group").empty(),$.ajax({type:"post",url:"/backend/group-by-course",data:{_token:$("input[name=_token]").val(),course_id:t},success:function(t){var a;t.errors||($.each(t.group,(function(t,n){e==n.id?a+='<option value="'+n.id+'" selected>'+n.name+"</option>":a+='<option value="'+n.id+'">'+n.name+"</option>"})),$("#group").append(a))}})},e.g.previewLesson=function(){var e=document.querySelector("#image_lesson"),t=document.querySelector("#img_lesson").files[0],a=new FileReader;if(a.onloadend=function(){e.src=a.result},t){var n=t.name,o=n.substring(n.lastIndexOf(".")+1).toLowerCase();t.size/1e6>3?(alert("Ảnh vượt quá dung lượng"),e.src=noImageUrl,$("#img_lesson").val("")):"gif"!=o&&"jpg"!=o&&"jpeg"!=o&&"png"!=o&&"svg"!=o?(alert("Ảnh không đúng định dạng"),e.src=noImageUrl,$("#img_lesson").val("")):a.readAsDataURL(t)}else e.src=noImageUrl},$("input[name=is_examination]:checked").length?$(".refer_total_marks").slideDown("250"):$(".refer_total_marks").slideUp("250"),$("input[name=is_examination]").on("change",(function(){$("input[name=is_examination]:checked").length?$(".refer_total_marks").slideDown("250"):$(".refer_total_marks").slideUp("250")})),$("input[name=total_marks_calculation]:checked").length?$("#total_marks").attr("disabled",!0):$("#total_marks").attr("disabled",!1),$("input[name=total_marks_calculation]").on("change",(function(){$("input[name=total_marks_calculation]:checked").length?$("#total_marks").attr("disabled",!0):$("#total_marks").attr("disabled",!1)})),e.g.displayTask=function(e){var t,a,n,o;switch($(".global_error").addClass("hidden"),console.log("displayTask: ",e),e){case 2:showFormById("#body_video"),$("#link_video").val(""),$("#title_video").val(""),$("#video_sort").val(""),$("#video_show_on").prop("checked",!0),manipulation("video_add");break;case 5:showFormById("#body_mp3"),$("#mp3_descr").val(""),$("#mp3_link").val(""),$("#mp3_sort").val(""),$("#mp3_show_on").prop("checked",!0),$("#mp3MondaiName").val(lesson_task.length?null===(t=lesson_task[lesson_task.length-1].mondai)||void 0===t?void 0:t.title:""),manipulation("mp3_add");break;case 1:showFormById("#body_content"),$("#content_sort").val(""),$("#content_show_on").prop("checked",!0),$("#contentMondaiName").val(lesson_task.length?null===(a=lesson_task[lesson_task.length-1].mondai)||void 0===a?void 0:a.title:""),manipulation("content_add");break;case 3:if(showFormById("#body_multi_choice"),$("#choice_sort").val(""),$("#choice_grade").val(""),$(".answer_content").empty().html(),$(".feedback_add").empty().html(),$("#choice_show_on").prop("checked",!0),$("#choiceMondaiName").val(lesson_task.length?null===(n=lesson_task[lesson_task.length-1].mondai)||void 0===n?void 0:n.title:""),manipulation("choice_add"),b=1,F&&F.length>=1)for(var i=0;i<F.length;i++)CKEDITOR.instances["answer_content_"+(i+1)].destroy(!0);F=[],y=1;break;case 6:if(showFormById("#body_answer"),$(".faq_content_wraper").empty().html(),$("#faq_sort").val(""),$("#faq_grade").val(""),$("#faq_show_on").prop("checked",!0),manipulation("faq_add"),k=1,j.length>=1)for(i=0;i<j.length;i++)CKEDITOR.instances["faq_question_"+(i+1)].destroy(!0);j=[];break;case 8:showFormById("#body_pdf"),$("#body_pdf").data("type","add"),$("#task_submit").data("type","pdf_upload"),$("#progress-pdf-upload").hide(),manipulation("pdf_add"),$("#pdfFilename").val("");break;case 7:showFormById("#body_audio"),$("#body_audio").data("type","add"),$("#task_submit").data("type","audio"),manipulation("audio_add"),$("#audio_filename").val(""),$("#audio_text").val(""),$("#is_kaiwa2").prop("checked",!1);break;case 9:showFormById("#body_flashcard"),$("#body_flashcard").data("type","add"),$("#task_submit").data("type","audio_upload"),$("#progress-pdf-upload").hide(),manipulation("flashcard_add"),$("#pdfFilename").val("");break;case 13:showFormById("#body_conjunction"),D=0,C=[],resetCkeditorConjunction(),$("#title_question_conjunction").val(""),$(".flc_img_preview").empty(),$("#audio_preview_conjunction").empty(),$("#audio_preview_explain_mp3_conjunction").empty(),$("#text_vi_conjunction").val(""),$(".question_item_content").empty(),$("#conjunctionMondaiName").val(lesson_task.length?null===(o=lesson_task[lesson_task.length-1].mondai)||void 0===o?void 0:o.title:""),manipulation("conjunction_add");break;case 14:resetFormSentenceJumble("add"),showFormById("#body_sentence_jumble"),manipulation("sentence_jumble_add");break;case 15:resetFormWordPairMatching("add"),showFormById("#body_word_pair_matching"),manipulation("word_pair_matching_add");break;case 16:resetFormSpeaking("add"),showFormById("#body_speaking"),manipulation("speaking_add");break;default:if(showFormById("#body_result"),$(".result_content_wraper").empty().html(),$("#result_sort").val(""),$("#result_show_on").prop("checked",!0),manipulation("result_add"),w=1,x.length>=1)for(var s=0;s<x.length;s++)CKEDITOR.instances["result_content_"+(s+1)].destroy(!0);x=[]}},e.g.showFormById=function(e){$(".form-lesson-component").hide(),$(e).show()},e.g.hideAllForm=function(){$(".form-lesson-component").hide()},e.g.manipulation=function(e){var t=["video_edit","video_add","mp3_add","mp3_edit","content_add","content_edit","choice_add","choice_edit","faq_add","faq_edit","result_add","result_edit","pdf_add","pdf_edit","quiz_add","quiz_edit","gap_fill_add","gap_fill_edit","conjunction_add","conjunction_edit","sentence_jumble_add","sentence_jumble_edit","word_pair_matching_add","word_pair_matching_edit","speaking_add","speaking_edit"].join(" ");$(".actionBtn").removeClass(t),$(".actionBtn").addClass(e)},$(document).on("click","#task-add",(function(){Object.assign(vueInstance.$data,originalData),vueInstance.$data.filters.createdAt="desc",vueInstance.$data.filters.name="",vueInstance.$data.isAddVideo=!0,$("#task_submit").data("type",""),setTimeout((function(){$("#get-videos-vn").click()}),0);var e=$("#task-type").val();17!=e?(displayTask(parseInt(e)),c.setData(""),l.setData(""),r.setData(""),d.setData(""),$("#audio_preview").empty(),u.setData(""),p.setData(""),f.setData(""),_.setData(""),m.setData(""),v.setData(""),h.setData(""),$("#lesson_component_id").val(""),$("#pageModal").modal("show")):window.open("/backend/flashcard/create?lesson_id="+lesson.id,"_blank")}));var F=[];e.g.add_answer_option=function(e){var t=$(document.createElement("div")).attr("id","answer"+b);if(t.after().html('<div class="col-sm-12" style="margin-bottom: 10px;">\n      <textarea class="form-control" id="answer_content_'.concat(b,'" name="answer_content_').concat(b,'" placeholder="Đáp án ').concat(b,'"></textarea>\n      <span class="btn btn-xs btn-info btn-add-mp3-answer-content" data-id="').concat(b,'"><i class="fa fa-volume-down"></i> Thêm mp3</span>\n      <label class="mt5">\n        <span>Điểm: </span>\n      </label>\n      <input type="number" id="answer_grade_').concat(b,'" name="answer_grade_').concat(b,'" value="0" style="width: 60px;">\n      </div>')),t.appendTo(".answer_content"),"new"==e){var a=CKEDITOR.replace("answer_content_"+b,s);CKFinder.setupCKEditor(a)}F.push("answer_content_"+b),b++},$(document).on("click",".btn-add-mp3-answer-content",(function(){var e=$(this).data("id"),t=document.createElement("input");t.type="file",t.accept="audio/mp3",t.style.display="none",t.onchange=function(t){var a=t.target.files[0];if(a){var n=new FileReader;n.onload=function(t){uploadAudio(a,(function(t){CKEDITOR.instances["answer_content_"+e].insertHtml("{! ".concat(t.data.file_path," !}"))}))},n.readAsDataURL(a)}},document.body.appendChild(t),t.click()})),e.g.del_answer_option=function(){b>1&&(b--,CKEDITOR.instances["answer_content_"+b].destroy(!0),F.splice(b-1,1),$("#answer"+b).remove())},e.g.add_feedback_option=function(){var e=$(document.createElement("div")).attr("id","feedback"+y);e.after().html('<div class="col-sm-12"><span>Điểm yêu cầu: </span><input type="number" id="feedback_grade_'+y+'" name="feedback_grade_'+y+'" style="margin-bottom: 4px; width: 60px;"><textarea class="form-control" placeholder="Phản hồi'+y+'" id="feedback_content_'+y+'" name="feedback_content_'+y+'" style="margin-bottom: 10px;"></textarea><hr></div>'),e.appendTo(".feedback_add"),y++},e.g.del_feedback_option=function(){y>1&&(y--,$("#feedback"+y).remove())},e.g.add_faq=function(e){var t=$(document.createElement("div")).attr("id","faq"+k);if(t.after().html('<div class="row" style="margin: 10px 0px;"><textarea id="faq_question_'+k+'" name="faq_question_'+k+'" class="form-control" style="height: 80px; "></textarea><input type="text" id="faq_content_'+k+'" name="faq_content_'+k+'" class="form-control" placeholder="Đáp án '+k+'"><input type="number" id="faq_grade_'+k+'" name="faq_grade_'+k+'"class="form-control" placeholder="Điểm "></div>'),t.appendTo(".faq_content_wraper"),"new"==e){var a=CKEDITOR.replace("faq_question_"+k,s);CKFinder.setupCKEditor(a)}j.push("faq_question_"+k),k++},e.g.del_faq=function(){k>1&&(k--,CKEDITOR.instances["faq_question_"+k].destroy(!0),j.splice(k-1,1),$("#faq"+k).remove())},e.g.add_result=function(e){var t=$(document.createElement("div")).attr("id","result"+w);if(t.after().html('<input type="text" id="result_title_'+w+'" name="result_title_'+w+'" class="form-control"><textarea id="result_content_'+w+'" name="result_content_'+w+'" class="form-control" style="height: 120px;"></textarea>'),t.appendTo(".result_content_wraper"),"new"==e){var a=CKEDITOR.replace("result_content_"+w,s);CKFinder.setupCKEditor(a)}x.push("result_content_"+w),w++},e.g.del_result=function(){w>1&&(w--,CKEDITOR.instances["result_content_"+w].destroy(!0),x.splice(w-1,1),$("#result"+w).remove())};var q="";$(document).on("click",".edit-task",(function(){vueInstance.$data.isAddVideo=!1,setTimeout((function(){$("#get-videos-vn").click()}),0);var e=$(this).data("info");$.ajax({type:"get",url:"/backend/task/"+e,success:function(e){displayTask(e[0].type),fillmodalData(e[0]),q=e[0].video_name}}),$("#pageModal").modal("show")})),e.g.fillmodalData=function(e){var t,a,n,o,i,_,m,f;switch(e.type){case 5:manipulation("mp3_edit"),$("#mp3_id").val(e.id),$("#mp3MondaiPart").val(null===(t=e.part)||void 0===t?void 0:t.type),$("#mp3MondaiName").val(null===(a=e.mondai)||void 0===a?void 0:a.title),$("#mp3Skill").val(e.skill);var v=JSON.parse(e.value);$("#mp3_descr").val(v.name),$("#mp3_link").val(v.link),1==e.show?$("#mp3_show_on").prop("checked",!0):$("#mp3_show_off").prop("checked",!0);break;case 3:if(manipulation("choice_edit"),$("#choice_id").val(e.id),$("#part").val(null===(n=e.part)||void 0===n?void 0:n.type),$("#mondaiName").val(null===(o=e.mondai)||void 0===o?void 0:o.title),$("#skill").val(e.skill),l.setData(e.value),r.setData(e.suggest),d.setData(e.explain),$("#audio_preview").empty(),e.explain_mp3&&e.explain_mp3.length>0){var h='<audio controls controlslist="nodownload">\n          <source id="source_audio" src="/cdn/audio/'.concat(e.explain_mp3,'" type="audio/mp3">\n        </audio>');$("#audio_preview").append(h)}$("#choice_grade").val(e.grade),null==e.type_ld?$("#typeld").val(""):$("#typeld").val(e.type_ld),1==e.show?$("#choice_show_on").prop("checked",!0):$("#choice_show_off").prop("checked",!0),$.ajax({type:"post",url:"/backend/lesson/task/answer",data:{id:e.id},success:function(e){e.errors||$.each(e,(function(e,t){add_answer_option("edit");var a=CKEDITOR.replace("answer_content_"+(b-1),s).setData(t.value);CKFinder.setupCKEditor(a),$("#answer_grade_"+(b-1)).val(t.grade)}))}});break;case 1:manipulation("content_edit"),$("#content_id").val(e.id),$("#contentMondaiPart").val(null===(i=e.part)||void 0===i?void 0:i.type),$("#contentMondaiName").val(null===(_=e.mondai)||void 0===_?void 0:_.title),$("#contentSkill").val(e.skill),$("#skill").val(e.skill),c.setData(e.value),1==e.show?$("#content_show_on").prop("checked",!0):$("#content_show_off").prop("checked",!0),null==e.type_ld?$("#typeld").val(""):$("#typeld").val(e.type_ld);break;case 2:manipulation("video_edit"),$("#video_id").val(e.id),$("#link_video").val(e.video_name),$("#title_video").val(e.video_title),1==e.show?$("#video_show_on").prop("checked",!0):$("#video_show_off").prop("checked",!0),vueInstance.$data.videoFull=e.video_full;break;case 4:manipulation("result_edit"),$("#result_id").val(e.id),$.each(JSON.parse(e.value),(function(e,t){w=t.pos,add_result("edit");var a=CKEDITOR.replace("result_content_"+t.pos,s).setData(t.content);CKFinder.setupCKEditor(a),$("#result_title_"+t.pos).val(t.name)})),1==e.show?$("#result_show_on").prop("checked",!0):$("#result_show_off").prop("checked",!0);break;case 7:$("#body_audio").data("type","edit"),$("#task_submit").data("type","audio"),manipulation("audio_edit");var g=JSON.parse(e.value);$("#audio_filename").val(g.link),$("#audio_text").val(g.name),$("#audio_filename").data("id",e.id),1==g.type?$("#is_kaiwa2").prop("checked",!0):$("#is_kaiwa2").prop("checked",!1),1==e.show?$("#audio_show_on").prop("checked",!0):$("#audio_show_off").prop("checked",!0);break;case 8:$("#body_pdf").data("type","edit"),$("#task_submit").data("type","pdf_upload"),$("#progress-pdf-upload").hide(),manipulation("pdf_edit"),$("#pdfFilename").val(e.value),$("#pdfFilename").data("id",e.id),$("#pdfFilename").data("old-file",e.value),1==e.show?$("#pdf_show_on").prop("checked",!0):$("#pdf_show_off").prop("checked",!0);break;case 9:break;case 13:var y=JSON.parse(e.value);if(console.log("value: ",y),C=[],resetCkeditorConjunction(),$("#title_question_conjunction").val(y.title_question),$("#type_question_conjunction").val(y.type_question),$("#grade_question_conjunction").val(e.grade),$("input:radio[name=conjunction_show]").filter("[value=".concat(e.show,"]")).prop("checked",!0),$(".question_item_content").empty(),Object.keys(y.question).forEach((function(e){console.log(e,y.question[e]),D=e,add_conjunction("edit"),CKEDITOR.instances["lesson[item][".concat(D,"][value]")].setData(y.question[e].value),$("input[name='lesson[item][".concat(D,"][type]'][value=").concat(y.question[e].type,"]")).prop("checked",!0)})),$(".flc_img_preview").empty(),null!==y.img&&""!==y.img){var x='<img src="/cdn/lesson/default/'.concat(y.img,'" width="64px" class="img-rounded">');$(".flc_img_preview").append(x)}if($("#lesson_component_id").val(e.id),$("#audio_preview_conjunction").empty(),console.log("value______________: ",y,""!=y.audio,null!=y.audio,y.audio.length>0),""!=y.audio&&null!=y.audio&&y.audio.length>0){var j='<audio controls controlslist="nodownload">\n                                    <source id="source_audio" src="/cdn/audio/'.concat(y.audio,'"\n                                            type="audio/mp3">\n                                </audio>');console.log("contentAudio: ",j),$("#audio_preview_conjunction").append(j)}if($("#audio_preview_explain_mp3_conjunction").empty(),console.log("value______________: ",y,""!=y.audio,null!=y.audio,y.audio.length>0),""!=e.explain_mp3&&null!=e.explain_mp3&&e.explain_mp3.length>0){var T='<audio controls controlslist="nodownload">\n                                    <source id="source_audio" src="/cdn/audio/'.concat(e.explain_mp3,'"\n                                            type="audio/mp3">\n                                </audio>');console.log("contentAudio: ",T),$("#audio_preview_explain_mp3_conjunction").append(T)}$("#grade_question_conjunction").val(e.grade),$("#text_vi_conjunction").empty(),console.log(1123123123123123e10,y),$("#text_vi_conjunction").val(y.text_vi),$("#text_vi_conjunction").empty(),console.log(1123123123123123e10,y),$("#text_vi_conjunction").val(y.text_vi),$("#conjunctionPart").val(null===(m=e.part)||void 0===m?void 0:m.type),$("#conjunctionMondaiName").val(null===(f=e.mondai)||void 0===f?void 0:f.title),$("#conjunctionSkill").val(e.skill),p.setData(y.suggest),u.setData(y.explain),console.log("value.suggest: ",y.suggest),manipulation("conjunction_edit");break;case 14:resetFormSentenceJumble("edit"),manipulation("sentence_jumble_edit"),fillDataSentenceJumble(e);break;case 15:resetFormWordPairMatching("edit"),fillDataWordPairMatching(e),manipulation("word_pair_matching_edit");break;case 16:console.log("task: ",e),resetFormSpeaking("edit"),fillDataSpeaking(e),manipulation("speaking_edit");break;default:manipulation("faq_edit"),$("#faq_id").val(e.id),$.each(JSON.parse(e.value),(function(e,t){add_faq("edit");var a=CKEDITOR.replace("faq_question_"+(k-1),s).setData(t.question);CKFinder.setupCKEditor(a),$("#faq_grade_"+(k-1)).val(t.grade),$("#faq_content_"+(k-1)).val(t.answer)})),$("#faq_grade").val(e.grade),1==e.show?$("#faq_show_on").prop("checked",!0):$("#faq_show_off").prop("checked",!0)}},$(document).on("click","#btn-choose-video",(function(){window.open("/backend/video?type=choose","Chọn video")})),e.g.setUrlVideo=function(e){$("#link_video").val(e)},$("#new-footer").on("click",".video_add",(function(){var e=i(i({_token:$("input[name=_token]").val(),link:$("#link_video").val(),title:$("#title_video").val(),sort:$("#video_sort").val(),type:2,lesson_id:$("#lesson_id").val(),show:$("input[name=video_show]:checked").val()},"sort",$("#task_table tbody tr").length+1),"video_full",vueInstance.$data.videoFull);axios.post("/backend/lesson/task/video/add",e).then((function(e){e.data.errors?Object.keys(e.data.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e.data),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}))})),$("#new-footer").on("click",".video_edit",(function(){var e={_token:$("input[name=_token]").val(),id:$("#video_id").val(),link:$("#link_video").val(),title:$("#title_video").val(),sort:$("#video_sort").val(),lesson_id:$("#lesson_id").val(),show:$("input[name=video_show]:checked").val(),old_video_name:q,video_full:vueInstance.$data.videoFull};axios.post("/backend/lesson/task/video/edit",e).then((function(e){e.data.errors?Object.keys(e.data.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e.data),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}))})),$(document).on("click",".delete-task",(function(){var e=$(this).data("info");$(".deleteBtn").addClass("task_delete"),$(".task_id").text(e),$("#lessModal").modal("show")})),$("#delete-footer").on("click",".task_delete",(function(){$.ajax({type:"post",url:"/backend/lesson/task/video/delete",data:{_token:$("input[name=_token]").val(),id:$(".task_id").text()},beforeSend:function(){$(".preloader").show()},complete:function(){$(".preloader").hide()},success:function(e){$(".item"+$(".task_id").text()).remove()}})})),$(document).on("click","#btn-add-mp3-link",(function(){$("#audio_file_mp3_type").trigger("click")})),$("#audio_file_mp3_type").on("change",(function(){var e=this.files[0],t=new FileReader;$("#mp3_link").val(""),t.onload=function(e){uploadAudio($("#audio_file_mp3_type")[0].files[0],(function(e){$("#mp3_link").val(e.data.file_path)}))},e&&t.readAsDataURL(e)})),e.g.uploadAudio=function(e,t){$.ajax({type:"get",url:"/backend/video/api/get-token",success:function(a){var n=new FormData;n.append("token",a),n.append("file_upload",e),$.ajax({type:"post",url:videoBaseURL+"/api/admin/upload-audio",processData:!1,contentType:!1,data:n,success:function(e){t&&t(e)},error:function(e){alert("Lỗi upload file")}})}})},$("#new-footer").on("click",".mp3_add",o(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:$.ajax({type:"post",url:"/backend/lesson/task/mp3/add",data:{_token:$("input[name=_token]").val(),description:$("#mp3_descr").val(),type:5,link:$("#mp3_link").val(),lesson_id:$("#lesson_id").val(),show:$("input[name=mp3_show]:checked").val(),sort:$("#task_table tbody tr").length+1,partId:$("#partId").val(),part:$("#mp3MondaiPart").val(),mondaiId:$("#mp3MondaiId").val(),mondaiName:$("#mp3MondaiName").val(),skill:$("#mp3Skill").val()},success:function(e){e.errors?Object.keys(e.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}});case 1:case"end":return e.stop()}}),e)})))),$("#new-footer").on("click",".mp3_edit",(function(){$.ajax({type:"post",url:"/backend/lesson/task/mp3/edit",data:{_token:$("input[name=_token]").val(),id:$("#mp3_id").val(),description:$("#mp3_descr").val(),sort:$("#mp3_sort").val(),link:$("#mp3_link").val(),lesson_id:$("#lesson_id").val(),show:$("input[name=mp3_show]:checked").val(),partId:$("#partId").val(),part:$("#mp3MondaiPart").val(),mondaiId:$("#mp3MondaiId").val(),mondaiName:$("#mp3MondaiName").val(),skill:$("#mp3Skill").val()},success:function(e){e.errors?Object.keys(e.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".content_add",(function(){$.ajax({type:"post",url:"/backend/lesson/task/content/add",data:i(i(i(i(i(i({_token:$("input[name=_token]").val(),content:c.getData(),sort:$("#content_sort").val(),type:1,type_ld:$("#typeld").val(),lesson_id:$("#lesson_id").val(),show:$("input[name=content_show]:checked").val()},"sort",$("#task_table tbody tr").length+1),"partId",$("#partId").val()),"part",$("#contentMondaiPart").val()),"mondaiId",$("#mondaiId").val()),"mondaiName",$("#contentMondaiName").val()),"skill",$("#contentSkill").val()),success:function(e){e.errors?Object.keys(e.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".content_edit",(function(){$.ajax({type:"post",url:"/backend/lesson/task/content/edit",data:{_token:$("input[name=_token]").val(),id:$("#content_id").val(),content:c.getData(),type_ld:$("#typeld").val(),sort:$("#content_sort").val(),lesson_id:$("#lesson_id").val(),show:$("input[name=content_show]:checked").val(),partId:$("#partId").val(),part:$("#contentMondaiPart").val(),mondaiId:$("#contentMondaiId").val(),mondaiName:$("#contentMondaiName").val(),skill:$("#contentSkill").val()},success:function(e){e.errors?Object.keys(e.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$(document).on("click","#btn-add-mp3",(function(){$("#audio_file_multi_choice").trigger("click")})),$("#audio_file_multi_choice").on("change",(function(){var e=this.files[0],t=new FileReader;t.onload=function(t){var a=CKEDITOR.instances.task_choice.getData();uploadAudio(e,(function(e){var t="{! ".concat(e.data.file_path," !}");l.setData(a+t)}))},e&&t.readAsDataURL(e)})),$("#new-footer").on("click",".choice_add",(function(){var e=new FormData($("#form_task")[0]);e.append("task_choice",CKEDITOR.instances.task_choice.getData()),e.append("suggest",CKEDITOR.instances.suggest.getData()),e.append("explain",CKEDITOR.instances.explain.getData()),$.each(F,(function(t,a){e.delete(a),e.append(a,CKEDITOR.instances[a].getData())})),e.append("sort",$("#task_table tbody tr").length+1),$.ajax({type:"post",url:"/backend/lesson/task/choice/add",processData:!1,contentType:!1,data:e,success:function(e){e.errors?Object.keys(e.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#audio_preview").empty(),$("#audio_file_explain").val(""),$("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".choice_edit",(function(){var e=new FormData($("#form_task")[0]);$.each(F,(function(t,a){e.delete(a),e.append(a,CKEDITOR.instances[a].getData())})),e.append("task_choice",CKEDITOR.instances.task_choice.getData()),e.append("suggest",CKEDITOR.instances.suggest.getData()),e.append("explain",CKEDITOR.instances.explain.getData()),$.ajax({type:"post",url:"/backend/lesson/task/choice/edit",processData:!1,contentType:!1,data:e,success:function(e){e.errors?Object.keys(e.errors).length>0&&($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#audio_preview").empty(),$("#audio_file_explain").val(""),$("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".faq_add",(function(){var e=new FormData($("#form_task")[0]);$.each(j,(function(t,a){e.append(a,CKEDITOR.instances[a].getData())})),e.append("sort",$("#task_table tbody tr").length+1),$.ajax({type:"post",url:"/backend/lesson/task/faq/add",processData:!1,contentType:!1,data:e,success:function(e){"error"==e?($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".faq_edit",(function(){var e=new FormData($("#form_task")[0]);$.each(j,(function(t,a){e.append(a,CKEDITOR.instances[a].getData())})),$.ajax({type:"post",url:"/backend/lesson/task/faq/edit",processData:!1,contentType:!1,data:e,success:function(e){"error"==e?($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".result_add",(function(){var e=new FormData($("#form_task")[0]);$.each(x,(function(t,a){e.append(a,CKEDITOR.instances[a].getData())})),e.append("sort",$("#task_table tbody tr").length+1),$.ajax({type:"post",url:"/backend/lesson/task/result/add",processData:!1,contentType:!1,data:e,success:function(e){"error"==e?($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".result_edit",(function(){var e=new FormData($("#form_task")[0]);$.each(x,(function(t,a){e.append(a,CKEDITOR.instances[a].getData())})),e.append("number_result",w),$.ajax({type:"post",url:"/backend/lesson/task/result/edit",processData:!1,contentType:!1,data:e,success:function(e){"error"==e?($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !")):($("#pageModal").modal("hide"),$("#task_area").html(e),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table"))}})})),$("#new-footer").on("click",".conjunction_add",(function(){var e=new FormData($("#form_task")[0]);console.log("content: ",e),console.log(C),$.each(C,(function(t,a){console.log("item: ",t),console.log("value: ",a),console.log(CKEDITOR.instances[a].getData()),e.append(a,CKEDITOR.instances[a].getData())})),e.append("number_conjunction",D),e.append("lesson[explain]",u.getData()),e.append("lesson[suggest]",p.getData()),console.log("content: ",e),$.ajax({type:"post",url:"/backend/lesson/add-conjunction",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),$("#new-footer").on("click",".conjunction_edit",(function(){console.log("vao dy.........");var e=new FormData($("#form_task")[0]);$.each(C,(function(t,a){console.log("item: ",t),console.log("value: ",a),e.append(a,CKEDITOR.instances[a].getData())})),e.append("number_conjunction",D),e.append("lesson[explain]",u.getData()),e.append("lesson[suggest]",p.getData()),$.ajax({type:"post",url:"/backend/lesson/edit-conjunction",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),e.g.add_conjunction=function(e){D++;var t=$(document.createElement("div")).attr("id","result"+D).attr("class","p-2 w-full flex justify-around items-center border-solid border-[1px] border-[#e4e3e3]");t.after().html('<div class="max-w-[70%] form-control reloadCKeditor" style="max-width: 70%" contenteditable="true"\n               id="lesson[item]['.concat(D,'][value]" data-text="dap an"></div>\n          <label class="tcb-inline">\n              <input type="radio" name="lesson[item][').concat(D,'][type]" value="default" checked>\n              <span class="labels"> Thành phần</span>\n          </label>\n          <label class="tcb-inline">\n              <input type="radio" name="lesson[item][').concat(D,'][type]" value="question">\n              <span class="labels"> Đáp án </span>\n          </label>')),t.appendTo(".question_item_content"),CKEDITOR.inline("lesson[item][".concat(D,"][value]"),{extraPlugins:"furigana,colorbutton",allowedContent:{$1:{elements:CKEDITOR.dtd,attributes:!0,styles:!0,classes:!0}},disallowedContent:"script;style; *[on*]"}),C.push("lesson[item][".concat(D,"][value]"))},$("#new-footer").on("click",".sentence_jumble_add",(function(){var e=new FormData($("#form_task")[0]);console.log("content: ",e),console.log(T),$.each(T,(function(t,a){console.log("item: ",t),console.log("value: ",a),console.log(CKEDITOR.instances[a].getData()),e.append(a,CKEDITOR.instances[a].getData())})),e.append("number_sentence_jumble",E),e.append("lesson[explain]",m.getData()),e.append("lesson[suggest]",_.getData()),e.append("lesson[title_question]",f.getData()),console.log("content: ",e),$.ajax({type:"post",url:"/backend/lesson/add-sentence-jumble",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),$("#new-footer").on("click",".sentence_jumble_edit",(function(){var e=new FormData($("#form_task")[0]);console.log("content: ",e),console.log(T),$.each(T,(function(t,a){console.log("item: ",t),console.log("value: ",a),console.log(CKEDITOR.instances[a].getData()),e.append(a,CKEDITOR.instances[a].getData())})),e.append("number_sentence_jumble",E),e.append("lesson[explain]",m.getData()),e.append("lesson[suggest]",_.getData()),e.append("lesson[title_question]",f.getData()),console.log("content: ",e),$.ajax({type:"post",url:"/backend/lesson/edit-sentence-jumble",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),e.g.add_sentence_jumble=function(e){var t="";t=1===++E?'<div class="item_component_sentence_jumble flex items-center" data-number="'.concat(E,'" id="item_component_sentence_jumble_').concat(E,'">\n                        <div  id="lesson[value][').concat(E,']" contenteditable="true"  class="form-control reloadCKeditor min-w-[100px]" name="lesson[value][').concat(E,']"  placeholder="Thành phần ').concat(E,'"></div>\n                    </div>'):'<div class="item_component_sentence_jumble flex items-center" data-number="'.concat(E,'" id="item_component_sentence_jumble_').concat(E,'">\n                        <p class="mx-2"> / </p>\n                        <div id="lesson[value][').concat(E,']" contenteditable="true"  class="form-control reloadCKeditor min-w-[100px]" name="lesson[value][').concat(E,']"  placeholder="Thành phần ').concat(E,'"></div>\n                    </div>'),$(".sentence_jumble_item_content").append(t),CKEDITOR.inline("lesson[value][".concat(E,"]"),{extraPlugins:"furigana,colorbutton"}),T.push("lesson[value][".concat(E,"]"))},e.g.add_item_sentence_jumble_false=function(){var e="";e=1===++I?'<div class="item_component_sentence_jumble_false flex items-center" data-number="'.concat(I,'">\n                            <div class="form-control reloadCKeditor min-w-[100px]" id="lesson[item_false][').concat(I,']" contenteditable="true" name="lesson[item_false][').concat(I,']" placeholder="Thành phần sai ').concat(I,'"></div>\n                        </div>'):'<div class="item_component_sentence_jumble_false flex items-center" data-number="'.concat(I,'">\n                            <p class="mx-2"> , </p>\n                            <div class="form-control reloadCKeditor min-w-[100px]" id="lesson[item_false][').concat(I,']" contenteditable="true" name="lesson[item_false][').concat(I,']" placeholder="Thành phần sai ').concat(I,'"></div>\n                        </div>'),$(".sentence_jumble_item_content_false").append(e),CKEDITOR.inline("lesson[item_false][".concat(I,"]"),{extraPlugins:"furigana,colorbutton"}),T.push("lesson[item_false][".concat(I,"]"))},e.g.resetCkeditorConjunction=function(){for(var e in CKEDITOR.instances)console.log("instanceName: ",e),CKEDITOR.instances.hasOwnProperty(e)&&e.includes("lesson[item]")?(console.log("instanceName destroy: ",e),CKEDITOR.instances[e].destroy(!0)):C.includes(e)||C.push(e)},e.g.fillDataSentenceJumble=function(e){var t,a,n=JSON.parse(e.value);f.setData(n.title_question),$("#type_question_sentence_jumble").val(n.type_question),$("input:radio[name=sentence_jumble_show]").filter("[value=".concat(e.show,"]")).prop("checked",!0),$("#grade_question_sentence_jumble").val(e.grade),$(".flc_img_preview").empty();var o='<img src="/cdn/lesson/default/'.concat(n.img,'" width="64px" class="img-rounded">');if($(".flc_img_preview").append(o),$("#audio_preview_sentence_jumble").empty(),""!=n.audio&&null!=n.audio&&n.audio.length>0){var i='<audio controls controlslist="nodownload">\n                                    <source id="source_audio" src="/cdn/audio/'.concat(n.audio,'"\n                                            type="audio/mp3">\n                                </audio>');$("#audio_preview_sentence_jumble").append(i)}if($("#audio_preview_explain_mp3_sentence_jumble").empty(),""!=e.explain_mp3&&null!=e.explain_mp3&&e.explain_mp3.length>0){var s='<audio controls controlslist="nodownload">\n                                    <source id="source_audio" src="/cdn/audio/'.concat(e.explain_mp3,'"\n                                            type="audio/mp3">\n                                </audio>');$("#audio_preview_explain_mp3_sentence_jumble").append(s)}_.setData(n.suggest),m.setData(n.explain),T=[],$("#lesson_component_id").val(e.id),E=0,Object.keys(n.question).forEach((function(e){add_sentence_jumble("edit"),CKEDITOR.instances["lesson[value][".concat(E,"]")].setData(n.question[e].value)})),$(".sentence_jumble_item_content_false").empty(),I=0,Object.keys(n.item_false).forEach((function(e){add_item_sentence_jumble_false("edit"),CKEDITOR.instances["lesson[item_false][".concat(I,"]")].setData(n.item_false[e].value)})),$("#sentencePart").val(null===(t=e.part)||void 0===t?void 0:t.type),$("#sentenceMondaiName").val(null===(a=e.mondai)||void 0===a?void 0:a.title)},e.g.resetFormSentenceJumble=function(e){var t;E=0,I=0,$(".sentence_jumble_item_content").empty(),$(".sentence_jumble_item_content_false").empty(),$("#audio_preview_sentence_jumble").empty(),$(".flc_img_preview").empty(),$("#audio_preview_explain_mp3_sentence_jumble").empty(),$("#grade_question_sentence_jumble").val(""),$("#sentenceMondaiName").val(lesson_task.length?null===(t=lesson_task[lesson_task.length-1].mondai)||void 0===t?void 0:t.title:""),"add"==e&&(T=["lesson[value][1]","lesson[value][2]"]).forEach((function(e){add_sentence_jumble()})),"edit"==e&&(T=[])},e.g.resetFormWordPairMatching=function(e){var t;if(K=0,O=[],$(".word_pair_matching_item_content").empty(),$("#grade_question_word_pair_matching").val(""),$("#lesson_component_id").val(""),$("#wordPairMondaiName").val(lesson_task.length?null===(t=lesson_task[lesson_task.length-1].mondai)||void 0===t?void 0:t.title:""),"add"==e)for(var a=1;a<3;a++)add_word_pair_matching();console.log("reset fỏm word pair matching")},e.g.add_word_pair_matching=function(){K++;var e='<div class="word_pair_matching_item w-full mb-2" data-number="'.concat(K,'">\n                        <div class="flex items-center justify-between">\n                            <div class="w-3/12 form-control reloadCKeditor min-w-[100px]" id="lesson[value][').concat(K,'][left]" contenteditable="true" name="lesson[value][').concat(K,'][left]" placeholder="Thành phần 1"></div>\n                            <div class="w-3/12 form-control reloadCKeditor min-w-[100px]" id="lesson[value][').concat(K,'][right]" contenteditable="true" name="lesson[value][').concat(K,'][right]" placeholder="Thành phần 1"></div>\n                            <div class="btn_word_pair_matching_item_del_content cursor-pointer text-red underline italic font-bold" data-number="').concat(K,'">\n                                Xóa\n                            </div>\n                        </div>\n                    </div>');$(".word_pair_matching_item_content").append(e),console.log("add_word_pair_matching"),CKEDITOR.inline("lesson[value][".concat(K,"][left]"),{extraPlugins:"furigana,colorbutton"}),O.push("lesson[value][".concat(K,"][left]")),CKEDITOR.inline("lesson[value][".concat(K,"][right]"),{extraPlugins:"furigana,colorbutton"}),O.push("lesson[value][".concat(K,"][right]"))},e.g.fillDataWordPairMatching=function(e){var t,a;console.log("task: ",e);var n=JSON.parse(e.value);if(console.log("value: ",n),$("#type_question_word_pair_matching").val(n.type_question),$("input:radio[name=word_pair_matching_show]").filter("[value=".concat(e.show,"]")).prop("checked",!0),$("#grade_question_word_pair_matching").val(e.grade),$("#audio_preview_explain_mp3_word_pair_matching").empty(),""!=e.explain_mp3&&null!=e.explain_mp3&&e.explain_mp3.length>0){var o='<audio controls controlslist="nodownload">\n                                    <source id="source_audio" src="/cdn/audio/'.concat(e.explain_mp3,'"\n                                            type="audio/mp3">\n                                </audio>');$("#audio_preview_explain_mp3_word_pair_matching").append(o)}if(v.setData(n.explain),O=[],$("#lesson_component_id").val(e.id),K=0,$(".word_pair_matching_item_content").empty(),$("#wordPairPart").val(null===(t=e.part)||void 0===t?void 0:t.type),$("#wordPairMondaiName").val(null===(a=e.mondai)||void 0===a?void 0:a.title),n.question.left)for(var i=0;i<n.question.left.length;i++)add_word_pair_matching("edit"),console.log("i: ",i),console.log(n.question.left[i]),console.log("number_item_sentence_jumble_false: ",K),CKEDITOR.instances["lesson[value][".concat(K,"][left]")].setData(n.question.left[i].value),CKEDITOR.instances["lesson[value][".concat(K,"][right]")].setData(n.question.right[i].value)},e.g.resetFormSpeaking=function(e){$("#grade_question_speaking").val(""),h.setData(""),$(".flc_img_preview").empty(),$("#audio_preview_speaking_speed_default").empty(),$("#audio_preview_speaking_speed_slow").empty(),$("#input_image_speaking").val(""),$("#input_audio_speaking_speed_default").val(""),$("#input_audio_speaking_speed_slow").val(""),console.log("mode: ",e)},e.g.fillDataSpeaking=function(e){console.log("task: ",e);var t=JSON.parse(e.value);console.log("value: ",t),$("#grade_question_speaking").val(e.grade),$("#lesson_component_id").val(e.id),setTimeout((function(){h.setData(t.title_question)}),500);var a='<img src="/cdn/lesson/default/'.concat(t.img,'" width="64px" class="img-rounded">');$(".flc_img_preview").append(a);var n='<audio controls controlslist="nodownload">\n                                    <source id="source_audio" src="/cdn/audio/'.concat(t.audio_speaking_speed_default,'"\n                                            type="audio/mp3">\n                                </audio>');$("#audio_preview_speaking_speed_default").append(n);var o='<audio controls controlslist="nodownload">\n                                    <source id="source_audio" src="/cdn/audio/'.concat(t.audio_speaking_speed_slow,'"\n                                            type="audio/mp3">\n                                </audio>');$("#audio_preview_speaking_speed_slow").append(o)},$("#new-footer").on("click",".word_pair_matching_add",(function(){var e=new FormData($("#form_task")[0]);console.log("content: ",e),console.log(O),$.each(O,(function(t,a){console.log("item: ",t),console.log("value: ",a),console.log(CKEDITOR.instances[a].getData()),e.append(a,CKEDITOR.instances[a].getData())})),e.append("number_word_pair_matching",K),e.append("lesson[explain]",v.getData()),console.log("content: ",e),$.ajax({type:"post",url:"/backend/lesson/add-word-pair-matching",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),$("#new-footer").on("click",".word_pair_matching_edit",(function(){var e=new FormData($("#form_task")[0]);console.log("content: ",e),console.log(O),$.each(O,(function(t,a){console.log("item: ",t),console.log("value: ",a),console.log(CKEDITOR.instances[a].getData()),e.append(a,CKEDITOR.instances[a].getData())})),e.append("number_sentence_jumble",K),e.append("lesson[explain]",v.getData()),console.log("content: ",e),$.ajax({type:"post",url:"/backend/lesson/edit-word-pair-matching",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),$("#new-footer").on("click",".speaking_add",(function(){var e=new FormData($("#form_task")[0]);console.log("content: ",e),e.append("lesson[title_question]",h.getData()),console.log("content: ",e),$.ajax({type:"post",url:"/backend/lesson/add-speaking",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),$("#new-footer").on("click",".speaking_edit",(function(){var e=new FormData($("#form_task")[0]);e.append("lesson[title_question]",h.getData()),console.log("content: ",e),$.ajax({type:"post",url:"/backend/lesson/edit-speaking",processData:!1,contentType:!1,data:e,success:function(e){console.log("data: ",e),"success"===e.status?($("#pageModal").modal("hide"),location.reload()):($("#pageModal").modal("show"),$(".global_error").removeClass("hidden"),$(".global_error").text("Dữ liệu còn trống !"))}})})),$.fn.modal.Constructor.prototype.enforceFocus=function(){modal_this=this,$(document).on("focusin.modal",(function(e){modal_this.$element[0]===e.target||modal_this.$element.has(e.target).length||$(e.target.parentNode).hasClass("cke_dialog_ui_input_select")||$(e.target.parentNode).hasClass("cke_dialog_ui_input_textarea")||$(e.target.parentNode).hasClass("cke_dialog_ui_input_text")||modal_this.$element.focus()}))},function(e){"use strict";var t=function(){this.btnUpload=e("#btn-upload-pdf"),this.pdfFilename=e("#pdfFilename"),this.pdfFile=e("#pdfFile"),this.taskSubmit=e("#task_submit"),this.bodyElement=e("#body_pdf")};t.prototype.readFileName=function(t,a){t.files&&t.files[0]&&e(a).val(t.files[0].name)},t.prototype.resetValueUpload=function(){e(this.pdfFile).val(""),e(this.pdfFilename).val("")},t.prototype.setEvents=function(){var t=this;t.pdfFilename.on("click",(function(){t.resetValueUpload(),t.pdfFile.trigger("click")})),t.pdfFile.on("change",(function(){t.readFileName(this,t.pdfFilename)})),t.taskSubmit.on("click",(function(){"pdf_upload"==e(this).data("type")&&"add"==t.bodyElement.data("type")&&t.addPdfDocument(),"pdf_upload"==e(this).data("type")&&"edit"==t.bodyElement.data("type")&&t.editPdfDocument()})),t.btnUpload.on("click",(function(){uploadFile("pdfFile","/backend/lesson/upload-pdf")}))},t.prototype.addPdfDocument=function(){if(""==this.pdfFilename.val())return e(".global_error").text("Dữ liệu còn trống !"),void e(".global_error").removeClass("hidden");e(".global_error").addClass("hidden");var t=new FormData;t.append("_token",window.Laravel.csrfToken),t.append("filename",this.pdfFilename.val()),t.append("lesson_id",e("#lesson_id").val()),t.append("show",e("input[name='pdf_show']:checked").val()),e.ajax({type:"post",url:"/backend/lesson/save-pdf-entity",async:!0,dataType:"JSON",data:t,cache:!1,contentType:!1,processData:!1,success:function(t){e("#pageModal").modal("hide"),e("#task_area").html(t.html),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table")},error:function(t){e(".global_error").removeClass("hidden"),e(".global_error").text("Tệp tin không tồn tại!")}})},t.prototype.editPdfDocument=function(){var t=this;if(""==t.pdfFilename.val())return e(".global_error").text("Dữ liệu còn trống !"),void e(".global_error").removeClass("hidden");e(".global_error").addClass("hidden");var a=new FormData;a.append("_token",window.Laravel.csrfToken),a.append("id",t.pdfFilename.data("id")),a.append("filename",t.pdfFilename.val()),a.append("filename_old",t.pdfFilename.data("old-file")),a.append("lesson_id",e("#lesson_id").val()),a.append("show",e("input[name='pdf_show']:checked").val()),e.ajax({type:"post",url:"/backend/lesson/save-pdf-entity",async:!0,dataType:"JSON",data:a,cache:!1,contentType:!1,processData:!1,success:function(t){e("#pageModal").modal("hide"),e("#task_area").html(t.html),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table")},error:function(t){e(".global_error").removeClass("hidden"),e(".global_error").text("Tệp tin không tồn tại!")}})},t.prototype.init=function(){this.setEvents()},e.FormPdf=new t,e.FormPdf.Constructor=t,e.FormPdf.init()}(window.jQuery),function(e){"use strict";var t=function(){this.audio_filename=e("#audio_filename"),this.audio_text=e("#audio_text"),this.taskSubmit=e("#task_submit"),this.bodyElement=e("#body_audio"),this.iskaiwa2=e("#is_kaiwa2")};t.prototype.setEvents=function(){var t=this;t.taskSubmit.on("click",(function(){"audio"==e(this).data("type")&&"add"==t.bodyElement.data("type")&&t.add(),"audio"==e(this).data("type")&&"edit"==t.bodyElement.data("type")&&t.edit()}))},t.prototype.add=function(){var t=this;if(""==t.audio_filename.val()||""==t.audio_text.val())return e(".global_error").text("Dữ liệu còn trống !"),void e(".global_error").removeClass("hidden");e(".global_error").addClass("hidden");var a=new FormData;a.append("_token",window.Laravel.csrfToken),a.append("audio_filename",t.audio_filename.val()),a.append("audio_text",t.audio_text.val()),a.append("lesson_id",e("#lesson_id").val()),a.append("show",e("input[name='pdf_show']:checked").val()),a.append("is_kaiwa2",t.iskaiwa2[0].checked),e.ajax({type:"post",url:"/backend/lesson/save-audio-entity",async:!0,dataType:"JSON",data:a,cache:!1,contentType:!1,processData:!1,success:function(t){e("#pageModal").modal("hide"),e("#task_area").html(t.html),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table")},error:function(t){e(".global_error").removeClass("hidden"),e(".global_error").text("Tệp tin không tồn tại!")}})},t.prototype.edit=function(){var t=this;if(""==t.audio_filename.val())return e(".global_error").text("Dữ liệu còn trống !"),void e(".global_error").removeClass("hidden");e(".global_error").addClass("hidden");var a=new FormData;a.append("_token",window.Laravel.csrfToken),a.append("id",t.audio_filename.data("id")),a.append("audio_filename",t.audio_filename.val()),a.append("audio_text",t.audio_text.val()),a.append("lesson_id",e("#lesson_id").val()),a.append("show",e("input[name='pdf_show']:checked").val()),a.append("is_kaiwa2",t.iskaiwa2[0].checked),e.ajax({type:"post",url:"/backend/lesson/save-audio-entity",async:!0,dataType:"JSON",data:a,cache:!1,contentType:!1,processData:!1,success:function(t){e("#pageModal").modal("hide"),e("#task_area").html(t.html),sortTable("body_task",2,1,"/backend/lesson/task/sort","sort","task_table")},error:function(t){e(".global_error").removeClass("hidden"),e(".global_error").text("Tệp tin không tồn tại!")}})},t.prototype.init=function(){this.setEvents()},e.FormAudio=new t,e.FormAudio.Constructor=t,e.FormAudio.init()}(window.jQuery)}();