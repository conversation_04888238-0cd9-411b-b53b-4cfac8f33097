!function(){var a={};a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(a){if("object"==typeof window)return window}}();var t=window.location.origin+"/cdn/flashcard/default/",n=$("textarea[name=vi_lang]"),e=$("textarea[name=jp_lang]"),i=$("input[name=jpSz]"),s=$("textarea[name=example]"),o=$("input[name=flashcard_show]"),c=$("input[name=image_file]"),l=$("input[name=audio_file]"),d=$("#body_flashcard"),r=$("input[name=lesson_id]"),p=$("#pageModal"),h=$(".flc_img_preview"),g=$("#mp3_file"),u=$("#progressBarUpload"),f=$(".body_task"),v=$("input[name=flashcard_hint]"),m=$(".comment-type"),b=$("#fc_jp_word"),w=$("#fc_jp_ex"),_=$("#fc_vi_img"),x=$("#fc_vi_meaning");function y(a){return a.replace(/\n/gi,"<br/>")}a.g.previewCourse=function(a){var t=$("#fc_vi_img");changeViDisplay();var n=$(a)[0].files[0],e=new FileReader;e.onloadend=function(){$(h).html('<img src="'.concat(e.result,'" width="64px" class="img-rounded">')),t.html('<img src="'.concat(e.result,'">'))},n&&e.readAsDataURL(n)},a.g.saveDataFlashCard=function(a){console.log("vao day....",a);var t,h=new FormData;if(h.append("type",a),h.append("vi_lang",n.val()),h.append("jp_lang",e.val()),h.append("jpSz",i.val()),h.append("example",s.val()),h.append("lesson_id",r.val()),h.append("flashcard_hint",v.val()),o.each((function(){$(this).prop("checked")&&h.append("show",$(this).val())})),0!==c[0].files.length&&h.append("image",c[0].files[0]),0!==l[0].files.length&&h.append("audio",l[0].files[0]),window.EDIT_MODE){t=d.data("uri_edit");var g=d.data("id_task");h.append("id_task",g),window.isDelImg&&h.append("is_delImg",!0),window.isDelAud&&h.append("is_delAud",!0)}else t=d.data("uri");$.ajax({url:t,type:"post",data:h,contentType:!1,processData:!1,dataType:"json",success:function(a){if("success"===a.status)u.css("width","100%"),setTimeout((function(){if(p.modal("hide"),window.EDIT_MODE){var t=".status-"+a.detail.id,n=1==a.detail.show?'<span class="label label-success">Bật</span>':'<span class="label label-danger">Tắt</span>';$(t).html(n)}else fillDataFlashcard(a.detail.id,a.detail.show,a.detail.sort);resetFlashCard()}),500);else{for(var t in a.detail){var n=".error_";$(n+=t).html(a.detail[t][0])}setTimeout((function(){for(var t in a.detail){var n=".error_";$(n+=t).html("")}}),3e3)}}})},a.g.showDataFlashCard=function(a,c){var l,r=JSON.parse(a.detail.value);n.val(r.vi),e.val(r.jp),i.val(r.jpSz||""),s.val(r.ex),""===r.img?h.html("Chưa chọn ảnh"):(h.html('<img src="'.concat(t+r.img,'" width="64px" class="img-rounded">\n        <button type="button" class="btn btn-danger" style="margin-left: 15px" onclick="deleteImageFlashcard()"><span class="glyphicon glyphicon-trash"></span></button>')),_.html('<img src="'.concat(t+r.img,'">'))),l=""===r.audio?"Chưa chọn âm thanh":"\n                <span>".concat(r.audio,'</span><button type="button" class="btn btn-default" style="border: 0px; background: #edf1f5; font-size: 20px" onclick="openAudioFlashcard(this)" data-audio="audio_flashcard"><span class="glyphicon glyphicon-volume-up" aria-hidden="true"></span></button>\n                <audio controls id="audio_flashcard" style="display: none">\n                  <source src="').concat(window.location.origin+"/cdn/audio/"+r.audio,'" type="audio/mpeg">\n                    Your browser does not support the audio element.\n                </audio>\n                <button type="button" class="btn btn-danger" style="margin-left: 15px" onclick="deleteAudioFlashcard()"><span class="glyphicon glyphicon-trash"></span></button>'),g.html(l),o.each((function(){$(this).val()==a.detail.show&&$(this).prop("checked",!0)})),d.data("id_task",c),""!==a.detail.lesson.flashcard_hint&&m.find("li").each((function(){$(this).removeClass("active"),$(this).data("flashcard_hint")==a.detail.lesson.flashcard_hint&&$(this).addClass("active")}))},a.g.openAudioFlashcard=function(a){var t=$(a).data("audio");document.getElementById(t).play()},a.g.resetFlashCard=function(){n.val(""),e.val(""),s.val(""),h.html(""),g.text(""),u.css("width",0),c.val(""),l.val(""),window.EDIT_MODE=!1,o.each((function(){1==$(this).val()&&$(this).prop("checked",!0)})),d.data("id_task",null),window.isDelAud=!1,window.isDelImg=!1,resetFlashcardType()},a.g.resetFlashcardType=function(){m.find("li").removeClass("active"),m.find("li").each((function(){1==$(this).data("flashcard_hint")&&$(this).addClass("active")})),v.val(1)},a.g.fillDataFlashcard=function(a,t,n){var e=1==t?'<span class="label label-success">Bật</span>':'<span class="label label-danger">Tắt</span>',i='\n    <tr class="item'.concat(a,' ui-sortable-handle" style="">\n    <td class="text-center">').concat(n,'</td>\n    <td class="text-center">').concat(a,'</td>\n    <td class="text-center">\n                                    Flash Card\n                            </td>\n    <td class="task_content text-left">\n        <span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">\n\n                                    </span>\n    </td>\n    <td class="text-center status-').concat(a,'">\n                                    ').concat(e,'\n                            </td>\n    <td class="text-center">\n        <button class="change-lesson btn btn-warning text-dark" type="button" style="padding: 2px 5px; border-radius: 3px;" onclick="changeLesson(').concat(a,')">\n            <span class="glyphicon glyphicon-random"></span>\n        </button>\n        <button class="edit-task btn btn-info" data-info="').concat(a,'" data-type="#task-type" type="button" style="padding: 2px 5px; border-radius: 3px;" onclick="editData(this)" data-uri="').concat(window.location.origin,'/backend/lesson/get-flashcard">\n            <span class="glyphicon glyphicon-edit"></span>\n        </button>\n        <button class="delete-task btn btn-danger" data-info="').concat(a,'" type="button" style="padding: 2px 5px; border-radius: 3px;">\n            <span class="glyphicon glyphicon-trash"></span>\n        </button>\n    </td>\n</tr>\n    ');f.append(i)},a.g.deleteImageFlashcard=function(){h.html(""),window.isDelImg=!0,changeViDisplay()},a.g.deleteAudioFlashcard=function(){g.html(""),window.isDelAud=!0,changeJPDisplay()},a.g.previewFlashcard=function(a){$(".card__word--text").css({"font-size":i.val()+"px"}),b.html(y(e.val())),w.html(highlightVocab(e.val(),s.val())),x.text(n.val()),changeJPDisplay(a),changeViDisplay(a),e.on("keyup",(function(){b.html(y(e.val())),w.html(highlightVocab(e.val(),s.val()))})),i.on("change",(function(){$(".card__word--text").css({"font-size":i.val()+"px"})})),s.on("keyup",(function(){changeJPDisplay(),w.html(highlightVocab(e.val(),s.val()))})),n.on("keyup",(function(){x.text(n.val())})),l.on("change",(function(){changeJPDisplay()}))},a.g.changeJPDisplay=function(a){var t=a&&JSON.parse(a.detail.value),n=!1;0===l.get(0).files.length&&window.isDelAud&&(n=!0),a&&""==t.audio&&0===l.get(0).files.length&&(n=!0),a||0!==l.get(0).files.length||(n=!0),n?$(".card__voice--button").hide():$(".card__voice--button").show(),""==s.val()?$(".card__word--example").hide():(w.html(highlightVocab(e.val(),s.val())),$(".card__word--example").show()),""==s.val()||0===l.get(0).files.length&&window.isDelAud?($("#card_comment--title-text").show(),$(".card__word").css({"padding-top":"0","justify-content":"center"}),""==i.val()&&$(".card__word--text").css({"font-size":"100px"})):($("#card_comment--title-text").hide(),$(".card__word").css({"padding-top":"70px","justify-content":"flex-start"}),""==i.val()&&$(".card__word--text").css({"font-size":"3em"}))},a.g.changeViDisplay=function(a){var t=a&&JSON.parse(a.detail.value),n=!1;0===c.get(0).files.length&&window.isDelAud&&(n=!0),a&&""==t.img&&0===c.get(0).files.length&&(n=!0),a||0!==c.get(0).files.length||(n=!0),n?($("#fc_vi_img").hide(),$(".card_meaning").css({"justify-content":"center","font-size":"45px","line-height":"58px"}),$(".meaning").css({"font-size":"34px","line-height":"1.3"})):($(".card_meaning").css({"justify-content":"space-between","font-size":"20px","line-height":"26px"}),$(".meaning").css({"font-size":"20px","line-height":"1.3"}),$("#fc_vi_img").show())},a.g.mp3Ex=function(a){var t=new RegExp(/\{\!(.+?)\!\}/,"gi"),n=a,e=t.exec(n);for(e&&(n=n.replace(e[0],"<span class='text-info' title='".concat(e[1],"'>")+"<i class='fa fa-volume-up'></i></span>"));e;)(e=t.exec(n))&&(n=n.replace(e[0],'<span class="text-info" title=\''.concat(e[1],"'>")+'<i class="fa fa-volume-up"></i></span>'));return n},a.g.highlightVocab=function(a,t){var n=new RegExp(a,"gi"),e=new RegExp(/\*+(.+?)\*+/,"gi"),i=t.replace(n,'<span class="highlight">'+a+"</span>"),s=e.exec(i);for(s&&(i=i.replace(s[0],'<span class="highlight">'+s[1]+"</span>"));s;)(s=e.exec(i))&&(i=i.replace(s[0],'<span class="highlight">'+s[1]+"</span>"));return i=y(i),i=mp3Ex(i)},$("#fcExampleInput").select((function(a){var t=$(this),n=t.val(),e=t.prop("selectionStart"),i=t.prop("selectionEnd"),s=t.val().substring(0,e),o=t.val().substring(i,t.val().length),c=t.val().substring(e,i);var l=s+("*"+c+"*")+o;$("#_hlBtn").click((function(a){t.val(l),changeJPDisplay()})),$("#_udBtn").click((function(a){t.val(n),changeJPDisplay()}))})),$(document).on("click","#_mp3Btn",(function(a){$("#audio_file_flashcard").trigger("click")})),$("#audio_file_flashcard").on("change",(function(){var a=this.files[0],t=new FileReader;t.onload=function(a){uploadAudio($("#audio_file_flashcard")[0].files[0],(function(a){$("#fcExampleInput").val($("#fcExampleInput").val()+"{! "+a.data.file_path+" !}"),changeJPDisplay()}))},a&&t.readAsDataURL(a)})),$("#card-inner").on("click",(function(){$(this).toggleClass("flip")})),$((function(){m.find("li").click((function(){m.find("li").removeClass("active"),$(this).addClass("active");var a=$(this).data("flashcard_hint");v.val(a)}))})),a.g.saveData=function(a){var t=$(a).data("task");if("9"===$(t).val())saveDataFlashCard(9)},a.g.editData=function(a){var t=$(a).data("info"),n=$(a).data("uri"),e=$(a).data("type");window.EDIT_MODE=!0,$.ajax({url:n,type:"get",data:{id:t},dataType:"json",success:function(a){"success"===a.status&&showData(a,t,e)}})},a.g.showData=function(a,t,n){var e=a.detail.type;if($(n).val(e),9===e)console.log("case 9"),showDataFlashCard(a,t),previewFlashcard(a)},a.g.resetData=function(a){var t=$(a).data("task");if("9"===$(t).val())resetFlashCard(),previewFlashcard()},a.g.changeLesson=function(a){bootbox.prompt({title:"Nhập ID bài học",backdrop:!0,callback:function(t){if(t){var n={flashcardId:a,lessonId:t};$.post(window.location.origin+"/backend/lesson/flashcard-lesson",n,(function(n,e){switch(n.code){case 404:$.toast({text:n.error,position:"top-right",stack:!1,icon:"error"});break;case 409:$.toast({text:n.error,position:"top-right",stack:!1,icon:"info"});break;case 200:$(".item"+a).fadeOut(),$.toast({text:"Đã chuyển flashcard sang bài học <a target='blank' href="+window.location.origin+"/backend/lesson/"+t+"/edit>#"+t+"</a>",position:"top-right",stack:!1,icon:"success"});break;default:$.toast({text:"Hệ thống gặp sự cố",position:"top-right",stack:!1,icon:"error"})}})).fail((function(){$.toast({text:"Hệ thống gặp sự cố",position:"top-right",stack:!1,icon:"error"})}))}""==t&&$.toast({text:"Không có thay đổi",position:"top-right",stack:!1,icon:"info"})}})},a.g.cloneToLesson=function(a){bootbox.prompt({title:"Nhập ID bài học",backdrop:!0,callback:function(t){if(t){var n={flashcardId:a,lessonId:t};$.post(window.location.origin+"/backend/lesson/clone-flashcard-to-lesson",n,(function(a,n){switch(a.code){case 404:$.toast({text:a.error,position:"top-right",stack:!1,icon:"error"});break;case 409:$.toast({text:a.error,position:"top-right",stack:!1,icon:"info"});break;case 200:$.toast({text:"Đã copy flashcard sang bài học <a target='blank' href="+window.location.origin+"/backend/lesson/"+t+"/edit>#"+t+"</a>",position:"top-right",stack:!1,icon:"success"});break;default:$.toast({text:"Hệ thống gặp sự cố",position:"top-right",stack:!1,icon:"error"})}})).fail((function(){$.toast({text:"Hệ thống gặp sự cố",position:"top-right",stack:!1,icon:"error"})}))}""==t&&$.toast({text:"Không có thay đổi",position:"top-right",stack:!1,icon:"info"})}})}}();