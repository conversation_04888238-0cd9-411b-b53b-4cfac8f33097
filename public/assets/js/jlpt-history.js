!function(){var t="http://localhost:3333";-1!=window.location.href.indexOf("-test")&&(t="https://jlpt-test.dungmori.com"),-1!=window.location.href.indexOf("dungmori.com")&&-1==window.location.href.indexOf("web-test")&&(t="https://mjt.dungmori.com");new Vue({el:"#main-history-right",data:function(){return{items:items,certificateUser:null,formData:{fullname:"",dob:"",mobile:"",email:"",country:"vi",postalcode:"",address:""},locale:locale,validation:{},translate:{en:{msg_required:"Please enter this fields",msg_email_format:"Incorrect email format",msg_mobile_format:"Incorrect mobile format",msg_ise:"Internal Server Error",msg_success:"Successfully!"},vi:{msg_required:"Trường này không được để trống",msg_email_format:"Email không đúng định dạng",msg_mobile_format:"Điện thoại có 10 đến 12 ký tự là số",msg_ise:"Có lỗi xảy ra. Vui lòng thực hiện lại sau!",msg_success:"Cập nhật thông tin thành công!"}},myResult:""}},watch:{"formData.country":function(t,e){this.detailValiCheck("country")},"formData.postalcode":function(t,e){this.detailValiCheck("postalcode")},"formData.address":function(t,e){this.detailValiCheck("address")},"formData.fullname":function(t,e){this.detailValiCheck("fullname")},"formData.dob":function(t,e){this.detailValiCheck("dob")},"formData.mobile":function(t,e){this.detailValiCheck("mobile")},"formData.email":function(t,e){this.detailValiCheck("email")}},methods:{btoa:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){return btoa(t)})),showAnswer:function(e,i){this.PopupCenter(t+"/rs/"+window.btoa("akOiW"+window.btoa(e))+"/"+window.btoa(i),"XXX","750","800")},PopupCenter:function(t,e,i,n){var o=null!=window.screenLeft?window.screenLeft:screen.left,a=null!=window.screenTop?window.screenTop:screen.top,r=(window.innerWidth?window.innerWidth:document.documentElement.clientWidth?document.documentElement.clientWidth:screen.width)/2-i/2+o,s=(window.innerHeight?window.innerHeight:document.documentElement.clientHeight?document.documentElement.clientHeight:screen.height)/2-n/2+a,l=window.open(t,e,"scrollbars=yes, width="+i+", height="+n+", top="+s+", left="+r);window.focus&&l.focus()},showCertificate:function(t){this.certificateUser=this.items[t],setTimeout((function(){$(".fancybox").fancybox().trigger("click")}),100)},printDate:function(t){return t.substring(0,4)+"年 "+t.substring(5,7)+"月 "+t.substring(8,10)+"日"},printTotal:function(t,e,i){return t+e+i},printResult:function(t,e,i,n){if(["N4","N5"].includes(t)&&(e<38||n<19))return!1;if(["N3","N2","N1"].includes(t)&&(e<19||i<19||n<19))return!1;var o=this;return"N5"==t&&o.printTotal(e,i,n)>=80||("N4"==t&&o.printTotal(e,i,n)>=90||("N3"==t&&o.printTotal(e,i,n)>=95||("N2"==t&&o.printTotal(e,i,n)>=90||"N1"==t&&o.printTotal(e,i,n)>=100)))},showModal:function(t,e){t=(t=t.replace(/[\r\n]/g,"\\n"))?JSON.parse(t):this.formData,this.formData.fullname=t.fullname,this.formData.dob=t.dob,this.formData.mobile=t.mobile,this.formData.country=t.country,this.formData.email=t.email,this.formData.postalcode=t.postalcode,this.formData.address=t.address,this.myResult=e,$("#infoModal").modal("show")},submitCertificate:function(){var e=this;e.validationCheck(),0==Object.keys(e.validation).length&&$.post(t+"/certificate/"+e.myResult,e.formData).then((function(i){i.status?($("#infoModal").modal("hide"),location.reload(),window.open(t+"/certificate/"+btoa(e.myResult),"_blank")):alert(e.translate[e.locale].msg_ise)}))},validationCheck:function(){for(var t in this.formData)this.detailValiCheck(t);return{}},detailValiCheck:function(t){var e=this;e.formData[t]?(delete e.validation[t],"email"!=t||/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(e.formData.email)||(e.validation[t]=e.translate[e.locale].msg_email_format),"mobile"!=t||/^\d{10,12}$/.test(e.formData.mobile)||(e.validation[t]=e.translate[e.locale].msg_mobile_format)):e.validation[t]=e.translate[e.locale].msg_required,"country"!=t&&"postalcode"!=t&&"address"!=t||e.valiAddressCheck()},valiAddressCheck:function(){var t=this;t.validation.msgAddress=t.translate[t.locale].msg_required,t.formData.country&&t.formData.postalcode&&t.formData.address&&delete t.validation.msgAddress},isShowEditButton:function(t){return this.printResult(t.course,t.score_1,t.score_2,t.score_3)}},created:function(){this.items=this.items.map((function(t){return t.userName=userName,t}))}})}();