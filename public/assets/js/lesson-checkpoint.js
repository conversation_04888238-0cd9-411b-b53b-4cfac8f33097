!function(){Vue.filter("time",(function(e){return moment(e).format("HH:mm")})),Vue.filter("two_digits",(function(e){return e.toString().length<=1?"0"+e.toString():e.toString()}));new Vue({el:"#lesson-checkpoint",data:function(){return{url:window.location.origin,mp3:null,questions:[],numOfQuestions:0,solutions:[],userAnswers:{},result:null,showPopup:!1,showResult:!1,name:"",email:"",phone:"",age:"",career:"",place:"",purpose:"",errors:{},timer:0,lessonDetail:null,checkHastime:!1,showAnswers:!1}},created:function(){deleteCookie("time_checkpoint_"+lessonDetail.id)},watch:{userAnswers:{handler:function(e){setCookie("SAVE_ANSWER_"+lessonDetail.id,JSON.stringify(e),5)},deep:!0},timerValue:function(e){0==e&&this.submitDataExercise()}},computed:{currentDate:function(){return new Date},timerValue:function(){return this.timer>0?this.timer:0},timeText:function(){return{hour:moment.duration(this.timerValue).hours(),minute:moment.duration(this.timerValue).minutes(),second:moment.duration(this.timerValue).seconds()}},showResults:function(){var e=this;return e.questions.map((function(t){if(t.answers.length>0){var s=e.userAnswers[t.id];t.answers=t.answers.map((function(e){return s&&s==e.id&&(e.user_chose=1,e.grade>0?e.is_true=1:e.is_true=0),e}))}return t}))}},methods:{initData:function(){for(var e=this,t=0;t<lesson_tasks.length;t++)1!=lesson_tasks[t].type&&3!=lesson_tasks[t].type||e.questions.push(lesson_tasks[t]),5==lesson_tasks[t].type&&(e.mp3=JSON.parse(lesson_tasks[t].value).link),3==lesson_tasks[t].type&&(e.numOfQuestions+=1);if(e.lessonDetail.max_duration>0){e.checkHastime=!0;var s=getCookie("time_checkpoint_"+e.lessonDetail.id);if(s)this.timer=s-this.currentDate.getTime();else{var i=e.currentDate.getTime()+6e4*this.lessonDetail.max_duration;setCookie("time_checkpoint_"+this.lessonDetail.id,i,1),e.timer=6e4*e.lessonDetail.max_duration}setInterval(this.updateTimer,1e3)}else e.checkHastime=!1},submitDataExercise:function(){var e=this;_.isEmpty(e.userAnswers)?alert("Bạn chưa chọn đáp án"):(e.totalScore=0,_.forEach(e.userAnswers,(function(t,s){var i=_.find(e.questions,{id:parseInt(s)}),n=_.find(i.answers,{id:parseInt(t)});parseInt(n.grade)>0&&(e.totalScore+=parseInt(n.grade))})),e.result={grade:e.totalScore,total_grade:lessonDetail.total_marks},e.showPopup=!0)},saveUserInfo:function(){var e=this,t=this,s=t.currentDate.getTime();axios.post(window.location.origin+"/checkpoint/save",{user_info:JSON.stringify({name:t.name,email:t.email,phone:t.phone,age:t.age,career:t.career,place:t.place,purpose:t.purpose}),lessonId:parseInt(lessonDetail.id),answers:JSON.stringify(t.userAnswers),grade:t.totalScore,total_grade:lessonDetail.total_marks}).then((function(i){200==i.status&&(t.$message.success("Gửi kết quả thành công!"),setCookie("time_checkpoint_"+e.lessonDetail.id,s,1),t.timer=0,t.showPopup=!1,t.showAnswers=!0)})).catch((function(s){t.$message.error("Có lỗi xảy ra. Vui lòng thử lại!"),e.errors=s.response.data}))},closePopupResult:function(){this.showPopup=!1,this.showResult=!1},reTest:function(){deleteCookie("time_checkpoint_"+lessonDetail.id),window.location.reload()},clearField:function(e){delete this.errors[e]},updateTimer:function(){0!=this.lessonDetail.max_duration&&(this.timer=this.timer-1e3)},closePopup:function(){this.showAnswers=!1,this.userAnswers={}}},mounted:function(){this.lessonDetail=lessonDetail,this.initData()}})}();