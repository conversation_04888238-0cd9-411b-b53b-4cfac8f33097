!function(){var e={};e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),function(e){var t;function n(){return t||(t=this,this.init=function(){t.setVars(),e(document).on("click",".head",(function(){e(this).attr("class").split(/\s+/).includes("disabled")||(e(this).next().toggle(),e(this).toggleClass("is-open"),e(this).closest(".stage").find(".lesson-group-item").each((function(){e(this).addClass("is-open")})))})),e(document).on("click",".btn-teacher-info",(function(){e("#modal-teacher-info").modal("show"),e(".teacher-info").hide(),e(e(this).data("id")).show()})),e(document).on("click",".btn-more-feedback",(function(){e(".feedback-more-item").show(),e(this).hide()})),t.openAllButton.on("click",(function(){e(this).hasClass("active")?(t.head.next().hide(),t.head.removeClass("is-open"),e(this).removeClass("active"),e(this).text("Mở rộng tất cả các phần")):(t.head.next().show(),t.head.addClass("is-open"),e(this).addClass("active"),e(this).text("Đóng tất cả các phần"),t.overlayFoot.hide(),t.btnShowAllStage.hide(),t.stageItem.show())})),t.btnShowAllStage.on("click",(function(){t.overlayFoot.hide(),t.btnShowAllStage.hide(),t.stageItem.show()})),e(document).on("click",".lesson-group-item__head",(function(){e(this).closest(".lesson-group-item").toggleClass("is-open")})),e(document).on("click",".course-step",(function(){var t=parseInt(e(this).data("step")),n=window.innerWidth>1024;if(n)2==t?(o=e("#step-content-3")).length&&e("html, body").animate({scrollTop:o.offset().top-270},100):window.scrollTo({top:0,behavior:"smooth"});else if(1==t)(o=e("#step-content-2")).length&&e("html, body").animate({scrollTop:o.offset().top-450},100);else if(2==t){var o;(o=e("#step-content-3")).length&&e("html, body").animate({scrollTop:o.offset().top-330},100)}else window.scrollTo({top:0,behavior:"smooth"});if(3==t&&!n)return e(".tutorial").remove(),void enableScroll();e("[data-step="+(t+1)+"]").length>0?(e(".course-step").removeClass("active"),e("[data-step="+(t+1)+"]").addClass("active")):(e(".course-step").removeClass("active"),e(".tutorial").remove(),enableScroll())})),e(document).on("click",".lesson-step",(function(){var t=parseInt(e(this).data("step")),n=window.innerWidth>1024;return n||3!=t?(1==t&&n&&!e("#tabList").length&&(t+=1),2==t&&(e("#menuTabComment").trigger("click"),e("#lesson-basic-container").addClass("training")),3==t&&(e("#menuTabLessonList").trigger("click"),e("#lesson-basic-container").addClass("training")),4==t?(e(".lesson-tutorial").remove(),enableScroll(),void e("#lesson-basic-container").removeClass("training")):void(e("[data-step="+(t+1)+"]").length>0?(e(".lesson-step").removeClass("active"),e("[data-step="+(t+1)+"]").addClass("active")):(e(".lesson-step").removeClass("active"),e(".lesson-tutorial").remove(),e("#lesson-basic-container").removeClass("training"),enableScroll()))):(e(".lesson-tutorial").remove(),enableScroll(),void e("#lesson-basic-container").removeClass("training"))})),fixRequireButton(),e(document).on("click",".btn-profile",(function(){e(this).find(".menu").toggleClass("hidden")}))},t.setVars=function(){t.openAllButton=e(".open-all"),t.head=e(".head"),t.overlayFoot=e(".overlay-foot"),t.btnShowAllStage=e(".btn-show-all-stage"),t.stageItem=e(".stage")},t)}e.fn.singleton=function(){return(new n).init(),this}}(jQuery),$(document).ready((function(){$(document).singleton()})),e.g.fixRequireButton=function(){$(".require-icon").each((function(e,t){if($(t).closest(".truncate")){var n=$(t).closest(".truncate")[0].scrollWidth,o=$(t).parent().innerWidth();n>0&&n===o&&$(t).addClass("text-over")}}))};var t={37:1,38:1,39:1,40:1};function n(e){e.preventDefault()}function o(e){if(t[e.keyCode])return n(e),!1}var s=!1;try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){s=!0}}))}catch(e){}var i=!!s&&{passive:!1},a="onwheel"in document.createElement("div")?"wheel":"mousewheel";e.g.disableScroll=function(){window.addEventListener("DOMMouseScroll",n,!1),window.addEventListener(a,n,i),window.addEventListener("touchmove",n,i),window.addEventListener("keydown",o,!1)},e.g.enableScroll=function(){window.removeEventListener("DOMMouseScroll",n,!1),window.removeEventListener(a,n,i),window.removeEventListener("touchmove",n,i),window.removeEventListener("keydown",o,!1)}}();