/*! For license information please see vocabulary.js.LICENSE.txt */
!function(){var t={3191:function(t,e,a){"use strict";var n=a(31928);function i(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var e;this.promise=new Promise((function(t){e=t}));var a=this;t((function(t){a.reason||(a.reason=new n(t),e(a.reason))}))}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var t;return{token:new i((function(e){t=e})),cancel:t}},t.exports=i},5449:function(t){"use strict";t.exports=function(t,e,a,n,i){return t.config=e,a&&(t.code=a),t.request=n,t.response=i,t}},7018:function(t,e,a){"use strict";var n=a(9516);t.exports=function(t,e){n.forEach(t,(function(a,n){n!==e&&n.toUpperCase()===e.toUpperCase()&&(t[e]=a,delete t[n])}))}},7522:function(t,e,a){"use strict";var n=a(47763);t.exports=function(t,e,a){var i=a.config.validateStatus;a.status&&i&&!i(a.status)?e(n("Request failed with status code "+a.status,a.config,null,a.request,a)):t(a)}},9516:function(t,e,a){"use strict";var n=a(69012),i=a(87206),r=Object.prototype.toString;function s(t){return"[object Array]"===r.call(t)}function o(t){return null!==t&&"object"==typeof t}function l(t){return"[object Function]"===r.call(t)}function c(t,e){if(null!=t)if("object"==typeof t||s(t)||(t=[t]),s(t))for(var a=0,n=t.length;a<n;a++)e.call(null,t[a],a,t);else for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.call(null,t[i],i,t)}t.exports={isArray:s,isArrayBuffer:function(t){return"[object ArrayBuffer]"===r.call(t)},isBuffer:i,isFormData:function(t){return"undefined"!=typeof FormData&&t instanceof FormData},isArrayBufferView:function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&t.buffer instanceof ArrayBuffer},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isObject:o,isUndefined:function(t){return void 0===t},isDate:function(t){return"[object Date]"===r.call(t)},isFile:function(t){return"[object File]"===r.call(t)},isBlob:function(t){return"[object Blob]"===r.call(t)},isFunction:l,isStream:function(t){return o(t)&&l(t.pipe)},isURLSearchParams:function(t){return"undefined"!=typeof URLSearchParams&&t instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:c,merge:function t(){var e={};function a(a,n){"object"==typeof e[n]&&"object"==typeof a?e[n]=t(e[n],a):e[n]=a}for(var n=0,i=arguments.length;n<i;n++)c(arguments[n],a);return e},extend:function(t,e,a){return c(e,(function(e,i){t[i]=a&&"function"==typeof e?n(e,a):e})),t},trim:function(t){return t.replace(/^\s*/,"").replace(/\s*$/,"")}}},11985:function(t,e){"use strict";function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function n(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function i(t,e,a){return(e=s(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function r(t,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,s(n.key),n)}}function s(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=a(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}var o=function(){return t=function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?n(Object(a),!0).forEach((function(e){i(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({stackedView:"Top",rotate:!0,visibleItems:3,margin:10,useOverlays:!0},e),this.currentPosition=0,this.isFirstTime=!0,this.elTrans=0,this.container=document.getElementById("stacked-cards-block"),this.cardsContainer=this.container.querySelector(".stackedcards-container"),this.cards=Array.from(this.cardsContainer.children),this.rightOverlay=this.container.querySelector(".stackedcards-overlay.right"),this.leftOverlay=this.container.querySelector(".stackedcards-overlay.left"),this.maxElements=this.cards.length,this.options.visibleItems=Math.min(this.options.visibleItems,this.maxElements),this.init()},e=[{key:"init",value:function(){var t=this;this.setupCards(),this.addEventListeners(),this.updateUI(),setTimeout((function(){return t.container.classList.remove("init")}),150)}},{key:"setupCards",value:function(){var t=this,e=this.options,a=e.stackedView,n=e.visibleItems,i=e.margin*(n-1);this.cardsContainer.style.marginBottom="".concat(i,"px"),this.elTrans="Top"===a?i:0,this.cards.slice(n).forEach((function(e){e.classList.add("stackedcards-".concat(a.toLowerCase()),"stackedcards--animatable","stackedcards-origin-".concat(a.toLowerCase())),e.style.cssText="\n        z-index: 0;\n        opacity: 0;\n        transform: scale(".concat(1-.04*n,") translateY(").concat(t.elTrans,"px);\n      ")})),this.updateActiveCard(),this.setupOverlays()}},{key:"setupOverlays",value:function(){var t=this;if(!this.options.useOverlays)return this.leftOverlay.classList.add("stackedcards-overlay-hidden"),void this.rightOverlay.classList.add("stackedcards-overlay-hidden");[this.leftOverlay,this.rightOverlay].forEach((function(e){e.style.transform="translateY(".concat(t.elTrans,"px)")}))}},{key:"updateActiveCard",value:function(){this.cards[this.currentPosition]&&this.cards[this.currentPosition].classList.add("stackedcards-active")}},{key:"addEventListeners",value:function(){this.cards.forEach((function(t){t.addEventListener("click",(function(e){for(var a=e.target,n=!0;a&&a!==t;){if(a.classList.contains("noFlip")||a.classList.contains("card_audio")||a.classList.contains("underline")||"BUTTON"===a.tagName||"A"===a.tagName||"SVG"===a.tagName||"path"===a.tagName||null!==a.closest(".card-footer")||null!==a.closest("[class*='cursor-pointer']")){n=!1;break}a=a.parentElement}if(n){if(!t.querySelector(".card-inner").classList.contains("flip")&&[39,40].includes(course_id)&&0===parseInt(authUser.isTester)&&["production","prod"].includes("production")){var i=39===parseInt(course_id)?"n5_flc_behind":"n4_flc_behind";"undefined"!=typeof ga&&ga("send","event","nx_flc_behind",i,i)}t.querySelector(".card-inner").classList.toggle("flip")}}))}))}},{key:"swipeLeft",value:function(){var t=this;this.currentPosition>=this.maxElements||(this.transformCard(-1e3,0,0),this.options.useOverlays&&this.transformOverlay(this.leftOverlay,-1e3,0,1,(function(){return t.resetOverlay(t.leftOverlay)})),this.nextCard())}},{key:"swipeRight",value:function(){var t=this;this.currentPosition>=this.maxElements||(this.transformCard(1e3,0,0),this.options.useOverlays&&this.transformOverlay(this.rightOverlay,1e3,0,1,(function(){return t.resetOverlay(t.rightOverlay)})),this.nextCard())}},{key:"undo",value:function(){this.currentPosition<=0||(this.currentPosition--,this.updateUI(),this.updateActiveCard())}},{key:"nextCard",value:function(){this.currentPosition++,this.updateUI(),this.updateActiveCard()}},{key:"transformCard",value:function(t,e,a){var n=this.cards[this.currentPosition];n&&(n.classList.remove("no-transition"),n.style.zIndex=6,this.transformElement(n,t,e,a))}},{key:"transformOverlay",value:function(t,e,a,n,i){t.classList.remove("no-transition"),t.style.zIndex=8,this.transformElement(t,e,a,n),setTimeout(i,300)}},{key:"resetOverlay",value:function(t){var e=this;t.classList.add("no-transition"),requestAnimationFrame((function(){t.style.transform="translateY(".concat(e.elTrans,"px)"),t.style.opacity=0})),this.isFirstTime=!1}},{key:"transformElement",value:function(t,e,a,n){var i=this,r=this.options.rotate?Math.min(Math.max(e/10,-15),15):0;requestAnimationFrame((function(){t.style.transform="translateX(".concat(e,"px) translateY(").concat(a+i.elTrans,"px) rotate(").concat(r,"deg)"),t.style.opacity=n}))}},{key:"updateUI",value:function(){var t=this;requestAnimationFrame((function(){var e,a=t.options,n=a.visibleItems,i=a.margin,r=5;t.cards.slice(t.currentPosition,t.currentPosition+n).forEach((function(t,a){e=4===r?"scale(0.97)":3===r?"scale(0.95)":"",t.style.cssText="\n          z-index: ".concat(r--,";\n          opacity: 1;\n          transform: ").concat(e," translateY(").concat(i*a*(1-.04*n),"px);\n        "),t.classList.add("stackedcards--animatable")}))}))}},{key:"destroy",value:function(){this.cards.forEach((function(t){if(t.querySelector(".card-inner")){var e=t.cloneNode(!0);t.parentNode.replaceChild(e,t)}}))}}],e&&r(t.prototype,e),a&&r(t,a),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,a}();e.A=o},17980:function(t){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},18015:function(t,e,a){"use strict";var n=a(9516),i=a(69012),r=a(35155),s=a(96987);function o(t){var e=new r(t),a=i(r.prototype.request,e);return n.extend(a,r.prototype,e),n.extend(a,e),a}var l=o(s);l.Axios=r,l.create=function(t){return o(n.merge(s,t))},l.Cancel=a(31928),l.CancelToken=a(3191),l.isCancel=a(93864),l.all=function(t){return Promise.all(t)},l.spread=a(17980),t.exports=l,t.exports.default=l},29137:function(t){"use strict";t.exports=function(t){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)}},31928:function(t){"use strict";function e(t){this.message=t}e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,t.exports=e},33948:function(t,e,a){"use strict";var n=a(9516);t.exports=n.isStandardBrowserEnv()?{write:function(t,e,a,i,r,s){var o=[];o.push(t+"="+encodeURIComponent(e)),n.isNumber(a)&&o.push("expires="+new Date(a).toGMTString()),n.isString(i)&&o.push("path="+i),n.isString(r)&&o.push("domain="+r),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read:function(t){var e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(t){this.write(t,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},35155:function(t,e,a){"use strict";var n=a(96987),i=a(9516),r=a(83471),s=a(64490),o=a(29137),l=a(84680);function c(t){this.defaults=t,this.interceptors={request:new r,response:new r}}c.prototype.request=function(t){"string"==typeof t&&(t=i.merge({url:arguments[0]},arguments[1])),(t=i.merge(n,this.defaults,{method:"get"},t)).method=t.method.toLowerCase(),t.baseURL&&!o(t.url)&&(t.url=l(t.baseURL,t.url));var e=[s,void 0],a=Promise.resolve(t);for(this.interceptors.request.forEach((function(t){e.unshift(t.fulfilled,t.rejected)})),this.interceptors.response.forEach((function(t){e.push(t.fulfilled,t.rejected)}));e.length;)a=a.then(e.shift(),e.shift());return a},i.forEach(["delete","get","head","options"],(function(t){c.prototype[t]=function(e,a){return this.request(i.merge(a||{},{method:t,url:e}))}})),i.forEach(["post","put","patch"],(function(t){c.prototype[t]=function(e,a,n){return this.request(i.merge(n||{},{method:t,url:e,data:a}))}})),t.exports=c},35592:function(t,e,a){"use strict";var n=a(9516),i=a(7522),r=a(79106),s=a(62012),o=a(64202),l=a(47763),c="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||a(42537);t.exports=function(t){return new Promise((function(e,d){var u=t.data,p=t.headers;n.isFormData(u)&&delete p["Content-Type"];var f=new XMLHttpRequest,m="onreadystatechange",v=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in f||o(t.url)||(f=new window.XDomainRequest,m="onload",v=!0,f.onprogress=function(){},f.ontimeout=function(){}),t.auth){var h=t.auth.username||"",g=t.auth.password||"";p.Authorization="Basic "+c(h+":"+g)}if(f.open(t.method.toUpperCase(),r(t.url,t.params,t.paramsSerializer),!0),f.timeout=t.timeout,f[m]=function(){if(f&&(4===f.readyState||v)&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))){var a="getAllResponseHeaders"in f?s(f.getAllResponseHeaders()):null,n={data:t.responseType&&"text"!==t.responseType?f.response:f.responseText,status:1223===f.status?204:f.status,statusText:1223===f.status?"No Content":f.statusText,headers:a,config:t,request:f};i(e,d,n),f=null}},f.onerror=function(){d(l("Network Error",t,null,f)),f=null},f.ontimeout=function(){d(l("timeout of "+t.timeout+"ms exceeded",t,"ECONNABORTED",f)),f=null},n.isStandardBrowserEnv()){var b=a(33948),x=(t.withCredentials||o(t.url))&&t.xsrfCookieName?b.read(t.xsrfCookieName):void 0;x&&(p[t.xsrfHeaderName]=x)}if("setRequestHeader"in f&&n.forEach(p,(function(t,e){void 0===u&&"content-type"===e.toLowerCase()?delete p[e]:f.setRequestHeader(e,t)})),t.withCredentials&&(f.withCredentials=!0),t.responseType)try{f.responseType=t.responseType}catch(e){if("json"!==t.responseType)throw e}"function"==typeof t.onDownloadProgress&&f.addEventListener("progress",t.onDownloadProgress),"function"==typeof t.onUploadProgress&&f.upload&&f.upload.addEventListener("progress",t.onUploadProgress),t.cancelToken&&t.cancelToken.promise.then((function(t){f&&(f.abort(),d(t),f=null)})),void 0===u&&(u=null),f.send(u)}))}},37694:function(t,e,a){"use strict";var n=a(76798),i=a.n(n)()((function(t){return t[1]}));i.push([t.id,".dialog-vocabulary-info .el-dialog{background:url(/images/vocabulary/bg-info-popup.svg),#fff!important;background-position:top!important;background-repeat:no-repeat!important;border-radius:32px;max-width:1224px!important}.flashcard-popup-overlay{-webkit-box-align:center;-ms-flex-align:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-align-items:center;align-items:center;background-color:rgba(0,0,0,.7);bottom:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;justify-content:center;left:0;position:fixed;right:0;top:0;z-index:1000}.flashcard-popup{max-height:90vh;max-width:800px;width:90%}.flashcard-popup-content{-ms-flex-align:center;-webkit-box-pack:justify;-ms-flex-pack:justify;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;-webkit-justify-content:space-between;justify-content:space-between;padding:30px}.flashcard-nav-button,.flashcard-popup-content{-webkit-box-align:center;-webkit-align-items:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flashcard-nav-button{-ms-flex-align:center;-webkit-box-pack:center;-ms-flex-pack:center;background-color:#ccf8d1;border:none;border-radius:50%;color:#07403f;cursor:pointer;font-size:18px;height:50px;-webkit-justify-content:center;justify-content:center;-webkit-transition:background-color .2s,-webkit-transform .2s;transition:background-color .2s,-webkit-transform .2s;transition:background-color .2s,transform .2s;transition:background-color .2s,transform .2s,-webkit-transform .2s;width:50px}.flashcard-nav-button:hover{background-color:#0a5e5c;-webkit-transform:scale(1.05);-ms-transform:scale(1.05);transform:scale(1.05)}.flashcard-nav-button:disabled{background-color:#ccc;cursor:not-allowed;-webkit-transform:none;-ms-transform:none;transform:none}.flashcard-container{-webkit-box-flex:1;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex:1;-ms-flex:1;flex:1;-webkit-justify-content:center;justify-content:center;margin:0 20px;-webkit-perspective:1000px;perspective:1000px}.flashcard{height:400px;position:relative;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition:-webkit-transform .6s;transition:-webkit-transform .6s;transition:transform .6s;transition:transform .6s,-webkit-transform .6s;width:100%}.flashcard.flipped{-webkit-transform:rotateY(180deg);transform:rotateY(180deg)}.flashcard-back,.flashcard-front{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;-webkit-backface-visibility:hidden;backface-visibility:hidden;border-radius:16px;-webkit-box-shadow:0 4px 10px rgba(0,0,0,.1);box-shadow:0 4px 10px rgba(0,0,0,.1);display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;height:100%;-webkit-justify-content:center;justify-content:center;padding:30px;position:absolute;width:100%}.flashcard-front{background-color:#f8f9fa;border:2px solid #07403f}.flashcard-back{background-color:#07403f;color:#fff;-webkit-transform:rotateY(180deg);transform:rotateY(180deg)}.flashcard-word{font-size:48px;font-weight:700;margin-bottom:20px;text-align:center}.flashcard-reading{color:#666;font-size:24px;text-align:center}.flashcard-meaning{font-size:36px;font-weight:700;margin-bottom:30px;text-align:center}.flashcard-example{max-width:500px;width:100%}.example-jp{font-size:20px;margin-bottom:10px;text-align:center}.example-vi{color:#ccc;font-size:18px;text-align:center}",""]),e.A=i},42537:function(t){"use strict";function e(){this.message="String contains an invalid character"}e.prototype=new Error,e.prototype.code=5,e.prototype.name="InvalidCharacterError",t.exports=function(t){for(var a,n,i=String(t),r="",s=0,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";i.charAt(0|s)||(o="=",s%1);r+=o.charAt(63&a>>8-s%1*8)){if((n=i.charCodeAt(s+=3/4))>255)throw new e;a=a<<8|n}return r}},47763:function(t,e,a){"use strict";var n=a(5449);t.exports=function(t,e,a,i,r){var s=new Error(t);return n(s,e,a,i,r)}},50150:function(t,e,a){"use strict";var n=a(76798),i=a.n(n)()((function(t){return t[1]}));i.push([t.id,".flashcard-module[data-v-f11f69a2]{margin:0 auto;max-width:800px;width:100%}.card-inner[data-v-f11f69a2]{height:100%;position:relative;text-align:center;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition:-webkit-transform .6s;transition:-webkit-transform .6s;transition:transform .6s;transition:transform .6s,-webkit-transform .6s;width:100%}.card-inner.flip[data-v-f11f69a2]{-webkit-transform:rotateY(180deg);transform:rotateY(180deg)}.card__face[data-v-f11f69a2]{-webkit-backface-visibility:hidden;backface-visibility:hidden;border-radius:20px;height:100%;overflow:hidden;position:absolute;width:100%}.card__face--back[data-v-f11f69a2],.card__face--front[data-v-f11f69a2]{background-color:#fff}.card__face--back[data-v-f11f69a2]{-webkit-transform:rotateY(180deg);transform:rotateY(180deg)}.action-button[data-v-f11f69a2]{cursor:pointer;-webkit-transition:-webkit-transform .2s;transition:-webkit-transform .2s;transition:transform .2s;transition:transform .2s,-webkit-transform .2s}.action-button[data-v-f11f69a2]:hover{-webkit-transform:scale(1.1);-ms-transform:scale(1.1);transform:scale(1.1)}.stackedcards-container[data-v-f11f69a2]{max-width:600px;position:relative}.card-item[data-v-f11f69a2],.stackedcards-container[data-v-f11f69a2]{height:500px;margin:0 auto;width:100%}.card-item[data-v-f11f69a2]{background:#fff;border-radius:20px;-webkit-box-shadow:0 2px 15px rgba(0,0,0,.1);box-shadow:0 2px 15px rgba(0,0,0,.1);left:0;position:absolute;right:0;-webkit-transition:opacity .3s,-webkit-transform .3s ease;transition:opacity .3s,-webkit-transform .3s ease;transition:transform .3s ease,opacity .3s;transition:transform .3s ease,opacity .3s,-webkit-transform .3s ease}.stackedcards-overlay[data-v-f11f69a2]{height:100%;left:0;opacity:0;pointer-events:none;position:absolute;top:0;-webkit-transition:opacity .3s;transition:opacity .3s;width:100%}.stackedcards-overlay.left[data-v-f11f69a2]{background:hsla(1,100%,74%,.2);border-radius:20px}.stackedcards-overlay.right[data-v-f11f69a2]{background:rgba(87,208,97,.2);border-radius:20px}",""]),e.A=i},62012:function(t,e,a){"use strict";var n=a(9516);t.exports=function(t){var e,a,i,r={};return t?(n.forEach(t.split("\n"),(function(t){i=t.indexOf(":"),e=n.trim(t.substr(0,i)).toLowerCase(),a=n.trim(t.substr(i+1)),e&&(r[e]=r[e]?r[e]+", "+a:a)})),r):r}},64202:function(t,e,a){"use strict";var n=a(9516);t.exports=n.isStandardBrowserEnv()?function(){var t,e=/(msie|trident)/i.test(navigator.userAgent),a=document.createElement("a");function i(t){var n=t;return e&&(a.setAttribute("href",n),n=a.href),a.setAttribute("href",n),{href:a.href,protocol:a.protocol?a.protocol.replace(/:$/,""):"",host:a.host,search:a.search?a.search.replace(/^\?/,""):"",hash:a.hash?a.hash.replace(/^#/,""):"",hostname:a.hostname,port:a.port,pathname:"/"===a.pathname.charAt(0)?a.pathname:"/"+a.pathname}}return t=i(window.location.href),function(e){var a=n.isString(e)?i(e):e;return a.protocol===t.protocol&&a.host===t.host}}():function(){return!0}},64490:function(t,e,a){"use strict";var n=a(9516),i=a(82881),r=a(93864),s=a(96987);function o(t){t.cancelToken&&t.cancelToken.throwIfRequested()}t.exports=function(t){return o(t),t.headers=t.headers||{},t.data=i(t.data,t.headers,t.transformRequest),t.headers=n.merge(t.headers.common||{},t.headers[t.method]||{},t.headers||{}),n.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),(t.adapter||s.adapter)(t).then((function(e){return o(t),e.data=i(e.data,e.headers,t.transformResponse),e}),(function(e){return r(e)||(o(t),e&&e.response&&(e.response.data=i(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)}))}},65606:function(t){var e,a,n=t.exports={};function i(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function s(t){if(e===setTimeout)return setTimeout(t,0);if((e===i||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(a){try{return e.call(null,t,0)}catch(a){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:i}catch(t){e=i}try{a="function"==typeof clearTimeout?clearTimeout:r}catch(t){a=r}}();var o,l=[],c=!1,d=-1;function u(){c&&o&&(c=!1,o.length?l=o.concat(l):d=-1,l.length&&p())}function p(){if(!c){var t=s(u);c=!0;for(var e=l.length;e;){for(o=l,l=[];++d<e;)o&&o[d].run();d=-1,e=l.length}o=null,c=!1,function(t){if(a===clearTimeout)return clearTimeout(t);if((a===r||!a)&&clearTimeout)return a=clearTimeout,clearTimeout(t);try{return a(t)}catch(e){try{return a.call(null,t)}catch(e){return a.call(this,t)}}}(t)}}function f(t,e){this.fun=t,this.array=e}function m(){}n.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var a=1;a<arguments.length;a++)e[a-1]=arguments[a];l.push(new f(t,e)),1!==l.length||c||s(p)},f.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=m,n.addListener=m,n.once=m,n.off=m,n.removeListener=m,n.removeAllListeners=m,n.emit=m,n.prependListener=m,n.prependOnceListener=m,n.listeners=function(t){return[]},n.binding=function(t){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(t){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},67145:function(t,e,a){"use strict";a.d(e,{A:function(){return z}});function n(t,e,a,n,i,r,s,o){var l,c="function"==typeof t?t.options:t;if(e&&(c.render=e,c.staticRenderFns=a,c._compiled=!0),n&&(c.functional=!0),r&&(c._scopeId="data-v-"+r),s?(l=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(s)},c._ssrRegister=l):i&&(l=o?function(){i.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var d=c.render;c.render=function(t,e){return l.call(e),d(t,e)}}else{var u=c.beforeCreate;c.beforeCreate=u?[].concat(u,l):[l]}return{exports:t,options:c}}var i=n({props:["currentTab"],data:function(){return{searchField:""}},watch:{searchField:function(t){this.$emit("update-search",t)}},methods:{hideMenu:function(){this.$emit("hide-menu")}}},(function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex items-center"},[e("button",{staticClass:"flex-none flex items-center justify-between border-none bg-[#F5F5F5] w-[85px] h-[40px] px-4 rounded-full",on:{click:t.hideMenu}},[e("img",{staticClass:"h-[14px]",attrs:{src:"/images/icons/hide.png",alt:"hide.png"}}),t._v(" "),e("span",{staticClass:"text-sm font-averta-regular"},[t._v("Ẩn")])]),t._v(" "),"comment"!=t.currentTab?e("div",{staticClass:"relative w-full ml-5"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchField,expression:"searchField"}],staticClass:"pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full sp:hidden",attrs:{type:"text",placeholder:"Tìm kiếm bài học với từ khoá bất kì"},domProps:{value:t.searchField},on:{input:function(e){e.target.composing||(t.searchField=e.target.value)}}}),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchField,expression:"searchField"}],staticClass:"pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full desktop:hidden",attrs:{type:"text",placeholder:"Tìm kiếm bài học"},domProps:{value:t.searchField},on:{input:function(e){e.target.composing||(t.searchField=e.target.value)}}}),t._v(" "),e("div"),t._v(" "),e("img",{staticClass:"absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]",class:"".concat(t.searchField.length>0?"block":"hidden"),attrs:{src:"/images/icons/clear.png",alt:"clear.png"},on:{click:function(e){t.searchField=""}}}),t._v(" "),e("img",{staticClass:"absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]",class:"".concat(0===t.searchField.length?"block":"hidden"),attrs:{src:"/images/icons/search.png",alt:"search.png"}})]):t._e()])}),[],!1,null,null,null).exports,r={props:{percent:{type:[Number,String],default:0,validator:function(t){var e=parseFloat(t);return!isNaN(e)&&e>=0&&e<=100}},widthPercent:{type:String,default:"120px"},color:{type:String,default:"#57D061"},height:{type:String,default:"10px"},background:{type:String,default:"white"}},data:function(){return{width:"0%",height:"10px",background:"white",color:"#57D061",widthPercent:"120px"}},computed:{computedWidth:function(){var t=parseFloat(this.percent);return isNaN(t)?"0%":"".concat(t,"%")}}},s=a(85072),o=a.n(s),l=a(87935),c={insert:"head",singleton:!1},d=(o()(l.A,c),l.A.locals,n(r,(function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"progress-bar",style:{height:t.height,background:t.background,width:t.widthPercent}},[e("div",{staticClass:"progress-bar-inner",style:{width:t.computedWidth,background:t.color}})])])}),[],!1,null,"2b96e190",null).exports),u=a(11985),p={name:"FlashCardModule",props:{cards:{type:Array,required:!0,default:function(){return[]}},isJapanese:{type:Boolean,default:!0}},data:function(){return{stackedCardsInstance:null,currentPosition:0}},mounted:function(){this.initStackedCards()},methods:{initStackedCards:function(){var t=this;this.$nextTick((function(){t.stackedCardsInstance=new u.A({visibleItems:3,margin:22,rotate:!0,useOverlays:!0}),t.updateCurrentFlashcard()}))},updateCurrentFlashcard:function(){this.stackedCardsInstance&&(this.currentPosition=this.stackedCardsInstance.currentPosition,this.cards[this.currentPosition]&&this.$emit("card-changed",{card:this.cards[this.currentPosition],position:this.currentPosition}),this.currentPosition>=this.cards.length&&this.$emit("cards-completed"))},swipeLeft:function(){if(this.stackedCardsInstance){var t=this.stackedCardsInstance.currentPosition,e=this.cards[t];e&&this.$emit("card-swiped",{direction:"left",card:e,position:t}),this.stackedCardsInstance.swipeLeft(),this.updateCurrentFlashcard()}},swipeRight:function(){if(this.stackedCardsInstance){var t=this.stackedCardsInstance.currentPosition,e=this.cards[t];e&&this.$emit("card-swiped",{direction:"right",card:e,position:t}),this.stackedCardsInstance.swipeRight(),this.updateCurrentFlashcard()}},playAudio:function(t){t&&(this.$emit("play-audio",t),new Audio("https://video-test.dungmori.com/audios/".concat(t)).play())},flipCard:function(){this.$emit("flip-card")},resetCards:function(){this.stackedCardsInstance&&(this.stackedCardsInstance.destroy(),this.stackedCardsInstance=null),this.initStackedCards()}},watch:{cards:{handler:function(){this.resetCards()},deep:!0}},beforeDestroy:function(){this.stackedCardsInstance&&this.stackedCardsInstance.destroy()}},f=a(50150),m={insert:"head",singleton:!1};o()(f.A,m),f.A.locals,n(p,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"flashcard-module relative"},[e("div",{staticClass:"stackedcards stackedcards--animatable a_cursor--pointer",attrs:{id:"stacked-cards-block"}},[e("div",{staticClass:"stackedcards-container",staticStyle:{"margin-bottom":"20px"}},t._l(t.cards,(function(a,n){return e("div",{key:a.id,staticClass:"card-item",class:{"stackedcards-active":0===n,"stackedcards-top":!0,"stackedcards--animatable":!0,"stackedcards-origin-top":!0},attrs:{"data-id":a.id}},[e("div",{staticClass:"card-inner",class:{flip:!t.isJapanese},attrs:{"data-id":a.id}},[e("div",{staticClass:"card__face card__face--jp card__face--front"},[e("div",{staticClass:"card-wrap p-4 h-[90%] overflow-y-auto relative"},[e("div",{staticClass:"card_header flex items-center"},[a.value&&a.value.audio?e("div",{staticClass:"card_audio noFlip mr-2 cursor-pointer",on:{click:function(e){return t.playAudio(a.value.audio)}}},[e("svg",{attrs:{width:"34",height:"34",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})])]):t._e(),t._v(" "),e("div",{staticClass:"card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",domProps:{innerHTML:t._s(a.value&&a.value.word_stress)}})]),t._v(" "),e("div",{staticClass:"card_content min-h-[calc(100%-34px)] flex flex-col"},[e("div",{staticClass:"content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex",staticStyle:{"flex-grow":"1"},domProps:{innerHTML:t._s(a.value&&a.value.word)}}),t._v(" "),a.value&&a.value.front_image?e("div",{staticClass:"content-img text-center p-[40px]"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(a.value.front_image)}})]):t._e(),t._v(" "),a.value&&a.value.example&&a.value.example.length?e("div",{staticClass:"example-wrap"},[e("div",{staticClass:"example-title font-beanbag-medium text-[#757575] text-xl"},[t._v("\n                      Ví dụ:\n                    ")]),t._v(" "),t._l(a.value.example,(function(a,n){return e("div",{key:n,staticClass:"example-content font-gen-jyuu-gothic-medium text-[#07403F] text-2xl"},[e("div",{domProps:{innerHTML:t._s(a)}})])}))],2):t._e()])])]),t._v(" "),e("div",{staticClass:"card__face card__face--vi card__face--back"},[e("div",{staticClass:"card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"},[e("div",{staticClass:"card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",staticStyle:{"flex-grow":"1"}},[e("div",{staticClass:"text-center",domProps:{innerHTML:t._s(a.value&&a.value.meaning)}})]),t._v(" "),e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",domProps:{innerHTML:t._s(a.value&&a.value.kanji_meaning)}}),t._v(" "),a.value&&a.value.back_image?e("div",{staticClass:"card_img_back p-[40px] content-img text-center"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(a.value.back_image)}})]):t._e(),t._v(" "),a.value&&a.value.meaning_example&&a.value.meaning_example.length?e("div",{staticClass:"example-wrap"},[e("div",{staticClass:"example-title font-beanbag-medium text-[#757575] text-xl"},[t._v("\n                    Ví dụ:\n                  ")]),t._v(" "),t._l(a.value.meaning_example,(function(a,n){return e("div",{key:n,staticClass:"example-content font-beanbag-medium text-[#07403F] text-2xl"},[e("div",{domProps:{innerHTML:t._s(a)}})])}))],2):t._e()])])])])})),0),t._v(" "),e("div",{staticClass:"stackedcards-overlay top"}),t._v(" "),t._m(0),t._v(" "),t._m(1)])])}),[function(){var t=this._self._c;return t("div",{staticClass:"stackedcards-overlay right"},[t("span",{staticClass:"stackedcards-overlay-img"})])},function(){var t=this._self._c;return t("div",{staticClass:"stackedcards-overlay left"},[t("span",{staticClass:"stackedcards-overlay-img"})])}],!1,null,"f11f69a2",null).exports,a(72505);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function h(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,n)}return a}function g(t,e,a){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var n=a.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}var b={OVERVIEW:1,SEARCH:2,STACKED_CARD:3,RESULT:4},x=[{id:1,word:"もり",description:"Rừng, rừng rậm",specialized:"JLPT N5"},{id:2,word:"はな",description:"Lá",specialized:"JLPT N4"},{id:3,word:"きのう",description:"Hôm qua",specialized:"Chuyên ngành Thực phẩm",lock:!0},{id:4,word:"あした",description:"Ngày mai",specialized:"Chuyên ngành Xây dựng",lock:!0},{id:5,word:"もり",description:"Rừng, rừng rậm",specialized:"JLPT N5",lock:!0},{id:6,word:"はな",description:"Lá",specialized:"JLPT N4",lock:!0},{id:7,word:"きのう",description:"Hôm qua",specialized:"Chuyên ngành Thực phẩm",lock:!0},{id:8,word:"あした",description:"Ngày mai",specialized:"Chuyên ngành Xây dựng",lock:!0},{id:9,word:"もり",description:"Rừng, rừng rậm",specialized:"JLPT N5",lock:!0},{id:10,word:"はな",description:"Lá",specialized:"JLPT N4",lock:!0},{id:11,word:"きのう",description:"Hôm qua",specialized:"Chuyên ngành Thực phẩm",lock:!0},{id:12,word:"あした",description:"Ngày mai",specialized:"Chuyên ngành Xây dựng",lock:!0}],_=[{id:103570,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:'<p><span style="color:#ff9900"><ruby>日本</ruby></span></p><p><ruby><rt><span style="color:#ff9900">にほん</span></rt></ruby><span style="color:#ff9900">の&nbsp;</span></p>',word_stress:'<p><span style="text-decoration:overline">にほん</span>の</p>',word_type:"Danh từ",meaning:"<p>NHẬT</p><p>Tiếng Nhật của bạn c&oacute; tốt kh&ocirc;ng thế</p>",audio:"2025-04-11/sample-3s.mp3",front_image:"2025-04-11/WebStore-ld4_0.png",back_image:null,example:[{example:'<p><span style="color:#ef6d13"><span style="text-decoration:overline">特定原</span></span>材料とくてい<span style="text-decoration:overline">げん</span>ざいりょうに準<span style="text-decoration:overline">じゅんず</span>るもの</p>',audio:"2025-04-11/sample-6s.mp3"},{example:"<p><ruby>機器<rt>きき</rt></ruby>や<ruby>器具</ruby></p>",audio:"2025-04-11/sample-3s.mp3"}],meaning_example:['<p>B&aacute;c của t&ocirc;i l&agrave; gi&aacute;o vi&ecirc;n dạy<span style="color:#ef6d13"> tiếng Nhật&nbsp;</span>tại trung t&acirc;m Nhật ngữ Dũng Mori ieo EOKJ JNF kf lkp;wpe njjgbj; hhe kjijwi&nbsp;</p>',"<p>Học tiếng Nhật c&oacute; kh&oacute; kh&ocirc;ng?</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:1,show:1,is_quiz:0,video_full:0,updated_at:"2025-05-13T01:14:59.000000Z",created_at:"2025-04-11T07:57:41.000000Z",answers:[],comment:{id:463576,content:"cmt 1",user_id:540422,count_like:3,created_at:"2025-04-29 07:26:47",pin:0,parent_id:0,table_id:103570,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Quỳnh Anh",avatar:"1711587724_0_76076.jpeg",userId:540422},time_created:"29/04/2025 07:26",replies:[{id:463577,table_id:103570,table_name:"flashcard",user_id:540422,admin_log:null,kwadmin_id:null,content:"124",img:null,audio:null,rate:0,is_tester:0,parent_id:463576,count_like:2,ulikes:null,readed:0,tag_data:null,status:0,pin:0,is_correct:0,updated_at:"2025-04-29 07:27:03",created_at:"2025-04-29 07:27:03",user_info:{email:"<EMAIL>",name:"Quỳnh Anh",avatar:"1711587724_0_76076.jpeg",userId:540422},time_created:"29/04/2025 07:27",replies:null,table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""}}],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},{id:103571,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>はな</p>",word_stress:'<p><span style="text-decoration:overline">は</span>な</p>',word_type:"Danh từ",meaning:"<p>Hoa</p>",audio:null,front_image:null,back_image:"2025-04-11/1712378501768.jpg",example:[{example:"<p>は他たの</p>",audio:null},{example:"<p>なった疑うたがい</p>",audio:null}],meaning_example:["<p>Đi ăn cơm</p>","<p>Học b&agrave;i đến 10h tối.</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:2,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-11T07:59:29.000000Z",created_at:"2025-04-11T07:59:29.000000Z",answers:[],comment:{id:463284,content:"bdbbdb",user_id:540308,count_like:3,created_at:"2025-04-15 15:29:28",pin:0,parent_id:0,table_id:103571,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Thu Vũ",avatar:"1711587724_0_76076.jpeg",userId:540308},time_created:"15/04/2025 15:29",replies:[],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[{comment_id:463284,user_id:540308}]}},{id:103572,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p><ruby>作業<rt>さぎょう</rt></ruby>ス</p>",word_stress:'<p><span style="text-decoration:overline">作業さ</span>ぎょうス</p>',word_type:null,meaning:"<p>Cuộc sống</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>作業さぎ</p>",audio:null}],meaning_example:["<p>Cuộc sống ở Nhật</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:3,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-11T08:00:45.000000Z",created_at:"2025-04-11T08:00:45.000000Z",answers:[],comment:{id:463306,content:"hello",user_id:540308,count_like:0,created_at:"2025-04-19 15:34:10",pin:0,parent_id:0,table_id:103572,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Thu Vũ",avatar:"1711587724_0_76076.jpeg",userId:540308},time_created:"19/04/2025 15:34",replies:[],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},{id:103573,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>ならないよう</p>",word_stress:'<p>なら<span style="text-decoration:overline">ない</span>よう</p>',word_type:"Động từ",meaning:"<p>Th&uacute; vị</p>",audio:null,front_image:null,back_image:null,example:[],meaning_example:[],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:4,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-11T08:01:41.000000Z",created_at:"2025-04-11T08:01:41.000000Z",answers:[],comment:{id:463604,content:"fgfg",user_id:540308,count_like:0,created_at:"2025-05-09 09:08:20",pin:0,parent_id:0,table_id:103573,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Thu Vũ",avatar:"1711587724_0_76076.jpeg",userId:540308},time_created:"09/05/2025 09:08",replies:[],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},{id:103574,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>わたしたち</p>",word_stress:'<p><span style="text-decoration:overline">わたし</span>たち</p>',word_type:"Danh từ",meaning:"<p>Ch&uacute;ng t&ocirc;i</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>わたしたちはズンもりのがくせいです。</p>",audio:null}],meaning_example:["<p>Ch&uacute;ng t&ocirc;i l&agrave; người Việt Nam</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:5,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-11T08:03:10.000000Z",created_at:"2025-04-11T08:03:10.000000Z",answers:[],comment:{id:463322,content:"25346666",user_id:540308,count_like:0,created_at:"2025-04-21 11:24:47",pin:0,parent_id:0,table_id:103574,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Thu Vũ",avatar:"1711587724_0_76076.jpeg",userId:540308},time_created:"21/04/2025 11:24",replies:[],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},{id:103575,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>かれ</p>",word_stress:null,word_type:null,meaning:"<p>Anh ấy, bạn trai</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>かれはにほんじんですか。</p>",audio:null}],meaning_example:["<p>Anh ấy l&agrave; người Nhật &agrave;?</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:6,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-11T08:04:15.000000Z",created_at:"2025-04-11T08:04:15.000000Z",answers:[],comment:{id:463276,content:"ggg",user_id:540309,count_like:0,created_at:"2025-04-15 14:42:52",pin:0,parent_id:0,table_id:103575,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Dinhsuu",avatar:"1711587724_0_76076.jpeg",userId:540309},time_created:"15/04/2025 14:42",replies:[],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},{id:103576,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>あなた</p>",word_stress:'<p>あ<span style="text-decoration:overline">な</span>た</p>',word_type:"Danh từ",meaning:"<p>Anh/ chị/ &ocirc;ng/ b&agrave;, bạn (ng&ocirc;i thứ 2 số &iacute;t)</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>あなたはベトナムじんですか。</p>",audio:null}],meaning_example:["<p>Bạn l&agrave; người Việt Nam phải kh&ocirc;ng?</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:7,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-21T03:57:27.000000Z",created_at:"2025-04-11T08:05:08.000000Z",answers:[],comment:{id:463320,content:"hfh",user_id:540416,count_like:0,created_at:"2025-04-21 10:58:39",pin:0,parent_id:0,table_id:103576,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"2104",avatar:"1711587724_0_76076.jpeg",userId:540416},time_created:"21/04/2025 10:58",replies:[],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},{id:103577,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>おくさん</p>",word_stress:null,word_type:null,meaning:"<p>Vợ (người kh&aacute;c)<br />lưu &yacute;: khi muốn hỏi hay giới thiệu vợ của ai đ&oacute; th&igrave; sử dụng từ n&agrave;y.</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>かれのおくさんはズンもりの　せんせいです。</p>",audio:null}],meaning_example:["<p>Vợ của anh ấy l&agrave; gi&aacute;o vi&ecirc;n dạy tiếng Nhật c&ugrave;ng trung t&acirc;m với t&ocirc;i</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:8,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-11T08:23:53.000000Z",created_at:"2025-04-11T08:23:53.000000Z",answers:[],comment:null},{id:103578,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>どなた</p>",word_stress:null,word_type:null,meaning:"<p>Vị n&agrave;o</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>ゆかせんせいはどなたですか。</p>",audio:null}],meaning_example:["<p>C&ocirc; Yuka l&agrave; ai thế?</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:9,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-11T08:24:51.000000Z",created_at:"2025-04-11T08:24:51.000000Z",answers:[],comment:null},{id:103598,lesson_id:10847,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<ruby>私<rt>わたし</rt></ruby>たち",word_stress:'<span style="text-decoration:overline">わたし</span>たち',word_type:"Danh từ",meaning:"Chúng tôi",audio:null,front_image:null,back_image:null,example:[{example:'<span style="color:#EF6D13">わたしたち</span>はがくせいです。',audio:null}],meaning_example:['<span style="color:#EF6D13">Chúng tôi</span> là học sinh.'],quiz_question:[],kanji_meaning:"Hoa"},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:10,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-21T03:54:16.000000Z",created_at:"2025-04-21T00:04:31.000000Z",answers:[],comment:{id:463321,content:"123",user_id:540308,count_like:0,created_at:"2025-04-21 11:24:28",pin:0,parent_id:0,table_id:103598,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Thu Vũ",avatar:"1711587724_0_76076.jpeg",userId:540308},time_created:"21/04/2025 11:24",replies:[],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}}],w={components:{ProgressBar:d},props:["courseId"],name:"FlashCard",watch:{currentView:function(t,e){console.log("currentView: ",t),t===b.STACKED_CARD&&this.initStackedCards()}},data:function(){return{listVocabulary:[],TYPES_VIEW:b,currentView:b.OVERVIEW,searchResults:x,searchQuery:"",dataFlashCard:_,isJapanese:!0}},created:function(){this.getListVocabulary()},methods:{getListVocabulary:function(){var t=this;this.listVocabulary=this.listVocabulary.filter((function(e){return e.course_id===t.courseId}))},playAudio:function(t,e){console.log("Play audio for card id: ".concat(t,", url: ").concat(e))},toggleCommentTab:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"open",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};console.log("mo tab cmt with handle: ".concat(t));var a=function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?h(Object(a),!0).forEach((function(e){g(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):h(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({handle:t,flashcard:this.dataFlashcard[0]},e);this.$emit("open-comment-tab",a);var n=new CustomEvent("open-comment-tab",{detail:a});document.dispatchEvent(n)},initStackedCards:function(){var t=this;this.$nextTick((function(){t.stackedCardsInstance=new u.A({visibleItems:3,margin:22,rotate:!0,useOverlays:!0})})),this.updateCurrentFlashcard()},updateCurrentFlashcard:function(){if(this.stackedCardsInstance){var t=this.stackedCardsInstance.currentPosition,e=this.dataFlashcard[t];if(e){console.log("Current flashcard updated:",e);var a=this.$parent.$children.find((function(t){return"container"===t.$options.name}));a?a.setCurrentFlashcard(e):console.error("Container component not found")}}}},mounted:function(){window.onfocus=function(){console.log("window is focused")},window.onblur=function(){console.log("window is blurred")}}},y=n(w,(function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"flashcard-wrap"},[t.currentView===t.TYPES_VIEW.OVERVIEW?e("div",{staticClass:"flex flex-col justify-center items-center w-[90%] max-w-[1000px]"},[t._m(0),t._v(" "),e("div",{staticClass:"w-full"},t._l(4,(function(a){return e("div",{staticClass:"max-w-[420px] mx-auto progress-item flex justify-between items-center mb-4"},[e("div",{staticClass:"font-averta-regular text-[20px] text-[#1E1E1E]"},[t._v("Đã học")]),t._v(" "),e("progress-bar",{attrs:{percent:20*a}}),t._v(" "),e("div",{staticClass:"font-averta-regular text-[20px] text-[#757575]"},[t._v(t._s(20*a)+"%・"+t._s(280*a)+" từ")])],1)})),0),t._v(" "),e("div",{staticClass:"w-[340px]"},[e("div",{staticClass:"w-full text-xl text-[#07403F] font-beanbag rounded-full p-3 mt-10 flex justify-between items-center border-[1px] border-[#07403F] cursor-pointer",on:{click:function(e){t.currentView=t.TYPES_VIEW.SEARCH}}},[e("div",{staticClass:"text-[#07403F] font-averta-regular text-xl"},[t._v("\n            Danh sách toàn bộ từ\n          ")]),t._v(" "),e("div",[e("svg",{attrs:{width:"8",height:"8",viewBox:"0 0 8 8",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M0.5 4H7.5M7.5 4L4 0.5M7.5 4L4 7.5",stroke:"#07403F","stroke-linecap":"round","stroke-linejoin":"round"}})])])]),t._v(" "),e("div",{staticClass:"text-center font-averta-regular text-xl text-[#757575] italic mt-3 text-[#EF6D13]"},[t._v("\n          *Học thử 300 từ đầu tiên trong bộ từ vựng JLPT N5\n        ")])]),t._v(" "),e("button",{staticClass:"w-full bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer"},[t._v("\n        học ngay\n      ")])]):t._e(),t._v(" "),t.currentView===t.TYPES_VIEW.SEARCH?e("div",[e("div",{staticClass:"flex justify-between items-center mb-10"},[e("div",{staticClass:"flex items-center"},[e("div",{staticClass:"vocabulary-search-btn-back h-[65px] w-[65px]",on:{click:function(e){t.currentView=t.TYPES_VIEW.OVERVIEW}}},[e("i",{staticClass:"fas fa-arrow-left"})]),t._v(" "),t._m(1)]),t._v(" "),e("div",{staticClass:"vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]"},[t._m(2),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchQuery,expression:"searchQuery"}],staticClass:"search-input leading-[65px]",attrs:{type:"text",placeholder:"Nhập từ vựng muốn tìm kiếm"},domProps:{value:t.searchQuery},on:{input:function(e){e.target.composing||(t.searchQuery=e.target.value)}}})])]),t._v(" "),t.searchResults.length>0?e("div",{staticClass:"search-result list-favorite"},[e("div",{staticClass:"search-result-items"},t._l(t.searchResults,(function(a,n){return e("div",{key:n,staticClass:"search-result-item cursor-pointer relative"},[e("div",{staticClass:"search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] pt-3 pb-3",on:{click:function(e){return t.alert(1)}}},[e("div",{staticClass:"search-result-item-title font-gen-jyuu-gothic-medium text-bold text-black text-[20px]"},[t._v("\n                "+t._s(a.word)+"\n              ")]),t._v(" "),e("div",{staticClass:"search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"},[t._v("\n                "+t._s(a.description)+"\n              ")]),t._v(" "),e("div",{staticClass:"search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"},[t._v("\n                "+t._s(a.specialized)+"\n              ")])]),t._v(" "),a.lock?e("div",{staticClass:"absolute h-full top-0 left-0 w-full flex items-center",staticStyle:{background:"linear-gradient(to right, rgba(234, 246, 235, 1), rgba(255, 255, 255, 0))"}},[e("i",{staticClass:"fas fa-lock text-white text-4xl text-[#757575] ml-[32px]"})]):t._e()])})),0)]):t._e(),t._v(" "),e("button",{staticClass:"fixed bottom-[56px] left-[50%] transform w-full bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] w-[340px] cursor-pointer",on:{click:function(e){t.currentView=t.TYPES_VIEW.STACKED_CARD}}},[t._v("\n        bắt đầu\n      ")])]):t._e(),t._v(" "),t.currentView===t.TYPES_VIEW.STACKED_CARD?e("div",{staticClass:"flashcards-wrap"},[t._m(3),t._v(" "),e("div",{staticClass:"cards-wrap content mx-auto w-[80%] max-w-[648px]"},[e("div",{staticClass:"content-wrap mx-auto mb-6"},[e("div",{staticClass:"stackedcards stackedcards--animatable a_cursor--pointer",attrs:{id:"stacked-cards-block"}},[e("div",{staticClass:"stackedcards-container",staticStyle:{"margin-bottom":"20px"}},t._l(t.dataFlashCard,(function(a,n){return e("div",{key:a.id,staticClass:"card-item",class:{"stackedcards-active":0===n,"stackedcards-top":!0,"stackedcards--animatable":!0,"stackedcards-origin-top":!0},attrs:{"data-id":a.id}},[e("div",{staticClass:"card-inner",class:{flip:!t.isJapanese},attrs:{"data-id":a.id}},[e("div",{staticClass:"card__face card__face--jp card__face--front"},[e("div",{staticClass:"card-wrap p-4 h-[90%] overflow-y-auto relative"},[e("div",{staticClass:"card_header flex items-center"},[a.value.audio?e("div",{staticClass:"card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer",on:{click:function(e){return t.playAudio(a.id,a.value.audio)}}},[e("svg",{staticClass:"noFlip",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})])]):t._e(),t._v(" "),e("div",{staticClass:"card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",domProps:{innerHTML:t._s(a.value.word_stress)}})]),t._v(" "),e("div",{staticClass:"card_content min-h-[calc(100%-34px)] flex flex-col"},[e("div",{staticClass:"content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex",staticStyle:{"flex-grow":"1"},domProps:{innerHTML:t._s(a.value.word)}}),t._v(" "),a.value.front_image?e("div",{staticClass:"content-img text-center p-[40px]"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(a.value.front_image)}})]):t._e(),t._v(" "),a.value.example.length?e("div",{staticClass:"example-wrap"},[e("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[t._v("\n                            Ví dụ\n                          ")]),t._v(" "),e("div",{staticClass:"list-example"},[t._l(a.value.example,(function(n,i){return[e("div",{staticClass:"example-item flex items-start mb-1"},[n.audio?e("svg",{staticClass:"w-[36px] h-[36px] noFlip cursor-pointer",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},on:{click:function(e){return t.playAudio(a.id+"_example_"+i,n.audio)}}},[e("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})]):t._e(),t._v(" "),e("div",{staticClass:"ml-2 font-beanbag-regular text-2xl flex items-start w-[90%]"},[t._v("\n                                  "+t._s(i+1)+". "),e("span",{staticClass:"ml-2",domProps:{innerHTML:t._s(n.example)}})])])]}))],2)]):t._e()])]),t._v(" "),e("div",{staticClass:"card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]"},[e("svg",{staticClass:"m-3 noFlip",attrs:{width:"33",height:"28",viewBox:"0 0 33 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z",fill:"#FF7C79",stroke:"#FF7C79"}})])])]),t._v(" "),e("div",{staticClass:"card__face card__face--vi card__face--back"},[e("div",{staticClass:"card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"},[e("div",{staticClass:"card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",staticStyle:{"flex-grow":"1"}},[e("div",{staticClass:"text-center",domProps:{innerHTML:t._s(a.value.meaning)}})]),t._v(" "),e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",domProps:{innerHTML:t._s(a.value.kanji_meaning)}}),t._v(" "),a.value.back_image?e("div",{staticClass:"card_img_back p-[40px] content-img text-center"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(a.value.back_image)}})]):t._e(),t._v(" "),a.value.meaning_example.length?e("div",{staticClass:"example-wrap"},[e("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[t._v("\n                          Ví dụ\n                        ")]),t._v(" "),e("div",{staticClass:"list-example"},[t._l(a.value.meaning_example,(function(a,n){return[e("div",{staticClass:"example-item flex items-center mb-1"},[e("div",{staticClass:"ml-2 font-averta-regular text-2xl flex items-start"},[t._v("\n                                "+t._s(n+1)+". "),e("span",{staticClass:"ml-1",domProps:{innerHTML:t._s(a)}})])])]}))],2)]):t._e()]),t._v(" "),e("div",{staticClass:"how-remember-wrap px-4 h-[40%] flex flex-col"},[t._m(4,!0),t._v(" "),a.comment&&a.comment.user_info?e("div",{staticClass:"how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"},[e("div",{staticClass:"how-remember-wrap-avatar w-[28px]"},[e("img",{staticClass:"rounded-full",attrs:{src:"/cdn/avatar/small/".concat(a.comment.user_info.avatar)}})]),t._v(" "),e("div",{staticClass:"how-remember-wrap-info flex text-[#073A3B]"},[e("span",{staticClass:"font-averta-bold"},[t._v("\n                          "+t._s(a.comment.user_info.name)+"・\n                        ")]),t._v(" "),e("span",{staticClass:"font-averta-regular"},[t._v("\n                          "+t._s(a.comment.time_created)+"\n                        ")]),t._v(" "),a.comment.pin?e("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z",fill:"#B3B3B3"}})]):t._e()]),t._v(" "),e("div",{staticClass:"col-start-2 font-averta-regular text-[#07403F]",staticStyle:{display:"-webkit-box","-webkit-line-clamp":"2","-webkit-box-orient":"vertical",overflow:"hidden","text-overflow":"ellipsis"}},[t._v("\n                          "+t._s(a.comment.content)+"\n                        ")]),t._v(" "),e("div",{staticClass:"col-start-2 flex justify-between items-center"},[e("div",{staticClass:"font-averta-regular text-[#009951] flex"},[e("div",{staticClass:"flex items-center mr-5"},[e("svg",{staticClass:"mr-1 noFlip",attrs:{width:"13",height:"12",viewBox:"0 0 13 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",fill:a.comment.comment_like.length?"#009951":"none",stroke:"#009951"}})]),t._v("\n                              "+t._s(a.comment.count_like)+"\n                            ")]),t._v(" "),a.comment&&a.comment.replies?e("div",[t._v("\n                              "+t._s(a.comment.replies.length)+" Trả lời\n                            ")]):t._e()]),t._v(" "),e("div",{staticClass:"underline decoration-solid text-[#009951] cursor-pointer noFlip",on:{click:function(e){return t.toggleCommentTab("open")}}},[t._v("\n                            Xem thêm >>\n                          ")])])]):e("div",{staticClass:"items-center flex grow",staticStyle:{"flex-grow":"1"}},[e("div",{staticClass:"underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip",on:{click:function(e){return t.toggleCommentTab("open")}}},[t._v("\n                          Đóng góp cách nhớ của bạn >>\n                        ")])])])])])])})),0),t._v(" "),e("div",{staticClass:"stackedcards--animatable stackedcards-overlay left stackedcards-origin-top font-averta-bold text-[#975102]"},[t._v("\n              Chưa nhớ\n            ")]),t._v(" "),e("div",{staticClass:"stackedcards--animatable stackedcards-overlay right stackedcards-origin-top font-averta-bold text-[#02542D]"},[t._v("\n              Đã học\n            ")])])])])]):t._e()])])}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"w-full text-center p-4 rounded-3xl mb-6",staticStyle:{background:"linear-gradient(to bottom, #FFFFFF, #F4F5FA)"}},[e("div",{staticClass:"img mb-7"},[e("img",{attrs:{src:"/images/vocabulary/img-over-view-course.svg",alt:""}})]),t._v(" "),e("div",{staticClass:"title text-center text-[#07403F]"},[e("div",{staticClass:"font-beanbag-medium text-[20px] uppercase"},[t._v("Bộ từ vựng")]),t._v(" "),e("div",{staticClass:"font-zuume-semibold text-[48px] text-bold uppercase"},[t._v("jlpt n5")]),t._v(" "),e("div",{staticClass:"font-averta-regular text-[20px]"},[t._v("Học và ghi nhớ 1122 từ vựng thuộc cấp độ N5")])])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-section-favorite-title"},[e("div",{staticClass:"flex font-beanbag-medium text-[24px] text-[#07403F] items-center"},[t._v("\n              Từ vựng yêu thích\n              "),e("i",{staticClass:"fas fa-heart text-[#FF7C79] ml-2"})]),t._v(" "),e("div",{staticClass:"font-beanbag-regular text-[20px] text-[#07403F]"},[t._v("1122 từ")])])},function(){var t=this._self._c;return t("div",{staticClass:"search-icon"},[t("i",{staticClass:"fas fa-search"})])},function(){var t=this._self._c;return t("div",{staticClass:"mx-auto w-[80%] max-w-[648px]"},[t("div",{staticClass:"tag_card rounded-full px-2 py-1 bg-[#14AE5C] font-beanbag-medium text-white text-base flex items-center mb-4",staticStyle:{"line-height":"1",width:"fit-content","align-self":"flex-start"}},[this._v("\n          Từ vựng ôn lại\n        ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"how-remember-wrap-header flex mb-5"},[e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl"},[t._v("\n                          Cách nhớ\n                        ")]),t._v(" "),e("div",{staticClass:"border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"})])}],!1,null,"618570a6",null).exports,C={OVERVIEW:1,SEARCH:2,SPECIALIZED:3,FAVORITE:4},k=[{id:1,word:"もり",description:"Rừng, rừng rậm",specialized:"JLPT N5"},{id:2,word:"はな",description:"Lá",specialized:"JLPT N4"},{id:3,word:"きのう",description:"Hôm qua",specialized:"Chuyên ngành Thực phẩm"},{id:4,word:"あした",description:"Ngày mai",specialized:"Chuyên ngành Xây dựng"},{id:4,word:"もり",description:"Rừng, rừng rậm",specialized:"JLPT N5"},{id:5,word:"はな",description:"Lá",specialized:"JLPT N4"},{id:6,word:"きのう",description:"Hôm qua",specialized:"Chuyên ngành Thực phẩm"},{id:7,word:"あした",description:"Ngày mai",specialized:"Chuyên ngành Xây dựng"},{id:8,word:"もり",description:"Rừng, rừng rậm",specialized:"JLPT N5"},{id:9,word:"はな",description:"Lá",specialized:"JLPT N4"},{id:10,word:"きのう",description:"Hôm qua",specialized:"Chuyên ngành Thực phẩm"},{id:11,word:"あした",description:"Ngày mai",specialized:"Chuyên ngành Xây dựng"}],F=[{id:103569,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>わたし</p>",word_stress:'<p>わ<span style="text-decoration:overline">たしr</span></p>',word_type:"Động từ",meaning:"<p>3</p>",audio:null,front_image:null,back_image:null,example:[{example:'<p><span style="color:#ef6d13">わたし</span>はきょうしです。</p>',audio:null}],meaning_example:['<p><span style="color:#ef6d13">T&ocirc;i</span> l&agrave; gi&aacute;o vi&ecirc;n.</p>'],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:3,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:45:14.000000Z",created_at:"2025-04-10T01:40:02.000000Z",answers:[]},{id:103579,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>風邪を</p>",word_stress:'<p><span style="text-decoration:overline">風</span>邪を</p>',word_type:"Động từ",meaning:"<p>4</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>風邪をひいて</p>",audio:null}],meaning_example:["<p>T&ocirc;i đi chơi với bạn</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:6,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:45:29.000000Z",created_at:"2025-04-14T01:35:44.000000Z",answers:[]},{id:103580,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>なぐられている</p>",word_stress:'<p>なぐ<span style="text-decoration:overline">られて</span>いる</p>',word_type:"Danh từ",meaning:"<p>5</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>ハンマーでなぐられている</p>",audio:null}],meaning_example:["<p>Tết n&agrave;y t&ocirc;i mua hoa mai</p>"],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:7,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:45:40.000000Z",created_at:"2025-04-14T01:36:43.000000Z",answers:[]},{id:103581,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p><ruby>隣<rt>となり</rt></ruby>の<ruby>部<rt>へ</rt></ruby></p>",word_stress:'<p><span style="text-decoration:overline">とな</span>りの</p>',word_type:"Tính từ đuôi な",meaning:"<p>6</p>",audio:null,front_image:null,back_image:null,example:[],meaning_example:[],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:8,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:45:50.000000Z",created_at:"2025-04-14T01:37:36.000000Z",answers:[]},{id:103582,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>優しくて</p>",word_stress:'<p><span style="text-decoration:overline">優しく</span>て</p>',word_type:"Động từ",meaning:"<p>7</p>",audio:null,front_image:null,back_image:null,example:[{example:"<p>ttet</p>",audio:null}],meaning_example:[],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:9,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:46:00.000000Z",created_at:"2025-04-14T01:37:52.000000Z",answers:[]},{id:103583,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>パーテ</p>",word_stress:'<p><span style="text-decoration:overline">パー</span>テ</p>',word_type:"Tính từ đuôi い",meaning:"<p>8</p>",audio:null,front_image:null,back_image:null,example:[],meaning_example:[],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:10,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:46:11.000000Z",created_at:"2025-04-14T01:38:15.000000Z",answers:[]}],T={name:"vocabulary",components:{FlashCard:y,SearchBar:i},props:{list_vocabulary:{type:Object,required:!0}},watch:{searchFocused:function(t,e){console.log("currentView: ",this.currentView),console.log("searchFocused: ",t),t&&(this.currentView=C.SEARCH),console.log("currentView: ",this.currentView)},searchQuery:function(t,e){console.log("searchQuery: ",t),t?(this.currentView=C.SEARCH,this.searchResults=k):this.searchResults=[],console.log("currentView: ",this.currentView)}},data:function(){return{searchQuery:"",searchFocused:!1,showFlashcardPopup:!1,isFlashcardFlipped:!1,currentFlashcardIndex:0,favoriteFlashcards:[{id:1,word:"前",reading:"まえ",meaning:"Phía trước",example:{japanese:"前に行きましょう。",vietnamese:"Hãy đi về phía trước."}},{id:2,word:"後",reading:"あと",meaning:"Phía sau",example:{japanese:"後ろを見てください。",vietnamese:"Hãy nhìn phía sau."}},{id:3,word:"上",reading:"うえ",meaning:"Bên trên",example:{japanese:"上を見てください。",vietnamese:"Hãy nhìn lên trên."}},{id:4,word:"下",reading:"した",meaning:"Bên dưới",example:{japanese:"下を見てください。",vietnamese:"Hãy nhìn xuống dưới."}},{id:5,word:"左",reading:"ひだり",meaning:"Bên trái",example:{japanese:"左に曲がってください。",vietnamese:"Hãy rẽ trái."}}],currentView:C.FAVORITE,typesView:C,chart:null,categories:[],searchResults:k,showDialogInfo:!1,cards:F,card:{id:103569,lesson_id:10846,exam_part_id:null,skill:1,mondai_id:null,type:17,type_ld:null,server:null,video_name:null,video_title:null,value:{word:"<p>わたし</p>",word_stress:'<p>わ<span style="text-decoration:overline">たしr</span></p>',word_type:"Động từ",meaning:"<p>3</p>",audio:null,front_image:null,back_image:null,example:[{example:'<p><span style="color:#ef6d13">わたし</span>はきょうしです。</p>',audio:null}],meaning_example:['<p><span style="color:#ef6d13">T&ocirc;i</span> l&agrave; gi&aacute;o vi&ecirc;n.</p>'],quiz_question:[],kanji_meaning:null},suggest:null,explain:null,explain_mp3:null,value_data:null,grade:"",sort:3,show:1,is_quiz:0,video_full:0,updated_at:"2025-04-25T01:45:14.000000Z",created_at:"2025-04-10T01:40:02.000000Z",answers:[],comment:{id:463617,content:"hi",user_id:540309,count_like:0,created_at:"2025-05-16 09:54:28",pin:0,parent_id:0,table_id:103569,table_name:"flashcard",user_info:{email:"<EMAIL>",name:"Dinhsuu",avatar:"1711587724_0_76076.jpeg",userId:540309},time_created:"16/05/2025 09:54",replies:[{id:463618,table_id:103569,table_name:"flashcard",user_id:540308,admin_log:null,kwadmin_id:null,content:"122",img:null,audio:null,rate:0,is_tester:0,parent_id:463617,count_like:0,ulikes:null,readed:0,tag_data:null,status:0,pin:0,is_correct:0,updated_at:"2025-05-16 09:54:44",created_at:"2025-05-16 09:54:44",user_info:{email:"<EMAIL>",name:"Thu Vũ",avatar:"1711587724_0_76076.jpeg",userId:540308},time_created:"16/05/2025 09:54",replies:null,table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""}}],table_info:{id:"",course_id:"",name:"",SEOurl:"",course_url:""},comment_like:[]}},courseSpecializedActive:null}},computed:{chartData:function(){var t={learned:0,temporary:0,memorized:0,mastered:0};return this.categories.forEach((function(e){e.selected&&(t.learned+=e.data.learned,t.temporary+=e.data.temporary,t.memorized+=e.data.memorized,t.mastered+=e.data.mastered)})),t},totalWords:function(){var t=0;return this.categories.forEach((function(e){e.selected&&(t+=e.data.learned)})),t},currentFlashcard:function(){return this.favoriteFlashcards[this.currentFlashcardIndex]||{}}},methods:{openFlashcardPopup:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.currentFlashcardIndex=t,this.isFlashcardFlipped=!1,this.showFlashcardPopup=!0,document.body.style.overflow="hidden"},closeFlashcardPopup:function(){this.showFlashcardPopup=!1,document.body.style.overflow=""},flipCard:function(t,e){if(console.log("id: ",t),e.target.closest(".noFlip, .card_audio, button"))console.log("Click on non-flippable element, skipping flip");else{var a=document.querySelector("#card-inner-".concat(t));a&&a.classList.toggle("flip")}},prevFlashcard:function(){this.currentFlashcardIndex>0&&(this.currentFlashcardIndex--,this.isFlashcardFlipped=!1)},nextFlashcard:function(){this.currentFlashcardIndex<this.favoriteFlashcards.length-1&&(this.currentFlashcardIndex++,this.isFlashcardFlipped=!1)},getBarHeight:function(t){if(!t)return"0%";var e=Math.max(this.chartData.learned||0,this.chartData.temporary||0,this.chartData.memorized||0,this.chartData.mastered||0);if(e<=0)return"5%";var a=t/e*80;return"".concat(Math.max(a,5),"%")},initCategories:function(){var t=this;this.categories=[],this.list_vocabulary.jlpt&&this.list_vocabulary.jlpt.data&&this.list_vocabulary.jlpt.data.forEach((function(e){var a=e.word_count||0,n={learned:Math.floor(.7*a),temporary:Math.floor(.4*a),memorized:Math.floor(.2*a),mastered:Math.floor(.1*a)};t.categories.push({name:e.name,id:e.id,selected:e.name.includes("N5"),data:n})})),this.list_vocabulary.specialized&&this.list_vocabulary.specialized.data&&this.list_vocabulary.specialized.data.forEach((function(e){var a=e.word_count||0,n={learned:Math.floor(.6*a),temporary:Math.floor(.3*a),memorized:Math.floor(.15*a),mastered:Math.floor(.05*a)};t.categories.push({name:e.name,id:e.id,selected:!1,data:n})}))},playAudio:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";console.log("Play audio for id: ".concat(t,", url: ").concat(e)),console.log("this.$root: ",this.$root),this.$root.toggleMp3?this.$root.toggleMp3(t,"flashcard","https://video-test.dungmori.com/audio/".concat(e)):console.error("toggleMp3 method not found in root instance")},openCourseFlashCardDetail:function(t){console.log("openCourseFlashCardDetail: ",t),this.courseSpecializedActive=t,this.currentView=this.typesView.SPECIALIZED}},mounted:function(){console.log("Component mounted, chart data:",this.chartData)},created:function(){console.log("list_vocabulary",this.list_vocabulary),this.initCategories()}},E=T,j=a(82230),S={insert:"head",singleton:!1},A=(o()(j.A,S),j.A.locals,a(37694)),P={insert:"head",singleton:!1},V=(o()(A.A,P),A.A.locals,n(E,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-container"},[e("div",{staticClass:"nav-menu",attrs:{id:"navMenu"}},[e("div",{staticClass:"nav-menu-header"},[e("div",{staticClass:"nav-menu-title cursor-pointer",on:{click:function(e){t.currentView=t.typesView.OVERVIEW}}},[t._v("Từ vựng")]),t._v(" "),t._m(0)]),t._v(" "),e("div",{staticClass:"nav-menu-items"},[t._m(1),t._v(" "),e("div",{staticClass:"nav-submenu",attrs:{id:"submenu1"}},t._l(t.list_vocabulary.jlpt.data,(function(a,n){return e("div",{staticClass:"nav-submenu-item font-beanbag-regular text-[18px] text-black",on:{click:function(e){return t.openCourseFlashCardDetail(a.id)}}},[e("span",{staticClass:"text-black"},[t._v(t._s(a.name))])])})),0),t._v(" "),t._m(2),t._v(" "),e("div",{staticClass:"nav-submenu",attrs:{id:"submenu2"}},t._l(t.list_vocabulary.specialized.data,(function(a,n){return e("div",{staticClass:"nav-submenu-item font-beanbag-regular text-[18px] text-black"},[e("span",{staticClass:"text-black"},[t._v(t._s(a.name))])])})),0),t._v(" "),e("div",{staticClass:"nav-menu-item justify-between",on:{click:function(e){t.currentView=t.typesView.FAVORITE}}},[e("span",{staticClass:"nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"},[t._v("Yêu thích")]),t._v(" "),e("i",{staticClass:"fas fa-heart text-[#FF7C79]"})])])]),t._v(" "),e("div",{staticClass:"content-area"},[e("div",{staticClass:"content-section active max-w-[1096px] mx-auto mt-[58px]",attrs:{id:"default"}},[t.currentView!==t.typesView.FAVORITE&&t.currentView!==t.typesView.SPECIALIZED?e("div",{staticClass:"vocabulary-search-container"},[t.currentView===t.typesView.SEARCH?e("div",{staticClass:"vocabulary-search-btn-back h-[80px] w-[80px]",on:{click:function(e){t.currentView=t.typesView.OVERVIEW}}},[e("i",{staticClass:"fas fa-arrow-left"})]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-search-bar flex items-center rounded-full bg-white shadow-md h-[80px] px-[20px] transition-all duration-300 flex-1 min-w-0"},[t._m(3),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchQuery,expression:"searchQuery"}],staticClass:"search-input",attrs:{type:"text",placeholder:"Nhập từ vựng muốn tìm kiếm"},domProps:{value:t.searchQuery},on:{focus:function(e){t.searchFocused=!0},blur:function(e){t.searchFocused=!1},input:function(e){e.target.composing||(t.searchQuery=e.target.value)}}}),t._v(" "),t.searchQuery?e("div",{staticClass:"search-clear cursor-pointer bg-[#757575] px-[10px] py-[5px] rounded-full",on:{click:function(e){t.searchQuery=""}}},[e("i",{staticClass:"fas fa-times"})]):t._e()])]):t._e(),t._v(" "),t.currentView===t.typesView.OVERVIEW?[e("div",{staticClass:"section-content overview-content"},[e("div",{staticClass:"chart-overview-container flex mb-[66px]"},[e("div",{staticClass:"chart-container w-2/3"},[e("div",{staticClass:"text-right"},[e("button",{staticClass:"btn-info-chart bg-[#FFB98F] text-white rounded-full px-[12px]",on:{click:function(e){t.showDialogInfo=!0}}},[e("span",{staticClass:"text-[20px] font-beanbag-medium"},[t._v("i")])])]),t._v(" "),e("div",{staticClass:"html-chart"},[e("div",{staticClass:"chart-columns"},[e("div",{staticClass:"chart-column",style:{height:t.chartData.learned?t.getBarHeight(t.chartData.learned):"0%"}},[e("div",{staticClass:"column-value font-beanbag-medium"},[t._v(t._s(t.chartData.learned)+" từ")]),t._v(" "),t._m(4),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Đã học")])]),t._v(" "),e("div",{staticClass:"chart-column",style:{height:t.chartData.temporary?t.getBarHeight(t.chartData.temporary):"0%"}},[e("div",{staticClass:"column-value font-beanbag-medium"},[t._v(t._s(t.chartData.temporary)+" từ")]),t._v(" "),t._m(5),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Tạm nhớ")])]),t._v(" "),e("div",{staticClass:"chart-column",style:{height:t.chartData.memorized?t.getBarHeight(t.chartData.memorized):"0%"}},[e("div",{staticClass:"column-value font-beanbag-medium"},[t._v(t._s(t.chartData.memorized)+" từ")]),t._v(" "),t._m(6),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Ghi nhớ")])]),t._v(" "),e("div",{staticClass:"chart-column",style:{height:t.chartData.mastered?t.getBarHeight(t.chartData.mastered):"0%"}},[e("div",{staticClass:"column-value font-beanbag-medium"},[t._v(t._s(t.chartData.mastered)+" từ")]),t._v(" "),t._m(7),t._v(" "),e("div",{staticClass:"column-label"},[t._v("Thuộc lòng")])])])])]),t._v(" "),e("div",{staticClass:"chart-header w-1/3"},[e("div",{staticClass:"flex justify-between items-center border-b-[1px] pb-3",staticStyle:{"border-bottom-style":"dashed","border-bottom-color":"#176867"}},[e("div",{staticClass:"chart-title font-beanbag-medium text-[20px] text-[#176867]"},[t._v("Tổng số từ vựng")]),t._v(" "),e("div",{staticClass:"total-word rounded-full text-[20px] font-beanbag-medium text-[#176867] px-[15px] py-[2px] bg-[#CEFFD8]"},[t._v("\n                  "+t._s(t.totalWords)+"\n                ")])]),t._v(" "),e("div",{staticClass:"chart-filters pt-3"},[e("div",{staticClass:"filter-checkboxes overflow-y-auto h-[330px]"},t._l(t.categories,(function(a,n){return e("label",{key:n,staticClass:"checkbox-container"},[e("input",{directives:[{name:"model",rawName:"v-model",value:a.selected,expression:"category.selected"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(a.selected)?t._i(a.selected,null)>-1:a.selected},on:{change:function(e){var n=a.selected,i=e.target,r=!!i.checked;if(Array.isArray(n)){var s=t._i(n,null);i.checked?s<0&&t.$set(a,"selected",n.concat([null])):s>-1&&t.$set(a,"selected",n.slice(0,s).concat(n.slice(s+1)))}else t.$set(a,"selected",r)}}}),t._v(" "),e("span",{staticClass:"checkbox-label font-beanbag-medium font-medium"},[t._v(t._s(a.name))])])})),0)])])]),t._v(" "),e("div",{staticClass:"vocabulary-section",attrs:{id:"section-jlpt"}},[t._m(8),t._v(" "),e("div",{staticClass:"vocabulary-cards"},t._l(t.list_vocabulary.jlpt.data,(function(a,n){return e("div",{key:n,staticClass:"vocabulary-card",class:"incoming"===a.status?"vocabulary-card-incoming":"",attrs:{"data-key":n}},[e("div",{staticClass:"vocabulary-card-content"},["new"===a.status?e("div",{staticClass:"vocabulary-card-status"},[e("span",{staticClass:"badge badge-new font-gen-jyuu-gothic-medium text-[12px]"},[t._v("NEW")])]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-line bg-[#57D061]"}),t._v(" "),e("div",{staticClass:"vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px]"},[t._v(t._s(a.name)+"\n                  ")]),t._v(" "),e("div",{staticClass:"vocabulary-card-count font-gen-jyuu-gothic text-[20px]"},[t._v("\n                    "+t._s(a.word_count)+" từ vựng\n                  ")])]),t._v(" "),"incoming"!==a.status?e("div",{staticClass:"vocabulary-card-name font-zuume-semibold"},[e("h1",[t._v("\n                    "+t._s(a.level)+"\n                  ")])]):e("div",{staticClass:"vocabulary-card-incoming-text"},[e("p",{staticClass:"text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]"},[t._v("Sắp ra mắt")])])])})),0)]),t._v(" "),e("div",{staticClass:"vocabulary-section",attrs:{id:"section-specialized"}},[t._m(9),t._v(" "),e("div",{staticClass:"vocabulary-cards"},t._l(t.list_vocabulary.specialized.data,(function(a,n){return e("div",{key:n,staticClass:"vocabulary-card",class:"incoming"===a.status?"vocabulary-card-incoming":"",attrs:{"data-key":n}},[e("div",{staticClass:"vocabulary-card-content"},["hot"===a.status?e("div",{staticClass:"vocabulary-card-status"},[e("span",{staticClass:"badge badge-new font-gen-jyuu-gothic-medium text-[12px]"},[t._v("HOT")])]):t._e(),t._v(" "),e("div",{staticClass:"vocabulary-line bg-[#4E87FF]"}),t._v(" "),e("div",{staticClass:"vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px] leading-[20px]"},[t._v("\n                    "+t._s(a.name)+"\n                  ")]),t._v(" "),e("div",{staticClass:"vocabulary-card-count font-gen-jyuu-gothic text-[20px]"},[t._v("\n                    "+t._s(a.word_count)+" từ vựng\n                  ")])]),t._v(" "),"incoming"!==a.status?e("img",{staticClass:"vocabulary-card-image",attrs:{src:"/images/vocabulary/test.png",alt:a.name}}):e("div",{staticClass:"vocabulary-card-incoming-text"},[e("p",{staticClass:"text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]"},[t._v("Sắp ra mắt")])])])})),0)]),t._v(" "),e("div",{staticClass:"vocabulary-section",attrs:{id:"section-favorite"}},[t._m(10),t._v(" "),t._l(t.list_vocabulary.favorite.data,(function(a,n){return e("div",{key:n,staticClass:"vocabulary-card card-favorite",attrs:{"data-key":n}},[e("div",{staticClass:"vocabulary-card-content-favorite"},[e("div",{staticClass:"absolute h-[50px] bg-[#FF7C79] top-[40px] left-[25px] w-[3px] bg-[#FF7C79] rounded-full"}),t._v(" "),e("div",{staticClass:"w-full flex justify-between"},[e("div",{staticClass:"flex flex-col justify-around"},[e("div",{staticClass:"pt-[23px] pl-[32px] pb-[13px]"},[e("div",{staticClass:"text-[28px] font-gen-jyuu-gothic-medium text-bold"},[t._v("\n                        "+t._s(a.name)+"\n                      ")]),t._v(" "),e("div",{staticClass:"vocabulary-card-count font-gen-jyuu-gothic text-[20px]"},[t._v("\n                        "+t._s(a.word_count)+" từ vựng\n                      ")])]),t._v(" "),t._m(11,!0)]),t._v(" "),e("div",[e("img",{staticClass:"h-full object-cover",attrs:{src:"/images/vocabulary/bg-favorite-overview.svg",alt:a.name}})])])])])}))],2)])]:t._e(),t._v(" "),t.currentView===t.typesView.SEARCH?[e("div",{staticClass:"section-content search-content"},[t.searchResults.length>0?e("div",{staticClass:"search-result"},[e("div",{staticClass:"search-result-items"},t._l(t.searchResults,(function(a,n){return e("div",{key:n,staticClass:"search-result-item"},[e("div",{staticClass:"search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3"},[e("div",{staticClass:"search-result-item-title font-gen-jyuu-gothic-medium text-bold text-black text-[20px]"},[t._v("\n                    "+t._s(a.word)+"\n                  ")]),t._v(" "),e("div",{staticClass:"search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"},[t._v("\n                    "+t._s(a.description)+"\n                  ")]),t._v(" "),e("div",{staticClass:"search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"},[t._v("\n                    "+t._s(a.specialized)+"\n                  ")])])])})),0)]):e("div",{staticClass:"search-not-found"},[t._m(12),t._v(" "),t.searchQuery?e("div",{staticClass:"search-not-found-text"},[t._v('\n              Không tìm thấy kết quả nào cho "'+t._s(t.searchQuery)+'"\n            ')]):e("div",{staticClass:"search-not-found-text"},[t._v("\n              Nhập từ vựng bạn muốn tìm kiếm\n            ")])])])]:t._e(),t._v(" "),t.currentView===t.typesView.SPECIALIZED?[e("div",{staticClass:"section-content specialized-content"},[e("flash-card",{attrs:{"course-id":t.courseSpecializedActive}})],1)]:t._e(),t._v(" "),t.currentView===t.typesView.FAVORITE?[e("div",{staticClass:"section-content favorite-content"},[e("div",{staticClass:"flex justify-between items-center mb-10"},[e("div",{staticClass:"flex items-center"},[e("div",{staticClass:"vocabulary-search-btn-back h-[65px] w-[65px]",on:{click:function(e){t.currentView=t.typesView.OVERVIEW}}},[e("i",{staticClass:"fas fa-arrow-left"})]),t._v(" "),t._m(13)]),t._v(" "),e("div",{staticClass:"vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]"},[t._m(14),t._v(" "),e("input",{directives:[{name:"model",rawName:"v-model",value:t.searchQuery,expression:"searchQuery"}],staticClass:"search-input leading-[65px]",attrs:{type:"text",placeholder:"Nhập từ vựng muốn tìm kiếm"},domProps:{value:t.searchQuery},on:{input:function(e){e.target.composing||(t.searchQuery=e.target.value)}}})])]),t._v(" "),t._m(15),t._v(" "),t.searchResults.length>0?e("div",{staticClass:"search-result list-favorite"},[e("div",{staticClass:"search-result-items"},t._l(t.searchResults,(function(a,n){return e("div",{key:n,staticClass:"search-result-item cursor-pointer",on:{click:function(e){return t.openFlashcardPopup(n)}}},[e("div",{staticClass:"search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3"},[e("div",{staticClass:"search-result-item-title font-gen-jyuu-gothic-medium text-bold text-black text-[20px]"},[t._v("\n                    "+t._s(a.word)+"\n                  ")]),t._v(" "),e("div",{staticClass:"search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"},[t._v("\n                    "+t._s(a.description)+"\n                  ")]),t._v(" "),e("div",{staticClass:"search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"},[t._v("\n                    "+t._s(a.specialized)+"\n                  ")])])])})),0)]):t._e()])]:t._e()],2)]),t._v(" "),e("el-dialog",{staticClass:"dialog-vocabulary-info",attrs:{visible:t.showDialogInfo,"max-width":"1097px",top:"8vh",width:"80%",height:"80vh",center:"","show-close":!1,"close-on-click-modal":!0},on:{"update:visible":function(e){t.showDialogInfo=e}},scopedSlots:t._u([{key:"title",fn:function(){return[e("div",{staticClass:"custom-title pt-[29px] pl-[43px] pr-[38px] flex justify-between items-end rounded-[32px]"},[e("div",{staticClass:"flex items-center gap-2"},[e("div",{staticClass:"text-[#073A3B] font-beanbag text-[24px] bg-[#CCF8D1] px-5 py-1 rounded-full"},[t._v("\n            Rừng ngôn từ\n          ")]),t._v(" "),e("div",{staticClass:"text-[#0C403F] text-[27px]"},[t._v("\n            言葉の森\n          ")])]),t._v(" "),e("el-button",{attrs:{icon:"el-icon-close",circle:""},on:{click:function(e){t.showDialogInfo=!1}}})],1)]},proxy:!0}])},[t._v(" "),e("div",{staticClass:"dialog-wrapper overflow-y-scroll h-[60vh] pl-[43px] py-[12px] pr-[38px] mx-[43px] my-[12px] custom-scrollbar"},[e("div",{staticClass:"font-beanbag-regular text-[#07403F] text-[20px] mb-3 break-normal"},[t._v("\n        Là nơi thống kê chi tiết số lượng từ vựng nằm ở các "),e("span",{staticClass:"text-italic font-beanbag-medium break-normal"},[t._v("mức độ ghi nhớ")]),t._v("\n        khác nhau thông qua phương pháp học "),e("span",{staticClass:"text-italic font-beanbag-medium break-normal"},[t._v("Lặp lại ngắt quãng (Spaced Repetition)")]),t._v(",\n        giúp bạn hoàn toàn làm chủ quá trình học của bản thân!\n      ")]),t._v(" "),e("div",{staticClass:"text-center mb-5"},[e("img",{attrs:{src:"/images/vocabulary/img-popup-info.svg",alt:"Thông tin"}})]),t._v(" "),e("div",{staticClass:"mb-5"},[e("ul",{staticClass:"list-disc font-beanbag-regular text-[#07403F] text-[20px]"},[e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#FFD1B0] rounded-[8px]"},[t._v("Đã học")]),t._v(":\n            Những từ bạn mới được học.\n          ")]),t._v(" "),e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#FFEF94] rounded-[8px]"},[t._v("Tạm nhớ")]),t._v(":\n            Những từ bạn vẫn nhớ sau 1 thời gian ngắn không sử dụng tới.\n          ")]),t._v(" "),e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#D0FCA1] rounded-[8px]"},[t._v("Ghi nhớ")]),t._v(":\n            Từ vựng đã được lưu vào trí nhớ dài hạn của bạn rồi!\n          ")]),t._v(" "),e("li",{staticClass:"py-[5px]",staticStyle:{"list-style":"disc"}},[e("span",{staticClass:"font-beanbag-medium bg-[#80FBA6] rounded-[8px]"},[t._v("Thuộc lòng")]),t._v(":\n            Tuyệt vời! Những từ vựng này giờ là của bạn!\n          ")])])]),t._v(" "),e("div",{staticClass:"font-beanbag-regular text-[#07403F] text-[20px]"},[t._v("\n        Chăm chỉ học tập, bạn sẽ có được một "),e("span",{staticClass:"font-beanbag-medium italic"},[t._v("Rừng ngôn từ")]),t._v(" thật phong\n        phú, đa dạng! Cùng học bạn nhé!\n      ")]),t._v(" "),e("div",{staticClass:"flex justify-center mt-5"},[e("button",{staticClass:"px-12 py-4 flex items-center justify-center bg-[#57D061] rounded-full cursor-pointer drop-shadow",on:{click:function(e){t.showDialogInfo=!1}}},[e("span",{staticClass:"text-btn font-beanbag-medium text-[#07403F] text-xl mr-1 text-[20px]"},[t._v("\n            Mình đã hiểu!\n          ")])])])])]),t._v(" "),t.showFlashcardPopup?e("div",{staticClass:"flashcard-popup-overlay",on:{click:function(e){return e.target!==e.currentTarget?null:t.closeFlashcardPopup.apply(null,arguments)}}},[e("div",{staticClass:"flashcard-popup"},[e("div",{staticClass:"flashcard-popup-content"},[e("button",{staticClass:"flashcard-nav-button prev",attrs:{disabled:0===t.currentFlashcardIndex},on:{click:t.prevFlashcard}},[e("i",{staticClass:"fas fa-chevron-left"})]),t._v(" "),e("div",{staticClass:"flashcards-wrap cursor-pointer",staticStyle:{width:"600px"}},t._l(t.cards,(function(a,n){return e("div",{directives:[{name:"show",rawName:"v-show",value:n===t.currentFlashcardIndex,expression:"index === currentFlashcardIndex"}],key:a.id,staticClass:"card-item",class:{"stackedcards-active":0===n,"stackedcards-top":!0,"stackedcards--animatable":!0,"stackedcards-origin-top":!0},attrs:{"data-id":a.id}},[e("div",{staticClass:"card-inner",attrs:{id:"card-inner-".concat(a.id)},on:{click:function(e){return t.flipCard(a.id,e)}}},[e("div",{staticClass:"card__face card__face--jp card__face--front"},[e("div",{staticClass:"card-wrap p-4 h-[90%] overflow-y-auto relative"},[e("div",{staticClass:"card_header flex items-center"},[e("div",{staticClass:"card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer",on:{click:function(e){return t.playAudio("audio")}}},[e("svg",{staticClass:"noFlip",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})])]),t._v(" "),e("div",{staticClass:"card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",domProps:{innerHTML:t._s(a.value.word_stress)}})]),t._v(" "),e("div",{staticClass:"card_content min-h-[calc(100%-34px)] flex flex-col"},[e("div",{staticClass:"content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex",staticStyle:{"flex-grow":"1"},domProps:{innerHTML:t._s(a.value.word)}}),t._v(" "),a.value.front_image?e("div",{staticClass:"content-img text-center p-[40px]"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(a.value.front_image)}})]):t._e(),t._v(" "),a.value.example.length?e("div",{staticClass:"example-wrap"},[e("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[t._v("\n                        Ví dụ\n                      ")]),t._v(" "),e("div",{staticClass:"list-example"},[t._l(a.value.example,(function(n,i){return[e("div",{staticClass:"example-item mb-1"},[n.audio?e("svg",{staticClass:"w-[36px] h-[36px] noFlip cursor-pointer",attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},on:{click:function(e){return t.playAudio(a.id+"_example_"+i,n.audio)}}},[e("path",{attrs:{d:"M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{opacity:"0.4",d:"M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",fill:"#4E87FF"}}),t._v(" "),e("path",{attrs:{d:"M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",fill:"#4E87FF"}})]):t._e(),t._v(" "),e("div",{staticClass:"ml-2 font-beanbag-regular text-2xl flex items-start text-balance"},[t._v("\n                              "+t._s(i+1)+". "),e("span",{staticClass:"ml-2",domProps:{innerHTML:t._s(n.example)}})])])]}))],2)]):t._e()])]),t._v(" "),e("div",{staticClass:"card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]"},[e("svg",{staticClass:"m-3 noFlip",attrs:{width:"33",height:"28",viewBox:"0 0 33 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z",fill:"#FF7C79",stroke:"#FF7C79"}})])])]),t._v(" "),e("div",{staticClass:"card__face card__face--vi card__face--back"},[e("div",{staticClass:"card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"},[e("div",{staticClass:"card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",staticStyle:{"flex-grow":"1"}},[e("div",{staticClass:"text-center",domProps:{innerHTML:t._s(a.value.meaning)}})]),t._v(" "),e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",domProps:{innerHTML:t._s(a.value.kanji_meaning)}}),t._v(" "),a.value.back_image?e("div",{staticClass:"card_img_back p-[40px] content-img text-center"},[e("img",{attrs:{src:"https://video-test.dungmori.com/images/".concat(a.value.back_image)}})]):t._e(),t._v(" "),a.value.meaning_example.length?e("div",{staticClass:"example-wrap"},[e("p",{staticClass:"w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"},[t._v("\n                      Ví dụ\n                    ")]),t._v(" "),e("div",{staticClass:"list-example"},[t._l(a.value.meaning_example,(function(a,n){return[e("div",{staticClass:"example-item flex items-center mb-1"},[e("div",{staticClass:"ml-2 font-averta-regular text-2xl flex items-start"},[t._v("\n                            "+t._s(n+1)+". "),e("span",{staticClass:"ml-1",domProps:{innerHTML:t._s(a)}})])])]}))],2)]):t._e()]),t._v(" "),e("div",{staticClass:"how-remember-wrap px-4 h-[40%] flex flex-col"},[t._m(16,!0),t._v(" "),a.comment&&a.comment.user_info?e("div",{staticClass:"how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"},[e("div",{staticClass:"how-remember-wrap-avatar w-[28px]"},[e("img",{staticClass:"rounded-full",attrs:{src:"/cdn/avatar/small/".concat(a.comment.user_info.avatar)}})]),t._v(" "),e("div",{staticClass:"how-remember-wrap-info flex text-[#073A3B]"},[e("span",{staticClass:"font-averta-bold"},[t._v("\n                          "+t._s(a.comment.user_info.name)+"・\n                        ")]),t._v(" "),e("span",{staticClass:"font-averta-regular"},[t._v("\n                          "+t._s(a.comment.time_created)+"\n                        ")]),t._v(" "),a.comment.pin?e("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z",fill:"#B3B3B3"}})]):t._e()]),t._v(" "),e("div",{staticClass:"col-start-2 font-averta-regular text-[#07403F]",staticStyle:{display:"-webkit-box","-webkit-line-clamp":"2","-webkit-box-orient":"vertical",overflow:"hidden","text-overflow":"ellipsis"}},[t._v("\n                      "+t._s(a.comment.content)+"\n                    ")]),t._v(" "),e("div",{staticClass:"col-start-2 flex justify-between items-center"},[e("div",{staticClass:"font-averta-regular text-[#009951] flex"},[e("div",{staticClass:"flex items-center mr-5"},[e("svg",{staticClass:"mr-1 noFlip",attrs:{width:"13",height:"12",viewBox:"0 0 13 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",fill:a.comment.comment_like.length?"#009951":"none",stroke:"#009951"}})]),t._v("\n                          "+t._s(a.comment.count_like)+"\n                        ")]),t._v(" "),a.comment&&a.comment.replies?e("div",[t._v("\n                          "+t._s(a.comment.replies.length)+" Trả lời\n                        ")]):t._e()]),t._v(" "),e("div",{staticClass:"underline decoration-solid text-[#009951] cursor-pointer noFlip",on:{click:function(e){return t.toggleCommentTab("open")}}},[t._v("\n                        Xem thêm >>\n                      ")])])]):e("div",{staticClass:"items-center flex grow",staticStyle:{"flex-grow":"1"}},[e("div",{staticClass:"underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip",on:{click:function(e){return t.toggleCommentTab("open")}}},[t._v("\n                      Đóng góp cách nhớ của bạn >>\n                    ")])])])])])])})),0),t._v(" "),e("button",{staticClass:"flashcard-nav-button next",attrs:{disabled:t.currentFlashcardIndex===t.favoriteFlashcards.length-1},on:{click:t.nextFlashcard}},[e("i",{staticClass:"fas fa-chevron-right"})])])])]):t._e()],1)}),[function(){var t=this._self._c;return t("div",{staticClass:"toggle-menu",attrs:{onclick:"toggleMenu()"}},[t("i",{staticClass:"fas fa-bars"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-menu-item",attrs:{onclick:"toggleSubmenu('submenu1')"}},[e("span",{staticClass:"nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"},[t._v("JLPT")]),t._v(" "),e("i",{staticClass:"fas fa-chevron-down ml-auto"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"nav-menu-item",attrs:{onclick:"toggleSubmenu('submenu2')"}},[e("span",{staticClass:"nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"},[t._v("Chuyên ngành")]),t._v(" "),e("i",{staticClass:"fas fa-chevron-down ml-auto"})])},function(){var t=this._self._c;return t("div",{staticClass:"search-icon"},[t("i",{staticClass:"fas fa-search"})])},function(){var t=this._self._c;return t("div",{staticClass:"column-bar learned-bar"},[t("div",{staticClass:"column-bar-inner"})])},function(){var t=this._self._c;return t("div",{staticClass:"column-bar temporary-bar"},[t("div",{staticClass:"column-bar-inner"})])},function(){var t=this._self._c;return t("div",{staticClass:"column-bar memorized-bar"},[t("div",{staticClass:"column-bar-inner"})])},function(){var t=this._self._c;return t("div",{staticClass:"column-bar mastered-bar"},[t("div",{staticClass:"column-bar-inner"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-section-title"},[t._v("\n              JLPT\n              "),e("span",{staticClass:"badge badge-vip"},[t._v("VIP")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-section-title"},[t._v("\n              Chuyên ngành\n              "),e("span",{staticClass:"badge badge-free"},[t._v("Free")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-section-title"},[t._v("\n              Yêu thích\n              "),e("span",{staticClass:"badge badge-favorite"},[t._v("Favorite")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"pl-[32px] pb-[23px]"},[e("div",{staticClass:"font-gen-jyuu-gothic text-[20px]"},[t._v("Từ thêm gần đây")]),t._v(" "),e("div",{staticClass:"favorite-word-new text-[24px]"},[t._v("\n                        木漏れ日\n                      ")]),t._v(" "),e("div",{staticClass:"time-add-new-word font-beanbag-regular text-[20px] text-[#757575]"},[t._v("\n                        9:05 18-02-2025\n                      ")])])},function(){var t=this._self._c;return t("div",{staticClass:"search-not-found-image"},[t("img",{attrs:{src:"/images/vocabulary/bg-search-not-found.svg",alt:"Không tìm thấy kết quả"}})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"vocabulary-section-favorite-title"},[e("div",{staticClass:"flex font-beanbag-medium text-[24px] text-[#07403F] items-center"},[t._v("\n                  Từ vựng yêu thích\n                  "),e("i",{staticClass:"fas fa-heart text-[#FF7C79] ml-2"})]),t._v(" "),e("div",{staticClass:"font-beanbag-regular text-[20px] text-[#07403F]"},[t._v("1122 từ")])])},function(){var t=this._self._c;return t("div",{staticClass:"search-icon"},[t("i",{staticClass:"fas fa-search"})])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"flex items-center mb-10"},[e("div",{staticClass:"bg-[#CCF8D1] py-2 px-3 rounded-full flex font-beanbag-medium text-[20px] text-[#07403F] items-center mr-5"},[e("i",{staticClass:"fas fa-list mr-2"}),t._v("\n              Danh sách\n            ")]),t._v(" "),e("div",{staticClass:"font-beanbag-regular text-[20px] text-[#B3B3B3] flex items-center"},[e("img",{attrs:{src:"/images/icons/tag-flashcard.svg",alt:"tag"}}),t._v("\n              Thẻ\n            ")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"how-remember-wrap-header flex mb-5"},[e("div",{staticClass:"font-beanbag-medium text-[#757575] text-xl"},[t._v("\n                      Cách nhớ\n                    ")]),t._v(" "),e("div",{staticClass:"border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"})])}],!1,null,"78f4f302",null)),z=V.exports},69012:function(t){"use strict";t.exports=function(t,e){return function(){for(var a=new Array(arguments.length),n=0;n<a.length;n++)a[n]=arguments[n];return t.apply(e,a)}}},72505:function(t,e,a){a(18015)},76798:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var a=t(e);return e[2]?"@media ".concat(e[2]," {").concat(a,"}"):a})).join("")},e.i=function(t,a,n){"string"==typeof t&&(t=[[null,t,""]]);var i={};if(n)for(var r=0;r<this.length;r++){var s=this[r][0];null!=s&&(i[s]=!0)}for(var o=0;o<t.length;o++){var l=[].concat(t[o]);n&&i[l[0]]||(a&&(l[2]?l[2]="".concat(a," and ").concat(l[2]):l[2]=a),e.push(l))}},e}},79106:function(t,e,a){"use strict";var n=a(9516);function i(t){return encodeURIComponent(t).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(t,e,a){if(!e)return t;var r;if(a)r=a(e);else if(n.isURLSearchParams(e))r=e.toString();else{var s=[];n.forEach(e,(function(t,e){null!=t&&(n.isArray(t)&&(e+="[]"),n.isArray(t)||(t=[t]),n.forEach(t,(function(t){n.isDate(t)?t=t.toISOString():n.isObject(t)&&(t=JSON.stringify(t)),s.push(i(e)+"="+i(t))})))})),r=s.join("&")}return r&&(t+=(-1===t.indexOf("?")?"?":"&")+r),t}},82230:function(t,e,a){"use strict";var n=a(76798),i=a.n(n)()((function(t){return t[1]}));i.push([t.id,'.vocabulary-search-container[data-v-78f4f302]{-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;margin:0 auto 30px;position:relative;-webkit-transition:all .3s ease-in-out;transition:all .3s ease-in-out;width:100%}.vocabulary-favorite-search-bar[data-v-78f4f302]:hover,.vocabulary-search-bar[data-v-78f4f302]:hover{-webkit-box-shadow:0 1px 6px rgba(32,33,36,.4);box-shadow:0 1px 6px rgba(32,33,36,.4)}.vocabulary-favorite-search-bar[data-v-78f4f302]:focus-within,.vocabulary-search-bar[data-v-78f4f302]:focus-within{-webkit-box-shadow:0 1px 10px rgba(32,33,36,.4);box-shadow:0 1px 10px rgba(32,33,36,.4)}.vocabulary-search-btn-back[data-v-78f4f302]{-webkit-box-align:center;-ms-flex-align:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-align-items:center;align-items:center;background-color:#fff;border-radius:50%;-webkit-box-shadow:0 1px 6px rgba(32,33,36,.28);box-shadow:0 1px 6px rgba(32,33,36,.28);cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;justify-content:center;margin-right:15px;-webkit-transition:all .2s;transition:all .2s}.vocabulary-search-btn-back[data-v-78f4f302]:hover{background-color:rgba(7,64,63,.05);-webkit-box-shadow:0 1px 6px rgba(32,33,36,.4);box-shadow:0 1px 6px rgba(32,33,36,.4)}.vocabulary-search-btn-back i[data-v-78f4f302]{color:#07403f;font-size:22px}.search-icon[data-v-78f4f302]{color:#9aa0a6;font-size:22px;margin-right:12px}.search-input[data-v-78f4f302]{-webkit-box-flex:1;background:transparent;border:none;color:#202124;-webkit-flex:1;-ms-flex:1;flex:1;font-size:16px;outline:none;-webkit-transition:all .3s ease-in-out;transition:all .3s ease-in-out;width:100%}.search-input[data-v-78f4f302]::-webkit-input-placeholder{color:#9aa0a6;font-family:Averta-Regular;font-size:20px}.search-input[data-v-78f4f302]::-moz-placeholder{color:#9aa0a6;font-family:Averta-Regular;font-size:20px}.search-input[data-v-78f4f302]:-ms-input-placeholder{color:#9aa0a6;font-family:Averta-Regular;font-size:20px}.search-input[data-v-78f4f302]::-ms-input-placeholder{color:#9aa0a6;font-family:Averta-Regular;font-size:20px}.search-input[data-v-78f4f302]::placeholder{color:#9aa0a6;font-family:Averta-Regular;font-size:20px}.chart-overview-container[data-v-78f4f302]{height:430px}.chart-header[data-v-78f4f302]{-webkit-box-orient:vertical;-webkit-box-direction:normal;background:#f0fff1;border-radius:12px;-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1);-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;margin-left:12px;padding:20px}.chart-header[data-v-78f4f302],.checkbox-container[data-v-78f4f302]{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.checkbox-container[data-v-78f4f302]{-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;cursor:pointer}.checkbox-container input[type=checkbox][data-v-78f4f302]{cursor:pointer;height:0;opacity:0;position:absolute;width:0}.filter-checkboxes .checkbox-container[data-v-78f4f302]{-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;margin-bottom:8px;padding-left:28px;position:relative}.filter-checkboxes .checkbox-container[data-v-78f4f302]:before{background-color:#fff;border:2px solid #407845;border-radius:50%;content:"";height:18px;left:0;position:absolute;top:50%;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:all .2s ease;transition:all .2s ease;width:18px}.filter-checkboxes .checkbox-container input[type=checkbox]:checked~.checkbox-label[data-v-78f4f302]:after{background-color:#407845;border-radius:50%;content:"";height:10px;left:-24px;position:absolute;top:50%;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:all .2s ease;transition:all .2s ease;width:10px}.filter-checkboxes .checkbox-container[data-v-78f4f302]:hover:before{border-color:#2c5530}.filter-checkboxes .checkbox-label[data-v-78f4f302]{color:#176867;cursor:pointer;position:relative}.chart-container[data-v-78f4f302]{background-color:#fff;border-radius:12px;-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1);padding:30px}.chart-container[data-v-78f4f302],.html-chart[data-v-78f4f302]{height:100%;position:relative}.html-chart[data-v-78f4f302]{-webkit-box-pack:center;-ms-flex-pack:center;background-color:transparent;background-position:top;background-size:auto 150px;-webkit-justify-content:center;justify-content:center;padding:20px 20px 50px}.chart-columns[data-v-78f4f302],.html-chart[data-v-78f4f302]{-webkit-box-align:end;-ms-flex-align:end;-webkit-align-items:flex-end;align-items:flex-end;background-repeat:no-repeat;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;width:100%}.chart-columns[data-v-78f4f302]{-ms-flex-pack:distribute;background-image:url(/images/vocabulary/bg-chart-overview.svg);background-position:bottom;background-size:contain;border-bottom:2px solid #176867;height:100%;-webkit-justify-content:space-around;justify-content:space-around}.chart-column[data-v-78f4f302]{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-box-align:center;-ms-flex-align:center;-webkit-align-items:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;max-width:75px;position:relative;-webkit-transition:all .5s cubic-bezier(.34,1.56,.64,1);transition:all .5s cubic-bezier(.34,1.56,.64,1);width:20%}.column-value[data-v-78f4f302]{fill:hsla(0,0%,100%,.6);-webkit-backdrop-filter:blur(3.6785595417022705px);backdrop-filter:blur(3.6785595417022705px);background:#fff;border-radius:8px;color:#1f7274;-webkit-filter:drop-shadow(0 5.518px 5.518px rgba(97,124,154,.15));filter:drop-shadow(0 5.518px 5.518px rgba(97,124,154,.15));font-size:18px;font-weight:700;margin-bottom:5px;position:absolute;text-align:center;top:-40px;width:100%}.column-value[data-v-78f4f302]:after{border:5px solid transparent;border-top-color:#fff;content:"";left:50%;margin-left:-5px;position:absolute;top:100%}.column-bar[data-v-78f4f302]{border-radius:8px 8px 0 0;-webkit-box-shadow:0 0 10px rgba(0,0,0,.1);box-shadow:0 0 10px rgba(0,0,0,.1);height:100%;overflow:hidden;position:relative;-webkit-transform-origin:bottom center;-ms-transform-origin:bottom center;transform-origin:bottom center;-webkit-transition:all .6s cubic-bezier(.34,1.56,.64,1);transition:all .6s cubic-bezier(.34,1.56,.64,1);width:100%}.learned-bar[data-v-78f4f302]{background-color:rgba(255,106,0,.31)}.temporary-bar[data-v-78f4f302]{background-color:rgba(255,217,0,.42)}.memorized-bar[data-v-78f4f302]{background-color:rgba(128,255,0,.37)}.mastered-bar[data-v-78f4f302]{background:url(/images/vocabulary/bg-mastered-bar-column-chart-overview.svg),-webkit-linear-gradient(58deg,rgba(0,255,115,.5) 32.16%,rgba(0,255,84,.5) 50.05%,rgba(0,255,22,.5) 71.77%,rgba(255,255,0,.5) 96.05%);background:url(/images/vocabulary/bg-mastered-bar-column-chart-overview.svg),linear-gradient(32deg,rgba(0,255,115,.5) 32.16%,rgba(0,255,84,.5) 50.05%,rgba(0,255,22,.5) 71.77%,rgba(255,255,0,.5) 96.05%);background-position:top;background-repeat:no-repeat}.column-label[data-v-78f4f302]{bottom:-35px;color:#07403f;font-family:Beanbag_Dungmori_Rounded_Medium;font-size:22px;position:absolute;text-align:center;width:-webkit-max-content;width:-moz-max-content;width:max-content}@media (max-width:768px){.chart-header[data-v-78f4f302]{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column}.chart-filters[data-v-78f4f302]{margin-top:10px}.filter-checkboxes[data-v-78f4f302]{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;gap:8px}}.filter-checkboxes[data-v-78f4f302]::-webkit-scrollbar{width:6px}.filter-checkboxes[data-v-78f4f302]::-webkit-scrollbar-track{background:transparent}.filter-checkboxes[data-v-78f4f302]::-webkit-scrollbar-thumb{background-color:#407845;border:none;border-radius:10px}.filter-checkboxes[data-v-78f4f302]::-webkit-scrollbar-button{display:none}.filter-checkboxes[data-v-78f4f302]{scrollbar-color:#407845 transparent;scrollbar-width:thin}.dialog-wrapper.custom-scrollbar[data-v-78f4f302]{overflow-x:hidden;overflow-y:auto;scrollbar-color:#57d061 transparent;scrollbar-width:thin}.custom-scrollbar[data-v-78f4f302]::-webkit-scrollbar{width:6px}.custom-scrollbar[data-v-78f4f302]::-webkit-scrollbar-track{background:transparent}.custom-scrollbar[data-v-78f4f302]::-webkit-scrollbar-thumb{background-color:#407845;border:none;border-radius:10px}.search-not-found[data-v-78f4f302]{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-box-align:center;-ms-flex-align:center;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-align-items:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;min-height:400px;padding:40px 20px;text-align:center}.search-not-found-image[data-v-78f4f302]{margin-bottom:20px;max-width:250px;width:100%}.search-not-found-image img[data-v-78f4f302]{height:auto;width:100%}.search-not-found-text[data-v-78f4f302]{color:#757575;font-family:Averta-Regular;font-size:18px;line-height:1.5;max-width:400px}',""]),e.A=i},82881:function(t,e,a){"use strict";var n=a(9516);t.exports=function(t,e,a){return n.forEach(a,(function(a){t=a(t,e)})),t}},83471:function(t,e,a){"use strict";var n=a(9516);function i(){this.handlers=[]}i.prototype.use=function(t,e){return this.handlers.push({fulfilled:t,rejected:e}),this.handlers.length-1},i.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},i.prototype.forEach=function(t){n.forEach(this.handlers,(function(e){null!==e&&t(e)}))},t.exports=i},84680:function(t){"use strict";t.exports=function(t,e){return e?t.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):t}},85072:function(t,e,a){"use strict";var n,i=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},r=function(){var t={};return function(e){if(void 0===t[e]){var a=document.querySelector(e);if(window.HTMLIFrameElement&&a instanceof window.HTMLIFrameElement)try{a=a.contentDocument.head}catch(t){a=null}t[e]=a}return t[e]}}(),s=[];function o(t){for(var e=-1,a=0;a<s.length;a++)if(s[a].identifier===t){e=a;break}return e}function l(t,e){for(var a={},n=[],i=0;i<t.length;i++){var r=t[i],l=e.base?r[0]+e.base:r[0],c=a[l]||0,d="".concat(l," ").concat(c);a[l]=c+1;var u=o(d),p={css:r[1],media:r[2],sourceMap:r[3]};-1!==u?(s[u].references++,s[u].updater(p)):s.push({identifier:d,updater:h(p,e),references:1}),n.push(d)}return n}function c(t){var e=document.createElement("style"),n=t.attributes||{};if(void 0===n.nonce){var i=a.nc;i&&(n.nonce=i)}if(Object.keys(n).forEach((function(t){e.setAttribute(t,n[t])})),"function"==typeof t.insert)t.insert(e);else{var s=r(t.insert||"head");if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(e)}return e}var d,u=(d=[],function(t,e){return d[t]=e,d.filter(Boolean).join("\n")});function p(t,e,a,n){var i=a?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(t.styleSheet)t.styleSheet.cssText=u(e,i);else{var r=document.createTextNode(i),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(r,s[e]):t.appendChild(r)}}function f(t,e,a){var n=a.css,i=a.media,r=a.sourceMap;if(i?t.setAttribute("media",i):t.removeAttribute("media"),r&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(r))))," */")),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}var m=null,v=0;function h(t,e){var a,n,i;if(e.singleton){var r=v++;a=m||(m=c(e)),n=p.bind(null,a,r,!1),i=p.bind(null,a,r,!0)}else a=c(e),n=f.bind(null,a,e),i=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(a)};return n(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;n(t=e)}else i()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=i());var a=l(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var n=0;n<a.length;n++){var i=o(a[n]);s[i].references--}for(var r=l(t,e),c=0;c<a.length;c++){var d=o(a[c]);0===s[d].references&&(s[d].updater(),s.splice(d,1))}a=r}}}},87206:function(t){function e(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t){return null!=t&&(e(t)||function(t){return"function"==typeof t.readFloatLE&&"function"==typeof t.slice&&e(t.slice(0,0))}(t)||!!t._isBuffer)}},87935:function(t,e,a){"use strict";var n=a(76798),i=a.n(n)()((function(t){return t[1]}));i.push([t.id,".progress-bar[data-v-2b96e190]{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;margin:0 15px;overflow:hidden;position:relative}.progress-bar[data-v-2b96e190],.progress-bar-inner[data-v-2b96e190]{border-radius:9999px;-webkit-box-shadow:none;box-shadow:none}.progress-bar-inner[data-v-2b96e190]{height:100%;left:0;position:absolute;top:0;-webkit-transition:width .6s ease;transition:width .6s ease}",""]),e.A=i},93864:function(t){"use strict";t.exports=function(t){return!(!t||!t.__CANCEL__)}},96987:function(t,e,a){"use strict";var n=a(65606),i=a(9516),r=a(7018),s={"Content-Type":"application/x-www-form-urlencoded"};function o(t,e){!i.isUndefined(t)&&i.isUndefined(t["Content-Type"])&&(t["Content-Type"]=e)}var l,c={adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==n)&&(l=a(35592)),l),transformRequest:[function(t,e){return r(e,"Content-Type"),i.isFormData(t)||i.isArrayBuffer(t)||i.isBuffer(t)||i.isStream(t)||i.isFile(t)||i.isBlob(t)?t:i.isArrayBufferView(t)?t.buffer:i.isURLSearchParams(t)?(o(e,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):i.isObject(t)?(o(e,"application/json;charset=utf-8"),JSON.stringify(t)):t}],transformResponse:[function(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(t){}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(t){return t>=200&&t<300}};c.headers={common:{Accept:"application/json, text/plain, */*"}},i.forEach(["delete","get","head"],(function(t){c.headers[t]={}})),i.forEach(["post","put","patch"],(function(t){c.headers[t]=i.merge(s)})),t.exports=c}},e={};function a(n){var i=e[n];if(void 0!==i)return i.exports;var r=e[n]={id:n,exports:{}};return t[n](r,r.exports,a),r.exports}a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,{a:e}),e},a.d=function(t,e){for(var n in e)a.o(e,n)&&!a.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},a.nc=void 0,new Vue({el:"#vocabulary_content",data:{list_vocabulary:list_vocabulary},components:{vocabulary:a(67145).A},methods:{showContent:function(t){$(".content-section").removeClass("active"),$("#content-"+t).addClass("active"),$("#navMenu").removeClass("active")}}})}();