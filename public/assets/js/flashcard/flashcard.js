!function(){function a(a){return function(a){if(Array.isArray(a))return e(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||function(a,n){if(a){if("string"==typeof a)return e(a,n);var t={}.toString.call(a).slice(8,-1);return"Object"===t&&a.constructor&&(t=a.constructor.name),"Map"===t||"Set"===t?Array.from(a):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?e(a,n):void 0}}(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e(a,e){(null==e||e>a.length)&&(e=a.length);for(var n=0,t=Array(e);n<e;n++)t[n]=a[n];return t}Vue.component("v-select",VueSelect.VueSelect),new Vue({el:"#flashcardEdit",data:function(){return{component:component,formData:{word:"",word_stress:"",meaning:"",word_type:[],audio:"",front_image:"",back_image:"",kanji_meaning:"",show:"1",examples:[],meaning_examples:[],quiz_questions:["","","",""]},wordTypes:[{id:"Danh từ",text:"Danh từ"},{id:"Động từ",text:"Động từ"},{id:"Tính từ đuôi な",text:"Tính từ đuôi な"},{id:"Tính từ đuôi い",text:"Tính từ đuôi い"},{id:"Phó từ",text:"Phó từ"},{id:"Liên từ",text:"Liên từ"},{id:"Katakana",text:"Katakana"},{id:"Số lượng từ",text:"Số lượng từ"},{id:"Câu thông dụng",text:"Câu thông dụng"},{id:"Chữ Hán",text:"Chữ Hán"},{id:"Danh động từ",text:"Danh động từ"},{id:"Trạng từ",text:"Trạng từ"}],configCkEditor:{extraPlugins:"stylescombo,maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",fontSize_sizes:"8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;64/64px;72/72px;",allowedContent:{$1:{elements:CKEDITOR.dtd,attributes:!0,styles:!0,classes:!0}},disallowedContent:"script;style; *[on*]",colorButton_colors:"EF6D13,FF0000,FF9900,FFFF00,008000,0000FF,800080,000000,FFFFFF,FFC0CB,A52A2A,808080,00FFFF,FFD700,FF1493,87CEEB,FF6347,40E0D0,EE82EE,F5DEB3,C0C0C0",stylesSet:[{name:"Overline Text",element:"span",attributes:{style:"text-decoration: overline;"}}]}}},mounted:function(){this.initData(),this.initCkEditor(),this.initEventHandlers()},methods:{initData:function(){if(this.component&&this.component.value){var e=JSON.parse(this.component.value);if(this.formData.word=e.word||"",this.formData.word_stress=e.word_stress||"",this.formData.meaning=e.meaning||"",this.formData.word_type=e.word_type&&e.word_type.split(", ")||[],this.formData.audio=e.audio||"",this.formData.front_image=e.front_image||"",this.formData.back_image=e.back_image||"",this.formData.kanji_meaning=e.kanji_meaning||"",this.formData.show=e.show||"1",e.example&&Array.isArray(e.example)&&(this.formData.examples=e.example.map((function(a,e){return{id:e+1,example:a.example||"",audio:a.audio||""}}))),e.meaning_example&&Array.isArray(e.meaning_example)&&(this.formData.meaning_examples=e.meaning_example.map((function(a,e){return{id:e+1,content:a}}))),e.quiz_question&&Array.isArray(e.quiz_question))for(this.formData.quiz_questions=a(e.quiz_question);this.formData.quiz_questions.length<4;)this.formData.quiz_questions.push("")}},initCkEditor:function(){var a=this;$(".editor").each((function(e,n){CKEDITOR.instances[n.id]||CKEDITOR.replace(n.id,a.configCkEditor)})),this.$nextTick((function(){CKEDITOR.instances.word&&CKEDITOR.instances.word.setData(a.formData.word),CKEDITOR.instances.word_stress&&CKEDITOR.instances.word_stress.setData(a.formData.word_stress),CKEDITOR.instances.meaning&&CKEDITOR.instances.meaning.setData(a.formData.meaning),a.formData.examples.forEach((function(e,n){var t="example-".concat(e.id);CKEDITOR.instances[t]&&(CKEDITOR.instances[t].setData(e.example),CKEDITOR.instances[t].on("change",(function(){var n=a.formData.examples.findIndex((function(a){return a.id===e.id}));Vue.set(a.formData.examples,n,{id:e.id,example:CKEDITOR.instances[t].getData(),audio:e.audio})})))})),a.formData.meaning_examples.forEach((function(e,n){var t="meaning-example-".concat(e.id);CKEDITOR.instances[t]&&(CKEDITOR.instances[t].setData(e.content),CKEDITOR.instances[t].on("change",(function(){var n=a.formData.meaning_examples.findIndex((function(a){return a.id===e.id}));Vue.set(a.formData.meaning_examples,n,{id:e.id,content:CKEDITOR.instances[t].getData()})})))}))}))},initEventHandlers:function(){var a=this;this.$nextTick((function(){CKEDITOR.instances.word&&CKEDITOR.instances.word.on("change",(function(){a.formData.word=CKEDITOR.instances.word.getData()})),CKEDITOR.instances.word_stress&&CKEDITOR.instances.word_stress.on("change",(function(){a.formData.word_stress=CKEDITOR.instances.word_stress.getData()})),CKEDITOR.instances.meaning&&CKEDITOR.instances.meaning.on("change",(function(){a.formData.meaning=CKEDITOR.instances.meaning.getData()}))})),$(document).on("click",".btn-remove-row",(function(e){var n=$(e.target).closest(".example-group").data("id");a.formData.examples=a.formData.examples.filter((function(a){return a.id!==n})),a.$emit("change",a.formData.examples),$(e.target).closest(".example-group").remove(),a.$nextTick((function(){$(".example-group").map((function(a,e){$(e).find(".example-label").text("Ví dụ "+(a+1)+":")}))}))})),$(document).on("click",".btn-remove-meaning",(function(e){var n=$(e.target).closest(".meaning-example-group").data("id");a.formData.meaning_examples=a.formData.meaning_examples.filter((function(a){return a.id!==n})),$(e.target).closest(".meaning-example-group").remove(),a.$nextTick((function(){$(".meaning-example-group").map((function(a,e){$(e).find(".meaning-example-label").text("Ví dụ "+(a+1)+":")}))}))})),$(document).on("click",".btn-upload-file",(function(a){var e=$(a.target).data("id");$("#"+e).trigger("click")})),$(document).on("change",".upload-file",(function(e){var n=$(e.target).prop("files")[0],t=$(e.target).data("type"),i=$(e.target).data("id");a.uploadFile(n,t,(function(n){if($("#"+i).val(n.data.file_path),$("#"+i+"_file").val(""),"audio"===i)a.formData.audio=n.data.file_path;else if("front_image"===i)a.formData.front_image=n.data.file_path;else if("back_image"===i)a.formData.back_image=n.data.file_path;else if(i.startsWith("audio_example_")){var t=$(e.target).closest(".example-group").data("id");a.formData.examples.find((function(a){return a.id===t})).audio=n.data.file_path}}))})),$("#add-example").click((function(){var e=Math.random().toString(36).substring(2,15);a.formData.examples.push({id:e,example:"",audio:""});var n='\n          <div class="example-group mb-3" data-id="'.concat(e,'">\n            <label class="example-label">Ví dụ :</label>\n            <textarea class="form-control mb-2 editor" id="example-').concat(e,'" name="examples[]"></textarea>\n            <div class="input-group my-2 flex">\n              <div class="form-group">\n                <label class="col-sm-2 control-label">Audio</label>\n                <div class="col-sm-8">\n                  <input type="text" name="audio_examples[]" placeholder="Nhấp để chọn file" readonly id="audio_example_').concat(e,'" class="form-control">\n                  <input type="file" style="display: none" accept="audio/*" class="upload-file" id="audio_example_').concat(e,'_file" data-id="audio_example_').concat(e,'" data-type="audio">\n                </div>\n                <div class="col-sm-2">\n                  <div class="flex">\n                    <button type="button" class="btn btn-primary btn-upload-file" data-id="audio_example_').concat(e,'_file">Tải lên</button>\n                    <button type="button" class="ml-1 btn btn-danger btn-remove-row">Xóa</button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ');$("#examples-container").append(n),a.$nextTick((function(){$(".example-group").map((function(a,e){$(e).find(".example-label").text("Ví dụ "+(a+1)+":")})),CKEDITOR.replace("example-"+e,a.configCkEditor),CKEDITOR.instances["example-"+e].on("change",(function(){var n=a.formData.examples.findIndex((function(a){return a.id===e}));Vue.set(a.formData.examples,n,{id:e,example:CKEDITOR.instances["example-"+e].getData(),audio:""}),console.log(a.formData.examples)}))}))})),$("#add-meaning-example").click((function(){var e=Math.random().toString(36).substring(2,15);a.formData.meaning_examples.push({id:e,content:""});var n='\n          <div class="meaning-example-group mb-3" data-id="'.concat(e,'">\n            <label class="meaning-example-label">Ví dụ ').concat(e,':</label>\n            <textarea class="form-control editor" id="meaning-example-').concat(e,'" name="meaning_examples[]"></textarea>\n            <button type="button" class="btn btn-danger btn-remove-meaning">Xóa</button>\n          </div>\n        ');$("#meaning-examples-container").append(n),a.$nextTick((function(){$(".meaning-example-group").map((function(a,e){$(e).find(".meaning-example-label").text("Ví dụ "+(a+1)+":")})),CKEDITOR.replace("meaning-example-"+e,a.configCkEditor),CKEDITOR.instances["meaning-example-"+e].on("change",(function(){var n=a.formData.meaning_examples.findIndex((function(a){return a.id===e}));Vue.set(a.formData.meaning_examples,n,{id:e,content:CKEDITOR.instances["meaning-example-"+e].getData()})}))}))})),$("#flashcardForm").submit((function(e){e.preventDefault();var n=new FormData;n.append("lesson_id",$("#lesson_id").val()),n.append("word",a.formData.word),n.append("word_stress",a.formData.word_stress),n.append("meaning",a.formData.meaning),console.log(a.formData.word_type),n.append("word_type",a.formData.word_type.join(", ")),n.append("audio",a.formData.audio),n.append("front_image",a.formData.front_image),n.append("back_image",a.formData.back_image),n.append("kanji_meaning",a.formData.kanji_meaning),n.append("show",a.formData.show),a.formData.quiz_questions.forEach((function(a,e){n.append("quiz_questions[".concat(e,"]"),a)})),a.formData.examples.forEach((function(a,e){n.append("examples[".concat(e,"]"),a.example),n.append("audio_examples[".concat(e,"]"),a.audio)})),a.formData.meaning_examples.forEach((function(a,e){n.append("meaning_examples[".concat(e,"]"),a.content)})),$.ajax({url:"/backend/flashcard/"+(a.component?a.component.id+"/update":"create"),type:"POST",headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")},data:n,processData:!1,contentType:!1,success:function(a){a.success?window.location.href="/backend/flashcard":alert(a.error)},error:function(a){alert(a.responseJSON.error)}})}))},uploadFile:function(a,e,n){$.ajax({type:"get",url:"/backend/video/api/get-token",success:function(t){var i=new FormData;i.append("token",t),i.append("file_upload",a),i.append("type",e),$.ajax({type:"post",url:videoBaseURL+"/api/admin/upload-file",processData:!1,contentType:!1,data:i,success:function(a){n&&n(a)},error:function(){alert("Lỗi upload file")}})}})}}})}();