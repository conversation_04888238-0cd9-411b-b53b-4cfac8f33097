<?php

namespace App\Http\Controllers\Backend\Vip;

use App\Base\Controller\BaseController;
use App\CustomFilters\VipInvoiceFilters;
use App\Exports\BookExport;
use App\Exports\VipInvoiceExport;
use App\Http\Models\Admin;
use App\Http\Models\Combo;
use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityGroupUser;
use App\Http\Models\Conversation;
use App\Http\Models\Course;
use App\Http\Models\CourseOwner;
use App\Http\Models\InvoiceBonus;
use App\Http\Models\ThiThu\CheckpointLog;
use App\Http\Models\Vip\InvoiceBook;
use App\Http\Models\LessonOwner;
use App\Http\Models\PaymentMethod;
use App\Http\Models\School\CourseStudent;
use App\Http\Models\School\Department;
use App\Http\Models\School\SchoolCourse;
use App\Http\Models\School\SchoolUser;
use App\Http\Models\Users;
use App\Http\Models\Vip\VipCombo;
use App\Http\Models\Vip\VipCourse;
use App\Http\Models\Invoice;
use App\Http\ModelsFrontend\ConversationUser;
use App\Http\Requests\CreateVipInvoiceRequest;
use App\Http\Requests\UpdateVipInvoiceRequest;
use App\Models\Achievement;
use App\Models\Pivots\RewardAudit;
use App\Services\Community\CommunityGroupService;
use App\Services\Community\CommunityUserService;
use App\User;
use Carbon\Carbon;
use Hashids\Hashids;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Auth, DB;
use Pusher;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Hash;

class VipInvoiceController extends BaseController
{
    /** @var CommunityUserService */
    protected $communityUserService;

    /*** @var CommunityGroupService */
    protected $communityGroupService;

    /**
     * @param CommunityUserService $communityUserService
     * @param CommunityGroupService $communityGroupService
     */
    public function __construct(
        CommunityUserService  $communityUserService,
        CommunityGroupService $communityGroupService
    )
    {
        $this->communityUserService = $communityUserService;
        $this->communityGroupService = $communityGroupService;
    }

    /**
     * @param $target
     * @param $subject
     * @param $column
     * @param $from
     * @param $to
     * @return \stdClass
     */
    private function generateLog($target, $subject, $column, $from, $to): \stdClass
    {
        $log = new \stdClass();
        $log->timestamp = now();
        $log->target = $target;
        $log->subject = $subject;
        $log->column = $column;
        $log->from = $from;
        $log->to = $to;
        $log->created_by = Auth::guard('admin')->user()->id . ' ' . Auth::guard('admin')->user()->name;
        return $log;
    }

    /** Lấy ra đơn hàng lớp vip */
    public function getVipInvoiceList(Request $request)
    {
        $comboList = Combo::query()->select('id', 'name', 'price', 'type', 'services')->where('type', '<>', 'book')->get();
        $vipComboList = VipCombo::query()->select('id', 'name', 'price', 'type', 'services')->get();
        $courseList = VipCourse::query()->select('id', 'name')->get();
        $paymentMethodList = PaymentMethod::query()->select('id', 'name')->whereNotIn('id', [2, 5])->get();
        $departmentList = Department::query()->select('id', 'name', 'deleted')->where('deleted', 0)->get();
        $saleList = Admin::query()->select('id', 'name')->whereIn('desc', ['sale', 'sale_offline'])->get();
        return view('backend.vip.vip_invoice')
            ->with([
                'comboList' => $comboList,
                'vipComboList' => $vipComboList,
                'courseList' => $courseList,
                'paymentMethodList' => $paymentMethodList,
                'saleList' => $saleList,
                'departmentList' => $departmentList,
            ]);
    }

    public function getList(VipInvoiceFilters $filters, Request $request)
    {
        $page = $request->page ? $request->page : 1;
        $per_page = $request->per_page ? $request->per_page : 20;

        $invoiceIds = [];
        if (isset($request->depositStatus) && in_array($request->depositStatus, [3,4])) {
            $childSums = DB::table('invoice as relevant_invoices')
                ->select('paid_for_id', DB::raw('SUM(paid_money + discount_money) as total_paid_money'))
                ->whereNotNull('paid_for_id')
                ->groupBy('paid_for_id');

            // Then, join the results with the parent invoices and filter

            $invoiceIds = DB::table('invoice as parents')
                ->leftJoinSub($childSums, 'relevant_invoices', function($join) {
                    $join->on('parents.id', '=', 'relevant_invoices.paid_for_id');
                })
                ->where(function($query) use ($request) {
                    $query->whereColumn('relevant_invoices.total_paid_money', $request->depositStatus == 3 ? '>=' : '<', DB::raw('parents.price - parents.paid_money'));
                    if ($request->depositStatus == 4) {
                        $query->orWhereNull('relevant_invoices.total_paid_money');
                    }

                })
                ->pluck('parents.id');
        }


        $query = Invoice::query()
            ->select(
                'id', 'uuid', 'user_id', 'product_id', 'book_id', 'coupon_id', 'product_type', 'department_id',
                'product_name', 'info_contact', 'payment_status', 'invoice_status', 'price', 'currency', 'extra_price', 'extra_days',
                'active_time', 'admin_active', 'paid_at', 'admin_active_name', 'admin_create', 'payment_method_id', 'note',
                'hide', 'logs', 'updated_at', 'created_at', 'discount_money', 'paid_money', 'gift', 'book_info', 'paid_for_id'
            )
            ->with('user.conversation', 'voucher', 'book')
            ->with(['combo' => function ($q) {
                $q->select('id', 'name', 'price', 'status', 'type', 'services');
            }])
            ->with(['course' => function ($q) {
                $q->select('id', 'name', 'price', 'show', 'type');
            }])
            ->with(['vip_combo' => function ($q) {
                $q->select('id', 'name', 'price', 'status', 'type');
            }])
            ->with('vip_combo.courses')
            ->with(['user' => function ($q) {
                $q->with(['groups' => function ($q) {
                    $q->select('community_groups.id', 'name', 'vip_level');
                }])->with(['offline_user' => function ($q) {
                    $q->select('id', 'first_name', 'last_name', 'email', 'phone', 'department_id', 'dmr_id')->with('courses');
                }])->select('id', 'name', 'email', 'phone', 'phone_number');
            }])
            ->with(['sale' => function ($q) {
                $q->select('id', 'name');
            }])
            ->with(['payment_method' => function ($q) {
                $q->select('id', 'name');
            }])
            ->with(['coupon' => function ($q) {
                $q->select('id', 'code', 'value', 'type');
            }])
            ->with(['product' => function ($q) {
                $q->select('id', 'name', 'price', 'status', 'type');
            }])
            ->with(['voucher' => function ($q) {
                $q->select('id', 'key', 'invoice_id');
            }])
            ->with(['book' => function ($q) {
                $q->select('id', 'name', 'type', 'price', 'status');
            }])
            ->with(['department' => function ($q) {
                $q->select('id', 'name');
            }])
            ->with(['bonus' => function ($q) {
                $q->select('id', 'invoice_id', 'course_id', 'days', 'status')->with('course:id,name');
            }])
            ->with(['bonuses' => function ($q) {
                $q->select('id', 'invoice_id', 'course_id', 'days', 'status')->with('course:id,name');
            }])
            ->with('books')
            ->with('refundBooks')
            ->with(['applied_reward' => function ($q) {
                $q->select('claim_at', 'id', 'invoice_id', 'reward_id', 'user_id')
                    ->with(['reward' => function ($q) {
                        $q->select('id', 'value', 'type');
                    }]);
            }])
            ->with(['relevant_invoices' => function ($q) {
                $q->select(
                    'id', 'uuid', 'user_id', 'product_id', 'book_id', 'coupon_id', 'product_type', 'department_id',
                    'product_name', 'info_contact', 'payment_status', 'invoice_status', 'price', 'currency', 'extra_price', 'extra_days',
                    'active_time', 'admin_active', 'paid_at', 'admin_active_name', 'admin_create', 'payment_method_id', 'note',
                    'hide', 'logs', 'updated_at', 'created_at', 'discount_money', 'paid_money', 'gift', 'book_info', 'paid_for_id'
                );
            }])
            ->with(['deposit_invoice' => function ($q) {
                $q->select(
                    'id', 'uuid', 'user_id', 'product_id', 'book_id', 'coupon_id', 'product_type', 'department_id',
                    'product_name', 'info_contact', 'payment_status', 'invoice_status', 'price', 'currency', 'extra_price', 'extra_days',
                    'active_time', 'admin_active', 'paid_at', 'admin_active_name', 'admin_create', 'payment_method_id', 'note',
                    'hide', 'logs', 'updated_at', 'created_at', 'discount_money', 'paid_money', 'gift', 'book_info', 'paid_for_id'
                );
            }])
            ->filter($filters)
            ->orderBy('id', 'DESC');
        if (isset($request->depositStatus) && in_array($request->depositStatus, [3,4])) {
            $query->whereIntegerInRaw('id', $invoiceIds);
        }
        $results = $query->fastPaginate($per_page, $page);
        return $this->statusOK($results);
    }

    public function getInvoice($id)
    {
        $invoice = Invoice::query()
            ->select(
                'id', 'uuid', 'user_id', 'product_id', 'book_id', 'coupon_id', 'product_type', 'department_id',
                'product_name', 'info_contact', 'payment_status', 'invoice_status', 'price', 'currency', 'extra_price', 'extra_days',
                'active_time', 'admin_active', 'paid_at', 'admin_active_name', 'admin_create', 'payment_method_id', 'note',
                'hide', 'logs', 'updated_at', 'created_at', 'discount_money', 'paid_money', 'gift', 'book_info', 'paid_for_id'
            )
            ->with('user.conversation', 'voucher', 'book')
            ->with(['combo' => function ($q) {
                $q->select('id', 'name', 'price', 'status', 'type');
            }])
            ->with(['vip_combo' => function ($q) {
                $q->select('id', 'name', 'price', 'status', 'type');
            }])
            ->with('vip_combo.courses')
            ->with(['user' => function ($q) {
                $q->select('id', 'name');
            }])
            ->with(['bonus' => function ($q) {
                $q->select('id', 'invoice_id', 'course_id', 'days', 'status')->with('course:id,name');
            }])
            ->with(['bonuses' => function ($q) {
                $q->select('id', 'invoice_id', 'course_id', 'days', 'status')->with('course:id,name');
            }])
            ->with('user.groups')
            ->with('user.offline_user.courses')
            ->with(['sale' => function ($q) {
                $q->select('id', 'name');
            }])
            ->with(['payment_method' => function ($q) {
                $q->select('id', 'name');
            }])
            ->with(['coupon' => function ($q) {
                $q->select('id', 'code', 'value', 'type');
            }])
            ->with(['product' => function ($q) {
                $q->select('id', 'name', 'price', 'status', 'type');
            }])
            ->with(['voucher' => function ($q) {
                $q->select('id', 'key', 'invoice_id');
            }])
            ->with('book')
            ->with('books')
            ->with('refundBooks')
            ->with('department')->find($id);
        return $this->statusOK($invoice);
    }

    public function export(VipInvoiceFilters $filters, Request $request)
    {
        $page = $request->page ? $request->page : 1;
        $per_page = $request->per_page ? $request->per_page : 20;
        if (isset($request->depositStatus) && in_array($request->depositStatus, [3,4])) {
            $childSums = DB::table('invoice as relevant_invoices')
                ->select('paid_for_id', DB::raw('SUM(paid_money + discount_money) as total_paid_money'))
                ->whereNotNull('paid_for_id')
                ->groupBy('paid_for_id');

            // Then, join the results with the parent invoices and filter

            $invoiceIds = DB::table('invoice as parents')
                ->leftJoinSub($childSums, 'relevant_invoices', function($join) {
                    $join->on('parents.id', '=', 'relevant_invoices.paid_for_id');
                })
                ->where(function($query) use ($request) {
                    $query->whereColumn('relevant_invoices.total_paid_money', $request->depositStatus == 3 ? '>=' : '<', DB::raw('parents.price - parents.paid_money'));
                    if ($request->depositStatus == 4) {
                        $query->orWhereNull('relevant_invoices.total_paid_money');
                    }

                })
                ->pluck('parents.id');
        }
        $query = Invoice::query()->with(
            'combo', 'vip_combo.courses',
            'sale', 'payment_method',
            'coupon', 'voucher', 'book', 'department', 'group_user.group'
        )
            ->with(['group_user.group' => function ($q) {
                $q->select('community_groups.id', 'name', 'vip_level');
            }])
            ->with(['books' => function ($q) {
            $q->select('combo.id', 'name', 'price', 'jpy_price', 'quantity', 'alias_name');
        }])
            ->with(['user' => function ($q) {
                $q->with(['groups' => function ($q) {
                    $q->select('community_groups.id', 'name', 'vip_level');
                }])->with(['offline_user' => function ($q) {
                    $q->select('id', 'first_name', 'last_name', 'email', 'phone', 'department_id', 'dmr_id')
                        ->with(['courses.info' => function ($q) {
                            $q->with('room')->with('periods');
                        }])
                        ->with('courses.infos')
                        ->with('courses.teachers')
                    ;
                }])->select('id', 'name', 'email', 'phone', 'phone_number');
            }])
            ->filter($filters)->orderBy('id', 'DESC');
        if (isset($request->depositStatus) && in_array($request->depositStatus, [3,4])) {
            $query->whereIntegerInRaw('id', $invoiceIds);
        }
        $results = $query->get()->toArray();


//        dd($results[4]['user']);
        return Excel::download(new VipInvoiceExport(
            $results
        ), 'invoices.xlsx');
    }

    public function exportBookExcel(VipInvoiceFilters $filters, Request $request)
    {
        $ids = Invoice::query('id')->filter($filters)->orderBy('id', 'DESC')->pluck('id')->toArray();;

        if ($ids) {
            $timeExport = Carbon::now()->format("Y-m-d");
            DB::table('invoice')->where("book_info->exportDate", "")->whereIn('id', $ids)->update(["book_info->exportDate" => $timeExport]);
        }

        $query = Invoice::query('id')->with(['books' => function ($q) {
            $q->select('combo.id', 'name', 'price', 'jpy_price', 'quantity', 'alias_name');
        }])->filter($filters)->orderBy('id', 'DESC');

        $results = $query->get()->toArray();

        DB::table('invoice')->whereIn('id', $ids)->update(["book_info->exportStatus" => "print"]);

        $time = Carbon::now()->format("d-m-Y_H-i");

        return Excel::download(new BookExport(
            $results
        ), 'book-invoices_' . $time . '.xlsx');
    }

    public function activeOnline(Request $request)
    {
        $user = User::find($request->userId);

        $this->activeOnlineCourseForUser($user->id, $request->invoiceId);
        return "success";
    }

    private function activeOnlineCourseForUser($userId, $invoiceId): void
    {
        // Đánh dấu hoàn thành invoice
        $invoice = Invoice::query()->find($invoiceId);
        $tmp = $invoice->logs ?? [];

        $isVipCombo = in_array($invoice->product_type, ['vip_combo', 'offline']);
        // kích hoạt tất cả khoá có trong combo theo thời hạn của combo
        if ($isVipCombo) {
            $vipCombo = VipCombo::with('courses')->find($invoice->product_id);
            foreach ($vipCombo->courses as $vip_course) {
                $courseIds = [];
                foreach (ONLINE_COURSES as $key => $value) {
                    if (in_array($vip_course->level, $value)) {
                        $courseIds[] = $key;
                    }
                }
    
                foreach ($courseIds as $courseId) {
                    $this->activeComboCourse($userId, $courseId, $vipCombo->watch_expired);
                }
            }
        } else {
            if ($invoice->product_type == 'combo') {
                $vipCombo = Combo::query()->find($invoice->product_id);
                $courseIds = json_decode($vipCombo->services)->courses;
                $watchExpiredValue = json_decode($vipCombo->services)->course_watch_expired_value;
                foreach ($courseIds as $courseId) {
                    $this->activeComboCourse($userId, $courseId, $watchExpiredValue);
                }
            } else {
                $course = Course::query()->find($invoice->product_id);
                $this->activeComboCourse($userId, $invoice->product_id, $course->watch_expired);
            }
        }

        $invoice->logs = $tmp;
        if (is_null($invoice->active_time)) {
            $invoice->active_time = now();
        }
        $invoice->save();
    }

    public function activeComboCourse($userId, $courseId, $watchExpiredDay = 0) {
        $course = Course::find($courseId);
        $oldItem = LessonOwner::where('owner_id', $userId)->where('course_id', $courseId)->first();

        $extra_days = 0;
        //nếu chưa tồn tại sở hữu -> tạo record mới
        if (!$oldItem) {
            //ngày hết hạn =  ngày giới hạn của course + ngày của khóa plus
            $watch_expired_day = Carbon::now()->addDays($watchExpiredDay + $extra_days);
            $active = new LessonOwner();
            $active->title = $course->name;
            $active->owner_id = $userId;
            $active->course_id = $courseId;
            $active->watch_expired_day = $watch_expired_day;
            $active->extra_expired_day = $watch_expired_day;
            $active->extra_days = $extra_days;

            //nếu là admin kích hoạt
            if (Auth::guard('admin')->user())
                $active->admin_active = Auth::guard('admin')->user()->username;
            else
                $active->admin_active = "user";

            //gia hạn 10 buổi kaiwa
            if ($active->course_id == 21) $active->kaiwa_total_booking = 10;

            //nếu là n3 -> mở cho khóa zoom n3
            // if($invoice->product_id == 3) $this->activeZoom($invoice->user_id, 27, $active->watch_expired_day);

            $active->save();
            $tmp[] = $this->generateLog($active->title, 'online_course_activation', 'course_owner.watch_expired_day', null, $watch_expired_day);
            //nếu đã tồn tại sở hữu -> gia hạn thêm
        } else {
            //truong hop khoa đó còn hạn
            if ($oldItem->watch_expired_day >= date("Y-m-d H:i:s")) {
                $watch_expired_day = Carbon::parse($oldItem->watch_expired_day)->addDays($watchExpiredDay + $extra_days);
                //gia hạn 10 buổi kaiwa
                if ($oldItem->course_id == 21) $oldItem->kaiwa_total_booking += 10;

                //truong hop khoa đó hết hạn
            } else {
                $watch_expired_day = Carbon::now()->addDays($watchExpiredDay + $extra_days);
                //gia hạn 10 buổi kaiwa
                if ($oldItem->course_id == 21) $oldItem->kaiwa_total_booking = 10;
            }

            if ($oldItem->extra_days == 0 || $oldItem->extra_days == null) {
                $oldItem->extra_days = $extra_days;
            }

            if ($oldItem->extra_expired_day == 0 || $oldItem->extra_expired_day == null) {
                $oldItem->extra_expired_day = $watch_expired_day;
            }
            $tmp[] = $this->generateLog($oldItem->title, 'online_course_activation', 'course_owner.watch_expired_day', $oldItem->watch_expired_day, $watch_expired_day);

            $oldItem->watch_expired_day = $watch_expired_day;
            $oldItem->admin_update = Auth::guard('admin')->user()->username;
            $oldItem->save();
            //nếu là n3 -> mở cho khóa zoom n3
            // if($invoice->product_id == 3) $this->activeZoom($invoice->user_id, 27, $oldItem->watch_expired_day);
        }
    }

    public function getUser(Request $request)
    {
        $user = null;
        if (isset($request->email) && $request->email) {
            $user = Users::query()->with(['course_owner' => function ($q) {
                $q->where('watch_expired_day', '>', now());
            }])
                ->where('email', $request->email)->first();
            if (!is_null($user)) {
                $user->checkpoint_logs = CheckpointLog::where('user_info->email', $request->email)->with('lesson.getCourse')->get();
            }
        }
        if (isset($request->userId) && $request->userId) {
            $user = Users::query()->with(['course_owner' => function ($q) {
                $q->where('watch_expired_day', '>', now());
            }])
                ->find($request->userId);
        }

        return $this->statusOK($user);
    }

    /** tạo mới đơn hàng lớp vip */
    public function createNewVipInvoice(CreateVipInvoiceRequest $request): \Illuminate\Http\JsonResponse
    {
        $formData = $request->only(
            'forceDuplicate', 'isVip', 'isOnline', 'activeOnline', 'email', 'password', 'fullname', 'phone', 'facebook', 'department',
            'chat', 'comboId', 'comboType', 'coupon', 'paymentMethod', 'paidAt', 'sale', 'note', 'groupId', 'price', 'currency',
            'discountMoney', 'paidMoney', 'gift', 'rewardId', 'bookData', 'bookIds', 'bonus', 'bonuses', 'paidFor', 'address');

        $email = $formData['email'] ? trim(preg_replace('/ /', '', $formData['email'])) : '';
        $phone = $formData['phone'] ? trim(preg_replace('/ /', '', $formData['phone'])) : '';
        $combo = [];
        if (isset($formData['isVip'])
            && $formData['isVip']
            && isset($formData['isOnline'])
            && $formData['isOnline']
            && !isset($formData['fullname'])
        ) {
            throw new HttpResponseException(response()->json([
                'status' => 'error',
                'detail' => [
                    'fullname' => ['Không được để trống']
                ]
            ]));
        }
        // Kiểm tra đơn hàng combo
        if (isset($formData['comboId']) && $formData['comboId']) {
            if (isset($formData['isVip']) && $formData['isVip']) {
                $combo = VipCombo::query()->where('id', $formData['comboId']['id'])->first();
            } else {
                $combo = Combo::query()->where('id', $formData['comboId']['id'])->first();
            }
        }

        $bookName = '';
        if (isset($formData['bookIds'])) {
            foreach ($formData['bookIds'] as $book) {
                $bookName = trim($bookName . ' + ' . $book['name'], ' + ');
            }
        }

        if (!$formData['isOnline'] && !isset($formData['paidFor'])) {
            $checkProductName = $combo['name'] ?? '';
            $isDuplicated = Invoice::query()
                ->where('info_contact->email', $email)
                ->where('info_contact->phone', $phone)
                ->where('product_name', 'LIKE', $checkProductName . '%')
                ->where('invoice_status', '<>', 'canceled')
                ->exists();
            if ($isDuplicated && !isset($formData['forceDuplicate'])) {
                throw new HttpResponseException(response()->json([
                    'code' => 409,
                    'msg' => 'Đơn hàng tương tự của người dùng đã được tạo. Nếu là đơn chưa thanh toán, vui lòng chọn nộp thêm ở đơn cũ'
                ]));
            }
        }
        if ($combo || isset($formData['bookIds'])) {
            // Kiểm tra đơn hàng tồn tại trong 24h
            $checkProductName = $combo['name'] ?? '';

//            $isDuplicated = Invoice::query()
//                ->where('info_contact->email', $email)
//                ->where('info_contact->phone', $phone)
//                ->where('book_info->location', $formData['bookData']['location'])
//                ->where('product_name', 'LIKE', $checkProductName . '%')
//                ->where('product_name', 'LIKE', $bookName . '%')
//                ->whereDate('created_at', Carbon::today())
//                ->where('invoice_status', '<>', 'canceled')
//                ->exists();
            $isDuplicated = false;
            if ($isDuplicated && !isset($formData['forceDuplicate'])) {
                throw new HttpResponseException(response()->json([
                    'code' => 409,
                    'msg' => 'Đơn hàng tương tự của người dùng đã được tạo trong hôm nay. Xác nhận tiếp tục tạo?'
                ]));
            }
        }
        // End check invoice exists
        $user = Users::query()->where('email', $email)->first();
        if (!$formData['password']) {
            if ($formData['isVip'] || $combo) {
                if ($formData['isOnline'] || $combo) {
                    if (!isset($formData['email']) || !$formData['email']) {
                        throw new HttpResponseException(response()->json([
                            'status' => 'error',
                            'detail' => [
                                'email' => ['Không để trống email']
                            ]
                        ]));
                    }
                    /** Tìm user theo email, không có user thì báo lỗi */
                    if (is_null($user)) {
                        throw new HttpResponseException(response()->json([
                            'status' => 'error',
                            'detail' => [
                                'email' => ['Người dùng không tồn tại']
                            ]
                        ]));
                    }
                }
            }
        } else {
            $user = Users::query()->where('email', $email)->first();
            if (is_null($user)) {
                $user = new Users();
                $user->email = $email;
                $user->password = Hash::make($formData['password']);
                $user->name = $formData['fullname'];
                $user->phone = $formData['phone'];
                $user->activation = '1';
                $user->level_id = 1;
                $user->save();
            }
        }

        if (isset($formData['isOnline']) && !$formData['isOnline'] && $combo) {
            $offlineUser = SchoolUser::query()->where('phone', $formData['phone'])->orWhere('email', $email)->first();
            if (is_null($offlineUser)) {
                $offlineUser = new SchoolUser();
                $offlineUser->email = trim(preg_replace('/\t/', '', $email));
                $offlineUser->dmr_id = $user->id;
                $offlineUser->password = Hash::make($formData['password']);
                $parts = explode(" ", $formData['fullname']);
                if (count($parts) > 1) {
                    $offlineUser->first_name = array_pop($parts);
                    $offlineUser->last_name = implode(" ", $parts);
                } else {
                    $offlineUser->last_name = $formData['fullname'];
                    $offlineUser->first_name = " ";
                }
                $offlineUser->phone = $formData['phone'];
                $offlineUser->department_id = $formData['department'];
                $offlineUser->role = 3;
                $offlineUser->status = 0;
                $offlineUser->deleted = 0;
                $offlineUser->created = now();
                $offlineUser->updated = now();
                $offlineUser->save();
            } else {
                $offlineUser->dmr_id = $user->id;
                $offlineUser->phone = $formData['phone'];
                $offlineUser->email = trim(preg_replace('/\t/', '', $email));
                $offlineUser->department_id = $formData['department'];
                $offlineUser->save();
            }
        }

        if (isset($formData['groupId']) && count($formData['groupId']) && $formData['isOnline']) {
            foreach ($formData['groupId'] as $groupId) {
                $group = CommunityGroup::with('group_user')->find($groupId);
                if ($group->group_user->count() >= $group->size) {
                    return $this->statusNG(['msg' => 'Lớp ' . $group->name . ' đã đầy, không thể thêm mới']);
                }
            }
        }

        // áp mã giảm giá
        $hashids = new Hashids('', 12);
        $newInvoice = new Invoice();
        $newInvoice->uuid = $hashids->encode($newInvoice->id + strtotime(now()));
        $newInvoice->user_id = $user->id ?? null;

        // product type data
        if ($formData['isVip']) {
            $newInvoice->product_type = $formData['isOnline'] ? 'vip_combo' : 'offline';
        } else {
            $newInvoice->product_type = 'combo';
        }
        // dữ liệu product id, name
        if (isset($formData['comboId'])) {
            $newInvoice->product_id = $formData['comboId']['id'];
            $productName = $formData['comboId']['name'] ?? '';

            if (isset($formData['bookIds'])) {
                foreach ($formData['bookIds'] as $bookName) {
                    $productName = trim($productName . ' + ' . $bookName['name'], ' + ');
                }
            }
            $newInvoice->product_name = $productName;
        } else {
            $newInvoice->product_name = $bookName;
        }
        // Thông tin người mua hàng
        $info = [
            'name' => $formData['fullname'] ?? '',
            'phone' => $formData['phone'] ?? '',
            'email' => $email ?? '',
            'facebook' => $formData['facebook'] ?? '',
            'chat' => $formData['chat'] ?? '',
            'address' => $formData['address'] ?? '',
        ];
        // Info_contact
        $newInvoice->info_contact = $info;
        if (isset($formData['bookIds']) && $formData['bookIds']) {
            // Khởi tạo thông tin cho đơn hàng sách
            $bookInfomation = [
                'postCode' => $formData['bookData']['postCode'] ?? '',
                'address' => $formData['bookData']['address'] ?? '',
                'price' => $formData['bookData']['price'] ?? '',
                'payer' => $formData['bookData']['payer'] ?? '',
                'totalPrice' => $formData['bookData']['totalPrice'] ?? '',
                'phone' => $formData['phone'] ?? '',
                'status' => $formData['bookData']['status'] ?? '',
                'location' => $formData['bookData']['location'] ?? '',
                'refundFault' => $formData['bookData']['refundFault'] ?? '',
                'refundShip' => $formData['bookData']['refundShip'] ?? '',
                'refundDate' => '',
                'modifyStatus' => $formData['bookData']['modifyStatus'] ?? '',
                'exportStatus' => 'none',
                'exportDate' => '',
            ];
            // Delivery infomation
            $newInvoice->book_info = $bookInfomation;
        }
        if ($formData['isVip']) {
            // nếu là đơn hàng vip status = new
            $newInvoice->invoice_status = 'new';
        } else {
            // kiểm tra đơn hàng sách hay combo
            if (!$formData['comboId'] && isset($formData['bookIds'])) {
                // nếu là đơn hàng sách trạng thái = hoàn thành
                $newInvoice->invoice_status = "completed";
            } else {
                $newInvoice->invoice_status = "new";
            }
        }
        if (isset($formData['paidAt']) && !is_null($formData['paidAt'])) {
            $newInvoice->payment_status = "paid";
        } else {
            $newInvoice->payment_status = "unpaid";
        }
        if (isset($formData['isOnline']) && !$formData['isOnline']) {
            $newInvoice->department_id = $formData['department'];
        }
        $newInvoice->price = (int)$formData['price'];
        $newInvoice->currency = $formData['currency'];
        $newInvoice->coupon_id = $formData['coupon'] ?? null;
        $newInvoice->payment_method_id = $formData['paymentMethod'] ?? null;
        $newInvoice->paid_at = $formData['paidAt'] ?? null;
        $newInvoice->note = $formData['note'] ?? null;
        $newInvoice->admin_create = Auth::guard('admin')->user()->id;
        $newInvoice->admin_active = Auth::guard('admin')->user()->id;
        $newInvoice->admin_active_name = Auth::guard('admin')->user()->name;
        $newInvoice->discount_money = $formData['discountMoney'] ?? 0;
        $newInvoice->paid_money = $formData['paidMoney'] ?? 0;
        $newInvoice->gift = $formData['gift'] ?? null;
        if (isset($formData['paidFor'])) {
            $newInvoice->paid_for_id = $formData['paidFor'];
        }
        $newInvoice->save();

        // kiểm tra nếu đơn hàng có sách lưu sách vào invoice_book
        if (isset($formData['bookIds']) && $formData['bookIds']) {
            foreach ($formData['bookIds'] as $book) {
                $invoiceBook = new InvoiceBook();
                $invoiceBook->book_id = $book["id"];
                $invoiceBook->invoice_id = $newInvoice->id;
                $invoiceBook->location = $formData['bookData']['location'];
                $invoiceBook->quantity = $book["quantity"];
                $invoiceBook->type = $newInvoice->price == 0 ? 'invalue' : 'out';
                $invoiceBook->refund = 0;
                $invoiceBook->created_at = now();
                $invoiceBook->updated_at = now();
                $invoiceBook->save();
            }
        }
        // init log
        $newLog = $this->generateLog($newInvoice->id, 'invoice_creation', 'invoice.id', null, $newInvoice->id);
        // assign log to model
        $newInvoice->logs = [$newLog];
        $newInvoice->save();
        if (isset($formData['rewardId'])) {
            $reward = RewardAudit::find($formData['rewardId']);
            $reward->invoice_id = $newInvoice->id;
            $reward->claim_at = now();
            $reward->save();
        }

        if (isset($formData['groupId']) && count($formData['groupId'])) {
            foreach ($formData['groupId'] as $groupId) {
                if ($formData['isOnline']) {
                    $group = CommunityGroup::with('group_user')->find($groupId);
                } else {
                    $group = SchoolCourse::find($groupId);
                }

                // check if user have checkpoint log
                $checkpointLog = null;
                if (!is_null($user) && $user->email) {
                    $checkpointLog = CheckpointLog::where('user_info->email', [$user->email])->whereHas('lesson.getCourse', function ($q) use ($group) {
                        $q->where('name', $group->vip_level);
                    })->first();
                }

                if (!is_null($group)) {
                    if ($formData['isOnline']) {
                        $userGroupExist = CommunityGroupUser::query()->where('user_id', $user->id)->where('group_id', $group->id)->exists();
                    } else {
                        $userGroupExist = CourseStudent::query()->where('student_id', $offlineUser->id)->where('course_id', $group->id)->exists();
                    }
                    if (!$userGroupExist) {
                        if ($formData['isOnline']) {
                            $group->users()->attach($user->id,
                                [
                                    'entry_point' => is_null($checkpointLog) ? 0 : $checkpointLog->grade,
                                    'invoice_id' => $newInvoice->id,
                                    'created_at' => Carbon::now(),
                                    'updated_at' => Carbon::now()
                                ]
                            );
                        } else {
                            $group->students()->attach($offlineUser->id,
                                [
                                    'created' => Carbon::now(),
                                    'updated' => Carbon::now()
                                ]
                            );
                        }
                        if ($formData['isOnline']) {
                            $this->communityGroupService->addGroupChatMember($user->id, $group->id);
                        }
                        $newInvoice->payment_status = "paid";
                        if (!in_array($newInvoice->product_type, ['combo', 'course'])) {
                            $newInvoice->invoice_status = 'completed';
                        }
                        // modify log
                        $tmp = $newInvoice->logs ?? [];
                        // assign log to model
                        $tmp[] = $this->generateLog($user->id, 'user_attachment', $formData['isOnline'] ? 'community_group_user.group_id' : 'course_students_tbl.course_id', null, $group->id);
                        $newInvoice->logs = $tmp;
                        $newInvoice->save();

                        // pusher
                        if ($formData['isOnline']) {
                            $groupTmp = $group->logs ?? [];
                            $groupTmp[] = $this->generateLog($user->id, 'user_attachment', $formData['isOnline'] ? 'community_group_user.group_id' : 'course_students_tbl.course_id', null, $user->email);
                            $group->logs = $groupTmp;
                            $group->save();

                            $group->load('group_user');

                            $options = array('cluster' => 'ap3', 'useTLS' => true);
                            $pusher = new Pusher\Pusher(
                                '7d39d4954f600d3bb86c',
                                '1c5d76572b3452594008',
                                '996684',
                                $options
                            );

                            $pusher->trigger('invoice', 'reservation', [
                                'id' => $group->id,
                                'count' => $group->group_user->count()
                            ]);
                        }
                    }
                }
            }
        }
        if (isset($formData['activeOnline']) && $formData['activeOnline']) {
            $this->activeOnlineCourseForUser($user->id, $newInvoice->id);
        }

        // tạo invoice bonus sau khi lưu invoice
//        if ($formData['bonus']['courseId']) {
//            $this->createInvoiceBonus($newInvoice, $formData['bonus']);
//        }
        if (isset($formData['bonuses']) && $formData['bonuses'] && count($formData['bonuses'])) {
            foreach ($formData['bonuses'] as $bonus) {
                if ($bonus['courseId']) {
                    $this->createInvoiceBonus($newInvoice, $bonus);
                }
            }
        }
        return $this->statusOK($newInvoice->load('vip_combo.courses', 'user.groups', 'sale', 'payment_method',
            'user.conversation', 'product', 'book', 'department', 'combo', 'books', 'bonus.course', 'relevant_invoices'));
    }

    /**
     * @param $invoice
     * @param $bonus
     * @return void
     */
    private function createInvoiceBonus($invoice, $bonus)
    {
        InvoiceBonus::create([
            'invoice_id' => $invoice->id,
            'course_id' => $bonus['courseId'],
            'days' => $bonus['days'],
            'status' => 1
        ]);
    }

    /**
     * @param $invoice
     * @param $bonus
     * @return void
     */
    private function updateInvoiceBonus($invoice, $bonus)
    {
        $existedBonus = InvoiceBonus::where('invoice_id', $invoice->id)->where('course_id', $bonus['courseId'])->first();

        if ($existedBonus) {
            $existedBonus->course_id = $bonus['courseId'];
            $existedBonus->days = $bonus['days'];
            $existedBonus->save();
        } else {
            InvoiceBonus::create([
                'invoice_id' => $invoice->id,
                'course_id' => $bonus['courseId'],
                'days' => $bonus['days'],
                'status' => 1
            ]);
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function activeBonus($id)
    {
        $invoice = Invoice::find($id);
        $bonuses = InvoiceBonus::where('invoice_id', $id)->with('course:id,name')->get();
        foreach ($bonuses as $bonus) {
            if (is_null($bonus) || !$bonus->status) continue;

            $owner = CourseOwner::where('course_id', $bonus->course_id)->where('owner_id', $invoice->user_id)->first();
            $course = Course::find($bonus->course_id);
            if (is_null($owner)) {
                $owner = new CourseOwner();
                $owner->owner_id = $invoice->user_id;
                $owner->title = $course->name;
                $owner->course_id = $course->id;
                $owner->watch_expired_day = Carbon::now()->copy()->addDays($bonus->days);
                $owner->admin_active = auth()->guard('admin')->user()->id;
                $owner->admin_update = auth()->guard('admin')->user()->id;
                $owner->save();
            } else {
                $owner->watch_expired_day = Carbon::parse($owner->watch_expired_day)->isPast()
                    ? Carbon::now()->copy()->addDays($bonus->days)
                    : Carbon::parse($owner->watch_expired_day)->addDays($bonus->days);
                $owner->admin_update = auth()->guard('admin')->user()->id;
                $owner->save();
            }
            $bonus->status = 0;
            $bonus->save();

            $tmp = $invoice->logs;
            $tmp[] = $this->generateLog($invoice->id, 'active_bonus', 'course_owner.watch_expired_day', null, $bonus->course->name . ' - ' . $owner->watch_expired_day);
            $invoice->logs = $tmp;
            $invoice->save();
        }

        return $this->statusOK();
    }

    /**
     * @param CreateVipInvoiceRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateVipInvoice(UpdateVipInvoiceRequest $request): \Illuminate\Http\JsonResponse
    {
        $formData = $request->only(
            'id', 'email', 'password', 'fullname', 'phone', 'facebook', 'chat', 'comboId', 'department',
            'coupon', 'paymentMethod', 'paidAt', 'sale', 'note', 'groupId', 'price', 'currency', 'isVip',
            'discountMoney', 'paidMoney', 'gift', 'rewardId', 'bookIds', 'refundBooks', 'bookData', 'bonus', 'bonuses', 'address');

        $invoice = Invoice::find($formData['id']);
        if (
            $invoice->invoice_status == 'canceled'
        ) {
            return $this->statusNG();
        }
        $user = Users::query()->where('email', $formData['email'])->first();
        if (!$formData['password']) {
            if (isset($formData['comboId']) && $formData['comboId']) {
                if (!isset($formData['email']) || !$formData['email']) {
                    throw new HttpResponseException(response()->json([
                        'status' => 'error',
                        'detail' => [
                            'email' => ['Không để trống email']
                        ]
                    ]));
                }

                // tìm user theo email, không có user thì báo lỗi
                if (is_null($user)) {
                    throw new HttpResponseException(response()->json([
                        'status' => 'error',
                        'detail' => [
                            'email' => ['Người dùng không tồn tại']
                        ]
                    ]));
                }
            }
        } else {
            if (!is_null($user)) {
                throw new HttpResponseException(response()->json([
                    'status' => 'error',
                    'detail' => [
                        'email' => ['Người dùng đã tồn tại']
                    ]
                ]));
            }
            $user = new Users();
            $user->email = trim(preg_replace('/\t/', '', $formData['email']));
            $user->password = Hash::make($formData['password']);
            $user->name = $formData['fullname'];
            $user->phone = $formData['phone'];
            $user->activation = '1';
            $user->save();
        }

        if (isset($formData['isVip']) && $formData['isVip'] == '1' && isset($user->id) && $user->id != $invoice->user_id && $invoice->invoice_status == 'completed') {
            $groupUsers = CommunityGroupUser::query()->where('invoice_id', $invoice->id)->get();
            foreach ($groupUsers as $groupUser) {
                $oldUserId = $groupUser->user_id;
                $groupUser->user_id = $user->id;
                $groupUser->save();
                $conversation = Conversation::query()->where('group_id', $groupUser->group_id)->pluck('id')->toArray();
                ConversationUser::query()->whereIn('conversation_id', $conversation)->where('user_id', $oldUserId)->update(['user_id' => $user->id]);
            }
            $tmp = $invoice->logs ?? [];
            $tmp[] = $this->generateLog($invoice->id, 'user_id_reservation', 'community_group_user.user_id', $invoice->user_id, $user->id);
            $invoice->logs = $tmp;
        }

        if (isset($formData['isOnline']) && $formData['isOnline']) {
            $invoice->payment_status = $formData['payment_status'];
        }

        $invoice->user_id = $user->id ?? null;
        $info = [
            'name' => $formData['fullname'] ?? '',
            'phone' => $formData['phone'] ?? '',
            'email' => $formData['email'] ? trim(preg_replace('/\t/', '', $formData['email'])) : '',
            'facebook' => $formData['facebook'] ?? '',
            'chat' => $formData['chat'] ?? '',
            'address' => $formData['address'] ?? ''
        ];
        if (count(array_diff($info, $invoice->info_contact))) {
            $tmp = $invoice->logs ?? [];
            foreach (array_diff($info, $invoice->info_contact) as $field => $value) {
                $tmp[] = $this->generateLog($invoice->id, 'information_changed', 'invoice.info_contact.' . $field, $invoice->info_contact[$field] ?? '', $value);
            }
            $invoice->logs = $tmp;
        }
        // info_contact
        $invoice->info_contact = $info;

        $newInvoiceData = [
            'price' => $formData['price'] ?? '',
            'currency' => $formData['currency'] ?? '',
            'discount_money' => $formData['discountMoney'] ?? '',
            'payment_method_id' => $formData['paymentMethod'] ?? '',
            'department_id' => $formData['department'] ?? '',
            'paid_money' => $formData['paidMoney'] ?? '',
            'paid_at' => $formData['paidAt'] ?? '',
            'note' => $formData['note'] ?? ''
        ];
        $oldInvoiceData = [
            'price' => $invoice['price'] ?? '',
            'currency' => $invoice['currency'] ?? '',
            'discount_money' => $invoice['discount_money'] ?? '',
            'payment_method_id' => $invoice['payment_method_id'] ?? '',
            'department_id' => $invoice['department_id'] ?? '',
            'paid_money' => $invoice['paid_money'] ?? '',
            'paid_at' => $invoice['paid_at'] ?? '',
            'note' => $invoice['note'] ?? ''
        ];
        if (count(array_diff($newInvoiceData, $oldInvoiceData))) {
            $tmp = $invoice->logs ?? [];
            foreach (array_diff($newInvoiceData, $oldInvoiceData) as $field => $value) {
                $tmp[] = $this->generateLog($invoice->id, 'information_changed', 'invoice.' . $field, $invoice[$field] ?? '', $value);
            }
            $invoice->logs = $tmp;
        }

        if ($request->editLogs) {
            $tmp = $invoice->logs ?? [];
            foreach ($request->editLogs as $log) {
                $log['timestamp'] = now();
                $log['created_by'] = Auth::guard('admin')->user()->id . ' ' . Auth::guard('admin')->user()->name;
                $tmp[] = $log;
            }
            $invoice->logs = $tmp;
        }

        if (isset($formData['bookIds']) && $formData['bookIds']) {
            $bookInfomation = [
                'postCode' => $formData['bookData']['postCode'] ?? '',
                'address' => $formData['bookData']['address'] ?? '',
                'price' => $formData['bookData']['price'] ?? '',
                'payer' => $formData['bookData']['payer'] ?? '',
                'totalPrice' => $formData['bookData']['totalPrice'] ?? '',
                'phone' => $formData['phone'] ?? '',
                'status' => $formData['bookData']['status'] ?? '',
                'location' => $formData['bookData']['location'] ?? '',
                'refundFault' => $formData['bookData']['refundFault'] ?? '',
                'refundShip' => $formData['bookData']['refundShip'] ?? '',
                'modifyStatus' => $formData['bookData']['modifyStatus'] ?? '',
                'exportStatus' => $formData['bookData']['exportStatus'] ?? '',
                'exportDate' => $formData['bookData']['exportDate'] ?? '',
            ];

            $oldBookInfo = $invoice->book_info ? $invoice->book_info : [];

            if (count(array_diff($bookInfomation, $oldBookInfo))) {
                $tmp = $invoice->logs ?? [];
                foreach (array_diff($bookInfomation, $oldBookInfo) as $field => $value) {
                    if ($value || ($oldBookInfo[$field] ?? '')) {
                        $tmp[] = $this->generateLog($invoice->id, 'information_changed', 'invoice.book_info.' . $field, $oldBookInfo[$field] ?? '', $value);
                    }
                }
                $invoice->logs = $tmp;
            }

            if (isset($formData['refundBooks']) && $formData['bookData']['status'] == 'return') {
                $bookInfomation['refundDate'] = Carbon::now()->format('Y-m-d H:i:s');
            }

            //Book delivery infomation
            $invoice->book_info = $bookInfomation;
            $bookLocation = $formData['bookData']['location'];
        }

        // Xủ lý tên đơn hàng
        if (is_null($invoice->active_time)) {
            if (isset($formData['comboId'])) {
                $invoice->product_id = $formData['comboId']['id'] ?? null;
                $productName = $formData['comboId']['name'] ?? '';

                if (isset($formData['bookIds'])) {
                    foreach ($formData['bookIds'] as $bookName) {
                        $productName = trim($productName . ' + ' . $bookName['name'], ' + ');
                    }
                }
                $invoice->product_name = $productName;
            } else {
                $productName = '';
                if (isset($formData['bookIds'])) {
                    foreach ($formData['bookIds'] as $bookName) {
                        $productName = trim($productName . ' + ' . $bookName['name'], ' + ');
                    }
                }
                $invoice->product_name = $productName;
            }
        }

        $invoice->coupon_id = $formData['coupon'] ?? null;
        $invoice->payment_method_id = $formData['paymentMethod'] ?? null;

        // Chuyển thành đã thanh toán khi điền ngày gửi tiền
        $invoice->paid_at = $formData['paidAt'] ?? null;
        if (isset($formData['paidAt']) && !is_null($formData['paidAt'])) {
            $invoice->payment_status = "paid";
        } else {
            $invoice->payment_status = "unpaid";
        }

        $invoice->note = $formData['note'] ?? null;
        $invoice->department_id = $formData['department'] ?? null;
        $invoice->price = $formData['price'] ?? null;
        $invoice->currency = $formData['currency'] ?? null;
        $invoice->updated_at = now();
        $invoice->discount_money = $formData['discountMoney'] ?? 0;
        $invoice->paid_money = $formData['paidMoney'] ?? 0;
        $invoice->gift = $formData['gift'] ?? null;
        $invoice->save();

        // Xử lý sách trong đơn hàng
        $this->syncBook($formData['bookIds'] ?? [], 'book', $invoice->id, $bookLocation ?? '');
        // Xử lý sách hoàn trong đơn hàng
        $this->syncBook($formData['refundBooks'] ?? [], '', $invoice->id, $bookLocation ?? '');
        // nếu giá đơn hàng 0đ không trừ sách trong kho
        if (isset($formData['bookIds']) && !$invoice->price) {
            DB::table("invoice_book")->where('invoice_id', $invoice->id)->where('type', 'out')->update(['type' => 'invalue']);
        }

        if (isset($formData['rewardId'])) {
            $reward = RewardAudit::find($formData['rewardId']);
            $reward->invoice_id = $invoice->id;
            $reward->claim_at = now();
            $reward->save();
        }
        // tạo invoice bonus sau khi lưu invoice
//        if ($formData['bonus']['courseId']) {
//            $this->updateInvoiceBonus($invoice, $formData['bonus']);
//        }

        if ($formData['bonuses'] && count($formData['bonuses'])) {
            $allBonus = InvoiceBonus::where('invoice_id', $invoice->id)->get();
            foreach ($allBonus as $bonus) {
                if (!in_array($bonus->course_id, array_column($formData['bonuses'], 'courseId')) && $bonus->status == 1) {
                    $bonus->status = 2;
                    $bonus->save();
                } else if ($bonus->status === 2) {
                    $bonus->status = 1;
                    $bonus->save();
                }
            }
            foreach ($formData['bonuses'] as $bonus) {
                if ($bonus['courseId']) {
                    $this->updateInvoiceBonus($invoice, $bonus);
                }
            }
        }
        return $this->statusOK($invoice->load('vip_combo.courses', 'user.groups', 'sale', 'payment_method', 'user.conversation', 'product', 'book', 'books', 'bonus', 'bonuses', 'relevant_invoices'));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * using on invoice create modal
     */
    public function getGroupByCombo(Request $request): \Illuminate\Http\JsonResponse
    {
        $departmentId = $request->get('departmentId');
        $isOnline = $request->get('isOnline');
        $isVip = $request->get('isVip');
        $comboId = $request->comboId;
        $combo = [];

        if ($isVip) {
            $combo = VipCombo::query()->with('courses')->find($comboId);
        } else {
            $combo = Combo::query()->find($comboId);
        }

        $vipLevels = [];
        if ($isVip) {
            $vipLevels = $combo->courses->pluck('level');
        } else {
            $courseIds = json_decode($combo->services)->courses;
            $vipLevels = array_map(function ($courseId) {
                return implode(ONLINE_COURSES_FOR_GROUP[$courseId]);
            }, $courseIds);
        }
        
        $search = $request->search;

        if ($isOnline) {
            
            $query = CommunityGroup::query()
                ->select('id', 'type', 'name', 'shift_time', 'shift_type', 'shift_time', 'size', 'start_date', 'vip_level', 'group_chat_id')->whereIn('status', [1, 2]);
            
            if ($isVip) {
                $query = CommunityGroup::query()->where('type', '<>', 'basic');
            } else {
                $query = CommunityGroup::query()->where('type', 'basic');
            }
            
            if ($combo && $combo->type == 5) {
                $query = $query->where('type', 'offline');
            }
            if (count($vipLevels)) {
                $query = $query->whereIn('vip_level', $vipLevels);
            }
            if ($search) {
                $query = $query->where('name', 'LIKE', '%' . $search . '%');
            }
            $group = $query->with([
                'group_user' => function ($q) {
                    $q->select('id', 'group_id', 'user_id', 'entry_point');
                },
                'group_teacher' => function ($q) {
                    $q->select('id', 'teacher_id', 'group_id')->with(['teacher' => function ($q) {
                        $q->select('id', 'first_name', 'last_name');
                    }]);
                }])->orderBy('id', 'desc')->take(50)->get();
        } else {
            $query = SchoolCourse::query()
                ->with('teachers')
                ->with('students')
                ->with('info.room', 'info.periods')
                ->where('building', $departmentId)
                ->where('is_stop', 0)
                ->where('deleted', 0)
                ->where('date_end', '>', now());
            if (count($vipLevels)) {
                $query = $query->whereIn('level_of_n', array_map(function ($l) {
                    return OFFLINE_LEVEL[$l];
                }, $vipLevels->toArray()));
            }
            if ($search) {
                $query = $query->whereHas('teachers', function ($q) use ($search) {
                    $q->where('first_name', 'LIKE', '%' . $search . '%')->orWhere('last_name', 'LIKE', '%' . $search . '%');
                });
            }
            $group = $query->orderBy('id', 'desc')->get();
        }

        return $this->statusOK($group);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * using on group choice modal
     */
    public function getUserGroupByCourse(Request $request)
    {
        $userId = $request->userId;
        $invoiceId = $request->invoiceId;
        $level = $request->courseName;
        $search = $request->search;
        $invoice = Invoice::query()->find($invoiceId);
        $isOffline = $invoice->product_type === 'offline';

        if (!$isOffline) {
            $query = CommunityGroup::query()
                ->select('id', 'type', 'name', 'shift_time', 'shift_type', 'shift_time', 'size', 'start_date', 'vip_level', 'group_chat_id')
                ->where('start_time', '>=', now()->addMonths(-2))
                ->whereIn('status', [1, 2]);
            if ($invoice->product_type == 'vip_combo') {
                $query = CommunityGroup::query()->where('type', '<>', 'basic');
            } else {
                $query = CommunityGroup::query()->where('type', 'basic');
            }

            
            if ($level) {
                $query = $query->where('vip_level', $level);
            }

            if ($search) {
                $query = $query->where('name', 'LIKE', '%' . $search . '%');
            }
            $group = $query->with([
                'group_user' => function ($q) {
                    $q->select('id', 'group_id', 'user_id', 'entry_point', 'invoice_id');
                },
                'group_teacher' => function ($q) {
                    $q->select('id', 'teacher_id', 'group_id')->with(['teacher' => function ($q) {
                        $q->select('id', 'first_name', 'last_name');
                    }]);
                }])->whereDoesntHave('group_user', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->orderBy('id', 'desc')->get();
        } else {
            $user = SchoolUser::query()->where('dmr_id', $userId)->first();
            $query = SchoolCourse::query()
                ->with('teachers')
                ->with('students')
                ->with(['info' => function ($q) {
                    $q->with('room')->with('periods')->where('deleted', 0);
                }])
                ->where('building', $user->department_id)
                ->where('is_stop', 0)
                ->where('deleted', 0)
                ->where('date_end', '>', now());

            $query = $query->where('level_of_n', OFFLINE_LEVEL[$level]);

            if ($search) {
                $query = $query->whereHas('teachers', function ($q) use ($search) {
                    $q->where('first_name', 'LIKE', '%' . $search . '%')->orWhere('last_name', 'LIKE', '%' . $search . '%');
                });
            }
            $group = $query->whereDoesntHave('students', function ($q) use ($user) {
                $q->where('student_id', $user->id);
            })->orderBy('id', 'desc')->get();
        }

        return $this->statusOK($group);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJoinedGroupByCourse(Request $request): \Illuminate\Http\JsonResponse
    {
        $userId = $request->userId;
        $level = $request->courseName;
        $isOffline = $request->isOffline;
        $type = $request->type;
        if (!$isOffline) {
            $query = CommunityGroup::query()->where('type', '<>', 'basic');
            if ($type == 'vip_combo') {
                $query = CommunityGroup::query()->where('type', '<>', 'basic');

            } else {
                $query = CommunityGroup::query()->where('type', 'basic');
            }
            if ($level) {
                $query = $query->where('vip_level', $level);
            }

            $group = $query->with('group_user')->whereHas('group_user', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->get();
        } else {
            $user = SchoolUser::query()->where('dmr_id', $userId)->first();
            $query = SchoolCourse::query();
            if ($level) {
                $query = $query->where('level_of_n', OFFLINE_LEVEL[$level]);
            }
            $group = $query
                ->with('students')
                ->with('teachers')
                ->with(['info' => function ($q) {
                    $q->with('room')->with('periods')->where('deleted', 0);
                }])
                ->whereHas('students', function ($q) use ($user) {
                    $q->where('student_id', $user->id);
                })->get();
        }
        return $this->statusOK($group);
    }

    public function detachUser(Request $request)
    {
        $invoice = Invoice::find($request->invoiceId);
        $isOffline = $invoice->product_type == 'offline';

        if ($isOffline) {
            $user = SchoolUser::query()->where('dmr_id', $request->userId)->first();
            $group = SchoolCourse::with('students')->with('teachers')->with('info.room', 'info.periods')->find($request->groupId);
            $group->students()->detach($user->id);
            $group = SchoolCourse::with('students')->with('teachers')->with('info.room', 'info.periods')->find($request->groupId);

        } else {
            $group = CommunityGroup::with('group_user')->find($request->groupId);
            $group->users()->detach($request->userId);
            $this->communityGroupService->removeGroupChatMember($request->userId, $request->groupId);
            $group = CommunityGroup::with('group_user')->find($request->groupId);
            $user = Users::find($request->userId);
        }

        if ($invoice) {
            // modify log
            $tmp = $invoice->logs ?? [];
            $tmp[] = $this->generateLog($request->userId, 'user_detachment', 'community_group_user.group_id', $group->id, null);
            $invoice->logs = $tmp;

            if ($invoice->product_type == 'vip_combo') {
                $invoice->invoice_status = 'new';
            }

            $invoice->save();

            if (!$isOffline) {
                $groupTmp = $group->logs ?? [];
                $groupTmp[] = $this->generateLog($request->userId, 'user_detachment', 'community_group_user.group_id', $user->email, null);
                $group->logs = $groupTmp;
                $group->save();
            }
        }
        if (!$isOffline) {
            // pusher
            $options = array('cluster' => 'ap3', 'useTLS' => true);
            $pusher = new Pusher\Pusher(
                '7d39d4954f600d3bb86c',
                '1c5d76572b3452594008',
                '996684',
                $options
            );

            $pusher->trigger('invoice', 'reservation', [
                'id' => $group->id,
                'count' => $group->group_user->count()
            ]);
        }

        return $this->statusOK($group);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function attachUser(Request $request): \Illuminate\Http\JsonResponse
    {
        $invoice = Invoice::find($request->invoiceId);
        $isOffline = $invoice->product_type == 'offline';

        $group = $isOffline ? SchoolCourse::with('students')->with('teachers')->with('info.room', 'info.periods')->find($request->groupId) : CommunityGroup::with('group_user')->find($request->groupId);
        $user = $isOffline ? SchoolUser::query()->where('dmr_id', $request->userId)->first() : Users::find($request->userId);

        if (!$isOffline) {
            if ($group->group_user->count() >= $group->size) {
                return $this->statusNG(['msg' => 'Lớp đã đầy, không thể thêm mới']);
            }
        }
        if (isset($request->activeOnline) && $request->activeOnline) {
            $this->activeOnlineCourseForUser($isOffline ? $user->dmr_id : $user->id, $request->invoiceId);
            $invoice = Invoice::find($request->invoiceId);
        }
        $userGroupExist = !$isOffline
            ? CommunityGroupUser::query()->where('user_id', $request->userId)->where('group_id', $group->id)->exists()
            : CourseStudent::query()->where('student_id', $user->id)->where('course_id', $group->id)->exists();

        // check if user have checkpoint log
        $checkpointLog = null;
        if (!is_null($user) && $user->email) {
            $checkpointLog = CheckpointLog::where('user_info->email', [$user->email])->whereHas('lesson.getCourse', function ($q) use ($group) {
                $q->where('name', $group->vip_level);
            })->first();
        }
        if (!$userGroupExist) {
            if ($isOffline) {
                $group->students()->attach($user->id, [
                    'cost' => $group->cost,
                    'status' => 0,
                    'deleted' => 0,
                    'created' => now(),
                    'updated' => now(),
                ]);
            } else {
                $group->users()->attach($request->userId, [
                    'invoice_id' => $request->invoiceId,
                    'entry_point' => is_null($checkpointLog) ? 0 : $checkpointLog->grade
                ]);
                $this->communityGroupService->addGroupChatMember($request->userId, $group->id);
            }

            // modify log
            $tmp = $invoice->logs ?? [];
            $tmp[] = $this->generateLog($user->id, 'user_attachment', 'community_group_user.group_id', null, $group->id);
            $invoice->logs = $tmp;

            if (!$isOffline) {
                $groupTmp = $group->logs ?? [];
                $groupTmp[] = $this->generateLog($user->id, 'user_attachment', 'community_group_user.group_id', null, $user->email);
                $group->logs = $groupTmp;
                $group->save();
            }
        }
        $invoice->payment_status = "paid";

        if (!in_array($invoice->product_type, ['combo', 'course'])) {
            $invoice->invoice_status = 'completed';
        }

        $invoice->save();

        if ($isOffline) {
            $group = SchoolCourse::with('students')->with('teachers')->with('info.room', 'info.periods')->find($request->groupId);
        } else {
            $group = CommunityGroup::with('group_user')->find($request->groupId);
            // pusher
            $options = array('cluster' => 'ap3', 'useTLS' => true);
            $pusher = new Pusher\Pusher(
                '7d39d4954f600d3bb86c',
                '1c5d76572b3452594008',
                '996684',
                $options
            );

            $pusher->trigger('invoice', 'reservation', [
                'id' => $group->id,
                'count' => $group->group_user->count()
            ]);
        }


        return $this->statusOK($group);
    }

    public function setEntryPoint(Request $request)
    {
        $group = CommunityGroupUser::query()->where('group_id', $request->groupId)->where('user_id', $request->userId);
        $group->update(['entry_point' => $request->point]);
        return $this->statusOK();
    }

    public function updateUserInformation(Request $request, $id)
    {
        $user = Users::find($id);
        $user[$request->field] = $request->value;
        $user->save();
        return $this->statusOK();
    }

    public function getUserAvailableRewards(Request $request)
    {
        $user = User::firstWhere('email', $request->email);
        return $this->statusOK($user->availableDiscountRewards ?? []);
    }

    /** Hàm xử lý sách cho đơn hàng có sách */
    public function syncBook($books, $type, $invoice, $location)
    {
        $ids = [];
        $refund = $type == 'book' ? false : true;
        foreach ($books as $book) {
            $ids[] = $book['id'];
            $bookModel = InvoiceBook::query()
                ->where('invoice_id', $invoice)
                ->where('book_id', $book['id'])
                ->where('refund', $refund)
                ->first();
            if ($bookModel) {
                $bookModel->quantity = (int)$book['quantity'];
                $bookModel->location = $location ?? 'vn';
                $bookModel->type = 'out';
                $bookModel->refund = $refund;
                $bookModel->updated_at = now();
                $bookModel->save();
            } else {
                $newBookInvoice = new InvoiceBook();
                $newBookInvoice->book_id = $book['id'];
                $newBookInvoice->invoice_id = $invoice;
                $newBookInvoice->quantity = (int)$book['quantity'];
                $newBookInvoice->location = $location ?? 'vn';
                $newBookInvoice->refund = $refund;
                $newBookInvoice->created_at = now();
                $newBookInvoice->updated_at = now();
                $newBookInvoice->save();
            }
        }
        InvoiceBook::query()
            ->where('invoice_id', $invoice)
            ->where('refund', $refund)
            ->whereNotIn('book_id', $ids)
            ->delete();

        return $this->statusOK();
    }
}
