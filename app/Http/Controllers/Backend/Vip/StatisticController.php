<?php

namespace App\Http\Controllers\Backend\Vip;

use App\Http\Controllers\Controller;
use App\Http\Models\Admin;
use App\Http\Models\Combo;
use App\Http\Models\Community\CommunityGroup;
use App\Http\Models\Community\CommunityGroupUser;
use App\Http\Models\Course;
use App\Http\Models\CourseOwner;
use App\Http\Models\ExamResult;
use App\Http\Models\Invoice;
use App\Http\Models\Lesson;
use App\Http\Models\LessonResult;
use App\Http\Models\LessonToTask;
use App\Http\Models\LessonTrackingStat;
use App\Http\Models\School\CourseTimeStudent;
use App\Http\Models\School\SchoolUser;
use App\Http\Models\Users;
use App\Http\ModelsFrontend\LessonProgress;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Http\Models\School\CourseTime;
use Illuminate\Support\Collection;
use Illuminate\Http\Request;
use App\Http\Models\Vip\VipCombo;
use App\Http\Models\Vip\VipComboCourse;
use App\Http\Models\Vip\VipCourse;
use App\Http\Models\School\SchoolCourse;

class StatisticController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    public function statistic()
    {
        $classes = CommunityGroup::select('id', 'name', 'type', 'vip_level')->get();
        $year = Carbon::now()->format('Y');
        $register = $this->registerUser($year, 'all', 'all', 'all');
        return view('backend.vip.statistic', compact('register', 'classes'));
    }

    public function registerUser($year, $level, $type, $class)
    {
        $testerIds = DB::table('users')
            ->where('is_tester', 1)
            ->pluck('id')
            ->toArray();

        $data = DB::table('community_group_users AS gu')
            ->join('community_groups AS g', 'g.id', 'gu.group_id')
            ->groupBy(DB::raw('MONTH(gu.created_at)'))
            ->selectRaw('COUNT(user_id) as total, MONTH(gu.created_at) as month')
            ->whereYear('gu.created_at', $year)
            ->whereNotIn('g.vip_level', ['online', 'b2b'])
            ->whereNotIn('gu.user_id', $testerIds);
        if ($level !== 'all') {
            $data = $data->where('g.vip_level', $level);
        }
        if ($type !== 'all') {
            $data = $data->where('g.type', $type);
        }
        if ($class !== 'all') {
            $data = $data->where('gu.group_id', $class);
        }

        $data = $data->get();
        $data = $this->mapChartData($data);

        $count = DB::table('community_group_users AS gu')
            ->join('community_groups AS g', 'g.id', 'gu.group_id')
            ->whereYear('gu.created_at', $year)
            ->whereNotIn('g.vip_level', ['online', 'b2b'])
            ->whereNotIn('gu.user_id', $testerIds);
        if ($level !== 'all') {
            $count = $count->where('g.vip_level', $level);
        }
        if ($type !== 'all') {
            $count = $count->where('g.type', $type);
        }
        if ($class !== 'all') {
            $count = $count->where('gu.group_id', $class);
        }
        $count = $count->count();

        $result = new \stdClass();
        $result->count = $count;
        $result->data = $data;

        return json_encode($result);
    }

    function mapChartData($data)
    {
        $output = [];
        $dataMap = [];
        $monthGr = [1 => 1, 2 => 2, 3 => 3, 4 => 4, 5 => 5, 6 => 6, 7 => 7, 8 => 8, 9 => 9, 10 => 10, 11 => 11, 12 => 12];

        foreach ($data as $item) {
            $dataMap[$item->month] = $item->total;
        }

        $i = 0;
        foreach ($monthGr as $key => $item) {
            if (isset($dataMap[$key])) {
                $output[$i]['label'] = 'Tháng ' . $key;
                $output[$i]['y'] = $dataMap[$key];
            } else {
                $output[$i]['label'] = 'Tháng ' . $key;
                $output[$i]['y'] = 0;
            }
            $i++;
        }
        return $output;
    }

    public function getStudyStatisticPage()
    {
        return view('backend.vip.learnStatistic');
    }

    public function studyAndCountMember($startMonth, $endMonth, $older)
    {
        // Đếm số buổi học
        $courseTimes = CourseTime::select('group_id', DB::raw('COUNT(group_id) AS learnCount'))
            ->where('group_id', '>', 0)
            ->where('date_attendance', '>=', $startMonth . '-01')
            ->where('date_attendance', '<', $endMonth . '-01')
            ->groupBy('group_id')
            ->orderBy('group_id')
            ->get();
        $groupIds = $courseTimes->pluck('group_id')->toArray();
        $communityGroup = CommunityGroup::select('id', 'name', 'type', 'start_date', 'vip_level', 'size', 'status')
            ->whereIn('id', $groupIds)
            ->orderBy('id')
            ->get();
        $validGroupIds = $communityGroup->pluck('id')->toArray();

        // Tính sĩ số
        $testerIds = DB::table('users')
            ->where('is_tester', 1)
            ->pluck('id')
            ->toArray();
        $studentCount = CommunityGroupUser::select('group_id', DB::raw('COUNT(user_id) AS number_of_student'))
            ->where('created_at', '<', $endMonth . '-01 00:00:00')
            ->whereIn('group_id', $validGroupIds)
            ->whereNotIn('user_id', $testerIds)
            ->groupBy('group_id')
            ->get();
        $countStudentGroupIds = $studentCount->pluck('group_id')->toArray();
        $groups = new Collection();
        foreach ($courseTimes as $courseTime) {
            if (in_array($courseTime->group_id, $validGroupIds)) {
                $index = array_search($courseTime->group_id, $validGroupIds);
                $group = $communityGroup[$index];
                $courseTime->name = $group->name;
                $courseTime->type = $group->type;
                $courseTime->start_date = $group->start_date;
                $courseTime->vip_level = $group->vip_level;
                $courseTime->size = $group->size;
                $courseTime->status = $group->status;

                $index2 = array_search($courseTime->group_id, $countStudentGroupIds);

                if ($index2 > -1) {
                    $group2 = $studentCount[$index2];
                    $courseTime->number_of_student = $group2->number_of_student;
                } else {
                    $courseTime->number_of_student = 0;
                }

                $groups->push($courseTime);
            }
        }

        $result = new \stdClass();
        $result->groups = $groups;

        // Lấy các lớp có thể thiếu do ko có dữ liệu điểm danh
        $otherGroups = CommunityGroupUser::select(
            'group_id',
            'g.name',
            DB::raw('COUNT(user_id) AS number_of_student')
        )
            ->join('community_groups AS g', 'g.id', 'community_group_users.group_id')
            ->where('community_group_users.created_at', '<', $endMonth . '-01 00:00:00')
            ->whereNotIn('group_id', $validGroupIds)
            ->whereNotIn('user_id', $testerIds)
            ->where('g.id', '>', 6) // Bỏ qua các nhóm cộng đồng basic n1 -> n5
            ->whereNull('g.deleted_at')
            ->where('g.expired_at', '>=', $older . '-01 00:00:00')
            ->groupBy('group_id')
            ->get();

        $result->otherGroups = $otherGroups;

        return response()->json($result);
    }

    public function learnUpRatioInvoicePage(Request $request)
    {
        $result = $this->learnUpRatio('all', 'all', 'all', 'all', 'all');
        return view('backend.vip.learnUpRatioInvoice')->with([
            'result' => $result
        ]);
    }

    public function learnUpRatio($vipType, $timeFromBase, $timeToBase, $timeFrom, $timeTo)
    {
        $testerIds = DB::table('users')
            ->where('is_tester', 1)
            ->pluck('id')
            ->toArray();
        if ($timeFromBase && $timeFromBase !== 'all') {
            $timeFromBase = Carbon::createFromFormat('d-m-Y H:i', $timeFromBase);
        }
        if ($timeToBase && $timeToBase !== 'all') {
            $timeToBase = Carbon::createFromFormat('d-m-Y H:i', $timeToBase);
        }
        if ($timeFrom && $timeFrom !== 'all') {
            $timeFrom = Carbon::createFromFormat('d-m-Y H:i', $timeFrom);
        }
        if ($timeTo && $timeTo !== 'all') {
            $timeTo = Carbon::createFromFormat('d-m-Y H:i', $timeTo);
        }

        $n5ComboIds = [43, 47, 51];
        $n4ComboIds = [42, 44, 48, 52, 55];
        $n3ComboIds = [28, 34, 40, 41, 45, 49, 53, 56, 58, 77];
        $n2ComboIds = [29, 31, 33, 35, 37, 38, 39, 46, 50, 54, 57, 59, 60, 76];
        $n1ComboIds = [30, 32, 33, 36, 78, 79, 105, 75];

        $n5FilterComboIds = $this->getFilterComboIds($n5ComboIds, $vipType);
        $n4FilterComboIds = $this->getFilterComboIds($n4ComboIds, $vipType);
        $n3FilterComboIds = $this->getFilterComboIds($n3ComboIds, $vipType);
        $n2FilterComboIds = $this->getFilterComboIds($n2ComboIds, $vipType);
        $n1FilterComboIds = $this->getFilterComboIds($n1ComboIds, $vipType);

        $targetN4ComboIds = $this->targetComboId(7, $vipType);
        $targetN3ComboIds = $this->targetComboId(6, $vipType);
        $targetN2ComboIds = $this->targetComboId(3, $vipType);
        $targetN1ComboIds = $this->targetComboId(5, $vipType);

        $n5UserIds = $this->getVipUserIds($n5FilterComboIds, 'N5', $timeFromBase, $timeToBase, $testerIds);
        $n4UserIds = $this->getVipUserIds($n4FilterComboIds, 'N4', $timeFromBase, $timeToBase, $testerIds);
        $n3UserIds = $this->getVipUserIds($n3FilterComboIds, 'N3', $timeFromBase, $timeToBase, $testerIds);
        $n2UserIds = $this->getVipUserIds($n2FilterComboIds, 'N2', $timeFromBase, $timeToBase, $testerIds);
        $n1UserIds = $this->getVipUserIds($n1FilterComboIds, 'N1', $timeFromBase, $timeToBase, $testerIds);

        $n4VipUpUserIds = $this->getVipUserIds($targetN4ComboIds, 'N4', $timeFrom, $timeTo, $testerIds, $n5UserIds);
        $n3VipUpUserIds = $this->getVipUserIds($targetN3ComboIds, 'N3', $timeFrom, $timeTo, $testerIds, $n4UserIds);
        $n2VipUpUserIds = $this->getVipUserIds($targetN2ComboIds, 'N2', $timeFrom, $timeTo, $testerIds, $n3UserIds);
        $n1VipUpUserIds = $this->getVipUserIds($targetN1ComboIds, 'N1', $timeFrom, $timeTo, $testerIds, $n2UserIds);

        $n4VipUpRatio = count($n5UserIds) === 0 ? null : (count($n4VipUpUserIds) / count($n5UserIds) * 100);
        $n3VipUpRatio = count($n4UserIds) === 0 ? null : (count($n3VipUpUserIds) / count($n4UserIds) * 100);
        $n2VipUpRatio = count($n3UserIds) === 0 ? null : (count($n2VipUpUserIds) / count($n3UserIds) * 100);
        $n1VipUpRatio = count($n2UserIds) === 0 ? null : (count($n1VipUpUserIds) / count($n2UserIds) * 100);

        $result = new \stdClass();
        $result->n5 = count($n5UserIds);
        $result->n4 = count($n4UserIds);
        $result->n3 = count($n3UserIds);
        $result->n2 = count($n2UserIds);
        $result->n1 = count($n1UserIds);
        $result->n4Up = count($n4VipUpUserIds);
        $result->n3Up = count($n3VipUpUserIds);
        $result->n2Up = count($n2VipUpUserIds);
        $result->n1Up = count($n1VipUpUserIds);
        $result->n4UpRatio = $n4VipUpRatio;
        $result->n3UpRatio = $n3VipUpRatio;
        $result->n2UpRatio = $n2VipUpRatio;
        $result->n1UpRatio = $n1VipUpRatio;

        return json_encode($result);
    }

    public function targetComboId($courseId, $type)
    {
        $query = VipCombo::select('vip_combo.*')
            ->join('vip_combo_course', 'vip_combo_course.combo_id', 'vip_combo.id')
            ->where(function ($q) {
                $q->where('vip_combo.name', 'like', 'VIP9%')
                    ->orWhere('vip_combo.name', 'like', 'VIP15%')
                    ->orWhere('vip_combo.name', 'like', 'VIP500%');
            });

        if ($type !== 'all') {
            if ($type === 'vip500') {
                $query->where('vip_combo.name', 'like', 'VIP500%');
            } else {
                $query->where(function ($q) {
                    $q->where('vip_combo.name', 'like', 'VIP9%')
                        ->orWhere('vip_combo.name', 'like', 'VIP15%');
                });
            }
        }

        return $query
            ->where('status', 1)
            ->where('vip_combo_course.course_id', $courseId)
            ->pluck('id')
            ->toArray();
    }

    public function getFilterComboIds($comboIds, $type)
    {
        $query = VipCombo::whereIn('id', $comboIds)
            ->where('status', 1);

        if ($type !== 'all') {
            if ($type === 'vip500') {
                $query->where('vip_combo.name', 'like', 'VIP500%');
            } else {
                $query->where(function ($q) {
                    $q->where('vip_combo.name', 'like', 'VIP9%')
                        ->orWhere('vip_combo.name', 'like', 'VIP15%');
                });
            }
        }
        return $query->pluck('id')->toArray();
    }

    public function getVipUserIds($comboIds, $n, $timeFrom, $timeTo, $testerIds, $oldUserIds = null)
    {
        $query = DB::table('invoice')
            ->join('community_group_users AS gu', 'gu.invoice_id', 'invoice.id')
            ->join('community_groups AS g', 'g.id', 'gu.group_id')
            ->where('g.vip_level', $n)
            ->where('product_type', 'vip_combo')
            ->whereNotIn('invoice.user_id', $testerIds)
            ->whereIn('product_id', $comboIds)
            ->where('invoice_status', 'completed');

        if ($timeFrom && $timeFrom !== 'all') {
            $query->where('invoice.created_at', '>=', $timeFrom);
        }

        if ($timeTo && $timeTo !== 'all') {
            $query->where('invoice.created_at', '<=', $timeTo);
        }

        if ($oldUserIds !== null) {
            $query->whereIn('invoice.user_id', $oldUserIds);
        }

        return $query->distinct()
            ->pluck('invoice.user_id')
            ->toArray();
    }

    /** getView saleStatistic */
    public function getSalesStatisticPage()
    {
        if (!in_array(\Auth::guard('admin')->user()->email, [
            '<EMAIL>', '<EMAIL>',
            '<EMAIL>', '<EMAIL>'
        ])) {
            abort(404);
        }
        return view('backend.vip.salesStatistic');
    }

    /** get data saleStatistic */
    public function getSalesStatistic(Request $request)
    {
        $params = $request->all();

        if (isset($params['dateFrom'])) {
            $params['dateFrom'] = Carbon::parse($request->dateFrom)->format('Y-m-d');
        }
        if (isset($params['dateTo'])) {
            $params['dateTo'] = Carbon::parse($request->dateTo)->format('Y-m-d');
        }

        switch ($params['sale']) {
            case "all":
                $params['sale'] = ['sale', 'sale_offline'];
                break;
            case "sale":
                $params['sale'] = ['sale'];
                break;
            case "sale_offline":
                $params['sale'] = ['sale_offline'];
                break;
        }

        $query = Admin::query()
            ->with(['invoices' => function ($invoi) use ($params) {
                $invoi->where('product_type', 'offline')
                    ->where('payment_status', 'paid');
                if (isset($params['dateFrom'])) {
                    $invoi = $invoi->whereDate('created_at', '>=', $params['dateFrom']);
                }
                if (isset($params['dateTo'])) {
                    $invoi = $invoi->whereDate('created_at', '<=', $params['dateTo']);
                }
            }]);
        if (isset($params['keywords']) && $params['keywords'] != null) {
            $query = $query->where('name', 'LIKE', '%' . $params['keywords'] . '%');
            $query = $query->where('email', 'LIKE', '%' . $params['keywords'] . '%');
        }
        $query = $query->whereIn('desc', $params['sale'])
            ->paginate($params['per_page'], '*', 'page', $params['page']);

        return $query;
    }

    public function getExtendStats(Request $request)
    {
        $params = $request->all();
        $data = isset($params['type']) && $params['type'] === 'online_basic' ? $this->getStudentsByCourse($params) : $this->getStudentsByGroup($params);

        $studentIds = $data['studentIds'];
        $studentCount = $data['studentCount'];

        $toFilter = Carbon::createFromFormat('Y-m-d', $params['to'])->addMonths(2)->format('Y-m-d');
        $fromFilter = Carbon::createFromFormat('Y-m-d', $params['from'])->subMonths(1)->format('Y-m-d');
        $extendCombo = [116];
        $invoiceQuery = Invoice::query()->select('id', 'user_id', 'product_id', 'product_type', 'product_name', 'price', 'currency', 'created_at')
            ->with(['user' => function ($q) {
                $q->select('id', 'name', 'email');
            }])
            ->where('product_type', '<>', 'offline')
            ->whereIntegerInRaw('user_id', $studentIds)
            ->where('price', '>', 0)
            ->whereNotIn('product_id', $extendCombo)
            ->whereNotNull('product_id')
            ->where('created_at', '>=', $fromFilter)
            ->where('created_at', '<=', $toFilter);
        if (isset($params['new_type']) && $params['new_type']) {
            if ($params['new_type'] === 'online_basic') {
                $invoiceQuery->whereIn('product_type', ['combo', 'course']);
            }
            if ($params['new_type'] === 'vip') {
                $invoiceQuery->whereIn('product_type', ['vip_combo', 'vip_course']);
            }
            if (isset($params['new_product']) && $params['new_product']) {
                $invoiceQuery->whereIn('product_id', explode(',', $params['new_product']));
            }
            if (isset($params['new_level']) && $params['new_level']) {
                foreach (explode(',', $params['new_level']) as $level) {
                    $invoiceQuery->where('product_name', 'LIKE', '%' . $level . '%');
                }
            }
        }
        $comboInvoices = Invoice::query()->where('user_id', $studentIds)->where('product_name', 'LIKE', '%+%')->pluck('id')->toArray();
        $invalidUsers = User::whereIn('id', $studentIds)->whereHas('invoices', function ($query) {
            $query->havingRaw('COUNT(*) < 2');
        })->pluck('id')->toArray();
        $invoiceQuery->whereNotIn('user_id', $invalidUsers);
        $invoices = $invoiceQuery->orderBy('created_at', 'ASC')->get();
        $countInvoice = $invoiceQuery->distinct('user_id')->count();
        return view('backend.vip.extendStat', compact('invoices', 'countInvoice', 'studentCount'));
    }

    public function getOffExtendStats(Request $request)
    {
        $params = $request->all();
        $data = $this->getOfflineStudents($params);

        $studentIds = $data['studentIds'];
        $studentCount = $data['studentCount'];

        $toFilter = Carbon::createFromFormat('Y-m-d', $params['to'])->addMonths(2)->format('Y-m-d');
        $invoiceQuery = Invoice::query()->select('id', 'user_id', 'product_id', 'product_type', 'product_name', 'price', 'currency', 'created_at')
            ->with(['user' => function ($q) {
                $q->select('id', 'name', 'email');
            }])
            ->where('product_type', '=', 'offline')
            ->whereIntegerInRaw('user_id', $studentIds)
            ->where('price', '>', 0)
            ->whereNotNull('product_id')
            ->where('created_at', '>=', $params['from'])
            ->where('created_at', '<=', $toFilter);
        if (isset($params['new_product']) && $params['new_product']) {
            $invoiceQuery->whereIn('product_id', explode(',', $params['new_product']));
        }
        if (isset($params['new_level']) && $params['new_level']) {
            foreach (explode(',', $params['new_level']) as $level) {
                $invoiceQuery->where('product_name', 'LIKE', '%' . $level . '%');
            }
        }
        $comboInvoices = Invoice::query()->where('user_id', $studentIds)->where('product_name', 'LIKE', '%+%')->pluck('id')->toArray();
        $invalidUsers = User::whereIn('id', $studentIds)->whereHas('invoices', function ($query) {
            $query->havingRaw('COUNT(*) < 2');
        })->pluck('id')->toArray();
        $invoiceQuery->whereNotIn('user_id', $invalidUsers);
        $invoices = $invoiceQuery->orderBy('created_at', 'ASC')->get();
        $countInvoice = $invoiceQuery->distinct('user_id')->count();
        return view('backend.offline.offExtendStat', compact('invoices', 'countInvoice', 'studentCount'));
    }

    private function getStudentsByGroup($params)
    {
        $groupQuery = CommunityGroup::select('id', 'vip_session');
        if (isset($params['product']) && $params['product']) {
            $groupQuery = $groupQuery->whereIn('type', explode(',', $params['product']));
        }

        if (isset($params['level']) && $params['level']) {
            $groupQuery = $groupQuery->whereIn('vip_level', explode(',', $params['level']));
        }

        $groupIds = $groupQuery->get()
            ->mapWithKeys(function ($group) {
                return [$group->id => $group->vip_session];
            });
        $courseTime = CourseTime::whereIn('group_id', $groupIds->keys())->whereNotNull('group_id')->where('deleted', 0)
            ->select('group_id', DB::raw('COUNT(*) as attendance_count'), DB::raw('MAX(id) as last_attendance_id'))
            ->groupBy('group_id')
            ->get();

        $filteredAttendances = $courseTime->filter(function ($attendance) use ($groupIds) {
            return $attendance->attendance_count == $groupIds[$attendance->group_id];
        });

        $lastAttendanceIds = $filteredAttendances->pluck('last_attendance_id');
        $attendances = CourseTimeStudent::query()->select('id', 'course_time_id', 'student_id', 'deleted')
        ->whereRelation('course_time', function ($q) use ($params, $lastAttendanceIds) {
            $q->where('date_attendance', '>=', $params['from'])
                ->where('date_attendance', '<=', $params['to'])->whereIn('id', $lastAttendanceIds);
        })->where('deleted', 0)->distinct('student_id')->get();

        return [
          'studentIds' => $attendances->pluck('student_id')->toArray(),
          'studentCount' => $attendances->count(),
        ];
    }

    private function getOfflineStudents($params)
    {
        $groupQuery = SchoolCourse::select('id', 'total');
        if (isset($params['level']) && $params['level']) {
            $groupQuery = $groupQuery->whereIn('level_of_n', explode(',', $params['level']));
        }

        $groupIds = $groupQuery->get()
            ->mapWithKeys(function ($group) {
                return [$group->id => $group->total];
            });
        $courseTime = CourseTime::whereIn('course_id', $groupIds->keys())->whereNotNull('course_id')->where('deleted', 0)
            ->select('course_id', DB::raw('COUNT(*) as attendance_count'), DB::raw('MAX(id) as last_attendance_id'))
            ->where('status', 0)
            ->groupBy('course_id')
            ->get();

        $filteredAttendances = $courseTime->filter(function ($attendance) use ($groupIds) {
            return $attendance->attendance_count == $groupIds[$attendance->course_id];
        });

        $lastAttendanceIds = $filteredAttendances->pluck('last_attendance_id');
        $attendances = CourseTimeStudent::query()->select('id', 'course_time_id', 'student_id', 'deleted')
        ->whereRelation('course_time', function ($q) use ($params, $lastAttendanceIds) {
            $q->where('date_attendance', '>=', $params['from'])
                ->where('date_attendance', '<=', $params['to'])->whereIn('id', $lastAttendanceIds);
        })->where('deleted', 0)->distinct('student_id')->get();

        $dmrIds = SchoolUser::whereIn('id', $attendances->pluck('student_id')->toArray())
            ->pluck('dmr_id')->toArray();

        return [
          'studentIds' => $dmrIds,
          'studentCount' => $attendances->count(),
        ];
    }

    private function getStudentsByCourse($params)
    {
        $owners = CourseOwner::query()->where('watch_expired_day', '>=', $params['from'])->where('watch_expired_day', '<=', $params['to']);

        if (isset($params['product']) && $params['product']) {
            $owners = $owners->whereIn('course_id', explode(',', $params['product']));
        }

        // duyệt từng owner, check xem course_id của owner đó có thuộc combo trong đơn gần nhất không, nếu có thì có phải là course cuối cùng không
        $owners = $owners->with('latest_invoice')->get();

        $owners = $owners->filter(function ($owner) {
            $ownerCourseName = $owner->title;
            $invoiceProductName = $owner->latest_invoice->product_name ?? '';
            if (
                (str_contains($invoiceProductName, '+' . $ownerCourseName) || str_contains($invoiceProductName, '+ ' . $ownerCourseName)) &&
                (!str_contains($invoiceProductName, '+' . $ownerCourseName . '+') && !str_contains($invoiceProductName, '+ ' . $ownerCourseName . ' +'))) {
                return true;
            }
            if (str_contains($invoiceProductName, $ownerCourseName) && !str_contains($invoiceProductName, '+')) {
                return true;
            }
            return false;
        });
        return [
            'studentIds' => $owners->pluck('owner_id')->toArray(),
            'studentCount' => $owners->count()
        ];
    }

    public function getCourses()
    {
        $courses = Course::all();
        return response()->json([
            'data' => $courses
        ]);
    }
    public function getVipCombo()
    {
        $courses = VipCombo::all();
        return response()->json([
            'data' => $courses
        ]);
    }

    public function basicRatio()
    {
        return view('backend.basic.index');
    }

    public function getBasicRatio(Request $request)
    {
        $data = [];
        $invoices = new Collection();
        $courseId = $request->courseId;
        $courses = [
            17 => 'N1',
            16 => 'N2',
            3 => 'N3',
            40 => 'N4',
            39 => 'N5'
        ];

        $courseRequiredLessonIds = Lesson::where('course_id', $courseId)->where('require', 1)->pluck('id')->toArray();
        $courseLessons = \App\Http\ModelsFrontend\Lesson::select('id', 'show')->with('component:id,lesson_id,type,show')->where('course_id', $courseId)->where('show', ACTIVE)->get();
        $courseLessonIds = $courseLessons->pluck('id')->toArray();
        $comboIds = [];
        $trackingStats = [];
        $progresses = [];
        $lessonResults = [];
        $examResults = [];

        if ($request->has('from') && $request->has('to') && $request->from && $request->to) {
            $query = Invoice::query()
                ->select('id', 'active_time', 'user_id', 'product_name')
                ->with(['user' => function ($q) use ($courseLessonIds) {
                    $q->select('id', 'name', 'email');
                }])
                ->whereNotNull('active_time')
                ->whereBetween('active_time', [Carbon::parse($request->from)->startOfDay(), Carbon::parse($request->to)->endOfDay()]);
            switch ($request->type) {
                case 'offline':
                    $ids = VipCombo::where('type', 5)->where('name', 'like', '%'.$courses[$courseId].'%')->pluck('id')->toArray();
                    $query->where('product_type', 'offline')->whereIn('product_id', $ids);
                    break;
                case 'online_basic':
                    $comboIds = Combo::whereIn('type', ['jlpt'])->where('services', 'like', '%"'.$courseId.'"%')->pluck('id')->toArray();

                    $query->where(function ($query) use ($comboIds, $courseId) {
                        $query->where(function ($q) use ($comboIds) {
                            $q->where('product_type', 'combo')
                                ->whereIn('product_id', $comboIds);
                        })->orWhere(function ($query) use ($courseId) {
                            $query->where('product_type', 'course')
                                ->where('product_id', $courseId);
                        });
                    });
                    break;
                case 'vip':
                    $ids = VipCombo::whereIn('type', [1,2,3,6])->where('name', 'like', '%'.$courses[$courseId].'%')->pluck('id')->toArray();
                    $query->where('product_type', 'vip_combo')->whereIn('product_id', $ids);
                    break;
                default:
                    $query->where('product_type', $request->type);
                    break;
            }

            $invoices = $query->get();
            $userIds = $invoices->sortBy('user_id')->unique('user_id')->pluck('user_id')->toArray();
            $trackingStats = LessonTrackingStat::query()->select('user_id', 'lesson_id', 'category_id', 'spent_time', 'created_at')
                ->whereIn('user_id', $userIds)
                ->whereIn('lesson_id', $courseLessonIds)->get();
            $progresses = LessonProgress::query()->select('user_id', 'lesson_id', 'video_progress', 'example_progress', 'created_at')
                ->whereIntegerInRaw('user_id', $userIds)
                ->whereIntegerInRaw('lesson_id', $courseLessonIds)->get();
            $lessonResults = LessonResult::query()->select('id', 'lesson_id', 'user_id', 'total_grade', 'created_at', 'passed')
                ->whereIntegerInRaw('user_id', $userIds)
                ->whereIntegerInRaw('lesson_id', $courseLessonIds)
                ->whereNotNull('data')
                ->where('created_at', '>=', Carbon::parse($request->from)->startOfDay())
                ->orderBy('passed', 'desc')
                ->get();
            $lessonResults = $lessonResults->map(function ($lessonResult) use ($courseLessons) {
                $lesson = $courseLessons->where('id', $lessonResult['lesson_id'])->first();
                $lessonResult['passed'] = $lessonResult['total_grade'] >= $lesson->pass_marks;
                return $lessonResult;
            });
            $examResults = ExamResult::query()->select('id', 'lesson_id', 'user_id', 'total_score', 'is_passed', 'created_at')
                ->whereIntegerInRaw('user_id', $userIds)
                ->whereIntegerInRaw('lesson_id', $courseLessonIds)
                ->whereNotNull('submit_at')
                ->where('created_at', '>=', Carbon::parse($request->from)->startOfDay())
                ->orderBy('is_passed', 'desc')
                ->get();
        }

        $courseLessons = $courseLessons->map(function ($lesson) {
            $componentExercise = $lesson->component->whereIn('type', LessonToTask::COMPONENT_TYPES_EXERCISE);
            $lesson['exercise'] = !$componentExercise->isEmpty();
            return $lesson;
        });

        return response()->json([
            'data' => $data,
            'invoices' => $invoices,
//            'courseRequiredLessonsIds' => $courseRequiredLessonIds,
//            'courseLessonIds' => $courseLessonIds,
            'courseLessons' => $courseLessons->makeHidden(['component']),
            'comboIds' => $comboIds,
            'trackingStats' => $trackingStats,
            'progresses' => $progresses,
            'lessonResults' => $lessonResults,
            'examResults' => $examResults,
        ]);
    }
}
