<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Models\LessonToTask;
use App\Http\ModelsFrontend\Comment;
use App\Http\ModelsFrontend\LessonProgress;
use App\Models\FrontEnd\ResultFlashCard;
use Illuminate\Http\Request;

class FlashCardController extends Controller
{
    public function getFirstComments(Request $request)
    {
//        dd($request->all());
        // lay comment tuong ung voi moi the flashcard voi dieu kienj uu tien ghim -> like nhieu nhat -> moi nhat
        $comments = [];
        foreach ($request['ids'] as $id) {
            $comment = \App\Http\Models\Comment::query()
                ->where('table_id', $id)
                ->where('table_name', $request->name)
                ->where('parent_id', 0)
                ->orderBy('pin', 'desc')
                ->orderBy('count_like', 'desc')
                ->orderBy('id', 'desc')
                ->with(['comment_like' => function ($q) {
                    $q->select('comment_id', 'user_id')->where('user_id', auth()->id());
                }])
                ->select('id', 'content', 'user_id', 'count_like', 'created_at', 'pin', 'parent_id', 'table_id', 'table_name')
                ->first();
            $comments[$id] = $comment;
        }

        return response()->json(['comments' => $comments], 200);
    }

    public function getResultByUser(Request $request)
    {
        $result = ResultFlashCard::query()
            ->where('lesson_id', $request->lesson_id)
            ->where('user_id', auth()->id())
            ->first();
        return response()->json(['result' => $result], 200);
    }

    public function saveResult(Request $request)
    {
        $result = ResultFlashCard::query()
            ->where('lesson_id', $request->lesson_id)
            ->where('user_id', auth()->id())
            ->first();

        $progress = LessonProgress::query()
            ->where('user_id', auth()->id())
            ->where('lesson_id', $request->lesson_id)
            ->first();

        $countComponent = LessonToTask::query()
            ->where('lesson_id', $request->lesson_id)
            ->where('type', LessonToTask::TYPE_FLASHCARD)
            ->where('show', ACTIVE)
            ->count('id');

        if (!$progress) {
            $progress = new LessonProgress();
            $progress->user_id = auth()->id();
            $progress->lesson_id = $request->lesson_id;
            $progress->example_progress = 0;
        }

        if (!$result) {
            $result = new ResultFlashCard();
            $result->lesson_id = $request->lesson_id;
            $result->user_id = auth()->id();
            $result->flashcard = json_encode($request->flashcard_ids);
            $result->save();
            return response()->json(['result' => $result], 200);
        }
        $result->flashcard = json_decode($result->flashcard);

        // so sanh mang flashcard_ids với mang flashcard trong result. sau đó chỉ thay dđổi id thuộc mảng ids gửi lên
        foreach ($request->flashcard_ids['remember'] as $id) {
            foreach ($result->flashcard->non as $key => $value) {
                if ($value == $id) {
                    unset($result->flashcard->non[$key]);
                }
            }
            if (!in_array($id, $result->flashcard->remember)) {
                array_push($result->flashcard->remember, $id);
            }
        }

        foreach ($request->flashcard_ids['non'] as $id) {
            foreach ($result->flashcard->remember as $key => $value) {
                if ($value == $id) {
                    unset($result->flashcard->remember[$key]);
                }
            }
            if (!in_array($id, $result->flashcard->non)) {
                array_push($result->flashcard->non, $id);
            }
        }
//        dd(count($result->flashcard->remember), $countComponent, (count($result->flashcard->remember) / $countComponent), (count($result->flashcard->remember) / $countComponent) * 100);

        if (((count($result->flashcard->remember) / $countComponent) * 100) > $progress->example_progress) {
            $progress->example_progress = (count($result->flashcard->remember) / $countComponent) * 100;
        }
        $progress->save();

//        dd($progress);
        $result->flashcard->remember = array_values($result->flashcard->remember);
        $result->flashcard->non = array_values($result->flashcard->non);

        $result->flashcard = json_encode($result->flashcard);

        $result->save();
        $result->example_progress = $progress->example_progress;
        return response()->json(['result' => $result], 200);
    }

    public function likeComment(Request $request)
    {
        $comment = Comment::query()->find($request->id);
        dd($request->all());
    }

    public function getCommentById(Request $request)
    {
        dd($request->all());
        $flashcard = LessonToTask::query()->find($request->id);
        $comment = Comment::query()->find($request->comment_id);
        dd($comment);
    }
}
