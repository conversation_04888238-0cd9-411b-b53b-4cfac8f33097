<?php

namespace App\Http\ModelsFrontend;

use App\Models\FrontEnd\CourseVote;
use Illuminate\Database\Eloquent\Model;
use App\Http\ModelsFrontend\Teacher;
use Illuminate\Support\Facades\Cache;

class Course extends Model
{

    const
        ID_COURSE_N3_OLD = 3,
        ID_COURSE_N2_OLD = 16,
        ID_COURSE_N1_OLD = 17,
        ID_COURSE_N4_NEW = 40,
        ID_COURSE_N5_NEW = 39,
        ID_COURSE_N3_NEW = 44,
        ID_COURSE_N2_NEW = 45,
        ID_COURSE_N1_NEW = 46,
        ID_COURSE_KANJI_N5 = 47,
        ID_COURSE_PRACTICE_TEST_N4 = 30,
        ID_COURSE_PRACTICE_TEST_N5 = 41;

    const IDS_COURSE_DISCOUNT = [
        self::ID_COURSE_KANJI_N5,
        self::ID_COURSE_N3_NEW,
        self::ID_COURSE_N2_NEW,
        self::ID_COURSE_N1_NEW,
    ];

    protected $table = 'course';

    //lấy ra tên tác giả
    public function getAuthorName()
    {

        $listNames = Cache::get('teachers_name_' . $this->SEOurl);
        if (!$listNames) {

            $listID = json_decode($this->author_id);
            $teachers = Teacher::select('name')->whereIn('id', $listID)->get()->toArray();
            if (sizeof($teachers) == 0) return null;
            $teachers = array_pop($teachers);
            //            $teachers = array_map('array_pop', $teachers);
            $listNames = implode(", ", $teachers);
            Cache::put('teachers_name_' . $this->SEOurl, $listNames, 1440);
        }

        return $listNames;
    }

    public function getFriendlyUrl()
    {

        $string = new StringConvert();
        //url = url than thien + id
        if ($this->name == 'N1' || $this->name == 'N2' || $this->name == 'N3' || $this->name == 'N4' || $this->name == 'N5')
            $url = $string->sanitizeTitle('khoa-' . $this->name);
        else
            $url = $string->sanitizeTitle($this->name);
        return $url;
    }

    //lấy ra ngày giờ ở dạng thân thiện
    public function getFriendlyTime()
    {

        //2015-12-01 07:43:55
        $thisDateTime = $this->created_at;
        $compareOnDay = substr($this->created_at, 0, 10);

        //nếu là trong ngày
        if (date('Y-m-d') == $compareOnDay) {
            return "Hôm nay, lúc " . substr($thisDateTime, 11, 5);

            //nếu không phải trong ngày
        } else {

            $extraYear = substr($this->created_at, 0, 4);
            $thisYear = date("Y");

            //nếu là trong năm
            if ($extraYear == $thisYear) {
                return substr($thisDateTime, 8, 2) . " tháng " . substr($thisDateTime, 5, 2) . ", lúc " . substr($thisDateTime, 11, 5);
            } else {
                return substr($thisDateTime, 8, 2) . " tháng " . substr($thisDateTime, 5, 2) . ", " . $extraYear;
            }
        }
    }

    public function intro_lesson()
    {
        return $this->hasOne(Lesson::class, 'id', 'first_lesson');
    }

    public function lessons(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Lesson::class, 'course_id');
    }

    public function rating(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(CourseVote::class, 'course_id');
    }
}
