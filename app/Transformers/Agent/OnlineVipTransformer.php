<?php
namespace App\Transformers\Agent;

use Carbon\Carbon;
use League\Fractal\TransformerAbstract;

class OnlineVipTransformer extends TransformerAbstract
{
    const LEVELS = [
        "N1"       => "N1",
        "N2"       => "N2",
        "N3"       => "N3",
        "N4"       => "N4",
        "N5"       => "N5",
        "kaiwacb"  => "Kaiwa cơ bản",
        "kaiwasc"  => "Kaiwa sơ cấp",
        "kaiwatc"  => "Kaiwa trung cấp 1",
        "kaiwatc2" => "Kaiwa trung cấp 2",
        "ldn1"     => "Luyện đề N1",
        "ldn2"     => "Luyện đề N2",
        "ldn3"     => "Luyện đề N3",
        "ldn4"     => "Luyện đề N4",
        "ldn5"     => "Luyện đề N5",
    ];

    const TYPE_LIST = [
        "basic"   => "Cơ bản",
        "vip500"  => "Vip500",
        "vip15"   => "Vip15",
        "vip9"    => "Vip9",
        "vip1"    => "Vip1",
        "luyende" => "Luyện đề",
        "online"  => "Online",
        "offline" => "Offline",
        "captoc"  => "Cấp tốc",
        "matgoc"  => "Mất gốc",
        "kaiwa"   => "Kaiwa",
        "b2b"     => "Doanh nghiệp",
        "tokutei" => "Tokutei",
    ];

    const NAME_DETAIL = [
        "basic"   => "Online VIP Cơ bản",
        "vip500"  => "Online VIP500",
        "vip15"   => "Online VIP15",
        "vip9"    => "Online VIP9",
        "vip1"    => "Online VIP1",
        "luyende" => "Luyện đề online qua Zoom",
        "online"  => "Online",
        "offline" => "Offline",
        "captoc"  => "Cấp tốc",
        "matgoc"  => "Mất gốc",
        "kaiwa"   => "Kaiwa online qua Zoom",
        "b2b"     => "Doanh nghiệp",
        "tokutei" => "Tokutei",
    ];

    public function transform($entity)
    {
        return [
            'id'                   => $entity->id,
            'name'                 => $this->getNameDetail($entity->type),
            'shift_type'           => $this->getShiftType($entity->shift_type),
            'shift_time'           => $entity->shift_time,
            'type'                 => $this->getType($entity->type),
            'price_vn'             => $entity->vip_combo->price,
            'price_jp'             => convertVNDToJPY($entity->vip_combo->price),
            'start_date'           => Carbon::parse($entity->start_date)->format('Y-m-d'),
            'finish_date'          => Carbon::parse($entity->end_at)->format('Y-m-d'),
            'total_current_member' => $entity->users_count,
            'class_size'           => $entity->size,
            'level'                => $this->getLevel($entity->vip_level),
            'status'               => $this->getStatus($entity),
            'type_note'            => $entity->type_note,
        ];
    }

    private function getShiftType($shiftType)
    {
        if (empty($shiftType)) {
            return '';
        }

        $numbers = str_split($shiftType);
        $days    = [];

        foreach ($numbers as $number) {
            if (is_numeric($number)) {
                if ($number == '8') {
                    $days[] = 'Chủ nhật';
                } else {
                    $days[] = 'Thứ ' . $number;
                }
            }
        }
        return implode(' - ', $days);
    }

    private function getStatus($entity)
    {
        if ($entity->users_count == $entity->size) {
            return "Đã đủ số lượng";
        }
        return "Đang tuyển thành viên";
    }

    private function getLevel($level)
    {
        return self::LEVELS[$level];
    }

    private function getType($type)
    {
        return isset(self::TYPE_LIST[$type]) ? self::TYPE_LIST[$type] : $type;
    }

    private function getNameDetail($type)
    {
        return isset(self::NAME_DETAIL[$type]) ? self::NAME_DETAIL[$type] : $type;
    }

}
