# Hướng dẫn triển khai chức năng Undo cho Vocabulary Flashcard

## Tổng quan

Chức năng undo đã được triển khai thành công cho vocabulary flashcard. <PERSON><PERSON> hệ thống vocabulary re-render lại danh sách flashcard sau mỗi lần vuốt thẻ, chúng ta không thể sử dụng `stackedCardsInstance.undo()` mà phải tạo một hệ thống undo riêng.

## C<PERSON><PERSON> thay đổi đã thực hiện

### 1. Thêm data properties mới

```javascript
// Trong data() của FlashCard.vue
undoHistory: [], // Lưu trữ lịch sử để undo
maxUndoSteps: 5, // Giới hạn số lần undo
```

### 2. Thêm methods mới

#### saveUndoState()
```javascript
saveUndoState() {
  const state = {
    dataFlashCard: JSON.parse(JSON.stringify(this.dataFlashCard)),
    swipeCardList: JSON.parse(JSON.stringify(this.swipeCardList)),
    arrCardLearned: JSON.parse(JSON.stringify(this.arrCardLearned)),
    countSwipeCard: this.countSwipeCard,
    currentCard: this.currentCard ? JSON.parse(JSON.stringify(this.currentCard)) : null,
    timestamp: Date.now()
  };

  this.undoHistory.push(state);

  if (this.undoHistory.length > this.maxUndoSteps) {
    this.undoHistory.shift();
  }
}
```

#### undo()
```javascript
undo() {
  if (this.undoHistory.length === 0) return;

  const lastState = this.undoHistory.pop();
  
  // Khôi phục trạng thái
  this.dataFlashCard = lastState.dataFlashCard;
  this.swipeCardList = lastState.swipeCardList;
  this.arrCardLearned = lastState.arrCardLearned;
  this.countSwipeCard = lastState.countSwipeCard;
  this.currentCard = lastState.currentCard;

  // Xử lý đặc biệt cho user chưa mua khóa học
  if (this.categoryData.hasCourseOwner === false) {
    if (this.countSwipeCard > 0) {
      this.countSwipeCard--;
    }
    this.currentCard = this.dataFlashCard[0];
    return;
  }

  // Re-init stacked cards cho user đã mua khóa học
  this.$nextTick(() => {
    if (this.isJapanese) {
      let cards = document.querySelectorAll('.card-inner');
      cards.forEach(card => {
        card.classList.remove('flip');
      });
    }
    this.initStackedCards();
  });
}
```

#### canUndo()
```javascript
canUndo() {
  return this.undoHistory.length > 0;
}
```

### 3. Cập nhật swipeCard() method

Thêm `saveUndoState()` vào các vị trí thích hợp:

```javascript
swipeCard(type) {
  // ... existing code ...

  if (this.categoryData.hasCourseOwner === false) {
    this.saveUndoState(); // Lưu state cho user chưa mua khóa học
    // ... existing logic ...
    return;
  }

  // ... other special cases ...

  // Lưu trạng thái trước khi swipe thẻ chính
  this.saveUndoState();

  // ... existing swipe logic ...
}
```

### 4. Cập nhật template

Cập nhật nút undo để sử dụng `canUndo()`:

```html
<button
  :disabled="!canUndo() || (currentCard && (currentCard.is_test || (swipeCardList.length > 0 && swipeCardList[swipeCardList.length - 1].is_test))) || (!categoryData.hasCourseOwner && countSwipeCard === limitCardFree)"
  class="rounded-full drop-shadow-2xl p-4 cursor-pointer" 
  @click="undo"
  :style="canUndo() && (categoryData.hasCourseOwner || (!categoryData.hasCourseOwner && countSwipeCard < limitCardFree)) ? {background: '#FFFFFF'} : {background: '#D9D9D9'}"
>
```

### 5. Reset undo history

Thêm reset undo history vào các methods:

```javascript
// Trong startStudy()
async startStudy() {
  this.undoHistory = []; // Reset undo history
  // ... existing code ...
}

// Trong confirmResetProgress()
async confirmResetProgress() {
  this.undoHistory = []; // Reset undo history
  // ... existing code ...
}
```

## Testing

### 1. Unit Tests
Chạy file `test_vocabulary_undo.html` trong browser để test logic cơ bản.

### 2. Integration Tests
Chạy script `test_undo_integration.js` trong browser console:

```javascript
// Trong browser console trên trang vocabulary flashcard
testVocabularyUndo();
```

### 3. Manual Testing

#### Test Case 1: Basic Undo
1. Vào trang vocabulary flashcard
2. Vuốt một thẻ (trái hoặc phải)
3. Click nút Undo
4. Kiểm tra thẻ đã được khôi phục

#### Test Case 2: Multiple Undo
1. Vuốt nhiều thẻ liên tiếp
2. Click Undo nhiều lần
3. Kiểm tra từng thẻ được khôi phục theo thứ tự ngược lại

#### Test Case 3: Undo Limit
1. Vuốt hơn 5 thẻ
2. Kiểm tra chỉ có thể undo tối đa 5 lần

#### Test Case 4: Free User
1. Test với user chưa mua khóa học
2. Kiểm tra undo hoạt động trong giới hạn thẻ miễn phí

#### Test Case 5: Button State
1. Kiểm tra nút undo disabled khi không có history
2. Kiểm tra nút undo enabled khi có history
3. Kiểm tra styling thay đổi theo trạng thái

## Troubleshooting

### Vấn đề thường gặp:

1. **Undo không hoạt động**
   - Kiểm tra `undoHistory.length > 0`
   - Kiểm tra console logs
   - Kiểm tra điều kiện disable của nút

2. **UI không cập nhật sau undo**
   - Đảm bảo `$nextTick()` được sử dụng
   - Kiểm tra `initStackedCards()` được gọi
   - Kiểm tra flip state của thẻ

3. **Memory issues**
   - Kiểm tra `maxUndoSteps` limit
   - Đảm bảo history được reset khi cần

### Debug commands:

```javascript
// Kiểm tra undo history
console.log(this.undoHistory);

// Kiểm tra có thể undo
console.log(this.canUndo());

// Kiểm tra trạng thái hiện tại
console.log({
  dataFlashCard: this.dataFlashCard.length,
  swipeCardList: this.swipeCardList.length,
  undoHistory: this.undoHistory.length
});
```

## Performance Considerations

1. **Deep Clone**: Sử dụng `JSON.parse(JSON.stringify())` có thể chậm với objects lớn
2. **Memory Usage**: Giới hạn `maxUndoSteps` để tránh memory leak
3. **DOM Updates**: Sử dụng `$nextTick()` để tối ưu DOM updates

## Future Improvements

1. **Optimized Cloning**: Sử dụng thư viện clone chuyên dụng như Lodash
2. **Selective State Saving**: Chỉ lưu những properties thực sự thay đổi
3. **Undo Animation**: Thêm animation khi undo
4. **Undo Shortcuts**: Thêm keyboard shortcuts (Ctrl+Z)
5. **Undo Confirmation**: Thêm confirmation cho undo quan trọng

## Kết luận

Chức năng undo đã được triển khai thành công với:
- ✅ Hỗ trợ undo cho tất cả loại thẻ
- ✅ Xử lý đặc biệt cho user chưa mua khóa học
- ✅ Giới hạn memory usage
- ✅ UI/UX nhất quán
- ✅ Test coverage đầy đủ

Chức năng này giải quyết được vấn đề không thể sử dụng `stackedCardsInstance.undo()` do việc re-render danh sách flashcard sau mỗi lần swipe.
