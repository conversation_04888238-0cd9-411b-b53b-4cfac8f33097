var gulp = require('gulp');
var sass = require('gulp-sass');
var babel = require('gulp-babel');
var concat = require('gulp-concat');
var uglify = require('gulp-uglify');
var cleanCSS = require('gulp-clean-css');
var postcss = require('gulp-postcss');
var tailwindcss = require('tailwindcss');
var del = require('del');
var sourcemaps = require('gulp-sourcemaps');
var autoprefixer = require('gulp-autoprefixer');

var paths = {
    styles: {
        src: 'resources/assets/sass/frontend/*.scss',
        dest: 'public/assets/css/'
    },
    stylesBE: {
        src: 'resources/assets/sass/backend/*.scss',
        dest: 'public/assets/backend/css/'
    },
    scripts: {
        src: 'resources/assets/js/frontend/*.js',
        dest: 'public/assets/js/'
    },
    scriptsBE: {
        src: [
            'resources/assets/js/backend/*.js',
            'resources/assets/js/backend/**/*.js',
            '!resources/assets/js/backend/course-stage/*.js',
        ],
        dest: 'public/assets/backend/js/'
    }
};

/* Not all tasks need to use streams, a gulpfile is just another node program
 * and you can use all packages available on npm, but it must return either a
 * Promise, a Stream or take a callback and call it
 */

/*
 * Define our tasks using plain functions
 */
function baseStyles() {
    return gulp.src(
        'resources/assets/sass/index.scss'
    )
        .pipe(sass())
        .pipe(cleanCSS())
        // pass in options to the stream
        .pipe(gulp.dest('public/css/'));
}
function styles() {
    return gulp.src(
        paths.styles.src,
        '!resources/assets/sass/frontend/styles.scss',
        '!resources/assets/sass/frontend/account.scss',
        '!resources/assets/sass/frontend/home.scss',
        '!resources/assets/sass/frontend/header_footer.scss',
        '!resources/assets/sass/frontend/course.scss',
        '!resources/assets/sass/frontend/teachers.scss',
        '!resources/assets/sass/frontend/blog.scss',
        '!resources/assets/sass/frontend/support.scss',
        '!resources/assets/sass/frontend/page.scss',
        '!resources/assets/sass/frontend/payment.scss',
        '!resources/assets/sass/frontend/search.scss',
        '!resources/assets/sass/frontend/book.scss',
        '!resources/assets/sass/frontend/common.scss',
        '!resources/assets/sass/frontend/community.scss',
        '!resources/assets/sass/frontend/test_online.scss',
        '!resources/assets/sass/frontend/about.scss',

        '!resources/assets/sass/mobile/style.scss',
        '!resources/assets/sass/mobile/account.scss',
        '!resources/assets/sass/mobile/home.scss',
        '!resources/assets/sass/mobile/header_footer.scss',
        '!resources/assets/sass/mobile/search.scss',
        '!resources/assets/sass/mobile/teacher.scss',
        '!resources/assets/sass/mobile/support.scss',
        '!resources/assets/sass/mobile/blog.scss',
        '!resources/assets/sass/mobile/course.scss',
        '!resources/assets/sass/mobile/page.scss',
        '!resources/assets/sass/mobile/payment.scss',
        '!resources/assets/sass/mobile/book.scss'
    )
        .pipe(sass())
        .pipe(cleanCSS())
        // pass in options to the stream
        .pipe(gulp.dest(paths.styles.dest));
}

function stylesBE() {
    return gulp.src(
        paths.stylesBE.src,
        '!resources/assets/sass/backend/dashboard.scss',
        '!resources/assets/sass/backend/feedback_manager.scss',
        '!resources/assets/sass/backend/document_manager.scss',
        '!resources/assets/sass/backend/popup_manager.scss',
        '!resources/assets/sass/backend/slider_manager.scss',
        '!resources/assets/sass/backend/comment.scss',
        '!resources/assets/sass/backend/task_manager.scss',
        '!resources/assets/sass/backend/global-notification.scss',
        '!resources/assets/sass/backend/schedule-notification.scss',
        '!resources/assets/sass/backend/new-lesson-detail.scss',
        '!resources/assets/sass/backend/common.scss',
        '!resources/assets/sass/backend/common-vip.scss'
    )
        .pipe(sass())
        .pipe(cleanCSS())
        // pass in options to the stream
        .pipe(gulp.dest(paths.stylesBE.dest));
}

function scripts() {
    return gulp.src([paths.scripts.src,
        '!resources/assets/js/components/CommentsCommunity.js',
        '!resources/assets/js/frontend/community.js',
        '!resources/assets/js/frontend/community_me.js'
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(gulp.dest(paths.scripts.dest));
}

function scriptsBE() {
    return gulp.src(paths.scriptsBE.src, { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(gulp.dest(paths.scriptsBE.dest));
}
function pluginCSS() {
    return gulp.src([
        'public/plugin/bootstrap/css/bootstrap.min.css',
        'public/plugin/fancybox/dist/jquery.fancybox.min.css',
        'public/plugin/font-awesome/css/font-awesome.min.css',
        'public/plugin/md-iconic/css/material-design-iconic-font.min.css',
        'public/plugin/slick/slick.css',
        'public/plugin/slick/slick-theme.css',
        'public/css/index.css',
    ])
        .pipe(autoprefixer({
            cascade: false
        }))
        // chỉ bật source map lên khi làm trên local muốn debug css
        .pipe(cleanCSS({ rebaseTo: 'public/css' }))
        .pipe(concat('plugins.css'))
        // pass in options to the stream
        .pipe(gulp.dest('public/css'));
}
function feCSS() {
    return gulp.src([
        'resources/assets/sass/frontend/styles.scss',
        'resources/assets/sass/frontend/account.scss',
        'resources/assets/sass/frontend/home.scss',
        'resources/assets/sass/frontend/header_footer.scss',
        'resources/assets/sass/frontend/course.scss',
        'resources/assets/sass/frontend/teachers.scss',
        'resources/assets/sass/frontend/blog.scss',
        'resources/assets/sass/frontend/support.scss',
        'resources/assets/sass/frontend/page.scss',
        'resources/assets/sass/frontend/payment.scss',
        'resources/assets/sass/frontend/search.scss',
        'resources/assets/sass/frontend/book.scss',
        'resources/assets/sass/frontend/common.scss',
        'resources/assets/sass/frontend/community.scss',
        'resources/assets/sass/frontend/test_online.scss',
        'resources/assets/sass/frontend/about.scss',

        'resources/assets/sass/mobile/style.scss',
        'resources/assets/sass/mobile/account.scss',
        'resources/assets/sass/mobile/home.scss',
        'resources/assets/sass/mobile/header_footer.scss',
        'resources/assets/sass/mobile/search.scss',
        'resources/assets/sass/mobile/teacher.scss',
        'resources/assets/sass/mobile/support.scss',
        'resources/assets/sass/mobile/blog.scss',
        'resources/assets/sass/mobile/course.scss',
        'resources/assets/sass/mobile/page.scss',
        'resources/assets/sass/mobile/payment.scss',
        'resources/assets/sass/mobile/book.scss'
    ])
        // .pipe(sourcemaps.init())
        // chỉ bật source map lên khi làm trên local muốn debug css
        .pipe(sass())
        .pipe(postcss([
            tailwindcss('./tailwind.config.js'),
            require('autoprefixer')
        ]))
        .pipe(cleanCSS())
        .pipe(concat('styles.css'))
        // .pipe(sourcemaps.write())

        // pass in options to the stream
        .pipe(gulp.dest('public/assets/css'));
}

function beCSS() {
    return gulp.src([
        'resources/assets/sass/backend/dashboard.scss',
        'resources/assets/sass/backend/feedback_manager.scss',
        'resources/assets/sass/backend/document_manager.scss',
        'resources/assets/sass/backend/popup_manager.scss',
        'resources/assets/sass/backend/slider_manager.scss',
        'resources/assets/sass/backend/comment.scss',
        'resources/assets/sass/backend/task_manager.scss',
        'resources/assets/sass/backend/global-notification.scss',
        'resources/assets/sass/backend/schedule-notification.scss',
        'resources/assets/sass/backend/new-lesson-detail.scss',
        'resources/assets/sass/backend/common.scss',
        'resources/assets/sass/backend/common-vip.scss'
    ], {base: 'public/plugin'})
        .pipe(sourcemaps.init())
        .pipe(sass())
        .pipe(postcss([
            tailwindcss('./tailwind.config.js'),
            require('autoprefixer')
        ]))
        .pipe(cleanCSS({ sourceMap: true }))
        .pipe(concat('dashboard.css'))
        .pipe(sourcemaps.write())
        // pass in options to the stream
        .pipe(gulp.dest('public/assets/backend/css'));
}

function feJlptCSS() {
    return gulp.src([
        'resources/assets/sass/jlpt/jlpt.scss',
    ], {base: 'public/plugin'})
        .pipe(sass())
        .pipe(cleanCSS({ sourceMap: true }))
        .pipe(concat('jlpt.css'))

        // pass in options to the stream
        .pipe(gulp.dest('public/assets/css'));
}

function feHeadlibs() {
    return gulp.src([
        'public/plugin/jquery/jquery.min.js',
        'public/plugin/fancybox/dist/jquery.fancybox.min.js',
        'public/plugin/autosize/autosize.js',
        'resources/assets/js/frontend/default.js',
    ], { sourcemaps: true })
        .pipe(concat('headlibs.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function feHls() {
    return gulp.src([
        'public/plugin/videojs_hls/videojs.js',
        'public/plugin/videojs_hls/videojs-hls.js',
        'public/plugin/videojs_hls/videojs-viewport.js',
        'public/plugin/videojs_hls/videojs-resolution-switcher.js',
    ], { sourcemaps: true })
        .pipe(uglify())
        .pipe(concat('lib-hls.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function feComponents() {
    return gulp.src([
        'resources/assets/js/components/CourseGroup.js',
        'resources/assets/js/components/CourseGroupLD.js',
        'resources/assets/js/components/CourseGroupPremium.js',
        'resources/assets/js/components/CommentFacebook.js',
        'resources/assets/js/components/HomeFeedback.js',
        'resources/assets/js/components/Components.js',
        'resources/assets/js/components/Comments.js',
        'resources/assets/js/components/CommentBH.js',
        'resources/assets/js/components/CommentEJU.js',
        'resources/assets/js/components/CommentsFeedbacks.js',
        'resources/assets/js/components/CommentFacebook.js',
        'resources/assets/js/components/MixedTest.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('components.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function feApp() {
    return gulp.src([
        'public/plugin/bootstrap/js/bootstrap.min.js',
        'public/plugin/clientjs/client.min.js',
        'resources/assets/js/frontend/tracking.js',
        'resources/assets/js/PublicFunction.js',
    ], { sourcemaps: true })
        .pipe(uglify())
        .pipe(concat('app.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function feAuth() {
    return gulp.src([
        'resources/assets/js/frontend/login.js',
        'resources/assets/js/frontend/register.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('GVuZ3RoKCk.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function feCommunity() {
    return gulp.src([
        'resources/assets/js/components/CommentsCommunity.js',
        'resources/assets/js/frontend/community.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('community.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function feCommunityMe() {
    return gulp.src([
        'resources/assets/js/components/CommentsCommunity.js',
        'resources/assets/js/frontend/community_me.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('community_me.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function feHome() {
    return gulp.src([
        'public/plugin/waypoint/waypoints.min.js',
        'public/plugin/counter-up/jquery.counterup.min.js',
        'resources/assets/js/frontend/home.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('home.js'))
        .pipe(gulp.dest(paths.scripts.dest));
}

function beCourseStage() {
    return gulp.src([
        'resources/assets/js/backend/course-stage/stage-panel.js',
        'resources/assets/js/backend/course-stage/category-panel.js',
        'resources/assets/js/backend/course-stage/group-panel.js',
        'resources/assets/js/backend/course-stage/course-stage.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('course-stage.js'))
        .pipe(gulp.dest('public/assets/backend/js/course-stage/'));
}

function beAdventure() {
    return gulp.src([
        'resources/assets/js/backend/adventure/stage-panel.js',
        'resources/assets/js/backend/adventure/path-panel.js',
        'resources/assets/js/backend/adventure/checkpoint-panel.js',
        'resources/assets/js/backend/adventure/index.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('adventure.js'))
        .pipe(gulp.dest(paths.scriptsBE.dest));
}

function beLesson() {
    return gulp.src([
        'resources/assets/js/backend/lesson/modal.js',
        'resources/assets/js/backend/lesson/task-content-form.js',
        'resources/assets/js/backend/lesson/task-quiz-form.js',
        'resources/assets/js/backend/lesson/task-gap-fill-form.js',
        'resources/assets/js/backend/lesson/task-interactive-video-form.js',
        'resources/assets/js/backend/lesson/lesson-info.js',
        'resources/assets/js/backend/lesson/lesson-tasks.js',
        'resources/assets/js/backend/lesson/new-lesson-detail.js',
    ], { sourcemaps: true })
        .pipe(babel())
        .pipe(uglify())
        .pipe(concat('backend-lesson.js'))
        .pipe(gulp.dest('public/assets/backend/js/lesson/'));
}

function watch() {
    gulp.watch(['resources/assets/sass/*.scss'], baseStyles);
    gulp.watch([paths.styles.src], styles);
    gulp.watch([paths.stylesBE.src], stylesBE);
    gulp.watch([paths.scripts.src], scripts);
    gulp.watch(paths.scriptsBE.src, scriptsBE);
    gulp.watch(['resources/assets/sass/frontend/*.scss', 'resources/assets/sass/mobile/*.scss'], feCSS);
    gulp.watch(['resources/assets/sass/backend/*.scss'], beCSS);
    gulp.watch(['resources/assets/sass/jlpt/*.scss'], feJlptCSS);
    gulp.watch(['resources/assets/js/frontend/default.js'], feHeadlibs);
    gulp.watch(['resources/assets/js/components/*.js'], feComponents);
    gulp.watch(['resources/assets/js/PublicFunction.js'], feApp);
    gulp.watch([
        'resources/assets/js/frontend/login.js',
        'resources/assets/js/frontend/register.js',
    ], feAuth);
    gulp.watch([
        'resources/assets/js/components/CommentsCommunity.js',
        'resources/assets/js/frontend/community.js'
    ], feCommunity);
    gulp.watch([
        'resources/assets/js/components/CommentsCommunity.js',
        'resources/assets/js/frontend/community_me.js'
    ], feCommunityMe);
    gulp.watch(['resources/assets/js/frontend/home.js'], feHome);
    gulp.watch(['resources/assets/js/backend/course-stage/*.js'], beCourseStage);
    gulp.watch(['resources/assets/js/backend/adventure/*.js'], beAdventure);
    gulp.watch(['resources/assets/js/backend/lesson/*.js'], beLesson);
}

/*
 * Specify if tasks run in series or parallel using `gulp.series` and `gulp.parallel`
 */
var build = gulp.series(gulp.parallel(
    baseStyles,
    styles,
    stylesBE,
    scripts,
    scriptsBE,
    pluginCSS,
    feCSS,
    feJlptCSS,
    feHeadlibs,
    feHls,
    feComponents,
    feApp,
    feAuth,
    feCommunity,
    feCommunityMe,
    feHome,
    beCSS,
    beCourseStage,
    beAdventure,
    beLesson
));

/*
 * You can use CommonJS `exports` module notation to declare tasks
 */
//separated files
exports.baseStyles = baseStyles;
exports.styles = styles;
exports.stylesBE = stylesBE;
exports.scripts = scripts;
exports.scriptsBE = scriptsBE;
exports.plugin = pluginCSS;
// frontend styles
exports.css = feCSS;
exports.jlpt = feJlptCSS;
// frontend scripts
exports.headlibs = feHeadlibs;
exports.hls = feHls;
exports.components = feComponents;
exports.app = feApp;
exports.auth = feAuth;
exports.community = feCommunity;
exports.communityMe = feCommunityMe;
exports.home = feHome;
// backend styles
exports.beCSS = beCSS;
// backend scripts
exports.beCourseStage = beCourseStage;
exports.beAdventure = beAdventure;
exports.beLesson = beLesson;

// common command
exports.watch = watch;
exports.build = build;
/*
 * Define default task that can be called by just running `gulp` from cli
 */
exports.default = build;
