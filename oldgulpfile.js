const elixir = require("laravel-elixir");
require("laravel-elixir-vue-2");

elixir((mix) => {
  // // mix sass for libs
  // mix.sass([
  // './public/plugin/bootstrap/css/bootstrap.min.css',
  // './public/plugin/fancybox/dist/jquery.fancybox.min.css',
  // './public/plugin/font-awesome/css/font-awesome.min.css',
  // './public/plugin/md-iconic/css/material-design-iconic-font.min.css',
  // './public/plugin/slick/slick.css',
  // './public/plugin/slick/slick-theme.css',
  //
  // ], './public/assets/css/dmrlib.css');

  // // các thư viện js tải trước
  // mix.scripts([
  // './public/plugin/jquery/jquery.min.js',
  // './public/plugin/fancybox/dist/jquery.fancybox.min.js',
  // './public/plugin/autosize/autosize.js',
  // './resources/assets/js/frontend/default.js',
  //
  // ], './public/assets/js/headlibs.js');

  // //nén js video hls trong bài học
  // mix.scripts(
  //   [
  //     "./public/plugin/videojs_hls/videojs.js",
  //     // './public/plugin/videojs_hls/videojs-hls.js',
  //     "./public/plugin/videojs_hls/videojs-viewport.js",
  //     "./public/plugin/videojs_hls/videojs-resolution-switcher.js",
  //   ],
  //   "./public/assets/js/lib-hls.js"
  // );

  // mix sass for frontend
  // mix.sass(
  //   [
  //     "./public/assets/css/dmrlib.css",
  //
  //     "./resources/assets/sass/frontend/styles.scss",
  //     "./resources/assets/sass/frontend/account.scss",
  //     "./resources/assets/sass/frontend/home.scss",
  //     "./resources/assets/sass/frontend/header_footer.scss",
  //     "./resources/assets/sass/frontend/course.scss",
  //     "./resources/assets/sass/frontend/teachers.scss",
  //     "./resources/assets/sass/frontend/blog.scss",
  //     "./resources/assets/sass/frontend/support.scss",
  //     "./resources/assets/sass/frontend/page.scss",
  //     "./resources/assets/sass/frontend/payment.scss",
  //     "./resources/assets/sass/frontend/search.scss",
  //     "./resources/assets/sass/frontend/book.scss",
  //     "./resources/assets/sass/frontend/common.scss",
  //     "./resources/assets/sass/frontend/community.scss",
  //     "./resources/assets/sass/frontend/test_online.scss",
  //     "./resources/assets/sass/frontend/about.scss",
  //
  //     "./resources/assets/sass/mobile/style.scss",
  //     "./resources/assets/sass/mobile/account.scss",
  //     "./resources/assets/sass/mobile/home.scss",
  //     "./resources/assets/sass/mobile/header_footer.scss",
  //     "./resources/assets/sass/mobile/search.scss",
  //     "./resources/assets/sass/mobile/teacher.scss",
  //     "./resources/assets/sass/mobile/support.scss",
  //     "./resources/assets/sass/mobile/blog.scss",
  //     "./resources/assets/sass/mobile/course.scss",
  //     "./resources/assets/sass/mobile/page.scss",
  //     "./resources/assets/sass/mobile/payment.scss",
  //     "./resources/assets/sass/mobile/book.scss",
  //   ],
  //   "./public/assets/css/styles.css"
  // );

  // // mix sass for frontend
  // mix.sass([
  // './public/plugin/bootstrap/css/bootstrap.min.css',
  // './public/plugin/fancybox/dist/jquery.fancybox.min.css',
  // './public/plugin/font-awesome/css/font-awesome.min.css',
  // './public/plugin/md-iconic/css/material-design-iconic-font.min.css',
  // './resources/assets/sass/jlpt/jlpt.scss',
  // ], './public/assets/css/jlpt.css');
  //
  // mix.scripts([
  //   './resources/assets/js/components/CourseGroup.js',
  //   './resources/assets/js/components/CourseGroupLD.js',
  //   './resources/assets/js/components/CourseGroupPremium.js',
  //   './resources/assets/js/components/CommentFacebook.js',
  //   './resources/assets/js/components/HomeFeedback.js',
  //   './resources/assets/js/components/crypto.js',
  //   './resources/assets/js/components/Components.js',
  //   './resources/assets/js/components/Comments.js',
  //   './resources/assets/js/components/CommentBH.js',
  //   './resources/assets/js/components/CommentEJU.js',
  //   './resources/assets/js/components/CommentsFeedbacks.js',
  //   './resources/assets/js/components/CommentFacebook.js',
  //   './resources/assets/js/components/MixedTest.js',
  // ], './public/assets/js/components.js');

  // mix.scripts([
  // './public/plugin/bootstrap/js/bootstrap.min.js',
  // './public/plugin/clientjs/client.min.js',
  // './resources/assets/js/frontend/tracking.js',
  // './resources/assets/js/PublicFunction.js',
  // ], './public/assets/js/app.js');

  // mix.scripts([
  // './resources/assets/js/frontend/login.js',
  // './resources/assets/js/frontend/register.js',
  // ], './public/assets/js/GVuZ3RoKCk.js');

  // mix.scripts([
  // './resources/assets/js/frontend/chatbox.js',
  // ], './public/assets/js/chatbox.js');
  //
  // mix.scripts([
  // './resources/assets/js/components/CommentsCommunity.js',
  // './resources/assets/js/frontend/community.js',
  // ], './public/assets/js/community.js');
  // //
  // mix.scripts([
  // './resources/assets/js/components/CommentsCommunity.js',
  // './resources/assets/js/frontend/community_me.js',
  // ], './public/assets/js/community_me.js');

  // mix.scripts([
  //     './resources/assets/js/frontend/test_online.js',
  // ], './public/assets/js/test_online.js');
  //
  // mix.scripts([
  //     './resources/assets/js/frontend/test_online_room.js',
  // ], './public/assets/js/test_online_room.js');

  // mix.scripts([
  // './resources/assets/js/frontend/avatar.js',
  // ], './public/assets/js/avatar.js');

  // mix.scripts([
  // './resources/assets/js/frontend/result.js'
  // ], './public/assets/js/result.js');

  // mix.scripts([
  // './resources/assets/js/frontend/support.js'
  // ], './public/assets/js/support.js');

  // mix.scripts([
  // './resources/assets/js/frontend/fill_active_code.js'
  // ], './public/assets/js/fill_active_code.js');

  // mix.scripts([
  // './resources/assets/js/frontend/global-notification.js',
  // ], './public/assets/js/notifications.js');

  // mix.scripts([
  // './resources/assets/js/backend/schedule/schedule-notification.js',
  // ], './public/assets/backend/js/schedule/schedule-notification.js');

  // mix.scripts([
  // './resources/assets/js/backend/schedule/sent-list.js',
  // ], './public/assets/backend/js/schedule/sent-list.js');

  // mix.scripts([
  // './resources/assets/js/backend/schedule/schedule-list.js',
  // ], './public/assets/backend/js/schedule/schedule-list.js');

  // mix.scripts([
  // './public/plugin/waypoint/waypoints.min.js',
  // './public/plugin/counter-up/jquery.counterup.min.js',
  // './resources/assets/js/frontend/home.js',
  // ], './public/assets/js/home.js');

  // mix.scripts(['./resources/assets/js/frontend/mp4-player.js'], './public/assets/js/mp4-player.js');
  // mix.scripts(['./resources/assets/js/frontend/lesson-ld.js'], './public/assets/js/lesson-ld.js');

  // mix.scripts([
  // './resources/assets/js/frontend/profile.js',
  // ], './public/assets/js/profile.js');

  // mix.scripts([
  // './resources/assets/js/frontend/flashcards.js'
  // ], './public/assets/js/flashcards.js');
  //
  // mix.scripts([
  // './resources/assets/js/frontend/detail_lesson.js',
  // ], './public/assets/js/detail_lesson.js');
  //

  //
  // mix.scripts([
  //     './resources/assets/js/frontend/constants.js',
  // ], './public/assets/js/constants.js');

  // mix.scripts([
  // './resources/assets/js/frontend/payment.js',
  // ], './public/assets/js/payment.js');
  //
  // mix.scripts([
  //     './resources/assets/js/frontend/guest-payment.js',
  // ], './public/assets/js/guest-payment.js');

  // mix.scripts([
  // './resources/assets/js/frontend/jlpt-ranking.js',
  // ], './public/assets/js/jlpt-ranking.js');

  // mix.scripts([
  // './resources/assets/js/frontend/jlpt-history.js',
  // ], './public/assets/js/jlpt-history.js');

  // mix.scripts([
  // './resources/assets/js/frontend/jlpt-exam.js',
  // ], './public/assets/js/jlpt-exam.js');

  // mix.scripts([
  // './resources/assets/js/frontend/countdown.js',
  // ], './public/assets/js/countdown.js');

  // mix sass for backend
  // mix.sass([
  // './resources/assets/sass/backend/dashboard.scss',
  // './resources/assets/sass/backend/feedback_manager.scss',
  // './resources/assets/sass/backend/document_manager.scss',
  // './resources/assets/sass/backend/popup_manager.scss',
  // './resources/assets/sass/backend/slider_manager.scss',
  // './resources/assets/sass/backend/comment.scss',
  // './resources/assets/sass/backend/task_manager.scss',
  // './resources/assets/sass/backend/global-notification.scss',
  // './resources/assets/sass/backend/schedule-notification.scss',
  // './resources/assets/sass/backend/new-lesson-detail.scss',
  // './resources/assets/sass/backend/common.scss',
  // './resources/assets/sass/backend/common-vip.scss',
  // ], './public/assets/backend/css/dashboard.css');

  // //mix sass for backend
  // mix.sass([
  // './resources/assets/sass/backend/image_manager.scss',

  // ], './public/assets/css/backend_styles.css');

  // mix.scripts([
  // './resources/assets/js/backend/comment.js',
  // ], './public/assets/backend/js/comment.js');

  // mix.scripts([
  // './resources/assets/js/backend/comment_fc.js',
  // ], './public/assets/backend/js/comment_fc.js');

  // mix.scripts([
  // './resources/assets/js/backend/dashboard.js',
  // ], './public/assets/backend/js/dashboard.js');

  // mix.scripts([
  // './resources/assets/js/backend/backend-global-notification.js',
  // ], './public/assets/backend/js/backend-notifications.js');

  // Mix sass for chat
  // mix.sass(['./resources/assets/sass/backend/chat.scss'], './public/assets/backend/css/chat.css');

  // Mix script for chat
  // mix.scripts(['./resources/assets/js/backend/chat.js'], './public/assets/backend/js/chat.js');

  // mix.scripts([
  //     './resources/assets/js/backend/booking-kaiwa.js',
  // ], './public/assets/backend/js/booking-kaiwa.js');

  // mix.scripts([
  //     './resources/assets/js/backend/global-notification.js'
  // ], './public/assets/backend/js/global-notification.js');

  // Mix script for tips
  // mix.scripts(['./resources/assets/js/backend/tips.js'], './public/assets/backend/js/tips.js');

  // Mix sass for tips
  // mix.sass(['./resources/assets/sass/backend/tips.scss'], './public/assets/backend/css/tips.css');

  // Mix script for sale_commission
  // mix.scripts(['./resources/assets/js/backend/sale_commission.js'], './public/assets/backend/js/sale_commission.js');

  // Mix sass for sale_commission
  // mix.sass(['./resources/assets/sass/backend/sale_commission.scss'], './public/assets/backend/css/sale_commission.css');

  // Mix script for jlpt_result
  // mix.scripts(['./resources/assets/js/backend/jlpt_results.js'], './public/assets/backend/js/jlpt_results.js');

  // Mix sass for jlpt_result
  // mix.sass(['./resources/assets/sass/backend/jlpt_results.scss'], './public/assets/backend/css/jlpt_results.css');

  // Mix script for backend jlpt detail
  // mix.scripts(['./resources/assets/js/backend/jlpt_detail.js'], './public/assets/backend/js/jlpt_detail.js');

  // Mix script for backend jlpt-list
  // mix.scripts(['./resources/assets/js/backend/jlpt_list.js'], './public/assets/backend/js/jlpt_list.js');

  // Mix sass for chat
  // mix.sass(['./resources/assets/sass/backend/chat.scss'], './public/assets/backend/css/chat.css');

  // Mix script for chat
  // mix.scripts(['./resources/assets/js/backend/chat.js'], './public/assets/backend/js/chat.js');

  // Mix script for dashboard
  // mix.scripts(['./resources/assets/js/backend/new_dashboard.js'], './public/assets/backend/js/new_dashboard.js');

  // Mix sass for dashboard
  // mix.sass(['./resources/assets/sass/backend/new_dashboard.scss'], './public/assets/backend/css/new_dashboard.css');
  //
  // mix.scripts([
  //     './resources/assets/js/backend/modal.js',
  // ], './public/assets/backend/js/modal.js');
  //
  // mix.scripts([
  //     './resources/assets/js/backend/course-stage/stage-panel.js',
  //     './resources/assets/js/backend/course-stage/category-panel.js',
  //     './resources/assets/js/backend/course-stage/group-panel.js',
  //     './resources/assets/js/backend/course-stage/course-stage.js',
  // ], './public/assets/backend/js/course-stage/course-stage.js');

  // Mix js for adventure
  // mix.scripts([
  //     './resources/assets/js/backend/adventure/stage-panel.js',
  //     './resources/assets/js/backend/adventure/path-panel.js',
  //     './resources/assets/js/backend/adventure/checkpoint-panel.js',
  //     './resources/assets/js/backend/adventure/index.js',
  // ], './public/assets/backend/js/adventure.js');
  // //
  // // // Mix sass for stage
  // mix.sass(['./resources/assets/sass/backend/course-stage.scss'], './public/assets/backend/css/course-stage.css');

  // mix.scripts([
  // './resources/assets/js/frontend/modal.js',
  // ], './public/assets/js/modal.js');

  // mix.scripts([
  //     './resources/assets/js/frontend/video-modal.js',
  // ], './public/assets/js/video-modal.js');
  //
  // mix.scripts([
  //     './resources/assets/js/frontend/pie-timer.js',
  // ], './public/assets/js/pie-timer.js');

  // Mix script for backend lesson
  // mix.scripts([
  //     './resources/assets/js/backend/lesson/modal.js',
  //     './resources/assets/js/backend/lesson/task-content-form.js',
  //     './resources/assets/js/backend/lesson/task-quiz-form.js',
  //     './resources/assets/js/backend/lesson/task-gap-fill-form.js',
  //     './resources/assets/js/backend/lesson/task-interactive-video-form.js',
  //     './resources/assets/js/backend/lesson/lesson-info.js',
  //     './resources/assets/js/backend/lesson/lesson-tasks.js',
  //     './resources/assets/js/backend/lesson/gap-fill.js',
  //     './resources/assets/js/backend/lesson/new-lesson-detail.js',
  // ], './public/assets/backend/js/lesson/backend-lesson.js');

  // // Mix script for backend video questions
  // mix.scripts(['./resources/assets/js/backend/lesson/video-questions.js'], './public/assets/backend/js/lesson/video-questions.js');
  //
  // // Mix sass for backend video questions
  // mix.sass(['./resources/assets/sass/backend/video-questions.scss'], './public/assets/backend/css/video-questions.css');
  //
  // mix.scripts([
  //     './resources/assets/js/frontend/interactive_video.js',
  // ], './public/assets/js/interactive_video.js');

  // mix.scripts([
  //     './resources/assets/js/frontend/teacher-detail.js',
  // ], './public/assets/js/teacher-detail.js');
  // Mix script for fb_share management
  // mix.scripts(['./resources/assets/js/backend/marketing/fb-share.js'], './public/assets/backend/js/marketing/fb-share.js');

  // Mix script for coupon management
  // mix.scripts(['./resources/assets/js/backend/coupon.js'], './public/assets/backend/js/coupon.js');

  // Mix sass for backend video questions
  // mix.sass(['./resources/assets/sass/backend/fb-share.scss'], './public/assets/backend/css/fb-share.css');

  // Mix sass for backend common table with filter
  // mix.sass(['./resources/assets/sass/backend/filterable_list.scss'], './public/assets/backend/css/filterable_list.css');

  // Mix script for user
  // mix.scripts(['./resources/assets/js/backend/user/user.list.js'], './public/assets/backend/js/user/user.list.js');

  // Mix script for user
  // mix.scripts(['./resources/assets/js/backend/vip/invoice_list.js'], './public/assets/backend/js/vip/invoice_list.js');

  // mix.scripts([
  // './resources/assets/js/frontend/roadmap.js',
  // ], './public/assets/js/roadmap.js');

  // Mix js vip result
  // mix.scripts(['./resources/assets/js/backend/vip_result.js'], './public/assets/backend/js/vip_result.js');

  // mix.scripts([
  // './resources/assets/js/backend/discuss.js',
  // ], './public/assets/backend/js/discuss.js');
});

// Watch
gulp.task("watch", function () {
  gulp.watch("./resources/assets/sass/frontend/*.scss", ["default"]);
  gulp.watch("./resources/assets/sass/mobile/*.scss", ["default"]);
  gulp.watch("./resources/assets/sass/backend/*.scss", ["default"]);
  gulp.watch("./resources/assets/js/*.js", ["default"]);
  gulp.watch("./resources/assets/js/frontend/*.js", ["default"]);
  gulp.watch("./resources/assets/js/backend/*.js", ["default"]);
  gulp.watch("./resources/assets/js/backend/adventure/*.js", ["default"]);
  gulp.watch("./resources/assets/js/backend/user/*.js", ["default"]);
  gulp.watch("./resources/assets/js/backend/vip/*.js", ["default"]);
  gulp.watch("./resources/assets/js/components/**", ["default"]);
});
