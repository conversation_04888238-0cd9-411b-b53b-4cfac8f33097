{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production && npm run tailwind-build", "production": "mix --production", "tailwind-watch": "npx tailwindcss -i ./public/css/index.css -o ./public/css/base.css --watch", "tailwind-build": "npx tailwindcss -i ./public/css/index.css -o ./public/css/base.css --minify"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/register": "^7.22.5", "autoprefixer": "^10.4.12", "axios": "^0.16.2", "babel-plugin-component": "^1.1.1", "bootstrap-sass": "^3.4.1", "cross-env": "^5.0.1", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-minify": "^3.1.0", "gulp-postcss": "^9.0.1", "gulp-sourcemaps": "^3.0.0", "jquery": "^3.1.1", "laravel-elixir": "^6.0.0-18", "laravel-elixir-vue-2": "^0.3.0", "laravel-elixir-webpack-official": "^1.0.10", "laravel-mix": "^6.0.43", "lodash": "^4.17.4", "node-sass": "^6.0.1", "nodemon": "^2.0.2", "prettier": "^2.7.1", "resolve-url-loader": "^4.0.0", "sass": "^1.43.4", "sass-loader": "^12.3.0", "tailwindcss": "3.2.4", "vue": "^2.6.12", "vue-loader": "^15.9.6", "vue-template-compiler": "^2.6.12"}, "dependencies": {"@ffmpeg/core": "^0.10.0", "@ffmpeg/ffmpeg": "^0.10.1", "@fullcalendar/bootstrap": "^6.1.10", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/list": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue": "^6.1.10", "@json2csv/plainjs": "^7.0.1", "bootstrap": "^5.1.3", "chart.js": "^4.4.2", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-datalabels": "^2.2.0", "cron": "^1.3.0", "date-fns": "^3.6.0", "dom-to-image": "^2.6.0", "ejs": "^2.6.1", "element-ui": "^2.15.14", "es6-promise": "^4.2.8", "express": "^3.2.6", "gulp": "^4.0.2", "jsonwebtoken": "^8.2.2", "mysql": "^2.15.0", "perfect-scrollbar": "^1.5.6", "plyr": "^3.7.8", "postcss": "^8.3.11", "pusher-js": "^8.0.2", "redis": "^2.8.0", "socket.io": "^1.0.6", "uuid": "^3.3.3", "video.js": "7.21.1", "vue-axios": "^3.5.0", "vue-chartjs": "^5.3.0", "vue-ctk-date-time-picker": "^2.5.0", "vue-i18n": "^8.28.2", "vue-infinite-loading": "^2.4.5", "vue-perfect-scrollbar": "^0.2.1", "vue-router": "^3.0.0", "vue-select": "^3.0.2", "vue-upload-multiple-image": "^1.1.6", "vue2-datepicker": "^3.10.3", "vuejs-paginate": "^2.1.0", "vuex": "^3.6.2", "wavesurfer.js": "^7.9.1", "webpack": "^5.90.0", "websocket": "^1.0.31"}, "browserslist": ["last 2 version", "> 1%", "> 1%", "ie >= 8", "edge >= 15", "ie_mob >= 10", "ff >= 45", "chrome >= 45", "safari >= 7", "opera >= 23", "ios >= 7", "android >= 4", "bb >= 10"], "name": "dungmori-new", "description": "*<PERSON><PERSON> h<PERSON> online Tiếng Nhật cho người Việt*", "version": "1.0.0", "main": "app.js", "directories": {"test": "tests"}, "repository": {"type": "git", "url": "git+https://github.com/dungmori/dungmori-new.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/dungmori/dungmori-new/issues"}, "homepage": "https://github.com/dungmori/dungmori-new#readme"}