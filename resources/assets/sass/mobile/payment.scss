@media only screen and (max-width: 768px) {
    .main {
        .main-payment {
            .nav-wizard { margin-top: 0; z-index: 998;
                .step-text{display: none;}
                li { a {border-radius: 0 !important;}}
            }
            .mb-stt{padding: 5px 15px; display: block;
                .step-mb{padding: 0 4px; border-radius: 50%; background-color: #588d3f; color: #fff;}
            }
            .steps-container { margin-top: 0px;
                .steps-container-left {width: 100%; padding-bottom: 30px;
                    input {border: 1px solid #DDD;}
                    .table{width: 100vw; margin-left: -15px;}
                    .guest-info-container{padding: 50px 0;}
                    .customer-info-container {
                        .customer-info-table { padding: 5px 15px 0px 15px;
                            .user-form-item { width: auto; }
                        }
                        .customer-successfull-gates {padding: 20px 15px 20px 15px; text-align: justify; }
                    }
                    .invoice-warning .close {margin: -13px -9px 2px 5px; }
                    .step-1-container {
                        .customer-info-container { padding-bottom: 10px;
                            .payment-heading {
                                span {margin-left: 0; padding-left: 15px; font-size: 19px; }
                                .refresh { padding-right: 15px; font-size: 17px; }
                            }
                        }
                    }
                    .step-2-container {
                        .customer-choose-payment-gates label span { width: calc(100% - 35px); }
                    }
                    .continue-container{
                        .dmr-btn {float: left; margin-left: calc((100vw - 167px)/2); }
                        .buy-btn {padding: 10px 15px; font-size: 16px; margin-right: 15px;}
                        .light-btn{padding: 10px 15px; font-size: 16px;}
                    } 
                    .invoice-warning .label { width: 100%; margin-bottom: 7px; font-size: 14px; padding-top: 10px; padding-bottom: 10px; }
                    .delivery-info{width: calc(100vw); margin-left: -25px;}
                    .customer-info-container{padding-bottom: 0;}
                    .customer-choose-payment-gates{padding-bottom: 0 !important;}
                    .link-checkout{word-wrap: break-word !important;}
                }
                .steps-container-right { margin-bottom: 20px; margin-left: calc((100vw - 355px)/2); padding: 0;
                    .payment-info-container .combo-item { padding: 10px 10px 15px 10px;
                        .combo-detail-container .course-info { padding-left: 15px; }
                    }
                }
                .steps-container-left .continue-container {padding-left: 15px; padding-right: calc((100vw - 332px)/2); }
            }
            .checkout-container {width: 100%; padding: 0 15px; }
        }
    }
}

@media only screen and (max-width: 481px) {
    .main .main-center .steps-container .steps-container-right { margin-left: 0; width: calc(100vw - 15px); padding-left: 15px; }
    .main .main-payment .steps-container .steps-container-left .continue-container {
        padding-right: calc((100vw - 322px)/2);
        .light-btn {
            margin-right: 5px;
        }
    }
}