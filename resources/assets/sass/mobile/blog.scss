@media only screen and (max-width: 768px) {
  .main {
    .main-center {
      width: 100%;
      .new-blog-container {
        margin: 0;
        width: 100%;
        .newest-blog-box {
          max-height: none;
        }
        .new-blog-content {
          width: 100%;
          padding-top: 30px;
          padding-bottom: 20px;
        }
        .new-blog-item {
          padding: 15px;
          flex-direction: column;
          margin: 15px;
          .new-blog-img {
            width: 100%;
            min-width: 100%;
            height: calc((100vw - 60px) * 0.73);
          }
          .new-blog-info {
            padding: 15px 0 0 0;
            h2 {
              margin-top: 10px;
              margin-bottom: 5px;
              font-size: 24px;
              -webkit-line-clamp: 2;
              max-height: calc(24px * 1.3 * 2);
            }
            .blog-intro {
              margin-top: 7px;
              -webkit-line-clamp: 5;
              height: calc(15px * 1.5 * 5);
              max-height: calc(15px * 1.5 * 5);
            }
          }
        }
      }
      .blog-main-left {
        width: 100% !important;
      }
      .mobile-ct-group {
        display: block;
        margin: 20px 0 0 0;
      }
      .accordion {
        color: #fff;
        cursor: pointer;
        padding: 10px;
        width: calc(100% - 30px);
        margin: 0 15px;
        border: none;
        text-align: center;
        outline: none;
        font-size: 15px;
        transition: 0.4s;
        background: #41a336;
        border-radius: 4px;
        &.active,
        &:hover {
          background: #41a336 !important;
        }
      }

      .news-item {
        width: 100%;
        margin: 10px 0;
      }
      .main-left {
        width: 100%;
        .panel {
          padding: 0 15px;
          background-color: white;
          box-shadow: none;
          max-height: 0;
          overflow: hidden;
          transition: max-height 0.2s ease-out;
        }
        .blog-content {
          grid-template-columns: auto;
          margin: 0;
        }
        .blog-heading {
          padding: 0 15px;
          margin-top: 35px;
        }
        .featured {
          width: 100%;
          padding: 10px 15px;
          a .title {
            width: 100%;
            font-size: 19px;
            padding: 0;
            margin-left: 0;
          }
          a .lazyload {
            width: 100%;
            height: calc(100vw * 9 / 16);
          }
          .info {
            display: none;
          }
          .brief {
            width: 100%;
            padding: 0;
          }
        }
        .featured-sub {
          margin-top: 20px;
          width: 100%;
          float: left;
          padding: 0 15px;
          margin-left: 0;
          a .title {
            width: 100%;
            font-size: 16px;
            padding: 0;
            margin-left: 0;
          }
          a .lazyload {
            width: 100%;
            height: calc(100vw * 9 / 16);
          }
        }
        .bgbr {
          display: none;
        }
        .featured-more {
          padding: 15px;
          a .lazyload {
            width: 100%;
            height: calc(100vw * 9 / 16);
          }
          a .title {
            width: 100%;
            font-size: 16px;
            padding: 0;
            margin-top: 10px;
            color: #555;
            margin-left: 0;
          }
          .info {
            display: none;
          }
          .brief {
            display: inherit;
            width: 100%;
            margin-left: 0;
            opacity: 0.7;
          }
          .read-more {
            display: none;
          }
        }
        .pagination {
          padding: 0 15px;
          margin: 10px 0;
        }
        .blog-detail-container {
          width: 100%;
          margin: 0;
          padding: 0 15px;
          .related-blogs-container {
            width: 100%;
            .blog-heading {
              padding: 0;
            }
            .related-blogs-content {
              grid-template-columns: auto;
            }
          }
          .blog-detail-thumb {
            width: 100vw;
            max-width: 100vw;
            margin-left: -15px;
            height: auto;
          }
          .blog-detail-title {
            margin-top: 20px;
            margin-bottom: 20px;
            font-size: 23px;
            padding-left: 0 15px;
          }
          .blog-detail-content .main-content {
            text-align: justify;
            word-wrap: break-word;

            span {
              width: auto !important;
              height: auto !important;
            }
            img {
              height: auto !important;
              max-width: 100vw !important;
            }
          }
          .comment-container {
            .comment-heading span {
              font-size: 21px;
            }
            .fb-comments {
              margin-left: 0px;
            }
          }
        }
      }
      .blog-main {
        display: none;
      }
      .blog-main-right {
        width: 100% !important;
        margin: 0 !important;
        padding-bottom: 30px !important;
      }
      .main-right {
        width: 100%;
        padding: 0 15px;
        margin: 0;
        .list-category {
          display: none;
        }
        .related-news-item {
          padding: 10px 0;
          a .lazyload {
            width: 102px;
            height: 68px;
          }
          a .title {
            margin-left: 10px;
            width: calc(100vw - 160px);
          }
          a .info {
            margin-left: 10px;
            width: calc(100vw - 160px);
          }
        }
      }
    }
  }

  .fixed-panel {
    display: none;
  }
}
