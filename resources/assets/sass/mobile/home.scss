@import "variables";
@import "mixins";

@media only screen and (max-width: 1024px) {

    #application{width: 100%; float: left; padding-top: 60px;}
    .fixed-panel{transform: scale(0.9); right: 8px; bottom: 0;
        .go-top{display: none;}
    }

    .site-main{width: 100%; float: left;

        .home-screen__top{padding-top: 20px; padding-bottom: 0;}

        .home-screen__slider--item{padding: 0 6px 17px;
         .img { img{border-radius: 6px;} }
        }

        .home-screen__why{width: 90%; height: 50px;
            .home-screen__why--title{font-size: 14px; padding: 0 25px; text-align: center; height: 30px;}
        }

        .home-screen__main{width: 100vw;
            .home-screen__main--background{opacity: 0;}
            .home-screen__main--content{box-shadow: none;}
            .home-screen__main--header{ display: initial; padding: 0 30px;
                .home-screen__main--header-item{width: 65px; height: 65px; border-radius: 15px; border-width: 1px;
                    float: left; padding-top: 15px; margin-bottom: 70px;
                    img{height: 30px;}
                    div{width: 40vw; position: absolute; margin-top: 60px;
                        b{font-size: 13px;}
                        span{font-size: 11px; opacity: .7;}
                    }
                }
                .item-2{float: right;}
            }
            .home-screen__main--title{margin-top: 250px; font-size: 14px; line-height: 1.4;}
            .home-screen__main--story{ height: 420px;
                 .home-screen__main--story-image{width: 100%; text-align: center;
                    img{width: 200px; display: inline;}
                 }
                .home-screen__main--story-content{width: 70%; margin-top: 230px; position: absolute; text-align: center; font-size: 13px; line-height: 1.2;
                    .p2{font-size: 14px; margin: 10px 0;}
                }
                .home-screen__btn--light-green{margin-top: 15px; background: green; color: #fff; padding: 5px 10px;}
            }
            .home-screen__main--badge-left{font-size: 14px; height: 60px;
                .leaf{width: 30px; height: auto; right: -15px;}
            }
            .home-screen__main--course-list{margin-top: 40px; padding-top: 60px; height: 370px;}
            .home-screen__main--course-list-menu{width: 90vw; position: absolute; height: 50px; background: transparent; box-shadow: none;
                ul{width: 100%; float: left;
                    li{float: left; color: #444; font-size: 13px; padding: 0 15px 15px 0; }
                }
            }
            .home-screen__main--course-list-floating{left: 10px; }
            .home-screen__main--course-list-shape{width: 100vw; height: 230px; border-radius: 0; left: -15px; top: 85px; position: absolute;}
            .home-screen__main--course-list-slider{top: 40px; left: 0;}
            .home-screen__main--course-list-slider-nav .course-nxt{left: 77vw;}

            .badge-preview {display: none;}
            .home-screen__main--preview {display: none;}

        }

        .home-screen__feedback{
            .home-screen__feedback--count-inner{width: 200px; background: #fff; height: 100px;
                .circle-img{width: 60px;
                    img{width: 30px;}
                }
                span{font-size: 14px !important; }
            }
            .home-screen__feedback--shape-inner{width: 74vw; height: 300px; border-radius: 15px;}
            .home-screen__feedback--nav-btn.prv{ left: calc(50% - 40vw); }
            .home-screen__feedback--nav-btn.nxt{ right: calc(50% - 40vw); }
        }

        .home-screen__feedback--slider{ padding-top: 70px; width: 90vw;
            .home-screen__feedback--slide-inner{width: 22vw; margin: 0 10px 0 0;}
            .home-screen__feedback--slide-image{height: 120px; border-radius: 6px;}
            .home-screen__feedback--slide-info{
                .fb_name{font-size: 13px;} .fb_job{font-size: 13px;} .fb_add{font-size: 13px;}
            }
            .home-screen__feedback--slide{width: 100px;}

        }

        .home-screen__main--badge{
            .home-screen__main--badge-center{width: 60%; height: 80px; font-size: 14px;
                .leaf{width: 35px; height: auto; left: -17px;}
            }
        }

        .home-screen__blog{padding-top: 50px; width: 90vw;
            .home-screen__blog--feature-title{display: none;}
            .home-screen__blog--feature{width: 100%; margin-bottom: 30px;
                .feature-post-title{font-size: 14px;}
                span{font-size: 14px;}

            }
            .home-screen__blog--categories-title{display: none;}
            .home-screen__blog--categories-posts{margin: 0;
                .post-intro{margin-bottom: 15px; font-size: 14px;}
            }
        }

        .home-screen__contact--form-inner{width: 100vw; margin: 0; padding: 0;
            .contact-form-title{font-size: 18px !important; text-align: center; padding-left: 0 !important; margin-bottom: 20px;}
            .input--kohana{padding: 0 !important; margin: 0 !important; width: 100%; max-width: 100%; border-radius: 4px;}
        }

        .home-screen__blog--categories-menu{ padding: 10px 15px; background: #fff; font-size: 14px !important; border-radius: 0;
            .menu-item { color: #41A336; padding: 5px 10px; border-radius: 16px; border: 1px solid #EEE; box-shadow: none; }
            .active{background: #41A336; color: #fff; }
        }

        .metro-container{
            .metro-center{display: none; }
            .metro-center-mobile{width: 100%; float: left; overflow: hidden; display: block; padding: 25px 0 0 0;
                a{ width: 100%; float: left;
                    img{width: calc(100% - 30px); margin-left: 15px;}
                }
            }
        }


        .block-why-info{ padding-bottom: 20px;
            .desktop{display: none;}
            .mobile{display: block; font-size: 16px; line-height: 1.5; padding: 10px 0; margin-bottom: 0; color: #000;
                .under-line{height: 4px; width: 80px; background-color: $primaryColor; display: inline-block; }
            }
            .reason-item{ width: 100%; float: left; border-top: 1px solid #DDD; padding: 15px 15px;
                .reason-img{height: 30px; width: auto; float: left;}
                span{width: calc(100% - 70px); float: left; margin-left: 15px; margin-top: 5px; font-size: 15px; }
                i{float: right; font-size: 20px; margin-top: 5px;}
                .reason-title{font-weight: bold; font-style: italic;}
            }
        }

        .block-easy-info{width: 100%; min-height: 300px;
            .center-container{float: left; width: 100%; padding: 20px 15px;
                .left-container{width: 100%;
                    h1{font-size: 16px; margin-top: 0; font-weight: bold;}
                    p{width: 100%; font-size: 14px;}
                    .home-easy-img{ width: 100%; float: left;
                        a{ width: 17%;  margin-right: 3%; display: block; float: left;
                           img{width: 100%; height: auto;}
                        }
                        a:last-child{ width: 27%; height: auto; margin-left: 2%;}
                    }

                }
                .right-container{display: none;}
            }
        }

        .block-buy-kh {height: auto; padding-bottom: 0; padding-top: 25px;
            .center-container{width: 100%; float: left;
                .course-header{font-size: 16px; padding-top: 0; margin-top: 0; padding-bottom: 10px; margin-bottom: 10px; color: #000;
                    .under-line{height: 4px; width: 80px;}
                }
                .dmr-carousel{padding-top: 0; margin-top: 0;
                    .slick-dots{top: 0;}
                }
                .carosel-item{
                    .course-container{width: calc(100% - 20px) !important; margin-left: 10px;
                        .course-item-container{width: 100%; height: 80px;
                            .course-left { height: 100%; width: 80px; padding: 5px; float: left; display: flex; align-items: center;
                                .course-name-box {width: 100%; text-align: center; font-weight: bold;
                                    .course-name {color: #fff; margin-top: 10px; margin-bottom: 0; width: 100%; font-size: 12px; font-style: italic;}
                                    .under-name {color: #fff; font-size: 20px; margin-top: -5px; }
                                }
                            }
                            .course-right {float: left; width: calc(100% - 80px); height: 100%;
                                .course-info {width: calc(100% - 90px); float: left; align-items: center; text-align: left; justify-content: center; display: flex;
                                    .info-box {width: 100%; text-align: right; padding-top: 10px;
                                        i{font-size: 10px; font-weight: 700; text-transform: uppercase; }
                                        .course-price {margin: 0; width: 100%; font-size: 25px; font-weight: 900;
                                            @media only screen and (max-width: 320px) { font-size: 20px; }
                                        }
                                    }
                                }
                                .course-action {height: 100%; width: 80px; margin-left: 10px; float: left; align-items: center; padding-top: 15px;
                                    .btn-buy {width: 70px; height: 45px; color: #fff; font-size: 12px; font-weight: 800; float: left; padding: 5px 5px;
                                        word-break: break-all; text-align: center;
                                        .mb{display: inline;}
                                        .pc{display: none;}
                                    }
                                }
                            }
                        }
                        .combo{
                            .course-left{
                                 .under-name {font-size: 13px !important; }
                            }
                        }
                    }
                }
            }
    		.slick-next { display: none !important; }
    	}
		.nav-tabs { margin-top: 10px;
            .tab-item {
			     margin-right: 10px;
			     a { font-size: 18px; }
            }
		}

		.block-all-courses .center-container {
            .see-more-course { font-weight: normal; }
            .from-us-span { display: none; }
			.course-header {font-size: 16px; margin-bottom: 0px; padding: 10px 8px 0px 20px; color: #000;
                .under-line{height: 4px; width: 80px;}
            }
	    	.preview-course-container { width: 100%; float: left; padding: 0 15px;
                .grid-item-course{margin-top: 10px; height: calc(((100vw)/2 - 22px)*0.62);
                    .course-detail a b {overflow: hidden; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical;
                    }
                }
                .head-title { font-size: 16px; margin-top: 20px;
                    .head-title-left {float: left; color: #444; font-size: 15px;
                        .for-newbie { display: none; }
                    }
                }
                .see-more-link {
                    .see-more { font-size: 16px; font-style: italic; }
                }
                .courses-linear { margin-top: 5px; padding: 0;
                    .grid-item-course:nth-child(2) {display: none;}
                    .course-detail{ height: 30px; margin-top: -30px;
                        .arthor { padding: 2px 5px 5px 5px; width: calc((100vw)/2 - 43px); }
                        img {display: inline; width: 16px; height: 16px; padding-top: 4px; padding-left: 5px; float: left; }
                    }
                    .grid-item-course {width: calc((100vw)/2 - 22px); float: left;
                        &:hover {
                            .hover-to-play { display: none; }
                        }
                        .course-thumbnail { height: calc(((100vw)/2 - 22px)*0.62); }
                        .course-detail .name {padding: 8px 5px 3px 5px; font-size: 13px;
                        }
                    }
                }
            }
        }

        .block-student-feedback { min-height: 500px; overflow-x: hidden !important;  padding-bottom: 30px;
            .center-container {
                .img-student { display: block; }
            }
            .student-feedback-title { padding: 10px 15px 0 15px; font-size: 16px; color: #000;
                .under-line{height: 4px; width: 80px;}
                .student-feedback-title-top { font-size: 16px; padding-bottom: 0; margin-bottom: 0;
                     .bigger{font-weight: bold; font-size: 16px;}
                }
                .student-feedback-title-bottom { font-size: 22px; margin-top: 10px;
                    span { font-size: 34px; }
                }
            }
            .student-feedback-container { padding-top: 10px; padding-bottom: 10px;

                .slick-next { display: none !important; }
                .slick-prev { display: none !important; }
				.student-feedback-slider { padding: 10px 25px 10px 25px;
					.slick-list { overflow: inherit !important;
                        .slick-track { // width: 97% !important;
                            .student-feedback-item { margin: 0 10px;
                                .student-info-avatar{width: 50px; height: 50px; float: left; padding: 0;
                                    img{width: 50px; height: 50px; margin-left: 15px;}
                                }
                                .student-info{ width: calc(100% - 80px); float: right; margin-right: 0; padding-right: 0;
                                    .student-name{margin-top: 0 !important;}
                                }
                            }
                        }
                    }

				}
			}
            .student-feedback-container .student-feedback-slider .slick-list .slick-track .student-feedback-item .student-info-box .student-name{
                margin-top: 10px;
            }
        }

        .block-form-consultation{ width: 100%; float: left; padding-bottom: 50px;
            .left-container{display: none;}
            .right-container{width: 100%; float: left;
                .form-resgister-for-support { width: calc(100% - 30px); margin-left: 15px; margin-top: 35px;
                    .register-form-title{ margin-top: 0;
                        h2{font-size: 22px;}
                    }
                }
            }
        }


        .block-news-container { padding-bottom: 30px;
            .information-update {width: 100%; margin: 20px auto 0 auto;
                h3 {font-size: 16px; margin-bottom: 0; text-align: center; margin-top: 10px; color: #000;
                    .under-line{height: 4px; width: 80px;}
                }

                .news-container{width: 100%; float: left;
                    .col{float: left; width: 100%; padding: 0 15px; margin-top: 30px;
                        .news-ct-item{ width: 100%; float: left; margin-bottom: 10px;
                            .reason-img{width: 40px; margin-right: 10px;}
                            // .reason-title{text-transform: uppercase; font-size: 15px;}
                        }
                        .item-featured{float: left; width: 100%; margin-top: 20px; margin-bottom: 20px;
                            img{width: 140px; height: 95px; object-fit: cover; float: left; margin-right: 10px;}
                            .featured-title{width: calc(100% - 150px); float: left; color: #111; margin-top: 0; font-size: 16px;}
                            .brief{width: calc(100% - 150px); float: left; margin-top: 5px; opacity: 0.6; display: -webkit-box; line-height: 1.2; -webkit-line-clamp: 3;
                                max-height: calc(14px * 1.1 * 3); font-size: 14px; overflow: hidden; text-overflow: ellipsis;
                            }
                        }
                        .title-blog-area {float: left; width: 90%; margin-top: 10px;
                            i{float: left; font-size: 12px;}
                            .title-blog { display: block; font-size: 14px; color: #222; float: left;  padding: 0; margin: 0;
                                margin-left: 20px; width: calc(100% - 15px); margin-top: -15px;

                            }
                        }
                        .view-all{width: 100%; float: left; text-align: right; padding-right: 40px;}
                    }
                    // .col-3{display: none;}
                }
            }
        }


        .resgister-for-support {
            padding: 40px 15px 50px 15px;
            min-height: auto;

            .register-form-title {
                font-size: 18px;
            }
            .resgister-for-support-title {
                .dungmori-title {
                    font-size: 21px;
                }
                .detail-title {
                    font-size: 18px;
                }
            }
        }

        .info-update-container .information-update {
            display: inline-block;
            width: 100%;
            padding: 10px 15px 35px 15px;
            border-bottom: 1px solid #66a532;
            font-size: 14px;
            .lesson-link {
                display: inline-block;
                margin-bottom: 5px;
            }
            .title {
                margin-top: 15px;
                margin-bottom: 15px;
                text-align: center;
                font-size: 27px;
                color: black;
            }
            .title-blog-area {
                width: calc(100vw - 125px);
                margin-bottom: 0;
            }
            .title-blog {
                width: calc(100vw - 164px);
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                margin: 0;
                font-size: 14px;
            }
            .time {
                color: #ddd;
                float: right;
                position: absolute;
                right: 15px;
                margin-top: -1px;
                margin-bottom: 0;
            }
            .fa-book { float: left; margin-right: 5px; }
            .label-danger { position: absolute; right: 92px; margin-top: -17px; }
            a {
                width: 100%;
            }
            p {
                margin-top: 7px;
            }
        }
		.fb-comments-container{ padding: 0 15px;
            .col-md-4{padding: 0;
                iframe {width: 100% !important; height: 250px !important;}
            }
            .center-container .row-content-fb {
    			margin: 0;
    			.col-content {
    				padding: 0;
    			}
            }
		}
    }

    .popup-nihongozin-app {
        width: 92%;
        min-height: 250px;
        .popup-nihongozin-app-content {
            height: auto;
            .get-app-text {
                height: auto;
                padding: 10px 0 0 0;
            }
            .get-app-icon {
                margin-top: 15px;
                .android-app-img {
                    margin-top: 5px;
                }
            }
        }
    }
}
@media only screen and (max-width: 1480px) and (min-width: 1366px ) {
    .home {
        &-feedback {
            &__slide {
                &-content {
                    left: 7%;
                }
            }
        }
    }
}
@media only screen and (max-width: 1366px) {
    .home {
        &-feedback {
            &__slide {
                &-image {
                    &:hover {
                        transform: scale(2) translateY(-20px);
                    }
                }
                &-content {
                    left: 7%;
                }
            }
        }
        &-header {
            &__slider {
                &-wrapper {
                    bottom: 60px;
                    right: 0;
                }
                &-nav {
                    position: absolute;
                    bottom: calc(12vw + 70px);
                    font-family: Montserrat, sans-serif;
                    font-size: 25px;
                    font-weight: 800;
                }
            }
            &__slide {
                &-inner {
                    border-radius: 10px;
                    overflow: hidden;
                }
                &-image {

                }
            }
            &__slogan {
                bottom: 10px;
            }
        }
    }
}
@media only screen and (max-width: 1200px) {
    .home {
        &-preview {
            height: 350px;
        }
    }
}
@media only screen and (max-width: 1024px) {
    .pc {
        display: none;
    }
    .mobile {
        display: flex;
    }
    .home {
        &-header {
            &__slider {
                &-wrapper {
                    bottom: 60px;
                    left: 0;
                    width: 100vw;
                }
                &-nav {
                }
            }
            &__slide {
                &-inner {
                    max-width: 95vw;
                    height: 25vw;
                }
                &-image {

                }
            }
            &__slogan {
                bottom: 10px;
            }
        }

        &-products {
            padding: 70px 50px;
        }

        &-analysis {
            flex-flow: column;
            align-items: center;
            &__item {
                margin-right: 0;
                margin-top: 70px;
            }
        }
        &-philosophy {
            &__text {
                width: 100%;
            }
            &__icon {
                max-width: 90px;
            }
        }
        &-news {
            flex-flow: column;
            &__column {
                width: 100%;
                &:not(:first-child) {
                    margin-top: 20px;
                }
            }
            &__item {
                &-desc {
                    font-size: 18px;
                }
                &-thumbnail {
                    display: flex;
                    justify-content: center;
                }
            }
        }
    }
}
@media only screen and (max-width: 767px) {
    .bookmark-ribbon {
        font-size: 21px;
    }
    .home {
        &-feedback {
            &__slide {
                &-image {
                    &:hover {
                        transform: scale(2) translateY(-20px);
                    }
                }
                &-content {
                    left: 7%;
                }
            }
        }
        &-partner {
            margin-top: 100px;
            &__list {
                justify-content: center;
                grid-template-columns: repeat(2, 1fr);
            }
        }
        &-header {
            &__slider {
                &-wrapper {
                    width: 100vw;
                }
            }
            &__slide {
                &-inner {
                    max-width: 95vw;
                    height: 30vw;
                }
                &-image {

                }
            }
            &__slogan {
                font-size: 10px;
                bottom: 10px;
            }
        }
        &-products {
            padding: 70px 0;
        }
        &-title {
            &__name {
                font-size: 14px;
            }
            &__desc {
                font-size: 15px;
            }
            &__icon {
                display: none;
            }
        }
        &-preview {
            display: none;
        }
        &-tab {
            &__inner {
                font-size: 12px;
                padding: 6px;
            }
        }

        &-news {
            &__item {
                &-desc {
                    font-size: 12px;
                }
            }
        }
        &-blog {
            flex-flow: column;
            &__column {
                width: 100%;
            }
        }
        &-youtube {
            flex-flow: column;
            &__column {
                width: 100%;
            }
        }
    }
}
@media only screen and (max-width: 600px) {
    .home {
        &-header {
            @include aspect-ratio(1, 1);
            &__slider {
                &-wrapper {
                    bottom: 15vw;
                }
            }
            &__slide {
                &-inner {
                    max-width: 95vw;
                    height: 55vw;
                }
                &-image {

                }
            }
            &__slogan {
                font-size: 10px;
                bottom: 10px;
            }
        }
    }
}
