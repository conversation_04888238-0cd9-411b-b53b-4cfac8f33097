@import "variables";
@import "mixins";

@media only screen and (max-width: 1200px) {
    .teacher-detail {
        &__avatar {
            width: 300px;
            height: 300px;
            padding: 20px;
        }
    }
}
@media only screen and (max-width: 768px) {
    .teacher-detail {
        &__info {
            flex-flow: column;
            padding: 20px;
        }
        &__avatar {
            width: 200px;
            height: 200px;
            padding: 15px;
            margin-top: 130px;
        }
        &__overview {
            margin-top: 30px;
            margin-left: 0;
        }
        &__screen {
            &--slider {
                &:before {
                    padding: 0;
                    position: static;
                }
                &-inner {
                    position: static;
                }
            }
        }
    }
    .teacher__screen--wrapper{width: 100%; float: left; margin-top: 0;
        .teacher__screen--slider-avatar-mask{width: 45%; left: 15px;}
        .teacher__screen--slider-avatar{width: 40%; left: 15px;
            img{max-width: 115%; width: 115%;}
        }
        .teacher__screen--slider-info{width: 52%; font-size: 16px; bottom: 40%;
            .teacher__screen--slider-title{font-size: 16px; line-height: 1.3; margin-bottom: 10px;}
            .teacher__screen--slider-description{font-size: 12px; line-height: 1.3;}
        }
        .teacher__screen--slider-buttons{bottom: 10%; a{font-size: 13px;} width: 52%; display: initial;
            .teacher__screen--slider-button-detail{display: block; width: 60%; float: left; margin-right: 0 !important; text-align: center; padding-top: 4px; height: 26px;}
            .teacher__screen--slider-button-video{display: block; width: 100%; float: left; padding-top: 10px; padding-left: 0;
                .fa-2x{font-size: 14px !important;}
                .mr-5{margin-right: 4px !important;}
            }
        }
        .owl-dots{
            .owl-dot{width: 10px !important; height: 10px !important; margin: 0 8px !important;}
        }
    }

    .teacher__container{width: 100%; float: left; margin-bottom: 30px;
        .teacher__cards--feature{width: 100%; float: left;}
        .teacher__text--title-medium{font-size: 14px;}
        .teacher__text--content{background: transparent; font-size: 14px; line-height: 1.3; padding: 0 15px;}
    }


    .teacher__cards--feature{display: initial;
        .teacher__card--image{height: 80%; top: -80%;}
        .teacher__card--info{height: 50%;}
        .teacher__card--feature{ width: calc(50% - 25px); height: 450px; min-width: auto; margin: 0 0 0 17px; float: left;
            .teacher__card--full-name{font-size: 14px; height: 12%;}
            .teacher__card--desc-short{font-size: 11px; line-height: 1.1; font-weight: 400;}
            .teacher__card--desc{padding: 10% 10% 12px 10%;}

            .teacher__card--button{ display: block; font-size: 14px;
                .teacher__card--button-detail{display: block; width: 100%; float: left; margin-right: 0 !important; text-align: center; padding-top: 4px; height: 26px; font-size: 14px;}
                .teacher__card--button-video{display: block; width: 100%; float: left; font-size: 11px; padding: 0; padding-top: 15px;
                    .fa-2x{font-size: 14px !important;}
                    .mr-5{margin-right: 4px !important;}
                }
            }
        }
    }

    .teacher__cards--normal{width: 100%; float: left; display: block; margin-top: 20px; float: left; width: 100%;
        .teacher__card{width: calc(50% - 25px); height: 450px; min-width: auto; margin: 0 0 20px 17px; float: left;
            .teacher__card--full-name{font-size: 14px !important; height: 12%;}
            .teacher__card--desc-short{font-size: 11px; line-height: 1.1; font-weight: 400;}
            .teacher__card--desc{padding: 10% 10% 12px 10%;}

            .teacher__card--button{ display: block; font-size: 14px;
                .teacher__card--button-detail{display: block; width: 100% !important; float: left; margin-right: 0 !important; text-align: center; padding-top: 4px; height: 26px; font-size: 14px;}
                .teacher__card--button-video{display: block; width: 100% !important; float: left; font-size: 11px; padding: 0; padding-top: 5px;
                    .fa-2x{font-size: 14px !important;}
                    .mr-5{margin-right: 4px !important;}
                }
            }
        }
    }

    .teacher__subscribe{width: 100%; float: left;
        .teacher__subscribe--content{display: block; text-align: center;
            a{display: inline-block;}
        }
        .teacher__subscribe--title{font-size: 18px; width: 100%; float: left; padding:30px 20px 10px 20px; line-height: 1.4;}
    }



    // detail teacher
    .teacher-detail__screen--wrapper{margin-top: 0; padding-top: 0;

        .teacher-detail__screen--info-content{width: 92%; padding-top: 90px;}
        .teacher-detail__screen--info-fullname{font-size: 16px; background: transparent; top: 0;
            span{ }
            .teacher-name-bg{ background: rgba(17, 60, 12, 0.5); position: absolute; left: 0; font-size: 20px !important; width: 280px; margin-top: 80px; padding: 15px 15px; }
        }
        .teacher-detail__screen--info-biography{width: 135%; bottom: 14px; left: 99%;
            .teacher-name{display: none;}
        }
        .teacher-detail__screen--info-biography-content{background: rgba(0, 0, 0, 0.3); font-size: 14px; line-height: 1.3; padding: 10px;
            span{font-size: 11px !important; line-height: 1.2; br{}}
        }
        .teacher-detail__screen--info-avatar{
            img{width: 85%;}
        }

        .teacher-detail__screen--info-content{width: 100%; border-radius: 0; margin-top: 0;}


        .teacher-detail__screen--story{width: 100%; padding: 30px 0; margin-top: 0;
            .teacher-detail__screen--story-header{
                span{font-size: 20px; text-transform: uppercase; font-weight: bold;}
                .teacher-detail__screen--separate{width: 40vw; border-width: 6px;}
            }
            .teacher-detail__screen--timeline-content{font-size: 13px; line-height: 1.4; font-weight: 300;
                ul { li{padding: 20px;} }
            }
        }

        .teacher-detail__screen--comments{width: 100%; padding: 20px 15px; margin-top: 0;
            .teacher-detail__screen--comments-header{ margin-bottom: 20px;
                span{font-size: 20px; text-transform: uppercase; font-weight: bold;}
                .teacher-detail__screen--separate{width: 40vw; border-width: 6px;}
            }
            .li-tab{ a{font-size: 13px;}}
            .list-comments .comment-item .comment-content .name{width: 100%;}
        }
    }
}
