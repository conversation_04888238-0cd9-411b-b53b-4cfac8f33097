@media only screen and (max-width: 768px) {
    .main {
        .page-detail-container{width: 100%;
            .page-detail-info{width: 100%; float: left;}
        }
        .main-page-center { padding: 0 18px;
            .page-detail-container { width: 100%;
                .page-detail-title {
                    margin: 20px 0 10px 0;
                }
                .page-detail-info { width: 100%; }
                .page-detail-content .main-content { text-align: justify;
                    p { text-align: justify; }
                    span { text-align: justify; }
                    img{height: auto !important; width: 100% !important; }
                }
                .comment-container .comment-heading span { font-size: 21px; }
                .comment-container .fb-comments { margin: 0; }
            }
        }
    }
}

@media only screen and (max-width: 481px) {

}