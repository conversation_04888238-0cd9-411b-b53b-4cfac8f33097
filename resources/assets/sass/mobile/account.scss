@media only screen and (max-width: 768px) {
    .main {
        width: 100%;
        .main-center {
            width: 100%;
            .main-user-left {
                background-color: #EEE;
            }
            .main-user-left {width: 100%; min-height: 0; float: none; padding: 0;
                .clear-preview-upload { margin-top: 4px; margin-left: calc((100vw - 125px)/2); }
                .change-avatar {margin: 10px 0 10px 0; width: 105px; margin-left: calc((100vw - 110px)/2); padding: 3px 10px;
                    .change-avatar-btn-mobile { display: inline; }
                    .change-avatar-btn-desktop { display: none; }
                }
                .save-avatar { display: inline; margin: 10px 0 10px 0; width: 105px; margin-left: calc((100vw - 110px)/2); }
                .account-left-menu{ float: left; width: 100%; padding: 0 15px; border-bottom: 1px solid #EEE;
                    .item-user {width: 20%; margin-top: 8px; text-align: center; 
                        i{font-size: 20px;}
                        span {display: none;}
                    }
                }
                .current{border-bottom: 2px solid #588d3f;}
                .logout-tab { display: none; }
                .user-avatar-container {
                    .user-avatar {width: 105px; height: 105px; margin-left: calc((100vw - 110px)/2); margin-top: 15px;}
                }
            }
            .main-user-right {width: 100%; padding: 0; min-height: 550px; padding: 0 15px;
                
                #user-info-table { width: 100%; 
                    .desc{ position: absolute; margin-top: -6px; font-size: 10px; border: none; display: block; background: #fff; padding: 2px 3px 2px 0; width: auto; text-transform: uppercase; }
                }
                .captcha-active-course { margin-top: 5px; }
                .change-captcha-icon { margin-top: 18px; }
                .table-box { width: 100%; overflow-x: auto; }
                .captcha-container { margin-left: 0; }
                .user-form-item { padding-left: 0;
                    .user-form-input { min-width: auto;  }
                    .send-mail-verify-email { margin-top: 5px; }
                }
                input, select, textarea {border: 1px solid #DDD;}
                .main-user-title { padding-bottom: 15px; font-size: 18px; font-weight: 700; width: 100%; }
                .mark-as-readed {padding-top: 5px; padding-bottom: 15px; }
                .dm-tab { height: auto;
                    .tab-item {text-align: center; height: auto; width: 45%; margin: 0; padding: 5px; border: 0; 
                        i { display: none }
                    }
                    .review-more-courses { width: 100%; }
                }
                .tab-content-container {
                    .bought-courses-container { width: 100%;
                        .notification-empty-container { padding: 130px 0; }
                        .course-item {width: 47%; margin: 0 3px 10px 3px; 
                            .images { width: 100%;
                                a img { width: 100%; }
                            }
                        }
                    }
                }
                .notification-item{
                    .user-form-item{width: calc(100vw - 70px); padding-left: 20px; padding-right: 0;
                        i {margin-left: -20px;}
                    }
                    .notifi-info{width: 70px; padding-left: 10px;}
                }
                .bought-courses {
                    margin-top: 30px;
                }
                .bought-other-courses {
                    margin-bottom: 20px;
                }
                .paginate-container {
                    width: 100%;
                }
                .course-test{
                    #reviewTestResult {
                        .test-detail-result {
                            max-width: calc(100vw - 50px) !important;
                        }
                        .test-modal-dialog {
                            width: auto;
                        }
                        .question-box { 
                            .answer-box {
                                div:last-child {
                                    hr { top: -15px; }
                                }
                                .three-line-answer { height: auto; }
                                .two-line-answer { height: 30px; }
                                .question-answer .labels {
                                    display: inline-block;
                                    max-width: 100%;
                                    white-space: normal;
                                    text-align: left;
                                    line-height: 1.5;
                                }
                                .result-answer-area {
                                    padding-left: 0;
                                    padding-right: 0;
                                }
                                audio {
                                    width: 100%;
                                }
                                img { max-width: 100%; height: auto !important; }
                            }
                            img { max-width: 100%; height: auto !important; }
                        }
                    }

                    .review-test-result { position: static; margin-top: 10px; float: none; }
                    .remove-test-result { position: static; margin-top: 10px; }
                }
            }
        }
    }
}

@media only screen and (max-width: 370px) {
    .main {
        .main-center {
            .main-user-right {
                #user-info-table {
                    .form-text-email { font-size: 14px; }
                }
            }
        }
    }
}
