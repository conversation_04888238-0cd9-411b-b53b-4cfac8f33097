@import "variables";
@import "mixins";

@media only screen and (max-width: 1366px) {
  .main {
    .book {
      &__list {
        &-navigation {
          width: 82%;
        }
      }
    }
    .new-release {
      &__box {
        .book__list-navigation {
          max-width: 60%;
        }
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .main {
    .main-book {
      .new-release {
        @include aspect-ratio(1046, 1200);
        background: none;
      }
    }
    .book {
      &__navigation {
        margin-top: 180px;
        &-items {
          padding: 0;
        }
        &-item {
          padding: 8px 10px;
          font-size: 15px;
        }
      }
      &-detail {
        margin-top: 20px;
        flex-flow: column;
        align-items: center;
        &__carousel {
          width: 80%;
        }
        &__content {
          width: 90%;
          margin-left: 0;
          margin-top: 20px;
        }
      }
      &__relate {
        padding: 0 20px;
        &-items {
          flex-flow: column;
          align-items: center;
        }
        &-item {
          margin: 0;
        }
      }
    }
    .new-release {
      &__item {
        margin: 0;
        margin-top: 20px;
        &-inner {
          padding: 0 10px;
        }
      }
      &__box {
        .book__list-navigation {
          max-width: 95%;
        }
      }
    }
    .audio {
      &__header {
        padding: 0 20px;
      }
      &__list {
        padding: 0 20px;
      }
      &__item {
        flex-flow: column;
        &-filename,
        &-file,
        &-action {
          width: 100%;
          margin-bottom: 10px;
        }
      }
    }
  }
}
