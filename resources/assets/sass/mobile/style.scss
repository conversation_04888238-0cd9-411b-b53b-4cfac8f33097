@import "variables";

@media only screen and (max-width: 1023px) {

    body{width: 100%; float: left; overflow-x: hidden;}
    #application{overflow-x: hidden;}
    .main{overflow-x: hidden;}
    .site-header{display: none;
        .account-container{display: block;}
    }
    #share-for-gift{display: none;}
    .mobile-header {display: block; position: fixed; height: 60px; padding-top: 5px; top: 0;
        float: left; width: 100%; z-index: 100; background-color: #fff;
        #nav-icon {
            margin-left: 8px; margin-top: 2px;
            width: 45px; height: 45px; float: right; -webkit-transform: rotate(0deg);
            -moz-transform: rotate(0deg);  -o-transform: rotate(0deg);  transform: rotate(0deg);  -webkit-transition: .1s ease-in-out;
            -moz-transition: .1s ease-in-out;  -o-transition: .1s ease-in-out;  transition: .1s ease-in-out;  cursor: pointer;
            .nav-span {
                display: block;  position: absolute;  height: 2px;  width: 18px;  background: #fff;  border-radius: 2px;  opacity: 1;  left: 0;
                -webkit-transform: rotate(0deg);  -moz-transform: rotate(0deg);  -o-transform: rotate(0deg);  transform: rotate(0deg);
                -webkit-transition: .25s ease-in-out;  -moz-transition: .25s ease-in-out;  -o-transition: .25s ease-in-out;  transition: .25s ease-in-out;

                &:nth-child(1) { left: 9px;  top: 16px;  -webkit-transform-origin: left center;  -moz-transform-origin: left center;
                    -o-transform-origin: left center;  transform-origin: left center; }
                &:nth-child(2) { left: 9px;  top: 22px; -webkit-transform-origin: left center;  -moz-transform-origin: left center;
                    -o-transform-origin: left center;  transform-origin: left center; }
                &:nth-child(3) { left: 9px; top: 28px;  -webkit-transform-origin: left center;  -moz-transform-origin: left center;
                    -o-transform-origin: left center;  transform-origin: left center; }
            }

            .dropdown-menu {
                margin-top: 4px;
                width: calc(100vw);
                margin-left: calc(-100vw + 54px);
                height: calc(100dvh - 55px);
                --webkit-height: calc(100vh - 55px);
                font-size: 16px; padding-bottom: 10px; border-top-left-radius: 0; border: 0; box-shadow: none;
                li{ padding: 7px 0;
                    svg {
                        margin-right: 15px;
                    }
                    a { color: #fff; padding-left: 15px;display: flex;align-items: center;font-family: Montserrat, Arial, sans-serif; font-weight: 500; font-size: 15px;
                        p {margin-bottom: 0;}
                    }
                }
                li:last-child { border: 0; }
                .courses-detail-list {
                    padding: 15px 15px;
                    .course-list-table {
                        font-size: 12px;
                        text-align: center;
                        td { border: 1px solid #82ae1d; }
                    }
                    table { margin-bottom: 0; }
                    .course-list-table td {
                        padding: 12px !important;
                    }
                }
                .icon-menu-item { float: left; color: #82ae1d; width: 19px; height: 19px; margin-right: 10px; }
            }

            $midnight: #2c3e50;
            $clouds: #ecf0f1;

            /* Accordion styles */
            .tab {
                width: 100%; color: white; overflow: hidden;
                &:not(:first-child) { border-top: 1px solid #eee; }
                &-label {display: flex; justify-content: flex-start; cursor: pointer; color: #fff; font-weight: 500; font-size: 15px;padding: 10px 15px; margin-bottom: 0;font-family: Montserrat, Arial, sans-serif;
                    &.child-label {
                        padding-left: 50px;
                    }
                    svg {
                        margin-right: 15px;
                    }
                p{text-align: left; margin-right: 5px}
            }
            &-content {max-height: 0; padding: 0; color: #fff; transition: all .35s;
                a{padding: 0 5px 10px 5px; p{padding-left: 20px; margin-top: -10px;} color: #fff; font-size: 16px; font-family: Montserrat, Arial, sans-serif; font-weight: 500;
                    &.child-label {
                     .content-label { display: flex; justify-content: flex-start; align-items: center;padding-left: 50px;}
                    }
                    .free{ padding: 5px 10px; margin-left: 10px;border-radius: 3px; font-size: 10px; background-color: #FB6D3A; color: #fff; font-weight: 500}
                }
            }
              &-close {display: flex; justify-content: flex-end; padding: 1em; font-size: 0.75em; cursor: pointer; }
              input {position: absolute; opacity: 0; z-index: -1; }
              .label-account::after{display: none;}
              .account-expand{height: calc(100vh - 100px);
                i{margin-right: 10px;}
              }
              .user-info-box{ width: 100%; margin-top: 10px;
                .user-avatar-circle{border-radius: 50%; width: 45px; height: 45px; float: left;  border: 2px solid #000;}
                  .user-name {
                      font-family: Montserrat, Arial, sans-serif;
                  }
                div{float: left; margin-left: 10px;
                    p{margin: 5px 0 -5px 0;font-weight: bold;}
                    span, i{font-size: 11px; margin-top: -4px; color: #41A336;font-weight: 500;}
                }
              }
            }

            input:checked {
              + .tab-label {
                &::after { transform: rotate(90deg); }
              }
              ~ .tab-content {max-height: 100vh;}
            }



        }

        #nav-icon.open{
            span:nth-child(1) {
                -webkit-transform: rotate(45deg);  -moz-transform: rotate(45deg);  -o-transform: rotate(45deg);
                transform: rotate(45deg);  top: 16px;  left: 13px;
            }
            span:nth-child(2) {  width: 0;  opacity: 0;}
            span:nth-child(3) {
                -webkit-transform: rotate(-45deg);  -moz-transform: rotate(-45deg);  -o-transform: rotate(-45deg);
                transform: rotate(-45deg);  top: 29px;  left: 13px;
            }
        }

        .logo {padding: 0; height: auto; margin: 0; float: none;
            img {height: 26px; margin-right: 0; margin-top: 13px; margin-left: 15px; }
        }
        .nav-toggle-menu { display: none; }
        .caret { display: none; }

        .account-container{float: right; padding: 0;
            .text-login{float: right; margin-right: 20px; margin-top: 15px; outline: none; font-size: 14px; font-family: Roboto;
                &:hover{ color: #111; }
                @media only screen and (max-width: 320px) {
                    font-size: 14px; margin-right: 10px;
                }
            }
            .text-register{ cursor: pointer; margin-right: 3px; outline: none; float: right; font-size: 14px; font-family: Roboto;
                &:hover{ color: #111; }
                @media only screen and (max-width: 320px) {
                    font-size: 14px;
                }
            }

            .profile-no-login-icon {display: none;}

            .auth-container{cursor: pointer; padding: 10px 0; max-width: 250px; text-align: right; margin-top: 5px;
                .messenger-icon{display: inline; float: left; padding: 0 10px;
                    img{width: 19px; height: 19px;  vertical-align: top; opacity: 0.8; margin-top: 1px;}
                    .mess-counts{background-color: #fa3e3e; border-radius: 4px; text-align: center; font-size: 11px; padding: 0 3px 0 3px;
                    color: #fff; float: right; position: absolute; margin: -4px 0 0 -6px; }
                }

                .svgIcon {width: 18px; height: 18px;
                    svg {opacity: 0.7}
                    .noti-counts {background-color: #fa3e3e; border-radius: 4px; text-align: center; font-size: 11px;
                    padding: 0px 3px 0px 3px; color: #fff; float: left; position: absolute; margin: -2px 0 0 15px; opacity: 1;}
                }
            }


            // .auth-container{cursor: pointer; padding: 10px 0; text-align: right;
            //     .user-avatar-circle{width: 30px; height: 30px; border-radius: 50%; object-fit: cover; border: 1px solid #EEE; background: #ccc;}
            //     .user-name{ margin-right: 5px; padding-top: 8px;}
            //     .caret{opacity: 0.7;}
            //     .user-menu{right: -1px; top: 55px; position: fixed; width: 175px; left: unset; border-top-right-radius: 0; border-bottom-right-radius: 0;
            //         .caret-up{position: absolute; float: right; margin-top: -13px; right: 60px;}
            //         li{ width: 100%; float: left; padding: 0;
            //             a{padding: 7px 15px;
            //               i{margin-right: 10px;}
            //             }
            //         }
            //         .dropdown-divider {height: 0; margin: .5rem 0; overflow: hidden; border-top: 1px solid #e9ecef; }
            //     }
            // }
        }

        // .account-container { margin-right: 7px; height: 50px;position: fixed; top: 0; right: 0; padding: 0;
        //     .search-box { display: block; float: left;
        //         .search-icon {color: #588d3f; font-size: 17px; margin: 16px 10px; }
        //     }
        //     .user-menu {
        //         position: fixed;
        //         margin: 0;
        //         left: calc(100vw - 186px);
        //         width: 187px;
        //         li{
        //             a{padding: 10 15px;}
        //         }
        //     }
        //     .profile-no-login-icon {
        //         height: 50px;
        //         display: flex;
        //         justify-content: center;
        //         align-items: center;
        //         button {
        //             background: transparent;
        //             color: #588d3f;
        //             border: 0;
        //             padding: 6px;
        //             font-size: 17px;
        //         }
        //         ul {margin-top: 14px; left: -106px; border-top-right-radius: 0; padding-bottom: 10px;
        //             a{
        //                 p{padding: 10px 0; margin-bottom: 0; font-size: 16px;}
        //             }
        //         }
        //     }
        //     .auth-container {min-width: auto; padding: 7px 0; float: right;
        //         .user-info-box {
        //             display: flex;
        //             align-items: center;
        //         }
        //         .user-name {
        //             margin-top: 5px;
        //             width: calc(50vw - 60px);
        //             overflow: hidden;
        //             white-space: nowrap;
        //             text-overflow: ellipsis;
        //         }
        //         .user-avatar-circle { width: 25px; height: 25px; margin-top: 5px; margin-right: 10px; }
        //     }
        //     .register-box { display: none; }
        //     .login-box { display: none; }
        // }
    }

    .verify-area {
        padding: 10px 15px 0 15px;
        img {
            width: 45%;
        }
    }

    .main .main-user-left .item-user i {
        margin: 0;
    }

    .login-container {
        width: 70%;
        .login-left-container {
            display: none;
        }
        .login-right-container {width: 100%; height: auto;
            .control-label{display: none;}
            .col-md-8{padding: 0 15px !important;
                .break-line{display: block;}
                .btn{width: 100%; padding: 10px auto; margin-bottom: 10px;}
            }
        }
    }

    .fixed-panel{ display: initial !important; bottom: 100px !important;
    }

    .footer { padding-bottom: 10px; width: 100%; float: left; padding: 0;
        .center-container { width: 100%; float: left; padding: 0 15px;

            .general-box{width: 100%; float: left;
                h3{padding-left: 15px;}
                .links {float: left; margin-top: 15px; padding-bottom: 15px; right: 0; width: calc(50vw - 20px); text-align: left; padding-left: 15px; }
            }
            .hiden-mobile{display: none;}

            .general-infomation-box{width: 50%; float: left; padding-left: 15px; }

            .contact-box {padding: 15px 20px; text-align: left; margin-top: 230px;
                .title { margin-top: 20px; width: 0; height: 120px; margin-right: 0; float: left; }
                a { width: calc(100%); float: left; text-align: left; font-size: 14px; padding: 4px 0;
                    p{width: 100%; float: left;}
                }
            }

        }

        .social-box { width: 100%; float: left; display: flex; justify-content: center;
                .business{width: 100%; float: left; text-align: center; display: initial;
                    img{display: inline; float: none; width: 150px; height: auto;}
                }
                .bocongthuong{float: left;}
                .footer-logo{margin-left: 15px; width: 100px; height: auto;}
                h3 { text-align: center; font-size: 14px; }
                .social { text-align: center; float: left; margin-top: 14px;
                    span{float: none;}
                    .nihongo{float: none;}
                    img{width: 30px; height: 30px;}
                }
                .mobile-app {text-align: center; width: 60%; text-align: left; float: left; margin:20px 0 10px 15px; display: initial;
                    img{margin-bottom: 5px; height:27px;}
                }
            }
        .footer-item-mobile {
            padding: 0 15px !important;
        }
        .about-us-box {
            margin-top: 30px;
        }
    }
    .home-featured-popup {width: 100%; padding: 15px; margin: 0; min-height: auto;
        a img {
            width: 100%;
        }
    }
    .user-profile-popup .user-profile-container {
        .cover-container {width: auto;}
        #user-info-table {
            .info-item-email { font-size: 11px; }
        }
    }
    .user-profile-popup {width: 90%; min-height: 75%; margin: 0; }
    .ctrlq.fb-button{width: 40px; height: 40px; bottom: 30px; right: 15px;}
    .go-top{left: 15px !important;}

    //cho phần commnents
    .comment-tab {
        li {
            a {
                font-size: 14px;
            }
        }
    }
    .list-comments{
        .input-comment-container {
            .input-comment {
                height: 66px;
                width: calc(100% - 100px) !important;
                font-size: 15px;
            }
        }
        .comment-item {
            .comment-content {
                width: 100%;
                .reply-container {
                    .child-comment-item{width: calc(100vw - 80px);
                        .comment-content{width: calc(100% - 50px);
                            .child-name{width: 100%; overflow-wrap: break-word;}
                        }
                        .delete-comment{float: right; margin-top: -20px; font-size: 15px;}
                    }
                    .reply-form { width: calc(100vw - 80px);
                        .input-comment{width: calc(100% - 110px) !important;font-size: 15px !important;}
                        .post-comment-btn{float: right; margin-top: 0; padding: 6px 5px;}
                    }
                    .admin-edit-pen {float: right;}
                }
            }
        }
    }
}

@media only screen and (max-width: 481px) {
    .login-container {
        width: 90%;
        .login-left-container {
            display: none;
        }
        .login-right-container {
            width: 100%;
            height: auto;
        }
    }
}

@media only screen and (min-device-width : 768px) and (max-device-width : 1023px){
    body{width: 100%; float: left; overflow-x: hidden;}
    .site-header .header-content {
        position: fixed;
        height: 50px;
        border-bottom: 1px solid #CCC;
        #search-input-mobile {float: right; margin-right: 5px;
            .search-input-mobile {
                width: calc(100vw - 108px); margin: 10px 5px 0 5px; height: 28px; text-indent: 5px; border-radius: 3px; border: 1px solid #CCC;
            }
            .close-icon {color: $currencyColor; padding: 10px; font-size: 15px; }
        }
        .container{
            width: 100%;

            #nav-icon {
                margin-left: 8px;
                width: 45px; height: 45px; float: left; -webkit-transform: rotate(0deg);
                -moz-transform: rotate(0deg);  -o-transform: rotate(0deg);  transform: rotate(0deg);  -webkit-transition: .1s ease-in-out;
                -moz-transition: .1s ease-in-out;  -o-transition: .1s ease-in-out;  transition: .1s ease-in-out;  cursor: pointer;
                .nav-span {
                    display: block;  position: absolute;  height: 2px;  width: 18px;  background: #588d3f;  border-radius: 2px;  opacity: 1;  left: 0;
                    -webkit-transform: rotate(0deg);  -moz-transform: rotate(0deg);  -o-transform: rotate(0deg);  transform: rotate(0deg);
                    -webkit-transition: .25s ease-in-out;  -moz-transition: .25s ease-in-out;  -o-transition: .25s ease-in-out;  transition: .25s ease-in-out;

                    &:nth-child(1) { left: 9px;  top: 16px;  -webkit-transform-origin: left center;  -moz-transform-origin: left center;
                        -o-transform-origin: left center;  transform-origin: left center; }
                    &:nth-child(2) { left: 9px;  top: 22px; -webkit-transform-origin: left center;  -moz-transform-origin: left center;
                        -o-transform-origin: left center;  transform-origin: left center; }
                    &:nth-child(3) { left: 9px; top: 28px;  -webkit-transform-origin: left center;  -moz-transform-origin: left center;
                        -o-transform-origin: left center;  transform-origin: left center; }
                }

                .dropdown-menu {margin-top: 4px; margin-left: -9px; width: calc((100vw)*0.85); max-width: calc(100vw);
                    font-size: 16px; padding-bottom: 10px; border-top-left-radius: 0;
                    li{ padding: 7px 0;
                        a { color: #fff; padding-left: 15px;display: flex;
                            p {margin-bottom: 0;}
                        }
                        border-bottom: 1px solid #eee;
                    }
                    li:last-child { border: 0; }
                    // .courses-detail-list { background: #EEE; padding-top: 0; padding-bottom: 0;
                    //     ul { border-top: 1px solid #EEE; border-bottom: 1px solid #EEE;
                    //         li { padding: 10px 15px;
                    //             a{padding-left: 0;}
                    //             .course-nx { padding: 10px; border: 1px solid #DDD;
                    //                 label { font-weight: normal; }
                    //             }
                    //         }
                    //     }
                    // }
                    .courses-detail-list {
                        padding: 15px 15px;
                        .course-list-table {
                            font-size: 12px;
                            text-align: center;
                            td { border: 1px solid #82ae1d; }
                        }
                        table { margin-bottom: 0; }
                        .course-list-table td {
                            padding: 12px;
                        }
                    }
                    .icon-menu-item { float: left; color: #82ae1d; font-size: 19px; width: 23px; }
                }
            }

            #nav-icon.open{
                span:nth-child(1) {
                    -webkit-transform: rotate(45deg);  -moz-transform: rotate(45deg);  -o-transform: rotate(45deg);
                    transform: rotate(45deg);  top: 16px;  left: 13px;
                }
                span:nth-child(2) {  width: 0;  opacity: 0;}
                span:nth-child(3) {
                    -webkit-transform: rotate(-45deg);  -moz-transform: rotate(-45deg);  -o-transform: rotate(-45deg);
                    transform: rotate(-45deg);  top: 29px;  left: 13px;
                }
            }

            .logo {
                padding: 0;
                height: auto;
                margin: 0;
                float: none;
                margin-left: calc(50vw - 92px);
                img {
                    height: 30px;
                    margin-right: 25px;
                    margin-top: 10px;
                }
            }
            .nav-toggle-menu { display: none; }
            .caret { display: none; }
            .account-container { margin-right: 10px; position: fixed; top: 0; right: 0; padding: 0;
                .search-box { display: block; float: left;
                    .search-icon {color: #588d3f; font-size: 17px; margin: 16px 10px; }
                }
                .user-menu { margin: 0; left: -135px; width: 187px;
                    li{
                        a{padding: 10 15px;}
                    }
                }
                .profile-no-login-icon {
                    height: 50px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    button {background: transparent; color: #588d3f; border: 0; padding: 14px; font-size: 17px; }
                    ul {margin-top: 14px; left: -106px; border-top-right-radius: 0; padding-bottom: 10px;
                        a{
                            p{padding: 10px 0; margin-bottom: 0; font-size: 16px;}
                        }
                    }
                }
                .auth-container {min-width: auto; padding: 7px 0; float: right;
                    .user-name { display: none; }
                    .user-avatar-circle{width: 25px; height: 25px; margin-top: 5px; margin-right: 10px;}
                    .search-input-mobile {  }
                }
                .register-box { display: none; }
                .login-box { display: none; }
            }

        }
    }

    .verify-area {
        padding: 10px 15px 0 15px;
        img {
            width: 45%;
        }
    }

    .main .main-user-left .item-user i {
        margin: 0;
    }

    .login-container {
        width: 70%;
        .login-left-container {
            display: none;
        }
        .login-right-container {width: 100%; height: auto;
            .control-label{display: none;}
            .col-md-8{padding: 0 15px !important;
                .break-line{display: block;}
                .btn{width: 100%; padding: 10px auto; margin-bottom: 10px;}
            }
        }
    }
    .footer { padding-bottom: 10px;
        .center-container {
           .general-box{width: 100%;
                h3{padding-left: 15px;}
           }
        }
    }
    .home-featured-popup {width: 100%; padding: 15px; margin: 0; min-height: auto;
        a img {
            width: 100%;
        }
    }
    .user-profile-popup .user-profile-container {
        .cover-container {width: auto;}
        #user-info-table {
            .info-item-email { font-size: 11px; }
        }
    }
    .user-profile-popup {width: 90%; min-height: 75%; margin: 0; }
    .ctrlq.fb-button{width: 40px; height: 40px; bottom: 30px; right: 15px;}
    .go-top{left: 15px !important;}

    //cho phần commnents
    .list-comments{
        .comment-item {
            .comment-content {
                .reply-container {
                    .child-comment-item{width: calc(100vw - 80px);
                        .comment-content{width: calc(100% - 50px);
                            .child-name{width: 100%; overflow-wrap: break-word;}
                        }
                        .delete-comment{float: right; margin-top: -20px;}
                    }
                    .reply-form { width: calc(100vw - 80px);
                        .input-comment{width: calc(100% - 50px); float: right; margin-right:0;}
                        .post-comment-btn{float: right; margin-top: 10px;}
                    }
                }
            }
        }
    }
}

