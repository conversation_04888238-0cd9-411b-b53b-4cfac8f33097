
.ftext-mb{display: none;}
@media only screen and (max-width: 768px) {
    .break-line { display: none; }
    .main .main-center {
        .main-support-left {width: 100%; padding: 30px 15px 15px 15px; min-height: auto; 
            .item-question-heading { color: green; margin-bottom: 5px; font-weight: bold; }
            .item-question {width: 100%; padding: 10px 0; color: #555; line-height: 24px; 
                margin: 0; font-size: 14px;
                &:first-child{ border-top: 1px solid #ced0d4;}
            }
            .active { color: green;}
        }
    }

    .main .main-support-right {width: 100%; padding: 10px 15px 0 15px; border-left: none;
        .block-hotro {
            .block-content {margin: 0; padding-top: 10px; 
                .support-right {padding: 0; margin-top: 40px;}
            }
        }
        .block-contact{padding: 0;}
        .support-detail-container {
            .support-detail-title {margin-top: 5px; font-size: 23px; }
            .support-detail-content .main-content {padding-bottom: 10px; padding-top: 0px; text-align: justify;
                p { text-align: justify; }
                img{height: auto !important; }
            }
        }
        .support-detail-container { width: 100%;
            .comment-container .comment-heading span{font-size: 21px; }

            .support-detail-info {
                width: 100%;
            }
        }
    }

    .main .loader-area {
        .loader {
            margin-top: calc((100vh - 100px)/2);
            border: 2px solid white;
            border-top: 2px solid #3498db;
            width: 50px;
            height: 50px;
        }
    }



    .feedback-page{width: 100%; float: left;
        .section-title{h2{font-size: 20px;} margin-top: -20px;}
        .row{width: 100% !important; float: left;
            .col-md-8{left: 0 !important;}
            .ftext{width: 100%; float: left; display: none;}
            .ftext-mb{display: block;}
        }
        #comment-container{border: none !important; padding: 20px 15px; width: 100vw !important; margin-left: -15px;}
    }


}

@media only screen and (max-width: 481px) {
}