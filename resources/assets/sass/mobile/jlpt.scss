@media only screen and (max-width: 768px) {

    .main { width: 100%;
        .main-exam-menu{width: 100%; float: left; padding: 20px 0 10px 0;
            .exam-container-menu{ width: 100%; margin: 0 auto;
                .item-exam{ width: 33%; margin: 0; padding-bottom: 0;}
                .active { box-shadow: none; color: #588d3f; padding: 15px 5px;
                    img{height: 45px;}
                }
            }
        }
        .main-exam-right { padding: 0 1px; min-height: 210px; width: 100%; margin-top: 0;

            .requre-auth{width: calc(100% - 30px); margin-left: 15px; padding: 10px;}
            .mobile-hidden{display: none;}
            .guest-cover-container{ 
                h3 { font-size: 14px; }
                .btn-register{ padding: 2px 6px; }
                .btn-login { padding: 3px 10px; }
            }
            .table-box { overflow-x: auto; width: 100%;
                .dashboard { margin-top: 11px; max-width: fit-content; margin-bottom: 25px;
                    .heartbit { position: absolute; }
                }
            }
            .dashboard{
                #exam_0 {width: 50px;}
                #exam_1 {width: 64px;}
            }
            .notification{ margin-top: 2px; width: calc(100% - 30px); margin-left: 15px; padding: 10px; }
            .no-auth-notifi { width: calc(100% - 30px); margin-left: 15px; padding: 10px; margin-bottom: 0; margin-top: 15px;
                h3{line-height: 150%;}
            }
        }
        
    }


    .main { width: 100%;
        .main-center {  width: 100%;
            .main-ranking-menu{width: 100%; float: left; padding: 20px 0 10px 0;
                .exam-container-menu{ width: 100%; margin: 0 auto;
                    .item-exam{ width: 33%; margin: 0;}
                    .active { box-shadow: none; color: #588d3f; padding: 15px 5px;
                        img{height: 45px;}
                    }
                }
            }
            .main-ranking-right { padding: 0 1px; min-height: 210px; width: 100%; margin-left: 0; margin-top: 0;
                .guest-cover-container{ 
                    h3 { font-size: 14px; }
                    .btn-register{ padding: 2px 6px; }
                    .btn-login { padding: 3px 10px; }
                }
                .year-exam{ margin-left: 3px; }
                .month-exam, .year-exam, .level-exam{ margin-right: 4px; box-shadow: 0px 0px 0px #666; }
                .search { margin-left: 119px; padding: 3px 11px; margin-right: -18px; margin-top: 9px; box-shadow: 0px 0px 0px #666; display: inherit;}
                .findMe { margin-left: 56%; font-size: 15px; margin-top: 9px; }

                .notification-empty-container {width: calc(100% - 20px); margin-left: 10px;}
                .no-rank {width: calc(100% - 20px); margin-left: 10px;}
                .my-rank{width: calc(100% - 20px); margin-left: 10px; }
                .rank-not-auth { margin-left: 10px; color: #999; width: calc(100% - 20px); }
                .title-exam-cover {margin-top: 0; font-size: 17px; border: none; height: 80px;
                    .title-text{width: 100%; float: left; border: none; text-align: center;}
                    .filter-container{width: 100%; float: left; padding-left: 5%;
                        .dropdown{float: left; width: 30%; text-align: center; margin: 0;
                            .drop-name{font-size: 16px;}
                        }
                    }
                }
                .list-students-container{ margin-top: 20px; margin-bottom: 30px;
                    .show-pc{display: none;}
                    .title-rank { font-size:12px; }
                    th{padding: 8px 5px !important;}
                    td{padding: 8px 5px !important;}
                    .indexRank{ vertical-align: inherit; width: 50px;
                        img {width: 25px !important;}
                        span { margin-left: 0;}
                    } 
                    .avatar{ vertical-align: inherit;
                        img { border-radius: 50%; margin-right: 5px; margin-left: 0;}
                        span { margin-left: 0px; font-size: 13px; font-weight: 500; }
                    }
                    .score { width:45px; vertical-align: inherit; }
                    .passed { vertical-align: inherit; padding: 0px; width: 45px;
                        .ok { color:#588d3f; }
                        .not-ok { color:red; }
                    }
                }

            }
        }
    }
    #jlpt-certificate {width: 96%;
        .jlpt-certificate{padding: 15px 10px;}
    }




    .main { width: 100%;
        .main-center { width: 100%;
             .main-history-menu{width: 100%; float: left; padding: 20px 0 10px 0;
                .exam-container-menu{ width: 100%; margin: 0 auto;
                    .item-exam{ width: 33%; margin: 0;}
                    .active { box-shadow: none; color: #588d3f; padding: 15px 5px;
                        img{height: 45px;}
                    }
                }
            }
            .main-history-right { padding: 0 1px; min-height: 210px; width: 100%; margin-left: 0; margin-top: 0;
                .show-pc{display: none;}
                .title-exam-cover{ margin-top: 0;
                    .title-text{margin-left: 15px;}
                }
                .result-not-auth{width: calc(100% - 30px); margin-left: 15px; padding: 10px;}
            }
        }
    }
}