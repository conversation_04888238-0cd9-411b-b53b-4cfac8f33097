@import "variables";
@media only screen and (max-width: 1550px) {
    .course-ldp {
        &__feature {
            .two-item {
                margin-top: 60px;
            }
            .two-item > * {
                flex-flow: column;
                align-items: center;
                .fold-paper {
                    margin-left: 0;
                }
            }
        }
        &__benefit {
            .three-item {
                padding-left: 0;
            }
        }
    }
}
@media only screen and (max-width: 1200px) {
    .course-cta {
        padding: 10px;
        &__title {
            margin-right: 0;
        }
        &__fb, &__buy {
            padding: 9px 14px;
            font-size: 14px;
            min-width: unset;
        }

    }
    .course-ldp {
        margin-top: 170px;
        &__header {
            background-image: none;
            background-color: white;
            &-image {
                display: none;
            }
            &::before {
                display: none;
            }
            &-inner {
                position: static;
                margin-top: 80px;
            }
            &-board {
                width: 84%;
                height: auto;
                margin: auto;
                position: static;
                .board-name {
                    .small {
                        font-size: 40px;
                    }
                    font-size: 40px;
                }
                .board-title {
                    top: -54px;
                    left: 14%;
                }
                .board-expired {
                    font-size: 15px;
                    color: #828282;
                    margin-top: 10px;
                }
                .board-price {
                    font-size: 21px;
                }
            }
            .cta {
                position: static;
                justify-content: center;
                margin-top: 50px;
                &-button {
                    height: 40px;
                    font-size: 14px;
                    padding: 0px 30px;
                }
            }
        }
        &__introduce {
            &:before {
                display: none;
            }
            &-inner {
                padding: 30px 0;
                position: static;
                height: 60vw;
            }
            .movie-play, #iframe-youtube {
                width: 90%;
                height: 100%;
                position: relative;
                top: unset;
                left: unset;
                margin: auto;
            }
            &-name {
                display: none;
            }
        }
        &__adventure {
            padding-top: 30px;
            padding-bottom: 30px;
            &-inner {
                margin-top: 30px;
                width: 98%;
                padding: 15px;
                .bookmark-ribbon {
                    font-size: 14px;
                    padding: 10px 30px;
                }
                & > img {
                    display: none;
                }
            }
            &-content {
                margin-top: 40px;
                padding: 20px;
                flex-flow: column;
                & > * {
                    width: 100%;
                }
                & > *:not(:first-child) {
                    margin-top: 20px;
                }
            }
        }
        &__feature {
            padding: 10px;
            .three-item,
            .two-item {
                margin-top: 0;
                & > *:not(:last-child) {
                    margin-right: 0;
                }
            }
            &-item {
                width: calc((100vw - 40px)/3);
                height: 55vw;
                video {
                    width: 396%;
                    top: -9vw;
                    left: -48vw;
                }
            }
            .two-item {
                & > * {
                    flex-flow: column;
                }
                .fold-paper {
                    margin-left: 0;
                    margin-top: 50px;
                }
            }
        }
        &__trial {
            padding: 30px 10px;
            &-inner {
                width: 90%;
                margin-top: 30px;
            }
            &-item {
                width: 25vw;
                height: 15vw;
            }
            &-item:nth-child(2) {
                width: 30vw;
                height: 20vw;
                margin: 0 30px;
            }
        }
        &__benefit {
            padding: 30px 10px;
            .three-item {
                margin-top: 0;
                padding-left: 0;
                justify-content: space-between;
            }
            &-item {
                margin-left: 0 !important;
                margin-top: 60px;
            }
        }
        &__teacher {
            padding: 40px 10px;
            padding-bottom: 60px;
            h1 {

            }
            &-inner {
                flex-wrap: wrap;
                width: 100%;
            }
            &-item {
                width: calc((100% - 60px) / 4);
                margin-right: 10px !important;
                &:nth-child(1) {
                    order: 3;
                }
                &:nth-child(2) {
                    order: 1;
                }
                &:nth-child(3) {
                    order: 2;
                }
                &:nth-child(4) {
                    order: 4;
                }
            }
        }
        &__promo {
            &-inner {
                padding: 30px;
            }
            &-item {
                margin-right: 10px !important;
                margin-top: 10px;
            }
            .cta-button {
                font-size: 14px;
                height: 50px;
                padding: 0px 40px;
            }
        }
        &__feedback {
            padding: 30px 0;
        }
        &__comment {
            width: 95%;
        }
    }
}
@media only screen and (max-width: 768px) {
    .main {
        .all-courses-container{
            li{
                font-size: 14px;
                padding: 10px 20px;
                a, a:hover {
                    img {
                        width: 20px;
                        margin-right: 10px;
                    }
                }
            }
            .title-container{
                .tab-title{width: 100%; font-size: 18px;}
            }

            .scroll-horizontal{ width: 100vw; overflow-x: scroll; margin-left: -15px;

                .lp2{width: 100%;
                    .course-item{}
                }
            }

            .lp1{width: 100%; display: initial; flex-wrap: nowrap;}

        }

        .main-course {
            .main-left { padding: 60px 15px 0 15px; clear: both;
                .btn-list-lesson-box{display: none;}
                .hidden-pc{display: block;}
                .timer-container{bottom: 80px; margin-right: 0%;
                    span { font-size: 16px; }
                    #timer { font-size: 16px; }
                    .heartbit{ position: absolute; width: 10px; height: 10px; border-radius: 50% !important; background-color: #54ff3a; animation: pulse1 .7s linear infinite; margin-left: 13px; margin-top: 10px; }
                    #client { margin-top: 1px; margin-left: 28px; font-size: 14px; }
                }
                #myModal .test-dialog { width: auto;
                    .content-test-detail { max-width: calc(100vw - 50px) !important;
                        .answer-area {
                            padding-left: 0; padding-right: 0;
                        }
                        .audio-task-area audio {
                            width: 100%;
                        }
                        img { max-width: 100%; height: auto !important; }
                    }
                }
                .course-detail-title {margin: 20px 0 20px 0; font-size: 20px;}
                .free-course-box { margin-bottom: 15px;
                    font-size: 18px;
                    span {
                        display: inline-block;
                        text-align: justify;
                    }
                }
                .course-heading{
                    span{font-size: 18px;}
                }
                .lesson-detail-title {
                    margin-top: 20px;
                }
                .btn-list-lesson-box {
                    .btn-list-lesson {
                        display: inline-block;
                        background-color: #fff;
                        color: rgb(255, 0 , 0);
                        width: 100%;
                        margin-bottom: 10px;
                        padding: 8px 12px;
                        font-weight: bold;
                        font-size: 16px;
                        border: 2px solid rgb(255, 0 , 0);
                    }
                    .btn-list-lesson-2 {
                        margin-top: 20px;
                        margin-bottom: 0;
                    }
                }
                .cover-container { margin-bottom: 40px;
                    .course-info-status-mobile { display: block; margin-top: 4px; }
                    .movie-play {
                        img {
                            max-width: 100vw;
                            height: calc((100vw)/1.77);
                        }
                        .play-icon-btn {
                            padding-top: 2px;
                        }
                    }
                    iframe {
                        width: calc(100vw) !important;
                        height: calc(100vw * 9 / 16) !important;
                        margin-left: -15px;
                    }
                    .myplayer{
                        width: calc(100vw) !important;
                        height: calc(100vw * 9.5 / 16) !important;
                        margin-left: -15px;
                    }
                    .list-video-area-parent {
                        width: calc(100% + 15px);
                        .video-item-area {
                            width: calc((100vw - 60px) / 3);
                            margin-right: 15px;
                            margin-bottom: 20px;
                            height: auto;
                            text-align: center;
                            .play-icon-btn-sm {
                                margin-left: calc(50% - 23px);
                            }
                            .image-video {
                                width: 100%;
                            }
                        }
                    }
                }
                .guest-cover-container{
                    padding: 50px 0;
                    h3{font-size: 18px;}
                }
                .course-tab {
                    li a {font-size: 14px; text-align: center; padding: 5px 0px 5px 0px;}
                    li {text-indent: 0; }
                }
                .comment-container {
                    .comment-tab {
                        .user-tab {text-align: center; width: calc((100vw - 47px) * 0.38);
                            a {font-size: 14px; padding-left: 0; padding-right: 0; }
                        }
                        .facebook-tab {text-align: center; width: calc((100vw - 47px) * 0.62);
                            a {font-size: 14px; padding-left: 0; padding-right: 0; }
                        }
                    }
                }
                .course-detail-container {
                    text-align: justify;
                }
                .comment-container .fb-comments {
                    margin: 0;
                }
                #user-comment-content .list-comments .comment-item .comment-content .name {
                    width: 100%;
                    text-align: justify;
                    word-wrap: break-word;
                }
                .cover-container .server-localtion-container { text-align: center;
                    .server-item { display: inline-block !important; margin-bottom: 20px;
                        .server-localtion{ width: 70%; padding: 10px 15px; }
                    }
                    .select-localtion-select {
                        display: block;
                        opacity: 1;
                        color: black;
                        float: left;
                        padding: 6px 7px !important;
                        border: 1px solid #588d3f;
                        outline: none;
                        // color: #fff !important;
                        background: #588d3f;
                        font-size: 12px;
                        // -webkit-appearance:none;
                    }
                    .choose-quality{ width: 100%; font-size: 12px;
                        .select-video-quality {
                            // -webkit-appearance:none;
                            opacity: 1;
                            color: black;
                            float: right;
                            padding: 6px 7px !important;
                            border: 1px solid #e74c3c;
                            outline: none;
                            // color: #fff !important;
                            option { color: black; }
                        }
                    }
                }
                .cover-container {
                    min-height: auto; margin: 10px 0 7px 0;
                    .dmrx-player .fast-area i {
                        font-size: 62px;
                    }
                    .lesson-content-detail {
                        .review-result {
                            position: static;
                            margin-top: 10px;
                            float: none;
                        }
                        .remove-result {
                            position: static;
                            margin-top: 10px;
                        }
                        .multimedia-item {
                            audio { width: 100%; }
                        }
                        .question-answer .question-answer-content {
                            display: inline-block;
                            max-width: 100%;
                            white-space: normal;
                            text-align: left;
                            line-height: 1.5;
                        }
                        img { max-width: 100%; height: auto !important; }
                        .answer-box-area { overflow: hidden; }
                        .two-line-answer { height: 60px; }
                    }
                }
                .preview-course-container{ display: block; padding-bottom: 25px;
                    .course-item{width: 100%; float: left; margin-bottom: 20px;
                        img{height: 163px; width: 100%; float: left; object-fit: cover;}
                    }
                }

                //combo
                .combo-list-container{
                    .combo-item{
                        .combo-name-container{padding: 30px 15px; width: 100px;
                            h1 { font-size: 25px; }
                        }
                        .combo-detail-container{width: calc(100% - 100px);
                            .course-info{width: calc(100% - 15px); font-size: 14px; margin-top: 0;}
                            .dmr-btn{padding: 5px; width: 90px; font-size: 13px; margin-top: 10px;}
                        }
                    }
                }
                //css cho JLPT
                .jlpt-certificate{
                    .jlpt-cer-title-en { font-size: 14px; }
                }

                .course-info-container{ margin-top: 20px; width: 100%; float: left;
                    .course-heading{width: 100%; float: left; background: $primaryColor; padding: 8px 0; text-align: center;
                        span{font-size: 20px; color: #fff;}
                    }
                    .price{width: 100%; float: left; text-align: center; color: $currencyColor; font-size: 30px; font-weight: 600;}
                    .price-yen{width: 100%; float: left; text-align: center; color: #222; font-size: 20px; font-weight: 500; margin-bottom: 20px;}
                    .info{width: 100%; float: left; color: #222; font-size: 16px; font-weight: 500; padding: 10px 15px; border-top: 1px solid #EEE;}
                    .buy-item{ text-align: center; width: 100%; float: left; padding: 0 0 20px 0;
                        .buy-btn{padding: 10px 20px; background-color: $currencyColor; color: #fff; border-radius: 3px; font-size: 18px; width: 100%;
                          &:hover{cursor: pointer;  border: 1px solid $currencyColor; background-color: #fff; color: $currencyColor; }
                        }
                        .bought{background-color: #69aa00;
                            &:hover{cursor: pointer;  border: 1px solid #69aa00; background-color: #fff; color: #69aa00; }
                        }
                    }
                }
                .see-more{width: 100%; float: left; text-align: center; padding: 10px 0; border: 1px solid $currencyColor; color: $currencyColor; border-radius: 3px; font-size: 15px;
                &:hover{cursor: pointer;  background-color: $currencyColor; color: #fff; }
            }

            }
            .main-right{padding-bottom: 25px;
                .hidden-mobile{display: none;}
                .course-info-status-pc { display: none; }
            }


            .main-left-premium{width: 100% !important; margin-top: 0 !important; padding-top: 0;
                .course-detail-title{width: 90%;}
                .btn-list-lesson-box{display: none;}
            }

            .main-right-premium{width: 100%; margin: 0; padding: 0 15px;}

        }
        .main-center {
            .all-courses-title { text-align: center; padding: 0 15px; margin: 20px 0 20px 0; font-size: 22px; }
            .all-courses-container:last-child {
                // .course-item:last-child { margin-left: calc(25vw - 5px); }
            }
            .all-courses-container {padding: 0 15px; margin: 0;
                .course-item { margin-bottom: 20px; width: 100%;
                    .course-info{padding-left: 10px; padding-right: 10px;}
                    .course-card{
                        .course-name {margin-bottom: 10px; }
                        .course-info {font-size: 12px; }
                    }
                    .combox4 {
                        .course-name {font-size: 19px; margin-bottom: 26px; margin-top: 27px; }
                    }
                }
                .item-mobile{display: none;}
                .combo-it{ margin: 20px 0 10px 15px; width: 100% !important;
                    .combo{
                        //.course-icon{width: 65px; height: 65px;}
                        .course-name{font-size: 20px; margin-top: -3px; width: 70%; padding-bottom: 5px;}
                        .course-info{font-size: 18px !important;}
                        .ci2 {margin-top: 3px !important;}
                        .buy-btn {padding: 4px 10px; width: auto;}
                    }
                }
                .combo-x3{
                    .combox3{
                        .course-name{font-size: 25px; margin-top: 24px; margin-bottom: 21px;}
                    }
                }
            }

            .kaiwa-container{width: 100%; padding-left: 0 !important;}


        }
    }
    .course-cta {
        padding: 10px;
        &__title {
            margin-right: 0;
        }
        &__left {
            flex-flow: column;
        }
        &__right {
            justify-content: center;
            margin-top: 10px;
        }
        &__inner {
            width: 100%;
            flex-flow: column
        }
        &__fb, &__buy {
            padding: 9px 14px;
            font-size: 14px;
            min-width: unset;
        }

    }
    .course-ldp {
        &__header {
            background-image: none;
            background-color: white;
           &-image {
               display: none;
           }
            &::before {
                display: none;
            }
            &-inner {
                position: static;
                margin-top: 80px;
            }
            &-board {
                width: 84%;
                height: auto;
                margin: auto;
                position: static;
                .board-name {
                    .small {
                        font-size: 40px;
                    }
                    font-size: 40px;
                }
                .board-title {
                    top: -54px;
                    left: 14%;
                }
                .board-expired {
                    font-size: 15px;
                    color: #828282;
                    margin-top: 10px;
                }
                .board-price {
                    font-size: 21px;
                }
            }
            .cta {
                position: static;
                justify-content: center;
                margin-top: 50px;
                &-button {
                    height: 40px;
                    font-size: 14px;
                    padding: 0px 30px;
                    min-width: unset;
                }
            }
        }
        &__introduce {
            &:before {
                display: none;
            }
            &-inner {
                padding: 30px 0;
                position: static;
                height: 60vw;
            }
            .movie-play, #iframe-youtube {
                width: 90%;
                height: 100%;
                position: relative;
                top: unset;
                left: unset;
                margin: auto;
            }
            &-name {
                display: none;
            }
        }
        &__adventure {
            padding-top: 30px;
            padding-bottom: 30px;
            &-inner {
                margin-top: 30px;
                width: 98%;
                padding: 15px;
                .bookmark-ribbon {
                    font-size: 14px;
                    padding: 10px 30px;
                }
                & > img {
                    display: none;
                }
            }
            &-content {
                margin-top: 40px;
                padding: 20px;
                flex-flow: column;
                & > * {
                    width: 100%;
                }
                & > *:not(:first-child) {
                    margin-top: 20px;
                }
            }
        }
        &__feature {
            padding: 10px;
            .three-item,
            .two-item {
                margin-top: 0;
                flex-flow: column;
                & > *:not(:last-child) {
                    margin-right: 0;
                }
            }
            &-item {
                width: 100%;
                height: 170vw;
                video {
                    width: 386%;
                    top: -20vw;
                    left: -140vw;
                }
            }
            .two-item {
                & > * {
                    flex-flow: column;
                }
                .fold-paper {
                    margin-left: 0;
                    margin-top: 50px;
                }
            }
        }
        &__trial {
            padding: 30px 10px;
            &-inner {
                width: 90%;
                margin-top: 30px;
                flex-flow: column;
            }
            &-item {
                width: 100%;
                height: 50vw;
            }
            &-item:nth-child(2) {
                width: 100%;
                height: 50vw;
                margin: 30px 0;
            }
        }
        &__benefit {
            padding: 30px 10px;
            .three-item {
                margin-top: 0;
                padding-left: 0;
                flex-flow: column;
            }
            &-item {
                margin-left: 0 !important;
                margin-top: 60px;
            }
        }
        &__teacher {
            padding: 40px 10px;
            padding-bottom: 60px;
            h1 {

            }
            &-inner {
                flex-wrap: wrap;
                width: 100%;
            }
            &-item {
                width: calc((100% - 20px) / 2);
                margin-right: 10px !important;
                &:nth-child(1) {
                    order: 3;
                }
                &:nth-child(2) {
                    order: 1;
                }
                &:nth-child(3) {
                    order: 2;
                }
                &:nth-child(4) {
                    order: 4;
                }
            }
        }
        &__promo {
            &-inner {
                padding: 30px;
            }
            &-item {
                margin-right: 10px !important;
                margin-top: 10px;
            }
            .cta-button {
                font-size: 14px;
                height: 50px;
                padding: 0px 40px;
            }
        }
        &__feedback {
            padding: 30px 0;
        }
        &__comment {
            width: 95%;
        }
    }
#specialized-featured-popup{
    width: auto; min-height: auto; padding: 0px 0px;border-radius: 6px; border: 1px solid rgba(0, 0, 0, 0.2);
}
#lesson-previous-popup{
    width: 93%; min-height: auto; padding: 0px 0px;border-radius: 6px; border: 1px solid rgba(0, 0, 0, 0.2);
}
.main .main-course .main-left .cover-container .lesson-content-detail {
    max-height: none;
}
.flashcards {
    .stackedcards-overlay.left,
    .stackedcards-overlay.right {
        height: 30px;
        font-size: 16px;
    }
    .modal-body {
        .setting {
            width: 100%;
        }
    }
    .settings {
        font-size: 2em;
        #settingTitle {
            display: none;
        }
    }
    .content {
        width: 85%;
    }
    .final-state {
        width: 100%;
        .btn {
            width: 80%;
            margin-bottom: 1em;
            border: none;
        }
    }
    .right-action,
    .left-action {
        height: 40px;
        font-size: 14px;

    }
    .card-item {
        .card-inner {
            min-height: 430px;
            .card__face {
                padding: 20px 10px 0 10px;
                .card_meaning {
                    .meaning {
                        font-size: 16px;
                        line-height: 21px;
                    }
                    img {
                        max-height: 60%;
                        object-fit: cover;
                        border: 0;
                    }
                }
                &--jp {
                    .card__word {
                        &--text {
                            font-size: 2em;
                        }
                        &--example {
                            line-height: 1.5em;
                        }
                    }

                    .card__voice {
                        &--button {
                            width: 10vmin;
                            height: 10vmin;
                            font-size: 1em;
                        }
                    }
                }
                .card_comment {
                    &--main {
                        //height: 18%;
                        grid-template-columns: 30px 1fr 30px;
                    }
                    &--title {
                        font-size: 13px;
                    }
                    &--content {
                        line-height: 21px;
                        .comment {
                            margin-top: 0;
                        }
                    }
                    &--toggle {
                        font-size: 1.5em;
                        grid-template-columns: 30px 1fr 30px;
                    }
                }
            }
        }
    }
}

.main{
    .parrent-scroll{width: 100%; float: left; height: 275px; overflow: hidden;
        .mobile-arrow-left{ z-index: 9; background:#E3F2FF; color: #3F9BEA; padding: 10px 20px 10px 30px; position: absolute; left: -18px; border-radius: 18px; margin-top: 30px;}
        .eju-teacher-scroll-container{width: 100%; float: left; height: 285px; overflow-x: scroll; overflow-y: hidden;}
        .eju-why-scroll-container{width: 100%; float: left; height: 285px; overflow-x: scroll; overflow-y: hidden;}
    }
    .main-eju-feature .eju-why-item p{text-align: center;}

    .main-eju-feature{width: 100%; float: left; margin-top: 0;
        .topf{ display: none;} .topf-mb{ display: block;}
        .eju-center{width: 100%; float: left;
            .ej-block-right{width: 100%; float: left; left: 0; top: 280px;}
            .eju-why-item{width: calc(100%); margin: 0; padding: 0 15%;
                p{text-align: center;}
            }
        }
        .anhdao-block{height: 400px;
            .btn-container{width: 100%;}
        }
        .why{height: 1000px;

        }
        .intro p{padding: 0px 20px;}

        .list-teacher{width: 1024px;}

    }
    .hv-share{ width: 100%;
        h4{font-size: 18px;}
        .parrent-scroll{height: 360px;
            .mobile-arrow-left{margin-top: 120px;}
            .eju-why-scroll-container{height: 370px;}
        }
        .why{width: 985px; height: 450px;
            .eju-why-item{ width: 280px; padding: 0 15px; margin: 30px 15px 20px 15px;
            }
        }

        .hv-fee{width: 100% !important; float: left; height: 250px !important; }
    }

    .intro{margin-left: 0; }
    .introx{margin-left: calc((100% - 250px) /2) !important; text-align: center;}
    .main-center{
        .main-left{
        .eju-top-menu{width: 100%; float: left;
            .active { background: #fff !important;}
        }
        .ez-preview{ height: 160px;
            .eju-why-scroll-container{height: 170px;}
        }
        .preview-course-container{width: 700px; display: flex;
            .course-item{ //width: 190px !important;
                img{width: 100%; height: auto;}
            }
        }

    }


    }
}

.main .main-course .m-l-checkpoint{
    width: calc(100% - 30px) !important;
    h1 b{font-size: 30px;}
    .btn-cham-diem{margin-bottom: 25px;padding: 10px 20px !important;}
}
#lesson-checkpoint .popup-result{
    width: 100%;
    top: 50px;
    left: 0;
    overflow: hidden;
    .main-popup{
        .result-icone{
            svg{
                width: 50%;
                height: 50%;
            }
        }
        .btn-submit{
            padding: 20px 10px;
            margin-top: 10px;
        }

        .checkpoint-submit span{
            padding: 15px 45px;
        }
    }
    .zmdi-close{top: 17px;left: 92%;}
    .popup-title-mb{font-size: 18px; padding: 20px 0;}
}

}

@media only screen and (max-width: 481px) {
    .main .main-course .main-left .cover-container .list-video-area-parent .video-item-area {
        width: calc((100vw - 45px) / 2);
    }
    .course-cta {
        &.dark {
          top: 53px;
        }
    }
    .course-ldp {
        margin-top: 230px;
        &__promo {
            &-inner {
               justify-content: center;
            }
            &-cta {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
        &__introduce {
            margin-top: 40px;
        }
    }
    .cta-button {
        min-width: 50%;
        padding: 0 1px;
    }
}

@media only screen and (min-device-width : 768px) and (max-device-width : 1023px) {
#specialized-featured-popup{
    width: 500px; min-height: auto; padding: 0px 0px;border-radius: 6px; border: 1px solid rgba(0, 0, 0, 0.2);
}
}

@media only screen and (max-width: 414px){
#lesson-checkpoint{
    .answer-item{
        width: 100% !important;
        display: flex;
        align-items: flex-start;
        input{
            margin-top: 8px !important;
            margin-right: 5px;
        }
        label{
            margin-bottom: 0 !important;
        }
    }
}
}
