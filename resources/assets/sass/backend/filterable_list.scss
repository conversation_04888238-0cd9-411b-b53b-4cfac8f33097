.filterable-list {
  &__tab {
    padding: 5px 15px;
    border: 2px solid transparent;
    margin-right: 5px;
    border-radius: 0px;
    cursor: pointer;
    font-weight: 600;
    color: #000;
    transition: 0.15s ease-in-out;
    display: flex;
    align-items: center;
    gap: 5px;
    &:hover {
      border-color: #6060ff;
      background-color: #6060ff;
      color: white;
    }
    &.active {
      border-color: #3131ff;
      background-color: #3131ff;
      color: white;
    }
  }
  &__screen {
    .checkbox__switch {
      position: relative;
      display: inline-block;
      width: calc(3em + 6px);
      height: calc(1.5em + 6px);
      input {
        opacity: 0;
        width: 0;
        height: 0;
      }
    }

    .checkbox__slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      -webkit-transition: .4s;
      transition: .4s;
      border-radius: 34px;
      &:before {
        position: absolute;
        content: "";
        height: 1.5em;
        width: 1.5em;
        left: 3px;
        bottom: 3px;
        border-radius: 50%;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
      }
    }

    input:checked + .checkbox__slider {
      background-color: #69AA00;
    }

    input:focus + .checkbox__slider {
      box-shadow: 0 0 1px #69AA00;
    }

    input:checked + .checkbox__slider:before {
      -webkit-transform: translateX(1.5em);
      -ms-transform: translateX(1.5em);
      transform: translateX(1.5em);
    }
  }
  &__note {
    margin-top: 10px;
    font-size: 12px;
    color: #9f041b;
  }
  &__filter {
    z-index: 2;
    -webkit-box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    -moz-box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    background: #FFF;
    margin: 10px 0;
    padding: 20px 10px;
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(7, 1fr );
    .form-group {
      margin-bottom: 0 !important;
    }
  }
  &__list {
    background: #FFF;
    margin: 10px 0;
    table {
      border: 1px solid #eee;
      width: 100%;
    }
    thead th {
      background: #f5f5f5;
      position: sticky;
      top: -1px;
      z-index: 1;
    }
    tbody tr {
      transition: all 0.6s ease;
      &:hover {
        background: #e6f7ff;
      }
      border-bottom: 1px solid #eee;
    }
    td, th {
      padding: 15px 10px;
    }
    .expand-on-focus {
      width: 100px;
      -webkit-transition: all 0.2s ease 0s;
      -moz-transition: all 0.2s ease 0s;
      -o-transition: all 0.2s ease 0s;
      transition: all 0.2s ease 0s;
      &:focus {
        width: 300px;
      }
    }
  }
  &__paginate {
    position: fixed;
    bottom: 0;
    background: #FFF;
    height: 80px;
    width: calc(100% - 50px);
    padding: 5px 25px;
    -webkit-box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    -moz-box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    display: flex;
    flex-flow: row;
    justify-content: space-between;
    align-items: center;
  }
  &__badge {
    display: inline-block;
    padding: 2px 6px;
    font-weight: bold;
    color: #FFF;
    font-size: 11px;
    border-radius: 5px;
    cursor: pointer;
    &--red {
      background: red;
    }
    &--orange {
      background: #a76800;
    }
  }
}
