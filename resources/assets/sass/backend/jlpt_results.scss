.a_cursor--pointer {
  cursor: pointer !important;
}
.exam__results {
  &--screen {
    .checkbox__switch {
      position: relative;
      display: inline-block;
      width: calc(3em + 6px);
      height: calc(1.5em + 6px);
      input {
        opacity: 0;
        width: 0;
        height: 0;
      }
    }

    .checkbox__slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      -webkit-transition: .4s;
      transition: .4s;
      border-radius: 34px;
      &:before {
        position: absolute;
        content: "";
        height: 1.5em;
        width: 1.5em;
        left: 3px;
        bottom: 3px;
        border-radius: 50%;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
      }
    }

    input:checked + .checkbox__slider {
      background-color: #69AA00;
    }

    input:focus + .checkbox__slider {
      box-shadow: 0 0 1px #69AA00;
    }

    input:checked + .checkbox__slider:before {
      -webkit-transform: translateX(1.5em);
      -ms-transform: translateX(1.5em);
      transform: translateX(1.5em);
    }
  }
  &--note {
    margin-top: 10px;
    font-size: 12px;
    color: #9f041b;
  }
  &--filter {
    z-index: 2;
    -webkit-box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    -moz-box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    background: #FFF;
    margin: 10px 0;
    padding: 20px 10px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .form-group {
      margin-right: 15px;
      margin-bottom: 0 !important;
    }
  }
  &--list {
    background: #FFF;
    margin: 10px 0;
    table {
      border: 1px solid #eee;
      width: 100%;
    }
    thead th {
      background: #f5f5f5;
      position: sticky;
      top: -1px;
      z-index: 1;
    }
    tbody tr {
      transition: all 0.6s ease;
      &:hover {
        background: #e6f7ff;
      }
      border-bottom: 1px solid #eee;
    }
    td, th {
      padding: 15px 10px;
    }
    .expand-on-focus {
      width: 100px;
      -webkit-transition: all 0.2s ease 0s;
      -moz-transition: all 0.2s ease 0s;
      -o-transition: all 0.2s ease 0s;
      transition: all 0.2s ease 0s;
      &:focus {
        width: 300px;
      }
    }
  }
  &--paginate {
    position: fixed;
    bottom: 0;
    left: 0;
    background: #FFF;
    height: 80px;
    width: 100vw;
    padding: 5px 25px;
    -webkit-box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    -moz-box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    display: flex;
    flex-flow: row;
    justify-content: space-between;
    align-items: center;
  }
}
#resp-table {
  width: 100%;
  display: table;
  }
  #resp-table-header{
  display: table-header-group;
  background-color: gray;
  font-weight: bold;
  font-size: 25px;
  }
  .table-header-cell{
  display: table-cell;
  padding: 10px;
  text-align: justify;
  border-bottom: 1px solid black;
  }
  #resp-table-body{
  display: table-row-group;
  }
  .resp-table-row{
  display: table-row;
  }
  .table-body-cell{
  display: table-cell;
  }