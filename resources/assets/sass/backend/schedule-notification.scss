.schedule__screen {
  &--navigation {
    margin-top: 10px;
    height: 50px;
    font-weight: bold;
    text-transform: uppercase;
    background: #FFF;
    padding: 0 20px;
    div {
      height: 100%;
      a {
        margin: auto 5px;
        padding: 10px 20px;
        cursor: pointer;
        text-decoration: none;
        color: #CCC;
        transition: 0.3s;
        &.active {
          border-bottom: 3px solid #000;
          color: #111;
        }
      }
    }
  }
  &--list {
    min-height: 60vh;
    background: #FEFEFE;
    margin-top: 10px;
    padding: 20px;
    table {
      width: 100%;
    }
    .status {
      font-size: 12px;
      padding: 5px 10px;
      border-radius: 5px;
      font-weight: bold;
      &-gray {
        background: rgba(213, 213, 213, 0.25);
        color: #111;
        border: 2px solid #111;
      }
      &-blue {
        background: rgba(74, 175, 255, 0.5);
        color: #005bff;
        border: 2px solid #005bff;
      }
      &-red {
        background: rgba(255, 21, 0, 0.25);
        color: #d43c39;
        border: 2px solid #d43c39;
      }
      &-green{
        background: rgba(0, 171, 23, 0.25);
        color: #4ada1b;
        border: 2px solid #4ada1b;
      }
    }
  }
}
.hover-able {
  transition: 0.3s;
  cursor: pointer;
  &.hover-red {
    &:hover {
      color: red;
    }
  }
  &.hover-green {
    &:hover {
      color: green;
    }
  }
  &.hover-bg-red {
    background: rgba(250, 4, 34, 0.75);
    color: #FFF;
    &:hover {
      background: #a83330;
    }
  }
  &.hover-bg-green {
    background: rgba(31, 185, 75, 0.8);
    color: #FFF;
    &:hover {
      background: #20a14c;
    }
  }
  &.hover-darken {
    &:hover {
      box-shadow:inset 0 0 0 99999px rgba(0,0,0,0.2);
    }
  }
}