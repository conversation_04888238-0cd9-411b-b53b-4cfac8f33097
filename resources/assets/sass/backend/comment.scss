.main-comment{ width: calc(100% + 25px); float: left;
	.main-comment-left{ width: 200px; float: left; padding-top: 50px; padding-right: 10px;

		.has_sub{list-style: none; float: left; width: 100%; margin-bottom: 20px;
			.child-container{width: 100%; float: left; background-color: #EEE; padding: 10px 0 0 0; padding-left: 20px; margin-bottom: 10px;
				.radio-success{width: 45%; display: inline-flex; box-sizing: border-box; margin-top: 0;}
			}
		}

	}
	.main-comment-right{ background-color: #FBFBFB; width: calc(100% - 200px); float: left; padding-top: 40px; padding-left: 25px; border-top: 0.5px solid #EEE;
		.nav-tabs{ width: 90%;
			li{
				a{font-weight: bold; font-size: 20px; padding-left: 0; margin-right: 20px; cursor: pointer;
					&:hover{border: none; font-weight: bold; border-bottom: 2px solid #CCC;}
				}
			}
			.active{
				a{border: none; border-bottom: 1px solid #337ab7; color: #337ab7; background-color: transparent;}
			}
			.label{border-radius: 4px; padding: 1px 3px; margin: 5px 0 0 10px;}
			.label-primary {background-color: #10a31a !important; }
		}
		#table_comment{width: 90%;

			tr{border-bottom: 1px solid #EEE; border-top: none;}
			.id-field{width:70px; padding-left: 0;}
			.avatar-field{width:45px; padding-left: 0;
				.avatar{border-radius: 50%; width: 35px; height: 35px; margin-top: 5px;}
			}
			.text-left{font-weight: 400; font-family: arial; padding: 8px 0;
				.cmt-content{color: #111; font-size: 15px; line-height: 1.1;}
				.preview-image{ padding-top: 10px; text-align: left;
					img{max-width: 80px; max-height: 40px;}
				}
				.comment-action{ margin-bottom: 0; font-size: 13px;
					a{color: #447030;}
					.answer{cursor: pointer;}
				}
				.reply-container{width: 100%; float: left;

			        .load-more-reply{cursor: pointer; color: #447030; font-size: 13px;
			          i{margin-right: 4px;}
			        }

			        .delete-comment{float: right; cursor: pointer; margin-right: -80px;}
			        .admin-edit-pen{float: right; cursor: pointer; margin-right: -80px; margin-top: 25px;}

			        .child-comment-item{ width: 100%; float: left; border-top: 1px solid #EEE; padding: 8px 0;
			          	&:first-child{margin-top: 5px;}
			         	&:last-child{padding-bottom: 0;}
			          	.avatar-container{width: 50px; height: 35px;
			            	img{width: 35px; height: 35px; border-radius: 50%;}
			            	.zmdi-check-circle{float: right; margin: 22px 0 0 -10px; position: absolute; color: #578fff; background: #fff;border-radius: 50%;}
			          	}
			          	.comment-content{width: calc(100% - 90px); padding-left: 50px;
			           	 	.child-name{margin-bottom: 0;width: 100%; font-size: 15px; line-height: 1.2; margin-bottom: 1px; color: #777; float: left;
			              		b {color: #447030; cursor: pointer; }
			              		.preview-image{ padding-top: 10px; float: left;
									img{max-width: 80px; max-height: 40px;}
								}
			            	}
				            .child-comment-action{font-size: 13px; margin-bottom: 0;
				              .answer{cursor: pointer;}
				              .time{opacity: 0.4;}
				            }
			          	}
			        }

			        .reply-form{ width: 100%; float: left; border-top: 1px solid #EEE; padding-top: 10px; margin-top: 5px;
			          .me-avatar{width: 35px; height: 35px; border-radius: 50%; float: left;}
			          .input-comment{width: calc(100% - 150px) !important; float: left; margin: 0 15px; padding: 6px 10px; box-sizing: border-box; outline: none;
			            border: 1px solid #DDD; border-radius: 2px;
			            &:focus{outline: none; border-color: #AAA;}
			          }
			          .pick-image{padding: 8px; float: left; margin-left: -76px; position: relative; cursor: pointer;
			            i{font-size: 16px; opacity: 0.8; display: block;}
			            .form-pick-image{width: 20px; height: 25px; overflow: hidden; float: left; margin: -20px 0 0 -2px; position: relative; opacity: 0; cursor: pointer;}
			          }
			          .preview-image{width: 100%; float: left; padding: 0 0 0 50px;
			            img{max-width: 200px; max-height: 150px; border-radius: 6px; margin-top: 10px;}
			          }
			          .post-comment-btn{padding: 6px 20px; background-color: #447030; color: #fff; border-radius: 2px; cursor: pointer; float: left;
			              &:hover{ opacity : 0.9; background: #5cbfaa; color: #fff; }
			          }
			          // .preview-image{width: 100%; float: left; padding: 5px 0 0 50px;
			          //   img{max-width: 200px; height: auto;  border-radius: 4px;}
			          // }
			        }
			    }
			}
		}
	}

	.pagination{width: 100%; float: left;}
}

.popup-user-info{ display: none; width: 400px;
	h4{font-family: arial;}
}

.edit-cmt-popup{ display: none; width: 600px;
	h4{font-family: arial;}
    .edit-comment-area{width: 100%; float: left; margin-bottom: 15px; height: 150px;}
    .edit-comment-btn-save{padding: 6px 20px; background-color: #447030; color: #fff; border-radius: 2px; cursor: pointer; float: right;
        &:hover{ opacity: 0.9; background: #5cbfaa; color: #fff; }
    }
    .edit-comment-btn-cancel{background-color: #EEE; color: #777; padding: 6px 20px; border-radius: 2px; cursor: pointer; float: right; margin-right: 10px;}
}
