

/** css button, label, tag for vip invoice */

.vip-orange{
  background-color: #e88504;
}

.vip-label{
  font-size: 15px;
  font-weight: 500;
  font-family: monospace;
  margin-bottom: 0;
  display: inline-block;
  padding: 3px 0px;
  border-radius: 20px;
  color: #000000;
}

.vip-invoice-trigger {
  padding: 3px 12px;
  border: none;
  font-size: 15px;
  background: #0FB78F;
  border-radius: 20px;
  color: #FFFFFF;
  outline: none;
}

.vip-iv-create {
  background: #8dcc76;
  border-radius: 5px;
  padding: 10px 15px;
  color: white;
  outline: none;
  border: none;
}


#admin-vip-combo{
  .filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 10px 20px;
    background: #a9a6a6;
    border-radius: 5px;
    .filter-left{

    }

    .filter-right {
      display: flex;
      align-items: center
    }
  }

  .vip-modal{
    width: 100%;
    border-radius: 5px;
    background-color: whitesmoke;
  }

  .vip-dialog{
    width: 70%;
    margin: 20px auto;
  }

  .vip-combo-form{
    display: flex;
    .vip-combo-left{
      width: 70%;
      margin-right: 20px;
    }
    .vip-combo-rigth{
      width: 30%;
    }

  }

  .vip-combo-bar{
    padding-bottom: 10px;
    .vip-combo-submit{
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
      button{
        padding: 10px 20px;
        background-color: #8dcc76;
        border: none;
        border-radius: 5px;
        color: #ffffff;
        font-weight: 500;
      }
    }
  }

  //custom scrollbar for vip course modal
  .v-modal-container::-webkit-scrollbar {
    display: none;
  }

  .v-modal-container {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  //custome vue-select css
  .vs__search{
    font-size: 15px;
  }
}


//css phục vụ cho giao điện khoá học vip
#vip-course{
  .vip-course-filter{
    display: flex;
    align-items: center;
    background: #b5b2c2;
    padding: 5px 0;
    border-radius: 5px;
    justify-content: space-between;
    margin-top: 20px;
    height: 60px;
    input,button{
      height: 40px;
      outline: none;
      border: none;
      border-radius: 5px;
      margin-left: 20px;
    }
    .vcourse-right{
      button{
        background: #e07309;
        color: white;
        font-weight: 600;
      }
    }
  }

  .vip-course-save{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    button{
      border-radius: 5px;
    }
  }
}

