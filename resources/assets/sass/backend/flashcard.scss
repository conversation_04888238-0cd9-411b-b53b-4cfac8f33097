.flashcard-preview {
  padding: 20px;
  position: fixed;
  top: 100px;
  right: 20px;
  width: 400px;
  height: 100%;
  h4 {
    margin-bottom: 15px;
    font-size: 18px;
    color: #333;
  }

  .flashcard-front,
  .flashcard-back {
    background: #fff;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    position: relative;
    min-height: 200px;
  }
  .flashcard-image-front,
  .flashcard-image-back {
    text-align: center;
    margin: 20px 0;

    img {
      max-width: 100%;
      max-height: 200px;
      object-fit: contain;
    }
  }

  .flashcard-front,
  .flashcard-back {
    .audio {
      position: absolute;
      top: 10px;
      left: 10px;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
        opacity: 0.6;
        &:hover {
          opacity: 1;
        }
      }
    }

    .flashcard-word {
      font-size: 32px;
      text-align: center;
      margin: 20px 0;
      color: #333;
      font-weight: 500;
    }

    .flashcard-examples {
      margin-top: 30px;
      padding: 15px;
      border-radius: 8px;

      &-title {
        span {
          font-weight: 500;
          margin-bottom: 10px;
          color: #07403f;
          background: #ceffd8;
          border-radius: 9999px;
          padding: 5px 10px;
        }
      }

      &-list {
        margin-top: 10px;
        .flex {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
        }

        img {
          width: 20px;
          height: 20px;
          opacity: 0.6;
          cursor: pointer;
          &:hover {
            opacity: 1;
          }
        }

        span {
          &.mr-2 {
            margin-right: 8px;
          }
        }
      }
    }
  }

  .flashcard-back {
    .flashcard-meaning {
      font-size: 24px;
      text-align: center;
      margin-bottom: 20px;
      color: #333;
    }

    .flashcard-kanji-meaning {
      font-size: 16px;
      text-align: center;
      color: #666;
    }
  }

  // Furigana styles
  ruby {
    rt {
      font-size: 10px;
      color: #666;
    }
  }

  // Bold text styles
  b {
    color: #e74c3c;
    font-weight: 500;
  }
}
