.sale__commission {
  &--screen {

  }
  &--filter {
    position: sticky;
    top: 0;
    z-index: 2;
    -webkit-box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    -moz-box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    box-shadow: -2px 6px 24px -18px rgba(0,0,0,0.75);
    background: #FFF;
    margin: 10px 0;
    padding: 20px 10px;
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(7, 1fr );
  }
  &--list {
    background: #FFF;
    margin: 10px 0;
    table {
      border: 1px solid #eee;
      width: 100%;
    }
    thead tr {
      background: #f5f5f5;
    }
    tbody tr {
      transition: all 0.6s ease;
      &:hover {
        background: #e6f7ff;
      }
      border-bottom: 1px solid #eee;
    }
    td, th {
      padding: 15px 10px;
    }
    .expand-on-focus {
      width: 100px;
      -webkit-transition: all 0.2s ease 0s;
      -moz-transition: all 0.2s ease 0s;
      -o-transition: all 0.2s ease 0s;
      transition: all 0.2s ease 0s;
      &:focus {
        width: 300px;
      }
    }
  }
  &--paginate {
    position: fixed;
    bottom: 0;
    background: #FFF;
    height: 80px;
    width: calc(100% - 50px);
    padding: 5px 25px;
    -webkit-box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    -moz-box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
    box-shadow: 0px 10px 22px -8px rgba(0,0,0,0.75);
  }
  &--bill-image {
    width: 50px;
    height: 50px;
    position: relative;
    margin: 0 auto;
    overflow: hidden;
    cursor: pointer;
    img {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 0;
    }
    &-add {
      cursor: pointer;
    }
    &-edit, &-save {
      display: block;
      background: rgba(0,0,0,0.3);
      color: #FFF;
      height: 20px;
      cursor: pointer;
      position: absolute;
      bottom: -20px;
      z-index: 1;
      transition: all 0.3s ease;
      &:hover {
        background: rgba(0,0,0,1);
      }
    }
    &-edit {
      width: 100%;
    }
    &-save {
      bottom: 0;
      width: 100%;
      display: grid;
      grid-gap: 0;
      grid-template-columns: 1fr 1fr;
      background: rgba(0,0,0,1);
    }
    &:hover &-edit {
      bottom: 0;
    }
    &-default {
      width: 100%;
    }
  }
}