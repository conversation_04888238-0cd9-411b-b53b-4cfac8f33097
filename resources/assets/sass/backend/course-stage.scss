.course-stages{
  &__screen {
    display: grid;
    grid-gap: 1rem;
    grid-template-columns: 1fr 1fr 2fr;
    height: 100%;
    margin-top: 2rem;
    font-size: 16px;
    &--panel {
      height: 80vmin;
      padding: 0 1.5rem 1.5rem 1.5rem;
      background: #FFF;
      overflow-y: scroll;
      position: relative;
      &-loading {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  &__panel {
    &--header {
      position: sticky;
      top: 0;
      background: #FFF;
      padding: 1rem 0;
    }
    &--course {
      &-list {
        background: rgba(212,212,212,0.3);
        padding: 10px;
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        label {
          cursor: pointer;
        }
      }
    }
    &--stage {
      &-list {
        display: grid;
        grid-template-columns: 1fr;
      }
      &-item {
        background: rgba(212,212,212,0.3);
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        padding: 5px;
        cursor: pointer;
        transition: ease-in-out 0.1s;
        &-selected {
          background: rgba(150,150,150,0.3);
        }
        &-info {
          display: flex;
          flex-flow: column;
          justify-content: center;
          padding-left: 20px;
        }
        &-action {
          display: flex;
          flex-flow: column;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
.lesson-categories {
  &__panel {
    &--header {
      display: flex;
      justify-content: space-between;
      position: sticky;
      top: 0;
      background: #FFF;
      padding: 1rem 0;
    }
    &--item {
      background: rgba(212,212,212,0.3);
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      padding: 5px 10px;
      cursor: pointer;
      transition: ease-in-out 0.1s;
      &-icon {
        img {
          max-width: 100px;
        }
      }
      &-info {
        display: flex;
        flex-flow: row;
        justify-content: flex-start;
        padding-left: 10px;
      }
      &-selected {
        background: rgba(150,150,150,0.3);
      }
    }
  }
}
.lesson-groups {
  &__panel {
    &--header {
      display: flex;
      justify-content: space-between;
      position: sticky;
      top: 0;
      background: #FFF;
      padding: 1rem 0;
    }
    &--item {
      background: rgba(212,212,212,0.3);
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      padding: 5px 10px;
      cursor: pointer;
      transition: ease-in-out 0.1s;
      &-info {
        display: flex;
        flex-flow: row;
        justify-content: center;
        padding-left: 10px;
      }
      &-selected {
        background: rgba(150,150,150,0.3);
      }
    }
  }
}
.adventure {
  &-map {
    display: flex;
    flex-wrap: wrap;
  }
  &-checkpoint {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #96D962;
    border-radius: 50%;
    font-size: 20px;
    font-weight: bold;
    font-family: Mulish, Arial, sans-serif;
    cursor: pointer;
    box-shadow: 0 5px 0 0 #4BA72E;
    transition: 0.2s ease-in-out;
    &:hover {
      transform: translateY(2px);
      box-shadow: 0 2px 0 0 #4BA72E;
      .adventure-checkpoint__inner {
        background: #96D962;
        color: white;
        border-color: white;
      }
    }
    &__inner {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #F5FFED;
      color: #96D962;
      border: 3px solid #72C658;
      transition: 0.2s ease-in-out;
    }
  }
}
