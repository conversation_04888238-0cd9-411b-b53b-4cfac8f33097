.sidebar{box-shadow: none !important;}
#side-menu>li>a {padding: 0 !important; }
#side-menu>li>a.active{border-bottom: none !important; }
.nav-second-level {z-index: 1}
.sidebar li>a>i {margin-right: 2px !important; }
.bg-title{margin-left: 0px !important; margin-right: 0 !important; padding: 15px 0 10px 0 !important;}
.grid-stack {
    position: relative;
}

.grid-stack-item {
    position: absolute;
    padding: 0;
}

.grid-stack-item .grid-stack-item-content,
.grid-stack-item .placeholder-content {
    margin: 0;
    position: absolute;
    top: 0;
    left: 10px;
    right: 10px;
    bottom: 0;
    width: auto;
    z-index: 0 !important;
    overflow: auto;
}

.grid-stack-placeholder .placeholder-content {
    border: 1px dashed lightgray;
}

.grid-stack-item.ui-draggable-dragging,
.grid-stack-item.ui-resizable-resizing {
    z-index: 100;
}

.grid-stack-item.ui-draggable-dragging .grid-stack-item-content,
.grid-stack-item.ui-resizable-resizing .grid-stack-item-content {
    box-shadow: 1px 4px 6px rgba(0, 0, 0, 0.2);
    opacity: 0.8;
}

.grid-stack-item .ui-resizable-handle {
    padding: 3px;
    margin: 3px 0;
    cursor: nwse-resize;
    color: gray;

    position: absolute;
    bottom: 0;
    right: 15px;

    font: normal normal normal 14px/1 FontAwesome;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);

    font-size: 10px;
}

.grid-stack-item .ui-resizable-handle::before {
    content: "\f065";
}

.grid-stack-item[data-gs-width="12"] { width: 100% }
.grid-stack-item[data-gs-width="11"] { width: 91.66666667% }
.grid-stack-item[data-gs-width="10"] { width: 83.33333333% }
.grid-stack-item[data-gs-width="9"]  { width: 75% }
.grid-stack-item[data-gs-width="8"]  { width: 66.66666667% }
.grid-stack-item[data-gs-width="7"]  { width: 58.33333333% }
.grid-stack-item[data-gs-width="6"]  { width: 50% }
.grid-stack-item[data-gs-width="5"]  { width: 41.66666667% }
.grid-stack-item[data-gs-width="4"]  { width: 33.33333333% }
.grid-stack-item[data-gs-width="3"]  { width: 25% }
.grid-stack-item[data-gs-width="2"]  { width: 16.66666667% }
.grid-stack-item[data-gs-width="1"]  { width: 8.33333333% }

.grid-stack-item[data-gs-x="12"] { left: 100% }
.grid-stack-item[data-gs-x="11"] { left: 91.66666667% }
.grid-stack-item[data-gs-x="10"] { left: 83.33333333% }
.grid-stack-item[data-gs-x="9"]  { left: 75% }
.grid-stack-item[data-gs-x="8"]  { left: 66.66666667% }
.grid-stack-item[data-gs-x="7"]  { left: 58.33333333% }
.grid-stack-item[data-gs-x="6"]  { left: 50% }
.grid-stack-item[data-gs-x="5"]  { left: 41.66666667% }
.grid-stack-item[data-gs-x="4"]  { left: 33.33333333% }
.grid-stack-item[data-gs-x="3"]  { left: 25% }
.grid-stack-item[data-gs-x="2"]  { left: 16.66666667% }
.grid-stack-item[data-gs-x="1"]  { left: 8.33333333% }

@media (max-width: 768px) {
    .grid-stack-item {
        position: relative !important;
        width: auto !important;
        left: 0 !important;
        top: auto !important;
        margin-bottom: 20px;
    }

    .grid-stack {
        height: auto !important;
    }
}

.grid-stack.grid-stack-animate, .grid-stack.grid-stack-animate .grid-stack-item {
    -webkit-transition: left .3s, top .3s, height .3s, width .3s;
    -moz-transition: left .3s, top .3s, height .3s, width .3s;
    -o-transition: left .3s, top .3s, height .3s, width .3s;
    -ms-transition: left .3s, top .3s, height .3s, width .3s;
    transition: left .3s, top .3s, height .3s, width .3s;
}
/*Don't animate the placeholder or when dragging/resizing*/
.grid-stack.grid-stack-animate .grid-stack-item.ui-draggable-dragging,
.grid-stack.grid-stack-animate .grid-stack-item.ui-resizable-resizing,
.grid-stack.grid-stack-animate .grid-stack-item.grid-stack-placeholder{
    -webkit-transition: left .0s, top .0s, height .0s, width .0s;
    -moz-transition: left .0s, top .0s, height .0s, width .0s;
    -o-transition: left .0s, top .0s, height .0s, width .0s;
    transition:  left .0s, top .0s, height .0s, width .0s;
}

 .grid-stack-item {
    padding-right: 20px !important;
        }
.grid-stack-item-content{
   display:none;
}
.white-box{
    padding:10px !important;
}
.two-part li span{
    font-size:30px  !important;
}
.grid-stack-item[data-gs-height="3"]{
    height:150px !important;
}
.grid-stack-item[data-gs-y="3"]{
    top:155px  !important;
}
.grid-stack-item[data-gs-y="13"]{
    top:780px  !important;
}
#articlewidget, #orderwidget, #transactionwidget{padding-right: 0px !important;}

.preloader {
  width: 100%;
  height: 31px;
  text-align: center;
}

.nb-notifications {
  border-radius: 12px;
  background: green;
  padding: 5px 10px;
}

.cssload-speeding-wheel {
  width: 31px;
  height: 31px;
  margin: 0 auto;
  border: 2px solid rgba(97,100,193,0.98);
  border-radius: 50%;
  border-left-color: transparent;
  border-right-color: transparent;
  animation: cssload-spin 425ms infinite linear;
    -o-animation: cssload-spin 425ms infinite linear;
    -ms-animation: cssload-spin 425ms infinite linear;
    -webkit-animation: cssload-spin 425ms infinite linear;
    -moz-animation: cssload-spin 425ms infinite linear;
}
.card-box{
  padding: 10px;
  border: 1px solid rgba(54, 64, 74, 0.05);
  -webkit-border-radius: 5px;
  border-radius: 5px;
  -moz-border-radius: 5px;
  background-clip: padding-box;
  margin-bottom: 10px;
  background-color: #ffffff;
}
.bg-db{
  overflow: hidden;
  margin-top: 10px;
}
.w10{
  width: 10%
}
.w20{
  width: 20%
}
.text-dark {
  color: #797979 !important;
}
.font-600 {
  font-weight: 600;
}
.m-0 {
  margin: 0px !important;
}
h2 {
  line-height: 35px;
}
.m-t-5 {
  margin-top: 5px !important;
}
.m-t-15 {
  margin-top: 15px !important;
}
.ico-das{
  font-size: 55px !important;
  position: absolute;
  right: 0px;
  bottom: 0px;
  top: 0px;
  line-height: 60px !important;
  padding: 25px 40px;
}
.widget-panel {
  padding: 30px 20px;
  padding-left: 30px;
  border-radius: 4px;
  position: relative;
  margin-bottom: 20px;
  cursor: pointer;

}
.bg-white {
  background-color: #ffffff !important;
}
.text-pink{
  color: #f963ac;
}
.text-bluee{
  color: #00bfbf;
}
.text-red{
  color: #660000;
}
.line-chart{
  height: 370px;
  width: 100%;
}
.line-chart2{
  height: 200px;
  width: 100%;
}
.year{
  text-align: right;
  margin-top: 15px;
}
.title span{
  font-weight: initial ;
}
@keyframes cssload-spin {
  100%{ transform: rotate(360deg); transform: rotate(360deg); }
}

@-o-keyframes cssload-spin {
  100%{ -o-transform: rotate(360deg); transform: rotate(360deg); }
}

@-ms-keyframes cssload-spin {
  100%{ -ms-transform: rotate(360deg); transform: rotate(360deg); }
}

@-webkit-keyframes cssload-spin {
  100%{ -webkit-transform: rotate(360deg); transform: rotate(360deg); }
}

@-moz-keyframes cssload-spin {
  100%{ -moz-transform: rotate(360deg); transform: rotate(360deg); }
}

.logo b img {
    width: 128px;
    padding-left: 13px;
}

.pagination {
    display: inline-block;
    margin: 20px 0;
    border-radius: 4px;
    float: right;
}

@media screen and (min-width: 768px) {
  #pageModal .modal-dialog  {width:90%;}
  //scroll table
  //.table-fixed{
  //  height: 800px;
  //}
  //table {
  //  display: flex;
  //  flex-flow: column;
  //  width: 100%;
  //}
  //
  //thead {
  //  flex: 0 0 auto;
  //}
  //
  //tbody {
  //  flex: 1 1 auto;
  //  display: block;
  //  overflow-y: auto;
  //  overflow-x: hidden;
  //}
  //
  //tr {
  //  width: 100%;
  //  display: table;
  //  table-layout: fixed;
  //}

}

@media screen and (min-width: 768px) {
  #sortGroup .modal-dialog{
    width : 54%;
    left: 2%;
  }
}

@media screen and (min-width: 768px) {
  #sortLesson .modal-dialog{
    width : 54%;
    left: 2%;
  }
}
.modal-content{
  background : #edf1f5;
}
.permiss{
  display: inline;
}

@media screen and (min-width: 500px) {
  #adminModal .modal-dialog  {width:50%;}
}
.global_error {
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 0;
    color: #a94442;
    margin-left: 12px;
}
.delete-image{
    background: #fb9678;
    border: 1px solid #fb9678;
    margin-bottom: 5px;
    margin-left: 11px;
}

.count-message-red {
  color: red;
  border: solid 1px red;
}
.count-message-green {
  color: green;
  border: solid 1px green;
}
