//@import "./../frontend/variables";
//@import "./../frontend/mixins";
.m {
  &-0 {
    margin: 0 !important;
  }
  &-1 {
    margin: (1rem * .25) !important;
  }
  &-2 {
    margin: (1rem * .5) !important;
  }
  &-3 {
    margin: (1rem * .75) !important;
  }
  &-4 {
    margin: (1rem * 1) !important;
  }
  &-5 {
    margin: (1rem * 1.25) !important;
  }
}
.mt {
  &-0 {
    margin-top: 0 !important;
  }
  &-1 {
    margin-top: (1rem * .25) !important;
  }
  &-2 {
    margin-top: (1rem * .5) !important;
  }
  &-3 {
    margin-top: (1rem * .75) !important;
  }
  &-4 {
    margin-top: (1rem * 1) !important;
  }
  &-5 {
    margin-top: (1rem * 1.25) !important;
  }
}
.mb {
  &-0 {
    margin-bottom: 0 !important;
  }
  &-1 {
    margin-bottom: (1rem * .25) !important;
  }
  &-2 {
    margin-bottom: (1rem * .5) !important;
  }
  &-3 {
    margin-bottom: (1rem * .75) !important;
  }
  &-4 {
    margin-bottom: (1rem * 1) !important;
  }
  &-5 {
    margin-bottom: (1rem * 1.25) !important;
  }
}
.ml {
  &-0 {
    margin-left: 0 !important;
  }
  &-1 {
    margin-left: (1rem * .25) !important;
  }
  &-2 {
    margin-left: (1rem * .5) !important;
  }
  &-3 {
    margin-left: (1rem * .75) !important;
  }
  &-4 {
    margin-left: (1rem * 1) !important;
  }
  &-5 {
    margin-left: (1rem * 1.25) !important;
  }
}
.mr {
  &-0 {
    margin-right: 0 !important;
  }
  &-1 {
    margin-right: (1rem * .25) !important;
  }
  &-2 {
    margin-right: (1rem * .5) !important;
  }
  &-3 {
    margin-right: (1rem * .75) !important;
  }
  &-4 {
    margin-right: (1rem * 1) !important;
  }
  &-5 {
    margin-right: (1rem * 1.25) !important;
  }
}
.p {
  &-0 {
    padding: 0 !important;
  }
  &-1 {
    padding: (1rem * .25) !important;
  }
  &-2 {
    padding: (1rem * .5) !important;
  }
  &-3 {
    padding: (1rem * .75) !important;
  }
  &-4 {
    padding: (1rem * 1) !important;
  }
  &-5 {
    padding: (1rem * 1.25) !important;
  }
}
.pt {
  &-0 {
    padding-top: 0 !important;
  }
  &-1 {
    padding-top: (1rem * .25) !important;
  }
  &-2 {
    padding-top: (1rem * .5) !important;
  }
  &-3 {
    padding-top: (1rem * .75) !important;
  }
  &-4 {
    padding-top: (1rem * 1) !important;
  }
  &-5 {
    padding-top: (1rem * 1.25) !important;
  }
}
.pb {
  &-0 {
    padding-bottom: 0 !important;
  }
  &-1 {
    padding-bottom: (1rem * .25) !important;
  }
  &-2 {
    padding-bottom: (1rem * .5) !important;
  }
  &-3 {
    padding-bottom: (1rem * .75) !important;
  }
  &-4 {
    padding-bottom: (1rem * 1) !important;
  }
  &-5 {
    padding-bottom: (1rem * 1.25) !important;
  }
}
.pl {
  &-0 {
    padding-left: 0 !important;
  }
  &-1 {
    padding-left: (1rem * .25) !important;
  }
  &-2 {
    padding-left: (1rem * .5) !important;
  }
  &-3 {
    padding-left: (1rem * .75) !important;
  }
  &-4 {
    padding-left: (1rem * 1) !important;
  }
  &-5 {
    padding-left: (1rem * 1.25) !important;
  }
}
.pr {
  &-0 {
    padding-right: 0 !important;
  }
  &-1 {
    padding-right: (1rem * .25) !important;
  }
  &-2 {
    padding-right: (1rem * .5) !important;
  }
  &-3 {
    padding-right: (1rem * .75) !important;
  }
  &-4 {
    padding-right: (1rem * 1) !important;
  }
  &-5 {
    padding-right: (1rem * 1.25) !important;
  }
}
.d {
  &-flex {
    display: flex;
  }
  &-block {
    display: block;
  }
}
.flex {
  &-row {
    flex-flow: row;
  }
  &-column {
    flex-flow: column;
  }
  &-grow {
    flex-grow: 1;
  }
}
.justify-content {
  &-center {
    justify-content: center;
  }
  &-space-between {
    justify-content: space-between;
  }
  &-flex-end {
    justify-content: flex-end;
  }
  &-flex-start {
    justify-content: flex-start;
  }
}
.align-items {
  &-center {
    align-items: center;
  }
  &-baseline {
    align-items: baseline;
  }
  &-flex-end {
    align-items: flex-end;
  }
  &-flex-start {
    align-items: flex-start;
  }
  &-stretch {
    align-items: stretch;
  }
}
.min-h {
  &-full {
    min-height: 100%;
  }
}
.w {
  &-0.3 {
    width: 30% !important;
  }
}
.a-cursor {
  &-pointer {
    cursor: pointer;
  }
}
.text {
  &-bold {
    font-weight: bold !important;
  }
  &-red {
    color: #F3010A;
  }
}
.v-modal-mask {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
  display: table;
  transition: opacity .3s ease;
}

.v-modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.v-modal-container {
  width: 80vmax;
  max-height: 90vh;
  overflow-y: scroll;
  margin: 0px auto;
  padding: 20px 30px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, .33);
  transition: all .1s ease;
  font-family: Helvetica, Arial, sans-serif;
}

.v-modal-header h3 {
  margin-top: 0;
  color: #42b983;
}

.v-modal-body {
  margin: 20px 0;
}

.v-modal-default-button {
  float: right;
}

/*
 * The following styles are auto-applied to elements with
 * transition="v-modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the v-modal transition by editing
 * these styles.
 */

.v-modal-enter {
  opacity: 0;
}

.v-modal-leave-active {
  opacity: 0;
}

.v-modal-enter .v-modal-container,
.v-modal-leave-active .v-modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}
.grid-1-3 {
  display: grid;
  grid-gap: 20px;
  grid-template-columns: 1fr 3fr;
  align-items: center;
  margin-bottom: 10px;
}
.checkbox__switch {
  position: relative;
  display: inline-block;
  width: calc(3em + 6px);
  height: calc(1.5em + 6px);
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
}

.checkbox__slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 34px;
  &:before {
    position: absolute;
    content: "";
    height: 1.5em;
    width: 1.5em;
    left: 3px;
    bottom: 3px;
    border-radius: 50%;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
  }
}

input:checked + .checkbox__slider {
  background-color: #69AA00;
}

input:focus + .checkbox__slider {
  box-shadow: 0 0 1px #69AA00;
}

input:checked + .checkbox__slider:before {
  -webkit-transform: translateX(1.5em);
  -ms-transform: translateX(1.5em);
  transform: translateX(1.5em);
}
.text-center {
  text-align: center !important;
}
.hamburger {
  cursor: move;
}

.badge {
  &-gray {
    background-color: #ccc !important;
    color: white !important;
  }
}
