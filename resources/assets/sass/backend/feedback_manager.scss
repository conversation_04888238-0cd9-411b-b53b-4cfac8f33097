.bg-title {
	.feedback-item {
		width: 100%;
		height: 150px;
		
		.username {
			padding-top: 30px;
		}
		.content {
			padding-top: 30px;
		}
		.modify-action {
			padding-top: 30px;
		}
	}
	.new-feedback-area {
		border-bottom: 1px solid #e4e7ea;
	    overflow: hidden;
	    padding: 5px 20px 10px 25px;
	    margin-bottom: 25px;
	    margin-left: -25.5px;
	    margin-right: -25.5px;
	}
	.new-feedback-btn { float: right; }
	.header-feedback-manager {
		margin-bottom: 50px;
		.header-feedback-title { font-weight: bold; }
	}
}

.feedback-user-avatar {
			height: 100px;
			border-radius: 50%;
		}

.background-popup {
	position: fixed;
	background-color: gray;
	opacity: 0.5;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
}

.popup-edit-data {
	position: fixed;
	width: 600px;
	margin: 30px auto;
	background-color: green;
	z-index: 1050;
	top: 200px;
	width: 50%;
	left: 25%;
	margin: auto;
	padding: 30px;

	.data-email {
		
	}

	.data-content {
		margin-top: 40px;
	}
}

.modal-dialog {
	.error-area {
		.error-item {
			color: red;
		}
	}
}