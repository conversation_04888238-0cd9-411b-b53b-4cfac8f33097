#new-lesson-detail-screen {
  .lesson-detail {
    &__tabs {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #ccc;
      .active {
        color: #0d95e8;
        border-bottom: 3px solid #0d95e8;
      }
    }
    &__tab {
      padding: 15px 20px;
      cursor: pointer;
    }
    &__content {
      width: 100%;
      padding: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &__info,
    &__tasks {
      width: 1000px;
    }
    &__tasks {
      &-chip {
        padding: 5px 10px;
        border-radius: 20px;
        color: #FFF;
        font-weight: bold;
        font-size: 12px;
      }
      &-table {

      }
      &-answers {
        display: grid;
        grid-gap: 30px;
        grid-template-columns: 1fr 1fr;
      }
      &-answer {
        display: flex;
        flex-flow: row;
        align-items: center;

        input {
          margin: 0 5px;
        }
        input[type=radio] {
          height: 40px;
          width: 20px;
        }
      }
    }
  }
}