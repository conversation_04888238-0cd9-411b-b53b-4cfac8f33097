#chat_container { font-weight: 400;
    display: flex;
    flex: 1;
    margin-left: -25px;
    width: 100vw;
    border-top: 0.5px solid #EEE;

    .sender-content {
        display: flex;
        width: 320px;
        flex-direction: column;
        background: #FFF;

        .search-box {
            padding: 10px 15px 10px 5px;
            // background: #f06;
            font-weight: 400;
            display: block;
            border-bottom: 1px #e7e7e7 solid;
            border-right: 1px #F2F2F2 solid;

            .input-search {
                float: left;
                width: 55%;
                height: 27px;
                border-radius: 4px;
                outline: none;
                border: none;
                text-indent: 10px; border-radius: 0;
                border: 0.5px #DDD solid;
                padding: 3px 4px !important;
                // &:focus{border-bottom: 1px solid #CCC;}
            }
            .clear-search{float: left;margin: 8px 0 0 0; font-size: 18px; cursor: pointer;}
            .dropdown{float: right; margin: 7px 0 0 15px; font-weight: 400; color: #385898;
                .dropdown-toggle{}
            }
            .date-form{margin-bottom: 0;}
            .filter-date{width: 90px; height: 20px; margin-top: 10px; font-size: 12px;}
        }
        #sender_box {
            display: flex;
            width: 320px;
            flex-direction: column;
            overflow: hidden;
            overflow-y: scroll;
            height: calc(100% - 55px);

            .senderItem {
                display: flex;
                cursor: pointer;
                padding: 10px;
                margin: 0;
                border-bottom: 0.5px #F2F2F2 solid;
            }
            .avatar-sender {
                width: 50px;
                height: 50px;
                border-radius: 25px;
                object-fit: cover;
            }
            .info-sender-box {
                display: flex;
                flex: 1;
                flex-direction: column;
                padding-left: 10px;

                .name-and-time {
                    display: flex;
                    height: 20px;

                    .name-sender {
                        margin: 0;
                        display: flex;
                        align-items: center;
                        flex: 1;
                        font-size: 14px;
                        font-family: Arial;
                        color: #222;
                        span{
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            width: 140px;
                        }
                    }
                    .sender-item-time {
                        margin: 0;
                        font-size: 12px;
                        color: gray;
                        text-align: right;
                    }
                }
                .last-message {
                    width: 200px;
                    margin: -2px 0 0 0;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 13px;
                    color: #90949c;
                    font-family: arial;
                }
                .admin-tags{
                    width: 100%;
                    .tag{
                        font-size: 11px; 
                        font-weight: 400;
                        background: #DDD; 
                        border-radius: 4px; 
                        padding: 0 5px 0 5px;
                        margin-right: 5px;
                        color: #444;
                    }
                    .fa-graduation-cap{
                        color: green; margin: 5px 0; float: right;
                    }
                }
            }

            .selectSenderItem {
                display: flex;
                cursor: pointer;
                padding: 10px;
                margin: 0;
                background-color: #f5f6f7;
                border-bottom: 1px solid #fff;
            }
            .senderItemNotRead {
                display: flex;
                align-items: center;
                cursor: pointer;
                padding: 10px;
                background-color: #f5f6f7;
                border-bottom: 1px solid #fff;
                .name-sender {
                    font-weight: bold;
                    color: #385898 !important;
                }
                .sender-item-time {
                    font-weight: bold;
                    color: #385898 !important;
                }
                .last-message {
                    color: #333;
                    font-weight: bold;
                }
            }
        }
    }
    .chat-content {
        display: flex;
        flex: 1;
        flex-direction: column;
        background: #FFF;
        border-right: 1px solid #EEE;

        .user-info {
            padding: 7px 0;
            padding-left: 20px;
            border-bottom: 1px solid #e7e7e7;

            .avatar-sender { float: left;
                width: 41px;
                height: 41px;
                border-radius: 50%;
                object-fit: cover;
                margin-right: 10px;
            }
            .username { float: left;
                font-size: 15px;
            }
            .mark-as-unread{font-size: 20px; float: right; margin-right: 20px; padding: 5px; cursor: pointer; }
            .tu-van{float: right; padding: 10px; cursor: pointer;}
            .tag-box{float: right; padding-top: 10px; margin-right: 10px;
                .c-tag-item{float: right;margin-left: 5px; padding: 1px 13px 1px 3px; color: #fff; font-size: 9px; cursor: pointer;}
                i{margin: 2px -12px 0 0; float: right;}
            }
            
        }
        #screen_box {
            flex: 1;
            overflow: hidden;
            overflow-y: scroll;
            padding-right: 15px;
            padding-left: 20px;

            .senderMsgBox {
                display: flex;
                margin: 20px 0;
            }
            .myMsgBox {
                display: flex;
                justify-content: flex-end;
                margin: 40px 0;
            }
            .message-info {
                display: flex;
                flex-direction: column;
                justify-content: center;

                .time-message {
                    font-size: 13px;
                    color: gray;
                }

            }
            .senderMsg {
                padding: 8px 12px 7px 15px;
                border-radius: 1.3em;
                background: #e7e7e7;
                max-width: 400px;
                text-align: left;
                white-space: pre-line;
                color: rgba(0, 0, 0, 1);
                line-height: 1.34;
            }
            .myMsg {
                padding: 10px 12px 7px 15px;
                border-radius: 1.3em;
                background: #197676;
                max-width: 400px;
                text-align: left;
                p{width: 100%; float: left;}
                .bubble-detail{width: 100%; height: 20px; float: left; text-align: right;  margin-bottom: -30px; padding-top: 10px; font-size: 12px; font-weight: 500;
                    .remove-icon {
                        cursor: pointer;
                        color: rgb(246, 85, 105);
                        font-weight: bold;
                    }
                    .admin-name {
                        color: #385898;
                        font-weight: bold;
                    }
                }
            }
            .myMsgImage {
                .bubble-detail-image {
                    width: 100%;
                    height: 20px;
                    float: left;
                    text-align: right;
                    margin-bottom: -30px;
                    padding-top: 10px;
                    font-size: 12px;
                    font-weight: 500;
                    .remove-icon {
                        cursor: pointer;
                        color: rgb(246, 85, 105);
                        font-weight: bold;
                    }
                    .admin-name {
                        color: #385898;
                        font-weight: bold;
                    }
                }
            }
            .myNotSentMsg {
                padding: 15px 20px;
                border-radius: 5px;
                background: #197676;
                opacity: 0.5;
                max-width: 400px;
                text-align: left;
            }
            .senderText {
                font-size: 16px;
                font-weight: 500;
                line-height: 1.34;
                margin-bottom: 0;
            }
            .myText {
                color: #fff;
                font-size: 16px;
                font-weight: 500;
                line-height: 1.34;
                margin-bottom: 0;
                white-space: pre-line;
                overflow-wrap: break-word;
            }
            .images-box-single {
                width: 300px;
                text-align: right;
            }
            .images-box-double {
                width: 370px;
                text-align: right;
            }
            .images-box-multi {
                width: 555px;
                text-align: right;
            }
            .image {
                width: 300px;
                height: auto;
                border-radius: 7px;
                cursor: pointer;
            }
            .image-in-multi {
                width: 175px;
                height: 175px;
                object-fit: cover;
                border-radius: 7px;
                margin: 5px 5px;
                cursor: pointer;
            }
            .user-time-message {
                display: flex;
                align-items: center;
                font-size: 13px;
                color: gray;
            }
        }
        .root-msg-content {
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1; 
            -webkit-box-orient: vertical;
        }
        .input-box {
            padding: 0px 30px 15px 20px;
        }
        #send_image_form{margin-right: 15px;}
        .input-content {
            display: flex; height: 50px;

            #input_file {
                cursor: pointer;
                display: inline-block;
                width: 45px;
                padding: 49px 0 0px 0;
                height: 42px;
                overflow: hidden;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
                background: url('../../img/camera.png') center center no-repeat;
                border-radius: 10px;
                background-size: 30px 30px;
                opacity: 0.5;
                &::hover{
                    cursor: pointer;
                }
            }
            .form-message {
                display: flex;
                flex: 1;
                padding: 8px;
                border: 0.5px solid #e5e4e4;
                border-radius: 26px;
                background: #fff;

                .text-input {
                    width: 100%;
                    max-height: 135px;
                    min-height: 35px;
                    height: 35px;
                    border: none;
                    padding: 5.5px 5px 5.5px 5px !important;
                    resize: none;
                    font-size: 15px;
                    line-height: 1.5;
                }
            }
        }
    }

    .tag-box{float: left;}
    .tag-item{padding: 2px 5px; border-radius: 4px;  color: #fff; margin-left: 5px; cursor: pointer; display: block; float: right; text-align: center;}

    .chat-info{
        // display: flex;
        width: 300px; background: #fff;

        .info-tab{width: 100%; float: left;
            .li-tab{ border-bottom: 1px solid #DDD; width: 33%; margin: 0;
                a{padding: 17px 12px; text-align: center;}
            }
            .active a{background: transparent; border-bottom: 1px solid #385898; color:#385898; }
        }
        .tab-content{padding: 15px 15px; margin-top: 55px;
            .user-info-box{text-align: center;}
        }
    }
    .quick_reply {
        &__list {
            padding: 0;
            width: 100%;
            list-style: none;
            color: #fff;
        }
        &__top {
            display: flex;
            flex-flow: row;
            justify-content: space-between;
        }
        &__bottom {
            &--content {
                textarea {
                    border: none; width: 100%; resize: vertical; padding: 10px
                }
                div {
                    white-space: pre-wrap; border: none; width: 100%; padding: 10px; margin-bottom: 5px; word-break: break-word;
                }
            }
        }
        &__search {
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 10px;
        }
    }
    .color__palette {
        height: 30px;
        display: grid;
        grid-gap: 0;
        grid-template-columns: repeat(6, 1fr);
    }
}
#page-wrapper {
    display: flex;
    padding-bottom: 0;

    .container-fluid {
        display: flex;
        flex: 1;
    }
}
.modal-dialog-custom {
    width: 90%;

    .modal-body-custom {
        text-align: center;

        .image-review {
            min-width: 600px;
            max-height: calc(75vh);
            object-fit: cover;
        }
    }
}
