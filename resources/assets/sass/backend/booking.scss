.table-booking-kaiwa {
  margin-top: 20px;
  .btn-cal-booking {
    float: right;
    padding: 6px 20px;
    border: 1px solid #ccc;
    background: #fff;
  }

  .table th {
    font-weight: bold;
  }

  .booking-title {
    font-weight: bold;
  }

  // status button switch
  .switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
    input {
      opacity: 0;
      width: 0;
      height: 0;
      &:checked + .slider {
        background-color: #10a31a;
      }
      &:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
      }
      &:checked + .slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
      }
    }
  }



  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
    &:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      -webkit-transition: .4s;
      transition: .4s;
    }
    /* Rounded sliders */
  }
  .round {
    border-radius: 34px;
    &:before {
      border-radius: 50%;
    }
  }
}

.backend-booking-calendar {
  * {box-sizing: border-box;}
  ul {list-style-type: none;}
  body {font-family: Verdana, sans-serif;}

  .month {
    padding: 70px 25px;
    width: 100%;
    background: #1abc9c;
    text-align: center;
  }

  .month ul {
    margin: 0;
    padding: 0;
  }

  .month ul li {
    color: white;
    font-size: 20px;
    text-transform: uppercase;
    letter-spacing: 3px;
  }

  .month .prev {
    float: left;
    padding-top: 10px;
  }

  .month .next {
    float: right;
    padding-top: 10px;
  }

  .weekdays {
    margin: 0;
    padding: 10px 0;
    background-color: #ddd;
  }

  .weekdays li {
    display: inline-block;
    width: 13.6%;
    color: #666;
    text-align: center;
  }

  .days {
    padding: 10px 0;
    background: #eee;
    margin: 0;
  }

  .days li {
    list-style-type: none;
    display: inline-block;
    width: 13.6%;
    text-align: center;
    margin-bottom: 5px;
    font-size: 16px;
    color: #777;
  }

  .days li .active {
    padding: 5px;
    /* background: #1abc9c; */
    color: #f70000 !important;
    font-weight: bold;
  }

  /* Add media queries for smaller screens */
  @media screen and (max-width:720px) {
    .weekdays li, .days li {width: 13.1%;}
  }

  @media screen and (max-width: 420px) {
    .weekdays li, .days li {width: 12.5%;}
    .days li .active {padding: 2px;}
  }

  @media screen and (max-width: 290px) {
    .weekdays li, .days li {width: 12.2%;}
  }
}










