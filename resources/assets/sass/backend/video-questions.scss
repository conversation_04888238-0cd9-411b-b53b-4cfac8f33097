.fullscreen {
  width: 100vmax !important;
  height: 100vmin !important;
  position: absolute; top: 0; left: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.61);
}
#video__questions-screen {
  background: #000;
  height: 800px;

  .video-screen-title {
    height: 5%;
    background: #092532;
    margin-bottom: 3px !important;
    padding: 0 5px !important;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
    h4 {
      color: #FFF !important;
    }
  }
  .video__questions {
    &--editor {
      display: grid;
      grid-template-columns: 1fr 4fr;
      grid-gap: 3px;
      &-templates {
        background: #092532;
        color: #FFF;
        padding: 10px;
      }
      &-template {
        width: 100%;
        height: 40px;
        display: grid;
        grid-template-columns: 3fr 1fr;
        align-items: center;
        background: #08456e;
        margin-bottom: 3px;
      }
      &-form {
        padding: 10px;
        display: grid;
        grid-template-columns: 1fr 2fr;
        background: #092532;
        &-timing {
          padding: 5px;
        }
        &-content {
          padding: 5px;
          &-inline-ckeditor {
            background: #FFF;
            border: 1px solid #ccc;
            line-height: 10px;
          }
          &-answers {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-gap: 20px;
          }
          &-answer {
            background: #FFF;
          }
        }
        &-input {
          display: block;
          width: 100%;
          font-size: 12px;
          border: 1px solid #ccc;
          padding: 5px;
          font-weight: bold;
        }
        &-label {
          display: block;
          color: #FFF;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }

    &--content {
      width: 100%;
      height: 45%;
      background: rgba(0, 0, 0, 1);
      margin: 0 auto;
      display: grid;
      grid-template-columns: 1.5fr 1fr;
      grid-gap: 3px;
      .myplayer {
        width: 620px;
        height: 350px;
      }
    }
    &--preview {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #092532;
    }
    &--timeline {
      height: 50%;
      background: #000;
      color: #FFF;
      margin-top: 3px;
      &-utilities {
        height: 40px;
        background: #092532;
        padding: 4px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      &-statistics {
        display: flex;
        flex-flow: row;
      }
      &-current {
        display: block;
        height: 25px;
        text-align: center;
        line-height: 25px;
        padding: 0 10px;
        background: rgba(80, 80, 80, 1);
        font-weight: bold;
        color: #f0f0f0
      }
      &-layers {
        width: 100%;
        height: calc(100% - 40px);
        overflow: scroll;
        background: #092532;
        margin-top: 3px;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      &-ruler {
        height: 40px;
        position: sticky;
        top: 0;
        z-index: 1;
        background: #092532;
      }
    }
  }
}
.vdr {
  position: static !important;
  border: none !important;
  border-top: 1px solid #000 !important;
  border-bottom: 1px solid #000 !important;
}
.handle {
  &-false {
    display: none !important;
  }
  &-ml {
    left: 0 !important;
  }
  &-mr {
    right: 0 !important;
  }
}
.cke_editable_inline {
  height: 170px;
  overflow-y: scroll;
  padding: 10px;
}
.video-js.vjs-modal-dialog {
  background: transparent !important;
}

.video__js {
  &--overlay {
    .vjs-modal-dialog {
      background: none !important;
    }
    .vjs-modal-dialog-content {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      padding: 0 !important;
    }
    &-green-tea {
      background: rgba(127, 219, 218, 0.8) !important;
    }
    &-transparent {
      background: transparent !important;
    }
    &-dark {
      background: rgba(57, 154, 191, 0.8) !important;
    }
    &-half-screen {
      top: initial !important;
      bottom: 0 !important;
      height: 100% !important;
      .video__js {
        &--overlay {
          &-question {
            height: 50%;
          }
          &-answers {

          }
          &-answer {

          }
        }
      }
    }

    &-quiz {
      width: 100%;
      height: 100%;
      padding: 3% 3% 2% 3%;

      &-wrapper {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        flex-flow: column;
        justify-content: flex-end;
        align-items: flex-end;
      }
      &-qa {
        width: 100%;
        height: 100%;
        display: flex;
        flex-flow: column;
        justify-content: flex-start;
      }
      &-content {
        width: 100%;
        height: 50%;
        display: flex;
        flex-flow: column;
        justify-content: flex-start;
        align-items: center;
        margin: 0 auto;
        .h-100 {
          height: 100%;
        }
        .h-50 {
          height: 50%;
        }
        .h-25 {
          height: 25%;
        }
        .grid-1-1 {
          grid-template-columns: 1fr !important;;
        }
        .grid-2-1 {
          grid-template-columns: 1fr 1fr !important;;
        }
        .grid-3-1 {
          grid-template-columns: 1fr 1fr 1fr !important;
        }
      }
      &-timer {
        width: 100%;
        height: 40px;
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        #countdown {
          position: relative;
          height: 40px;
          width: 40px;
          text-align: center;
          #countdown-number {
            color: white;
            display: inline-block;
            line-height: 40px;
          }
          svg {
            position: absolute;
            top: 0;
            right: 0;
            width: 40px;
            height: 40px;
            transform: rotateY(-180deg) rotateZ(-90deg);
            circle {
              stroke-dasharray: 113px;
              stroke-dashoffset: 0px;
              stroke-linecap: round;
              stroke-width: 2px;
              stroke: white;
              fill: none;
              animation: countdown 20s linear 1 forwards;
            }
          }
        }
        @keyframes countdown {
          from {
            stroke-dashoffset: 0px;
          }
          to {
            stroke-dashoffset: 113px;
          }
        }
      }
    }
    &-question {
      background: rgba(0,0,0,0.9);
      font-weight: bold;
      font-size: 18px;
      width: 100%;
      font-family: 'Noto Serif JP', serif;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 20px;
      margin-bottom: 10px;
      border-radius: 4px;
    }
    &-answers {
      width: 100%;
      display: grid;
      grid-gap: 10px;
      grid-template-columns: 1fr 1fr;
    }
    &-answer {
      background: rgba(0,0,0,0.9);
      color: #FFF;
      display: grid;
      grid-template-columns: 1fr 4fr;
      font-size: 1.3em;
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      height: 100%;
      border-radius: 9px;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      box-shadow: inset -2px -4px 0 rgba(0,0,0,0.3);
      &:hover {
        background: rgba(0,0,0,0.5);
      }
      &-blink {
        animation: blinkColor 0.1s 4;
      }
    }
    &-navigation {
      //width: 100%;
      padding: 5px 0;
      text-align: left;
      &-button {
        padding: 7px 26px;
        background: rgba(21, 114, 150, 0.7);
        cursor: pointer;
        transition: 0.3s;
        border-radius: 3px;
        box-shadow: inset -2px -4px 0 rgba(0,0,0,0.3);
        &:hover {
          background: rgba(15, 79, 155, 0.9);
        }
      }
    }
    &-full-screen {
      .video__js {
        &--overlay {
          &-quiz {
            &-wrapper {
              justify-content: flex-start;
            }
            &-content {
              width: 100%;
              height: 100%;
              padding: 5% 0;
              display: flex;
              flex-flow: row;
              justify-content: flex-start;
              align-items: flex-end;
              margin: 0 auto;
            }
            &-qa {
              width: 65%;
              margin-right: 10px;
              justify-content: flex-end;
            }
          }
          &-question {
            background: rgba(21, 114, 150, 0.4);
          }
          &-answers {
            width: 100%;
            display: grid;
            grid-gap: 9px;
            grid-template-columns: 1fr 1fr;
          }
          &-answer {
            display: flex;
            padding: 1em 1em;
          }
        }
      }
    }
  }
}
.vjs-fullscreen {
  .video__js {
    &--overlay {
      &-quiz {
        padding: 1% 1% 1% 1%;
      }
      &-question {
        font-size: 30px !important;
        border-radius: 8px;
      }
      &-answer {
        padding: 0;
        font-size: 3em !important;
        border-radius: 6px;
      }
      &-half-screen {
        .video__js {
          &--overlay {
            &-quiz {
              &-content {
                padding: 0 5%;
              }
              &-timer {
                padding: 0 5%;
              }
            }
            &-question {
              padding: auto 5vmin;
              height: 30%;
            }
            &-answer {

            }
          }
        }
      }
      &-full-screen {
        .video__js {
          &--overlay {
            &-question {

            }
            &-answer {
              padding: 0 3em;
              height: 10vmin;
            }
          }
        }
      }
      &-navigation {
        padding: 2vmin 0;
        &-button {
          padding: 2vmin 2.5vmin;
        }
      }
    }
  }
}
@keyframes blinkColor {
  0%{
    background-color: #7ECADE;
  }
  /* YOU CAN ADD MORE COLORS IN THE KEYFRAMES IF YOU WANT
  50%{
    background-color: #55d66b;
    border: 5px solid #126620;
  }
  */
  100%{
    background-color: #93CA8C;
  }
}