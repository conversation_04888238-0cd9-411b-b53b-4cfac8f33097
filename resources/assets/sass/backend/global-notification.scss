#previewImg {
  width: 100%;
}
.right-panel {

}
.required-asterisk {
  font-weight: bold;
  color: red;
}
.form-error {
  color: red;
}
.notification {
  &__screen {
    display: grid;
    grid-template-columns: 1fr 3fr;
  }
  &__list {
    height: 700px;
  }
  &__form {
    background: #fff;
    margin: 10px 0;
    padding: 0 20px;
    border: 1px solid rgba(0,0,0, 0.1);
    -webkit-box-shadow: 10px 10px 10px -13px rgba(0,0,0,0.75);
    -moz-box-shadow: 10px 10px 10px -13px rgba(0,0,0,0.75);
    box-shadow: 10px 10px 10px -13px rgba(0,0,0,0.75);
  }
  &__list {
    margin-top: 10px;
    overflow-y: scroll;
    padding: 0 10px;
  }
  &__item {
    background: white;
    border-radius: 10px;
    border: 1px solid rgba(0,0,0, 0.1);
    padding-left: 10px;
    margin-bottom: 10px;
    min-height: 100px;
    -webkit-box-shadow: 10px 10px 16px -13px rgba(0,0,0,0.75);
    -moz-box-shadow: 10px 10px 16px -13px rgba(0,0,0,0.75);
    box-shadow: 10px 10px 16px -13px rgba(0,0,0,0.75);
    display: grid;
    grid-gap: 0;
    grid-template-columns: 1fr 20fr 0.5fr;
    &--image,
    &--content {
      display: flex;
      align-items: center;
    }

    &--content {
      border-right: 1px solid #ccc;
      padding: 10px;
      text-align: justify;
    }
    &--actions {
      text-align: center;
    }
    &--push {
      border-top-right-radius: 10px;
      background: green;
    }
    &--remove {
      border-bottom-right-radius: 10px;
      background: red;
    }
    &--edit {
      background: orange;
    }
    &--action {
      color: #fff;
      height: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      a {
        color: #fff;
      }
      &:not(:last-child) {
        border-bottom: 1px solid #fff;
      }
    }
    &--tags {
      display: flex;
    }
    &--tag {
      border: 1px solid #fff;
      margin-right: 5px;
      padding: 1px 5px;
      color: #fff;
      a {
        text-decoration: none;
        color: #fff;
      }
      &-blue {
        background-color: #0f74a8;
      }
      &-red {
        background-color: #9f041b;
      }
      &-yellow {
        background-color: #b88b01;
      }
      &-green {
        background-color: #007700;
      }
    }
    &--logs {
      line-height: 30px;
      li {
        list-style-type: square;
      }
    }
  }
}

