.flashcard__field {
  margin-bottom: 20px;
  display: grid;
  grid-gap: 10px;
  grid-template-columns: 2.5fr 1fr;
  //.flashcard__form {
  //  display: flex;
  //  flex-direction: column;
  //}
  .flashcard__preview {
    .card-item {
      display: flex;
      align-items: center;
      .card-inner.flip {
        transform: rotateY(180deg);
      }
      .card-inner {
        position: relative;
        transition: transform 0.6s;
        width: 100%;
        height: 550px;
        width: 350px;
        transform-style: preserve-3d;
        .card__face {
          padding: 10px 20px 20px 20px;
          box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.30);
          border-radius: 10px;
          position: absolute;
          height: 100%;
          width: 100%;
          backface-visibility: hidden;
          background-color: #FFF;
          .title {
            text-align: center;
            margin-top: 20px;
          }
          &--jp {
            .card__word {
              height: 85%;
              border-bottom: 1px solid #ccc;
              display: flex;
              flex-flow: column;
              justify-content: flex-start;
              padding-top: 70px;
              &--text {
                padding: 0;
                text-align: center;
                font-family: "Noto Serif JP script=all rev=1", "Adobe Blank";
                font-size: 3em;
                line-height: 1.3em;
                font-weight: 700;
                font-style: normal;
              }
              &--example {
                padding: 20px;
                line-height: 1.5em;
                .highlight {
                  color: red;
                  text-decoration: underline;
                }
              }
            }
            .card__voice {
              height: 15%;
              display: flex;
              align-items: center;
              &--button {
                vertical-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 50px;
                height: 50px;
                background: green;
                border-radius: 50%;
                color: #FFF;
                font-size: 1.5em;
              }
            }
          }
          &--back {
            transform: rotateY( 180deg);
          }
          .card_meaning {
            display: flex;
            flex-flow: column;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 20px;
            height: 65%;
            .meaning {
              max-width: 100%;
              text-align: left;
              font-size: 20px;
              line-height: 26px;
            }
            img {
              display: block;
              max-height: 270px;
              max-width: 100%;
            }
          }
          .card_comment {
            &--title {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 10%;
              font-size: 16px;
              color: red;
              text-transform: uppercase;
              text-align: center;
              border-top: 1px solid #e6e6e6;
            }
            &--avatar {
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: flex-start;
              padding-top: 5px;
              div {
                width: 25px;
                height: 25px;
                border-radius: 50%;
                border: 1px solid #ccc;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow: hidden;
              }
            }
            &--content {
              display: flex;
              flex-flow: column;
              height: 100%;
              line-height: 18px;
              .time {
                font-size: 12px;
                color: #BCBCBC;
              }
              .comment {
                display: block;
                display: -webkit-box;
                text-align: left;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                font-size: 13px;
                width: 100%;
                line-height: 1.4;
                height: calc(13px * 1.4 * 3);
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
            &--empty {
              color: green;
              display: flex;
              flex-flow: column;
              justify-content: center;
              align-items: center;
              height: 20%;
            }
            &--main {
              display: grid;
              align-items: center;
              font-size: 13px;
              grid-template-columns: 1fr 5fr 1fr;
              height: 20%;
            }
            &--action,
            &--toggle {
              color: green;
            }
            &--action {
              display: flex;
              flex-flow: column;
              justify-content: space-between;
              align-items: flex-start;
              height: 100%;
              font-size: 1.2em;
            }
            &--toggle {
              height: 10%;
              display: grid;
              align-items: flex-start;
              grid-template-columns: 1fr 5fr 1fr;
              font-size: 2em;
              i {
                grid-column-start: 3;
              }
            }
          }
        }
      }
    }
  }
}