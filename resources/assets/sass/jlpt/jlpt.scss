@import "variables";
@import "../frontend/header_footer";
@import "../frontend/common";
body { margin: 0; font-size: 14px; font-family: arial, sans-serif; font-weight: 400; color: #404040; background-color: #fff; overflow-x: hidden; }
a {color: $primaryColor; } a:hover,
a:focus {outline: none; text-decoration: none; color: #69aa00; }
img {max-width: 100%; }
ul, ol {margin: 0; padding: 0; }
ul li, ol li {list-style: none; }
p{margin: 0;}
body.fancybox-active{ overflow-y: auto; }

$menuWidth: 520px;

.refresh-cache{position: fixed; z-index: 999; cursor: pointer; right: -3px; top: 200px; background: #fff; border-radius: 4px; padding: 2px 10px; box-shadow: 0 3px 5px 0 rgba(52, 52, 52, 0.1); }

.cluster-option{ width: 350px; padding-top: 20px; margin-top: 60px; position: fixed; color: #26751e;
	.active{background: #EEE; border-bottom-left-radius: 0; border-bottom-right-radius: 0;  }
}

.download-store{width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; background: #fff;
    z-index: 9999; padding: 20% 15px; text-align: center; display: none;
    h2{width: 100%; float: left;}
    p{width: 100%; float: left; font-size: 15px; color: #999;}
    .mobile-app {width: 100%; float: left; margin-top: 30px;
        img{margin-right: 20px; width: 260px; margin-bottom: 10px}
    }
}

#jlpt-page{width: 100%; float: left; padding-top: 100px;

.site-header .header-content .container {
    width: 1024px;
    padding: 0;
}

.site-header{ z-index: 100; position: fixed; width: 100%; top: 0;
    .header-top {background-color: #fff; position: relative; z-index: 10; border-bottom: 1px solid #EEE;
        .nav-left{ width: 1024px; margin: 0 auto;
            li {margin-right: 20px; display: inline-block; vertical-align: top;
                a{ padding: 10px 0 8px 0; float: left; font-size: 16px; }
                .active{border-bottom: 1px solid $primaryColor;}
            }
            .shop-icon{float: right; margin-right: 110px; padding-top: 8px; img{width: 16px;}}
            .account-container{float: right; padding: 0; height: 33px;
                .text-login{float: right; margin-right: 20px; margin-top: 10px; cursor: pointer; outline: none; font-size: 16px;
                    &:hover{ color: #111; }
                }
                .text-register{ cursor: pointer; margin-top: 10px; cursor: pointer; outline: none; color: $primaryColor; float: right; font-size: 16px;
                    &:hover{ color: #111; }
                }

                .profile-no-login-icon {display: none;}

                .auth-container{cursor: pointer; padding: 10px 0; max-width: 250px; text-align: right;
                    .messenger-icon{display: inline; float: left; margin-left: -90px; padding: 0 10px;
                        img{width: 19px; height: 19px;  vertical-align: top; opacity: 0.8; margin-top: 1px;}
                        .mess-counts{background-color: #fa3e3e; border-radius: 4px; text-align: center; font-size: 11px; padding: 0 3px 0 3px;
                        color: #fff; float: right; position: absolute; margin: -4px 0 0 -6px;  }
                    }

                    .svgIcon {width: 26px; height: 26px; float: left; margin-top: -2px; margin-left: -45px;
                        svg {opacity: 0.7}
                        .noti-counts {background-color: #fa3e3e; border-radius: 4px; text-align: center; font-size: 11px;
                        padding: 0px 3px 0px 3px; color: #fff; float: left; position: absolute; margin: -2px 0 0 15px; opacity: 1;}
                    }


                    .user-avatar-circle{width: 18px; height: 18px; border-radius: 50%; object-fit: cover; border: 1px solid #EEE; background: #ccc;}
                    .user-name{ margin-right: 5px; padding-top: 8px;}
                    .caret{opacity: 0.7;}
                    .user-menu{margin-left:0; float: right; right: 0; top: 37px; width: 190px; left: unset;
                        .caret-up{position: absolute; float: right; margin-top: -13px; right: 20px;}
                        li{ width: 100%; float: left; padding: 0;
                            a{padding: 7px 15px;
                              i{margin-right: 10px;}
                            }
                        }
                        .dropdown-divider {height: 0; margin: .5rem 0; overflow: hidden; border-top: 1px solid #e9ecef; }
                    }
                }
            }
        }
    }
    .header-content{ background: #fff;
        .logo {float: left; padding: 12px 0; height: 70px; overflow: hidden; width: 120px;
            &:hover{opacity: 0.8;}
            img { height: 37px; margin-right: 20px; margin-top: 5px; }
        }
        .block-nav-menu{ width: calc(100% - 120px); float: left;
         .ui-menu{ width: 100%; float: left;
           li.nav-item-mn { height: 70px; font-size: 18px; color: #111; display: initial; float: left;
            .hover-box{float: left; width: 200px; background: #fff; margin-top: 70px; position: fixed; padding: 15px 0; display: none;
              box-shadow: 0px 0px 11.6854px rgba(0, 0, 0, 0.15); border-radius: 11px; border-top-left-radius: 0;
              .mn-item{width: 100%; float: left; padding: 8px 15px; img{margin-right: 10px;} font-size: 15px; font-family: Roboto;
                &:hover{font-weight: bold;}
              }
            }
            .group-name{float: left; height: 70px; line-height: 70px; cursor: pointer; padding: 0 15px; i{font-size: 13px; opacity: .6;}}
            &:hover{ background: #EFEFEF;
                .hover-box{display: block; background: #FFF;}
            }


            .free{ margin: 0 0 0 0; height: 15px; line-height: 15px; padding: 0 5px; border-radius: 3px; font-size: 10px; position: absolute; background-color: #f22b2b; color: #fff;}
            // a {padding: 8px 19px; height: 70px; font-size: 18px; color: #111;
            //     &:hover{cursor: pointer;}
            // }
            // .active{border-bottom: 2px solid $primaryColor;}
        }}
        }
    }
}


}


.jlpt-header{width: 100%; float: left; border-bottom: 0.5px solid #EEE; height: 60px;
	.header-center{width: 1024px;height: 60px; margin: 0 auto;
		.logo {float: left; padding: 11px 0; height: 70px; overflow: hidden; width: 120px;
            &:hover{opacity: 0.8;}
            img { height: 36px; margin-right: 20px; margin-top: 2px; }
        }
        .text-login{padding: 4px 10px; background: $primaryColor; border-radius: 4px; color: #fff; cursor: pointer;}
		.auth-box{float: right; padding: 15px 0;  text-align: right;
			.user-avatar-circle{width: 30px; height: 30px; cursor: pointer; border-radius: 50%; object-fit: cover; border: 1px solid #EEE; background: #ccc;}
            .user-name{ margin-right: 5px; padding-top: 8px;}
            .user-menu{margin-left:0; float: right; right: 0; top: 50px; width: 190px; left: unset;
                .caret-up{position: absolute; float: right; margin-top: -13px; right: 18px;}
                li{ width: 100%; float: left; padding: 0; cursor: pointer;
                    a{padding: 7px 15px;
                      i{margin-right: 10px;}
                    }
                }
                .dropdown-divider {height: 0; margin: .5rem 0; overflow: hidden; border-top: 1px solid #e9ecef; }
            }
		}
	}
}
.login-container{padding: 0; width:800px;
  .login-left-container{width: 320px; height: 600px; float: left; background: #CCC; overflow: hidden;}
  .login-right-container{width: calc(100% - 320px); height: 600px; float: left; overflow: hidden; padding: 10px 15px 20px 15px; box-sizing: border-box;
    .nav-pills{margin: 0 14px; border-bottom: 1px solid #EEE; height: 47px; }
    .nav-pills li.active>a, .nav-pills li.active>a:focus {color: rgba(96,106,116,.98); background: #FFF; border-bottom: 2px solid $primaryColor; border-radius: 0; }
    .nav-pills li>a {border-radius: 0; height: 47px; padding: 15px 0 7px 0; margin-right: 25px; color: #434a54; font-family: 'Open Sans',sans-serif; font-size: 16px; font-weight: 400; }
    .tab-content{padding-top: 20px;
      .agree-policy{font-size: 12px; margin-right: 0;}
      .btn-register{float: left; width: 100%; background: $primaryColor; color: #fff; border-radius: 3px; padding: 7px 0; text-align: center; cursor: pointer; margin-top: 15px; }
      .btn-login{float: left; width: 100%; background: $primaryColor; color: #fff; border: none; border-radius: 3px; padding: 7px 0;text-align: center; margin: 0 0 25px 0; cursor: pointer;}
      .form-group{margin-bottom: 10px;}
      .form-control { background: #EEE; height: 30px;}
      #login-content, #register-content{
        .error-container{width: 100%;
          .alert-danger{ padding-top: 5px; line-height: 1.1; background-color: transparent; }
        }
      }
      #register-content{
          .form-group{margin-bottom: 5px;}
      }
      #register-month, #register-day, #register-year {padding:4px 0 4px 5px; }
    }
    .break-line{padding-bottom: 10px;}
    .btn-googleplus {background-color: #dd4b39!important; }
    .btn-facebook {background-color: #3b5998!important;}
    .btn-apple {
      border: 1px solid #333;
      background-color: #333 !important;
    }
    .btn-social {
      width: 40px;
      color: #fff;
      padding: 3px 7px;
      font-size: 20px;
    }
  }
}
.fancybox-container{height: 100%; display: inline-block;}
.main{width: 100%; float: left; min-height: 500px;
  .main-center{ width: 1024px; margin: 0 auto;}
}
.footer{ width: 100%; float: left; color: #444; padding: 10px 20px; font-size: 14px; border-top: 0.5px solid #EEE;}

//phòng chờ
.main{

	.lang-btn{cursor: pointer; float: right;margin-top: -120px; padding: 5px 15px; border: 1px solid #EEE; border-radius: 2px;}
	.main-exam-menu{width: 100%; float: left; padding: 20px 0 20px 0;

	}
	.exam-container-menu{ width: $menuWidth; margin: 0 auto; height: 120px; background: #fff; position: relative;
		 display: flex; justify-content: space-between; flex-wrap: wrap;
		.item-exam{padding: 28px 5px 10px 5px; width: 155px; text-align: center; color: #666; border: 1px solid #f2f2f2; border-radius: 8px; background: #F6F6F6;
			svg{height: 44px; margin-bottom: 4px; display: inline;}
			span { font-size: 16px;}
			&:hover{font-weight: bold;}
		}

		.active { box-shadow: inset 5px 5px 10px #c9c9c9, inset -5px -5px 10px #ffffff; background: #FFF; font-family: Montserrat, Arial, sans-serif;}

	}
	.main-exam-right{  width: 100%; float: left; padding-bottom: 40px; min-height: 530px; border-top: 1px solid #EEE; margin-top: -40px; font-family: Montserrat, Arial, sans-serif;
		.jlpt-name{font-weight: bold; font-style: italic;}
		.requre-auth { color: #333; width: 100%; text-align: center; margin-top: 60px; padding: 80px 0; border: 1px #588d3f dashed;
			a{color: $primaryColor; font-weight: bold;
				&:hover{color: #17b50e;}
			}
		}
		.table-borderless{border: none; width: 100%; float: left; margin-top: 20px;
			thead{font-size: 16px;}
			td{font-size: 16px; vertical-align: middle; text-align: center;}
			th, td, tr{border: none;}
			th{ vertical-align: middle; text-align: center;
				span{}
			}
			tr{ padding: 8px 0; height: 54px; border-bottom: 1px solid #EEE;}
			.center{text-align: center;}
			.nx{ padding: 7px 0; font-size: 16px; color: #fff; border-radius: 2px;width: 62px;display: block; text-align: center}
			.N5{background-color: #7fb324;}
			.N4{background-color: #00a8b5;}
			.N3{background-color: #774898;}
			.N2{background-color: #e62a76;}
			.N1{background-color: #d32e35;}
			.enable{ border-radius: 8px !important; margin-bottom: 10px;
				box-shadow: 0 0 6px 6px rgba(0,150,0,.1), 0 6px 20px 0 rgba(0,150,0,.1);}
			.dmr-btn{background-color: #17b50e; color: #fff; font-weight: bold; padding: 5px 18px;}
			.online{font-weight: bold;}
			.action-area{
				.label{padding: .1em .5em .1em;}
			}
			.top {position: relative; display: inline-block; width: 9px; height: 9px; border-radius: 50%; margin-right: 10px; }
			.top.a2, .top.a2:before{background: #69c820; animation-delay: 0.2s; }
			.top:before{ content: ""; display: block; position: absolute; left: -8px; top: -8px; width: 25px;
            			height: 25px; border-radius: 50%; animation: ani 1s infinite ease-in; }
			@keyframes ani{
			    0%{
			        transform: scale(0.5);
			        opacity: 1;
			    }
			    100%{
			        transform: scale(1);
			        opacity: 0;
			    }
			}
		}

		.top {position: relative; display: inline-block; width: 9px; height: 9px; border-radius: 50%; margin-right: 10px; }
			.top.a2, .top.a2:before{background: #69c820; animation-delay: 0.2s; }
			.top:before{ content: ""; display: block; position: absolute; left: -8px; top: -8px; width: 25px;
            			height: 25px; border-radius: 50%; animation: ani 1s infinite ease-in; }
			@keyframes ani{
			    0%{
			        transform: scale(0.5);
			        opacity: 1;
			    }
			    100%{
			        transform: scale(1);
			        opacity: 0;
			    }
			}

		.no-auth-notifi { width: 100%; float: left; padding: 20px 20px; text-align: left;
			margin-top: 65px; font-size: 16px; text-align: center;
		}
	}
}






//ranking
.main{
	.main-ranking-menu{width: 100%; float: left; padding: 20px 0 20px 0;
	}

	.main-ranking-right{ width: 1150px; float: left; padding-bottom: 40px; min-height: 700px; margin-left: -50px; font-family: Montserrat, Arial, sans-serif;
		.cover-container{width: 100%; float: left; margin-bottom: 25px; min-height: 455px; margin-left: 2px;}
		.guest-cover-container{text-align: center; padding: 47px 0;
			.btn-login{padding: 6px 20px; background-color: $primaryColor; color: #fff; border-radius: 20px; margin-left: 10px;
			   cursor: pointer; outline: none; display: inline; margin-top: 4px; font-size: 16px;
			    &:hover{ opacity: 0.9; background: #5cbfaa; color: #fff; }
			}
			.btn-register{padding: 5px 20px; cursor: pointer; border-radius: 20px; border: 1px solid $primaryColor; background-color: #fff;  outline: none;
			    color: $primaryColor; display: inline; margin-top: 4px; cursor: pointer; font-size: 16px;
			    &:hover{ opacity: 0.9; background: #5cbfaa; color: #fff; border-color: #5cbfaa; }
			}
		}

		.title-exam-cover { color: #588d3f; font-weight: bold; margin-bottom: 10px; display: flex; justify-content: space-between;; align-items: center;
			.title-text{font-size: 20px;}

			.filter-container{ text-align: center; display: flex; align-items: center;

				.dropdown{margin-left: 10px; margin-top: -5px; border: 1px solid #EEE;
					.drop-name{font-weight: bold; font-size: 18px; color: #222;}
					.user-menu{ padding-bottom: 0; min-width: 130px;
						.caret-up{position: absolute; margin-top: -13px; left: 50px;}
						li{ border-bottom: 1px solid #EEE; font-size: 15px; cursor: pointer;
							a{padding-top: 8px; padding-bottom: 8px;}
						}
					}
				}

				.month-exam, .year-exam, .level-exam { height: 30px; box-shadow: 0px 1px 5px #666;}
				.month-exam, .year-exam, .level-exam, .search, .my-ranking{ margin-right: 20px; }
				.search {background-color: #588d3f; border-color: #588d3f; box-shadow: 0px 1px 5px #666; height: 30px; padding-top: 3px; padding-bottom: 3px;}
			}
		}

		.notification-empty-container {width: 100%; float: left; padding: 150px 0;
			margin-bottom: 30px; text-align: center; color: #666; border: 1px #DDD dashed;
		}
		.no-rank{ color: #666; width: 100%; text-align: center; margin-top: 20px; padding: 10px 0; border: 1px #DDD dashed;}
		.my-rank{ color: #337ab7; width: 100%; text-align: center; margin-top: 20px; padding: 20px 0 30px 0;
			font-weight: bold; background-color: #ebf0f4; border: 1px #62b4e8 dashed;
			span{color: #fff; background-color: #337ab7; border-radius: 50%; padding: 10px; font-size: 25px; display: inline; }
		}
		.rank-not-auth { color: #666; width: 100%; text-align: center; margin-top: 20px; padding: 10px 0; border: 1px #DDD dashed; }

		.list-students-container{ margin-top: 20px; font-weight: bold;

			.top-1{ background-color: #FFF9C0; }
			.top-2{ background-color: #F9E2A1; }
			.top-3{ background-color: #FFC896; }
			.top-4-9{ background-color: #f9fef2; }
			.fa-star-o{float: left; margin: 0 0 0 -15px;}
			.indexRank{ width: 70px; vertical-align: inherit; padding-left: 20px; width: 90px; text-align: center;
				.number{font-size: 24px; -webkit-text-stroke-width: 1px;}
				img { width: 28px; display: inline; }
			}
			.avatar{ vertical-align: inherit;
				img { border-radius: 50%; height: 35px; margin-right: 15px; margin-left: 5px; }
				span { margin-left: 10px; }
			}
			.detail{vertical-align: inherit;}
			.score { vertical-align: inherit; font-weight: bold; }
			.passed { vertical-align: inherit; padding-right: 0px; width: 80px;
				.ok { color:#588d3f; }
				.not-ok { color:red; }
			}
			.noResult { text-align: center; color: #666; }
			td{border-top: 0.5px solid #EEE;}
		}

	}
}

#jlpt-certificate{ width: 600px; padding: 0; border-radius: 6px; border: 1px solid rgba(0, 0, 0, 0.2); background: antiquewhite;
	.jlpt-certificate { font-size: 15px; text-align: center; background-image: url(/assets/img/bg_jlpt.jpg); padding: 15px; background-size: 20px 20px; margin-top: 5px;
		.jlpt-cer-title, .jlpt-cer-title-en, .jlpt-cer-t-test { font-weight: bold; }
		.jlpt-date, .jlpt-more-infor { text-align: left;line-height: 28px; display: flex; -webkit-justify-content: space-between;}
		.jlpt-score { width: 100%; border: 1px solid #999; padding: 4px; clear: both; margin-top: 12px;
			.jlpt-detail-score { width: 100%; }
			.jlpt-detail-score td, .jlpt-detail-score tr { border: 1px dotted #555; vertical-align: middle; width: 25%;}
			.jlpt-conclude-pass-c { height: 40px; width: 162px; border: 1px dotted #555; line-height: 40px; text-align: center; bottom: 0px; }
			.stamp{ width: 90px; height: 75px; position: relative; padding-right: 20px; }
			.jlpt-conclude { width:100%; }
		}
	}
}


//history
.main{
	.main-history-menu{width: 100%; float: left; padding: 20px 0 20px 0;

	}
	.main-history-right{ width: 100%; float: left; padding-bottom: 40px; min-height: 500px; border-top: 1px solid #EEE; margin-top: -40px;

		.title-exam-cover { margin-top: 20px;  color: #588d3f; font-weight: bold; border-bottom: 1px solid #CCC; height: 36px; margin-top: 60px;
			.title-text{padding: 10px 0; border-bottom: 1px solid #588d3f; font-size: 20px;}
		}
		.notification-empty-container {	width: 100%; float: left; padding: 150px 0; margin-bottom: 30px; text-align: center; color: #999; border: 1px #DDD dashed; margin-top: 47px; }
		.result-not-auth { color: #666; width: 100%; text-align: center; margin-top: 80px; padding: 80px 0; border: 1px #DDD dashed;
			a{font-weight: bold;}
		}
		.table{ width: 100%; float: left; margin-top: 80px; font-family: Montserrat, Arial, sans-serif;
			thead{font-size: 16px;}
			td{font-size: 16px; vertical-align: middle; text-align: center;}
			th, td, tr{border: none;}
			th{ vertical-align: middle; text-align: center;
				span{}
			}
			tr{ padding: 8px 0; height: 54px; border-bottom: 1px solid #EEE;}
			.title-rank{padding: 0 0 !important;}
		}
	}
}
.has-badge .badge--red {
	background-color: #fa3e3e;
	border-radius: 4px;
	text-align: center;
	font-size: 11px;
	padding: 0px 3px 0px 3px;
	color: #fff;
	position: absolute;
	margin: -8px 0 0 10px;
	opacity: 1;
}
