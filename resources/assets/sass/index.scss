@tailwind base;
@tailwind components;
@tailwind utilities;

canvas, img, svg {
  display: inline-flex;
  vertical-align: middle;
}
img {
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
}
.collapse.in {
  visibility: visible;
}
.grecaptcha-badge { display: none !important; }

.glow-overlay {
  --glow-size: 30rem;

  position: absolute;
  inset: 0;
  pointer-events: none;
  user-select: none;
  opacity: var(--glow-opacity, 0);
  mask: radial-gradient(
                  var(--glow-size) var(--glow-size) at var(--glow-x) var(--glow-y),
                  var(--glow-color) 1%,
                  transparent 50%
  );
  transition: 400ms mask ease;
  will-change: mask;
}

table[border="1"] {
  border: 1px solid black !important;
  border-collapse: collapse;
}
table[border="2"] {
  border: 2px solid black !important;
  border-collapse: collapse;
}
table[border="3"] {
  border: 3px solid black !important;
  border-collapse: collapse;
}

