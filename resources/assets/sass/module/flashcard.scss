.opacity-0-important {
  opacity: 0 !important;
}

.text-result p span, .text-result {
  font-size: 24px !important;
}

.pulse-container-flashcard {
  position: absolute;
  top: -40px;
  width: 100%;
  height: -webkit-fill-available;
  overflow: hidden;
}

.pulse-circle-flashcard {
  top: -130px;
  left: 25%;
  width: 52%;
  height: 272px;
  border-radius: 50%;
  position: absolute;
  transition: transform 0.1s ease-in-out;
  filter: blur(35px);
}

.card-wrap::after {
  content: '';
  position: fixed;
  bottom: 71px;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, transparent, white);
  pointer-events: none;
  transition: opacity 0.3s ease;
  width: calc(100% - 2rem);
  margin-left: 1rem;
}

.card-wrap-back::after {
  content: '';
  position: fixed;
  bottom: 224px;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to bottom, transparent, white);
  pointer-events: none;
  transition: opacity 0.3s ease;
  width: calc(100% - 2rem);
  margin-left: 1rem;
}

/* Class để ẩn phần mờ */
.card-wrap.fade-hidden::after {
  opacity: 0;
}


.card-wrap-back::-webkit-scrollbar, .card-wrap::-webkit-scrollbar {
  width: 6px;
  border-radius: 10px;
}

.card-wrap-back::-webkit-scrollbar-track, .card-wrap::-webkit-scrollbar-track {
  border-radius: 6px;
}

.card-wrap-back::-webkit-scrollbar-thumb, .card-wrap::-webkit-scrollbar-thumb {
  background: #D9D9D9;
  border-radius: 10px;
}

.card-wrap-back::-webkit-scrollbar-thumb:hover, .card-wrap::-webkit-scrollbar-thumb:hover {
  background: #8e8d8d;
}

.language-switch, .shuffle-switch {
  position: relative;
  display: inline-block;
  width: 64px;
  height: 32px;
}

.language-switch input, .shuffle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.language-slider, .shuffle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: .4s;
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.language-slider {
  background-color: #CCF8D1;
}

.shuffle-slider {
  background-color: #B3B3B3;
}

.language-slider .slider-text {
  font-family: Beanbag_Dungmori_Rounded_Medium;
  color: #757575;
  font-size: 12px;
  font-weight: bold;
  position: absolute;
  right: 8px;
}

.language-slider .flag {
  position: absolute;
  height: 26px;
  width: 26px;
  left: -6px;
  bottom: 0;
  transition: .4s;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
}

.flag.vi {
  background-image: url('/path-to-your-vietnam-flag.png');
  background: #FF0000;
  position: relative;
}

.flag.vi::after {
  content: '★';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #FFFF00;
  font-size: 14px;
}

.flag.ja {
  background-image: url('/path-to-your-japan-flag.png'); /* Thay đổi đường dẫn tới ảnh cờ Nhật */
  /* Hoặc sử dụng CSS để vẽ cờ */
  background: #FFFFFF;
  position: relative;
}

.flag.ja::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: #FF0000;
  border-radius: 50%;
}

input:checked + .language-slider, .shuffle-switch input:checked + .shuffle-slider {
  background-color: #CCF8D1;
}

input:checked + .language-slider .flag {
  transform: translateX(32px);
}

input:checked + .language-slider .slider-text {
  left: 8px;
  right: auto;
}

.language-slider.round, .shuffle-slider.round {
  border-radius: 34px;
}

/* Thêm hiệu ứng hover */
.language-switch:hover .language-slider, .shuffle-switch:hover .shuffle-slider {
  opacity: 0.9;
}

.shuffle-slider .slider-circle {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 3px;
  background-color: #D9D9D9;
  transition: .4s;
  border-radius: 50%;
}

.shuffle-switch input:checked + .shuffle-slider .slider-circle {
  transform: translateX(30px);
  background-color: #07403F;
}

/* Popover styles */
.popover_setting_content {
  margin-top: 10px;
}

.popover_setting_content::before {
  content: '';
  position: absolute;
  margin: 15px;
  top: -24px;
  right: 30px;
  width: 16px;
  height: 16px;
  background-color: white;
  transform: rotate(45deg);
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
}

.popover_setting_content > div {
  background-color: white;
  position: relative;
  z-index: 1;
}

.popover_setting_content_item {
  transition: background-color 0.2s ease;
}

.flashcards-wrap {
  display: flex;
  flex-flow: column;
  align-items: center;
  position: relative;

  .a_cursor--pointer {
    cursor: pointer;
  }

  .content {
    width: 80%;
  }

  .no-transition {
    -webkit-transition: none ! important;
    -o-transition: none ! important;
    transition: none ! important;
  }

  .stackedcards.init {
    opacity: 0;
    /* set về 0 để tạo hiệu ứng hiện ra từ hư vô thật ma mị */
  }

  .stackedcards {
    position: relative;
  }

  .stackedcards * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .stackedcards--animatable {
    -webkit-transition: all 400ms ease;
    -o-transition: all 400ms ease;
    transition: all 400ms ease;
  }

  .stackedcards .stackedcards-container > *,
  .stackedcards-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    will-change: transform, opacity;
    top: 0;
    border-radius: 10px 10px 0 0;
  }

  .stackedcards-overlay.left > div,
  .stackedcards-overlay.right > div,
  .stackedcards-overlay.top > div {
    width: 100%;
    height: 100%;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .stackedcards-overlay.left,
  .stackedcards-overlay.right,
  .stackedcards-overlay.top {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    left: 0;
    opacity: 0;
    top: -44px;
    height: 100%;
    font-size: 24px;
    font-weight: 700;
  }

  .stackedcards-overlay.top {
    background: transparent;
    color: transparent;
  }

  .stackedcards-overlay.right {
    height: 50px;
    background: #CEFFD8;
  }

  .stackedcards-overlay.left {
    height: 50px;
    background: #FFF193;
  }

  .stackedcards-overlay.left:empty,
  .stackedcards-overlay.right:empty,
  .stackedcards-overlay.top:empty {
    display: none !important;
  }

  .stackedcards-overlay-hidden {
    display: none;
  }

  .stackedcards-origin-top {
    -webkit-transform-origin: top;
    -ms-transform-origin: top;
    transform-origin: top;
  }

  .stackedcards-top {
    background: transparent;
    height: 100%;
  }

  .stackedcards .stackedcards-container > :nth-child(1) {
    position: relative;
    display: block;
  }

  /* elements on stacked cards */

  .card-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;

    .card-inner.flip {
      transform: rotateY(180deg);
    }

    .card-inner {
      position: relative;
      transition: transform 0.6s;
      width: 100%;
      min-height: 550px;
      transform-style: preserve-3d;
      -webkit-transform-style: preserve-3d;

      .card__face {
        padding: 20px 20px 20px 20px;
        box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.30);
        border-radius: 32px;
        position: absolute;
        height: 100%;
        width: 100%;
        backface-visibility: hidden;
        -moz-backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        background-color: #FFF;

        .card-inner.flip {
          transform: rotateY(180deg);
        }

        &--back {
          transform: rotateY(180deg);
        }
      }

      .card__face--back {
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg);
      }
    }
  }
}

.left-action, .right-action {
  transition: background-color 0.3s ease;
}

.left-action:hover, .left-action:active, .left-action.active {
  background-color: #FFE271;
}

.right-action:hover, .right-action:active, .right-action.active {
  background-color: #98FFAE;
}
