@import "./mixin.scss";
.el-header {
  background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.el-aside {
  color: #333;
}
.app-header {
  font-size: 20px;
  font-weight: bold;
  text-align: left;
  height: 40px !important;
  line-height: 40px;
  background: #96d962;
}

.render-again {
  .video-name {
    font-weight: bold;
    font-size: 16px;
  }
}

.btn-upload {
  background: #007bff !important;
  color: #fff;
  height: 42px;
  width: 180px;
  border-radius: 5px;
}

.btn-search {
  background: #007bff !important;
  color: #fff;
  height: 42px;
  border-radius: 5px;
  width: 200px;
  margin-left: 10px;
}
.el-progress-bar {
  .el-progress-bar__innerText {
    color: #fff !important;
  }
  margin-bottom: 10px;
}
.upload-area {
  margin-bottom: 10px;
}
.video-player {
  position: fixed;
  top: 50%;
  z-index: 999;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background: rgb(221, 221, 221);
  border-radius: 10px;
  .action {
    text-align: right;
    margin-top: 10px;
  }
}
