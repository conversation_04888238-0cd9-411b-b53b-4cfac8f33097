@import "variables";

.new-blog-container {
  width: calc(100vw - 15px);
  margin-left: calc((100vw - 1039px) / -2);
  background-color: #f7fff0;
  overflow: hidden;
  .new-blog-content {
    width: 1064px;
    margin: 0 auto;
    padding-top: 65px;
    padding-bottom: 40px;
    .blog-heading {
      margin-left: 20px;
      margin-right: 20px;
      margin-top: 0;
    }
  }
  .slick-track {
    background-color: hsl(92, 100%, 97%);
  }
  .newest-blog-box {
    max-height: 490px;
  }
  .new-blog-item {
    display: flex !important;
    padding: 28px 24px;
    background-color: #fff;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 7px;
    margin: 20px;
    .new-blog-img {
      width: 500px;
      min-width: 500px;
      height: 364px;
      border-radius: 5px;
      object-fit: cover;
    }
    .new-blog-info {
      padding: 15px 0 15px 50px;
      .new-blog-tag {
        background-color: #96d962;
        border-radius: 3px;
        padding: 4px 8px;
        gap: 10px;
        font-family: "Noto Sans";
        font-size: 12px;
      }
      h2 {
        font-family: "Montserrat";
        font-style: normal;
        font-weight: 500;
        font-size: 26px;
        line-height: 1.3;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        max-height: calc(26px * 1.3 * 3);
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        color: #222222;
      }
      .blog-time {
        font-family: "Quicksand";
        font-size: 12px;
        opacity: 0.7;
        color: #777777;
        margin-bottom: 10px;
      }
      .blog-intro {
        font-family: "Quicksand";
        font-size: 15px;
        line-height: 1.5;
        text-align: justify;
        color: #555555;
        margin-top: 10px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 7;
        max-height: calc(15px * 1.5 * 7);
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
      }
    }
  }
  .slick-dots i {
    color: #d9d9d9;
  }
  .slick-dots .slick-active i {
    color: #96d962;
  }
  .arrow {
    cursor: pointer;
    position: absolute;
    top: 45%;
    right: -70px;
    background-color: #fff !important;
    border-radius: 50%;
    border: 1px solid #96d962;
    color: #96d962;
    width: 50px;
    height: 50px;
    display: flex !important;
    justify-content: center;
    align-items: center;
    text-align: center;
    font-size: 16px;
    i {
      margin-bottom: 5px;
    }
  }
  .prev-arrow {
    left: -70px;
  }
}

.no-more-blog {
  font-family: "Montserrat";
  font-weight: 700;
  font-size: 18px;
  text-align: center;
  color: #41a336;
  background-color: #d8edb9;
  border-radius: 8px;
  cursor: pointer;
  padding: 10px 28px;
  margin-top: 50px;
}

.see-more-blog-facebook {
  font-family: "Montserrat";
  font-size: 16px;
  margin-top: 5px;
  display: inline-block;
  padding: 5px 0;
}

.new-type {
  margin-top: 28px;
  span {
    background-color: #d8edb9;
    padding: 3px 6px;
    gap: 10px;
    color: #666666;
    border-radius: 3px;
    font-size: 12px;
  }
}

.blog-main-left {
  width: 684px !important;
}

.blog-main-right {
  width: 280px !important;
  margin-left: 60px !important;
  padding-bottom: 30px;
}

.blog-heading {
  font-family: "Montserrat";
  text-transform: capitalize;
  font-weight: 500;
  font-size: 24px;
  margin-top: 50px;
}

.mobile-ct-group {
  display: none;
}

.main-left {
  .blog-hidden-before {
    display: none;
  }
  .blog-content {
    display: grid;
    grid-template-columns: auto auto;
    margin-left: -10px;
    margin-right: -10px;
  }
  .load-more-box {
    margin: 30px 0 80px 0;
    .load-more-text {
      width: 274px;
      height: 46px;
      font-family: "Montserrat";
      font-weight: 700;
      font-size: 18px;
      text-align: center;
      color: #41a336;
      background-color: #d8edb9;
      border-radius: 8px;
      cursor: pointer;
    }
  }
  .blog-heading {
    margin-top: 75px;
  }
  .heading-box .selectpicker {
    display: none;
  }
  .heading-box .prefix-title {
    display: none;
  }
  .news-item {
    display: flex;
    flex-direction: column;
    padding: 20px 0;
    margin: 10px;
    width: 322px;
    img {
      width: 320px;
      height: 225px;
      -o-object-fit: cover;
      object-fit: cover;
      border: 1px solid #fafafa;
      border-radius: 5px;
    }
    .title {
      font-family: "Montserrat";
      margin-top: 15px;
      margin-bottom: 10px;
      font-weight: 500;
      font-size: 24px;
      line-height: 1.3;
      color: #222222;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      max-height: calc(25px * 1.3 * 2);
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
    }
    .info {
      font-family: "Quicksand";
      opacity: 0.7;
      font-size: 13px;
      color: #777777;
    }
    .brief {
      font-family: "Quicksand";
      text-align: justify;
      font-size: 16px;
      margin-top: 8px;
      display: -webkit-box;
      line-height: 1.4;
      color: #555555;
      -webkit-line-clamp: 3;
      max-height: calc(16px * 1.4 * 3);
      font-size: 16px;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .read-more {
      padding: 5px 20px;
      background: #588d3f;
      color: #fff;
      margin-left: 25px;
      margin-top: 10px;
    }
  }
  .featured {
    width: 450px;
    float: left;
    margin: 0;
    border-bottom: none;
    img {
      width: 450px;
      height: 270px;
      overflow: hidden;
      -o-object-fit: cover;
      object-fit: cover;
      float: left;
    }
    .title {
      font-weight: bold;
      font-size: 22px;
      width: 450px;
      float: left;
      margin-left: 0;
      margin-top: 10px;
      line-height: 1.2;
      color: #111;
    }
    .info {
      width: 450px;
      float: left;
      margin-left: 0;
      opacity: 0.7;
      font-size: 13px;
      margin-top: 5px;
    }
    .brief {
      width: 450px;
      float: left;
      margin-left: 0;
      font-size: 16px;
      margin-top: 10px;
    }
  }
  .featured-sub {
    width: 260px;
    margin-left: 20px;
    float: left;
    border-bottom: none;
    padding: 0;
    margin-top: 20px;
    img {
      width: 260px;
      height: 150px;
      overflow: hidden;
      -o-object-fit: cover;
      object-fit: cover;
      float: left;
    }
    .title {
      width: 260px;
      font-weight: bold;
      font-size: 16px;
      opacity: 0.8;
      float: left;
      margin-left: 0;
      line-height: 1.2;
      color: #111;
      margin-top: 10px;
    }
    .info {
      display: none;
    }
    .brief {
      display: none;
    }
  }

  .pagination {
    width: 100%;
    float: left;
    text-align: center;
    .active > span {
      background: $primaryColor;
      border-color: $primaryColor;
    }
  }
}

.list-category {
  width: 100%;
  float: left;
  margin-top: 45px;
  margin-bottom: 30px;
  .list-group-item {
    font-family: "Quicksand";
    border-top: 1px dashed #d1e7e5;
    border-left: none;
    border-right: none;
    border-bottom: 1px dashed #d1e7e5;
    color: #41a336;
    padding-left: 5px;
    padding-right: 5px;
    .blog-count {
      float: right;
    }
  }
  .active {
    font-weight: bold;
    background-color: #fff;
  }
  .item-heading {
    font-family: "Montserrat";
    font-weight: 500;
    font-size: 24px;
    text-transform: capitalize;
    border: none;
    color: #000;
    margin-bottom: 5px;
  }
}

.detail-list-category {
  margin-top: 54px;
}

.related-title {
  width: 100%;
  float: left;
  font-family: "Montserrat";
  font-weight: 500;
  font-size: 24px;
  line-height: 140%;
  text-transform: capitalize;
  border: none;
  color: #000;
  margin-bottom: 5px;
  &:hover {
    color: #41a336;
  }
}
.related-news-item {
  width: 100%;
  float: left;
  padding: 15px 0;
  margin: 0;
  .facebook-new-type {
    margin-top: 0;
    float: left;
    span {
      margin-left: 15px;
    }
  }
  img {
    width: 105px;
    height: 83px;
    overflow: hidden;
    -o-object-fit: cover;
    object-fit: cover;
    float: left;
    border-radius: 4px;
  }
  .title {
    font-family: "Montserrat";
    font-weight: 500;
    font-size: 14px;
    width: 150px;
    float: left;
    margin-left: 15px;
    margin-top: 8px;
    line-height: 1.2;
    color: #222222;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    max-height: calc(14px * 1.2 * 2);
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .info {
    font-family: "Quicksand";
    width: 150px;
    float: left;
    opacity: 0.7;
    margin-left: 15px;
    font-size: 12px;
    margin-top: 5px;
    color: #777777;
  }
}

.blog-detail-container {
  .blog-info-box {
    justify-content: space-between;
    margin-top: 45px;
    .detail-blog-category {
      margin-top: 0;
    }
  }
  .blog-detail-title {
    font-family: "Montserrat";
    font-weight: 600;
    font-size: 30px;
    line-height: 130%;
    text-transform: capitalize;
    color: #222222;
    margin-top: 15px;
    text-align: justify;
  }
  .blog-detail-info {
    font-family: "Quicksand";
    float: left;
    opacity: 0.7;
    font-size: 13px;
    color: #777777;
    margin-top: 2px;
  }
  .blog-social-like {
    width: 100%;
    float: left;
    padding: 10px 0;
  }
  .blog-detail-thumb {
    width: 88%;
    float: left;
    margin-top: 30px;
    margin-bottom: 40px;
    margin-left: 6%;
  }
  .blog-detail-content {
    font-size: 16px;
    .main-content {
      width: 100%;
      float: left;
      padding-bottom: 30px;
      iframe {
        max-width: 100% !important;
      }
      img {
        max-width: 100% !important;
      }
    }
  }
  .related-blogs-container {
    float: left;
    .blog-heading {
      margin-top: 0;
    }
    .related-blogs-content {
      display: grid;
      grid-template-columns: auto auto;
    }
  }
  .comment-container {
    padding-bottom: 40px;
    width: 100%;
    float: left;
    .comment-heading {
      width: 100%;
      float: left;
      margin-top: 20px;
      display: flex;
      span {
        font-family: "Montserrat";
        width: auto !important;
        font-size: 25px;
        color: #333333;
        font-weight: 500;
        padding: 4px 0;
      }
      .border-comment-heading {
        border-bottom: 1px solid #d1e7e5;
        margin-bottom: 10px;
        margin-left: 25px;
        flex: 1;
      }
    }
    .fb-comments {
      margin-left: -5px;
    }
  }
}
