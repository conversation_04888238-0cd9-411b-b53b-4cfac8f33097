@import "variables";

.main{
	.main-center{

		.checkout-container{width: 600px; margin: 50px auto 100px auto; font-size: 16px;
			.label{padding: 5px 10px;}
			.invoice-info{ padding: 20px 15px 20px 15px !important; border-radius: 4px !important; border: 2px dashed #336695 !important;
				background: #e8f1f9 !important; clear: both !important; margin-top: 20px; line-height: 1.5;
				h3 {margin-top: 0; margin-bottom: 20px;}
			}
		}

		.steps-container{ width: 100%; float: left; margin-top: 30px;
			.steps-container-left{width: 680px; float: left; padding-bottom: 50px;
				.invoice-warning{width: 100%; float: left;
					.label{padding: 6px 7px 7px 7px; background: #f47a42;}
					.close{margin: -18px -14px 0 5px;}
				}
				.guest-info-container{background: #EEE; text-align: center; padding: 170px 0;
					.btn-login{padding: 6px 20px; background-color: $primaryColor; color: #fff; border-radius: 20px; margin-left: 10px;
					   cursor: pointer; outline: none; display: inline; margin-top: 4px; font-size: 16px;
					    &:hover{ opacify : 0.9; background: #5cbfaa; color: #fff; }
					}
					.btn-register{padding: 5px 20px; cursor: pointer; border-radius: 20px; border: 1px solid $primaryColor; background-color: #fff;  outline: none;
					    color: $primaryColor; display: inline; margin-top: 4px; cursor: pointer; font-size: 16px;
					    &:hover{ opacify : 0.9; background: #5cbfaa; color: #fff; border-color: #5cbfaa; }
					}
					.btn-buy{padding: 5px 20px; cursor: pointer; border-radius: 20px; border: 1px solid $currencyColor; background-color: #fff; outline: none;
					    color: $currencyColor; display: inline; margin-top: 4px; cursor: pointer; font-size: 16px;
					    &:hover{ opacify : 0.9; background: $currencyColor; color: #fff; border-color: $currencyColor; }
					}
				}
				.customer-info-container{background-color: #fff; width: 100%; float: left; border-radius: 3px; padding-bottom: 30px; border: 5px solid $subColor;
				    .payment-heading{width: 100%; float: left; background: $subColor; color: #fff; margin-bottom: 10px; padding: 8px 0; text-align: left;
						span { font-size: 20px; margin-left: 50px; }
                        .refresh { float: right; font-size: 18px; margin-top: 3px; padding-right: 25px; cursor: pointer; color: #71846d; }
					}

					// step1
					.customer-info-table{ padding: 20px 50px 20px 50px; margin-top: 20px; float: left; width: 100%;
						td {border-top: none; border-top: 1px solid #ddd; padding-left: 0;}
						.important{
							span {color: $currencyColor; cursor: pointer;}
						}
						tr{ &:last-child{border-bottom: none;} }
						.user-form-item{padding: 12px 8px;}
						.user-form-text-area{padding: 5px; width: calc(100% - 50px); border: 1px solid gray; border-radius: 5px;}
						.alert{padding: 5px 10px;}
						.action{float: right; color: $primaryColor;
							i {opacity: 0;}
							&:hover{color: #666; cursor: pointer;
								i{opacity: 0.8;}
							}
						}
						.protected{i {opacity: 0.8 !important;}}
						.close{float: right; font-size: 13px;}
						.error-info{font-size: 12px; color: $currencyColor;}
						.customer-input{margin-bottom: 6px; text-indent: 5px;}
					}

					//step2
					.customer-payment-gates{ padding: 20px 50px 20px 50px; margin-top: 20px; float: left; width: 100%;

					}

					.customer-choose-payment-gates{ padding: 20px 10px 20px 10px; float: left; width: 100%;
						label{margin-bottom: 15px; font-weight: 600;
							input {margin: 10px 10px 0 0; float: left;}
							span{float: left; width: calc(100% - 25px); line-height: 1.2;}
						}
						.delivery-info{margin-top: 20px; padding: 25px 15px; background: #e8f1f9;
							input {margin-bottom: 5px;}
							.important{
								span {color: $currencyColor; cursor: pointer;}
							}
							.alert{padding: 5px 10px;}
						}
                        .payment-right-info-area { table { width: 100% !important; } }
					}

					.customer-successfull-gates{ padding: 20px 40px 20px 40px; float: left; width: 100%;
						.invoice-info{
							padding: 20px 15px 10px 15px !important; border-radius: 4px !important; border: 2px dashed #336695 !important;
							background: #e8f1f9 !important; clear: both !important; margin-top: 20px; line-height: 1.5;
						}
						.pm-notice-method{
							li {padding-left: 25px;}
						}
					}
				}
	            .continue-container{ width: 100%; float: left; margin-top: 10px;
					.dmr-btn{padding: 10px 25px; font-size: 18px; margin-top: 15px; float: right; background-color: $subColor;
		              &:hover{cursor: pointer; opacity: 0.8; }
		            }
		            .buy-btn{padding: 10px 25px; font-size: 18px; margin-top: 15px; float: right; background-color: $currencyColor; color: #fff; border-radius: 6px;
		              &:hover{cursor: pointer; opacity: 0.8; }
		            }
					.light-btn{padding: 10px 25px; font-size: 18px; margin-top: 15px; float: right; background-color: #DDD; color: #999; margin-right: 15px; border-radius: 3px;
		                &:hover{cursor: pointer; opacity: 0.8; }
		            }
				}
			}
			.step-text {
				color: #96D962;
			}
			.steps-container-right{width: 344px; float: left; padding-left: 30px;
				.payment-info-container{ background: #fff; width: 100%; float: left; border-radius: 3px; overflow: hidden; border: 5px solid #96D962;
					.payment-heading{width: 100%; float: left; background: #ccc; color: #444; margin-bottom: 10px; padding: 8px 0; text-align: center;
						background: #96D962; color: #fff; span{font-size: 20px;}
					}
					.combo-item {width: 100%; float: left; padding: 15px 15px 20px 15px;
						.combo-name-container{padding: 20px; min-height: 100px; width: 100%; float: left; text-align:center; background: #96D962; color: #fff;
							border-radius: 4px; margin-bottom: 15px;
							p{margin-bottom: 1px;}
							h1{margin-top: 0; font-size: 30px; word-wrap: break-word; margin-bottom: 0; }
						}
						.combo-detail-container{height: 120px; width: 100%; float: left; background: #fff;
							.course-info{width: 95%; float: left; margin-bottom: 0; margin-top: 5px; font-size: 16px;
							}
							.dmr-btn{width: 120px; float: right; text-align: center; margin-right: 15px;}
						}
					}
					.total-payment{padding: 20px 0 0 0; margin: 5px 15px; border-top: 1px solid #DDD; float: left; width: calc(100% - 30px); box-sizing: border-box;
						span{float: right; font-weight: bold; color: #e74c3c;}
						&.sub {
							border: none;
							margin: 5px 15px;
							padding: 0 !important;
						}
						&:last-child {
							margin-bottom: 10px;
						}
					}
				}
			}
		}

		.nav-wizard{ margin-top: 20px;
			 li {
			  float: left; width: 33.33%; text-align: center; cursor: pointer;
			}
			.step{padding: 0 4px; border-radius: 50%; background-color: $primaryColor; color: #fff;}
			.active{
				.step{background-color: #fff; color: $subColor;}
			}
		}
        .mb-stt{display: none;}
		.nav-wizard > li > a {
		  position: relative;
		  background-color: #eeeeee;
		}
		.nav-wizard > li > a .badge {
		  margin-left: 3px;
		  color: #eeeeee;
		  background-color: #428bca;
		}
		.nav-wizard > li:not(:first-child) > a {
		  padding-left: 34px;
		}
		.nav-wizard > li:not(:first-child) > a:before {
		  width: 0px; height: 0px; border-top: 20px inset transparent; border-bottom: 20px inset transparent; border-left: 20px solid #ffffff; position: absolute; content: ""; top: 0; left: 0; }
		.nav-wizard > li:not(:last-child) > a {
		  margin-right: 6px;
		}
		.nav-wizard > li:not(:last-child) > a:after {
		  width: 0px; height: 0px; border-top: 20px inset transparent; border-bottom: 20px inset transparent; border-left: 20px solid #eeeeee; position: absolute; content: ""; top: 0; right: -20px; z-index: 2; }
		.nav-wizard > li:first-child > a {
		  border-top-left-radius: 4px;
		  border-bottom-left-radius: 4px;
		}
		.nav-wizard > li:last-child > a {
		  border-top-right-radius: 4px;
		  border-bottom-right-radius: 4px;
		}
		.nav-wizard > li.done:hover > a,
		.nav-wizard > li:hover > a {
		  background-color: #d5d5d5;
		}
		.nav-wizard > li.done:hover > a:before,
		.nav-wizard > li:hover > a:before {
		  border-right-color: #d5d5d5;
		}
		.nav-wizard > li.done:hover > a:after,
		.nav-wizard > li:hover > a:after {
		  border-left-color: #d5d5d5;
		}
		.nav-wizard > li.done > a {
		  background-color: #e2e2e2;
		}
		.nav-wizard > li.done > a:before {
		  border-right-color: #e2e2e2;
		}
		.nav-wizard > li.done > a:after {
		  border-left-color: #e2e2e2;
		}
		.nav-wizard > li.active > a,
		.nav-wizard > li.active > a:hover,
		.nav-wizard > li.active > a:focus {
		  color: #ffffff;
		  background-color: $subColor;
		}
		.nav-wizard > li.active > a:after {
		  border-left-color: $subColor;
		}
		.nav-wizard > li.active > a .badge {
		  color: $subColor;
		  background-color: #ffffff;
		}
		.nav-wizard > li.disabled > a {
		  color: #777777;
		}
		.nav-wizard > li.disabled > a:hover,
		.nav-wizard > li.disabled > a:focus {
		  color: #777777;
		  text-decoration: none;
		  background-color: #eeeeee;
		  cursor: default;
		}
		.nav-wizard > li.disabled > a:before {
		  border-right-color: #eeeeee;
		}
		.nav-wizard > li.disabled > a:after {
		  border-left-color: #eeeeee;
		}
		.nav-wizard.nav-justified > li {
		  float: none;
		}
		.nav-wizard.nav-justified > li > a {
		  padding: 10px 15px;
		}
		@media (max-width: 768px) {
		  .nav-wizard.nav-justified > li > a {
		    border-radius: 4px;
		    margin-right: 0;
		  }
		  .nav-wizard.nav-justified > li > a:before,
		  .nav-wizard.nav-justified > li > a:after {
		    border: none !important;
		  }
		}
	}
}
.input-payment {
	border-radius: 5px;
	padding: 20px;
}
