@import "variables";

.fancybox-container{z-index: 122;}


.posts-form-new{ width: 550px !important; min-height: 350px;
	h1{margin-top: 0; font-size: 22px; padding-left: 50px;}
	h4{font-size: 15px; margin-top: 0;}
	.panel-body{padding: 0 0 15px 0;}
	.user-avatar-circle{border-radius: 50%; float: left; width: 35px; height: 35px;}
	.content-input{width: calc(100% - 51px); height: 75px; margin: 0 0 0 15px; float: right; border-radius: 8px;
		outline: none; border: 1px solid #CCC; padding: 7px 15px 7px 10px;}

	.media-container{width: 100%; float: left; padding-left: 50px; display: flex;
    justify-content: space-between;}
	.media-options{float: left;
		.btn-sm{background: #EEE; border-radius: 6px; padding: 3px 10px;}
		.dropdown-item{width: 100%; float: left; padding: 5px 15px; cursor: pointer; color: #333;}
	}

	.media-pick-container{float: left; padding-left: 15px; width: calc(100% - 95px);
		.pick-image{float: left; width: 80%; padding: 3px 0; color: blue;}
		#postsImagePicked{float: left; width: 60%; margin-top: -24px; opacity: 0; cursor: pointer;}
		.embed-link{width: 100%; float: left; border: 1px solid #CCC; border-radius: 4px; outline: none; text-indent: 5px; font-size: 12px; padding: 3px 0;}
		.clear{float: right; margin-right: -50px; cursor: pointer; display: none;}
	}
	.preview-image{width: 100%; float: left; clear: both; padding: 10px 0 0 50px;
		img{float: left; width: 130px; height: 120px; border-radius: 6px; margin: 0 10px 10px 0; object-fit: cover;}
		i{float: left; margin-left: -29px; margin-top: 5px; display: none; &:hover{color: red; cursor: pointer;} }
	}
	.prepare-hashtag{ width: 100%; float: left; clear: both; padding: 5px 0 0 50px;

		.tag-item{padding: 4px 6px; border: 1px solid #EEE; margin: 0 5px 5px 0; float: left; font-size: 11px; border-radius: 3px;
			a{color: green;}
			small{color: green; background: #cbe9d2; padding: 0 2px; border-radius: 2px;}
		}
		.expand-tag{width: 100%; float: left; text-align: center; margin-top: 20px; font-size: 12px; cursor: pointer;}

	}
	.select2-container--default.select2-container--focus .select2-selection--multiple {border: solid #DDD 1px; }

	.post-bottom-area{width: 100%; float: left; border-top: 1px solid #EEE; margin-top: 15px; padding-top: 15px;
		.datepicker{float: left; width: 140px; margin-right: 5px;}
		.timepicker{float: left; width: 110px;}
	}
	.post-btn{float: right; background: #96D962; outline: none; border-radius: 15px; padding: 5px 15px; font-family: 'Montserrat', sans-serif;
	font-size: 12px; margin-top: 2px; font-weight: bold;}
	.posting-icon{height: 13px;}

}


#main-profile-center{
	.avatar-box{float: left;width: 100%; margin-top: -95px; text-align: center; margin-bottom: 20px;
		.profile-avatar{ height: 100px; width: 100px; object-fit: cover; border: 5px solid #fff; box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;}
		.name{font-weight: bold; color: #222; font-size: 18px; span{ background: #fff; border-radius: 6px; padding: 3px 8px; box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px; }}
	}
	.filter-bar{margin-top: 0; font-size: 16px; }
	.search-header{ float: left; width: 100%; margin: 0 0 15px 0;
	}

}

.main{
	.main-community-center{ width: 1120px; margin-top: 34px;

		.not-auth-box{width: 100%; float: left; text-align: center; background: #fff; padding: 20px 30px;}

		a{color: #222;}

		.head-panel{width: 100%; float: left; height: 130px; padding: 25px 20px; background: #fff; margin-bottom: 15px;
			.heading{padding-left: 55px;}
			.cover-left{float: left; height: 100%; overflow: hidden; color: #fff;
	            .logo{float: left; margin: 18px 15px 0 0; height: 40px;}
	            h3{font-size: 14px; font-weight: bold; color: #000; font-family: 'Montserrat', sans-serif;}
	            h4{color: #646464; font-size: 12px;}

	        }
	        .search-box{float: right; width: 200px; margin-top: 40px;
	        	#search-submit{ cursor: pointer; width: 18px; height: 18px; background-size: 90%; background-repeat: no-repeat;
	        		margin-top: 4px; margin-left: 8px; background-position: center;
	        		background: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjxzdmcgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCINCgl3aWR0aD0iMTlweCIgaGVpZ2h0PSIxOXB4IiB2aWV3Qm94PSIwIDAgMTkgMTkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDE5IDE5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCgk8cGF0aCBmaWxsPSIjNjY2NjY2IiBkPSJNMTcuNjMyLDE2Ljk1NWwtNC43NjEtNC43NjJjMS4xMDktMS4xODgsMS43OTUtMi43NzYsMS43OTUtNC41MjdjMC0zLjY2Ny0yLjk4Mi02LjY0OS02LjY0OS02LjY0OQ0KCQljLTMuNjY3LDAtNi42NDksMi45ODItNi42NDksNi42NDhjMCwzLjY2NywyLjk4Miw2LjY0Nyw2LjY0OSw2LjY0N2MxLjM5MSwwLDIuNjgyLTAuNDMyLDMuNzUtMS4xNjRsNC44MzQsNC44MzRMMTcuNjMyLDE2Ljk1NXoNCgkJTTIuODI0LDcuNjY2YzAtMi44NjMsMi4zMy01LjE5Miw1LjE5Mi01LjE5MmMyLjg2NCwwLDUuMTkyLDIuMzI5LDUuMTkyLDUuMTkyYzAsMi44NjEtMi4zMjgsNS4xOTEtNS4xOTIsNS4xOTENCgkJQzUuMTU0LDEyLjg1NSwyLjgyNCwxMC41MjcsMi44MjQsNy42NjZ6Ii8+DQoJPC9zdmc+");}
				.header-search{ margin-top: -25px; float: left; height: 15px;}
				.search-input{width: 200px; font-size: 13px; padding-top:2px; font-weight: 400; margin-top: 1px;
					margin-right: -10px; text-indent: 30px; height: 28px; border-radius: 15px; opacity: 0.8; cursor: pointer; transition: width 0.5s;
					 -moz-transition:width 0.5s;  -webkit-transition:width 0.5s;  -o-transition:width 0.5s; border: 1px solid #CCC !important; }
				.search-input:focus{outline: none; width: 200px; cursor: text; }
	        }
		}



		.body-panel{ width: 100%; float: left; display: flex;
			.community-left-pannel{width: 260px; height: 800px; background: #fff; margin-right: 20px; margin-bottom: 30px; position: sticky; top: 90px;
				.group-icon{width: 25px; height: 25px; float: left;}
				.item-group{padding: 15px 15px 15px 25px; float: left; width: 100%;
					span{font-size: 14px; float: left; width: calc(100% - 40px); margin-left: 10px; line-height: 1.2;}
					strong{margin-left: 10px; font-family: 'Montserrat', sans-serif;}
					p{margin-top: 5px; font-size: 11px; color: #00b530;}
				}
				.active{ background: #F7FFF0; border-left: 4px solid #96D962; font-weight: bold; padding-left: 21px; }
				.master{padding: 20px 15px 20px 12px; cursor: pointer;}
				.empty-groups{padding: 15px 30px 15px 30px; margin: 0 15px 0 15px; border-radius: 4px; border: 1px dashed #CCC; background: #fbfff8; text-align: center; float: left; }
			}
			.community-center-pannel{width: 550px; min-height: 800px;  margin-right: 20px;
				.panel-post{width: 100%; float: left; background: #fff; padding: 20px 15px 10px 15px; margin-bottom: 30px;
					.panel-body{padding: 0 0 15px 0;}
					.user-avatar-circle{border-radius: 50%; float: left; width: 35px; height: 35px;}
					.content-input{width: calc(100% - 155px); height: 35px; margin: 0 0 0 15px; float: left; border-radius: 17px;
						outline: none; border: 1px solid #CCC; padding: 7px 15px 7px 15px;}
					.post-btn{float: right; background: #96D962; outline: none; border-radius: 15px; padding: 5px 15px; font-family: 'Montserrat', sans-serif;
					font-size: 12px; margin-top: 2px; font-weight: bold;}

				}


				.search-header{background: #fff; padding: 10px 15px 11px 15px; border-radius: 10px;
					h4{margin: 0; width: 80%;}
					.bx-x{margin-top: -30px; font-size: 33px; color: red; float: right; cursor: pointer;}

				}

				.filter-bar{width: 100%; float: left; margin-bottom: 10px;
					.select-filter{float: right; outline: none; background: #F7FFF0; border: 1px solid #96D962; border-radius: 13px; padding: 2px 8px 1px 8px; font-size: 12px;}
					.filter-tag{padding: 2px 10px; margin: 0 5px 5px 0; background: green; border-radius: 8px; color: #fff; font-size: 12px;}
				}

				.search-content{ width: 100%; float: left;

					.empty-posts{padding: 50px 20px 50px 20px; border-radius: 4px; border: 1px dashed #CCC; background: #fbfff8; text-align: center; margin-bottom: 15px;}

					.posts-item{ background: #fff; border: 1px solid #EEE; margin-bottom: 20px; width: 100%; float: left;

						.pin-box{padding: 6px 15px 5px 15px; border-bottom: 1px solid #EEE; background: #e3ede6; color: green;
							i{font-size: 20px;}
						}

						.admin-tick{position: absolute; margin: 34px 0 0 -10px; color: #578fff; background: #fff; border-radius: 50%;}

						.posts-top{ min-height: 60px; background: #FFF !important; border-bottom: none !important; margin-bottom: 0 !important; width: 100%; float: left;}

						.post-avatar{width: 40px; height: 40px; border-radius: 20px; float: left; margin-top: 5px;}
						.celebrity-avatar{object-fit: cover;height: 40px; width: 40px; float: left;}
						.post-info{  float: left; margin-top: 5px; width: calc(100% - 50px); margin-left: 10px;
							// .date-posted{margin-top: 25px; margin-right: -13px;}
						}
						.post-info b{font-size: 14px;}
						.post-info span{font-size: 13px; opacity: 0.7; float: right;}
						.tags-list{text-align: left; color: green;
							span{float: left; font-size: 14px;}
							a{vertical-align: top;}
							b{color: green;}
							i.bx{font-size: 18px;}
						}

						.posts-content{font-size: 14px; padding-top: 5px; width: 100%; padding-bottom: 20px;
							white-space: pre-wrap;word-wrap:break-word; display:inline-block;  width: 100%; float: left;
							font-weight: 500; font-family: arial,'lucida grande',sans-serif; color: #111; overflow: hidden;
							display: block;
						    line-height: 1.4;
						    -webkit-line-clamp: 6;
						    max-height: calc(14px * 1.41 * 6);
						    margin: 0 auto;
						    font-size: 14px;
						    -webkit-box-orient: vertical;
						    overflow: hidden;
						    text-overflow: ellipsis;

							.see-more{ margin-bottom: 30px; color: blue; cursor: pointer;}
							a {color: #1b3af2; text-decoration: none;}
							.hashtag{margin-top: 10px;}
							padding: 0 15px 10px 15px;
						}

						.expand-content{padding: 0 0 10px 65px; color: green; float: left; cursor: pointer;}

						section{width: 100%; float: left;}
						.update-posts{width: 100%; resize: none; border: 1px solid #CCC; overflow: hidden;}
						.update-posts:focus{outline: none;}
						.posts-option{float: right; cursor: pointer; margin: -2px -5px 0 8px;
							.dropdown-menu{margin-left: -60px; min-width: 90px;}
							i{font-size: 16px;}
						}
						.album{ display: flex; justify-content: space-evenly; flex-flow: no-wrap;
							img{ object-fit: cover; margin-bottom: 5px; border: 0.5px solid #DDD; }
							.single-image{width: 550px; max-height: 550px;}
							.two-image{width: 260px; height: 260px;}
							.three-image{width: 32%; height: 200px;}
							//
						}
						.slick-gallery{width: 100%; height: 250px;
							.slick-item{width: 230px; height: 230px; float: left; margin-right: 10px;}
							.multiples-image{width: 100%; height: 100%; object-fit: cover; margin-bottom: 5px; border: 0.5px solid #DDD; }
							.slick-arrow{width: 35px; height: 35px;
								&:before{color: green;}
							}
							.slick-arrow{background: transparent !important;}
						}

						iframe{min-height: 315px; background: #EEE;}
						blockquote{margin: 0; border: none; padding: 0 10px;
							iframe{background: #EEE;}
						}

						.posts-bottom-action{width: 100%; float: left; padding: 13px 30px 13px 30px; display: flex; justify-content: space-between;
							border-top: 0.5px solid #EEE;
							span{ cursor: pointer;
								i{font-size: 18px; vertical-align: sub;}
							}
							.active{color: #00b530; a{color: #00b530;}}
						}


						.comment-box{width: 100%; float: left; padding: 15px 15px 10px 15px; border-top: 1px solid #EEE; background: #fafafa;
							.list-comments .comment-item{border-top: none; border-bottom: 1px solid #EEE;}
							.love-btn{cursor: pointer;}
							.comment-content {
								.name{ font-size: 14px; b{color: #333;} }
								.child-name{ font-size: 14px;
									b{color: #444;}
								}

								a{color: #1b3af2;}

								.child-comment-action{ font-size: 13px;
									.time{opacity: 0.6; font-size: 12px;}
								}
								.end-of-list{margin: 0;}
							}

							.admin-tick{margin-top: 24px;}

							.cmt-option{ float: right;
								.dropdown-toggle{cursor: pointer;}
								li{cursor: pointer;}
							}
							.load-more-comment{background: none; color: green;}
						}
					}

					.load-more-posts{width: 100%; float: left; text-align: center; background: green; color: #fff; padding: 5px 0; border-radius: 2px; margin-bottom: 50px;
					    &:hover{opacity: 0.9; cursor: pointer;}
					    .loading-icon{width: 20px;}
					}
					.end-of-list{width: 100%; float: left; text-align: center; color: #444; padding: 5px 0; border-radius: 2px; border: 1px dashed #DDD; margin-bottom: 50px;}

				}

			}
			.community-right-pannel{width: 270px; min-height: 800px; //position: sticky; top: 60px;

				.file-panel{width: 100%; background: #fff; float: left; margin-bottom: 20px;
					h3{margin: 5px 0; border-bottom: 1px solid #EEE; font-family: 'Montserrat', sans-serif; font-weight: bold; font-size: 17px;}
					.file-body{padding: 15px 18px; width: 100%; float: left; display: flex; justify-content: space-between; flex-flow: wrap;
						.file-item{ width: 65px; height: 65px; object-fit: cover; margin-bottom: 8px;
						}
						.expand-tag{width: 100%; float: left; text-align: center; margin-top: 20px; font-size: 12px; cursor: pointer;}
					}
				}
				.tag-panel{width: 100%; background: #fff; float: left; margin-bottom: 20px;
					h3{margin: 5px 0; border-bottom: 1px solid #EEE; font-family: 'Montserrat', sans-serif; font-weight: bold; font-size: 17px;}
					.tag-body{padding: 15px; width: 100%; float: left;
						.tag-item{padding: 4px 6px; border: 1px solid #EEE; margin: 0 5px 5px 0; float: left; font-size: 11px; border-radius: 3px;
							a{color: green;}
							small{color: green; background: #cbe9d2; padding: 0 2px; border-radius: 2px;}
						}
						.expand-tag{width: 100%; float: left; text-align: center; margin-top: 10px; font-size: 12px; cursor: pointer;}
					}
				}
				.test-panel{width: 100%; background: #fff; float: left; margin-bottom: 20px;
					h3{margin: 5px 0; border-bottom: 1px solid #EEE; font-family: 'Montserrat', sans-serif; font-weight: bold; font-size: 17px;}
					.test-body{padding: 15px 10px; width: 100%; float: left; display: flex; justify-content: space-between; flex-flow: wrap;
						.test-item{ width: 65px; height: 65px; object-fit: cover; margin-bottom: 8px;
						}
						.expand-tag{width: 100%; float: left; text-align: center; margin-top: 20px; font-size: 12px; cursor: pointer;}
						.test-list {
							margin-top: 10px;
							&-item {
								padding: 10px;
								background: #eee;
								margin: 10px 0;
								border-radius: 5px;
								cursor: pointer;
								&:hover,
								&.active {
									outline: 2px solid #ccc;
								}
							}
						}
					}
				}
			}
		}
	}
}
