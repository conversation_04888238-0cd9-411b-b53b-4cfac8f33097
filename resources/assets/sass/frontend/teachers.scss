@import "variables";
@import "mixins";
.teacher {
  &__container {
    width: 1024px;
    margin: 0 auto;
  }
  &__screen {
    &--wrapper {
      .v-modal-container {
        background: transparent !important;
        box-shadow: none !important;
        overflow: visible !important;
        width: 80vw;
        height: 80vh;
      }
    }
    &--slider {
      background: rgba(0,0,0,0.35) url("../img/teacher/teacher_slider_bg.png") no-repeat center;
      background-size: cover;
      background-blend-mode: overlay;
      position: relative;
      &-content {
        color: #FFF;
        font-size: 100px;
        .owl-carousel {
          height: 100%;
          position: relative;
        }
      }
      .owl-dots#carousel-custom-dots {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        bottom: 0;
        text-align: center;
        width: 100%;
        height: 10%;
        .owl-dot {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 15px;
          margin-left: 15px;
          background: none;
          border: 2px solid #FFF;
          cursor: pointer;
          z-index: 9999;
          &.active {
            background: #FFF;
          }
        }
      }
      &-slide {
        height: 100vmax * (620/1440);
        padding: 0;
        position: relative;
      }
      &-avatar {
        width: 22%;
        overflow: visible;
        position: absolute;
        bottom: 0;
        left: 18%;
        &-mask {
          position: absolute;
          bottom: 0;
          left: 16%;
          width: 25%;
          height: 27%;
          border-radius: 30px 30px 0 0;
          background: rgba(149, 217, 139, 0.3);
        }
      }
      &-info {
        position: absolute;
        bottom: 33%;
        left: 46%;
        width: 40%;
      }
      &-title {
        font-size: 2.5vw;
        line-height: 4.25vw;
      }
      &-description {
        font-size: 1vw;
        line-height: 1.7vw;
      }
      &-buttons {
        position: absolute;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 40%;
        left: 46%;
        bottom: 25%;
        margin-top: 4%;
        flex-flow: row;
        font-size: 15px;
      }
      &-button {
        display: flex;
        flex-flow: row;
        justify-content: space-between;
        align-items: center;
        &-detail {
          width: 20%;
          height: 35px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 25px;
          background: rgba(149, 217, 139, 1);
          text-decoration: none;
          color: $gray-100;
          cursor: pointer;
          transition: 0.3s;
          user-select: none;
          font-size: 1vw;
          svg {
            path {
              stroke: $gray-100;
            }
          }
          &-sm {
            font-size: 14px;
          }
          &-md {
            font-size: 18px;
          }
          &:hover {
            background: rgb(171, 255, 167);
            text-decoration: none;
            color: $gray-100;
          }
          &:active {
            text-decoration: none;
            color: $gray-100;
          }
        }
        &-video {
          width: 30%;
          height: 35px;
          display: flex;
          padding: 0 10px;
          flex-flow: row;
          justify-content: center;
          font-size: 0.8vw;
          align-items: center;
          border-radius: 25px;
          color: #FFF;
          cursor: pointer;
          transition: 0.3s;
          user-select: none;
          &-sm {
            font-size: 11px;
          }
          &-md {
            font-size: 0.8vw;
          }
          &:hover {
            box-shadow: 0 0 5px #fff;
            text-decoration: none;
            color: $gray-100;
          }
        }
      }
    }

  }
  &__text {
    &--block {
      display: flex;
      height: 200px;
      margin: 50px auto;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      font-family: 'Roboto', Arial, sans-serif;
    }
    &--title {
      font-weight: 700;
      text-transform: uppercase;
      &-medium {
        font-size: 40px;
      }
    }
    &--content {
      font-size: 20px;
      line-height: 30px;
      font-weight: 300;
    }
  }
  &__cards {
    padding-top: 50px;
    margin: 0 auto 100px auto;
    &--feature {
      display: flex;
      flex-flow: row;
      justify-content: center;
      margin-top: 120px;
    }
    &--normal {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;
    }
  }
  &__card {
    @include aspect-ratio(312, 613);
    border-radius: 24px;
    color: $gray-100;
    margin: 150px 1% 0 1%;
    width: 30%;
    //background-image: url("../img/teacher/teacher-item-bg.jpg");
    background-size: cover;
    background-position: center;
    &--info {
      width: 100%;
      height: 100%;
      background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='24' ry='24' stroke='%23293142FF' stroke-width='2' stroke-dasharray='14' stroke-dashoffset='27' stroke-linecap='butt'/%3e%3c/svg%3e");
      background-color: $white;
      border-radius: 24px;
      padding: 40% 30px 30px 30px;
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-flow: column;
      justify-content: flex-start;
      align-items: center;
      transition: 0.2s ease-in-out;
      :hover > & {
      }
    }
    &:hover {
      .teacher-image {
        border: none;
        background: $green-secondary;
        .circle {
          border: none;
          background: $green-secondary;
          box-shadow:  20px 20px 60px #bdd7bd,
          -20px -20px 60px #ffffff;
        }
      }
    }
    .teacher-image {
      &-wrapper {
        position: absolute;
        top: -18%;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      width: 70%;
      @include aspect-ratio(1,1);
      background: $white;
      border: 1px solid $gray-75;
      border-radius: 50%;
      user-select: none;
      overflow: hidden;
      transition: 0.2s ease-in-out;
      .image-inner {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-flow: column;
        justify-content: flex-end;
        align-items: center;
        z-index: 1;
      }
      .circle {
        width: 90%;
        @include aspect-ratio(1,1);
        border: 2px solid $gray-75;
        background-color: $white;
        border-radius: 50%;
        position: absolute;
        top: 5%;
        left: 5%;
        z-index: 0;
        transition: 0.2s ease-in-out;
      }
      img {
        height: 85%;
      }
    }
    &--feature {
      width: 452px;
      @include aspect-ratio(452, 532);
      .teacher-image {
        &-wrapper {
          top: -29.5%;
        }
      }
    }
    &--full-name {
      font-size: 30px;
      height: 20%;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      :hover > & {
        //color: $white;
      }
    }
    &--desc {
      width: 100%;
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      font-weight: 400;
      :hover > & {
        //color: $white;
        //font-weight: 400;
      }
    }
    &--desc-short {
      text-align: left;
      font-family: 'Roboto', Arial, sans-serif;
      &-sm {
        font-size: 15px;
        line-height: 25px;
      }
      &-md {
        font-size: 18px;
        line-height: 30px;
      }
    }
    &--button {
      width: 100%;
      display: flex;
      flex-flow: row;
      justify-content: space-between;
      align-items: center;
      font-family: Montserrat, Arial, sans-serif;
      margin-top: auto;
      &-detail {
        height: 35px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 25px;
        text-decoration: none;
        color: $gray-100;
        cursor: pointer;
        transition: 0.3s;
        user-select: none;
        font-size: 18px;
        svg {
          path {
            stroke: $gray-100;
          }
        }
        &-sm {
          font-size: 14px;
        }
        &-md {
          font-size: 18px;
        }
        &:hover {
          text-decoration: none;
          color: $green-dark;
          svg {
            path {
              stroke: $green-dark;
            }
          }
        }
      }
      &-video {
        height: 40px;
        display: flex;
        padding: 0 10px;
        flex-flow: row;
        justify-content: center;
        font-size: 15px;
        align-items: center;
        border-radius: 25px;
        color: $gray-100;
        cursor: pointer;
        transition: 0.3s;
        user-select: none;
        &-sm {
          font-size: 11px;
        }
        &-md {
          font-size: 15px;
        }
        &:hover {
          text-decoration: none;
          color: $gray-100;
          background: transparent;
          box-shadow:  8px 8px 16px #bcbcbc,
          -8px -8px 16px #ffffff;
        }
      }
    }
  }
  &__subscribe {
    width: 100%;
    height: 200px;
    background: #4A7349;
    color: #FFF;

    margin-top: 50px;
    &--content {
      height: 100%;
      display: flex;
      flex-flow: row;
      justify-content: space-between;
      align-items: center;
    }
    &--title {
      width: 50%;
      font-size: 28px;
      line-height: 38px;
      font-weight: 700;

    }

    &--button {
      a {
        display: block;
        position: relative;
        margin: 0;
        z-index: 1;
        text-decoration: none;
        color: #FFF;
        span {
          border: none;
          font-family: inherit;
          font-size: inherit;
          color: inherit;
          background: #97DA8C;
          cursor: pointer;
          padding: 15px 50px;
          display: inline-block;
          margin: 15px 30px;
          text-transform: uppercase;
          letter-spacing: 1px;
          font-weight: 700;
          outline: none;
          position: relative;
          box-shadow: 6px 6px #FFF;
          -webkit-transition: none;
          -moz-transition: none;
          transition: none;
          -webkit-user-select: none;
          -ms-user-select: none;
          user-select: none;
          &:hover {
            box-shadow: 4px 4px #FFF;
            top: 2px;
          }
          &:active {
            box-shadow: 0 0 #FFF;
            top: 6px;
          }
        }
      }
    }
  }
}
.teacher-detail {
  &__info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  &__avatar {
    width: 400px;
    height: 400px;
    border-radius: 25px;
    border: 2px solid $gray-75;
    background-color: #D8EDB9;
    padding: 25px;
    position: relative;
    svg {
      position: absolute;
      left: -90px;
      top: -80px;
    }
    &-small {
      background-color: $white;
      border-radius: 20px;
      border: 2px solid $gray-100;
      padding: 20px;
      width: 100%;
      height: 100%;
      position: relative;
    }
    &-image {
      position: absolute;
      height: 150%;
      bottom: 0;
      left: 10%;
    }
  }
  &__overview {
    flex: 1;
    position: relative;
    margin-left: 20px;
    &-icon {
      position: absolute;
      right: 0;
      top: 40px;
    }
  }
  &__screen {
    &--wrapper {
      margin-top: 7px;
      padding-bottom: 100px;
      p {
        font-family: 'Roboto', Arial, sans-serif !important;
      }
    }
    &--slider {
      @include aspect-ratio(1920,821);
      background: url("../img/teacher/teacher-bg.png") no-repeat center;
      background-size: cover;
      background-blend-mode: overlay;
      position: relative;
      z-index: -999;
      &-inner {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    &--info {
      width: 100%;
      margin: 0 auto;
      position: relative;
      font-family: 'Roboto', Arial, sans-serif;
      &-avatar {
        position: absolute;
        width: 100%;
        bottom: 100%;
        text-align: center;
        img {
          width: 20vw;
        }
      }
      &-fullname {
        width: 40vw;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 4vw;
        background: rgba(17, 60, 12, 0.5);
        color: #FFF;
        position: absolute;
        top: 4vw;
      }
      &-description {
        padding: 0 5%;
        p {
          font-family: 'Roboto', Arial, sans-serif !important;
        }
      }
      &-content {
        position: relative;
        width: 60%;
        min-height: 100%;
        padding: 3vw 0;
        margin: -5% auto 0 auto;
        border-radius: 20px;
        background-color: $white;
        border: 2px dashed $gray-50;
      }
      &-biography {
        position: absolute;
        width: 100%;
        bottom: 250%;
        left: 90%;
        &-content {
          //text-indent: 2vw;
          padding: 1.5rem 0;
          text-align: justify;
          background: rgba(0,0,0,0.3);
          background: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.8) 50%, rgba(0,0,0,0) 100%);
          p {
            color: #FFF !important;
            font-size: 1.2vw !important;
            line-height: 2.04vw !important;
            font-weight: 100;
            font-family: 'Roboto', Arial, sans-serif !important;
          }
        }
      }
    }
    &--separate {
      width: 20vw;
      border-bottom: 10px solid #72C169;
      margin-top: 1vw;
    }
    &--video,
    &--story {
      width: 53%;
      margin: 100px auto 0 auto;
      &-header {
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
        font-size: 2vw;
        line-height: 51px;
        margin: 2vw auto;
      }
      &-content {
        width: 60%;
        margin: 0 auto;
        img {
          width: 100%;
        }
        .v-modal-container {
          background: transparent !important;
          box-shadow: none !important;
          overflow: visible !important;
          width: 80vw;
          height: 80vh;
        }
      }
    }

    &--video {
      &-image {
        &-mask {
          cursor: pointer;
          width: 100%;
          height: 100%;
          background: rgba(255,255,255,0.5);
          position: absolute;
          left: 0;
          top: 0;
          color: rgba(94, 187, 94, 1);
          transition: 0.3s;
          &:hover {
            background: rgba(255,255,255,0.1);
            color: rgb(0, 109, 1);
          }
        }
      }

    }
    &--timeline {
      &-content {
        width: 100%;
        font-size: 1vw;
        line-height: 1.7vw;
        margin: 50px auto;
        ul {
          list-style-type: circle;
          li {
            padding: 1vw 4vw;
            font-family: 'Roboto', Arial, sans-serif !important;
            &:nth-child(odd) {
              background: #E9E9E9;
            }
            p {
              font-family: 'Roboto', Arial, sans-serif !important;
            }
          }
        }
      }
    }
    &--comments {
      width: 70%;
      margin: 100px auto 0 auto;
      &-header {
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
        font-size: 2vw;
        line-height: 51px;
        margin: 2vw auto;
      }
    }
  }

}
