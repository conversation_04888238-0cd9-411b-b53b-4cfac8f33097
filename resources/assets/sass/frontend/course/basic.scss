.pc-header {
  background-color: rgb(255 255 255 / 0.5);
  backdrop-filter: blur(4px);
  .container {
    max-width: 100%;
    width: 90%;
  }
  .header-menu {
    display: none;
  }
  .stage {
    display: none;
  }
  .leaf {
    display: none;
  }
  .logo1 {
    display: none;
  }
  .logo2 {
    display: block !important;
    img {
      width: 176px;
    }
  }
  .icon-color {
    color: rgb(7 58 59 / 1);
  }
  .header-notification {
    .dropdown-toggle-icon {
      color: rgb(7 58 59 / 1);
    }
  }

  .auth-button {
    padding-left: 0;
    border: none;
    span {
      display: none;
    }
    background: transparent;
    font-family: "Beanbag_Dungmori_Rounded";
    #text-login,
    #text-register {
      color: #07403f;
      font-size: 16px;
    }

    #text-register {
      margin-left: 10px;
      height: 38px;
      width: 152px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      background: #ec6e23;
      border-radius: 9999px;
    }
  }
}
.mobile-header {
  padding-top: 0;
  background-color: rgb(255 255 255 / 0.5) !important;
  backdrop-filter: blur(4px);
  #nav-icon {
    display: none;
  }
  #logo {
    display: none;
  }
  .logo3 {
    display: block !important;
    img {
      width: 45px;
    }
  }
  .icon-color {
    color: rgb(7 58 59 / 1);
  }
  #account-container {
    display: flex;
    align-items: center;
    justify-content: end;
  }
  .login-button {
    color: #07403f;
    font-size: 16px;
    background-color: transparent;
    border: none;
  }
  .register-button {
    border: none;
    color: #07403f;
    font-size: 12px;
    height: 32px;
    width: 112px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    background: #ec6e23;
    border-radius: 9999px;
  }
  .btn-store {
    display: inline-block !important;
  }
  .svgIcon--bell {
    display: none;
  }
  #auth-container {
    margin-top: 0 !important;
    display: flex;
    align-items: center;
    .btn-profile {
      display: block !important;
    }
  }
  .messenger-icon {
    .stroke-current {
      color: rgb(7 58 59 / 1);
    }
  }
}
.header-avatar {
  .dropdown-toggle,
  .dropdown-menu {
    display: none;
  }
  .btn-profile {
    display: block !important;
    @media screen and (min-width: 1024px) {
      .menu {
        right: 4.2%;
      }
    }
  }
}
.course-basic {
  .video-js {
    max-width: 100%;
    width: 100%;
    video {
      width: 100% !important;
    }
    .vjs-poster img {
      max-width: 100%;
      object-fit: cover;
    }
  }
  .tutorial {
    .course-step {
      visibility: hidden;
      &.step-4 {
        transition-delay: 200ms;
      }
      &.active {
        visibility: visible;
      }
    }
  }
  .stages {
    .stage {
      .head {
        background-color: #e6e6e6;
        .arrow-icon {
          position: relative;
          top: 2px;
        }
        .learning {
          display: none;
        }
        &.is-open {
          background-color: #c1eaca;
          .arrow-icon {
            transform: rotate(180deg);
            top: 0;
          }
          .learning {
            display: flex;
          }
          .schedule {
            display: none;
          }
        }
      }
    }
  }

  .lesson-progress {
    position: relative;
    &::after {
      content: "";
      display: block;
      position: absolute;
      left: -3px;
      top: -3px;
      right: -3px;
      bottom: -3px;
      background-color: transparent;
      background-image: conic-gradient(
        orange,
        orange var(--data-percent),
        transparent var(--data-percent)
      );
      z-index: -100;
      border-radius: 21.5%;
    }
  }
}

.lesson-tutorial {
  .lesson-step {
    visibility: hidden;
    &.active {
      visibility: visible;
    }
  }
}

.lesson-group-item {
  .group-list {
    display: none;
  }
  &.is-open {
    .group-list {
      display: grid;
    }
    .arrow-icon {
      transform: rotate(90deg);
    }
  }
}

.tooltip-custom__wrapper {
  position: relative;
  .tooltip-custom {
    display: none;
    font-size: 16px;
    color: #07403f;
    background: #c1eaca;
    white-space: nowrap;
    padding: 3px 13px;
    border-radius: 5px;
  }
  &:hover {
    .tooltip-custom {
      display: block;
    }
  }
}

.lesson {
  .video {
    .video-js {
      width: 100%;
      position: relative;
      padding-top: 56.3%;
    }
  }
  .server-video {
    &.active {
      background: #c1eaca;
    }
  }
  .quality-video {
    &.active {
      background: #d9d9d9;
    }
  }
}
#lesson-list {
  .btn-show-menu {
    .tooltip-menu {
      right: -200px;
    }
    &:hover {
      .tooltip-menu {
        right: 85px;
      }
    }
  }
}
#lesson-basic-container {
  &.training {
    .menu-content {
      outline: rgba($color: #000000, $alpha: 0.9) solid 5000px;
    }
    .overlay-tutorial {
      height: 80px !important;
    }
  }
}
#lesson-main {
  &.menu-hidden {
    .lesson-main-container {
      margin-left: auto;
      margin-right: auto;
    }
  }
}
.group-list {
  .show-arrow {
    .lock-icon {
      display: none;
    }
  }
  .show-lock {
    .arrow-group {
      display: none;
    }
  }
  .lessons {
    display: none;
  }
  &.is-open {
    .arrow-group {
      rotate: 90deg;
      display: block;
    }
    .lessons {
      display: block;
    }
    .lock-icon {
      display: none;
    }
  }
}
.require-icon {
  position: relative;
  top: -5px;
  &.text-over {
    position: absolute;
    right: 0;
    top: 0;
  }
}

.btn-download {
  .download-icon {
    position: relative;
    &.downloaded {
      &::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        bottom: 0;
        right: 0;
        background-image: url("/images/icons/done.png");
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
      }
    }
  }
}

@keyframes shadow-first-lesson {
  0% {
    box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
  }

  50% {
    box-shadow: 0 0 9px 0 var(--data-first-lesson-color);
  }

  to {
    box-shadow: 0 0 25px 0 var(--data-first-lesson-color);
  }
}

.first-lesson {
  animation: shadow-first-lesson 1s infinite;
}
