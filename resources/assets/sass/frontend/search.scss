@import "variables";

.main{
	.search-key-title{width: 100%; float: left; margin-top: 40px; margin-bottom: 20px;}
	.all-search-result-container{float: left; width: 700px; padding-bottom: 40px; display: -webkit-box; display: flex; 
		-webkit-box-pack: justify; justify-content: space-between; flex-wrap: wrap;
		.search-result-item{width: 100%; border-bottom: 1px solid #EEE; padding: 15px 0; margin: 0;
			.image-search-item{float: left; width: 100px; height: 100px; border-radius: 3px; border: 1px solid #EEE; object-fit: cover;}
			.infor-box{ padding-right: 10px; float: left; width: calc(100% - 140px); padding-left: 20px;
				.title-search-item{font-size: 20px; 
					.category-name{color: #AAA; font-size: 16px; font-weight: 500; }
				}
				.content-search-item{float: left; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; color: #888; width: 100%; }
			}
		}
	}
	.paginate-container{width: 700px; float: left;
		.pagination{float: right;}
	}
}