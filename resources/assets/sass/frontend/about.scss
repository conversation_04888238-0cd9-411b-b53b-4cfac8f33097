/** <PERSON><PERSON> v<PERSON><PERSON> banner slide full width */
#about-banner-slider {
  position: relative;
  margin: 0 auto;
  width: 100%;

  .center .slick-center img {
    transform: scale(0.95);
    color: #e67e22;
    opacity: 1;
  }

  .center img {
    opacity: 0.8;
    transition: all 300ms ease;
    bottom: 0;
    transform: scale(0.75);
  }

  .slider {
    width: 100%;

    .slick-dots {
      bottom: 50px;

      li button:before {
        font-size: 10px;
      }

      li.slick-active button:before {
        color: #96D962;
      }
    }

    .slick-current {

      .slide-img:hover {
        .teacher {
          .main-img {
            display: none;
          }

          .hover-img {
            display: block;
          }
        }
      }

      .banner-left-fix {
        display: block !important;
      }

      .banner-right-fix {
        display: block !important;
      }

      .teacher {
        display: block !important;
      }
    }

    .slide-arrow {
      width: 240px;
      height: 480px;
      cursor: pointer;
    }

    .prev-arrow {
      top: 8%;
      opacity: 0;
      position: absolute;
      z-index: 9999;
    }

    .next-arrow {
      position: absolute;
      top: 70px;
      left: 87%;
      opacity: 0;
      z-index: 99999;
    }

    .slide-item {
      position: relative;
      margin-bottom: 80px;

      .banner-left-fix {
        display: none;
        position: absolute;
        top: 14%;
        left: -7%;
      }

      .banner-right-fix {
        display: none;
        position: absolute;
        top: 7%;
        left: 80%;
      }

      .teacher {
        display: none;
        position: absolute;
        top: 71%;
        left: 69%;
        width: 250px;

        .hover-img {
          display: none;
        }
      }

      .teacher:hover {
        .main-img {
          display: none;
        }

        .hover-img {
          display: block;
        }
      }
    }

    .banner-second {
      .banner-right-fix {
        top: 2% !important;
      }
    }
  }
}

/** lớp bao toàn bộ trang about us*/
#about-container{
  /** giới thiệu dungmori */
  .gioi-thieu-dmr {
    text-transform: uppercase;
    font-size: 45px;
    font-weight: bold;
    color: #000000;
  }



  /** thông tin về dungmori */
  .about-title {
    display: flex;
    flex-flow: column;
    align-items: center;

    .about-title__name {
      padding: 2px 5px;
      font-family: 'Mulish', sans-serif;
      font-weight: 700;
      font-size: 25px;
      text-align: center;
      background: #F7FFF0;
      border: 2px solid #293142;
      border-bottom: none;
    }

    .about-title__desc {
      width: 80%;
      font-family: Montserrat, sans-serif;
      font-weight: 700;
      font-size: 35px;
      line-height: 1.61;
      color: #293142;
      border-top: 2px solid #000000;
      text-transform: uppercase;
      padding: 10px 15px;
    }
  }

  /** tổng quan về dungmori */
  .tong-quan {
    margin: 50px 0;

    .para-desc {
      margin: 30px 10%;
      font-family: 'Quicksand';
      font-style: normal;
      font-weight: 400;
      font-size: 18px;
      line-height: 22px;
      text-align: center;
    }

    .item-zone {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
    }

    .tq-item {
      border: 1px dashed #A4A4A4;
      border-radius: 30px;
      width: 300px;
      height: 360px;
      text-align: center;
      background-color: #FCFFF9;
      padding: 0 20px;

      span {
        display: block;
        font-size: 24px;
        font-weight: 700;
        color: #000000;
        margin-top: 24px;
      }

      img {
        margin: 35px 0;
      }

      p{
        font-family: 'Quicksand';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        text-align: center;
      }
    }
  }

  /** sản phẩm của dungmori */
  .about-product {
    margin-top: 30px;
    .item-product {
      display: flex;;
      margin-bottom: 80px;

      .pr-item-img {
        width: 40%;

        img {
          max-width: 148px;
        }
      }

      .pr-item-content {
        text-align: left;
        width: 60%;
        p{
          text-align: justify;
          font-family: 'Quicksand';
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 18px;
          color: #000000;
        }
      }

      .view-detail {
        margin-top: 20px;
        color: #828282;
        font-size: 18px;
        font-family: 'Quicksand';
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 5px;

        i {
          font-size: 25px;
        }
      }
    }

    .pr-title {
      margin: 0;
      margin-bottom: 15px;
      font-family: 'Montserrat';
      font-style: normal;
      font-weight: 700;
      font-size: 24px;
      line-height: 29px;
    }
  }

  /** cam kết dungmori */
  .commit {
    .commit-box {
      margin-top: 10vh;
      .commit-img{
        display: inline-block;
        padding: 27px;
        border: 1px solid #96D962;
        box-shadow: 0px 3.92857px 19.6429px rgba(0, 0, 0, 0.1);
        border-radius: 35.9778px;
      }
      .box-mobile{
        display: none;
      }
    }

    .commit-content-item {
      img {
        width: 80%;
      }

      p {
        text-align: justify;
        margin-left: 26%;
        margin-right: 10%;
        padding-top: 20px;
        font-family: 'Quicksand';
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        color: #4F4F4F;
        line-height: 25px;
        letter-spacing: -0.145454px;
      }
    }
  }

  /** custome slick*/
  .pull-left,.pull-right{
    position: absolute;
    top: 25%;
    border: none;
    padding: 0;
  }

  .slick-arrow i{
    font-size: 30px;
    cursor: pointer;
  }

  .slick-list{
    padding: 0 40px;
  }

  .pull-right{
    right: -25px;
  }

  /** css quan niệm giáo dục dungmori*/
  .slogant {
    margin-top: 120px;

    .slogant-content {

      .slogant-start {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        margin-bottom: 20px;

        h5 {
          color: #353535;
          font-family: 'Quicksand';
          font-style: normal;
          font-weight: 700;
          font-size: 20px;
          line-height: 25px;
        }
      }

      .slogant-image {
        margin: 20px 0;
      }

      .slogant-desc {
        width: 84%;
        margin: 0 auto;
        margin-bottom: 90px;
        font-family: 'Quicksand';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        text-align: center;
        color: #000000;
      }
    }
  }

  .slick-current {
    h3 {
      color: #000000;
      font-family: 'Montserrat';
      font-style: normal;
      font-weight: bold;
      font-size: 24px;
      line-height: 29px;
      border-bottom: 5px solid #96D962;
    }
  }

  .slick-next:before {
    content: ">";
    color: #000000;
    font-size: 35px;
    font-weight: bold;
    line-height: 0.7;
  }

  .slick-prev:before {
    content: "<";
    color: black;
    font-size: 35px;
    font-weight: bold;
    line-height: 0.7;
  }

  .slick-arrow {
    background: none !important;
    padding: none !important;
    border-radius: none !important;
  }

  .sl-item {
    display: flex !important;
    gap: 60px;
    padding: 0 80px;
    align-items: center;

    .sl-content {
      text-align: left;

      h2 {
        margin-top: 0;
        font-family: 'Montserrat';
        font-style: normal;
        font-weight: 700;
        font-size: 24px;
        margin-bottom: 15px;
      }

      p {
        font-family: 'Quicksand';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        text-align: justify;
      }
    }
  }
}

/** responsive mobile screen */
@media  only screen and (max-width: 768px) {
  .tong-quan {
    .item-zone {
      flex-direction: column !important;
      .tq-item{
        margin: 0 auto;
        margin-bottom: 30px;
      }
    }
  }

  .sl-item{
    flex-direction: column;
    padding: 0 20px;
  }

  .slick-arrow{
    display: none !important;
  }

  .slick-list{
    padding: 0 !important;
  }

  .box-desktop{
    display: none;
  }

  .box-mobile{
    display: block !important;
  }

  .commit-content-item {
    img {
      padding-top: 30px;
    }

    p {
      padding: 0 20px;
      margin: 0 !important;
    }
  }

  .slogant{
    .slogant-content{
      .slogant-start{
        flex-direction: column;
      }
    }
  }

  .item-product{
    flex-direction: column;
    align-items: center;
    margin-bottom: 45px !important;
    .view-detail{
      display: block !important;
    }
    .pr-item-img{
      width: 100% !important;
      margin-bottom: 25px;
    }
    .pr-item-content{
      width: 100% !important;
      text-align: center !important;
    }
  }

  .box-desktop{
    display: none;
  }

  .box-mobile{
    display: block !important;
  }

  .sl-item{
    flex-direction: column;
    padding: 0 20px;
  }
}

@media only screen and (max-width: 425px) {
  .banner-left-fix {
    img {
      width: 25% !important;
    }
  }

  .slider {
    .slide-item {
      img {
        width: 350px !important;
      }

      .banner-left-fix {
        img {
          width: 35% !important;
        }
      }

      .teacher {
        top: 61% !important;
        left: 65% !important;
        width: 200px !important;

        img {
          width: 55% !important;
        }
      }
    }
  }
}
