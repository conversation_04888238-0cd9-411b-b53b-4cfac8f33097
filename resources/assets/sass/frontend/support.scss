@import "variables";

.main{
	.main-support-left{width: 250px; float: left; min-height: 400px; padding: 60px 0 20px 0;
		.item-question-heading{font-size: 18px; color: #333; width: 100%; float: left; margin-bottom: 15px;}
		.item-question{padding: 5px 0; width: 85%; float: left; color: #999;
			&:hover{color: $primaryColor;}
		}
		.active{color: #111; font-weight: bold; }
	}

	.main-support-right{ width: 770px; float: left; border-left: 1px solid #DDD; padding-left: 40px; padding-bottom: 40px; min-height: 700px;
		.support-detail-container{ width: 97%;
			.support-detail-title{font-weight: bold; margin-top: 60px; font-family: 'HelveticaNeue-Thin', 'Helvetica Neue Light', 'Helvetica Neue', 'Segoe UI', Helvetica, Arial, 'Lucida Grande', sans-serif; }
			.support-detail-info{ width: 470px; float: left; opacity: 0.6; font-size: 13px; margin-top: 5px;}
			.support-social-like{width: 100%; float: left; padding: 15px 0;}
			.support-detail-content{font-size: 16px;
				.main-content{width: 100%; float: left; padding-bottom: 30px; padding-top: 30px;
					iframe{max-width: 100% !important;}
					img{max-width: 100% !important;}

				}
			}
			.comment-container{  padding-bottom: 40px; width: 100%; float: left;
				.comment-heading{width: 100%; float: left; border-bottom: 1px solid $primaryColor; margin-top: 20px;
					span{font-size: 25px; background: $primaryColor; color: #fff; padding: 4px 15px;}
				}
				.fb-comments{margin-left: -5px;}
			}
			
		}
		.error-list{
			display: none;
			padding-left: 30px;
			margin-bottom: 20px;
			li{list-style-type: circle};
		}
		.block-hotro .block-content { padding-top: 60px; }
	}

	.support-right{padding-left: 25px;}

	.loader-area
	{
		display: none;
		z-index: 1050;
		position: fixed;
	    top: 0;
	    bottom: 0;
		background-color: gray;
		opacity: 0.3;
		width: 100%;
		height: 100%;
		.loader {
			margin:0 auto;
			margin-top: calc((100vh - 100px)/2);
			border: 3px solid white;
			border-radius: 50%;
			border-top: 3px solid #3498db;
			width: 70px;
			height: 70px;
			-webkit-animation: spin 2s linear infinite; /* Safari */
			animation: spin 1s linear infinite;
		}
	}

	/* Safari */
	@-webkit-keyframes spin {
		0% { -webkit-transform: rotate(0deg); }
		100% { -webkit-transform: rotate(360deg); }
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
}