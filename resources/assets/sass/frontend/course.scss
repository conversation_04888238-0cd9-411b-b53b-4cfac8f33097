@import "variables";
@import "mixins";

#driver-page-overlay {
  opacity: 0.1 !important;
}
[v-cloak] {
  display: none;
}
.pdfobject-container {
  height: 95rem;
  border: 0 solid rgba(0, 0, 0, .1);
}

.roadmap-container {
  padding: 0;
  background-image: url('../img/roadmap/roadmappu.png');
  background-size: 100%;
  background-position: top center;
  background-color: transparent;

  .roadmap-bg {
    width: 500px;
    height: 722px;
    font-family: 'Quicksand', sans-serif;

    p {
      margin-bottom: 12px;
    }

    h3 {
      padding: 20px 108px;
      font-size: 15px;
      text-align: center;
      color: #fff;
    }

    h4 {
      font-size: 16px;
    }

    .form-group {
      padding: 20px 100px;
      margin-top: 100px;
      float: left;
      width: 100%;
      font-weight: 600;

      label {
        margin-bottom: 0;
        color: green;
        cursor: pointer;
      }

      input {
        margin-right: 5px;
      }

      small {
        padding-left: 25px;
        font-weight: 400;
      }

      .roadmap-btn-submit {
        background: orange;
        color: #fff;
        outline: none;
        border-radius: 6px;
        border: none;
        width: 140px;
        padding: 6px 0;
        margin: 5px 0 0 50px;
        border-bottom: 2px solid #c75626;
        text-shadow: 1px 1px #c75626;
        cursor: pointer;

      }
    }
  }

  .fancybox-close-small {
    margin-top: 32px;
  }
}

.course-list-roadmap {
  .hide-bg-btn, .expand-btn {
    float: right;
    margin-top: -22px;
    font-size: 20px;
    cursor: pointer;
  }

  .expand-stage {
    width: 1200px !important;
    height: 100vh;
    position: fixed;
    top: 0;
    left: calc((100vw - 1200px) / 2);
    z-index: 99;

    .path-bg {
      margin: 100px 290px;
      width: 600px;
    }

    .st-step {
      width: 110px !important;
    }

    .st1 {
      margin: -532px 0 0 387px !important;
    }

    .st2 {
      margin: -305px 0 0 483px !important;
    }

    .st3 {
      margin: -654px 0 0 563px !important;
    }
    .st4 {
      margin: -544px 0 0 743px !important
    }

    .fc-mn-item {
      float: left;
      padding: 3px 10px;
      width: 30.8%;
      border: 1px solid green;
      border-radius: 8px;
      margin: 0 0 15px 20px;

      img {
        width: 22px;
      }
    }
  }

  .fc-popup-container {
    padding: 30px 100px;
  }

  .fc-popup {
    width: 1100px !important;
    left: calc((100vw - 1120px) / 2);
    margin-top: 70px;
    background: #EEE !important;
  }

  .sc-popup {
    width: 1100px !important;
    left: calc((100vw - 1120px) / 2);
    margin-top: 70px;
    background: #EEE !important;

    .guide-item {
      width: 100%;
      float: left;
      padding-left: 50px;
      margin-top: 20px;
      color: #333;
    }

  }

  .scroll-box-flashcard {
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    float: left;
    height: calc(100vh - 200px);
    padding-bottom: 30px;
  }

  .nt-popup {
    width: 1100px !important;
    left: calc((100vw - 1120px) / 2);
    margin-top: 70px;
    background: #EEE !important;

    .date-picker {
      width: 350px;
      height: 310px;
      margin: 20px 40px 0 20px;
      float: left;

      .vc-container {
        width: 100%;
      }
    }

    .date-history {
      width: 640px;
      min-height: 400px;
      float: left;
      background: #F5FFED;
      border-radius: 12px;
      padding: 10px 25px;
      border: 1px solid #1ad66b;

      .history-container {
        width: 100%;
        float: left;
        max-height: 60vh;
        overflow-y: auto;
      }

      .history-item {
        width: 100%;
        float: left;
        margin-bottom: 10px;
        color: #222;
      }
    }
  }
}


.main {
  .course-tabs {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    border-bottom: 0.5px solid #A4A4A4;
    padding-bottom: 60px;
    margin-top: 30px;
    //li{ background: #F6F6F6; margin-right: 10px; padding: 16px 20px; border-radius: 6px;
    //  a{ display: inline-block; width: 100%; font-family: Montserrat, Arial, sans-serif; font-weight: bold; color: #333;
    //    img{margin: 8px 0; filter: grayscale(100%);}
    //    section{font-size: 13px; color: #ccc; font-weight: 500;}
    //  }
    //}
    li {
      background: #F6F6F6;
      margin-right: 30px;
      padding: 16px 25px;
      border-radius: 15px;
      font-size: 20px;
      font-family: Montserrat, Arial, sans-serif;
      font-weight: 500;
      display: flex;
      align-items: center;

      a,
      a:hover {
        padding: 0;
        display: flex;
        align-items: center;
        background-color: transparent;
        border: none;
        border-radius: 0;
        text-decoration: none;
        color: $gray-100;

        img {
          filter: grayscale(100%);
          width: 40px;
          margin-right: 15px;
        }
      }
    }

    .active {
      font-weight: 700;
      -webkit-box-shadow: 0px 0px 19px 0px rgba(0, 0, 0, 0.12);
      box-shadow: 0px 0px 19px 0px rgba(0, 0, 0, 0.12);
      border: 2px solid #96D962;

      a, a:hover {
        background-color: transparent;
        border: none;
        border-radius: 0;
        text-decoration: none;
        color: $gray-100;

        img {
          filter: grayscale(0%);
        }
      }
    }
  }

  .tab-content {
    width: 100%;
    float: left;
    padding-top: 10px;

    .tab-pane {
      min-height: 400px;
    }
  }

  .main-eju-feature {
    width: 100%;
    float: left;
    padding-bottom: 20px;

    .topf {
      width: 100%;
    }

    .topf-mb {
      width: 100%;
      display: none;
    }

    .eju-center {
      width: 1024px;
      margin: 0 auto;
    }

    .ej-block-right {
      position: absolute;
      width: 622px;
      height: 190px;
      padding: 25px 0 0 30px;
      left: 819px;
      top: 310px;
      background: #FFFFFF;
      border-radius: 40px 0px 0px 0px;
      //     box-shadow:
      // 0 2.8px 2.2px rgba(0, 0, 0, 0.034),
      // 0 6.7px 5.3px rgba(0, 0, 0, 0.048),
      // 0 12.5px 10px rgba(0, 0, 0, 0.06),
      // 0 22.3px 17.9px rgba(0, 0, 0, 0.072),
      // 0 41.8px 33.4px rgba(0, 0, 0, 0.086),
      // 0 100px 80px rgba(0, 0, 0, 0.12);
      h5 {
        width: 327px;
        height: 36px;
        font-family: Roboto;
        font-style: normal;
        font-weight: bold;
        font-size: 15px;
        line-height: 18px;
        color: #000000;
      }

      span {
        width: 326px;
        height: 28px;
        font-family: Roboto;
        font-style: normal;
        font-weight: 300;
        font-size: 12px;
        line-height: 14px;
        color: #000000;
      }

      .btn-container {
        width: 80%;
        float: left;

        .btn-eju {
          width: 140px;
          height: 30px;
          background: linear-gradient(178.19deg, #65C7F7 3.32%, #0052D4 151.77%);
          padding: 5px 20px;
          color: #fff;
          box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
          border-radius: 22.5px;
          text-align: center;
          margin-top: 15px;
          float: left;
          margin-right: 15px;
        }

        .btn-tran {
          background: transparent;
          box-shadow: none;
          color: #222;
        }
      }
    }


    .mobile-arrow-left {
      display: none;
    }

    .eju-why-item {
      width: 300px;
      float: left;
      height: 300px;
      margin: 30px 15px 20px 15px;
      padding: 2vw 0;
      align-items: center;
      background: #FFF;
      text-align: center;

      b {
        width: 100%;
        float: left;
        font-size: 20px;
        color: #3F9BEA;
        margin-bottom: 20px;
      }

      span {
        font-weight: 300;
        font-size: 14px;
        line-height: 16px;
        text-align: justify;
        color: #555;
        font-family: Roboto;
      }

      p.link {
        color: #124896;
        width: 100%;
        text-align: right;
        margin-top: 10px;
        text-decoration: underline;
      }
    }

    .why {
      height: 380px;
    }

    .intro {
      text-align: center;

      h3 {
        color: #124896;
        font-family: Roboto;
        font-weight: bold;
      }

      p {
        padding: 0 20%;
      }

      .intro-more {
        margin-top: 20px;
        color: #888;
        text-decoration: underline;

        a {
          color: #3F9BEA;
        }
      }

      .register-now {
        background: linear-gradient(178.19deg, #65C7F7 3.32%, #0052D4 151.77%);
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
        border-radius: 22.5px;
        padding: 10px 25px;
        color: #fff;
        margin-top: 30px;
      }
    }

    .introx {
      text-align: center;
    }

  }

  .hv-share {
    .eju-why-item {
      width: 260px;
      margin: 30px 30px 20px 40px;

      img {
        width: 100%;
        border-radius: 16px;
        height: 283px;
      }
    }

    h4 {
      text-align: center;
      font-family: Roboto;
      font-style: normal;
      font-weight: bold;
      font-size: 30px;
      line-height: 35px;
      color: #124896;
      margin-top: 25px;
    }

    .hv-info {
      background: url('../img/eju/bubble.png') no-repeat;
      display: none;
      width: 243px;
      height: 92px;
      float: right;
      margin: -25px -25px 0 0;
      position: relative;
      padding: 10px 0 0 20px;

      span, b {
        color: #fff;
        text-align: left;
        margin: 0;
      }

      span {
        font-size: 13px;
        float: left;
        width: 100%;
      }
    }
  }

  .feature-teacher-eju {
    background: #FAFCFF;
    padding: 80px 0 40px 0;
    text-align: center;
    margin-top: 50px;

    p {
      color: #4F92E4;
      font-weight: bold;
      margin-top: -35px;
      margin-bottom: 15px;
    }

    .p-center {
      margin-right: -190px;
    }

    .list-teacher {
      width: 1030px;
    }

    .eju-teacher-item {
      width: 200px;
      float: left;
      height: 280px;
      padding: 0 10px;

      img {
        margin-bottom: 25px;
      }

      span {
        width: 100%;
        font-family: Roboto;
        opacity: .8;
      }
    }

    .seperate {
      width: 2px;
      border-right: 1px solid #CCC;
    }

    .intro-more {
      margin-top: 20px;
      color: #888;
      text-decoration: underline;
      width: 100%;
      float: left;

      a {
        color: #3F9BEA;
      }
    }
  }

  .eju-top-menu {
    width: 1020px;
    float: left;
    display: flex;
    justify-content: space-between;
    margin: 0 0 20px 0;
    z-index: 1;
    position: initial;

    .eju-course-item {
      padding: 10px 0;
      width: 32.5%;
      box-sizing: border-box;
      text-align: center;
      border-radius: 5px;
      color: #666;
      text-transform: uppercase;

      img {
        height: 40px;
        margin-bottom: 25px;
      }
    }

    .active {
      color: #4F92E4;;
    }
  }

  .kaiwa-top-menu {
    width: 1020px;
    float: left;
    display: flex;
    justify-content: space-between;
    margin: 0 0 20px 0;
    z-index: 1;
    position: initial;

    .kaiwa-course-item {
      position: relative;
      padding: 10px 0;
      width: 50%;
      border: 1px solid #c38c27;
      box-sizing: border-box;
      box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
      text-align: center;
      border-radius: 5px;
      color: #d6902e;
      text-transform: uppercase;
      margin-right: 10px;

      img {
        position: absolute;
        bottom: 0;
        left: 10%;
      }
    }

    .active {
      color: #fff;
      background: linear-gradient(180deg, #d6902e 0%, #dd982d 48.44%, #d6902e 100%);
      box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    }
  }


  &.pricing__screen {
    padding: 100px 0;
    background-color: #f7f7f7;
  }

  //list courses
  .all-courses-container {
    width: 100%;

    .pricing {
      &__title {
        padding-left: 20px;
        border-left: 4px solid #95D133;
        font-family: Montserrat, Arial, sans-serif;
        font-size: 30px;
        font-weight: 700;
      }
    }

    .zmdi-bookmark {
      color: green;
    }

    .text-code {
      color: $gray-100;
    }

    .text-off {
      color: $gray-100;
    }

    .title-container {
      width: 100%;
      text-align: left;

      h2 {
        font-family: Montserrat, Arial, sans-serif;
        font-weight: 700;
        font-size: 30px;
        width: 50%;
        display: inline-block;
        padding-bottom: 20px;
      }
    }

    .list-product-container {
      width: 100%;
      display: -webkit-box;
      display: flex;
      -webkit-box-pack: justify;
      justify-content: flex-start;
      flex-wrap: wrap;
      padding-top: 50px;
    }

    .buying-container {
      width: 150px;
      height: 100%;
      position: absolute;
      margin-left: 740px;
      padding-top: 215px;

      .buy-btn {
        padding: 14px 28px;
        background: #0F4C82;
        color: #fff;
        float: left;
        border-radius: 16px;
      }
    }

    .combo-it {
      font-family: Montserrat, Arial, sans-serif;
      height: auto;
      width: 230px;
      background: #FFFFFF;
      box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.25);
      border-radius: 10px;
      min-height: 107px;
      margin-right: 20px;
      padding-bottom: 20px;

      .course-icon {
        border-radius: 10px;
        width: 100%;
      }

      .course-name {
        font-family: Montserrat, Arial, sans-serif;
        font-weight: 700;
        font-size: 20px;
        padding: 20px 0 15px 15px;
        line-height: 0.9;
        margin: 0;
      }

      .course-info {
        font-family: Quicksand, Arial, sans-serif;
        font-weight: 400;
        font-size: 14px;
        padding: 10px 15px;
        color: #444;

        span {
          font-weight: 600;
        }

      }

      a {
        padding: 0 15px;
        display: block;
      }

      .buy-btn {
        padding: 4px 0;
        background-color: #96D962;
        color: #fff;
        border-radius: 8px;
        font-family: Montserrat, Arial, sans-serif;
        font-weight: 700;
        font-size: 14px;

        &:hover {
          cursor: pointer;
          opacity: 0.8;
        }

        text-align: center;
      }

      .buy-info {
        text-align: center;
      }

      .plus-intro {
        width: 100%;
        color: #F3A055;
        cursor: pointer;
        padding: 15px;
        margin-bottom: 5px;
        word-wrap: break-word;

        h4 {
          font-family: Montserrat, Arial, sans-serif;
          font-size: 13px;
        }
      }
    }

    .course-item {
      width: 230px;
      border-radius: 18px;
      margin-right: 15px;
      font-family: Roboto;
      color: #111;
      box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);

      .course-icon {
        width: 100%;
      }

      .course-name {
        width: 100%;
        text-align: center;
        padding: 15px 0;
        font-size: 14px;
        line-height: 0.9;
        font-weight: bold;
        margin: 0 0 15px 0;
        border-bottom: 1px dashed #CCC;

        span {
          text-transform: uppercase;
        }
      }

      .combo {
        .course-name {
          font-size: 45px;
          font-weight: 500;
          line-height: 1.0;
          margin-top: 0;
        }
      }

      .combox3 {
        .course-name {
          font-size: 30px;
          font-weight: 500;
          line-height: 1.0;
          margin-top: 13px;
          margin-bottom: 30px;
        }
      }

      .combox4 {
        .course-name {
          font-size: 30px;
          font-weight: 500;
          line-height: 1.0;
          margin-top: 13px;
          margin-bottom: 0;
        }
      }

      .course-info {
        width: 100%;
        padding: 15px;
        font-weight: 600;
        font-size: 15px;
        color: #444;
        text-align: center;

        span {
        }

        .buy-btn {
          font-family: Montserrat, Arial, sans-serif;
          font-size: 14px;
          font-weight: 700;
          padding: 6px 15px;
          background-color: #96D962;
          color: #fff;
          border-radius: 8px;
          margin-top: 18px;

          &:hover {
            cursor: pointer;
            opacity: 0.8;
          }
        }
      }

      .buy-info {
        text-align: center;
      }
    }

    .see-schedule {
      padding: 10px 20px;
      background-color: $primaryColor;
      color: #fff;
      border-radius: 22px;
      width: 400px;
      display: inline;
      font-size: 20px;

      &:hover {
        cursor: pointer;
        border: 1px solid $primaryColor;
        background-color: #fff !important;
        color: $primaryColor;
      }
    }

    .v2 {
      .list-product-container {
        padding-top: 20px !important;

        .combo-it {
          width: 230px;
          margin-bottom: 30px;
          margin-right: 20px;

          .course-card {
            .course-icon {
              width: 100%;
            }
          }

          .plus-intro {
            h4 {
              text-decoration-line: underline;
              font-size: 13px;
            }
          }
        }
      }
    }
  }

  #myModal .test-dialog {
    width: 90%;
  }

  //courses detail
  .course-detail-cover {
    width: 100%;
    float: left;
  }

  .main-course {

    ::-webkit-scrollbar {
      width: 7px;
      height: 7px;
    }

    ::-webkit-scrollbar-button {
      width: 1px;
      height: 1px;
    }

    ::-webkit-scrollbar-thumb {
      background: rgba(139, 217, 135, 0.8);
      border: 0px solid #ffffff;
      border-radius: 44px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: rgba(155, 255, 153, 0.81);
    }

    ::-webkit-scrollbar-thumb:active {
      background: rgba(87, 207, 79, 0.8);
    }

    ::-webkit-scrollbar-track {
      background: #d9d9d9;
      border: 0px solid #ffffff;
      border-radius: 54px;
    }

    ::-webkit-scrollbar-track:hover {
      background: #9e9e9e;
    }

    ::-webkit-scrollbar-track:active {
      background: #333333;
    }

    ::-webkit-scrollbar-corner {
      background: transparent;
    }

    .main-left {
      width: 700px;

      .hidden-pc {
        display: none;
      }

      .lesson-detail-title {
        margin: 21px 0 12px 0px;
        width: 100%;
        float: left;
        font-size: 20px;
      }

      .timer-container {
        position: fixed;
        bottom: 150px;
        right: 25px;
        color: red;
        transition: all .2s ease-in-out;
        z-index: 500;
        display: -webkit-inline-box;

        span {
          font-size: 24px;
        }

        #timer {
          font-size: 24px;
        }

        .heartbit {
          position: absolute;
          width: 10px;
          height: 10px;
          border-radius: 50% !important;
          background-color: #54ff3a;
          animation: pulse1 .7s linear infinite;
          margin-left: 45px;
          margin-top: 17px;
        }

        #client {
          margin-top: 3px;
          margin-left: 62px;
          font-size: 21px;
          margin-right: 3px;
        }
      }

      .course-detail-title {
        margin: 30px 0 15px;
        width: 100%;
        float: left;
      }

      .cover-container {
        width: 100%;
        float: left;
        margin-bottom: 15px;
        min-height: 455px;

        .course-info-status-mobile {
          display: none;
        }

        .movie-play {
          position: relative;
          display: flex;
          height: 100%;
          &:hover {
            cursor: pointer;
          }

          img {
            width: 700px;
            height: 395px;
            object-fit: cover;
          }
        }

        .play-icon-btn {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        audio {
          width: 99%;
          float: left;
        }

        iframe {
          background: #EEE;
        }

        .dmrx-player {
          position: relative;

          .fast-area {
            position: absolute;
            text-align: center;
            z-index: 10;
            top: 35%;
            img {
              padding: 0 65px;
              opacity: 0;
              font-size: 80px;
              color: #fff;
              user-select: none;
              transition: opacity 0.5s;
            }
          }
          .fast-right { right: 0; }
          .fast-area img:hover {
            opacity: 100;
            cursor: pointer;
          }
        }

        //#see-correct-answer{display: none;}

        .myplayer {
          display: none;
          width: 100%;
          height: 356px;
        }

        .server-localtion-container {
          width: 100%;
          padding: 20px 0 0 0;

          .server-item {
            cursor: pointer;
          }

          .server-localtion {
            padding: 3px 10px;
            background-color: #ccc;
            color: #444444;
            margin-right: 10px;
            border-radius: 3px;
          }

          .server-localtion-heading {
            background: #fff;
            padding-left: 0;
          }

          .active {
            background-color: $primaryColor;
            color: #fff;
          }

          .choose-quality {
            outline: none;

            .select-video-quality {
              float: right;
              background: $currencyColor;
              opacity: 0.8;
              padding: 3px 10px !important;
              color: #fff;
              outline: none;
              border: none;

              &:focus {
                outline: none;
              }

              option {
                padding: 3px 10px;
              }
            }
          }

          .select-localtion-select {
            display: none;
            float: left;
            background: green;
            opacity: 0.8;
            padding: 3px 10px !important;
            color: #fff;
            outline: none;
            border: 1px solid #CCC;

            option {
              padding: 3px 10px;
            }
          }

          .quality-item {
            float: right;
            color: #444C38;
            font-family: Quicksand, Arial, sans-serif;

            .qitem {
              margin-left: 10px;
              cursor: pointer;
              vertical-align: text-top;
              padding: 5px 8px;
              border-radius: 5px;

              sup {
                color: red;
                font-size: 9px;
              }
            }

            .active {
              background-color: #E0E0E0 !important;
              color: #111 !important;
              padding: 3px 6px 2px 8px !important;
            }
          }
        }

        .list-video-area-parent {
          width: 100%;
          float: left;
          padding-bottom: 30px;
          padding-top: 15px;
          font-size: 15px;
          line-height: 2;
          overflow-x: hidden;

          .list-video-area-child {
            width: calc(100% + 25px);
            display: inline-block;
          }

          .video-item-area {
            width: 216px;
            height: 210px;
            float: left;
            margin-right: 25px;

            .image-video {
              width: 216px;
              height: 130px;
              overflow: hidden;
              object-fit: cover;
            }

            .play-icon-btn-sm {
              margin-top: -88px;
              margin-left: 85px;
              position: relative;
              float: left;
              font-size: 24px;
              padding: 0px 19px;
              border-radius: 50%;
              opacity: 0.9;
              width: 45px;
              height: 45px;
              background: rgba(0, 0, 0, 0.8);
              color: #fff;

              &:hover {
                background: #fff;
                color: $primaryColor;
              }
            }

            .video-title {
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
            }
          }


          .panel {
            .clickable {
              cursor: pointer;
            }

            .panel-heading {
              background-color: #E5E6FF;
              height: 40px;

              .panel-title {
                color: #000000;
                font-style: normal;
                font-weight: 500;
                font-size: 16px;
                line-height: 19px;
              }

              span {
                font-size: 15px;
              }
            }

            .panel-body {
              background-color: #F6F7FA;

              .video-item {
                margin-left: 10px;
                cursor: pointer;
                //margin-bottom: 10px;
                .video-btn {
                  margin-right: 10px;
                }

                .active {
                  color: #474DDA !important;

                  &:hover {
                    color: black !important;
                  }
                }

                a {
                  color: black;
                  font-weight: 500;
                  font-size: 14px;
                  line-height: 16px;

                  &:hover {
                    color: #474DDA;
                  }
                }
              }
            }

          }

        }

        .ytplayer {
          width: 100%;
          float: left;
          height: 395px;
          border: none;
          display: none;
        }

        .lesson-content-detail {
          width: 100%;
          margin-top: 20px;
          float: left;
          padding-top: 5px;
          padding-bottom: 30px;
          font-size: 15px;
          max-height: 1700px;
          overflow-y: auto;
          line-height: 2;
          overflow-x: hidden;
          padding-left: 5px;
          padding-right: 5px;

          table {
            width: 100% !important;
          }

          #see-guides {
            display: flex;
            justify-content: center;
          }

          //img {
          //  //width: 100% !important;
          //  height: auto !important;
          //}
          .three-line-answer {
            height: 140px;
          }

          .two-line-answer {
            height: 150px;
          }

          .movie-play .play-icon-btn {
            font-size: 39px;
            padding: 0px 30px;
          }

          .question-answer {
            font-size: 16px;

            .question-answer-content {
              padding-top: 5px;
              font-weight: 100;
              font-size: 15px;
              display: inline-block;
              max-width: 100%;
              white-space: normal;
              text-align: left;
              line-height: 1.5;
              padding: 3px 8px;
            }

            .label-true {
              color: #5cb85c;
              border: 1px solid #5cb85c;
              padding: 2px 7px;
              border-radius: 8px;
            }

            .label-false {
              color: red;
              border: 1px solid red;
              padding: 2px 7px;
              border-radius: 8px;
            }
          }

          .multimedia-item {
            padding: 8px 0;
            border-top: 1px solid #f0f0f0;
            margin-top: 20px;
          }

          .submited, .pre-not-submit {
            padding: 9px 7px;
            background-color: #999;
            color: #fff;
            border-radius: 5px;
          }

          .trac-nghiem {
            margin: 25px 15%;
            width: 50%;
            background: #008fff;
            border-color: #008fff;
          }

          .btn-display-answer {
            width: 81%;
            padding: 8px 0;
            margin: 10px 0;
            background: #6c5bde;
            border-color: #6c5bde;
          }

          .answers-input {
            cursor: pointer;
          }

          .review-result {
            float: right;
            position: relative;
            top: -70px;
          }

          .remove-result {
            float: right;
            position: relative;
            top: -30px;
            right: -104px;
          }

          .answer-box {
            ol {
              display: block;
              list-style-type: decimal;
              -webkit-margin-before: 1em;
              -webkit-margin-after: 1em;
              -webkit-margin-start: 0px;
              -webkit-margin-end: 0px;
              -webkit-padding-start: 40px;

              li {
                display: list-item;
                list-style-type: decimal;
                text-align: -webkit-match-parent;
              }
            }
          }
        }

        .msg-fb-box {
          margin-top: 10px;
        }
      }

      .guest-cover-container {
        background: #EEE;
        text-align: center;
        padding: 170px 0;

        .free-course-info {
          padding-left: 25px;
          padding-right: 25px;

          h3 {
            line-height: inherit;
            color: #e74c3c
          }
        }

        .btn-login {
          padding: 6px 20px;
          background-color: $primaryColor;
          color: #fff;
          border-radius: 20px;
          margin-left: 10px;
          cursor: pointer;
          outline: none;
          display: inline;
          margin-top: 4px;
          font-size: 16px;

          &:hover {
            opacity: 0.9;
            background: #5cbfaa;
            color: #fff;
          }
        }

        .btn-register {
          padding: 5px 20px;
          cursor: pointer;
          border-radius: 20px;
          border: 1px solid $primaryColor;
          background-color: #fff;
          outline: none;
          color: $primaryColor;
          display: inline;
          margin-top: 4px;
          cursor: pointer;
          font-size: 16px;

          &:hover {
            opacity: 0.9;
            background: #5cbfaa;
            color: #fff;
            border-color: #5cbfaa;
          }
        }

        .btn-buy {
          padding: 5px 20px;
          cursor: pointer;
          border-radius: 20px;
          background: $currencyColor;
          color: #fff;
          border-color: $currencyColor;
          display: inline;
          margin-top: 4px;
          cursor: pointer;
          font-size: 16px;

          &:hover {
            opacity: 0.9;
            border: 1px solid $currencyColor;
            background-color: #fff;
            outline: none;
            color: $currencyColor;
          }
        }
      }

      .course-heading {
        width: 100%;
        float: left;
        background: #EEE;
        margin-bottom: 10px;
        padding: 5px 0;

        span {
          font-size: 20px;
          color: #444;
          padding: 4px 15px;
        }
      }

      .btn-list-lesson-box .btn-list-lesson {
        display: none;
      }

      .free-course-box {
        display: block;
        padding: 10px 0;
        background-color: #e8f2e9;
        width: 100%;
        float: left;
        margin-top: 10px;
        border-radius: 3px;
        color: #588d3f;
        font-size: 16px;
        border: 1px dashed #588d3f !important;

        .icon-container {
          float: left;
          width: 60px;
          text-align: center;
          font-size: 25px;
          padding-top: 5px;
        }

        .content-container {
          float: left;
          width: calc(100% - 80px);

          span {
            font-size: 15px;
          }

          a {
            font-weight: bold;
            color: #0c4d17;
          }
        }
      }

      .course-tab {
        width: 100%;
        float: left;
        background: #d2e5b2;
        margin-bottom: 30px;

        li {
          width: 50%;
          box-sizing: border-box;
          margin-left: 0 !important;
          font-size: 18px;
          text-indent: 25px;

          a {
            color: #666;
            padding: 5px 0 3px 0;
          }
        }

        li.active > a, .nav-pills > li.active > a:focus, .nav-pills > li.active > a:hover {
          background: $primaryColor;
          color: #fff;
          border-radius: 0;
        }
      }

      .course-detail-container {
        width: 100%;
        float: left;
        min-height: 300px;
        font-size: 17px;
        padding-bottom: 40px;

        .course-price-container {
          padding: 10px 20px 10px 20px !important;
          border-radius: 4px !important;
          border: 1px dashed #336695 !important;
          background: #e8f1f9 !important;
          clear: both !important;
          margin-bottom: 20px;
          line-height: 1.5;
          float: left;

          .info {
            width: 100%;
            float: left;
            color: #222;
            font-size: 16px;
            font-weight: 500;
            padding: 5px 0;
            border-bottom: 1px solid #d7e8f7;

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }

      .preview-course-container {
        width: 100%;
        float: left;
        display: -webkit-box;
        display: flex;
        -webkit-box-pack: justify;
        justify-content: space-between;
        padding-bottom: 60px;

        .course-item {
          width: 204px;
          float: left;
          position: relative;

          .info {
            background: rgba(0, 0, 0, 0.6) none repeat scroll 0 0;
            border: 0 none;
            bottom: 0;
            float: left;
            padding: 3px 10px 5px 10px;
            position: absolute;
            width: 100%;

            .title a {
              color: #eee;
              font-size: 13px;
            }

            .name-gv {
              font-size: 10px;
              color: #CCC;
            }
          }
        }
      }

      .comment-container {
        padding-bottom: 40px;
        width: 100%;
        float: left;

        .comment-heading {
          width: 100%;
          float: left;
          border-bottom: 1px solid $primaryColor;
          margin-top: 20px;

          span {
            font-size: 25px;
            background: $primaryColor;
            color: #fff;
            padding: 4px 15px;
          }
        }

        .fb-comments {
          margin-left: -5px;
        }
      }

      .lesson-detail-container {
        width: 100%;
        float: left;
        font-size: 17px;

      }


      .combo-list-container {
        width: 100%;
        float: left;
        margin-bottom: 30px;

        .combo-item {
          width: 100%;
          float: left;
          margin: 0 0 20px 0;

          .combo-name-container {
            padding: 15px;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            height: 140px;
            width: 120px;
            float: left;
            background: #e6e6e6;
            text-align: center;

            p {
              margin-bottom: 1px;
            }

            h1 {
              margin: 0;
              font-size: 30px;
            }
          }

          .combo-detail-container {
            height: 140px;
            width: calc(100% - 120px);
            float: left;
            background: #f8f8f8;
            padding-top: 10px;

            .course-info {
              width: 70%;
              float: left;
              margin-bottom: 0;
              margin-top: 5px;
              padding-left: 20px;
              font-size: 16px;

            }

            .dmr-btn {
              width: 120px;
              float: right;
              text-align: center;
              margin-right: 15px;
            }
          }
        }

        .combo-eju {
          .combo-name-container {
            width: 200px;
            padding: 30px 0;

            h1 {
              font-size: 22px;
              margin-top: 10px;
            }
          }

          .combo-detail-container {
            width: calc(100% - 200px);
          }
        }
      }


      //css cho thi JLPT
      .jlpt-certificate {
        font-size: 15px;
        text-align: center;
        background-image: url(/assets/img/bg_jlpt.jpg);
        padding: 10px;
        background-size: 20px 20px;
        padding-bottom: 12px;
        padding-top: 12px;
        margin-top: 5px;

        .jlpt-date, .jlpt-more-infor {
          text-align: left;
          line-height: 28px;
          display: flex;
          -webkit-justify-content: space-between;
        }

        .jlpt-score {
          width: 100%;
          border: 2px solid #000;
          padding: 4px;
          clear: both;
          margin-top: 12px;

          .jlpt-detail-score {
            width: 100%;
          }
        }

        .jlpt-detail-score td, .jlpt-detail-score tr {
          border: 1px dotted #000;
          vertical-align: middle;
          width: 33%;
        }

        .jlpt-conclude-pass-c {
          height: 40px;
          width: 160px;
          border: 2px dotted #1b1b1b;
          line-height: 40px;
          text-align: center;
          bottom: 0px;
        }

        .stamp {
          width: 70px;
          height: 70px;
          position: relative;
        }
      }

      .review-ranking {
        text-align: center;
        margin-top: 8px;
      }
    }

    .main-left-premium {
      width: 630px !important;

      .course-detail-title {
        margin: 50px 0 0 0;
        width: 50%;
        float: left;
        font-size: 24px;
        color: #41A336;

        b {
          color: #4F4F4F;
        }
      }

      .lesson-detail-title {
        margin: 20px 0 20px 0;
        width: 100%;
        float: left;
        font-size: 24px;
        color: #41A336;

        b {
          color: #4F4F4F;
        }
      }

      .premium-expried {
        margin-top: 13px;
        margin-bottom: 5px;
        border-radius: 14px;
        padding: 4px 30px 2px 30px;
        display: inline-flex;
        color: #F2994A;
        border: 1px solid #F2994A;
        font-size: 12px;
      }

      .buy-this-course {
        color: #f22b2b;
        border-color: #f22b2b;
        background: #fff;

        &:hover {
          background: #f22b2b;
          color: #fff;
        }
      }

      .buy-in-lesson {
        margin-top: -25px;
      }

      .cover-container {

        .movie-play {
          img {
            border-radius: 6px;
            width: 100%;
            height: 347px;
          }

          //.play-icon-btn {
          //  margin-top: -210px;
          //  margin-left: 280px;
          //}
        }

        .server-localtion-container {
          .server-localtion {
            font-family: Quicksand, Arial, sans-serif;
            color: #000 !important;
            background-color: transparent;
            padding: 5px 19px !important;
            border-radius: 5px;
            transition: 0.15s ease-in-out;
          }
          .active {
            background-color: #D8EDB9 !important;
          }
        }
      }

      .pager {
        margin: 0;
      }
    }

    // giao diện tiến trình premium
    .main-right-premium {
      width: 375px;
      margin-top: 25px;
      min-height: 1000px;
      float: left;
      margin-left: 85px;
      font-family: Quicksand, Arial, sans-serif;
      position: relative;

      .adventure-toggle {
        width: 100%;
        padding: 20px 25px;
        flex: 1;
        border-radius: 10px;
        background: #D8EDB9;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24px;
        font-family: Montserrat;
        font-weight: 700;
        border: 2px solid #96D962;
        overflow: hidden;
        position: relative;
        h4 {
          color: #000;
          text-transform: uppercase;
          text-align: center;
          margin: 0;
          font-weight: 700;
        }
        .gear-option {
          color: white;
          font-size: 30px;
          position: absolute;
          right: 0;
          height: 100%;
          padding: 13px 31px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #96D962;
        }
      }
      .see-more {
        width: 90%;
        float: left;
        color: #2D9CDB;

        a {
          float: left;
        }
      }

      .gear-option {
        cursor: pointer;

        img {
          width: 30px;
        }
      }

      .course-list-premium {
        width: 100%;
        float: left;

        .search-group-container {
          box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
          border-radius: 8px;
          width: 100%;
          height: 35px;
          margin-top: 20px;

          .search-input-container {
            width: 100%;
            float: left;
            height: 35px;
            cursor: pointer;

            #search-submit {
              cursor: pointer;
              width: 18px;
              height: 18px;
              background-size: 90%;
              background-repeat: no-repeat;
              margin-top: 9px;
              margin-left: 10px;
              background-position: center;
              background: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjxzdmcgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB4PSIwcHgiIHk9IjBweCINCgl3aWR0aD0iMTlweCIgaGVpZ2h0PSIxOXB4IiB2aWV3Qm94PSIwIDAgMTkgMTkiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDE5IDE5IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCgk8cGF0aCBmaWxsPSIjNjY2NjY2IiBkPSJNMTcuNjMyLDE2Ljk1NWwtNC43NjEtNC43NjJjMS4xMDktMS4xODgsMS43OTUtMi43NzYsMS43OTUtNC41MjdjMC0zLjY2Ny0yLjk4Mi02LjY0OS02LjY0OS02LjY0OQ0KCQljLTMuNjY3LDAtNi42NDksMi45ODItNi42NDksNi42NDhjMCwzLjY2NywyLjk4Miw2LjY0Nyw2LjY0OSw2LjY0N2MxLjM5MSwwLDIuNjgyLTAuNDMyLDMuNzUtMS4xNjRsNC44MzQsNC44MzRMMTcuNjMyLDE2Ljk1NXoNCgkJTTIuODI0LDcuNjY2YzAtMi44NjMsMi4zMy01LjE5Miw1LjE5Mi01LjE5MmMyLjg2NCwwLDUuMTkyLDIuMzI5LDUuMTkyLDUuMTkyYzAsMi44NjEtMi4zMjgsNS4xOTEtNS4xOTIsNS4xOTENCgkJQzUuMTU0LDEyLjg1NSwyLjgyNCwxMC41MjcsMi44MjQsNy42NjZ6Ii8+DQoJPC9zdmc+");
            }

            .header-search {
              margin-top: -23px;
              float: left;
              height: 15px;

              .search-input {
                width: 300px;
                box-sizing: border-box;
                font-size: 13px;
                padding-top: 2px;
                font-weight: 400;
                margin-top: 1px;
                margin-right: -10px;
                text-indent: 30px;
                height: 26px;
                border-radius: 3px;
                border: none;
                opacity: 0.5;
                cursor: pointer;
              }

              .search-input:focus {
                outline: none;
                width: 200px;
                opacity: 0.9;
                cursor: text;
              }
            }

            span {
              float: right;
              margin: -18px 12px 0 0;

              i {
                font-size: 16px;
              }
            }
          }

          .search-result-dropdown {
            width: 100%;
            float: left;
            margin-top: 5px;
            position: relative;
            z-index: 2;
            min-height: 53px;
            overflow-y: scroll;
            max-height: 50vh;
            box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
            border-radius: 8px;
            margin-bottom: 10px;

            li {
              display: block;
            }

            li a {
              display: flex;
              align-items: center;
              line-height: 24px;
              padding: 10px 10px;
              line-height: 1.2;
              background-color: #fff;
              color: #222;
              font-family: 'Roboto', sans-serif;
              border-bottom: 1px solid #CCC;

              img {
                margin-right: 5px;
                display: inline;
              }

              img.active {
                display: none;
              }

              .lesson-info {
                width: 280px;
                display: inline-block;
                padding-left: 6px;

                .lesson-name {
                  width: 100%;
                  display: inline-block;
                }

                .group-name {
                  color: #41A336;
                  font-size: 12px;
                }
              }

              .hoc-thu {
                width: 58px;
                display: inline-block;
              }
            }

            li a:hover {
              color: #444;
              font-weight: bold;
            }
          }
          .search-result-dropdown::-webkit-scrollbar {
            display: none;
          }
        }

        .stage-container {
          width: 100%;
          float: left;
          margin: 15px 0 0 0;
          border-bottom: 2px dashed #96D962;
          display: flex;
          flex-wrap: nowrap;
          // a{padding: 5px 18px; background: #FFFFFF; box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.25); border-radius: 5px; cursor: pointer; width: 105px;}
          // .active{background: #41A336; color: #fff; svg{color:#fff !important;}}
          li {
            cursor: pointer;
            width: calc(100% / 4);
            &:not(.active) > a:hover {
              background-color: #F7FFF0;
              border: none;
            }
            a {
              font-family: Montserrat, sans-serif;
              font-weight: 700;
              text-align: center;
              color: #41A336;
              padding: 7px 23px 7px 23px;
              & > div:first-child {
                font-size: 11px;
                line-height: 12px;
              }
              & > div:last-child {
                margin-top: 0;
                font-size: 25px;
                line-height: 25px;
              }
              //box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
            }

            img {
              width: 18px;
              height: 18px;
              display: inline;
            }

            //&:hover {
            //  a {
            //    background-image: linear-gradient(to top, rgba(255, 0, 0, 0), rgba(255, 255, 255, 1));
            //    border-color: transparent;
            //    padding-bottom: 9px !important;
            //  }
            //}
          }

          .active {
            a {
              background-color: #F7FFF0;
              border-color: #96D962;
              border-bottom-color: #F7FFF0;
              border-style: dashed;
              border-width: 2px;
              margin-bottom: -2px;
              margin-right: 0 !important;
            }
          }
        }

        .hide-bottom-shadow {
          width: 100%;
          height: 5px;
          float: left;
          margin-top: 0;
          margin-bottom: 5px;
          background: #fff;
          position: relative;
          z-index: 1;
        }

        .progress-container {
          width: 100%;
          padding-top: 65px;
          margin: 25px 0 20px 0;
          display: flex;
          flex-flow: column;
          align-items: center;
          border: 2px dashed #96D962;
          border-radius: 10px;
          position: relative;
          .total-circlebar {
            width: 101.5%;
            padding: 20px 25px;
            flex: 1;
            border-radius: 10px;
            background: #D8EDB9;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 24px;
            font-family: Montserrat;
            font-weight: 700;
            border: 2px solid #96D962;
            overflow: hidden;
            position: absolute;
            top: -2px;
            left: -2px;
            .ldBar {
              width: 110px !important;
              height: 110px !important;
              color: $primaryColor;
              margin-left: 5px;
            }

            h4 {
              color: #000;
              text-transform: uppercase;
              text-align: center;
              margin: 0;
              font-weight: 700;
            }
            .percentage {
              color: white;
              font-size: 30px;
              position: absolute;
              right: 0;
              height: 100%;
              padding: 13px 25px;
              display: flex;
              justify-content: center;
              align-items: center;
              background: #96D962;
            }
            .ldBar-label {
              text-align: center;
              position: absolute;
              width: 100%;
              top: 37px;
              font-size: 30px;
            }

            .ldBar path.mainline {
              stroke-width: 4;
              stroke: $primaryColor;
              stroke-linecap: round;
            }
          }

          .stage-bar {
            width: 100%;
            min-height: 172px;
            padding: 13px 33px 15px 33px;
            flex: 1;
            border-radius: 8px;
            h4 {
              font-size: 20px;
              color: #000;
              text-align: center;
              font-family: Montserrat, sans-serif;
              font-weight: 700;
              display: flex;
              justify-content: center;
              align-items: center;
              &::before, &::after {
                content: '•';
                display: inline-flex;
                align-items: center;
                margin: 0 10px;
              }
            }

            .ldBar {
              width: 155px !important;
            }

            .ct-progress-item {
              width: 100%;
              margin-bottom: 20px;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              p {
                width: 100px;
                font-size: 11px;
                display: inline-flex;
              }

              .pgpercent,
              p {
                font-family: Quicksand, Arial, sans-serif;
                font-weight: 700;
                font-size: 14px;
              }
              .pgpercent {
                width: 30px;
                margin-left: 10px;
              }
              .pg100 {
                width: calc(100% - 100px - 50px);
                background: #EFEFEF;
                height: 10px;
                margin-top: 2px;
                overflow: hidden;
                margin-left: 10px;
                border-radius: 30px;
                border: 1px solid #96D962;
                .pgdata {
                  background: #96D962;
                  height: 8px;
                  width: 0%;
                  -webkit-transition: width 1s;
                  border-radius: 30px;
                }
              }
            }
          }
        }

        h3 {
          width: 100%;
          float: left;
          margin: 20px 0 0 0;
          font-size: 16px;
          text-align: center;

          span {
            background: #41A336;
            border-radius: 8px;
            color: #fff;
            padding: 2px 15px 0 15px;
            font-size: 11px;
            text-transform: uppercase;
          }
        }
        .guide-search {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 20px 7px;
          width: 100%;
          .search {
            width: 70px;
            height: 50px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            background: #D8EDB9;
            border: 2px solid #96D962;
            border-radius: 10px;
            cursor: pointer;
          }
        }
        .guide {
          min-height: 50px;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          flex: 1;
          background: #96D962;
          border-radius: 10px;
          text-align: center;
          color: black;
          cursor: pointer;
          margin-right: 20px;
          font-family: Quicksand, Arial, sans-serif;
          font-size: 16px;
          font-weight: 600;
        }

        .guide-colapse {
          width: 100%;
          float: left;
          min-height: 10px;
          margin: -5px 0 15px 0;

          li {
            list-style: none;
            &:last-child > a{
              border-bottom: none;
            }
          }

          a {
            display: flex;
            align-items: center;
            padding: 10px 10px;
            line-height: 1.2;
            color: #222;
            font-family: 'Roboto', sans-serif;
            border-bottom: 1px solid #CCC;

            .free {
              background: #888;
              color: #fff;
              border-radius: 2px;
              padding: 3px 5px 2px 5px;
              font-size: 11px;
              height: 18px;
            }

            img {
              margin-right: 5px;
              height: 13px;
              display: inline;
            }

            img.active {
              display: none;
            }

            .lesson-name {
              width: 295px;
              display: inline-block;
              padding-left: 6px;
            }

            .hoc-thu {
              width: 58px;
              display: inline-block;
            }
          }
        }
        .category-container {
          width: 100%;
          float: left;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          background-color: #F7FFF0;
          border-left: 2px dashed #96D962;
          border-right: 2px dashed #96D962;
          padding: 20px;

          li a:hover {
            color: $primaryColor;
          }

          li.active {
            border: none;

            a {
              color: $primaryColor;
            }
          }

          .p-item {
            margin-bottom: 15px;
            width: calc((100% - 8px) / 3);
            &.special {
              width: calc((100% - 2px) * 2 / 3);
              overflow: hidden;
              img {
                width: 218px;
                height: 90px;
                margin-top: -28px;
              }
            }
          }

          .cate-item {
            height: 90px;
            background: #FFFFFF;
            box-sizing: border-box;
            cursor: pointer;
            text-align: center;
            color: #000;
            border: 2px solid #E0E0E0;
            border-radius: 15px;
            padding-top: 8px;
            user-select: none;
            transition: 0.15s ease-in-out;
            will-change: transform;
            img {
              margin-top: 7px;
              margin-bottom: 3px;
              height: 25px;
            }
            &:hover {
              transform: translateY(-5px);
              box-shadow: -1px 9px 3px -6px rgba(0,0,0,0.20);
              -webkit-box-shadow: -1px 9px 3px -6px rgba(0,0,0,0.20);
              -moz-box-shadow: -1px 9px 3px -6px rgba(0,0,0,0.20);
            }
          }

          .active {
            .cate-item {
              border: 2px solid #96D962;
            }
          }
        }

        .panel-group {
          float: left;
          width: 100%;
          padding: 0 20px 20px 20px;
          background-color: #F7FFF0;
          border: 2px dashed #96D962;
          border-top: none;
          border-bottom-left-radius: 10px;
          border-bottom-right-radius: 10px;
        }

        .panel {
          border: none;
          cursor: pointer;
        }

        .panel-heading {
          background: transparent;
          background-color: transparent;
          padding: 0;
          border: 0;

          a {
            font-family: Quicksand, Arial, sans-serif;
            border: 1px solid #DDD;
            border-radius: 6px;
            padding: 5px 17px;
            text-align: center;
            display: flex;
            background: #FFF;
            color: $primaryColor;
            font-weight: 700;

            strong {
              width: 100%;
              text-align: center;

              .new-group {
                margin: 3px 0 0 5px;
                padding: 0 5px;
                border-radius: 3px;
                font-size: 10px;
                position: absolute;
                background-color: #f22b2b;
                color: #fff;
              }
            }

            transition: 0.2s ease-in-out;
          }

          a[aria-expanded="true"] {
            border: 2px solid #96D962;
          }
        }

        .panel-body {
          padding: 0;
        }

        .course-list-container .panel-default > .panel-heading .lv0 {
          border: 2px solid #69aa00;
          background-color: transparent;
          color: #69aa00;
          text-align: center;
          text-transform: uppercase;
          margin-bottom: 3px;
          margin-top: 3px;
          line-height: 18px;
        }

        .panel-default .panel-body li {
          display: block;
          &:last-child > a{
            border-bottom: none;
          }
        }

        .panel-default .panel-body li a {
          display: flex;
          align-items: center;
          padding: 10px 10px;
          line-height: 1.2;
          background-color: transparent;
          color: #222;
          font-family: Quicksand, Arial, sans-serif;
          border-bottom: 0.5px solid #96D962;

          .free {
            background: #888;
            color: #fff;
            border-radius: 2px;
            padding: 3px 5px 2px 5px;
            font-size: 11px;
            height: 18px;
          }

          img {
            margin-right: 5px;
            display: inline;
          }

          img.active {
            display: none;
          }

          .lesson-name {
            width: 280px;
            display: inline-block;
            padding-left: 6px;
          }

          .hoc-thu {
            width: 58px;
            display: inline-block;
          }
        }

        .panel-group .panel-heading + .panel-collapse > .list-group, .panel-group .panel-heading + .panel-collapse > .panel-body {
          border-top: none;
        }
        .type-icon {
          width: 20px;
          justify-content: center;
          align-items: center;
          img {
            max-width: 15px;
          }
        }
        .panel-default .panel-body li a:hover {
          background-color: transparent;
          color: #41A336;

          .fa {
            color: #41A336 !important;
          }

          .icon {
            display: none;
          }
          img.active {
            display: inline;
          }
        }

        .panel-default .panel-body li.active a {
          background-color: transparent;
          color: #41A336;

          .fa {
            //color: #fff !important;
          }

          .icon {
            display: none;
          }

          img.active {
            display: inline;
          }
        }

        .panel-group .panel {
          border: none;
          background-color: transparent;
          border-radius: 0;
        }

        .group-lv0 {
          padding-left: 90px;
          margin-bottom: 70px;
        }

      }

    }

    .eju-tab-title {
      color: #0D4890;
      font-weight: 500;
      font-size: 16px;
      margin-bottom: 10px;
      float: left;
    }


    .main-right {
      width: 280px;
      margin-top: 35px;

      .buy-item {
        text-align: center;
        width: 100%;
        float: left;
        padding: 0 0 30px 0;
        height: 72px;

        .buy-btn {
          padding: 12px 20px;
          background-color: $currencyColor;
          color: #fff;
          border-radius: 3px;
          font-size: 20px;
          width: 100%;

          &:hover {
            cursor: pointer;
            border: 1px solid $currencyColor;
            background-color: #fff;
            color: $currencyColor;
          }
        }

        .bought {
          background-color: #69aa00;

          &:hover {
            cursor: pointer;
            border: 1px solid #69aa00;
            background-color: #fff;
            color: #69aa00;
          }
        }
      }

      .course-info-container {
        margin-top: 62px;
        width: 100%;
        float: left;

        .course-heading {
          width: 100%;
          float: left;
          background: $primaryColor;
          padding: 8px 0;
          text-align: center;
          margin-bottom: 15px;

          span {
            font-size: 20px;
            color: #fff;
          }
        }

        .price {
          width: 100%;
          float: left;
          text-align: center;
          color: $currencyColor;
          font-size: 30px;
          font-weight: 600;
        }

        .price-yen {
          width: 100%;
          float: left;
          text-align: center;
          color: #222;
          font-size: 20px;
          font-weight: 500;
          margin-bottom: 20px;
        }

        .info {
          width: 100%;
          float: left;
          color: #222;
          font-size: 16px;
          font-weight: 500;
          padding: 10px 15px;
          border-top: 1px solid #EEE;
        }

      }

      .numberOfDay {
        text-align: center;
        color: red;
        padding-bottom: 12px;
        font-size: 16px;
      }

      .specialized-container {
        margin-top: 0px;
        width: 100%;
        float: left;

        .specialized-title {
          font-size: 16px;
          color: #69aa00;
          float: left;
          width: 100%;
          background: #143a77;
          color: #fff;
          text-align: center;
          line-height: 30px;
          padding: 10px 0;
        }

        .panel-group {
          width: 100%;
          float: left;
          margin-bottom: 0;
        }

        .panel-default > .panel-heading {
          padding: 0;
          background-color: transparent;
          border: none;
          border-radius: 0;
        }

        .panel-default > .panel-heading a {
          display: block;
          padding: 10px 15px;
          background-color: #2659af;
          color: #fff;
          border-bottom: 1px solid #fff;
        }

        .panel-default .panel-body li a {
          display: block;
          line-height: 24px;
          padding: 10px 15px;
          line-height: 1.2;
          padding-left: 20px;
          background-color: #dee9fc;
          color: #404040;

          .free {
            background: #888;
            color: #fff;
            border-radius: 2px;
            padding: 3px 5px 2px 5px;
            float: right;
            margin-right: -3px;
            font-size: 11px;
          }
        }

        .panel-default > .panel-heading + .panel-collapse > .panel-body {
          padding: 0;
          background-color: transparent;
          border: none;
          border-radius: 0;
          border-bottom: 1px solid #fff;

          .scroll-items {
            max-height: 370px;
            overflow-y: auto;
          }
        }

        .panel-default .panel-body li a:hover {
          background-color: #ee6022;
          color: #fff;
        }

        .panel-default .panel-body li.active a {
          background-color: #ee6022;
          color: #fff;
        }

        .panel-default .panel-body li + li {
          border-top: 1px solid #fff;
        }

        .panel-group .panel {
          border: none;
          background-color: transparent;
          border-radius: 0;
        }

        .panel-group .panel + .panel {
          margin: 0;
        }
      }

      .exam-container {
        margin-top: -30px;
        width: 100%;
        float: left;

        .exam-title {
          font-size: 16px;
          color: #69aa00;
          float: left;
          width: 100%;
          background: #143a77;
          color: #fff;
          text-align: center;
          line-height: 30px;
          padding: 10px 0;
        }

        .panel-group {
          width: 100%;
          float: left;
        }

        .panel-default > .panel-heading {
          padding: 0;
          background-color: transparent;
          border: none;
          border-radius: 0;
        }

        .panel-default > .panel-heading a {
          display: block;
          padding: 10px 15px;
          background-color: #2659af;
          color: #fff;
          border-bottom: 1px solid #fff;
        }

        .panel-default .panel-body li a {
          display: block;
          line-height: 24px;
          padding: 10px 15px;
          line-height: 1.2;
          padding-left: 20px;
          background-color: #dee9fc;
          color: #404040;

          .free {
            background: #888;
            color: #fff;
            border-radius: 2px;
            padding: 3px 5px 2px 5px;
            float: right;
            margin-right: -3px;
            font-size: 11px;
          }
        }

        .panel-default > .panel-heading + .panel-collapse > .panel-body {
          padding: 0;
          background-color: transparent;
          border: none;
          border-radius: 0;
          border-bottom: 1px solid #fff;

          .scroll-items {
            max-height: 370px;
            overflow-y: auto;
          }
        }

        .panel-default .panel-body li a:hover {
          background-color: #ee6022;
          color: #fff;
        }

        .panel-default .panel-body li.active a {
          background-color: #ee6022;
          color: #fff;
        }

        .panel-default .panel-body li + li {
          border-top: 1px solid #fff;
        }

        .panel-group .panel {
          border: none;
          background-color: transparent;
          border-radius: 0;
        }

        .panel-group .panel + .panel {
          margin: 0;
        }
      }

      .protected-download {
        background-color: #d2e5b2;
        color: $primaryColor;
        font-weight: bold;
        width: 100%;
        float: left;
        padding: 30px 10px 40px 10px;
        text-align: center;
        font-size: 16px;
      }

      .download-item {
        width: 100%;
        float: left;
        padding: 10px 73px 10px 15px;
        list-style-type: none;
        line-height: 1.2;
        border-top: 1px solid;
        background-color: #EEE;
        border-color: #DDD;
        word-wrap: break-word;

        .badge {
          background-color: #AAA;
          cursor: pointer;
          float: right;
          margin-right: -65px;

          &:hover {
            background-color: $primaryColor;
            color: #fff;
          }
        }
      }

      .see-more {
        width: 100%;
        float: left;
        text-align: center;
        padding: 10px 0;
        border: 1px solid $currencyColor;
        color: $currencyColor;
        border-radius: 3px;
        font-size: 15px;
        margin-bottom: 25px;

        &:hover {
          cursor: pointer;
          background-color: $currencyColor;
          color: #fff;
        }
      }

      .course-list-container {
        .badge {
          display: inline;
        }

        .recuiter-banner {
          width: 100%;
          float: left;

          img {
            width: 100%;
            float: left;
            margin-bottom: 20px;
          }
        }

        .new-group {
          margin: 0;
          padding: 0 5px 2px 5px;
          border-radius: 3px;
          font-size: 10px;
          background-color: #f22b2b;
          color: #fff;
        }
      }

      .lesson-course-list {
        //margin-top: 62px;
      }

      .lesson-specialized-list {
        margin-top: 52px;
        margin-bottom: -52px;
      }

      .lesson-exam-list {
        margin-top: 60px;
        width: 100%;
        float: left;
      }
    }

    .m-r-eju {
      .buy-item {
        .buy-btn {
          padding: 5px 15px;
          background: #fff;
          border: 1px solid red;
          border-radius: 8px;
          text-transform: lowercase;
          font-size: 14px;
          color: red;
          width: 200px;
        }
      }

      .course-list-container {
        margin-top: -95px;

        .group-step-item {
          background: #124896 !important;
          text-align: center !important;
          text-transform: uppercase;
          color: #fff !important;
          font-size: 18px;
          padding: 10px 0 8px 0 !important;

          .zmdi-caret-down {
            left: -56px;
            position: relative;
          }
        }
      }
    }
  }

}


.course-list-container {
  margin: 0px 0 30px;
  float: left;
  width: 100%;

  .block-title {
    font-size: 18px;
    text-transform: uppercase;
    color: #69aa00;
    float: left;
    width: 100%;
    background: $primaryColor;
    color: #fff;
    text-align: left;
    line-height: 30px;
    padding: 10px 10px;
  }

  .panel-group {
    width: 100%;
    float: left;
  }

  .panel-default > .panel-heading {
    padding: 0;
    background-color: transparent;
    border: none;
    border-radius: 0;
  }
}

.course-list-container .panel-default > .panel-heading a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #69aa00;
  color: #fff;
  border-bottom: 1px solid #fff;
}

.course-list-container .panel-default > .panel-heading + .panel-collapse > .panel-body {
  padding: 0;
  background-color: transparent;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #fff;

  .scroll-items {
    max-height: 370px;
    overflow-y: auto;
  }
}

.course-list-container .panel-default > .panel-heading .lv0 {
  border: 2px solid #69aa00;
  background-color: transparent;
  color: #69aa00;
  text-align: center;
  text-transform: uppercase;
  margin-bottom: 3px;
  margin-top: 3px;
  line-height: 18px;
}

.course-list-container .panel-default .panel-body li {
  display: block;
}

.course-list-container .panel-default .panel-body li a {
  display: flex;
  align-items: center;
  line-height: 24px;
  padding: 10px 10px;
  line-height: 1.2;
  background-color: #d2e5b2;
  color: #404040;

  .free {
    background: #888;
    color: #fff;
    border-radius: 2px;
    padding: 3px 5px 2px 5px;
    font-size: 11px;
    height: 18px;
  }
}

.course-list-container .panel-default .panel-body li a:hover {
  background-color: #ee6022;
  color: #fff;
}

.course-list-container .panel-default .panel-body li.active a {
  background-color: #ee6022;
  color: #fff;
}

.course-list-container .panel-default .panel-body li + li {
  border-top: 1px solid #fff;
}

.course-list-container .panel-group .panel {
  border: none;
  background-color: transparent;
  border-radius: 0;
}

.course-list-container .panel-group .panel + .panel {
  margin: 0;
}

.course-list-container .group-lv0 {
  padding-left: 90px;
  margin-bottom: 70px;
}

#specialized-featured-popup {
  width: 500px;
  min-height: auto;
  padding: 0px 0px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);

  .specialized-featured-title {
    text-align: center;
    font-size: 17px;
    color: #fff;
    background-color: #143a77;
    padding: 9px;
  }

  .specialized-featured-content {
    margin-top: 16px;
    margin-left: 30px
  }

  .container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 15px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }

  .specialized-confirm {
    text-align: center;
    color: red;

    .confirm {
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }
}

#specialized-featured-popup .specialized-featured-content .container .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #eee;
  border-radius: 50%;
}

#specialized-featured-popup .specialized-featured-content .container:hover input ~ .checkmark {
  background-color: #ccc;
}

#specialized-featured-popup .specialized-featured-content .container input:checked ~ .checkmark {
  background-color: #2196F3;
}

#specialized-featured-popup .specialized-featured-content .container .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

#specialized-featured-popup .specialized-featured-content .container input:checked ~ .checkmark:after {
  display: block;
}

#specialized-featured-popup .specialized-featured-content .container .checkmark:after {
  top: 6px;
  left: 6px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
}

#timeout-featured-popup {
  width: 500px;
  min-height: auto;
  padding: 0px 0px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);

  .timeout-featured-title {
    text-align: center;
    font-size: 17px;
    color: #fff;
    background-color: #143a77;
    padding: 9px;
  }

  .timeout-featured-content {
    margin-top: 16px;
    margin-left: 30px
  }

  .container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 15px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }
}

#lesson-previous-popup {
  width: 500px;
  min-height: auto;
  padding: 0px 0px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);

  .lesson-previous-title {
    text-align: center;
    font-size: 17px;
    color: #fff;
    background-color: #606163;
    padding: 9px;
  }

  .lesson-previous-content {
    margin-top: 16px;
    margin-left: 20px;
    margin-right: 20px;
  }

  .lesson-previous-confirm {
    text-align: center;

    .confirm, .cancel {
      margin-top: 10px;
      margin-bottom: 10px;
      background-color: #606163;
      color: #fff;
    }
  }
}

.vjs-playback-rate {
  .vjs-menu {
    z-index: 1000;
  }
}

.app-pdf {
  border: 1px solid #C4C4C4;
  .toolbar {
    /* border: 1px solid #A4A4A4; */
    font-family: Quicksand, Arial, sans-serif;
    font-weight: 700;
    .pager {
      background: #F7FFF0;
      padding: 3px;
      margin: 0;

      .page-prev, .page-next {
        color: #333;
        font-size: 12px;
        border-radius: 2px;
        padding: 0px 2px;
        border: 0px;
        background: #eee;

        &:focus {
          outline: -webkit-focus-ring-color auto 0px;
        }

        &:hover {
          background: #ddd;
        }
      }
    }
  }

  .viewport {
    overflow: hidden;
    overflow-y: scroll;
  }

  #viewer {
    #toolbar {
      height: 30px;
    }
  }

  // :host { --viewer-pdf-toolbar-height: 36px; }

}


.flashcards {
  display: flex;
  flex-flow: column;
  align-items: center;
  position: relative;

  .switch {
    position: relative;
    display: inline-block;
    width: calc(2em + 4px);
    height: calc(1em + 4px);

    input {
      opacity: 0;
      width: 0;
      height: 0;
    }
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;

    &:before {
      position: absolute;
      content: "";
      height: 1em;
      width: 1em;
      left: 2px;
      bottom: 2px;
      background-color: white;
      -webkit-transition: .4s;
      transition: .4s;
    }

    /* Rounded sliders */
    &.round {
      border-radius: 34px;

      &:before {
        border-radius: 50%;
      }
    }
  }

  input:checked + .slider {
    background-color: #2196F3;
  }

  input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(1em);
    -ms-transform: translateX(1em);
    transform: translateX(1em);
  }

  .modal-body {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;

    .setting {
      display: grid;
      grid-gap: 20px;
      grid-template-columns: 1fr 1fr;
      justify-content: center;
      align-items: center;
      margin-bottom: 0.5em;
      padding: 0.5em;
      width: 60%;

      span {
        text-align: right;
      }
    }
  }

  .a_cursor--pointer {
    cursor: pointer;
  }

  .settings {
    color: $subColor;
    font-size: 1.3em;
    position: absolute;
    top: 0;
    right: 0;

    span {
      cursor: pointer;
    }
  }

  .stats {
    font-size: 2em;

    &__not_learned {
      color: #eb3434;
    }

    &__learned {
      color: $subColor;
    }
  }

  .content {
    width: 50%;
  }

  .no-transition {
    -webkit-transition: none ! important;
    -o-transition: none ! important;
    transition: none ! important;
  }

  .stackedcards-overflow {
    overflow-y: hidden !important;
  }

  .stackedcards.init {
    opacity: 0;
    /* set về 0 để tạo hiệu ứng hiện ra từ hư vô thật ma mị */
  }

  .stackedcards {
    position: relative;
  }

  .stackedcards * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .stackedcards--animatable {
    -webkit-transition: all 400ms ease;
    -o-transition: all 400ms ease;
    transition: all 400ms ease;
  }

  .stackedcards .stackedcards-container > *,
  .stackedcards-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    will-change: transform, opacity;
    top: 0;
    border-radius: 10px 10px 0 0;
  }

  .stackedcards-overlay.left > div,
  .stackedcards-overlay.right > div,
  .stackedcards-overlay.top > div {
    width: 100%;
    height: 100%;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .stackedcards-overlay.left,
  .stackedcards-overlay.right,
  .stackedcards-overlay.top {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    left: 0;
    opacity: 0;
    top: 0;
    height: 100%;
    font-size: 24px;
    text-transform: uppercase;
    font-weight: 500;
    color: #fff;
  }

  .stackedcards-overlay.top {
    background: transparent;
    color: transparent;
  }

  .stackedcards-overlay.right {
    height: 50px;
    background: #5cb85c;
  }

  .stackedcards-overlay.left {
    height: 50px;
    background: #d9534f;
  }

  .stackedcards-overlay.left:empty,
  .stackedcards-overlay.right:empty,
  .stackedcards-overlay.top:empty {
    display: none !important;
  }

  .stackedcards-overlay-hidden {
    display: none;
  }

  .stackedcards-origin-bottom {
    -webkit-transform-origin: bottom;
    -ms-transform-origin: bottom;
    transform-origin: bottom;
  }

  .stackedcards-origin-top {
    -webkit-transform-origin: top;
    -ms-transform-origin: top;
    transform-origin: top;
  }

  .stackedcards-bottom,
  .stackedcards-top,
  .stackedcards-none {
    background: transparent;
    /* set card background background */
    height: 100%;
  }

  .stackedcards .stackedcards-container > :nth-child(1) {
    position: relative;
    display: block;
  }

  /* global actions buttons*/
  .global-actions {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
    width: 100%;
    margin-top: 50px;
    text-align: center;
  }

  .undo-action {
    font-size: 24px;
    margin-top: 20px;
  }

  #settingModal {
    .fl-setting-header-title {
      width: 100%;
      margin-left: 41px;
    }
  }

  .right-action,
  .left-action {
    width: 100%;
    height: 50px;
    text-transform: uppercase;
    font-weight: 500;
    color: #fff;
  }

  .right-action,
  .left-action {
    background: #5cb85c;

    &:hover {
      background-color: #71c171;
    }
  }

  .final-state {
    width: 50%;
    min-height: 550px;
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
    transition: all 400ms ease;

    &.active {
      opacity: 1;
    }

    &.hidden {
      opacity: 0;
    }

    .btn {
      width: 80%;
      margin-bottom: 1em;
      border: none;
    }
  }

  /* elements on stacked cards */
  .card-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;

    .card-inner.flip {
      transform: rotateY(180deg);
    }

    .card-inner {
      position: relative;
      transition: transform 0.6s;
      width: 100%;
      min-height: 550px;
      transform-style: preserve-3d;
      -webkit-transform-style: preserve-3d;
      .card__face {
        padding: 10px 20px 10px 20px;
        box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.30);
        border-radius: 10px;
        position: absolute;
        height: 100%;
        width: 100%;
        backface-visibility: hidden;
        -moz-backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        background-color: #FFF;

        .title {
          text-align: center;
          margin-top: 20px;
        }

        &--jp {
          .card__word {
            height: 90%;
            border-bottom: 1px solid #ccc;
            display: flex;
            flex-flow: column;
            justify-content: flex-start;
            padding-top: 55px;
            padding-bottom: 10px;

            &--text {
              padding: 0;
              text-align: center;
              font-family: "Noto Serif JP script=all rev=1", "Adobe Blank";
              font-size: 3em;
              line-height: 1.3em;
              font-weight: 700;
              font-style: normal;
            }

            &--example {
              padding: 20px;
              line-height: 2em;
              font-size: 17px;
              overflow: hidden;
              text-align: justify;

              .highlight {
                color: red;
                text-decoration: underline;
              }
            }
            &--example:hover{
              overflow-y: scroll;
            }
          }

          .card__voice {
            height: 10%;
            display: flex;
            align-items: center;
            margin-top: 5px;

            &--button {
              vertical-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              width: 50px;
              height: 50px;
              background: $subColor;
              border-radius: 50%;
              color: #FFF;
              font-size: 1.5em;
            }
          }
        }

        .card-inner.flip {
          transform: rotateY(180deg);
        }
        &--back {
          transform: rotateY(180deg);
        }

        .card_meaning {
          height: 65%;
          display: flex;
          flex-flow: column;
          justify-content: center !important;
          align-items: center;
          padding-bottom: 10px;
          min-height: 45%;

          .meaning {
            max-width: 100%;
            text-align: left;
            font-size: 20px;
            line-height: 26px;
            margin-top: 10px;
          }

          img {
            display: block;
            max-height: 270px;
          }
        }

        .card-bottom {
          height: 35%;
        }

        .card_comment {
          &--title {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 5%;
            font-size: 16px;
            color: red;
            text-transform: uppercase;
            text-align: center;
            border-top: 1px solid #e6e6e6;
            margin-bottom: 10px;
            padding-top: 15px;
          }

          &--avatar {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 5px;

            div {
              width: 25px;
              height: 25px;
              border-radius: 50%;
              border: 1px solid #ccc;
              display: flex;
              justify-content: center;
              align-items: center;
              overflow: hidden;
            }
          }

          &--content {
            display: flex;
            flex-flow: column;
            height: 100%;
            line-height: 18px;
            width: 70%;

            .time {
              font-size: 12px;
              color: #BCBCBC;
            }

            .comment {
              display: block;
              display: -webkit-box;
              text-align: left;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              font-size: 13px;
              width: 100%;
              line-height: 1.4;
              //height: calc(13px * 1.4 * 3);
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          &--empty {
            color: $subColor;
            display: flex;
            flex-flow: column;
            justify-content: center;
            align-items: center;
            height: 20%;
            padding-top: 10px;
          }

          &--main {
            display: flex;
            flex-flow: row;
            align-items: center;
            font-size: 13px;
          }

          &--action,
          &--toggle {
            color: $subColor;
          }

          &--action {
            display: flex;
            flex-flow: column;
            justify-content: space-between;
            align-items: flex-start;
            height: 100%;
            font-size: 1.2em;
          }

          &--toggle {
            max-height: 10%;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-size: 2em;

            i {
              grid-column-start: 3;
            }
          }
        }
      }
    }
  }
}

.lesson__progress {
  &--bar {
    .progressbar-text {
      position: absolute;
      right: 13px;
      top: -37px;
      font-size: 18px;
    }
  }

  &--circle {
    width: 30px;
    position: relative;

    .progressbar-text {
      position: absolute;
      left: 50%;
      top: 50%;
      padding: 0px;
      margin: 0px;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 14px;
    }
  }
}

.quiz {
  &__button {
    border: 2px solid #FFF;
    border-radius: 40px;
    padding: 10px 10px;
    text-align: center;
    background: #7fbd18;
    width: 100%;
    color: #FFF;
    cursor: pointer;
    transition: all ease 0.2s;
    user-select: none;

    &:hover {
      background: #3A6E18;
    }
  }

  &__circle-icon {
    width: 50px;
    height: 50px;
    background: white;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    font-size: 20px;
    -webkit-box-shadow: 0px 0px 9px -4px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: 0px 0px 9px -4px rgba(0, 0, 0, 0.75);
    box-shadow: 0px 0px 9px -4px rgba(0, 0, 0, 0.75);
  }

  &__container {
    background: none;
    width: 100%;
    padding: 0;
    height: 600px;

    &--loading {
      background: none;
    }
  }

  &__header {
    width: 100%;
    padding: 0 20px;
    color: #fff;
    height: 50px;

    &--progress {
      display: grid;
      width: 100%;
      height: 30px;

      &-block {
        position: relative;
        display: flex;
        flex-flow: column;
        justify-content: flex-end;
        align-items: center;
      }

      &-number {
        display: block;
        width: 100%;
        padding: 0;
        margin: 0;
        text-align: center;
      }

      &-flag {
        color: black;
        padding: 0;
      }
    }
  }

  &__body {
    padding: 0 20px 10px 20px;
    width: 100%;
    height: 95%;
    border-radius: 0 0 17px 17px;

    &--result {
      height: 80%;
      width: 100%;

      &-card {
        //background: #69AA00;
        background: none;
        min-height: 200px;
        border-radius: 20px;
        padding: 20px 0;
        color: #69AA00;
        text-align: center;
        display: flex;
        justify-content: center;
        //grid-template-columns: 1fr 3fr;
        -webkit-box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.26);
        -moz-box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.26);
        box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.26);

        &-left {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        &-right {
          padding: 0 20px;
          display: flex;
          flex-flow: column;
          justify-content: center;
          text-align: justify;
        }

        &-point {
          height: 150px;
          padding: 0 30px;
          border-right: 2px solid rgba(255, 255, 255, 0.8);
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 30px;
          font-weight: bold;
        }
      }

      &-button {
        margin-top: 20px;
        display: flex;
        justify-content: center;

        .btn {
          margin-right: 10px;
        }
      }

      &-questions {
        width: 80%;
        padding: 20px;
        background: #f7f7f7;
        margin: 0 auto;
      }

      &-question:not(:last-child) {
        padding: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid #ededed;
      }

      &-answers {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;

        &-gap {
          display: grid;
          grid-gap: 10px;
          grid-template-columns: 1fr 1fr 1fr 1fr;
        }
      }

      &-answer {
        &-correct {
          padding: 0 5px;
          border: 2px solid #0ab22c;
          border-radius: 10px;
          color: #0ab22c;
        }

        &-wrong {
          padding: 0 5px;
          border: 2px solid #9f041b;
          border-radius: 10px;
          color: #9f041b;
        }
      }
    }

    &--title {
      //padding: 10px 0;

      &-content {
        //font-size: 2em;
      }

      .checkbox__switch {
        position: relative;
        display: inline-block;
        width: calc(3em + 6px);
        height: calc(1.5em + 6px);

        input {
          opacity: 0;
          width: 0;
          height: 0;
        }
      }

      .checkbox__slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
        border-radius: 34px;

        &:before {
          position: absolute;
          content: "";
          height: 1.5em;
          width: 1.5em;
          left: 3px;
          bottom: 3px;
          border-radius: 50%;
          background-color: white;
          -webkit-transition: .4s;
          transition: .4s;
        }
      }

      input:checked + .checkbox__slider {
        background-color: #69AA00;
      }

      input:focus + .checkbox__slider {
        box-shadow: 0 0 1px #69AA00;
      }

      input:checked + .checkbox__slider:before {
        -webkit-transform: translateX(1.5em);
        -ms-transform: translateX(1.5em);
        transform: translateX(1.5em);
      }
    }

    &--slider {
      position: relative;
      height: 80%;
      width: 100%;
    }

    &--slide {
      position: absolute;
      top: 0;
      left: 0;
      //height: 100%;
      width: 100%;
      display: flex;
      flex-flow: column;
      justify-content: space-between;
    }

    &--content {
      &-question {
        display: flex;
        align-items: center;
        font-size: 22px;
        line-height: 37px;
        font-weight: 200;

        span {
          font-size: 22px !important;
        }

        rt > span {
          font-size: 12px !important;
        }
      }

      &-answers {
        display: grid;
        grid-gap: 20px;
        grid-template-columns: 1fr 1fr;
      }

      &-answer {
        padding: 3px 20px;
        border: 2px solid #7fbd18;
        color: #111;
        font-weight: 200;
        background: #FFF;
        border-radius: 30px;
        text-align: left;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      &-block {
        color: #FFF;
        border: 2px solid #FFF;
        padding: 0 5px;

        &-correct {
          background: #0c4d17;
        }

        &-wrong {
          background: #9f041b;
        }
      }
    }

    .slider {
      position: relative;
      height: 80%;
      width: 100%;

      .slide {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        display: flex;
        flex-flow: column;
        justify-content: space-between;
      }
    }

    &--navigation {
      width: 100%;
      min-height: 175px;

      &-button {
        width: 100%;
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 0;
        z-index: 2;
      }

      &-hint {
        background: aliceblue;
        padding: 20px 20px 60px 20px;
        border-radius: 25px;
        position: absolute;
        width: 100%;
        bottom: 0;
        z-index: 1;
        display: flex;
        flex-flow: row;
        justify-content: space-between;
        -webkit-box-shadow: -1px -2px 4px -4px rgba(0, 0, 0, 0.75);
        -moz-box-shadow: -1px -2px 4px -4px rgba(0, 0, 0, 0.75);
        box-shadow: -1px -2px 4px -4px rgba(0, 0, 0, 0.75);
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity .5s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.list-enter-active, .list-leave-active {
  transition: all 1s ease-in-out;
}

.list-enter, .list-leave-to /* .list-leave-active below version 2.1.8 */
{
  opacity: 0;
}

.hint-enter-active,
.hint-leave-active,
.hint-move {
  transition: 500ms cubic-bezier(0.59, 0.12, 0.34, 0.95);
  transition-property: opacity, transform;
}

.hint-enter {
  opacity: 0;
  transform: translateX(0) scaleY(0.5);
  transform-origin: center bottom;
}

.hint-enter-to {
  opacity: 1;
  transform: translateX(0) scaleY(1);
  transform-origin: center bottom;
}

.hint-leave-active {
  position: absolute;
}

.hint-leave-to {
  opacity: 0;
  transform: scaleY(0);
  transform-origin: center bottom;
}

.ltr-enter-active,
.ltr-leave-active,
.ltr-move {
  transition: 500ms cubic-bezier(0.59, 0.12, 0.34, 0.95);
  transition-property: opacity, transform;
}

.ltr-enter {
  opacity: 0;
  transform: translateX(-30px);
  transform-origin: center top;
}

.ltr-enter-to {
  opacity: 1;
  transform: translateX(0);
  transform-origin: center top;
}

.ltr-leave-to {
  opacity: 0;
  transform: translateX(30px);
  transform-origin: center top;
}

.video-js.vjs-modal-dialog {
  background: transparent !important;
}

.video__js {
  &--overlay {
    .vjs-modal-dialog {
      background: none !important;
    }

    .vjs-modal-dialog-content {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      padding: 0 !important;
    }

    &-green-tea {
      background: rgba(127, 219, 218, 0.8) !important;
    }

    &-transparent {
      background: transparent !important;
    }

    &-dark {
      background: rgba(57, 154, 191, 0.8) !important;
    }

    &-half-screen {
      top: initial !important;
      bottom: 0 !important;
      height: 100% !important;

      .video__js {
        &--overlay {
          &-question {
            height: 50%;
          }

          &-answers {

          }

          &-answer {

          }
        }
      }
    }

    &-skip-btn {
      padding: 7px 26px;
      background: rgb(21,114,150);
      cursor: pointer;
      -webkit-transition: .3s;
      transition: .3s;
      border-radius: 3px;
      -webkit-box-shadow: inset -2px -4px 0 rgba(0,0,0,.3);
      box-shadow: inset -2px -4px 0 rgba(0,0,0,.3);
    }

    &-quiz {
      width: 100%;
      height: 100%;
      padding: 3% 3% 2% 3%;

      &-wrapper {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        flex-flow: column;
        justify-content: flex-end;
        //align-items: flex-end;
      }

      &-content {
        width: 100%;
        height: 45%;
        display: flex;
        flex-flow: column;
        justify-content: flex-start;
        align-items: center;
        margin: 0 auto;

        .h-100 {
          height: 100%;
        }

        .h-50 {
          height: 45%;
        }

        .h-25 {
          height: 25%;
        }

        .grid-1-1 {
          grid-template-columns: 1fr !important;;
        }

        .grid-2-1 {
          grid-template-columns: 1fr 1fr !important;;
        }

        .grid-3-1 {
          grid-template-columns: 1fr 1fr 1fr !important;
        }
      }

      &-qa {
        width: 100%;
        height: 100%;
        display: flex;
        flex-flow: column;
        justify-content: flex-start;

        &.longg {
          justify-content: center;
        }
      }

      &-timer {
        width: 100%;
        height: 40px;
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        #countdown {
          position: relative;
          height: 40px;
          width: 40px;
          text-align: center;

          #countdown-number {
            color: white;
            display: inline-block;
            line-height: 40px;
          }

          svg {
            position: absolute;
            top: 0;
            right: 0;
            width: 40px;
            height: 40px;
            transform: rotateY(-180deg) rotateZ(-90deg);

            circle {
              stroke-dasharray: 113px;
              stroke-dashoffset: 0px;
              stroke-linecap: round;
              stroke-width: 2px;
              stroke: white;
              fill: none;
              animation: countdown 20s linear 1 forwards;
            }
          }
        }

        @keyframes countdown {
          from {
            stroke-dashoffset: 0px;
          }
          to {
            stroke-dashoffset: 113px;
          }
        }
      }
    }

    &-question {
      background: rgba(0, 0, 0, 0.9);
      font-weight: bold;
      font-size: 18px;
      width: 100%;
      font-family: 'Noto Serif JP', serif;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 20px;
      margin-bottom: 10px;
      border-radius: 4px;
    }

    &-answers {
      width: 100%;
      display: grid;
      grid-gap: 10px;
      grid-template-columns: 1fr 1fr;
    }

    &-answer {
      background: rgba(0, 0, 0, 0.9);
      color: #FFF;
      display: grid;
      grid-template-columns: 1fr 4fr;
      font-size: 1.3em;
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      height: 100%;
      border-radius: 9px;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      box-shadow: inset -2px -4px 0 rgba(0, 0, 0, 0.3);

      &:hover {
        //background: rgba(126, 202, 222, 0.71) !important;
        opacity: 90% !important;
      }

      &-blink {
        animation: blinkColor 0.1s 4;
      }
    }

    &-navigation {
      //width: 100%;
      padding: 5px 0;
      text-align: left;

      &-button {
        padding: 7px 26px;
        background: rgba(21, 114, 150, 0.7);
        cursor: pointer;
        transition: 0.3s;
        border-radius: 3px;
        box-shadow: inset -2px -4px 0 rgba(0, 0, 0, 0.3);

        &:hover {
          background: rgba(15, 79, 155, 0.9);
        }
      }
    }

    &-full-screen {
      .video__js {
        &--overlay {
          &-quiz {
            &-wrapper {
              justify-content: flex-start;
            }

            &-content {
              width: 100%;
              height: 100%;
              padding: 5% 0;
              display: flex;
              flex-flow: row;
              justify-content: flex-start;
              align-items: flex-end;
              margin: 0 auto;
            }

            &-qa {
              width: 65%;
              margin-right: 10px;
              justify-content: flex-end;

              &.longgg {
                justify-content: center !important;
              }
            }
          }

          &-question {
            background: rgba(21, 114, 150, 0.4);
          }

          &-answers {
            width: 100%;
            display: grid;
            grid-gap: 9px;
            grid-template-columns: 1fr 1fr;
          }

          &-answer {
            display: flex;
            padding: 1em 1em;

            &.longgg {
              padding: 0 1em;
            }
          }
        }
      }
    }
  }
}

.vjs-fullscreen {
  .video__js {
    &--overlay {
      &-quiz {
        padding: 1% 1% 1% 1%;
      }

      &-question {
        font-size: 30px !important;
        border-radius: 8px;
      }

      &-answer {
        padding: 0;
        font-size: 3em !important;
        border-radius: 6px;
      }

      &-half-screen {
        .video__js {
          &--overlay {
            &-quiz {
              &-content {
                padding: 0 5%;
              }

              &-timer {
                padding: 0 5%;
              }
            }

            &-question {
              padding: auto 5vmin;
              height: 30%;
            }

            &-answer {

            }
          }
        }
      }

      &-full-screen {
        .video__js {
          &--overlay {
            &-question {

            }

            &-answer {
              padding: 0 3em;
              height: 10vmin;
            }
          }
        }
      }

      &-navigation {
        padding: 2vmin 0;

        &-button {
          padding: 2vmin 2.5vmin;
        }
      }
    }
  }
}

@keyframes blinkColor {
  0% {
    background-color: #7ECADE;
  }
  /* YOU CAN ADD MORE COLORS IN THE KEYFRAMES IF YOU WANT
  50%{
    background-color: #55d66b;
    border: 5px solid #126620;
  }
  */
  100% {
    background-color: #93CA8C;
  }
}


.buy-box {
  width: 40%;
  float: right;
  margin: 50px 0 30px 0;

  .buy-btn {
    padding: 5px 15px 3px 15px;
    border-radius: 8px;
    border: 1px solid red;
    float: right;
    color: red;
  }
}

.ld-detail-title {
  font-size: 25px;
  width: 60%;
  float: left;
  color: #474DDA;
  margin: 50px 0 30px 0;

  b {
    color: #444;
  }
}

.ld-top-menu {
  width: 1020px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #474DDA;
  padding: 20px;
  border-radius: 10px;
  line-height: 1;
  background-color: #E5E6FF;
  color: #474DDA;
  font-weight: 600;
  font-family: Montserrat;
  font-size: 20px;
  margin-top: 20px;
  .tab-item {
    font-size: 16px;
    display: flex;
    align-items: center;
  }

  .active {
    border: 1px solid #474DDA;
    box-shadow: 0px 0px 4px #474DDA;
    color: #474DDA;
  }
}

.banner-img {
  width: 100%;
  float: left;
  border-radius: 8px;
}


.main .main-course .main-left-ld {
  margin-top: 10px;
  width: 670px !important;

  .graph-container {
    background: #fff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
    min-height: 400px;
    margin-bottom: 40px;
    margin-top: 0;
  }

  .cover-container {
    background: #fff;
    min-height: 400px;
    margin-top: 0;

    img {
      max-width: 100%;
    }

    .myplayer {
      height: 380px !important;
    }

    .play-icon-btn {
      //margin-left: 300px !important;
    }
  }

  .cover-container-mb {
    margin-bottom: 60px;
  }

  .course-price-container {
    padding: 10px 20px 10px 20px !important;
    border-radius: 4px !important;
    background: #FFFFFF;
    border: 1px dashed #8486F1;
    clear: both !important;
    margin-bottom: 50px;
    line-height: 1.5;
    float: left;

    .info {
      width: 100%;
      float: left;
      font-size: 16px;
      font-weight: 500;
      padding: 5px 0;
      border-bottom: 1px solid #EEE;

      &:last-child {
        border-bottom: none;
      }

      span {
        color: #474DDA;
      }
    }
  }

  .apexcharts-toolbar {
    display: none;
  }

  .chart-container {
    width: 100%;
    float: left;
    background-image: linear-gradient(#F4F4F4, #fff);
    border-radius: 8px;

    .canvas-container {
      width: 94%;
      margin: 15px 3% 10px 3%;
      height: 406px;
    }

    .tab-container {
      width: 100%;
      float: left;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      margin-top: 20px;
      padding: 0 20px;
      box-sizing: border-box;

      .tab-item {
        background: #FFFFFF;
        border: 0.646982px solid #E5E5E5;
        box-shadow: 0px 0px 6.46982px rgba(0, 0, 0, 0.15);
        padding: 3px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 15px;
        color: #666;
        margin-top: 5px;
        margin-right: 5px;
      }
    }
  }
}

.main {
  .main-course {
    .main-right-ld {
      margin-top: 10px !important;
      width: 310px;
    }
  }
}

.course-list-ld {
  .course-group-menu {
    background: #FFFFFF;
    border: 0.5px solid #DFDFDF;
    box-sizing: border-box;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
    border-radius: 7px;
    margin-bottom: 15px;
    a {
      display: flex;
      align-items: center;
      padding: 10px 10px;
      line-height: 1.2;
      color: #404040;
      border-top: 1px solid #DDD;
      font-size: 15px;
      font-family: Roboto;
      opacity: .8;
    }
    .panel-heading {
      background: #fff;
      color: #222;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        a {
          color: #000;
        }
      }
    }

    .cg-icon {
      width: 15px;
      height: 15px;
      margin-right: 10px;
    }

    a {
      color: #444;
      font-size: 16px;
      font-family: arial;
    }

    .panel-heading a:before {
      font-family: 'Glyphicons Halflings';
      content: "\e114";
      float: right;
      transition: all 0.5s;
      font-size: 12px;
      color: #ccc;
    }

    .panel-heading.active a:before {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      transform: rotate(180deg);
    }

  }

  .panel-body {
    padding: 0 15px;
    border-top: none !important;
  }

  .panel-default .panel-body li a {
    display: flex;
    align-items: center;
    line-height: 24px;
    padding: 10px 10px;
    line-height: 1.2;
    color: #404040;
    border-top: 1px solid #DDD;
    font-size: 15px;
    font-family: Roboto;
    opacity: .8;

    .score {
      color: #474DDA;
    }

    .passed {
      color: #0FB78F;
    }

    .not-passed {
      color: #d43030;
    }
  }

  .mn-tab-2 {
    display: flex;
    align-items: center;
    line-height: 24px;

    .score {
      color: #474DDA;
    }

    .passed {
      color: #0FB78F;
    }

    .not-passed {
      color: #d43030;
    }
  }

}


#main-lesson-ld {
  height: auto;
  margin-bottom: 30px;

  .empty-box {
    width: 100%;
    float: left;
    border-radius: 6px;
    border: 1px dashed #474DDA;
    padding: 150px;
    text-align: center;
  }

  .popup-result {
    width: 760px;
    padding: 30px 30px;
    background: #fff;
    position: fixed;
    top: 150px;
    -webkit-backdrop-filter: saturate(180%) blur(15px);
    backdrop-filter: saturate(180%) blur(15px);
    border: 1px solid #CCC;
    z-index: 9999999999;
    left: calc(50vw - 380px);
    background-color: rgba(255, 255, 255, 0.72);
    transition: background-color 0.5s cubic-bezier(0.28, 0.11, 0.32, 1);
    transition-property: background-color, -webkit-backdrop-filter;
    transition-property: background-color, backdrop-filter;
    transition-property: background-color, backdrop-filter, -webkit-backdrop-filter;

    .zmdi-close {
      float: right;
      margin: -30px -30px;
      padding: 15px;
      cursor: pointer;
      background: #f06;
    }

    h2, h1, h3 {
      text-align: center;
    }

    h1 {
      padding: 50px 0;
      margin: 20px 150px;
      border: 1px dashed #474DDA;
    }

    .img-bg {
      width: 695px;
      height: 412px;
    }

    .rinfo {
      span {
        font-size: 12px;
        position: absolute;
      }

      .ru_name {
        float: left;
        margin: -255px 0px 0 160px;
      }

      .ru_level {
        float: left;
        margin: -232px 0px 0 160px;
      }

      .ru_pass {
        float: left;
        margin: -207px 0px 0 160px;
      }

      .ru_score1 {
        margin: -54px 0 0 66px;
        font-size: 18px;
      }

      .ru_score2 {
        margin: -54px 0 0 243px;
        font-size: 18px;
      }

      .ru_score3 {
        margin: -54px 0 0 420px;
        font-size: 18px;
      }

      .ru_total {
        margin: -54px 0 0 570px;
        font-size: 18px;
      }
    }

    .btn-do-again {
      padding: 12px 40px;
      font-size: 17px;
      margin: 20px auto 0 auto;
      color: #fff;
      display: block;
      width: 160px;
      background: #474DDA;
      border-radius: 10px;
    }

  }

}

#main-lesson-ld:after {
  clear: both;
  content: ".";
  display: block;
  height: 0;
  line-height: 0;
  visibility: hidden;
}


.lesson-ld-tab-menu {
  width: 100%;
  padding: 0 0 20px 0;

  .exam-container-menu {
    width: 480px;
    margin: 0 auto;
    height: 130px;
    background: #fff;
    position: relative;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .item-exam {
      padding: 25px 5px 10px 5px;
      width: 150px;
      text-align: center;
      color: #999;
      cursor: pointer;

      img {
        height: 40px;
        margin-bottom: 10px;
        display: inline;
      }

      span {
        font-size: 17px;
        font-weight: bold;
        color: #222;
      }

      &:hover {
        font-weight: bold;
      }
    }

    .active {
    }
  }
}

.heading-bar {
  background: #bcbdef;
  width: calc(100% + 30px);
  margin: -30px 0 30px -15px;
  padding-left: 15px;

  .panel-tab {
    .btn {
      width: 32%;
      font-size: 15px;
      font-weight: bold;
      padding: 4px 0 3px 0;
      margin: 9px 0;
      border-radius: 4px;
    }

    .active {
      background: #fff;
    }
  }
}
.exercise-container {
  width: 100%;
  float: left;
  padding-right: 30px;
  border: .5px solid #EEE;
  padding: 30px 15px;
  border-radius: 8px;
  overflow: hidden;

  .nb-bar {
    width: 668px;
    float: left;
    margin: -70px 0 0 -15px;
    font-size: 18px;
    position: absolute;

    .timer {
      width: 50%;
      float: left;
      color: green;
    }

    .btn {
      float: right;
      margin-left: 15px;
      height: 32px;
    }

    .download-pdf {
      border-radius: 8px;
      color: #8486F1;
      height: 26px;
      border: 1.5px solid #8486F1;
      padding-top: 2px;
    }
  }
  .mp3-container {
    width: 100%;
    float: left;
    margin: -10px 0 30px 0;

    audio {
      width: 100%;
      float: left;
      height: 35px;
    }
  }

  .question {
    font-size: 16px;
    width: 100%;
    float: left;
  }

  label > input[type="radio"] {
    display: none;
  }

  label > input[type="radio"] + *::before {
    content: "";
    display: inline-block;
    vertical-align: bottom;
    width: 1rem;
    height: 1rem;
    margin-right: 0.3rem;
    border-radius: 50%;
    border-style: solid;
    border-width: 0.1rem;
    border-color: gray;
  }

  label > input[type="radio"]:checked + * {
    color: teal;
  }

  label > input[type="radio"]:checked + *::before {
    background: radial-gradient(teal 0%, teal 40%, transparent 50%, transparent);
    border-color: teal;
  }

  fieldset {
    margin: 20px;
    max-width: 400px;
  }

  label > input[type="radio"] + * {
    display: inline-block;
    padding: 0.5rem 1rem;
  }

  .answer-container {
    margin: 10px 0 20px 0;
    width: 100%;
    float: left;

    .answer-item {
      width: 50%;
      float: left;
      padding-right: 20px;
      margin-bottom: 10px;

      .answers-label {
        font-weight: 400;
        display: inline;
      }
    }
  }
}

.submit-exam-btn {
  background: #F2994A;
  border-radius: 20px;
  color: #fff;
  width: 100%;
  margin-top: 10px;
}

.main-comment {
  width: 1024px;
  margin: 0 auto;

  .comment-container {
    width: 680px;
    float: left;
  }
}


.m-l-checkpoint {
  //margin-left: 185px;
  background: #FFF;
  border-radius: 12px;
  padding: 15px 20px;
  margin-bottom: 30px;

  label {
    font-family: 'Montserrat' !important;
    font-weight: normal !important;
  }
}

//main css for lesson checkpoint
#lesson-checkpoint {
  display: flex;
  justify-content: center;
  height: auto;
  text-align: center;
  margin-bottom: 30px;
  margin-top: 30px;

  .main-block {
    display: flex;

    .main-left {
      background: whitesmoke;

      .lesson-title {
        text-align: center;
      }
    }

    .main-right {
      margin: 0;
      padding: 0;
      min-height: 0;

      .time-end {
        text-align: center;
        font-weight: 400;
        font-size: 25px;
        color: red;
      }

      .time-remaining {
        font-size: 20px;

        span {
          margin-top: 5px;
          font-size: 30px;
          display: block;
        }
      }
    }
  }

  .cover-container {
    text-align: left;

    .questions-container {
      margin-top: 25px;
    }
  }

  .empty-box {
    width: 100%;
    float: left;
    border-radius: 6px;
    border: 1px dashed #474DDA;
    padding: 150px;
    text-align: center;
  }

  .popup-result {
    width: 760px;
    background: #fff;
    position: fixed;
    top: 90px;
    -webkit-backdrop-filter: saturate(180%) blur(15px);
    backdrop-filter: saturate(180%) blur(15px);
    border: 1px solid rgba(0, 0, 0, 0.87);;
    z-index: 9999999999; //left: calc(50vw - 380px);
    background-color: rgba(255, 255, 255, 0.72);
    transition: background-color 0.5s cubic-bezier(0.28, 0.11, 0.32, 1);
    transition-property: background-color, -webkit-backdrop-filter;
    transition-property: background-color, backdrop-filter;
    transition-property: background-color, backdrop-filter, -webkit-backdrop-filter;

    .zmdi-close {
      position: absolute;
      top: 15px;
      left: 95%;
      color: #FFFFFF;
      font-size: 24px;
    }

    h2, h1, h3 {
      text-align: center;
      margin: 0;
    }

    h1 {
      padding: 50px 0;
      margin: 20px 80px;
      border: 1px dashed #474DDA;
      border-radius: 10px;
    }

    .img-bg {
      width: 695px;
      height: 412px;
    }

    .rinfo {
      span {
        font-size: 12px;
        position: absolute;
      }

      .ru_name {
        float: left;
        margin: -255px 0px 0 160px;
      }

      .ru_level {
        float: left;
        margin: -232px 0px 0 160px;
      }

      .ru_pass {
        float: left;
        margin: -207px 0px 0 160px;
      }

      .ru_score1 {
        margin: -54px 0 0 66px;
        font-size: 18px;
      }

      .ru_score2 {
        margin: -54px 0 0 243px;
        font-size: 18px;
      }

      .ru_score3 {
        margin: -54px 0 0 420px;
        font-size: 18px;
      }

      .ru_total {
        margin: -54px 0 0 570px;
        font-size: 18px;
      }
    }

    .btn-do-again {
      padding: 12px 40px;
      font-size: 17px;
      margin: 20px auto 0 auto;
      color: #fff;
      display: block;
      width: 160px;
      background: #474DDA;
      border-radius: 10px;
    }


  }

  .btn-cham-diem {
    background: #96D962;
    color: #000000;
    border: none;
    border-radius: 10px;
    padding: 9px 35px;
    text-align: center;
    cursor: pointer;
    font-weight: 700;
    font-size: 20px;
    margin-top: 15px;
  }

  .btn-retest {
    background: #ff000a;
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 9px 35px;
    text-align: center;
    cursor: pointer;
    font-weight: 700;
    font-size: 20px;
    margin-top: 20px;
  }

  .btn-submit {
    background: #96D962;
    color: #000000;
    border: none;
    border-radius: 9999px;
    padding: 21px 142px;
    text-align: center;
    cursor: pointer;
    font-weight: 700;
    font-size: 22px
  }

  .checkpoint-submit {
    span {
      display: inline-block;
      background-color: #96D962;
      font-size: 25px;
      font-weight: 700;
      padding: 15px 80px;
      border-radius: 9999px;
      text-transform: capitalize;
      cursor: pointer;
    }
  }

  .popup-result {
    width: 550px;
    border-radius: 20px;

    .popup-title-mb {
      color: #FFFFFF;
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
      background-color: #96D962;
      padding: 10px 0;
    }

    .popup-warning {
      font-size: 18px;
      color: #000000;
      font-weight: 400;
      display: block;
      margin-bottom: 10px;
    }

    .main-popup {
      padding: 0px 40px;
      input, select {
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.87);
        background-color: #fff;
      }

      button {
        padding-top: 10px;
        margin-bottom: -10px;
      }
    }

    .triangle-bottomright {
      svg {
        float: right;
      }
    }
  }

  .results-title {
    background: #96d962;
    width: 100%;
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #ffffff;
  }

  .main-popup-results {
    p {
      text-transform: uppercase;
    }

    .result-text {
      span {
        display: inline-block;
        margin-top: 20px;
        text-align: center;
        text-transform: uppercase;
      }

      .result-text-pass {
        color: #96D962;
      }

      .result-text-faill {
        color: #FB6D3A;
      }
    }
  }

  .answer-result {
    text-align: left;
    padding: 0 20px;

    h3 {
      font-weight: bold;
    }

    .answer-true {
      border: 1px solid #ffd336;
      border-radius: 5px;
      padding: 0px 2px;
    }

    .answer-false {
      border: 1px solid #e00707;
      border-radius: 5px;
      padding: 0px 2px;
    }

    .questions-container {
      .question-content {
        font-weight: normal;
        white-space: break-spaces;
        strong {
          font-weight: normal;
        }
        table {
          width: 100% !important;
        }
      }
    }

    .answer-container {

      .answer-item {
        margin: 7px 0;

        .label-answer {
          font-weight: unset;

          .icon-true {
            color: #96d962;
          }

          .icon-fasle {
            color: #e00707;
          }
        }
      }
    }
  }

  .v-modal-container {
    width: 30pc;
    border-radius: 5px;
    padding: 0;
  }
}

//css reponsive lesson checkpoint
@media only screen and (max-width: 1024px) {
  .main-block {
    flex-direction: column;
    align-items: center;
  }

  .btn-cham-diem {
    margin-top: 30px;
  }

  .time-remaining {
    margin-top: 15px;
  }

  .time-remaining-mobile {
    display: block !important;
    font-size: 20px;
    margin-top: 20px;

    span {
      font-size: 30px;
    }
  }

  .lesson-title {
    margin-top: 30px !important;
  }
}

@media only screen and (min-width: 1025px) {
  .timer-sm {
    position: fixed;
    margin-left: 30px;
  }
}

@media only screen and (max-width: 768px) {

  #lesson-checkpoint {
    margin-top: 0px;
  }

  .time-remaining-zone {
    position: fixed;
    width: 100%;
    left: 0;

    .time-remaining-mobile {
      margin: 0px 15px 0px 15px;

      .time-remaining-title {
        background: #ffe179;
        color: #000000;
        border-radius: 15px;
        padding: 5px 0;
        font-size: 15px;
        font-weight: 900;

        .time-remaining-center {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        span {
          margin-left: 5px;
          display: inline;
          color: #ff0000;
        }
      }
    }
  }

  .lesson-title {
    margin-top: 120px !important;
  }

  .time-remaining {
    display: none;
  }

  .time-end {
    margin-top: 20px;
  }

  //course extend
  .course_extension{
    flex-direction: column;
    .extension_policy_data,.extension_more{
      width: 100% !important;
    }
    .extension_policy_data {
      margin-bottom: 40px !important;
    }
  }
  .course-ldp__extension {
    padding: 0 50px !important;
  }

}

@media only screen and (max-width: 425px) {
  #lesson-checkpoint {
    margin-top: 0px;

    .v-modal-container {
      width: 22pc;
    }
  }
}

@media only screen and (max-width: 320px) {
  #lesson-checkpoint {
    margin-top: 0px;

    .v-modal-container {
      width: 19pc;
    }
  }
}

//popup plus info
.plus-info {
  display: none;
  width: 661px;
  min-height: 305px;
  box-sizing: border-box;
  border-radius: 8px;
  border: 1px solid #F2994A;
  padding: 0;

  .title {
    background: #F2994A;
    border-radius: 8px;
    color: white;
    width: 100%;
    height: 54px;
    position: relative;
    bottom: 10px;
    vertical-align: middle;

    h4 {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      font-size: 20px;
      line-height: 23px;
      margin-left: 20px;
    }
  }

  .content {
    margin-top: 10px;
    margin-left: 30px;
    font-size: 15px;
    line-height: 24px;
    color: black;
    min-height: 175px;
  }

  .end {
    text-align: center;
    margin-bottom: 10px;
    margin-top: 5px;

    .plus-buy {
      width: 207px;
      background: #F2994A;
      border-radius: 30.24px;
      color: white;
      font-size: 18.292px;
    }
  }

  .fancybox-close-small {
    display: none;
  }
}

//end popup plus info
.kaiwa-container {
  .plus-intro {
    h4 {
      font-size: 12px !important;
    }
  }
}

.course-list-roadmap {
  width: 100%;
  margin-top: 14px;
  min-height: 1000px;
  float: left;
  font-family: 'Montserrat', sans-serif;

  #stage-container {
    display: none;
  }

  .stage-container {
    background: #E0F5C5;
    border-radius: 12px;
    width: 100%;
    float: left;
    padding: 30px 10px;

    .st-step {
      float: left;
      width: 90px;
      position: relative;
      z-index: 2;

      &:hover {
        transform: scale(1.1);
      }
    }

    .st1 {
      margin: -250px 0 0 10px;

      &:hover {
        margin-top: -275px;
        cursor: pointer;
      }
    }

    .st2 {
      margin: -130px 0 0 100px;

      &:hover {
        margin-top: -115px;
        cursor: pointer;
      }
    }

    .st3 {
      margin: -365px 0 0 170px;

      &:hover {
        margin-top: -380px;
        cursor: pointer;
      }
    }
    .st4 {
      margin: -275px 0 0 260px;

      &:hover {
        margin-top: -260px;
        cursor: pointer;
      }
    }
  }

  .roadmap-category {
    .rmot {
      width: 70px;
      height: 62px;
      background: #FFBF00;
      border-radius: 10px;
      color: black;
      padding: 7px;
      position: fixed;
      bottom: 33px;
      right: 100px;
      font-size: 11px;
      font-weight: 500;
      display: flex;
      justify-content: center;
      text-align: center;
      align-items: center;
      box-shadow: 0 5px 0px 0px rgba(143,102,0,0.75);
      &:hover {
        transform: translateY(5px);
        box-shadow: none;
      }
    }
    .rmboxmn {
      height: 220px;
      width: 70px;
      margin-top: -200px;
      position: fixed;
      transform: scale(1.4);

      .rm_mn-item {
        width: 50px;
        float: left;
        margin-bottom: 10px;
        cursor: pointer;
        opacity: 0.9;

        &:hover {
          transform: scale(1.1);
          opacity: 1;
        }
      }
    }
  }

  .stage-container-show {
    animation: stage-container-zoomout 100ms linear forwards;
  }

  .stage-container-hide {
    animation: stage-container-zoomin 100ms linear forwards;
  }

  @keyframes stage-container-zoomout {
    from { height: 209px; }
    to { height: 522px; }
  }

  @keyframes stage-container-zoomin {
    from { height: 522px; }
    to { height: 209px; }
  }

  .stage-small-container {
    background: #E0F5C5;
    border-radius: 12px;
    width: 100%;
    padding: 10px 46.5px;
    position: relative;
    .stage-small {
      background-image: url('../img/roadmap/ttbg_small.png');
      background-position: center center;
      background-size: contain;
      background-repeat: no-repeat;
      width: 127px;
      height: 40px;
      position: absolute;
      top: 10px;
      left: 15px;
      span {
        display: inline-block;
        width: 100%;
        text-align: center;
        font-weight: bold;
        margin-top: 8px;
        font-size: 12px;
      }
    }
    .search-stage {
      position: absolute;
      bottom: 12px;
      right: 18px;
      background-color: #96D962;
      width: 45px;
      height: 45px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 7px;
      svg {
        width: 35px;
      }
    }
  }

  .stage-lesson-head {
    margin-top: 0;
    width: 100%;
    float: left;
    background-image: url('../img/roadmap/ttbg.png');
    background-position: center center;
    height: 80px;
    background-size: 100%;
    position: relative;
    .lesson-path-prev {
      position: absolute;
      top: 17px;
      left: 29px;
      cursor: pointer;
      width: 38px;
    }
    .lesson-path-next {
      position: absolute;
      top: 17px;
      right: 35px;
      cursor: pointer;
      width: 37px;
    }

    .st-title {
      margin: 28px 0;
      float: left;
      width: 100%;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      background-repeat: no-repeat;
    }
  }

  .stage-lesson {
    margin-top: 30px;
    width: 100%;
    float: left;
    background-image: url('../img/roadmap/mn.png');
    background-repeat: repeat-y;
    border-radius: 12px;
    box-shadow: 0 2px 5px 1px #ddd;
    min-height: 200px;
    background-color: transparent;
    background-size: 100%;

    .mnbg {
      width: 435px;
      padding-left: 25%;
      background-size: 95%;
      float: left;
      margin-top: 5px;
      padding-bottom: 20px;
      padding-right: 92px;
      max-height: 1200px;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      .stg-item {
        width: 60px;
        height: 60px;
        float: left;
        margin-bottom: 25px;
        margin-right: 50px;

        .stage-drop-menu {
          margin-left: -40px;
          float: right;
          width: 250px;
          margin-top: 20px;
          background-color: #F5FFED;
          z-index: 33;

          .caret-up {
            position: absolute;
            float: left;
            margin-top: -13px;
            left: 60px;
          }

          li {
            width: 100%;
            float: left;
            padding: 0;
            border-bottom: 1px solid #96D962;

            a {
              padding: 7px 15px;
              white-space: unset;

              i {
                margin-right: 10px;
              }
            }
          }

          li:last-child {
            border: none;
          }

          .dropdown-divider {
            height: 0;
            margin: .5rem 0;
            overflow: hidden;
            border-top: 1px solid #e9ecef;
          }
        }
      }

      .item3 {
        margin-left: 57px;
      }

      .g-item {
        border: 8px solid #E6E1CD;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        background-color: #fff;
        box-shadow: 0 6px #CCC;
        text-align: center;
        color: #BFBFBF;
        padding-top: 7px;
        font-size: 24px;
        font-weight: bold;
        font-family: 'Montserrat';
        position: relative;
        z-index: 22;

        &:hover {
          cursor: pointer;
          background: #EEE;
        }
      }

      .done {
        border: 8px solid #96D962;
        box-shadow: 0 6px #4BA72F;
        color: green;
        background: #F5FFED;
      }

      .in-progress {
        border: 8px solid #FFE335;
        box-shadow: 0 6px #FF9900;
        color: orange;
        background: #FFF8CA;
      }
    }
  }

  //css vùng expand
  .rm-expand {
    background: #FCFCFC;
    display: flex;
    padding: 30px 0;

    .bg-left, .bg-right {
      width: 230px;
      min-height: 80vh;

      .bg-img {
        width: 100%;
        margin: 100px 0 0 0;
      }
    }

    .bg-center {
      width: 62%;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .bg-center::-webkit-scrollbar {
      display: none;
    }

    .stage-lesson-head {
      background-repeat: no-repeat;
      background-position: center center;
      height: 80px;
      background-size: 45%;

      .st-title {
        margin: 28px 0;
        float: left;
        width: 100%;
        text-align: center;
        font-size: 16px;
        font-weight: bold;
        background-repeat: no-repeat;
      }
    }

    .mnbg {
      width: 650px;
      margin-left: 16%;
      background-size: 45%;
      float: left;
      margin-top: 45px;
      padding-bottom: 20px;

      .stg-item {
        width: 60px;
        height: 60px;
        float: left;
        margin-bottom: 25px;
        margin-right: 88px;

        .stage-drop-menu {
          margin-left: -40px;
          float: right;
          width: 250px;
          margin-top: 20px;
          background-color: #F5FFED;
          z-index: 33;

          .caret-up {
            position: absolute;
            float: left;
            margin-top: -13px;
            left: 60px;
          }

          li {
            width: 100%;
            float: left;
            padding: 0;
            border-bottom: 1px solid #96D962;

            a {
              padding: 7px 15px;
              white-space: unset;

              i {
                margin-right: 10px;
              }
            }
          }

          li:last-child {
            border: none;
          }

          .dropdown-divider {
            height: 0;
            margin: .5rem 0;
            overflow: hidden;
            border-top: 1px solid #e9ecef;
          }
        }
      }

      .item5 {
        margin-left: 230px;
        margin-right: 230px;
      }

      .g-item {
        border: 8px solid #E6E1CD;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        background-color: #fff;
        box-shadow: 0 6px #CCC;
        text-align: center;
        color: #BFBFBF;
        padding-top: 7px;
        font-size: 24px;
        font-weight: bold;
        font-family: 'Montserrat';
        position: relative;
        z-index: 22;

        &:hover {
          cursor: pointer;
          background: #EEE;
        }
      }

      .done {
        border: 8px solid #96D962;
        box-shadow: 0 6px #4BA72F;
        color: green;
        background: #F5FFED;
      }

      .in-progress {
        border: 8px solid #FFE335;
        box-shadow: 0 6px #FF9900;
        color: orange;
        background: #FFF8CA;
      }
    }
  }
}

@media only screen and (max-width: 600px) {
  .main .main-course .main-left-ld {
    width: 100% !important;
  }
  .fast-area{
    img{
      width: 30%;
    }
  }
}
.cta {
  position: absolute;
  bottom: -38%;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  &-button {
    display: inline-flex;
    font-size: 1.2vw;
    font-weight: 700;
    text-decoration: none;
    color: white;
    height: 60px;
    padding: 0 60px;
    border-radius: 100px;
    box-shadow: 2px 4px 0 0 rgba(0,0,0,0.7);
    cursor: pointer;
    align-items: center;
    justify-content: center;
    margin: 7px;
    user-select: none;
    transition: 0.15s ease-in-out;
    will-change: transform;
    min-width: 250px;
    text-align: center;
    svg {
      width: 2vw;
      margin-right: 7px;
    }
    &:hover, &:active, &:focus {
      text-decoration: none;
      color: white;
    }
    &:hover {
      transform: translate(2px, 2px);
      box-shadow: none;
    }
  }
}
.course-cta {
  min-height: 90px;
  width: 100vw;
  padding: 24px;
  background-color: #96D962;
  color: #000;
  display: flex;
  align-items: center;
  &__inner {
    width: 1200px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
  }
  &__title {
    font-family: Montserrat, Arial, sans-serif;
    font-size: 30px;
    font-weight: 700;
    margin-right: 60px;
    text-transform: uppercase;
    a {
      text-decoration: none;
      color: #000;
    }
  }
  &.dark {
    background-color: #000;
    .course-cta__title a {
      color: #fff;
    }
    //position: sticky;
    //top: 70px;
    z-index: 5;
  }
  &__price {
    font-family: Quicksand, Arial, sans-serif;
    font-size: 21px;
    font-weight: 400;
    border-radius: 7px;
    background-color: white;
    border: 2px solid #000;
    text-decoration: none;
    color: black;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    &:hover,
    &:active,
    &:focus,
    &:visited {
      text-decoration: none;
      color: black;
    }
    .cart-icon {
      height: 100%;
      padding: 0 10px;
      background-color: #FB6D3A;
      border-radius: 0 5px 5px 0;
      cursor: pointer;
    }
  }

  &__fb,
  &__buy {
    padding: 12px 20px;
    border-radius: 23px;
    font-family: Montserrat, Arial, sans-serif;
    font-size: 16px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-width: 207px;
    cursor: pointer;
    transition: 0.15s ease-in-out;
    box-shadow: 4px 8px 0 0 rgba(0,0,0,0.25);
    -webkit-box-shadow: 4px 8px 0 0 rgba(0,0,0,0.25);
    -moz-box-shadow: 4px 8px 0 0 rgba(0,0,0,0.25);

    &:hover {
      transform: translateX(2px) translateY(2px);
      box-shadow: 10px 12px 16px -19px rgba(0, 0, 0, 0.25);
      -webkit-box-shadow: 10px 12px 16px -19px rgba(0, 0, 0, 0.25);
      -moz-box-shadow: 10px 12px 16px -19px rgba(0, 0, 0, 0.25);
    }
  }

  &__fb {
    border: 2px solid white;
    background: #96D962;
    color: #fff;
  }

  &__buy {
    background: #fff;
    color: #000;
  }

  &__ld-back {
    background-color: white;
    color: #8486F1;
    padding: 8px 20px;
  }
}
.play-icon-btn {
  svg#play {
    width: 100px;
    margin: 120px auto;
    display:block;
    cursor: pointer;
    transform-origin: 50% 50%;
    border-radius: 50%;
  }

  svg#play #triangle {
    fill:white;
    transition:500ms;
    transform-origin: 50% 50%;
  }


  svg #lineOne, svg #lineTwo {
    transform-origin: 50% 50%;
    transition: 1s;
  }
  &:hover {
    background: rgba(255,255,255,0.3);
    svg#play #triangle {
      fill: #96D962;
      transform-origin: 50% 50%;
    }
    svg #lineOne {
      transform: rotate(260deg);
      -webkit-transform: rotate(260deg);
      -moz-transform: rotate(260deg);
      -o-transform: rotate(260deg);
      transform-origin: 50% 50%;
    }
    svg #lineTwo {
      transform: rotate(-450deg);
      transform-origin: 50% 50%;
    }
  }
}
.course-ldp {
  font-family: Montserrat, sans-serif;
  font-size: 24px;
  line-height: 1.2;
  font-weight: 400;
  color: black;
  img {
    max-width: 100%;
  }
  &__header {
    @include aspect-ratio(1920, 796);
    background-image: url("../img/new_course/course-header-bg.svg");
    background-position: center;
    background-size: cover;
    position: relative;
    background-repeat: no-repeat;
    margin-top: 70px;
    &-inner {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }
    &-image {
      position: absolute;
      width: 37vw;
      top: 9%;
      left: 10%;
    }
    &-board {
      width: 35vw;
      height: 20vw;
      background-color: #96D962;
      position: absolute;
      top: 23.3%;
      left: 53%;
      box-shadow:
          0 0 0 2px #000,
          0 0 0 24px #fff,
          0 0 0 26px #000,
          15px 15px 0 30px rgba(0,0,0,0.3);
      display: flex;
      flex-flow: column;
      justify-content: flex-start;
      align-items: center;
      padding: 30px;
      .board {
        &-title {
          background-color: #000;
          text-transform: uppercase;
          color: white;
          font-weight: 700;
          padding: 12px 30px;
          position: absolute;
          top: -53px;
          left: 27%;
        }
        &-name {
          font-size: 5vw;
          font-weight: 800;
          line-height: 1;
          .small {
            font-size: 3vw;
          }
        }
        &-expired {
          font-size: 24px;
          color: #828282;
          margin-top: 25px;
        }
        &-price {
          padding: 19px 10px 9px 10px;
          background-color: white;
          border: 2px solid black;
          font-size: 41px;
          font-weight: 700;
          color: black;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 15px 15px 0 0 rgba(0,0,0,0.3);
          margin-top: 24px;
          .yen,
          .vnd {
            position: relative;
            margin-left: 60px;
            &::before {
              font-weight: 400;
              font-size: 22px;
              position: absolute;
              top: -8px;
              left: -55px;
            }
          }
          .yen::before {
            content: 'JPN';
          }
          .vnd::before {
            content: 'VNĐ';
          }
        }
      }
    }
  }
  &__introduce {
    @include aspect-ratio(1920, 796);
    background-image: url("../img/new_course/video-gioi-thieu.svg");
    background-position: center;
    background-size: cover;
    position: relative;
    background-repeat: no-repeat;
    &-inner {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }
    &-name {
      position: absolute;
      top: 61.2%;
      left: 36.3%;
      font-weight: 800;
      font-size: 1.2vw;
    }
  }
  &__adventure {
    padding-top: 70px;
    padding-bottom: 230px;
    background-image: url("../img/new_course/adventure-bg.svg");
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: flex-start;
    &-inner {
      margin-top: 60px;
      background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='30' ry='30' stroke='%23333' stroke-width='2' stroke-dasharray='16' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");      width: 1265px;
      border-radius: 30px;
      background-color: white;
      padding: 75px;
      display: flex;
      flex-flow: column;
      align-items: center;
      box-shadow: rgba(100, 100, 111, 0.2) 0 7px 29px 0;
      position: relative;
    }
    &-content {
      padding: 30px 60px;
      width: 100%;
      margin-top: 60px;
      margin-bottom: 20px;
      border-radius: 15px;
      font-size: 16px;
      font-family: Quicksand, Arial, sans-serif;
      font-weight: 400;
      display: flex;
      .title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
      }
      .description {
        list-style-type: disc;
        color: black;
        li {
          list-style: disc;
          line-height: 1.61;
          list-style-position: inside;
        }
      }
      & > * {
        width: calc((100% - 2%) / 3);
        &:not(:last-child) {
          margin-right: 4%;
        }
      }
    }
  }
  &__feature {
    padding: 120px;
    width: 100%;
    background-image: url("../img/new_course/feature-bg.svg");
    min-height: 1560px;
    position: relative;
    &-icon {
      position: absolute;
      top: 50%;
      left: 5%;
    }
    .three-item,
    .two-item {
      display: flex;
      justify-content: center;
      & > * {
        &:not(:last-child) {
          margin-right: 133px;
        }
      }
    }
    .three-item {
      margin-top: 100px;
    }
    .two-item {
      margin-top: 120px;
    }
    &-item {
      width: 267px;
      height: 478px;
      overflow: hidden;
      position: relative;
      video {
        position: absolute;
        width: 386%;
        max-width: 386%;
        top: -73px;
        left: -389px;
      }
    }
  }
  &__trial {
    padding: 100px;
    background-image: url("../img/new_course/trial-bg.svg");
    &-inner {
      width: 75%;
      margin: auto;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 60px;
    }
    &-item {
      width: 350px;
      height: 174px;
      box-shadow: 0 0 17.6175px rgba(0, 0, 0, 0.2);
      border-radius: 25px;
      background-color: white;
      overflow: hidden;
      position: relative;
      transition: 0.2s;
      &:hover {
        transform: translateY(-10px);
      }
      &:nth-child(2) {
        width: 30vw;
        height: 15vw;
        margin: 0 3vw;
      }
      a {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        transition: 0.2s;
        &:hover {
          filter: contrast(102%) brightness(102%);
        }
      }
    }
  }
  &__benefit {
    padding: 125px;

    &-inner {
      background-image: url("../img/new_course/benefit-bg.svg");
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      min-height: 540px;
    }

    .three-item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      margin: auto;
      margin-top: 60px;
      padding-left: 140px;
    }
    &-item {
      width: 266px;
      height: 266px;
      margin-left: 60px;
      position: relative;
      &::after {
        position: absolute;
        background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='133' ry='133' stroke='%23333' stroke-width='2' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='round'/%3e%3c/svg%3e");
        content: ' ';
        width: 100%;
        height: 100%;
        border-radius: 50%;
        top: 0;
        left: 0;
        z-index: 1;
      }
      &::before {
        content: ' ';
        width: 100%;
        height: 100%;
        position: absolute;
        left: -14px;
        top: 0;
        border-radius: 50%;
        z-index: 0;
      }
      &.yellow::before {
        background-color: #FFE179;
      }
      &.green::before {
        background-color: #D8EDB9;
      }
      &.blue::before {
        background-color: #C7E1FF;
      }
      img {
        position: absolute;
        top: -38px;
        left: 80px;
        z-index: 2;
      }
      div {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        font-family: Quicksand, Arial, sans-serif;
        line-height: 1.21;
        text-align: center;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 3;
        padding: 40px;
      }
    }
  }
  &__teacher {
    padding-bottom: 240px;
    h1 {
      font-family: 'Montserrat', sans-serif;
      font-style: normal;
      font-weight: 700;
      font-size: 36px;
      text-align: center;
      line-height: 1.21;
      .green {
        color: #96D962;
      }
    }
    &-inner {
      width: 800px;
      margin: auto;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-top: 85px;
    }
    &-item {
      width: calc((100% - 120px) / 4);
      text-align: center;
      font-size: 14px;
      font-family: Quicksand, Arial, sans-serif;
      font-weight: 400;
      &:not(:last-child) {
        margin-right: 40px;
      }
      &:nth-child(even) > img {
        margin-bottom: 60px;
        &:hover {
          transform: perspective(3000px) rotateY(-15deg);
        }
      }
      &:nth-child(odd) > img {
        margin-bottom: 113px;
        &:hover {
          transform: perspective(3000px) rotateY(15deg);
        }
      }
      img {
        width: 100%;
        transform: perspective(1500px);
        transition: transform 1s ease 0s;
      }
      ul {
        padding: 13px;
      }
    }
  }
  &__promo {
    .title {
      font-family: Montserrat, sans-serif;
      font-size: 40px;
      font-weight: 700;
      color: white;
      background: #FFBF00;
      padding: 24px;
      text-align: center;
    }
    &-cta {
      background-color: #F7FFF0;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-bottom: 60px;
    }
    &-item {
      position: relative;
      margin-top: 30px;
      background: #f6f6f6;
      .combo-info {
        border: solid 1px #ddd;
        border-radius: 10px;
        padding-top: 15px;
        margin-top: -25px;
        padding-bottom: 15px;
      }
      .sold {
        padding-top: 10px;
        font-family: Quicksand, Arial, sans-serif;
        font-size: 14px;
        width: 100%;
        text-align: center;
        border-top: 1px dotted #ccc;
        color: #FB6D3A;
      }
      .board-price {
        padding-bottom: 15px;
        font-size: 16px;
        font-weight: 700;
        color: black;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 24px;
        .yen,
        .vnd {
          position: relative;
          margin-left: 25px;
          &::before {
            font-weight: 400;
            font-size: 9px;
            position: absolute;
            top: -8px;
            left: -20px;
          }
        }
        .yen::before {
          content: 'JPN';
        }
        .vnd::before {
          content: 'VNĐ';
        }
      }
    }
    &-inner {
      background-color: #F7FFF0;
      padding: 54px;
      display: flex;
      flex-wrap: wrap;
      margin: auto;
      justify-content: space-evenly;
    }
  }
  &__feedback {
    padding: 100px;
  }
  &__comment {
    width: 60%;
    margin: auto;
    padding-bottom: 100px;
  }
  &__extension{
    padding: 0 150px;
    margin-bottom: 70px;

    .course_extension {
      display: flex;
      margin-top: 60px;
      .extension_policy_data {
        width: 55%;
        border: 1px solid #96D962;
        border-radius: 20px;
        overflow: hidden;
        margin-right: 45px;
        table {
          text-align: center !important;
          height: 100%;
          width: 100%;
          thead {
            background: #96D962;
            tr {
              font-family: 'Quicksand';
              font-style: normal;
              font-weight: 700;
              font-size: 20px;
              line-height: 25px;
              color: #000000;
              th {
                padding: 20px 0;
                text-align: center !important;
                text-transform: capitalize;
              }
            }
          }
          tbody {
            tr {
              border-top: 1px solid #96D962;
              font-size: 16px;
              td {
                padding: 25px 0;
              }
              td:nth-child(3){
                font-weight: bold;
              }
            }
          }
        }

      }

      .extension_more {
        width: 45%;
        padding: 0px 50px;
        padding-top: 50px;
        background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='30' ry='30' stroke='%23333' stroke-width='2' stroke-dasharray='16' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");

        h4 {
          font-family: 'Quicksand';
          font-style: normal;
          font-weight: 700;
          font-size: 24px;
          line-height: 30px;
          color: #000000;
        }

        ul {
          font-size: 16px;

          li {
            font-size: 16px;
            margin-bottom: 10px;
          }
        }

        .conntact-now {
          margin-top: 30px;

          a {
            font-size: 16px;
            color: #41A336;
            background: #F5FEEC;
            padding: 10px 40px;
            border: 1px solid #96D962;
            border-radius: 30px;
          }
        }

        .pencil-image {
          display: flex;
          flex-direction: row-reverse;
        }
      }
    }
  }
}
