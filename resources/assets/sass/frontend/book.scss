@import "variables";
@import "mixins";
.w {
  &-80 {width: 80% !important;}
}
.btn-download {
  &-all {
    font-family: Quicksand, Arial, sans-serif;
    font-size: 18px;
    font-weight: 500;
    padding: 4px 20px;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    transition: 0.15s ease-in-out;
    display: inline-block;
    width: auto;
    background-color: $green-secondary;
    border: 1px solid $green-dark;
    color: $green-dark;
    &:hover {
      background-color: $green-dark;
      color: $white;
    }
  }
  &-one {
    font-family: Quicksand, Arial, sans-serif;
    font-size: 18px;
    font-weight: 500;
    padding: 5px 30px;
    border-radius: 50px;
    display: inline-block;
    cursor: pointer;
    color: $white;
    background-color: $green-primary;
    transition: 0.15s ease-in-out;
    &:hover {
      background-color: $green-dark;
      color: $white;
    }
  }
}
.btn-book {
  width: 90%;
  border-radius: 5px;
  font-family: Quicksand, <PERSON><PERSON>, sans-serif;
  font-size: 18px;
  font-weight: 500;
  padding: 4px 20px;
  text-align: center;
  cursor: pointer;
  transition: 0.15s ease-in-out;
  &--green {
    &-100 {
      background-color: $green-primary;
      &:hover {
        color: $white;
      }
    }
    &-0 {
      background-color: $green-secondary;
      border: 1px solid $green-primary;
    }
  }
}
html {
  scroll-behavior: smooth;
}
.main-books {
  padding-bottom: 300px;
  .slider-book {
    width: 100vw;
    background: url('../img/book/book-bn.svg') no-repeat center;
    background-size: cover;

    @include aspect-ratio(1369, 513);
  }
  .main-book{
    .new-release{
      width: 100%;
      @include aspect-ratio(1046, 443);
      margin: 66px auto;
      box-sizing: border-box;
      border-radius: 40px;
      background: no-repeat center url('../img/book/new_book_border.svg');
      background-size: contain;
      &__content {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }
      h2 {
        text-align:center;
        margin-top: 30px;
        img{
          height: 45px;
        }
      }
    }
  }
  .book {
    &__navigation {
      background-color: $green-secondary;
      height: 67px;
      width: 100%;
      position: relative;
      margin-top: 110px;
      &-items {
        position: absolute;
        display: flex;
        padding: 0 100px;
        bottom: 0;
        left: 0;
      }
      &-item {
        padding: 8px 40px;
        margin: auto 5px;
        cursor: pointer;
        font-size: 18px;
        font-weight: 400;
        font-family: 'Quicksand', serif;
        display: flex;
        flex-flow: column;
        justify-content: flex-end;
        align-items: center;
        filter: grayscale(100%);
        transition: 0.15s ease-in-out;
        img {
          max-width: 44px;
        }
        &:hover,
        &.active {
          filter: grayscale(0);
          border-bottom: 4px solid $green-primary;
          font-weight: 700;
        }
      }
    }
    &__relate {
      max-width: 1000px;
      margin: 100px auto;
      &-items {
        width: 100%;
        display: flex;
        justify-content: flex-start;
      }
      &-title {
        font-family: Roboto, Arial, sans-serif;
        font-size: 25px;
        font-weight: 600;
        margin-bottom: 30px;
      }
    }
    &__category {
      margin-top: 60px;
      padding: 0 30px;
    }
    &__title {
      font-family: Montserrat, Arial, sans-serif;
      font-size: 25px;
      font-weight: 700;
      img {
        margin-right: 10px;
        max-width: 52px;
      }
    }
    &__description {
      font-family: Montserrat, Arial, sans-serif;
      font-size: 16px;
      font-weight: 400;
      max-width: 550px;
      margin: 15px auto;
      text-align: center;
      &.quote {
        position: relative;
        .quote-symbol {
          position: absolute;
          &--before {
            top: 0;
            left: -30px;
          }
          &--after {
            bottom: 10px;
            right: -20px;
          }
        }
      }
    }
    &__slider {
      max-width: 100%;
    }
    &__list {
      position: relative;
      margin: 30px auto;
      display: flex;
      justify-content: center;
      align-items: center;
      max-width: 1170px;
      &-navigation {
        position: absolute;
        width: 108%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .slide-btn {
          cursor: pointer;
          opacity: 0.7;
          transition: 0.15s ease-in-out;
          &:hover {
            opacity: 1;
          }
        }
      }
    }
    &__item {
      width: 224px;
      margin: 0 25px;
      @include aspect-ratio(224, 385);
      background: no-repeat center url('../img/book/book-item-bg.svg');
      background-size: contain;
      &-inner {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-flow: column;
        align-items: center;
        justify-content: space-between;
        padding: 18px 0;
        text-decoration: none;
        color: $gray-100;
        .image {
          cursor: pointer;
          width: 85%;
          img {
            filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.25));
          }
        }
        .title,
        .solds {
          font-family: Quicksand, Arial, sans-serif;
        }
        .title {
          font-size: 16px;
        }
        .solds {
          font-size: 14px;
          color: $green-dark;
        }
      }
    }
    &__price {
      font-family: Quicksand, Arial, sans-serif;
      font-size: 15px;
      border: 2px dashed $green-primary;
      background-color: $green-secondary;
      border-radius: 2px;
      padding: 0;
      div {
        padding: 4px 25px;
        &:nth-child(1) {
          border-bottom: 2px dashed #96D962;
        }
      }
    }
  }

  .book-detail {
    max-width: 960px;
    margin: auto;
    margin-top: 90px;
    display: flex;
    justify-content: flex-start;
    &__title {
      font-family: Roboto, Arial, sans-serif;
      font-size: 30px;
      line-height: 1.6;
      font-weight: 700;
    }
    &__description {
      font-family: Quicksand, Arial, sans-serif;
      font-size: 16px;
      line-height: 1.6;
    }
    &__carousel {
      width: 30%;
      &-preview {
        background-color: #eeeeee;
        width: 100%;
        cursor: pointer;
        transition: 0.3s ease-in-out;
        border: 1px solid $gray-10;
        &:hover {
          background-color: #b8ffba;
        }
      }
      &-items {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 10px;
        margin-top: 10px;
      }
      &-item {
        @include aspect-ratio(81, 68);
        width: 100%;
        background-color: #eeeeee;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
        transition: 0.3s ease-in-out;
        &:hover {
          background-color: #b8ffba;
        }
      }
    }
    &__content {
      width: 60%;
      margin-left: 65px;
    }
    &__price {
      padding: 7px 10px;
      margin-left: 15px;
      border: 2px dashed $green-primary;
      background-color: $green-secondary;
      border-radius: 4px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      sup {
        margin-right: 7px;
      }
      .divider {
        margin: 0 10px;
      }
    }
    &__btn {
      &--buy {
        display: block;
        background-color: $green-primary;
        padding: 10px 0;
        margin-top: 35px;
        text-align: center;
        font-size: 18px;
        color: $white;
        border-radius: 6.5px;
        cursor: pointer;
      }
    }
  }
  .audio {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 1044px;
      margin: auto;
      margin-top: 80px;
    }
    &__item {
      padding: 30px 0;
      width: 1044px;
      margin: auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:first-child {
        padding: 20px 0;
        font-family: Montserrat, Arial, sans-serif;
        font-size: 20px;
        font-weight: 700;
      }
      &:not(:last-child) {
        border-bottom: 1px solid #C4C4C4;
      }
      &-filename {
        width: 30%;
      }
      &-file {
        width: 50%;
        audio {
          width: 100%;
          &::-webkit-media-controls-play-button {
            width: 100px;
          }
        }
        audio::-webkit-media-controls-play-button {
          width: 100px;
        }
      }
    }
  }
  .new-release {
    &__box {
      position: relative;
      margin: 30px auto;
      display: flex;
      justify-content: center;
      align-items: center;
      max-width: 1170px;
      .book__slider {
        max-width: 80%;
      }
      .book__list-navigation {
        max-width: 104%;
        margin-left: 1%;
        margin-bottom: 5%;
      }
    }
    &__item {
      width: 153px;
      margin: 0 25px;
      &:first-child {
        margin-left: 0;
      }
      &:last-child {
        margin-right: 0;
      }
      &-inner {
        display: flex;
        flex-flow: column;
        align-items: flex-start;
        justify-content: space-between;
        text-decoration: none;
        color: $gray-100;
        .image {
          cursor: pointer;
          width: 100%;
          img {
            width: 100%;
            filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.25));
          }
        }
        .title,
        .solds,
        .price {
          font-family: Quicksand, Arial, sans-serif;
        }
        .title {
          font-size: 16px;
          margin-top: 15px;
        }
        .solds {
          font-size: 14px;
          color: $green-dark;
        }
        .price {
          strong {
            font-size: 16px;
          }
          span {
            font-size: 12px;
            color: #828282;
          }
        }
      }
    }

  }
}
