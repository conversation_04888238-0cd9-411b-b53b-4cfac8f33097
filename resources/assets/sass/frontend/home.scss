@import "variables";
@import "mixins";

.mobile {
  display: none;
}
.fb-comments-container {
  background-color: #fff;
  .center-container{padding: 30px 0;
    .nav-pills{
      li{
        a{padding: 5px 20px;
          h3 {
            margin: 0;
            font-size: 20px;
            padding-top: 3px;
            padding-bottom: 2px;
          }
        }
      }
    }
  }
  .row-content-fb{
    .col-md-4{
      img{margin-bottom:15px;}
    }
  }
  .nihongozin-app-fancybox {
    cursor: pointer;
  }
}

#noti_register_support {
  .modal-title {
    font-size: 18px;
  }
}

.popup-nihongozin-app {
  width: 500px;
  min-height: 250px;
  border-radius: 5px;
  .popup-nihongozin-app-content {
    height: 200px;
    .get-app-text {
      height: 100px;
      text-align: center;
      padding: 30px;
      font-size: 20px;
    }
    .get-app-icon {
      text-align: center;
      margin-top: 18px;
      img {
        width: 150px;
      }
    }
  }
}

  .fb_dialog {background: transparent !important}
 .fb_dialog_advanced{bottom: 80px !important;}


// .fb-livechat, .fb-widget{display: none }
// .fb-livechat{
//   &:hover{.bubble-msg{display: block;}}
// }
// .ctrlq.fb-button, .ctrlq.fb-close{position: fixed; right: 25px; cursor: pointer }
// .ctrlq.fb-button{
//      background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjwhRE9DVFlQRSBzdmcgIFBVQkxJQyAnLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4nICAnaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkJz48c3ZnIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDEyOCAxMjgiIGhlaWdodD0iMTI4cHgiIGlkPSJMYXllcl8xIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAxMjggMTI4IiB3aWR0aD0iMTI4cHgiIHhtbDpzcGFjZT0icHJlc2VydmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxnPjxyZWN0IGZpbGw9IiMwMDg0RkYiIGhlaWdodD0iMTI4IiB3aWR0aD0iMTI4Ii8+PC9nPjxwYXRoIGQ9Ik02NCwxNy41MzFjLTI1LjQwNSwwLTQ2LDE5LjI1OS00Niw0My4wMTVjMCwxMy41MTUsNi42NjUsMjUuNTc0LDE3LjA4OSwzMy40NnYxNi40NjIgIGwxNS42OTgtOC43MDdjNC4xODYsMS4xNzEsOC42MjEsMS44LDEzLjIxMywxLjhjMjUuNDA1LDAsNDYtMTkuMjU4LDQ2LTQzLjAxNUMxMTAsMzYuNzksODkuNDA1LDE3LjUzMSw2NCwxNy41MzF6IE02OC44NDUsNzUuMjE0ICBMNTYuOTQ3LDYyLjg1NUwzNC4wMzUsNzUuNTI0bDI1LjEyLTI2LjY1N2wxMS44OTgsMTIuMzU5bDIyLjkxLTEyLjY3TDY4Ljg0NSw3NS4yMTR6IiBmaWxsPSIjRkZGRkZGIiBpZD0iQnViYmxlX1NoYXBlIi8+PC9zdmc+) center no-repeat #0084ff;
//      width: 60px; height: 60px; z-index: 999; text-align: center; bottom: 80px; border: 0; outline: 0; border-radius: 60px; -webkit-border-radius: 60px; -moz-border-radius: 60px;
//       -ms-border-radius: 60px; -o-border-radius: 60px; box-shadow: 0 1px 6px rgba(0, 0, 0, .06), 0 2px 32px rgba(0, 0, 0, .16); -webkit-transition: box-shadow .2s ease; background-size: 80%;
//       transition: all .2s ease-in-out }
// .ctrlq.fb-button:focus, .ctrlq.fb-button:hover{transform: scale(1.1); box-shadow: 0 2px 8px rgba(0, 0, 0, .09), 0 4px 40px rgba(0, 0, 0, .24) }
// .fb-widget{background: #fff; z-index: 1000; position: fixed; width: 360px; height: 435px; overflow: hidden; opacity: 0; bottom: 0; right: 24px; border-radius: 6px; -o-border-radius: 6px;
//   -webkit-border-radius: 6px; box-shadow: 0 5px 40px rgba(0, 0, 0, .16); -webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, .16); -moz-box-shadow: 0 5px 40px rgba(0, 0, 0, .16);
//   -o-box-shadow: 0 5px 40px rgba(0, 0, 0, .16) }
// .fb-credit{text-align: center; margin-top: 8px }
// .fb-credit a{transition: none; color: #bec2c9; font-family: Helvetica, Arial, sans-serif; font-size: 12px; text-decoration: none; border: 0; font-weight: 400 }
// .ctrlq.fb-overlay{z-index: 0; position: fixed; height: 100vh; width: 100vw; -webkit-transition: opacity .4s, visibility .4s; transition: opacity .4s, visibility .4s; top: 0; left: 0; background: rgba(0, 0, 0, .05); display: none }
// .ctrlq.fb-close{z-index: 4; padding: 0 6px; background: #365899; font-weight: 700; font-size: 11px; color: #fff; margin: 8px; border-radius: 3px }
// .ctrlq.fb-close::after{content: "X"; font-family: sans-serif; }
// .bubble{width: 20px; height: 20px; background: #c00; color: #fff; position: absolute; z-index: 999999999; text-align: center; vertical-align: middle; top: -2px; left: -5px; border-radius: 50%;}
// .bubble-msg{width: 120px; left: -140px; top: 5px; position: relative; display: none; background: rgba(59, 89, 152, .8); color: #fff; padding: 5px 8px; border-radius: 8px; text-align: center; font-size: 13px; }


.buy-course-fixed {position: fixed; bottom: 150px; right: 24px; color: #fff; transition: all .2s ease-in-out ; z-index: 500;
  .circle-btn{width: 60px; height: 60px; border-radius: 50%; background: #f06; text-align: center; padding-top: 15px; cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .09), 0 4px 40px rgba(0, 0, 0, .24) ;
    i{font-size: 32px;}
  }
  .hover-info{float: left; position: absolute; margin-left: -130px; background: #f06; margin-top: -50px; padding: 10px 15px; border-radius: 6px; font-weight: bold; display: none;}
  &:hover{
    .hover-info{display: block;}
    .circle-btn{}
    transform: scale(1.1);
  }
}

.thi-thu-fixed{position: fixed; bottom: 229px; right: 24px; color: #fff; transition: all .2s ease-in-out ; z-index: 500;
  .circle-btn{width: 60px; height: 60px; border-radius: 50%; background: #cc0033; text-align: center; padding-top: 8px; cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .09), 0 4px 40px rgba(0, 0, 0, .24) ; font-size: 10px;
    i{font-size: 34px;}
  }
  .hover-info{float: left; position: absolute; margin-left: -130px; background: #c00; margin-top: -50px; padding: 10px 15px; border-radius: 6px; font-weight: bold; display: none;}
  &:hover{
    .hover-info{display: block;}
    .circle-btn{}
    transform: scale(1.1);
  }
}

.call-hotline-fixed {
  position: fixed;
  width: 60px;
  height: 60px;
  bottom: 35px;
  left: 25px;
  z-index: 999;
  .call-hotline-img {
    width: 60px;
    height: 60px;
  }

  .ringing_phone,
  .active_phone {
    display: inline-block;
    color: #fff;
    border-radius: 50%;
    background: #05B0F7;
    padding: 7px;
    width: 60px;
    height: 60px;
    text-align: center;
    position: relative;
  }

  .ringing_phone i,
  .active_phone i {
    line-height: 50px;
    font-size: 2.5em;
  }

  .ringing_phone i {
    animation: shake 2s infinite cubic-bezier(.36, .07, .19, .97) both;
  }

  .active_phone:after {
    position: absolute;
    content: '';
    top: 50%;
    left: 50%;
    background: rgba(5, 176, 247, 0.1);
    transform: translateX(-50%) translateY(-50%);
    border-radius: 50%;
    padding: 0.5em;
    animation: activeCall 2s ease-in-out infinite both;
  }

  .active_phone:before {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(5, 176, 247, 0.1);
    border-radius: 50%;
    animation: activeCall2 4s linear infinite both;
  }

  .ringing_phone:after,
  .ringing_phone:before {
    position: absolute;
    content: '';
    opacity: 0;
    border-right: 2px solid #ffffff;
    width: 15px;
    height: 15px;
    left: 40%;
    top: 28%;
    border-radius: 50%;
    transform: rotate(-40deg);
    animation: fadeInOne 2s infinite both;
  }

  .ringing_phone:before {
    width: 20px;
    height: 20px;
    left: 40%;
    top: 20%;
    animation: fadeInTwo 2s infinite both;
  }

  @keyframes activeCall {
    20% {
      padding: 2em;
    }
    25% {
      padding: 1em;
    }
    35% {
      padding: 3em;
    }
    50% {
      padding: 2em;
    }
    60% {
      padding: 3.1em;
    }
    80% {
      padding: 1.4em;
    }
    100% {
      padding: 1em;
    }
  }

  @keyframes activeCall2 {
    0% {
      padding: 0em;
      background-color: rgba(76, 175, 80, 0);
    }
    25% {
      padding: 1em;
      background-color: rgba(5, 176, 247, 0.5);
      transform: translateX(-1em) translateY(-1em);
    }
    26%,
    100% {
      padding: 0;
      opacity: 0;
    }
  }

  @keyframes shake {
    5%,
    45% {
      transform: rotate3d(0, 0, 1, -7deg);
    }
    10%,
    40% {
      transform: rotate3d(0, 0, 1, 7deg);
    }
    15%,
    25%,
    35% {
      transform: rotate3d(0, 0, 1, -7deg);
    }
    20%,
    30% {
      transform: rotate3d(0, 0, 1, 7deg);
    }
    51% {
      transform: rotate3d(0, 0, 0, 0deg);
    }
    100% {
      transform: rotate3d(0, 0, 0, 0deg);
    }
  }

  @keyframes fadeInOne {
    45% {
      opacity: 0
    }
    100% {
      opacity: 1
    }
  }

  @keyframes fadeInTwo {
    55% {
      opacity: 0
    }
    100% {
      opacity: 1
    }
  }
}
.home {
  &-wrapper {
    width: 100vw;
    overflow: hidden;
  }
  &-header {
    @include aspect-ratio(1920, 811);
    background-image: url("../img/new_home/header-bg.webp");
    background-position: center;
    background-size: cover;
    position: relative;
    background-attachment: fixed;
    background-repeat: no-repeat;
    &__inner {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }
    &__slider {
      &-wrapper {
        position: absolute;
        bottom: 8vw;
        right: calc(-12vw);
        width: 70vw;
      }
      &-nav {
        position: absolute;
        bottom: calc(12vw + 170px);
        right: calc(30vw - 200px);
        font-family: Montserrat, sans-serif;
        font-size: 25px;
        font-weight: 800;
        #prv_sld,
        #nxt_sld {
          rect {
            fill: $gray-75;
            transition: 0.2s ease-in-out;
          }
          &:hover {
            rect {
              fill: $green-primary;
            }
          }
        }
      }
    }
    &__slide {
      &-inner {
        display: flex;
        flex-flow: column;
        height: 12vw;
        border-radius: 10px;
        margin: 10px;
        overflow: hidden;
      }
      &-image {
        height: 100%;
        align-self: center;
        border: 2px solid $gray-100;
        border-radius: 10px;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      }
    }
    &__slogan {
      position: absolute;
      text-transform: uppercase;
      font-family: Quicksand, Arial, sans-serif;
      font-size: 20px;
      font-weight: 700;
      color: $gray-100;
      width: 100%;
      text-align: center;
      bottom: 5vw;
    }
  }
  &-products {
    padding: 140px 2vw;
    background-image: url("../img/new_home/x-o-bg.svg");
    background-repeat: repeat;
    background-position: center;
  }
  &-product {
    margin-top: 60px;
    .home-ribbon {
      animation: ribbon-shaking 1.5s ease-in-out infinite;
    }
    &:hover {

    }
    &__button {
      font-family: Montserrat, Arial, sans-serif;
      font-weight: 700;
      font-size: 16px;
      padding: 16px 35px;
      border-radius: 40px;
      border: 1px solid $gray-100;
      background-color: $white;
      text-decoration: none;
      color: $gray-100;
      display: inline-flex;
      margin: 30px auto;
      box-shadow: 0 5px 0 0 rgba(0,0,0,0.2);
      -webkit-box-shadow: 0 5px 0 0 rgba(0,0,0,0.2);
      -moz-box-shadow: 0 5px 0 0 rgba(0,0,0,0.2);
      transition: 0.15s ease-in-out;
      &:hover {
        color: $white;
        background-color: $green-primary;
      }
    }
    &__info {

    }
    &-link {
      font-family: Quicksand, Arial, sans-serif;
      font-weight: 600;
      padding: 4px 9px;
      margin: auto 20px;
      border-radius: 10px;
      text-decoration: none;
      position: relative;
      transition: 0.3s ease-in-out;
      border: 2px solid $green-dark;
      &.free {
        &:before {
          content: 'Miễn phí';
          position: absolute;
          color: $white;
          background-color: $orange-100;
          padding: 1px 2px;
          border-radius: 2px;
          font-size: 10px;
          top: -12px;
          left: -3px;
        }
      }
      &--green {
        border-color: $green-dark;
        color: $green-dark;
        &:hover,
        &:active,
        &:focus {
          background-color: $green-dark;
        }
      }
      &--orange {
        border-color: $orange-100;
        color: $orange-100;
        &:hover,
        &:active,
        &:focus {
          background-color: $orange-100;
        }
      }
      &--blue {
        border-color: $blue-50;
        color: $blue-50;
        &:hover,
        &:active,
        &:focus {
          background-color: $blue-50;
        }
      }
      &--violet {
        border-color: $violet-25;
        color: $violet-25;
        &:hover,
        &:active,
        &:focus {
          background-color: $violet-25;
        }
      }
      &:hover,
      &:active,
      &:focus {
        text-decoration: none;
        color: $white;
      }
      &.disabled {
        border-color: $gray-25;
        color: $gray-50;
      }
    }
  }
  &-philosophy {
    background-image: url("../img/new_home/12-2021/philosophy-bg.webp");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;

    padding: 80px 0;
    position: relative;
    &__icon {
      position: absolute;
      bottom: 0;
    }
    &__content {
      align-items: center;
      margin-top: 20px;
    }
    &__text {
      width: 55%;
      font-family: Quicksand, Arial, sans-serif;
      font-size: 0;
      font-weight: 100;
      text-align: center;
      line-height: 1.6;
      font-style: italic;
      user-select: none;
      .text-ghost {
        opacity: 0;
        font-size: 0;
      }
    }
    &__image {
      width: 45%;
      display: flex;
      align-items: center;
    }
  }
  &-divider {
    width: 800px;
    margin: 60px auto;
    min-height: 2px;
    background-image: url("../img/new_home/12-2021/divider.svg");
    background-size: cover;
    background-position: center;
  }
  &-ribbon {
    display: flex;
    position: relative;
    transform: rotate(0);
    transform-origin: bottom left;
    svg {
      z-index: 2;
    }
  }
  &-title {
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    &__content {
      display: flex;
      flex-flow: column;
      align-items: center;
    }
    &__name {
      padding: 2px 50px;
      font-family: 'Mulish', sans-serif;
      font-weight: 700;
      font-size: 25px;
      text-align: center;
      background: $green-secondary;
      border: 2px solid $gray-100;
      border-bottom: none;
    }
    &__desc {
      font-family: Montserrat, sans-serif;
      font-weight: 700;
      font-size: 35px;
      line-height: 1.61;
      color: $gray-100;
      border-top: 2px solid $gray-100;
      text-transform: uppercase;
      padding: 10px 15px;
    }
    &__icon {
      margin: auto 20px;
    }
  }
  &-tabs {
    margin-top: 40px;
  }
  &-tab {
    border-right: 1px solid $gray-100;
    padding-right: 15px;
    &:not(:first-child) {
      padding-left: 15px;
    }
    &:last-child {
      border-right-width: 0;
    }
    &.active {
      .home-tab__inner {
        background-color: $green-primary;
        cursor: default;
        &:hover {
          background-color: $green-primary;
        }
      }
    }
    &__inner {
      text-align: center;
      font-family: Montserrat, Arial, sans-serif;
      font-weight: 700;
      font-size: 16px;
      padding: 6px 20px;
      border-radius: 10px;
      cursor: pointer;
      transition: 0.2s ease-in-out;
      text-decoration: none;
      color: $gray-100;
      text-transform: uppercase;
      &--green {
        &:hover,
        &:active,
        &:focus {
          background-color: $green-primary;
        }
      }
      &--orange {
        &:hover,
        &:active,
        &:focus {
          background-color: $orange-75;
        }
      }
      &--blue {
        &:hover,
        &:active,
        &:focus {
          background-color: $violet-25;
        }
      }
      &--violet {
        &:hover,
        &:active,
        &:focus {
          background-color: $violet-25;
        }
      }
      &:hover,
      &:active,
      &:focus {
        text-decoration: none;
        color: $gray-100;
      }
    }
  }
  &-teacher {
    width: 200px;
    border: 2px solid $gray-100;
    border-radius: 20px;
    padding: 20px 15px;
    position: relative;
    font-size: 10px;
    font-family: Quicksand, Arial, sans-serif;
    text-align: center;
    &-name {
      width: 100%;
      position: absolute;
      &#thanh {
        top: -62px;
        left: 60px;
        transform: rotate(15deg);
        transform-origin: bottom left;
        animation: ribbon-shaking-2 3s ease-in-out infinite;
      }
      &#dung {
        top: -62px;
        left: 40px;
        transform: rotate(0deg);
        transform-origin: bottom left;
        animation: ribbon-shaking 1.5s ease-in-out infinite;
      }
      &#nghia {
        bottom: 17px;
        left: 9px;
        transform: scale(-1, 1);
        transform-origin: bottom left;
        animation: ribbon-shaking-3 4s ease-in-out infinite;
        strong {
          transform: scale(-1, 1);
        }
      }
    }
    .home-ribbon {
      .bookmark-ribbon {
        height: 32px;
        font-size: 13px;
        top: 0;
        &::before {
          border-width: 16px;
        }
      }
    }
    &__arrow-left {
      width: 0;
      height: 0;
      border-top: 11px solid transparent;
      border-bottom: 11px solid transparent;
      border-right: 15px solid black;
      position: absolute;
      top: calc(50% - 10px);
      left: -15px;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-right: 13px solid $white;
        position: absolute;
        top: -10px;
        left: 2px;
      }
    }
    &__arrow-right {
      width: 0;
      height: 0;
      border-top: 11px solid transparent;
      border-bottom: 11px solid transparent;
      border-left: 15px solid black;
      position: absolute;
      top: calc(50% - 10px);
      right: -15px;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-left: 14px solid $white;
        position: absolute;
        top: -10px;
        right: 1px;
      }
    }
    &__arrow--orange {
      &:after {
        border-right-color: $orange-75;
      }
    }
    &__arrow--green {
      &:after {
        border-right-color: $green-25;
      }
    }
    &__arrow--gray {
      &:after {
        border-right-color: $gray-25;
      }
    }
    &__arrow--blue {
      &:after {
        border-right-color: $blue-25;
      }
    }
    &__icon {
      position: absolute;
      bottom: -30px;
      &--left {
        left: -50px;
      }
      &--right {
        right: -40px;
      }
    }
    &__inside {
      border: 2px solid $gray-100;
      border-radius: 15px;
      height: 160px;
      background-color: $white;
      position: relative;
      img {
        max-width: unset;
        position: absolute;
      }
    }
  }
  &-preview {
    width: 100%;
    height: 400px;
    //border: 1px solid $gray-100;
    border-radius: 20px;
    padding: 20px;
    //box-shadow: 0px 0px 0 0 rgba(0,0,0,0.2);
    //-webkit-box-shadow: 0px 0px 0 0 rgba(0,0,0,0.2);
    //-moz-box-shadow: 0px 0px 0 0 rgba(0,0,0,0.2);
    //background-color: #E8FFD4;
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(232, 255, 212, 0.5);
    position: relative;
    transition: all 0.3s ease-in-out;
    &:hover {
      background-color: rgba(232, 255, 212, 1);
      //transform: translate3d(10px, -10px, -10px);
      //box-shadow: 10px 10px 0 0 rgba(0,0,0,0.2);
      //-webkit-box-shadow: 10px 10px 0 0 rgba(0,0,0,0.2);
      //-moz-box-shadow: 10px 10px 0 0 rgba(0,0,0,0.2);
    }
    &__arrow-left {
      width: 0;
      height: 0;
      border-top: 11px solid transparent;
      border-bottom: 11px solid transparent;
      border-right: 15px solid transparent;
      position: absolute;
      top: calc(50% - 10px);
      left: -15px;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-right: 14px solid rgba(232, 255, 212, 0.75);
        position: absolute;
        top: -10px;
        left: 1px;
      }
    }
    &__arrow-right {
      width: 0;
      height: 0;
      border-top: 11px solid transparent;
      border-bottom: 11px solid transparent;
      border-left: 15px solid transparent;
      position: absolute;
      top: calc(50% - 10px);
      right: -15px;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-left: 14px solid rgba(232, 255, 212, 0.75);
        position: absolute;
        top: -10px;
        right: 1px;
      }
    }
    &__icon {
      position: absolute;
      bottom: -30px;
      &--left {
        left: -50px;
      }
      &--right {
        right: -40px;
      }
    }
    &__inside {
      box-shadow: 1px 1px 10px rgba(0,0,0,0.2);
      border-radius: 15px;
      overflow: hidden;
      img {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }
  &-analysis {
    //width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 50px auto;
    &__item {
      width: 242px;
      position: relative;
      display: flex;
      flex-flow: column;
      align-items: center;
      &:last-child {
        margin-right: 0;
      }
      &-main {
        border-radius: 10px;
        border: 3px solid $gray-100;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 242px;
        height: 132px;
        font-size: 55px;
        font-family: Quicksand, Arial, sans-serif;
        font-weight: bold;
        z-index: 2;
        box-shadow: 0 4px 3px rgba(0,0,0,0.2);
      }
      &-icon {
        position: absolute;
        height: 84px;
        text-align: center;
        width: 100%;
        top: -42px;
        z-index: 3;
        img {
          max-height: 100%;
        }
      }
      &-color {
        height: 30px;
        width: 80%;
        border: 2px solid $gray-100;
        border-top: none;
        border-radius: 0 0 6px 6px;
        z-index: 1;
      }
      &-stat {
        margin-top: 10px;
        width: 80%;
        font-family: Quicksand, Arial, sans-serif;
        font-weight: 700;
        font-size: 18px;
        text-align: center;
      }
    }
  }
  &-news {
    display: flex;
    &__column {
      width: 50%;
      display: flex;
      flex-flow: column;
    }
    &__item {
      border-radius: 20px;
      border: 3px solid $gray-100;
      padding: 30px;
      background-color: $white;
      flex: 1;
      box-shadow: 10px 10px 0px 0px rgba(230,230,230,1);
      -webkit-box-shadow: 10px 10px 0px 0px rgba(230,230,230,1);
      -moz-box-shadow: 10px 10px 0px 0px rgba(230,230,230,1);
      position: relative;
      &-thumbnail {
        display: flex;
        flex-flow: column;
      }
      &-source {
        min-width: 24px;
        max-width: 120px;
      }
      &-corner {
        position: absolute;
        bottom: 0;
        right: 0;
      }
      &-lg {
        display: flex;
        flex-flow: column;
      }
      &-md {
        display: flex;
        flex-flow: column;
        padding: 20px;
      }
      &-sm {
        display: flex;
        align-items: center;
        .home-news__item-thumbnail {
          max-width: 180px;
          margin-right: 30px;
        }
      }
      &-desc {
        font-family: Quicksand, Arial, sans-serif;
        font-weight: 600;
      }
    }
  }
  &-blog {
    flex-wrap: wrap;
    display: flex;
    &__column {
      width: 30%;
      margin-right: 3.3%;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  &-feedback {
    margin-top: 100px;
    &__slide {
      display: flex;
      padding: 0 20px;
      &-content {
        position: absolute;
        padding: 8px 20px;
        font-family: Mulish, Arial, sans-serif;
        font-weight: 700;
        font-size: 14px;
        border-radius: 40px;
        bottom: -25px;
        left: 21%;
        color: $white;
        opacity: 1;
        transition: 0.3s ease-in-out;
        svg {
          position: absolute;
          bottom: -10px;
        }
      }
      &-inner {
        border: 3px solid $gray-50;
        border-radius: 10px;
        background: $gray-10;
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
      }
      &-image {
        border-radius: 10px;
        //overflow: hidden;
        position: relative;
        transition: 0.2s ease-in-out;
        transform-origin: top center;
        &:hover {
          transform: scale(1.5) translateY(-20px);
          .home-feedback__slide-content {
            opacity: 0;
          }
        }
        img {
          border-radius: 10px;
        }
        //width: 80%;
      }
      &-info {
        margin-top: 35px;
        .fb_name {
          font-family: Montserrat, Arial, sans-serif;
          font-size: 25px;
          font-weight: 800;
        }
        .fb_job {
          font-family: Quicksand, Arial, sans-serif;
          font-size: 16px;
          font-weight: 400;
        }
      }
    }
  }
  &-partner {
    margin-top: 150px;
    &__list {
      max-width: 1000px;
      margin: auto;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      justify-items: center;
    }
    &__item {
      margin-top: 20px;
      transition: 0.2s ease-in-out;
      &:hover {
        transform: translateY(-10px);
      }
    }
  }
  &-youtube {
    display: flex;
    align-items: center;
    font-family: Quicksand, Arial, sans-serif;
    font-size: 16px;
    margin-bottom: 150px;
    &__title {
      font-family: Montserrat, sans-serif;
      font-size: 35px;
      font-weight: 800;
    }
    &__button {
      font-family: Montserrat, Arial, sans-serif;
      font-weight: 700;
      font-size: 16px;
      padding: 16px 35px;
      border-radius: 40px;
      border: 1px solid $gray-100;
      text-decoration: none;
      color: $white;
      display: inline-flex;
      margin: 30px auto;
      box-shadow: 0 5px 0 0 rgba(0,0,0,0.2);
      -webkit-box-shadow: 0 5px 0 0 rgba(0,0,0,0.2);
      -moz-box-shadow: 0 5px 0 0 rgba(0,0,0,0.2);
      transition: 0.15s ease-in-out;
      background: #EC342C;
      &:hover {
        color: $white;
        background-color: $green-primary;
      }
    }
  }
  &-link {
    text-decoration: none;
    color: $gray-100;
    transition: 0.15s ease-in-out;
    &:hover {
      text-decoration: none;
      color: $green-dark;
    }
  }
}
.honor-box {
  height: 75px;
  max-height: 75px;
  position: fixed;
  padding: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FF9A0F;
  .honer-image {
    height: 75px;
  }
  .slick-box {
    width: 724px;
  }
  .honer-item {
    height: 48px;
    width: 550px;
    margin-top: 12px;
    border-radius: 57px;
    border: 2px solid #FFF;
    background: #FF9A0F;
    box-shadow: 0px 2px 4.5px 1px #B84A24;
    .honer-text {
      padding-left: 12px;
      padding-right: 56px;
      .honer-text-after {
        margin-left: 12px;
        margin-top: 2px;
      }
    }
    .image-border {
      width: 63px;
      min-width: 63px;
      height: 63px;
      top: -10px;
      left: -10px;
    }
  }
}
.slider {
  //height: 560px;
  margin: auto;
  overflow:hidden;
  position: relative;

  &::after {
    right: 0;
    top: 0;
    -webkit-transform: rotateZ(180deg);
    transform: rotateZ(180deg);
  }

  &::before {
    left: 0;
    top: 0;
  }

  .slide-track {
    -webkit-animation: scroll 80s linear infinite;
    animation: scroll 80s linear infinite;

    &:hover {
      -webkit-animation-play-state: paused;
      animation-play-state: paused;
    }
    display: flex;
    height: 100%;
  }

  .slide {
    height: 100%;
  }
}
@-webkit-keyframes scroll {
  0% { -webkit-transform: translateX(0); transform: translateX(0); }
  100% { -webkit-transform: translateX(-500vw); transform: translateX(-500vw); }
}
@keyframes scroll {
  0% { -webkit-transform: translateX(0); transform: translateX(0); }
  100% { -webkit-transform: translateX(-500vw); transform: translateX(-500vw); }
}

@media (max-width: 1024px) {
  @-webkit-keyframes scroll {
    0% { -webkit-transform: translateX(0); transform: translateX(0); }
    100% { -webkit-transform: translateX(-500vw); transform: translateX(-500vw); }
  }
  @keyframes scroll {
    0% { -webkit-transform: translateX(0); transform: translateX(0); }
    100% { -webkit-transform: translateX(-500vw); transform: translateX(-500vw); }
  }
}

@media (max-width: 767px) {
  @-webkit-keyframes scroll {
    0% { -webkit-transform: translateX(0); transform: translateX(0); }
    100% { -webkit-transform: translateX(-1000vw); transform: translateX(-1000vw); }
  }
  @keyframes scroll {
    0% { -webkit-transform: translateX(0); transform: translateX(0); }
    100% { -webkit-transform: translateX(-1000vw); transform: translateX(-1000vw); }
  }
}

@keyframes ribbon-shaking {
  0% { transform: rotate(0deg) }
  50% { transform: rotate(3deg) }
  100% { transform: rotate(0deg) }
}
@keyframes ribbon-shaking-2 {
  0% { transform: rotate(15deg) }
  50% { transform: rotate(0deg) }
  100% { transform: rotate(15deg) }
}
@keyframes ribbon-shaking-3 {
  0% { transform: scale(-1, 1) rotate(-5deg) }
  50% { transform: scale(-1, 1) rotate(15deg) }
  100% { transform: scale(-1, 1) rotate(-5deg) }
}
