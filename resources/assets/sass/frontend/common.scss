@import "variables";

[v-cloak] {
  display: none;
}

.slick-slide {
  outline: none !important;
}

.justify-content {
  &-center {
    justify-content: center;
  }
  &-space-between {
    justify-content: space-between;
  }
  &-flex-end {
    justify-content: flex-end;
  }
  &-flex-start {
    justify-content: flex-start;
  }
}
.align-items {
  &-center {
    align-items: center;
  }
  &-baseline {
    align-items: baseline;
  }
  &-flex-end {
    align-items: flex-end;
  }
  &-flex-start {
    align-items: flex-start;
  }
  &-stretch {
    align-items: stretch;
  }
}
.a-cursor {
  &-pointer {
    cursor: pointer;
  }
}
.text {
  &-bold {
    font-weight: bold !important;
  }
  &-red {
    color: #F3010A;
  }
}

.v-modal-mask {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, .5);
  display: table;
  transition: opacity .3s ease;
}

.v-modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.v-modal-container {
  width: 80vmax;
  max-height: calc(100vh - 150px);
  overflow-y: scroll;
  margin: 0px auto;
  padding: 20px 30px;
  background-color: #fff;
  border-radius: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, .33);
  transition: all .1s ease;
  font-family: Helvetica, Arial, sans-serif;
  &::-webkit-scrollbar {
    display: none;
  }
}

.v-modal-header h3 {
  margin-top: 10px;
  color: #07403F;
  font-family: 'Beanbag_Dungmori_Rounded', arial;
  font-size: 20px;
}

.v-modal-body {
  margin: 0;
}


.v-modal-default-button {
  float: right;
}

/*
 * The following styles are auto-applied to elements with
 * transition="v-modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the v-modal transition by editing
 * these styles.
 */

.v-modal-enter {
  opacity: 0;
}

.v-modal-leave-active {
  opacity: 0;
}

.v-modal-enter .v-modal-container,
.v-modal-leave-active .v-modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}
.bookmark-ribbon {
  height: 56px;
  transform: rotate(-3deg);
  font-family: Montserrat, Arial, sans-serif;
  font-size: 30px;
  font-weight: bold;
  padding: 15px 40px 15px 30px;
  position: absolute;
  top: -3px;
  left: 8px;
  z-index: 0;
  border: 1px solid transparent;
  border-right: none;
  display: flex;
  align-items: center;
  &__content {
    position: static;
  }
  &:before {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    border-right: 15px solid transparent;
  }
  &--green {
    &:before {
      border-bottom: 27px solid $green-25;
      border-top: 27px solid $green-25;
    }
  }
  &--yellow {
    &:before {
      border-bottom: 27px solid $yellow-25;
      border-top: 27px solid $yellow-25;
    }
  }
  &--blue {
    &:before {
      border-bottom: 27px solid $blue-25;
      border-top: 27px solid $blue-25;
    }
  }
  &--violet {
    &:before {
      border-bottom: 27px solid $violet-25;
      border-top: 27px solid $violet-25;
    }
  }
  &--gray {
    &:before {
      border-bottom: 27px solid $gray-10;
      border-top: 27px solid $gray-10;
    }
  }
}

// FONTS
.font {
  // Font families
  &-montserrat {
    font-family: Montserrat, Arial, sans-serif;
  }
  &-quicksand {
    font-family: Quicksand, Arial, sans-serif;
  }

  // Font weight
  &-extrabold {
    font-weight: 800;
  }
  &-bold {
    font-weight: 700;
  }
  &-semibold {
    font-weight: 600;
  }
  &-medium {
    font-weight: 500;
  }
  &-normal {
    font-weight: 400;
  }
  &-light {
    font-weight: 300;
  }
  &-extralight {
    font-weight: 200;
  }
  &-thin {
    font-weight: 100;
  }
}


// Background color
.bg {
  &-orange {
    &-25 {
      background-color: $orange-25;
      svg {
        path {
          fill: $orange-25;
        }
      }
    }
    &-75 {
      background-color: $orange-75;
      svg {
        path {
          fill: $orange-75;
        }
      }
    }
  }
  &-green {
    &-75 {
      background-color: $green-primary;
      svg {
        path {
          fill: $green-primary;
        }
      }
    }
    &-25 {
      background-color: $green-25;
      svg {
        path {
          fill: $green-25;
        }
      }
    }
  }
  &-gray {
    &-25 {
      background-color: $gray-25;
      svg {
        path {
          fill: $gray-25;
        }
      }
    }
    &-75 {
      background-color: $gray-75;
      svg {
        path {
          fill: $gray-75;
        }
      }
    }
  }
  &-blue {
    &-50 {
      background-color: $blue-50;
      svg {
        path {
          fill: $blue-50;
        }
      }
    }
    &-25 {
      background-color: $blue-25;
      svg {
        path {
          fill: $blue-25;
        }
      }
    }
  }
  &-violet {
    &-25 {
      background-color: $violet-25;
      svg {
        path {
          fill: $violet-25;
        }
      }
    }
  }
}
.fold-paper {
  max-width: 282px;
  font-size: 16px;
  color: black;
  font-family: Quicksand, Arial, sans-serif;
  font-weight: 400;
  position: relative;
  padding: 66px 40px;
  margin-left: 44px;
  background-image: url('../img/new_course/fold.png');
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  &__title {
    position: absolute;
    width: 88%;
    top: -30px;
    left: 14px;
    padding: 10px 30px;
    border: 3px solid black;
    font-weight: 700;
    font-family: Montserrat, sans-serif;
    background-color: #B1E18B;
  }
}
.collapse {
  visibility: visible;
}
