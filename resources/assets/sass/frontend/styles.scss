@import 'variables';
@import 'mixins';
@font-face {
  font-family: NotoSerifJPRegular;
  src: url('../fonts/NotoSerifJP-Regular.woff');
  font-display: swap;
}
html {
  text-size-adjust: 100%;
}

@media (max-width: 768px) {
  #chat_modal .modal-dialog {
    transform: none;
    top: 20%; /* Đặt modal cao hơn một chút trên màn hình nhỏ */
    left: auto;
    margin: 20px; /* Căn giữa theo chiều ngang */
  }
}
@media (min-width: 769px) {
  #chat_modal .modal-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0
  }
}

body {
  margin: 0;
  font-size: 14px;
  font-family: arial, sans-serif;
  font-weight: 400;
  color: #404040;
  background-color: #fff;
  overflow-x: hidden;
}
a {
  color: $primaryColor;
}
a:hover,
a:focus {
  outline: none;
  text-decoration: none;
  color: #69aa00;
}

#modalSelectLevelJapanese {
  .modal-content {
    border-radius: 32px !important;
  }

  .btn-n5 {
    background-image: linear-gradient(45deg, #98FF9C, #CEFFD8);
  }

  .btn-n4 {
    background-image: linear-gradient(45deg, #FEC09F, #FFE3DA);
  }
}

#modalEnterCouponCode {
  .modal-content {
    border-radius: 32px !important;
  }
  .modal-body {
    padding: 24px;
  }
}

#modalPromotion {
  .modal-body, .modal-content {
    border-radius: 32px;
  }

  @media (min-width: 769px) {
    .modal-dialog {
      min-width: 1097px !important;
    }

    .img-main {
      height: 190px;
    }
  }
}

@media (min-width: 769px) {
  #modalEnterCouponCode .modal-dialog {
    width: 568px !important;
    height: 396px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0
  }

  #modalSelectLevelJapanese {
    .modal-content {
      border-radius: 32px !important;
    }
    .modal-dialog {
      width: 800px !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      margin: 0
    }

    .modal-body {
      padding: 64px;
    }

    .btn-n5 {
      background-image: linear-gradient(45deg, #98FF9C, #CEFFD8);
    }

    .btn-n4 {
      background-image: linear-gradient(45deg, #FEC09F, #FFE3DA);
    }
  }
}

// style footer promotion
.promotion-item {
  justify-content: center;

  .text-reduce, .value-reduce, .time-stop {
    color: #B3B3B3;
  }

  @media (min-width: 678px) {
    .group-promotion-time {
      margin-left: 12px;
    }
  }
}

.expired.promotion-item {
  .text-reduce, .value-reduce {
    text-decoration: line-through;
    color: #B3B3B3;
  }
}

@media (max-width: 678px) {
  .text-reduce {
    font-size: 10px;
    line-height: 1;
  }

  .expired.promotion-item,  {
    .text-reduce {
      font-size: 10px;
      line-height: 1;
    }

    .value-reduce {
      font-size: 28px;
      line-height: 1;
    }
  }
}

.promotion-active.promotion-item {
  cursor: pointer;
  background-image: url("/images/lessons/svg/lesson-promotion-footer-active.svg");
  @media (min-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  @media (max-width: 768px) {
    .text-reduce {
      font-size: 28px;
      line-height: 1;
    }

    .group-promotion-reduce {
      align-items: center;
    }
  }
}

.promotion-active.promotion-item {
  box-shadow: 0 0 13.48px rgba(255, 95, 0, 1);

  .text-reduce, .value-reduce {
    color: white;
  }
  .group-promotion-time>.time-active {
    display: block;
  }
}

.promotion-active.promotion-item .group-promotion-time .time-stop {
  display: none;
}

.promotion-item .group-promotion-time .time-active {
  display: none;
}

.content-question {
  font-size: 20px;
  p {
    font-size: 20px;
  };
  ruby rt {
    font-size: 10px;
  };

  img {
    max-width: 80%;
    height: auto !important;
    margin: 0 auto;
  };
}

//.dialog-user-result-lesson {
//  overflow: unset !important;
//  max-width: 1240px;
//  margin: 0 auto !important;
//}

.time-stop {
  display: flex;
}

.name-lesson p {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

$primary: #11998e;
$secondary: #38ef7d;
$white: #fff;
$gray: #9b9b9b;

.form__group {
  position: relative;
}

.form__field {
  width: 100%;
  border: 0;
  border-bottom: 1px solid $gray;
  outline: 0;
  font-size: 1.3rem;
  padding: 10px 0;
  background: transparent;
  transition: border-color 0.2s;

  //&::placeholder {
  //  color: transparent;
  //}

  &:placeholder-shown ~ .form__label {
    font-size: 1.3rem;
    cursor: text;
    top: 10px;
  }
}

.form__label {
  position: absolute;
  top: -7px;
  display: block;
  transition: 0.2s;
  font-size: 1rem;
  color: $gray;
}

.form__field:focus {
  ~ .form__label {
    position: absolute;
    top: -7px;
    display: block;
    transition: 0.2s;
    font-size: 1rem;
  }
  padding-bottom: 10px;
  border-width: 3px;
  border-bottom: 1px solid #757575;
}
/* reset input */
.form__field{
  &:required,&:invalid { box-shadow:none; }
}

img {
  max-width: 100%;
}
ul,
ol {
  margin: 0;
  padding: 0;
}
ul li,
ol li {
  list-style: none;
}
p {
  margin: 0;
}

body.fancybox-active {
  overflow-y: auto;
}
#application {
  padding-top: 76px;
}

.error-image {
  width: 100%;
  float: left;
  padding: 50px 100px 100px 100px;
  text-align: center;
  img {
    width: 500px;
  }
  h2 {
    margin: 30px 0 20px 0;
  }
  h5 {
    font-size: 18px;
    line-height: 1.3;
  }
}

.download-store {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9999;
  padding: 20% 15px;
  text-align: center;
  display: none;
  h2 {
    width: 100%;
    float: left;
  }
  p {
    width: 100%;
    float: left;
    font-size: 15px;
    color: #999;
  }
  .mobile-app {
    width: 100%;
    float: left;
    margin-top: 30px;
    img {
      margin-right: 20px;
      width: 260px;
      margin-bottom: 10px;
    }
  }
}

.refresh-cache {
  position: fixed;
  z-index: 999;
  cursor: pointer;
  right: -3px;
  top: 200px;
  background: #fff;
  border-radius: 4px;
  padding: 2px 10px;
  box-shadow: 0 3px 5px 0 rgba(52, 52, 52, 0.1);
}

.mobile-header {
  display: none;
}
.site-header {
  z-index: 100;
  position: fixed;
  width: 100%;
  top: 0;
  .header-top {
    background-color: #fff;
    position: relative;
    z-index: 10;
    .nav-left {
      width: 1024px;
      margin: 0 auto;
      li {
        margin-right: 20px;
        display: inline-block;
        vertical-align: top;
        a {
          padding: 10px 0 8px 0;
          float: left;
          font-size: 16px;
        }
        .active {
          border-bottom: 1px solid $primaryColor;
        }
      }
      .shop-icon {
        float: right;
        margin-right: 110px;
        padding-top: 8px;
        img {
          width: 16px;
        }
      }
      .account-container {
        float: right;
        padding: 0;
        height: 33px;
        .text-login {
          float: right;
          margin-right: 20px;
          margin-top: 10px;
          cursor: pointer;
          outline: none;
          font-size: 16px;
          &:hover {
            color: #111;
          }
        }
        .text-register {
          cursor: pointer;
          margin-top: 10px;
          outline: none;
          color: $primaryColor;
          font-size: 16px;
          &:hover {
            color: #111;
          }
        }

        .profile-no-login-icon {
          display: none;
        }

        .auth-container {
          cursor: pointer;
          padding: 10px 0;
          max-width: 250px;
          text-align: right;
          .messenger-icon {
            display: inline;
            float: left;
            margin-left: -90px;
            padding: 0 10px;
            img {
              width: 19px;
              height: 19px;
              vertical-align: top;
              opacity: 0.8;
              margin-top: 1px;
            }
            .mess-counts {
              background-color: #fa3e3e;
              border-radius: 4px;
              text-align: center;
              font-size: 11px;
              padding: 0 3px 0 3px;
              color: #fff;
              float: right;
              position: absolute;
              margin: -4px 0 0 -6px;
            }
          }

          .svgIcon {
            width: 26px;
            height: 26px;
            float: left;
            margin-top: -2px;
            margin-left: -45px;
            svg {
              opacity: 0.7;
            }
            .noti-counts {
              background-color: #fa3e3e;
              border-radius: 4px;
              text-align: center;
              font-size: 11px;
              padding: 0px 3px 0px 3px;
              color: #fff;
              float: left;
              position: absolute;
              margin: -2px 0 0 15px;
              opacity: 1;
            }
          }

          .user-avatar-circle {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid #eee;
            background: #ccc;
          }
          .user-name {
            margin-right: 5px;
            padding-top: 8px;
          }
          .caret {
            opacity: 0.7;
          }
          .user-menu {
            margin-left: 0;
            float: right;
            right: 0;
            top: 37px;
            width: 190px;
            left: unset;
            .caret-up {
              position: absolute;
              float: right;
              margin-top: -13px;
              right: 20px;
            }
            li {
              width: 100%;
              float: left;
              padding: 0;
              a {
                padding: 7px 15px;
                i {
                  margin-right: 10px;
                }
              }
            }
            .dropdown-divider {
              height: 0;
              margin: 0.5rem 0;
              overflow: hidden;
              border-top: 1px solid #e9ecef;
            }
          }
        }
      }
    }
  }
  .header-content {
    .logo {
      float: left;
      padding: 12px 0;
      height: 70px;
      overflow: hidden;
      width: 120px;
      &:hover {
        opacity: 0.8;
      }
      img {
        height: 37px;
        margin-right: 20px;
        margin-top: 5px;
      }
    }
    .block-nav-menu {
      width: calc(100% - 120px);
      .ui-menu {
        width: 100%;
        float: left;
        li.nav-item-mn {
          height: 70px;
          font-size: 18px;
          color: #111;
          .hover-box {
            float: left;
            width: 200px;
            background: #fff;
            margin-top: 70px;
            position: fixed;
            padding: 15px 0;
            display: none;
            box-shadow: 0px 0px 11.6854px rgba(0, 0, 0, 0.15);
            border-radius: 11px;
            border-top-left-radius: 0;
            .mn-item {
              width: 100%;
              float: left;
              padding: 8px 15px;
              img {
                margin-right: 10px;
              }
              font-size: 15px;
              font-family: Roboto;
              &:hover {
                font-weight: bold;
              }
            }
          }
          .group-name {
            float: left;
            height: 70px;
            line-height: 70px;
            cursor: pointer;
            padding: 0 15px;
            i {
              font-size: 13px;
              opacity: 0.6;
            }
          }
          &:hover {
            background: #efefef;
            .hover-box {
              display: block;
              background: #fff;
            }
          }

          .free {
            margin: 0 0 0 0;
            height: 15px;
            line-height: 15px;
            padding: 0 5px;
            border-radius: 3px;
            font-size: 10px;
            position: absolute;
            background-color: #f22b2b;
            color: #fff;
          }
          // a {padding: 8px 19px; height: 70px; font-size: 18px; color: #111;
          //     &:hover{cursor: pointer;}
          // }
          // .active{border-bottom: 2px solid $primaryColor;}
        }
      }
    }
  }
}

.chat-box {
  width: 390px;
  height: 455px;
  background: #fff;
  z-index: 88888;
  position: fixed;
  bottom: 0;
  right: 25px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.3);
  display: none;
  .conversation-list {
    width: 390px;
  }
  .top-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .chat-top {
    width: 100%;
    height: 45px;
    border-bottom: 1px solid #ddd;
    font-size: 16px;
    .admin-item {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 5px 0px 5px 10px;
      font-weight: bold;
      .back-icon {
        cursor: pointer;
        color: green;
        padding: 8px 8px 8px 2px;
      }
      .admin-img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 1px solid #ddd;
      }
      .admin-active {
        width: 11px;
        height: 11px;
        background: green;
        position: absolute;
        margin: 20px 0 0 -12px;
        border-radius: 50%;
        border: 2px solid #fff;
      }
      .admin-name {
        flex: 1;
        padding-left: 3px;
      }
      .group-chat-manager {
        padding: 5px 8px;
        color: #96D962;
      }
      .group-name-box {
        padding-left: 3px;
        flex: 1;
      }
      .group-name {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .edit-group-name {
        border-radius: 5px;
        border: solid 1px #ddd;
        font-weight: normal;
        font-size: 14px;
        padding: 2px 5px;
      }
    }
    .close-icon {
      padding: 3px 12px 3px 5px;
      cursor: pointer;
      margin-top: 5px;
      svg {
        width: 18px;
        height: 18px;
      }
    }
    .chat-title-box {
      padding-left: 12px;
    }
  }

  .group-chat-setting {
    width: 100%;
    height: 361px;
    font-size: 15px;
    position: absolute;
    z-index: 150;
    background: #fff;
    top: 45px;
    .setting-item {
      padding: 8px 12px;
      i {
        color: #96d962;
        width: 20px;
      }
    }
  }

  .search-con {
    background-color: #f3f3f3;
    border: 0;
    width: calc(100% - 20px);
    margin: 6px 10px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 12px;
    padding: 8px 15px;
  }

  .clear-search {
    position: absolute;
    right: 18px;
    font-size: 18px;
    top: 59px;
  }

  .con-container {
    height: 356px;
    padding: 0px 10px;
    .con-item {
      margin: 2px 0;
      padding: 7px;
      border-radius: 10px;
      cursor: pointer;
      border: solid 1px #fff;
      &:hover {
        border: solid 1px #ddd;
      }
      .user-avatar {
        width: 48px;
        min-width: 48px;
        height: 48px;
        min-height: 48px;
        border-radius: 24px;
        border: 1px solid #ddd;
      }
      .con-info {
        padding-left: 8px;
        max-width: 290px;
        width: 100%;
        position: relative;
        justify-content: center;
        .user-name {
          flex: 1;
          font-family: 'Montserrat';
          font-weight: 600;
          font-size: 14px;
          line-height: 150%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          line-clamp: 3;
          -webkit-box-orient: vertical;
        }
        .last-msg {
          font-family: 'Quicksand';
          font-weight: 700;
          font-size: 13px;
          line-height: 150%;
          color: #7f807f;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          line-clamp: 3;
          -webkit-box-orient: vertical;
        }
        .time-last-msg {
          font-family: 'Quicksand';
          font-weight: 700;
          font-size: 12px;
          line-height: 150%;
          color: #b3b3b3;
          i {
            color: #96d962;
            margin-left: 4px;
          }
        }
      }
    }
    .con-item-border {
      border: solid 1px #96D962;
    }
  }

  .show-people {
    .modal-dialog {
      width: 400px;
    }
    .modal-header {
      background-color: #96d962;
      padding-left: 35px;
      .modal-title {
        flex: 1;
        color: #fff;
        font-weight: bold;
      }
      button {
        font-size: 25px;
      }
    }
    .modal-body {
      max-height: calc(100vh - 107px);
      min-height: 60vh;
      overflow-y: scroll;
      text-align: left;
      .member-item {
        padding: 7px 0;
        border-top: solid 1px #ddd;
        .member-avatar {
          width: 40px;
          height: 40px;
          border-radius: 20px;
          border: solid 1px #ddd;
        }
        .member-name {
          font-family: 'Quicksand';
          margin-left: 10px;
        }
      }
      .search-msg-result-txt {
        margin: 10px 0;
      }
      .search-msg-time {
        font-family: 'Quicksand';
        font-size: 12px;
        color: #b3b3b3;
        margin-left: 10px;
      }
      .search-msg-content {
        font-family: 'Quicksand';
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
      }
    }
  }

  #show_people {
    .modal-body::-webkit-scrollbar {
      display: none;
    }
    .modal-body {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
    }
  }

  #search_msg {
    .modal-dialog {
      width: 500px;
    }
  }

  .notSeenTextBox {
    position: absolute;
    top: 32px;
    right: 30px;
    color: rgb(0, 132, 255);
    i {
      font-size: 16px;
      margin-right: 7px;
    }
  }

  .tagTextBox {
    top: 52px;
  }

  @keyframes color_change {
    from { background-color: #e4e6eb; }
    to { background-color: #bdbec0; }
  }

  .chat-content {
    position: relative;
    border-left: solid 1px #ddd;
    flex: 1
  }

  .pin-container::-webkit-scrollbar {
    display: none;
  }
  .pin-container {
    position: absolute;
    top: 45px;
    left: 0;
    right: 15px;
    overflow-y: scroll;
    max-height: 355px;
    background-color: #eee;
    z-index: 100;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
    font-size: 15px;
    .pin-item {
      padding: 5px 20px 5px 8px;
      border-top: solid 1px #ddd;
    }
    .pin-first {
      padding: 5px 20px 5px 8px;
    }
    .pin-icon {
      padding: 3px 4px;
      color: red;
    }
    .pin-item-content {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      margin-left: 4px;
    }
    .display-content {
      display: contents;
    }
    .show-full-icon {
      position: absolute;
      right: 2px;
      top: 0;
      font-size: 18px;
      padding: 3px 6px;
    }
    .pin-image {
      padding-left: 4px;
      padding-right: 8px;
      img {
        max-height: 26px;
        margin-left: 5px;
      }
    }
    .pin-file {
      a {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }

  .chat-container {
    width: 389px;
    height: 362px;
    background: #fff;
    border-bottom: 1px solid #ddd;
    overflow-y: scroll;
    padding-bottom: 15px;
    .load-more {
      width: 90%;
      margin: 5px auto 0 auto;
      padding: 4px 0;
      text-align: center;
      font-size: 14px;
      color: rgb(0, 132, 255);
      cursor: pointer;
    }
    .item-container {
      width: 100%;
      float: left;
      .root-msg {
        margin-top: 8px;
        .text-root-msg {
          padding: 5px 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical;
          border-radius: 16px;
          font-size: 15px;
          max-width: 60%;
          white-space: pre-line;
          overflow-wrap: break-word;
          line-height: 1.4;
          margin-left: 40px;
          margin-right: 10px;
          margin-bottom: -14px;
          background-color: #989898;
          color: #fff;
        }
        .img-root-msg {
          border-radius: 16px;
          max-width: 60%;
          margin-left: 40px;
          margin-right: 19px;
          margin-bottom: -14px;
          background-color: #989898;
          img {
            max-width: 80px;
            max-height: 80px;
            margin: 0 2px;
          }
        }
      }
      .root-msg-left {
        justify-content: flex-start;
      }
      .root-msg-right {
        justify-content: flex-end;
      }
      .seen-user-box {
        display: flex;
        padding: 0 10px;
        margin-top: 3px;
        .seen-avatar {
          width: 15px;
          height: 15px;
          border-radius: 50%;
        }
      }
      .seen-user-box-left {
        justify-content: flex-end;
      }
    }
    .chat-item {
      width: 100%;
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
      position: relative;
      .menu-option {
        position: relative;
      }
      ul {
        top: 30px;
        right: -69px;
        left: auto;
        li {
          padding: 3px 10px;
          cursor: pointer;
        }
      }
      .content {
        padding: 8px 12px 8px 12px;
        background: rgb(228, 230, 235);
        color: #222;
        border-radius: 16px;
        font-size: 13px;
        max-width: 60%;
        font-size: 15px;
        position: relative;
        span {
          float: left;
          width: 100%;
        }
        white-space: pre-line;
        overflow-wrap: break-word;
        line-height: 1.2;
        .like-box {
          position: absolute;
          bottom: -8px;
          font-size: 12px;
          background: #fff;
          padding: 2px;
          border-radius: 22px;
          color: gray;
          span {
            margin-right: 3px;
          }
          .like-icon {
            padding: 1px;
            cursor: pointer;
          }
          .like-icon-red {
            color: #fa3e3e;
          }
          .like-number {
            cursor: pointer;
            &:hover + .like-tooltip {
              display: block !important;
            }
          }
        }
      }
      .ct-img {
        padding: 0;
        background: transparent;
        text-align: left;
        img {
          border-radius: 8px;
          max-width: 80px;
          max-height: 80px;
          margin: 0 2px;
          cursor: pointer;
        }
      }
      .ct-file {
        padding-top: 7px;
        margin-left: 5px;
      }
      .mess-time {
        display: table-cell;
        font-size: 10px;
        padding: 9px 0;
        opacity: 0;
      }
      .reply-icon {
        padding: 10px 5px;
        opacity: 0;
        font-size: 12px;
      }
      .avatar {
        position: relative;
        .admin-active {
          width: 11px;
          height: 11px;
          background: green;
          position: absolute;
          border-radius: 50%;
          border: 2px solid #fff;
          right: -2px;
          bottom: -2px;
        }
      }
      &:hover {
        .mess-time {
          opacity: 0.9;
        }
        .reply-icon {
          opacity: 1;
        }
        .option {
          opacity: 1;
        }
      }
      .option {
        opacity: 0;
        padding: 5px 8px;
        margin-top: 5px;
        cursor: pointer;
      }
    }
    .admin-chat {
      display: flex;
      justify-content: flex-start;
      .avatar-flex {
        width: 40px !important;
        display: table-cell;
        padding: 0 5px 0 8px;
        .avatar {
          width: 30px;
          height: 30px;
          img {
            vertical-align: unset;
          }
        }
      }
      .ad-avatar {
        border-radius: 50%;
      }
      .content {
        max-width: 65%;
      }
      .content-ani {
        animation: color_change 0.5s forwards;
      }
    }
    .user-chat {
      ul {
        left: -100px;
        right: auto;
        top: 30px;
      }
      .content {
        float: right;
        background: rgb(0, 132, 255);
        color: #fff;
        margin-right: 10px;
        .msg-link {
          color: #fff;
          text-decoration: underline;
        }
      }
      .ct-img {
        padding: 0;
        background: transparent;
        text-align: right;
        img {
          border-radius: 8px;
          max-width: 80px;
          max-height: 80px;
          margin: 0 2px;
          cursor: pointer;
        }
      }
      .ct-file {
        padding-top: 7px;
      }
    }
  }
  .chat-bottom {
    width: 100%;
    float: left;
    font-size: 15px;
    .preview-image {
      height: 50px;
      margin: -50px 0 0 0;
      position: fixed;
      z-index: 999999;
      padding: 5px 5px;
      display: none;
      opacity: 0.95;
      background-color: rgba(225, 232, 242, 0.85);
      border-top-right-radius: 12px;
      img {
        border-radius: 8px;
        height: 40px;
        width: 40px;
        object-fit: cover;
        float: left;
        margin-right: 5px;
      }
      .send-btn {
        font-size: 22px;
        margin: 8px 0 0 5px;
        cursor: pointer;
        color: rgb(0, 160, 0);
        float: left;
        &:hover {
          color: rgb(0, 132, 255);
        }
      }
      .close-btn {
        height: 18px;
        float: left;
        margin: -5px 0 0 -9px;
        background: #bbb;
        border-radius: 50%;
        color: #555;
        padding: 1px 3px;
        cursor: pointer;
        &:hover {
          background: #f06;
          color: #fff;
        }
      }
    }
    .reply-info-box {
      position: fixed;
      margin-top: -41px;
      width: 375px;
      padding: 5px 24px 5px 8px;
      font-size: 12px;
      background-color: rgba(221, 221, 221, 0.75);
      .root-cm-content {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-height: 14px;
      }
      i {
        font-size: 18px;
        padding: 5px;
        position: absolute;
        right: 0;
        top: 3px;
      }
    }
    .member-tag-container {
      position: absolute;
      z-index: 110;
      width: 375px;
      background-color: #f0f0f0;
      padding: 4px;
      border-top: 1px solid #ddd;
      .member-tag-item {
        font-family: 'Quicksand';
        font-size: 13px;
        padding: 3px 4px;
        border-radius: 4px;
        .tag-avatar {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 1px solid #c8c8c8;
        }
      }
    }
    .form-chat {
      width: 100%;
      float: left;
      position: relative;
      .like-icon {
        position: absolute;
        bottom: 10px;
        margin-left: -28px;
        font-size: 18px;
        cursor: pointer;
      }
      div > a {
        top: 11px !important;
        margin-left: -28px !important;
      }
      .mess {
        width: 294px !important;
        max-height: 150px;
        overflow-y: scroll;
        -webkit-user-select: text;
        -moz-user-select: text;
        user-select: text;
        white-space: pre-wrap;
        word-break: break-word;
        outline: none;
        float: left;
        outline: none;
        border: none;
        padding-top: 12px;
        margin: 0 38px 0 58px;
        -ms-overflow-style: none;
        scrollbar-width: none;
        span {
          color: dodgerblue;
        }
      }
      .mess-incognito {
        width: 322px !important;
        margin-left: 30px;
      }
      .mess::-webkit-scrollbar {
        display: none;
      }
      #mess-input[placeholder]:empty:before {
        content: attr(placeholder);
        color: #a9a9a9;
      }
      #send_image_form {
        width: 25px;
        height: 35px;
        float: left;
        margin: -20px 0 0 8px;
        .fa-camera {
          opacity: 0.5;
          font-size: 14px;
        }
      }
      #send_file_form {
        width: 25px;
        height: 35px;
        float: left;
        margin: -20px 0 0 0px;
        .fa-paperclip {
          opacity: 0.5;
          font-size: 16px;
        }
      }
      #input_image_mess, #input_file_mess {
        position: absolute;
        width: 18px;
        height: 35px;
        box-sizing: border-box;
        opacity: 0;
        padding: 0 0px 0px 0;
        overflow: hidden;
        margin: -20px 0 0 0;
      }
    }
  }
}

.tooltip-i {
  position: relative;
  display: inline-block;
}

.tooltip-i .tooltiptext {
  visibility: hidden;
  width: 170px;
  background-color: rgba(51, 51, 51, 0.9);;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 100%;
  left: 50%;
  margin-left: -90px;
  white-space: normal;
  font-size: 14px;
  font-weight: normal;
}

.tooltip-i:hover .tooltiptext {
  visibility: visible;
}

.full-container {
  width: 100%;
  float: left;
  min-width: 100px;
  .center-container {
    width: 1024px;
    margin: 0 auto;
    .comment-container {
      padding-bottom: 40px;
      width: 100%;
      float: left;
      .comment-heading {
        width: 100%;
        float: left;
        border-bottom: 1px solid $primaryColor;
        margin-top: 20px;
        span {
          font-size: 25px;
          background: $primaryColor;
          color: #fff;
          padding: 4px 15px;
        }
      }
      .fb-comments {
        margin-left: -5px;
      }
    }
  }
}

.verify-area {
  padding: 100px 100px 100px 100px;
  img {
    width: 30%;
  }
}

.block-teacher {
  padding-bottom: 30px;
}

.auth-featured-image {
  width: 350px;
  height: 600px;
  object-fit: cover;
}

.label {
  cursor: pointer;
}

.green-text {
  color: $primaryColor;
}
.success-text {
  color: #18af5f;
}
.danger-text {
  color: #ff1e52;
}
.empty-info {
  opacity: 0.7;
}
.notifications-counts {
  background-color: #fa3e3e;
  border-radius: 4px;
  text-align: center;
  font-size: 11px;
  padding: 1px 3px 0 3px;
  color: #fff;
  float: right;
  position: absolute;
  margin: -5px 0 0 -22px;
}

.dmr-btn {
  padding: 6px 20px;
  background-color: $primaryColor;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    opacity: 0.9;
    background: #5cbfaa;
    color: #fff;
  }
}
.change-btn {
  border-radius: 2px;
  cursor: pointer;
  display: inline-block;
  font-size: 12px;
  -webkit-font-smoothing: antialiased;
  font-weight: bold;
  line-height: 18px;
  color: #fff;
  padding: 2px 6px;
  text-align: center;
  text-decoration: none;
  text-shadow: none;
  vertical-align: top;
  white-space: nowrap;
  background-color: #71846d;
  border: 1px solid #71846d;
}
.cancel-btn {
  border-radius: 2px;
  cursor: pointer;
  display: inline-block;
  font-size: 12px;
  -webkit-font-smoothing: antialiased;
  font-weight: bold;
  line-height: 18px;
  color: #333;
  padding: 2px 6px;
  text-align: center;
  text-decoration: none;
  text-shadow: none;
  vertical-align: top;
  white-space: nowrap;
  background-color: #f6f7f9;
  border: 1px solid #ced0d4;
}

.list-comments {
  width: 100%;
  float: left;
  padding: 0 0 15px 0;
  .input-comment-container {
    width: 100%;
    margin-bottom: 35px;
    font-size: 16px;
    p {
      margin-bottom: 10px;
    }
    .me-avatar {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      float: left;
    }
    .input-comment {
      width: calc(100% - 200px) !important;
      float: left;
      margin: 0 0 0 15px;
      padding: 18px 60px 18px 20px;
      box-sizing: border-box;
      outline: none;
      border: 1px solid #ddd;
      border-radius: 15px;
      &:focus {
        outline: none;
        border-color: #aaa;
      }
    }
    .pick-image {
      padding: 7px;
      float: left;
      margin-left: -34px;
      position: relative;
      cursor: pointer;
      i {
        font-size: 18px;
        opacity: 0.8;
        display: block;
      }
      .form-pick-image {
        width: 20px;
        height: 25px;
        overflow: hidden;
        float: left;
        margin: -20px 0 0 -2px;
        position: relative;
        opacity: 0;
      }
    }
    .preview-image {
      width: 100%;
      float: left;
      padding: 0 0 0 50px;
      img {
        max-width: 200px;
        height: auto;
        border-radius: 6px;
        margin-top: 10px;
      }
    }
    .post-comment-btn {
      background-color: $primaryColor;
      padding: 10px 20px;
      color: #fff;
      line-height: 1;
      cursor: pointer;
      border-radius: 10px;
      border: 2px dashed $primaryColor;
      box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
      transition: 0.4s;
      margin-left: 13px;
      margin-top: 5px;
      display: inline-block;
    }

    .post-comment-btn span:last-child {
      display: none;
    }

    .post-comment-btn:hover {
      transition: 0.4s;
      border: 2px dashed $primaryColor;
      background-color: #fff;
      color: $primaryColor;
    }

    .post-comment-btn:active {
      background-color: $primaryColor;
    }
  }

  .comment-item {
    width: 100%;
    list-style: none;
    border-top: 1px solid #eee;
    padding: 10px 0;
    display: flex;
    align-items: baseline;
    &:first-child {
      border-top: none;
    }
    .preview-image {
      width: 100%;
      float: left;
      padding: 5px 0 0 0;
      img {
        max-width: 260px;
        height: auto;
        border-radius: 4px;
      }
    }
    p {
      margin-bottom: 10px;
    }
    .avatar-container {
      width: 50px;
      height: 35px;
      float: left;
      img {
        width: 35px;
        height: 35px;
        border-radius: 50%;
      }
      .zmdi-check-circle {
        float: right;
        margin: 22px 0 0 -10px;
        position: absolute;
        color: #578fff;
        background: #fff;
        border-radius: 50%;
      }
    }
    .comment-content {
      width: calc(100% - 90px);
      margin: 5px 0;
      .name {
        margin-bottom: 0;
        width: calc(100% - 60px);
        font-size: 16px;
        line-height: 1.2;
        margin-bottom: 5px;
        b {
          color: #96d962;
          font-weight: 700;
          cursor: pointer;
        }
        .time {
          opacity: 0.6;
          font-size: 12px;
          font-weight: 300;
          font-family: Roboto, sans-serif;
          color: black !important;
        }
        .comment-content {
          font-size: 13px;
          font-weight: 400;
        }
      }
      .comment-action {
        font-size: 13px;
        margin-bottom: 0;
        .me-tiny-avatar {
          width: 15px;
          height: 15px;
          border-radius: 50%;
        }
        .answer {
          cursor: pointer;
        }
      }
      .reply-container {
        width: 100%;
        float: left;

        .load-more-reply {
          cursor: pointer;
          color: #96d962;
          font-size: 13px;
          i {
            margin-right: 4px;
          }
        }
        .admin-edit-pen {
          cursor: pointer;
        }
        .child-comment-item {
          width: 100%;
          display: flex;
          border-top: 1px solid #eee;
          padding: 8px 0;
          &:first-child {
            margin-top: 5px;
          }
          &:last-child {
            padding-bottom: 0;
          }
          .avatar-container {
            width: 50px;
            height: 35px;
            img {
              width: 35px;
              height: 35px;
              border-radius: 50%;
            }
            .zmdi-check-circle {
              float: right;
              margin: 22px 0 0 -10px;
              position: absolute;
              color: #578fff;
              background: #fff;
              border-radius: 50%;
            }
          }
          .comment-content {
            width: calc(100% - 90px);
            .child-name {
              margin-bottom: 0;
              width: calc(100% - 60px);
              font-size: 15px;
              line-height: 1.2;
              margin-bottom: 1px;
              color: #777;
              b {
                color: #96d962;
                cursor: pointer;
              }
              .time {
                opacity: 0.6;
                font-size: 12px;
                font-weight: 300;
                font-family: Roboto, sans-serif;
                color: black;
              }
              .comment-content {
                font-size: 13px;
                font-weight: 400;
              }
            }
            .child-comment-action {
              font-size: 13px;
              margin-bottom: 0;
              .answer {
                cursor: pointer;
              }
              .time {
                opacity: 0.6;
              }
            }
          }
        }

        .reply-form {
          width: 110%;
          float: left;
          border-top: 1px solid #eee;
          padding-top: 10px;
          margin-top: 5px;
          .me-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            float: left;
          }
          .input-comment {
            width: calc(100% - 200px) !important;
            float: left;
            margin: 0 0 0 15px;
            padding: 18px 60px 18px 20px;
            box-sizing: border-box;
            outline: none;
            border: 1px solid #ddd;
            border-radius: 15px;
            font-size: 16px;
            &:focus {
              outline: none;
              border-color: #aaa;
            }
          }
          .pick-image {
            padding: 8px;
            float: left;
            margin-left: -65px;
            position: relative;
            cursor: pointer;
            i {
              font-size: 18px;
              opacity: 0.8;
              display: block;
            }
            .form-pick-image {
              width: 20px;
              height: 25px;
              overflow: hidden;
              float: left;
              margin: -20px 0 0 -2px;
              position: relative;
              opacity: 0;
              cursor: pointer;
            }
          }
          .preview-image {
            width: 100%;
            float: left;
            padding: 0 0 0 50px;
            img {
              max-width: 200px;
              height: auto;
              border-radius: 6px;
              margin-top: 10px;
            }
          }
          .post-comment-btn {
            background-color: $primaryColor;
            padding: 10px 20px;
            color: #fff;
            line-height: 1;
            cursor: pointer;
            border-radius: 10px;
            border: 2px dashed $primaryColor;
            box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
            transition: .4s;
            margin-left: 13px;
            margin-top: 5px;
            display: inline-block;
          }

          .post-comment-btn span:last-child {
            display: none;
          }

          .post-comment-btn:hover {
            transition: .4s;
            border: 2px dashed $primaryColor;
            background-color: #fff;
            color: $primaryColor;
          }

          .post-comment-btn:active {
            background-color: $primaryColor;
          }
          // .preview-image{width: 100%; float: left; padding: 5px 0 0 50px;
          //   img{max-width: 200px; height: auto;  border-radius: 4px;}
          // }
        }
      }
    }
    .delete-comment {
      opacity: 0.5;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      i {
        opacity: 0;
        margin-right: 5px;
      }
      &:hover {
        opacity: 0.9;
        i {
          opacity: 0.9;
        }
      }
    }
  }
  .load-more-comment {
    padding: 20px 30px;
    text-align: center;
    background: #f7fff0;
    color: #96d962;
    border-radius: 2px;
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    text-align: center;
    &:hover {
      opacity: 0.9;
      cursor: pointer;
    }
    .loading-icon {
      width: 20px;
    }
  }
  .end-of-list {
    width: 100%;
    float: left;
    text-align: center;
    color: #444;
    padding: 5px 0;
    margin-top: 15px;
    border-radius: 2px;
    border: 1px dashed #ddd;
  }
}

.user-profile-popup {
  width: 400px;
  min-height: 600px;
  padding: 25px 15px;
  .user-profile-container {
    float: left;
    width: 100%;
    .cover-container {
      width: 400px;
      height: 150px;
      text-align: center;
      background-color: #eee;
      margin: -25px -15px 100px -15px;
      .user-avatar {
        width: 150px;
        height: 150px;
        border-radius: 4px;
        margin: 50px auto;
        border: 1px solid #eee;
      }
    }
  }
}

#edit-cmt-popup {
  display: none;
  width: 100%;
  max-width: 600px;
  .edit-comment-area {
    width: 100%;
    float: left;
    margin-bottom: 15px;
    height: 150px;
  }
  .edit-comment-btn-save {
    padding: 6px 20px;
    background-color: $primaryColor;
    color: #fff;
    border-radius: 2px;
    cursor: pointer;
    float: right;
    &:hover {
      opacity: 0.9;
      background: #5cbfaa;
      color: #fff;
    }
  }
  .edit-comment-btn-cancel {
    background-color: #eee;
    color: #777;
    padding: 6px 20px;
    border-radius: 2px;
    cursor: pointer;
    float: right;
    margin-right: 10px;
  }
}

.home-featured-popup {
  width: 880px;
  min-height: 470px;
  padding: 25px 15px;
}
.fancybox-close-small:after{background-color: #FFF; border-radius: 50%;}

.comment-tab {
  width: 100%;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  li {
    box-sizing: border-box;
    margin-left: 0 !important;
    font-size: 18px;
    a {
      color: black !important;
      background-color: transparent !important;
      font-family: Quicksand, Arial, sans-serif;
      border-bottom: 10px solid transparent;
      color: black;
      padding: 6px 15px 3px 15px;
      font-size: 20px;
      border-radius: 0;
    }
  }
  .active > a {
    font-weight: 800;
    border-bottom: 10px solid #96d962;
    color: #376a00 !important;
    &:hover {
      opacity: 0.9;
    }
  }
}

.pagination .active > span {
  background: $primaryColor;
  border-color: $primaryColor;
}

.loading-circle,
.loading-circle:after {
  border-radius: 50%;
  width: 30px;
  height: 30px;
}
.loading-circle {
  font-size: 10px;
  position: fixed;
  left: calc(50% - 10px);
  top: 200px;
  text-indent: -9999em;
  border-top: 3px solid rgba(255, 255, 255, 0.7);
  border-right: 3px solid rgba(255, 255, 255, 0.7);
  border-bottom: 3px solid rgba(255, 255, 255, 0.7);
  border-left: 3px solid #7a9cda;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 0.4s infinite linear;
  animation: load8 0.4s infinite linear;
}
@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

$fixedSize: 60px;
.fixed-panel {
  min-height: $fixedSize;
  position: fixed;
  right: 40px;
  bottom: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  &-icon {

  }
  &-board {
    position: absolute;
    bottom: -300px;
    right: -25px;
    padding: 20px;
    min-width: 300px;
    display: flex;
    color: black;
    align-items: flex-start;
    background: #f7fff0;
    border: 1px solid #25be62;
    border-radius: 10px;
    user-select: none;
    & > i {
      position: absolute;
      top: 5px;
      right: 5px;
      cursor: pointer;
    }
  }
  .go-top {
    cursor: pointer;
    width: $fixedSize;
    height: 24px;
    text-align: center;
    padding: 5px 15px;
    border-radius: 6px;
    color: #fff;
    background: #aaa;
    opacity: 0.8;
    margin-top: 10px;
    text-align: center;
    span {
      float: left;
      margin-top: -10px;
    }
  }
  .muahang-btn{cursor: pointer; width: $fixedSize; text-align: center; padding: 10px 0; color: #000;
    padding-bottom: 20px; text-align: center;
    img{width: 40px;}
    p{float: left; margin-top: 2px; width: 100%; font-family: Quicksand, Arial, sans-serif; font-weight: 500; font-size: 9px;text-align: center;}
  }
  .call-btn {
    cursor: pointer;
    width: $fixedSize;
    text-align: center;
    padding: 10px 6px;
    color: #000;
    img {
      width: 40px;
    }

    p {
      float: left;
      margin-top: 2px;
      width: 100%;
      font-family: Quicksand, Arial, sans-serif;
      font-weight: 500;
      font-size: 9px;
      text-align: center;
    }
  }
  .thithu-btn {
    cursor: pointer;
    width: $fixedSize;
    height: $fixedSize;
    text-align: center;
    padding: 10px 5px;
    color: #000;
    margin-top: 10px;
    text-align: center;
    img {
      width: 40px;
    }
    p {
      float: left;
      margin-top: 2px;
      width: 100%;
      font-family: Quicksand, Arial, sans-serif;
      font-weight: 500;
      font-size: 10px;
      text-align: center;
    }
  }
}
.footer a:hover,
.footer a:focus {
  color: #5cbfaa;
}
.animated-modal {
  max-width: 550px;
  border-radius: 4px;
  overflow: hidden;
  transform: translateY(-50px);
  transition: all 0.7s;
}
.animated-modal h2,
.animated-modal p {
  transform: translateY(-50px);
  opacity: 0;
  transition-property: transform, opacity;
  transition-duration: 0.4s;
} /* Final state */
.fancybox-slide--current .animated-modal,
.fancybox-slide--current .animated-modal h2,
.fancybox-slide--current .animated-modal p {
  transform: translateY(0);
  opacity: 1;
} /* Reveal content with different delays */
.fancybox-slide--current .animated-modal h2 {
  transition-delay: 0.1s;
}
.fancybox-slide--current .animated-modal p {
  transition-delay: 0.3s;
}
.additional-data-popup {
  display: none;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  justify-content: center;
  align-items: center;
  .popup-jp-level {
    border-radius: 5px;
    padding: 28px;
    background-color: white;
    position: relative;
    z-index: 2050;
    .title-box {
      font-size: 19px;
      font-weight: 550;
      padding: 3px 0;
      margin-bottom: 10px;
      color: #588d3f;
    }
    .form-group {
      margin-top: 3px;
      label {
        margin-bottom: 3px;
      }
      .form-control {
        width: 210px;
        margin-top: 5px;
        border-radius: 3px;
      }
    }
    .action-box {
      display: flex;
      justify-content: flex-end;
      .cancel-type-jp-level {
        border-radius: 4px;
        padding: 6px 12px;
      }
      .cancel-type-jp-level:hover {
        background-color: #dcdcdd;
      }
      .done-btn {
        margin-left: 10px;
      }
    }
  }
}
.login-container {
  padding: 0;
  width: 1050px;
  margin-top: calc(50vh - 330px);
  .save-login-info:before {
    width: 14px !important;
    height: 14px !important;
  }
  .agree-policy-info:before {
    width: 14px !important;
    height: 14px !important;
  }
  .agree-policy {
    margin: 0;
  }
  .auth-item {
    .form-control {
      border: 1px solid #57D061;
      border-radius: 7px;
      height: 34px;
    }
  }
  .login-left-container {
    width: 320px;
    height: 600px;
    float: left;
    background: #ccc;
    overflow: hidden;
  }
  .login-right-container {
    width: calc(100% - 320px);
    height: 600px;
    float: left;
    overflow: hidden;
    padding: 10px 15px 20px 15px;
    box-sizing: border-box;
    .nav-pills {
      margin: 0 14px;
      border-bottom: 1px solid #eee;
      height: 47px;
    }
    .nav-pills li.active > a,
    .nav-pills li.active > a:focus {
      color: rgba(96, 106, 116, 0.98);
      background: #fff;
      border-bottom: 2px solid $primaryColor;
      border-radius: 0;
    }
    .nav-pills li > a {
      border-radius: 0;
      height: 47px;
      padding: 15px 0 7px 0;
      margin-right: 25px;
      color: #434a54;
      font-family: 'Open Sans', sans-serif;
      font-size: 16px;
      font-weight: 400;
    }
    .tab-content {
      padding-top: 20px;
      .agree-policy {
        font-size: 12px;
        margin-right: 0;
      }
      .btn-register {
        float: left;
        width: 100%;
        background: $primaryColor;
        color: #fff;
        border-radius: 3px;
        padding: 7px 0;
        text-align: center;
        cursor: pointer;
        margin-top: 15px;
      }
      .btn-login {
        float: left;
        width: 100%;
        background: $primaryColor;
        color: #fff;
        border: none;
        border-radius: 3px;
        padding: 7px 0;
        text-align: center;
        margin: 0 0 25px 0;
        cursor: pointer;
      }
      .form-group {
        margin-bottom: 10px;
      }
      .form-control {
        background: #eee;
        height: 30px;
      }
      #login-content,
      #register-content {
        .error-container {
          width: 100%;
          .alert-danger {
            padding-top: 5px;
            line-height: 1.1;
            background-color: transparent;
          }
        }
      }
      #register-content {
        .form-group {
          margin-bottom: 15px;
        }
      }
      #register-month,
      #register-day,
      #register-year {
        padding: 4px 0 4px 5px;
      }
    }
    .break-line {
      padding-bottom: 10px;
    }
    .btn-googleplus {
      background-color: #dd4b39 !important;
    }
    .btn-facebook {
      background-color: #3b5998 !important;
    }
    .btn-apple {
      border: 1px solid #333;
      background-color: #333 !important;
    }
    .btn-social {
      width: 40px;
      color: #fff;
      padding: 3px 7px;
      font-size: 20px;
    }
  }
}
.fancybox-container {
  height: 100%;
  display: inline-block;
}

.main {
  width: 100%;
  float: left;
  min-height: 500px;
  .main-center {
    width: 1100px;
    margin: 0 auto;
    .main-left {
      width: 734px;
      float: left;
      min-height: 300px;
    }
    .main-right {
      width: 250px;
      float: left;
      min-height: 300px;
      margin-left: 40px;
      margin-top: 20px;
    }
  }
}

select::-ms-expand {
  display: none;
}
.form-control {
  height: 32px;
  border: 1px solid #d7d7d7;
  border-radius: 0;
  box-shadow: none;
}
select.form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-position: center right 14px;
  background-repeat: no-repeat;
  padding-right: 34px;
  background-size: 15px auto;
}
select.form-control option {
  padding-top: 4px;
  padding-bottom: 4px;
}
.radio label,
.checkbox label {
  font-weight: normal;
  display: inline-block;
  padding: 0;
  position: relative;
  margin-right: 20px;
}
.radio input[type='radio'],
.checkbox input[type='radio'] {
  position: absolute;
  opacity: 0;
}
.radio input[type='radio'] + span:before,
.checkbox input[type='radio'] + span:before {
  content: '';
  font-family: 'FontAwesome';
  text-align: center;
  line-height: 18px;
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 100%;
  border: 1px solid #a51817;
  vertical-align: middle;
  margin-right: 5px;
  color: #fff;
}
.radio input[type='radio']:checked + span:before,
.checkbox input[type='radio']:checked + span:before {
  content: '\f00c';
  background-color: #a51817;
}
.radio input[type='checkbox'],
.checkbox input[type='checkbox'] {
  position: absolute;
  opacity: 0;
}
.radio input[type='checkbox'] + span:before,
.checkbox input[type='checkbox'] + span:before {
  content: '';
  font-family: 'FontAwesome';
  text-align: center;
  line-height: 15px;
  width: 16px;
  height: 16px;
  display: inline-block;
  border: 1px solid #ccc;
  background-color: #fff;
  vertical-align: middle;
  margin-right: 5px;
  color: #a51817;
}
.radio input[type='checkbox']:checked + span:before,
.checkbox input[type='checkbox']:checked + span:before {
  content: '\f00c';
}
select::-ms-expand {
  display: none;
}
textarea.form-control {
  height: 100px;
}
.form-horizontal .control-label {
  text-align: left;
  font-weight: normal;
}
.form-horizontal .control-label sup {
  color: #a51817;
}
.form-horizontal .btn {
  height: 40px;
  padding: 0 10px;
  text-transform: uppercase;
  color: #fff;
  background-color: #5cbfaa;
  border-radius: 15px;
  font-size: 16px;
  display: inline-block;
  text-align: center;
}
.form-horizontal .btn:hover,
.form-horizontal .btn:focus {
  background-color: #3a9481;
}
.form-horizontal .btn.btn_block {
  width: 100%;
  display: block;
}
.form-control[disabled],
fieldset[disabled] .form-control {
  background-color: #e1e1e1;
  border-color: #e1e1e1;
}

.owl-carousel .owl-item img {
  width: auto;
  transform-style: flat;
}

@media (max-width: 767px) {
  .site-header .account {
    position: absolute;
    top: -48px;
    right: 15px;
    z-index: 10;
    margin-right: 0;
  }
}
.site-header .account .dropdown-toggle {
  display: block;
  position: relative;
  line-height: 30px;
  white-space: nowrap;
  text-transform: capitalize;
  color: #69aa00;
}
.site-header .account .dropdown-toggle .count {
  width: 12px;
  height: 12px;
  border-radius: 100%;
  background-color: red;
  position: absolute;
  top: 0;
  left: 15px;
  text-align: center;
  font-size: 9px;
  line-height: 12px;
  color: #fff;
}
.site-header .account .dropdown-toggle:before {
  content: '\f007';
  font-family: 'FontAwesome';
  margin-right: 10px;
  font-size: 24px;
}
.site-header .account .dropdown-toggle .caret {
  border-width: 7px 7px 0 7px;
}
.site-header .account .dropdown-menu {
  padding: 0;
  border-radius: 0;
  right: 0;
  left: auto;
  margin: 0;
  box-shadow: 0 -3px 24px 0 rgba(52, 52, 52, 0.7);
  border: none;
  min-width: 100%;
}
.site-header .account .dropdown-menu li {
  display: block;
  line-height: 24px;
  text-align: center;
  padding: 0;
}
.site-header .account .dropdown-menu li a {
  display: block;
  padding: 8px 15px;
  line-height: 24px;
  color: #404040;
}
.site-header .account .dropdown-menu li a:hover {
  background-color: #ebebeb;
}
.site-header .account .dropdown-menu li + li {
  border-top: 1px solid #69aa00;
}
.site-header .account:hover .dropdown-menu {
  display: block;
}

.site-header .block-nav-menu {
  float: left;
}
.site-header .block-nav-menu .block-title {
  display: none;
  position: relative;
  text-align: center;
  text-transform: uppercase;
  line-height: 40px;
  border-bottom: 1px solid #dfdfdf;
}

.site-header .block-nav-menu .ui-menu {
  display: block;
}
.site-header .block-nav-menu .ui-menu:before,
.site-header .block-nav-menu .ui-menu:after {
  content: '';
  display: table;
}
.site-header .block-nav-menu .ui-menu:after {
  clear: both;
}
.site-header .block-nav-menu .ui-menu > li {
  display: block;
  position: relative;
  float: left;
}
.site-header .block-nav-menu .ui-menu > li > a {
  display: block;
  text-transform: none;
  font-size: 15px;
  padding: 0 9px;
  color: $primaryColor;
  font-weight: normal;
  line-height: 60px;
}
.site-header .block-nav-menu .ui-menu > li > a:hover {
  color: #fff;
  background-color: $primaryColor;
}
.site-header .block-nav-menu .ui-menu > li.active > a {
  color: #fff;
  background-color: #69aa00;
}
.site-header .block-nav-menu .ui-menu > li:hover > a {
  color: #fff;
  background-color: #69aa00;
}
.site-header .block-nav-menu .ui-menu > li:hover .submenu {
  display: block;
}
.site-header .block-nav-menu .ui-menu > li .submenu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0;
  min-width: 180px;
  z-index: 2000;
  display: none;
  width: 500px;
  text-align: center;
  border: 1px solid $subColor;
}
.site-header .block-nav-menu .ui-menu > li .submenu .content > li {
  display: block;
  display: -webkit-flex;
  display: flex;
  flex: 1 1 0;
  -webkit-flex-direction: row;
  flex-direction: row;
  -webkit-align-items: stretch;
  align-items: stretch;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  background-color: #fff;
  position: relative;
  z-index: 4;
}
.site-header .block-nav-menu .ui-menu > li .submenu .content > li + li {
  z-index: 1;
}
.site-header .block-nav-menu .ui-menu > li .submenu .col-left {
  width: 33%;
  float: left;
  text-transform: uppercase;
  color: $primaryColor;
  display: -webkit-flex;
  display: flex;
  flex: 1 1 0;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  justify-content: center;
  font-size: 17px;
}

.offline-mn {
  border-top: 1px solid $subColor;
}
.offline-ct {
  border-top: 1px solid $subColor;
}
.site-header .block-nav-menu .ui-menu > li .submenu .col-right {
  width: 77%;
  border-left: 1px solid $subColor;
  border-top: 1px solid $subColor;
  .col-character-table {
    border-bottom: 1px solid #69aa00;
  }
}
.site-header .block-nav-menu .ui-menu > li .submenu .col-right .col-md-12 {
  padding: 0;
}
.site-header .block-nav-menu .ui-menu > li .submenu .col-right .col-md-3 {
  padding: 0;
  width: 25%;
}
.site-header .block-nav-menu .ui-menu > li .submenu .col-right .col-md-3 + .col-md-3 {
  border-left: 1px solid #69aa00;
}
.site-header .block-nav-menu .ui-menu > li .submenu .col-right a {
  font-size: 17px;
  display: block;
  color: #404040;
  padding: 20px 0;
  &:hover {
    background: #69aa00;
    color: #fff;
  }
}
.site-header .block-nav-menu .ui-menu > li .submenu .col-bottom {
  border-top: 1px solid #69aa00;
}
.site-header .block-nav-menu .ui-menu .toggle-submenu span {
  display: none;
}

.site-header .container {
  position: relative;
  width: 1024px;
}
.site-header .nav-toggle-menu {
  display: none;
  cursor: pointer;
  color: #404040;
  text-align: center;
  width: 30px;
  line-height: 28px;
  float: right;
  margin-top: 26px;
  font-size: 20px;
  border: 1px solid #404040;
  border-radius: 3px;
  position: absolute;
  top: 50%;
  margin-top: -15px;
  right: 15px;
}
.site-header .nav-toggle-menu span {
  display: none;
}
.site-header .nav-toggle-menu:before {
  content: '\f0c9';
  font-family: 'FontAwesome';
}
.site-header .nav-toggle-menu:hover {
  background-color: #69aa00;
  border-color: #69aa00;
  color: #fff;
}

.site-header .header-content {
  top: 0;
  width: 100%;
  z-index: 100;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 5px 0px;
  background-color: #fff;
  #search-input-mobile {
    display: none;
  }
  .container {
    width: 1024px;
    padding: 0;
  }
}

body.menu-open {
  overflow: hidden;
}
.sticky-wrapper.is-sticky .mid-header {
  z-index: 999;
}

/*==========================================================

	9. PAGES

==========================================================*/
.page-title {
  min-height: 300px;
  background-color: #5cbfaa;
  color: #fff;
  margin-bottom: 48px;
  text-align: center;
  font-size: 20px;
  padding: 30px 15px;
  display: -webkit-flex;
  display: flex;
  flex: 1 1 0;
  -webkit-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
.page-title h1 {
  font-size: 30px;
}
@media (max-width: 767px) {
  .page-title {
    font-size: 16px;
    min-height: 200px;
    margin-bottom: 20px;
  }
  .page-title h1 {
    font-size: 16px;
  }
}
.heading-opt1 {
  border-bottom: 2px solid #69aa00;
  font-size: 20px;
  text-transform: uppercase;
  margin-top: 26px;
  line-height: 30px;
  padding: 8px 0;
  margin-bottom: 50px;
}
.heading-opt1 a {
  color: #404040;
}
.heading-opt1 a:hover,
.heading-opt1 a:focus {
  color: #69aa00;
}
@media (max-width: 767px) {
  .heading-opt1 {
    font-size: 16px;
  }
}
.text-uppercase {
  text-transform: uppercase;
}
.block-about-us {
  margin-bottom: 60px;
}
.block-about-us iframe {
  width: 100% !important;
}
@media (max-width: 767px) {
  .block-about-us {
    margin-bottom: 20px;
  }
}
.table-top {
  font-size: 13px;
  margin: 0;
}
.table-top tbody tr td,
.table-top tbody tr th {
  padding: 5px 3px;
  vertical-align: middle;
  border: none;
}
.table-top tbody .t-stt {
  font-weight: bold;
}
.table-top tbody .avata {
  display: block;
  width: 22px;
}
.table-top tbody .img-top {
  display: block;
  width: 16px;
  padding-bottom: 5px;
}
.table-top tbody .img-top img {
  display: block;
  width: 100%;
}
.block-tops {
  margin-top: 80px;
  margin-bottom: 20px;
}
.block-tops .block-title {
  line-height: 20px;
  font-size: 16px;
  text-align: center;
  color: #fff;
  background-color: #69aa00;
  text-transform: uppercase;
  padding: 8px 5px;
}
@media (max-width: 767px) {
  .block-tops .block-title {
    font-size: 16px;
  }
}
.block-tops .block-content {
  border: 3px solid #489036;
  border-top: none;
  padding: 10px 10px 10px 6px;
}
@media (max-width: 767px) {
  .block-tops {
    margin-top: 20px;
  }
}
.block-dethi {
  margin-bottom: 40px;
}
.block-dethi .content .item {
  margin-bottom: 30px;
}
.block-dethi .heading {
  display: -webkit-flex;
  display: flex;
  flex: 1 1 0;
  -webkit-flex-direction: row;
  flex-direction: row;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
.block-dethi .heading .title {
  margin-bottom: 15px;
  display: block;
  line-height: 30px;
}
.block-dethi .heading .time {
  margin-bottom: 15px;
  padding-left: 30px;
  background-position: top 5px left;
  background-repeat: no-repeat;
  background-image: url(../images/icon/icon3.png);
  display: block;
  line-height: 30px;
  background-size: auto 20px;
}
.block-dethi .heading .view {
  margin-bottom: 15px;
  padding-left: 40px;
  background-position: top left;
  background-repeat: no-repeat;
  background-image: url(../images/icon/icon2.png);
  display: block;
  line-height: 30px;
  background-size: auto 30px;
}
@media (max-width: 767px) {
  .block-dethi .heading {
    display: block;
  }
}
.block-post-new {
  margin-bottom: 60px;
}
.block-post-new .items {
  margin: 0 -15px;
}
.block-post-new .items:before,
.block-post-new .items:after {
  content: '';
  display: table;
}
.block-post-new .items:after {
  clear: both;
}
@media (max-width: 479px) {
  .block-post-new .items {
    margin: 0 -7px;
  }
}
.block-post-new .item {
  padding: 0 15px;
  float: left;
  width: 33.33333333%;
  margin-bottom: 50px;
}
.block-post-new .item:nth-child(3n + 1) {
  clear: both;
}
@media (max-width: 639px) {
  .block-post-new .item {
    width: 50%;
  }
  .block-post-new .item:nth-child(n) {
    clear: none;
  }
  .block-post-new .item:nth-child(2n + 1) {
    clear: both;
  }
}
@media (max-width: 479px) {
  .block-post-new .item {
    width: 50%;
    padding: 0 7px;
  }
}
@media (max-width: 359px) {
  .block-post-new .item {
    width: 100%;
  }
}
.block-post-relate {
  border-top: 2px solid #e1e1e1;
  padding-top: 40px;
  margin-top: 30px;
}
.block-post-relate .block-title {
  font-size: 30px;
  color: #69aa00;
  text-transform: uppercase;
  margin-bottom: 20px;
}
.block-post-relate .block-title strong {
  font-weight: normal;
}
@media (max-width: 767px) {
  .block-post-relate .block-title {
    font-size: 20px;
    margin-bottom: 10px;
  }
}
.block-post-detail .info > span {
  display: inline-block;
  vertical-align: top;
  margin-bottom: 15px;
  margin-right: 20px;
}
.block-post-detail h3.title {
  display: block;
  text-transform: uppercase;
  margin-bottom: 20px;
  font-size: 20px;
}
@media (max-width: 767px) {
  .block-post-detail h3.title {
    font-size: 18px;
  }
}
.block-post-detail .view {
  padding-left: 30px;
  background-position: top 2px left;
  background-repeat: no-repeat;
  background-image: url(../images/icon/view2.png);
  line-height: 20px;
}
.slide-gv {
  margin: 20px 0 30px;
  padding: 0 30px;
}
.slide-gv .item {
  position: relative;
}
.slide-gv .item img {
  display: block;
  margin: auto;
}
.slide-gv .item .des {
  display: block;
  position: absolute;
  bottom: 15px;
  width: 120px;
  background-repeat: no-repeat;
  background-image: url(../images/icon/icon4.png);
  background-position: center;
  background-size: 90%;
  background-color: transparent;
  font-size: 12px;
  padding: 35px 15px 10px;
  color: #000;
  opacity: 0;
  background-image: none;
  width: auto;
  left: 15px;
  right: 15px;
  border: 2px solid #69aa00;
  background-color: #fff;
  min-height: 60px;
  padding: 15px;
  text-align: center;
}
.slide-gv .item .des:before {
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0 30px 39px;
  border-color: transparent transparent #69aa00 transparent;
  position: absolute;
  bottom: 100%;
  margin-top: -30px;
  right: 20px;
  z-index: 1;
}
.slide-gv .item .des:after {
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0 26px 35px;
  border-color: transparent transparent #fff transparent;
  position: absolute;
  bottom: 100%;
  margin-top: -30px;
  right: 22px;
  z-index: 5;
}
@media (max-width: 479px) {
  .slide-gv .item .des {
    width: 100px;
  }
}
.slide-gv .item:hover .des {
  opacity: 1;
}
.slide-gv .owl-nav .owl-prev,
.slide-gv .owl-nav .owl-next {
  display: block;
  width: 20px;
  height: 40px;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 50%;
  margin-top: -20px;
  background-size: 100% auto;
}
.slide-gv .owl-nav .owl-prev {
  background-image: url(../images/icon/prev.png);
  left: -30px;
}
.slide-gv .owl-nav .owl-next {
  background-image: url(../images/icon/next.png);
  right: -30px;
}
.block-detail-gv {
  padding: 20px 0;
}
.block-detail-gv._bg {
  background-color: #f4f5eb;
}
.block-detail-gv .avata {
  display: block;
  margin-top: 20px;
}
.block-detail-gv .avata .btn {
  text-transform: uppercase;
  color: #a51817;
  box-shadow: none;
  white-space: normal;
}
.block-detail-gv .avata .btn:hover,
.block-detail-gv .avata .btn:focus {
  box-shadow: none;
}
.block-detail-gv .avata img {
  max-width: 300px;
  width: 100%;
}
@media (max-width: 767px) {
  .block-detail-gv .avata img {
    max-width: 200px;
  }
}
@media (max-width: 639px) {
  .block-detail-gv .avata {
    text-align: center;
  }
  .block-detail-gv .avata img {
    max-width: 120px;
  }
}
.block-detail-gv .name {
  display: block;
  font-size: 20px;
  text-transform: uppercase;
  color: #69aa00;
  margin-bottom: 15px;
}
._right.block-detail-gv .name {
  text-align: right;
}
@media (max-width: 639px) {
  ._right.block-detail-gv .name {
    text-align: center;
  }
}
@media (max-width: 639px) {
  .block-detail-gv .name {
    text-align: center;
  }
}
.block-detail-gv .des {
  padding: 30px;
  padding-left: 70px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 4px 13px 0px;
  line-height: 24px;
  background-image: url(../images/icon/que-left.png);
  background-position: top 20px left 20px;
  background-repeat: no-repeat;
  font-style: italic;
  margin-bottom: 15px;
}
.block-detail-gv .des:after {
  content: '';
  width: 39px;
  display: inline-block;
  vertical-align: text-top;
  height: 36px;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url(../images/icon/que-right.png);
  margin-left: 10px;
}
.block-detail-gv .des:first-letter {
  font-size: 40px;
  padding-right: 5px;
  font-family: 'Great Vibes', cursive;
}
.block-detail-gv h3 {
  display: block;
  text-transform: uppercase;
  font-size: 18px;
}
.block-hotro {
  margin-bottom: 40px;
}
.block-hotro legend {
  border-bottom: 1px solid #69aa00;
  text-transform: uppercase;
  color: #a51817;
  font-size: 16px;
  padding-bottom: 10px;
}
.block-hotro .table tr td,
.block-hotro .table tr th {
  border: none;
}
.block-kqkiemtra .list .item {
  margin-bottom: 20px;
  background-color: #fcf8e3;
  padding: 15px;
}
.block-kqkiemtra .list .btn {
  line-height: 40px;
  color: #fff;
  background-color: #5bc0de;
  border-radius: 6px;
  padding: 0 15px;
  display: inline-block;
  border: none;
}
.block-kqkiemtra .list .btn:hover,
.block-kqkiemtra .list .btn:focus {
  background-color: #28a1c5;
}
@media (max-width: 767px) {
  .block-kqkiemtra .nav-link_opt1 {
    display: block;
    text-align: center;
  }
  .block-kqkiemtra .nav-link_opt1 li {
    white-space: normal;
    display: inline-block;
    vertical-align: text-top;
  }
  .block-kqkiemtra .nav-link_opt1 li a {
    white-space: normal;
  }
}
.block-kh-mua .list-item-kh {
  margin: 0 -15px;
}
.block-kh-mua .list-item-kh:before,
.block-kh-mua .list-item-kh:after {
  content: '';
  display: table;
}
.block-kh-mua .list-item-kh:after {
  clear: both;
}
.block-kh-mua .list-item-kh .item-kh {
  padding: 0 15px;
  float: left;
  width: 33.33333333%;
  margin-bottom: 30px;
}
.block-kh-mua .list-item-kh .item-kh .detail {
  padding-right: 75px;
}
.block-kh-mua .list-item-kh .item-kh:nth-child(3n + 1) {
  clear: both;
}
@media (max-width: 767px) {
  .block-kh-mua .list-item-kh .item-kh {
    width: 50%;
  }
  .block-kh-mua .list-item-kh .item-kh:nth-child(n) {
    clear: none;
  }
  .block-kh-mua .list-item-kh .item-kh:nth-child(3n + 1) {
    clear: both;
  }
}
@media (max-width: 479px) {
  .block-kh-mua .list-item-kh .item-kh {
    width: 100%;
  }
}
.block-thanh-toan .xacnhan {
  padding-left: 30px;
}
.block-thanh-toan .nav-checkout {
  /* display: -webkit-flex;
		display: flex;
		flex: 1 1 0;
		-webkit-flex-direction: row;
		flex-direction: row;
		-webkit-align-items: flex-end;
		align-items: flex-end;
		-webkit-justify-content: space-between;
		justify-content: space-between;  */
  position: relative;
  margin-bottom: 30px;
  display: block;
}
.block-thanh-toan .nav-checkout:before,
.block-thanh-toan .nav-checkout:after {
  content: '';
  display: table;
}
.block-thanh-toan .nav-checkout:after {
  clear: both;
}
.block-thanh-toan .nav-checkout li {
  text-align: center;
  width: 33.33333333%;
  float: left;
  position: relative;
}
.block-thanh-toan .nav-checkout li .step {
  width: 50px;
  line-height: 48px;
  display: inline-block;
  text-align: center;
  background-color: #fff;
  border: 1px solid #ebebeb;
  border-radius: 100%;
  height: 50px;
  font-size: 20px;
  position: relative;
  z-index: 5;
}
.block-thanh-toan .nav-checkout li .text {
  display: block;
}
.block-thanh-toan .nav-checkout li.active .step {
  background-color: #00b6f0;
  color: #fff;
}
.block-thanh-toan .nav-checkout li:after {
  content: '';
  display: inline-block;
  height: 12px;
  background-color: #ebebeb;
  vertical-align: middle;
  position: absolute;
  bottom: 18px;
  z-index: 2;
  width: 100%;
  left: 50%;
}
.block-thanh-toan .nav-checkout li._complate:after {
  background-color: #00b6f0;
}
.block-thanh-toan .nav-checkout li:last-child {
  background-color: #fff;
}
.block-thanh-toan .nav-checkout li:last-child:after {
  content: none;
}
@media (max-width: 479px) {
  .block-thanh-toan .nav-checkout {
    display: block;
  }
  .block-thanh-toan .nav-checkout:before,
  .block-thanh-toan .nav-checkout:after {
    content: '';
    display: table;
  }
  .block-thanh-toan .nav-checkout:after {
    clear: both;
  }
  .block-thanh-toan .nav-checkout li {
    width: 33.33333333%;
    float: left;
  }
  .block-thanh-toan .nav-checkout li .text {
    height: 40px;
  }
}
@media (max-width: 479px) {
  .block-thanh-toan .tab-opt2 .nav li a {
    font-size: 11px;
  }
}
.block-content-cart .block-title {
  border-bottom: 3px solid #69aa00;
  padding-bottom: 5px;
  padding-top: 5px;
  margin-bottom: 15px;
}
.block-content-cart .block-title:before,
.block-content-cart .block-title:after {
  content: '';
  display: table;
}
.block-content-cart .block-title:after {
  clear: both;
}
.block-content-cart .block-title strong {
  float: left;
  color: #69aa00;
  text-transform: uppercase;
  font-size: 16px;
  font-weight: normal;
  line-height: 30px;
}
.block-content-cart .block-title a {
  float: right;
  background-color: #d7d7d7;
  display: block;
  padding: 0 5px;
  text-align: center;
  line-height: 30px;
  color: #333;
  border: 1px solid #c2c2c2;
  border-radius: 5px;
}
.block-content-cart .block-title a:hover,
.block-content-cart .block-title a:focus {
  background-color: #b1b1b1;
}
.block-content-cart table tr td {
  border: none;
}
.block-content-cart table tr .t-img {
  padding-left: 0;
}
.block-content-cart table tr .t-img .img {
  width: 100px;
}
.block-content-cart table tr .t-price {
  text-align: right;
}
.block-content-cart .t-remove {
  display: none;
}
.block-content-cart .total {
  border-top: 2px solid #d7d7d7;
  padding-top: 20px;
  margin-bottom: 20px;
}
.block-content-cart .total .lable {
  float: left;
  display: block;
  font-size: 16px;
  line-height: 30px;
  padding: 0;
  width: 40%;
}
.block-content-cart .total .value {
  display: block;
  float: right;
  color: #e44a34;
  font-size: 24px;
  line-height: 30px;
  width: 60%;
  text-align: right;
}
.block-thanhtoan {
  margin-bottom: 30px;
}
.block-thanhtoan .block-title {
  display: block;
  background-color: #69aa00;
  color: #fff;
  text-transform: uppercase;
  padding: 10px 15px;
  line-height: 30px;
  font-size: 16px;
}
.block-thanhtoan .block-content {
  border: 2px solid #69aa00;
  padding: 20px;
  margin-bottom: 20px;
}
.block-thanhtoan .form-horizontal {
  padding-left: 31px;
  margin-bottom: 25px;
}
.block-thanhtoan .radio,
.block-thanhtoan .checkbox {
  margin-bottom: 25px;
  margin-top: 15px;
  margin-left: 10px;
}
.block-thanhtoan .radio > p,
.block-thanhtoan .checkbox > p {
  padding-left: 21px;
}
.block-thanhtoan .radio label,
.block-thanhtoan .checkbox label {
  font-weight: normal;
  display: inline-block;
  padding: 0;
  position: relative;
  margin-right: 20px;
  margin: 0 0 15px;
}
.block-thanhtoan .radio input[type='radio'],
.block-thanhtoan .checkbox input[type='radio'] {
  position: absolute;
  opacity: 0;
}
.block-thanhtoan .radio input[type='radio'] + span,
.block-thanhtoan .checkbox input[type='radio'] + span {
  font-weight: bold;
}
.block-thanhtoan .radio input[type='radio'] + span:before,
.block-thanhtoan .checkbox input[type='radio'] + span:before {
  content: '';
  font-family: 'FontAwesome';
  text-align: center;
  line-height: 18px;
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 100%;
  border: 2px solid #00b6f0;
  vertical-align: middle;
  margin-right: 5px;
  color: #fff;
  padding: 4px;
  background-clip: content-box !important;
  -moz-background-clip: content-box !important;
  -webkit-background-clip: content-box !important;
}
.block-thanhtoan .radio input[type='radio']:checked + span:before,
.block-thanhtoan .checkbox input[type='radio']:checked + span:before {
  background-color: #00b6f0;
}
.block-thanhtoan .radio input[type='checkbox'],
.block-thanhtoan .checkbox input[type='checkbox'] {
  position: absolute;
  opacity: 0;
}
.block-thanhtoan .radio input[type='checkbox'] + span:before,
.block-thanhtoan .checkbox input[type='checkbox'] + span:before {
  content: '';
  font-family: 'FontAwesome';
  text-align: center;
  line-height: 16px;
  width: 20px;
  height: 20px;
  display: inline-block;
  border: 2px solid #e1e1e1;
  vertical-align: middle;
  margin-right: 5px;
  color: #a51817;
}
.block-thanhtoan .radio input[type='checkbox']:checked + span:before,
.block-thanhtoan .checkbox input[type='checkbox']:checked + span:before {
  content: '\f00c';
}
.block-tt-hocvien {
  border: 2px solid #69aa00;
  margin-bottom: 30px;
}
.block-tt-hocvien .block-title {
  text-transform: uppercase;
  line-height: 30px;
  color: #69aa00;
  font-size: 16px;
  padding: 10px 15px;
  border-bottom: 2px solid #69aa00;
}
.block-tt-hocvien .block-content {
  padding-top: 20px;
}
.block-tt-hocvien table tr td,
.block-tt-hocvien table tr th {
  border: none;
  text-align: left;
}
.block-tt-hocvien table tr .t-label {
  white-space: nowrap;
}
.block-mua-kh {
  margin-bottom: 50px;
}
.block-mua-kh .block-title {
  display: block;
  text-align: center;
  margin-bottom: 15px;
}
.block-mua-kh .block-title strong {
  display: block;
  text-transform: uppercase;
  color: #69aa00;
  font-size: 24px;
  margin-bottom: 6px;
}
.block-mua-kh li {
  padding: 0 15px;
  float: left;
  width: 50%;
}
.block-mua-kh li:nth-child(2n + 1) {
  clear: both;
}
@media (max-width: 639px) {
  .block-mua-kh li {
    width: 100%;
  }
}
.block-mua-kh .radio,
.block-mua-kh .checkbox {
  margin: 15px 0;
}
.block-mua-kh .radio label,
.block-mua-kh .checkbox label {
  font-weight: normal;
  display: block;
  padding: 0;
  margin-right: 20px;
  position: relative;
  padding-left: 40px;
}
.block-mua-kh .radio input[type='radio'],
.block-mua-kh .checkbox input[type='radio'],
.block-mua-kh .radio input[type='checkbox'],
.block-mua-kh .checkbox input[type='checkbox'] {
  position: absolute;
  opacity: 0;
}
.block-mua-kh .radio input[type='radio'] + span,
.block-mua-kh .checkbox input[type='radio'] + span,
.block-mua-kh .radio input[type='checkbox'] + span,
.block-mua-kh .checkbox input[type='checkbox'] + span {
  box-shadow: 0 2px 18px 0 rgba(0, 0, 0, 0.3);
  display: table;
  width: 100%;
  max-width: 360px;
  border-radius: 5px;
  margin-right: auto;
  margin-left: auto;
}
.block-mua-kh .radio input[type='radio'] + span:before,
.block-mua-kh .checkbox input[type='radio'] + span:before,
.block-mua-kh .radio input[type='checkbox'] + span:before,
.block-mua-kh .checkbox input[type='checkbox'] + span:before {
  content: '';
  font-family: 'FontAwesome';
  text-align: center;
  line-height: 16px;
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 100%;
  border: 2px solid #69aa00;
  vertical-align: middle;
  margin-right: 5px;
  color: #fff;
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -10px;
}
.block-mua-kh .radio input[type='radio'] + span .count,
.block-mua-kh .checkbox input[type='radio'] + span .count,
.block-mua-kh .radio input[type='checkbox'] + span .count,
.block-mua-kh .checkbox input[type='checkbox'] + span .count {
  color: #fff;
  background-color: #69aa00;
  width: 60px;
  text-align: center;
  display: table-cell;
  vertical-align: middle;
  font-size: 20px;
  border-radius: 5px 0 0 5px;
}
.block-mua-kh .radio input[type='radio'] + span .detail,
.block-mua-kh .checkbox input[type='radio'] + span .detail,
.block-mua-kh .radio input[type='checkbox'] + span .detail,
.block-mua-kh .checkbox input[type='checkbox'] + span .detail {
  display: table-cell;
  padding: 15px;
}
.block-mua-kh .radio input[type='radio'] + span .title,
.block-mua-kh .checkbox input[type='radio'] + span .title,
.block-mua-kh .radio input[type='checkbox'] + span .title,
.block-mua-kh .checkbox input[type='checkbox'] + span .title {
  display: block;
  color: #69aa00;
  font-size: 20px;
  text-transform: uppercase;
  margin-bottom: 10px;
}
.block-mua-kh .radio input[type='radio'] + span .label,
.block-mua-kh .checkbox input[type='radio'] + span .label,
.block-mua-kh .radio input[type='checkbox'] + span .label,
.block-mua-kh .checkbox input[type='checkbox'] + span .label {
  float: left;
  color: #333;
  padding: 0;
  font-size: 14px;
  font-weight: normal;
  display: block;
  line-height: 1.42857;
  padding-right: 5px;
  width: 50%;
  text-align: left;
}
.block-mua-kh .radio input[type='radio'] + span .value,
.block-mua-kh .checkbox input[type='radio'] + span .value,
.block-mua-kh .radio input[type='checkbox'] + span .value,
.block-mua-kh .checkbox input[type='checkbox'] + span .value {
  display: block;
  float: right;
  text-align: right;
  width: 50%;
  white-space: nowrap;
}
.block-mua-kh .radio input[type='radio']:checked + span:before,
.block-mua-kh .checkbox input[type='radio']:checked + span:before,
.block-mua-kh .radio input[type='checkbox']:checked + span:before,
.block-mua-kh .checkbox input[type='checkbox']:checked + span:before {
  content: '\f00c';
  background-color: #69aa00;
}

.block-kp-thi {
  margin-bottom: 20px;
}
.block-kp-thi .block-title {
  line-height: 35px;
  font-size: 16px;
  text-align: center;
  color: #fff;
  background-color: #69aa00;
  text-transform: uppercase;
  padding: 0 5px;
  margin-bottom: 5px;
}
.block-kp-thi .block-title a {
  color: #fff;
}
.block-kp-thi .block-title a:hover,
.block-kp-thi .block-title a:focus {
  color: #fff;
}
.block-kp-thi .block-content {
  border: 2px solid #489036;
  overflow: hidden;
}
.block-kp-thi .nav {
  position: relative;
  border-bottom: 2px solid #489036;
}
.block-kp-thi .nav:before,
.block-kp-thi .nav:after {
  content: '';
  display: table;
}
.block-kp-thi .nav:after {
  clear: both;
}
.block-kp-thi .nav li {
  display: block;
  float: left;
  width: 20%;
}
.block-kp-thi .nav li + li a {
  border-left: 2px solid #489036;
}
.block-kp-thi .nav li a {
  display: block;
  line-height: 30px;
  text-align: center;
  color: #69aa00;
  padding: 0;
  /*border-bottom: 2px solid #489036;*/
  font-size: 16px;
  background-color: #fff;
  position: relative;
  z-index: 10;
}
.block-kp-thi .nav li a span {
  display: block;
  position: relative;
  z-index: 20;
}
.block-kp-thi .nav li a:hover,
.block-kp-thi .nav li a:focus {
  background-color: #fff;
}
.block-kp-thi .nav li a:before {
  content: '';
  display: block;
  background-color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: -2px;
  z-index: 10;
  opacity: 0;
}
.block-kp-thi .nav li:last-child a:before {
  content: '';
  display: block;
  background-color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  right: -5px;
  bottom: -2px;
}
.block-kp-thi .nav li.active a {
  /*border-bottom: 2px solid transparent;*/
  background-color: transparent;
}
.block-kp-thi .nav li.active a:before {
  opacity: 1;
}
.block-kp-thi .table {
  margin: 0;
  width: 100%;
}
.block-kp-thi .table tbody tr td,
.block-kp-thi .table tbody tr th {
  border: none;
  padding: 5px;
}
.block-kp-thi .table .t-stt {
  font-weight: bold;
  width: 65px;
  text-align: center;
}

.block-slide-banner .owl-nav .owl-next,
.block-slide-banner .owl-nav .owl-prev,
.block-buy-kh .owl-nav .owl-next,
.block-buy-kh .owl-nav .owl-prev {
  display: block;
  width: 30px;
  height: 42px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: auto 100%;
  position: absolute;
  top: 50%;
  margin-top: -31px;
}
.block-buy-kh .owl-nav .owl-prev {
  left: -30px;
  background-image: url(../images/icon/prev.png);
}
.block-buy-kh .owl-nav .owl-next {
  right: -30px;
  background-image: url(../images/icon/next.png);
}
@media (max-width: 639px) {
  .block-buy-kh .block-content {
    padding: 0 15px;
  }
  .block-buy-kh .owl-item {
    padding: 0;
  }
  .block-slide-banner .owl-nav .owl-prev,
  .block-slide-banner .owl-nav .owl-next,
  .block-buy-kh .owl-nav .owl-prev,
  .block-buy-kh .owl-nav .owl-next {
    width: 12px;
    height: 42px;
    margin-top: -31px;
    background-size: 100% auto;
  }
  .block-slide-banner .owl-nav .owl-prev,
  .block-buy-kh .owl-nav .owl-prev {
    left: -15px;
    background-image: url(../images/icon/prev.png);
  }
  .block-slide-banner .owl-nav .owl-next,
  .block-buy-kh .owl-nav .owl-next {
    right: -15px;
    background-image: url(../images/icon/next.png);
  }
  .block-buy-kh .item {
    font-size: 13px;
  }
  .block-buy-kh .item .title {
    font-size: 15px;
  }
  .block-buy-kh .item:hover {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
}
.block-thanh-vien {
  margin-bottom: 40px;
  background: #eee;
}
.block-thanh-vien .block-title {
  font-size: 36px;
  text-align: center;
  margin-bottom: 15px;
}
.block-thanh-vien .block-title strong {
  color: #fad21b;
  font-size: 80px;
  vertical-align: middle;
  font-weight: normal;
}
@media (max-width: 767px) {
  .block-thanh-vien .block-title {
    font-size: 20px;
  }
  .block-thanh-vien .block-title strong {
    font-size: 40px;
  }
}
.block-thanh-vien .block-content {
  background-position: top left;
  background-repeat: no-repeat;
  background-image: url(../images/icon/bg-thanh-vien2.png);
  min-height: 426px;
  background-size: 100% auto;
}
@media (max-width: 1199px) {
  .block-thanh-vien .block-content {
    background-repeat: repeat;
  }
}
@media (max-width: 767px) {
  .block-thanh-vien .block-content {
    min-height: 0;
  }
}
.block-thanh-vien .avata {
  text-align: center;
  margin-bottom: 100px;
}
.block-thanh-vien .avata img {
  height: 120px;
  width: 120px;
  display: inline-block;
  border-radius: 100%;
  -webkit-transition: 0.8s;
  -o-transition: 0.8s;
  transition: 0.8s;
  cursor: pointer;
}
.block-thanh-vien .avata img:hover {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
  -webkit-transition: 0.8s;
  -o-transition: 0.8s;
  transition: 0.8s;
}
@media (max-width: 767px) {
  .block-thanh-vien .avata {
    margin-bottom: 40px;
  }
  .block-thanh-vien .avata img {
    width: 60px;
    height: 60px;
  }
}
.block-thanh-vien .popover {
  border: 2px solid #6cc5b2;
  border-radius: 0;
}
.block-thanh-vien .popover .popover-title {
  color: #398373;
  font-weight: bold;
  font-size: 16px;
  background-color: transparent;
  border: none;
  padding-bottom: 3px;
}
.block-thanh-vien .popover.top .arrow {
  border-top-color: #6cc5b2;
}
.block-lo-trinh {
  margin-bottom: 40px;
  margin-top: 40px;
  background-color: #fff;
  padding: 15px 30px;
}
.block-lo-trinh .block-title {
  font-size: 30px;
  text-align: center;
  margin-bottom: 30px;
  text-transform: uppercase;
  margin-top: 20px;
}
.block-lo-trinh .block-title strong {
  color: #69aa00;
}
@media (max-width: 767px) {
  .block-lo-trinh .block-title {
    font-size: 20px;
  }
}
.block-lo-trinh .block-content {
  text-align: justify;
}
.block-lo-trinh .box-content {
  display: table;
  margin: 30px 0 20px;
  width: 100%;
}
.block-lo-trinh .box-content .center {
  height: 90px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top left;
  background-image: url(../images/icon/bg2.png);
  padding: 0 45px;
  line-height: 90px;
  display: block;
  font-size: 24px;
  width: 100%;
  display: table-cell;
  vertical-align: middle;
}
.block-lo-trinh .box-content .left,
.block-lo-trinh .box-content .right {
  color: #5cbfaa;
  text-transform: uppercase;
  font-size: 30px;
  display: table-cell;
  white-space: nowrap;
  width: 1%;
  vertical-align: middle;
  padding: 0 20px;
}
@media (max-width: 639px) {
  .block-lo-trinh .box-content .center {
    height: 60px;
    line-height: 60px;
    padding: 0 30px;
    font-size: 15px;
  }
  .block-lo-trinh .box-content .left,
  .block-lo-trinh .box-content .right {
    font-size: 16px;
    padding: 0 5px;
  }
}
.block-lo-trinh .btn {
  color: #fff;
  line-height: 47px;
  padding: 0 35px;
  border-radius: 9px;
  display: inline-block;
  background-color: #5cbfaa;
  font-size: 20px;
  text-transform: uppercase;
  margin: 20px 0;
}
.block-lo-trinh .btn:hover,
.block-lo-trinh .btn:focus {
  background-color: #3a9481;
}
@media (max-width: 639px) {
  .block-lo-trinh .btn {
    font-size: 16px;
    line-height: 40px;
  }
}
.block-chia-se {
  border: 3px solid #69aa00;
  padding: 15px;
  margin-bottom: 40px;
  margin-top: 40px;
}
.block-chia-se .block-title {
  font-size: 16px;
  text-align: center;
  margin-bottom: 10px;
  text-transform: uppercase;
  color: #a51817;
  text-decoration: underline;
  background-position: top left;
  background-size: 38px auto;
  background-repeat: no-repeat;
  background-image: url(../images/icon/icon1.png);
  padding-left: 40px;
  min-height: 40px;
}
.block-chia-se .block-title img {
  height: 50px;
  margin-right: 10px;
  display: none;
}
.block-chia-se .item {
  padding-bottom: 15px;
  position: relative;
}
.block-chia-se .item:before,
.block-chia-se .item:after {
  content: '';
  display: table;
}
.block-chia-se .item:after {
  clear: both;
}
.block-chia-se .item .link {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.block-chia-se .item .avata {
  display: block;
  width: 50px;
  height: 50px;
  border-radius: 100%;
  overflow: hidden;
  float: left;
}
.block-chia-se .item .avata img {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 100%;
}
.block-chia-se .item .detail {
  margin-left: 60px;
}
.block-chia-se .item .title {
  font-weight: bold;
  font-size: 16px;
  display: block;
}
.block-chia-se .item .des {
  font-style: italic;
}
.block-chia-se .block-content {
  position: relative;
  padding: 30px 0 20px;
}
.block-chia-se .bx-prev,
.block-chia-se .bx-next {
  height: 18px;
  width: 100%;
  display: block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: auto 100%;
  position: absolute;
  left: 0;
}
.block-chia-se .bx-prev {
  background-image: url(../images/icon/prev-bx.png);
  bottom: 0;
}
.block-chia-se .bx-next {
  background-image: url(../images/icon/next-bx.png);
  top: 0;
}
.block-carousel-kh {
  margin-bottom: 40px;
}
.block-carousel-kh .block-title {
  margin-bottom: 40px;
}
.block-carousel-kh .block-title:before,
.block-carousel-kh .block-title:after {
  content: '';
  display: table;
}
.block-carousel-kh .block-title:after {
  clear: both;
}
.block-carousel-kh .block-title strong {
  display: inline-block;
  font-size: 18px;
  background-color: #69aa00;
  color: #fff;
  padding: 10px 50px;
  border-radius: 30px;
  line-height: 30px;
  font-weight: normal;
  text-transform: uppercase;
  float: left;
  min-width: 200px;
  text-align: center;
}
@media (max-width: 639px) {
  .block-carousel-kh .block-title strong {
    font-size: 16px;
    padding: 10px 10px;
    display: block;
  }
}
@media (max-width: 479px) {
  .block-carousel-kh .block-title strong {
    float: none;
    clear: both;
    display: block;
  }
}
.block-carousel-kh .block-title .view-all {
  text-transform: uppercase;
  color: #69aa00;
  font-size: 16px;
  margin-top: 20px;
  display: inline-block;
  float: right;
}
@media (max-width: 639px) {
  .block-carousel-kh .block-title .view-all {
    font-size: 16px;
  }
}
@media (max-width: 479px) {
  .block-carousel-kh .block-title .view-all {
    font-size: 13px;
  }
}
@media (max-width: 639px) {
  .block-carousel-kh .block-title {
    margin-bottom: 20px;
  }
}
.block-carousel-kh .block-content {
  padding: 0 40px;
}
@media (max-width: 639px) {
  .block-carousel-kh .block-content {
    padding: 0 15px;
  }
}
.block-carousel-kh .item-kh {
  padding: 10px;
}
.block-carousel-kh .item-kh .item-info {
  box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 3px 3px;
  background-color: #fff;
}
.block-carousel-kh .item-kh .photo img {
  display: block;
  width: 100%;
}
.block-carousel-kh .item-kh .detail {
  padding: 7px 10px;
}
.block-carousel-kh .item-kh .name {
  display: block;
  font-weight: bold;
  height: 65px;
  overflow: hidden;
  margin-bottom: 10px;
  font-size: 16px;
}
.block-carousel-kh .item-kh .name a {
  color: #404040;
}
.block-carousel-kh .item-kh .name a:hover,
.block-carousel-kh .item-kh .name a:focus {
  color: #69aa00;
}
.block-carousel-kh .item-kh .arthur {
  float: left;
  background-position: left top;
  background-repeat: no-repeat;
  background-image: url(../images/icon/arthur.png);
  padding-left: 28px;
  background-size: 22px auto;
}
.block-carousel-kh .item-kh .view {
  float: right;
  background-position: left top 3px;
  background-repeat: no-repeat;
  background-image: url(../images/icon/view.png);
  background-size: 25px auto;
  padding-left: 30px;
}
@media (max-width: 991px) {
  .block-carousel-kh .owl-nav .owl-prev,
  .block-carousel-kh .owl-nav .owl-next {
    display: block;
    width: 30px;
    height: 42px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: auto 100%;
    position: absolute;
    top: 50%;
    margin-top: -31px;
  }
  .block-carousel-kh .owl-nav .owl-prev {
    left: -30px;
    background-image: url(../images/icon/prev.png);
  }
  .block-carousel-kh .owl-nav .owl-next {
    right: -30px;
    background-image: url(../images/icon/next.png);
  }
}
@media (max-width: 639px) {
  .block-carousel-kh .owl-item {
    padding: 0;
  }
  .block-carousel-kh .owl-nav .owl-prev,
  .block-carousel-kh .owl-nav .owl-next {
    width: 12px;
    height: 42px;
    margin-top: -31px;
    background-size: 100% auto;
  }
  .block-carousel-kh .owl-nav .owl-prev {
    left: -15px;
    background-image: url(../images/icon/prev.png);
  }
  .block-carousel-kh .owl-nav .owl-next {
    right: -15px;
    background-image: url(../images/icon/next.png);
  }
  .block-carousel-kh .item {
    font-size: 13px;
  }
  .block-carousel-kh .item .title {
    font-size: 15px;
  }
  .block-carousel-kh .item:hover {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
}
.block-register {
  margin-bottom: 40px;
}
.block-register .block-title {
  font-size: 30px;
  text-transform: uppercase;
  overflow: hidden;
  text-align: center;
  color: #69aa00;
  margin-bottom: 10px;
}
.block-register .block-title strong {
  font-weight: normal;
  padding: 0 50px;
  display: inline-block;
  vertical-align: middle;
}
.block-register .block-title:after,
.block-register .block-title:before {
  content: '';
  width: 100%;
  border-top: 3px solid #69aa00;
  display: inline-block;
  vertical-align: middle;
}
.block-register .block-title:after {
  margin-right: -100%;
}
.block-register .block-title:before {
  margin-left: -100%;
}
@media (max-width: 767px) {
  .block-register .block-title {
    font-size: 20px;
    margin-bottom: 10px;
  }
  .block-register .block-title strong {
    padding: 0 10px;
  }
}
.block-register .block-content {
  position: relative;
}
.block-register .block-content:before {
  content: '';
  width: 1px;
  display: block;
  background-color: #ccc;
  position: absolute;
  top: 10px;
  left: 50%;
  bottom: 0;
}
@media (max-width: 767px) {
  .block-register .block-content {
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
  .block-register .block-content .form-horizontal {
    margin-bottom: 40px;
  }
  .block-register .block-content:before {
    content: none;
  }
}
.block-register .title {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: normal;
  margin-bottom: 20px;
  display: block;
  margin-top: 20px;
}
@media (max-width: 991px) {
  .block-register .title {
    min-height: 40px;
  }
}
@media (max-width: 767px) {
  .block-register .title {
    min-height: 0;
  }
}
@media (max-width: 479px) {
  .block-register .title {
    font-size: 14px;
    text-align: center;
    min-height: 0px;
  }
}
.block-register .form-horizontal .form-group {
  margin-bottom: 5px;
}
.block-register .form-horizontal .control-label {
  text-align: left;
  font-weight: normal;
}
.block-register .form-horizontal .form-control {
  height: 34px;
  border: 1px solid #d7d7d7;
  border-radius: 0;
  box-shadow: none;
}
.block-register .form-horizontal textarea.form-control {
  height: 70px;
}
.block-register .form-horizontal select.form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-position: center right 14px;
  background-repeat: no-repeat;
  background-image: url(../images/icon/arrow-select.png);
  padding-right: 34px;
  background-size: 15px auto;
}
.block-register .form-horizontal select.form-control option {
  padding-top: 4px;
  padding-bottom: 4px;
}
.block-register .form-horizontal select::-ms-expand {
  display: none;
}
.block-register .form-horizontal .radio label,
.block-register .form-horizontal .checkbox label {
  font-weight: normal;
  display: inline-block;
  padding: 0;
  position: relative;
  margin-right: 20px;
}
.block-register .form-horizontal input[type='radio'] {
  position: absolute;
  opacity: 0;
}
.block-register .form-horizontal input[type='radio'] + span:before {
  content: '';
  font-family: 'FontAwesome';
  text-align: center;
  line-height: 18px;
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 100%;
  border: 1px solid #a51817;
  vertical-align: middle;
  margin-right: 5px;
  color: #fff;
}
.block-register .form-horizontal input[type='radio']:checked + span:before {
  content: '\f00c';
  background-color: #a51817;
}
.block-register .form-horizontal input[type='checkbox'] {
  position: absolute;
  opacity: 0;
}
.block-register .form-horizontal input[type='checkbox'] + span:before {
  content: '';
  font-family: 'FontAwesome';
  text-align: center;
  line-height: 16px;
  width: 20px;
  height: 20px;
  display: inline-block;
  border: 2px solid #e1e1e1;
  vertical-align: middle;
  margin-right: 5px;
  color: #a51817;
}
.block-register .form-horizontal input[type='checkbox']:checked + span:before {
  content: '\f00c';
}
.block-register .form-horizontal .btn {
  height: 40px;
  padding: 0 10px;
  text-transform: uppercase;
  color: #fff;
  background-color: #5cbfaa;
  border-radius: 15px;
  font-size: 16px;
  display: inline-block;
  width: 100%;
  text-align: center;
  max-width: 400px;
}
.block-register .form-horizontal .btn:hover,
.block-register .form-horizontal .btn:focus {
  background-color: #3a9481;
}
.block-register .log-facebook {
  text-align: center;
}
.block-register .log-facebook a {
  display: inline-block;
  margin-bottom: 10px;
}
.block-register .log-facebook a:hover img {
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}
.block-register .log-facebook strong {
  display: block;
  overflow: hidden;
  text-align: center;
  margin-bottom: 15px;
  font-weight: normal;
}
.block-register .log-facebook strong span {
  padding: 0 10px;
}
.block-register .log-facebook strong:after,
.block-register .log-facebook strong:before {
  content: '';
  width: 100%;
  border-top: 1px solid #d7d7d7;
  display: inline-block;
  vertical-align: middle;
}
.block-register .log-facebook strong:after {
  margin-right: -100%;
}
.block-register .log-facebook strong:before {
  margin-left: -100%;
}
.block-comment {
  margin-bottom: 40px;
}
.block-comment .block-title {
  font-size: 30px;
  color: #69aa00;
  text-transform: uppercase;
  margin-bottom: 20px;
}
.block-comment .block-title strong {
  font-weight: normal;
}
@media (max-width: 767px) {
  .block-comment .block-title {
    font-size: 18px;
    margin-bottom: 15px;
    .block-title-h3 {
      font-size: 18px;
      margin: 0;
    }
  }
}

.fb-comments {
  width: 100% !important;
  span {
    width: 100% !important;
    iframe {
      width: 100% !important;
    }
  }
}

.block-comment iframe {
  width: 100% !important;
}
.block-comment .nav {
  border-bottom: 1px solid #69aa00;
  margin-bottom: 10px;
}
.block-comment .nav li a {
  background-color: #ccc;
  color: #333;
  border-radius: 0;
  font-size: 20px;
  text-transform: uppercase;
  font-weight: normal;
}
.block-comment .nav li + li {
  margin-left: 7px;
}
.block-comment .nav li.active a {
  background-color: #69aa00;
  color: #fff;
}
.block-comment .tab-pane .block-title {
  display: none;
}
.block-banner {
  margin-bottom: 40px;
}
.block-banner img {
  display: block;
  width: 100%;
}
#wdg-countdown {
  margin-top: 14px;
}
.countdown-done {
  width: 1024px;
}
.countdown-process {
  margin-top: 10px;
  align-items: center;
  display: flex;
  justify-content: center;
  cursor: pointer;
  .left {
    width: 325px;
    margin-left: 10px;
    margin-right: -45px;
  }
  .right {
    width: 325px;
    margin-right: 10px;
    margin-left: -45px;
  }
  .wdg-time {
    position: relative;
    .title {
      font-size: 30px;
      text-align: center;
    }
    .date {
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      font-style: normal;
      font-size: 30px;
      line-height: 75px;
      position: absolute;
      left: 190px;
    }
    img {
      position: absolute;
      z-index: -1;
      width: 290px;
      top: -75px;
      left: 110px;
    }
    .content {
      margin-top: 80px;
      img {
        width: 40px;
        top: 112px;
        left: 0;
      }
      span {
        font-size: 18px;
        margin-left: 40px;
      }
    }
    li {
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 10px 0;
      text-align: center;
      background: rgba(255, 255, 255, 0.3);
      margin-left: 10px;
      float: left;
      border-radius: 10px;
      color: #333;
      text-transform: uppercase;
      font-weight: bold;
      span {
        font-family: initial;
        font-weight: 500;
        font-size: larger;
      }
      div.time {
        width: 100px;
        height: 100px;
        background: #eaebec;
        border: 1px solid #a3a3a3;
        box-sizing: border-box;
        border-radius: 8px;
        color: #333;
        position: relative;
        margin-bottom: 15px;

        .bottom {
          position: absolute;
          background-color: #fff;
          top: 49px;
          height: 49px;
          width: 98px;
          border-radius: 0 0 8px 8px;
        }
        p {
          font-size: 50px;
          position: absolute;
          top: 12px;
          left: 23px;
          z-index: 1;
          font-weight: 100;
        }
      }
    }
  }
  a.detail {
    padding: 7px 20px;
    color: #fff;
    font-size: 12px;
    background-color: #e8362f;
    border-radius: 5px;
    margin-left: 15px;
    text-transform: uppercase;
    &:hover {
      background-color: #e95d58;
    }
  }
  a.join {
    border: 1px solid rgba(255, 255, 255, 0.3);
    width: 260px;
    padding: 10px 15px;
    text-align: center;
    background-color: #fff;
    background: rgba(255, 232, 122, 0.2);
    margin-left: 10px;
    float: left;
    border-radius: 10px;
    color: #fff;
    text-transform: uppercase;
    font-weight: bold;
    &:hover {
      -moz-box-shadow: 0 0 10px #555a56;
      -webkit-box-shadow: 0 0 10px #555a56;
      box-shadow: 0 0 10px #555a56;
    }
  }
  .hand-icon {
    color: #ddffd2;
    font-size: 40px;
    padding: 15px;
    animation-name: thumb;
    animation-duration: 800ms;
    transform-origin: 50% 50%;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }
}
@keyframes thumb {
  0% {
    transform: rotate(30deg);
  }
  50% {
    transform: rotate(-30deg);
  }
  100% {
    transform: rotate(30deg);
  }
}
.h-190 {
  height: 190px;
}
.h-300 {
  height: 300px;
}

.countdown {
  width: 500px;
  margin: 0 auto;
}
.countdown .bloc-time {
  float: left;
  margin-right: 20px;
  text-align: center;
}
.countdown .bloc-time:last-child {
  margin-right: 0;
}
.countdown .count-title {
  font-family: Roboto;
  font-style: normal;
  display: block;
  margin-bottom: 15px;
  color: #1a1a1a;
  text-transform: uppercase;
  font-size: 20px;
}
.countdown .figure {
  position: relative;
  float: left;
  height: 110px;
  width: 100px;
  margin-right: 10px;
  background-color: #f8f9fb;
  border-radius: 8px;
  -moz-box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.2), inset 2px 4px 0 0 rgba(255, 255, 255, 0.08);
  -webkit-box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.2), inset 2px 4px 0 0 rgba(255, 255, 255, 0.08);
  box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.2), inset 2px 4px 0 0 rgba(255, 255, 255, 0.08);
  margin-bottom: 15px;
  margin-top: 15px;
}
.countdown .figure:last-child {
  margin-right: 0;
}
.countdown .figure > span {
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  font: normal 3.6em/107px 'Helvetica';
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #333333;
}
.countdown .figure .top:after,
.countdown .figure .bottom-back:after {
  content: '';
  position: absolute;
  z-index: -1;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.countdown .figure .top {
  z-index: 3;
  background-color: #eaebec;
  -webkit-transform-origin: 50% 100%;
  transform-origin: 50% 100%;
  -moz-border-radius-topleft: 10px;
  -webkit-border-top-left-radius: 10px;
  border-top-left-radius: 10px;
  -moz-border-radius-topright: 10px;
  -webkit-border-top-right-radius: 10px;
  border-top-right-radius: 10px;
  -moz-transform: perspective(200px);
  -ms-transform: perspective(200px);
  -webkit-transform: perspective(200px);
  transform: perspective(200px);
}
.countdown .figure .bottom {
  z-index: 1;
}
.countdown .figure .bottom:before {
  content: '';
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background-color: rgba(0, 0, 0, 0.02);
}
.countdown .figure .bottom-back {
  z-index: 2;
  top: 0;
  height: 50%;
  overflow: hidden;
  background-color: #eaebec;
  -moz-border-radius-topleft: 10px;
  -webkit-border-top-left-radius: 10px;
  border-top-left-radius: 10px;
  -moz-border-radius-topright: 10px;
  -webkit-border-top-right-radius: 10px;
  border-top-right-radius: 10px;
}
.countdown .figure .bottom-back span {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.countdown .figure .top,
.countdown .figure .top-back {
  height: 50%;
  overflow: hidden;
  -moz-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.countdown .figure .top-back {
  z-index: 4;
  bottom: 0;
  background-color: #f8f9fb;
  -webkit-transform-origin: 50% 0;
  transform-origin: 50% 0;
  -moz-transform: perspective(200px) rotateX(180deg);
  -ms-transform: perspective(200px) rotateX(180deg);
  -webkit-transform: perspective(200px) rotateX(180deg);
  transform: perspective(200px) rotateX(180deg);
  -moz-border-radius-bottomleft: 10px;
  -webkit-border-bottom-left-radius: 10px;
  border-bottom-left-radius: 10px;
  -moz-border-radius-bottomright: 10px;
  -webkit-border-bottom-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
.countdown .figure .top-back span {
  position: absolute;
  top: -100%;
  left: 0;
  right: 0;
  margin: auto;
}

.modal-header .close {
  padding: 1rem;
  margin: -1rem -1rem -1rem auto;
}

.modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}
#infoModal {
  .modal-header {
    background-color: #568b3e;
    color: #fff;
    .close {
      float: right;
      font-size: 28px;
      font-weight: bold;
    }
  }
  .modal-body {
    .form-group {
      label {
        font-weight: 100 !important;
      }
      .ip-add {
        width: 30%;
        float: left;
        margin-right: 3%;
      }
      .ip-add:last-child {
        margin: 0;
        width: 33%;
      }
      select.form-control {
        -webkit-appearance: auto;
      }
      a {
        background-color: #e88936;
        color: #fff;
        text-decoration: none;
        padding: 7px;
        margin-bottom: 5px;
        text-align: center;
        border-radius: 3px;
        width: 25%;
        &:hover {
          background-color: #e18d43;
          box-shadow: 0 7px 5px -5px black;
        }
      }
      textarea {
        resize: vertical;
        margin-top: 40px;
      }

      span.has-error {
        color: #dc3f3f;
        font-size: 13px;
        margin-bottom: 10px;
      }
      span.required {
        color: #dc3f3f;
        font-size: 15px;
        margin-left: 5px;
      }
    }
    .title {
      padding: 18px;
      font-size: 16px;
    }
  }
}

@keyframes orange-glow {
  from {
    box-shadow: 0px 0px 11px -1px rgba(255, 113, 68, 0);
  }
  to {
    box-shadow: 0px 0px 11px -1px rgba(255, 113, 68, 1);
  }
}
.label-hot {
  background-color: #f22b2b;
  color: #fff;
  padding: 2px;
  border-radius: 3px;
  font-size: 10px;
}
.combo-plus {
  .title {
    color: #389c15;
    font-size: 18px;
  }
  .chk-plus {
    cursor: pointer;
    font-weight: unset;
    input {
      display: none;
    }
    .fa {
      color: rgb(46, 115, 46);
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
  p {
    font-size: 15px;
  }
  .vnd-plus {
    color: #389c15;
  }
}
.has-badge {
  position: relative;
  .badge--red {
    background-color: #fa3e3e;
    border-radius: 4px;
    text-align: center;
    font-size: 11px;
    padding: 0px 3px 0px 3px;
    color: #fff;
    position: absolute;
    margin: -8px 0 0 10px;
    opacity: 1;
  }
}
