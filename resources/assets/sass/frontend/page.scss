@import "variables";

.main{
	.main-page-center{ width: 750px;
		.page-detail-container{ width: 97%;
			.page-detail-title{font-weight: bold; margin-top: 80px; font-family: 'HelveticaNeue-Thin', 'Helvetica Neue Light', 'Helvetica Neue', 'Segoe UI', Helvetica, Arial, 'Lucida Grande', sans-serif; }
			.page-detail-info{ width: 470px; float: left; opacity: 0.6; font-size: 13px; margin-top: 5px;}
			.page-social-like{width: 100%; float: left; padding: 15px 0;}
			.page-detail-content{font-size: 16px;
				.main-content{width: 100%; float: left; padding-bottom: 30px; padding-top: 30px;
					iframe{max-width: 100% !important;}
					img{max-width: 100% !important;}

				}
			}
			.comment-container{  padding-bottom: 40px; width: 100%; float: left;
				.comment-heading{width: 100%; float: left; border-bottom: 1px solid $primaryColor; margin-top: 20px;
					span{font-size: 25px; background: $primaryColor; color: #fff; padding: 4px 15px;}
				}
				.fb-comments{margin-left: -5px;}
			}
			
		}
	}
}