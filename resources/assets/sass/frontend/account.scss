@import "variables";

.main{

	.main-user-left{width: 230px; float: left; min-height: 500px; padding: 60px 20px 20px 0;
		.user-avatar{width: 180px; height: 180px; border-radius: 6px; object-fit: cover; border: 0.5px #EEE solid;}
		.change-avatar, .save-avatar{margin: 15px 0 30px 0; width: 180px; text-align: center; border: 1px solid #CCC; outline: none;
			&:focus{outline: none; border-color: $primaryColor;}
		}
		.change-avatar .change-avatar-btn-mobile { display: none; }
		.item-user{padding: 7px 5px; width: 100%; float: left; color: #999;
			&:hover{color: $primaryColor; background: #eee; }
			i{margin-right: 10px; width: 12px;}
		}
		.current{color: $primaryColor; font-weight: bold; }
		.clear-preview-upload{position: absolute; font-size: 25px; height: 25px; width: 25px; float: right; cursor: pointer; margin: -10px 0 0 -10px; display: none;
			i{background: #fff; border-radius: 50%; height: 21px; color: #f06; }
		}
	}

	.main-user-right{ width: 794px; min-height: 700px; float: left; border-left: 1px solid #EEE; padding: 40px 0 40px 30px;
        .error-list { padding-left: 30px; margin-bottom: 20px; color: red;
            li { list-style-type: circle; font-size: 14px; }
        }
        .change-captcha-icon { margin-top: 12px; margin-left: 10px; }
		.main-user-title{padding-bottom: 30px; font-size: 25px;}
        .captcha-input { float: left; }
		.table>tbody>tr{
			td{border-top: 1px solid #eee; padding-left: 5px; padding-right: 10px;}
			&:hover{background: #FAFAFA;}
		}
		.user-form-item{padding: 15px 0; font-size: 15px;
			.error-list{
				padding-left: 20px;
				margin-bottom: 10px;
				color: red;
				li{ list-style-type: circle; font-size: 14px; }
			}
			.user-form-input{padding: 2px 10px; outline: none; min-width: 250px;}
			.change-btn{margin-top: 8px;}
			.cancel-btn{margin-top: 8px; margin-left: 3px;}
			.change-title{border-bottom: 1px solid #ccc; padding-bottom: 5px;}
			.user-form-day{width: 60px; margin-right: 10px; }
			.user-form-month{width: 50px;}
			.user-form-year{width: 80px;}
			.user-form-text-area{padding: 5px; width: 100%; border: 1px solid gray; border-radius: 5px;}
            .send-mail-verify-email { padding: 3px 7px; }

		}
		.desc{color: #999; width: 170px;}
		.action{color: #588d3f; width: 100px; text-align: right; font-weight: 400; font-size: 14px;
			i{opacity: 0;}
			&:hover{color: #666; cursor: pointer;
				i{opacity: 0.8;}
			}
		}

		#captcha-input {float: left;}
		.captcha-container{float: left; margin-left: 10px; border: 1px solid #d7d7d7;}
		.change-btn-container{width: 100%; float: left;}
		.protected{ i{color: #ccc; cursor: pointer;} text-align: right; }
		.lock{font-weight: 500; color: #9b5a74;}

		.notification-empty-container{width: 100%; float: left; padding: 150px 0; margin-bottom: 30px; text-align: center; color: #999; border: 1px #DDD dashed;}

		.noti-title{width: 500px; float: left; padding-bottom: 15px;}
		.mark-as-readed{float: right; padding-top: 27px; cursor: pointer; color: $primaryColor;}
		.notification-item{ padding: 8px 0; height: 50px; cursor: pointer;
			.notifi-info{text-align: right; width: 175px; padding-left: 0; padding-right: 5px;}
			i{margin: 0 5px;}
			&:last-child{border-bottom: 1px solid #EEE;}
			a{color: #444;}
		}
		.unread{background: #f4f7f5; border-top-color: #DDD !important;
			a{color: #000;}
		}

		.paginate-container{ text-align: right;
			.pagination{float: right; display: inline;
				.active>span{background: $primaryColor; border-color: $primaryColor;}
			}
		}

		.password-label{width: 200px;}

		.dm-tab{ width: 100%; float: left; margin-top: 10px; border-bottom: 1px solid #EEE; height: 35px;
			.tab-item{ float: left; margin-right: 30px; padding-bottom: 10px; font-size: 16px; cursor: pointer; color: #333;
				&:hover{color: $primaryColor;}
				i{margin-right: 5px;}
			}
			.active{border-bottom: 2px solid $primaryColor; color: $primaryColor; height: 35px;}
		}

		.tab-content-container{width: 100%; float: left;}
		.bought-courses{ margin-top: 50px; margin-bottom: 20px; overflow-x: hidden;
			.bought-courses-container{width: 800px; float: left;}
			.course-item{width: 234px; float: left; margin-right: 30px; height: 200px;
				.images{width: 234px; height: 135px;
					img{width: 234px; height: 135px; object-fit: cover;}
				}
				.info{background: rgba(0, 0, 0, 0.6) none repeat scroll 0 0; border: 0 none; float: left; box-sizing: border-box;
					padding: 3px 10px 5px 10px; width: 100%; margin-top: -42px; position: relative;
					.title a{color: #eee; font-size: 13px;}
					.name-gv{font-size: 10px; color: #BBB;}
				} 
				.expried{width: 100%; float: left; padding: 7px 0 7px 10px; font-size: 15px; background: #F3F3F3;}
			}
		}
		
		.bought-other-courses{  overflow-x: hidden; margin-top: 20px;
			.bought-courses-container{width: 800px; float: left;
				.course-item{width: 170px; float: left; position: relative; margin-bottom: 20px; margin-right: 30px; 
					.info{background: rgba(0, 0, 0, 0.6) none repeat scroll 0 0; border: 0 none; bottom: 0; float: left; padding: 3px 10px 5px 10px; position: absolute; width: 100%;
						.title a{color: #eee; font-size: 13px;}
						.name-gv{font-size: 10px; color: #BBB;}
					} 
					.expried{ width: 100%; float: left; padding: 10px 0 0 0; font-size: 15px; }
					.images { width: 100%;
                        a img { width: 100%; height: 96px; }
                    }
				}
			}
		}

		.course-journey{ margin-top: 50px; 
			.journey-item{ width: 100%; float: left; background: #EEE; padding: 15px 20px; margin-bottom: 10px; border-radius: 6px; margin-left: 0; margin-right: 0; }
		}

		.course-test{ margin-top: 50px;
			.scroll-items{max-height: 400px; overflow-y: auto; width: 250px; float: left; border-top: 1px solid #DDD; border-bottom: 1px solid #DDD;
				.test-item{width: 100%; float: left; padding: 7px 0; border-bottom: 1px solid #DDD; } 
			}
			.review-test-result { float: right; position: relative; top: -70px; }
			.remove-test-result { float: right; position: relative; top: -30px; right: -104px; }
			#reviewTestResult {
				.test-modal-dialog {
					width: 90%;
                    .question-box {
                        .answer-box {
                            .three-line-answer { height: 140px; }
                            .two-line-answer { height: 150px; }
                            .question-answer {
                                .labels {
                                    font-size: 16px;
                                    font-weight: 100;
                                    display: inline-block;
                                    max-width: 100%;
                                    white-space: normal;
                                    text-align: left;
                                    line-height: 1.5;
                                    padding: 3px 8px;
                                }
                                .label-true {
                                    color: #5cb85c;
                                    border: 1px solid #5cb85c;
                                    padding: 2px 7px;
                                    border-radius: 8px;
                                }
                                .label-false {
                                    color: red;
                                    border: 1px solid red;
                                    padding: 2px 7px;
                                    border-radius: 8px;
                                }
                            }
                        }
                    }
				}
			}
		}
		.score-group-image {
			width: 50px;
			height: 50px;
			border-radius: 8px;
			object-fit: cover;
			border: solid 1px #ddd;
			margin-right: 8px;
		}
	}

	.improve-score-container {
		.improve-score-content {
			padding-bottom: 70px;
			.statistic-table {
				margin-top: 30px;
			}
			.comment-box {
				background: #F7FFF0;
				border: 2px dashed #96D962;
				border-radius: 10px;
				padding: 65px 55px 30px 55px;
				margin-top: 140px;
				margin-left: 35px;
				position: relative;
				.teacher-comment-img {
					position: absolute;
					top: -32px;
					left: 36px;
				}
				.mori-icon {
					width: 113px;
					position: absolute;
					top: -58px;
					left: -45px;
					z-index: 1000;
				}
				.teacher-comment {
					font-family: 'Quicksand';
					line-height: 26px;
					font-size: 16px;
				}
			}
			.error-score {
				font-family: 'Quicksand';
				font-size: 16px;
				color: red;
			}
		}
	}
}