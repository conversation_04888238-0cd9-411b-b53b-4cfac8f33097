@import "variables";
@import "mixins";
.test-online {
  &-page {
    margin-top: 20px;
    .guest-cover-container{background: #FFF; text-align: center; padding: 170px 0;
      .free-course-info { padding-left: 25px; padding-right: 25px;
        h3 { line-height: inherit; color: #e74c3c }
      }
      .btn-login{padding: 6px 20px; background-color: $primaryColor; color: #fff; border-radius: 20px; margin-left: 10px;
        cursor: pointer; outline: none; display: inline; margin-top: 4px; font-size: 16px;
        &:hover{ opacity: 0.9; background: #5cbfaa; color: #fff; }
      }
      .btn-register{padding: 5px 20px; cursor: pointer; border-radius: 20px; border: 1px solid $primaryColor; background-color: #fff;  outline: none;
        color: $primaryColor; display: inline; margin-top: 4px; cursor: pointer; font-size: 16px;
        &:hover{ opacity : 0.9; background: #5cbfaa; color: #fff; border-color: #5cbfaa; }
      }
      .btn-buy{padding: 5px 20px; cursor: pointer; border-radius: 20px; background: $currencyColor; color: #fff; border-color: $currencyColor;
        display: inline; margin-top: 4px; cursor: pointer; font-size: 16px;
        &:hover{ opacity : 0.9; border: 1px solid $currencyColor; background-color: #fff; outline: none; color: $currencyColor; }
      }
    }
  }
  &__navigation {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    &:after {
      content: ' ';
      position: absolute;
      width: 100%;
      height: 50%;
      top: 0;
      left: 0;
      border-bottom: 2px dashed #eee;
      z-index: -1;
    }
    &-item {
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      background: #F6F6F6;
      border-radius: 10px;
      margin: auto 15px;
      width: 160px;
      height: 130px;
      cursor: pointer;
      transition: 0.2s ease-in-out;
      border-color: transparent;
      svg {
        path {
          transition: 0.4s ease-in-out;
        }
      }
      &:hover {
        border: 2px dashed #c9c9c9;
      }
      &.active {
        background: #ffffff;
        box-shadow: inset 5px 5px 10px #c9c9c9,
        inset -5px -5px 10px #ffffff;
        svg {
          path {
            &:first-child {
              fill: #96D962;
            }
          }
        }
      }
    }
  }
  &__list {
    width: 800px;
    margin: 50px auto;
  }
  &__date {
    width: 100%;
    margin-top: 50px;
    text-align: center;
    font-size: 30px;
    font-weight: 400;
    color: #828282;
  }
  &__group {
    margin-top: 15px;
    &-name {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 20px;
    }
  }
  &__schedule {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px;
    border-left: 3px solid #C4C4C4;
    //border-bottom: 1px solid #C4C4C4;
    margin: 8px auto;
    margin-left: 30px;
  }
  &__join-btn {
    padding: 6px 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid #96D962;
    background: #F7FFF0;
    color: #96D962;
    font-weight: bold;
    border-radius: 8px;
    cursor: pointer;
    transition: 0.2s ease-in-out;
    user-select: none;
    &.disabled {
      border: 2px solid #dcdcdc;
      background: #F4F4F4;
      color: #dcdcdc;
      cursor: default;
      &:hover {
        background: #F4F4F4;
      }
    }
    &:hover {
      background: #e5ffc8;
    }
  }
  &__rank {
    width: 800px;
    margin: 50px auto;
    &-avatar {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 1.5px solid rgba(0, 0, 0, 0.68);
      overflow: hidden;
    }
    &-student {
      padding: 5px 0;
      margin: 2px auto;
      border-radius: 40px;
      &.highlight {
        font-weight: bold;
      }
    }
    &-order {
      font-family: Mulish, Arial, sans-serif;
      font-weight: bold;
      -webkit-text-stroke-width: 1px;
      -webkit-text-stroke-color: black;
    }
    &-header {
      display: flex;
      align-items: center;
      height: 80px;
      font-family: Arial, sans-serif;
      font-weight: 500;
      font-size: 18px;
      color: $gray-100;
      div {
        height: 100%;
        width: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        cursor: pointer;
        &:not(&.active) {
          border-bottom: 2px solid #96D962;
        }
        &.active {
          border: 2px solid #96D962;
          border-bottom: 0;
        }
      }
    }
    &-body {
      padding: 30px;
      border: 2px solid #96D962;
      border-top: 0;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
    }
    &-filter {
      display: flex;
      justify-content: space-between;
      .el-select .el-input .el-select__caret,
      .el-input__icon {
        color: #96D962 !important;
      }
      .el-input__inner {
        border-width: 0;
        border-bottom: 2px solid #96D962;
        color: #96D962;
      }
    }
  }
  &__question-block {
    &-list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: 100%;
      gap: 4px;
    }
    &-item {
      cursor: pointer;
      width: 15px;
      height: 15px;
      padding: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px solid #aeaeae;
      border-radius: 5px;
      font-weight: bold;
      color: #aeaeae;
      font-size: 14px;
      font-family: Quicksand, Arial, sans-serif;
      &.active {
        font-size: 14px;
        font-family: Quicksand, Arial, sans-serif;
        background-color: #3da038;
        color: white;
        border: 2px solid #3da038;
      }
    }
  }
  &__exam {
    border: 1px solid #cfcfcf;
    border-radius: 10px;
    overflow: hidden;
    width: 670px;
    &-wrapper {
      display: flex;
      margin-bottom: 20px !important;
    }
    &-header {
      text-align: center;
      padding: 15px;
      background: #87D15F;
      color: #FFF;
      font-size: 20px;
      font-weight: bold;
    }
    &-body {
      padding: 30px 20px;
      table,
      img {
        max-width: 100% !important;
      }
    }
    &-result {
      padding: 0 10%;
    }
    &-lesson {
      padding: 10px 20px;
      background: #96D962;
      font-family: Quicksand, Arial, sans-serif;
      color: #FFF;
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 1.21;
      margin-bottom: 15px;
    }
    &-questions {
      font-size: 16px;
    }
    &-answers {
      margin-top: 5px;
      font-size: 14px;
      label {
        font-weight: 400;
      }
      .chose {
        padding: 2px 3px;
        color: #fa0000;
        border: 1px solid #fa0000;
      }
      .correct {
        color: #87D15F;
        border: 1px solid #87D15F;
      }
    }
    &-answer {
      display: inline-flex;
    }
    &-side {
      width: 310px;
      height: 68vh;
      position: sticky;
      top: 135px;
      margin-left: 42px;
      .submit-exam-btn{background: #87D15F; border-radius: 20px; color: #fff; width: 100%; margin-top: 10px;}
    }
    &-audio {
      width: 100vw;
      position: sticky;
      top: 80px;
      left: 0;
      display: flex;
      justify-content: center;
      z-index: 98;
      &-wrapper {
        width: 1100px;
        display: flex;
        justify-content: flex-start;
      }
      audio {
        width: 655px;
        opacity: 70%;
        transition: 0.3s ease-in-out;
        &:hover {
          opacity: 100%;
        }

      }
    }
  }
  &__history {
    width: 800px;
    margin: 50px auto;
    &-item {
      display: grid;
      grid-template-columns: 4fr 1fr 1fr;
      padding-left: 50px;
      margin-top: 20px;
    }
  }
}
/* On screens that are 992px or less, set the background color to blue */
@media screen and (max-width: 1024px) {
  .test-online {
    &__exam {
      width: 100%;
      padding: 0;
      &-wrapper {
        flex-direction: column;
        padding: 0 20px;
      }
      &-body {
        padding: 5px;
        table,
        img {
          width: 100% !important;
        }
      }
      &-side {
        width: 100%;
        padding: 10px;
        margin-left: 0;
      }
    }
  }
}
