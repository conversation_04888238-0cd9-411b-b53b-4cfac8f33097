@import "mixins";
@import "variables";

.header {
  &--pc {
    width: 100vw;
    height: 77px;
    position: fixed;
    top: 0;
    z-index: 99;
    background-color: rgba(255, 255, 255, 1);
    //border: 1px solid rgba(0, 0, 0, 0.1);
    transition: 0.2s ease-in-out;
    border-bottom: 1px solid $gray-10;
    &:hover {
      background-color: $white;
      backdrop-filter: blur(16px) saturate(180%);
      -webkit-backdrop-filter: blur(16px) saturate(180%);
      background-color: rgba(255, 255, 255, 0.75);
      border-bottom: 1px solid rgba(209, 213, 219, 0.3);
    }
    .container {
      height: 100%;
    }
    .header-logo {
      min-width: 94px;
    }
    .login-logout {
      display: flex;
      align-items: center;
      background-color: $green-primary;
      border-radius: 20px;
      overflow: hidden;
      a {
        display: block;
        padding: 10px;
        font-family: <PERSON>ser<PERSON>, Arial, sans-serif;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        color: $gray-100;
        text-transform: uppercase;
        line-height: 1;
        transition: 0.2s ease-in-out;
        &:hover {
          color: $white;
        }
        &:first-child {
          padding-right: 4px;
          box-shadow: 9px 0px 5px -10px rgba(0,0,0,0.75);
        }
        &:last-child {
          padding-left: 4px;
        }
      }
    }
    .hover-green {
      transition: 0.15s ease-in-out;
      &:hover > svg {
        path {
          stroke: $green-dark;
        }
      }
    }
  }
  &-inner {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .my-course-btn {
      background: #F7FFF0;
      border: 2px solid #96D962;
      border-radius: 50px;
      padding: 7px 15px;
      color: #41A336;
      font-size: 14px;
      margin-right: 5px;
      margin-right: 1.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover {
        background-color: #b0ed81;
      }
    }

    .my-course-show {
      animation: my-course-fadein 200ms linear forwards;
    }

    .my-course-hide {
      animation: my-course-fadeout 200ms linear forwards;
    }

    @keyframes my-course-fadein {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes my-course-fadeout {
      from { opacity: 1; }
      to { opacity: 0; }
    }

    #my-course {
      display: none;
      position: absolute;
      top: 76px;
      background-color: rgba(0, 0, 0, 0.4);
      width: 100%;
      height: calc(100vh - 77px);
      left: 0;

      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      ::-webkit-scrollbar-button {
        width: 1px;
        height: 1px;
      }

      ::-webkit-scrollbar-thumb {
        background: rgba(139, 217, 135, 0.8);
        border: 0px solid #ffffff;
        border-radius: 44px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: rgba(155, 255, 153, 0.81);
      }

      ::-webkit-scrollbar-thumb:active {
        background: rgba(87, 207, 79, 0.8);
      }

      ::-webkit-scrollbar-track {
        background: #fff;
        border: 0px solid yellow;
        border-radius: 54px;
        box-shadow: inset 0 0 2px grey;
      }

      ::-webkit-scrollbar-track:hover {
        background: #d9d9d9;
      }

      .my-course-main {
        background-color: #fff;
        .my-course-container {
          width: 1140px;
          margin: 0 auto;
          .title-box {
            position: relative;
            padding: 0;
            i {
              position: absolute;
              color: #BFBFBF;
              font-size: 56px;
              top: 0;
              margin-top: 18px;
              right: 0;
              cursor: pointer;
            }
          }
          .title {
            font-family: 'Montserrat';
            font-weight: 700;
            font-size: 38px;
          }
          .description {
            font-family: 'Quicksand';
            font-weight: 400;
            font-size: 18px;
          }
          hr {
            border-width: 2px;
            border-color: #bcbcbc;
          }
          .my-course-content {
            display: none;
            max-height: calc(100vh - 200px);
            padding: 20px 80px;
            display: grid;
            grid-template-columns: auto auto auto auto;
            overflow-y: scroll;
            .empty-box {
              display: flex;
              flex-direction: column;
              width: 100%;
              align-items: center;
              font-family: 'Quicksand';
              font-size: 25px;
              img {
                width: 250px;
              }
              span {
                margin: 20px 0;
              }
              a {
                background-color: #6bd55f;
                color: #fff;
                padding: 10px 60px;
                border-radius: 30px;
                cursor: pointer;
              }
            }
            .my-course-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin: 20px 0;
              cursor: pointer;
              img {
                width: 210px;
                height: 138px;
              }
              span {
                display: flex;
                align-items: center;
                font-family: 'Montserrat';
                font-weight: 500;
                font-size: 14px;
                color: #000;
                margin-top: 10px;
                i {
                  font-size: 6px;
                  margin: 0px 10px;
                }
              }
            }
          }
        }
      }
    }
  }
  &-menu {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &__item {
      height: 46px;
      min-width: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: auto 1px;
      padding: 10px 10px;
      position: relative;
      cursor: pointer;
      text-transform: uppercase;
      text-decoration: none;
      color: white;
      font-weight: 400;
      line-height: 1.61;
      transition: 0.15s ease-in-out;
      border-radius: 8px;
      &:hover {
        color: white;
        .header-menu__dropdown {
          display: flex;
          opacity: 1;
        }
      }
      &.featured {
        color: $orange-100;
        &:hover {
          background-color: transparent;
        }
      }
      &:hover,
      &.active {
        //color: $green-dark;
        background-color: $green-dark;
        //-webkit-box-shadow: 0 5px 0 0 $green-dark;
        //box-shadow: 0 5px 0 0 $green-dark;
        &:first-child {
          //background-color: transparent;
        }
        svg {
          path {
            fill: $green-dark;
          }
        }
      }
      &:first-child {
        &:hover {
          box-shadow: none;
        }
      }
    }
    &__dropdown {
      position: absolute;
      min-width: 320px;
      display: none;
      opacity: 0;
      top: 44px;
      padding-top: 30px;
      left: 0;
      z-index: 99;
      line-height: 1.2;
      transition: 0.3s ease-in-out;
      &.mega {
        min-width: 720px;
      }
      &-inner {
        width: 100%;
        padding: 20px 0;
        border-radius: 20px;
        border: 2px solid #fff;
        background: $green-primary;
        color: white;
        .border-right {
          border-right: 1px solid #fff;
        }
        .menu-column {
          display: flex;
          flex-flow: column;
          min-height: 100%;
        }
      }
      &-item,
      &-sub-item {
        transition: 0.2s ease-in-out;
        padding: 0 20px;
        margin-top: 2px;
        margin-bottom: 2px;
        display: block;
        text-decoration: none;
        font-weight: 400;
        color: white;
        &:hover,
        &.active {
          text-decoration: none;
          color: white;
        }
        &-inner {
          padding: 15px;
          transition: 0.2s ease-in-out;
          border-radius: 15px;
          display: block;
          text-decoration: none;
          color: white;
          &:hover,
          &:active,
          &:focus {
            text-decoration: none;
            color: white;
          }
          svg {
            path {
              fill: none !important;
            }
          }
        }
        &:hover {
          .header-menu__dropdown-item-inner {
            background-color: $green-dark;
            //box-shadow: inset 5px 5px 0px #3c5727,
            //inset -5px -5px 0px #f0ff9d;
          }
        }
        &.active {
          .header-menu__dropdown-item-inner {
            background-color: $green-dark;
            font-weight: 600;
            //box-shadow: inset 5px 5px 0px #3c5727,
            //inset -5px -5px 0px #f0ff9d;
          }
        }
        .title {
          user-select: none;
          font-size: 16px;
          font-weight: 600;
        }
        .description {
          font-family: Quicksand, Arial, sans-serif;
          user-select: none;
          font-size: 12px;
          font-weight: 400;
          text-transform: none;
        }
      }
      &-sub-item {
        text-transform: none;
        padding: 0 10px;
        .title {
          font-weight: 400;
        }
        &-inner {
          padding: 10px 15px;
          border-radius: 50px;
        }
        &:hover {
          .header-menu__dropdown-sub-item-inner {
            background-color: $green-dark;
          }
        }
        &.active {
          .header-menu__dropdown-sub-item-inner {
            background-color: $green-dark;
            font-weight: 600;
            .title {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  &-avatar {
    position: relative;
    .user-menu {
      margin-left:0; right: -15px; top: 37px; width: 190px; left: unset;
      .caret-up{position: absolute; float: right; margin-top: -13px; right: 20px;}
      li{ width: 100%; float: left; padding: 0;
        a{padding: 7px 15px;
          i{margin-right: 10px;}
        }
        .trophy-icon {
          margin-right: 8px;
        }
      }
      .dropdown-divider {height: 0; margin: .5rem 0; overflow: hidden; border-top: 1px solid #e9ecef; }
    }
    .user-info-box { width: 26px; }
    .user-avatar-circle {
      width: 26px; height: 26px; border-radius: 50%; object-fit: cover; border: 1px solid $gray-100; background: #ccc;cursor: pointer;
      transition: 0.3s ease-in-out;
      &:hover {
        transform: rotate(30deg);
      }
    }
  }

  &-notification {
    position: relative;
    margin-right: 1.5rem;
    .badge--red{
      background-color: #fa3e3e;
      border-radius: 4px;
      text-align: center;
      font-size: 11px;
      padding: 0px 3px 0px 3px;
      color: #fff;
      position: absolute;
      margin: -8px 0 0 10px;
      opacity: 1;
    }
    .announce-menu {
      margin-left: 0;
      border-radius: 15px;
      right: -15px;
      top: 50px;
      width: 440px;
      left: unset;
      overflow: hidden;
      padding: 0 !important;

      h1 {
        margin: 0;
      }
    }

    .annouce-header {
      padding: 8px 0;
      background: #57D061;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .ann-header-left {
        display: flex;
        align-items: center;

        a {
          color: #333333;
          font-weight: 700;
          font-family: Montserrat;
        }
      }

      svg {
        margin: 0 10px;
      }

      .ann-header-right {
        display: flex;
        align-items: center;
        span{
         cursor: pointer;
        }
      }
    }

    .annouce-container::-webkit-scrollbar {
      display: none;
    }
    .annouce-container {
      overflow-y: scroll;
      height: 430px !important;
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
      .unread{
        background: #EFEFEF;
      }
      .annouce-item {
        padding: 15px 15px;
        border-bottom: 1px solid #E4E8EE;

        a {
          display: flex;
          padding-left: 5px;
          color: #000000;
        }

        img {
          width: 35px;
          height: 35px;
          border-radius: 50%;
        }

        .ann-item-content {
          margin-left: 20px;
          width: 90%;
          display: inline-block;
          .ann-title {
            display: inline-block;
            margin-bottom: 15px;
            color: #1A1F36;
          }
          .ann-create-time{
            color: #A5ACB8;
          }
        }
      }
    }
  }
}
.footer{
  line-height: 2;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column;
  font-family: Quicksand, Arial, sans-serif;
  background-color: #FFF;
  background-image: url("/assets/img/new_home/footer-bg.jpg");
  background-size: cover;
  background-position: center;
  color: $gray-100;
  padding: 50px 0 0 0;
  font-size: 15px;
  border-top: 0.5px solid #EEE;
  &-inner {
    display: flex;
  }
  a {
    text-decoration: none;
    color: $gray-100;
    &:hover,
    &:focus,
    &:active {
      color: $orange-100;
    }
  }
  .center-container{ width: 74vw; margin: 0 auto 20px auto; padding: 0 2vw;
    .general-infomation-box { padding-left: 30px }
    .col{padding: 0;}
    p{margin-bottom: 5px;}
    .footer-logo{height: 60px;}
    h3 {display: block; text-transform: uppercase; margin: 20px 0 15px; font-size: 18px;
      font-weight: normal; text-align: left; line-height: 1.2; color: $gray-100; }
    .links{width: 100%;line-height: 2;
      li{margin-bottom: 10px;}
      a{color: $gray-100; padding: 3px 0; font-weight: 100;text-decoration: none;
        &:hover{text-decoration: none; color: #FF6531;}
      }
    }
    .contact-box{
      padding-right: 80px;
      a{color: $gray-100; padding: 3px 0;text-decoration: none;
        &:hover{text-decoration: none; color: #FF6531;}
        i{color: $gray-100; margin-right: 5px;}
      }
    }
    .schedule{line-height: 1.8;}
  }
  .social-box{
    width: 70vw;
    margin: 20px auto 30px auto;
    display: flex;
    .box {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        height: 35px;
      }
    }
    .business {
      margin-right: auto;
    }
    .mobile-app {
      img {
        margin: 0 5px;
        &:first-child {
          margin-left: 0;
        }
      }
    }
    .social {
      img {
        width: 32px;
      }
      .social_link {
        margin-left: 25px;
      }
      .social-line2 {
        margin-top: 18px;
        text-align: center;
      }
    }
  }
  .copyright-box {
    width: 100vw;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid #FFF;
  }
}

@media (max-width: 1200px) {
  .header-inner #my-course .my-course-main .my-course-container .title-box {
    padding: 0 80px;
  }
}
