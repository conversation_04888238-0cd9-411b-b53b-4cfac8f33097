import api from "../config/api";
import axios from "axios";

export default {
  loadList: (data) =>
    new Promise((resolve, reject) => {
      api
        .get(
          `/get-video-list?page=${data.page}&per_page=${data.perPage}&name=${data.name}`
        )
        .then((response) => resolve(response.data))
        .catch((error) => reject(error.response.data));
    }),
  deleteVideo: (params) =>
    new Promise((resolve, reject) => {
      api
        .post(`/delete`, params)
        .then((response) => resolve(response.data))
        .catch((error) => reject(error.response.data));
    }),
  renderAgain: (params) =>
    new Promise((resolve, reject) => {
      api
        .post(`/render-again`, params)
        .then((response) => resolve(response.data))
        .catch((error) => reject(error.response.data));
    }),

  getToken: () =>
    new Promise((resolve, reject) => {
      api
        .get(`/get-token`)
        .then((response) => resolve(response.data))
        .catch((error) => reject(error.response.data));
    }),
  saveVideo: (params) =>
    new Promise((resolve, reject) => {
      api
        .post(`/upload`, params)
        .then((response) => resolve(response.data))
        .catch((error) => reject(error.response.data));
    }),

  upload: (form, uploadProgress) =>
    new Promise((resolve, reject) => {
      axios
        .post(`/api/admin/upload-video`, form, {
          withCredentials: false,
          baseURL: videoBaseURL,
          onUploadProgress: (progressEvent) =>
            uploadProgress(progressEvent.loaded / progressEvent.total),
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((response) => resolve(response.data))
        .catch((error) => reject(error.response.data));
    }),
};
