<template>
  <div>
    <div class="container">
      <div class="note">
        <el-alert title="Lưu ý" type="error" show-icon>
          <p><PERSON><PERSON> render lại KHÔNG cần tải lại mp4 file</p>
          <p>
            <PERSON><PERSON> render chỉ cần 720p hãy chọn chất lượng 720p trong tùy chọn chất
            lượng
          </p>
          <p>Khi tải lên video => Nếu trùng tên sẽ ghi đè</p>
        </el-alert>
      </div>
      <div class="flex justify-between items-center mt-4">
        <div class="flex items-center search-area">
          <el-input
            v-model="filters.name"
            prefix-icon="el-icon-search"
            placeholder="Tìm kiếm bằng tên"
            clearable
            @keyup.enter.native="getList"
          ></el-input>
          <button class="btn-search" @click="getList">Tìm kiếm</button>
        </div>
        <div class="items-center">
          <div class="upload-area">
            <input
              type="file"
              accept="video/mp4"
              v-on:change="onChangeFile"
              ref="uploadFile"
            />
            <button
              class="btn-upload"
              :disabled="isUploading"
              @click="onUpload"
            >
              Tải lên
            </button>
          </div>
          <div class="render-quality">
            <input
              type="radio"
              id="render-quality-720"
              v-model="quality"
              value="720"
            />
            <label for="render-quality-720">Chỉ render 720p trở xuống</label>
            <input
              type="radio"
              id="render-quality-1080"
              v-model="quality"
              value="1080"
            />
            <label for="render-quality-1080">Render 1080p</label>
          </div>
          <div>
            <el-progress
              v-if="upload_progress > 0"
              class="progress-bar"
              :text-inside="true"
              :stroke-width="26"
              :percentage="upload_progress"
            ></el-progress>
            <el-alert type="warning">
              <p>File MP4 có kích thước nhỏ hơn 16GB</p>
            </el-alert>
          </div>
        </div>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      height="75vh"
      style="width: 100%; margin-top: 10px"
      header-cell-class-name="user-table__header"
    >
      <el-table-column prop="id" label="ID" width="80"> </el-table-column>
      <el-table-column prop="name" label="Tên video"> </el-table-column>
      <el-table-column prop="render_type" label="Chất lượng"> </el-table-column>
      <el-table-column prop="history" label="Lịch sử">
        <template slot-scope="scope">
          <span class="text" v-html="scope.row.history"></span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="Trạng thái"> </el-table-column>
      <el-table-column prop="created_at" label="Ngày tạo"> </el-table-column>

      <el-table-column width="300">
        <template slot-scope="scope">
          <el-button
            v-if="type == 'choose'"
            size="mini"
            type="success"
            @click="onChooseFile(scope.row)"
            >Chọn file</el-button
          >
          <el-button size="mini" type="primary" @click="preview(scope.row)"
            >Xem thử</el-button
          >
          <el-button size="mini" type="primary" @click="renderAgain(scope.row)"
            >Render lại</el-button
          >
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)"
            >Xóa</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <render-again-dialog :entity="selectedVideoEntity" :handleClose="getList" />
    <delete-dialog :entity="selectedVideoEntity" :handleClose="getList" />
    <video-player :entity="selectedVideoEntity" />

    <div style="margin-top: 10px">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size.sync="perPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total_result"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import api from "../api";
import RenderAgainDialog from "../components/RenderAgainDialog.vue";
import DeleteDialog from "../components/DeleteDialog.vue";
import VideoPlayer from "../components/VideoPlayer.vue";

export default {
  components: { RenderAgainDialog, DeleteDialog, VideoPlayer },
  data() {
    return {
      page: 1,
      perPage: 20,
      loading: false,
      selectedVideoEntity: {},
      isUploading: false,
      filters: {
        name: "",
      },
      file: null,
      quality: "720",
      upload_progress: 0,
      type: this.$route.query.type,
    };
  },
  computed: {
    ...mapGetters("video", ["video_list", "total_result"]),
    tableData() {
      return this.video_list.map((video) => {
        switch (video.status) {
          case 1:
            video.status = "Đang xử lý";
            break;
          case 4:
            video.status = "Có lỗi";
            break;
          case 9:
            video.status = "Đã hoàn thành";
            break;
          default:
            video.status = "Mới thêm";
            break;
        }

        switch (video.render_type) {
          case 1:
            video.render_type = "720p";
            break;
          default:
            video.render_type = "1080p";
            break;
        }

        if (video.history) {
          const items = JSON.parse(video.history);
          let html = "";
          items.map((item) => {
            html += item.message + " - " + item.time + "<br>";
          });
          video.history = html;
        }
        return video;
      });
    },
  },
  async mounted() {
    await this.getList();
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    ...mapActions("video", [
      "getVideoList",
      "toggleDialogRenderAgain",
      "toggleDialogDelete",
      "toggleVideoPlayer",
    ]),
    async getList() {
      this.loading = true;
      const params = {
        ...this.filters,
        page: this.page,
        perPage: this.perPage,
      };
      await this.getVideoList(params);
      this.loading = false;
    },
    async handleCurrentChange(page) {
      await this.getList();
    },
    async handleSizeChange(perPage) {
      await this.getList();
    },
    renderAgain(entity) {
      this.selectedVideoEntity = entity;
      this.toggleDialogRenderAgain(true);
    },
    handleDelete(entity) {
      const self = this;
      self.selectedVideoEntity = entity;
      self.toggleDialogDelete(true);
    },
    onChangeFile(e) {
      this.file = null;
      let files = e.target.files || e.dataTransfer.files;
      if (!files.length) return;
      this.file = files[0];
    },
    uploadProgress(percentage) {
      this.upload_progress = Math.round(percentage * 100);
    },
    onUpload() {
      const self = this;
      if (!self.file) {
        self.$message({
          type: "error",
          message: "Hãy chọn file",
        });
        return;
      }

      const form = new FormData();
      form.append("file_upload", self.file);
      self.isUploading = true;

      api.video
        .getToken()
        .then((token) => {
          form.append("token", token);
          form.append("quality", self.quality);
          api.video
            .upload(form, self.uploadProgress)
            .then(() => {
              const videoName = self.file.name;
              api.video
                .saveVideo({ name: videoName, quality: self.quality })
                .then(() => {
                  self.$message({
                    type: "success",
                    message: "Đã tải lên thành công!",
                  });
                  self.file = null;
                  self.$refs.uploadFile.value = null;
                  self.getList();
                })
                .catch(() => {
                  self.$message({
                    type: "error",
                    message: "Có lỗi khi lưu file",
                  });
                })
                .finally(() => {
                  self.isUploading = false;
                });
            })
            .catch(() => {
              self.$message({
                type: "error",
                message: "Có lỗi khi upload",
              });
              self.isUploading = false;
            })
            .finally(() => {
              self.upload_progress = 0;
            });
        })
        .catch(() => {
          self.$message({
            type: "error",
            message: "Có lỗi khi lấy token",
          });
          self.isUploading = false;
        });
    },
    preview(entity) {
      this.selectedVideoEntity = entity;
      this.toggleVideoPlayer(true);
    },
    onChooseFile(entity) {
      window.opener.setUrlVideo(entity.name);
      window.close();
    },
  },
};
</script>
