import axios from "axios";

let http = axios.create({
  baseURL: "/backend/video/api",
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Response interceptor
http.interceptors.response.use(
  (response) => response,
  (error) => {
    const { status } = error.response || {};
    if (status >= 500) {
    }
    if (status === 401) {
      // 401 Unauthorized
      window.location.href = `${process.env.APP_URL}/backend/dang-nhap`;
    }

    return Promise.reject(error);
  }
);
export default http;
