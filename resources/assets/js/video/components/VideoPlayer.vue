<template>
  <div
    class="video-player"
    :style="{ display: show_video_player ? 'block' : 'none' }"
  >
    <video ref="videoPlayer" controls class="video-js" width="500"></video>
    <div class="action">
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose">Đóng</el-button>
      </span>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import "video.js/dist/video-js.min.css";
import "video.js/dist/video.min.js";
import videojs from "video.js";

export default {
  props: ["entity"],
  computed: {
    ...mapGetters("video", ["show_video_player"]),
  },
  data() {
    return {
      options: {},
      player: null,
    };
  },
  watch: {
    entity: function (newVal, oldVal) {
      this.player.src({
        src: `${videoServerURL}/${newVal.render_type}/${newVal.name}/index.m3u8`,
      });
      if (this.show_video_player) {
        this.player.play();
      }
    },
  },
  mounted() {
    this.player = videojs(this.$refs.videoPlayer, this.options, () => {
      this.player.log("onPlayerReady", this);
    });
  },
  beforeDestroy() {
    if (this.player) {
      this.player.dispose();
    }
  },
  methods: {
    ...mapActions("video", ["toggleVideoPlayer"]),
    onClose() {
      this.player.pause();
      this.toggleVideoPlayer(false);
    },
  },
};
</script>
