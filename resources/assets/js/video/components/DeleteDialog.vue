<template>
    <el-dialog
      title="Cảnh báo xóa video"
      :visible="show_dialog_delete"
      width="30%"
      :before-close="onClose"
    >
      <div class="render-again">
        <p>Bạn muốn xóa video?</p>
        <p class="video-name">Tên video: {{ entity.name }}</p>
        <div class="options">
          <el-form ref="form" :model="form">
            <el-form-item label="Chất lượng muốn xóa">
              <el-radio-group v-model="form.quality">
                <el-radio :label="0">Tất cả</el-radio>
                <el-radio :label="1">Chỉ xóa mp4</el-radio>
                <el-radio :label="2">1080p</el-radio>
                <el-radio :label="3">720p</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <el-alert type="warning" show-icon>
            <p>Dữ liệu KHÔNG thể khôi phục sau khi xóa.</p>
            <p>
              Ch<PERSON><PERSON> chất lượng nếu muốn xóa chỉ một chất lượng
            </p>
          </el-alert>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose">Đóng</el-button>
        <el-button type="primary" @click="onConfirm">OK</el-button>
      </span>
    </el-dialog>
  </template>
  
  <script>
  import { mapActions, mapGetters } from "vuex";
  import api from "../api";
  
  export default {
    props: ["entity", "handleClose"],
    computed: {
      ...mapGetters("video", ["show_dialog_delete"]),
    },
    data() {
      return {
        form: {
          quality: 0,
        },
      };
    },
    methods: {
      ...mapActions("video", ["toggleDialogDelete"]),
      onClose() {
        this.toggleDialogDelete(false);
      },
      onConfirm() {
        const self = this;
        api.video
          .deleteVideo({
            video_id: self.entity.id,
            quality: self.form.quality,
          })
          .then(() => {
            self.$message({
              type: "success",
              message:
                "Đã xóa thành công video!",
            });
          })
          .catch(() => {
            self.$message({
              type: "error",
              message: "Có lỗi xảy ra, vui lòng thử lại",
            });
          })
          .finally(() => {
            self.handleClose();
            self.onClose();
          });
      },
    },
  };
  </script>
  