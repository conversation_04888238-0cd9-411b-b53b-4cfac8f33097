<template>
  <el-dialog
    title="Render lại video"
    :visible="show_dialog_render_again"
    width="30%"
    :before-close="onClose"
  >
    <div class="render-again">
      <p>Bạn muốn render lại video chứ?</p>
      <p class="video-name">Tên video: {{ entity.name }}</p>
      <div class="options">
        <el-form ref="form" :model="form">
          <el-form-item label="Chất lượng">
            <el-radio-group v-model="form.quality">
              <el-radio :label="0">T<PERSON>t cả</el-radio>
              <el-radio :label="1">Chỉ 720p trở xuống</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-alert type="warning" show-icon>
          <p>
            File HLS sẽ bị xóa và render lại. Nếu chỉ chọn chất lượng 720p thì
            chất lượng 1080p sẽ bị xóa.
          </p>
        </el-alert>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">Đóng</el-button>
      <el-button type="primary" @click="onConfirm">OK</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import api from "../api";

export default {
  props: ["entity", "handleClose"],
  computed: {
    ...mapGetters("video", ["show_dialog_render_again"]),
  },
  data() {
    return {
      form: {
        quality: 0,
      },
    };
  },
  methods: {
    ...mapActions("video", ["toggleDialogRenderAgain"]),
    onClose() {
      this.toggleDialogRenderAgain(false);
    },
    onConfirm() {
      const self = this;
      api.video
        .renderAgain({
          video_id: self.entity.id,
          quality: self.form.quality,
        })
        .then(() => {
          self.$message({
            type: "success",
            message:
              "Video đang trong quá trình render. Sẽ có thông báo khi hoàn thành.",
          });
        })
        .catch(() => {
          self.$message({
            type: "error",
            message: "Có lỗi xảy ra, vui lòng thử lại",
          });
        })
        .finally(() => {
          self.handleClose();
          self.onClose();
        });
    },
  },
};
</script>
