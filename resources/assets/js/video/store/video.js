import Vue from "vue";
import api from "../api";
import * as types from "./mutation-types";
const video = {
  namespaced: true,
  state: {
    video_list: [],
    total_result: 0,
    show_dialog_render_again: false,
    show_dialog_delete: false,
    show_video_player: false,
  },
  getters: {
    video_list: (state) => state.video_list,
    total_result: (state) => state.total_result,
    show_dialog_render_again: (state) => state.show_dialog_render_again,
    show_dialog_delete: (state) => state.show_dialog_delete,
    show_video_player: (state) => state.show_video_player,
  },
  mutations: {
    [types.SET_VIDEO_LIST](state, data) {
      state.video_list = data.data;
      state.total_result = data.total;
    },
    [types.SET_SHOW_DIALOG_RENDER_AGAIN](state, payload) {
      state.show_dialog_render_again = payload;
    },
    [types.SET_SHOW_DIALOG_DELETE](state, payload) {
      state.show_dialog_delete = payload;
    },
    [types.SET_SHOW_VIDEO_PLAYER](state, payload) {
      state.show_video_player = payload;
    },
  },
  actions: {
    getVideoList({ commit }, data) {
      return api.video
        .loadList(data)
        .then((response) => {
          commit(types.SET_VIDEO_LIST, response.data);
        })
        .catch();
    },
    toggleDialogRenderAgain({ commit }, payload) {
      commit(types.SET_SHOW_DIALOG_RENDER_AGAIN, payload);
    },
    toggleDialogDelete({ commit }, payload) {
      commit(types.SET_SHOW_DIALOG_DELETE, payload);
    },
    toggleVideoPlayer({ commit }, payload) {
      commit(types.SET_SHOW_VIDEO_PLAYER, payload);
    },
  },
};

export default video;
