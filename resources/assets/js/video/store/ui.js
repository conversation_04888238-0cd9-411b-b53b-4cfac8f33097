import Vue from "vue";
import api from "../api";
import * as types from "./mutation-types";
const ui = {
  namespaced: true,
  state: {
    title: "Quản lý video",
  },
  getters: {
    title: (state) => state.title,
  },
  mutations: {
    [types.SET_TITLE_PAGE](state, data) {
      state.title = data;
    },
  },
  actions: {
    setTitlePage({ commit }, data) {
      commit("SET_TITLE_PAGE", data);
    },
  },
};

export default ui;
