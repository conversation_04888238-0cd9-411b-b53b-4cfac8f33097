<template>
  <div>
    <div class="flashcard-wrap">
      <!--    overview-->
      <div v-if="currentView === TYPES_VIEW.OVERVIEW"
           class="flex flex-col justify-center items-center w-[90%] max-w-[1000px]">
        <div style="background: linear-gradient(to bottom, #FFFFFF, #F4F5FA);"
             class="w-full text-center p-4 rounded-3xl mb-6">
          <div class="img mb-7">
            <img src="/images/vocabulary/img-over-view-course.svg" alt=""/>
          </div>
          <div class="title text-center text-[#07403F] ">
            <div class="font-beanbag-medium text-[20px] uppercase">Bộ từ vựng</div>
            <div class="font-zuume-semibold text-[48px] text-bold uppercase">jlpt n5</div>
            <div class="font-averta-regular text-[20px]">Học và ghi nhớ 1122 từ vựng thuộc cấp độ N5</div>
          </div>
        </div>

        <div class="w-full">
          <div v-for="i in 4" class="max-w-[420px] mx-auto progress-item flex justify-between items-center mb-4">
            <div class="font-averta-regular text-[20px] text-[#1E1E1E]">Đã học</div>
            <progress-bar :percent="i * 20"/>
            <div class="font-averta-regular text-[20px] text-[#757575]">{{ i * 20 }}%・{{ i * 280 }} từ</div>
          </div>
        </div>

        <div class="w-[340px]">
          <div
              class="w-full text-xl text-[#07403F] font-beanbag rounded-full p-3 mt-10 flex justify-between items-center border-[1px] border-[#07403F] cursor-pointer"
              @click="currentView = TYPES_VIEW.SEARCH">
            <div class="text-[#07403F] font-averta-regular text-xl">
              Danh sách toàn bộ từ
            </div>
            <div>
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.5 4H7.5M7.5 4L4 0.5M7.5 4L4 7.5" stroke="#07403F" stroke-linecap="round"
                      stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div class="text-center font-averta-regular text-xl text-[#757575] italic mt-3 text-[#EF6D13]">
            *Học thử 300 từ đầu tiên trong bộ từ vựng JLPT N5
          </div>
        </div>

        <button
            class="w-full bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer"
        >
          học ngay
        </button>
      </div>

      <div v-if="currentView === TYPES_VIEW.SEARCH">
        <div class="flex justify-between items-center mb-10">
          <div class="flex items-center">
            <div class="vocabulary-search-btn-back h-[65px] w-[65px]" @click="currentView = TYPES_VIEW.OVERVIEW">
              <i class="fas fa-arrow-left"></i>
            </div>
            <div class="vocabulary-section-favorite-title">
              <div class="flex font-beanbag-medium text-[24px] text-[#07403F] items-center">
                Từ vựng yêu thích
                <i class="fas fa-heart text-[#FF7C79] ml-2"></i>
              </div>
              <div class="font-beanbag-regular text-[20px] text-[#07403F]">1122 từ</div>
            </div>
          </div>
          <div
              class="vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]">
            <div class="search-icon">
              <i class="fas fa-search"></i>
            </div>
            <input
                type="text"
                v-model="searchQuery"
                placeholder="Nhập từ vựng muốn tìm kiếm"
                class="search-input leading-[65px]"
            />
          </div>
        </div>

        <div class="search-result list-favorite" v-if="searchResults.length > 0">
          <div class="search-result-items">
            <div class="search-result-item cursor-pointer relative" v-for="(item, index) in searchResults" :key="index">
              <div class="search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] pt-3 pb-3"
                   @click="alert(1)">
                <div class="search-result-item-title font-gen-jyuu-gothic-medium text-bold text-black text-[20px]">
                  {{ item.word }}
                </div>
                <div class="search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]">
                  {{ item.description }}
                </div>
                <div
                    class="search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1">
                  {{ item.specialized }}
                </div>
              </div>
              <!--              overlay làm mờ và có biểu tượng khóa nếu từ có giá trị lock = true-->
              <div v-if="item.lock" class="absolute h-full top-0 left-0 w-full flex items-center"
                   style="background: linear-gradient(to right, rgba(234, 246, 235, 1), rgba(255, 255, 255, 0));">
                <i class="fas fa-lock text-white text-4xl text-[#757575] ml-[32px]"></i>
              </div>
            </div>
          </div>
        </div>

        <button
            class="fixed bottom-[56px] left-[50%] transform w-full bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] w-[340px] cursor-pointer"
            @click="currentView = TYPES_VIEW.STACKED_CARD"
        >
          bắt đầu
        </button>
      </div>

      <div v-if="currentView === TYPES_VIEW.STACKED_CARD" class="flashcards-wrap">

        <div class="mx-auto w-[80%] max-w-[648px]">
          <div
              class="tag_card rounded-full px-2 py-1 bg-[#14AE5C] font-beanbag-medium text-white text-base flex items-center mb-4"
              style="line-height: 1; width: fit-content; align-self: flex-start;">
            Từ vựng ôn lại
          </div>
        </div>




        <div class="cards-wrap content mx-auto w-[80%] max-w-[648px]">

          <div class="content-wrap mx-auto mb-6">
            <div id="stacked-cards-block" class="stackedcards stackedcards--animatable a_cursor--pointer">
              <div class="stackedcards-container" style="margin-bottom: 20px;">
                <div
                    v-for="(card, index) in dataFlashCard"
                    :key="card.id"
                    :data-id="card.id"
                    class="card-item"
                    :class="{
                  'stackedcards-active': index === 0,
                  'stackedcards-top': true,
                  'stackedcards--animatable': true,
                  'stackedcards-origin-top': true
                }"
                >
                  <div class="card-inner" :class="{ 'flip': !isJapanese }" :data-id="card.id">
                    <div class="card__face card__face--jp card__face--front">
                      <div class="card-wrap p-4 h-[90%] overflow-y-auto relative">
                        <div class="card_header flex items-center">
                          <div v-if="card.value.audio"
                               class="card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer"
                               @click="playAudio(card.id, card.value.audio)"
                          >
                            <svg class="noFlip" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                                  fill="#4E87FF"/>
                              <path
                                  d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                                  fill="#4E87FF"/>
                              <path opacity="0.4"
                                    d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                    fill="#4E87FF"/>
                              <path
                                  d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                                  fill="#4E87FF"/>
                            </svg>
                          </div>
                          <div class="card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]"
                               v-html="card.value.word_stress">
                          </div>
                        </div>
                        <div class="card_content min-h-[calc(100%-34px)] flex flex-col">
                          <div
                              class="content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex"
                              style="flex-grow: 1;"
                              v-html="card.value.word">
                          </div>
                          <div v-if="card.value.front_image" class="content-img text-center p-[40px]">
                            <img
                                :src="`https://video-test.dungmori.com/images/${card.value.front_image}`">
                          </div>
                          <div v-if="card.value.example.length" class="example-wrap">
                            <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                              Ví dụ
                            </p>
                            <div class="list-example">
                              <template v-for="(example, index) in card.value.example">
                                <div class="example-item flex items-start mb-1">
                                  <svg v-if="example.audio" class="w-[36px] h-[36px] noFlip cursor-pointer" width="24"
                                       height="24" viewBox="0 0 24 24" fill="none"
                                       xmlns="http://www.w3.org/2000/svg"
                                       @click="playAudio(card.id + '_example_' + index, example.audio)">
                                    <path
                                        d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                                        fill="#4E87FF"/>
                                    <path
                                        d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                                        fill="#4E87FF"/>
                                    <path opacity="0.4"
                                          d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                          fill="#4E87FF"/>
                                    <path
                                        d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                                        fill="#4E87FF"/>
                                  </svg>
                                  <div class="ml-2 font-beanbag-regular text-2xl flex items-start w-[90%]">
                                    {{ index + 1 }}. <span class="ml-2" v-html="example.example"></span>
                                  </div>
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]">
                        <svg class="m-3 noFlip" width="33" height="28" viewBox="0 0 33 28" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                          <path
                              d="M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z"
                              fill="#FF7C79" stroke="#FF7C79"/>
                        </svg>
                      </div>
                    </div>
                    <div class="card__face card__face--vi card__face--back">
                      <div class="card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative">
                        <div
                            class="card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5"
                            style="flex-grow: 1"
                        >
                          <div class="text-center" v-html="card.value.meaning"></div>
                        </div>
                        <div class="font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]"
                             v-html="card.value.kanji_meaning"></div>
                        <div class="card_img_back p-[40px] content-img text-center" v-if="card.value.back_image">
                          <img
                              :src="`https://video-test.dungmori.com/images/${card.value.back_image}`">
                        </div>
                        <div v-if="card.value.meaning_example.length" class="example-wrap">
                          <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                            Ví dụ
                          </p>
                          <div class="list-example">
                            <template v-for="(meaning_example, index) in card.value.meaning_example">
                              <div class="example-item flex items-center mb-1">
                                <div class="ml-2 font-averta-regular text-2xl flex items-start">
                                  {{ index + 1 }}. <span class="ml-1" v-html="meaning_example"></span>
                                </div>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                      <div class="how-remember-wrap px-4 h-[40%] flex flex-col">
                        <div class="how-remember-wrap-header flex  mb-5">
                          <div class="font-beanbag-medium text-[#757575] text-xl">
                            Cách nhớ
                          </div>
                          <div class="border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"></div>
                        </div>
                        <div v-if="card.comment && card.comment.user_info"
                             class="how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"
                             style="">
                          <div class="how-remember-wrap-avatar w-[28px]">
                            <img class="rounded-full"
                                 :src="`/cdn/avatar/small/${card.comment.user_info.avatar}`">
                          </div>
                          <div class="how-remember-wrap-info flex text-[#073A3B]">
                          <span class="font-averta-bold">
                            {{ card.comment.user_info.name }}・
                          </span>
                            <span class="font-averta-regular">
                            {{ card.comment.time_created }}
                          </span>
                            <svg v-if="card.comment.pin" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z"
                                  fill="#B3B3B3"/>
                            </svg>
                          </div>
                          <div class="col-start-2 font-averta-regular text-[#07403F]" style="display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;"
                          >
                            {{ card.comment.content }}
                          </div>
                          <div class="col-start-2 flex justify-between items-center">
                            <div class="font-averta-regular text-[#009951] flex">
                              <div class="flex items-center mr-5">
                                <svg class="mr-1 noFlip" width="13" height="12" viewBox="0 0 13 12" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                  <path
                                      d="M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z"
                                      :fill="card.comment.comment_like.length ? '#009951' : 'none'" stroke="#009951"/>
                                </svg>
                                {{ card.comment.count_like }}
                              </div>
                              <div v-if="card.comment && card.comment.replies">
                                {{ card.comment.replies.length }} Trả lời
                              </div>
                            </div>
                            <div class="underline decoration-solid text-[#009951] cursor-pointer noFlip"
                                 @click="toggleCommentTab('open')">
                              Xem thêm >>
                            </div>
                          </div>
                        </div>
                        <div v-else
                             class="items-center flex grow "
                             style="flex-grow: 1;">
                          <div
                              class="underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip"
                              @click="toggleCommentTab('open')">
                            Đóng góp cách nhớ của bạn >>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                  class="stackedcards--animatable stackedcards-overlay left stackedcards-origin-top font-averta-bold text-[#975102]">
                Chưa nhớ
              </div>
              <div
                  class="stackedcards--animatable stackedcards-overlay right stackedcards-origin-top font-averta-bold text-[#02542D]">
                Đã học
              </div>
            </div>
          </div>
        </div>


      </div>

    </div>
  </div>
</template>

<script>

import ProgressBar from "../../component/ProgressBar.vue";
import FlashCardModule from "../../components/FlashCardModule.vue";
import axios from "axios";
import StackedCards from "../../module/flashcard";

const TYPES_VIEW = {
  OVERVIEW: 1,
  SEARCH: 2,
  STACKED_CARD: 3,
  RESULT: 4,
}

const searchResults = [
  {id: 1, word: "もり", description: "Rừng, rừng rậm", specialized: "JLPT N5"},
  {id: 2, word: "はな", description: "Lá", specialized: "JLPT N4"},
  {id: 3, word: "きのう", description: "Hôm qua", specialized: "Chuyên ngành Thực phẩm", lock: true},
  {id: 4, word: "あした", description: "Ngày mai", specialized: "Chuyên ngành Xây dựng", lock: true},
  {id: 5, word: "もり", description: "Rừng, rừng rậm", specialized: "JLPT N5", lock: true},
  {id: 6, word: "はな", description: "Lá", specialized: "JLPT N4", lock: true},
  {id: 7, word: "きのう", description: "Hôm qua", specialized: "Chuyên ngành Thực phẩm", lock: true},
  {id: 8, word: "あした", description: "Ngày mai", specialized: "Chuyên ngành Xây dựng", lock: true},
  {id: 9, word: "もり", description: "Rừng, rừng rậm", specialized: "JLPT N5", lock: true},
  {id: 10, word: "はな", description: "Lá", specialized: "JLPT N4", lock: true},
  {id: 11, word: "きのう", description: "Hôm qua", specialized: "Chuyên ngành Thực phẩm", lock: true},
  {id: 12, word: "あした", description: "Ngày mai", specialized: "Chuyên ngành Xây dựng", lock: true},
]

const dataFlashcard = [
  {
    "id": 103570,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p><span style=\"color:#ff9900\"><ruby>日本</ruby></span></p><p><ruby><rt><span style=\"color:#ff9900\">にほん</span></rt></ruby><span style=\"color:#ff9900\">の&nbsp;</span></p>",
      "word_stress": "<p><span style=\"text-decoration:overline\">にほん</span>の</p>",
      "word_type": "Danh từ",
      "meaning": "<p>NHẬT</p><p>Tiếng Nhật của bạn c&oacute; tốt kh&ocirc;ng thế</p>",
      "audio": "2025-04-11/sample-3s.mp3",
      "front_image": "2025-04-11/WebStore-ld4_0.png",
      "back_image": null,
      "example": [
        {
          "example": "<p><span style=\"color:#ef6d13\"><span style=\"text-decoration:overline\">特定原</span></span>材料とくてい<span style=\"text-decoration:overline\">げん</span>ざいりょうに準<span style=\"text-decoration:overline\">じゅんず</span>るもの</p>",
          "audio": "2025-04-11/sample-6s.mp3"
        },
        {
          "example": "<p><ruby>機器<rt>きき</rt></ruby>や<ruby>器具</ruby></p>",
          "audio": "2025-04-11/sample-3s.mp3"
        }
      ],
      "meaning_example": [
        "<p>B&aacute;c của t&ocirc;i l&agrave; gi&aacute;o vi&ecirc;n dạy<span style=\"color:#ef6d13\"> tiếng Nhật&nbsp;</span>tại trung t&acirc;m Nhật ngữ Dũng Mori ieo EOKJ JNF kf lkp;wpe njjgbj; hhe kjijwi&nbsp;</p>",
        "<p>Học tiếng Nhật c&oacute; kh&oacute; kh&ocirc;ng?</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 1,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-05-13T01:14:59.000000Z",
    "created_at": "2025-04-11T07:57:41.000000Z",
    "answers": [],
    "comment": {
      "id": 463576,
      "content": "cmt 1",
      "user_id": 540422,
      "count_like": 3,
      "created_at": "2025-04-29 07:26:47",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103570,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Quỳnh Anh",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540422
      },
      "time_created": "29/04/2025 07:26",
      "replies": [
        {
          "id": 463577,
          "table_id": 103570,
          "table_name": "flashcard",
          "user_id": 540422,
          "admin_log": null,
          "kwadmin_id": null,
          "content": "124",
          "img": null,
          "audio": null,
          "rate": 0,
          "is_tester": 0,
          "parent_id": 463576,
          "count_like": 2,
          "ulikes": null,
          "readed": 0,
          "tag_data": null,
          "status": 0,
          "pin": 0,
          "is_correct": 0,
          "updated_at": "2025-04-29 07:27:03",
          "created_at": "2025-04-29 07:27:03",
          "user_info": {
            "email": "<EMAIL>",
            "name": "Quỳnh Anh",
            "avatar": "1711587724_0_76076.jpeg",
            "userId": 540422
          },
          "time_created": "29/04/2025 07:27",
          "replies": null,
          "table_info": {
            "id": "",
            "course_id": "",
            "name": "",
            "SEOurl": "",
            "course_url": ""
          }
        }
      ],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": []
    }
  },
  {
    "id": 103571,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p>はな</p>",
      "word_stress": "<p><span style=\"text-decoration:overline\">は</span>な</p>",
      "word_type": "Danh từ",
      "meaning": "<p>Hoa</p>",
      "audio": null,
      "front_image": null,
      "back_image": "2025-04-11/1712378501768.jpg",
      "example": [
        {
          "example": "<p>は他たの</p>",
          "audio": null
        },
        {
          "example": "<p>なった疑うたがい</p>",
          "audio": null
        }
      ],
      "meaning_example": [
        "<p>Đi ăn cơm</p>",
        "<p>Học b&agrave;i đến 10h tối.</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 2,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-11T07:59:29.000000Z",
    "created_at": "2025-04-11T07:59:29.000000Z",
    "answers": [],
    "comment": {
      "id": 463284,
      "content": "bdbbdb",
      "user_id": 540308,
      "count_like": 3,
      "created_at": "2025-04-15 15:29:28",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103571,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Thu Vũ",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540308
      },
      "time_created": "15/04/2025 15:29",
      "replies": [],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": [
        {
          "comment_id": 463284,
          "user_id": 540308
        }
      ]
    }
  },
  {
    "id": 103572,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p><ruby>作業<rt>さぎょう</rt></ruby>ス</p>",
      "word_stress": "<p><span style=\"text-decoration:overline\">作業さ</span>ぎょうス</p>",
      "word_type": null,
      "meaning": "<p>Cuộc sống</p>",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [
        {
          "example": "<p>作業さぎ</p>",
          "audio": null
        }
      ],
      "meaning_example": [
        "<p>Cuộc sống ở Nhật</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 3,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-11T08:00:45.000000Z",
    "created_at": "2025-04-11T08:00:45.000000Z",
    "answers": [],
    "comment": {
      "id": 463306,
      "content": "hello",
      "user_id": 540308,
      "count_like": 0,
      "created_at": "2025-04-19 15:34:10",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103572,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Thu Vũ",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540308
      },
      "time_created": "19/04/2025 15:34",
      "replies": [],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": []
    }
  },
  {
    "id": 103573,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p>ならないよう</p>",
      "word_stress": "<p>なら<span style=\"text-decoration:overline\">ない</span>よう</p>",
      "word_type": "Động từ",
      "meaning": "<p>Th&uacute; vị</p>",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [],
      "meaning_example": [],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 4,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-11T08:01:41.000000Z",
    "created_at": "2025-04-11T08:01:41.000000Z",
    "answers": [],
    "comment": {
      "id": 463604,
      "content": "fgfg",
      "user_id": 540308,
      "count_like": 0,
      "created_at": "2025-05-09 09:08:20",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103573,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Thu Vũ",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540308
      },
      "time_created": "09/05/2025 09:08",
      "replies": [],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": []
    }
  },
  {
    "id": 103574,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p>わたしたち</p>",
      "word_stress": "<p><span style=\"text-decoration:overline\">わたし</span>たち</p>",
      "word_type": "Danh từ",
      "meaning": "<p>Ch&uacute;ng t&ocirc;i</p>",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [
        {
          "example": "<p>わたしたちはズンもりのがくせいです。</p>",
          "audio": null
        }
      ],
      "meaning_example": [
        "<p>Ch&uacute;ng t&ocirc;i l&agrave; người Việt Nam</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 5,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-11T08:03:10.000000Z",
    "created_at": "2025-04-11T08:03:10.000000Z",
    "answers": [],
    "comment": {
      "id": 463322,
      "content": "25346666",
      "user_id": 540308,
      "count_like": 0,
      "created_at": "2025-04-21 11:24:47",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103574,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Thu Vũ",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540308
      },
      "time_created": "21/04/2025 11:24",
      "replies": [],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": []
    }
  },
  {
    "id": 103575,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p>かれ</p>",
      "word_stress": null,
      "word_type": null,
      "meaning": "<p>Anh ấy, bạn trai</p>",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [
        {
          "example": "<p>かれはにほんじんですか。</p>",
          "audio": null
        }
      ],
      "meaning_example": [
        "<p>Anh ấy l&agrave; người Nhật &agrave;?</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 6,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-11T08:04:15.000000Z",
    "created_at": "2025-04-11T08:04:15.000000Z",
    "answers": [],
    "comment": {
      "id": 463276,
      "content": "ggg",
      "user_id": 540309,
      "count_like": 0,
      "created_at": "2025-04-15 14:42:52",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103575,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Dinhsuu",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540309
      },
      "time_created": "15/04/2025 14:42",
      "replies": [],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": []
    }
  },
  {
    "id": 103576,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p>あなた</p>",
      "word_stress": "<p>あ<span style=\"text-decoration:overline\">な</span>た</p>",
      "word_type": "Danh từ",
      "meaning": "<p>Anh/ chị/ &ocirc;ng/ b&agrave;, bạn (ng&ocirc;i thứ 2 số &iacute;t)</p>",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [
        {
          "example": "<p>あなたはベトナムじんですか。</p>",
          "audio": null
        }
      ],
      "meaning_example": [
        "<p>Bạn l&agrave; người Việt Nam phải kh&ocirc;ng?</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 7,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-21T03:57:27.000000Z",
    "created_at": "2025-04-11T08:05:08.000000Z",
    "answers": [],
    "comment": {
      "id": 463320,
      "content": "hfh",
      "user_id": 540416,
      "count_like": 0,
      "created_at": "2025-04-21 10:58:39",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103576,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "2104",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540416
      },
      "time_created": "21/04/2025 10:58",
      "replies": [],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": []
    }
  },
  {
    "id": 103577,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p>おくさん</p>",
      "word_stress": null,
      "word_type": null,
      "meaning": "<p>Vợ (người kh&aacute;c)<br />lưu &yacute;: khi muốn hỏi hay giới thiệu vợ của ai đ&oacute; th&igrave; sử dụng từ n&agrave;y.</p>",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [
        {
          "example": "<p>かれのおくさんはズンもりの　せんせいです。</p>",
          "audio": null
        }
      ],
      "meaning_example": [
        "<p>Vợ của anh ấy l&agrave; gi&aacute;o vi&ecirc;n dạy tiếng Nhật c&ugrave;ng trung t&acirc;m với t&ocirc;i</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 8,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-11T08:23:53.000000Z",
    "created_at": "2025-04-11T08:23:53.000000Z",
    "answers": [],
    "comment": null
  },
  {
    "id": 103578,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<p>どなた</p>",
      "word_stress": null,
      "word_type": null,
      "meaning": "<p>Vị n&agrave;o</p>",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [
        {
          "example": "<p>ゆかせんせいはどなたですか。</p>",
          "audio": null
        }
      ],
      "meaning_example": [
        "<p>C&ocirc; Yuka l&agrave; ai thế?</p>"
      ],
      "quiz_question": [],
      "kanji_meaning": null
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 9,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-11T08:24:51.000000Z",
    "created_at": "2025-04-11T08:24:51.000000Z",
    "answers": [],
    "comment": null
  },
  {
    "id": 103598,
    "lesson_id": 10847,
    "exam_part_id": null,
    "skill": 1,
    "mondai_id": null,
    "type": 17,
    "type_ld": null,
    "server": null,
    "video_name": null,
    "video_title": null,
    "value": {
      "word": "<ruby>私<rt>わたし</rt></ruby>たち",
      "word_stress": "<span style=\"text-decoration:overline\">わたし</span>たち",
      "word_type": "Danh từ",
      "meaning": "Chúng tôi",
      "audio": null,
      "front_image": null,
      "back_image": null,
      "example": [
        {
          "example": "<span style=\"color:#EF6D13\">わたしたち</span>はがくせいです。",
          "audio": null
        }
      ],
      "meaning_example": [
        "<span style=\"color:#EF6D13\">Chúng tôi</span> là học sinh."
      ],
      "quiz_question": [],
      "kanji_meaning": "Hoa"
    },
    "suggest": null,
    "explain": null,
    "explain_mp3": null,
    "value_data": null,
    "grade": "",
    "sort": 10,
    "show": 1,
    "is_quiz": 0,
    "video_full": 0,
    "updated_at": "2025-04-21T03:54:16.000000Z",
    "created_at": "2025-04-21T00:04:31.000000Z",
    "answers": [],
    "comment": {
      "id": 463321,
      "content": "123",
      "user_id": 540308,
      "count_like": 0,
      "created_at": "2025-04-21 11:24:28",
      "pin": 0,
      "parent_id": 0,
      "table_id": 103598,
      "table_name": "flashcard",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Thu Vũ",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540308
      },
      "time_created": "21/04/2025 11:24",
      "replies": [],
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      },
      "comment_like": []
    }
  }
]

export default {
  components: {
    ProgressBar,
    // FlashCardModule
  },
  props: ["courseId"],
  name: "FlashCard",
  watch: {
    currentView(newVal, oldVal) {
      console.log(`currentView: `, newVal);
      if (newVal === TYPES_VIEW.STACKED_CARD) {
        this.initStackedCards();
      }
    }
  },
  data() {
    return {
      listVocabulary: [],
      TYPES_VIEW: TYPES_VIEW,
      currentView: TYPES_VIEW.OVERVIEW,
      searchResults: searchResults,
      searchQuery: '',
      dataFlashCard: dataFlashcard,
      isJapanese: true,
    };
  },
  created() {
    this.getListVocabulary();
  },
  methods: {
    getListVocabulary() {
      this.listVocabulary = this.listVocabulary.filter(item => item.course_id === this.courseId);
    },
    playAudio(card_id, audioUrl) {
      console.log(`Play audio for card id: ${card_id}, url: ${audioUrl}`);
    },
    toggleCommentTab(handle = 'open', extraData = {}) {
      console.log(`mo tab cmt with handle: ${handle}`);

      const currentCard = this.dataFlashcard[0];

      const eventData = {
        handle: handle,
        flashcard: currentCard,
        ...extraData
      };

      this.$emit('open-comment-tab', eventData);

      const customEvent = new CustomEvent('open-comment-tab', {detail: eventData});
      document.dispatchEvent(customEvent);
    },
    initStackedCards() {
      this.$nextTick(() => {
        this.stackedCardsInstance = new StackedCards({
          visibleItems: 3,
          margin: 22,
          rotate: true,
          useOverlays: true
        });
      });

      this.updateCurrentFlashcard();
    },
    updateCurrentFlashcard() {
      if (!this.stackedCardsInstance) return;

      const currentPos = this.stackedCardsInstance.currentPosition;
      const currentCard = this.dataFlashcard[currentPos];

      if (currentCard) {
        console.log('Current flashcard updated:', currentCard);

        // Tìm component Container và gọi phương thức setCurrentFlashcard
        const containerComponent = this.$parent.$children.find(child => child.$options.name === 'container');
        if (containerComponent) {
          containerComponent.setCurrentFlashcard(currentCard);
        } else {
          console.error('Container component not found');
        }
      }
    }
  },
  mounted() {
    // this.initStackedCards();
    // check window focus
    window.onfocus = function() {
      console.log('window is focused');
    };
    window.onblur = function() {
      console.log('window is blurred');
    };
  }
}
</script>

<style scoped>
</style>
