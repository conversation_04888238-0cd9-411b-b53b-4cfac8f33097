new Vue({
  el: "#vocabulary_content",
  data: {
    list_vocabulary: list_vocabulary,
  },
  components: {
    vocabulary: require("./Index.vue").default,
  },
  methods: {
    showContent: function (type) {
      //hiển thị nội dung của section
      $(".content-section").removeClass("active");
      $("#content-" + type).addClass("active");

      //ẩn menu
      $("#navMenu").removeClass("active");
    },
  },
});
