<template>
  <div class="p-5">
    <div
      class="text-xs hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300"
    >
      Ti<PERSON><PERSON> đ<PERSON>
    </div>
    <el-input
      class="mt-3"
      placeholder="Nhập tiêu đề"
      v-model="formData.title"
      clearable
    >
    </el-input>
    <div
      class="mt-3 text-xs hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300"
    >
      Số tiền
    </div>
    <div class="mt-3 flex items-center">
      <el-input-number
        size="medium"
        placeholder="Nhập số tiền"
        v-model="formData.price"
        :controls="false"
      >
      </el-input-number>
      <strong class="ml-3 cursor-pointer"> VNĐ </strong>
    </div>
    <div
      class="mt-3 text-xs hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300"
    >
      Phân loại
    </div>
    <el-radio-group class="mt-2" v-model="formData.type" size="medium">
      <el-radio-button label="1">Thưởng</el-radio-button>
      <el-radio-button label="0">Phạt lỗi</el-radio-button>
    </el-radio-group>
    <div class="mt-3">
      <el-button type="primary" @click="save" style="width: 100%"
        >Lưu</el-button
      >
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";

export default {
  name: "AddSalaryForm",
  data() {
    return {
      formData: {
        title: "",
        price: 0,
        type: 1,
      },
    };
  },
  mounted() {
    // console.log('mounted');
  },
  methods: {
    ...mapActions("salary", ["storeSalary"]),
    async save() {
      await this.storeSalary(this.formData);
      this.$emit("submit");
    },
  },
};
</script>
<style></style>
