<template>
  <el-form
    :model="formData"
    ref="formData"
    label-width="180px"
    class="demo-formData p-5"
  >
    <div class="flex items-center justify-center gap-3">
      <div class="flex flex-col items-center mb-2">
        <el-upload
            class="avatar-uploader"
            :action="url + '/backend/upload-image'"
            :headers="{ 'X-CSRF-TOKEN': csrf }"
            :data="{ object: 'avatar' }"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
        >
          <img
              v-if="formData.avatar_url"
              :src="url + '/' + formData.avatar_url"
              class="avatar"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <span class="font-quicksand font-semibold">Avatar</span>
      </div>
      <div class="flex flex-col items-center mb-2">
        <el-upload
            class="avatar-uploader"
            :action="url + '/backend/upload-image'"
            :headers="{ 'X-CSRF-TOKEN': csrf }"
            :data="{ object: 'avatar' }"
            :show-file-list="false"
            :on-success="handleAdImageSuccess"
        >
          <img
              v-if="formData.metadata?.ad_image"
              :src="url + '/' + formData.metadata?.ad_image"
              class="avatar"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <span class="font-quicksand font-semibold">Ảnh hoàn thiện</span>
      </div>

    </div>

    <el-form-item label="Họ" prop="last_name">
      <el-input v-model="formData.last_name"></el-input>
    </el-form-item>
    <el-form-item label="Tên" prop="first_name">
      <el-input v-model="formData.first_name"></el-input>
    </el-form-item>
    <el-form-item label="Ngày sinh" required>
      <el-form-item prop="birth_day">
        <el-date-picker
          type="date"
          placeholder="Chọn ngày sinh"
          v-model="formData.birth_day"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>
    </el-form-item>
    <el-form-item label="Địa chỉ" prop="home">
      <el-input v-model="formData.home"></el-input>
    </el-form-item>
    <el-form-item label="Giọng" prop="accent">
      <el-input v-model="formData.metadata.accent"></el-input>
    </el-form-item>
    <el-form-item label="Số điện thoại" prop="phone">
      <el-input v-model="formData.phone"></el-input>
    </el-form-item>
    <el-form-item label="Email" prop="email">
      <el-input v-model="formData.email"></el-input>
    </el-form-item>
    <el-form-item label="Liên hệ qua" prop="contact_app">
      <el-input v-model="formData.metadata.contact_app"></el-input>
    </el-form-item>
    <el-form-item label="Chi tiết LH" prop="contact_profile">
      <el-input v-model="formData.metadata.contact_detail"></el-input>
    </el-form-item>
    <el-form-item label="Trình độ" prop="level">
      <el-input v-model="formData.level_of_japanese"></el-input>
    </el-form-item>
    <el-form-item label="Cấp độ đang dạy" prop="teaching_level">
      <el-input v-model="formData.metadata.teaching_level"></el-input>
    </el-form-item>
    <el-form-item label="Kinh nghiệm đi Nhật" prop="jp_exp">
      <el-switch v-model="formData.metadata.jp_exp"></el-switch>
    </el-form-item>
    <el-form-item label="Công việc ở Nhật" prop="jp_job">
      <el-input v-model="formData.metadata.jp_job"></el-input>
    </el-form-item>
    <el-form-item label="Trường tốt nghiệp" prop="school">
      <el-input v-model="formData.metadata.school"></el-input>
    </el-form-item>
    <el-form-item label="Kinh nghiệm giảng dạy" prop="teaching_exp">
      <el-input v-model="formData.metadata.teaching_exp"></el-input>
    </el-form-item>
    <el-form-item label="Ghi chú" prop="note">
      <el-input v-model="formData.note"></el-input>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submit">Lưu</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import axios from "axios";

export default {
  props: ["user"],
  data() {
    return {
      url: window.location.origin,
      formData: {
        first_name: "",
        last_name: "",
        birth_day: "",
        home: "",
        phone: "",
        email: "",
        level_of_japanese: "",
        note: "",
        avatar_url: "",
        metadata: {
          accent: "",
          contact_app: "",
          contact_detail: "",
          teaching_level: "",
          jp_exp: false,
          jp_job: "",
          teaching_exp: "",
          school: "",
          ad_image: "",
        },
      },
    };
  },
  computed: {
    csrf() {
      return document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content");
    },
  },
  mounted() {
    this.formData = {
      first_name: this.user.first_name,
      last_name: this.user.last_name,
      birth_day: this.user.birth_day,
      home: this.user.home,
      phone: this.user.phone,
      email: this.user.email,
      level_of_japanese: this.user.level_of_japanese,
      note: this.user.note,
      avatar_url: this.user.avatar_url,
      metadata: {
        accent: this.user.metadata?.accent,
        contact_app: this.user.metadata?.contact_app,
        contact_detail: this.user.metadata?.contact_detail,
        teaching_level: this.user.metadata?.teaching_level,
        jp_exp: !!this.user.metadata?.jp_exp,
        jp_job: this.user.metadata?.jp_job,
        teaching_exp: this.user.metadata?.teaching_exp,
        school: this.user.metadata?.school,
        ad_image: this.user.metadata?.ad_image,
      },
    };
  },
  methods: {
    async submit() {
      await axios
        .put(
          window.location.origin +
            "/backend/school/api/v1/user/" +
            this.user.id,
          this.formData
        )
        .then(async (res) => {
          this.$emit("updated", this.formData);
        });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleAvatarSuccess(res, file) {
      this.formData.avatar_url = 'upload/avatar/' + res;
    },
    handleAdImageSuccess(res, file) {
      this.formData.metadata.ad_image = 'upload/avatar/' + res;
    },
  },
};
</script>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
