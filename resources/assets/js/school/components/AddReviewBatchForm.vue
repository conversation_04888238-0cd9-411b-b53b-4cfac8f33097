<template>
  <div class="p-5">
    <div>
      <label for="email" class="block text-sm font-medium leading-6 text-gray-900">Ti<PERSON><PERSON> đ<PERSON></label>
      <div class="mt-2">
        <input
          v-model="formData.title"
          type="text"
          name="title"
          id="title"
          class="block w-full rounded-md border-0 py-1.5 px-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          placeholder=""
        />
      </div>
    </div>
    <div class="flex">
      <div class="mt-3 w-full">
        <label for="email" class="block text-sm font-medium leading-6 text-gray-900">Từ ngày</label>
        <el-date-picker
          v-model="formData.from"
          type="date"
          placeholder="Chọn ngày"
          format="dd/MM/yyyy"
          value-format="yyyy-MM-dd"
          class="w-full"
        >
        </el-date-picker>
      </div>
      <div class="mt-3 w-full">
        <label for="email" class="block text-sm font-medium leading-6 text-gray-900">Đến ngày</label>
        <el-date-picker
          v-model="formData.to"
          type="date"
          placeholder="Chọn ngày"
          format="dd/MM/yyyy"
          value-format="yyyy-MM-dd"
          class="w-full"
        >
        </el-date-picker>
      </div>
    </div>
    <div class="w-full mt-3">
      <el-button type="primary" class="w-full" @click="save" :disabled="loading" :loading="loading">Lưu</el-button>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex';

export default {
  data() {
    return {
      formData: {
        title: '',
        from: '',
        to: '',
      },
      loading: false
    }
  },
  methods: {
    ...mapActions('review', ['createBatch']),
    async save() {
      this.loading = true
      await this.createBatch(this.formData)
      this.$emit('created')
      this.loading = false
    }
  }
}
</script>
