<template>
  <div>
    <div class="mt-3 px-5">
      <el-switch
        v-model="tax"
        active-text="Tính thuế"
        inactive-text="Không tính thuế"
        :active-value="1"
        :inactive-value="0"
        @change="toggleTax"
      >
      </el-switch>
    </div>
    <div class="mt-3 px-5">
      <el-button
        v-if="!isAdding"
        type="success"
        style="width: 100%"
        @click="isAdding = true"
        >Thêm</el-button
      >
      <div v-else class="flex justify-between items-center">
        <el-button
          type="secondary"
          style="width: 100%"
          @click="isAdding = false"
          >Huỷ</el-button
        >
        <el-button type="primary" style="width: 100%" @click="addNew"
          >Lưu</el-button
        >
      </div>
    </div>
    <div
      v-if="isAdding"
      class="px-5 py-3 grid grid-cols-7 justify-between items-center gap-2"
    >
      <el-select
        v-model="formData.type"
        placeholder="Chọn khung t.gian"
        style="width: 100%"
      >
        <el-option
          v-for="item in timeFrames"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-date-picker
        v-model="formData.valid_from"
        type="date"
        placeholder="Chọn ngày"
        format="dd/MM/yyyy"
        value-format="yyyy-MM-dd"
        style="width: 100%"
      >
      </el-date-picker>
      <el-select
        v-model="formData.level"
        placeholder="Chọn cấp độ"
        style="width: 100%"
      >
        <el-option
          v-for="item in levelOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <el-input-number
        size="medium"
        placeholder="ID lớp"
        v-model="formData.group_id"
        :controls="false"
        style="width: 100%"
      >
      </el-input-number>
      <el-input-number
        size="medium"
        placeholder="Nhập số tiền"
        v-model="formData.base_salary"
        :controls="false"
        style="width: 100%"
      >
      </el-input-number>
      <el-select
        v-model="formData.currency"
        placeholder="Chọn loại tiền"
        style="width: 100%"
      >
        <el-option
          v-for="item in ['vnd', 'yen']"
          :key="item"
          :label="item"
          :value="item"
        >
        </el-option>
      </el-select>
      <div></div>
    </div>
    <div
      class="px-5 py-2 grid grid-cols-7 justify-between items-center font-bold gap-2"
    >
      <div>T.gian tính lương</div>
      <div>Ngày áp dụng</div>
      <div>Cấp độ</div>
      <div>Lớp</div>
      <div>Mức lương/ngày</div>
      <div>Loại tiền</div>
      <div></div>
    </div>
    <div
      class="px-5 py-3 grid grid-cols-7 justify-between items-center border gap-1"
      v-for="salary in list"
    >
      <div>
        <el-select
          v-model="salary.type"
          placeholder="Chọn khung t.gian"
          style="width: 100%"
          @change="updateTimeFrame(salary)"
        >
          <el-option
            v-for="item in timeFrames"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div>{{ salary.valid_from | dateTimeToDate }}</div>
      <div>{{ salary.level }}</div>
      <div>
        <!--        <a :href="`${url}/backend/school/classroom/${salary.group_id}`" target="_blank">{{ salary.group_id }}</a>-->
        <div @click="editSalaryGroup(salary)" class="cursor-pointer">
          {{ teacherType === 'online' ? salary.group_id : salary.course_id || "--" }}
        </div>
      </div>
      <div>
        <div @click="editSalary(salary)" class="cursor-pointer">
          {{ salary.base_salary | decimal }}
        </div>
      </div>
      <div>
        <el-select
          name=""
          id=""
          v-model="salary.currency"
          @change="updateCurrency(salary)"
        >
          <el-option value="vnd" label="VND"></el-option>
          <el-option value="yen" label="Yen"></el-option>
        </el-select>
      </div>
      <div>
        <div class="inline-flex rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100 cursor-pointer" @click="onRemoveSalary(salary)"
        >Xoá
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { orderBy } from "lodash";
import axios from "axios";
export default {
  name: "SalaryManagement",
  props: ["user", "timeFrame", "teacherType"],
  data() {
    return {
      url: window.location.origin,
      isAdding: false,
      formData: {
        type: 1,
        valid_from: null,
        base_salary: "",
        level: "N1",
        group_id: null,
        course_id: null,
        currency: "vnd",
      },
      tax: 0,
      levelOptions: [
        { value: "N1", label: "N1" },
        { value: "N2", label: "N2" },
        { value: "N3", label: "N3" },
        { value: "N4", label: "N4" },
        { value: "N5", label: "N5" },
        { value: "kaiwa", label: "KAIWA" },
      ],
      timeFrames: [
        { value: 1, label: "26 th.trước - 25 th.này" },
        { value: 2, label: "Đầu tháng - cuối tháng" },
      ],
    };
  },
  computed: {
    ...mapGetters("salary", ["currentUserSalaries"]),
    ...mapGetters("teacher", ["currentDate"]),
    list() {
      return orderBy(
        this.currentUserSalaries,
        ["valid_from", "id"],
        ["desc", "desc"]
      );
    },
  },
  watch: {
    isAdding(value) {
      if (!value)
        this.formData = {
          type: 1,
          valid_from: null,
          base_salary: "",
          level: 1,
          group_id: null,
          currency: "vnd",
        };
    },
  },
  mounted() {
    this.getSalariesByUser({ userId: this.user.id, teacherType: this.teacherType });
    this.tax = this.user.tax_id || 0;
  },
  methods: {
    ...mapActions("salary", [
      "getSalariesByUser",
      "storeSalary",
      "removeSalary",
      "updateSalary",
    ]),
    ...mapActions("teacher", ["updateTableTeacher"]),
    async addNew() {
      const data = {
        ...this.formData,
        user_id: this.user.id,
      };

      if (this.teacherType === "offline") {
        data.course_id = data.group_id;
        delete data.group_id;
      }

      await this.storeSalary(data);
      this.isAdding = false;
      await this.updateTableTeacher({
        userId: this.user.id,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
        teacherType: this.teacherType,
      });
    },
    async onRemoveSalary(salary) {
      const data = {
        id: salary.id,
      };
      await this.removeSalary(data);
      await this.updateTableTeacher({
        userId: this.user.id,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
        teacherType: this.teacherType,
      });
    },
    async editSalaryGroup(salary) {
      const group_id = prompt("Nhập ID lớp");
      if (group_id) {
        let data = {
          id: salary.id,
        }
        if (this.teacherType === "online") {
          data.group_id = group_id;
        }
        if (this.teacherType === "offline") {
          data.course_id = group_id;
        }

        await this.updateSalary(data);
        await this.updateTableTeacher({
          userId: this.user.id,
          curDate: this.currentDate,
          timeFrame: this.timeFrame,
          teacherType: this.teacherType,
        });
      }
    },
    async editSalary(salary) {
      const conf = prompt("Nhập lương");
      if (conf) {
        const data = {
          id: salary.id,
          base_salary: conf,
        };
        await this.updateSalary(data);
        await this.updateTableTeacher({
          userId: this.user.id,
          curDate: this.currentDate,
          timeFrame: this.timeFrame,
          teacherType: this.teacherType,
        });
      }
    },
    async updateCurrency(salary) {
      const data = {
        id: salary.id,
        currency: salary.currency,
      };
      await this.updateSalary(data);
      await this.updateTableTeacher({
        userId: this.user.id,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
        teacherType: this.teacherType,
      });
    },
    async updateTimeFrame(salary) {
      const data = {
        id: salary.id,
        type: salary.type,
      };
      await this.updateSalary(data);
      await this.updateTableTeacher({
        userId: this.user.id,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
        teacherType: this.teacherType,
      });
    },
    async toggleTax() {
      await axios.post(
        window.location.origin + "/backend/school/api/v1/user/toggle-tax",
        {
          id: this.user.id,
          value: this.tax ? 3.063 : 0,
        }
      );
      await this.updateTableTeacher({
        userId: this.user.id,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
        teacherType: this.teacherType,
      });
    },
  },
};
</script>
