<template>
  <div>
    <div
      class="px-5 py-2 flex justify-between items-center font-bold grid grid-cols-7"
    >
      <div><PERSON><PERSON>y</div>
      <div>Thưởng LL</div>
      <div>Thưởng</div>
      <div>Lỗi phạt</div>
      <div>Link</div>
      <div><PERSON>hi chú</div>
      <div>Ghi chú GV</div>
    </div>
    <div v-for="group in groupedList">
      <div class="mt-5 px-5 font-bold flex">
        <a
          :href="`${url}/backend/school/classroom/${group.id}`"
          target="_blank"
          >{{ teacherType === "online" ? group.name : `${group?.department?.name || '--' } | ${group?.info?.periods[0]?.period_code || '--' } | ${ group?.info?.room?.room_name || '--'  } | ${group?.date_start || '--' } - ${group?.date_end || '--' }` }}</a
        >
        <span style="color: red">| {{ group.attendances.length }} buổi</span>
        <div class="flex-auto"></div>
        <a
          v-if="group.group_report"
          :href="`${url}/backend/school/classroom/${group.id}?focus=report`"
          target="_blank"
          >Xem báo cáo</a
        >
      </div>
      <div
        class="px-5 py-3 flex justify-between items-center border grid grid-cols-7"
        v-for="attendance in group.attendances"
      >
        <div class="mx-1">{{ attendance.date_attendance }}</div>
        <div class="mx-1">
          <el-input
            placeholder="Please input"
            v-model="attendance.ref_reward"
            @keyup.native.enter="
              onChangePayoff(attendance.id, $event.target.value, 'ref_reward')
            "
          ></el-input>
        </div>
        <div class="mx-1">
          <el-select
            v-model="attendance.reward"
            filterable
            clearable
            placeholder="Chọn mục thưởng"
            @change="onChangePayoff(attendance.id, $event, 'reward')"
          >
            <el-option
              v-for="item in rewards"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="mx-1">
          <el-select
            v-model="attendance.fault"
            filterable
            clearable
            placeholder="Chọn lỗi"
            @change="onChangePayoff(attendance.id, $event, 'fault')"
          >
            <el-option
              v-for="item in faults"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="mx-1">
          <div
            v-if="attendance.link"
            @click="openZoomDetail(attendance)"
            class="cursor-pointer"
          >
            <i class="fa fa-share"></i>
          </div>
          <div v-else>--</div>
        </div>
        <div class="mx-1">
          <el-tooltip
            class="item"
            effect="dark"
            :content="attendance.note"
            placement="top-start"
          >
            <div class="cursor-pointer" @click="editNote(attendance)">
              {{ attendance.note || "--" }}
            </div>
          </el-tooltip>
        </div>
        <div class="mx-1">
          <el-tooltip
            class="item"
            effect="dark"
            :content="attendance.note_cancel"
            placement="top-start"
          >
            <div class="truncate ... cursor-pointer">
              {{ attendance.note_cancel || "--" }}
            </div>
          </el-tooltip>
        </div>
      </div>
    </div>
    <el-drawer
      :title="`${currentZoomDetail.groupName} | ${currentZoomDetail.dateAttendance}`"
      :append-to-body="true"
      direction="ltr"
      :visible.sync="showZoomDetail"
    >
      <div
        class="p-5"
        v-if="currentZoomDetail.link"
        v-html="currentZoomDetail.link"
      ></div>
      <div class="p-5 font-bold">
        <span>Password: {{ currentZoomDetail.password || "--" }}</span>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { orderBy, find, findIndex } from "lodash";

export default {
  name: "WorkDayManagement",
  props: ["user", "timeFrame", "teacherType"],
  data() {
    return {
      url: window.location.origin,
      showZoomDetail: false,
      currentZoomDetail: {},
    };
  },
  computed: {
    ...mapGetters("payoff", ["payoffList"]),
    ...mapGetters("teacher", ["currentDate"]),
    list() {
      return orderBy(
        this.user.work_days,
        ["date_attendance", "id"],
        ["desc", "desc"]
      );
    },
    groupedList() {
      const list = [];
      if (this.teacherType === 'online') {
        this.list.forEach((day) => {
          if (!day.group) return;
          const index = findIndex(list, function (o) {
            return o.id === day.group.id;
          });
          if (index === -1) {
            list.push({
              ...day.group,
              attendances: [day],
            });
          } else {
            list[index].attendances.push(day);
          }
        });
        return list;
      } else {
        this.list.forEach((day) => {
          if (!day.course) return;
          const index = findIndex(list, function (o) {
            return o.id === day.course_id;
          });
          if (index === -1) {
            list.push({
              ...day.course,
              attendances: [day],
            });
          } else {
            list[index].attendances.push(day);
          }
        });
        return list;
      }
    },
    rewards() {
      return this.payoffList.filter((o) => o.type === 1);
    },
    faults() {
      return this.payoffList.filter((o) => o.type === 0);
    },
  },
  mounted() {
    // this.getSalariesByUser({ userId: this.user.id });
  },
  methods: {
    ...mapActions("salary", ["getSalariesByUser", "storeSalary"]),
    ...mapActions("teacher", [
      "updateTableTeacher",
      "updateAttendancePayoff",
      "updateAttendance",
    ]),
    ...mapActions("classroom", ["updateAttendance"]),
    getPayoff(id) {
      const payoff = find(this.payoffList, function (o) {
        return o.id === id;
      });
      return payoff ? payoff.title : "--";
    },
    async onChangePayoff(id, payoffId, type) {
      const data = { id, payoffId, type };
      await this.updateAttendancePayoff(data);
      await this.updateTableTeacher({
        userId: this.user.id,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
      });
    },
    openZoomDetail(content) {
      this.currentZoomDetail = {
        link: content.link,
        groupName: content.group?.name || "--",
        dateAttendance: content.date_attendance || "--",
        password: content.password,
      };
      this.showZoomDetail = true;
    },
    async editNote(attendance) {
      let value = window.prompt("Nhập ghi chú");
      if (!value) {
        value = "-1";
      }
      const data = {
        id: attendance.id,
        note: value,
      };
      await this.updateAttendance(data);
      await this.updateTableTeacher({
        userId: this.user.id,
        curDate: this.currentDate,
        timeFrame: this.timeFrame,
      });
    },
  },
};
</script>
