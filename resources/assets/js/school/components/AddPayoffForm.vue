<template>
  <div class="p-5">
    <div
      class="text-xs hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300"
    >
      Ti<PERSON><PERSON> đ<PERSON>
    </div>
    <el-input
      class="mt-3"
      placeholder="Nhập tiêu đề"
      v-model="formData.title"
      clearable
    >
    </el-input>
    <div
      class="mt-3 text-xs hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300"
    >
      Số tiền
    </div>
    <div class="mt-3 flex items-center">
      <el-input-number
        size="medium"
        placeholder="Nhập số tiền"
        v-model="formData.price"
        :controls="false"
      >
      </el-input-number>
      <strong class="ml-3 cursor-pointer"> VNĐ </strong>
    </div>
    <div
      class="mt-3 text-xs hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300"
    >
      Số tiền (Yen)
    </div>
    <div class="mt-3 flex items-center">
      <el-input-number
        size="medium"
        placeholder="Nhập số tiền yen"
        v-model="formData.yen_price"
        :controls="false"
      >
      </el-input-number>
      <strong class="ml-3 cursor-pointer"> Yen </strong>
    </div>
    <div
      class="mt-3 text-xs hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-300"
    >
      Phân loại
    </div>
    <el-radio-group class="mt-2" v-model="formData.type" size="medium">
      <el-radio-button label="1">Thưởng</el-radio-button>
      <el-radio-button label="0">Phạt lỗi</el-radio-button>
    </el-radio-group>
    <div class="mt-3">
      <el-button type="primary" @click="save" style="width: 100%"
        >Lưu</el-button
      >
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";

export default {
  name: "AddPayoffForm",
  props: ["payoff"],
  data() {
    return {
      formData: {
        title: "",
        price: 0,
        type: 1,
        yen_price: 0,
      },
    };
  },
  mounted() {
    if (this.payoff?.id) {
      this.formData = {
        ...this.formData,
        id: this.payoff.id,
        title: this.payoff.title,
        price: this.payoff.price,
        yen_price: this.payoff.yen_price,
        type: this.payoff.type,
      };
    }
  },
  methods: {
    ...mapActions("payoff", ["storePayoff", "updatePayoff"]),
    async save() {
      if (this.formData?.id) {
        await this.updatePayoff(this.formData);
      } else {
        await this.storePayoff(this.formData);
      }
      this.$emit("submit");
    },
  },
};
</script>
<style></style>
