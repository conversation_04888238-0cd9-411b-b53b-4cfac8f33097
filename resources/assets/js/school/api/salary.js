import api from './../config/api';
export default {
  loadList: (data) => new Promise((resolve, reject) => {
    api.get(`/salary/list?userId=${data.userId}&teacherType=${data.teacherType}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  store: (data) => new Promise((resolve, reject) => {
    api.post(`/salary/store`, data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  update: (data) => new Promise((resolve, reject) => {
    api.put(`/salary/${data.id}`, data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  remove: (data) => new Promise((resolve, reject) => {
    api.delete(`/salary/remove/${data.id}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
