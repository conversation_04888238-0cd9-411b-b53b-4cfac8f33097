import api from './../config/api';
export default {
  loadList: data => new Promise((resolve, reject) => {
    api.get(`/students/x-absences`, { params: data.params })
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  sendBulkMessage: data => new Promise((resolve, reject) => {
    api.post('/students/send', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),

};
