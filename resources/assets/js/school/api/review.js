import api from './../config/api';
export default {
  loadList: () => new Promise((resolve, reject) => {
    api.get(`/review/list`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  createBatch: (data) => new Promise((resolve, reject) => {
    api.post(`/review`, data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  fetchUserReviews: (payload) => new Promise((resolve, reject) => {
    api.get(`/review/user-reviews`, {params: {...payload}})
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  createReview: (data) => new Promise((resolve, reject) => {
    api.post(`/review/user-review`, data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  deleteReview: (id) => new Promise((resolve, reject) => {
    api.delete(`/review/user-review/${id}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  updateReview: (data) => new Promise((resolve, reject) => {
    api.put(`/review/user-review/${data.id}`, data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
