import api from "./../config/api";

export default {
    loadList: (data) =>
        new Promise((resolve, reject) => {
            api
                .post("/classroom/list/", data)
                .then((response) => resolve(response.data))
                .catch((error) => reject(error.response.data));
        }),
    getStudentByClass: (data) =>
        new Promise((resolve, reject) => {
            api
                .get(
                    `/classroom/${data.classId}/student?page=${data.page}&per_page=${data.perPage}&key_search=${data.key_search}`
                )
                .then((response) => resolve(response.data))
                .catch((error) => reject(error.response.data));
        }),
    saveCourseTime: (data) =>
        new Promise((resolve, reject) => {
            api
                .post(`/classroom/save-course-time`, data)
                .then((response) => resolve(response.data))
                .catch((error) => reject(error.response.data));
        }),
    importAttendances: (data) =>
        new Promise((resolve, reject) => {
            api
                .post("/classroom/import-attendances", data)
                .then((response) => resolve(response.data))
                .catch((error) => reject(error.response.data));
        }),
    importOfflineTestResult: (data) =>
        new Promise((resolve, reject) => {
            api
                .post("/classroom/import-offline-test-result", data)
                .then((response) => resolve(response.data))
                .catch((error) => reject(error.response.data));
        }),
    getOptionClassRoom: (data) =>
        new Promise((resolve, reject) => {
            api
                .post("/classroom/option", data)
                .then((response) => resolve(response.data))
                .catch((error) => reject(error.response.data));
        }),

};
