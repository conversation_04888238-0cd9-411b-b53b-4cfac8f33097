import Vue from 'vue';
import { findIndex, cloneDeep, set } from 'lodash';
import moment from 'moment';
import api from '../api'
import * as types from './mutation-types';

const review = {
  namespaced: true,
  state: {
    reviews: [],
    currentReviewBatch: {},
    batchReviews: [],
    total: 0
  },
  getters: {
    reviews: state => state.reviews,
    currentReviewBatch: state => state.currentReviewBatch,
    batchReviews: state => state.batchReviews,
    total: state => state.total,
  },
  mutations: {
    [types.SET_REVIEW_LIST](state, data) {
      state.reviews = data;
    },
    [types.SET_USER_REVIEW_LIST](state, data) {
      state.batchReviews = data;
      state.total = data.total;
    },
    [types.BATCH_CREATED](state, data) {
      state.reviews.push(data)
    },
    [types.REVIEW_CREATED](state, data) {
      state.batchReviews.push(data)
    },
    [types.REVIEW_UPDATED](state, data) {
      const idx = findIndex(state.batchReviews, ['id', data.id])
      Vue.set(state.batchReviews, `${idx}`, data)
    },
    [types.REVIEW_DELETED](state, id) {
      state.reviews = state.reviews.filter(o => o.id !== id)
    },
  },
  actions: {
    getList({ commit }, data) {
      return api.review.loadList(data)
          .then(response => commit('SET_REVIEW_LIST', response.data))
          .catch();
    },
    getUserReviews({ commit }, data) {
      return api.review.fetchUserReviews(data)
          .then(response => commit('SET_USER_REVIEW_LIST', response.data))
          .catch();
    },
    createBatch({ commit }, data) {
      return api.review.createBatch(data)
          .then(response => commit('BATCH_CREATED', response.data))
          .catch();
    },
    createReview({ commit }, data) {
      return api.review.createReview(data)
          .then(response => commit('REVIEW_CREATED', response.data))
          .catch();
    },
    updateReview({ commit }, data) {
      return api.review.updateReview(data)
          .then(response => commit('REVIEW_UPDATED', response.data))
          .catch();
    },
    deleteReview({ commit }, id) {
      return api.review.deleteReview(id)
          .then(response => commit('REVIEW_DELETED', id))
          .catch();
    },
  }
};

export default review;
