import Vue from 'vue';
import api from '../api'
import * as types from './mutation-types';
const payoff = {
  namespaced: true,
  state: {
    list: [],
  },
  getters: {
    payoffList: state => state.list,
  },
  mutations: {
    [types.SET_PAYOFF_LIST](state, data) {
      state.list = data;
    },
    [types.SET_CREATED_PAYOFF](state, data) {
      state.list.push(data);
    },
    [types.SET_UPDATED_PAYOFF](state, data) {

    },
    [types.SET_DELETED_PAYOFF](state, id) {
      state.list = state.list.filter(o => {
        return o.id !== id
      })
    },
  },
  actions: {
    getPayoffList({ commit }) {
      return api.payoff.loadList()
          .then(response => commit('SET_PAYOFF_LIST', response.data))
          .catch();
    },
    storePayoff({ commit }, data) {
      return api.payoff.store(data)
          .then(response => commit('SET_CREATED_PAYOFF', response.data))
          .catch();
    },
    updatePayoff({ commit }, data) {
      const id = data.id;
      return api.payoff.update(id, data)
          .then(response => commit('SET_UPDATED_PAYOFF', response.data))
          .catch();
    },
    deletePayoff({ commit }, id) {
      return api.payoff.delete(id)
          .then(response => commit('SET_DELETED_PAYOFF', id))
          .catch();
    },
  }
};

export default payoff;
