import Vue from 'vue';
import 'es6-promise/auto'
import Vuex from 'vuex';
import ui from "./ui";
import teacher from "./teacher";
import payoff from "./payoff";
import salary from "./salary";
import classroom from "./classroom";
import review from "./review";
import student from "./student";

Vue.use(Vuex);
const store = new Vuex.Store({
  namespaced: true,
  modules: {
    ui: ui,
    teacher: teacher,
    payoff: payoff,
    salary: salary,
    classroom: classroom,
    review: review,
    student: student,
  }
});

export default store;
