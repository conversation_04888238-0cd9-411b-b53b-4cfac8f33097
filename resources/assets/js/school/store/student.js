import api from '../api'
import * as types from './mutation-types';
const teacher = {
  namespaced: true,
  state: {
    list: [],
    admins: [],
    absence: [],
    exams: [],
  },
  getters: {
    students: state => state.list,
    admins: state => state.admins,
    absence: state => state.absence,
    exams: state => state.exams,
  },
  mutations: {
    [types.SET_STUDENT_LIST](state, data) {
      state.list = data.students;
      state.admins = data.admins;
      state.absence = data.absence;
      state.exams = data.exams;
    },
  },
  actions: {
    getStudents({ commit }, data) {
      return api.student.loadList(data)
          .then(response => commit('SET_STUDENT_LIST', response.data))
          .catch();
    },
    sendBulkMessage({ commit }, data) {
      return api.student.sendBulkMessage(data)
          .then(response => commit('MESSAGE_SENT', response.data))
          .catch();
    },
  }
};

export default teacher;
