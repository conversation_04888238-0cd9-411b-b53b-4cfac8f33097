import Vue from 'vue';
import api from '../api'
import * as types from './mutation-types';
const ui = {
  namespaced: true,
  state: {
    title: '',
  },
  getters: {
    title: state => state.title,
  },
  mutations: {
    [types.SET_PAGE](state, data) {
      state.title = data;
    },
  },
  actions: {
    setPage({ commit }, data) {
      commit('SET_PAGE', data);
    },
  }
};

export default ui;
