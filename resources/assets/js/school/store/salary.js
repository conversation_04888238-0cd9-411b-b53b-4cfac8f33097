import Vue from 'vue';
import {cloneDeep, findIndex, omit} from 'lodash';
import api from '../api'
import * as types from './mutation-types';
const salary = {
  namespaced: true,
  state: {
    list: [],
  },
  getters: {
    currentUserSalaries: state => state.list,
  },
  mutations: {
    [types.SET_SALARY_LIST](state, data) {
      state.list = data;
    },
    [types.SET_CREATED_SALARY](state, data) {
      state.list.unshift(data);
    },
    [types.REMOVE_SALARY](state, data) {
      const idx = findIndex(state.list, (item) => item.id === data.id);
      state.list.splice(idx, 1);
    },
    [types.UPDATE_SALARY](state, data) {
      const idx = findIndex(state.list, (item) => item.id === data.id);
      const tmp =  omit(data, ['id']);
      Vue.set(state.list, idx, {
        ...state.list[idx],
        ...tmp
      });
    },
  },
  actions: {
    getSalariesByUser({ commit }, data) {
      return api.salary.loadList(data)
          .then(response => commit('SET_SALARY_LIST', response.data))
          .catch();
    },
    storeSalary({ commit }, data) {
      return api.salary.store(data)
          .then(response => commit('SET_CREATED_SALARY', response.data))
          .catch();
    },
    removeSalary({ commit }, data) {
      return api.salary.remove(data)
          .then(response => commit('REMOVE_SALARY', data))
          .catch();
    },
    updateSalary({ commit }, data) {
      return api.salary.update(data)
          .then(response => commit('UPDATE_SALARY', data))
          .catch();
    },
  }
};

export default salary;
