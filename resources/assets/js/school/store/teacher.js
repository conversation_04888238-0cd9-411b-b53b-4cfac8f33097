import Vue from 'vue';
import { findIndex, cloneDeep } from 'lodash';
import moment from 'moment';
import api from '../api'
import * as types from './mutation-types';
const teacher = {
  namespaced: true,
  state: {
    users: [],
    payoffs: [],
    totalResult: undefined,
    currentDate: moment().format('yyyy-MM-DD'),
  },
  getters: {
    users: state => state.users,
    totalResult: state => state.totalResult,
    payoffs: state => state.payoffs,
    currentDate: state => state.currentDate,
  },
  mutations: {
    [types.SET_CURRENT_DATE](state, data) {
      state.currentDate = data;
    },
    [types.SET_USER_LIST](state, data) {
      state.users = data.data;
      state.totalResult = data.total;
    },
    [types.SET_PAYOFF_LIST](state, data) {
      state.payoffs = data;
    },
    [types.UPDATE_TABLE_TEACHER](state, data) {
      const idx = findIndex(state.users, user => user.id === data.id);
      Vue.set(state.users, idx, cloneDeep(data));
    },
    [types.UPDATE_ATTENDANCE_PAYOFF](state, data) {
      // const idx = findIndex(state.users, user => user.id === data.id);
      // Vue.set(state.users, idx, cloneDeep(data));
    },
  },
  actions: {
    changeCurrentDate({ commit }, data) {
      commit('SET_CURRENT_DATE', data)
    },
    getUserList({ commit }, data) {
      return api.teacher.loadList(data)
          .then(response => commit('SET_USER_LIST', response.data))
          .catch();
    },
    getPayoff({ commit }) {
      return api.teacher.getPayoff()
          .then(response => commit('SET_PAYOFF_LIST', response.data))
          .catch();
    },
    updateTableTeacher({ commit }, data) {
      return api.teacher.updateTableTeacher(data)
          .then(response => commit('UPDATE_TABLE_TEACHER', response.data))
          .catch();
    },
    updateAttendancePayoff({ commit }, data) {
      return api.teacher.updateAttendancePayoff(data)
          .then(response => commit('UPDATE_ATTENDANCE_PAYOFF', response.data))
          .catch();
    },
    updateUsers({ commit }, data) {
      commit('SET_USER_LIST', data);
    }
  }
};

export default teacher;
