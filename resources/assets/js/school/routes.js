// Pages
import Dashboard from "./pages/Dashboard";
import Payoff from "./pages/Payoff";
import Level from "./pages/Level";
import ClassroomList from "./pages/ClassroomList";
import Classroom from "./pages/Classroom";
import ReviewList from "./pages/ReviewList";
import ReviewTeacherList from "./pages/ReviewTeacherList";
import StudentList from "./pages/StudentList";
import Stats from "./pages/Stats.vue";
import TeacherList from "./pages/TeacherList.vue";
// Routes
const routes = [
  {
    path: "/",
    name: "app",
    component: Dashboard,
    meta: {
      auth: true,
    },
  },
  {
    path: "/dashboard",
    name: "dashboard",
    component: Dashboard,
    meta: {
      auth: true,
    },
  },
  {
    path: "/reviews",
    name: "reviews",
    component: ReviewList,
    meta: {
      auth: true,
    },
  },
  {
    path: "/reviews/:id",
    name: "review",
    component: ReviewTeacherList,
    meta: {
      auth: true,
    },
  },
  {
    path: "/payoff",
    name: "payoff",
    component: Payoff,
    meta: {
      auth: true,
    },
  },
  {
    path: "/level",
    name: "level",
    component: Level,
    meta: {
      auth: true,
    },
  },
  {
    path: "/level/:level",
    name: "classroomList",
    component: ClassroomList,
    meta: {
      auth: true,
    },
  },
  {
    path: "/classroom/:id",
    name: "classroom",
    component: Classroom,
    meta: {
      auth: true,
    },
  },
  {
    path: "/students",
    name: "students",
    component: StudentList,
    meta: {
      auth: true,
    },
  },
  {
    path: "/teachers",
    name: "teachers",
    component: TeacherList,
    meta: {
      auth: true,
    },
  },
  {
    path: "/stats",
    name: "stats",
    component: Stats,
    meta: {
      auth: true,
    },
  },
];
export default routes;
