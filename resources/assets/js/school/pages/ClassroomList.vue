<template>
  <div>
    <div style="display: flex; justify-content: space-between">
      <div style="display: flex;">
        <div style="margin-right: 15px;">
          <div>Tên</div>
          <el-input
              v-model="filters.name"
              style="width: 200px;"
              @keyup.enter.native="search"
              placeholder="Tìm kiếm bằng tên"
          ></el-input>
        </div>
        <div style="margin-right: 15px;">
          <div>Trạng thái chăm sóc</div>
          <el-select
              clearable
              v-model="filters.care_status"
              style="width: 200px;"
              @keyup.enter.native="search"
              placeholder="Trạng thái chăm sóc"
          >
            <el-option
                v-for="item in option_care"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div style="margin-right: 15px;">
          <div><PERSON><PERSON><PERSON> chăm sóc</div>
          <el-select
              clearable
              v-model="filters.type_phase"
              style="width: 200px;"
              @keyup.enter.native="search"
              placeholder="Trạng thái chăm sóc"
          >
            <el-option
                v-for="item in optionClassRoom.type_phase_take_care"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div style="margin-right: 15px;">
          <div>Đợt chăm sóc</div>
          <el-select
              clearable
              v-model="filters.number_phase"
              style="width: 200px;"
              @keyup.enter.native="search"
              placeholder="Đợt chăm sóc"
          >
            <el-option
                v-for="i in maxLengthPhaseTakeCare"
                :key="i"
                :label="`Đợt ${i}`"
                :value="i">
            </el-option>
          </el-select>
        </div>
        <div style="margin-right: 15px;">
          <div>Số buổi học</div>
          <el-input
              v-model="filters.number_lesson"
              style="width: 200px;"
              pattern="\d*" inputmode="numeric"
              @keyup.enter.native="search"
              placeholder="Nhập số buổi đã học"
          ></el-input>
        </div>
        <div style="margin-right: 15px;">
          <div>Loại sản phẩm</div>
          <el-select
              v-model="filters.type"
              style="width: 200px;"
              @keyup.enter.native="search"
              placeholder="Chọn loại sản phẩm"
          >
            <el-option
                v-for="item in option_type"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
          <el-button @click="search">Tìm kiếm</el-button>
        </div>
      </div>
      <div style="display: flex; align-items: end">
        <el-button @click="showDialogListPhaseTakeCare">Danh sách</el-button>
        <el-button @click="dialogFormPhaseTakeCare = true">Thêm mới</el-button>
      </div>
    </div>

    <router-link v-for="(classroom) in classroomList"
                 :to="{ name: 'classroom', params: { id: classroom.id } }"
    >
      <div
          :key="`class-${classroom.id}`"
          class="p-5 mt-2 shadow hover:shadow-lg cursor-pointer"
      >
        <div>
          {{ classroom.name }}
          <el-tag
              v-if="isPast(classroom.expired_at)"
              type="danger">
            Đã kết thúc
          </el-tag>
        </div>
        <div v-if="listPhaseTakeCareGroupByType[classroom.type] !== undefined" class="flex">
          <template
              v-if="type_group_class_has_option.includes(classroom.type) && vip_level_group_class_has_option.includes(classroom.vip_level)">
            <div
                v-if="listPhaseTakeCareGroupByType[classroom.type][classroom.vip_session] !== undefined && phaseTakeCare.type_phase === 1"
                v-for="(phaseTakeCare, key) in listPhaseTakeCareGroupByType[classroom.type][classroom.vip_session]"
                :key="key" style="font-style: italic; font-size: 12px;" class="mt-2 mr-10">
              Chăm sóc nhắn tin đợt {{ phaseTakeCare.number_phase }}:
              <span style="font-weight: bold">
                {{
                  classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] : 0
                }}/{{ classroom.count_users }}
              </span>
              <i v-if="(classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] : 0) === classroom.count_users && classroom.count_users > 0"
                 style="color: green;" class="el-icon-success"></i>
            </div>
          </template>
          <template v-else>
            <div
                v-for="(phaseTakeCare, key) in Object.values(listPhaseTakeCareGroupByType[classroom.type])[0]"
                v-if="phaseTakeCare.type_phase === 1"
                :key="key" style="font-style: italic; font-size: 12px;" class="mt-2 mr-10">
              Chăm sóc nhắn tin đợt {{ phaseTakeCare.number_phase }}:
              <span style="font-weight: bold">
                {{
                  classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] : 0
                }}/{{ classroom.count_users }}
              </span>
              <i v-if="(classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_phase_${phaseTakeCare.number_phase}`] : 0) === classroom.count_users && classroom.count_users > 0"
                 style="color: green;" class="el-icon-success"></i>
            </div>
          </template>
        </div>
        <div v-if="listPhaseTakeCareGroupByType[classroom.type] !== undefined" class="flex">
          <template
              v-if="type_group_class_has_option.includes(classroom.type) && vip_level_group_class_has_option.includes(classroom.vip_level)">
            <div
                v-if="listPhaseTakeCareGroupByType[classroom.type][classroom.vip_session] !== undefined && phaseTakeCare.type_phase === 2"
                v-for="(phaseTakeCare, key) in listPhaseTakeCareGroupByType[classroom.type][classroom.vip_session]"
                :key="key" style="font-style: italic; font-size: 12px;" class="mt-2 mr-10">
              Chăm sóc gửi khảo sát đợt {{ phaseTakeCare.number_phase }}:
              <span style="font-weight: bold">
                {{
                  classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] : 0
                }}/{{ classroom.count_users }}
              </span>
              <i v-if="(classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] : 0) === classroom.count_users && classroom.count_users > 0"
                 style="color: green;" class="el-icon-success"></i>
            </div>
          </template>
          <template v-else>
            <div
                v-for="(phaseTakeCare, key) in Object.values(listPhaseTakeCareGroupByType[classroom.type])[0]"
                v-if="phaseTakeCare.type_phase === 2"
                :key="key" style="font-style: italic; font-size: 12px;" class="mt-2 mr-10">
              Chăm sóc gửi khảo sát đợt {{ phaseTakeCare.number_phase }}:
              <span style="font-weight: bold">
                {{
                  classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] : 0
                }}/{{ classroom.count_users }}
              </span>
              <i v-if="(classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] !== undefined ? classroom[`total_user_take_care_survey_phase_${phaseTakeCare.number_phase}`] : 0) === classroom.count_users && classroom.count_users > 0"
                 style="color: green;" class="el-icon-success"></i>
            </div>
          </template>
        </div>
      </div>
    </router-link>

    <el-dialog title="Thêm lịch đợt chăm sóc" :visible.sync="dialogFormPhaseTakeCare"
               @close="closeDialogFormPhaseTakeCare">
      <el-form :model="formPhaseTakeCare">
        <el-form-item label="Vip level" :label-width="formLabelWidth">
          <el-input readonly style="width: 200px;" v-model="formPhaseTakeCare.vip_level" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="Loại sản phẩm" :label-width="formLabelWidth">
          <el-select v-model="formPhaseTakeCare.type" placeholder="Chọn loại sản phẩm">
            <el-option v-for="item in option_type" :label="item.label" :value="item.value"
                       :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="formPhaseTakeCare.vip_level === 'N5' && formPhaseTakeCare.type === 'vip15'" label="Loại lớp"
                      :label-width="formLabelWidth">
          <el-select v-model="formPhaseTakeCare.class_type" placeholder="Chọn loại lớp">
            <el-option v-for="item in optionClassRoom.option_N5_vip15.type_class" :label="item.label"
                       :value="item.value"
                       :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
            v-if="(formPhaseTakeCare.vip_level === 'N2' || formPhaseTakeCare.vip_level === 'N3') && formPhaseTakeCare.type === 'vip15'"
            label="Loại lớp"
            :label-width="formLabelWidth">
          <el-select v-model="formPhaseTakeCare.number_lesson" placeholder="Chọn số buổi học">
            <el-option v-for="item in optionClassRoom.option_N2_N3_vip15.number_lesson" :label="item.label"
                       :value="item.value"
                       :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
            label="Loại chăm sóc"
            :label-width="formLabelWidth">
          <el-radio v-for="item in optionClassRoom.type_phase_take_care" v-model="formPhaseTakeCare.type_phase"
                    :label="item.value" :key="item.value">{{ item.label }}
          </el-radio>
        </el-form-item>
        <div>
          <el-divider></el-divider>
        </div>
        <el-form-item :label="'Đợt' + ` ${key + 1}`" :label-width="formLabelWidth"
                      v-for="(item, key) in formPhaseTakeCare.number_take_care" :key="key">
          <el-input @input="onInputNumber(key)" style="width: 200px;" v-model="formPhaseTakeCare.number_take_care[key]"
                    autocomplete="off">
            <template slot="append">buổi</template>
          </el-input>
          <i style="cursor: pointer;" class="el-icon-error" @click="deleteInputTakeCare(key)"></i>
        </el-form-item>
        <div class="center">
          <el-row :gutter="20">
            <el-col :span="12" :offset="6">
              <el-button size="mini" @click="addInputTakeCare">Thêm đợt chăm sóc</el-button>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogFormPhaseTakeCare = false">Cancel</el-button>
        <el-button type="primary" @click="createPhaseTakeCare">Thêm mới</el-button>
      </span>
    </el-dialog>

    <el-dialog title="Danh sách đợt chăm sóc" :visible.sync="dialogListPhaseTakeCare">
      <el-tabs v-model="activeName">
        <el-tab-pane label="Nhắn tin" name="first">
          <el-table
              :data="listPhaseTakeCareMessage"
              border
              stripe
              style="width: 100%">
            <el-table-column
                align="center"
                prop="type"
                label="Loại lớp"
                width="180">
              <template slot-scope="{row}">
                {{ row.type }} {{ row.option_number_lesson !== '' ? ` - ${row.option_number_lesson} buổi` : '' }}
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                :label="`Đợt ${i}`"
                v-for="i in maxLengthPhaseTakeCare" :key="i">
              <template slot-scope="{row}">
                <template v-if="row.data[i - 1] !== undefined">
                  {{ row.data[i - 1].number_lesson_take_care_after }} buổi
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="Gửi đợt khảo sát" name="second">
          <el-table
              :data="listPhaseTakeCareSurvey"
              border
              stripe
              style="width: 100%">
            <el-table-column
                align="center"
                prop="type"
                label="Loại lớp"
                width="180">
              <template slot-scope="{row}">
                {{ row.type }} {{ row.option_number_lesson !== '' ? ` - ${row.option_number_lesson} buổi` : '' }}
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                :label="`Đợt ${i}`"
                v-for="i in maxLengthPhaseTakeCare" :key="i">
              <template slot-scope="{row}">
                <template v-if="row.data[i - 1] !== undefined">
                  {{ row.data[i - 1].number_lesson_take_care_after }} buổi
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogListPhaseTakeCare = false">Cancel</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import {mapActions, mapGetters} from "vuex";
import {isPast} from "date-fns";
import axios from "axios";
import api from "./../config/api";

const option_care = [
  {label: "Đã chăm sóc", value: "done"},
  {label: "Đã chăm sóc - muộn", value: "done-late"},
  {label: "Chưa chăm sóc", value: "not"},
  {label: "Đang chăm sóc", value: "doing"},
]
const option_type = [
  {value: "vip500", label: "Vip500"},
  {value: "vip15", label: "Vip15"},
  {value: "vip9", label: "Vip9"},
  {value: "vip1", label: "Vip1"},
  {value: "luyende", label: "Luyện đề"},
  {value: "online", label: "Online"},
  {value: "offline", label: "Offline"},
  {value: "captoc", label: "Cấp tốc"},
  {value: "matgoc", label: "Mất gốc"},
  {value: "kaiwa", label: "Kaiwa"},
  {value: "b2b", label: "Doanh nghiệp"},
  {value: "tokutei", label: "Tokutei"},
]

let vip_level_group_class_has_option = ['N2', 'N3', 'N5']
let type_group_class_has_option = ['vip15']

export default {
  data() {
    return {
      filters: {
        vip_level: undefined,
        name: undefined,
        type: undefined,
        care_status: undefined,
        number_lesson: undefined,
        phase_care: undefined,
        page: 0,
        limit: 10
      },
      isPast: isPast,
      option_care: option_care,
      option_type: option_type,
      dialogFormPhaseTakeCare: false,
      dialogListPhaseTakeCare: false,
      listPhaseTakeCare: [],
      formLabelWidth: '120px',
      formPhaseTakeCare: {
        vip_level: this.$route.params?.level,
        type_phase: "1",
        number_take_care: []
      },
      maxLengthPhaseTakeCare: 0,
      listPhaseTakeCareGroupByType: {},
      vip_level_group_class_has_option: vip_level_group_class_has_option,
      type_group_class_has_option: type_group_class_has_option,
      loading: false,   // Trạng thái loading
      canLoadMore: true, // Điều kiện load thêm dữ liệu
      classroomList: [],
      cancelTokenSource: null,  // Khai báo để lưu cancel token
      listPhaseTakeCareMessage: [],
      listPhaseTakeCareSurvey: [],
      activeName: 'first'
    };
  },
  watch: {
    formPhaseTakeCare: {
      deep: true,
      handler(old_value, new_value) {
      }
    }
  },
  created() {
    this.getListPhaseTakeCare()
  },
  computed: {
    ...mapGetters("classroom", ["optionClassRoom"]),
    disabled() {
      return this.loading
    }
  },
  mounted() {
    this.filters.vip_level = this.$route.params?.level;
    this.getOptionClassRoom(null);
    this.getClassroomList();
  },
  beforeDestroy() {
    this.resetClassroomList();
    // Hủy tất cả các request khi component bị hủy
    if (this.cancelTokenSource) {
      this.cancelTokenSource.cancel('Component destroyed.');
    }
  },
  methods: {
    ...mapActions("classroom", ["resetClassroomList", "getOptionClassRoom"]),
    async getClassroomList() {
      const fetchData = async () => {
        if (!this.canLoadMore) {
          this.loading = false;
          return;
        }

        // Nếu đã có token cũ thì hủy request cũ
        if (this.cancelTokenSource) {
          this.cancelTokenSource.cancel('Request canceled due to component destruction.');
        }

        // Tạo cancel token mới
        this.cancelTokenSource = axios.CancelToken.source();

        await api.post('/classroom/list/', this.filters, {
          cancelToken: this.cancelTokenSource.token  // Thêm cancel token vào request
        }).then(async res => {
          if (res.data.other.is_end) {
            this.loading = false
          } else {
            this.classroomList = [...this.classroomList, ...res.data.data]
            this.filters.page += 1;
            await fetchData();
          }
        }).catch(err => {
          if (err.response.status === 422) {
            this.$message.error(err.response.data.message)
          }
        })
      }
      await fetchData(); // Bắt đầu quá trình load dữ liệu
    },
    async search() {
      this.classroomList = []
      this.filters.page = 1
      await this.getClassroomList(this.filters);
    },
    addInputTakeCare() {
      this.formPhaseTakeCare.number_take_care.push('')
    },
    deleteInputTakeCare(key) {
      this.formPhaseTakeCare.number_take_care.splice(key, 1);
    },
    onInputNumber(key) {
      this.formPhaseTakeCare.number_take_care[key] = this.formPhaseTakeCare.number_take_care[key].replace(/\D/g, '');
    },
    async createPhaseTakeCare() {
      let res = await api.post('/classroom/create-take-care', this.formPhaseTakeCare).then(res => {
        if (res.data.code === 200) {
          this.$message.success('Thêm thành công')
          this.dialogFormPhaseTakeCare = false
        }
      }).catch(err => {
        if (err.response.status === 422) {
          this.$message.error(err.response.data.message)
          return
        }
        this.$message.error(err)
      })
    },
    async showDialogListPhaseTakeCare() {
      await this.getListPhaseTakeCare()
      this.dialogListPhaseTakeCare = true
    },
    async getListPhaseTakeCare() {
      await api.get('/classroom/list-phase-take-care', {
        params: {
          vip_level: this.$route.params?.level,
          // type_phase: 1
        }
      }).then(res => {
        if (res.data.code === 200) {
          this.listPhaseTakeCare = [];
          this.listPhaseTakeCareMessage = [];
          this.listPhaseTakeCareSurvey = [];
          this.maxLengthPhaseTakeCare = 0;
          this.listPhaseTakeCareGroupByType = res.data.data.phaseTakeCare
          for (let key in res.data.data.phaseTakeCare) {
            for (let key_number_lesson in res.data.data.phaseTakeCare[key]) {
              if (res.data.data.phaseTakeCare[key][key_number_lesson].length > this.maxLengthPhaseTakeCare) this.maxLengthPhaseTakeCare = res.data.data.phaseTakeCare[key][key_number_lesson].length;
              let item = {
                type: key,
                data: [],
                option_number_lesson: key_number_lesson
              }
              let arr_mes = res.data.data.phaseTakeCare[key][key_number_lesson].filter(e => e.type_phase === 1)
              let arr_survey = res.data.data.phaseTakeCare[key][key_number_lesson].filter(e => e.type_phase === 2)
              arr_mes.map((value, i) => {
                if (i == 0) {
                  value.number_lesson_take_care_after = value.number_lesson_take_care
                } else {
                  value.number_lesson_take_care_after = arr_mes[i - 1].number_lesson_take_care_after + value.number_lesson_take_care
                }
                return value;
              });
              arr_survey.map((value, i) => {
                if (i == 0) {
                  value.number_lesson_take_care_after = value.number_lesson_take_care
                } else {
                  value.number_lesson_take_care_after = arr_survey[i - 1].number_lesson_take_care_after + value.number_lesson_take_care
                }
                return value;
              });

              item.data = res.data.data.phaseTakeCare[key][key_number_lesson];

              let item_mes = {
                type: key,
                data: arr_mes,
                option_number_lesson: key_number_lesson
              }

              let item_survey = {
                type: key,
                data: arr_survey,
                option_number_lesson: key_number_lesson
              }

              this.listPhaseTakeCare.push(item)
              this.listPhaseTakeCareMessage.push(item_mes)
              this.listPhaseTakeCareSurvey.push(item_survey)
            }
          }
        }
      }).catch(err => {
      })
    },
    closeDialogFormPhaseTakeCare() {
      this.formPhaseTakeCare = {
        vip_level: this.$route.params?.level,
        number_take_care: []
      }
    }
  },
};
</script>
