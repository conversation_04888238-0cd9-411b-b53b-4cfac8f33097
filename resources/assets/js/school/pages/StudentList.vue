<template>
  <div>
    <div class="flex flex-col gap-3">
      <div class="flex items-center flex-start gap-2">
        <div>
          <label for="">Loại</label>
          <el-select v-model="params.type">
            <el-option value=""></el-option>
            <el-option
              v-for="type in [
                'vip500',
                'vip15',
                'luyende',
                'online',
                'b2b',
                'vip9',
                'offline',
                'vip1',
                'matgoc',
                'captoc',
                'tokutei',
              ]"
              :key="type"
              :value="type"
              >{{ type }}</el-option
            >
          </el-select>
        </div>
        <div>
          <label for="">Cấp độ</label>
          <el-select v-model="params.vip_level">
            <el-option value=""></el-option>
            <el-option
              v-for="type in ['N1', 'N2', 'N3', 'N4', 'N5', 'tokutei1', 'tokutei2']"
              :key="type"
              :value="type"
              >{{ type }}</el-option
            >
          </el-select>
        </div>
        <div>
          <label for=""><PERSON><PERSON><PERSON><PERSON> trừ</label>
          <el-select v-model="params.except" clearable>
            <el-option value=""></el-option>
            <el-option v-for="type in ['Đọc hiểu']" :key="type" :value="type">{{
              type
            }}</el-option>
          </el-select>
        </div>
        <el-date-picker
          v-model="params.timeRange"
          type="datetimerange"
          range-separator="-"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="HH:mm:ss dd-MM-yyyy"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="Từ ngày"
          end-placeholder="Đến ngày"
        >
        </el-date-picker>
        <el-button @click="fetchStudent">Lọc</el-button>
        <el-button @click="bulkChat">Nhắn tin hàng loạt</el-button>
      </div>
      <div class="flex items-center flex-start gap-2">
        <div class="flex items-center gap-2">
          <el-switch v-model="absenceCountFilter"> </el-switch>
          <label for="">Số ngày nghỉ</label>
          <el-select
            :disabled="!absenceCountFilter"
            v-if="absenceCountFilter"
            v-model="params.absenceCountDelimiter"
          >
            <el-option value="="> = </el-option>
            <el-option value=">"> > </el-option>
            <el-option value=">="> >= </el-option>
            <el-option value="<"> < </el-option>
            <el-option value="<="> <= </el-option>
          </el-select>
        </div>

        <el-input-number
          :disabled="!absenceCountFilter"
          v-if="absenceCountFilter"
          v-model="params.absenceCountValue"
        ></el-input-number>

        <div class="flex items-center gap-2">
          <el-switch v-model="missedExamCountFilter"> </el-switch>
          <label for="">Số bài test chưa làm</label>
          <el-select
            :disabled="!missedExamCountFilter"
            v-if="missedExamCountFilter"
            v-model="params.missedExamCountDelimiter"
          >
            <el-option value="="> = </el-option>
            <el-option value=">"> > </el-option>
            <el-option value=">="> >= </el-option>
            <el-option value="<"> < </el-option>
            <el-option value="<="> <= </el-option>
          </el-select>
        </div>

        <el-input-number
          :disabled="!missedExamCountFilter"
          v-if="missedExamCountFilter"
          v-model="params.missedExamCountValue"
        ></el-input-number>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="filteredStudents"
      border
      height="75vh"
      style="width: 100%; margin-top: 10px"
      header-cell-class-name="user-table__header"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="id" label="ID" width="80" fixed> </el-table-column>
      <el-table-column prop="email" label="Email">
        <template slot-scope="scope">
          <div>
            {{ scope.row.email }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="Họ và tên">
        <template slot-scope="scope">
          <div>
            {{ scope.row.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="absence_count" label="Số ngày nghỉ" sortable>
        <template slot-scope="scope">
          <div>
            {{ scope.row.absenceCount || 0 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Số bài test không làm" sortable>
        <template slot-scope="scope">
          <div>
            <el-popover placement="bottom" width="400" trigger="click">
              <div v-for="exam in scope.row.missedExam">
                {{ exam.title || "Bài test không có tiêu đề" }} -
                {{ exam.time_start }}
              </div>
              <p slot="reference" class="cursor-pointer">
                {{ scope.row.missedExamCount || 0 }}
              </p>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="title"
        sortable
        label="Nhóm chat"
      ></el-table-column>
    </el-table>
    <el-dialog title="Soạn tin nhắn" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="Gửi riêng bằng admin">
          <el-select v-model="form.selectedAdmin" filterable>
            <el-option value=""></el-option>
            <el-option
              v-for="admin in admins"
              :key="admin.user?.id"
              :value="admin.user?.id"
              :label="`${admin.user?.name} ${admin.user?.email}`"
              ><b>{{ admin.user?.name }}</b> {{ admin.user?.email }}</el-option
            >
          </el-select>
        </el-form-item>
        <el-form-item label="Nội dung">
          <el-input
            type="textarea"
            :rows="4"
            placeholder=""
            v-model="form.content"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">Huỷ</el-button>
        <el-button type="primary" @click="sendBulkChat">{{
          sendLoading ? "Đang gửi" : "Gửi"
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { uniq, cloneDeep } from "lodash";
import moment from "moment";
export default {
  data() {
    return {
      loading: true,
      sendLoading: false,
      dialogFormVisible: false,
      form: {
        content: "",
        selectedAdmin: "",
      },
      absenceCountFilter: true,
      missedExamCountFilter: true,
      params: {
        type: "vip15",
        vip_level: "N5",
        absenceCountDelimiter: "=",
        missedExamCountDelimiter: "=",
        absenceCountValue: 2,
        missedExamCountValue: 2,
        except: "",
        timeRange: [
          moment()
            .subtract(1, "days")
            .startOf("day")
            .format("yyyy-MM-DD HH:mm:ss"),
          moment().endOf("day").format("yyyy-MM-DD HH:mm:ss"),
        ],
      },
      selectedStudents: [],
    };
  },
  mounted() {
    this.setPage("Danh sách học viên");
    this.fetchStudent();
  },
  computed: {
    ...mapGetters("student", ["students", "admins", "exams", "absence"]),
    filteredStudents() {
      return this.students
        .map((student) => {
          const studentGroupExam = this.exams[student.group_id] || [];
          let missedExamCount = 0;
          let missedExam = [];
          if (studentGroupExam) {
            studentGroupExam.forEach((exam) => {
              const tmp = exam.exam_results.findIndex(
                (result) => result.user_id === student.id
              );
              if (tmp === -1) {
                const tmpExam = cloneDeep(exam);
                delete tmpExam.result;
                missedExam.push(tmpExam);
                missedExamCount++;
              }
            });
          }

          student.absenceCount = this.absence.filter(
            (item) => item.student_id === student.id
          ).length;
          student.missedExamCount = missedExamCount;
          student.missedExam = missedExam;
          return student;
        })
        .filter((student) => {
          let cond_absence = true;
          let cond_exam = true;
          switch (this.params.absenceCountDelimiter) {
            case "=":
              cond_absence =
                student.absenceCount === this.params.absenceCountValue;
              break;
            case ">":
              cond_absence =
                student.absenceCount > this.params.absenceCountValue;
              break;
            case ">=":
              cond_absence =
                student.absenceCount >= this.params.absenceCountValue;
              break;
            case "<":
              cond_absence =
                student.absenceCount < this.params.absenceCountValue;
              break;
            case "<=":
              cond_absence =
                student.absenceCount <= this.params.absenceCountValue;
              break;
            default:
              cond_absence =
                student.absenceCount === this.params.absenceCountValue;
          }
          switch (this.params.missedExamCountDelimiter) {
            case "=":
              cond_exam =
                student.missedExamCount === this.params.missedExamCountValue;
              break;
            case ">":
              cond_exam =
                student.missedExamCount > this.params.missedExamCountValue;
              break;
            case ">=":
              cond_exam =
                student.missedExamCount >= this.params.missedExamCountValue;
              break;
            case "<":
              cond_exam =
                student.missedExamCount < this.params.missedExamCountValue;
              break;
            case "<=":
              cond_exam =
                student.missedExamCount <= this.params.missedExamCountValue;
              break;
            default:
              cond_exam =
                student.missedExamCount === this.params.missedExamCountValue;
          }
          return cond_absence && cond_exam;
        });
    },
  },
  watch: {
    absenceCountFilter(val) {
      if (val) {
        this.params = {
          ...this.params,
          absenceCountDelimiter: "=",
          absenceCountValue: 2,
        };
      } else {
        this.params = {
          ...this.params,
          absenceCountDelimiter: ">=",
          absenceCountValue: 0,
        };
      }
    },
    missedExamCountFilter(val) {
      if (val) {
        this.params = {
          ...this.params,
          missedExamCountDelimiter: "=",
          missedExamCountValue: 2,
        };
      } else {
        this.params = {
          ...this.params,
          missedExamCountDelimiter: ">=",
          missedExamCountValue: 0,
        };
      }
    },
    filteredStudents: {
      deep: true,
      handler() {
        this.selectedStudents = [];
      },
    },
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    ...mapActions("student", ["getStudents", "sendBulkMessage"]),
    handleSelectionChange(val) {
      this.selectedStudents = val;
    },
    async fetchStudent() {
      this.loading = true;
      const data = { ...this.params };
      if (this.params.timeRange.length) {
        data.from = this.params.timeRange[0];
        data.to = this.params.timeRange[1];
      }
      await this.getStudents({ params: data });
      this.loading = false;
    },
    bulkChat() {
      this.dialogFormVisible = true;
    },
    async sendBulkChat() {
      if (this.sendLoading) return;
      this.sendLoading = true;
      const data = {
        message: this.form.content,
        selectedAdmin: this.form.selectedAdmin,
        listIds: uniq(this.selectedStudents.map((o) => o.id || null)).join(","),
        scheduleAt: "null",
      };

      await this.sendBulkMessage(data);
      this.form = {
        content: "",
        selectedAdmin: "",
      };
      this.dialogFormVisible = false;
      this.sendLoading = false;
    },
  },
};
</script>
<style>
.el-table .cell {
  word-break: break-word;
}
.el-table .el-table__cell {
  vertical-align: baseline !important;
}
.user-table__header {
  background-color: rgb(217 249 157) !important;
  color: black;
}
</style>
