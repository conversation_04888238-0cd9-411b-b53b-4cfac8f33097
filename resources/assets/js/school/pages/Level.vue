<template>
  <div>
    <router-link
      v-for="type in typeList"
      :key="`type-${type}`"
      :to="{ name: 'classroomList', params: { level: `N${type}` } }"
    >
      <div
        :key="`class-${type}`"
        class="p-5 mt-2 shadow hover:shadow-lg cursor-pointer"
      >
        Online VIP {{ parseInt(type) !== type ? "" : "N" }}{{ type }}
      </div>
    </router-link>
  </div>
</template>
<script>
import { mapActions } from "vuex";

export default {
  data() {
    return {
      typeList: [1, 2, 3, 4, 5, "KAIWA", "tokutei"],
    };
  },
  mounted() {
    this.setPage("<PERSON>h sách cấp độ");
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
  },
};
</script>
