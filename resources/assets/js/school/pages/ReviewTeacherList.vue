<template>
  <div>
    <div class="flex justify-between items-end mt-4" style="align-items: flex-end">
      <div class="flex items-center flex-grow">
        <el-input class="mr-3" v-model="filters.name" placeholder="<PERSON><PERSON><PERSON> kiếm bằng tên" style="width: 400px" @keyup.enter.native="getList"></el-input>
        <el-checkbox-group v-model="filters.level" @change="changeLevel">
          <el-checkbox-button label="N1">N1</el-checkbox-button>
          <el-checkbox-button label="N2">N2</el-checkbox-button>
          <el-checkbox-button label="N3">N3</el-checkbox-button>
          <el-checkbox-button label="N4">N4</el-checkbox-button>
          <el-checkbox-button label="N5">N5</el-checkbox-button>
        </el-checkbox-group>
      </div>
      <img :src="crits" alt="" class="w-[400px] mr-3" style="max-width: 600px">
      <button
        type="button"
        class="inline-flex items-center gap-x-1.5 rounded-md bg-green-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        @click="exportExcel"
      >
        Xuất excel
      </button>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      height="75vh"
      style="width: 100%; margin-top: 10px;"
      header-cell-class-name="user-table__header"
      :span-method="objectSpanMethod"
    >
      <el-table-column
        prop="name"
        label="Tên giáo viên"
      >
        <template slot-scope="scope">
          <div @click="confirmAddRow(scope.row.id)" class="cursor-pointer hover:text-blue-700">{{ scope.row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="levels"
        label="Cấp độ"
        width="50px"
      >
        <template slot-scope="scope">
          <div v-for="level in scope.row.levels">{{ level }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="groupCount"
        label="Số lớp"
        width="50px"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        label="Thông tin lớp (sĩ số, ngày khai giảng)"
      >
        <template slot-scope="scope">
          <div v-for="group in scope.row.groups">
            <div>{{ group.group?.vip_level }} | {{ group.group?.size }} | {{ group.group?.start_date }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="created_at"
        label="Thời điểm đánh giá"
      >
      </el-table-column>
      <el-table-column
        prop="created_by"
        label="Người đánh giá"
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.reviewer?.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Tiêu chí đánh giá" align="center">
        <el-table-column
          v-for="crit in 10"
          :key="`crit_${crit}`"
          width="50px"
          align="center"
        >
          <template slot="header" slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="critOptions[crit - 1]" placement="bottom">
              <div>{{ crit }}</div>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div class="w-full h-full cursor-pointer hover:bg-blue-100 rounded-sm" @click="updateField(scope.row.id, `crit_${crit}`)">
              {{ scope.row[`crit_${crit}`] || '--' }}
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column
        prop="total"
        label="Tổng điểm"
        width="60px"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="avg"
        label="Điểm TB"
        width="60px"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="rank"
        label="Xếp hạng"
        width="60px"
        align="center"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            width="280"
            trigger="click">
              <el-radio-group v-model="scope.row.rank" size="small" class="flex gap-2 justify-center" @change="update($event, scope.row.id, 'rank')">
                <el-radio-button v-for="item in rankOptions" :key="`rank-${item}`" :label="item"></el-radio-button>
              </el-radio-group>
              <div
                slot="reference"
                class="w-full h-full cursor-pointer hover:bg-blue-100 rounded-sm"
              >
                {{ scope.row.rank || '--' }}
              </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        prop="title"
        label="Danh hiệu"
        align="center"
        width="80px"
      >
        <template slot-scope="scope">
          <el-popover
            placement="bottom"
            width="460"
            trigger="click">
            <el-radio-group v-model="scope.row.title" size="small" class="flex gap-2 justify-center" @change="update($event, scope.row.id, 'title')">
              <el-radio-button v-for="item in titleOptions" :key="`title-${item.value}`" :label="item.value">{{ item.label }}</el-radio-button>
            </el-radio-group>
            <div
              slot="reference"
              class="w-full h-full cursor-pointer hover:bg-blue-100 rounded-sm"
            >
              {{ titles[scope.row.title - 1] || '--' }}
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 10px;">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="filters.page"
        :page-sizes="[20, 50, 100, 500, 1000, 5000, 10000]"
        :page-size.sync="filters.perPage"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import { uniq, orderBy, findIndex, mean, meanBy, intersection, maxBy } from 'lodash'
import { Parser } from '@json2csv/plainjs';
import crits from './../assets/img/crits.png'

export default {
  data() {
    return {
      crits: crits,
      loading: false,
      filters: {
        page: 1,
        per_page: 20,
        name: '',
        level: [],
      },
      rankOptions: ['A+', 'A', 'B', 'C', 'D', 'F'],
      titleOptions: [
        {value: 1, label: 'Xuất sắc'},
        {value: 2, label: 'Giỏi'},
        {value: 3, label: 'Khá'},
        {value: 4, label: 'Trung bình'},
        {value: 5, label: 'Kém'},
        {value: 6, label: 'Không đạt'},
      ],
      critOptions: [
        'Trình độ nghiệp vụ sư phạm (Bao gồm các kỹ năng trong nghề giáo, như kỹ năng soạn thảo giáo án, kỹ năng đứng lớp, truyền đạt cho học sinh hay kỹ năng ứng xử giải quyết tinh huống phát sinh trong công tác giảng dạy…)',
        'Sử dụng phương pháp dạy học và giáo dục đúng, chuẩn của trung tâm nhằm phát triển năng lực học sinh',
        'Thực hiện hiệu quả các biện pháp tư vấn và hỗ trợ phù hợp với từng đối tượng học sinh trong hoạt động dạy học và giáo dục',
        'Chủ động điều chỉnh kế hoạch dạy học và giáo dục phù hợp với điều kiện thực tế của học sinh trong lớp(cần trao đổi trước với người phụ trách)',
        'Chủ động cập nhật, vận dụng sáng tạo các hình thức, phương pháp, công cụ kiểm tra đánh giá theo hướng phát triển năng lực học sinh',
        'Tạo dựng mối quan hệ lành mạnh, tin tưởng giữa trung tâm, giáo viên và học sinh. Nhận được đánh giá tốt từ học viên qua khảo sát/review trên page Dũng Mori.',
        'Chủ động phối hợp với trung tâm, người phụ trách hoặc trợ giảng và các bên liên quan trong việc thực hiện các biện pháp hướng dẫn, hỗ trợ và động viên học sinh học tập, thực hiện chương trình, kế hoạch dạy học, định hướng cho học viên',
        'Tinh thần học tập nâng cao trình độ chuyên môn, nghiệp vụ',
        'Tỷ lệ đi học của học viên',
        'Tỷ lệ học viên học lên (có thể xét từ lớp kết thúc gần nhất)',
      ]
    }
  },
  computed: {
    ...mapGetters('review', ['currentReviewBatch', 'batchReviews', 'total']),
    titles() {
      return this.titleOptions.map(o => o.label)
    },
    curDate: {
      get() {
        return this.currentDate;
      },
      set(newValue) {
        this.changeCurrentDate(newValue);
      },
    },
    reviewId() {
      return this.$route.params.id;
    },
    tableData() {
      const reviews = this.batchReviews
      const tmp = reviews.map((review, index) => {
        review.idx = index + 1
        review.name = `${review.user.last_name} ${review.user.first_name}`;
        const countedCrit = [review.crit_1, review.crit_2, review.crit_3, review.crit_4, review.crit_5, review.crit_6, review.crit_7, review.crit_8]
        review.total = mean(countedCrit).toFixed(1);
        review.levels = uniq(review.user.groups.map(r => r.group?.vip_level))
        review.groups = review.user.groups
        review.maxGroup = maxBy(review.groups, function(o) {
          return o.group.start_date
        })

        review.groupCount = review.user.groups.length
        review.avg = meanBy(reviews.filter(o => o.user_id === review.user_id), o => mean([o.crit_1, o.crit_2, o.crit_3, o.crit_4, o.crit_5, o.crit_6, o.crit_7, o.crit_8])).toFixed(1)
        return review;
      });
      return orderBy(tmp, ['maxGroup.group.start_date']);
    },
  },
  async mounted() {
    this.setPage('Đánh giá giáo viên');
    await this.getList();
  },
  methods: {
    ...mapActions('ui', ['setPage']),
    ...mapActions('review', ['getUserReviews', 'createReview', 'updateReview']),
    async changeLevel() {
      await this.getList();
    },
    async handleCurrentChange(page) {
      await this.getList();
    },
    async handleSizeChange(perPage) {
      await this.getList();
    },
    checkScope(scope) {
      // console.log(scope.row)
    },
    async update(value, id, field) {
      const data = { id, field, value }
      await this.updateReview(data)
    },
    async updateField(id, field) {
      const value = prompt('Nhập số điểm từ 0 - 10')
      if (value) {
        const data = { id, field, value }
        await this.updateReview(data)
      }
    },
    async confirmAddRow(id) {
      const conf = confirm('Xác nhận thêm review')
      if (conf) {
        await this.createReview({ id })
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const firstIdx = findIndex(this.tableData, o => o.user_id === row.user_id)
      const arr = this.tableData.filter(o => o.user_id === row.user_id)
      if ((columnIndex < 4 || columnIndex > 17) && arr.length > 1) {
        if (rowIndex === firstIdx) {
          return {
            rowspan: arr.length,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    async getList() {
      this.loading = true;
      const data = {
        id: this.reviewId,
        ...this.filters
      }
      await this.getUserReviews(data);
      this.loading = false;
    },
    async exportExcel() {
      this.loading = true;
      try {
        const opts = {
          fields: [
            {
              label: 'Giáo viên',
              value: 'name'
            },
            {
              label: 'Cấp độ',
              value: (record) => record.levels.join(', ')
            },
            {
              label: 'Số lớp',
              value: 'groupCount'
            },
            {
              label: 'Thông tin lớp',
              value: (record) => {
                let str = ''
                record.groups.forEach(g => {
                  str += `${g.group?.vip_level} | ${g.group?.size} | ${g.group?.start_date} \n`
                })
                return str
              }
            },
            {
              label: 'Thời điểm đánh giá',
              value: 'created_at'
            },
            {
              label: 'Người đánh giá',
              value: (record) => record.reviewer?.name || '--'
            },
            {
              label: '1',
              value: 'crit_1'
            },
            {
              label: '2',
              value: 'crit_2'
            },
            {
              label: '3',
              value: 'crit_3'
            },
            {
              label: '4',
              value: 'crit_4'
            },
            {
              label: '5',
              value: 'crit_5'
            },
            {
              label: '6',
              value: 'crit_6'
            },
            {
              label: '7',
              value: 'crit_7'
            },
            {
              label: '8',
              value: 'crit_8'
            },
            {
              label: '9',
              value: 'crit_9'
            },
            {
              label: '10',
              value: 'crit_10'
            },
            {
              label: 'Tổng điểm',
              value: 'total'
            },
            {
              label: 'Điểm TB',
              value: 'avg'
            },
            {
              label: 'Xếp hạng',
              value: 'rank'
            },
            {
              label: 'Danh hiệu',
              value: (record) => this.titles[record.title - 1]
            },
          ],
          withBOM: true
        };
        const parser = new Parser(opts);
        const csv = parser.parse(this.tableData);
        const anchor = document.createElement('a');
        anchor.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csv);
        anchor.target = '_blank';
        anchor.download = `${this.currentReviewBatch.title} ${Date.now()}.csv`;
        anchor.click();
      } catch (err) {
        console.error(err);
      }
      // const data = {
      //   ...this.filters,
      //   curDate: this.currentDate,
      //   page: this.page,
      //   perPage: 500,
      // }
      // axios.post(window.location.origin + '/backend/school/api/v1/user/export', data, {
      //   responseType: 'blob'
      // }).then((response) => {
      //   const url = URL.createObjectURL(new Blob([response.data], {
      //     type: 'application/vnd.ms-excel'
      //   }))
      //   const link = document.createElement('a')
      //   link.href = url
      //   link.setAttribute('download', 'cham_cong_' + Date.now() + '.xlsx')
      //   document.body.appendChild(link)
      //   link.click()
      // });
      this.loading = false;
    },
  }
}
</script>
