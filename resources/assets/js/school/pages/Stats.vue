<template>
  <div>
    <div class="flex items-center justify-between">
      <div class="flex items-center justify-start gap-3">
        <el-select v-model="params.type" placeholder="Lọc loại lớp">
          <el-option value=""></el-option>
          <el-option
            v-for="type in types"
            :key="type"
            :value="type"
            :label="type"
          ></el-option>
        </el-select>
        <el-select v-model="params.vip_level" placeholder="Lọc trình độ">
          <el-option value=""></el-option>
          <el-option
            v-for="level in levels"
            :key="level"
            :value="level"
            :label="level"
          ></el-option>
        </el-select>
        <el-select v-model="params.timeGroup" placeholder="Nhóm theo">
          <el-option value=""></el-option>
          <el-option
            v-for="group in timeGroups"
            :key="group.value"
            :value="group.value"
            :label="group.label"
          ></el-option>
        </el-select>
        <el-date-picker
          v-model="params.timeRange"
          type="datetimerange"
          range-separator="-"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="HH:mm:ss dd-MM-yyyy"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="Từ ngày"
          end-placeholder="Đến ngày"
          @change="getData"
        >
        </el-date-picker>
        <el-button @click="getData">Lọc</el-button>
      </div>
      <div class="flex items-center justify-start"></div>
    </div>

    <Chart
      ref="chart"
      id="my-chart-id"
      :options="chartOptions"
      :data="chartData"
      :type="'bar'"
    />
  </div>
</template>

<script>
import { Bar, Chart } from "vue-chartjs";
import "chartjs-adapter-date-fns";
import ChartDataLabels from "chartjs-plugin-datalabels";
import axios from "axios";
import {
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  addDays,
  addWeeks,
  addMonths,
  format,
  isWithinInterval,
} from "date-fns";
import { uniq } from "lodash";
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  TimeScale,
} from "chart.js/auto";

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  TimeScale,
  ChartDataLabels
);

export default {
  name: "BarChart",
  components: { Bar, Chart },
  data() {
    return {
      chartOptions: {
        plugins: {
          title: {
            display: true,
            text: "Tỷ lệ học viên đi học",
          },
          tooltip: {
            callbacks: {
              footer(tooltipItems) {
                let sum = 0;

                tooltipItems.forEach(function (tooltipItem, index) {
                  if (index) {
                    sum += tooltipItem.parsed.y;
                  }
                });
                return "Tổng: " + sum;
              },
            },
          },
        },
        responsive: true,
        datalabels: {
          color: "white",
          labels: {
            title: {
              font: {
                weight: "bold",
              },
            },
            value: {
              color: "white",
            },
          },
        },
        scales: {
          x: {
            stacked: true,
          },
          y: {
            stacked: true,
            type: "linear",
            display: true,
            position: "left",
          },
          y1: {
            type: "linear",
            display: true,
            position: "right",

            // grid line settings
            grid: {
              drawOnChartArea: false, // only want the grid lines for one axis to show up
            },
            min: 0,
            max: 100,
          },
        },
        interaction: {
          intersect: false,
          mode: "index",
        },
      },
      types: [
        "vip500",
        "vip15",
        "luyende",
        "online",
        "b2b",
        "vip9",
        "offline",
        "vip1",
        "matgoc",
        "captoc",
        "tokutei",
      ],
      levels: ["N1", "N2", "N3", "N4", "N5", "tokutei1", "tokutei2"],
      timeGroups: [
        { value: "day", label: "Ngày" },
        { value: "week", label: "Tuần" },
        { value: "month", label: "Tháng" },
      ],
      params: {
        type: "vip15",
        vip_level: "N3",
        timeRange: [
          startOfWeek(new Date(), { weekStartsOn: 1 }),
          endOfWeek(new Date(), { weekStartsOn: 1 }),
        ],
        timeGroup: "day",
      },
      listData: [],
    };
  },
  computed: {
    timeRanges() {
      let dateArray = [];
      let currentDate = startOfDay(this.params.timeRange[0]);
      switch (this.params.timeGroup) {
        case "day":
          while (currentDate <= endOfDay(this.params.timeRange[1])) {
            dateArray.push([startOfDay(currentDate), endOfDay(currentDate)]);
            currentDate = addDays(currentDate, 1);
          }
          return dateArray;
        case "week":
          while (currentDate <= endOfDay(this.params.timeRange[1])) {
            dateArray.push([
              startOfWeek(currentDate, { weekStartsOn: 1 }),
              endOfWeek(currentDate, { weekStartsOn: 1 }),
            ]);
            currentDate = addWeeks(currentDate, 1, { weekStartsOn: 1 });
          }
          return dateArray;
        case "month":
          while (currentDate <= endOfDay(this.params.timeRange[1])) {
            dateArray.push([
              startOfMonth(currentDate),
              endOfMonth(currentDate),
            ]);
            currentDate = addMonths(currentDate, 1);
          }
          return dateArray;
        default:
          return dateArray;
      }
    },
    chartData() {
      return {
        labels: this.timeRanges.map((o) => {
          switch (this.params.timeGroup) {
            case "day":
              return format(o[0], "dd-MM-yyyy");
            case "week":
              return o.map((i) => format(i, "dd-MM-yyyy")).join(" - ");
            case "month":
              return format(o[0], "LLLL");
            default:
              return format(o[0], "dd-MM-yyyy");
          }
        }),
        datasets: [
          {
            label: "Nghỉ không phép",
            data: this.timeRanges.map((attendance) => {
              let total = 0;
              const dateAttendance = this.listData
                .filter((o) => {
                  return isWithinInterval(
                    startOfDay(new Date(o.date_attendance)),
                    {
                      start: attendance[0],
                      end: attendance[1],
                    }
                  );
                })
                .map((o) =>
                  o.attendances.filter((att) => att.status === 0 && !att.note)
                );
              dateAttendance.forEach((att) => {
                total += att.length;
              });
              return total;
            }),
            backgroundColor: "#fe6384",
            order: 2,
            yAxisID: "y",
          },
          {
            label: "Nghỉ có phép",
            data: this.timeRanges.map((attendance) => {
              let total = 0;
              const dateAttendance = this.listData
                .filter((o) => {
                  return isWithinInterval(
                    startOfDay(new Date(o.date_attendance)),
                    {
                      start: attendance[0],
                      end: attendance[1],
                    }
                  );
                })
                .map((o) =>
                  o.attendances.filter((att) => att.status === 0 && att.note)
                );
              dateAttendance.forEach((att) => {
                total += att.length;
              });
              return total;
            }),
            backgroundColor: "#36a2eb",
            order: 2,
            yAxisID: "y",
          },
          {
            label: "Đi",
            data: this.timeRanges.map((attendance) => {
              let total = 0;
              const dateAttendance = this.listData
                .filter((o) => {
                  return isWithinInterval(
                    startOfDay(new Date(o.date_attendance)),
                    {
                      start: attendance[0],
                      end: attendance[1],
                    }
                  );
                })
                .map((o) => o.attendances.filter((att) => att.status === 1));
              dateAttendance.forEach((att) => {
                total += att.length;
              });
              return total;
            }),
            backgroundColor: "#4bc0c0",
            order: 2,
            yAxisID: "y",
          },
          {
            label: "Tỷ lệ đi học (%)",
            data: this.timeRanges.map((attendance) => {
              let total = 0;
              let totalPresent = 0;
              const dateAttendance = this.listData
                .filter((o) => {
                  return isWithinInterval(
                    startOfDay(new Date(o.date_attendance)),
                    {
                      start: attendance[0],
                      end: attendance[1],
                    }
                  );
                })
                .map((o) => o.attendances);
              dateAttendance.forEach((att) => {
                total += att.length;
              });
              const present = this.listData
                .filter((o) => {
                  return isWithinInterval(
                    startOfDay(new Date(o.date_attendance)),
                    {
                      start: attendance[0],
                      end: attendance[1],
                    }
                  );
                })
                .map((o) => o.attendances.filter((att) => att.status === 1));
              present.forEach((att) => {
                totalPresent += att.length;
              });
              return total ? Math.round((totalPresent * 100) / total) : 0;
            }),
            fill: false,
            backgroundColor: "rgba(75, 192, 192, 0.2)",
            borderColor: "rgb(255,240,98)",
            tension: 0.1,
            type: "line",
            order: 1,
            yAxisID: "y1",
          },
        ],
      };
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      const vm = this;
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      await axios
        .get(
          window.location.origin +
            "/backend/school/api/v1/students/get-course-time-stats",
          {
            params: this.params,
          }
        )
        .then(async (res) => {
          vm.listData = res.data.data;
          loading.close();
        });
    },
  },
};
</script>
