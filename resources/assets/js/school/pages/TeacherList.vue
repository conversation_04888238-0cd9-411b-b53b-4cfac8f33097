<template>
  <div>
    <el-table :data="tableData" height="85vh" style="width: 100%" @row-click="editRow">
      <el-table-column fixed type="index" label="STT" width="50">
      </el-table-column>
      <el-table-column
        fixed
        prop="full_name"
        label="Họ và tên"
        width="250"
        sortable
      >
        <template slot-scope="scope">
          <div class="flex items-center gap-2">
            <el-avatar :size="'small'" :src="url + '/' + scope.row.avatar_url"></el-avatar>
            <span>
            {{ scope.row.full_name }}
          </span>
          </div>

        </template>
      </el-table-column>
      <el-table-column
        prop="dmr_code"
        label="Mã GV"
        width="100"
        sortable
      ></el-table-column>
      <el-table-column prop="birth_day" label="Ngày sinh" width="110">
        <template slot-scope="scope">
          <span>
            {{ format(scope.row.birth_day, "dd-MM-yyyy") }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="home" label="Địa chỉ hiện tại" width="300">
      </el-table-column>
      <el-table-column prop="metadata.accent" label="Ngôn ngữ giảng dạy" width="100">
      </el-table-column>
      <el-table-column prop="phone" label="Số điện thoại" width="150">
      </el-table-column>
      <el-table-column prop="email" label="Email" width="250">
      </el-table-column>
      <el-table-column prop="metadata.contact_app" label="Liên hệ"> </el-table-column>
      <el-table-column prop="metadata.contact_detail" label="TK Liên hệ"> </el-table-column>
      <el-table-column prop="level_of_japanese" label="Trình độ" width="100">
      </el-table-column>
      <el-table-column prop="metadata.teaching_level" label="Cấp độ dạy" width="100">
      </el-table-column>
      <el-table-column prop="metadata.jp_exp" label="Kinh nghiệm đi Nhật" width="100">
        <template slot-scope="scope">
          <span>
            {{ scope.row.metadata?.jp_exp ? 'Có' : 'Không' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="metadata.jp_job" label="Công việc ở Nhật" width="200">
      </el-table-column>
      <el-table-column prop="metadata.school" label="Trường tốt nghiệp" width="200">
      </el-table-column>
      <el-table-column prop="metadata.teaching_exp" label="Kinh nghiệm giảng dạy" width="200">
      </el-table-column>
      <el-table-column prop="note" label="Ghi chú" width="100">
      </el-table-column>
      <el-table-column prop="metadata.ad_image" label="Ảnh hoàn thiện" width="100">
        <template slot-scope="scope">
          <div class="flex items-center gap-3">
            <el-avatar
              :size="'medium'"
              :src="url + '/' + scope.row.metadata?.ad_image"
            ></el-avatar>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer
      :title="`${currentUser?.dmr_code} ${currentUser?.full_name}` || '--'"
      :visible.sync="editUserDialog"
      direction="ltr"
      size="700px"
    >
      <edit-teacher-form
        v-if="editUserDialog"
        :user="currentUser"
        @updated="handleUserUpdated"
      />
    </el-drawer>
  </div>
</template>
<script>
import axios from "axios";
import { format } from "date-fns";
import { cloneDeep } from "lodash";
import EditTeacherForm from "../components/EditTeacherForm.vue";
export default {
  components: { EditTeacherForm },
  data() {
    return {
      listData: [],
      loading: false,
      params: {},
      format,
      url: window.location.origin,
      editUserDialog: false,
      currentUser: null,
    };
  },
  computed: {
    tableData() {
      return cloneDeep(this.listData).map((item) => {
        item.full_name = this.getFullName(item.first_name, item.last_name);
        item.dmr_code = this.getDMRCode(item.first_name, item.last_name);
        return item;
      });
    },
  },
  watch: {
    editUserDialog(value) {
      if (!value) {
        this.currentUser = null
      }
    }
  },
  methods: {
    async getData() {
      const vm = this;
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      await axios
        .get(window.location.origin + "/backend/school/api/v1/user/teachers", {
          params: this.params,
        })
        .then(async (res) => {
          vm.listData = res.data.data;
          loading.close();
        });
    },
    getFullName(firstName, lastName) {
      const regex = /(?:DMR\s?\d*|KW\s?\d*)(.*)/g;
      const fullName = `${lastName} ${firstName}`;
      const match = regex.exec(fullName);
      return match ? match[1]?.trim() || match[2]?.trim() : fullName;
    },
    getDMRCode(firstName, lastName) {
      const fullName = `${lastName} ${firstName}`;
      const regex = /(DMR\s?\d*|KW\s?\d*)/g;
      const match = fullName.match(regex);
      return match ? match[0]?.trim() : "";
    },
    editRow(user) {
      this.currentUser = user
      this.editUserDialog = true
    },
    handleUserUpdated(user) {
      this.editUserDialog = false
      const idx = this.listData.findIndex(o => o.id === this.currentUser.id)
      const tmp = {
        ...this.listData[idx],
        ...user
      }
      this.$set(this.listData, idx, tmp)
    }
  },
  mounted() {
    this.getData();
  },
};
</script>
