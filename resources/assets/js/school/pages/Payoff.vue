<template>
  <div>
    <div class="flex justify-start items-center">
      <el-button type="success" @click="dialog = true">Thêm</el-button>
    </div>
    <el-drawer
      title="Thêm phần thưởng / lỗi phạt"
      :visible.sync="dialog"
      direction="ltr"
      size="500px"
    >
      <add-payoff-form
        v-if="dialog"
        :payoff="currentPayoff"
        @submit="handleSubmit"
      />
    </el-drawer>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%; margin-top: 10px"
    >
      <el-table-column prop="id" label="ID" width="80"> </el-table-column>
      <el-table-column prop="title" label="Tiêu đề"> </el-table-column>
      <el-table-column prop="price" label="Số tiền" :formatter="decimal">
      </el-table-column>
      <el-table-column
        prop="yen_price"
        label="Số tiền (Yen)"
        :formatter="decimal"
      >
      </el-table-column>
      <el-table-column prop="type" label="Phân loại" :formatter="type">
      </el-table-column>
      <el-table-column label="">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleEdit(scope.row)">Sửa</el-button>
          <el-popconfirm
            title="Xác nhận xoá"
            @confirm="handleDelete(scope.row)"
          >
            <el-button size="mini" type="danger" slot="reference"
              >Xoá</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { find, orderBy } from "lodash";
import AddPayoffForm from "./../components/AddPayoffForm";

export default {
  name: "RewardFault",
  components: { AddPayoffForm },
  data() {
    return {
      loading: false,
      dialog: false,
      currentPayoff: {},
    };
  },
  computed: {
    ...mapGetters("payoff", ["payoffList"]),
    tableData() {
      return orderBy(this.payoffList, ["id"], ["desc"]);
    },
  },
  mounted() {
    this.setPage("Thưởng thành tích / Lỗi phạt");
    this.getPayoffList();
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    ...mapActions("payoff", ["getPayoffList", "deletePayoff"]),
    decimal(row, column) {
      return new Intl.NumberFormat("vi-VN").format(row[column.property]);
    },
    type(row, column) {
      return parseInt(row[column.property]) ? "Thưởng" : "Phạt";
    },
    handleEdit(row, column) {
      this.currentPayoff = { ...row };
      this.dialog = true;
    },
    handleDelete(row, column) {
      this.deletePayoff(row.id);
    },
    handleSubmit() {
      this.dialog = false;
      this.getPayoffList();
    },
  },
};
</script>
<style></style>
