<template>
  <div>
    <el-drawer
      title="Thêm bản đánh giá giáo viên"
      :visible.sync="dialog"
      direction="ltr"
      size="500px"
    >
      <add-review-batch-form @created="dialog = false"/>
    </el-drawer>
    <div class="flex items-ceter">
      <button
        type="button"
        class="inline-flex mr-3 items-center gap-x-1.5 rounded-md bg-green-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        @click="dialog = true"
      >
        Thêm đánh giá
      </button>
      <el-radio-group v-model="filters.year">
        <el-radio-button label="2022"></el-radio-button>
        <el-radio-button label="2023"></el-radio-button>
      </el-radio-group>
    </div>
    <ul>

        <li
          v-for="review in reviewList"
          :key="`review-link-${review.id}`"
          class="p-5 mt-2 shadow hover:shadow-lg cursor-pointer flex justify-between items-center"
          @click="$router.push({ name: 'review', params: { id: review.id } })"
        >
          <span>{{ review.title }}</span>
          <el-popconfirm
            title="Xác nhận xoá bản đánh giá này?"
            @confirm="deleteReview(review.id)"
          >
            <el-button
              size="mini"
              type="danger"
              slot="reference"
              @click.stop="clickOnBtn"
            >Xoá</el-button>
          </el-popconfirm>
        </li>
    </ul>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import AddReviewBatchForm from '../components/AddReviewBatchForm';

export default {
  components: {AddReviewBatchForm},
  data() {
    return {
      dialog: false,
      filters: {
        year: '2023'
      }
    }
  },
  computed: {
    ...mapGetters('review', ['reviews']),
    reviewList() {
      return this.reviews.filter(o => o.from.substring(0,4) === this.filters.year)
    },
  },
  mounted() {
    this.setPage('Đánh giá giáo viên');
    this.getList();
  },
  methods: {
    ...mapActions('ui', ['setPage']),
    ...mapActions('review', ['getList', 'deleteReview']),
    clickOnBtn() {
      console.log('click')
    }
  }
}
</script>
