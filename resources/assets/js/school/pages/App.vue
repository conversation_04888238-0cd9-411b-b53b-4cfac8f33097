<template>
  <el-container style="width: 100%; height: 100%; border: 1px solid #eee">
    <el-aside
      width="200px"
      style="
        background-color: #fff;
        position: fixed;
        height: 100vh;
        overflow-y: scroll;
      "
    >
      <el-menu :default-openeds="['1', '3']">
        <el-submenu index="1">
          <template slot="title"
            ><i class="el-icon-message"></i>Lớp zoom vip</template
          >
          <router-link :to="{ name: 'dashboard' }">
            <el-menu-item index="1-1">Chấm công</el-menu-item>
          </router-link>
          <router-link :to="{ name: 'payoff' }">
            <el-menu-item index="1-2">Thưởng/Phạt</el-menu-item>
          </router-link>
          <router-link :to="{ name: 'level' }">
            <el-menu-item index="1-3">Quản lý lớp</el-menu-item>
          </router-link>
          <router-link :to="{ name: 'reviews' }">
            <el-menu-item index="1-4">Đánh giá GV</el-menu-item>
          </router-link>
          <router-link :to="{ name: 'students' }">
            <el-menu-item index="1-5">Học viên</el-menu-item>
          </router-link>
          <router-link :to="{ name: 'teachers' }">
            <el-menu-item index="1-6">Giáo viên</el-menu-item>
          </router-link>
          <router-link :to="{ name: 'stats' }">
            <el-menu-item index="1-7">Thống kê</el-menu-item>
          </router-link>
        </el-submenu>
      </el-menu>
    </el-aside>
    <el-container style="width: calc(100% - 200px); margin-left: 200px">
      <el-header class="app-header">
        {{ title }}
      </el-header>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
export default {
  name: "App",
  data() {
    const item = {
      date: "2016-05-02",
      name: "Tom",
      address: "No. 189, Grove St, Los Angeles",
    };
    return {
      tableData: Array(20).fill(item),
    };
  },
  computed: {
    ...mapGetters("ui", ["title"]),
  },
  methods: {
    // ...mapActions('ui', ['getProducts'])
  },
  mounted() {
    // this.getProducts(1);
  },
};
</script>
<style>
.el-header {
  background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.el-aside {
  color: #333;
}
.app-header {
  font-size: 20px;
  font-weight: bold;
  text-align: left;
  height: 40px !important;
  line-height: 40px;
  background: #96d962;
}
</style>
