<template>
  <div class="pb-5">
    <div class="infomation-bar flex justify-between mb-5">
      <div class="group-info">
        <h1 v-cloak class="font-bold">
          <a
              :href="`/backend/community/users?group_id=${classroom.id}`"
              target="_blank"
          >{{ classroom.name || "--" }}</a
          >
          |
          {{
            `${classroom.group_teacher?.teacher?.last_name || "--"}
        ${classroom.group_teacher?.teacher?.first_name || "--"}`
          }}
        </h1>
        <p v-cloak class="font-bold">
          Số buổi còn lại:
          {{
            classroom.vip_session - course_time.length >= 0
                ? classroom.vip_session - course_time.length
                : "--"
          }}
          / {{ classroom.vip_session || "--" }}
        </p>
        <el-input style="width: 80%" placeholder="Nhập email hoặc id hoặc tên để tìm kiếm"
                  v-model="form_request.key_search"></el-input>
      </div>
      <div class="flex justify-between">
        <el-button @click="importAttendances">Nhập file điểm danh</el-button>
        <el-button @click="importOfflineTest">Nhập bài test offline</el-button>
        <el-button @click="dialogFormSendMessageVisible = true">Nhắn tin hàng loạt</el-button>
      </div>
      <div
          class="flex justify-end w-1/2 max-h-9 gap-3 items-center"
          v-if="classroom.links"
      >
        <div
            v-if="classroom.links.groupLink"
            class="cursor-pointer p-0 rounded bg-emerald-600 px-2 py-1 text-xs font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          <a :href="classroom.links.groupLink.url" target="_blank"
          >Link Group</a
          >
        </div>
        <div
            v-if="classroom.links.doc"
            class="cursor-pointer p-0 rounded bg-emerald-600 px-2 py-1 text-xs font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          <a :href="classroom.links.doc.url" target="_blank">Link Doc</a>
        </div>
        <div
            v-if="classroom.links.messenger"
            class="cursor-pointer p-0 rounded bg-emerald-600 px-2 py-1 text-xs font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          <a :href="classroom.links.messenger.url" target="_blank">Messenger</a>
        </div>
        <div
            v-if="classroom.links.linkZoom"
            class="cursor-pointer p-0 rounded bg-emerald-600 px-2 py-1 text-xs font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          <a :href="classroom.links.linkZoom.url" target="_blank">Link zoom</a>
          <span
              class="inline-block bg-teal-500 text-white text-xs px-2 rounded-full ml-2 cursor-pointer"
              v-on:click="showPass(classroom.links.linkZoom.note)"
          >P</span
          >
        </div>
      </div>
    </div>
    <el-dialog title="Soạn tin nhắn" :visible.sync="dialogFormSendMessageVisible">
      <el-form :model="form_send_massage">
        <el-form-item label="Gửi riêng bằng admin">
          <el-select v-model="form_send_massage.selectedAdmin" filterable>
            <el-option value=""></el-option>
            <el-option
                v-for="admin in admins"
                :key="admin.user?.id"
                :value="admin.user?.id"
                :label="`${admin.user?.name} ${admin.user?.email}`"
            ><b>{{ admin.user?.name }}</b> {{ admin.user?.email }}
            </el-option
            >
          </el-select>
        </el-form-item>
        <el-form-item label="Nội dung">
          <el-input
              type="textarea"
              :rows="4"
              placeholder=""
              v-model="form_send_massage.content"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogFormSendMessageVisible = false">Huỷ</el-button>
        <el-button type="primary" @click="sendBulkChat">{{
            sendLoading ? "Đang gửi" : "Gửi"
          }}</el-button>
      </span>
    </el-dialog>

    <el-dialog title="Điểm danh theo ngày" :visible.sync="importFormVisible">
      <div class="flex flex-col gap-3">
        <label for="">Ngày điểm danh</label>
        <el-date-picker
            v-model="importForm.date"
            type="date"
            value-format="yyyy-MM-dd"
            format="dd-MM-yyyy"
            placeholder="Chọn ngày điểm danh"
        >
        </el-date-picker>
        <label for="">Buổi số</label>
        <el-input-number v-model="importForm.index"></el-input-number>
        <label for="">File điểm danh</label>
        <input type="file" @change="changeImportFile($event)"/>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="importFormVisible = false">Huỷ</el-button>
        <el-button type="primary" @click="handleImportAttendance"
        >Gửi</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
        title="Điểm danh theo ngày"
        :visible.sync="importOfflineTestVisible"
    >
      <div class="flex flex-col gap-3">
        <label for="">Chọn bài test</label>
        <el-select
            v-model="importOfflineTestForm.community_exam_id"
            filterable
            clearable
            placeholder="Chọn bài test muốn điểm danh lại hoặc điểm danh thêm học viên"
        >
          <el-option
              v-for="item in offlineExams"
              :key="item.id"
              :label="`${item.title || 'Bài cũ không có tên'} - ${
              item.time_start
            }`"
              :value="item.id"
          >
          </el-option>
        </el-select>
        <a
            v-if="selectedExam && selectedExam.file"
            :href="`/upload/excel/${selectedExam.file}`"
            target="_blank"
            class="text-blue-700"
        >File đã nhập {{ selectedExam.file }}</a
        >
        <label for="">Hoặc tạo bài mới</label>
        <el-input
            v-model="importOfflineTestForm.newExamName"
            filterable
            placeholder="Nhập tên bài test mới"
        >
        </el-input>
        <label for="">Ngày điểm danh (chỉ chọn nếu nhập bài test mới)</label>
        <el-date-picker
            v-model="importOfflineTestForm.date"
            type="date"
            value-format="yyyy-MM-dd"
            format="dd-MM-yyyy"
            placeholder="Chọn ngày điểm danh"
        ></el-date-picker>
        <label for="">File điểm danh</label>
        <input type="file" @change="changeImportOfflineFile($event)"/>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="importOfflineTestVisible = false">Huỷ</el-button>
        <el-button type="primary" @click="handleImportOfflineResult"
        >Gửi</el-button
        >
      </span>
    </el-dialog>
    <div v-if="classroom.vip_session - course_time.length === 0" class="mb-4">
      <div class="el-button el-button--primary" @click="tab = 1">
        Xem điểm danh
      </div>
      <div
          v-if="this.classroom.report"
          class="el-button el-button--primary"
          @click="tab = 2"
      >
        Xem báo cáo
      </div>
    </div>
    <div>
      Sĩ số: {{ classroom.students?.total }}
    </div>
    <div class="flex mb-2">
      <el-button :loading="loadingMakeMultiTakeCare" v-for="(item, key) in phaseTakeCareMessage" :key="item.id" size="mini" @click="makeMultiTakeCare(item)">{{ `Chăm sóc tin nhắn đợt ${item.number_phase}` }}</el-button>
      <el-button :loading="loadingMakeMultiTakeCare" v-for="(item, key) in phaseTakeCareSurvey" :key="item.id" size="mini" @click="makeMultiTakeCare(item)">{{ `Chăm sóc gửi khảo sát đợt ${item.number_phase}` }}</el-button>
    </div>
    <el-table
        ref="elTable"
        v-if="tab === 1 || classroom.vip_session - course_time.length !== 0"
        :data="tableData"
        style="width: 100%"
        max-height="700"
        border
        lazy
        @selection-change="handleSelectionChange"
    >
      <el-table-column
          type="selection"
          :selectable="canSelectRow"
          width="55">
      </el-table-column>
      <el-table-column label="TT Gửi khảo sát" fixed>
        <template slot-scope="{row}">
          <div>
            <svg v-for="(item, key_row) in row.phaseTakeCareSurvey"
                 @click="makeTakeCare(row, item, key_row, item.type_phase)"
                 :fill="item.take_care_user ? `#FD6A7E` : 'gray'"
                 v-if="row.id > 0" width="15px" height="15px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" cursor="pointer">
              <path
                  d="M19 21H5V5h2v2h10V5h2v2.172l1-1V4h-3V3h-3a2 2 0 0 0-4 0H7v1H4v18h16V11.828l-1 1zM8 4h3V2.615A.615.615 0 0 1 11.614 2h.771a.615.615 0 0 1 .615.615V4h3v2H8zm14.646 1.646l.707.707-8.853 8.854-3.854-3.854.707-.707 3.147 3.147z"/>
              <path fill="none" d="M0 0h24v24H0z"/>
            </svg>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="TT Chăm sóc" fixed>
        <template slot-scope="{row}">
          <div>
            <svg v-for="(item, key_row) in row.phaseTakeCareMessage"
                 @click="makeTakeCare(row, item, key_row, item.type_phase)"
                 v-if="row.id > 0"
                 opacity="1"
                 :fill="item.take_care_user ? `#FD6A7E` : 'gray'"
                 width="15px"
                 height="15px"
                 viewBox="0 0 30 30"
                 cursor="pointer"
                 xmlns="http://www.w3.org/2000/svg">
              <path class="st0"
                    d="M21.5,27.6c-1.9-1.2-3.5-2.7-5-4.4c-1.1-1.3-1-3.3,0.2-4.5c1.3-1.3,3.4-1.3,4.8,0l0,0l0,0  c1.3-1.3,3.4-1.3,4.8,0l0,0c1.2,1.2,1.3,3.2,0.2,4.5C25.1,25,23.4,26.4,21.5,27.6z M24,7H6C4.9,7,4,7.9,4,9v15c0,1.1,0.9,2,2,2h9v-2  H6V13h18v3h2V9C26,7.9,25.1,7,24,7z M23,4c0-0.8-0.7-1.5-1.5-1.5h0C20.7,2.5,20,3.2,20,4v1h3V4z M10,4c0-0.8-0.7-1.5-1.5-1.5h0  C7.7,2.5,7,3.2,7,4v1h3V4z"/>
            </svg>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed prop="id" label="ID">
        <template slot-scope="scope">
          <div>
            {{ scope.row.id < 0 ? "" : scope.row.id }}
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed label="Họ tên" width="200">
        <template slot-scope="{row}">
          <div>
            {{ row.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed prop="note" label="Ghi chú SV">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content" style="white-space: pre">
              {{ scope.row.note }}
            </div>
            <div
                class="truncate ... text-center"
                @click="editStudentNote(scope.row)"
            >
              {{ scope.row.note || "&nbsp;&nbsp;&nbsp;&nbsp;" }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
          v-for="day in classroom.vip_session"
          :key="`attendance-${day}`"
          :label="`${day}`"
          width="70"
          class="flex justify-center align-center"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.id === -1">
            <el-tooltip
                v-if="scope.row[`day_${day}`]?.date_attendance"
                class="item"
                effect="dark"
                :content="scope.row[`day_${day}`]?.date_attendance"
                placement="top-start"
            >
              <div
                  class="truncate ... text-center"
                  @click="editDate(scope.row[`day_${day}`], day)"
              >
                {{ getMonthDate(scope.row[`day_${day}`]?.date_attendance) }}
              </div>
            </el-tooltip>
            <div
                v-else
                class="truncate ... text-center"
                @click="editDate(scope.row[`day_${day}`], day)"
            >
              &nbsp;&nbsp;&nbsp;&nbsp;
            </div>
          </div>
          <div
              v-else-if="scope.row.id === -2"
              class="truncate ... cursor-pointer text-center flex items-center justify-center"
          >
            <a
                :href="scope.row[`day_${day}`]?.link"
                class="truncate ... block"
                target="_blank"
                @click="copy(scope.row[`day_${day}`]?.link)"
            >
              {{ scope.row[`day_${day}`]?.link }}
            </a>
            <i
                class="el-icon-edit"
                @click="editLink(scope.row[`day_${day}`])"
            ></i>
          </div>
          <div v-else-if="scope.row.id === -3">
            <el-tooltip
                v-if="scope.row[`day_${day}`]?.password"
                class="item"
                effect="dark"
                :content="scope.row[`day_${day}`]?.password"
                placement="top-start"
            >
              <div
                  class="truncate ... cursor-pointer text-center"
                  @click="editPassword(scope.row[`day_${day}`])"
              >
                {{ scope.row[`day_${day}`]?.password }}
              </div>
            </el-tooltip>
            <div
                v-else
                class="truncate ... cursor-pointer text-center"
                @click="editPassword(scope.row[`day_${day}`])"
            >
              &nbsp;&nbsp;&nbsp;&nbsp;
            </div>
          </div>

          <div v-else-if="scope.row.id === -4">
            <el-tooltip
                v-if="scope.row[`day_${day}`]?.note"
                class="item"
                effect="dark"
                :content="scope.row[`day_${day}`]?.note"
                placement="top-start"
            >
              <div
                  class="truncate ... text-center"
                  @click="editNote(scope.row[`day_${day}`])"
              >
                {{ scope.row[`day_${day}`]?.note }}
              </div>
            </el-tooltip>
            <div
                v-else
                class="truncate ... text-center"
                @click="editNote(scope.row[`day_${day}`])"
            >
              &nbsp;&nbsp;&nbsp;&nbsp;
            </div>
          </div>
          <div v-else-if="scope.row.id === -5">
            <el-tooltip
                v-if="scope.row[`day_${day}`]?.note_cancel"
                class="item"
                effect="dark"
                :content="scope.row[`day_${day}`]?.note_cancel"
                placement="top-start"
            >
              <div
                  class="truncate ... text-center cursor-pointer"
                  @click="editQtvNote(scope.row[`day_${day}`])"
              >
                {{ scope.row[`day_${day}`]?.note_cancel || "--" }}
              </div>
            </el-tooltip>
            <div
                v-else
                class="truncate ... text-center cursor-pointer"
                @click="editQtvNote(scope.row[`day_${day}`])"
            >
              {{ scope.row[`day_${day}`]?.note_cancel || "--" }}
            </div>
          </div>
          <div
              v-else
              style="width: 100%"
              @click="editCourseTime(scope.row, day)"
          >
            <div v-if="!scope.row[`day_${day}`]">
              {{
                course_time[day - 1]?.date_attendance
                    ? "&nbsp;&nbsp;&nbsp;&nbsp;"
                    : ""
              }}
            </div>
            <div
                v-else-if="scope.row[`day_${day}`]?.status"
                class="flex justify-center align-center"
            >
              <div v-if="scope.row[`day_${day}`]?.status === 2"></div>
              <div
                  v-else
                  style="
                  border: 2px solid #96d962;
                  border-radius: 50%;
                  width: 15px;
                  height: 15px;
                "
              ></div>
            </div>
            <div
                v-else-if="scope.row[`day_${day}`]?.note"
                class="flex justify-center align-center"
            >
              <div
                  class="truncate ..."
                  style="
                  color: black;
                  background-color: #96d962;
                  width: 40px;
                  height: 40px;
                  text-align: center;
                  line-height: 40px;
                "
              >
                <el-tooltip
                    class="item"
                    effect="dark"
                    :content="scope.row[`day_${day}`]?.note"
                    placement="top-start"
                >
                  <div class="truncate ... cursor-pointer text-center">
                    {{ scope.row[`day_${day}`]?.note }}
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div v-else class="flex justify-center align-center">
              <div
                  style="background-color: red; width: 40px; height: 40px"
              ></div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          prop="presents"
          label="Đi học"
      ></el-table-column>
      <el-table-column
          fixed="right"
          prop="absences"
          label="Nghỉ học"
      ></el-table-column>
      <el-table-column
          fixed="right"
          prop="ratio"
          label="Tỷ lệ (%)"
      ></el-table-column>
    </el-table>
    <div class="p-3 shadow mt-5 bg-white w-full">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size.sync="perPage"
          layout="total, sizes, prev, pager, next, jumper"
          :total="classroom.students?.total"
      >
      </el-pagination>
    </div>
    <div v-if="tab === 2">
      <div>
        <div>
          Họ tên giáo viên:
          {{
            `${classroom.group_teacher.teacher.last_name} ${classroom.group_teacher.teacher.first_name}`
          }}
        </div>
        <div>Lớp đảm nhận: {{ classroom.name }}</div>
        <div>Ngày khai giảng: {{ classroom.start_date }}</div>
        <div>Ngày kết thúc: {{ classroom.expired_at }}</div>
      </div>
      <div class="mt-6">
        <div class="mb-1"><b>Báo cáo chung về lớp</b></div>
        <el-table
            :data="reportTableGeneral"
            style="width: 100%"
            max-height="700"
            border
        >
          <el-table-column type="index" label="STT" width="50" fixed/>
          <el-table-column fixed prop="content" label="Nội dung"/>
          <el-table-column fixed prop="value" label="Số liệu"/>
          <el-table-column fixed prop="note" label="Ghi chú"/>
        </el-table>
      </div>
      <div class="mt-6">
        <div class="mb-1">
          <b>Cảm nhận của giáo viên về khóa học, học viên</b>
        </div>
        <el-table
            :data="reportTableFeeling"
            style="width: 100%"
            max-height="700"
            border
        >
          <el-table-column type="index" label="STT" width="50" fixed/>
          <el-table-column fixed prop="title" label="Tiêu đề"/>
          <el-table-column fixed prop="content" label="Nội dung"/>
        </el-table>
      </div>
      <div class="mt-6">
        <div class="mb-1">
          <b>Nhận xét, góp ý và những đề xuất cải thiện khóa học</b>
        </div>
        <div>
          <b>Ưu điểm:</b>
          <span class="underline decoration-gray-400">{{
              reportData.advantage
            }}</span>
        </div>
        <div>
          <b>Nhược điểm:</b>
          <span class="underline decoration-gray-400">{{
              reportData.disadvantage
            }}</span>
        </div>
        <div>
          <b>Đề xuất thay đổi:</b>
          <span class="underline decoration-gray-400">{{
              reportData.suggest
            }}</span>
        </div>
      </div>
    </div>

    <el-drawer
        :title="`Học sinh: ${rowEditing.name}`"
        :visible.sync="drawer"
        direction="ttb"
    >
      <div class="px-10">
        <div>
          <input id="attendant" type="radio" v-model="attendant" value="1"/>
          <label for="attendant"> Đi học</label>
        </div>
        <div class="mt-1">
          <input id="absence" type="radio" v-model="attendant" value="0"/>
          <label for="absence"> Nghỉ học</label>
        </div>
        <div v-if="attendant === '0'" class="mt-1">
          <span>Lý do nghỉ (không nhập sẽ tính là nghỉ không phép)</span><br/>
          <input v-model="reason" class="border w-80 p-1"/>
        </div>
        <div class="mt-1.5">
          <button class="el-button el-button--success" @click="save">
            Lưu
          </button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import {mapActions, mapGetters} from "vuex";
import {range, find, orderBy, uniq, cloneDeep} from "lodash";
import moment from "moment/moment";
import api from "./../config/api";

export default {
  data() {
    return {
      page: 1,
      perPage: 100,
      filters: {
        vip_level: undefined,
        name: undefined,
        type: undefined,
      },
      importFormVisible: false,
      importOfflineTestVisible: false,
      tab: null,
      drawer: false,
      attendant: "attendant",
      rowEditing: {},
      reason: "",
      importForm: {
        date: "",
        index: 1,
        file: null,
      },
      importOfflineTestForm: {
        newExamName: "",
        community_exam_id: "",
        file: null,
        date: "",
      },
      form_request: {
        classId: this.$route.params?.id,
        page: 1,
        perPage: 100,
        key_search: ''
      },
      timer: null,
      dialogFormSendMessageVisible: false,
      form_send_massage: {
        content: "",
        selectedAdmin: "",
      },
      sendLoading: false,
      selectedStudents: [],
      loadingMakeMultiTakeCare: false,
    };
  },
  watch: {
    form_request: {
      handler(newData) {
        if (newData.key_search === undefined) this.form_request.key_search = null
        this.searchTimeOut()
      },
      deep: true,
    }
  },
  computed: {
    ...mapGetters("classroom", ["classroom", "offlineExams", "admins", "phaseTakeCare", "phaseTakeCareSurvey", "phaseTakeCareMessage"]),
    selectedExam() {
      return this.offlineExams.find(
          (o) => o.id === this.importOfflineTestForm.community_exam_id
      );
    },
    students() {
      if (this.classroom?.students?.data) {
        let data = cloneDeep(this.classroom?.students?.data);
        data.sort(function (a, b) {
          return a.group_user[0].created_at > b.group_user[0].created_at ? 1 : -1
        });
        return data;
      }
      return [];
    },
    course_time() {
      return orderBy(this.classroom.course_time, ['index'], ['asc']) || [];
    },
    tableData() {
      const students = this.students.map((s) => {
        const presents = s.course_time.filter((c) => c.status);
        const tmp = {
          id: s.id,
          name: s.name,
          absences: this.classroom.course_time.length - presents.length,
          presents: presents.length,
          note: s.group_user[0]?.note,
          phaseTakeCareSurvey: [],
          phaseTakeCareMessage: [],
          ratio: (
              (presents.length * 100) /
              this.classroom.course_time.length
          ).toFixed(0),
        };
        this.phaseTakeCareSurvey.forEach(item => {
          let t = JSON.parse(JSON.stringify(item));
          t.take_care_user = item.take_care_user.find((i) => i.user_id === s.id);
          tmp.phaseTakeCareSurvey.push(t)
        })
        this.phaseTakeCareMessage.forEach(item => {
          let t = JSON.parse(JSON.stringify(item));
          t.take_care_user = item.take_care_user.find((i) => i.user_id === s.id);
          tmp.phaseTakeCareMessage.push(t)
        })
        for (const day in range(1, this.classroom.vip_session + 2, 1)) {
          const courseTime = this.course_time[day - 1];
          if (!courseTime) {
            tmp[`day_${day}`] = null;
          } else {
            const studentTime = find(s.course_time, [
              "course_time_id",
              courseTime.id,
            ]);
            tmp[`day_${day}`] = studentTime || null;
          }
        }
        return {
          ...tmp,
        };
      });
      const attendance = {
        id: -1,
        name: "Ngày học",
        absences: "",
        presents: "",
        note: "",
        ratio: "",
        disableCheckbox: true
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        attendance[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(attendance);
      const zoom = {
        id: -2,
        name: "Link zoom",
        absences: "",
        presents: "",
        note: "",
        ratio: "",
        disableCheckbox: true
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        zoom[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(zoom);
      const password = {
        id: -3,
        name: "Mật khẩu",
        absences: "",
        presents: "",
        note: "",
        ratio: "",
        disableCheckbox: true
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        password[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(password);
      const note = {
        disableCheckbox: true,
        id: -4,
        name: "Ghi chú",
        absences: "",
        presents: "",
        note: "",
        ratio: "",
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        note[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(note);
      const note_cancel = {
        disableCheckbox: true,
        id: -5,
        name: "Ghi chú QTV",
        absences: "",
        presents: "",
        note: "",
        ratio: "",
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        note_cancel[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(note_cancel);
      return students;
    },
    reportData() {
      if (!this.classroom.report) return {};
      return JSON.parse(this.classroom.report.data);
    },
    reportTableGeneral() {
      if (!this.classroom.report) return [];
      const data = JSON.parse(this.classroom.report.data);
      return [
        {
          content: "Số buổi giáo viên xin nghỉ trong cả khóa",
          value: data.countDayOff,
          note: data.note1,
        },
        {
          content: "Tổng sĩ số của lớp",
          value: data.totalStudent,
          note: data.note2,
        },
        {
          content: "Sĩ số học sinh tham gia ở những buổi cuối",
          value: data.avgOnline,
          note: data.note3,
        },
        {
          content: "Tỷ lệ học sinh thi thử đỗ",
          value: data.totalPass,
          note: data.note4,
        },
        {
          content: "Số học sinh lớp đảm nhận học lên cấp độ cao hơn",
          value: data.totalUpgrade,
          note: data.note5,
        },
        {
          content: "Khác",
          value: data.other,
          note: data.note6,
        },
      ];
    },
    reportTableFeeling() {
      if (!this.classroom.report) return [];
      const data = JSON.parse(this.classroom.report.data);
      return [
        {
          title: "Cảm nhận về khóa học",
          content: data.feedback1,
        },
        {
          title: "Cảm nhận về học viên",
          content: data.feedback2,
        },
        {
          title: "Ưu và khuyết điểm của bản thân trong khóa học",
          content: data.feedback3,
        },
        {
          title: "Khác",
          content: data.feedback4,
        },
      ];
    },
  },
  async mounted() {
    if (this.$route.query?.focus === "report") {
      this.tab = 2;
    }
    this.setPage("Chi tiết lớp học");
    const data = {
      classId: this.$route.params?.id,
      page: this.page,
      perPage: this.perPage,
      key_search: this.form_request.key_search
    };
    await this.getStudentByClass(data);
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    ...mapActions("classroom", [
      "getStudentByClass",
      "saveCourseTime",
      "updateAttendance",
      "updateStudent",
      "importAttendance",
      "importOfflineTestResult",
      "sendBulkMessageClassroom"
    ]),
    async handleSizeChange(perPage) {
      const data = {
        classId: this.$route.params?.id,
        page: this.page,
        perPage: this.perPage,
        key_search: this.form_request.key_search
      };
      await this.getStudentByClass(data);
    },
    async handleCurrentChange(perPage) {
      const data = {
        classId: this.$route.params?.id,
        page: this.page,
        perPage: this.perPage,
        key_search: this.form_request.key_search
      };
      await this.getStudentByClass(data);
    },
    getScope(scope) {
      return "";
    },
    getMonthDate(date) {
      const result = date ? moment(date).format("DD-MM-YYYY") : "";
      return result ? result.slice(0, 5) : "";
    },
    copy(text) {
      navigator.clipboard.writeText(text);
    },
    async editDate(attendance, day) {
      return;
      const value = window.prompt(
          "Nhập ngày",
          attendance && attendance.date_attendance
              ? moment(attendance.date_attendance).format("DD-MM-YYYY")
              : moment().format("DD-MM-YYYY")
      );
      if (value === null) {
        return;
      }
      const data = {
        id: attendance ? attendance.id : null,
        day,
        date_attendance: moment(value, "DD-MM-YYYY").format("YYYY-MM-DD"),
        group_id: this.classroom.id,
        teacher_id: this.classroom.group_teacher.teacher_id,
      };
      await this.updateAttendance(data);
    },
    async editQtvNote(attendance) {
      const value = window.prompt("Nhập ghi chú", attendance.note_cancel || "");
      if (value === null) {
        return;
      }
      const data = {
        id: attendance.id,
        note_cancel: value,
      };
      await this.updateAttendance(data);
    },
    async editNote(attendance) {
      const value = window.prompt("Nhập ghi chú", attendance.note || "");
      if (value === null) {
        return;
      }
      const data = {
        id: attendance.id,
        note: value,
      };
      await this.updateAttendance(data);
    },
    async editPassword(attendance) {
      const value = window.prompt("Nhập password", attendance.password || "");
      if (value === null) {
        return;
      }
      const data = {
        id: attendance.id,
        password: value,
      };
      await this.updateAttendance(data);
    },
    async editLink(attendance) {
      const value = window.prompt("Nhập link", attendance.link || "");
      if (value === null) {
        return;
      }
      const data = {
        id: attendance.id,
        link: value,
      };
      await this.updateAttendance(data);
    },
    showPass(pass) {
      alert("Mật khẩu:" + pass);
    },
    editCourseTime(row, day) {
      return;
      if (row.id < 0) {
        return;
      }
      this.rowEditing = {...row, day};
      this.attendant = row[`day_${day}`]
          ? row[`day_${day}`].status.toString()
          : "1";
      this.reason = row[`day_${day}`] ? row[`day_${day}`].note : "";
      this.drawer = true;
    },
    save() {
      const courseTimeElelemt = this.course_time[this.rowEditing.day - 1];
      if (!courseTimeElelemt || !courseTimeElelemt.id) {
        return;
      }
      this.saveCourseTime({
        courseTimeId: courseTimeElelemt.id,
        studentId: this.rowEditing.id,
        status: parseInt(this.attendant),
        reason: this.reason,
      });
      this.drawer = false;
      this.attendant = "attendant";
      this.reason = "";
      this.rowEditing = {};
      this.reason = "";
    },
    editStudentNote(student) {
      this.$prompt("Nhập note", "Nhập note", {
        inputValue: student.note || "",
        confirmButtonText: "OK",
        cancelButtonText: "Cancel",
        inputType: "textarea",
      })
          .then(({value}) => {
            const data = {
              userId: student.id,
              groupId: this.classroom.id,
              note: value,
            };
            this.updateStudent(data);

            this.$message({
              type: "success",
              message: "Thành công",
            });
          })
          .catch(() => {
          });
    },
    importAttendances() {
      this.importFormVisible = true;
    },
    importOfflineTest() {
      this.importOfflineTestVisible = true;
    },
    async handleImportAttendance() {
      const data = new FormData();
      data.append("groupId", this.classroom.id);
      data.append("date", this.importForm.date);
      data.append("file", this.importForm.file);
      data.append("index", this.importForm.index);
      await this.importAttendance(data);
      this.$message({
        type: "success",
        message: "Thành công",
      });
      this.importFormVisible = false;
    },
    async handleImportOfflineResult() {
      const data = new FormData();
      data.append(
          "community_exam_id",
          this.importOfflineTestForm.community_exam_id
      );
      if (!this.importOfflineTestForm.community_exam_id) {
        data.append("groupId", this.classroom.id);
        data.append("newExamName", this.importOfflineTestForm.newExamName);
        data.append("date", this.importOfflineTestForm.date);
      }

      data.append("file", this.importOfflineTestForm.file);
      await this.importOfflineTestResult(data);
      this.$message({
        type: "success",
        message: "Thành công",
      });
      this.importOfflineTestVisible = false;
    },
    changeImportFile(event) {
      this.importForm.file = event.target.files[0];
    },
    changeImportOfflineFile(event) {
      this.importOfflineTestForm.file = event.target.files[0];
    },
    searchTimeOut() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(async () => {
        let data = await this.getStudentByClass(this.form_request)
      }, 800);
    },
    canSelectRow(row, index) {
      return !row.disableCheckbox;
    },
    async sendBulkChat() {
      if (this.sendLoading) return;
      this.sendLoading = true;
      const data = {
        message: this.form_send_massage.content,
        selectedAdmin: this.form_send_massage.selectedAdmin,
        listIds: uniq(this.selectedStudents.map((o) => o.id || null)).join(","),
        scheduleAt: "null",
      };

      await this.sendBulkMessageClassroom(data);
      this.form_send_massage = {
        content: "",
        selectedAdmin: "",
      };
      this.dialogFormSendMessageVisible = false;
      this.sendLoading = false;
    },
    handleSelectionChange(val) {
      this.selectedStudents = val;
    },
    async makeTakeCare(row, phaseTakeCare, key_row, type_phase) {
      let data = {
        user_id: row.id,
        community_group_id: this.classroom.id,
        phase_take_care_id: phaseTakeCare.id,
        type_phase: type_phase
      }
      await api.post('/classroom/confirm-take-care', data).then(res => {
        if (res.data.code === 200) {
          if (type_phase == 1) {
            this.tableData.find(t => t.id === row.id).phaseTakeCareMessage[key_row].take_care_user = res.data.data != null ? res.data.data : undefined
          } else {
            this.tableData.find(t => t.id === row.id).phaseTakeCareSurvey[key_row].take_care_user = res.data.data != null ? res.data.data : undefined
          }
          this.$message.success('Đã thay đổi trạng thái chăm sóc!')
        }
      }).catch(err => {
        this.$message.error(err)
      })
    },
    async makeMultiTakeCare(phase_care) {
      this.loadingMakeMultiTakeCare = true
      if (this.selectedStudents.length === 0) {
        this.$message.warning('Vui lòng chọn học viên!')
        this.loadingMakeMultiTakeCare = false
        return;
      }

      let data = {
        community_group_id: this.classroom.id,
        phase_take_care_id: phase_care.id,
        user_ids: uniq(this.selectedStudents.map((o) => o.id || null)),
        type_phase: phase_care.type_phase
      }

      await api.post('/classroom/confirm-take-care-multi', data).then(res => {
        if (res.data.code === 200) {
          this.$message.success('Đã thay đổi trạng thái chăm sóc!')

          if (res.data.data.length === 0) {
            return;
          }
          this.tableData.map(user => {
            let find = res.data.data.find(i => i.user_id === user.id);
            if (phase_care.type_phase === 1 && find !== undefined) {
              user.phaseTakeCareMessage.find(t => t.id === phase_care.id).take_care_user = find
            }

            if (phase_care.type_phase === 2 && find !== undefined) {
              user.phaseTakeCareSurvey.find(t => t.id === phase_care.id).take_care_user = find
            }
          });

        }
      }).catch(err => {
        this.$message.error(err)
      })
      this.loadingMakeMultiTakeCare = false
    }
  },
};
</script>
<style scoped>
/*.el-table__cell {*/
/*  display: flex;*/
/*  justify-content: center;*/
/*  align-items: center;*/
/*}*/
.cell {
  text-align: center;
}
</style>
