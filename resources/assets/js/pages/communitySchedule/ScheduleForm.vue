<template>
  <div class="flex flex-col px-5">
    <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm" :label-position="'top'">
      <el-form-item label="Nhóm" prop="group_id" required>
        <b v-if="formData.id">{{ formData.group?.name }}</b>
        <el-select
          v-else
          v-model="formData.group_id"
          filterable
          remote
          reserve-keyword
          placeholder="Tìm kiếm bằng từ khoá"
          :remote-method="remoteMethod"
          :loading="loading"
          class="w-full"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Thời gian học" required>
        <el-col :span="11">
          <el-form-item prop="from" required>
            <el-date-picker :value-format="'yyyy-MM-dd HH:mm:ss'" type="datetime" placeholder="Bắt đầu" v-model="formData.from" style="width: 100%;"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col class="line text-center" :span="2">-</el-col>
        <el-col :span="11">
          <el-form-item prop="to" required>
            <el-date-picker :value-format="'yyyy-MM-dd HH:mm:ss'" type="datetime" placeholder="Kết thúc" v-model="formData.to" style="width: 100%;"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item label="Tự động thông báo">
        <el-switch v-model="formData.is_notice" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="Ghi chú" prop="note">
        <el-input type="textarea" v-model="formData.note"></el-input>
      </el-form-item>
      <el-form-item>
        <div class="flex items-between w-full">
          <el-button class="w-full" type="primary" @click="save('formData')">Lưu</el-button>
          <el-button v-if="formData.id" class="w-full" type="danger" @click="deleteSchedule(formData.id)">Xoá</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import axios from 'axios'
export default {
  props: ['schedule', 'groups'],
  data() {
    return {
      formData: {},
      loading: false,
      options: [],
      rules: {
        group_id: [
          { required: true, message: 'Bắt buộc chọn nhóm', trigger: 'change' }
        ],
        from: [
          { required: true, message: 'Bắt buộc chọn thời gian bắt đầu', trigger: 'change' }
        ],
        to: [
          { required: true, message: 'Bắt buộc chọn thời gian kết thúc', trigger: 'change' }
        ],
      }
    }
  },
  mounted() {
    this.formData = {...this.schedule}
    this.options = this.groups.filter(item => {
      return item.id === this.formData.group_id;
    });
  },
  methods: {
    remoteMethod(query) {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
        this.options = this.groups.filter(item => {
          return item.name.toLowerCase()
            .indexOf(query.toLowerCase()) > -1;
        });
      }, 200);
    },
    async save() {
      if (!this.formData.group_id) return;
      const data = {
        id: this.formData.id,
        from: this.formData.from,
        group_id: this.formData.group_id,
        to: this.formData.to,
        note: this.formData.note,
        is_notice: this.formData.is_notice ? 1 : 0,
      }
      const url = this.formData.id ? `/backend/community/group/schedule/${this.formData.id}` : '/backend/community/group/schedule/';
      const res = await axios({
        method: this.formData.id ? 'put' : 'post',
        url: url,
        data
      });
      this.$emit(this.formData.id ? 'updated' : 'created', res.data.data);
    },
    async deleteSchedule(id) {
      await axios.delete(window.location.origin + '/backend/community/group/schedule/' + id);
      this.$emit('deleted', id);
    }
  }
}
</script>
