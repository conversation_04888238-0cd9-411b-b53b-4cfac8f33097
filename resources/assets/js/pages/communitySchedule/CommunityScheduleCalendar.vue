<template>
  <div class="schedule-wrapper">
    <el-select
      v-model="groupIds"
      multiple
      filterable
      remote
      reserve-keyword
      placeholder="Please enter a keyword"
      :remote-method="remoteMethod"
      :loading="loading"
      class="w-full"
      @change="fetchEvent"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.id">
      </el-option>
    </el-select>
    <FullCalendar :options="calendarOptions">
      <template #eventContent="arg">
        <div class="w-full h-full text-left flex items-center gap-1 p-1 text-black bg-yellow-100 rounded-md font-quicksand" @click="openEvent(arg.event)">
          <div class="flex justify-between gap-1">
            <b>{{ formatBookingTime(arg.event.start) }}</b>
            <b>-</b>
            <b>{{ formatBookingTime(arg.event.end) }}</b>
          </div>
          <div class="w-full truncate text-ellipsis">{{ arg.event.title }}</div>
        </div>
      </template>
    </FullCalendar>
    <el-drawer
      title="Sửa lịch học"
      :visible.sync="isOpenBookingModal"
    >
      <ScheduleForm v-if="isOpenBookingModal" :schedule="editingEvent.extendedProps" :groups="groups" @created="created" @updated="updated" @deleted="deleted"/>
    </el-drawer>
  </div>
</template>
<script>
import FullCalendar from '@fullcalendar/vue'
import moment from 'moment'
import axios from 'axios'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin from '@fullcalendar/interaction'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import viLocale from '@fullcalendar/core/locales/vi'
import ScheduleForm from './ScheduleForm'
import bootstrapPlugin from '@fullcalendar/bootstrap';

export default {
  components: {
    FullCalendar, // make the <FullCalendar> tag available
    ScheduleForm
  },
  data() {
    return {
      loading: false,
      selectSlot: null,
      isOpenBookingModal: false,
      isOpenMoveBookingModal: false,
      groupIds: [],
      options: [],
      groups: [],
      value: 'Option1',
      editingEvent: {},
      eventMovingData: {},
      calendarOptions: {
        locales: [viLocale],
        locale: 'vi',
        plugins: [ dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin, bootstrapPlugin ],
        themeSystem: 'bootstrap',
        initialView: 'dayGridMonth',
        headerToolbar: {
          start: 'title', // will normally be on the left. if RTL, will be on the right
          center: 'dayGridMonth,timeGridWeek,timeGridDay,list',
          end: 'today prev,next' // will normally be on the right. if RTL, will be on the left
        },
        eventColor: '#AFE1AF',
        height: 800,
        contentHeight: 780,
        aspectRatio: 3,
        nowIndicator: true,
        selectable: true,
        select: (info) => {
          if (info.jsEvent.detail === 2) {
            this.editingEvent.extendedProps = {
              group_id: this.groupIds.length ? this.groupIds[0] : '',
              from: moment(info.startStr).format('yyyy-MM-DD HH:mm:ss'),
              to: moment(info.startStr).add(2, 'hours').format('yyyy-MM-DD HH:mm:ss'),
              is_notice: true,
              note: ''
            }
            this.isOpenBookingModal = true
          }
        },
        eventResize: (info) => {
          this.eventMovingData = {
            info,
            id: info.event.extendedProps.id,
            start: info.event.start,
            end: info.event.end,
          }
          this.eventMoveConfirmation(this.eventMovingData, info)
        },
        eventDrop: (info) => {
          this.eventMovingData = {
            info,
            id: info.event.extendedProps.id,
            start: info.event.start,
            end: info.event.end,
          }
          this.eventMoveConfirmation(this.eventMovingData, info)
        },
        slotDuration: `00:30:00`,
        slotLabelInterval: '01:00',
        allDaySlot: false,
        initialDate: moment.now(),
        editable: true,
        dayMaxEvents: true, // allow "more" link when too many events
        navLinks: true,
        events: [],
        businessHours: [] || null,
      }
    }
  },
  watch: {
    isOpenBookingModal(val) {
      if (!val) {
        this.editingEvent = {}
      }
    }
  },
  mounted() {
    this.getGroups();
    let urlParams = new URLSearchParams(window.location.search);
    const groupId = urlParams.get('group_id')
    if (groupId) this.groupIds = [parseInt(groupId)];
    this.fetchEvent()
  },
  methods: {
    eventMoveConfirmation(movingData, event) {
      this.$confirm(`Xác nhận sửa lịch học sang từ ${moment(movingData.start).format('HH:mm:ss DD-MM-yyyy')} đến ${moment(movingData.end).format('HH:mm:ss DD-MM-yyyy')}`, 'Cảnh báo', {
        confirmButtonText: 'Xác nhận',
        cancelButtonText: 'Huỷ',
        type: 'warning'
      }).then(async () => {
        const data = {
          id: movingData.id,
          from: moment(movingData.start).format('yyyy-MM-DD HH:mm:ss'),
          to: moment(movingData.end).format('yyyy-MM-DD HH:mm:ss'),
        }
        const url = `/backend/community/group/schedule/${data.id}`;
        const res = await axios.put(url, data);
        this.updated(res.data.data)
        this.$message({
          type: 'success',
          message: 'Sửa thành công'
        });
      }).catch(() => {
        event.revert()
        this.$message({
          type: 'info',
          message: 'Đã huỷ'
        });
      });
    },
    formatBookingTime(time) {
      return moment(time).format('HH:mm')
    },
    async getGroups() {
      const res = await axios.get(window.location.origin + '/backend/community/group/list-all');
      this.groups = res.data.data.map(o => ({
        id: o.id,
        name: o.name
      }))
      this.remoteMethod('')
    },
    remoteMethod(query) {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
        this.options = this.groups.filter(item => {
          return item.name.toLowerCase()
            .indexOf(query.toLowerCase()) > -1;
        });
      }, 200);
    },
    async fetchEvent() {
      const res = await axios.post(window.location.origin + '/backend/community/group/schedule/list', { ids: this.groupIds });
      this.calendarOptions.events = res.data.data.map(o => ({
        id: o.id,
        start: o.from,
        end: o.to,
        title: o.group?.name || '--',
        backgroundColor: 'rgb(255,87,90)',
        extendedProps: {
          ...o
        }
      }))
    },
    openEvent(event) {
      this.editingEvent = event
      this.isOpenBookingModal = true
    },
    created(event) {
      this.calendarOptions.events.push({
        id: event.id,
        start: event.from,
        end: event.to,
        title: event.group?.name || '--',
        extendedProps: {
          ...event
        }
      });
      this.isOpenBookingModal = false
      this.$message({
        type: 'success',
        message: 'Thêm thành công'
      });
    },
    updated(event) {
      const idx = this.calendarOptions.events.findIndex(o => o.id === event.id)
      this.$set(this.calendarOptions.events, idx, {
        id: event.id,
        start: event.from,
        end: event.to,
        title: event.group?.name || '--',
        extendedProps: {
          ...event
        }
      });
      this.isOpenBookingModal = false
      this.$message({
        type: 'success',
        message: 'Sửa thành công'
      });
    },
    deleted(event) {
      this.editingEvent.remove()
      this.isOpenBookingModal = false
      this.$message({
        type: 'success',
        message: 'Xoá thành công'
      });
    }
  }
}
</script>
<style lang="scss">
.schedule-wrapper {
  width: 80vw;
  margin: auto;
}
.fc-daygrid-day-number {
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 1px solid #D9D9D9;
}
.fc-day-today {
  background: white !important;
  .fc-daygrid-day-number {
    background: limegreen;
    color: white !important;
    font-weight: 600;
  }
}

.fc-event {
  border-radius: 5px;
  background-color: #AFE1AF;
  padding: 1px 5px;
  &:hover {
    background-color: #90EE90;
  }
}
.fc-day {
  padding: 3px;
}
</style>
