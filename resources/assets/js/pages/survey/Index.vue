<template>
  <div>
    <div class="search-box mb-5">
      <el-button @click="getList">Refresh</el-button>
      <el-button @click="dialogFormVisible = true">Thêm mới</el-button>
    </div>
    <div>
      <div class="table-data mb-10">
        <el-table
            v-loading="loadingTable"
            element-loading-text="Loading..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :data="tableData"
            stripe
            border
            ref="multipleTable"
            @selection-change="handleSelectionChange"
            style="width: 100%">
          <el-table-column
              header-align="center"
              type="selection"
              align="center"
              width="55">
          </el-table-column>
          <el-table-column
              header-align="center"
              prop="title"
              label="Tên khảo sát">
          </el-table-column>
          <el-table-column
              header-align="center"
              align="center"
              prop="public"
              label="Trạng thái">
            <template slot-scope="{row}">
              <el-tag type="success" v-if="row.public">K<PERSON>ch hoat</el-tag>
              <el-tag type="danger" v-else>Ẩn</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              header-align="center"
              prop="address"
              label="Số người làm khảo sát">
            <template slot-scope="{row}">
              {{ row.result.length }}
            </template>
          </el-table-column>
          <el-table-column
              header-align="center"
              prop="action"
              label="Hành động"
              class="tesetste"
          >
            <template slot-scope="{row}" class="center" align="center"
                      style="display: grid; justify-content: center !important; align-items: center !important;">
              <el-button type="text" icon="el-icon-delete"></el-button>
              <el-button type="text" icon="el-icon-view" @click="detailSurvey(row)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog title="Shipping address" :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="Tên khảo sát" :label-width="formLabelWidth">
          <el-input v-model="form.title" autocomplete="off"></el-input>
        </el-form-item>
        <el-divider></el-divider>

        <el-form-item v-for="(question, key_question) in form.question" :label="question.title"
                      :label-width="formLabelWidth">
          <div>
            <label>{{ `Câu ${key_question + 1}` }}
              <el-button type="text" icon="el-icon-delete" @click="deleteQuestion(key_question)"></el-button>
            </label>
            <el-input v-model="question.content" placeholder="Nhập nội dung câu hỏi"></el-input>
          </div>

          <div v-for="(answer, key_answer) in question.answer">
            <label>Đáp án</label>
            <div class="flex">
              <el-input v-model="form.question[key_question].answer[key_answer]" placeholder="Đáp án"></el-input>
              <el-button type="text" icon="el-icon-delete" @click="deleteAnswer(key_answer, key_question)"></el-button>
            </div>
          </div>
          <div>
            <el-button plain size="mini" type="primary" @click="addAnswer(key_question, question)">Thêm đáp án
            </el-button>
          </div>
        </el-form-item>
        <el-divider></el-divider>

        <el-button type="primary" @click="addQuestion">Thêm câu hỏi</el-button>
      </el-form>
      <span slot="footer" class="dialog-footer">
    <el-button @click="dialogFormVisible = false">Cancel</el-button>
    <el-button type="primary" @click="addSurvey">Thêm khảo sát</el-button>
  </span>
    </el-dialog>
    <el-dialog title="Chi tiết khảo sát" :visible.sync="dialogDetailVisible">
      <el-table
          v-loading="loadingTableDetail"
          element-loading-text="Loading..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          :data="resultStatistics"
          :span-method="objectSpanMethod"
          stripe
          border
          ref="multipleTable"
          style="width: 100%"
      >
        <el-table-column
            header-align="center"
            label=""
            prop="question"
        >
        </el-table-column>
        <el-table-column
            header-align="center"
            label="Nội dung câu hỏi"
            prop="answer"
        >
        </el-table-column>
        <el-table-column
            header-align="center"
            label="Số lượt bình chọn"
            prop="count"

        >
        </el-table-column>
      </el-table>
      <el-button @click="dialogDetailVisible = false">Cancel</el-button>
    </el-dialog>
  </div>
</template>

<script>

import RepositoryFactory from '../../repository/repositoryFactory'

const surveyReportRepository = RepositoryFactory.get('survey')
export default {
  data() {
    return {
      loadingTable: true,
      tableData: [],
      dialogFormVisible: false,
      dialogDetailVisible: false,
      form: {
        question: [
          {
            answer: []
          }
        ]
      },
      formLabelWidth: '120px',
      resultStatistics: [],
      maxAnswer: 1,
      loadingTableDetail: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loadingTable = true;
      let data = await surveyReportRepository.getList()
      if (data.status === 200) {
        this.tableData = data.data.data
      }
      this.loadingTable = false;
      console.log(`data: `, data)
      console.log(`tableData: `, this.tableData)
    },
    async getResults() {
      let result = await surveyReportRepository.getResults()

      console.log(`data: `, result)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    addQuestion() {
      this.form.question.push({
        title: '',
        answer: []
      })
    },
    addAnswer(key_question, question) {
      console.log(`addAnswer: `, question)
      console.log(`key_question: `, key_question)
      this.form.question[key_question].answer.push("")
    },
    deleteAnswer(key_answer, key_question) {
      this.form.question[key_question].answer.splice(key_answer, 1)
    },
    deleteQuestion(key_question) {
      this.form.question.splice(key_question, 1)
    },
    async addSurvey() {
      console.log(this.form)
      let result = await surveyReportRepository.addSurvey(this.form)
      console.log(`result: `, result)
    },
    async detailSurvey(row) {
      this.loadingTableDetail = true
      this.dialogDetailVisible = true

      let result = await surveyReportRepository.getDetailSurvey({id: row.id, is_result: 1})
      result = result.data.data

      let statistics = {};
      result.result.forEach(result_item => {
        result_item.answers.forEach(answer => {
          const questionId = answer.id;
          const answerId = answer.answer;

          if (!statistics[questionId]) {
            statistics[questionId] = {};
          }

          if (!statistics[questionId][answerId]) {
            statistics[questionId][answerId] = 0;
          }

          statistics[questionId][answerId]++;
        });
      });

      this.resultStatistics = [];

      Object.keys(result.question).forEach(questionId => {
        let question = result.question[questionId];
        console.log(`question: `, question)
        question.answer.forEach(answer => {
          this.resultStatistics.push({
            question_id: question.id,
            question: question.question,
            answer_id: answer.id,
            answer: answer.answer,
            count: statistics[question.id] && statistics[question.id][answer.id] ? statistics[question.id][answer.id] : 0
          });
        });
      });

      this.loadingTableDetail = false
      console.log(`maxAnswer: `, this.maxAnswer, this.resultStatistics)
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0) {
        const question_id = row.question_id;
        const count = this.resultStatistics.filter(item => item.question_id === question_id).length;

        if (rowIndex === this.resultStatistics.findIndex(item => item.question_id === question_id)) {
          return [count, 1];
        } else {
          return [0, 0];
        }
      }
    },
  }
}
</script>