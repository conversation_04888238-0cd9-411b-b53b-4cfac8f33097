<template>
  <div>
    <div class="d-flex justify-between">
      <div class="search-box mb-5">
        <el-select
            v-model="dataQuery.teacher_id"
            filterable
            remote
            reserve-keyword
            placeholder="Chọn giáo viên test"
            @change="handleSelect"
            value-key="id"
            clearable
            size="small"
            class="mb-2"
        >
          <el-option
              v-for="item in list_teacher"
              :key="item.id"
              :label="item.name"
              :value="item.id">
          </el-option>
        </el-select>

        <el-date-picker
            v-model="dataQuery.time_range"
            class="mb-2"
            type="daterange"
            range-separator="To"
            start-placeholder="Start date"
            format="dd-MM-yyyy"
            value-format="dd-MM-yyyy"
            size="small"
            end-placeholder="End date">
        </el-date-picker>

        <el-select
            v-model="dataQuery.user_id"
            filterable
            remote
            reserve-keyword
            placeholder="<PERSON>ì<PERSON> kiếm User"
            :remote-method="querySearch"
            :loading="loadingSearchUser"
            value-key="id"
            clearable
            class="mb-2"
            size="small"
        >
          <el-option
              v-for="item in user_option_search_box"
              :key="item.id"
              :label="item.name"
              :value="item.id">
          </el-option>
        </el-select>

        <el-input
            v-model="dataQuery.invoice_id"
            class="mb-2"
            size="small"
            placeholder="Nhập ID hóa đơn"
            type="text"
            style="width: 220px"
            clearable
        ></el-input>

        <i style="cursor: pointer;" class="el-icon-refresh mr-2" @click="refresh"></i>
        <el-button size="small" v-show="activeName == 'schedule_test'" class="mb-2" type="primary" icon="el-icon-search"
                   @click="getListTest">Search
        </el-button>
        <el-button size="small" v-show="activeName != 'schedule_test'" class="mb-2" type="primary" icon="el-icon-search"
                   @click="getScheduleNotFinalized">Search
        </el-button>
      </div>
      <div class="button-box">
        <el-button size="small" class="mb-2" type="primary" icon="el-icon-plus"
                   @click="dialogFormTestScheduleVisible = true">
          Thêm lịch TEST
        </el-button>
        <el-button size="small" class="mb-2" type="primary" icon="el-icon-plus"
                   @click="dialogFormFreeTimeTestVisible = true">
          Thêm khung giờ test
        </el-button>
        <!-- <el-button size="small" class="mb-2" type="primary" icon="el-icon-plus"
                   @click="dialogFormScheduleNotFinalizedVisible = true">
          Thêm lịch chưa chốt
        </el-button> -->
      </div>
    </div>

    <div class="mb-10">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="Lịch trình Test" name="schedule_test">
          <el-table
              v-loading="loadingTableListTest"
              :data="tableDataListTest"
              :span-method="objectSpanMethod"
              element-loading-text="Loading..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)"
              border
              stripe
              style="width: 100%">
            <el-table-column
                prop="parent_id"
                align="center"
                label="ID"
                width="100">
            </el-table-column>
            <el-table-column
                align="center"
                label="Khung giờ test Kaiwa"
                width="180">
              <template slot-scope="{row}">
                <div>
                  {{ row.day_free }}
                </div>
                <div>
                  {{ row.time_start_free }} - {{ row.time_end_free }}
                </div>
                <div>
                  <el-button type="text" @click="updateFreeTime(row)"
                             :disabled="row.id != null || (admin.desc != 'admin' && admin.desc != 'Super Admin')"
                             icon="el-icon-edit-outline" class="mr-2"></el-button>
                  <el-button style="color: red" type="text" @click="deleteFreeTime(row)"
                             :disabled="admin.desc != 'admin' && admin.desc != 'Super Admin'"
                             icon="el-icon-delete-solid" class="ml-2"></el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="ID Đơn hàng"
                width="100">
              <template slot-scope="{row}">
                <el-link v-if="row.invoice_id !== undefined && row.invoice_id !== null" type="primary"
                         :href="baseUrl + '/backend/invoice?status=completed&id=' + row.invoice_id" target="_blank">
                  {{ row.invoice_id }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column
                prop="user_id"
                align="center"
                label="User ID"
                width="150">
              <template slot-scope="{row}">
                <div style="font-weight: bold;">{{ row.user_id }}</div>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="User NAME"
                width="100">
              <template slot-scope="{row}">
                <el-tag v-if="row.user != null && row.user.name != null">{{ row.user.name }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
                prop="start_time"
                label="Giờ TEST"
                align="center"
                width="180">
              <template slot-scope="{row}">
                <div>{{ row.start_time }}</div>
                <div v-if="row.id !== null" style="font-size: 12px;">
                  <i class="el-icon-success" style="cursor: pointer;" @click="update_status_take_care(row, 'after')"
                     :style="row.take_care_after_test !== null ? 'color: green' : 'opacity: 0.6'">
                    <span class="ml-1" style="color: black;">Chăm sóc trước test</span>
                  </i>
                  <i class="el-icon-success" style="cursor: pointer;" @click="update_status_take_care(row, 'before')"
                     :style="row.take_care_before_test !== null ? 'color: green' : 'opacity: 0.6'">
                    <span class="ml-1" style="color: black;">Chăm sóc sau test</span>
                  </i>
                  <div>
                    <i class="el-icon-success" style="cursor: pointer;" @click="update_status_receive_shift(row)"
                       :style="row.status_receive_shift === 1 ? 'color: green' : 'opacity: 0.6'">
                      <span class="ml-1" style="color: black;">Giáo viên đã nhận ca</span>
                    </i>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
                prop="level"
                label="Cấp độ">
            </el-table-column>
            <el-table-column
                label="Giáo viên">
              <template slot-scope="{row}">
                <el-tag>{{ row.teacher_name }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
                label="Trạng thái">
              <template slot-scope="{row}">
                <el-tag :type="row.classfield_status ? 'success' : 'danger'">
                  {{ row.classfield_status ? 'Đã xếp lớp' : 'Chưa xếp lớp' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
                label="Link" align="center">
              <template slot-scope="{row}">
                <el-link v-if="row.invoice !== undefined && row.invoice.info_contact !== undefined"
                         :href="row.invoice.info_contact.facebook" target="_blank" type="info">
                  <svg style="margin-bottom: 4px" title="Link FB" width="20px" height="20px" viewBox="0 0 32 32" fill="none"
                       xmlns="http://www.w3.org/2000/svg">
                    <circle cx="16" cy="16" r="14" fill="url(#paint0_linear_87_7208)"/>
                    <path
                        d="M21.2137 20.2816L21.8356 16.3301H17.9452V13.767C17.9452 12.6857 18.4877 11.6311 20.2302 11.6311H22V8.26699C22 8.26699 20.3945 8 18.8603 8C15.6548 8 13.5617 9.89294 13.5617 13.3184V16.3301H10V20.2816H13.5617V29.8345C14.2767 29.944 15.0082 30 15.7534 30C16.4986 30 17.2302 29.944 17.9452 29.8345V20.2816H21.2137Z"
                        fill="white"/>
                    <defs>
                      <linearGradient id="paint0_linear_87_7208" x1="16" y1="2" x2="16" y2="29.917"
                                      gradientUnits="userSpaceOnUse">
                        <stop stop-color="#18ACFE"/>
                        <stop offset="1" stop-color="#0163E0"/>
                      </linearGradient>
                    </defs>
                  </svg>
                </el-link>
                <el-link title="Link Chat" type="info" v-if="row.invoice !== undefined && row.invoice.info_contact !== undefined"
                         :href="row.invoice.info_contact.chat" target="_blank">
                  <i style="font-size: 20px" class="el-icon-chat-dot-round"></i>
                </el-link>
                <i v-if="row.zoom_info !== null && row.zoom_info !== undefined" @click="showInfoZoom(row)" style="font-size: 20px; cursor: pointer" class="el-icon-video-camera" title="Thông tin Zoom"></i>
              </template>
            </el-table-column>
            <el-table-column
                label="Sale TV">
              <template slot-scope="{row}">
                <el-tag v-if="row.sale_name != null">{{ row.sale_name }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="KQ Test">
              <template slot-scope="{row}">
                <div>
                  <el-tag v-if="row.result_status != null" :type="row.result_status ? 'success' : 'danger'">
                    {{ row.result_status ? 'Đỗ' : 'Trượt' }}
                  </el-tag>
                  <el-tag v-else type="warning">Chưa có kết quả</el-tag>
                </div>
                <div class="mt-2" v-if="row.link_result !== null">
                  <el-link type="info" :href="row.link_result" target="_blank">{{ row.link_result }}</el-link>
                </div>
              </template>
            </el-table-column>
            <el-table-column
                prop="note"
                label="Ghi chú">
            </el-table-column>
            <el-table-column
                label="Thời gian tạo">
              <template slot-scope="{row}">
                <template v-if="row.created_at !== null">
                  {{ moment(row.created_at).format('HH:mm DD-MM-YYYY') }}
                </template>
              </template>
            </el-table-column>
            <el-table-column
                prop="action"
                label="Hành động">
              <template slot-scope="{row}">
                <el-dropdown @command="(command) => handleCommand(command, row)">
                  <el-button>
                    <i class="el-icon-more"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item icon="el-icon-finished" command="test_result" divided :disabled="row.id == null">
                      Cập nhật kết quả Test
                    </el-dropdown-item>
                    <el-dropdown-item icon="el-icon-s-unfold" command="class_field_status" divided
                                      :disabled="row.id == null">Trạng thái xếp lớp
                    </el-dropdown-item>
                    <!-- <el-dropdown-item icon="el-icon-notebook-2" command="update_note" divided>Sửa note</el-dropdown-item> -->
                    <el-dropdown-item icon="el-icon-edit-outline" command="update" divided :disabled="row.id == null">
                      Sửa
                    </el-dropdown-item>
                    <el-dropdown-item icon="el-icon-delete-solid" command="delete" divided :disabled="row.id == null">
                      Xóa
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <!--        <el-tab-pane label="Lịch chưa chốt" name="schedule_not_finalized">-->
        <!--          <template #label>-->
        <!--            <el-badge :value="countScheduleNotFinalized" class="item">-->
        <!--              Lịch chưa chốt-->
        <!--            </el-badge>-->
        <!--          </template>-->
        <!--          <el-table-->
        <!--              :data="tableDataScheduleNotFinalized"-->
        <!--              v-loading="loadingTableListTestNotFinalized"-->
        <!--              border-->
        <!--              element-loading-text="Loading..."-->
        <!--              element-loading-spinner="el-icon-loading"-->
        <!--              element-loading-background="rgba(0, 0, 0, 0.8)"-->
        <!--              style="width: 100%">-->
        <!--            <el-table-column-->
        <!--                prop="id"-->
        <!--                label="ID"-->
        <!--                align="center"-->
        <!--                width="180">-->
        <!--            </el-table-column>-->
        <!--            <el-table-column-->
        <!--                prop="user_id"-->
        <!--                label="User ID"-->
        <!--                align="center"-->
        <!--            >-->
        <!--            </el-table-column>-->
        <!--            <el-table-column-->
        <!--                label="User NAME"-->
        <!--            >-->
        <!--              <template slot-scope="{row}">-->
        <!--                <el-tag type="primary">{{ row?.user?.name }}</el-tag>-->
        <!--              </template>-->
        <!--            </el-table-column>-->
        <!--            <el-table-column-->
        <!--                label="Thời gian"-->
        <!--                align="center"-->
        <!--            >-->
        <!--              <template slot-scope="{row}">-->
        <!--                {{ moment(row.start_time).format('HH:mm D-MM-YYYY') }}-->
        <!--              </template>-->
        <!--            </el-table-column>-->
        <!--            <el-table-column-->
        <!--                label="Sale tạo"-->
        <!--            >-->
        <!--              <template slot-scope="{row}">-->
        <!--                <el-tag type="primary">{{ row.sale_name }}</el-tag>-->
        <!--              </template>-->
        <!--            </el-table-column>-->
        <!--            <el-table-column-->
        <!--                prop="note"-->
        <!--                label="Ghi chú">-->
        <!--            </el-table-column>-->
        <!--            <el-table-column-->
        <!--                prop="note"-->
        <!--                align="center"-->
        <!--                label="Hành động">-->
        <!--              <template slot-scope="{row}">-->
        <!--                <div>-->
        <!--                  <i class="el-icon-share" @click="attachBlankCalendar(row)"></i>-->
        <!--                  <i style="color: red; cursor: pointer;" class="el-icon-delete-solid"-->
        <!--                     @click="deleteScheduleNotFinalized(row)"></i>-->
        <!--                </div>-->
        <!--              </template>-->
        <!--            </el-table-column>-->
        <!--          </el-table>-->
        <!--        </el-tab-pane>-->
      </el-tabs>
    </div>

    <div v-show="activeName === 'schedule_test'">
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="dataQuery.page"
          :page-size="dataQuery.limit"
          background
          layout="total, prev, pager, next"
          :total="total"
      >
        <template #total>
          total {{ total }}
        </template>
      </el-pagination>
    </div>
    <div v-show="activeName !== 'schedule_test'">
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="dataQueryScheduleNotFinalized.page"
          :page-size="dataQueryScheduleNotFinalized.limit"
          background
          layout="total, prev, pager, next"
          :total="countScheduleNotFinalized"
      >
        <template #total>
          total {{ total }}
        </template>
      </el-pagination>
    </div>

    <el-dialog :title="isUpdate ? 'Sửa lịch test' : 'Thêm Lịch Test'" :visible.sync="dialogFormTestScheduleVisible"
               @closed="closedDialogFormTestSchedule">
      <el-form :model="formTestSchedule" :rules="ruleFormTestSchedule" ref="formTestSchedule">
        <el-form-item label="Lịch test" :label-width="formLabelWidth" prop="kaiwa_teacher_free_time_to_check_id">
          <el-select
              v-model="formTestSchedule.kaiwa_teacher_free_time_to_check_id"
              placeholder="Chọn lịch test"
              filterable
              style="width: 100%;"
              size="small"
              @change="selectTimeBlank"
          >
            <el-option
                v-for="item in teacherFreeTimeBlank"
                :key="item.id"
                :label="item.teacher_name + ' - ' + item.day_free + ' - ' +item.time_start_free + ' - ' + item.time_end_free"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Hóa đơn ID" :label-width="formLabelWidth" prop="invoice">
          <el-select
              v-model="formTestSchedule.invoice"
              filterable
              remote
              reserve-keyword
              placeholder="Nhập ID đơn hàng"
              :remote-method="searchInvoice"
              @change="selectInvoice"
              value-key="id"
              style="width: 100%;"
              clearable
          >
            <el-option
                v-for="item in invoice_option"
                :key="item.id"
                :label="item.id + ' - ' + item.product_name"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Học viên" :label-width="formLabelWidth" prop="user">
          <el-select
              v-model="formTestSchedule.user"
              readonly
              value-key="id"
              style="width: 100%;"
          >
            <el-option
                v-for="item in user_options"
                :key="item.id"
                :label="item.name"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Chọn thời gian bắt đầu" :label-width="formLabelWidth" prop="start_time">
          <el-select
              v-model="formTestSchedule.start_time"
              filterable
              placeholder="Chọn thời gian bắt đầu"
              style="width: 100%;"
              @change="selectTime"
              clearable
          >
            <el-option
                v-for="item in testing_time_frame"
                :disabled="list_time_exists.includes(item.value)"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="formTestSchedule.start_time == -1" label="Chọn mốc thời gian khác"
                      :label-width="formLabelWidth">
          <el-time-picker
              v-model="formTestSchedule.start_time_other"
              placeholder="Chọn mốc thời gian khác"
              style="width: 100%;"
              @change="selectTime"
              format="HH:mm"
              value-format="HH:mm"
          >
          </el-time-picker>
        </el-form-item>
        <el-form-item label="Chọn Cấp độ" :label-width="formLabelWidth" prop="level">
          <el-input
              v-model="formTestSchedule.level"
              :readonly="true"
              style="width: 100%;"
              clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Trạng thái xếp lớp" :label-width="formLabelWidth" prop="class_field_status">
          <el-select
              v-model="formTestSchedule.class_field_status"
              filterable
              placeholder="Trạng thái xếp lớp"
              style="width: 100%;"
              clearable
          >
            <el-option
                v-for="item in class_field_option"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Ghi chú" :label-width="formLabelWidth" prop="note">
          <el-input
              type="textarea"
              :rows="2"
              style="width: 100%;"
              placeholder="Please input"
              v-model="formTestSchedule.note">
          </el-input>
        </el-form-item>
        <el-form-item label="Thông tin Zoom" :label-width="formLabelWidth" prop="zoom_info">
          <el-input
              type="textarea"
              :rows="2"
              style="width: 100%;"
              placeholder="Nhập thông tin Zoom"
              v-model="formTestSchedule.zoom_info">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogFormTestScheduleVisible = false">Cancel</el-button>
        <el-button type="primary"
                   @click="createTestSchedule('formTestSchedule')">{{ isUpdate ? 'Lưu' : 'Thêm' }}</el-button>
      </span>
    </el-dialog>

    <el-dialog title="Thêm Lịch rảnh Giáo viên" :visible.sync="dialogFormFreeTimeTestVisible"
               @closed="closedDialogFormFreeTimeTest('formFreeTimeTest')">
      <el-form :model="formFreeTimeTest" :rules="rules" ref="formFreeTimeTest">
        <el-form-item label="Ngày rảnh" :label-width="formLabelWidth" prop="day">
          <el-date-picker
              v-model="formFreeTimeTest.day"
              type="date"
              value-format="yyyy-MM-dd"
              :picker-options="datePickerOptions"
              @change="selectTime"
              placeholder="Chọn ngày">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="Giờ bắt đầu" :label-width="formLabelWidth" prop="time_start">
          <el-time-select
              v-model="formFreeTimeTest.time_start"
              :picker-options="{
              start: '07:00',
              step: '00:05',
              end: '21:00',
              maxTime: formFreeTimeTest.time_end
            }"
              @change="selectTime"
              format="HH:mm"
              value-format="HH:mm"
              placeholder="Arbitrary time">
          </el-time-select>
        </el-form-item>
        <el-form-item label="Giờ kết thúc" :label-width="formLabelWidth" prop="time_end">
          <el-time-select
              v-model="formFreeTimeTest.time_end"
              :picker-options="{
              start: '07:00',
              step: '00:05',
              end: '21:00',
              minTime: formFreeTimeTest.time_start
            }"
              value-format="HH:mm"
              format="HH:mm"
              @change="selectTime"
              placeholder="Arbitrary time">
          </el-time-select>
        </el-form-item>
        <el-form-item label="Giáo viên" :label-width="formLabelWidth" prop="teacher_id">
          <el-select
              v-model="formFreeTimeTest.teacher_id"
              filterable
              placeholder="Chọn giáo viên"
              style="width: 220px"
              clearable
          >
            <el-option
                v-for="item in list_teacher"
                :key="item.id"
                :label="item.name"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogFormFreeTimeTestVisible = false">Cancel</el-button>
        <el-button type="primary" @click="createFreeTime('formFreeTimeTest')">Thêm</el-button>
      </span>
    </el-dialog>

    <el-dialog title="Thêm lịch chưa chốt" :visible.sync="dialogFormScheduleNotFinalizedVisible"
               @closed="resetForm('formScheduleNotFinalized')">
      <el-form :model="formScheduleNotFinalized" ref="formScheduleNotFinalized">
        <el-form-item label="Hóa đơn ID" :label-width="formLabelWidth" prop="invoice">
          <el-select
              v-model="formScheduleNotFinalized.invoice"
              filterable
              remote
              reserve-keyword
              placeholder="Nhập ID đơn hàng"
              :remote-method="searchInvoice"
              @change="selectInvoice"
              value-key="id"
              style="width: 100%;"
              clearable
          >
            <el-option
                v-for="item in invoice_option"
                :key="item.id"
                :label="item.id + ' - ' + item.product_name"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Học viên" :label-width="formLabelWidth" prop="user">
          <el-select
              v-model="formScheduleNotFinalized.user"
              readonly
              value-key="id"
              style="width: 100%;"
          >
            <el-option
                v-for="item in user_options"
                :key="item.id"
                :label="item.name"
                :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Chọn Cấp độ" :label-width="formLabelWidth" prop="level">
          <el-input
              v-model="formScheduleNotFinalized.level"
              :readonly="true"
              style="width: 100%;"
              clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="Chọn thời gian bắt đầu" :label-width="formLabelWidth" prop="start_time">
          <el-select
              v-model="formScheduleNotFinalized.start_time"
              filterable
              placeholder="Chọn thời gian bắt đầu"
              style="width: 100%;"
              @change="selectTime"
              clearable
          >
            <el-option
                v-for="item in testing_time_frame"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="formScheduleNotFinalized.start_time === -1" label="Chọn mốc thời gian khác"
                      :label-width="formLabelWidth" prop="start_time_other">
          <el-time-picker
              v-model="formScheduleNotFinalized.start_time_other"
              placeholder="Chọn mốc thời gian khác"
              style="width: 100%;"
              @change="selectTime"
              format="HH:mm"
              value-format="HH:mm"
          >
          </el-time-picker>
        </el-form-item>
        <el-form-item label="Ngày rảnh" :label-width="formLabelWidth" prop="start_date">
          <el-date-picker
              v-model="formScheduleNotFinalized.start_date"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :picker-options="datePickerOptions"
              placeholder="Chọn ngày">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="Ghi chú" :label-width="formLabelWidth" prop="note">
          <el-input
              type="textarea"
              :rows="2"
              style="width: 100%;"
              placeholder="Please input"
              v-model="formScheduleNotFinalized.note">
          </el-input>
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogFormScheduleNotFinalizedVisible = false">Cancel</el-button>
        <el-button type="primary" @click="createScheduleNotFinalized('formScheduleNotFinalized')">Thêm</el-button>
      </span>
    </el-dialog>

    <el-dialog
        title="Thông tin Zoom"
        :visible.sync="centerDialogVisibleInfoZoom"
        width="30%"
        center>
      <span style="white-space: pre-line;">{{ text_zoom_info }}</span>
      <span slot="footer" class="dialog-footer">
    <el-button @click="centerDialogVisibleInfoZoom = false">Đóng</el-button>
  </span>
    </el-dialog>
  </div>
</template>

<script>

import RepositoryFactory from '../../repository/repositoryFactory'
import moment from 'moment';

const kaiwaScheduleTestRepository = RepositoryFactory.get('kaiwaScheduleTest')
const userReportRepository = RepositoryFactory.get('communityUser')

const class_field_option = [
  {value: 0, label: 'Chưa xếp lớp'},
  {value: 1, label: 'Đã xếp lớp'},
]

const ruleFormFreeTimeTest = {
  day: [
    {required: true, message: 'Vui lòng chọn ngày', trigger: 'change'},
  ],
  time_start: [
    {required: true, message: 'Vui lòng chọn thời gian bắt đầu', trigger: 'change'}
  ],
  time_end: [
    {required: true, message: 'Vui lòng chọn thời gian kết thúc', trigger: 'change'}
  ],
  teacher_id: [
    {required: true, message: 'Vui lòng chọn giáo viên', trigger: 'change'}
  ],
};

const ruleFormTestSchedule = {
  kaiwa_teacher_free_time_to_check_id: [
    {required: true, message: 'Vui lòng lịch kiểm tra', trigger: 'change'},
  ],
  user: [
    {required: true, message: 'Vui lòng chọn User', trigger: 'change'}
  ],
  start_time: [
    {required: true, message: 'Vui lòng chọn thời gian test', trigger: 'change'}
  ],
  level: [
    {required: true, message: 'Vui lòng chọn cấp độ', trigger: 'change'}
  ],
  class_field_status: [
    {required: true, message: 'Vui lòng chọn trạng thái xếp lớp', trigger: 'change'}
  ],
  invoice: [
    {required: true, message: 'Vui lòng chọn hóa đơn', trigger: 'change'}
  ],
};

export default {
  name: "KaiWaListTest",
  props: ['testing_time_frame', 'admin', 'list_teacher'],
  data() {
    return {
      formTestSchedule: {
        kaiwa_teacher_free_time_to_check_id: '',
        user: '',
        start_time: '',
        level: '',
        class_field_status: '',
        note: '',
        invoice: '',
        start_time_other: '',
        zoom_info: ''
      },
      formFreeTimeTest: {
        day: '',
        time_start: '',
        time_end: ''
      },
      formScheduleNotFinalized: {
        invoice: '',
        user: '',
        start_time: '',
        note: '',
        level: '',
        start_date: ''
      },
      dataQuery: {
        limit: 10,
        page: 1,
        time_range: [],
        teacher_id: '',
        user_id: '',
        invoice_id: ''
      },
      dataQueryScheduleNotFinalized: {
        limit: 10,
        page: 1,
        time_range: [],
        teacher_id: '',
        user_id: '',
      },
      total: 1,
      loadingTableListTest: false,
      loadingTableListTestNotFinalized: false,
      activeName: 'schedule_test',
      tableDataListTest: [],
      user_option_search_box: [],
      user_option_search_box_not_finalized: [],
      loadingSearchUser: false,
      dialogFormTestScheduleVisible: false,
      dialogFormFreeTimeTestVisible: false,
      dialogFormScheduleNotFinalizedVisible: false,
      formLabelWidth: '170px',
      optionPickerTimeStart: '',
      optionPickerTimeEnd: '',
      rules: ruleFormFreeTimeTest,
      ruleFormTestSchedule: ruleFormTestSchedule,
      datePickerOptions: {
        disabledDate(time) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return time.getTime() < today.getTime();
        }
      },
      teacherFreeTimeBlank: [],
      teacherFreeTimeBlankOrigin: [],
      user_options: [],
      user_options_origin: [],
      // level_option: level_option,
      class_field_option: class_field_option,
      invoice_option: [],
      isUpdate: false,
      tableDataScheduleNotFinalized: [],
      countScheduleNotFinalized: 0,
      moment: moment,
      baseUrl: location.origin,
      list_time_exists: [],
      centerDialogVisibleInfoZoom: false,
      text_zoom_info: ''
    }
  },
  watch: {
    dataQuery: {
      handler(newValue, oldValue) {
        this.dataQueryScheduleNotFinalized.time_range = newValue.time_range
        this.dataQueryScheduleNotFinalized.user_id = newValue.user_id
      },
      deep: true
    }
  },
  created() {
    this.getListTest();
    this.getTeacherFreeTimeBlank();
    // this.getScheduleNotFinalized();
    console.log('this.admin', this.admin)
    console.log('testing_time_frame', this.testing_time_frame);

  },
  methods: {
    refresh() {
      this.getListTest();
      this.getTeacherFreeTimeBlank();
      this.getScheduleNotFinalized();
    },
    async getListTest() {
      this.loadingTableListTest = true;

      let res = await kaiwaScheduleTestRepository.getList(this.dataQuery)
      if (res.status == 200) {
        this.tableDataListTest = this.processDataTable(res.data.data)
        this.total = res.data.total
        this.dataQuery.page = res.data.currentPage
      }

      this.loadingTableListTest = false;
    },
    async getTeacherFreeTimeBlank() {
      let res = await kaiwaScheduleTestRepository.getTeacherFreeTimeBlank()
      this.teacherFreeTimeBlank = this.processDataTimeBlank(res.data.data)
      this.teacherFreeTimeBlankOrigin = this.teacherFreeTimeBlank
      console.log("getTeacherFreeTimeBlank", this.teacherFreeTimeBlank);
    },
    async getScheduleNotFinalized() {
      this.loadingTableListTestNotFinalized = true;
      let res = await kaiwaScheduleTestRepository.getScheduleNotFinalized(this.dataQueryScheduleNotFinalized)
      if (res.data.code == 200) {
        this.tableDataScheduleNotFinalized = res.data.data
        this.countScheduleNotFinalized = res.data.total
      }
      this.loadingTableListTestNotFinalized = false;
    },
    async querySearch(keyword) {
      this.loadingSearchUser = true
      let res = await userReportRepository.search({keyword})
      console.log(res);

      this.user_option_search_box = res.data.data

      console.log(this.user_option_search_box);

      this.loadingSearchUser = false
    },
    async querySearchBoxNotFinalized(keyword) {
      this.loadingSearchUser = true
      let res = await userReportRepository.search({keyword})
      console.log(res);

      this.user_option_search_box_not_finalized = res.data.data

      console.log(this.user_option_search_box_not_finalized);

      this.loadingSearchUser = false
    },
    async searchInvoice(id) {
      this.invoice_option = []
      this.loading = true
      let res = await kaiwaScheduleTestRepository.getDetailInvoice(id)
      if (res.data.code == 200 && res.data.data != null) {
        let data = res.data.data
        let invoice_option = {
          id: data.id,
          admin_active_name: data.admin_active_name,
          admin_active: data.admin_active,
          admin_create: data.admin_create,
          info_contact: data.info_contact,
          product_name: data.product_name,
          product_type: data.product_type,
          sale: data.sale,
          user: {
            id: data.user.id,
            name: data.user.name,
            email: data.user.email,
            phone: data.user.phone,
          },
          user_id: data.user_id
        }
        this.invoice_option.push(invoice_option)
      }
      console.log(this.invoice_option);

      this.loading = false
    },
    handleClick(tab, event) {
      console.log(tab.name);
    },
    handleSizeChange(val) {
      if (this.activeName == 'schedule_test') {
        this.dataQuery.limit = val
        this.getListTest()
      } else {
        this.dataQueryScheduleNotFinalized.limit = val
        this.getScheduleNotFinalized();
      }
    },
    handleCurrentChange(val) {
      if (this.activeName == 'schedule_test') {
        this.getListTest()
      } else {
        this.getScheduleNotFinalized();
      }
    },
    handleSelect(val) {
      console.log(`val handleSelect : ${val}`);
    },
    objectSpanMethod({row, column, rowIndex, columnIndex}) {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 8) {
        const parent_id = row.parent_id;
        const count = this.tableDataListTest.filter(item => item.parent_id === parent_id).length;

        if (rowIndex === this.tableDataListTest.findIndex(item => item.parent_id === parent_id)) {
          return [count, 1];
        } else {
          return [0, 0];
        }
      }
    },
    processDataTable(data_table) {
      let result = [];
      data_table.forEach(parent => {
        let data_parent = {
          id: parent?.kaiwa_test_scheduled === undefined ? parent.id : null,
          parent_id: parent?.id,
          end_time_free: parent?.end_time,
          start_time_free: parent?.start_time,
          teacher_name: parent?.teacher_name,
          teacher_id: parent?.teacher_id,
          day_free: moment(parent?.start_time).format('DD-MM-YYYY'),
          time_start_free: moment(parent?.start_time).format('HH:mm'),
          time_end_free: moment(parent?.end_time).format('HH:mm'),
          list_time_exists: [],
          status_receive_shift: parent.status_receive_shift
        }
        if (parent?.kaiwa_test_scheduled !== undefined) {
          if (parent.kaiwa_test_scheduled.length === 0) {
            result.push({
              link_result: null,
              classfield_status: null,
              created_at: null,
              id: null,
              invoice_id: null,
              kaiwa_teacher_free_time_to_check_id: null,
              level: null,
              note: null,
              result_status: null,
              sale_id: null,
              sale_name: null,
              scheduled_status: null,
              start_time: null,
              teacher_id: null,
              updated_at: null,
              user_id: null,
              take_care_after_test: null,
              take_care_before_test: null,
              ...data_parent
            });
          } else {
            parent.kaiwa_test_scheduled.forEach((child, index) => {
              if (child.invoice !== undefined) {
                child.invoice.info_contact = JSON.parse(child.invoice.info_contact)
              }
              data_parent.list_time_exists.push(moment(child.start_time).format("HH:mm"))
              result.push({...data_parent, ...child, start_time: moment(child.start_time).format("HH:mm DD-MM-YYYY")});
            });
          }
        } else {
          console.log('vaof ddaay', data_table)
          result.push(data_parent);
        }
      });
      console.log(result);

      return result;
    },
    processDataTimeBlank(data_blank) {
      let result = [];
      data_blank.forEach(item => {
        let data_parent = {
          id: item.id,
          parent_id: item?.id,
          end_time_free: item?.end_time,
          start_time_free: item?.start_time,
          teacher_name: item?.teacher_name,
          teacher_id: item?.teacher_id,
          day_free: moment(item?.start_time).format('DD-MM-YYYY'),
          time_start_free: moment(item?.start_time).format('HH:mm'),
          time_end_free: moment(item?.end_time).format('HH:mm'),
          list_time_exists: []
        }
        if (item.kaiwa_test_scheduled.length > 0) {
          item.kaiwa_test_scheduled.forEach((child, index) => {
            data_parent.list_time_exists.push(moment(child.start_time).format("HH:mm"))
          });
        }
        result.push(data_parent);
      })
      return result;
    },
    selectTime(val) {
      console.log(`value select: ${val}`);
      if (this.formTestSchedule.start_time != -1 && this.activeName == 'schedule_test') {
        this.formTestSchedule.start_time_other = ''
      }
      if (this.formScheduleNotFinalized.start_time != -1 && this.activeName != 'schedule_test') {
        this.formScheduleNotFinalized.start_time_other = ''
      }
    },
    async createFreeTime(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let res = await kaiwaScheduleTestRepository.createTimeFree(this.formFreeTimeTest)
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.$refs[formName].resetFields()
            this.dialogFormFreeTimeTestVisible = false
            this.getListTest();
            this.getTeacherFreeTimeBlank();
          } else {
            this.$message.error(res.data.msg)
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    async createTestSchedule(formName) {
      console.log(this.formTestSchedule);

      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let res = await kaiwaScheduleTestRepository.createTestSchedule(this.formTestSchedule)
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.$refs[formName].resetFields()
            this.dialogFormTestScheduleVisible = false;
            this.getListTest();
            this.getTeacherFreeTimeBlank();
          } else {
            this.$message.error(res.data.msg)
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    async createScheduleNotFinalized(formName) {
      console.log(this.formScheduleNotFinalized);

      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let res = await kaiwaScheduleTestRepository.createScheduleNotFinalized(this.formScheduleNotFinalized)
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.$refs[formName].resetFields()
            this.getScheduleNotFinalized();
            this.getTeacherFreeTimeBlank();
            this.dialogFormScheduleNotFinalizedVisible = false;
          } else {
            this.$message.error(res.data.msg)
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    selectInvoice(val) {
      console.log(val);
      this.formTestSchedule.user = ''
      this.formTestSchedule.level = ''
      if (val != undefined || val != null || val.length == 0) {
        let user = {
          id: val.user.id,
          name: val.user.name,
          email: val.user.email,
          phone: val.user.phone
        }
        this.user_options = [user]
        this.user_options_origin = this.user_options

        if (this.dialogFormTestScheduleVisible) {
          this.formTestSchedule.user = user
          this.formTestSchedule.level = val.product_name
        }
        if (this.dialogFormScheduleNotFinalizedVisible) {
          this.formScheduleNotFinalized.user = user
          this.formScheduleNotFinalized.level = val.product_name
        }
      }
    },
    async handleCommand(command, row) {
      console.log('handleCommand', row);
      if (command === 'test_result') {
        let action_start = ''
        let link_result = ''

        try {
          await this.$prompt('Bạn muốn cập nhật trạng thái kết quả test như nào?', 'Cảnh báo', {
            confirmButtonText: 'Đỗ',
            cancelButtonText: 'Trượt',
            type: 'warning',
            inputPlaceholder: 'Nhập link kết quả',
            inputValue: row.link_result,
            distinguishCancelAndClose: true,
            center: true,
            beforeClose: (action, instance, done) => {
              if (action === 'confirm' || action === 'cancel') {
                link_result = instance.inputValue;
                action_start = action
                done();
              } else {
                done();
              }
            }
            // inputValidator: (value) => {
            //   if (!value) {
            //     return 'Reason is required';
            //   }
            //   return true;
            // }
          })
        } catch (action) {
          console.log(action)
        }

        let data = {
          schedule_test_id: row.id
        }
        if (action_start === 'confirm' || action_start === 'cancel') {
          data.status_test = action_start === 'confirm' ? 1 : 0
          data.link_result = link_result
          let res = await kaiwaScheduleTestRepository.updateStatusScheduleTest(data)
          console.log(res);

          if (res.data.code === 200) {
            this.$message.success(res.data.msg)
            this.getListTest();
          } else {
            this.$message.error(res.data.msg)
          }
        } else {
          return
        }
      }

      if (command === 'class_field_status') {
        let action_start = ''
        try {
          let a = await this.$confirm('Bạn muốn cập nhật trạng thái xếp lớp như nào?', 'Warning', {
            confirmButtonText: 'Đã xếp lớp',
            cancelButtonText: 'Chưa xếp lớp',
            type: 'warning',
            distinguishCancelAndClose: true,
            center: true
          });

          action_start = a
        } catch (action) {
          action_start = action
        }

        let data = {
          schedule_test_id: row.id
        }
        if (action_start == 'confirm' || action_start == 'cancel') {
          data.class_field_status = action_start == 'confirm' ? 1 : 0
          let res = await kaiwaScheduleTestRepository.updateClassFieldStatus(data)
          console.log(res);

          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getListTest()
          } else {
            this.$message.error(res.data.msg)
          }
        } else {
          return
        }
      }

      if (command === 'update') {
        this.isUpdate = true
        let checkExitsTeacherFreeTimeBlank = this.teacherFreeTimeBlank.filter(item => {
          return item.id === row.kaiwa_teacher_free_time_to_check_id
        });

        console.log("checkExitsTeacherFreeTimeBlank", checkExitsTeacherFreeTimeBlank)
        if (checkExitsTeacherFreeTimeBlank.length === 0) {
          this.teacherFreeTimeBlank.push({
            teacher_name: row.teacher_name,
            day_free: row.day_free,
            time_start_free: row.time_start_free,
            time_end_free: row.time_end_free,
            id: row.kaiwa_teacher_free_time_to_check_id
          })
        }

        this.user_options = [row.user]
        this.formTestSchedule = {
          kaiwa_teacher_free_time_to_check_id: row.parent_id,
          user: row.user,
          start_time: row.time_other_status == 1 ? -1 : moment(row.start_time, "HH:mm DD-MM-YYYY").format('HH:mm'),
          level: row.level,
          class_field_status: row.classfield_status,
          note: row.note,
          invoice: row.invoice_id,
          start_time_other: row.time_other_status == 1 ? moment(row.start_time, 'HH:mm DD-MM-YYYY').format('HH:mm') : '',
          is_update: 1,
          id: row.id,
          teacher_id: row.teacher_id,
          zoom_info: row.zoom_info
        }

        this.list_time_exists = row.list_time_exists
        console.log(this.formTestSchedule);

        this.dialogFormTestScheduleVisible = true
      }

      if (command === 'delete') {
        let a = await this.$confirm('Bạn muốn xóa thời gian TEST không?', 'Cảnh báo', {
          confirmButtonText: 'Có',
          cancelButtonText: 'Không',
          type: 'warning',
          distinguishCancelAndClose: true,
          center: true
        }).then(async () => {
          let res = await kaiwaScheduleTestRepository.deleteScheduleTest({schedule_test_id: row.id})
          if (res.data.code == 200) {
            this.$message.success(res.data.msg)
            this.getListTest();
          } else {
            this.$message.error(res.data.msg)
          }
        }).catch((err) => {
          // this.$message.error(err)
        })
      }
    },
    closedDialogFormTestSchedule() {
      console.log('vào đây');
      console.log("teacherFreeTimeBlank", this.teacherFreeTimeBlank)
      console.log("teacherFreeTimeBlankOrigin", this.teacherFreeTimeBlankOrigin)
      this.isUpdate = false
      this.teacherFreeTimeBlank = this.teacherFreeTimeBlankOrigin
      this.user_options = this.user_options_origin
      this.formTestSchedule = {
        kaiwa_teacher_free_time_to_check_id: '',
        user: '',
        start_time: '',
        level: '',
        class_field_status: '',
        note: '',
        invoice: '',
        start_time_other: ''
      }
      this.list_time_exists = []
      this.resetForm('formTestSchedule');
    },
    closedDialogFormFreeTimeTest(formName) {
      this.formFreeTimeTest = {
        day: '',
        time_start: '',
        time_end: ''
      }
    },
    async updateFreeTime(row) {
      console.log('updateFreeTime', row);

      this.dialogFormFreeTimeTestVisible = true
      this.formFreeTimeTest = {
        day: moment(row.day_free, 'DD-MM-YYYY').format('yyyy-MM-DD'),
        time_start: row.time_start_free,
        time_end: row.time_end_free,
        is_update: 1,
        id: row.parent_id
      }
    },
    async deleteFreeTime(row) {
      console.log(row);
      let a = await this.$confirm('Bạn muốn xóa thời gian rảnh của giáo viên không?', 'Cảnh báo', {
        confirmButtonText: 'Có',
        cancelButtonText: 'Không',
        type: 'warning',
        distinguishCancelAndClose: true,
        center: true
      }).then(async () => {
        let res = await kaiwaScheduleTestRepository.deleteTeacherFreeTime({teacher_free_time_id: row.parent_id})
        if (res.data.code == 200) {
          this.$message.success(res.data.msg)
          this.getListTest();
        } else {
          this.$message.error(res.data.msg)
        }
      }).catch((err) => {
        this.$message.error(err)
      })
    },
    async attachBlankCalendar(row) {
      this.$prompt('Vui lòng nhập ID lịch test của giáo viên', 'Gắn lịch test', {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        inputPattern: /^\d+$/,
        inputErrorMessage: 'Invalid ID',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true;
            try {
              let res = await kaiwaScheduleTestRepository.attachBlankCalendar({
                teacher_free_time_id: instance.inputValue,
                schedule_test_id: row.id
              });

              if (res.data.code === 200) {
                this.getScheduleNotFinalized();
                this.getListTest();
                this.getTeacherFreeTimeBlank();
                this.$message.success(res.data.msg);
                done(); // Đóng prompt sau khi API trả về thành công
              } else {
                this.$message.error(res.data.msg);
              }
            } catch (error) {
              this.$message.error('Có lỗi xảy ra trong quá trình gọi API');
            } finally {
              instance.confirmButtonLoading = false;
            }
          } else {
            done();
          }
        }
      })
    },
    resetForm(form) {
      this.$refs[form].resetFields()
    },
    async deleteScheduleNotFinalized(row) {
      await this.$confirm('Bạn muốn xóa thời gian TEST không?', 'Cảnh báo', {
        confirmButtonText: 'Có',
        cancelButtonText: 'Không',
        type: 'warning',
        distinguishCancelAndClose: true,
        center: true
      }).then(async () => {
        let res = await kaiwaScheduleTestRepository.deleteScheduleTest({schedule_test_id: row.id})
        if (res.data.code == 200) {
          this.$message.success(res.data.msg)
          this.getScheduleNotFinalized();
        } else {
          this.$message.error(res.data.msg)
        }
      }).catch((err) => {
        // this.$message.error(err)
      })
    },
    async update_status_take_care(row, time) {
      await this.$confirm('Bạn muốn cập nhật trạng thái chăm sóc không?', 'Cảnh báo', {
        confirmButtonText: 'Có',
        cancelButtonText: 'Không',
        type: 'warning',
        distinguishCancelAndClose: true,
        center: true
      }).then(async () => {
        let res = await kaiwaScheduleTestRepository.updateStatusTakeCare({schedule_test_id: row.id, time})
        if (res.data.code == 200) {
          this.$message.success(res.data.msg)
          this.getListTest();
        } else {
          this.$message.error(res.data.msg)
        }
      }).catch((err) => {
        // this.$message.error(err)
      })
    },
    async update_status_receive_shift(row, time) {
      await this.$confirm('Bạn muốn cập nhật trạng thái giáo viên nhận ca không?', 'Cảnh báo', {
        confirmButtonText: 'Có',
        cancelButtonText: 'Không',
        type: 'warning',
        distinguishCancelAndClose: true,
        center: true
      }).then(async () => {
        let res = await kaiwaScheduleTestRepository.updateStatusReceiveShift({schedule_test_id: row.id, time})
        if (res.data.code === 200) {
          this.$message.success(res.data.msg)
          await this.getListTest();
        } else {
          this.$message.error(res.data.msg)
        }
      }).catch((err) => {
        // this.$message.error(err)
      })
    },

    selectTimeBlank(id) {
      let teacherFreeTimeBlankSelect = this.teacherFreeTimeBlank.find(item => item.id === id)
      this.list_time_exists = teacherFreeTimeBlankSelect.list_time_exists
    },
    showInfoZoom(val) {
      console.log("val", val)
      this.centerDialogVisibleInfoZoom = true
      this.text_zoom_info = val.zoom_info
    }
  }
}
</script>

<style>
.el-badge__content.is-fixed {
  top: 9px !important;
  right: 0 !important;
}

.el-table .cell {
  word-break: unset;
}

.el-table th.el-table__cell > .cell {
  text-align: center;
}
</style>
