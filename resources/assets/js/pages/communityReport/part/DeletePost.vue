<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header">Xoá bài viết</h3>
        <div slot="body">
            <p>Bạn có chắc muốn xoá bài viết này?</p>
            <div>
              <div class="flex justify-end items-center">
                <button class="btn btn-danger"><span class="glyphicon glyphicon-trash" @click="deleteItem">Xoá</span></button>
                <button class="btn btn-warning ml-5"><span class="glyphicon glyphicon-remove" @click="closeModal">Đóng</span></button>
              </div>
            </div>
        </div>
    </modal>
</template>
<script>
import RepositoryFactory from '../../../repository/repositoryFactory'
const communityPostRepository = RepositoryFactory.get('communityPost')

export default {
  components: {
  },
  props: {
    currentModal: Boolean,
    itemId: Number,
  },
  data() {
    return {
      validate : {
        name: true,
        expired_at: true,
        all : true
      }
    }
  },
  async mounted() {
    // this.getList()
  },
  methods: {
    closeModal() {
      this.$emit('update-delete-post-model', !this.currentModal);
    },
    async deleteItem() {
      let response = await communityPostRepository.delete(this.itemId);
      if(response.status == 200) {
        this.$emit('update-delete-post-model', !this.currentModal);
        return;
      }
    },
  }
}
</script>
