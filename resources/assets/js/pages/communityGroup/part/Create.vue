<template>
  <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
    <h3 slot="header">Thêm nhóm</h3>
    <div slot="body" @click="blurSelect">
      <form class="">
        <div class="form-group">
          <label>Tên lớp</label>
          <input class="form-control" v-model="group.name" required/>
          <div class="invalid-feedback text-red" v-if="!validate.name">
            Điền tên nhóm
          </div>
          <div v-if="!group.name?.includes(' - ')">{{ nameGen }}</div>
          <div v-if="nameGen && !group.name?.includes(' - ')" class="btn btn-block btn-success p-3 mt-5 w-[150px]"
               @click="applyNameGen">Dùng tên này
          </div>
        </div>
        <div class="form-group">
          <label>Vip Combo</label>
          <v-select
              v-model="group.vip_combo_id"
              :options="vipCombos"
              label="name"
              :reduce="(combo) => combo.id"
          >
          </v-select>
        </div>
        <div class="form-group">
          <label>Số học viên tối đa</label>
          <input
              class="form-control"
              v-model="group.size"
              type="number"
              required
          />
          <!-- <div class="invalid-feedbac text-redk" v-if="!validate.name">
            Điền số học viên
          </div> -->
        </div>
        <div v-if="!group.is_sample" class="form-group">
          <label>Ngày khai giảng</label>
          <VueCtkDateTimePicker
              v-model="group.start_date"
              :auto-close="true"
              :label="startDate.label"
              :only-date="startDate.onlyDate"
              :format="startDate.format"
          />
          <div class="invalid-feedback text-red" v-if="!validate.start_date">
            Điền ngày khai giảng
          </div>
        </div>
        <div class="form-group">
          <label>Số buổi học</label>
          <input
              class="form-control"
              v-model="group.vip_session"
              required
              type="number"
          />
          <div class="invalid-feedback text-red" v-if="!validate.vip_session">
            Điền số buổi học
          </div>
        </div>
        <div class="form-group">
          <label for="">Ngày học</label>
          <el-select
              :value="group.shift_type ? group.shift_type.split('').map((o) => parseInt(o)) : []"
              placeholder="Select"
              multiple
              @change="changeGroupShift(group, $event)"
              class="w-full"
              ref="shiftTypeSelect"
          >
            <el-option
                v-for="item in weekDayList"
                :key="`day-${item.value}`"
                :value="item.value"
                :label="item.label"
            ></el-option>
          </el-select>
        </div>
        <div v-if="!group.is_sample" class="form-group">
          <label>Ngày kết thúc</label>
          <VueCtkDateTimePicker
              v-model="group.end_at"
              :auto-close="true"
              :label="endDate.label"
              :only-date="endDate.onlyDate"
              :format="endDate.format"
          />
          <div class="invalid-feedback text-red" v-if="!validate.end_at">
            Điền ngày kết thúc
          </div>
        </div>
        <div v-if="!group.is_sample" class="form-group">
          <label>Ngày hết hạn</label>
          <VueCtkDateTimePicker
              v-model="group.expired_at"
              :auto-close="true"
              :label="date.label"
              :only-date="date.onlyDate"
              :format="date.format"
          />
          <div class="invalid-feedback text-red" v-if="!validate.expired_at">
            Điền ngày hết hạn
          </div>
        </div>
        <div class="form-group">
          <label for="">Loại</label>
          <select v-model="group.type" class="form-control">
            <option :value="index" v-for="(type, index) in types" :key="index">
              {{ type }}
            </option>
          </select>
        </div>
        <div class="form-group">
          <label for="">Loại hình lớp nhận</label>
          <input class="form-control" v-model="group.type_note" required/>
        </div>
        <div class="form-group">
          <label for="">Ca học</label>
          <VueCtkDateTimePicker
              id="shiftTimeFromPicker"
              v-model="group.shift_time"
              :label="shiftTimeStart.label"
              :minute-interval="5"
              :only-time="true"
              :format="shiftTimeStart.format"
              :formatted="shiftTimeStart.formatted"
          />
        </div>
        <div class="form-group">
          <label>Cấp độ nhóm vip</label>
          <select class="form-control" v-model="group.vip_level">
            <option value="null">Chọn cấp độ</option>
            <option :value="level.value" v-for="(level, index) in vipLevelList" :key="index">
              {{ level.title }}
            </option>
          </select>
          <div class="invalid-feedback text-red" v-if="!validate.vip_level">
            Vui lòng chọn cấp độ
          </div>
        </div>
        <div class="form-group">
          <label>Giáo viên</label>
          <v-select
              v-model="group.teacher_id"
              :options="teacherList"
              label="fullName"
              :reduce="(teacher) => teacher.id"
          >
          </v-select>
        </div>
        <div class="form-group">
          <label>Link nhóm</label>
          <input
              type="text"
              class="form-control"
              v-model="group.links.groupLink.url"
          />
          <label>Link tài liệu</label>
          <input
              type="text"
              class="form-control"
              v-model="group.links.doc.url"
          />
          <label>Link messenger</label>
          <input
              type="text"
              class="form-control"
              v-model="group.links.messenger.url"
          />
          <div class="flex" style="margin-top: 10px">
            <div style="width: 70%; margin-right: 30px">
              <label>Link zoom</label>
              <input
                  type="text"
                  class="form-control"
                  v-model="group.links.linkZoom.url"
              />
            </div>
            <div style="width: 30%">
              <label>Mật khẩu</label>
              <input
                  type="text"
                  class="form-control"
                  v-model="group.links.linkZoom.note"
              />
            </div>
          </div>
        </div>
        <div class="form-group">
          <label>Ghi chú</label>
          <input
              class="form-control"
              v-model="group.note"
              type="text"
          />
        </div>
        <div class="form-check">
          <input
              class="form-check-input"
              type="checkbox"
              v-model="group.is_sample"
              id="defaultCheck1"
          />
          <label class="form-check-label" for="defaultCheck1"> Nhóm mẫu </label>
        </div>
        <div v-if="!group.is_sample" class="form-group">
          <label>Chọn nhóm mẫu<small> (Nếu có)</small></label>
          <select
              class="form-control"
              id="exampleFormControlSelect1"
              @change="changeSampe($event)"
              v-model="group.isSampleGroupId"
          >
            <option selected>Lựa chọn nhóm mẫu</option>
            <option
                v-for="(item, index) in sampleGroups"
                :key="index"
                :value="item.id"
            >
              {{ item.name }}
            </option>
          </select>
        </div>
        <div v-if="group.is_sample" class="form-group">
          <label>Số ngày hoạt động</label>
          <input
              type="number"
              class="form-control"
              v-model="group.expired_after_days"
              min="0"
              max="9999"
          />
        </div>
        <div class="form-inline">
          <div class="form-group">
            <label>Ảnh đại diện test</label>
            <vue-upload-multiple-image
                @upload-success="uploadImageSuccess"
                @before-remove="beforeRemove"
                @edit-image="editImageAvatar"
                :data-images="images"
                :multiple="multiple"
                :idUpload="idUpload"
                :idEdit="idEdit"
            ></vue-upload-multiple-image>
          </div>
          <div class="form-group ml-5">
            <label>Ảnh bìa</label>
            <vue-upload-multiple-image
                @upload-success="uploadImageSuccessBanner"
                @before-remove="beforeRemoveBanner"
                @edit-image="editImageBanner"
                :data-images="bannerImages"
                :multiple="multiple"
                :idUpload="idUploadBanner"
                :idEdit="idEditBanner"
            ></vue-upload-multiple-image>
          </div>
        </div>
        <div class="invalid-feedback text-red" v-if="!validate.all">
          Đã có lỗi xảy ra
        </div>
      </form>
      <button
          class="btn btn-block btn-success p-5 mt-5"
          :disabled="loading"
          @click="createGroup"
      >
        Tạo
      </button>
    </div>
  </modal>
</template>
<script>
import VueCtkDateTimePicker from "vue-ctk-date-time-picker";
import "vue-ctk-date-time-picker/dist/vue-ctk-date-time-picker.css";
import VueUploadMultipleImage from "vue-upload-multiple-image";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

import RepositoryFactory from "../../../repository/repositoryFactory";

const communityGroupRepository = RepositoryFactory.get("communityGroup");
const communityPostRepository = RepositoryFactory.get("communityPost");

export default {
  components: {
    VueCtkDateTimePicker,
    VueUploadMultipleImage,
    "v-select": vSelect,
  },
  props: {
    currentModal: Boolean,
    teacherList: Array,
  },
  computed: {
    name: function () {
      return this.group.name;
    },
    teacher_id: function () {
      return this.group.teacher_id;
    },
    start_date() {
      return this.group.start_date;
    },
    vip_session() {
      return this.group.vip_session;
    },
    shift_type() {
      return this.group.shift_type;
    },
    shift_time() {
      return this.group.shift_time;
    },
    type() {
      return this.group.type;
    },
    vip_level() {
      return this.group.vip_level;
    },
  },
  data() {
    return {
      nameGen: '',
      group: {
        name: null,
        start_date: null,
        expired_at: null,
        end_at: null,
        vip_session: 0,
        is_sample: false,
        isSampleGroupId: null,
        expired_after_days: 0,
        avatar: null,
        banner: null,
        vip_level: null,
        size: 0,
        teacher_id: null,
        type: "vip15",
        type_note: "",
        shift_type: "246",
        shift_time: null,
        note: '',
        links: {
          groupLink: {
            url: "",
            status: false,
            note: "",
          },
          doc: {
            url: "",
            status: false,
            note: "",
          },
          messenger: {
            url: "",
            status: false,
            note: "",
          },
          linkZoom: {
            url: "",
            status: false,
            note: "",
          },
        },
      },
      vipLevelList: [
        {value: "N1", title: "N1"},
        {value: "N2", title: "N2"},
        {value: "N3", title: "N3"},
        {value: "N4", title: "N4"},
        {value: "N5", title: "N5"},
        {value: "kaiwacb", title: "Kaiwa cơ bản"},
        {value: "kaiwasc", title: "Kaiwa sơ cấp"},
        {value: "kaiwatc", title: "Kaiwa trung cấp 1"},
        {value: "kaiwatc2", title: "Kaiwa trung cấp 2"},
        {value: "kaiwanc", title: "Kaiwa nâng cao"},
        {value: "ldn1", title: "Luyện đề N1"},
        {value: "ldn2", title: "Luyện đề N2"},
        {value: "ldn3", title: "Luyện đề N3"},
        {value: "ldn4", title: "Luyện đề N4"},
        {value: "ldn5", title: "Luyện đề N5"},
        {value: "tokutei1", title: "Tokutei 1"},
        {value: "tokutei2", title: "Tokutei 2"},
      ],
      sampleGroups: [],
      images: [],
      bannerImages: [],
      idUpload: "avatarid",
      idUploadBanner: "bannerid",
      idEdit: "editAvatarid",
      idEditBanner: "editBannerid",
      multiple: false,
      validate: {
        name: true,
        start_date: true,
        expired_at: true,
        end_at: true,
        vip_session: true,
        all: true,
        vip_level: true,
      },
      startDate: {
        label: "Chọn ngày khai giảng",
        onlyDate: true,
        format: "YYYY-MM-DD",
      },
      endDate: {
        label: "Chọn ngày kết thúc",
        onlyDate: true,
        format: "YYYY-MM-DD",
      },
      date: {
        label: "Chọn ngày hết hạn",
        onlyDate: true,
        format: "YYYY-MM-DD",
      },
      shiftTimeStart: {
        label: "Ca học",
        onlyDate: false,
        format: "HH:mm:00",
        formatted: "HH:mm:00",
      },
      types: {
        basic: "Basic",
        vip500: "Vip500",
        vip15: "Vip15",
        vip1: "Vip1",
        luyende: "Luyện đề",
        online: "Online",
        vip9: "Vip9",
        offline: "Offline",
        captoc: "Cấp tốc",
        matgoc: "Mất gốc",
        kaiwa: 'Kaiwa',
        b2b: 'Doanh nghiệp',
        tokutei: 'Tokutei',
      },
      loading: false,
      weekDayList: [
        {value: 2, label: "Thứ 2"},
        {value: 3, label: "Thứ 3"},
        {value: 4, label: "Thứ 4"},
        {value: 5, label: "Thứ 5"},
        {value: 6, label: "Thứ 6"},
        {value: 7, label: "Thứ 7"},
        {value: 8, label: "Chủ nhật"},
      ],
      vipCombos: [],
    };
  },
  watch: {
    start_date: function value() {
      this.autoSetDate();
      this.autoSetName();
    },
    name: function value() {
      this.autoSetName();
    },
    vip_session: function value() {
      this.autoSetDate();
    },
    shift_type: function value() {
      this.autoSetDate();
      this.autoSetName();
    },
    shift_time: function value() {
      this.autoSetName();
    },
    teacher_id: function value() {
      this.autoSetName();
    },
    type: function value() {
      this.autoSetName();
    },
    vip_level: function value() {
      this.autoSetName();
    },
  },
  async mounted() {
    // this.getList()
    this.getSampleList();
    this.getVipCombo();
  },
  methods: {
    async getVipCombo() {
      try {
        let response = await communityGroupRepository.getVipCombo();
        this.vipCombos = response.data;
      } catch (e) {
        console.log(e);
      }
    },
    autoSetName() {
      let nameGen = this.group.name ?? '';
      if (this.group.vip_level) {
        nameGen += `${nameGen ? ' - ' : ''}${this.group.vip_level}`;
      }
      if (this.group.type) {
        nameGen += ` - ${this.group.type.toUpperCase()}`;
      }
      if (this.group.start_date) {
        nameGen += ` - ${moment(this.group.start_date).format('DDMMYY')}`;
      }
      if (this.group.shift_type) {
        nameGen += ` (${this.group.shift_type})`;
      }
      if (this.group.shift_time) {
        nameGen += ` (${this.group.shift_time.substring(0, 5)})`;
      }
      if (this.group.teacher_id) {
        const teacher = this.teacherList.find((t) => t.id == this.group.teacher_id);
        if (teacher) {
          nameGen += ` - ${teacher.fullName.toUpperCase()}`;
        }
      }
      this.nameGen = nameGen;
    },
    applyNameGen() {
      this.group.name = this.nameGen;
      this.nameGen = '';
    },
    autoSetDate() {
      const vip_session = parseInt(this.group.vip_session);
      if (this.group.start_date && vip_session && vip_session > 0 && this.group.shift_type) {
        const timeInterval = moment(this.group.start_date);

        let i = 0;
        while (i < vip_session) {
          if (this.group.shift_type.includes((timeInterval.day() + 1).toString())) {
            i++;
          }
          timeInterval.add(1, 'day');
        }
        this.group.end_at = timeInterval.subtract(1, 'day').format('YYYY-MM-DD');
        this.group.expired_at = timeInterval.add(1, 'month').format('YYYY-MM-DD');
      }
    },
    closeModal() {
      this.$emit("update-create-model", !this.currentModal);
    },
    changeGroupShift(group, value) {
      group.shift_type = value.sort().join("");
    },
    async createGroup() {
      if (this.loading) return;
      if (!this.group.name) {
        this.validate.name = false;
        return;
      } else {
        this.validate.name = true;
      }
      if (this.group.start_date === null && !this.group.is_sample) {
        this.validate.start_date = false;
        return;
      } else {
        this.validate.start_date = true;
      }
      if (this.group.expired_at === null && !this.group.is_sample) {
        this.validate.expired_at = false;
        return;
      } else {
        this.validate.expired_at = true;
      }
      if (this.group.end_at === null && !this.group.is_sample) {
        this.validate.end_at = false;
        return;
      } else {
        this.validate.end_at = true;
      }
      if (this.group.vip_session === null && !this.group.is_sample) {
        this.validate.vip_session = false;
        return;
      } else {
        this.validate.vip_session = true;
      }
      if (this.group.vip_level === null) {
        this.validate.vip_level = false;
        return;
      } else {
        this.validate.vip_level = true;
      }
      this.resetValide();
      try {
        let response = await communityGroupRepository.store(this.group);
        this.$emit("update-create-model", !this.currentModal);
        return;
      } catch (error) {
        console.log(error);
        this.validate.all = false;
        return false;
      }
    },
    resetValide() {
      this.validate = {
        name: true,
        start_date: true,
        expired_at: true,
        end_at: true,
        vip_session: true,
        all: true,
      };
    },
    async getSampleList() {
      try {
        // this.loading = true
        let response = await communityGroupRepository.getSampleList();
        this.sampleGroups = response.data.data;
      } catch (e) {
        console.log(e);
        // this.$bus.$emit('show-loading', false)
      }
    },
    async changeSampe(event) {
      let id = event.target.value;
      let response = await communityGroupRepository.urlGetById(id);
      this.group.start_date = response.data.data.start_date;
      this.group.end_at = response.data.data.end_at;
      this.group.expired_at = response.data.data.expired_at;
    },
    async uploadImageSuccess(formData, index, fileList) {
      let response = await communityPostRepository.uploadImage(formData);
      this.group.avatar = response.data.data;
    },
    async editImageAvatar(formData, index, fileList) {
      let response = await communityPostRepository.uploadImage(formData);
      this.group.avatar = response.data.data;
    },
    beforeRemove(index, done, fileList) {
      this.group.avatar = null;
      done();
    },
    async uploadImageSuccessBanner(formData, index, fileList) {
      this.loading = true;
      let response = await communityPostRepository.uploadImage(formData);
      this.group.banner = response.data.data;
      this.loading = false;
    },
    async editImageBanner(formData, index, fileList) {
      this.loading = true;
      let response = await communityPostRepository.uploadImage(formData);
      this.group.banner = response.data.data;
      this.loading = false;
    },
    beforeRemoveBanner(index, done, fileList) {
      this.group.banner = null;
      done();
    },
    blurSelect() {
      this.$refs.shiftTypeSelect.blur();
    },
  },
};
</script>
<style lang="scss">

</style>
