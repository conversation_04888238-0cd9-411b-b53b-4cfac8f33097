<template>
  <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
    <h3 slot="header">Cập nhật nhóm</h3>
    <div slot="body" @click="blurSelect">
      <form class="">
        <div class="form-group">
          <label>Tên lớp</label>
          <input class="form-control" v-model="group.name" required />
          <div class="invalid-feedback text-red" v-if="!validate.name">
            Điền tên nhóm
          </div>
        </div>
        <div class="form-group">
          <label>Vip Combo</label>
          <v-select
              v-model="group.vip_combo_id"
              :options="vipCombos"
              label="name"
              :reduce="(combo) => combo.id"
          >
          </v-select>
        </div>
        <div class="form-group">
          <label>Số học viên tối đa</label>
          <input
            class="form-control"
            v-model="group.size"
            type="number"
            required
          />
          <!-- <div class="invalid-feedback text-red" v-if="!validate.name">
            Đ<PERSON><PERSON><PERSON> số học viên
          </div> -->
        </div>
        <div v-if="!group.is_sample" class="form-group">
          <label>Ngày khai giảng</label>
          <input
            type="date"
            class="form-control date_picker mask_datess"
            v-model="start_date"
          />
          <div class="invalid-feedback text-red" v-if="!validate.start_date">
            Điền ngày hết hạn
          </div>
        </div>
        <div class="form-group">
          <label>Số buổi học</label>
          <input
            class="form-control"
            v-model="vip_session"
            required
            type="number"
          />
          <div class="invalid-feedback text-red" v-if="!validate.vip_session">
            Điền số buổi
          </div>
        </div>
        <div class="form-group">
          <label for="">Ngày học</label>
          <el-select
            :value="shift_type ? shift_type.split('').map((o) => parseInt(o)) : []"
            placeholder="Select"
            multiple
            @change="changeGroupShift(group, $event)"
            class="w-full"
            ref="shiftTypeSelect"
          >
            <el-option
              v-for="item in weekDayList"
              :key="`day-${item.value}`"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </div>
        <div v-if="!group.is_sample" class="form-group">
          <label>Ngày kết thúc</label>
          <input
            type="date"
            class="form-control date_picker mask_datess"
            v-model="group.end_at"
          />
          <div class="invalid-feedback text-red" v-if="!validate.end_at">
            Điền ngày kết thúc
          </div>
        </div>
        <div v-if="!group.is_sample" class="form-group">
          <label>Ngày hết hạn</label>
          <input
            type="date"
            class="form-control date_picker mask_datess"
            v-model="group.expired_at"
          />
          <div class="invalid-feedback text-red" v-if="!validate.expired_at">
            Điền ngày hết hạn
          </div>
        </div>
        <div class="form-group">
          <label for="">Loại</label>
          <select v-model="group.type" class="form-control">
            <option :value="index" v-for="(type, index) in types">
              {{ type }}
            </option>
          </select>
        </div>
        <div class="form-group">
          <label for="">Loại hình lớp nhận</label>
          <input class="form-control" v-model="group.type_note" required />
        </div>
        <div class="form-group">
          <label for="">Ca học</label>
          <VueCtkDateTimePicker
            id="shiftTimeFromPicker"
            v-model="group.shift_time"
            :label="shiftTimeStart.label"
            :minute-interval="5"
            :only-time="true"
            :format="shiftTimeStart.format"
            :formatted="shiftTimeStart.formatted"
          />
        </div>
        <div class="form-group">
          <label>Cấp độ nhóm vip</label>
          <select class="form-control" v-model="group.vip_level">
            <option value="null">Chọn cấp độ</option>
            <option :value="level.value" v-for="level in vipLevelList">
              {{ level.title }}
            </option>
          </select>
        </div>
        <div class="form-group">
          <label>Giáo viên</label>
          <v-select
            v-model="group.teacher_id"
            :options="teacherList"
            label="fullName"
            :reduce="(teacher) => teacher.id"
          >
          </v-select>
        </div>
        <div class="form-group">
          <label>Link nhóm</label>
          <input
            type="text"
            class="form-control"
            v-model="group.links.groupLink.url"
          />
          <label>Link tài liệu</label>
          <input
            type="text"
            class="form-control"
            v-model="group.links.doc.url"
          />
          <label>Link messenger</label>
          <input
            type="text"
            class="form-control"
            v-model="group.links.messenger.url"
          />
          <div class="flex" style="margin-top: 10px">
            <div style="width: 70%; margin-right: 30px">
              <label>Link zoom</label>
              <input
                type="text"
                class="form-control"
                v-model="group.links.linkZoom.url"
              />
            </div>
            <div style="width: 30%">
              <label>Mật khẩu</label>
              <input
                type="text"
                class="form-control"
                v-model="group.links.linkZoom.note"
              />
            </div>
          </div>
        </div>
        <div class="form-group">
          <label>Ghi chú</label>
          <input
              class="form-control"
              v-model="group.note"
              type="text"
          />
        </div>
        <div v-if="group.is_sample" class="form-group">
          <label>Số ngày hoạt động</label>
          <input
            type="number"
            class="form-control"
            v-model="group.expired_after_days"
            min="0"
            max="9999"
          />
        </div>
        <div class="form-inline">
          <div class="form-group">
            <label>Ảnh đại diện</label>
            <vue-upload-multiple-image
              @upload-success="uploadImageSuccess"
              @before-remove="beforeRemove"
              @edit-image="editImage"
              :data-images="images"
              :multiple="multiple"
              :idUpload="idUpload"
              :idEdit="idEdit"
            ></vue-upload-multiple-image>
          </div>
          <div class="form-group ml-5">
            <label>Ảnh bìa</label>
            <vue-upload-multiple-image
              @upload-success="uploadImageSuccessBanner"
              @before-remove="beforeRemoveBanner"
              @edit-image="editImageBanner"
              :data-images="bannerImages"
              :multiple="multiple"
              :idUpload="idUploadBanner"
              :idEdit="idEditBanner"
            ></vue-upload-multiple-image>
          </div>
        </div>
        <div class="invalid-feedback text-red" v-if="!validate.all">
          Đã có lỗi sảy ra
        </div>
      </form>
      <button
        class="btn btn-block btn-success p-5 mt-5"
        @click="updateGroup"
        :disabled="loading"
      >
        Cập nhật
      </button>
    </div>
  </modal>
</template>
<script>
import VueCtkDateTimePicker from "vue-ctk-date-time-picker";
import "vue-ctk-date-time-picker/dist/vue-ctk-date-time-picker.css";
import VueUploadMultipleImage from "vue-upload-multiple-image";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import moment from "moment";

import RepositoryFactory from "../../../repository/repositoryFactory";
const communityGroupRepository = RepositoryFactory.get("communityGroup");
const communityPostRepository = RepositoryFactory.get("communityPost");

export default {
  components: {
    VueCtkDateTimePicker,
    VueUploadMultipleImage,
    "v-select": vSelect,
  },
  props: {
    currentModal: Boolean,
    group: Object,
    teacherList: Array,
  },
  data() {
    return {
      start_date: null,
      vip_session: null,
      shift_type: null,
      images: [],
      bannerImages: [],
      idUpload: "avatarid",
      idUploadBanner: "bannerid",
      idEdit: "editAvatarid",
      idEditBanner: "editBannerid",
      multiple: false,
      vipLevelList: [
        { value: "N1", title: "N1" },
        { value: "N2", title: "N2" },
        { value: "N3", title: "N3" },
        { value: "N4", title: "N4" },
        { value: "N5", title: "N5" },
        { value: "kaiwacb", title: "Kaiwa cơ bản" },
        { value: "kaiwasc", title: "Kaiwa sơ cấp" },
        { value: "kaiwatc", title: "Kaiwa trung cấp 1" },
        { value: "kaiwatc2", title: "Kaiwa trung cấp 2" },
        { value: "kaiwanc", title: "Kaiwa nâng cao" },
        { value: "ldn1", title: "Luyện đề N1" },
        { value: "ldn2", title: "Luyện đề N2" },
        { value: "ldn3", title: "Luyện đề N3" },
        { value: "ldn4", title: "Luyện đề N4" },
        { value: "ldn5", title: "Luyện đề N5" },
        { value: "tokutei1", title: "Tokutei 1" },
        { value: "tokutei2", title: "Tokutei 2" },
      ],
      validate: {
        name: true,
        start_date: true,
        end_at: true,
        expired_at: true,
        vip_session: true,
        all: true,
      },
      shiftTimeStart: {
        label: "Ca học",
        onlyDate: false,
        format: "HH:mm:00",
        formatted: "HH:mm:00",
      },
      types: {
        basic: "Basic",
        vip500: "Vip500",
        vip15: "Vip15",
        vip1: "Vip1",
        luyende: "Luyện đề",
        online: "Online",
        vip9: "Vip9",
        offline: "Offline",
        captoc: "Cấp tốc",
        matgoc: "Mất gốc",
        kaiwa: 'Kaiwa',
        b2b: 'Doanh nghiệp',
        tokutei: 'Tokutei',
      },
      loading: false,
      weekDayList: [
        { value: 2, label: "Thứ 2" },
        { value: 3, label: "Thứ 3" },
        { value: 4, label: "Thứ 4" },
        { value: 5, label: "Thứ 5" },
        { value: 6, label: "Thứ 6" },
        { value: 7, label: "Thứ 7" },
        { value: 8, label: "Chủ nhật" },
      ],
      vipCombos: [],
    };
  },
  watch: {
    start_date: function value(newVal, oldVal) {
      if (oldVal) {
        this.autoSetDate();
      }
    },
    vip_session: function value(newVal, oldVal) {
      if (oldVal) {
        this.autoSetDate();
      }
    },
    shift_type: function value(newVal, oldVal) {
      if (oldVal) {
        this.autoSetDate();
      }
    }
  },
  created() {
    this.start_date = this.group.start_date;
    this.vip_session = this.group.vip_session;
    this.shift_type = this.group.shift_type;
  },
  async mounted() {
    // this.getList()
    this.checkGroupImage(this.group);
    this.getVipCombo();
  },
  methods: {
    async getVipCombo() {
      try {
        let response = await communityGroupRepository.getVipCombo();
        this.vipCombos = response.data;
      } catch (e) {
        console.log(e);
      }
    },
    autoSetDate() {
      const vip_session = parseInt(this.vip_session);
      if (this.start_date && vip_session && vip_session > 0 && this.shift_type) {
        const timeInterval = moment(this.start_date);

        let i = 0;
        while (i < vip_session) {
          if (this.shift_type.includes((timeInterval.day() + 1).toString())) {
            i++;
          }
          timeInterval.add(1, 'day');
        }
        this.group.end_at = timeInterval.subtract(1, 'day').format('YYYY-MM-DD');
        this.group.expired_at = timeInterval.add(1, 'month').format('YYYY-MM-DD');
      }
    },
    closeModal() {
      this.$emit("update-edit-model", !this.currentModal);
    },
    changeGroupShift(group, value) {
      group.shift_type = value.sort().join("");
      this.shift_type = value.sort().join("");
    },
    async updateGroup() {
      this.group.start_date = this.start_date;
      this.group.vip_session = this.vip_session;
      this.group.shift_type = this.shift_type;
      if (this.loading) return;
      if (this.group.name === null) {
        this.validate.name = false;
        return;
      } else {
        this.validate.name = true;
      }
      if (this.group.start_date === null) {
        this.validate.start_date = false;
        return;
      } else {
        this.validate.start_date = true;
      }
      if (this.group.end_at === null) {
        this.validate.end_at = false;
        return;
      } else {
        this.validate.end_at = true;
      }
      if (this.group.expired_at === null) {
        this.validate.expired_at = false;
        return;
      } else {
        this.validate.expired_at = true;
      }
      if (this.group.vip_session === null) {
        this.validate.vip_session = false;
        return;
      } else {
        this.validate.vip_session = true;

      }
      this.resetValide();
      let response = await communityGroupRepository.update(
        this.group.id,
        this.group
      );
      if (response.status == 200) {
        this.$emit("update-edit-model", !this.currentModal);
        return;
      } else {
        this.validate.all = false;
        return false;
      }
    },
    resetValide() {
      this.validate = {
        name: true,
        expried_date: true,
        all: true,
      };
    },
    async changeSampe(event) {
      let id = event.target.value;
      let response = await communityGroupRepository.urlGetById(id);
      this.group.start_date = response.data.data.start_date;
      this.group.expired_at = response.data.data.expired_at;
    },
    async uploadImageSuccess(formData, index, fileList) {
      let response = await communityPostRepository.uploadImage(formData);
      this.group.avatar = response.data.data;
    },
    async editImage(formData, index, fileList) {
      let response = await communityPostRepository.uploadImage(formData);
      this.group.avatar = response.data.data;
    },
    beforeRemove(index, done, fileList) {
      this.group.avatar = null;
      done();
    },
    async uploadImageSuccessBanner(formData, index, fileList) {
      this.loading = true;
      let response = await communityPostRepository.uploadImage(formData);
      this.group.banner = response.data.data;
      this.loading = false;
    },
    async editImageBanner(formData, index, fileList) {
      this.loading = true;
      let response = await communityPostRepository.uploadImage(formData);
      this.group.banner = response.data.data;
      this.loading = false;
    },
    beforeRemoveBanner(index, done, fileList) {
      this.group.banner = null;
      done();
    },
    checkGroupImage(group) {
      if (group.avatar) {
        this.images.push({
          path: `${window.location.origin}/cdn/community/default/${this.group.avatar}`,
          default: 1,
          highlight: 1,
        });
      }
      if (group.banner) {
        this.bannerImages.push({
          path: `${window.location.origin}/cdn/community/default/${this.group.banner}`,
          default: 1,
          highlight: 1,
        });
      }
    },
    blurSelect() {
      this.$refs.shiftTypeSelect.blur();
    },
  },
};
</script>
