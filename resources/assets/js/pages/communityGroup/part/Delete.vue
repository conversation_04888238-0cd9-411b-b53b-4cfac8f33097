<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header">Xoá nhóm</h3>
        <div slot="body">
            <p>Bạn có chắc muốn xoá nhóm?</p>
            <div>
              <div class="flex justify-end items-center">
                <button class="btn btn-danger"><span class="glyphicon glyphicon-trash" @click="deleteGroup">Xoá</span></button>
                <button class="btn btn-warning ml-5"><span class="glyphicon glyphicon-remove" @click="closeModal">Đóng</span></button>
              </div>
            </div>
        </div>
    </modal>
</template>
<script>
import RepositoryFactory from '../../../repository/repositoryFactory'
const communityGroupRepository = RepositoryFactory.get('communityGroup')

export default {
  components: {
  },
  props: {
    currentModal: <PERSON>olean,
    groupId: Number,
  },
  data() {
    return {
      validate : {
        name: true,
        expired_at: true,
        all : true
      }
    }
  },
  async mounted() {
    // this.getList()
  },
  methods: {
    closeModal() {
      this.$emit('update-delete-model', !this.currentModal);
    },
    async deleteGroup() {
      let response = await communityGroupRepository.delete(this.groupId);
      if(response.status == 200) {
        this.$emit('update-delete-model', !this.currentModal);
        return;
      }
    },
  }
}
</script>
