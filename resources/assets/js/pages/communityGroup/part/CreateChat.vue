<template>
  <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
    <div slot="header" class="flex justify-between">
      <h3>Tạo nhóm chat</h3>
      <span @click="closeModal" class="cursor-pointer">Đóng</span>
    </div>
    <div slot="body">
      <form class="">
        <div class="form-group">
          <label>Tên nhóm</label>
          <input class="form-control" v-model="groupName" placeholder="Điền tên nhóm" required />
        </div>
      </form>
      <span class="btn btn-block btn-success p-5 mt-5" @click="createGroupChat">
        Tạo
      </span>
    </div>
  </modal>
</template>
<script>
import RepositoryFactory from "../../../repository/repositoryFactory";
const communityGroupRepository = RepositoryFactory.get("communityGroup");

export default {
  components: {},
  props: {
    currentModal: Boolean,
    groupName: String,
    groupId: Number,
  },
  methods: {
    closeModal() {
      this.$emit("update-chat-model", !this.currentModal);
    },
    async createGroupChat() {
      let response = await communityGroupRepository.createGroupChat({ id: this.groupId, name: this.groupName });
      this.closeModal();
    },
  },
};
</script>
