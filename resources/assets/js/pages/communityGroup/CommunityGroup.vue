<template>
  <div>
    <div
        class="exam__results--screen filterable-list__screen"
        id="group__screen"
    >
      <div class="h4 mt-5">
        <PERSON><PERSON><PERSON><PERSON> lý nhóm cộng đồng
        <small
            @click="goToReport()"
            style="cursor: pointer"
        ><i class="text-danger"
        >( <PERSON><PERSON> {{ reports }} nội dung cần kiểm duyệ<PERSON>)</i
        ></small
        >
      </div>
      <div
          class="filterable-list__filter exam__results--filter flex-column"
          style="align-items: flex-start"
      >
        <div
            class="grid grid-cols-8 gap-2 justify-between groups-filter flex-wrap"
        >
          <div class="form-group w-full flex items-center gap-3">
            <label class="flex items-center gap-2"
            >Nhóm mẫu
              <input
                  class="form-check-input mt-0"
                  type="checkbox"
                  v-model="dataQuery.isSample"
                  @change="loadItems"
                  id="flexCheckDefault"
              /></label>
            <label class="flex items-center gap-2"
            ><PERSON><PERSON><PERSON> hế<PERSON> hạn
              <input
                class="form-check-input mt-0"
                type="checkbox"
                v-model="dataQuery.expiring"
                @change="loadItems"
                id="flexCheckDefault"
            /></label>
            <label class="flex items-center gap-2"
              >Đã kết thúc
              <input
                class="form-check-input mt-0"
                type="checkbox"
                v-model="dataQuery.ended"
                @change="loadItems"
                id="flexCheckDefault"
            /></label>
          </div>
          <div class="form-group w-full">
            <select
                class="form-control mr-5 w-full"
                v-model="dataQuery.vipLevel"
            >
              <option value="">Cấp độ</option>
              <option
                  v-for="level in vipLevelList"
                  :key="level.value"
                  :value="level.value"
              >
                {{ level.title }}
              </option>
            </select>
          </div>
          <div class="form-group w-full">
            <el-select v-model="dataQuery.type" placeholder="Loại" multiple>
              <el-option
                  v-for="type in typeList"
                  :key="type.value"
                  :label="type.title"
                  :value="type.value">
              </el-option>
            </el-select>
          </div>
          <v-select
              v-model="dataQuery.teacher"
              :options="teacherList"
              label="fullName"
              :reduce="(teacherList) => teacherList.id"
              :value="teacherList.id"
              placeholder="Giáo viên"
              class="teacher-dropdown w-full"
          >
          </v-select>
          <select
              class="form-control mr-5 w-full"
              v-model="dataQuery.start_date_order"
          >
            <option value="">Sắp xếp</option>
            <option value="desc">Ngày khai giảng (mới nhất)</option>
            <option value="asc">Ngày khai giảng (cũ nhất)</option>
          </select>
          <el-select
              :value="dataQuery.shift_type ? dataQuery.shift_type.split('').map((o) => parseInt(o)) : []"
              placeholder="Lọc theo ngày học"
              multiple
              @change="changeGroupShift"
              class="w-full"
              ref="shiftTypeSelect"
          >
            <el-option
                v-for="item in weekDayList"
                :key="`day-${item.value}`"
                :value="item.value"
                :label="item.label"
            ></el-option>
          </el-select>
          <select class="form-control mr-5 w-full" v-model="dataQuery.status">
            <option value="">Trạng thái</option>
            <option :value="1">Đang tuyển</option>
            <option :value="2">Đang học</option>
            <option :value="3">KT (Có zoom)</option>
            <option :value="4">KT (Ko zoom)</option>
          </select>
          <select class="form-control mr-5 w-full" v-model="dataQuery.slot">
            <option value="">Slot</option>
            <option value=">">Còn chỗ</option>
            <option value="<=">Đã đầy</option>
          </select>
          <VueCtkDateTimePicker
              id="shiftTimeFromPicker"
              v-model="dataQuery.shift_time"
              :label="shiftTimeStart.label"
              :minute-interval="5"
              :only-time="true"
              :format="shiftTimeStart.format"
              :formatted="shiftTimeStart.formatted"
              style="width: 100%"
          />
          <VueCtkDateTimePicker
              id="startDateFromPicker"
              v-model="dataQuery.start_date_from"
              :label="startDate.label"
              :only-date="startDate.onlyDate"
              :format="startDate.format"
              :formatted="startDate.formatted"
              style="width: 100%"
          />
          <VueCtkDateTimePicker
              id="startDateToPicker"
              v-model="dataQuery.start_date_to"
              :label="startDate.label"
              :only-date="startDate.onlyDate"
              :format="startDate.format"
              :formatted="startDate.formatted"
              style="width: 100%"
          />
          <div class="form-group w-full">
            <input
                type="text"
                class="form-control w-full"
                placeholder="Nhập từ khoá"
                v-model="dataQuery.name"
                style="height: 42px"
            />
          </div>
          <div class="form-group w-full">
            <el-date-picker
                type="months"
                v-model="dataQuery.month"
                format="yyyy/MM"
                value-format="yyyy-MM"
                placeholder="Chọn tháng cần tính số buổi trong xuất excel">
            </el-date-picker>
          </div>
        </div>
        <div class="flex items-center mt-5">
          <span class="btn btn-info" @click="loadItems()">Tìm kiếm</span>
          <div class="ml-5">
            <span class="btn btn-primary" @click="openCreateModal"
            >Tạo mới</span
            >
          </div>
          <div class="btn btn-info ml-5" @click="downloadExcel()">Tải excel</div>
        </div>
      </div>

      <div
          style="
          display: flex;
          flex-flow: row;
          justify-content: space-between;
          align-items: center;
        "
      >
        <span
        >Tìm thấy <b>{{ paginate.total }}</b> kết quả</span
        >
      </div>
      <div class="exam__results--list">
        <table>
          <thead>
            <th width="1%" class="text-right">ID</th>
            <th width="20%">Tên nhóm</th>
            <th class="text-center" width="7%">Loại</th>
            <th class="text-center" width="7%">Ca học</th>
            <th class="text-center">Cấp độ</th>
            <th class="text-center">Giá</th>
            <th width="7%">Ngày khai giảng</th>
            <th width="7%" v-if="!dataQuery.isSample">Ngày kết thúc</th>
            <th width="7%" v-if="!dataQuery.isSample">Ngày hết hạn</th>
            <th width="10%" v-if="dataQuery.isSample">Số ngày hoạt động</th>
            <th width="8%">Trạng thái</th>
            <th width="7%" class="text-right">Thành viên</th>
            <th width="7%" class="text-right"><span>Chưa duyệt</span></th>
            <th width="7%" class="text-center"><span>Thẻ</span></th>
            <th class="text-center">Group chat</th>
            <th width="7%" class="text-right"><span>Ghi chú</span></th>
            <th width="10%" class="text-center">Tuỳ chọn</th>
          </thead>
          <tbody v-if="!loading">
          <tr v-if="paginate.items.length == 0">
            <td colspan="12" class="text-center">Không có dữ liệu</td>
          </tr>
          <template v-else>
            <tr v-for="group in groupList" :key="'gr-' + group.id">
              <td>
                <div class="text-right">{{ group.id }}</div>
              </td>
              <td>
                <div style="display: flex">
                  <div style="width: 90%">
                    <div>
                      <div @click="goToUser(group.id)">
                        <div class="text-info a_cursor--pointer">
                          {{ group.name }}
                        </div>
                      </div>
                    </div>
                    <div
                        style="color: black; font-weight: 400"
                        v-if="
                          group.group_teacher && group.group_teacher.teacher
                        "
                    >
                      {{
                        `${group.group_teacher.teacher.last_name} ${group.group_teacher.teacher.first_name}` ||
                        "--"
                      }}
                    </div>
                  </div>
                  <div
                      style="
                        width: 10%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                      "
                  >
                    <a
                        :href="'../../groups/' + group.id + '-view'"
                        target="_blank"
                    >
                      <i
                          class="fa fa-arrow-circle-o-right"
                          aria-hidden="true"
                          style="font-size: 25px; color: orange; padding: 0 5px"
                      ></i>
                    </a>
                  </div>
                </div>
              </td>
              <td class="text-center">
                <div
                    class="label label-primary text-bold text-center"
                    style="font-size: 13px"
                >
                  {{ group.type }}
                </div>
                <div> {{ group.type_note }}</div>
              </td>
              <td class="text-center">
                <div
                    v-if="group.shift_type"
                    class="label label-success text-bold text-center"
                    style="font-size: 13px"
                >
                  {{ group.shift_type }}
                </div>
                <div class="text-bold text-center" style="font-size: 13px">
                  {{ group.shift_time }}
                </div>
              </td>
              <td class="text-center">
                <div
                    class="text-center label label-warning text-bold text-center"
                    style="font-size: 13px"
                    v-if="group.vip_level"
                  >
                    {{ group.vip_level }}
                  </div>
                </td>
                <td><b>{{ group.vip_combo?.price || '' }}</b></td>
                <td>
                  <div>{{ getReadableDate(group.start_date) }}</div>
                </td>
                <td v-if="!dataQuery.isSample">
                  <div>{{ group.end_at }}</div>
                </td>
                <td v-if="!dataQuery.isSample">
                  <div>{{ group.expired_at }}</div>
                </td>
                <td v-if="dataQuery.isSample">
                  <div>{{ group.expired_after_days }}</div>
                </td>
                <td>
                  <div class="flex flex-column justify-center">
                    <select
                      name=""
                      id=""
                      class="form-control"
                      :value="group.status"
                      @change="changeStatus(group.id, $event)"
                      style="
                        padding: 5px;
                        height: 30px;
                        border-radius: 20px;
                        text-align: center;
                      "
                  >
                    <option :value="1">Đang tuyển</option>
                    <option :value="2">Đang học</option>
                    <option :value="3">KT (Có zoom)</option>
                    <option :value="4">KT (Ko zoom)</option>
                  </select>
                  <div
                      class="badge mt-3"
                      :class="[
                        group.userCount < group.size
                          ? 'badge-success'
                          : 'badge-red',
                      ]"
                  >
                    {{ group.userCount < group.size ? "Còn chỗ" : "Đã đầy" }}
                  </div>
                  <div
                      v-if="group.stage === 2"
                      class="badge-warning badge mt-3"
                  >
                    Chặng Nghe-Đọc-Hiểu
                  </div>
                </div>
              </td>
              <td>
                <div
                    @click="goToUser(group.id)"
                    class="a_cursor--pointer text-bold text-right"
                    :class="
                      group.userCount < group.size ? 'text-success' : 'text-red'
                    "
                >
                  {{ group.userCount }} /
                  {{ group.size }}
                </div>
              </td>
              <td>
                <div
                    @click="goToPost(group.id, 0)"
                    class="a_cursor--pointer text-danger text-bold text-right"
                >
                  {{ group.unconfirm_posts }}
                </div>
              </td>
              <td>
                <div
                    @click="goToTag(group.id)"
                    class="a_cursor--pointer text-info text-bold text-center"
                >
                  Xem
                </div>
              </td>
              <td>
                <template v-if="group.group_chat_id">
                  <div class="text-info text-bold text-center">Đã tạo</div>
                  <div
                      @click="exportGroupChatMembers(group.id)"
                      class="a_cursor--pointer text-info text-bold text-center"
                  >
                    Tải danh sách
                  </div>
                  <div
                      style="cursor: pointer"
                      title="Copy to link chat"
                      @click="copyLinkChat(group.group_chat_id)"
                  >
                    <i class="el-icon-document-copy"></i>
                  </div>
                </template>

                <div
                    v-else
                    class="btn btn-info"
                    @click="createGroupChat(group)"
                >
                  +
                </div>
              </td>
              <td>
                <el-tooltip class="item" effect="dark" :content="group.note" placement="top-start">
                  <div
                      class="w-30 a_cursor--pointer text-red-500 text-bold text-right overflow-hidden"
                  >
                    {{ group.note }}
                  </div>
                </el-tooltip>
              </td>
              <td class="text-center">
                <el-dropdown trigger="click">
                  <el-button
                      icon="el-icon-more"
                      class="w-10 h-10 flex justify-center p-3"
                    ></el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item class="flex items-center" icon="el-icon-edit-outline">
                        <span class="w-full h-full inline-block" @click="editGroup(group)">Sửa nhóm</span>
                      </el-dropdown-item>
                      <el-dropdown-item class="flex items-center" icon="el-icon-time">
                        <span class="w-full h-full inline-block" @click="createSchedule(group.id)"
                          >Tạo lịch học</span
                        >
                      </el-dropdown-item>
                      <el-dropdown-item class="flex items-center" icon="el-icon-time">
                        <span class="w-full h-full inline-block" @click="viewSchedule(group.id)"
                          >Xem lịch học</span
                        >
                      </el-dropdown-item>
                      <el-dropdown-item class="flex items-center" icon="el-icon-notebook-2">
                        <a
                          class="w-full h-full"
                          title="Đơn hàng"
                          :href="`${url}/backend/vip/vip-invoice?groupId=${group.id}&status=completed&per_page=20`"
                          target="_blank"
                          >Đơn hàng</a
                        >
                      </el-dropdown-item>
                      <el-dropdown-item class="flex items-center" icon="el-icon-time">
                        <span class="w-full h-full inline-block" @click="changeStageGroup(group.id)"
                        >Đánh dấu chặng Nghe - Đọc - Hiểu</span
                        >
                      </el-dropdown-item>
                      <el-dropdown-item class="flex items-center" icon="el-icon-delete">
                        <span
                          class="text-red-500 w-full h-full inline-block"
                          @click="deleteGroup(group.id)"
                          >Xoá nhóm</span
                        >
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </td>
            </tr>
          </template>
          </tbody>
          <tbody v-if="loading">
          <tr style="height: 60vh; padding: 20px auto; text-align: center">
            <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="exam__results--screen" id="group__screen">
        <div class="exam__results--paginate">
          <div>
            Hiển thị
            <select v-model="paginate.per_page" @change="loadItems()">
              <option v-for="(option, index) in perPageOption" :key="index">
                {{ option }}
              </option>
            </select>
            trong số {{ paginate.total }} kết quả
          </div>
          <paginate
              :page-count="paginate.last_page"
              :page-range="4"
              :margin-pages="3"
              :click-handler="changePage"
              :prev-text="'&laquo;'"
              :next-text="'&raquo;'"
              :container-class="'pagination'"
              :page-class="'page-item'"
          >
          </paginate>
        </div>
      </div>
      <CreateGroup
          v-if="createModal"
          :currentModal="createModal"
          :teacherList="teacherList"
          @update-create-model="updateCreateModal"
      />
      <EditGroup
          v-if="editModal"
          :currentModal="editModal"
          :group="group"
          :teacherList="teacherList"
          @update-edit-model="updateEditModal"
      />
      <DeleteGroup
          :currentModal="deleteModal"
          :groupId="groupId"
          @update-delete-model="updateDeleteModal"
      />
      <CreateChat
          :currentModal="createChatModel"
          :groupId="groupId"
          :groupName="groupName"
          @update-chat-model="updateChatModel"
      />
    </div>
  </div>
</template>
<script>
import Vue from "vue";
import moment from "moment";
import axios from "axios";
import Pusher from "pusher-js";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import VueCtkDateTimePicker from "vue-ctk-date-time-picker";
import "vue-ctk-date-time-picker/dist/vue-ctk-date-time-picker.css";
// Repository
import RepositoryFactory from "../../repository/repositoryFactory";

const communityGroupRepository = RepositoryFactory.get("communityGroup");
const communityReportRepository = RepositoryFactory.get("communityReport");

// Part
import CreateGroup from "./part/Create";
import EditGroup from "./part/Edit";
import DeleteGroup from "./part/Delete";
import CreateChat from "./part/CreateChat";
// Pagination
import Paginate from "vuejs-paginate";
import VueUploadMultipleImage from "vue-upload-multiple-image";

Vue.component("paginate", Paginate);
Vue.component("modal", {
  template: "#modal-template",
  props: {
    bgColor: {
      type: String,
      default: function () {
        return "background-color: #fff;";
      },
    },
    shadow: {
      type: String,
      default: function () {
        return "box-shadow: 0 2px 8px rgba(0, 0, 0, .33);";
      },
    },
  },
  data() {
    return {
      containerStyle: "" + this.bgColor + this.shadow,
    };
  },
});
export default {
  name: "CommunityGroup",
  components: {
    VueCtkDateTimePicker,
    CreateGroup,
    EditGroup,
    DeleteGroup,
    CreateChat,
    "v-select": vSelect,
  },
  data() {
    return {
      url: window.location.origin,
      loading: false,
      paginate: {
        items: [],
        from: 1,
        to: 1,
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0,
      },
      perPageOption: [5, 10, 15, 20, 50, 100],
      dataQuery: {
        perPage: 10,
        page: 1,
        isSample: false,
        name: null,
        vipLevel: "",
        shift_type: "",
        status: "",
        slot: "",
        start_date_order: "",
        start_date_from: "",
        start_date_to: "",
        shift_time: "",
        teacher: "",
        type: [],
        expiring: false,
        ended: false,
        month: '',
      },
      vipLevelList: [
        {value: "N1", title: "N1"},
        {value: "N2", title: "N2"},
        {value: "N3", title: "N3"},
        {value: "N4", title: "N4"},
        {value: "N5", title: "N5"},
        {value: "kaiwacb", title: "Kaiwa cơ bản"},
        {value: "kaiwasc", title: "Kaiwa sơ cấp"},
        {value: "kaiwatc", title: "Kaiwa trung cấp 1"},
        {value: "kaiwatc2", title: "Kaiwa trung cấp 2"},
        {value: "kaiwanc", title: "Kaiwa nâng cao"},
        {value: "ldn1", title: "Luyện đề N1"},
        {value: "ldn2", title: "Luyện đề N2"},
        {value: "ldn3", title: "Luyện đề N3"},
        {value: "ldn4", title: "Luyện đề N4"},
        {value: "ldn5", title: "Luyện đề N5"},
        {value: "tokutei1", title: "Tokutei 1"},
        // {value: "tokutei2", title: "Tokutei 2"},
        {value: "tokutei2", title: "Tokutei 2"},
      ],
      reports: 0,
      createModal: false,
      editModal: false,
      deleteModal: false,
      createChatModel: false,
      group: {
        name: null,
        expired_at: null,
      },
      groupId: null,
      groupName: "",
      teacherList: [],
      startDate: {
        label: "Chọn ngày khai giảng",
        onlyDate: true,
        format: "YYYY-MM-DD",
        formatted: "YYYY-MM-DD",
      },
      shiftTimeStart: {
        label: "Ca học",
        onlyDate: false,
        format: "HH:mm:00",
        formatted: "HH:mm:00",
      },
      reservations: [],
      typeList: [
        {value: "basic", title: "Basic"},
        {value: "vip500", title: "Vip500"},
        {value: "vip15", title: "Vip15"},
        {value: "vip9", title: "Vip9"},
        {value: "vip1", title: "Vip1"},
        {value: "luyende", title: "Luyện đề"},
        {value: "online", title: "Online"},
        {value: "offline", title: "Offline"},
        {value: "captoc", title: "Cấp tốc"},
        {value: "matgoc", title: "Mất gốc"},
        {value: "kaiwa", title: "Kaiwa"},
        {value: "b2b", title: "Doanh nghiệp"},
        {value: "tokutei", title: "Tokutei"},
      ],
      weekDayList: [
        {value: 2, label: "Thứ 2"},
        {value: 3, label: "Thứ 3"},
        {value: 4, label: "Thứ 4"},
        {value: 5, label: "Thứ 5"},
        {value: 6, label: "Thứ 6"},
        {value: 7, label: "Thứ 7"},
        {value: 8, label: "Chủ nhật"},
      ],
    };
  },
  computed: {
    groupList() {
      return this.paginate.items.map((item) => {
        const idx = this.reservations.findIndex((r) => r.id === item.id);
        item.userCount =
            idx === -1 ? item.total_users : this.reservations[idx].count;
        return item;
      });
    },
  },
  created() {
    this.subscribe();
  },
  mounted() {
    this.getList();
    this.countReport();
    this.getTeacherList();
  },
  methods: {
    changeGroupShift(value) {
      this.dataQuery.shift_type = value.sort().join("");
    },
    getReadableDate(date) {
      return date ? moment(date, "YYYY-MM-DD").format("DD-MM-YYYY") : "-";
    },
    async getList() {
      try {
        // this.loading = true
        let response = await communityGroupRepository.getList(this.dataQuery);

        const data = response.data.data;

        this.paginate.items = data.items;
        this.paginate.from = data.from;
        this.paginate.to = data.to;
        this.paginate.current_page = data.current_page;
        this.paginate.last_page = data.last_page;
        this.paginate.total = data.total;
        this.paginate.from = 100;
        // this.loading = false
      } catch (e) {
        // this.$bus.$emit('show-loading', false)
      }
    },
    async getTeacherList() {
      let response = await communityGroupRepository.getTeacherList();
      this.teacherList = response.data.map(function (teacher) {
        teacher.fullName = teacher.last_name + " " + teacher.first_name;
        return teacher;
      });
    },
    async countReport() {
      try {
        let response = await communityReportRepository.count({});
        const data = response.data.data;
        this.reports = data.total;
      } catch (error) {
      }
    },
    goToTag(groupId) {
      const url = `${window.location.origin}/backend/community/tags?group_id=${groupId}`;
      window.open(url, "_self");
    },
    goToUser(groupId) {
      const url = `${window.location.origin}/backend/community/users?group_id=${groupId}`;
      window.open(url, "_self");
    },
    goToPost(groupId, verify) {
      const url = `${window.location.origin}/backend/community/posts?group_id=${groupId}&is_verify=${verify}`;
      window.open(url, "_self");
    },
    goToReport() {
      const url = `${window.location.origin}/backend/community/reports`;
      window.open(url, "_self");
    },
    loadItems() {
      this.dataQuery.perPage = this.paginate.per_page;
      this.getList();
    },
    async downloadExcel() {
      let currentUrl = (new URL(window.location.origin + `/backend/community/group/export-excel`));

      let searchParams = currentUrl.searchParams;

      Object.keys(this.dataQuery).forEach(key => {
        searchParams.append(key, this.dataQuery[key])
      })

      currentUrl.search = searchParams.toString();

      window.open(currentUrl, '_blank');
    },
    changePage: function (pageNum) {
      this.dataQuery.page = pageNum;
      this.getList();
    },
    openCreateModal() {
      this.createModal = true;
    },
    updateCreateModal: function (e) {
      this.getList();
      this.createModal = e;
    },
    updateChatModel(e) {
      this.getList();
      this.createChatModel = e;
    },
    editGroup(group) {
      console.log('group', group)
      if (!group.links) {
        group.links = {
          groupLink: {
            url: "",
            status: false,
            note: "",
          },
          doc: {
            url: "",
            status: false,
            note: "",
          },
          messenger: {
            url: "",
            status: false,
            note: "",
          },
          linkZoom: {
            url: "",
            status: false,
            note: "",
          },
        };
      }
      this.group = group;
      console.log("this.group......", this.group)
      // Object.keys(this.group).map((obj, i) => {
      //   console.log("obj", obj)
      //   console.log("i", i)
      //   console.log('value', this.group[obj])
      // })
      this.editModal = true;
    },
    updateEditModal(e) {
      this.getList();
      this.editModal = e;
    },
    deleteGroup(id) {
      this.deleteModal = true;
      this.groupId = id;
    },
    createGroupChat(group) {
      this.createChatModel = true;
      this.groupId = group.id;
      this.groupName = group.name;
    },
    updateDeleteModal(e) {
      this.getList();
      this.deleteModal = e;
    },
    async changeStatus(id, event) {
      const status = event.target.value;
      try {
        // this.loading = true
        let response = await communityGroupRepository.updateStatus(id, {
          status: status,
        });
      } catch (e) {
        // this.$bus.$emit('show-loading', false)
      }
    },
    async exportGroupChatMembers(groupId) {
      var url =
          window.location.origin +
          `/backend/community/group/${groupId}/export-group-chat-member` +
          window.location.search;
      window.open(url);
    },
    copyLinkChat(group_chat_id) {
      let currentUrl = (new URL(window.location.origin));

      let searchParams = currentUrl.searchParams;

      searchParams.append('chat', group_chat_id)

      currentUrl.search = searchParams.toString();

      navigator.clipboard.writeText(currentUrl.href).then(() => {
        this.$message.success('Copy thành công!');
      }).catch(err => {
        this.$message.error('Sao chép thất bại.');
      });
    },
    findName: function (id) {
      var teacher = this.teacherList.find(function (param) {
        return param.id == id;
      });

      if (teacher) {
        return `${teacher.last_name} ${teacher.first_name}`;
      }
    },
    subscribe() {
      const vm = this;
      let pusher = new Pusher("7d39d4954f600d3bb86c", {cluster: "ap3"});
      pusher.subscribe("invoice");
      pusher.bind("reservation", (data) => {
        const idx = vm.reservations.findIndex((r) => r.id === data.id);
        if (idx === -1) {
          vm.reservations.push(data);
        } else {
          vm.$set(vm.reservations, idx, data);
        }
      });
    },
    async createSchedule(groupId) {
      console.log("groupId...", groupId)
      const res = await axios.post(
          window.location.origin + "/backend/community/group/generate-schedule",
          {id: groupId}
      );
      this.$message({
        type: "success",
        message: "Thêm lịch học thành công",
      });
    },
    viewSchedule(groupId) {
      console.log("g");
      const url = `${window.location.origin}/backend/community/schedule?group_id=${groupId}`;
      window.open(url, "_blank");
    },
    async changeStageGroup(group_id) {
      this.$confirm('Bạn có muốn thay đổi trạng thái nghe đọc hiểu của lớp này chứ?', 'Warning', {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(async () => {
        let res = await communityGroupRepository.updateStage(group_id)
        console.log("res............", res)
        this.$message({
          type: 'success',
          message: 'Thay đổi thành công'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: 'Thay đổi không thành công'
        });
      });
    }
  },
};
</script>
<style>
.teacher-dropdown {
  min-width: 100px;
  margin-right: 10px;
}

.teacher-dropdown .vs__dropdown-toggle {
  min-height: 38px;
}

.teacher-dropdown .vs__dropdown-menu {
  width: 300px;
}

.groups-filter input,
select {
  border-radius: 5px !important;
}
</style>
