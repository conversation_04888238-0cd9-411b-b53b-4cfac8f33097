<template>
  <div class="relative z-[100]">
    <!-- Background Overlay -->
    <div
        class="fixed inset-0 transition-opacity"
        v-if="isOpenPopupCongratulate"
        @click="isOpenPopupCongratulate = false"
    ></div>

    <!-- Popup -->
    <div
        class="fixed bottom-0 inset-x-0 rounded-t-lg transform transition-transform duration-300 px-[20px] py-[170px] md:p-[60px] text-center"
        :class="{ 'translate-y-0': isOpenPopupCongratulate, 'translate-y-full': !isOpenPopupCongratulate }"
    >
      <div
          class="px-[44px] py-[24px] bg-[#F0FFF1] max-w-[1096px] flex items-center justify-between mx-auto rounded-[24px] mb-[10px]"
          style="box-shadow: 0 0 24px 0 rgba(7, 64, 63, 0.4);"
      >
        <div class="svg-icon">
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="20" cy="20" r="20" fill="#57D061"/>
            <path d="M27.6192 14.2861L17.143 24.7623L12.3811 20.0004" stroke="#EBFFEE" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="text-content">
          <div class="text-[#57D061] text-[20px] font-beanbag leading-[16px] mb-[14px]">Kích hoạt thành công 🎊</div>
          <div class="text-[#07403F] text-[20px] font-averta-regular font-[600] leading-[16px]">Khóa N5-N4 sơ cấp mới!</div>
        </div>
        <a
            class="text-[#57D061] font-beanbag text-[20px] hover:underline underline hover:text-[#57D061]"
            href="/khoa-hoc/so-cap-n5"
            @click="isOpenPopupCongratulate = false"
        >
          Xem ngay
        </a>
      </div>
      <div
          class="px-[44px] py-[24px] bg-[#F0FFF1] max-w-[1096px] flex items-center justify-between mx-auto rounded-[24px] mb-[10px]"
          style="box-shadow: 0 0 24px 0 rgba(7, 64, 63, 0.4);"
      >
        <div class="svg-icon">
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="20" cy="20" r="20" fill="#57D061"/>
            <path d="M27.6192 14.2861L17.143 24.7623L12.3811 20.0004" stroke="#EBFFEE" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="text-content">
          <div class="text-[#57D061] text-[20px] font-beanbag leading-[16px] mb-[14px]">Kích hoạt thành công 🎊</div>
          <div class="text-[#07403F] text-[20px] font-averta-regular font-[600] leading-[16px]">Khóa N5-N4 sơ cấp mới!</div>
        </div>
        <a
            class="text-[#57D061] font-beanbag text-[20px] hover:underline underline hover:text-[#57D061]"
            href="/khoa-hoc/so-cap-n5"
            @click="isOpenPopupCongratulate = false"
        >
          Xem ngay
        </a>
      </div>
      <div
          class="px-[44px] py-[24px] bg-[#F0FFF1] max-w-[1096px] flex items-center justify-between mx-auto rounded-[24px] mb-[10px]"
          style="box-shadow: 0 0 24px 0 rgba(7, 64, 63, 0.4);"
      >
        <div class="svg-icon">
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="20" cy="20" r="20" fill="#57D061"/>
            <path d="M27.6192 14.2861L17.143 24.7623L12.3811 20.0004" stroke="#EBFFEE" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="text-content">
          <div class="text-[#57D061] text-[20px] font-beanbag leading-[16px] mb-[14px]">Kích hoạt thành công 🎊</div>
          <div class="text-[#07403F] text-[20px] font-averta-regular font-[600] leading-[16px]">Khóa N5-N4 sơ cấp mới!</div>
        </div>
        <a
            class="text-[#57D061] font-beanbag text-[20px] hover:underline underline hover:text-[#57D061]"
            href="/khoa-hoc/so-cap-n5"
            @click="isOpenPopupCongratulate = false"
        >
          Xem ngay
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ActiveCourse",
  data() {
    return {
      isOpenPopupCongratulate: true
    }
  },
  created() {
    console.log(`created active course`)
  }
}
</script>
