<template>
  <div>
    <div class="search-box mb-5">
      <el-select
          v-model="user"
          filterable
          remote
          reserve-keyword
          placeholder="Please enter a keyword"
          :remote-method="querySearch"
          :loading="loading"
          @change="handleSelect"
          value-key="id"
          clearable
      >
        <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item">
        </el-option>
      </el-select>
    </div>
    <div class="mb-5 p-10" style="background: white; border-radius: 5px; border: 1px solid rgb(226 228 226);">
      <el-descriptions title="User Info">
        <el-descriptions-item label="Tên">{{ user.name ?? '' }}</el-descriptions-item>
        <el-descriptions-item label="ID">{{ user.id ?? '' }}</el-descriptions-item>
        <el-descriptions-item label="Email">{{ user.email ?? '' }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="table-data mb-10">
      <el-table
          v-loading="loadingTable"
          element-loading-text="Loading..."
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
          :data="tableData"
          stripe
          border
          ref="multipleTable"
          @selection-change="handleSelectionChange"
          style="width: 100%">
        <el-table-column
            header-align="center"
            type="selection"
            align="center"
            width="55">
        </el-table-column>
        <el-table-column
            header-align="center"
            class="center"
            prop="id"
            label="ID Khóa học">
        </el-table-column>
        <el-table-column
            header-align="center"
            prop="name"
            label="Tên">
        </el-table-column>
        <el-table-column
            header-align="center"
            prop="address"
            label="Tiến trình" >
            <template slot-scope="{row}">
              <el-progress :percentage="row.process"></el-progress>
            </template>
        </el-table-column>
        <el-table-column
            header-align="center"
            prop="action"
            label="Hành động"
            class="tesetste"
            >
            <template slot-scope="{row}" class="center" align="center" style="display: grid; justify-content: center !important; align-items: center !important;">
              <el-button type="text" icon="el-icon-delete" @click="confirmDelete(row.id)"></el-button>
              <el-button type="text" icon="el-icon-alarm-clock" @click="openResetTime(row.id)"></el-button>
            </template>
        </el-table-column>
      </el-table>
    </div>
    <div>
      <el-button icon="el-icon-delete" @click="confirmDelete(0 ,true)">Xóa nhiều</el-button>
    </div>
    <el-dialog title="Chọn khoảng thời gian cần xóa" :visible.sync="showResetTime">
      <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="-"
          start-placeholder="Ngày bắt đầu"
          end-placeholder="Ngày kết thúc"
          format="dd/MM/yyyy"
          value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showResetTime = false; courseIdToDelete = null">Hủy</el-button>
        <el-button type="primary" @click="confirmResetTime(courseIdToDelete)">Xóa</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import RepositoryFactory from '../../repository/repositoryFactory'
const userReportRepository = RepositoryFactory.get('communityUser')

export default {
  name: "LessonProcess",
  props: {
    id: Number
  },
  data() {
    return {
      loading: false,
      loadingTable: false,
      tableData: [],
      user: '',
      options: [],
      multipleSelection: [],
      courseIdToDelete: null,
      showResetTime: false,
      dateRange: [],
    }
  },
  methods: {
    async querySearch(keyword) {
      this.loading = true
      let res = await userReportRepository.search({keyword})
      this.options = res.data.data
      this.loading = false
    },
    async getList(id) {
      this.loadingTable = true
      if(id) {
        let res = await userReportRepository.getLesson({user_id: id});
        if (res.status == 200) {
          this.tableData = res.data.data
        }
      }
      this.loadingTable = false
    },
    async handleSelect(item) {
      if (this.user.id) {
        await this.getList(this.user.id)
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    openResetTime(id) {
      this.courseIdToDelete = id;
      this.showResetTime = true;
    },
    async confirmResetTime(id, is_multi = false) {
        let vm = this
        let data = {
          user_id: vm.user.id,
          lesson_progress_ids: [],
          course_ids: [],
          from: vm.dateRange.length ? vm.dateRange[0] : null,
          to: vm.dateRange.length ? vm.dateRange[1] : null,
        }
        data.course_ids.push(id)

        let res = await userReportRepository.deleteLessonTime(data)
        this.$message({
          type: 'success',
          message: 'Delete completed'
        });
        this.showResetTime = false;
        vm.getList(vm.user.id)
    },
    async confirmDelete(id, is_multi = false) {
      let vm = this
        this.$confirm('Bạn có muốn xóa tiến trình học đã chọn này không?', 'Cảnh báo', {
          confirmButtonText: 'OK',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then( async () => {
          let data = {
            user_id: vm.user.id,
            lesson_progress_ids: [],
            course_ids: []
          }

          if (is_multi) {
            vm.multipleSelection.forEach((item) => {
              data.course_ids.push(item.id)
              item.available_groups.forEach((item) => {
                item.lesson_default.forEach((item) => {
                  if (item.progress.length > 0) {
                    data.lesson_progress_ids.push(item.progress[0].id)
                  }
                })
              })
            })
          } else {
            let course = vm.tableData.find((item) => {return item.id == id})
            data.course_ids.push(course.id)
            if (course) {
              course.available_groups.forEach((item) => {
                item.lesson_default.forEach((item) => {
                  if (item.progress.length > 0) {
                    data.lesson_progress_ids.push(item.progress[0].id)
                  }
                })
              })
            }
          }
          let res = await userReportRepository.deleteLesson(data)
          this.$message({
            type: 'success',
            message: 'Delete completed'
          });
          vm.getList(vm.user.id)
        }).catch(err => {
          this.$message({
            type: 'info',
            message: 'Delete canceled'
          });          
        });
    }
  }
}
</script>
<style>
.el-table__cell .cell {
  display: block !important;
}

.cell {
  display: block !important;
}
</style>
