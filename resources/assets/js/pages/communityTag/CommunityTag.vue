<template>
  <div>
    <div class="exam__results--screen" id="group__screen">
      <BackToCommunity />
      <div class="exam__results--filter flex justify-between">
          <strong class="h4">Qu<PERSON>n lý thẻ trong nhóm: <b>{{ group.name }}</b></strong>
          <div class="flex justify-end items-center">
              <strong class="mr-5">Tì<PERSON> kiếm</strong>
              <div class="form-group">
                  <input type="text" class="form-control" placeholder="Nhập từ khoá" v-model="dataQuery.name">
              </div>
              <span class="btn btn-info" @click="loadItems()">Tìm kiếm</span>
              <div class="ml-5">
                <span class="btn btn-primary" @click="openCreateModel">Tạo mới</span>
              </div>
          </div>
      </div>
      <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
          <span>Tìm thấy <b>{{ paginate.total }}</b> kết quả</span>
      </div>
      <div class="exam__results--list">
          <table>
              <thead>
              <th width="1%" class="text-right">ID</th>
              <th width="40%">Tên thẻ</th>
              <th width="10%">Ngày tạo</th>
              <th width="10%" class="text-center">Tuỳ chọn</th>
              </thead>
              <tbody v-if="!loading">
              <tr v-if="paginate.items.length == 0">
                  <td colspan="12" class="text-center"> Không có dữ liệu</td>
              </tr>
               <tr v-else v-for="item in paginate.items" :key="'gr-' + item.id">
                  <td>
                      <div class="text-right">{{ item.id }}</div>
                  </td>
                  <td>
                      <div>{{ item.name }}</div>
                  </td>
                  <td>
                      <div>{{ item.created_at }}</div>
                  </td>
                  <td class="text-center">
                      <span class="btn btn-danger" title="Xoá nhóm" @click="deleteItem(item.id)"><i class="fa fa-trash"></i></span>
                      <span class="btn btn-warning" title="Edit group" @click="editItem(item)"><i class="glyphicon glyphicon-edit"></i></span>
                  </td>
              </tr>
            </tbody>
            <tbody v-if="loading">
              <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                  <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
              </tr>
            </tbody>
        </table>
      </div>
      <div class="exam__results--screen" id="group__screen">
          <div class="exam__results--paginate">
              <div>
                  Hiển thị
                  <select v-model="paginate.per_page" @change="loadItems()">
                      <option v-for="(option, index) in perPageOption" :key="index">{{option}}</option>
                  </select>
                  trong số {{ paginate.total }} kết quả
              </div>
              <paginate
                  :page-count="paginate.last_page"
                  :page-range="4"
                  :margin-pages="3"
                  :click-handler="changePage"
                  :prev-text="'&laquo;'"
                  :next-text="'&raquo;'"
                  :container-class="'pagination'"
                  :page-class="'page-item'"
              >
              </paginate>
          </div>
      </div>
      <CreateItem v-if="CreateModel" :currentModal="CreateModel" :groupId="id" @update-create-model="updateCreateModel"/>
      <EditItem v-if="EditModel" :currentModal="EditModel" :item="item" @update-edit-model="updateEditModel"/>
      <DeleteItem :currentModal="DeleteModel" :itemId="itemId" @update-delete-model="updateDeleteModel"/>
    </div>
  </div>
</template>
<script>
// Repository
import RepositoryFactory from '../../repository/repositoryFactory'
const communityTagRepository = RepositoryFactory.get('communityTag')

import BackToCommunity from './../../component/BackToCommunity'
// Part
import CreateItem from './part/Create'
import EditItem from './part/Edit'
import DeleteItem from './part/Delete'
// Pagination
import Paginate from 'vuejs-paginate'
Vue.component('paginate', Paginate)
Vue.component('modal', {
    template: '#modal-template'
});
export default {
  name: "CommunityTag",
  components: {
    CreateItem,
    EditItem,
    DeleteItem,
    BackToCommunity
  },
  props: {
      id: Number
  },
  data() {
    return {
      loading: false,
      paginate: {
        items: [],
        from: 1,
        to: 1,
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0
      },
      perPageOption : [5, 10, 15, 20, 50, 100],
      dataQuery : {
        perPage: 10,
        page: 1,
        name: null
      },
      item: {
        id: null,
        name: null
      },
      group: {},
      itemId: null,
      CreateModel: false,
      EditModel: false,
      DeleteModel: false,
    }
  },
  async mounted() {
    this.dataQuery.group_id = this.id;
    this.getList()
  },
  methods: {
    async getList() {
        try {
          // this.loading = true
          let response = await communityTagRepository.getList(this.dataQuery)
          const data = response.data.data
          this.paginate.items = data.items
          this.paginate.from = data.from
          this.paginate.to = data.to
          this.paginate.current_page = data.current_page
          this.paginate.last_page = data.last_page
          this.paginate.total = data.total
          this.paginate.from = 100;
          this.group = data.group;
          // this.loading = false
        } catch (e) {
            // this.$bus.$emit('show-loading', false)
        }
    },
    loadItems() {
      this.dataQuery.perPage = this.paginate.per_page
      this.getList()
    },
    changePage: function(pageNum) {
      this.dataQuery.page = pageNum
      this.getList()
    },
    openCreateModel() {
      this.CreateModel = true;
    },
    updateCreateModel: function(e) {
      this.getList();
      this.CreateModel = e;
    },
    editItem(item) {
      this.item = item;
      this.EditModel = true;
    },
    updateEditModel(e) {
      this.getList();
      this.EditModel = e;
    },
    deleteItem(id) {
      this.DeleteModel = true;
      this.itemId = id;
    },
    updateDeleteModel(e) {
      this.getList();
      this.DeleteModel = e;
    }
  }
}
</script>
<style>
</style>
