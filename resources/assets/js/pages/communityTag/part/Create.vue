<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header">Thêm thẻ</h3>
        <div slot="body">
          <form class="">
            <div class="form-group">
                <label>Tên</label>
                <input class="form-control" v-model="item.name" required/>
                <div class="invalid-feedback" v-if="!validate.name"><PERSON><PERSON><PERSON><PERSON> tên thẻ</div>
            </div>
            <div class="invalid-feedback" v-if="!validate.all">Đ<PERSON> có lỗi sảy ra</div>
          </form>
            <span class="btn btn-block btn-success p-5 mt-5" @click="createItem">Tạo</span>
        </div>
    </modal>    
</template>
<script>
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';

import RepositoryFactory from '../../../repository/repositoryFactory'
const communityTagRepository = RepositoryFactory.get('communityTag')

export default {
  components: {
    DatePicker
  },
  props: {
    currentModal: Boolean,
    groupId: Number
  },
  data() {
    return {
      item: {
        name: null
      },
      validate : {
        name: true,
        all : true
      }
    }
  },   
  async mounted() {
    // this.getList()
  },
  methods: {
    closeModal() {
      this.$emit('update-create-model', !this.currentModal);
    },
    async createItem() {
      if(this.item.name === null) {
        this.validate.name = false;
        return;
      }
      this.item.group_id = this.groupId;
      this.resetValide();
      let response = await communityTagRepository.store(this.item);
      if(response.status == 200) {
        this.$emit('update-create-model', !this.currentModal);
        return;
      } else {
        this.validate.all = false;
        return false;
      }
    },
    resetValide() {
      this.validate = {
        name: true,
        all : true
      }
    }
  } 
}
</script>