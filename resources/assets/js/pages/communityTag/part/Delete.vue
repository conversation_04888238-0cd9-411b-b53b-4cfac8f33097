<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header">Xoá thẻ</h3>
        <div slot="body">
            <p><PERSON><PERSON>n c<PERSON> chắc muốn xoá thẻ?</p>
            <div>
              <div class="flex justify-end items-center">
                <button class="btn btn-danger"><span class="glyphicon glyphicon-trash" @click="deleteItem">Xoá</span></button>
                <button class="btn btn-warning ml-5"><span class="glyphicon glyphicon-remove" @click="closeModal">Đóng</span></button>
              </div>
            </div>
        </div>
    </modal>
</template>
<script>
import RepositoryFactory from '../../../repository/repositoryFactory'
const communityTagRepository = RepositoryFactory.get('communityTag')

export default {
  components: {
  },
  props: {
    currentModal: <PERSON>olean,
    itemId: Number,
  },
  data() {
    return {
      validate : {
        name: true,
        expired_at: true,
        all : true
      }
    }
  },
  async mounted() {
    // this.getList()
  },
  methods: {
    closeModal() {
      this.$emit('update-delete-model', !this.currentModal);
    },
    async deleteItem() {
      let response = await communityTagRepository.delete(this.itemId);
      if(response.status == 200) {
        this.$emit('update-delete-model', !this.currentModal);
        return;
      }
    },
  }
}
</script>
