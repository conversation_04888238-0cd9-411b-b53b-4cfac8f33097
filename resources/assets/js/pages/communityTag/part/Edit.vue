<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header">Cập nhật thẻ</h3>
        <div slot="body">
          <form class="">
            <div class="form-group">
                <label>Tên</label>
                <input class="form-control" v-model="item.name" required/>
                <div class="invalid-feedback" v-if="!validate.name"><PERSON><PERSON><PERSON><PERSON> tên thẻ</div>
            </div>
            <div class="invalid-feedback" v-if="!validate.all">Đ<PERSON> có lỗi sảy ra</div>
          </form>
            <span class="btn btn-block btn-success p-5 mt-5" @click="updateGroup">Cập nhật</span>
        </div>
    </modal>    
</template>
<script>

import RepositoryFactory from '../../../repository/repositoryFactory'
const communityTagRepository = RepositoryFactory.get('communityTag')

export default {
  components: {
  },
  props: {
    currentModal: Boolean,
    item: Object
  },
  data() {
    return {
      validate : {
        name: true,
        all : true
      }
    }
  },   
  async mounted() {
    // this.getList()
  },
  methods: {
    closeModal() {
      this.$emit('update-edit-model', !this.currentModal);
    },
    async updateGroup() {
      if(this.item.name === null) {
        this.validate.name = false;
        return;
      }
      this.resetValide();
      let response = await communityTagRepository.update(this.item.id, this.item);
      if(response.status == 200) {
        this.$emit('update-edit-model', !this.currentModal);
        return;
      } else {
        this.validate.all = false;
        return false;
      }
    },
    resetValide() {
      this.validate = {
        name: true,
        all : true
      }
    }
  } 
}
</script>