<template>
  <div>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
      <div slot="body">
        <div id="submit-note-group-user">
          <ul>
            <li v-for="log in logs">
              <b>{{ log.timestamp | dateTimeToMinute }}</b> (<b>{{ log.created_by }}</b>):  <b> {{ subjects[log.subject] }}</b>
              <template v-if="log.subject !== 'invoice_creation'">
                <template v-if="log.subject == 'user_attachment'">
                  Thêm học viên <b>{{ log.to }}</b> ID: {{ log.target }}
                </template>
                <template v-else-if="log.subject == 'user_detachment'">
                  Xoá học viên <b>{{ log.from }}</b> ID: {{ log.target }}
                </template>
                <template v-else-if="log.subject == 'user_transfer'">
                  <PERSON><PERSON><PERSON>n <b>{{ log.target }}</b> từ nhóm <b><a :href="`${url}/backend/community/users?group_id=${log.from}`" target="_blank">{{ log.from }}</a></b> sang
                  <b><a :href="`${url}/backend/community/users?group_id=${log.to}`" target="_blank">{{ log.to }}</a></b>
                </template>
                <template v-else>
                  trường <b>{{ getColumn(log.column) }}</b> từ <b>{{ log.from ? log.from : 'null' }}</b> thành <b>{{ log.to ? log.to : 'null' }}</b>
                </template>
              </template>
            </li>
          </ul>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  props: {
    currentModal: Boolean,
    logs: Array,
  },
  data() {
    return {
      url: window.location.origin,
      subjects() {
        return {
          user_attachment: 'Thêm vào lớp',
          user_detachment: 'Xoá khỏi lớp',
          user_transfer: 'Chuyển lớp',
          information_changed: 'Thay đổi thông tin',
        }
      },
      columns() {
        return {
          invoice: {
            id: 'ID',
            info_contact: {
              name: 'Tên học viên',
              phone: 'Số điện thoại',
              email: 'Email',
              facebook: 'Link facebook',
              chat: 'Link chăm sóc',
            }
          },
          community_group_user: {
            group_id: 'ID nhóm',
            user_id: 'ID học viên'
          },
          course_owner: {
            watch_expired_day: 'Ngày hết hạn'
          }
        };
      }
    }
  },
  methods: {
    closeModal() {
      this.$emit('update-log-modal', !this.currentModal);
    },
    getColumn(path) {
      return _.get(this.columns, path)
    },
  }
}
</script>
