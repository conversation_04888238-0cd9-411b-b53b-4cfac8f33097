<template>
  <div>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
      <div slot="body">
        <div id="submit-note-group-user">
          <textarea :disabled="noteType !== 'note'" cols="30" rows="10" class="form-control" v-model="groupUser[noteType]"></textarea>
          <div v-if="noteType === 'note'" class="note-submit">
            <span class="btn btn-success" @click="saveNote">Lưu</span>
          </div>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
import RepositoryFactory from "../../../repository/repositoryFactory";

const communityUserRepository = RepositoryFactory.get('communityUser')

export default {
  props: {
    currentModal: Boolean,
    groupUser: Object,
    noteType: String,
  },
  data() {
    return {
      internalUG: {},
    };
  },
  methods: {
    async saveNote() {
      var groupUserNote = await communityUserRepository.saveUserNote(this.groupUser);

      if (groupUserNote.status == 200) {
        this.groupUser = groupUserNote.data;
        this.closeModal();
      }
    },
    closeModal() {
      this.$emit('save-note-model', !this.currentModal);
    },
  },
}
</script>

<style>
#submit-note-group-user .note-submit {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

#submit-note-group-user .note-submit span {
  padding: 10px 50px;
  border-radius: 5px;
}
</style>
