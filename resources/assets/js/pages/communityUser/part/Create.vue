<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header">Thêm thành viên</h3>
        <div slot="body">
          <div class="exam__results--screen" id="group__screen">
            <div class="exam__results--filter flex justify-between">
                <strong class="h4"></strong>
                <div class="flex justify-end items-center">
                    <strong class="mr-5">Tìm kiếm</strong>
                    <div class="form-group">
                        <input type="text" class="form-control" placeholder="Nhập từ khoá" v-model="dataQuery.textSearch" v-on:keyup.enter="loadItems()">
                    </div>
                    <span class="btn btn-info" @click="loadItems()">Tìm kiếm</span>
                </div>
            </div>
            <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
                <span>Tìm thấy <b>{{ paginate.total }}</b> kết qu<PERSON></span>
            </div>
            <div class="exam__results--list">
                <table>
                    <thead>
                    <th width="1%" class="text-right">ID</th>
                    <th width="40%">Tên</th>
                    <th width="10%">Username</th>
                    <th width="10%">Email</th>
                    <th width="10%">Phone</th>
                    <th width="10%" class="text-center">Tuỳ chọn</th>
                    </thead>
                    <tbody v-if="!loading">
                    <tr v-if="paginate.items.length == 0">
                        <td colspan="12" class="text-center"> Không có dữ liệu</td>
                    </tr>
                    <tr v-else v-for="(item, index) in paginate.items" :key="index">
                        <td>
                            <div class="text-right">{{ index + 1 }}</div>
                        </td>
                        <td>
                            <div>{{ item.name }}</div>
                        </td>
                        <td>
                            <div>{{ item.username }}</div>
                        </td>
                        <td>
                            <div>{{ item.email }}</div>
                        </td>
                        <td>
                            <div>{{ item.phone }}</div>
                        </td>
                        <td class="text-center">
                            <span class="btn btn btn-success" title="Thêm thành viên" @click="addGroupUser(item.user_id)"><i class="glyphicon glyphicon-plus"></i></span>
                        </td>
                    </tr>
                  </tbody>
                  <tbody v-if="loading">
                    <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                        <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                    </tr>
                  </tbody>
              </table>
            </div>
            <div class="exam__results--screen" id="group__screen">
                <div class="exam__results--paginate">
                    <div>
                        Hiển thị
                        <select v-model="paginate.per_page" @change="loadItems()">
                            <option v-for="(option, index) in perPageOption" :key="index">{{option}}</option>
                        </select>
                        trong số {{ paginate.total }} kết quả
                    </div>
                    <paginate
                        :page-count="paginate.last_page"
                        :page-range="4"
                        :margin-pages="3"
                        :click-handler="changePage"
                        :prev-text="'&laquo;'"
                        :next-text="'&raquo;'"
                        :container-class="'pagination'"
                        :page-class="'page-item'"
                    >
                    </paginate>
                </div>
            </div>
          </div>
        </div>
    </modal>
</template>
<script>
// Repository
import RepositoryFactory from '../../../repository/repositoryFactory';
const communityUserRepository = RepositoryFactory.get('communityUser')
// Pagination
import Paginate from 'vuejs-paginate'
Vue.component('tpaginate', Paginate)

export default {
  components: {
    // DatePicker
  },
  props: {
    currentModal: Boolean,
    groupId: Number
  },
  data() {
    return {
      loading: false,
      paginate: {
        items: [],
        from: 1,
        to: 1,
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0
      },
      perPageOption : [5, 10, 15, 20, 50, 100],
      dataQuery : {
        perPage: 10,
        page: 1,
        textSearch: null
      }
    }
  },
  async mounted() {
    this.dataQuery.group_id = this.groupId;
    this.getList()
  },
  methods: {
    async getList() {
        try {
          this.loading = true
          let response = await communityUserRepository.notInGroup(this.dataQuery)
          const data = response.data.data
          this.paginate.items = data.items
          this.paginate.from = data.from
          this.paginate.to = data.to
          this.paginate.current_page = data.current_page
          this.paginate.last_page = data.last_page
          this.paginate.total = data.total
          this.paginate.from = 100;
          this.loading = false
        } catch (e) {
            // this.$bus.$emit('show-loading', false)
        }
    },
    loadItems() {
      this.dataQuery.perPage = this.paginate.per_page
      this.getList()
    },
    changePage: function(pageNum) {
      this.dataQuery.page = pageNum
      this.getList()
    },
    closeModal() {
      this.$emit('update-create-model', !this.currentModal);
    },
    async addGroupUser(user_id) {
      try {
        await communityUserRepository.addGroupUser({ user_id: user_id, group_id: this.groupId });
        await this.getList();
      } catch (error) {

      }
    }
  }
}
</script>
