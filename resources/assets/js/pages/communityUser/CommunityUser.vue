<template>
  <div>
    <div class="exam__results--screen" id="group__screen">
      <BackToCommunity/>
      <div class="exam__results--filter flex justify-between">
        <div>
          <strong class="h4"><PERSON><PERSON><PERSON><PERSON> lý thành viên trong nhóm: <b>{{ group.name }}</b>
            <i v-if="group.logs" class="fa fa-file-text-o a-cursor-pointer" aria-hidden="true" @click="showLog"></i>
          </strong>
          <div class="group-links" v-if="group.links">
            <div class="btn-group-link" v-for="(link, index) in group.links">
              <a :href="link.url" target="_blank">
                <span v-if="index == 'groupLink'">
                  Link nhóm
                </span>
                <span v-else-if="index == 'doc'">
                  Link tài liệu
                </span>
                <span v-else-if="index == 'messenger'">
                  Link tin nhắn
                </span>
                <span v-else>
                  Link Zoom
                </span>
              </a>
              <i class="fa fa-sticky-note-o" v-if="link.note" @click="linkDetailModal = true"></i>
              <div class="link-status" @click="changeStatusLinks(!link.status, index)">
                <span class="btn-success badge" v-if="link.status">Đã gửi</span>
                <span class="btn-warning badge" v-else>Chưa gửi</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-end items-center">
          <strong class="mr-5">Tìm kiếm</strong>
          <div class="form-group">
            <input type="text" class="form-control" style="width: 150px; max-width: 200px" placeholder="Nhập từ khoá" v-model="dataQuery.textSearch">
          </div>
          <select class="form-control mr-5" style="max-width: 200px" v-model="dataQuery.is_debt">
            <option :value="null">Nợ học phí</option>
            <option value="1">Có</option>
            <option value="0">Không</option>
          </select>
          <select class="form-control mr-5" style="max-width: 200px" v-model="dataQuery.note">
            <option :value="null">Ghi chú trợ giảng</option>
            <option value="<>">Đã ghi chú</option>
            <option value="=">Chưa ghi chú</option>
          </select>
          <span class="btn btn-info" @click="loadItems()">Tìm kiếm</span>
          <div class="ml-5">
            <span v-if="auth.desc !== 'sale'" class="btn btn-primary" @click="openCreateModal">Thêm thành viên</span>
          </div>
          <div class="ml-5"
               v-if="['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(auth.email)">
            <span class="btn btn-success" @click="isOpenAddCourseModal = true">Tặng khoá học</span>
          </div>
        </div>
      </div>
      <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
        <span>Tìm thấy <b>{{ paginate.total }}</b> kết quả</span>
      </div>
      <div class="exam__results--list">
        <table>
          <thead>
          <th width="1%" class="text-right">ID</th>
          <th width="15%">Tên</th>
          <th width="10%">Email</th>
          <th width="10%">Liên hệ</th>
          <th width="5%">Điểm đầu vào</th>
          <th width="5%">Hỗ trợ</th>
          <th width="5%">Xác nhận</th>
          <th width="20%" class="text-center">Ghi chú</th>
          <th width="10%" class="text-center">Nợ</th>
          <th width="10%" class="text-center">Tuỳ chọn</th>
          </thead>
          <tbody v-if="!loading">
          <tr v-if="paginate.items.length == 0">
            <td colspan="12" class="text-center"> Không có dữ liệu</td>
          </tr>
          <tr v-else v-for="(item, index) in paginate.items" :key="'gr-' + item.id">
            <td>
              <div class="text-right">{{ index + 1}}</div>
            </td>
            <td>
              <div :style="item.is_debt == 1 ? {'color' : 'red'} : ''">{{ item.name }}</div>
            </td>
            <td>
              <div>{{ item.email }}</div>
              <a v-if="item.from_group_id" :href="`${url}/backend/community/users?group_id=${item.from_group_id}`"
                 target="_blank">Chuyển từ nhóm: {{ item.from_group_id }}</a>
            </td>
            <td>
              <div>{{ item.phone }}</div>
              <div class="flex items-center">
                <a v-if="item.info_contact && item.info_contact.facebook"
                   :href="item.info_contact ? item.info_contact.facebook : ''" target="_blank" class="mr-2">
                  <i class="fa fa-facebook-square"></i>
                </a>
                <div v-if="item.user_id">
                  <a v-if="item.conversation_id" :href="`${url}/backend/chat#${item.conversation_id}`" target="_blank"
                     class="mr-2">
                    <i class="fa fa-comment"></i>
                  </a>
                  <i v-else class="fa fa-comment mr-2" style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                     @click="initConversation(item.user_id)"
                  ></i>
                </div>
                <a v-if="item.info_contact && item.info_contact.chat" :href="item.info_contact.chat" target="_blank"
                   class="mr-2">
                  <i class="fa fa-codiepie"></i>
                </a>
              </div>
            </td>
            <td>
              <div class="a-cursor-pointer" @click="setPoint(item)">{{ item.entry_point }}</div>
            </td>
            <td class="text-center">
              <div class="a-cursor-pointer" @click="saveSupport(item)">
                <span v-if="item.support_num" class="badge btn-primary">{{item.support_num}}</span>
                <span v-else class="badge btn-primary">0</span>
              </div>
            </td>
            <td class="text-center">
              <div class="a-cursor-pointer" @click="saveSendDoc(item)">
                <span v-if="item.send_doc" class="badge btn-success">{{ sendDocStatuses[item.send_doc] }}</span>
                <span v-else class="badge btn-danger">Chưa xem</span>
              </div>
            </td>
            <td>
              <div class="flex align-items-flex-end">
                <b class="mr-2">Trợ giảng </b>
                <div class="note">
                  <div class="note-bubble" @click="showNote(item, 'note')">{{item.note}}</div>
                </div>
              </div>
              <div class="flex align-items-flex-end mt-3">
                <b class="mr-3">Sale </b>
                <div class="note">
                  <div class="note-bubble" @click="showNote(item, 'invoice_note')">{{item.invoice_note}}</div>
                </div>
              </div>
            </td>
            <td class="text-center">
              <div :style="item.is_debt == 1 ? {'color' : 'red'} : ''">{{ item.is_debt == 1 ? 'Đang nợ' : '' }}</div>
            </td>
            <td v-if="auth.desc !== 'sale'" class="text-center">
              <div class="btn btn-danger" title="Xoá nhóm" @click="deleteItem(item.id)">
                <i class="fa fa-trash"></i>
              </div>
              <div class="btn btn-info" title="Chuyển nhóm" @click="transferItem(item.id)">
                <i class="fa fa-retweet"></i>
              </div>
            </td>
          </tr>
          </tbody>
          <tbody v-if="loading">
          <tr style="height: 60vh; padding: 20px 0; text-align: center">
            <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="exam__results--screen" id="group__screen">
        <div class="exam__results--paginate">
          <div>
            Hiển thị
            <select v-model="paginate.per_page" @change="loadItems()">
              <option v-for="(option, index) in perPageOption" :key="index">{{option}}</option>
            </select>
            trong số {{ paginate.total }} kết quả
          </div>
          <paginate
              :page-count="paginate.last_page"
              :page-range="4"
              :margin-pages="3"
              :click-handler="changePage"
              :prev-text="'&laquo;'"
              :next-text="'&raquo;'"
              :container-class="'pagination'"
              :page-class="'page-item'"
          >
          </paginate>
        </div>
      </div>
      <CreateItem v-if="createModal" :currentModal="createModal" :groupId="id"
                  @update-create-model="updateCreateModal"/>
      <DeleteItem v-if="deleteModal" :currentModal="deleteModal" :itemId="itemId" :groupId="groupId"
                  @update-delete-model="updateDeleteModal"/>
      <GroupUserNote v-if="showNoteModal" :noteType="noteType" :currentModal="showNoteModal" :groupUser="groupUser"
                     @save-note-model="updateNoteModal"/>
      <GroupLog v-if="showLogModal" :currentModal="showLogModal" :logs="group.logs" @update-log-modal="closeLogModal"/>

      <modal v-if="linkDetailModal" @close="closeLinkModal" @mousedown="closeModal">
        <div slot="body">
          <h3> LINK: {{ group.links.linkZoom.url }}</h3>
          <h4> MẬT KHẨU: <b>{{ group.links.linkZoom.note }}</b></h4>
        </div>
      </modal>
      <modal v-if="isOpenAddCourseModal" @close="closeAddCourseModal" @mousedown="closeAddCourseModal">
        <div slot="header">Tặng khoá học cho cả nhóm</div>
        <div slot="body">
          <div class="flex flex-col">
            <label for="" class="font-md">Khoá học</label>
            <select class="form-control" v-model="addCourseId">
              <option value="33">Luyện đề N1</option>
              <option value="32">Luyện đề N2</option>
              <option value="31">Luyện đề N3</option>
              <option value="17">N1</option>
              <option value="16">N2</option>
              <option value="3">N3</option>
              <option value="4">N4</option>
              <option value="5">N5</option>
            </select>
            <label for="" class="font-md mt-3">Số tháng</label>
            <input class="form-control" v-model="months"/>
            <button class="btn mt-3 btn-success" :disabled="loadingAddCourse" @click="bulkActive">Áp dụng</button>
          </div>
        </div>
      </modal>
    </div>
  </div>
</template>
<script>
// Repository
import RepositoryFactory from '../../repository/repositoryFactory'

const communityUserRepository = RepositoryFactory.get('communityUser')
const communityGroupRepository = RepositoryFactory.get('communityGroup')

import BackToCommunity from './../../component/BackToCommunity'
// Part
import CreateItem from './part/Create'
import DeleteItem from './part/Delete'
import GroupUserNote from './part/Note'
import GroupLog from './part/Log'

// Pagination
import Paginate from 'vuejs-paginate'

Vue.component('paginate', Paginate)
Vue.component('modal', {
  template: '#modal-template'
});
export default {
  name: "CommunityTag",
  components: {
    CreateItem,
    // EditItem,
    DeleteItem,
    BackToCommunity,
    GroupUserNote,
    GroupLog,
  },
  props: {
    id: Number,
    auth: Object,
  },
  data() {
    return {
      url: window.location.origin,
      loading: false,
      paginate: {
        items: [],
        from: 1,
        to: 1,
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0
      },
      perPageOption: [5, 10, 15, 20, 50, 100],
      dataQuery: {
        perPage: 10,
        page: 1,
        textSearch: null,
        note: null,
        is_debt: null,
      },
      group: {},
      itemId: null,
      groupId: null,
      groupUser: null,
      createModal: false,
      deleteModal: false,
      showNoteModal: false,
      showLogModal: false,
      isOpenAddCourseModal: false,
      linkDetailModal: false,
      noteType: 'note',
      addCourseId: null,
      loadingAddCourse: false,
      months: 0,
      sendDocStatuses: ['Chưa xem', 'Đã xem', 'Đã nhận']
    }
  },
  async mounted() {
    this.dataQuery.group_id = this.id;
    this.getList();
  },
  methods: {
    async setPoint(user) {
      const point = window.prompt('Nhập điểm đầu vào');
      if (point !== null) {
        const data = {
          userId: user.user_id,
          groupId: this.id,
          point: point,
        }
        try {
          let response = await communityUserRepository.setUserEntryPoint(data);
          if (response.data.code === 200) {
            user.entry_point = point;
          }
        } catch (e) {
          alert('Có lỗi!')
        }
      }
    },
    updateAge(user) {
      const value = window.prompt('Nhập tuổi');
      this.updateUserInformation(user, 'age', value);
    },
    updateCareer(user) {
      const value = window.prompt('Nhập nghề nghiệp');
      this.updateUserInformation(user, 'career', value);
    },
    async updateUserInformation(user, field, value) {
      try {
        const data = {
          field: field,
          value: value
        }
        let response = await communityUserRepository.updateUserInformation(user.user_id, data);
        if (response.data.code === 200) {
          user[field] = value;
        }
      } catch (e) {
        alert('Có lỗi!')
      }
    },
    async getList() {
      try {
        // this.loading = true
        let response = await communityUserRepository.getList(this.dataQuery)
        const data = response.data.data
        console.log('data................', data)
        this.paginate.items = data.items.map(item => {
          item.info_contact = item.info_contact ? JSON.parse(item.info_contact) : null
          return item;
        })
        this.paginate.from = data.from
        this.paginate.to = data.to
        this.paginate.current_page = data.current_page
        this.paginate.last_page = data.last_page
        this.paginate.total = data.total
        this.paginate.from = 100;
        this.group = data.group;

        console.log('this.paginate...............', this.paginate)
        // this.loading = false
      } catch (e) {
        // this.$bus.$emit('show-loading', false)
      }
    },
    loadItems() {
      this.dataQuery.perPage = this.paginate.per_page
      this.getList()
    },
    changePage: function (pageNum) {
      this.dataQuery.page = pageNum
      this.getList()
    },
    openCreateModal() {
      this.createModal = true;
    },
    updateCreateModal: function (e) {
      this.getList();
      this.createModal = e;
    },
    editItem(item) {
      this.item = item;
      this.EditModel = true;
    },
    updateEditModel(e) {
      this.getList();
      this.EditModel = e;
    },
    deleteItem(id) {
      this.deleteModal = true;
      this.itemId = id;
      this.groupId = this.id;
    },

    async transferItem(id) {
      const groupId = prompt('Nhập mã lớp');
      if (groupId) {
        const data = {
          id: id,
          groupId: groupId
        }
        try {
          let response = await communityUserRepository.transferUser(data);
          await this.getList();
        } catch (e) {
          alert('Có lỗi!')
        }
      }
    },

    updateDeleteModal(e) {
      this.getList();
      this.deleteModal = e;
    },
    showNote(item, type) {
      this.showNoteModal = true;
      this.noteType = type;
      this.groupUser = item;
    },
    updateNoteModal(e) {
      this.getList();
      this.showNoteModal = e;
    },
    closeLogModal(e) {
      this.showLogModal = e;
    },
    async saveSupport(user) {
      const value = window.prompt('Nhập số lần support');

      if (value) {
        var data = {
          id: parseInt(user.id),
          update_value: value,
        };
        try {
          var response = await communityUserRepository.saveSupport(data);
          if (response.status === 200) {
            user.support_num = response.data.support_num;
          }
        } catch (e) {
          alert('Có lỗi!')
        }
      }
    },
    async saveSendDoc(user) {
      let updateValue = 0
      switch (user.send_doc) {
        case null:
          updateValue = 0
          break
        case 0:
          updateValue = 1
          break
        case 1:
          updateValue = 2
          break
        case 2:
          updateValue = 0;
          break;
        default:
          updateValue = 0
      }
      user.send_doc = updateValue
      var data = {
        id: parseInt(user.id),
        update_value: updateValue,
      };
      try {
        var response = await communityUserRepository.saveSendDoc(data);
        if (response.status === 200) {
          user.send_doc = response.data.send_doc;
        }
      } catch (e) {
        alert('Có lỗi!')
      }
    },
    async changeStatusLinks(status, link) {

      this.group.links[link].status = status;

      var response = await communityGroupRepository.changeLinkStatus(this.group.id, this.group.links);

      this.group.links = response.data;
    },
    closeLinkModal() {
      this.linkDetailModal = false;
    },
    closeAddCourseModal() {
      this.isOpenAddCourseModal = false;
    },
    //tạo cuộc hội thoại với user chưa có
    initConversation(userId) {
      $.ajax({
        type: 'post',
        url: window.location.origin + '/backend/user/create-conversation',
        data: {
          'id': userId
        },
        success: function (response) {
          window.open(window.location.origin + "/backend/chat#" + response, "_blank");
        }
      });
    },
    showLog() {
      this.showLogModal = true;
    },
    bulkActive() {
      const vm = this;
      if (this.loadingAddCourse) return;
      this.loadingAddCourse = true
      $.ajax({
        type: 'get',
        url: window.location.origin + `/backend/community/group/${this.id}/bulk-active`,
        data: {
          addCourseId: this.addCourseId,
          months: this.months,
        },
        success: function (response) {
          vm.loadingAddCourse = false
          alert('Kích hoạt thành công')
        }
      });
    }
  }
}
</script>
<style>
.group-links {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.group-links a {
  padding: 3px 5px;
  border-radius: 40px;
  color: #fff;
  font-weight: 500;
}

.btn-group-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px;
  background: gray;
  border-radius: 5px;
  color: #FFFFFF;
}

.btn-group-link i {
  padding-left: 10px;
  background: #0d95e8;
  padding: 5px;
  border-radius: 20%;
}

.btn-group-link a {
  border: 1px solid white;
}

.hide-scroll::-webkit-scrollbar {
  display: none;
}

.note-bubble {
  background: #d2f0ff;
  border-radius: 20px;
  padding: 5px 10px;
  max-width: 300px;
  min-width: 50px;
  min-height: 28px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

b {
  line-height: 1;
}

.note {
  position: relative;
}

.note:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: -4px;
  right: auto;
  top: auto;
  bottom: 0px;
  border: 10px solid;
  border-color: transparent transparent #d2f0ff transparent;
}
</style>
