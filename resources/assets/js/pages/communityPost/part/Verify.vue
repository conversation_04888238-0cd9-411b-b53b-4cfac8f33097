<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header"><PERSON><PERSON><PERSON> du<PERSON>ệt bài viết</h3>
        <div slot="body">
            <p>Bạn có chắc muốn cho đăng bài viết?</p>
            <div>
              <div class="flex justify-end items-center">
                <button class="btn btn-success"><span class="glyphicon glyphicon-ok" @click="verifyItem">Đồng ý</span></button>
                <button class="btn btn-warning ml-5"><span class="glyphicon glyphicon-remove" @click="closeModal">Đóng</span></button>
              </div>
            </div>
        </div>
    </modal>
</template>
<script>
import RepositoryFactory from '../../../repository/repositoryFactory'
const communityPostRepository = RepositoryFactory.get('communityPost')

export default {
  components: {
  },
  props: {
    currentModal: Boolean,
    itemId: Number,
  },
  data() {
    return {
      validate : {
        name: true,
        expired_at: true,
        all : true
      }
    }
  },
  async mounted() {
    // this.getList()
  },
  methods: {
    closeModal() {
      this.$emit('update-verify-model', !this.currentModal);
    },
    async verifyItem() {
      let response = await communityPostRepository.verify(this.itemId);
      if(response.status == 200) {
        this.$emit('update-verify-model', !this.currentModal);
        return;
      }
    },
  }
}
</script>
