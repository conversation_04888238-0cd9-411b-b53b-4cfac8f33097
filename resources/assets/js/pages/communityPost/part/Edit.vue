<template>
    <modal v-if="currentModal" @close="closeModal" @mousedown="closeModal">
        <h3 slot="header">Cập nhật bài đăng</h3>
        <div slot="body">
          <form class="">
            <div class="form-group">
                <label>Nội dung</label>
                <textarea class="form-control" v-model="item.content" required/>
                <div class="invalid-feedback" v-if="!validate.content">Điền nội dung</div>
            </div>
            <div v-if="item.isGroupSample" class="form-row">
              <div class="form-group col-md-6">
                <label>Đăng bài sau số ngày</label>
                <input type="Number" v-model="item.scheduled_posts.days" class="form-control" placeholder="Đăng sau số ngày">
              </div>
              <div class="form-group col-md-6">
                <label for="inputPassword4">Thời điểm</label>
                <VueCtkDateTimePicker 
                  v-model="item.scheduled_posts.time" 
                  :label="time.label" 
                  :only-time="time.onlyTime" 
                  :format="time.format"
                  :formatted="time.format"
                  :no-value-to-custom-elem="time.custom"  
                />
              </div>
            </div>
            <div v-if="!item.isGroupSample" class="form-group">
                <label>Thời điểm đăng bài</label>
                <VueCtkDateTimePicker v-model="item.published_at" :label="date.label" :only-time="date.onlyTime" :format="date.format"/>
            </div>
            <div class="form-group">
                <label>Gắn thẻ</label>
                <div v-if="!tags.length">Nhóm chưa có thẻ</div>
                <div>
                  <button v-for="(tag, index) in tags" :key="index" 
                    type="button" 
                    class="btn btn-sm m-1" 
                    :class="[{ 'btn-success': item.selectedTags.includes(tag.id) }, 'btn-light']"
                    @click="selectTag(tag.id)"
                    >
                      {{ tag.name }}
                  </button>
                </div>
            </div>
            <div class="form-group">
                <label>Gắn hình ảnh <small v-if="!validate.image_size" style="color:red">File ảnh phải nhỏ hơn 2 MB</small></label>
                <vue-upload-multiple-image
                  @upload-success="uploadImageSuccess"
                  @before-remove="beforeRemove"
                  @edit-image="editImage"
                  :data-images="images"
                  :max-image="max"
                  :show-primary="primary"
                ></vue-upload-multiple-image>
            </div>
            <div class="invalid-feedback" v-if="!validate.all">Đã có lỗi sảy ra</div>
          </form>
            <span v-if="!loading" class="btn btn-block btn-success p-5 mt-5" @click="updateItem">Cập nhật</span>
            <span v-if="loading" class="btn btn-block btn-success p-5 mt-5">Loading ...</span>
        </div>
    </modal>    
</template>
<script>
import VueCtkDateTimePicker from 'vue-ctk-date-time-picker';
import 'vue-ctk-date-time-picker/dist/vue-ctk-date-time-picker.css';
Vue.component('VueCtkDateTimePicker', VueCtkDateTimePicker);

import VueUploadMultipleImage from 'vue-upload-multiple-image'

import RepositoryFactory from '../../../repository/repositoryFactory'
const communityPostRepository = RepositoryFactory.get('communityPost')
const communityTagRepository = RepositoryFactory.get('communityTag')

export default {
  components: {
    VueUploadMultipleImage
  },
  props: {
    currentModal: Boolean,
    item: Object
  },
  data() {
    return {
      loading: false,
      max: 12,
      tags: [],
      primary: false,
      imageFormData: [],
      images: [],
      date: {
        label: "Chọn thời gian",
        onlyTime: false,
        format: "YYYY-MM-DD HH:mm"
      },
      time: {
        label: "Chọn thời điểm",
        onlyTime: true,
        custom: true,
        format: "HH:mm"
      },
      validate : {
        content: true,
        image_size: true,
        all : true
      }
    }
  },   
  async mounted() {
    this.resetValide();
    this.getByGroup();
    this.getImages(this.item.data);
  },
  methods: {
    getImages(images) {
      if(!images) {
        this.item.data = []
        return;
      }
      for (const [index, element] of images.entries()) {
        this.images.push(
          {
            path: `${window.location.origin}/cdn/community/default/${element}`,
            default: index == 0 ? 1 : 0,
            highlight: index == 0 ? 1 : 0
          }
        );
      }
    },
    closeModal() {
      this.$emit('update-edit-model', !this.currentModal);
    },
    async updateItem() {
      if(!this.item.content) {
        this.validate.content = false;
        return;
      }
      this.resetValide();
      this.loading = true
      for( const file of this.imageFormData) {
        try {
          let response = await communityPostRepository.uploadImage(file);
          this.item.data.push(response.data.data);
        } catch (error) {
          this.loading = false
          this.validate.image_size = false
          return;
        }
      }
      this.loading = false
      let response = await communityPostRepository.update(this.item.id, this.item);
      if(response.status == 200) {
        this.$emit('update-edit-model', !this.currentModal);
        return;
      } else {
        this.validate.all = false;
        return false;
      }
    },
    resetValide() {
      this.validate = {
        content: true,
        image_size: true,
        all : true
      }
    },
    async uploadImageSuccess(formData, index, fileList) {
      this.imageFormData.push(formData);
    },
    beforeRemove (index, done, fileList) {
      if (this.item.data[index] !== undefined) {
        this.item.data.splice(index, 1);
      }
      done();
    },
    editImage (formData, index, fileList) {
      console.log('edit data', formData, index, fileList)
    },
    async getByGroup() {
      let res = await communityTagRepository.getByGroup({group_id: this.item.group_id});
      if(res.status == 200) {
        this.tags = res.data.data
        return;
      } else {
        return; // add later
      }
    },
    async selectTag(tagId) {
      const index = this.item.selectedTags.indexOf(tagId);
      if (index > -1) {
        this.item.selectedTags.splice(index, 1);
      } else {
        this.item.selectedTags.push(tagId);
      }

    }
  } 
}
</script>