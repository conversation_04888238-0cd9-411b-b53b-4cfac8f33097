<template>
  <div>
    <div class="exam__results--screen" id="group__screen">
      <BackToCommunity />
      <div class="exam__results--filter flex justify-between">
          <strong class="h4">Quản lý bài viết trong nhóm: <b>{{ group.name }}</b></strong>
          <div class="flex justify-end items-center">
              <div class="form-group">
                <strong class="mr-5">B<PERSON><PERSON></strong>
                <input class="form-check-input" type="checkbox" v-model="dataQuery.isVerify" @change="loadItems" id="flexCheckDefault">
              </div>
              <strong class="mr-5">Tìm kiếm</strong>
              <div class="form-group">
                  <input type="text" class="form-control" placeholder="Nhập từ khoá" v-model="dataQuery.name">
              </div>
              <span class="btn btn-info" @click="loadItems()">Tì<PERSON> kiếm</span>
              <div class="ml-5">
                <span class="btn btn-primary" @click="openCreateModel">Tạo mới</span>
              </div>
          </div>
      </div>
      <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
          <span>Tìm thấy <b>{{ paginate.total }}</b> kết quả</span>
      </div>
      <div class="exam__results--list">
          <table>
              <thead>
              <th width="1%" class="text-right">ID</th>
              <th width="40%">Nội dung</th>
              <th width="10%">Ngày tạo</th>
              <th width="10%">Ngày đăng</th>
              <th width="10%">Người tạo</th>
              <th width="10%">Thẻ</th>
              <th width="10%">Ghim bài</th>
              <th width="10%" class="text-center">Tuỳ chọn</th>
              </thead>
              <tbody v-if="!loading">
              <tr v-if="paginate.items.length == 0">
                  <td colspan="12" class="text-center"> Không có dữ liệu</td>
              </tr>
               <tr v-else v-for="(item, index) in paginate.items" :key="'gr-' + item.id">
                  <td>
                      <div class="text-right">{{ dataQuery.perPage * (dataQuery.page - 1) + index + 1 }}</div>
                  </td>
                  <td>
                      <div>{{ item.content }}</div>
                  </td>
                  <td>
                      <div>{{ item.created_at }}</div>
                  </td>
                  <td>
                      <div>{{ item.published_at }}</div>
                  </td>
                  <td>
                      <div>{{ item.username }}</div>
                  </td>
                  <td>
                      <div>{{ item.tags }}</div>
                  </td>
                  <td>
                      <div><input class="form-check-input" type="checkbox" v-model="item.pined_at" @change="PinEvent(item.id, item.pined_at)"></div>
                  </td>
                  <td class="text-center">
                      <span class="btn btn-danger" title="Xoá nhóm" @click="deleteItem(item.id)"><i class="fa fa-trash"></i></span>
                      <span class="btn btn-warning" title="Edit post" @click="editItem(item)"><i class="glyphicon glyphicon-edit"></i></span>
                      <span v-if="!dataQuery.isVerify" class="btn btn-success" title="validate post" @click="verifyItem(item.id)"><i class="glyphicon glyphicon-ok"></i></span>
                  </td>
              </tr>
            </tbody>
            <tbody v-if="loading">
              <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                  <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
              </tr>
            </tbody>
        </table>
      </div>
      <div class="exam__results--screen" id="group__screen">
          <div class="exam__results--paginate">
              <div>
                  Hiển thị
                  <select v-model="paginate.per_page" @change="loadItems()">
                      <option v-for="(option, index) in perPageOption" :key="index">{{option}}</option>
                  </select>
                  trong số {{ paginate.total }} kết quả
              </div>
              <paginate
                  :page-count="paginate.last_page"
                  :page-range="4"
                  :margin-pages="3"
                  :click-handler="changePage"
                  :prev-text="'&laquo;'"
                  :next-text="'&raquo;'"
                  :container-class="'pagination'"
                  :page-class="'page-item'"
              >
              </paginate>
          </div>
      </div>
      <CreateItem v-if="CreateModel" :groupSample="group.is_sample" :currentModal="CreateModel" :groupId="id" @update-create-model="updateCreateModel"/>
      <EditItem v-if="EditModel" :currentModal="EditModel" :item="item" @update-edit-model="updateEditModel"/>
      <DeleteItem :currentModal="DeleteModel" :itemId="itemId" @update-delete-model="updateDeleteModel"/>
      <VerifyItem :currentModal="VerifyModel" :itemId="itemId" @update-verify-model="updateVerifyModel"/>
    </div>
  </div>
</template>
<script>
// Repository
import RepositoryFactory from '../../repository/repositoryFactory'
const communityPostRepository = RepositoryFactory.get('communityPost')

import BackToCommunity from './../../component/BackToCommunity'
// Part
import CreateItem from './part/Create'
import EditItem from './part/Edit'
import DeleteItem from './part/Delete'
import VerifyItem from './part/Verify'
// Pagination
import Paginate from 'vuejs-paginate'
Vue.component('paginate', Paginate)
Vue.component('modal', {
    template: '#modal-template'
});
export default {
  name: "CommunityTag",
  components: {
    CreateItem,
    EditItem,
    DeleteItem,
    VerifyItem,
    BackToCommunity
  },
  props: {
      id: Number,
      isPost: Number
  },
  data() {
    return {
      loading: false,
      paginate: {
        items: [],
        from: 1,
        to: 1,
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0
      },
      perPageOption : [5, 10, 15, 20, 50, 100],
      dataQuery : {
        isVerify: false,
        perPage: 10,
        page: 1,
        name: null
      },
      item: {
        id: null,
        name: null
      },
      group: {},
      itemId: null,
      CreateModel: false,
      EditModel: false,
      DeleteModel: false,
      VerifyModel: false
    }
  },
  async mounted() {
    this.dataQuery.group_id = this.id;
    this.dataQuery.isVerify = this.isPost == 0 ? false : true;
    this.getList()
  },
  methods: {
    async getList() {
        try {
          // this.loading = true
          let response = await communityPostRepository.getList(this.dataQuery)
          const data = response.data.data
          this.paginate.items = data.items
          this.paginate.from = data.from
          this.paginate.to = data.to
          this.paginate.current_page = data.current_page
          this.paginate.last_page = data.last_page
          this.paginate.total = data.total
          this.paginate.from = 100;
          this.group = data.group;
          // this.loading = false
        } catch (e) {
            // this.$bus.$emit('show-loading', false)
        }
    },
    loadItems() {
      this.dataQuery.perPage = this.paginate.per_page
      this.getList()
    },
    changePage: function(pageNum) {
      this.dataQuery.page = pageNum
      this.getList()
    },
    openCreateModel() {
      this.CreateModel = true;
    },
    updateCreateModel: function(e) {
      this.getList();
      this.CreateModel = e;
    },
    editItem(item) {
      this.item = item;
      this.EditModel = true;
    },
    updateEditModel(e) {
      this.getList();
      this.EditModel = e;
    },
    deleteItem(id) {
      this.DeleteModel = true;
      this.itemId = id;
    },
    verifyItem(id) {
      this.VerifyModel = true;
      this.itemId = id;
    },
    updateDeleteModel(e) {
      this.getList();
      this.DeleteModel = e;
    },
    updateVerifyModel(e) {
      this.getList();
      this.VerifyModel = e;
    },
    async PinEvent(id, pined_at) {
      this.loading = true
        try {
          let response = await communityPostRepository.pinedAt({id: id, pined_at: pined_at});
          this.loading = false
        } catch (e) {
            // this.$bus.$emit('show-loading', false)
        }
    }
  }
}
</script>
<style>
</style>
