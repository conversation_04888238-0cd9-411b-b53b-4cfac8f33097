class StackedCards {
  constructor(options = {}) {
    this.options = {
      stackedView: "Top",
      rotate: true,
      visibleItems: 3,
      margin: 10,
      useOverlays: true,
      ...options
    };

    this.currentPosition = 0;
    this.isFirstTime = true;
    this.elTrans = 0;

    this.container = document.getElementById("stacked-cards-block");
    this.cardsContainer = this.container.querySelector(".stackedcards-container");
    this.cards = Array.from(this.cardsContainer.children);
    this.rightOverlay = this.container.querySelector(".stackedcards-overlay.right");
    this.leftOverlay = this.container.querySelector(".stackedcards-overlay.left");

    this.maxElements = this.cards.length;
    this.options.visibleItems = Math.min(this.options.visibleItems, this.maxElements);
    this.init();
  }

  init() {
    this.setupCards();
    this.addEventListeners();
    this.updateUI();
    setTimeout(() => this.container.classList.remove("init"), 150);
  }

  setupCards() {
    const {stackedView, visibleItems, margin} = this.options;
    const marginTotal = margin * (visibleItems - 1);

    this.cardsContainer.style.marginBottom = `${marginTotal}px`;
    this.elTrans = stackedView === "Top" ? marginTotal : 0;

    this.cards.slice(visibleItems).forEach(card => {
      card.classList.add(`stackedcards-${stackedView.toLowerCase()}`, "stackedcards--animatable", `stackedcards-origin-${stackedView.toLowerCase()}`);
      card.style.cssText = `
        z-index: 0;
        opacity: 0;
        transform: scale(${1 - visibleItems * 0.04}) translateY(${this.elTrans}px);
      `;
    });

    this.updateActiveCard();
    this.setupOverlays();
  }

  setupOverlays() {
    if (!this.options.useOverlays) {
      this.leftOverlay.classList.add("stackedcards-overlay-hidden");
      this.rightOverlay.classList.add("stackedcards-overlay-hidden");
      return;
    }
    [this.leftOverlay, this.rightOverlay].forEach(overlay => {
      overlay.style.transform = `translateY(${this.elTrans}px)`;
    });
  }

  updateActiveCard() {
    if (this.cards[this.currentPosition]) {
      this.cards[this.currentPosition].classList.add("stackedcards-active");
    }
  }

  addEventListeners() {
    this.cards.forEach(card => {
      card.addEventListener("click", (e) => {
        // Kiểm tra xem phần tử được click hoặc bất kỳ phần tử cha nào của nó có class noFlip
        let targetElement = e.target;
        let shouldFlip = true;

        // Kiểm tra phần tử được click và tất cả phần tử cha của nó
        while (targetElement && targetElement !== card) {
          // Danh sách các class hoặc phần tử không nên gây ra việc lật thẻ
          if (
              targetElement.classList.contains("noFlip") ||
              targetElement.classList.contains("card_audio") ||
              targetElement.classList.contains("underline") ||
              targetElement.tagName === "BUTTON" ||
              targetElement.tagName === "A" ||
              targetElement.tagName === "SVG" ||
              targetElement.tagName === "path" ||
              targetElement.closest(".card-footer") !== null ||
              targetElement.closest("[class*='cursor-pointer']") !== null
          ) {
            shouldFlip = false;
            break;
          }
          targetElement = targetElement.parentElement;
        }

        // Nếu không có phần tử nào cần ngăn chặn việc lật thẻ, thì lật thẻ
        if (shouldFlip) {
          let version = process.env.NODE_ENV;
          // kiểm tra mặt truowcss khi lật nếu là mặt trước
          if (!card.querySelector(".card-inner").classList.contains("flip") && [39, 40].includes(course_id) && parseInt(authUser.isTester) === 0 && ["production", "prod"].includes(version)) {
            let event = parseInt(course_id) === 39 ? "n5_flc_behind" : "n4_flc_behind";

            // check function ga() co tai khong
            if (typeof ga !== 'undefined') {
              ga("send", "event", "nx_flc_behind", event, event);
            }
          }

          card.querySelector(".card-inner").classList.toggle("flip");
        }
      });
    });
  }

  swipeLeft() {
    if (this.currentPosition >= this.maxElements) {
      return;
    }
    this.transformCard(-1000, 0, 0);
    if (this.options.useOverlays) {
      this.transformOverlay(this.leftOverlay, -1000, 0, 1, () => this.resetOverlay(this.leftOverlay));
    }
    this.nextCard();
  }

  swipeRight() {
    if (this.currentPosition >= this.maxElements) {
      return;
    }
    this.transformCard(1000, 0, 0);
    if (this.options.useOverlays) {
      this.transformOverlay(this.rightOverlay, 1000, 0, 1, () => this.resetOverlay(this.rightOverlay));
    }
    this.nextCard();
  }

  undo() {
    if (this.currentPosition <= 0) {
      return;
    }
    this.currentPosition--;
    this.updateUI();
    this.updateActiveCard();
  }

  nextCard() {
    this.currentPosition++;
    this.updateUI();
    this.updateActiveCard();
  }

  transformCard(x, y, opacity) {
    const card = this.cards[this.currentPosition];
    if (!card) {
      return;
    }
    card.classList.remove("no-transition");
    card.style.zIndex = 6;
    this.transformElement(card, x, y, opacity);
  }

  transformOverlay(overlay, x, y, opacity, callback) {
    overlay.classList.remove("no-transition");
    overlay.style.zIndex = 8;
    this.transformElement(overlay, x, y, opacity);
    setTimeout(callback, 300);
  }

  resetOverlay(overlay) {
    overlay.classList.add("no-transition");
    requestAnimationFrame(() => {
      overlay.style.transform = `translateY(${this.elTrans}px)`;
      overlay.style.opacity = 0;
    });
    this.isFirstTime = false;
  }

  transformElement(element, x, y, opacity) {
    const rotate = this.options.rotate ? Math.min(Math.max(x / 10, -15), 15) : 0;
    requestAnimationFrame(() => {
      element.style.transform = `translateX(${x}px) translateY(${y + this.elTrans}px) rotate(${rotate}deg)`;
      element.style.opacity = opacity;
    });
  }

  updateUI() {
    requestAnimationFrame(() => {
      const {visibleItems, margin} = this.options;
      let zIndex = 5;
      let scale;

      this.cards.slice(this.currentPosition, this.currentPosition + visibleItems).forEach((card, i) => {
        scale = zIndex === 4 ? `scale(0.97)` : (zIndex === 3 ? "scale(0.95)" : "");
        card.style.cssText = `
          z-index: ${zIndex--};
          opacity: 1;
          transform: ${scale} translateY(${margin * i * (1 - visibleItems * 0.04)}px);
        `;
        card.classList.add("stackedcards--animatable");
      });
    });
  }

  destroy() {
    // Remove event listeners to prevent memory leaks
    this.cards.forEach(card => {
      const cardInner = card.querySelector(".card-inner");
      if (cardInner) {
        const newCard = card.cloneNode(true);
        card.parentNode.replaceChild(newCard, card);
      }
    });
  }
}
export default StackedCards;