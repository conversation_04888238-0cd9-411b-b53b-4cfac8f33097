import Vue from 'vue';
import 'es6-promise/auto'
import axios from 'axios';
import VueAxios from 'vue-axios';
import VueRouter from 'vue-router';
import ElementUI, {Tooltip} from 'element-ui'

import 'element-ui/lib/theme-chalk/index.css';
import locale from 'element-ui/lib/locale/lang/vi'
// resources
import store from './canon/store';
import routes from './canon/routes';
import App from './canon/pages/App';
// element ui
Vue.use(ElementUI, { locale });
Vue.use(Tooltip);

const router = new VueRouter({
  base: 'business/',
  history: true,
  mode: 'history',
  routes,
})
Vue.router = router
Vue.use(VueRouter)

// axios request
Vue.use(VueAxios, axios);
axios.defaults.baseURL = `${process.env.APP_URL}/business/api/v1`;

// components
Vue.component('app', App);

const app = new Vue({
  el: '#businessApp',
  router,
  store
});
app.$mount('#businessApp');
