import communityGroupRepository from "./communityGroupRepository";
import communityTagRepository from "./communityTagRepository";
import communityUserRepository from "./communityUserRepository";
import communityPostRepository from "./communityPostRepository";
import communityReportRepository from "./communityReportRepository";
import communityCommentRepository from "./communityCommentRepository";
import kaiwaScheduleTestRepository from "./kaiwaSchedulepository";
import surveyRepository from "./surveyRepository";

const repositories = {
    communityGroup: communityGroupRepository,
    communityTag: communityTagRepository,
    communityUser: communityUserRepository,
    communityPost: communityPostRepository,
    communityReport: communityReportRepository,
    communityComment: communityCommentRepository,
    kaiwaScheduleTest: kaiwaScheduleTestRepository,
    survey: surveyRepository,
};

export default {
    get: name => repositories[name]
};
