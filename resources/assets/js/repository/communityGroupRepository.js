import client from '../helper/client'

const urlGetList = '/backend/community/group/list';
const urlGetSampleList = '/backend/community/group/sample-list';
const urlGetById = '/backend/community/group/get-by-id';
const urlStore = '/backend/community/group/store';
const urlUpdate = '/backend/community/group/update';
const urlDelete = '/backend/community/group/delete';
const urlGetTeacherList = '/backend/community/group/get-teacher-list';
const urlCreateGroup = '/backend/community/group/create-group-chat';
const urlGroup = '/backend/community/group';
let urlUpdateStage = "/backend/community/group/change-stage";

export default {
    getList(params) {
        return client.get(urlGetList, {params: params});
    },
    getSampleList() {
        return client.get(urlGetSampleList, {});
    },
    updateStatus(id, status) {
        return client.post(`${urlUpdate}/${id}/status`, status);
    },
    urlGetById(id) {
        return client.get(`${urlGetById}/${id}`, {});
    },
    store(payload) {
        return client.post(urlStore, payload);
    },
    update(id, payload) {
        return client.post(`${urlUpdate}/${id}`, payload);
    },
    delete(id) {
        return client.post(`${urlDelete}/${id}`);
    },
    getTeacherList() {
        return client.get(`${urlGetTeacherList}`);
    },
    createGroupChat(payload) {
        return client.post(urlCreateGroup, payload);
    },
    changeLinkStatus(id, payload) {
        return client.post(`${urlGroup}/change-status-link/${id}`, {update_links: payload});
    },
    exportGroupChatMembers(id) {
        return client.post(`${urlGroup}/${id}/export-group-chat-member`);
    },
    updateStage(id) {
        return client.get(`${urlUpdateStage}/${id}`, {params: {id}});
    },
    getVipCombo() {
        return client.get('/backend/vip/get-all-vip-combo');
    }
};
