import client from '../helper/client'

const urlGetList = '/backend/community/tags/list';
const urlStore = '/backend/community/tags/store';
const urlUpdate = '/backend/community/tags/update';
const urlDelete = '/backend/community/tags/delete';
const urlGetByGroup = '/backend/community/tags/all-by-group';

export default {
  getList(params) {
    return client.get(urlGetList,{ params: params });
  },
  getByGroup(params) {
    return client.get(urlGetByGroup,{ params: params });
  },
  store(payload) {
    return client.post(urlStore, payload);
  },
  update(id, payload) {
    return client.post(`${urlUpdate}/${id}`, payload);
  },
  delete(id) {
    return client.post(`${urlDelete}/${id}`);
  }
}
