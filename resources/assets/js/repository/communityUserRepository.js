import client from '../helper/client'

const urlGetList = '/backend/community/users/list';
const urlUserGetList = '/backend/community/users/not-in-group';
const urlStore = '/backend/community/tags/store';
const urlUpdate = '/backend/community/tags/update';
const urlDelete = '/backend/community/users/delete';
const urlAddGroupUser = '/backend/community/users/add-group-user';
const urlSetEntryPoint = '/backend/vip/set-entry-point';
const urlUpdateUserInformation = '/backend/vip/update-user-information';
const userUrl = '/backend/community/users';
const urlSearchByNameOrEmail = '/backend/user/search'
const urlGetLesson = '/backend/user/get-lesson'
const urlDeleteLesson = '/backend/user/delete-lesson'
const urlDeleteLessonTime = '/backend/user/delete-lesson-time'

export default {
  getList(params) {
    return client.get(urlGetList,{ params: params });
  },
  notInGroup(params) {
    return client.get(urlUserGetList,{ params: params });
  },
  store(payload) {
    return client.post(urlStore, payload);
  },
  addGroupUser(payload) {
    return client.post(urlAddGroupUser, payload);
  },
  update(id, payload) {
    return client.post(`${urlUpdate}/${id}`, payload);
  },
  delete(id) {
    return client.post(`${urlDelete}/${id}`);
  },
  setUserEntryPoint(payload) {
    return client.post(urlSetEntryPoint, payload);
  },
  updateUserInformation(id, payload) {
    return client.put(`${urlUpdateUserInformation}/${id}`, payload);
  },
  saveUserNote(payload) {
    return client.post(userUrl + '/save-note', payload);
  },
  saveSupport(payload) {
    return client.post(userUrl + '/save-support', payload);
  },
  saveSendDoc(payload) {
    return client.post(userUrl + '/save-send-doc', payload);
  },
  transferUser(payload) {
    return client.post(userUrl + '/transfer', payload);
  },
  search(params) {
    return client.get(urlSearchByNameOrEmail,{ params: params });
  },
  getLesson(params) {
    return client.get(urlGetLesson,{ params: params });
  },
  deleteLesson(data) {
    return client.post(urlDeleteLesson, data);
  },
  deleteLessonTime(data) {
    return client.post(urlDeleteLessonTime, data);
  },
}
