import client from "../helper/client";

let urlGetList = "/backend/discuss/list-teacher-free-time";
let urlStore = "/backend/discuss/store-teacher-free-time";
let urlgGetTeacherFreeTimeBlank = "/backend/discuss/list-teacher-free-time/blank";
let urlStoreTestSchedule = "/backend/discuss/store-schedule-test";
let urlGetDetailInvoice = "/backend/vip/invoices/";
let urlUpdateStatusScheduleTest = "/backend/discuss/update-status-schedule-test";
let urlUpdateClassFieldStatus = "/backend/discuss/update-class-field-status";
let urlUpdateNote = "/backend/discuss/update-status-schedule-test";
let urDeleteScheduleTest = "/backend/discuss/delete-schedule-test";
let urlDeleteTeacherFreeTime = "/backend/discuss/delete-teacher-free-time";
let urlCreateScheduleNotFinalized = "/backend/discuss/create-schedule-not-finalized";
let urlAttachBlankCalendar = "/backend/discuss/attach-blank-calendar";
let urlGetScheduleNotFinalized = "/backend/discuss/list-schedule-not-finalized";
let urlUpdateStatusTakeCare = "/backend/discuss/update-status-take-care";
let urlUpdateStatusReceiveShift = "/backend/discuss/update-status-receive-shift";

export default {
    getList(params) {
        return client.get(urlGetList, {params: params});
    },
    createTimeFree(data) {
        return client.post(urlStore, data);
    },
    getTeacherFreeTimeBlank(params) {
        return client.get(urlgGetTeacherFreeTimeBlank, {params: params});
    },
    createTestSchedule(data) {
        return client.post(urlStoreTestSchedule, data);
    },
    getDetailInvoice(params) {
        return client.get(urlGetDetailInvoice + params);
    },
    updateStatusScheduleTest(data) {
        return client.post(urlUpdateStatusScheduleTest, data);
    },
    updateClassFieldStatus(data) {
        return client.post(urlUpdateClassFieldStatus, data);
    },
    updateNote(data) {
        return client.post(urlUpdateNote, data);
    },
    deleteScheduleTest(data) {
        return client.post(urDeleteScheduleTest, data);
    },
    deleteTeacherFreeTime(data) {
        return client.post(urlDeleteTeacherFreeTime, data);
    },
    createScheduleNotFinalized(data) {
        return client.post(urlCreateScheduleNotFinalized, data);
    },
    attachBlankCalendar(data) {
        return client.post(urlAttachBlankCalendar, data);
    },
    getScheduleNotFinalized(params) {
        return client.get(urlGetScheduleNotFinalized, {params: params});
    },
    updateStatusTakeCare(data) {
        return client.post(urlUpdateStatusTakeCare, data);
    },
    updateStatusReceiveShift(data) {
        return client.post(urlUpdateStatusReceiveShift, data);
    }
};
