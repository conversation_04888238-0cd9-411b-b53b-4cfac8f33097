import client from '../helper/client'

const urlGetList = '/backend/community/posts/list';
const urlStore = '/backend/community/posts/store';
const urlUploadImage = '/backend/community/posts/upload-image';
const urlUpdate = '/backend/community/posts/update';
const urlVerify = '/backend/community/posts/verify';
const urlDelete = '/backend/community/posts/delete';
const urlPin = '/backend/community/posts/pined-at';

export default {
  getList(params) {
    return client.get(urlGetList,{ params: params });
  },
  uploadImage(payload) {
    return client.post(urlUploadImage, payload,  { headers: {
      'Content-type': 'application/x-www-form-urlencoded',
      }
    });
  },
  store(payload) {
    return client.post(urlStore, payload);
  },
  verify(id) {
    return client.post(`${urlVerify}/${id}`);
  },
  update(id, payload) {
    return client.post(`${urlUpdate}/${id}`, payload);
  },
  delete(id) {
    return client.post(`${urlDelete}/${id}`);
  },
  pinedAt(payload) {
    return client.post(urlPin, payload);
  }
}
