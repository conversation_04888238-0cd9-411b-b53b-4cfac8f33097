var user = new Vue({

  el: '#result-test-profile',
  data: {
    listResults   : result_ListResults,
    listLessons   : result_ListLessons,
    tokenMp3      : result_tokenMp3,
    currentResult : {},
    tasks         : [],
    resultData    : {},
    answers       : [],
    currentIndex  : -1,
    currentLessonId: -1,
    mp3       : null
  },

  methods: {
    playAudio: function (audio) {
      // console.log("click play audio", audio);
      var vm = this;

      vm.playingQuestionAudio = false;
      vm.playingAnswerAudio = false;
      // if(vm.mp3 == null) vm.mp3 = new Audio(vm.url + '/cdn/audio/' + audio);
      if(vm.mp3 == null) vm.mp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
      else{
        vm.mp3.pause();
        vm.mp3.currentTime = 0;
        vm.mp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
        // vm.mp3 = new Audio(vm.url + '/cdn/audio/' + audio);
      }
      vm.mp3.play();
      vm.playingMp3 = true;
    },
    processResults: function(questions) {
      var vm = this;
      var regexAnswerAudio = /\{\!(.*?)\!\}/;
      var regexQuestionAudio = /\{\?(.*?)\?\}/;
      var regexHidden = /\{\*(.*?)\*\}/;
      var regexGap = /\[\[(.*?)\]\]/;
      var regexStar = /\[\*(.*?)\*\]/;
      questions.forEach(function (question) {
          question.mask = question.value;
          question.answered = 0;
          if (regexAnswerAudio.exec(question.mask)) {
            question.answer_audio = _.trim(regexAnswerAudio.exec(question.value)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
            question.mask = question.mask.replace(regexAnswerAudio, "");
          }
          while (regexAnswerAudio.exec(question.mask)) {
            question.answer_audio = _.trim(regexAnswerAudio.exec(question.mask)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
            question.mask = question.mask.replace(regexAnswerAudio, "");
          }
          if (regexQuestionAudio.exec(question.mask)) {
            question.question_audio = _.trim(regexQuestionAudio.exec(question.value)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
            question.mask = question.mask.replace(regexQuestionAudio, "");
          }
          while (regexQuestionAudio.exec(question.mask)) {
            question.question_audio = _.trim(regexQuestionAudio.exec(question.mask)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
            question.mask = question.mask.replace(regexQuestionAudio, "");
          }
          if (regexHidden.exec(question.mask)) {
            question.hidden = _.trim(regexHidden.exec(question.value)[1]);
            question.mask = question.mask.replace(regexHidden, `<span class="text-success text-bold">${question.hidden}</span>`);
          }
          while (regexHidden.exec(question.mask)) {
            question.hidden = _.trim(regexHidden.exec(question.mask)[1]);
            question.mask = question.mask.replace(regexHidden, `<span class="text-success text-bold">${question.hidden}</span>`);
          }
          if (regexGap.exec(question.mask)) {
            question.hidden = _.trim(regexGap.exec(question.value)[1]);
            question.mask = question.mask.replace(regexGap, `<span class="text-success text-bold">${question.hidden}</span>`);
          }
          while (regexGap.exec(question.mask)) {
            question.hidden = _.trim(regexGap.exec(question.mask)[1]);
            question.mask = question.mask.replace(regexGap, `<span class="text-success text-bold">${question.hidden}</span>`);
          }
          if (regexStar.exec(question.mask)) {
            question.hidden = _.trim(regexStar.exec(question.value)[1]);
            question.mask = question.mask.replace(regexStar, `<span class="text-success text-bold">${question.hidden}</span>`);
          }
          while (regexStar.exec(question.mask)) {
            question.hidden = _.trim(regexStar.exec(question.mask)[1]);
            question.mask = question.mask.replace(regexStar, `<span class="text-success text-bold">${question.hidden}</span>`);
          }
          // if (question.type === 11) {
          //     question.blocks = question.mask.split(/\s+/);
          //     question.answerBlocks = question.mask.split(/\s+/);
          //     question.gapMap = [];
          //     question.blocks.forEach(function (val, index) {
          //         if (val === "____") {
          //             question.gapMap.push(index);
          //         }
          //     });
          // }
          return question;
      });
      return questions;
    },
    // hiển thị giờ : phút từ 1 biến datetime
    printTime: function(datetimeInt) {
      var vm = this;
      var datetime = new Date(1000 * datetimeInt);
      return vm.convertNumber(datetime.getHours()) + ":" + vm.convertNumber(datetime.getMinutes());
    },

    // hiển thị ngày / tháng / năm từ 1 biến datetime
    printDate: function(datetimeInt) {
      var vm = this;
      var datetime = new Date(1000 * datetimeInt);
      return vm.convertNumber(datetime.getDate()) + "/" + vm.convertNumber(datetime.getMonth() + 1) + "/" + datetime.getFullYear();
    },

    // thêm '0' nếu số nhỉ hơn 10
    convertNumber: function(number) {
      if (number < 10) {
        return "0" + number;
      }
      return number + "";
    },

    // xem chi tiết 1 bài test
    reviewTestResult: function(lessonId, index) {
      var vm = this;
      vm.currentResult = vm.listResults[lessonId][index];

      var dataResult = {
        'lesson_id' : lessonId
      };

      // console.log(dataResult);

      $.ajax({
        url: window.location.origin + "/account/get-test-result-info", type:"POST", data: dataResult, async: true,
        error: function() {
        },
        success: function(response) {
          // console.log(response);
          vm.tasks = response.tasks;
          vm.answers = response.answers;
          vm.resultData = JSON.parse(vm.currentResult['data']);

          // kiêểm tra xem resultData có phải là object sau khi parse không. nếu không phải thì để nguyên như cũ
          if (typeof vm.resultData !== 'object') {
            if (typeof JSON.parse(vm.resultData) === 'object') {
              vm.resultData = JSON.parse(vm.resultData);
            }
          }


          for (var i = 0; i < vm.tasks.length; i++) {
            var id = vm.tasks[i].id;
            if (parseInt(vm.tasks[i].type) != 3 && parseInt(vm.tasks[i].type) != 10) {
              // task type = 3 là câu hỏi trắc nghiệm
              continue;
            }

            var answerObj = vm.resultData[id];
            if (answerObj == undefined || answerObj == "" ) {
              // dữ liệu cũ không lưu những câu không trả lời
              // nếu ko có data thì reset những câu trả lời đã chọn tương ứng
              for (var j = 0; j < vm.answers[id].length; j++) {
                vm.answers[id][j].checked = false;
              }
              continue;
            }

            var answerValue = answerObj;
            // kiểm tra câu trả lời để set vào 1 thuộc tính mới (checked cho việc hiển thị)
            for (var j = 0; j < vm.answers[id].length; j++) {
              vm.answers[id][j].checked = false;
              if(vm.answers[id][j].id == answerValue) {
                vm.answers[id][j].checked = true;
              }
            }
          }
          vm.tasks = vm.processResults(vm.tasks)
        }
      });
    },

    removeTestResult: function(lessonId, index) {
      var vm = this;
      vm.currentIndex = index;
      vm.currentLessonId = lessonId;
    },

    confirmRemoveResult: function() {
      var vm = this;

      var data = {
        'result_id' : vm.listResults[vm.currentLessonId][vm.currentIndex].id
      }

      // xóa 1 test result
      $.ajax({
        url: window.location.origin + "/account/delete-test-result", type:"DELETE", data: data, async: true,
        error: function() {
        },
        success: function(response) {
          // console.log(response);
        }
      });

      vm.listResults[vm.currentLessonId].splice(vm.currentIndex, 1);
      $('#removeTestResult').modal('toggle');
    },


    convertTime: function(time, type) {
      var date =  new Date(time)
      var result;
      var hours = date.getHours()
      var minutes = date.getMinutes()
      var day = date.getDate();
      var month = date.getMonth() + 1;
      var year = date.getFullYear();
      if (type === 'time') {
        result = ('0' + hours).slice(-2) + ':' + ('0' + minutes).slice(-2);
      } else if (type === 'date') {
        result = ('0' + day).slice(-2) + '/' + ('0' + month).slice(-2) + '/' + year;
      }
      return result;
    }
  },

  mounted: function(){
    var vm = this;
    $('#result-test-profile').css('display', 'block');
  }
});