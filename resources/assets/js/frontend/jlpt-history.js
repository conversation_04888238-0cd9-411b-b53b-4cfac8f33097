var jlptUrl = "http://localhost:3333";

//nếu là deploy trên web thật
if (window.location.href.indexOf("-test") != -1) {
  jlptUrl = "https://jlpt-test.dungmori.com";
}

//nếu là deploy trên web thật
if (
  window.location.href.indexOf("dungmori.com") != -1 &&
  window.location.href.indexOf("web-test") == -1
) {
  jlptUrl = "https://mjt.dungmori.com";
}
var ranking = new Vue({
  el: "#main-history-right",

  data: function () {
    return {
      items: items,
      certificateUser: null,
      formData: {
        fullname: "",
        dob: "",
        mobile: "",
        email: "",
        country: "vi",
        postalcode: "",
        address: "",
      },
      locale: locale,
      validation: {},
      translate: {
        en: {
          msg_required: "Please enter this fields",
          msg_email_format: "Incorrect email format",
          msg_mobile_format: "Incorrect mobile format",
          msg_ise: "Internal Server Error",
          msg_success: "Successfully!",
        },
        vi: {
          msg_required: "Trường này không được để trống",
          msg_email_format: "Email không đúng định dạng",
          msg_mobile_format: "Điện thoại có 10 đến 12 ký tự là số",
          msg_ise: "Có lỗi xảy ra. Vui lòng thực hiện lại sau!",
          msg_success: "Cập nhật thông tin thành công!",
        },
      },
      myResult: "",
    };
  },
  watch: {
    "formData.country": function (nValue, oValue) {
      this.detailValiCheck("country");
    },
    "formData.postalcode": function (nValue, oValue) {
      this.detailValiCheck("postalcode");
    },
    "formData.address": function (nValue, oValue) {
      this.detailValiCheck("address");
    },
    "formData.fullname": function (nValue, oValue) {
      this.detailValiCheck("fullname");
    },
    "formData.dob": function (nValue, oValue) {
      this.detailValiCheck("dob");
    },
    "formData.mobile": function (nValue, oValue) {
      this.detailValiCheck("mobile");
    },
    "formData.email": function (nValue, oValue) {
      this.detailValiCheck("email");
    },
  },

  methods: {
    btoa: function (str) {
      return btoa(str);
    },
    showAnswer: function (examId, userId) {
      this.PopupCenter(
        jlptUrl +
          "/rs/" +
          window.btoa("akOiW" + window.btoa(examId)) +
          "/" +
          window.btoa(userId),
        "XXX",
        "750",
        "800"
      );
    },
    PopupCenter: function (url, title, w, h) {
      // Fixes dual-screen position                         Most browsers      Firefox
      var dualScreenLeft =
        window.screenLeft != undefined ? window.screenLeft : screen.left;
      var dualScreenTop =
        window.screenTop != undefined ? window.screenTop : screen.top;

      var width = window.innerWidth
        ? window.innerWidth
        : document.documentElement.clientWidth
        ? document.documentElement.clientWidth
        : screen.width;
      var height = window.innerHeight
        ? window.innerHeight
        : document.documentElement.clientHeight
        ? document.documentElement.clientHeight
        : screen.height;

      var left = width / 2 - w / 2 + dualScreenLeft;
      var top = height / 2 - h / 2 + dualScreenTop;
      var newWindow = window.open(
        url,
        title,
        "scrollbars=yes, width=" +
          w +
          ", height=" +
          h +
          ", top=" +
          top +
          ", left=" +
          left
      );

      // Puts focus on the newWindow
      if (window.focus) {
        newWindow.focus();
      }
    },

    //xem chi tiết chứng chỉ kết quả
    showCertificate: function (index) {
      var vm = this;
      vm.certificateUser = vm.items[index];

      setTimeout(function () {
        $(".fancybox").fancybox().trigger("click");
      }, 100);
    },

    //hiển thị date
    printDate: function (date) {
      return (
        date.substring(0, 4) +
        "年 " +
        date.substring(5, 7) +
        "月 " +
        date.substring(8, 10) +
        "日"
      );
    },

    //hiển thị tổng điểm
    printTotal: function (a, b, c) {
      return a + b + c;
    },

    //hiển thị kết quả đỗ trượt
    printResult: function (course_name, a, b, c) {
      if (["N4", "N5"].includes(course_name) && (a < 38 || c < 19)) {
        return false;
      }
      if (
        ["N3", "N2", "N1"].includes(course_name) &&
        (a < 19 || b < 19 || c < 19)
      ) {
        return false;
      }
      var vm = this;

      if (course_name == "N5" && vm.printTotal(a, b, c) >= 80) {
        return true;
      } else if (course_name == "N4" && vm.printTotal(a, b, c) >= 90) {
        return true;
      } else if (course_name == "N3" && vm.printTotal(a, b, c) >= 95) {
        return true;
      } else if (course_name == "N2" && vm.printTotal(a, b, c) >= 90) {
        return true;
      } else if (course_name == "N1" && vm.printTotal(a, b, c) >= 100) {
        return true;
      }

      return false;
    },

    showModal: function (certificate, myResult) {
      certificate = certificate.replace(/[\r\n]/g, "\\n");
      if (certificate) certificate = JSON.parse(certificate);
      else certificate = this.formData;
      this.formData.fullname = certificate.fullname;
      this.formData.dob = certificate.dob;
      this.formData.mobile = certificate.mobile;
      this.formData.country = certificate.country;
      this.formData.email = certificate.email;
      this.formData.postalcode = certificate.postalcode;
      this.formData.address = certificate.address;
      this.myResult = myResult;
      $("#infoModal").modal("show");
    },

    //xử lý khi điền thông tin nhận bằng
    submitCertificate: function () {
      var vm = this;
      vm.validationCheck();
      if (Object.keys(vm.validation).length == 0)
        $.post(jlptUrl + "/certificate/" + vm.myResult, vm.formData).then(
          function (res) {
            if (res.status) {
              $("#infoModal").modal("hide");
              // $('#infoModal').on('hidden.bs.modal', function (e) {
              //     alert(vm.translate[vm.locale].msg_success);
              // });
              location.reload();
              window.open(
                jlptUrl + "/certificate/" + btoa(vm.myResult),
                "_blank"
              );
            } else alert(vm.translate[vm.locale].msg_ise);
          }
        );
    },
    validationCheck: function () {
      var vm = this;
      let validation = {};
      for (let element in this.formData) {
        vm.detailValiCheck(element);
      }
      return validation;
    },
    detailValiCheck: function (element) {
      var vm = this;
      if (!vm.formData[element]) {
        vm.validation[element] = vm.translate[vm.locale].msg_required;
      } else {
        delete vm.validation[element];
        if (
          element == "email" &&
          !/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(
            vm.formData.email
          )
        ) {
          vm.validation[element] = vm.translate[vm.locale].msg_email_format;
        }

        if (element == "mobile" && !/^\d{10,12}$/.test(vm.formData.mobile)) {
          vm.validation[element] = vm.translate[vm.locale].msg_mobile_format;
        }
      }
      if (
        element == "country" ||
        element == "postalcode" ||
        element == "address"
      ) {
        vm.valiAddressCheck();
      }
    },
    valiAddressCheck: function () {
      var vm = this;
      vm.validation.msgAddress = vm.translate[vm.locale].msg_required;
      if (vm.formData.country && vm.formData.postalcode && vm.formData.address)
        delete vm.validation.msgAddress;
    },
    isShowEditButton: function (item) {
      return this.printResult(
        item.course,
        item.score_1,
        item.score_2,
        item.score_3
      );
    },
  },
  created: function () {
    var vm = this;
    vm.items = vm.items.map((item) => {
      item.userName = userName;
      return item;
    });
  },
});
