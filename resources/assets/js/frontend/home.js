// Check vào tạo cookie hiện popup
var firstTimeVisit = getCookie("first_time_visit_today");
if(firstTimeVisit == null || firstTimeVisit == 0){
    $("#open-featured-popup").click();
    setCookie("first_time_visit_today", 1, 1);
}

// Mở rộng ô khóa học -> di động
function expandCourses(){
    console.log('xem thêm khóa học mobile');
    $('.mobile-content').css('height', 'auto');
    $('.view-more-courses').css('display', 'none');
}

// Người dùng click vào slider
function pressClick(id){
    $("#"+id).click();
}

// Fancybox cho video và popup
$(".fancybox").fancybox({ type: "iframe" })


// Register to be supported (send mail)
var isSendEmail = false;
function sendMailForSupport() {

    if (isSendEmail) {
        return;
    }
    isSendEmail = true;

    var name = $('#full_name').val();
    var email = $('#user_email').val();
    var phoneNumber = $('#phone_number').val();
    var courseName = $('#course_name').val();

    // Check empty
    if (!name || !email || !phoneNumber || !courseName) {
        $.fancybox.open('<div class="message"><h2>Lỗi!</h2><p>Vui lòng không để thông tin còn trống!</p></div>');
        isSendEmail = false;
        return;
    }

    if (checkspecialSymbol(name) || checkspecialSymbol(email) || checkspecialSymbol(phoneNumber)) {
        $.fancybox.open('<div class="message"><h2>Lỗi!</h2><p>Thông tin không được chứa ký tự đặc biệt!</p></div>');
        isSendEmail = false;
        return;
    }

    if (!validateEmail(email)) {
        $.fancybox.open('<div class="message"><h2>Lỗi!</h2><p>Email không đúng!</p></div>');
        isSendEmail = false;
        return;
    }

    var data = {
        'name': name,
        'email': email,
        'phoneNumber': phoneNumber,
        'courseName': courseName
    };

    // Post by ajax
    $.post(window.location.origin + "/register-for-support",
    data,
    function(data, status) {
        console.log("Data: ", data, "\nStatus: ", status);
        $('#noti_register_support').modal();
        $.fancybox.open('<div class="message"><h2>Thành công!</h2><p>Cảm ơn bạn đã gửi thông tin về dungmori, chúng tôi sẽ sớm liên hệ với bạn!</p></div>');

        // Reset data
        $('#full_name').val("");
        $('#user_email').val("");
        $('#phone_number').val("");
        $('#course_name').val("");
        isSendEmail = false;
    })
    .fail(function(jqXHR, textStatus, error) {
        console.log(error);
        $.fancybox.open('<div class="message"><h2>Lỗi!</h2><p>Có lỗi xảy ra, vui lòng thử lại!</p></div>');
        isSendEmail = false;
    });
}
