Vue.filter("time", function (value) {
  return moment(value).format("HH:mm");
});
Vue.filter("two_digits", function (value) {
  if (value.toString().length <= 1) {
    return "0" + value.toString();
  }
  return value.toString();
});
Vue.filter("slash_date", function (value) {
  return moment(value, "YYYY-MM-DD").format("DD/MM/YYYY");
});
new Vue({
  el: "#test-online-exam",
  data: {
    url: window.location.origin,
    exam: exam,
    userId: userId,
    lessonTypes: {
      N1: ["Từ vựng - <PERSON><PERSON> - <PERSON> pháp", "<PERSON><PERSON><PERSON> hiểu", "<PERSON>he hiểu"],
      N2: ["Từ vựng - <PERSON>ữ <PERSON> - <PERSON> pháp", "Đ<PERSON>c hiểu", "<PERSON>he hiểu"],
      N3: ["Từ vựng - <PERSON><PERSON>", "Ngữ pháp - <PERSON><PERSON><PERSON> hiểu", "<PERSON>he hiểu"],
      N4: ["Từ vựng - <PERSON><PERSON>", "<PERSON><PERSON> pháp - <PERSON>ọc hiểu", "<PERSON><PERSON> hiểu"],
      N5: ["Từ vựng - <PERSON>ữ Hán", "Ngữ pháp - <PERSON>ọc hiểu", "<PERSON>he hiểu"],
    },
    answers: {},
    timer: 0,
  },
  computed: {
    examDetail: function () {
      return this.exam.exam;
    },
    lessonTitles: function () {
      return this.lessonTypes[this.examDetail.course];
    },
    questionCount: function () {
      var count = 0;
      this.exam.exam.lessons.forEach(function (lesson) {
        var questions = lesson.questions.filter(function (question) {
          return question.is_content != 1;
        });
        count += questions.length;
      });
      return count;
    },
    answerCount: function () {
      var answers = step ? this["answer" + step] : this.answers;
      return Object.keys(answers).length;
    },
    time_start: function () {
      return this.exam.time_start;
    },
    time_end: function () {
      return this.exam.time_end;
    },
    timeEndFormat: function () {
      return moment(this.time_end).format("HH:mm");
    },
    mp3: function () {
      var exam = _.find(this.examDetail.lessons, ["type", "3"]);
      return exam ? exam.mp3 : null;
    },
    currentDate: function () {
      return new Date();
    },
    timerValue: function () {
      return this.timer > 0 ? this.timer : 0;
    },
    timeText: function () {
      return {
        hour: moment.duration(this.timerValue).hours(),
        minute: moment.duration(this.timerValue).minutes(),
        second: moment.duration(this.timerValue).seconds(),
      };
    },
    answer1: function () {
      var lesson = _.find(this.examDetail.lessons, { type: "1" });
      if (!lesson) return null;
      var question1 = lesson.questions.map(function (q) {
        return q.id;
      });
      return _.pick(this.answers, question1);
    },
    answer2: function () {
      var lesson = _.find(this.examDetail.lessons, { type: "2" });
      if (!lesson) return null;
      var question2 = lesson.questions.map(function (q) {
        return q.id;
      });
      return _.pick(this.answers, question2);
    },
    answer3: function () {
      var lesson = _.find(this.examDetail.lessons, { type: "3" });
      if (!lesson) return null;
      var question3 = lesson.questions.map(function (q) {
        return q.id;
      });
      return _.pick(this.answers, question3);
    },
  },
  watch: {
    answers: {
      handler: function (value) {
        if (!_.isEmpty(value)) {
          localStorage.setItem(
            "dmr_test_" + this.userId + "_" + this.exam.id,
            JSON.stringify(value)
          );
        }
      },
      deep: true,
      immediate: true,
    },
    timerValue: function (val) {
      if (val == 0) {
        this.submit();
      }
    },
  },
  mounted: function () {
    // console.log(this.exam);
    this.init();
  },
  methods: {
    init: function () {
      var time = step
        ? getCookie("dmr_timer_" + this.exam.id + "_" + step)
        : getCookie("dmr_timer_" + this.exam.id);
      if (!time) {
        var timestamp = this.currentDate.getTime();
        var endTime = 0;
        if (this.examDetail.time_split && step) {
          endTime = timestamp + this.examDetail.time_split[step - 1] * 60000;
          setCookie("dmr_timer_" + this.exam.id + "_" + step, endTime, 1);
          this.timer = this.examDetail.time_split[step - 1] * 60000;
        } else {
          endTime = timestamp + this.examDetail.maximum_time * 60000;
          setCookie("dmr_timer_" + this.exam.id, endTime, 1);
          this.timer = this.examDetail.maximum_time * 60000;
        }
      } else {
        this.timer = time - this.currentDate.getTime();
      }
      setInterval(this.updateTimer, 1000);

      var data = localStorage.getItem(
        "dmr_test_" + this.userId + "_" + this.exam.id
      );
      if (data) {
        this.answers = JSON.parse(data);
      }
      if (time && this.timerValue == 0) this.submit();
    },
    updateTimer: function () {
      this.timer = this.timer - 1000;
    },
    answerLength: function (content) {
      if (!content) return 0;
      var text = />(.*?)</g.exec(content);
      return text ? text[1].length : content.length;
    },
    selectAnswer: function (qId, aId) {
      this.$set(this.answers, qId, aId);
    },
    confirmSubmit: function () {
      if (this.loading) return;
      var confirm = false;
      if (this.answerCount < this.questionCount) {
        confirm = window.confirm(
          "Bạn mới làm " +
            this.answerCount +
            "/" +
            this.questionCount +
            " câu. Xin hãy hoàn thành bài thi?"
        );
      } else {
        confirm = window.confirm("Xác nhận nộp bài");
        if (confirm) {
          this.submit();
        }
      }
    },
    submit: function () {
      var vm = this;
      this.loading = true;
      var data = {
        testId: this.exam.id,
        examId: this.examDetail.id,
        lesson: step,
        answer1: this.answer1,
        answer2: this.answer2,
        answer3: this.answer3,
      };
      $.post(vm.url + "/test-online/submit", data, function (res) {
        vm.loading = false;
        if (res.code == 200) {
          deleteCookie("dmr_timer_" + vm.exam.id);
          window.location.reload();
        } else {
          alert(res.msg);
        }
      }).fail(function () {
        alert("Đã xảy ra lỗi");
      });
    },
  },
});
