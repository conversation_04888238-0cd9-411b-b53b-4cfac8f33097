const options = {
  playbackRates: [0.75, 1, 1.25, 1.5, 2],
  preload: "auto",
  controlBar: {
    volumeMenuButton: {
      vertical: true,
      inline: false,
      volumeBar: {
        vertical: true,
      },
      volumeLevel: true,
    },
  },
};

var x = null;

var mainLuyenDe = new Vue({
  el: "#main-lesson-ld",

  data: function () {
    return {
      url: window.location.origin,

      tab: (getCookie("LD_TAB_"+lessonInfo.id) != '') ? getCookie("LD_TAB_"+lessonInfo.id) : 1, //123 //tab bài kt, chữa đề và lịch sử
      part: 1, //1,2,3 3 phần của bài tổng hợp
      questions: [],
      numOfQuestions: 0,
      solutions: [],
      ldth: lgmenu == "ldth" ? true : false,
      userAnswers: {},

      mp3: null,
      pdf: null,

      countDownTime: "00:00",
      timeColor: "#008000",

      showResult: false,
      result: null,
      score1: 0,
      score2: 0,
      score3: 0,
      totalScore: 0,

      //mp4
      player: null,
      currentMediaName:
        getCookie("LD_MP4_" + lessonInfo.id) != ""
          ? getCookie("LD_MP4_" + lessonInfo.id)
          : atob(atob(atob(mdaId))),
      currentResult: null,
      currentServerId: listServers[0].id,
      currentServerUrl: listServers[0].url,
      currentQuality: "720p", // 720p, 480p, 360p
      currentTime: 0,
      listServers: listServers,
      lessonInfo: lessonInfo, //chỗ có nhiều video
      courseUrl: courseUrl,

      compareQuestions: [],
      history: [], //lịch sử các lần làm bài
      currentSelections: {},
      currentQBlock: null,
      showOnlyWrong: false,
      currentHistoryIndex: null,
    };
  },
  computed: {

  },
  watch: {
    tab: {
      handler: function(newTab) {
        var vm = this;
        setCookie("LD_TAB_" + lessonInfo.id, newTab, 10);
        $('.dmrx-player').hide();
        if (newTab == 2) {
          $('.dmrx-player').show();
          ///mp4/////////////////////////////////////////
          // hiển thị nút chọn server di động
          var width = window.innerWidth > 0 ? window.innerWidth : screen.width;
          if (width <= 768) {
            $(".select-localtion-select").css("display", "block");
            $(".server-item").css("display", "none");
          }

          // console.log("Tên video: ", vm.currentMediaName);

          //check lại cài đặt quality
          if (getCookie("v_quality")) {
            if (getCookie("v_quality") != vm.currentQuality) {
              vm.currentQuality = getCookie("v_quality");
            }
          }
          vm.checkServerLocation();
          //bài học chưa nhập video chữa đề
          // console.log("init video", vm.currentMediaName);
          if (vm.currentMediaName != "empty") {
            setTimeout(function() {
              vm.initVideo()
            }, 300);
          }
          // vm.clickPlayVideo();
        }
        if (newTab == 3) this.loadHistory();
        if (newTab != 2 && this.player) this.player.pause();
      },
      deep: true,
    },
    part: {
      handler: function(newPart) {
        // console.log("change part", newValue);
        $(".questions-container").css("display", "none");
        $(".type_ld_" + newPart).css("display", "block");
        $("table").addClass("reading-question");
      },
      deep: true,
    },
    userAnswers: {
      handler(newValue) {
        var vm = this;

        // console.log("userAnswers ", JSON.stringify(newValue));
        setCookie("SAVE_ANSWER_" + lessonInfo.id, JSON.stringify(newValue), 5); //lưu đáp án vào cookie trong 5p
      },
      deep: true,
    },
  },
  created () {
    window.addEventListener('scroll', this.handleScroll);
  },
  unmounted () {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    isReadingBlockInViewport: function(el) {
      var rect = el.getBoundingClientRect();
      return (
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
          rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
    },
    pinQuestion: function(question) {
      this.currentQBlock = {
        id: question.id,
        value: question.value
      };
    },
    isLongQuestion: function(question) {
      return question.value.includes('table') || question.value.includes('img');
    },
    handleScroll: function(event) {
      var vm = this;
      if (window.scrollY == 0) {
        vm.currentQBlock = null;
      }
    },
    //khởi tạo dữ liệu
    initData: function () {
      var vm = this;
      var typeld = 1;
      for (var i = 0; i < lesson_tasks.length; i++) {
        if (lesson_tasks[i].type == 1 || lesson_tasks[i].type == 3) {
          vm.questions.push(lesson_tasks[i]);
          vm.compareQuestions.push(lesson_tasks[i]);
        }
        if (lesson_tasks[i].type == 5)
          vm.mp3 = JSON.parse(lesson_tasks[i].value).link; //lấy ra mp3
        if (lesson_tasks[i].type == 8) vm.pdf = lesson_tasks[i].value; //lấy ra pdf

        if (lesson_tasks[i].type == 3) vm.numOfQuestions += 1; //đếm số lượng câu hỏi
      }
      if (vm.ldth == true) {
        $(".questions-container").css("display", "none");
        $(".type_ld_" + vm.part).css("display", "block");
      }

      // console.log("mp3: ", vm.mp3);
      // console.log("pdf: ", vm.pdf);
      // console.log("questions", vm.questions);
    },

    //lấy ra các kết quả bài test của lesson này
    loadHistory: function () {
      var vm = this;
      $.post(
        window.location.origin + "/lesson/load-result-history",
        {
          userId: authUserId, //khai báo ngoài blade
          lessonId: parseInt(lessonInfo.id),
        },
        function (response) {
          vm.history = response;
        }
      );
    },

    //so sánh kết quả đã làm vs đáp án
    compareResult: function (index) {
      var vm = this;
      this.showOnlyWrong = false;
      $(".a-l-n").css("border", "none");
      $(".a-l-n").css("padding", 0);

      vm.currentHistoryIndex = index;
      vm.currentSelections = JSON.parse(vm.history[index].data);
      // console.log("compare", vm.currentSelections);
      _.forEach(vm.currentSelections, function (value, key) {
        $("#c-q-" + value).css("border", "1px solid");
        $("#c-q-" + value).css("padding", "2px 8px 1px 8px");
      });
    },

    //hàm đếm ngược thời gian
    countDown: function (deltaTime) {
      var vm = this;

      var t = deltaTime;
      if (t == 0) t = 300000; //nếu thời gian < 5 phút gán thời gian = 5 phút

      // console.log("delta time", deltaTime);
      x = setInterval(function () {
        t = t - 1000;
        var hours = Math.floor((t % 86400000) / 3600000);
        var minutes = Math.floor((t % 3600000) / 60000);
        if (minutes < 10) minutes = "0" + minutes;

        var seconds = Math.floor((t % 60000) / 1000);
        if (seconds < 10) seconds = "0" + seconds;

        if (hours <= 0) vm.countDownTime = minutes + ":" + seconds;
        else if (hours > 0)
          vm.countDownTime = hours + ":" + minutes + ":" + seconds;

        //đổi màu thời gian cảnh báo người thi
        if (t < 300000) vm.timeColor = "#e33512";
        if (t < 0) {
          //nếu vừa hết giờ sau tối đa 1.8s -> tự động nộp bài
          if (t > -1800) vm.submitDataExercise();

          clearInterval(x);
          vm.countDownTime = "HẾT GIỜ";

          $("#submit-result").css("display", "none"); //ẩn nút nộp bài
        }
      }, 1000);
    },

    //ấn nút nộp bài kiểm tra -> chấm điểm luôn
    submitDataExercise: function () {
      var vm = this;

      // console.log("userAnswers", vm.userAnswers);
      //nếu chưa chọn câu nào
      if (_.isEmpty(vm.userAnswers)) {
        alert("Bạn chưa chọn đáp án");
        return;
      }

      vm.totalScore = 0;
      vm.score1 = 0;
      vm.score2 = 0;
      vm.score3 = 0;

      _.forEach(vm.userAnswers, function (value, key) {
        var checkQuest = _.find(vm.questions, { id: parseInt(key) });
        var checkAns = _.find(checkQuest.answers, { id: parseInt(value) });

        //nếu phương án lựa chọn dúng -> tính điểm
        if (parseInt(checkAns.grade) > 0) {
          vm.totalScore += parseInt(checkAns.grade);
          if (checkQuest.type_ld == "1") vm.score1 += parseInt(checkAns.grade);
          if (checkQuest.type_ld == "2") vm.score2 += parseInt(checkAns.grade);
          if (checkQuest.type_ld == "3") vm.score3 += parseInt(checkAns.grade);
        }
      });

      var passed = 0;
      var scoreData = null;

      //nếu là luyện đề tổng hợp
      if (vm.ldth == true) {
        var sum = vm.score1 + vm.score2 + vm.score3;
        if (vm.totalScore != sum) vm.totalScore = sum;

        scoreData = { s1: vm.score1, s2: vm.score2, s3: vm.score3 };

        if (
          vm.score1 >= 19 &&
          vm.score2 >= 19 &&
          vm.score3 >= 19 &&
          vm.totalScore >= lessonDetail.pass_marks
        )
          passed = 1;
        //nếu là luyện đề kỹ năng
      } else {
        //nếu lớn hơn điểm đạt
        if (vm.totalScore >= lessonDetail.pass_marks) passed = 1;
      }

      // console.log("score1", vm.score1);
      // console.log("score2", vm.score2);
      // console.log("score3", vm.score3);
      // console.log("totalScore", vm.totalScore);
      // console.log("lessonDetail.pass_marks", lessonDetail);

      $.post(
        window.location.origin + "/lesson/submit-exam-ld",
        {
          typeld: vm.ldth, // {true, false} là luyện đề tổng hợp hayy kỹ năng
          userId: authUserId, //khai báo ngoài blade
          lessonId: parseInt(lessonDetail.id),
          answers: vm.userAnswers,
          duration: 1000, //thời gian làm bài
          scoreData: scoreData,
          myScore: vm.totalScore,
          passed: passed,
          totalGrade: lessonDetail.total_marks,
          isLdth: vm.ldth,
        },
        function (response) {
          vm.showResult = true;
          vm.result = response;
          if (scoreData != null) {
            vm.result.score_data = JSON.parse(vm.result.score_data);
            vm.result.passed = parseInt(vm.result.passed);
          }

          // console.log("vm.result: ", vm.result);

          //xóa cookie lưu kết quả
          vm.userAnswers = {};
        }
      );
    },

    closePopupResult: function () {
      var vm = this;
      vm.userAnswers = {};
      vm.showResult = false;
      vm.result = null;

      clearInterval(x);
      vm.countDown(parseInt(lessonDetail.max_duration) * 60000); //chạy lại thời gian
    },

    //lấy ra text từ html
    switchTab: function (tab) {
      this.tab = tab;
      document.body.scrollTop = 0; // For Safari
      document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera

      if (tab == 1) {
        if (lessonDetail && lessonDetail.group_id) {
          setTimeout(function () {
            var groupItem = $("#group-ld-" + lessonDetail.group_id);
            if (groupItem && (groupItem.attr("aria-expanded") == false || groupItem.attr("aria-expanded") == 'false')) {
              $("#group-ld-" + lessonDetail.group_id).trigger('click');
            }
          }, 500);
        }
      }
    },

    //load từng phần của 3 bài tổng hợp
    loadPart: function (part) {
      this.part = part;
      document.body.scrollTop = 0; // For Safari
      document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
    },

    loadMp4: function (file) {
      var vm = this;
      var now = new Date();

      this.currentResult = null;

      // console.log("load mp4", file);

      setCookie("LD_MP4_" + lessonInfo.id, file, 10);

      vm.currentMediaName = file;

      // vm.initVideo();
      //khởi tạo video player
      var src =
          vm.currentServerUrl +
          "/" +
          vm.currentQuality +
          "/" +
          vm.currentMediaName +
          "/index.m3u8?" +
          now.getMinutes();

      console.log('src..........398', src)
      $.ajax({
        url: "/video/get-link?url=" + src,
        type: "GET",
        success: function (res) {
          console.log('res..............402', res)
          vm.player.src({
            src: res,
            type: "application/x-mpegURL",
            withCredentials: false,
          });
          vm.clickPlayVideo();
        },
      });
      document.body.scrollTop = 0; // For Safari
      document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
    },
    loadResult: function(content) {
      if (this.player) this.player.pause();
      this.currentMediaName = null;
      this.currentResult = content;
      document.body.scrollTop = 0; // For Safari
      document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
    },
    ////////video//////////////////////////////////////////
    /* chọn server khác di động */
    changeServerBySelectTag: function () {
      var vm = this;
      setCookie("v_location", vm.currentServerId, 30);
      location.reload();
    },

    /* chọn server khác */
    changeServerLocaltion: function (id) {
      var vm = this;
      var now = new Date();
      //đặt lại cookie cài đặt nhớ trong 30 ngày
      setCookie("v_location", id, 30);
      setCookie("current_time_" + lessonDetail.id, vm.player.currentTime(), 10);
      this.checkServerLocation();
      var src =
        vm.currentServerUrl +
        "/" +
        vm.currentQuality +
        "/" +
        vm.currentMediaName +
        "/index.m3u8?" +
        now.getMinutes();
      console.log('src..........446', src)

      $.ajax({
        url: "/video/get-link?url=" + src,
        type: "GET",
        success: function (res) {
          console.log('res..............450', res)
          vm.player.src({
            src: res,
            type: "application/x-mpegURL",
            withCredentials: false,
          });
          vm.clickPlayVideo();
        },
      });
      // location.reload();
    },

    /* chọn video chất lượng khác */
    changeQuality: function (quality) {
      var vm = this;
      var now = new Date();
      //đặt lại cookie cài đặt nhớ trong 30 ngày
      vm.currentQuality = quality;
      setCookie("v_quality", quality, 30);
      setCookie("current_time_" + lessonDetail.id, vm.player.currentTime(), 10);

      var src =
        vm.currentServerUrl +
        "/" +
        vm.currentQuality +
        "/" +
        vm.currentMediaName +
        "/index.m3u8?" +
        now.getMinutes();
      console.log('src..........481', src)

      $.ajax({
        url: "/video/get-link?url=" + src,
        type: "GET",
        success: function (res) {
          console.log('res..............484', res)
          vm.player.src({
            src: res,
            type: "application/x-mpegURL",
            withCredentials: false,
          });
          vm.clickPlayVideo();
        },
      });

      // location.reload();
    },

    // tua lại
    fastRewind: function () {
      var vm = this;
      vm.player.currentTime(vm.player.currentTime() - 10);
      if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        )
      ) {
        $(".left-fast-icon").css("display", "inline");
        setTimeout(function () {
          $(".left-fast-icon").css("display", "none");
        }, 1200);
      }
    },

    // tua đi
    fastForward: function () {
      var vm = this;
      vm.player.currentTime(vm.player.currentTime() + 10);
      if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        )
      ) {
        $(".right-fast-icon").css("display", "inline");
        setTimeout(function () {
          $(".right-fast-icon").css("display", "none");
        }, 1200);
      }
    },

    initVideo: function () {
      var vm = this;

      vm.player = videojs(
        "myplayer_" + playerId,
        options,
        function onPlayerReady() {
          // console.log('onPlayerReady', this);
        }
      );

      var now = new Date();

      videojs("myplayer_" + playerId).ready(function () {
        this.hotkeys({
          volumeStep: 0.1,
          seekStep: 10,
          enableMute: true,
          enableFullscreen: true,
          enableNumbers: false,
          enableVolumeScroll: true,
          enableHoverScroll: true,
          alwaysCaptureHotkeys: true,
          fill: true,
          fluid: true,
          preload: "meta",
          html5: {
            hls: {
              enableLowInitialPlaylist: true,
              smoothQualityChange: true,
              overrideNative: true
            }
          },
          // Kết hợp tiến lùi với Ctrl để tiến xa hơn
          seekStep: function (e) {
            if (e.ctrlKey && e.altKey) {
              return 5 * 60;
            } else if (e.shiftKey) {
              return 60;
            } else if (e.altKey) {
              return 30;
            } else {
              return 10;
            }
          },

          // Enhance existing simple hotkey with a complex hotkey
          fullscreenKey: function (e) {
            // fullscreen with the F key or Ctrl+Enter
            return e.which === 70 || (e.ctrlKey && e.which === 13);
          },

          // Custom Keys
          customKeys: {
            // Thêm phím custom đơn
            simpleKey: {
              key: function (e) {
                // Bắt sự kiện khi ấn S
                return e.which === 83;
              },
              handler: function (player, options, e) {
                // Example
                if (player.paused()) {
                  player.play();
                } else {
                  player.pause();
                }
              },
            },

            // Thêm tổ hợp phím custom
            complexKey: {
              key: function (e) {
                // Bắt sự kiện Ctrl + D để toggle mute
                return e.ctrlKey && e.which === 68;
              },
              handler: function (player, options, event) {
                // Example
                if (options.enableMute) {
                  player.muted(!player.muted());
                }
              },
            },

            // Sửa lại các phím số để seek theo yêu cầu
            numbersKey: {
              key: function (event) {
                // Override number keys
                return (
                  (event.which > 47 && event.which < 59) ||
                  (event.which > 95 && event.which < 106)
                );
              },
              handler: function (player, options, event) {
                // Do not handle if enableModifiersForNumbers set to false and keys are Ctrl, Cmd or Alt
                if (
                  options.enableModifiersForNumbers ||
                  !(event.metaKey || event.ctrlKey || event.altKey)
                ) {
                  var sub = 48;
                  if (event.which > 95) {
                    sub = 96;
                  }
                  var number = event.which - sub;
                  player.currentTime(player.duration() * number * 0.1);
                }
              },
            },

            emptyHotkey: {
              // Empty
            },

            withoutKey: {
              handler: function (player, options, event) {
                console.log("withoutKey handler");
              },
            },

            withoutHandler: {
              key: function (e) {
                return true;
              },
            },

            malformedKey: {
              key: function () {
                console.log(
                  "I have a malformed customKey. The Key function must return a boolean."
                );
              },
              handler: function (player, options, event) {
                //Empty
              },
            },
          },
        });
        this.on("timeupdate", function () {
          vm.currentTime = vm.player.currentTime();
        });
      });

      //khởi tạo video player
      var src =
        vm.currentServerUrl +
        "/" +
        vm.currentQuality +
        "/" +
        vm.currentMediaName +
        "/index.m3u8?" +
        now.getMinutes();
      console.log('src..........683', src)

      $.ajax({
        url: "/video/get-link?url=" + src,
        type: "GET",
        success: function (res) {
          console.log('res..............685', res)
          vm.player.src({
            src: res,
            type: "application/x-mpegURL",
            withCredentials: false,
          });
        },
      });
      // console.log("Click play hls", src);
    },

    clickPlayVideo: function () {
      var vm = this;

      // console.log("Click play hls", vm.currentMediaName);

      $(".movie-play").css("display", "none");
      $("#myplayer_" + playerId).css("display", "block");

      //nếu tồn tại ghi nhớ thời gian chạy video
      if (getCookie("current_time_" + lessonDetail.id)) {
        var seektime = getCookie("current_time_" + lessonDetail.id);
        //chuyển seekbar tới vị trí chạy video
        vm.player.currentTime(seektime);
      }

      vm.player.play();

      //cài vòng lặp ghi nhớ current time vào cookie sau mỗi 3s, không cần nữa vì có thể bắt event khi rời trang hoặc f5
      // setInterval(function () { setCookie('current_time_'+ lessonDetail.id, player.currentTime(), 10); }, 5000);

      // bắt sự kiện rời trang hoặc f5 và lưu current time vào cookie
      window.addEventListener("beforeunload", (event) => {
        setCookie(
          "current_time_" + lessonDetail.id,
          vm.player.currentTime(),
          10
        );
      });
    },
    checkServerLocation: function () {
      var vm = this;
      //check lại cài đặt localtion
      if (getCookie("v_location")) {
        var locationId = getCookie("v_location");
        if (locationId == listServers[0].id) {
          vm.currentServerId = listServers[0].id;
          vm.currentServerUrl = listServers[0].url;
        } else {
          vm.currentServerId = listServers[1].id;
          //ramdom để chia tải server
          var ran = Math.floor(Math.random() * Math.floor(2));
          if (ran == 0) vm.currentServerUrl = listServers[1].url;
          else vm.currentServerUrl = "https://vn.dungmori.com";
        }
      }
    },
    toggleShowOnlyWrong: function(event) {
      this.showOnlyWrong = event.target.checked;
    },
    isCorrect: function(question) {
      if (!question.answers) return false;
      var q = this.currentSelections[question.id];
      if (q) {
        var tmp = _.find(question.answers, ['id', parseInt(q)]);
        if (tmp && parseInt(tmp.grade) == 0) return true;
      }
      return false;
    },
  },

  mounted: function () {
    var vm = this;

    vm.initData();

    //chuyển hiển thị menu của đề tổng hợp
    // if (lgmenu == "ldth") {
    //   this.$root.$refs.luyende.changeTab(2);
    // }

    //fix lỗi hiện phát đầu
    if (vm.part == 1 && vm.ldth == true) {
      setTimeout(function () {
        $(".questions-container").css("display", "none");
        $(".type_ld_1").css("display", "block");
      }, 500);
    }

    //nếu ghi nhớ tab history
    if (vm.tab == 3) this.loadHistory();

    //đếm giờ
    vm.countDown(parseInt(lessonDetail.max_duration) * 60000);

    if (this.tab == 2) {
      ///mp4/////////////////////////////////////////
      // hiển thị nút chọn server di động
      var width = window.innerWidth > 0 ? window.innerWidth : screen.width;
      if (width <= 768) {
        $(".select-localtion-select").css("display", "block");
        $(".server-item").css("display", "none");
      }

      // console.log("Tên video: ", vm.currentMediaName);

      //check lại cài đặt quality
      if (getCookie("v_quality")) {
        if (getCookie("v_quality") != vm.currentQuality) {
          vm.currentQuality = getCookie("v_quality");
        }
      }
      vm.checkServerLocation();
      //bài học chưa nhập video chữa đề
      // console.log("init video", vm.currentMediaName);
      if (vm.currentMediaName != "empty") {
        vm.initVideo()
      }
      if (vm.pdf == null && vm.currentMediaName == "empty") {
        vm.loadResult(JSON.parse(lesson_otherVideo[0].value)[0].content);
      }
      // vm.clickPlayVideo();
    }
  },

  beforeDestroy: function () {
    if (this.player) {
      this.player.dispose();
    }
  },
});
