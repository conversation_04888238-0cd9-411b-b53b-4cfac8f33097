var ranking = new Vue({

    el: '#main-ranking-right',
    data: function () {
        return {

            url: window.location.origin, //đường dẫn host
            ranking : [],  //<PERSON><PERSON> sach top 100 theo tim kiem
            users   : [],  //danh sách user trong thứ hạng
            myId    : currentUserId, //Id của user hiện tại
            month   : lastMonth,
            year    : lastYear,
            level   : lastLevel,
            loading : false, //trạng thái loading
            myRank  : 0, //Rank cua user
            certificateUser: null, //người dùng load trong ô chứng chỉ,
            exams: exams, // ten cua ki thi
            examId: examId, // id ky thi
            isChangeExam: false, // kiem tra co thay doi ky thi hay k
            examInMonth: null, // lay ki thi trong thang
            isExamNull: false, // bien kiem tra ky thi co null k?
        }
    },

    methods: {

        //bắt sự kiện thay đổi điều kiện lọc
        changeCondition: function(type, val, target){
            var vm = this;

            if(type == 'year') vm.year = val;
            else if(type == 'month') {
                vm.month = val;
                $('#monthSelect').html(target.text);
            }
            else vm.level = val;

            vm.loading = true;
            vm.exams = null;
            vm.examId = null;
            vm.isChangeExam = false;

            setTimeout(function(){

                //gọi hàm tìm kiếm để update kết quả
                vm.searchRanking();
            }, 500);
        },

        changeExam: function(examId, name) {
            this.exams = name;
            this.loading = true;
            this.examId = examId;
            this.isChangeExam = true;

            var vm = this;
            setTimeout(function () {
                vm.searchRanking();
            }, 500);
        },

        //tìm kiếm ranking theo điều kiện lọc
        searchRanking: function(){

            var vm = this;
            var data = {
                'month' : vm.month,
                'year'  : vm.year,
                'level' : vm.level,
                'examId': vm.examId,
                'isChangeExam': vm.isChangeExam
            }

            $.post(vm.url +"/thi-thu/search-ranking", data, function(response){

                vm.ranking = response.ranking;
                vm.users   = response.listUsers;
                vm.myRank  = 0; //kiến tạo lại giá trị my ranking
                vm.examInMonth = response.examInMonth;

                console.log("response", response);

                //neu co cac ky thi trong thang/khoa hoc do thi isExamNull = false
                if (response.examInMonth !== null && response.examInMonth[0].exams !== null && !vm.isChangeExam) {
                    vm.exams = response.examInMonth[0].exams.name;
                    vm.isExamNull = false;
                } else if (response.examInMonth == null) {
                    vm.isExamNull = true;
                } else if (response.examInMonth[0].exams == null) {
                    vm.isExamNull = true;
                }
                //nếu có kết quả trả về -> join kết quả
                if(vm.users != null && vm.ranking !=null){

                    var countUser = vm.users.length;
                    for(var i = 0; i < vm.ranking.length; i++){

                        //cập nhật lại chỉ số thứ tự của mình
                        if(vm.myId == vm.ranking[i].user_id){

                            vm.myRank = i + 1; //vì i chạy từ 0
                            // console.log("rank của tôi", vm.myRank);
                        }

                        for(var n = 0; n < countUser; n++){
                            if(vm.users[n].id == vm.ranking[i].user_id){
                                vm.ranking[i].userName   = vm.users[n].name;
                                vm.ranking[i].userAvatar = vm.users[n].avatar;
                                break;
                            }
                        }
                    }
                }

                vm.loading = false;

                // console.log("dữ liệu tìm kiếm", response);
                // console.log("dữ liệu sau khi join", vm.ranking);
            });
        },

        //xem chi tiết chứng chỉ kết quả
        showCertificate: function(index){

            var vm = this;
            vm.certificateUser = vm.ranking[index];

            setTimeout(function(){
                $('.fancybox').fancybox().trigger('click');
            }, 100);

        },

        //hàm hiển thị cert dành riêng cho mobile
        showCertificateMobile: function(index){

            var vm = this;

            //nếu là mobile
            if(screen.width <= 768){
                vm.certificateUser = vm.ranking[index];

                setTimeout(function(){
                    $('.fancybox').fancybox().trigger('click');
                }, 100);
            }
        },

        //hien thi index sao cho đúng
        printIndex: function(index){
            return parseInt(index) + 1;
        },

        //hiển thị rank
        printRank: function(index){
            var vm = this;
            vm.myRank = parseInt(index) + 1;
            return vm.myRank ;
        },

        //hover hiển thị kết quả
        mouseOver: function(id){
            var vm = this;
            vm.check = id;
            $('.fancybox').fancybox().trigger('click');
        },

        //hiển thị date
        printDate: function(date){
            var vm = this;
            return vm.year + '年 ' + vm.month + '月 ' + date.substring(8, 10) + '日';
        },

        //hiển thị tổng điểm
        printTotal: function(a, b, c){
            return a + b + c;
        },

        //hiển thị kết quả đỗ trượt
        printResult: function(course_name, a, b, c){

            if(['N1','N2','N3'].includes(course_name)) {
                if(a < 19 || b < 19 || c < 19) return false; //true bằng đỗ
            } else {
                if(a < 38 || c < 19) return false; //true bằng đỗ
            }

            var vm = this;

            if(course_name == 'N5' && vm.printTotal(a, b, c) >= 80){
                return true;
            }else if(course_name == 'N4' && vm.printTotal(a, b, c) >= 90){
                return true;
            }else if(course_name == 'N3' && vm.printTotal(a, b, c) >= 95){
                return true;
            }else if(course_name == 'N2' && vm.printTotal(a, b, c) >= 90){
                return true;
            }else if(course_name == 'N1' && vm.printTotal(a, b, c) >= 100){
                return true;
            }

            return false;
        },
    },

    mounted: function() {

        var vm = this;

        vm.loading = true;
        vm.searchRanking();
    }
});
