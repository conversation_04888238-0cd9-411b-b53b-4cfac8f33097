// thư viện lấy ra thông tin agent
var myclient = new ClientJS();

var user = new Vue({
  el: '#login-form',
  data: {
    url: window.location.origin,
    currentBrowser: myclient.getBrowser(),
    currentOsName: myclient.getOS(),
    currentFingerprint: myclient.getFingerprint(),

    buttonState: true,
    error: null,
    countWrongPass: 0

  },
  methods: {

    //hàm refresh captcha
    changeCaptchaLogin: function(){
      const newImage = `${this.url}/account/refresh-captcha?${Date.now()}`
      $("#captcha_login_change").attr("src", newImage);
    },

    /* đăng nhập */
    login: function(){
      if (!$('#email').val() || !$('#password').val()) {
        return;
      }
      var vm = this;
      vm.buttonState = false;
      var loginForm = $("#login-form");
      var formData = loginForm.serialize() + '&countWrongPass=' + vm.countWrongPass;

      //thêm lưu vết người dùng vào chuỗi xác thực
      formData += '&browser='+vm.currentBrowser + '&os='+vm.currentOsName+'&fingerprint='+vm.currentFingerprint;

      //lấy params trên url hiện tại
      var url = new URL(window.location.href);
      var params = new URLSearchParams(url.search);
      var redirect = params.get('redirect');

      // console.log(formData);
      setTimeout(function(){
        $.ajax({
          url:window.location.origin+'/login', type:'POST', data:formData,
          error: function (errors) {

            // console.log(errors.responseJSON);
            vm.buttonState = true;

            if(errors.responseJSON.errors != null) {
              vm.error = errors.responseJSON.errors;
            }
            if (errors.responseJSON.count_wrong_pass != null) {
                vm.countWrongPass = errors.responseJSON.count_wrong_pass;
            }
            vm.changeCaptchaLogin();
          },
          success:function(response){

            console.log("redirect: ", redirect);
            if (redirect) {
              console.log("redirect: ", redirect);
              window.location.href = redirect;
              return;
            }
                //console.log(response);
                location.reload();
          }
        });
      }, 1000);
    },

    /* ẩn thông báo lỗi */
    hideError: function(){
      this.error = null;
    }

  },
  mounted: function() {}
});
