var notiCount = new Vue({
  el: '#auth-container',
  data: function () {
    return {
      countUnreadMess: 0
    };
  },
  methods: {
    updateMessageCount: function (message) {
      if (message.isGroupChat === false) {
        if (message.receiverId == chatUser) {
          this.countUnreadMess++;
        }
      } else {
        if (message.peoples && message.peoples.includes(chatUser)) {
          this.countUnreadMess++;
        }
      }
    }
  },
  mounted: function () {
    var vm = this;

    vm.countUnreadMess = parseInt(countMess);
  }
});
