
var community = new Vue({

    el: '#main-profile-center',
    data: {

        url: window.location.origin,
        crrurl: location.protocol + '//' + location.host + location.pathname,
        cdn: cdn,

        postnew_type: (getCookie("postnew_type") != '') ? getCookie("postnew_type") : 0,
        postnew_btn: 0, //trạng thái nút đăng
        postnew_error: '',

        //nội dung sửa
        postEditing: null,

        orderBy: (getCookie("order_by") != '') ? getCookie("order_by") : 'latest',
        listGroups: [],
        listPosts: [],
        listTags: [],
        searchKeyword: "",
        currTag: null,
        params:{
            page: 1,
            limit: 10
        },
        theEnd: false,
        showLoading: true
    },

    watch: {
        postnew_type: {
            handler(newValue){
                setCookie('postnew_type', newValue, 50);
            },
            deep: true
        },
        orderBy: {
            handler(newValue){
                setCookie('order_by', newValue, 50);
                // this.initPosts(); //load lại bài đăng theo thứ tự thay đổi
                location.reload();
            },
            deep: true
        },
    },

    methods: {

        mergePostsAndGroups: function(){
            var vm = this;

            vm.listPosts.forEach(function (item) {
                if(item.group == null){

                    //nếu là bài đăng trong cộng đồng chung
                    if(item.group_id == 1){
                        item.group = {};
                        item.group.id = 1;
                        item.group.name = "Cộng đồng chung";
                    }else{
                        item.group = _.find(vm.listGroups, {id: item.group_id});
                    }
                }
            });

        },

        initPosts: function(){

            var vm = this;

            //check lọc theo chủ đề
            var fullUrl = new URL(window.location.href);
            var tagId = fullUrl.searchParams.get("tag");
            var key = fullUrl.searchParams.get("q");

            if(tagId) vm.params.tagId = tagId;
            if(key) vm.params.text = key;

            if(getCookie("order_by") == 'featured') vm.params.orderBy = 'total_likes';
            else vm.params.orderBy = 'id';


            //lấy hết mygroups
            $.get(api +"/api/community/groups/get-user-groups", { page: 1}, function(res){

                console.log("groups:", res);
                vm.listGroups = res;


                //lấy bài đăng
                $.get(api +"/api/community/posts/posts-by-user", vm.params, function(res){


                    vm.listPosts = res.data;

                    vm.mergePostsAndGroups();

                    console.log("posts:", res);

                    vm.$nextTick(() => {

                        $('.slick-gallery').slick({
                            dots: true,
                            infinite: true,
                            speed: 300,
                            slidesToShow: 1,
                            centerMode: false,
                            variableWidth: true
                        });

                    });

                    if(res.data.length < vm.params.limit) {
                        vm.showLoading = false;
                        vm.theEnd = true;
                    }

                });

            });


        },

        loadMorePosts: function(){

            var vm = this;

            vm.showLoading = true;

            vm.params.page++;

            setTimeout(function() {

                if(vm.theEnd == false){

                    //lấy bài đăng
                    $.get(api +"/api/community/posts/posts-by-user", vm.params, function(res){

                        console.log("load more posts: page", vm.params.page);
                        // vm.listPosts = res;
                        //nối thêm mảng tải thêm
                        if(res.data.length > 0){
                            vm.listPosts = vm.listPosts.concat(res.data);
                            vm.showLoading = false;

                            vm.mergePostsAndGroups();

                            vm.$forceUpdate();

                            vm.$nextTick(() => {

                                for(var i = 0; i< res.data.length; i++){
                                    var posts = res.data[i];
                                    if(posts.type == 0 && posts.data != null && posts.data.length >= 3){

                                        console.log("slickxxxxx", posts.id);
                                        $('#slick-'+ posts.id).slick({
                                            dots: true,
                                            infinite: true,
                                            speed: 300,
                                            slidesToShow: 1,
                                            centerMode: false,
                                            variableWidth: true
                                        });
                                    }
                                }
                            });

                        }

                        else{
                            vm.theEnd = true;
                            vm.showLoading = false;
                        }
                    });
                }
            }, 1500);


        },


        //in ra thông tin có dấu cách
        renderContent: function(info){

            var result = _.escape(info);


            result = result.replace('<', '&#60;');
            result = result.replace('>', '&#62;');

            //xử lý xuống dòng
            // result =  info.replace(new RegExp('\r?\n','g'), '<br />');

            var re = /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/ig;
            return result.replace(re, function(match, lParens, url) {
              var rParens = '';
              lParens = lParens || '';

              // Try to strip the same number of right parens from url
              // as there are left parens.  Here, lParenCounter must be
              // a RegExp object.  You cannot use a literal
              //     while (/\(/g.exec(lParens)) { ... }
              // because an object is needed to store the lastIndex state.
              var lParenCounter = /\(/g;
              while (lParenCounter.exec(lParens)) {
                  var m;
                  // We want m[1] to be greedy, unless a period precedes the
                  // right parenthesis.  These tests cannot be simplified as
                  //     /(.*)(\.?\).*)/.exec(url)
                  // because if (.*) is greedy then \.? never gets a chance.
                  if (m = /(.*)(\.\).*)/.exec(url) ||
                          /(.*)(\).*)/.exec(url)) {
                      url = m[1];
                      rParens = m[2] + rParens;
                  }
              }
              return lParens + "<a href='" + url + "' target='_blank'>" + url + "</a>" + rParens;
            });
        },


        //in ra định dạng ngày giờ đẹp
        prettyDate: function(t){
            const event = new Date(t);
            return event.toLocaleDateString('pt-PT');
        },
        prettyTime: function(t){
            const event = new Date(t);
            return event.getHours() + ":"+ event.getMinutes();
        },

        //in ra ngày hết hạn
        prettyExpired: function(t){
            return new Date(t).toLocaleDateString('pt-PT');
        },

        //in ra ngày hết hạn
        prettyExpired: function(t){
            return new Date(t).toLocaleDateString('pt-PT');
        },

        expandContent: function(pid){
            $("#posts-content-"+pid).css('max-height', 'none');
            $("#expand-content-"+pid).css('display', 'none');
        },

        searchPosts: function(){
            var vm = this;

            console.log('tìm kiếm', vm.searchKeyword);

            var delayTimer;

            clearTimeout(delayTimer);

            delayTimer = setTimeout(function() {

                $.post(window.location.origin +"/posts/posts-by-user", {
                    'key'  : vm.searchKeyword
                }, function(response){
                    console.log(response);
                    vm.listPosts = response.data;
                });
            }, 100)
        },

        //bắt sự kiện click chuột vào khu vực post bài
        focusTextArea: function(){

            // nới khu vực post bài rộng ra
            $(".post-bottom-area").css("display", "block");
        },


        //Hiển thị hộp thoại cho phép sửa ghi chú
        showEditPosts: function(item){

            var vm = this;

            vm.postEditing = item;

            //lấy hết tags về thì mới sửa dc
            $.get(api +"/api/community/tags", {groupId: item.group_id, limit: 1000}, function(res){


                console.log("tags:", res);
                vm.listTags = res;

                vm.$forceUpdate();

                vm.$nextTick(() => {

                    $('#edit-select2-hashtag').select2();

                    if(item.tags.length > 0){
                        var arr = [];
                        for (var i = 0; i< item.tags.length; i++){
                            arr.push(item.tags[i].id);
                        }
                        console.log(arr);
                        $('#edit-select2-hashtag').val(arr);
                        $('#edit-select2-hashtag').trigger('change');
                        vm.$forceUpdate();

                    }
                });
            });

        },

        //lưu sửa nội dung
        saveEditPosts: async function(id){

            var vm = this;

            vm.postnew_btn = 1;

            setTimeout(function() {

                //nội dung trống
                if($("#edit-posts-input-area").val() == ''){
                    vm.postnew_error = "* Vui lòng nhập đủ nội dung";
                    vm.postnew_btn = 0;
                    return;
                }

                //hastag là bắt buộc
                if($('#edit-select2-hashtag').select2('data').length == 0){
                    vm.postnew_error = "* Vui lòng chọn hashtag";
                    vm.postnew_btn = 0;
                    return;
                }

                const formData = new FormData();
                formData.append("postId", id);
                formData.append("content", $("#edit-posts-input-area").val());


                //nếu sửa kèm hashtags
                if($('#edit-select2-hashtag').select2('data').length > 0){
                    var arr = [];
                    for(var i = 0; i < $('#edit-select2-hashtag').select2('data').length; i++)
                        arr.push($('#edit-select2-hashtag').select2('data')[i].id);

                    formData.append("tags", JSON.stringify(Object.assign({}, arr)));
                }



                axios.post(api +'/api/community/posts/edit', formData, {
                    headers: {
                        'Content-Type': 'application/json',
                        "mimeType": "multipart/form-data",
                        'token': token
                    }
                }).then(function (response) {
                    console.log(response);
                    if(response.status == 200){

                        vm.postnew_btn = 2;

                        //đóng form nhập
                        setTimeout(function() { $.fancybox.close(); }, 1000);
                        location.reload();

                    }
                })
                .catch(function (error) {
                    console.log(error);
                });

            }, 1000);
        },

        //pin bài đăng
        pinThisPosts: function(id, pin){

            var vm = this;

            $.post(api +"/api/community/posts/pin", {

                'postId': id,
                'pin': true
            }, function(res){

                location.reload();

            });

        },

        // xóa 1 ghi chú
        deletePosts: function(id){

            $.post(api +"/api/community/posts/delete", {

                'postId'  : id
            }, function(res){

                if(res && res.status == 1){
                    $.notify("Đã xóa nội dung", "success");
                    $('#posts-'+id).fadeOut(500);
                }
                else
                    $.notify("Lỗi khi xóa");

            });
        },

        // báo cáo 1 ghi chú
        reportPosts: function(id){

            $.post(api +"/api/community/reports/create", {

                'postId'  : id,
                'contentId': 2,
                'note': 'nội dung không hợp lệ'
            }, function(res){

                if(res && res.id)
                    $.notify("Đã report thành công");
                else
                    $.notify("report lỗi");

            });
        },

        //hiển thị khung comment của bài post theo id
        showCommentBoxOfPosts: function(id){
            var vm = this;
            console.log("showCommentBoxOfPosts :", id );

            let i = _.findIndex(vm.listPosts, {id: id});
            if(vm.listPosts[i].showBoxComment == null) vm.listPosts[i].showBoxComment = 1;
            else vm.listPosts[i].showBoxComment = null;
            vm.$forceUpdate();
        },





        //thích bài đăng
        likePost: function(id){

            var vm = this;

            //nếu chưa đăng nhập
            if(authId == null) { $("#login-text-btn").click(); return; }

            $.post(api +"/api/community/likes/like", {

                'postId'  : id

            }, function(res){

                console.log("like :", res );

                //nếu like thành công
                if(res.id){

                    let i = _.findIndex(vm.listPosts, {id: id});
                    vm.listPosts[i].is_like = 1;
                     vm.listPosts[i].total_likes ++;
                    vm.$forceUpdate();

                }else{
                    $(".like-btn").notify("Lỗi");
                }

            });
        },

        //bỏ thích bài đăng
        dislikePost: function(id){

            var vm = this;

            $.post(api +"/api/community/likes/dislike", {

                'postId'  : id

            }, function(res){

                console.log("dislike :", res );

                //nếu dislikePost thành công
                if(res.status == 1){

                    let i = _.findIndex(vm.listPosts, {id: id});
                    vm.listPosts[i].is_like = 0;
                    vm.listPosts[i].total_likes --;
                    vm.$forceUpdate();

                }else{
                    $(".like-btn").notify("Lỗi");
                }

            });
        },



        //follow bài đăng
        followPost: function(id){

            var vm = this;

            //nếu chưa đăng nhập
            if(authId == null) { $("#login-text-btn").click(); return;}

            $.post(api +"/api/community/follows/post", {

                'postId'  : id

            }, function(res){

                console.log("followPost :", res );

                //nếu follow thành công
                if(res.id){

                    let i = _.findIndex(vm.listPosts, {id: id});
                    vm.listPosts[i].is_follow = 1;
                    vm.$forceUpdate();

                }else{
                    $(".follow-btn").notify("Lỗi");
                }


            });
        },

        //follow bài đăng
        unfollowPost: function(id){

            var vm = this;

            $.post(api +"/api/community/follows/unfollow-post", {

                'postId'  : id

            }, function(res){

                console.log("unfollowPost :", res );

                //nếu follow thành công
                if(res.status == 1){

                    let i = _.findIndex(vm.listPosts, {id: id});
                    vm.listPosts[i].is_follow = 0;
                    vm.$forceUpdate();

                }else{
                    $(".follow-btn").notify("Lỗi");
                }
            });
        },

        //hàm lưu lại giá trị sửa
        saveEditCmt: function(){

            var vm = this;

            var id = $("#edit-comment-id").val();
            var content = $("#edit-comment-area").val();
            console.log("hiện sửa cmt", id);

            $.post(api +"/api/community/comments/update",  {commentId: id, comment: content}, function(res){
                if(res.id == id){

                    //cập nhật giao diện
                    $("#cmt-content-div-"+ id).html(content);
                    $.fancybox.close();

                }else{
                    alert("lưu thất bại");
                }
            });
        },

        //cancel đóng popup nếu không muốn sửa cmt
        cancelEditCmt: function(){
            $.fancybox.close();
        },




    },

    mounted: function() {

        var vm = this;

        vm.initPosts();

    }
});


//cuộn trang
$(window).scroll(function() {
    if($(window).scrollTop() == $(document).height() - $(window).height()) {
        community.loadMorePosts();
    }
});
