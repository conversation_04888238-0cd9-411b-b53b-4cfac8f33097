var user = new Vue({

    el: '#fill_active_code',
    data: {
        url: window.location.origin,
        errors: [],
        loading: false
    },

    methods: {
        // xử lý sự kiện nhấn nút thay đổi captcha
        changeCaptcha: function() {
            const newImage = `${this.url}/account/refresh-captcha?${Date.now()}`
            $("#captcha-change").attr("src", newImage);
        },

        // xử lý sự kiện nhấn nút kích hoạt
        activeCourse: function() {
            var vm = this;
            var activeCode = $("#pin").val();
            var captcha = $("#captcha").val();
            var token = $('#csrf_token').val();

            if (activeCode == undefined || activeCode == null || activeCode == "" || captcha == undefined || captcha == null || captcha == "" ) {

                // xác thực thông tin trống
                vm.errors = [];
                vm.errors.push("Thông tin còn trống");
                return;
            }
            if (checkspecialSymbol(activeCode) || checkspecialSymbol(captcha)) {

                // xác thực ký tự đặc biệt
                vm.errors = [];
                vm.errors.push("Thông tin không được chứa ký tự đặc biệt");
                return;
            }

            if (vm.loading) {
                return;
            }
            vm.loading = true;

            var data = {
                '_token' : token,
                'activeCode' : activeCode,
                'captcha'    : captcha
            };
            // console.log(data);
            $.ajax({
                url: window.location.origin+"/account/active-course", type:"POST", data: data, async: true,
                beforeSend: function (xhr) { if (token) return xhr.setRequestHeader('X-CSRF-TOKEN', token);},
                error: function() {
                    vm.errors = [];
                    vm.errors.push("Có lỗi xảy ra. Vui lòng kiểm tra lại mã kích hoạt hoặc đổi mã xác thực. Lưu ý: thông tin không được chứa ký tự đặc biệt");
                    vm.changeCaptcha();
                    vm.loading = false;
                    return false;
                },
                success: function(response){

                    // console.log("Thành công : " + response);
                    vm.loading = false;
                    if (response == "invalid_code") {
                        vm.errors = [];
                        vm.errors.push("Mã kích hoạt không đúng");
                        vm.changeCaptcha();
                        return;
                    }
                    if (response == "voucher_used") {
                        vm.errors = [];
                        vm.errors.push("Mã kích hoạt đã sử dụng. Vui lòng sử dụng mã khác");
                        vm.changeCaptcha();
                        return;
                    }
                    if (response == "voucher_expired") {
                        vm.errors = [];
                        vm.errors.push("Mã cần xác thực, vui lòng liên hệ fanpage Dungmori để được kích hoạt");
                        vm.changeCaptcha();
                        return;
                    }

                    vm.errors = [];
                    $("#successModal").modal('toggle');
                }
            });
        },

        // sự kiện nhấn nút đóng sau khi nhập mã kích hoạt thành công
        gotoCourseUrl: function() {
            window.location.href = window.location.origin + '/account/courses';
        }
    },

    mounted: function() {
        var vm = this;

        // hiển thị các thành phần vue sau khi đã load dữ liệu xong
        $("#error-list").css("display", "block");
    }
});
