var blogVue = new Vue({
  el: "#blog-container",
  data: {
    listBlog: listBlog,
    minId: listBlog.length > 0 ? listBlog[listBlog.length - 1].id : 1000000,
    categoryId: cId,
    loading: false,
    endOfList: listBlog.length < 20
  },
  computed: {},
  methods: {
    getFriendlyTime(time) {
      var thisDateTime = time;
      var compareOnDay = time.substr(0, 10);

      const today = moment();
      if (today.format("YYYY-MM-DD") == compareOnDay) {
        // Nếu là trong ngày
        return "Hôm nay, lúc " + thisDateTime.substr(11, 5);
      } else {
        // Nếu không phải trong ngày

        var extraYear = time.substr(0, 4);
        var thisYear = today.format("YYYY");

        // Nếu là trong năm
        if (extraYear == thisYear) {
          return (
            thisDateTime.substr(8, 2) + " tháng " + thisDateTime.substr(5, 2) + ", lúc " + thisDateTime.substr(11, 5)
          );
        } else {
          return thisDateTime.substr(8, 2) + " tháng " + thisDateTime.substr(5, 2) + ", " + extraYear;
        }
      }
    },
    loadMoreBlogs: function () {
      var vm = this;

      if (vm.endOfList || vm.loading) {
        return;
      }

      vm.loading = true;
      let url = window.location.origin + `/bai-viet/load-more/${vm.minId}`;
      console.log("categoryId", vm.categoryId);
      if (vm.categoryId) {
        url += `?categoryId=${vm.categoryId}`;
      }
      $.get(url, function (data) {
        if (data.length < 20) {
          vm.endOfList = true;
        }
        vm.listBlog = [...vm.listBlog, ...data];
        vm.loading = false;
      }).fail(function (err) {
        console.log("error", err);
        vm.loading = false;
      });
    },
  },
  mounted: function () {
    console.log("this.listBlog", this.listBlog);
    $(".blog-hidden-before").css("display", "block");
  },
});
