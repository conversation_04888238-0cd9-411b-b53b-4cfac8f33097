// Huỷ đóng popup thông báo khi người dùng click vào popup
$(document).on('click', '.dropdown-menu', function (e) {
    e.stopPropagation();
});
import UserAnnouce from '../component/UserAnnouce';

Vue.component('user-annouce', UserAnnouce);

var announce = new Vue({
    el: '#announce',
});
Vue.directive('click-outside', {
    bind: function (el, binding, vnode) {
        el.clickOutsideEvent = function (event) {
            // here I check that click was outside the el and his children
            if (!(el == event.target || el.contains(event.target))) {
                // and if it did, call method provided in attribute value
                vnode.context[binding.expression](event);
            }
        };
        document.body.addEventListener('click', el.clickOutsideEvent)
    },
    unbind: function (el) {
        document.body.removeEventListener('click', el.clickOutsideEvent)
    },
});
announce.$mount('#announce');
