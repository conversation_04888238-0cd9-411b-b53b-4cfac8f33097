var videoQuestionContent = Vue.component('video-question-content', {
    props: ['question', 'currentTime'],
    template: '#video-modal-template',
    components: {
        pieTimer,
    },
    data: function () {
        return {
            modalQuestion: {answers: []},
        };
    },
    methods: {
        timeUp: function () {
            var vm = this;
            interactVideoPlayer.timeUp(vm.modalQuestion);
        },
        skip: function () {
            interactVideoPlayer.skip(this.modalQuestion);
        },
        selectAnswer: function (answer, question) {
            var vm = this;
            if (!question.time_up) {
                interactVideoPlayer.selectAnswer(answer, vm.modalQuestion);
            }
        },
        triggerCloseModal: function () {
            interactVideoPlayer.triggerCloseModal();
        }
    },
    mounted: function () {
        var vm = this;

        vm.modalQuestion = vm.question;

        if (vm.currentTime >= vm.modalQuestion.time_end) {
            vm.question.time_up = true;
        }
    }
});
