// Create Countdown
var Countdown = {
  
    // Backbone-like structure
    $el: $('#wdg-countdown'),
    
    // Params
    countdown_interval: null,
    total_seconds     : 0,
    
    // Initialize the countdown  
    init: function() {
      
      // DOM
          this.$ = {
          days  : this.$el.find('.bloc-time.days .figure'),
          hours  : this.$el.find('.bloc-time.hours .figure'),
          minutes: this.$el.find('.bloc-time.min .figure'),
          seconds: this.$el.find('.bloc-time.sec .figure')
         };
  
      // Init countdown values
      this.values = {
          days  : this.$.days.parent().attr('data-init-value'),
          hours  : this.$.hours.parent().attr('data-init-value'),
          minutes: this.$.minutes.parent().attr('data-init-value'),
          seconds: this.$.seconds.parent().attr('data-init-value'),
      };
      
      // Initialize total seconds
      this.total_seconds = (parseInt(this.values.days) * 24 * 60 * 60) + (parseInt(this.values.hours) * 60 * 60) + (parseInt(this.values.minutes) * 60) + parseInt(this.values.seconds);


      // Animate countdown to the end 
      this.count();    
    },
    
    count: function() {
      
      var that    = this,
          $day = this.$.days.eq(0),
          $hour = this.$.hours.eq(0),
          $min  = this.$.minutes.eq(0),
          $sec  = this.$.seconds.eq(0);
  
      
          this.countdown_interval = setInterval(function() {

          if(that.total_seconds > 0) {
            that.$el.find('.countdown-process').show();
            that.$el.find('.countdown-done').hide();

              --that.values.seconds;              
  
              if(that.values.minutes >= 0 && that.values.seconds < 0) {
  
                  that.values.seconds = 59;
                  --that.values.minutes;
              }
  
              if(that.values.hours >= 0 && that.values.minutes < 0) {
  
                  that.values.minutes = 59;
                  --that.values.hours;
              }

              if(that.values.days >= 0 && that.values.hours < 0) {
                  that.values.hours = 23;
                  --that.values.days;
              }
  
              // Update DOM values
              // Days
              that.checkHour(that.values.days, $day);
              
              // Hours
              that.checkHour(that.values.hours, $hour);
  
              // Minutes
              that.checkHour(that.values.minutes, $min);
  
              // Seconds
              that.checkHour(that.values.seconds, $sec);
  
              --that.total_seconds;
          }
          else {
            that.$el.find('.countdown-done').show();
            that.$el.find('.countdown-process').hide();
            that.$.days.parent().attr('data-init-value', 0);
            that.$.hours.parent().attr('data-init-value', 0);
            that.$.minutes.parent().attr('data-init-value', 0);
            that.$.seconds.parent().attr('data-init-value', 0);
            clearInterval(that.countdown_interval);
          }
      }, 1000);    
    },
    
    animateFigure: function($el, value) {
      
       var that         = this,
               $top         = $el.find('.top'),
           $bottom      = $el.find('.bottom'),
           $back_top    = $el.find('.top-back'),
           $back_bottom = $el.find('.bottom-back');
  
      // Before we begin, change the back value
      $back_top.find('span').html(value);
  
      // Also change the back bottom value
      $back_bottom.find('span').html(value);
  
      if(value >= 0) {

          // Then animate
          TweenMax.to($top, 0.8, {
              rotationX           : '-180deg',
              transformPerspective: 300,
                ease                : Quart.easeOut,
              onComplete          : function() {
      
                  $top.html(value);
      
                  $bottom.html(value);
      
                  TweenMax.set($top, { rotationX: 0 });
              }
          });
      
          TweenMax.to($back_top, 0.8, { 
              rotationX           : 0,
              transformPerspective: 300,
                ease                : Quart.easeOut, 
              clearProps          : 'all' 
          });    
      }
    },
    
    checkHour: function(value, $el) {
      
      var val       = parseInt(value),
          fig_value = $el.find('.top').html();
          fig_value = parseInt(fig_value);
  
      if(value >= 10) {
  
          // Animate only if the figure has changed
          if(fig_value !== val) this.animateFigure($el, val);
      }
      else {
          if(fig_value !== val) this.animateFigure($el, ('0' + val));
      }    
    }
  };
  
  // Let's go !
  Countdown.init();
