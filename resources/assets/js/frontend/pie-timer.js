Vue.filter('floorTime', function(time) {
    return (_.floor(time) > 0) ? _.floor(time) : '';
});
var pieTimer = Vue.component('pie-timer', {
    props: ['timeStart', 'length', 'aLength', 'currentTime'],
    template: '#pie-timer-template',

    data: function () {
        return {
            remainingLength: 0,
            timeCount: 0,
            questionTime: 0,
        };
    },
    watch: {
        timeCount: {
            handler: function(value, oldVal) {
                if (value > 0) {
                    setTimeout(() => {
                        this.timeCount--;
                    }, 1000);
                }
            },
            immediate: true // This ensures the watcher is triggered upon creation
        },
        questionTime: {
            handler: function(value, oldVal) {
                if (value > 0) {
                    setTimeout(() => {
                        this.questionTime--;
                    }, 1000);
                }
                if (oldVal && value <= 0) {
                    this.$emit('timeUp')
                }
            },
            immediate: true // This ensures the watcher is triggered upon creation
        },
    },
    mounted: function (){
        var vm = this;

        var timePassed = vm.currentTime - vm.timeStart;
        vm.remainingLength = _.floor(timePassed < 1 ? vm.length : vm.length - timePassed);
        vm.timeCount = _.floor(timePassed < 1 ? vm.length : vm.length - timePassed);
        vm.questionTime = timePassed < 1 ? vm.length : vm.length - timePassed;
        vm.checkTimeUp();
    },

    methods: {
        checkTimeUp: function () {
            var vm = this;
            if (vm.questionTime <= 0) {
                this.$emit('timeUp');
            }
        }
    },
});
