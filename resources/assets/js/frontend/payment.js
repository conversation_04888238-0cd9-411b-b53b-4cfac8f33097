'use strict';

//ip của người dùng
var userIp = '';
var userNation = '';
var userCity = '';

//hàm trả về ip người dùng
$.getJSON("https://jsonip.com/?callback=?", function (response) {
  userIp = response.ip;
  userNation = "undefined";
  userCity = "undefined";
});
Vue.filter('country', function (value) {
  if (!value) {
    return 'Chưa có thông tin';
  }
  var countries = {
    vn: 'Việt Nam',
    jp: 'Nhật Bản'
  };
  return countries[value];
});
var paymentVue = new Vue({
  el: '.main-payment',
  data: {

    url: window.location.origin,

    itemType: itemType,
    steps: 1, //bước mấy
    gates: null, //id cổng thanh toán
    desc: "", //mô tả công thanh toán
    listMethods: [], //danh sách phương thức thanh toán

    changeName: false,
    changePhone: false,
    changeAddress: false,
    changeShippingCountry: false,
    changeZipCode: false,
    errorName: '',
    errorPhone: '',
    errorAddress: '',
    errorShippingCountry: '',
    errorZipCode: '',

    //step1 , khởi tạo thông tin khách hàng
    customerEmail: userEmail,
    customerName: userName,
    customerPhone: userPhone,
    customerShippingCountry: userShippingCountry,
    customerAddress: userAddress,
    customerZipCode: '',

    //step 2, khởi tạo thông tin người nhận hàng
    deliveryName: userName,
    deliveryPhone: userPhone,
    deliveryShippingCountry: userShippingCountry,
    deliveryAddress: userAddress,
    deliveryZipCode: '',

    //giá dạng yên nhật, dành cho thanh toán nhật bản
    jpyPrice: jpPrice,
    quantity: 1,
    //trạng thái các lỗi
    customerError: false,
    buyNextState: false,

    deliveryError: false,
    buyBtnState: false,

    //thông tin đơn hàng thành công
    successInvoice: null,
    successPaymentMedthodName: '',
    product: product,
    isPlus: false,
    shippingCountry: 'vn',
    useCoupon: false,
    coupon: '',
    couponObject: null,
    loading: false
  },

  computed: {
    filteredMethods: function filteredMethods() {
      if (this.itemType == 'book') {
        return this.listMethods.filter(function (item) {
          return [7, 8].includes(item.id);
        });
      }
      return this.listMethods.filter(function (item) {
        return ![7, 8].includes(item.id);
      });
    },
    shippingCountries: function shippingCountries() {
      return [{
        value: 'vn',
        label: 'Việt Nam'
      }, {
        value: 'jp',
        label: 'Nhật Bản'
      }];
    }
  },
  methods: {
    toggleCoupon: function toggleCoupon() {
      if (this.loading) return;
      if (!this.coupon) {
        alert('Chưa có mã giảm giá');
        return;
      }
      this.loading = true;
      var vm = this;
      if (this.useCoupon) {
        vm.useCoupon = !vm.useCoupon;
        vm.loading = false;
        return;
      }
      $.post(window.location.origin + "/payment/check-coupon", {
        coupon: this.coupon
      }, function (response) {
        if (response.data == "EMPTY") {
          alert('Không được bỏ trống mã giảm giá');
        }
        if (response.data == "INVALID") {
          alert('Mã giảm giá không hợp lệ');
        }
        if (response.data == "RUN_OUT") {
          alert("Mã giảm giá đã hết lượt sử dụng");
        }
        if (response.data.id) {
          vm.couponObject = response.data;
          vm.useCoupon = !vm.useCoupon;
        }
        vm.loading = false;
      });
    },
    //hàm in ra info, nếu trống -> in ra dạng mờ
    printInfo: function printInfo(value) {
      if (value == null || value === '') return '<span class="empty-info">Chưa có thông tin</span>';else return value;
    },

    printType: function printType(type) {
      if (type === 'course') return "Khóa học";
      return "Combo";
    },

    //hàm in ra tiền tệ
    formatNumber: function formatNumber(num) {
      return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
    },

    //reload lại trang thông tin
    refresh: function refresh() {
      location.reload();
    },

    //hiện trường cần chỉnh sửa
    showEditer: function showEditer(field) {

      var vm = this;
      switch (field) {

        case 'name':
          vm.changeName = true;break;
        case 'phone':
          vm.changePhone = true;break;
        case 'address':
          vm.changeAddress = true;break;
        case 'shipping_country':
          vm.changeShippingCountry = true;break;
        case 'zip_code':
          vm.changeZipCode = true;break;
        default:
          break;
      }
    },

    //ẩn trường cần chỉnh sửa
    hideEditer: function hideEditer(field) {

      var vm = this;
      switch (field) {
        case 'name':
          vm.changeName = false;break;
        case 'phone':
          vm.changePhone = false;break;
        case 'address':
          vm.changeAddress = false;break;
        case 'shipping_country':
          vm.changeShippingCountry = false;break;
        case 'zip_code':
          vm.changeZipCode = false;break;
        default:
          break;
      }
    },

    //bắt sự kiện lưu thay đổi
    saveChange: function saveChange(field) {

      var vm = this;

      // kiểm tra tấn công bấm submit liên tục
      switch (field) {
        case 'name':
        {

          //nếu thông tin rỗng
          if ($("#customer-name").val() === '') {

            vm.errorName = "Tên khách hàng không được bỏ trống";
          } else {
            vm.errorName = '';
            vm.ajaxSaveChange('name', 'customer-name');
          }
        }break;

        case 'phone':
        {

          //nếu thông tin rỗng
          if ($("#customer-phone").val() === '') {

            vm.errorPhone = "Số điện thoại không được bỏ trống";
          } else {
            vm.errorPhone = '';
            vm.ajaxSaveChange('phone', 'customer-phone');
          }
        }break;
        case 'address':
        {

          //nếu thông tin rỗng
          if ($("#customer-address").val() === '' && vm.itemType == 'book') {

            vm.errorAddress = "Không được bỏ trống";
          } else {
            vm.errorAddress = '';
            vm.ajaxSaveChange('address', 'customer-address');
          }
        }break;
        case 'shipping_country':
        {

          //nếu thông tin rỗng
          if ($("#customer-shipping-country").val() === '' && vm.itemType == 'book') {

            vm.errorShippingCountry = "Không được bỏ trống";
          } else {
            vm.errorShippingCountry = '';
            vm.ajaxSaveChange('shipping_country', 'customer-shipping-country');
          }
        }break;
        case 'zip_code':
        {

          //nếu thông tin rỗng
          if ($("#customer-zipcode").val() === '' && vm.itemType == 'book' && vm.customerShippingCountry == 'jp') {

            vm.errorZipCode = "Không được bỏ trống";
          } else {
            vm.errorZipCode = '';
            vm.ajaxSaveChange('zip_code', 'customer-zipcode');
          }
        }break;
        default:
          break;
      }
    },

    //hàm thay đổi, lấy tham số là tên trường và id input của trường cần đổi
    ajaxSaveChange: function ajaxSaveChange(field, idField) {

      var vm = this;
      $.post(window.location.origin + "/payment/change-customer-info", {

        field: field,
        value: $("#" + idField).val()

      }, function (response) {
        if (response === "success") location.reload();else alert("Thông tin không hợp lệ");
      });
    },

    // ẩn thông báo lỗi
    hideError: function hideError() {

      this.customerError = false;
      this.deliveryError = false;
      this.errorName = '';
      this.errorPhone = '';
      this.errorAddress = '';
      this.errorShippingCountry = '';
      this.errorZipCode = '';
    },

    // người dùng ấn nút tiếp tục
    nextSteps: function nextSteps() {

      console.log("ấn nút tiếp tục");
      var vm = this;

      //nếu các thông tin về khách hàng không hợp lệ
      if (vm.customerName === '' || vm.customerEmail === '' || vm.customerPhone === '') {

        vm.customerError = true;
      } else {

        vm.steps = 2;
      }
    },

    // bấm trở về bước trước
    prevStep: function prevStep() {

      console.log("trở về bước trước");
      var vm = this;
      vm.steps = 1;
    },

    // thay đổi phương thức thanh toán
    updateDesc: function updateDesc() {

      console.log("update phương thức thanh toán");
      var vm = this;
      for (var i = 0; i < vm.filteredMethods.length; i++) {
        if (vm.gates === vm.filteredMethods[i].id) vm.desc = vm.filteredMethods[i].description;
      }
    },

    // bấm nút đặt hàng mua khóa học
    clickBuy: function clickBuy() {

      console.log("đặt hàng khóa học này");
      var vm = this;

      //console.log(vm.gates);
      vm.buyBtnState = true;

      setTimeout(function () {

        //nếu thanh toán không cần ship hàng
        if (vm.gates !== 4) {

          var data = {

            comboId: cbid,
            comboType: itemType, //kiểu course or combo được định nghĩa ở view payment
            gates: vm.gates,
            //lấy thông tin người dùng làm địa chỉ ship hàng
            deliveryName: vm.customerName,
            deliveryPhone: vm.customerPhone,
            deliveryAddress: vm.customerAddress,
            deliveryShippingCountry: vm.customerShippingCountry,
            deliveryZipCode: vm.customerZipCode,
            localIp: userIp, //userip được định nghĩa ở đầu file này
            localNation: userNation,
            localCity: userCity,
            userAgent: navigator.userAgent,
            productExtra: vm.product,
            quantity: vm.quantity,
            coupon: vm.coupon,
            useCoupon: vm.useCoupon ? 1 : 0
          };

          //gọi hàm tạo invoice từ đata
          vm.createdInvoice(data);

          //nếu thanh toán qua ship thẻ
        } else {

          //nếu các thông tin về người nhận không hợp lệ
          if (vm.deliveryName === '' || vm.deliveryPhone === '' || vm.deliveryAddress === '') {

            vm.deliveryError = true;

            //nếu tất cả các thông tin là hợp lệ
          } else {

            var _data = {

              comboId: cbid, // biến cbid định nghĩa ở view payment
              comboType: itemType, //kiểu course or combo được định nghĩa ở view payment
              gates: vm.gates,

              //lấy thông tin ô nhập liệu làm địa chỉ ship hàng
              deliveryName: vm.deliveryName,
              deliveryPhone: vm.deliveryPhone,
              deliveryAddress: vm.deliveryAddress,
              deliveryShippingCountry: vm.deliveryShippingCountry,
              deliveryZipCode: vm.customerZipCode,

              localIp: userIp, //userip được định nghĩa ở đầu file này
              localNation: userNation,
              localCity: userCity,

              userAgent: navigator.userAgent,
              productExtra: vm.product,
              quantity: vm.quantity,
              coupon: vm.coupon,
              useCoupon: vm.useCoupon ? 1 : 0
            };

            //gọi hàm tạo invoice từ đata
            vm.createdInvoice(_data);
          }
        }

        vm.buyBtnState = false;
      }, 1000);
    },

    //tạo invoce và invoice order từ ddataa truyền vào
    createdInvoice: function createdInvoice(data) {

      var vm = this;

      console.log("dữ liệu gửi lên");console.log(data);

      $.ajax({
        url: window.location.origin + '/payment/create-invoice', type: 'POST', data: data,
        error: function error(errors) {

          //nếu cổng thanh toán = 4 (bằng chuyển khoản)
          //if(data.gates == 4) vm.deliveryError = true;
          console.log(errors);
        },
        success: function success(response) {

          console.log(response);

          if (response.status === 'success') {
            console.log("tạo đơn thành công: ", response.successInvoice);
            vm.successInvoice = response.successInvoice;
            vm.successPaymentMedthodName = response.gateName;
            vm.steps = 3;

            //cuộn trang lên đầu -> xử lý lỗi hiện thị trên di động theo yêu cầu anh Dũng
            $("html, body").animate({ scrollTop: 0 }, "slow");

            // //gửi mail thông tin đơn hàng cho người dùng
            vm.sendEmailPaymentSuccess(response.successInvoice.id);
          }
          // alert(response);
        }
      });
    },

    //hàm gửi email thông tin đơn hàng khi người dùng tạo thành công
    sendEmailPaymentSuccess: function sendEmailPaymentSuccess(invoiceId) {

      $.post(window.location.origin + "/payment/send-email-success", { invoiceId: invoiceId }, function (response) {
        console.log("gửi mail cho người dùng");console.log(response);
      });
    },
    getPrice: function getPrice() {
      var vm = this;
      if (vm.itemType == 'book') {
        var bookPrice = 0;
        if (this.customerShippingCountry == 'jp') {
          bookPrice = parseInt(vm.product.price) * vm.quantity;
        } else {
          bookPrice = parseInt(vm.product.price_at_vn) * vm.quantity;
        }
        return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(bookPrice);
      }
      var price = vm.product.price;
      var extra_price = vm.product.extra_price && vm.product.extra_price > 0 ? vm.product.extra_price : 0;
      if (vm.product.isPlus === '1') {
        price = parseInt(extra_price) + parseInt(vm.product.price);
      }
      var subTotal = price;
      var discount = 0;
      var total = price;
      if (vm.coupon && vm.useCoupon) {
        if (vm.couponObject.type == 'percentage') {
          discount = parseInt(subTotal) * parseFloat(vm.couponObject.value / 100);
          total = parseInt(total) - parseInt(discount);
        }
      }

      if (vm.product.price_discount_not_use_coupon > 0) {
        discount = vm.product.price_discount_not_use_coupon;
        total = parseInt(total) - parseInt(discount);
      }
      return {
        subTotal: new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(subTotal),
        discount: new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(discount),
        total: new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(total)
      };
    },
    setProductPlus: function setProductPlus() {
      var vm = this;
      if (vm.isPlus) {
        vm.product.isPlus = '1';
      } else {
        vm.product.isPlus = '0';
      }
    }
  },
  mounted: function mounted() {
    var vm = this;
    this.gates = allMethods[0].id;
    this.desc = allMethods[0].description;
    this.listMethods = allMethods;
    if (this.product.isPlus && this.product.isPlus === '1') {
      this.isPlus = true;
    }
    //danh sách phương thức thanh toán
    //console.log(this.listMethods);
  }
});
