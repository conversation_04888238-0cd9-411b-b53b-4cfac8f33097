for (var i = 0; i < paths.length; i++) {
  paths[i].illustrator = JSON.parse(paths[i].illustrator);
}

var roadmap = new Vue({
  el: '.rm-container',
  data: function () {
    return {
      url: window.location.origin, // Đường dẫn host
      paths: paths,
      checkpoints: checkpoints,

      courseId: this_course_id,
      currentStage: paths[0],
      currentLessons: [],
      thisLesson: null,
      currentPoints: [],
      progress: myPrg,

      // Tất cả flashcards
      groupFlashcard: groupFlashcards,
      currentGfc: null,
      allFlashcards: [],
      schedules: [],
      guideLessons: [],
      illustrator: { title: null, detail: null },

      // Biến trạng thái hiển thị
      zoom: false,
      fc: false,
      sc: false,
      nt: false,
      zoomStage: false,

      // Đoạn lịch sử học bài
      selectedDate: new Date(),
      dateFilter: new Date().toISOString().substring(0, 10),
      attributes: [
        // <PERSON><PERSON><PERSON> dấu chấm xanh vào calender
        {
          dot: 'green',
          dates: [
            // new Date(2022, 3, 1), // Jan 1st
            // new Date(2022, 3, 10), // Jan 10th
            // new Date(2022, 3, 22), // Jan 22nd
          ]
        }
      ],
      historyItems: [], //lịch sử bài học khi bấm vào từng ngày
      this_l_id: this_l_id,
      submitting: false,
    };
  },
  created: function () {
    this.getProgress();
  },
  watch: {
    currentStage: {
      handler: function(newValue) {
        // console.log(newValue);
        this.initMenu(newValue.id);
      },
      deep: true
    },
    selectedDate: {
      handler: function(newValue) {
        var tmp = new Date(newValue);
        // console.log("filter date:", dateFilter.toISOString().substring(0,10));
        this.dateFilter = tmp.toISOString().substring(0, 10);
        // console.log("filter date:", this.dateFilter);
      },
      deep: true
    }
  },
  methods: {
    initCheckPoints: function () {
      var vm = this;

      // Nếu là ở mode bài học
      if (this_l_id != null) {
        // this_l_id khai báo ở roadmap.blade
        var crLs = _.find(vm.checkpoints, ['id', this_l_id]);
        if (crLs) {
            vm.currentStage = _.find(vm.paths, ['id', crLs.path_id]);
        }
        vm.initMenu(vm.currentStage.id);
      } else {
        vm.initMenu(paths[0].id);
      }
    },

    // Khởi tạo các dấu chấm
    initMenu: function (id) {
      // console.log(id)
      var vm = this;
      vm.currentLessons = checkpoints.filter(function (checkpoint) {
        return checkpoint.path_id == id;
      });
      // console.log(vm.currentLessons)
      vm.currentPoints = _.uniqBy(vm.currentLessons, 'key');
      vm.currentLessons.forEach(function(lesson) {
        var tmpG = _.find(oldGroups, { id: parseInt(lesson.group_id) });
        if (tmpG) {
          lesson.cate = _.find(oldCategories, { id: parseInt(tmpG.lesson_category_id) });
        }
      });
      vm.calcProgress(vm.currentLessons);

      // console.log('vm.currentLessons', vm.currentLessons);
    },

    chooseRoadmap: function () {
      var vm = this;

      swal({
        title: 'Xác nhận',
        text: 'Bạn có chắc chắn muốn thay đổi lộ trình học ?',
        icon: 'warning',
        buttons: true,
        dangerMode: true
      }).then(function(yes) {
        if (yes) {
          vm.submiting = true;

          $.post(
            window.location.origin + '/khoa-hoc/change-roadmap',
            {
              courseId: this_course_id,
              periodId: vm.currPeriod
            },
            function (response) {
              if (response == 'success') {
                swal('Bạn đã thay đổi lộ trình học thành công!', {
                  icon: 'success'
                });

                setTimeout(function(){
                  location.reload();
                }, 1000);
              }
            }
          );
        }
      });
    },

    showPopover: function (title, detail) {
      var vm = this;
      vm.illustrator.title = title;
      vm.illustrator.detail = detail;
      // console.log('illustrator', this.illustrator);
      vm.$forceUpdate();
    },

    getProgress: function () {
      var vm = this;
      // console.log("MyProgress", vm.progress);
      vm.calcProgress(vm.currentLessons);
      vm.calcHistoryData();
    },

    // Tính toán để đánh dấu lộ trình
    calcProgress: function (lessons) {
      var vm = this;

      // Đếm số bài học trong mỗi chấm
      for (var i = 0; i < vm.currentPoints.length; i++) {
        vm.currentPoints[i].count = 0; //biến đếm số bài học
        for (var n = 0; n < lessons.length; n++) {
          if (vm.currentPoints[i].key == lessons[n].key) vm.currentPoints[i].count++;
        }
      }

      // Check tick done trên mỗi bài học
      for (var i = 0; i < lessons.length; i++) {
        lessons[i].done = 0;
        var checkdone = _.find(vm.progress, ['lesson_id', lessons[i].id]);
        if (checkdone) {
          lessons[i].done = 2;
          if (checkdone.video_progress == 100 && checkdone.example_progress == 100) {
            lessons[i].done = 1; //check tick done
            // console.log('checkdone', checkdone);
          }
        }
      }

      // Đếm số bài học done trong mỗi chấm để gán trạng thái của mỗi check point
      for (var i = 0; i < vm.currentPoints.length; i++) {
        vm.currentPoints[i].count_done = 0; // Biến đếm số bài học done
        vm.currentPoints[i].inprogress = 0; // Nút có trong trạng thái đang học hay không

        for (var n = 0; n < lessons.length; n++) {
          if (vm.currentPoints[i].key == lessons[n].key && lessons[n].done == 1) {
            vm.currentPoints[i].count_done++;
          }
          if (vm.currentPoints[i].key == lessons[n].key && lessons[n].done == 2) {
            vm.currentPoints[i].inprogress = 1;
          }
        }
      }

      vm.$forceUpdate();

      // console.log('current lessons', lessons);

      // Set màu cho path nối, gray -> cái trước và sau đều gray
      for (var i = 0; i < vm.currentPoints.length; i++) {
        var point = vm.currentPoints[i];
        if (point.count_done == 0 && point.inprogress == 0) {
          point.color = '#E6E1CD'; //gray
          if (i > 0) vm.currentPoints[i - 1].color = '#E6E1CD';
        }
      }

      // green, //nếu cái hiện tại xanh, cái tiếp xanh -> xanh
      for (var i = 0; i < vm.currentPoints.length; i++) {
        var point = vm.currentPoints[i];
        if (point.count_done == point.count && point.inprogress == 0) {
          if (i + 1 < vm.currentPoints.length) {
            var pnext = vm.currentPoints[i + 1];

            if (pnext.count_done == pnext.count && pnext.inprogress == 0) {
              point.color = '#96D962';
            }
          }
        }
      }

      // Còn lại mà chưa dc set sẽ là vàng
      for (var i = 0; i < vm.currentPoints.length; i++) {
        if (vm.currentPoints[i].color == null) {
          vm.currentPoints[i].color = '#FFE335';
        }
      }
      // console.log('currentPoints', vm.currentPoints);
    },

    // Tính toán dữ liệu lịch sử học
    calcHistoryData: function () {
      var vm = this;
      var tmp = dailyLogs.slice();
      // console.log("tmp", tmp);

      // Tạo trường date_at để nhóm theo ngày
      for (var i = 0; i < tmp.length; i++) {
        tmp[i].date_at = tmp[i].created_at.substring(0, 10);
      }

      // Nếu progress không thuộc khóa này -> bỏ item
      for (var i = 0; i < tmp.length; i++) {
        var tmpLesson = _.find(vm.checkpoints, ['id', tmp[i].lesson_id]);
        if (tmpLesson) {
          tmp[i].lesson = tmpLesson;
          vm.historyItems.push(tmp[i]);
        }
      }

      var historyDate = _.uniqBy(vm.historyItems, 'date_at');
      var listdates = []; //lấy ra listdates để làm attributes
      for (var i = 0; i < historyDate.length; i++) {
        listdates.push(new Date(historyDate[i].date_at));
      }

      // console.log("listdates", listdates);
      // console.log("historyItems", vm.historyItems);

      // Đánh dấu phần dấu chấm
      vm.attributes = [
        {
          dot: 'red',
          dates: listdates
        }
      ];

      vm.$forceUpdate();
    },

    // Danh sách bài hướng dẫn
    initGuidesList: function () {
      var vm = this;
      var categoryGuide = oldCategories[0];
      var groupGuide = null; // Nhóm của các bài hướng dẫn

      for (var i = 0; i < oldGroups.length; i++) {
        if (oldGroups[i].lesson_category_id == categoryGuide.id) {
          groupGuide = oldGroups[i];
          break;
        }
      }

      var countLesson = listLessons.length;
      for (var i = 0; i < countLesson; i++) {
        if (listLessons[i].group_id == groupGuide.id) {
          // vm.guideLessons.push(listLessons[i]);

          // Check bài học có trong lộ trình mới thì mới cho vào
          if (_.find(vm.checkpoints, ['id', listLessons[i].id])) {
            vm.guideLessons.push(listLessons[i]);
          }
        }
      }

      // console.log('guideLessons', vm.guideLessons);
      vm.$forceUpdate();
    },

    // In ra định dạng ngày giờ đẹp
    prettyDate: function (t) {
      var event = new Date(t);
      return event.toLocaleDateString('pt-PT');
    },

    prevStage: function () {
      var idx = _.findIndex(this.paths, ['id', this.currentStage.id]);
      if (idx > 0) {
        this.currentStage = this.paths[idx - 1];
      }
    },

    nextStage: function () {
      var idx = _.findIndex(this.paths, ['id', this.currentStage.id]);
      if (idx < this.paths.length - 1) {
        this.currentStage = this.paths[idx + 1];
      }
    }
  },

  mounted: function () {
    var vm = this;

    if (!currPeriod) return;

    if (currPeriod) {
      if (this.paths.length > 0) vm.initCheckPoints();
      if (myPrg.length <= 1) {
        var checkZoomed = localStorage.getItem("checkZoomed");
        if (!checkZoomed) {
          vm.zoom = true;
          localStorage.setItem("checkZoomed", 1);
        }
      }
      // console.log("Mount#####paths", paths);
      // console.log("checkpoints", checkpoints);
      // console.log("Mount#####listLessons of courses", listLessons);

      // Khởi tạo danh sách flashcarrd
      for (var i = 0; i < listLessons.length; i++) {
        if (listLessons[i].type == 'flashcard') {
          // vm.allFlashcards.push(listLessons[i]);

          // Check bài học có trong lộ trình mới thì mới cho vào
          if (_.find(vm.checkpoints, ['id', listLessons[i].id])) {
            vm.allFlashcards.push(listLessons[i]);
          }
        }
      }

      // console.log("group_and_flashcards then", group_and_flashcards);

      vm.groupFlashcard.forEach(function myFunction(group) {
        group.lessons = [];
        group_and_flashcards.forEach(function myFunction(item) {
          if (group.id == item.group_id) group.lessons.push(item);
        });
      });
      console.log(vm.groupFlashcard);

      vm.currentGfc = vm.groupFlashcard[0];
    }
    // console.log("vm.groupFlashcard then", vm.groupFlashcard);

    // console.log('oldGroups', oldGroups);
    setTimeout(function () {
      vm.initGuidesList();

      if (this_l_id) {
        var findCurrentLesson = checkpoints.find(function(p) {
          return p.id == this_l_id
        });
        if (findCurrentLesson) {
          var findCurrentPoint = vm.currentPoints.find(function(p) {
            return p.key == findCurrentLesson.key
          });
          if (findCurrentPoint) {
            $('#ul-dropdownMenu' + findCurrentPoint.id)
              .parent()
              .addClass('open');

            var currentPointIndex = 0;
            for (var i = 0; i < vm.currentPoints.length; i++) {
              if (findCurrentPoint.id === vm.currentPoints[i].id) {
                currentPointIndex = i + 1;
                break;
              }
            }

            if (currentPointIndex > 2) {
              var scrollValue = 0;
              if (currentPointIndex % 3 === 0) {
                scrollValue = (currentPointIndex / 3) * 2 - 1;
              } else {
                scrollValue = Math.floor(currentPointIndex / 3) * 2;
              }
              $('#ul-dropdownMenu' + findCurrentPoint.id)
                .parent()
                .parent()
                .animate(
                  {
                    scrollTop: 85 * scrollValue
                  },
                  400
                );
            }
          }
        }
      }
    }, 500);
  }
});

$('#stage-small-container').hover(function() {
  $('#stage-container').removeClass("stage-container-hide");
  $(this).addClass("stage-container-show");
  setTimeout(function() {
    $('#stage-small-container').css("display", "none");
    $('#stage-container').css("display", "block");
  }, 100);
});

$('#stage-container').hover(function() {}, function() {
  $('#stage-small-container').removeClass("stage-container-show");
  $(this).addClass("stage-container-hide");
  setTimeout(function() {
    $('#stage-container').css("display", "none");
    $('#stage-small-container').css("display", "block");
  }, 100);
});
