function stackedCards() {
    var stackedOptions = "Top"; //Change stacked cards view from "Bottom", "Top" or "None".
    var rotate = true; //Activate the elements" rotation for each move on stacked cards.
    var items = 3; //Number of visible elements when the stacked options are bottom or top.
    var elementsMargin = 10; //Define the distance of each element when the stacked options are bottom or top.
    var useOverlays = true; //Enable or disable the overlays for swipe elements.
    var maxElements; //Total of stacked cards on DOM.
    var currentPosition = 0; //Keep the position of active stacked card.
    var rightObj; //Keep the swipe right properties.
    var leftObj; //Keep the swipe left properties.
    var listElNodesObj; //Keep the list of nodes from stacked cards.
    var listElNodesWidth; //Keep the stacked cards width.
    var currentElementObj; //Keep the stacked card element to swipe.
    var stackedCardsObj;
    var isFirstTime = true;
    var obj;
    var elTrans;

    obj = document.getElementById("stacked-cards-block");
    console.log("", obj);
    stackedCardsObj = obj.querySelector(".stackedcards-container");
    listElNodesObj = stackedCardsObj.children;
    rightObj = obj.querySelector(".stackedcards-overlay.right");
    leftObj = obj.querySelector(".stackedcards-overlay.left");

    countElements();
    currentElement();
    listElNodesWidth = stackedCardsObj.offsetWidth;
    currentElementObj = listElNodesObj[0];
    flipCard();
    resetFlip();
    updateUi();

    //Prepare elements on DOM
    const addMargin = elementsMargin * (items - 1) + "px";

    if (stackedOptions === "Top") {

        for (let i = items; i < maxElements; i += 1) {
            listElNodesObj[i].classList.add("stackedcards-top", "stackedcards--animatable", "stackedcards-origin-top");
        }

        elTrans = elementsMargin * (items - 1);

        stackedCardsObj.style.marginBottom = addMargin;

    } else if (stackedOptions === "Bottom") {

        for (let i = items; i < maxElements; i += 1) {
            listElNodesObj[i].classList.add("stackedcards-bottom", "stackedcards--animatable", "stackedcards-origin-bottom");
        }

        elTrans = 0;

        stackedCardsObj.style.marginBottom = addMargin;

    } else if (stackedOptions === "None") {

        for (let i = items; i < maxElements; i += 1) {
            listElNodesObj[i].classList.add("stackedcards-none", "stackedcards--animatable");
        }

        elTrans = 0;

    }

    for (let i = items; i < maxElements; i += 1) {
        listElNodesObj[i].style.zIndex = 0;
        listElNodesObj[i].style.opacity = 0;
        listElNodesObj[i].style.webkitTransform = "scale(" + (1 - (items * 0.04)) + ") translateX(0) translateY(" + elTrans + "px) translateZ(0) rotate(0)";
        listElNodesObj[i].style.transform = "scale(" + (1 - (items * 0.04)) + ") translateX(0) translateY(" + elTrans + "px) translateZ(0) rotate(0)";
    }

    if (listElNodesObj[currentPosition]) {
        listElNodesObj[currentPosition].classList.add("stackedcards-active");
    }

    if (useOverlays) {
        leftObj.style.transform = "translateX(0px) translateY(" + elTrans + "px) translateZ(0px) rotate(0deg)";
        leftObj.style.webkitTransform = "translateX(0px) translateY(" + elTrans + "px) translateZ(0px) rotate(0deg)";

        rightObj.style.transform = "translateX(0px) translateY(" + elTrans + "px) translateZ(0px) rotate(0deg)";
        rightObj.style.webkitTransform = "translateX(0px) translateY(" + elTrans + "px) translateZ(0px) rotate(0deg)";

    } else {
        leftObj.className = "";
        rightObj.className = "";

        leftObj.classList.add("stackedcards-overlay-hidden");
        rightObj.classList.add("stackedcards-overlay-hidden");
    }

    //Remove class init
    setTimeout(function () {
        obj.classList.remove("init");
    }, 150);


    // Thêm logic cho flip card
    function flipCard() {
        $(".card-item").off().click(function (e) {
            if (!$(e.target).hasClass("noFlip")) {
                $(this).find(".card-inner").toggleClass("flip");
            }
        });
    }

    // Lật lại thẻ về mặc định
    function resetFlip() {
        $(".card-inner").removeClass("flip");
    }

    // Usable functions
    function countElements() {
        maxElements = listElNodesObj.length;
        if (items > maxElements) {
            items = maxElements;
        }
    }

    //Keep the active card.
    function currentElement() {
        currentElementObj = listElNodesObj[currentPosition];
    }

    //Functions to swipe left elements on logic external action.
    function onActionLeft() {
        if (currentPosition < maxElements) {
            if (useOverlays) {
                leftObj.classList.remove("no-transition");
                leftObj.style.zIndex = "8";
                transformUi(0, 0, 1, leftObj);

            }

            setTimeout(function () {
                onSwipeLeft();
                resetOverlayLeft();
            }, 300);
        }
    }

    //Functions to swipe right elements on logic external action.
    function onActionRight() {
        if (currentPosition < maxElements) {
            if (useOverlays) {
                rightObj.classList.remove("no-transition");
                rightObj.style.zIndex = "8";
                transformUi(0, 0, 1, rightObj);
            }

            setTimeout(function () {
                onSwipeRight();
                resetOverlayRight();
            }, 300);
        }
    }

    function onActionUndo() {
        if (currentPosition > 0) {
            currentPosition--;
            updateUi();
            currentElement();
            setActiveHidden();
        }
    }

    //Swipe active card to left.
    function onSwipeLeft() {
        removeNoTransition();
        transformUi(-1000, 0, 0, currentElementObj);
        if (useOverlays) {
            transformUi(-1000, 0, 0, leftObj); //Move leftOverlay
            resetOverlayLeft();
        }
        currentPosition = currentPosition + 1;
        updateUi();
        currentElement();
        setActiveHidden();
    }

    //Swipe active card to right.
    function onSwipeRight() {
        removeNoTransition();
        transformUi(1000, 0, 0, currentElementObj);
        if (useOverlays) {
            transformUi(1000, 0, 0, rightObj); //Move rightOverlay
            resetOverlayRight();
        }

        currentPosition = currentPosition + 1;
        updateUi();
        currentElement();
        setActiveHidden();
    }

    //Swipe active card to top.

    //Remove transitions from all elements to be moved in each swipe movement to improve perfomance of stacked cards.
    function removeNoTransition() {
        if (listElNodesObj[currentPosition]) {

            if (useOverlays) {
                leftObj.classList.remove("no-transition");
                rightObj.classList.remove("no-transition");
            }

            listElNodesObj[currentPosition].classList.remove("no-transition");
            listElNodesObj[currentPosition].style.zIndex = 6;
        }

    }

    //Move the overlay left to initial position.
    function resetOverlayLeft() {
        if (currentPosition < maxElements) {
            if (useOverlays) {
                setTimeout(function () {

                    if (stackedOptions === "Top") {

                        elTrans = elementsMargin * (items - 1);

                    } else if (stackedOptions === "Bottom" || stackedOptions === "None") {

                        elTrans = 0;

                    }

                    if (!isFirstTime) {

                        leftObj.classList.add("no-transition");

                    }

                    requestAnimationFrame(function () {

                        leftObj.style.transform = "translateX(0) translateY(" + elTrans + "px) translateZ(0)";
                        leftObj.style.webkitTransform = "translateX(0) translateY(" + elTrans + "px) translateZ(0)";
                        leftObj.style.opacity = "0";

                    });

                }, 300);

                isFirstTime = false;
            }
        }
    }

    //Move the overlay right to initial position.
    function resetOverlayRight() {
        if (currentPosition < maxElements) {
            if (useOverlays) {
                setTimeout(function () {

                    if (stackedOptions === "Top") {

                        elTrans = elementsMargin * (items - 1);

                    } else if (stackedOptions === "Bottom" || stackedOptions === "None") {

                        elTrans = 0;

                    }

                    if (!isFirstTime) {

                        rightObj.classList.add("no-transition");

                    }

                    requestAnimationFrame(function () {

                        rightObj.style.transform = "translateX(0) translateY(" + elTrans + "px) translateZ(0)";
                        rightObj.style.webkitTransform = "translateX(0) translateY(" + elTrans + "px) translateZ(0)";
                        rightObj.style.opacity = "0";

                    });

                }, 300);

                isFirstTime = false;
            }
        }
    }


    function setActiveHidden() {
        if (currentPosition < maxElements) {
            if (currentPosition > 0) {
                listElNodesObj[currentPosition - 1].classList.remove("stackedcards-active");
                listElNodesObj[currentPosition - 1].classList.add("stackedcards-hidden");
            }
            listElNodesObj[currentPosition].classList.remove("stackedcards-hidden");
            listElNodesObj[currentPosition].classList.add("stackedcards-active");
        }
    }

    //Add translate X and Y to active card for each frame.
    function transformUi(moveX, moveY, opacity, elementObj) {
        requestAnimationFrame(function () {
            var element = elementObj;

            // Function to generate rotate value
            function RotateRegulator(value) {
                if (value / 10 > 15) {
                    return 15;
                } else if (value / 10 < -15) {
                    return -15;
                }
                return value / 10;
            }

            let rotateElement = 0;
            if (rotate) {
                rotateElement = RotateRegulator(moveX);
            } else {
                rotateElement = 0;
            }

            if (stackedOptions === "Top") {
                elTrans = elementsMargin * (items - 1);
                if (element) {
                    element.style.webkitTransform = "translateX(" + moveX + "px) translateY(" + (moveY + elTrans) + "px) translateZ(0) rotate(" + rotateElement + "deg)";
                    element.style.transform = "translateX(" + moveX + "px) translateY(" + (moveY + elTrans) + "px) translateZ(0) rotate(" + rotateElement + "deg)";
                    element.style.opacity = opacity;
                }
            } else if (stackedOptions === "Bottom" || stackedOptions === "None") {

                if (element) {
                    element.style.webkitTransform = "translateX(" + moveX + "px) translateY(" + (moveY) + "px) translateZ(0) rotate(" + rotateElement + "deg)";
                    element.style.transform = "translateX(" + moveX + "px) translateY(" + (moveY) + "px) translateZ(0) rotate(" + rotateElement + "deg)";
                    element.style.opacity = opacity;
                }

            }
        });
    }

    //Action to update all elements on the DOM for each stacked card.
    function updateUi() {
        requestAnimationFrame(function () {
            elTrans = 0;
            var elZindex = 5;
            var elScale = 1;
            var elOpac = 1;
            var elTransTop = items;
            var elTransInc = elementsMargin;

            for (let i = currentPosition; i < (currentPosition + items); i += 1) {
                if (listElNodesObj[i]) {
                    if (stackedOptions === "Top") {

                        listElNodesObj[i].classList.add("stackedcards-top", "stackedcards--animatable", "stackedcards-origin-top");

                        if (useOverlays) {
                            leftObj.classList.add("stackedcards-origin-top");
                            rightObj.classList.add("stackedcards-origin-top");
                        }

                        elTrans = elTransInc * elTransTop;
                        elTransTop--;

                    } else if (stackedOptions === "Bottom") {
                        listElNodesObj[i].classList.add("stackedcards-bottom", "stackedcards--animatable", "stackedcards-origin-bottom");

                        if (useOverlays) {
                            leftObj.classList.add("stackedcards-origin-bottom");
                            rightObj.classList.add("stackedcards-origin-bottom");
                        }

                        elTrans = elTrans + elTransInc;

                    } else if (stackedOptions === "None") {

                        listElNodesObj[i].classList.add("stackedcards-none", "stackedcards--animatable");
                        elTrans = elTrans + elTransInc;

                    }
                    var rotateControl = [-2, -1, 1, 2];
                    var randomItem = rotateControl[Math.floor(Math.random() * rotateControl.length)];
                    // Bật 2 dòng dưới nếu muốn flashcard trông giống như một bộ bài lộn xộn
                    if (i === currentPosition) {
                        listElNodesObj[i].style.transform = "translateX(0) translateY(" + (elTrans - elTransInc) + "px) translateZ(0)";
                        listElNodesObj[i].style.webkitTransform = "translateX(0) translateY(" + (elTrans - elTransInc) + "px) translateZ(0)";
                    } else {
                        listElNodesObj[i].style.transform = "translateX(0) translateY(" + (elTrans - elTransInc) + "px) translateZ(0) rotate(" + randomItem + "deg)";
                        listElNodesObj[i].style.webkitTransform = "translateX(0) translateY(" + (elTrans - elTransInc) + "px) translateZ(0) rotate(" + randomItem + "deg)";
                    }
                    // Bật 2 dòng dưới lên nếu không muốn flashcard xoay lộn xộn
                    // listElNodesObj[i].style.transform ="scale(" + elScale + ") translateX(0) translateY(" + (elTrans - elTransInc) + "px) translateZ(0)";
                    // listElNodesObj[i].style.webkitTransform ="scale(" + elScale + ") translateX(0) translateY(" + (elTrans - elTransInc) + "px) translateZ(0)";


                    listElNodesObj[i].style.opacity = elOpac;
                    listElNodesObj[i].style.zIndex = elZindex;

                    elScale = elScale - 0.04;
                    // elOpac = elOpac - (1 / items); //Làm mờ từ thẻ thứ 2, không biết để làm gì mà cũng xấu nên comment lại

                    elZindex--;
                }
            }
        });
    }

    //Add listeners to call global action for swipe cards
    var buttonLeft = document.querySelector(".left-action");
    var buttonRight = document.querySelector(".right-action");
    var buttonUndo = document.querySelector(".undo-action");

    if (buttonLeft) {
        buttonLeft.addEventListener("click", onActionLeft, false);
    }
    if (buttonRight) {
        buttonRight.addEventListener("click", onActionRight, false);
    }
    if (buttonUndo) {
        buttonUndo.addEventListener("click", onActionUndo, false);
    }
}

document.onkeydown = checkKey;

function checkKey(e) {
    e = e || window.event;
    if (e.keyCode === "37") {
        $(".left-action").trigger("click");
    } else if (e.keyCode === "39") {
        $(".right-action").trigger("click");
    }
}
