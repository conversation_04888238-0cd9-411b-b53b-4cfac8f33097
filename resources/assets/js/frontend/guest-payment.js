var userIp = '';
var userNation = '';
var userCity = '';

var paymentVue = new Vue({
    el: '.main-payment',
    data: {
        url: window.location.origin,

        itemType: itemType,
        steps: 1, //bước mấy
        gates: null, //id cổng thanh toán
        desc: "", //mô tả công thanh toán
        listMethods: [], //danh sách phương thức thanh toán

        //giá dạng yên nh<PERSON>t, dành cho thanh toán nhật bản
        jpyPrice: jpPrice,
        quantity: 1,
        //trạng thái các lỗi
        customerError: false,
        errorName: '',
        errorPhone: '',
        errorEmail: '',
        buyNextState: false,

        deliveryError: false,
        deliveryName: '',
        deliveryPhone: '',
        deliveryShippingCountry: '',
        deliveryAddress: '',
        deliveryZipCode: '',

        buyBtnState: false,

        //thông tin đơn hàng thành công
        successInvoice: null,
        successPaymentMedthodName: '',
        product: product,
        isPlus: false,
        loading: false,
        shippingCountry: 'vn',
        phoneCountry: 'vn',
        showCaptcha: true,
        phoneVerifyCode: '',
        phoneVerified: false,
        phoneVerifying: false,
        openTel: false,
        guestForm: {
            name: '',
            phone: '',
            email: '',
        },
        useCoupon: false,
        coupon: '',
        couponObject: null,
        bindProps: {
            mode: 'international',
            inputOptions: {
                placeholder: 'Nhập số điện thoại của bạn'
            },
            allCountries: [
                {
                    name: 'Vietnam (Việt Nam)',
                    iso2: 'VN',
                    dialCode: '84'
                },
                {
                    name: 'Japan (日本)',
                    iso2: 'JP',
                    dialCode: '81'
                }
            ]
        },
    },
    computed: {
        filteredMethods: function () {
            if (this.itemType == 'book') {
                return this.listMethods.filter(function (item) {
                    return [7, 8].includes(item.id);
                });
            }
            return this.listMethods.filter(function (item) {
                return ![7, 8].includes(item.id);
            });
        },
    },
    watch: {
        phoneCountry: function (val) {
            if (this.guestForm.phone == '') return;
            if (this.guestForm.phone.startsWith('0')) {
                this.guestForm.phone = val + this.guestForm.phone.substring(1);
            }
            if (this.guestForm.phone.startsWith('+84')) {
                this.guestForm.phone = val + this.guestForm.phone.substring(3);
            }
            if (this.guestForm.phone.startsWith('+81')) {
                this.guestForm.phone = val + this.guestForm.phone.substring(3);
            }
        },
    },
    methods: {
        toggleCoupon: function toggleCoupon() {
            if (this.loading) return;
            if (!this.coupon) {
                alert('Chưa có mã giảm giá');
                return;
            }
            this.loading = true;
            var vm = this;
            if (this.useCoupon) {
                vm.useCoupon = !vm.useCoupon;
                vm.loading = false;
                return;
            }
            $.post(window.location.origin + "/payment/check-coupon", {
                coupon: this.coupon
            }, function (response) {
                if (response.data == "EMPTY") {
                    alert('Không được bỏ trống mã giảm giá');
                }
                if (response.data == "INVALID") {
                    alert('Mã giảm giá không hợp lệ');
                }
                if (response.data == "RUN_OUT") {
                    alert("Mã giảm giá đã hết lượt sử dụng");
                }
                if (response.data.id) {
                    vm.couponObject = response.data;
                    vm.useCoupon = !vm.useCoupon;
                }
                vm.loading = false;
            });
        },
        //hàm in ra info, nếu trống -> in ra dạng mờ
        printInfo: function (value) {
            if (value == null || value === '') return '<span class="empty-info">Chưa có thông tin</span>';
            else return value;
        },

        printType: function (type) {
            if (type === 'course') return "Khóa học";
            return "Combo";
        },

        //hàm in ra tiền tệ
        formatNumber: function (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
        },
        validate: function () {
            var vm = this;
            if (this.loading) return;
            if (this.guestForm.name == '') {
                this.customerError = true;
                this.errorName = 'Không được để trống';
                return;
            }
            if (this.guestForm.phone == '') {
                this.customerError = true;
                this.errorPhone = 'Không được để trống';
                return;
            }
            if (!this.phoneVerified) {
                this.customerError = true;
                this.errorPhone = 'Số điện thoại chưa được xác minh';
                return;
            }
            if (this.guestForm.email == '') {
                this.customerError = true;
                this.errorEmail = 'Không được để trống';
                return;
            }
            // var data = {
            //   phone: this.guestForm.phone,
            //   email: this.guestForm.email
            // }
            vm.nextSteps();
            //   this.loading = true;
            //   $.post(this.url + '/payment/guest-check', data, function (res) {
            //     if (res.code == 409) {
            //       if (res.field == 'phone') vm.errorPhone = 'exist';
            //       if (res.field == 'email') vm.errorEmail = 'exist';
            //     }
            //     if (res.code == 200) {
            //       vm.nextSteps();
            //     }
            //     vm.loading = false;
            //   }).fail(function (err) {
            //     if (err.status == 422) {
            //       if (err.responseJSON.errors.email) {
            //         vm.errorEmail = 'Email không đúng định dạng';
            //       }
            //       if (err.responseJSON.errors.phone) {
            //         vm.errorPhone = 'Số điện thoại không đúng định dạng';
            //       }
            //     }
            //     vm.loading = false;
            //   });
        },
        getPrice: function getPrice() {
            var vm = this;
            if (vm.itemType == 'book') {
                var bookPrice = 0;
                if (this.customerShippingCountry == 'jp') {
                    bookPrice = parseInt(vm.product.price) * vm.quantity;
                } else {
                    bookPrice = parseInt(vm.product.price_at_vn) * vm.quantity;
                }
                return new Intl.NumberFormat('vi-VN', {style: 'currency', currency: 'VND'}).format(bookPrice);
            }
            var price = vm.product.price;
            var extra_price = vm.product.extra_price && vm.product.extra_price > 0 ? vm.product.extra_price : 0;
            if (vm.product.isPlus === '1') {
                price = parseInt(extra_price) + parseInt(vm.product.price);
            }
            var subTotal = price;
            var discount = 0;
            var total = price;
            if (vm.coupon && vm.useCoupon) {
                if (vm.couponObject.type == 'percentage') {
                    discount = parseInt(subTotal) * parseFloat(vm.couponObject.value / 100);
                    total = parseInt(total) - parseInt(discount);
                }
            }
            return {
                subTotal: new Intl.NumberFormat('vi-VN', {style: 'currency', currency: 'VND'}).format(subTotal),
                discount: new Intl.NumberFormat('vi-VN', {style: 'currency', currency: 'VND'}).format(discount),
                total: new Intl.NumberFormat('vi-VN', {style: 'currency', currency: 'VND'}).format(total)
            };
        },
        setProductPlus: function () {
            var vm = this;
            if (vm.isPlus) {
                vm.product.isPlus = '1'
            } else {
                vm.product.isPlus = '0'
            }
        },
        // người dùng ấn nút tiếp tục
        nextSteps: function () {
            var vm = this;
            //nếu các thông tin về khách hàng không hợp lệ
            if (vm.guestForm.name === '' || vm.guestForm.phone === '' || vm.guestForm.email === '') {
                vm.customerError = true;
            } else {
                vm.steps = 2;
            }
        },
        // thay đổi phương thức thanh toán
        updateDesc: function () {
            var vm = this;
            for (var i = 0; i < vm.filteredMethods.length; i++)
                if (vm.gates === vm.filteredMethods[i].id)
                    vm.desc = vm.filteredMethods[i].description;
        },
        // ẩn thông báo lỗi
        hideError: function () {
            this.customerError = false;
        },
        clickBuy: function () {
            var vm = this;
            if (this.loading) return;
            console.log("đặt hàng khóa học này");
            var vm = this;

            //console.log(vm.gates);
            vm.loading = true;

            setTimeout(function () {

                //nếu thanh toán không cần ship hàng
                if (vm.gates !== 4) {

                    const data = {

                        comboId: cbid,
                        comboType: itemType, //kiểu course or combo được định nghĩa ở view payment
                        gates: vm.gates,
                        //lấy thông tin người dùng làm địa chỉ ship hàng
                        deliveryName: vm.guestForm.name,
                        deliveryPhone: vm.guestForm.phone,
                        deliveryAddress: '',
                        deliveryShippingCountry: '',
                        deliveryZipCode: '',
                        guestName: vm.guestForm.name,
                        guestPhone: vm.guestForm.phone,
                        guestEmail: vm.guestForm.email,
                        localIp: userIp, //userip được định nghĩa ở đầu file này
                        localNation: userNation,
                        localCity: userCity,
                        userAgent: navigator.userAgent,
                        productExtra: vm.product,
                        quantity: vm.quantity,
                        coupon: vm.coupon,
                        useCoupon: vm.useCoupon ? 1 : 0
                    };

                    //gọi hàm tạo invoice từ đata
                    vm.createdInvoice(data);

                    //nếu thanh toán qua ship thẻ
                } else {

                    //nếu các thông tin về người nhận không hợp lệ
                    if (vm.deliveryName === '' || vm.deliveryPhone === '' || vm.deliveryAddress === '') {

                        vm.deliveryError = true;

                        //nếu tất cả các thông tin là hợp lệ
                    } else {

                        const data = {

                            comboId: cbid, // biến cbid định nghĩa ở view payment
                            comboType: itemType, //kiểu course or combo được định nghĩa ở view payment
                            gates: vm.gates,

                            //lấy thông tin ô nhập liệu làm địa chỉ ship hàng
                            deliveryName: vm.deliveryName,
                            deliveryPhone: vm.deliveryPhone,
                            deliveryAddress: vm.deliveryAddress,
                            deliveryShippingCountry: vm.deliveryShippingCountry,
                            deliveryZipCode: vm.deliveryZipCode,
                            guestName: vm.guestForm.name,
                            guestPhone: vm.guestForm.phone,
                            guestEmail: vm.guestForm.email,

                            localIp: userIp, //userip được định nghĩa ở đầu file này
                            localNation: userNation,
                            localCity: userCity,

                            userAgent: navigator.userAgent,
                            productExtra: vm.product,
                            quantity: vm.quantity,
                            coupon: vm.coupon,
                            useCoupon: vm.useCoupon ? 1 : 0
                        };

                        //gọi hàm tạo invoice từ đata
                        vm.createdInvoice(data);

                    }
                }

                vm.loading = false;

            }, 1000);
        },

        //tạo invoce và invoice order từ ddataa truyền vào
        createdInvoice: function (data) {

            var vm = this;

            console.log("dữ liệu gửi lên");
            console.log(data);

            $.ajax({
                url: window.location.origin + '/payment/create-guest-invoice', type: 'POST', data: data,
                error: function (errors) {

                    //nếu cổng thanh toán = 4 (bằng chuyển khoản)
                    //if(data.gates == 4) vm.deliveryError = true;
                    console.log(errors);
                },
                success: function (response) {

                    console.log(response);

                    if (response.status === 'success') {
                        console.log("tạo đơn thành công: ", response.successInvoice);
                        vm.successInvoice = response.successInvoice;
                        vm.successPaymentMedthodName = response.gateName;
                        vm.steps = 3;

                        //cuộn trang lên đầu -> xử lý lỗi hiện thị trên di động theo yêu cầu anh Dũng
                        $("html, body").animate({scrollTop: 0}, "slow");

                        // //gửi mail thông tin đơn hàng cho người dùng
                        vm.sendEmailPaymentSuccess(response.successInvoice.id);
                    }
                    // alert(response);
                }
            });
        },
        login: function () {
            var loginBtn = document.getElementById('text-login');
            loginBtn.click();
        },
        // bấm trở về bước trước
        prevStep: function () {
            var vm = this;
            vm.steps = 1;
        },
        //hàm gửi email thông tin đơn hàng khi người dùng tạo thành công
        sendEmailPaymentSuccess: function (invoiceId) {
            $.post(window.location.origin + "/payment/send-email-success", {invoiceId: invoiceId}, function (response) {
                console.log("gửi mail cho người dùng");
                console.log(response);
            });
        },
        verify: function () {
            if (!this.checkNumberPhoneFormat()) {
                return;
            }
            var vm = this;
            var phoneNumber = this.guestForm.phone;

            //replace phone start 0 to +84 if is phone vietnam
            if (this.guestForm.phone.startsWith('0') && this.guestForm.phone.length === 10 && !this.guestForm.phone.startsWith('0081')) {
                phoneNumber = '+84' + this.guestForm.phone.substring(1);
            }
            if ( this.guestForm.phone.length === 14 && this.guestForm.phone.startsWith('0081')) {
                phoneNumber = '+81' + this.guestForm.phone.substring(4);
            }
            var appVerifier = window.recaptchaVerifier;
            firebase.auth().signInWithPhoneNumber(phoneNumber, appVerifier).then(function (confirmationResult) {
                // SMS sent. Prompt user to type the code from the message, then sign the
                // user in with confirmationResult.confirm(code).
                window.confirmationResult = confirmationResult;
                // ...
                vm.phoneVerifying = true;
            }).catch(function (error) {
                vm.customerError = true;
                vm.errorPhone = 'Đã xảy ra lỗi. Xin kiểm tra lại số điện thoại!';
            });
        },
        checkNumberPhoneFormat() {
            // replace all space in phone number
            this.guestForm.phone = this.guestForm.phone.replace(/\s/g, '');

            // kiểm tra xem số điện thoại có đúng định dạng không. có 4 định dạng hợp lệ
            if (!this.guestForm.phone.startsWith('0') && !this.guestForm.phone.startsWith('+84') && !this.guestForm.phone.startsWith('+81') && !this.guestForm.phone.startsWith('0081')) {
                this.customerError = true;
                this.errorPhone = 'Số điện thoại không đúng định dạng. Vui lòng nhập số điện thoại theo định dạng Việt Nam hoặc Nhật Bản';
                return false;
            }
            // +84--------- hoặc 0--------- hoặc  +81---------- hoặc 0081----------
            if (this.guestForm.phone.startsWith('0') && this.guestForm.phone.length !== 10 && !this.guestForm.phone.startsWith('0081')) {
                this.customerError = true;
                this.errorPhone = 'Số điện thoại không đúng định dạng số điện thoại Việt Nam. VD: 0987654321';
                return false;
            }
            if (this.guestForm.phone.startsWith('+84') && this.guestForm.phone.length !== 12) {
                this.customerError = true;
                this.errorPhone = 'Số điện thoại không đúng định dạng số điện thoại Việt Nam. VD: +84987654321';
                return false;
            }
            if (this.guestForm.phone.startsWith('+81') && this.guestForm.phone.length !== 13) {
                this.customerError = true;
                this.errorPhone = 'Số điện thoại không đúng định dạng số điện thoại Nhật Bản. VD: +819876543210';
                return false;
            }
            if (this.guestForm.phone.startsWith('0081') && this.guestForm.phone.length !== 14) {
                this.customerError = true;
                this.errorPhone = 'Số điện thoại không đúng định dạng số điện thoại Nhật Bản. VD: 00819876543210';
                return false;
            }

            // kiểm tra nếu sdt chỉ có các ký tự hợp lệ: + 0 1 2 3 4 5 6 7 8 9
            if (!/^[+0123456789]+$/.test(this.guestForm.phone)) {
                this.customerError = true;
                this.errorPhone = 'Số điện thoại không đúng định dạng, chỉ được chứa các ký tự số và dấu +';
                return false;
            }

            return true;
        },
        verifyCode: function () {
            var vm = this;
            var code = this.phoneVerifyCode;
            vm.errorPhone = '';
            window.confirmationResult.confirm(code).then(function (result) {
                // User signed in successfully.
                vm.phoneVerified = true;
                vm.phoneVerifying = false;
                // ...
            }).catch(function (error) {
                vm.customerError = true;
                vm.errorPhone = 'Đã xảy ra lỗi. Mã xác minh không đúng.';
            });
        },
        initVerification: function () {
            var vm = this;
            var ui = new firebaseui.auth.AuthUI(firebase.auth());
            firebase.auth().languageCode = 'it';
            window.recaptchaVerifier = new firebase.auth.RecaptchaVerifier('get-sign-in-code', {
                size: 'invisible',
                callback: function () {
                    // reCAPTCHA solved, allow signInWithPhoneNumber.
                },
            });

            window.recaptchaVerifier.render().then((widgetId) => {
                window.recaptchaWidgetId = widgetId;
            });
        }
    },
    mounted: function () {
        var vm = this;
        this.gates = allMethods[0].id;
        this.desc = allMethods[0].description;
        this.listMethods = allMethods;
        if (this.product.isPlus && this.product.isPlus === '1') {
            this.isPlus = true
        }
        this.initVerification();
    }
});
