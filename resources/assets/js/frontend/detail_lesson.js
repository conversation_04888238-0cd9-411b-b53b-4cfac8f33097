var user = new Vue({
  el: "#lesson-content-detail",

  data() {
    return {
      url: window.location.origin, //đường dẫn host
      tasks: lesson_tasks,
      answers: lesson_answers,
      lesson: lesson_lesson,
      results: lesson_results,
      writeQuestions: lesson_writeQuestions,
      currentResult: {}, // bài kiểm tra đã làm học viên chọn để xem lại
      resultData: {}, // data 1 bài thi của học viên
      userScore: 0,
      seeking: false,
      tokenPlayMp3: 0,
      currentIndex: -1,
      mp3: null,
      playingMp3: false,
      flashcards: [], // mảng flashcard
      cardStats: {}, // chỉ số card đã thuộc/chưa thuộc
      currentCard: 0, // index hiện tại của flashcard
      currentType: "all", // loại hiện tại của stack card (known, unknown, all)
      currentTempCard: 0, // id hiện tại của flashcard mà user học dở (number || "end")
      userSettings: {}, // setting của user sử dụng flashcard
      flashcardMp3: null,
      swipeLoading: false,
      focusFC: false,
      token: document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content"),
      //các biến cmts của riêng flashcards
      showComment: false,
      meid: meid,
      avatar: myAvatar,
      thisFlashcardId: 0,
      listComments: [], //sanh sách các comments
      page: 1, //trang thứ mấy
      numPost: 5,
      ref: null, //nguồn chuyển hướng (notice hoặc 0)
      showLoading: false, //trạng thái hiển thị button tải thêm
      theEnd: false, //thông báo hết danh sách
      showLoadingNewComment: false,
      likeLoading: false, // trạng thái của nút like (true: không được thao tác)
      suggestToShow: {},
      suggestionShowed: false,
      suggestionCount: 0,
      submiting: false,
      currPeriod: currPeriod, // Def in roadmap blad
      prevFlashcard: {},
      initTime: 0,
      videoDuration: 0,
      progress: currentProgress,
      currentLessonPoints: currentLessonPoints,
    };
  },
  computed: {
    isIos() {
      var ua = window.navigator.userAgent;
      var iOS = !!ua.match(/iPad/i) || !!ua.match(/iPhone/i);
      var isSafari =
        window.navigator.vendor &&
        window.navigator.vendor.indexOf("Apple") > -1 &&
        ua &&
        ua.indexOf("CriOS") == -1 &&
        ua.indexOf("FxiOS") == -1;
      var webkit = !!ua.match(/WebKit/i);
      var iOSSafari = iOS && webkit && !ua.match(/CriOS/i);
      return iOS || isSafari || iOSSafari;
    },
    suggestionCountdown() {
      var date = new Date(0);
      date.setSeconds(this.suggestionCount);
      return date.toISOString().substr(11, 8);
    },
    taskStats() {
      return {
        videoTask: Number(
          _.some(this.tasks, function (task) {
            return task.type == 12 || (task.type == 2 && task.server == null);
          })
        ),
        youtubeTask: Number(
          _.some(this.tasks, function (task) {
            return task.type == 2 && task.server == "youtube";
          })
        ),
        examTask: Number(
          _.some(this.tasks, function (task) {
            return task.type == 3;
          })
        ),
        flashcardTask: Number(
          _.some(this.tasks, function (task) {
            return task.type == 9;
          })
        ),
        quizTask: Number(
            _.some(this.tasks, function (task) {
              return task.type == 10;
            })
        ),
      };
    },
  },
  watch: {
    suggestionCount: {
      handler(value) {
        const vm = this;
        if (value > 0) {
          setTimeout(function () {
            vm.suggestionCount--;
          }, 1000);
        }
      },
      immediate: true, // This ensures the watcher is triggered upon creation
    },
  },
  methods: {
    // kiểm tra học viên đã nhập câu trả lời nào chưa
    checkEmptyAnswer() {
      const vm = this;
      for (var i = 0; i < vm.tasks.length; i++) {
        var id = vm.tasks[i].id;
        if (vm.tasks[i].type == 3) {
          for (var j = 0; j < vm.answers[id].length; j++) {
            if ($("#answer" + vm.answers[id][j].id).is(":checked")) {
              return false;
            }
          }
        } else if (vm.tasks[i].type == 6) {
          for (var j = 0; j < vm.writeQuestions[id].length; j++) {
            if (
              $(
                "#write-question" + id + "-" + vm.writeQuestions[id][j].id
              ).val() != null &&
              $(
                "#write-question" + id + "-" + vm.writeQuestions[id][j].id
              ).val() != undefined &&
              $(
                "#write-question" + id + "-" + vm.writeQuestions[id][j].id
              ).val() != ""
            ) {
              return false;
            }
          }
        }
      }
      return true;
    },

    // nộp bài thi
    sendTestResult(arg) {
      const vm = this;

      if (vm.checkEmptyAnswer()) {
        $("#empty_answers").modal("toggle");
        return;
      }

      //hiện đáp án
      // $("#see-correct-answer").css('display', 'block');
      $(".see-correct-answer-btn").removeAttr("disabled");

      var score = 0;
      var testResult = {};
      var passedCount = 0;
      for (var i = 0; i < vm.tasks.length; i++) {
        var taskId = vm.tasks[i].id;
        if (vm.tasks[i].type == 3) {
          // trắc nghiệm
          var scoreItem = $("input[name='task" + taskId + "']:checked")
            .next()
            .text();
          var answerId = $("input[name='task" + taskId + "']:checked")
            .prev()
            .text();

          var answer = answerId + "";
          // answer['feedback'] = "";
          if (answerId && answerId !== "") {
            testResult[taskId] = answer;
          }

          if (scoreItem == undefined || scoreItem == null || scoreItem == "") {
            scoreItem = 0;
          }
          score += parseInt(scoreItem);
          if (parseInt(scoreItem) > 0) {
            passedCount++;
          }

          // xóa toàn bộ câu trả lời hiện tại
          $("input[name='task" + taskId + "']:checked").prop("checked", false);
        } else if (vm.tasks[i].type == 6) {
          // tự luận
          var resultObj = {};
          for (var j = 0; j < vm.writeQuestions[taskId].length; j++) {
            var id = vm.writeQuestions[taskId][j].id;
            var answer = $("#write-question" + taskId + "-" + id).val();
            resultObj[id + ""] = answer;
            if (answer == vm.writeQuestions[taskId][j].answer) {
              score += parseInt(vm.writeQuestions[taskId][j].grade);
            }

            // xóa hết câu trả lời đã nhập
            $("#write-question" + taskId + "-" + id).val("");
          }

          testResult[taskId] = resultObj;
        }
      }

      // data hiển thị popup thông báo kết quả làm bài
      vm.userScore = score;
      //$('#myMessage').modal();
      if (arg == "no-auth") {
        // chưa đăng nhập
        return;
      }

      // chuyển kết quả sang định dạng JSON
      testResult = JSON.stringify(testResult);

      var pass = score > vm.lesson.pass_marks;
      var now = new Date();
      now = Math.floor(now.getTime() / 1000);

      const dataResult = {
        lesson_id: vm.lesson.id,
        grade: score,
        data: testResult,
        total_grade: vm.lesson.total_marks,
        passed: pass,
        created: now
      };

      // console.log(dataResult);
      $.ajax({
        url: window.location.origin + "/khoa-hoc/gui-ket-qua",
        type: "POST",
        data: dataResult,
        async: true,
        error() {},
        success(response) {
          vm.results.unshift(response);
          $("#myModal").modal();
          vm.reviewTestResult(0);
          vm.saveExamProgress(passedCount);
        },
      });
    },

    /*in ra nội dung hoặc câu hỏi có chứa audio và đáp án ẩn
    /* quy tắc:
        {? audio.mp3 ?} audio câu hỏi
        {! audio.mp3 !} audio câu trả lời full
        {* đáp án *} text đáp án
    */
    printContentWithAudio(string) {
      if (string == null || string == "") return string;

      var result = string;

      //hiện audio câu đọc
      result = result.replace(
        /{\?/gi,
        '<a style="font-size: 18px; cursor: pointer; color: #f22b2b;" onclick="listenMp3(\''
      );
      result = result.replace(
        /\?}/gi,
        '\')"> &nbsp;<i class="zmdi zmdi-volume-up"></i></a>'
      );

      //ẩn audio đáp án
      result = result.replace(/{\!/gi, '<a style="display: none;">');
      result = result.replace(/\!}/gi, "</a>");

      // ẩn đáp dán dạng text
      result = result.replace(
        /{\*/gi,
        '(<a class="dapantext" style="opacity:0; width: 20px;">'
      );
      result = result.replace(/\*}/gi, "</a>)");

      return result;
    },

    //in ra content nhưng ở dạng đáp án
    printContentAsAswer(string) {
      if (string == null || string == "") return string;

      var result = string;

      //ẩn audio câu đọc
      result = result.replace(/{\?/gi, '<a style="display: none;">');
      result = result.replace(/\?}/gi, "</a>");

      //hiện audio đáp án
      result = result.replace(
        /{\!/gi,
        '<a style="font-size: 18px; cursor: pointer; color: green;" onclick="listenMp3(\''
      );
      result = result.replace(
        /\!}/gi,
        '\')"> &nbsp;<i class="zmdi zmdi-volume-up"></i></a>'
      );

      // hiện đáp dán dạng text
      result = result.replace(/{\*/gi, '<a style="opacity: 1;">');
      result = result.replace(/\*}/gi, "</a>");

      return result;
    },

    // hiển thị giờ : phút từ 1 biến datetime
    printTime(datetimeInt) {
      const vm = this;
      var datetime = new Date(1000 * datetimeInt);
      return (
        vm.convertNumber(datetime.getHours()) +
        ":" +
        vm.convertNumber(datetime.getMinutes())
      );
    },

    // hiển thị ngày / tháng / năm từ 1 biến datetime
    printDate(datetimeInt) {
      const vm = this;
      var datetime = new Date(1000 * datetimeInt);
      return (
        vm.convertNumber(datetime.getDate()) +
        "/" +
        vm.convertNumber(datetime.getMonth() + 1) +
        "/" +
        datetime.getFullYear()
      );
    },

    // thêm '0' nếu số nhỉ hơn 10
    convertNumber(number) {
      if (number < 10) {
        return "0" + number;
      }
      return number + "";
    },

    // xem lại 1 kết quả kiểm tra
    reviewTestResult(index) {
      const vm = this;
      console.log("kết quả --> ", vm.results);
      vm.currentResult = vm.results[index];
      vm.resultData = JSON.parse(vm.currentResult.data);
      for (var i = 0; i < vm.tasks.length; i++) {
        var id = vm.tasks[i].id;

        if (parseInt(vm.tasks[i].type) != 3) {
          // task type = 3 là câu hỏi trắc nghiệm
          continue;
        }

        var answerObj = vm.resultData[vm.tasks[i].id];
        if (answerObj == undefined || answerObj == "") {
          // dữ liệu cũ không lưu những câu không trả lời
          // nếu ko có data thì reset những câu trả lời đã chọn tương ứng
          for (var j = 0; j < vm.answers[id].length; j++) {
            vm.answers[id][j].checked = false;
          }
          continue;
        }

        const answerValue = answerObj;
        // kiểm tra câu trả lời để set vào 1 thuộc tính mới (checked cho việc hiển thị)
        for (var j = 0; j < vm.answers[id].length; j++) {
          vm.answers[id][j].checked = false;
          if (vm.answers[id][j].id == answerValue) {
            vm.answers[id][j].checked = true;
          }
        }
      }
    },

    // click xóa 1 test result
    removeTestResult(index) {
      const vm = this;
      vm.currentIndex = index;
    },

    confirmRemove() {
      const vm = this;
      var data = {
        result_id: vm.results[vm.currentIndex].id,
      };

      // xóa 1 test result
      $.ajax({
        url: window.location.origin + "/account/delete-test-result",
        type: "DELETE",
        data: data,
        async: true,
        error() {},
        success(response) {
          // console.log(response);
        },
      });

      vm.results.splice(vm.currentIndex, 1);
      $("#removeResult").modal("toggle");
    },
    convertTime(time, type) {
      var date = new Date(time);
      var result;
      var hours = date.getHours();
      var minutes = date.getMinutes();
      var day = date.getDate();
      var month = date.getMonth() + 1;
      var year = date.getFullYear();
      if (type == "time") {
        result = ("0" + hours).slice(-2) + ":" + ("0" + minutes).slice(-2);
      } else if (type == "date") {
        result =
          ("0" + day).slice(-2) + "/" + ("0" + month).slice(-2) + "/" + year;
      }
      return result;
    },
    playCDNAudio(audio) {
      const vm = this;

      // console.log("click play audio", audio);

      if (vm.flashcardMp3 == null)
        vm.flashcardMp3 = new Audio("https://mp3-v2.dungmori.com/" + audio);
      else {
        vm.flashcardMp3.pause();
        vm.flashcardMp3.currentTime = 0;
        vm.flashcardMp3 = new Audio("https://mp3-v2.dungmori.com/" + audio);
      }
      vm.flashcardMp3.play();
      vm.playingMp3 = true;
    },
    //play audio trong flashcards
    playAudio(audio) {
      // console.log("click play audio", audio);
      const vm = this;

      if (vm.flashcardMp3 == null)
        vm.flashcardMp3 = new Audio(vm.url + "/cdn/audio/" + audio);
      else {
        vm.flashcardMp3.pause();
        vm.flashcardMp3.currentTime = 0;
        vm.flashcardMp3 = new Audio(vm.url + "/cdn/audio/" + audio);
      }
      vm.flashcardMp3.play();
      vm.playingMp3 = true;
    },
    // pause audio trong flashcards
    pauseAudio() {
      const vm = this;
      vm.flashcardMp3.pause();
      vm.flashcardMp3.currentTime = 0;
      vm.playingMp3 = false;
    },
    // Gửi api thay đổi setting
    onChangeCheckboxSetting(e) {
      const vm = this;

      var data = {};
      data[e.target.name] = e.target.checked ? 1 : 0;
      $.post(
        window.location.origin + "/api/flashcards/option",
        data,
        function (response, status) {
          $.extend({}, vm.userSettings, data);
        }
      );
    },
    // Call api sửa user settings
    onChangeSelectSetting(e) {
      const vm = this;

      var data = {};
      data[e.target.name] = e.target.value;
      $.post(
        window.location.origin + "/api/flashcards/option",
        data,
        function (response, status) {
          $.extend({}, vm.userSettings, data);
        }
      );
    },
    // Highlight từ vựng trong ví dụ
    highlightVocab(cards) {
      const vm = this;
      cards.map(function (card, index) {
        var jp = card.value.jp;

        var replace = new RegExp(jp, "gi");
        // Regex chuỗi bắt đầu và kết thúc bằng dấu * đồng thời trích ra chuỗi nằm giữa 2 dấu *
        var manualReplace = new RegExp(/\*+(.+?)\*+/, "gi");

        var newEx = card.value.ex.replace(
          replace,
          '<span class="highlight">' + jp + "</span>"
        );

        // Lấy object chứa chuỗi được trích ra (extract capturing group)
        var manualHighlightWord = manualReplace.exec(newEx);

        // Nếu tồn tại object thì thay thế chuỗi bằng dom html
        if (manualHighlightWord) {
          newEx = newEx.replace(
            manualHighlightWord[0],
            '<span class="highlight">' + manualHighlightWord[1] + "</span>"
          );
        }
        while (manualHighlightWord) {
          manualHighlightWord = manualReplace.exec(newEx);
          if (manualHighlightWord)
            newEx = newEx.replace(
              manualHighlightWord[0],
              '<span class="highlight">' + manualHighlightWord[1] + "</span>"
            );
        }
        newEx = vm.mp3Ex(newEx);
        card.value.ex = newEx;
        return card;
      });
    },
    mp3Ex(ex) {
      // Regex chuỗi bắt đầu và kết thúc bằng dấu * đồng thời trích ra chuỗi nằm giữa 2 dấu *
      var manualReplace = new RegExp(/\{\!(.+?)\!\}/, "gi");

      var newEx = ex;
      // Lấy object chứa chuỗi đầu tiên được trích ra (extract capturing group)
      var manualHighlightWord = manualReplace.exec(newEx);
      if (manualHighlightWord) {
        var mp3Name = _.trim(manualHighlightWord[1]);
        newEx = newEx.replace(
          manualHighlightWord[0],
          `<span class='text-info noFlip' onclick='user.playCDNAudio("${mp3Name}")'>` +
            "<i class='fa fa-volume-up noFlip'></i>" +
            "</span>"
        );
      }
      // Kiểm tra xem còn chuỗi nào thoả mãn nữa không, nếu có thì chạy vòng lặp
      while (manualHighlightWord) {
        manualHighlightWord = manualReplace.exec(newEx);
        if (manualHighlightWord) {
          var mp3Name = _.trim(manualHighlightWord[1]);
          newEx = newEx.replace(
            manualHighlightWord[0],
            `<span class="text-info" onclick='user.playCDNAudio("${mp3Name}")'>` +
              "<i class='fa fa-volume-up noFlip'></i>" +
              "</span>"
          );
        }
      }

      return newEx;
    },
    // lấy danh sách flashcards
    fetchCards(type, first) {
      const vm = this;

      var data = {
        lessonId: vm.lesson.id,
        type: type ? type : vm.currentType,
      };
      vm.cardLoading = true;
      $.post(
        window.location.origin + "/api/flashcards",
        data,
        function (response, status) {
          if (first) {
            vm.firstTime = false;
          }
          var params = vm.getQueryParameters(window.location.search);

          vm.cardStats = response.cardStats;

          vm.flashcards = response.flashcards;
          if (vm.userSettings.isShuffle) {
            vm.flashcards = _.shuffle(response.flashcards);
          }
          if (!vm.focusFC) {
            vm.flashcards = vm.loadRestCards(type, vm.flashcards);
          }

          vm.flashcards = vm.flashcards.map(function (card, index) {
            card.value = JSON.parse(card.value);
            card.value.jp = card.value.jp.replace(/\n/gi, "<br/>");
            card.value.vi = card.value.vi.replace(/\n/gi, "<br/>");
            card.value.ex = card.value.ex.replace(/\n/gi, "<br/>");
            return card;
          });

          // console.log(vm.flashcards);
          if (params.hasOwnProperty("focus_fc"))
            vm.flashcards = vm.flashcards.filter(function (card) {
              return card.id == params.focus_fc;
            });

          vm.highlightVocab(vm.flashcards);
          vm.cardStats = response.cardStats;

          setTimeout(function () {
            if (typeof stackedCards != "undefined") stackedCards();
          }, 100);

          vm.currentCard = 0;

          if (type) {
            vm.currentType = type;
            vm.currentTempCard = 0;
          }

          if (vm.userSettings.autoPlay && vm.currentTempCard != "end") {
            vm.flashcards.forEach(function (card, index) {
              if (!vm.isIos) {
                // index === vm.currentCard && setTimeout(function(){ vm.playAudio(card.value.audio) }, 300);
              }
            });
          }
          if (vm.showComment) {
            if (params.hasOwnProperty("focus_fc") || vm.currentTempCard != "end") {
              //gán id để call load comments
              vm.thisFlashcardId = vm.flashcards[0].id;
              vm.fetchlistComments();
              // console.log("id thawngf dau tien", vm.thisFlashcardId);
            }
          }
        }
      );
    },

    // event chuyển thẻ
    swipeCard(status) {
      const vm = this;
      var data = {
        cardId: vm.flashcards[vm.currentCard].id,
        status: status,
      };

      if (typeof userLoggedIn != "undefined" && userLoggedIn) vm.callApiSwipe(data);
      else vm.guestSwipe(data);
      this.prevFlashcard.prev = { ...this.prevFlashcard };
      this.prevFlashcard.status = status;
    },
    guestSwipe(data) {
      const vm = this;
      vm.saveCurrentFlashcard(data.status);
      vm.currentCard++;

      if (vm.userSettings.autoPlay && vm.currentTempCard != "end") {
        vm.flashcards.forEach(function (card, index) {
          if (!vm.isIos) {
            // index === vm.currentCard && setTimeout(function(){ vm.playAudio(card.value.audio) }, 300);
          }
        });
      } else {
        vm.playingMp3 && vm.pauseAudio();
      }
    },
    callApiSwipe(data) {
      const vm = this;
      // Call api cập nhật số thẻ thuộc/chưa thuộc
      vm.swipeLoading = true;
      setTimeout(function () {
        $.post(
          window.location.origin + "/api/flashcards/learn",
          data,
          function (response, status) {
            response.changes.forEach(function (change, index) {
              vm.cardStats[change.stat] =
                vm.cardStats[change.stat] + change.value;
            });

            vm.saveCurrentFlashcard(data.status);

            vm.currentCard++;

            if (vm.currentTempCard == "end") {
              vm.saveExamProgress(0);
            }
            if (vm.userSettings.autoPlay) {
              vm.flashcards.forEach(function (card, index) {
                if (!vm.isIos) {
                  // index === vm.currentCard && setTimeout(function(){ vm.playAudio(card.value.audio) }, 300);
                }
              });
            } else {
              vm.playingMp3 && vm.pauseAudio();
            }
            vm.swipeLoading = false;
            if (vm.showComment) {
              if (vm.currentTempCard != "end") {
                //gán id để call load comments
                vm.thisFlashcardId = vm.flashcards[vm.currentCard].id;
                vm.fetchlistComments();
              }
            }
          }
        );
      }, 500);
    },
    undo() {
      if (this.currentCard > 0) {
        if (this.prevFlashcard.status) {
          this.cardStats.current_total++;
          if (this.prevFlashcard.status == "known") {
            this.cardStats.current_known--;
          } else {
            this.cardStats.current_unknown--;
          }
        }
        this.prevFlashcard = this.prevFlashcard.prev;
        this.currentCard--;
        const vm = this;
        vm.thisFlashcardId = vm.flashcards[vm.currentCard].id;
        vm.fetchlistComments();
        var currentMemo = {
          lesson: vm.lesson.id,
          learned: [
            {
              id: vm.flashcards[vm.currentCard].id,
              status: this.prevFlashcard.status,
            },
          ],
          current: vm.flashcards[vm.currentCard].id,
          type: vm.currentType,
        };
        // Nếu không có cookie fc_memo thì set bằng giá trị currentMemo luôn
        // Nếu không thì check xem bài này có trong cookie không, nếu không có thì nối mảng, nếu có thì cập nhật lại object trong mảng
        var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
        var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
        var fc_memo;
        if (!lesson) {
          fc_memo = _.concat(cookie_memo, currentMemo);
          localStorage.setItem("fc_memo", JSON.stringify(fc_memo));
        } else {
          fc_memo = cookie_memo.map(function (memo) {
            if (memo.lesson == vm.lesson.id) {
              memo.learned =
                vm.currentCard == vm.flashcards.length - 1
                  ? []
                  : memo.type == vm.currentType
                  ? memo.learned.filter(
                      (t) => t.id != vm.flashcards[vm.currentCard].id
                    )
                  : currentMemo.learned;
              memo.current = currentMemo.current;
              memo.type = currentMemo.type;
            }
            return memo;
          });
          localStorage.setItem("fc_memo", JSON.stringify(fc_memo));

          // Cập nhật lại bộ đếm
          vm.currentTempCard = currentMemo.current;
        }
      }
    },
    // Lưu flashcard đang học hiện tại, các flashcard đã học của bài học, type đang học dở
    saveCurrentFlashcard(status) {
      const vm = this;
      // @param lesson: id bài học
      // @param learned: mảng id các flashcard đã thuộc trong bài học
      // @param current: id flashcard lưu vết cuối cùng của bài học
      var currentMemo = {
        lesson: vm.lesson.id,
        learned: [
          {
            id: vm.flashcards[vm.currentCard].id,
            status: status,
          },
        ],
        current:
          vm.currentCard != vm.flashcards.length - 1
            ? vm.flashcards[vm.currentCard + 1].id
            : "end",
        type: vm.currentType,
      };
      // Nếu không có cookie fc_memo thì set bằng giá trị currentMemo luôn
      // Nếu không thì check xem bài này có trong cookie không, nếu không có thì nối mảng, nếu có thì cập nhật lại object trong mảng
      if (!localStorage.getItem("fc_memo")) {
        localStorage.setItem("fc_memo", JSON.stringify([currentMemo]));
      } else {
        var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
        var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
        var fc_memo;
        if (!lesson) {
          fc_memo = _.concat(cookie_memo, currentMemo);
          localStorage.setItem("fc_memo", JSON.stringify(fc_memo));
        } else {
          fc_memo = cookie_memo.map(function (memo) {
            if (memo.lesson == vm.lesson.id) {
              memo.learned =
                vm.currentCard == vm.flashcards.length - 1
                  ? []
                  : memo.type == vm.currentType
                  ? _.union([], memo.learned, currentMemo.learned)
                  : currentMemo.learned;
              memo.current = currentMemo.current;
              memo.type = currentMemo.type;
            }
            return memo;
          });
          localStorage.setItem("fc_memo", JSON.stringify(fc_memo));

          // cập nhật lại bộ đếm
          vm.currentTempCard = currentMemo.current;
        }
      }
      vm.cardStats["current_" + status]++;
    },
    // Kiểm tra flashcard có đang học dở không, nếu đang học dở thì load những card còn lại
    // Đang bị duplicate code nhưng nếu viết ra thành function riêng thì không chạy được
    loadRestCards(type, cards) {
      const vm = this;
      if (type) {
        vm.cardStats.current_total = cards.length;
        vm.cardStats.current_known = 0;
        vm.cardStats.current_unknown = 0;
        return cards;
      }
      if (!localStorage.getItem("fc_memo")) {
        vm.cardStats.current_total = cards.length;
        vm.cardStats.current_known = 0;
        vm.cardStats.current_unknown = 0;
        return cards;
      } else {
        var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
        var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
        if (!lesson) {
          vm.cardStats.current_total = cards.length;
          vm.cardStats.current_known = 0;
          vm.cardStats.current_unknown = 0;
          return cards;
        } else {
          var fc_memo = cards.filter(function (card) {
            return !_.some(lesson.learned, ["id", card.id]);
          });
          fc_memo.sort(function (x, y) {
            return x.id == lesson.current ? -1 : y.id == lesson.current ? 1 : 0;
          });
          vm.cardStats.current_total = fc_memo.length;

          // Đếm số thẻ thuộc và chưa thuộc tạm thời (hiển thị trong giao diện stack thẻ)
          vm.cardStats.current_known =
            _.countBy(lesson.learned, ["status", "known"]).true || 0;
          vm.cardStats.current_unknown =
            _.countBy(lesson.learned, ["status", "unknown"]).true || 0;
          return fc_memo;
        }
      }
    },
    // Lấy ra trạng thái đang học dở trong cookie, nếu không thì mặc định tất cả thẻ
    getCurrentState() {
      const vm = this;
      if (!localStorage.getItem("fc_memo")) {
        vm.currentType = "all";
      } else {
        var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
        var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
        if (!lesson) {
          vm.currentType = "all";
        } else {
          vm.currentType = lesson.type;
          vm.currentTempCard = lesson.current;
        }
      }
    },
    //////////comment flashcards/////
    //in ra thông tin email dạng nửa kín nửa hở
    printPrivateEmail(email) {
      // console.log("Biến permission", enableFIV);

      if (email != "<EMAIL>" && email != "<EMAIL>") {
        //nếu biến cho phép hiển thị = true
        if (enableFIV && enableFIV == true) return email;
        else return "****" + email.slice(4);
      } else return "<EMAIL>";
    },

    //in ra thông tin mobile dạng nửa kín nửa hở
    printPrivatePhone(phone) {
      if (phone != "0969.86.84.85") {
        //nếu biến cho phép hiển thị = true
        if (enableFIV && enableFIV == true) return phone;
        else return "*******" + phone.slice(-5);
      } else return "0969.86.84.85";
    },

    //in ra định dạng ngày giờ đẹp
    prettyDate(t) {
      var d = new Date(t);
      return (
        d.toLocaleDateString("en-GB", {
          timeZone: "Asia/Ho_Chi_Minh",
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        }) +
        " " +
        t.substring(11, 16)
      );
    },

    //in ra thông tin có dấu cách
    printInfo(info) {
      var result = _.escape(info);

      result = result.replace("<", "&#60;");
      result = result.replace(">", "&#62;");

      //xử lý xuống dòng
      result = info.replace(new RegExp("\r?\n", "g"), "<br />");

      var re =
        /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;
      return result.replace(re, function (match, lParens, url) {
        var rParens = "";
        lParens = lParens || "";

        // Try to strip the same number of right parens from url
        // as there are left parens.  Here, lParenCounter must be
        // a RegExp object.  You cannot use a literal
        //     while (/\(/g.exec(lParens)) { ... }
        // because an object is needed to store the lastIndex state.
        var lParenCounter = /\(/g;
        while (lParenCounter.exec(lParens)) {
          var m;
          // We want m[1] to be greedy, unless a period precedes the
          // right parenthesis.  These tests cannot be simplified as
          //     /(.*)(\.?\).*)/.exec(url)
          // because if (.*) is greedy then \.? never get a chance.
          if ((m = /(.*)(\.\).*)/.exec(url) || /(.*)(\).*)/.exec(url))) {
            url = m[1];
            rParens = m[2] + rParens;
          }
        }
        return (
          lParens +
          "<a href='" +
          url +
          "' target='_blank'>" +
          url +
          "</a>" +
          rParens
        );
      });
    },

    //tải về các comments cho lần tải đầu tiên
    fetchlistComments() {
      const vm = this;

      //focus vào comment được đánh dấu
      vm.ref = "notice";

      const data = {
        id: vm.thisFlashcardId,
        name: "flashcard",
        numpost: vm.numPost,
        ref: vm.ref,
      };

      $.post(
        window.location.origin + "/api/flashcard/comments-load-first",
        data,
        function (response, status) {
          //console.log(response);

          vm.listComments = response.comments;
          //console.log(vm.listComments);

          //nếu đã hết danh sách
          if (response.comments.length < vm.numPost) vm.theEnd = true;
          // Nếu comment có trong danh sách like của user thì liked = 1
          vm.listComments.map(function (comment) {
            if (response.listLike.includes(comment.id)) comment.liked = 1;
            return comment;
          });
          //ẩn biểu tượng loading
          vm.showLoading = false;
        }
      );
    },

    //tải các phản hồi
    fetchMoreComments() {
      const vm = this;

      //hiện biểu tượng loading
      vm.showLoading = true;

      setTimeout(function () {
        const data = {
          id: vm.thisFlashcardId,
          name: "flashcard",
          numpost: vm.numPost,
          page: vm.page++,
        };
        //console.log(data);
        $.post(
          window.location.origin + "/api/flashcard/comments-load-more",
          data,
          function (response, status) {
            //nối thêm mảng tải thêm
            vm.listComments = vm.listComments.concat(response.comments);

            //nếu đã hết danh sách
            if (response.comments.length < vm.numPost) vm.theEnd = true;
            vm.listComments.map(function (comment) {
              if (response.listLike.includes(comment.id)) comment.liked = 1;
              return comment;
            });
            //ẩn biểu tượng loading
            vm.showLoading = false;
            console.log(vm.showLoading);
          }
        );
      }, 500);

      //console.log('tải thêm các bình luận');
    },

    //đăng bình luận mới
    postNewComment(tbid) {
      const vm = this;

      // bỏ qua comment rỗng
      if (
        $("#comment-content").val() == null ||
        $("#comment-content").val() == undefined ||
        $("#comment-content").val().trim() == ""
      ) {
        alert("Vui lòng nhập nội dung");
        return;
      }

      var form_data = new FormData();
      form_data.append("tbid", vm.thisFlashcardId);
      form_data.append("tbname", "flashcard");
      form_data.append("content", $("#comment-content").val());

      vm.showLoadingNewComment = true;

      setTimeout(function () {
        $.ajax({
          url: window.location.origin + "/api/comments/add-new-comment",
          type: "POST",
          data: form_data,
          contentType: false,
          cache: false,
          processData: false,
          success(response) {
            console.log(response);
            if (response == "imagesize")
              alert("ảnh vượt quá dung lượng cho phép");
            else if (response == "type") alert("định dạng ảnh không cho phép");
            else {
              var newComment = response;
              response.liked = 0;
              response.count_like = 0;

              vm.listComments.unshift(newComment);
              $("#comment-content").val("");
              $("#comment-content").css("height", "70px");
            }
            vm.showLoadingNewComment = false;
          },
        });
      }, 500);
    },

    // ghim comment len dau
    pinComment(comment) {
      const vm = this;
      var data = {
        id: comment.id,
      };
      $.post(
        window.location.origin + "/backend/binhluan/api/pin",
        data,
        function (res) {
          if (res.code === 200) {
            vm.listComments.forEach(function (comment) {
              if (comment.id === res.data.id) {
                comment.pin = res.data.pin;
              }
            });
          }
        }
      );
    },

    //xóa comment theo id
    delComment(id) {
      const vm = this;
      setTimeout(function () {
        $.post(
          window.location.origin + "/api/comments/delete-comment",
          { id: id },
          function (response, status) {
            if (response == "success") {
              vm.listComments = vm.listComments.filter(function (comment) {
                return comment.id != id;
              });
            } else {
              alert("thao tác không hợp lệ");
            }
          }
        );
      }, 500);
    },
    // like comment
    likeComment(id) {
      const vm = this
      vm.likeLoading = true;
      setTimeout(function () {
        $.post(
          window.location.origin + "/api/comments/like-comment",
          { id: id },
          function (response, status) {
            if (response.code == 200) {
              vm.listComments.map(function (comment) {
                if (comment.id == id) {
                  comment.count_like = response.data.count_like;
                  comment.liked = response.data.liked;
                }
                return comment;
              });
            } else if (response.code == 404) {
              alert("Bình luận không tồn tại");
            } else {
              alert("Lỗi hệ thống");
            }
            vm.likeLoading = false;
          }
        );
      }, 500);
    },
    printFirstComment(comment) {
      return _.truncate(comment, { length: 100 });
    },
    getQueryParameters(str) {
      return (str || document.location.search)
        .replace(/(^\?)/, "")
        .split("&")
        .map(
          function (n) {
            return (n = n.split("=")), (this[n[0]] = n[1]), this;
          }.bind({})
        )[0];
    },
    scrollToComment() {
      $("html, body").animate(
        {
          scrollTop: $("#list-comments").offset().top,
        },
        600
      );
    },
    saveProgress(videoProgress, examProgress, passedQuestion = 0) {
      var saveProgressData = {
        id: this.lesson.id,
        videoTask: this.taskStats.videoTask,
        youtubeTask: this.taskStats.youtubeTask,
        examTask: this.taskStats.examTask,
        flashcardTask: this.taskStats.flashcardTask,
        current_video_progress: videoProgress,
        current_exam_progress: examProgress,
        passed: passedQuestion
      };
      $.post(
        window.location.origin + "/api/lesson/progress/save",
        saveProgressData
      );
    },
    saveExamProgress(passedQuestion) {
      this.saveProgress(1, 100, passedQuestion);
      this.showSuggestion();
    },
    onLoadSaveProgress() {
      if (
        !this.taskStats.videoTask &&
        !this.taskStats.examTask &&
        !this.taskStats.youtubeTask &&
        !this.taskStats.flashcardTask &&
        !this.taskStats.quizTask
      ) {
        this.saveProgress(100, 100);
      }
    },
    async onExitSaveProgress() {
      const vm = this;
      // Nếu bài học có video
      if (this.taskStats.videoTask || this.taskStats.youtubeTask) {
        if (this.progress?.video_progress >= 85 && this.currentLessonPoints.length) return;
        if (this.taskStats.videoTask < 85 && this.taskStats.youtubeTask < 85) {
          setInterval(function () {
            vm.saveVideoProgress(
                vm.taskStats.videoTask,
                vm.taskStats.youtubeTask,
                vm.taskStats.examTask
            );
          }, 30000);
          window.addEventListener("beforeunload", function () {
            vm.saveVideoProgress(
                vm.taskStats.videoTask,
                vm.taskStats.youtubeTask,
                vm.taskStats.examTask
            );
          });

          if (this.taskStats.videoTask) {
            var videojsPlayer = videojs("myplayer_" + playerId, options);
            videojsPlayer.on("seeking", function (e) {
              // vm.saveVideoProgress(
              //     vm.taskStats.videoTask,
              //     vm.taskStats.youtubeTask,
              //     vm.taskStats.examTask
              // );
            });
          }
          if (this.taskStats.youtubeTask) {
            youtubePlayer.on("seeking", function (e) {
              // vm.saveVideoProgress(
              //     vm.taskStats.videoTask,
              //     vm.taskStats.youtubeTask,
              //     vm.taskStats.examTask
              // );
            });
          }
        }
      }
    },
    saveVideoProgress() {
      const vm = this;
      var current_time;
      var duration;
      var current_percent;
      if (this.taskStats.videoTask) {
        current_time = videojs("myplayer_" + playerId, options).currentTime();
        duration = videojs("myplayer_" + playerId, options).duration();
      }
      if (this.taskStats.youtubeTask) {
        current_time = youtubePlayer.currentTime;
        duration = youtubePlayer.duration;
      }
      // Thời gian tổng <= 4', không tính 60s cuối vào progress
      var isLastMinute = current_time >= duration - 60;
      var isLastTwoMinutes = current_time >= duration - 120;

      var currentPercent = isLastMinute
        ? 100
        : isLastTwoMinutes
        ? 100
        : (current_time * 100) / duration;

      if (currentPercent >= 85) {
        currentPercent = 100;
      }
      if (
        (this.taskStats.videoTask || this.taskStats.youtubeTask) &&
        this.taskStats.examTask &&
        currentPercent == 0
      ) {
        currentPercent = 1;
      }
      if (currentPercent >= 80) this.showSuggestion();
      if (this.taskStats.youtubeTask && currentPercent < 85) return;
      if (this.progress) {
        this.$set(this.progress, 'video_progress', currentPercent)
      } else {
        this.progress = {
          video_progress: currentPercent,
          example_progress: 1
        }
      }
      this.saveProgress(currentPercent, 1);
    },
    showSuggestion() {
      var cookie = getCookie("suggestion_cooldown_" + this.lesson.course_id);
      if (cookie) return;
      var LESSON_ID_TO_SUGGEST_COURSE = [];
      N5_IDS.forEach(function (id) {
        LESSON_ID_TO_SUGGEST_COURSE.push(
          _.merge({ lessonId: id }, SUGGEST_COURSE[_.sample([1, 2, 3])])
        );
      });
      N4_IDS.forEach(function (id) {
        LESSON_ID_TO_SUGGEST_COURSE.push(
          _.merge({ lessonId: id }, SUGGEST_COURSE[_.sample([4, 5])])
        );
      });
      N3_IDS.forEach(function (id) {
        LESSON_ID_TO_SUGGEST_COURSE.push(
          _.merge({ lessonId: id }, SUGGEST_COURSE[_.sample([6, 7])])
        );
      });
      N2_IDS.forEach(function (id) {
        LESSON_ID_TO_SUGGEST_COURSE.push(
          _.merge({ lessonId: id }, SUGGEST_COURSE[8])
        );
      });
      var suggest = _.find(LESSON_ID_TO_SUGGEST_COURSE, {
        lessonId: this.lesson.id,
      });
      if (suggest && !this.suggestionShowed) {
        this.suggestionCount = _.random(10799, 17999);
        this.suggestToShow = suggest;
        this.suggestToShow.url =
          this.url + "/assets/img/popup/" + suggest.image;
        this.suggestionShowed = true;
      }
      setCookie("suggestion_cooldown_" + this.lesson.course_id, true, 7);
    },
    sendSuggestMessage() {
      const vm = this;
      showChatbox();
      setTimeout(function () {
        var msg =
          "Tôi được gợi ý " +
          vm.suggestToShow.title +
          ", tôi cần tư vấn về khóa học này";
        chatbox.sendTemplateMess(msg);
        vm.suggestionShowed = false;
      }, 1000);
    },
    initFlashcards() {
      const vm = this;
      if (isFlashcards) {
        if (typeof userLoggedIn != "undefined" && userLoggedIn) this.showComment = true;

        this.userSettings = $.extend(
          {},
          typeof defaultUserSettings != "undefined" ? defaultUserSettings : null,
          typeof userSettings != "undefined" ? userSettings : null
        );

        this.cardStats = typeof defaultCardStats != "undefined" ? defaultCardStats : null;

        var params = this.getQueryParameters(window.location.search);
        if (params.hasOwnProperty("focus_fc")) vm.focusFC = true;

        this.getCurrentState();
        this.fetchCards();

        $(".stats").css("opacity", 100);
        $(".empty-cards").css("opacity", 100);
      }
    },

    chooseRoadmap() {
      const vm = this;

      swal({
        title: "Xác nhận",
        text: "Bạn có chắc chắn muốn thay đổi lộ trình học ?",
        icon: "warning",
        buttons: true,
        dangerMode: true,
      }).then(function (yes) {
        if (yes) {
          vm.submiting = true;

          $.post(
            window.location.origin + "/khoa-hoc/change-roadmap",
            {
              courseId: this_course_id,
              periodId: vm.currPeriod,
            },
            function (response) {
              if (response == "success") {
                swal("Bạn đã thay đổi lộ trình học thành công!", {
                  icon: "success",
                });

                setTimeout(function () {
                  location.reload();
                }, 1000);
              }
            }
          );
        }
      });
    },
    initTimer() {
      const vm = this;
      this.initTime = Date.now();
      document.addEventListener("visibilitychange", function () {
        if (document.visibilityState === "hidden") {
          vm.saveTimer();
        } else {
          vm.initTime = Date.now();
        }
      });
    },
    saveTimer() {
      if (this.meid) {
        var checkpointTime = Date.now() - this.initTime;
        var data = new FormData();
        data.append("joinedTime", this.initTime);
        data.append("time", checkpointTime);
        data.append("lessonId", this.lesson.id);
        data.append("_token", $('meta[name="csrf-token"]').attr("content"));
        navigator.sendBeacon("/save-timer", data);
      }
    },
  },
  mounted() {
    if (this.meid) {
      this.initTimer();
      // Lưu progress nếu là nội dung
      this.onLoadSaveProgress();
      // Lưu progress nếu là video
      this.onExitSaveProgress();
    }
    // hiển thị danh sách kết quả kiểm tra sau khi load data
    $("#result").css("display", "block");
    $(".server-localtion-container").css("display", "block");
    this.initFlashcards();
    // this.showSuggestion();
  },
});

$(document).ready(function() {
  $("#comment-content").on("keydown", function (e) {
    if (e.key == "Enter" && !e.shiftKey && typeof user != "undefined") {
      e.preventDefault();
      user.postNewComment();
    }
  });
});
