function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
        	console.log(e);
            $('#user-avatar-preview').attr('src', e.target.result);
            $(".save-avatar").css("display", 'inline');
            $(".save-admin-avatar").css("display", 'inline');
            $(".change-avatar").css("display", 'none');
            $(".clear-preview-upload").css("display", 'inline');
        };
        reader.readAsDataURL(input.files[0]);
    }
}

$(".change-avatar").click(function(){
	console.log("change avatar");
	$("#inputAvatar").click();
});

// {{-- ấn nút lưu lại và upload --}}
$(".save-avatar").click(function(){
	console.log("Lưu lại");

	var dataAvatar = new FormData($("#avatar-form")[0]);
                 
    console.log(dataAvatar);

    $.ajax({
        url: window.location.origin+"/account/change-avatar", 
        type: 'post', processData: false, contentType: false, data : dataAvatar,
        success: function(response) {
            console.log(response);
            if(response == "success"){
            	location.reload();
            }else if(response == "imagesize"){
            	alert("Lỗi dung lượng ảnh vượt quá giới hạn cho phép 3 MB");
            	location.reload();
            }else if(response == "imageType"){
            	alert("Định dạng ảnh không hợp lệ");
            	location.reload();
            }else{
            	alert("Lỗi khi up ảnh");
            	location.reload();
            }
        }
    });
	
});
$(".save-admin-avatar").click(function(){
    console.log("Lưu lại");

    var dataAvatar = new FormData($("#avatar-form")[0]);

    console.log(dataAvatar);

    $.ajax({
        url: window.location.origin+"/backend/account/change-avatar",
        type: 'post', processData: false, contentType: false, data : dataAvatar,
        success: function(response) {
            console.log(response);
            if(response == "success"){
                location.reload();
            }else if(response == "imagesize"){
                alert("Lỗi dung lượng ảnh vượt quá giới hạn cho phép 3 MB");
                location.reload();
            }else if(response == "imageType"){
                alert("Định dạng ảnh không hợp lệ");
                location.reload();
            }else{
                alert("Lỗi khi up ảnh");
                location.reload();
            }
        }
    });

});
// {{-- ẩn nút clear --}}
$(".clear-preview-upload").click(function(){
	$(".clear-preview-upload").css("display", 'none');
	$(".save-avatar").css("display", 'none');
	$(".save-admin-avatar").css("display", 'none');
	$(".change-avatar").css("display", 'inline');
	$('#user-avatar-preview').attr('src', userAvatar);
	$("#inputAvatar").val(''); 
});
