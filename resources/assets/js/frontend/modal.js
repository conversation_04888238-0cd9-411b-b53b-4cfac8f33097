// register modal component
Vue.component("modal", {
  template: "#modal-template",
  props: {
    width: {
      type: String,
      default: function () {
        return "80vw";
      },
    },
    bgColor: {
      type: String,
      default: function () {
        return "#fff";
      },
    },
    shadow: {
      type: String,
      default: function () {
        return "0 2px 8px rgba(0, 0, 0, .33)";
      },
    },
    padding: {
      type: String,
      default: function () {
        return "20px";
      },
    },
  },
  computed: {
    containerStyle: function () {
      return "" + this.bgColor + this.shadow + this.width;
    },
  },
});
