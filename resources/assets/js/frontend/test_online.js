Vue.filter('time', function (value) {
    return moment(value).format('HH:mm');
});
Vue.filter('datetime', function (value) {
    return moment(value).format('DD/MM/YYYY HH:mm');
});
Vue.filter('point', function (value) {
    return value ? value.toFixed(1) : 0;
});
Vue.filter('passed', function (value) {
    return value ? 'Đạt' : 'Không đạt';
});
new Vue({
    el: '#test-online-home',
    data: {
        tab: '',
        tabs: [
            {
                value: 'test',
                label: 'Làm bài'
            },
            {
                value: 'rank',
                label: 'Xếp hạng'
            },
            {
                value: 'history',
                label: '<PERSON><PERSON>ch sử'
            },
        ],
    },
    mounted: function() {
        this.tab = 'test';
    },
});
Vue.filter('two_digits', function (value) {
    if (value.toString().length <= 1) {
        return '0'+ value.toString();
    }
    return value.toString();
});
Vue.filter('slash_date', function (value) {
    return moment(value, 'YYYY-MM-DD').format('DD/MM/YYYY');
});
Vue.component('test-online-list', {
    template: '#test-online-list',
    data: function () {
        return {
            url: window.location.origin,
            api: api,
            userId: userId,
            groups: [],
            time: moment().format('YYYY-MM-DD'),
            lessonTypes: {
                N1: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
                N2: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
                N3: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
                N4: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
                N5: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
            },
        }
    },
    computed: {
        today: function() {
            return moment().format('YYYY-MM-DD');
        },
        tomorrow: function() {
            return moment().add('days', 1).format('YYYY-MM-DD');
        },
        lessonTitles: function () {
            return this.lessonTypes[this.examDetail.course];
        },
    },
    watch: {
        time: function(value) {
            this.getSchedules();
        },
    },
    methods: {
        getLessonTitle: function(exam, lesson) {
            var titles = this.lessonTypes[exam.course];
            return titles[lesson.type - 1];
        },
        getSchedules: function() {
            var vm = this;
            var data = {
                userId: this.userId,
                time: this.time,
            };
            $.get(this.url + "/test-online/get-schedule", data, function(res) {
                vm.groups = res.data;
            });
        },
        goTest: function (exam, lesson) {
            if (!exam.available) return;
            if (lesson) window.location.href = exam.url + '?lesson=' + lesson.type;
            else window.location.href = exam.url;
        },
    },
    mounted: function () {
        this.getSchedules();
    },
});
Vue.component('test-online-rank',{
    template: '#test-online-rank',
    data: function() {
        return {
            url: window.location.origin,
            api: api,
            tab: 'day',
            date: moment().format('YYYY-MM-DD'),
            group_id: null,
            exam_id: null,
            options: {
                firstDayOfWeek: 1,
            },
            userId: userId,
            groups: [],
            ladder: [],
        };
    },
    computed: {
        exams: function () {
            var group = _.find(this.groups, {'id': this.group_id});
            if (group) {
                return group.exams;
            }
            return [];
        },
        userResult: function() {
            return _.find(this.ladder, ['user_id', this.userId]);
        },
        userResultIndex: function() {
            return _.findIndex(this.ladder, ['user_id', this.userId]);
        }
    },
    watch: {
        date: function (val) {
            var vm = this;
            this.getGroups();
            var group = _.find(this.groups, {'id': this.group_id});
            if (group) {
                this.exams = group.exams;
                if (group.exams.length > 0) {
                    this.exam_id = group.exams[0].exam.id;
                } else {
                    this.exam_id = null;
                }
            } else {
                this.exams = [];
            }
        },
        group_id: function () {
            var group = _.find(this.groups, {'id': this.group_id});
            if (group) {
                this.exams = group.exams;
                if (this.tab == 'all') {
                    this.getRanking();
                    return;
                }
                if (group.exams.length > 0) {
                    this.exam_id = this.exams[0].exam.id;
                } else {
                    this.exam_id = null;
                }
            } else {
                this.exam_id = null;
            }

            // console.log(this.exam_id);
            //
            // this.$nextTick(function () {
            //   if (this.$refs.examSelector) {
            //     this.$refs.examSelector.init();
            //   }
            // })
        },
        exam_id: function (value) {
            if (value) {
                this.getRanking();
            } else {
                this.ladder = [];
            }
        },
        tab: function (value) {
            if (value) {
                this.getRanking();
            }
        },
    },
    mounted: function () {
        this.getGroups();
    },
    methods: {
        rankStyle: function(index) {
            var color = '';
            var fontSize = '25px;'
            switch (true) {
                case (index == 0):
                    color = '#FFC700';
                    break;
                case (index == 1):
                    color = '#FF9900';
                    break;
                case (index == 2):
                    color = '#EA5400';
                    break;
                case (index > 2):
                    color = '#96D962';
                    fontSize = '18px';
                    break;
                default:
                    color = '#000';
            }
            return 'color: ' + color + '; border-color: ' + color + '; font-size: ' + fontSize;
        },
        rankBg: function(index) {
            var color = '';
            switch (true) {
                case (index == 0):
                    color = '#FFF9C0';
                    break;
                case (index == 1):
                    color = '#F9E2A1';
                    break;
                case (index == 2):
                    color = '#FFC896';
                    break;
                case (index > 2 && index < 10):
                    color = '#EDFFDF';
                    break;
                default:
                    color = '#FFFFFF';
            }
            var opacity = 1 - (index / 10);
            var _opacity = Math.round(Math.min(Math.max(opacity || 1, 0), 1) * 255);
            return 'background: ' + color + _opacity.toString(16).toUpperCase() + '; box-shadow:  5px 5px 6px #e3e3e3, -5px -5px 6px #ffffff;';
        },
        getGroups: function () {
            var vm = this;
            var data = {
                userId: this.userId,
                date: this.date,
            };
            $.get(this.url + "/test-online/get-groups", data, function(res) {
                var blackList = [2,3,4,6];
                vm.groups = res.data.filter(function (group) {
                    return !blackList.includes(group.id);
                });
                if (vm.groups.length > 0) {
                    vm.group_id = vm.groups[0].id;
                }
            });
        },
        getRanking: function() {
            var vm = this;
            var data = {
                type: this.tab,
                group_id: this.group_id,
                exam_id: this.exam_id,
                date: this.date,
            }
            $.get(this.url + "/test-online/get-ranking", data, function(res) {
                if (vm.tab == 'day') {
                    vm.ladder = res.data;
                } else {
                    vm.ladder = res.data.map(function (item) {
                        const userExams = res.percentages.filter(o => o.user_id === item.user_id)
                        const percentages = userExams.map(o => o.exams ? o.total_score * 100 / o.exams?.maximum_point : 0);
                        item.percentage = Math.round(_.sum(percentages) / res.exam_count);
                        item.average = (item.total_point / res.exam_count);
                        return item;
                    });
                    vm.ladder = _.orderBy(vm.ladder, 'average', 'desc');
                }
            });
        },
    },
});
Vue.component('test-online-history', {
    template: '#test-online-history',
    data: function () {
        return {
            url: window.location.origin,
            tab: 'test',
            history: [],
            date: '',

            showResult: false,
            currentResult: {},
            lessonTypes: {
                N1: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
                N2: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
                N3: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
                N4: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
                N5: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
            },
            options: {
                firstDayOfWeek: 1,
            },
        };
    },
    computed: {
        lessonTitles: function () {
            return this.lessonTypes[this.currentResult.exam.course];
        },
        answers: function () {
            var answer1 = JSON.parse(this.currentResult.exam.result[0].data_1);
            var answer2 = JSON.parse(this.currentResult.exam.result[0].data_2);
            var answer3 = JSON.parse(this.currentResult.exam.result[0].data_3);
            return _.merge(answer1, answer2, answer3);
        },
        mp3: function () {
            var exam = _.find(this.currentResult.exam.lessons, ['type', '3']);
            return exam ? exam.mp3 : null;
        },
    },
    watch: {
        date: function (val) {
            this.getHistory();
        },
    },
    mounted: function () {
        this.getHistory();
    },
    methods: {
        validateResult(exam) {
            if (exam.exam.result.length === 0) return false
            if (!exam.exam.time_split) return true
            if (!exam.available) return true
            if (exam.exam.lessons.length === 1 && exam.exam.result[0].data_1) return true
            if (exam.exam.lessons.length === 2 && exam.exam.result[0].data_1 && exam.exam.result[0].data_2) return true
            if (exam.exam.lessons.length === 3 && exam.exam.result[0].data_1 && exam.exam.result[0].data_2 && exam.exam.result[0].data_3) return true
        },
        init: function () {
            this.getHistory();
        },
        getHistory: function () {
            var vm = this;
            var data = {
                date: this.date,
            }
            $.get(vm.url + "/test-online/get-history", data, function (res) {
                let data = res.data;
                data.map(item => {
                    item.exams.map(exams => {
                        exams.exam.result = exams.exam.result.filter(q => {
                            return q.community_exam_id === exams.id;
                        });
                        return exams;
                    });
                    return item;
                });
                vm.history = res.data;
            });
        },
        isPassed: function(exam) {
            if (exam.result.length > 0) {
                return exam.result[0].is_passed;
            }
            return false;
        },
        answerLength: function (content) {
            if (!content) return 0;
            var text = />(.*?)</g.exec(content)
            return text ? text[1].length : content.length;
        },
        show: function (result) {
            this.currentResult = result;
            this.showResult = true;
        },
        closeModal: function () {
            this.showResult = false;
            this.currentResult = {};
        },
    },
});
