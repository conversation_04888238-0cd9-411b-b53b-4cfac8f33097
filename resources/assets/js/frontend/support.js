var user = new Vue({

    el: '#form-push-user-opinion',
    data: {
        fullname      : "",
        email         : "",
        phoneNumber   : "",
        optionType    : "",
        optionContent : "",
        errors        : [],
        isBtnSubmitReady: true,   // gửi email mất 1 khoảng thời gian nên trong khi đang gửi, không cho phép gửi tiếp 1 mail khác
    },

    methods: {
      sendEmail: function(){
        var vm = this;

        if (!vm.isBtnSubmitReady) {
          return;
        }

        var token= $('#csrf_token').val();

        var data = {
          '_token'        : token,
          'fullname'      : $('#fullname').val(),
          'email'         : $('#user-email').val(),
          'phoneNumber'   : $('#phone-number').val(),
          'optionType'    : $('#option-type').val(),
          'optionContent' : $('#option-content').val(),
        };

        // kiểm tra ký tự đặc biệt
        if (checkspecialSymbol(data.fullname) || checkspecialSymbol(data.email) || checkspecialSymbol(data.phoneNumber) || checkspecialSymbol(data.optionContent)) {
          vm.errors = [];
          $("#error-list").css("color", "red");
          vm.errors.push("Thông tin không được chứa ký tự đặc biệt");
          return;
        }

        // kiểm tra thông tin trống
        if (data.fullname == null || data.fullname == undefined || data.fullname == "" ||
            data.email == null || data.email == undefined || data.email == "" ||
            data.phoneNumber == null || data.phoneNumber == undefined || data.phoneNumber == "" ||
            data.optionType == null || data.optionType == undefined || data.optionType == "" ||
            data.optionContent == null || data.optionContent == undefined || data.optionContent == "") {

          vm.errors = [];
          $("#error-list").css("color", "red");
          vm.errors.push("Vui lòng nhập thông tin còn trống");
          return;
        }

        vm.isBtnSubmitReady = false;
        $("#btn-submit").unbind('mouseenter mouseleave');
        vm.errors = [];
        $("#loader-send-email").css('display', 'block');
        // console.log(data);

        $.ajax({
          url: window.location.origin+"/gui-y-kien", type:"POST", data: data, async: true,
          beforeSend: function (xhr) { if (token) return xhr.setRequestHeader('X-CSRF-TOKEN', token); },
          error: function() { return false; },
          success: function(response) {
            // console.log("Thành công : "+response);
            if (response == "success") {
              vm.errors = [];
              $("#error-list").css("color", "blue");
              vm.errors.push("Ý kiến đóng góp của bạn đã được gửi đến hòm thử của Dungmori. Cảm ơn bạn rất nhiều!");
              setTimeout(function() {
                vm.errors = [];
              }, 3000);
              // Xóa data đang hiển thị
              $("#fullname").val("");
              $("#user-email").val("");
              $("#phone-number").val("");
              $("#option-content").val("");

              // đưa nút submit trờ về trạng thái ban đầu
              vm.isBtnSubmitReady = true;
              $("#loader-send-email").css('display', 'none');
            }
          }
        });
      },
    },

    mounted: function() {
      //sau khi load dữ liệu sẽ hiển thị
      $("#error-list").css('display', 'block');
    }
});