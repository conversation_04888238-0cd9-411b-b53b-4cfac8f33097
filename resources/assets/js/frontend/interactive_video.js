var options = {
    "playbackRates": [0.75, 1, 1.25, 1.5, 2],
    "preload": 'auto',
    controlBar: {
        volumeMenuButton: {
            vertical: true,
            inline: false,
            volumeBar: {
                vertical: true
            },
            volumeLevel: true
        }
    }
};
var interactVideoPlayer = new Vue({
    el: '.dmrx-player',

    data: function () {
        return {
            url: window.location.origin,
            userId: userId,
            player: null,
            currentMediaName: atob(atob(atob(mdaId))),
            currentServerId: listServers[0].id,
            currentServerUrl: listServers[0].url,
            currentQuality: "720p", // 720p, 480p, 360p
            currentTime: 0,
            taskId: taskId,
            listServers: listServers,
            questions: [],
            modal: [],
            modalOpenStatus: false,
            modalComponents: [],
            playbackSpeed: playbackSpeed,
            cdbh: cdbh,
            progress: currentProgress,
        };
    },
    watch: {
        currentTime: {
            // We have to move our method to a handler field
            handler: function (val) {
                var vm = this;

                vm.modalComponents.forEach(function (component) {
                    if (!component.question.modal) {
                        component.currentTime = vm.currentTime;
                    }
                });

                vm.questions.forEach(function (question, index) {
                    if (question.type === 1) {
                        let showShortQuestion = question && !question.skip && !vm.modal[index].opened_ && _.inRange(val, question.time_start, question.time_end + question.a_length);
                        if (showShortQuestion) {
                            vm.openModal(index);
                        }
                        if (!_.inRange(val, question.time_start, question.time_end + question.a_length)) {
                            if (vm.modal[index].opened_) {
                                vm.closeModal(index);
                            }
                        }
                    }
                    if (question.type === 2) {
                        let showLongQuestion = question && !question.open_once && !vm.modal[index].opened_ && _.inRange(val, question.time_start - 0.25, question.time_start + 0.25);

                        if (showLongQuestion) {
                            vm.openModal(index);
                        }
                    }
                });
            }
        }
    },
    computed: {
        getVideoQuality: function () {
            if (!videoFull && this.currentQuality == '1080p') {
                return '720p';
            }
            return this.currentQuality;
        }
    },
    methods: {
        /* chọn server khác di động */
        changeServerBySelectTag: function () {
            var vm = this;
            setCookie('v_location', vm.currentServerId, 30);
            location.reload();
        },

        /* chọn server khác */
        changeServerLocaltion: function (id) {
            var vm = this;
            var now = new Date();
            //đặt lại cookie cài đặt nhớ trong 30 ngày
            setCookie('v_location', id, 30);
            setCookie('current_time_' + lesson_lessonDetail.id, vm.player.currentTime(), 10);
            this.checkServerLocation();
            var src =
                vm.currentServerUrl +
                "/" +
                vm.getVideoQuality +
                "/" +
                vm.currentMediaName +
                "/index.m3u8?" +
                now.getMinutes();

            console.log('src..........107', src)
            $.ajax({
                url: "/video/get-link?url=" + src,
                type: "GET",
                success: function (res) {
                    console.log("res..........", res)
                    vm.player.src({
                        src: res,
                        type: "application/x-mpegURL",
                        withCredentials: false,
                    });
                },
            });
            vm.initPlayVideo();
            // location.reload();

        },

        playAudio: function (audio) {
            var vm = this;

            vm.mp3 = new Audio(vm.url + '/assets/audio/' + audio);
            vm.mp3.play();
        },

        /* chọn video chất lượng khác */
        changeQuality: function (quality) {
            var vm = this;
            var now = new Date();
            vm.currentQuality = quality;
            var qualityData = vm.getVideoQuality;

            //đặt lại cookie cài đặt nhớ trong 30 ngày
            setCookie('v_quality', quality, 30);
            setCookie('current_time_' + lesson_lessonDetail.id, vm.player.currentTime(), 10);

            var src =
                vm.currentServerUrl +
                "/" +
                qualityData +
                "/" +
                vm.currentMediaName +
                "/index.m3u8?" +
                now.getMinutes();
            console.log('src..........151', src)

            $.ajax({
                url: "/video/get-link?url=" + src,
                type: "GET",
                success: function (res) {
                    console.log('res..............155', res)
                    vm.player.src({
                        src: res,
                        type: "application/x-mpegURL",
                        withCredentials: false,
                    });
                },
            });

            vm.initPlayVideo();
        },

        // tua lại
        fastRewind: function () {
            var vm = this;
            vm.player.currentTime(vm.player.currentTime() - 10);
            if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                $('.left-fast-icon').css('display', 'inline');
                setTimeout(function () {
                    $('.left-fast-icon').css('display', 'none');
                }, 1200);
            }
        },

        // tua đi
        fastForward: function () {
            var vm = this;
            vm.player.currentTime(vm.player.currentTime() + 10);
            if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                $('.right-fast-icon').css('display', 'inline');
                setTimeout(function () {
                    $('.right-fast-icon').css('display', 'none');
                }, 1200);
            }
        },

        initVideo: function () {
            var vm = this;

            vm.player = videojs('myplayer_' + playerId, options);
            var now = new Date();
            vm.player.ready(function () {
                if (this.playbackSpeed) {
                    this.playbackRate(this.playbackSpeed);
                }
                //nếu tồn tại ghi nhớ thời gian chạy video
                if (getCookie("current_time_" + lesson_lessonDetail.id)) {
                    var seektime = getCookie("current_time_" + lesson_lessonDetail.id);
                    //chuyển seekbar tới vị trí chạy video
                    vm.player.currentTime(seektime);
                    vm.currentTime = seektime;
                }

                if (vm.cdbh && vm.progress?.video_progress < 85) {
                    var myPlayer = this;

                    //Set initial time to 0
                    var currentTime = vm.currentTime;

                    //This example allows users to seek backwards but not forwards.
                    //To disable all seeking replace the if statements from the next
                    //two functions with myPlayer.currentTime(currentTime);

                    myPlayer.on("seeking", function (event) {
                        if (currentTime < myPlayer.currentTime()) {
                            myPlayer.currentTime(currentTime);
                        }
                    });

                    myPlayer.on("seeked", function (event) {
                        if (currentTime < myPlayer.currentTime()) {
                            myPlayer.currentTime(currentTime);
                        }
                    });

                    setInterval(function () {
                        if (!myPlayer.paused()) {
                            currentTime = Math.max(currentTime, myPlayer.currentTime());
                        }
                    }, 1000);
                }

                this.hotkeys({
                    volumeStep: 0.1,
                    seekStep: 10,
                    enableMute: true,
                    enableFullscreen: true,
                    enableNumbers: false,
                    enableVolumeScroll: true,
                    enableHoverScroll: true,
                    alwaysCaptureHotkeys: true,
                    fill: true,
                    fluid: true,
                    preload: "meta",
                    html5: {
                        hls: {
                            enableLowInitialPlaylist: true,
                            smoothQualityChange: true,
                            overrideNative: true
                        }
                    },
                    // Kết hợp tiến lùi với Ctrl để tiến xa hơn
                    seekStep: function (e) {
                        if (e.ctrlKey && e.altKey) {
                            return 5 * 60;
                        } else if (e.shiftKey) {
                            return 60;
                        } else if (e.altKey) {
                            return 30;
                        } else {
                            return 10;
                        }
                    },

                    // Enhance existing simple hotkey with a complex hotkey
                    fullscreenKey: function (e) {
                        // fullscreen with the F key or Ctrl+Enter
                        return ((e.which === 70) || (e.ctrlKey && e.which === 13));
                    },

                    // Custom Keys
                    customKeys: {
                        // Thêm phím custom đơn
                        simpleKey: {
                            key: function (e) {
                                // Bắt sự kiện khi ấn S
                                return (e.which === 83);
                            },
                            handler: function (player, options, e) {
                                // Example
                                if (player.paused()) {
                                    player.play();
                                } else {
                                    player.pause();
                                }
                            }
                        },

                        // Thêm tổ hợp phím custom
                        complexKey: {
                            key: function (e) {
                                // Bắt sự kiện Ctrl + D để toggle mute
                                return (e.ctrlKey && e.which === 68);
                            },
                            handler: function (player, options, event) {
                                // Example
                                if (options.enableMute) {
                                    player.muted(!player.muted());
                                }
                            }
                        },

                        // Sửa lại các phím số để seek theo yêu cầu
                        numbersKey: {
                            key: function (event) {
                                // Override number keys
                                return ((event.which > 47 && event.which < 59) || (event.which > 95 && event.which < 106));
                            },
                            handler: function (player, options, event) {
                                // Do not handle if enableModifiersForNumbers set to false and keys are Ctrl, Cmd or Alt
                                if (options.enableModifiersForNumbers || !(event.metaKey || event.ctrlKey || event.altKey)) {
                                    var sub = 48;
                                    if (event.which > 95) {
                                        sub = 96;
                                    }
                                    var number = event.which - sub;
                                    player.currentTime(player.duration() * number * 0.1);
                                }
                            }
                        },

                        emptyHotkey: {
                            // Empty
                        },

                        withoutKey: {
                            handler: function (player, options, event) {
                                console.log('withoutKey handler');
                            }
                        },

                        withoutHandler: {
                            key: function (e) {
                                return true;
                            }
                        },

                        malformedKey: {
                            key: function () {
                                console.log('I have a malformed customKey. The Key function must return a boolean.');
                            },
                            handler: function (player, options, event) {
                                //Empty
                            }
                        }
                    }
                });
                this.on('timeupdate', function () {
                    vm.currentTime = vm.player.currentTime();
                });
                this.on('seeking', function () {
                    vm.questions.forEach(function (item) {
                        item.open_once = false;
                        return item;
                    })
                });
            });
            //khởi tạo video player
            // if(window.innerWidth <= 768) //nếu là mobile
            //     var src = window.innerWidth <= 768 ? vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/index.m3u8?" + now.getMinutes();
            // else
            //     var src = vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/?" + now.getMinutes();
            var qualityData = vm.getVideoQuality;

            var src =
                vm.currentServerUrl +
                "/" +
                qualityData +
                "/" +
                vm.currentMediaName +
                "/index.m3u8?" +
                now.getMinutes();
            console.log('src..........379', src)

            $.ajax({
                url: "/video/get-link?url=" + src,
                type: "GET",
                success: function (res) {
                    console.log('res..............379', res)
                    vm.player.src({
                        src: res,
                        type: "application/x-mpegURL",
                        withCredentials: false,
                    });
                },
            });
            vm.questions.forEach(function (question) {
                vm.initModal(question);
            });

            var markers = vm.questions.map(function (question) {
                return {
                    time: question.time_start,
                    text: 'Câu hỏi',
                    class: vm.getQuestionMarkerColor(question)
                }
            });
            vm.player.markers({
                markerStyle: {
                    'width': '6px',
                    'border-radius': '0',
                    'background-color': 'orange'
                },
                markerTip: {
                    display: true,
                    text: function (marker) {
                        return "Câu hỏi";
                    }
                },
                markers: markers
            });
        },

        getQuestionMarkerColor: function (question) {
            switch (question.type) {
                case 1:
                    return 'marker__orange';
                case 2:
                    return 'marker__orange';
                default:
                    return '';
            }
        },

        getQuestionsFromLocalStorage: function () {
            var vm = this;

            var dataGot = localStorage.getItem("video_quiz_" + vm.userId + "_" + vm.taskId);
            if (dataGot) {
                var dataParsed = JSON.parse(dataGot);
                vm.questions = vm.questions.map(function (question) {
                    dataParsed.forEach(function (item) {
                        if (question.id === item.id) {
                            question.time_up = item.time_up;
                            item.loggg = false
                            question.answers.forEach(function (answer, index) {
                                if (answer.id === item.answers[index].id) {
                                    answer.selected = item.answers[index].selected;
                                }
                                if (answer.value.length > 17) {
                                    item.longgg = true
                                }
                            });
                        }
                    });
                    return question;
                });
            }
        },

        getQuestions: function (callback) {
            var vm = this;

            var data = {
                taskId: vm.taskId
            };

            $.post(vm.url + '/api/interactive-video/questions', data, function (res) {
                if (res.code === 200) {
                    vm.questions = res.data.map(function (item) {
                        item.longgg = false
                        item.answers = JSON.parse(item.answers).map(function (answer) {
                            if (answer.value.length > 17) {
                                item.longgg = true
                            }
                            answer.selected = false;
                            answer.grade = parseInt(answer.grade);
                            return answer;
                        });
                        item.modal = false; // flag xác định modal đang mở hay không
                        item.open_once = false; // flag xác định modal này đã từng được mở chưa
                        item.time_up = false; // flag xác định hết thời gian trả lời câu hỏi
                        return item;
                    });
                    // vm.getQuestionsFromLocalStorage();
                    callback();
                }
            });
        },

        openModal: function (index) {
            var vm = this;
            vm.modal[index].open();
            vm.questions[index].open_once = true;

            $('.fast-left').css('display', 'none');
            $('.fast-right').css('display', 'none');
        },

        triggerCloseModal: function () {
            var vm = this;
            vm.modal.forEach(function (item) {
                if (item.opened_) {
                    item.close();
                    $('.fast-left').css('display', 'inline');
                    $('.fast-right').css('display', 'inline');
                }
            });
        },

        closeModal: function (index) {
            var vm = this;

            if (vm.modal[index].opened_) {
                $('.fast-left').css('display', 'inline');
                $('.fast-right').css('display', 'inline');
            }

            vm.modal[index].close();

        },

        initPlayVideo: function () {
            var vm = this;
            $(".movie-play").css("display", "none");
            $("#myplayer_" + playerId).css("display", "block");

            vm.player.play();
            //cài vòng lặp ghi nhớ current time vào cookie sau mỗi 3s, không cần nữa vì có thể bắt event khi rời trang hoặc f5
            // setInterval(function () { setCookie('current_time_'+ lesson_lessonDetail.id, player.currentTime(), 10); }, 5000);

            // bắt sự kiện rời trang hoặc f5 và lưu current time vào cookie
            window.addEventListener('beforeunload', (event) => {
                setCookie('current_time_' + lesson_lessonDetail.id, vm.player.currentTime(), 10);
                vm.player.dispose();
                vm.player = null;
            });
        },

        updateOrCreateLocalStorage: function (question) {
            var vm = this;
            localStorage.setItem("video_quiz_" + vm.userId + "_" + vm.taskId, JSON.stringify(vm.questions));
        },

        selectAnswer: function (answer, question) {
            var vm = this;
            if (question.type === 1) {
                setTimeout(function () {
                    question.skip = false;
                }, (question.time_end + question.a_length + 2 - this.currentTime) * 1000);
                question.skip = true;
            }
            question.answers = question.answers.map(function (item) {
                item.selected = item.id === answer.id ? 1 : 0;
                return item;
            });
            question.time_up = true;
            var selectedAnswer = _.findIndex(question.answers, 'selected');
            var correctAnswer = _.findIndex(question.answers, 'grade');
            if (selectedAnswer !== correctAnswer) {
                vm.playAudio('sai.wav')
            } else {
                vm.playAudio('dung.mp3')
            }
            setTimeout(function () {
                var index = _.findIndex(vm.questions, ['id', question.id])
                // console.log(index)
                vm.closeModal(index);
            }, 1500)
            // vm.updateOrCreateLocalStorage(question)
        },

        timeUp: function (question) {
            var vm = this;
            question.time_up = true;
            var selectedAnswer = _.findIndex(question.answers, 'selected');
            var correctAnswer = _.findIndex(question.answers, 'grade');
            if (selectedAnswer !== correctAnswer) {
                vm.playAudio('sai.wav')
            } else {
                vm.playAudio('dung.mp3')
            }
            // vm.updateOrCreateLocalStorage(question)
        },

        skip: function (question) {
            setTimeout(function () {
                question.skip = false;
            }, (question.time_end + question.a_length + 2 - this.currentTime) * 1000);
            this.player.play();
            // question.time_up = true; // Show answer if fastRewind
            question.skip = true;
            var index = _.findIndex(this.questions, ['id', question.id]);
            this.closeModal(index);
        },

        initModal: function (question) {
            var vm = this;
            var ModalDialog = videojs.getComponent('ModalDialog');

            var contentEl = document.createElement('div');
            contentEl.className = "video__js--overlay-quiz";

            var myexample = Vue.extend(videoQuestionContent);
            var component = new myexample({
                propsData: {
                    question: question,
                    currentTime: vm.currentTime
                }
            });

            vm.modalComponents.push(component);

            component.$mount();
            // probably better to just build the entire thing via DOM methods
            contentEl.appendChild(component.$el);

            var newModal = new ModalDialog(vm.player, {
                content: contentEl,
                // We don't want this modal to go away when it closes.
                temporary: false
            });

            newModal.closeable(false);

            newModal.removeAttribute('tabindex');
            newModal.addClass('video__js--overlay');
            newModal.addClass(question.type === 1 ? 'video__js--overlay-transparent' : 'video__js--overlay-dark');
            newModal.addClass(question.type === 1 ? 'video__js--overlay-half-screen' : 'video__js--overlay-full-screen');
            newModal.addClass('p-0');

            newModal.on('modalopen', function () {
                vm.player.pause();
                question.modal = true;
                vm.modalOpenStatus = true;
            });
            newModal.on('modalclose', function () {
                vm.player.play();
                question.modal = false;
                // question.answers = question.answers.map(function (answer) {
                //     answer.selected = false;
                //     return answer;
                // })
                vm.modalOpenStatus = false;
            });

            vm.modal.push(newModal);
            vm.player.addChild(newModal);
        },

        updateModalsCurrentTime: function (time) {
            var vm = this;

            vm.modalComponents.forEach(function (component) {
                component.currentTime = time;
            })
        },

        checkServerLocation: function () {
            var vm = this;
            //check lại cài đặt localtion
            if (getCookie("v_location")) {
                var locationId = getCookie("v_location");
                if (locationId == listServers[0].id) {
                    vm.currentServerId = listServers[0].id;
                    vm.currentServerUrl = listServers[0].url;
                } else {
                    vm.currentServerId = listServers[1].id;
                    //ramdom để chia tải server
                    var ran = Math.floor(Math.random() * Math.floor(2));
                    if (ran == 0)
                        vm.currentServerUrl = listServers[1].url;
                    else
                        vm.currentServerUrl = "https://tokyo-v2.dungmori.com"
                }
            }
        },
    },

    mounted: function () {
        let vm = this;

        // hiển thị nút chọn server di động
        var width = (window.innerWidth > 0) ? window.innerWidth : screen.width;
        if (width <= 768) {
            $('.select-localtion-select').css('display', 'block');
            $('.server-item').css('display', 'none');
        }

        console.log('width____mounted', width)
        //check lại cài đặt quality
        if (getCookie("v_quality")) {
            if (getCookie("v_quality") != vm.currentQuality) {
                vm.currentQuality = getCookie("v_quality");
            }
        }

        vm.checkServerLocation();
        vm.getQuestions(function () {
            console.log('call info video')
            vm.initVideo();
        });
    },
    beforeDestroy: function () {
        var vm = this;
        if (vm.player) {
            vm.player.dispose();
            vm.player = null;
        }
    }
});

