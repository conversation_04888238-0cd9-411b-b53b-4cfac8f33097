//token ở ngoài view
$.ajaxSetup({ headers: { token: token } });
Vue.component("v-select", VueSelect.VueSelect);

var community = new Vue({
  el: ".main-community-center",
  data: {
    isMod: isMod,
    url: window.location.origin,
    crrurl: location.protocol + "//" + location.host + location.pathname,
    cdn: cdn,
    apiMode: "/api/community/posts/posts", //chế độ mặc định và chế độ tìm kiếm

    postnew_type:
      getCookie("postnew_type") != "" ? getCookie("postnew_type") : 0,
    postnew_btn: 0, //trạng thái nút đăng
    postnew_error: "",

    //nội dung sửa
    postEditing: null,

    orderBy: getCookie("order_by") != "" ? getCookie("order_by") : "latest",
    groupId: gId,
    groupPage: 1,
    groupLoading: false,
    listGroups: [],
    listPosts: [],
    media: [],
    listTags: [],
    limitTag: 15,
    searchKeyword: "",
    currTag: null,
    selectedDate: null,
    exams: [],
    examList: examList,
    selectedDateExams: [],
    params: {
      page: 1,
      limit: 10,
      groupId: gId,
    },
    theEnd: false,
    showLoading: true,
    currentExam: {},
    modelConfig: {
      type: "string",
      mask: "YYYY-MM-DD HH:mm:00", // Uses 'iso' if missing
    },
    filterKeywords: "",
    postImages: [],
    postFile: null,
  },
  computed: {
    attributes: function () {
      return this.exams.map(function (exam) {
        return {
          dates: exam.time_start,
          bar: {
            style: {
              backgroundColor: "red",
            },
            color: "red",
            class: exam.type ? "opacity-75" : "",
          },
          popover: {
            label: exam.exam ? exam.exam.name : "",
          },
          customData: exam,
        };
      });
    },
  },
  watch: {
    postnew_type: {
      handler: function (newValue) {
        if (newValue != 4) {
          setCookie("postnew_type", newValue, 50);
        }
      },
      deep: true,
    },
    selectedDate: function (value) {
      if (value) {
        this.getCurrentDateExams();
      } else {
        this.selectedDateExams = [];
      }
    },
    orderBy: {
      handler: function (newValue) {
        setCookie("order_by", newValue, 50);
        this.listPosts = [];
        this.initPosts(); //load lại bài đăng theo thứ tự thay đổi
        // location.reload();
      },
      deep: true,
    },
    listPosts: {
      handler: function (newValue) {
        var vm = this;
        $(".slick-gallery").slick("unslick");
        if (window.location.href.includes("/groups/detail")) {
          vm.$nextTick(function () {
            $(".slick-gallery").slick({
              dots: true,
              infinite: true,
              speed: 300,
              slidesToShow: 1,
              centerMode: false,
              variableWidth: true,
            });
            $("#cmt-b-" + pid).click();
          });
        } else {
          vm.$nextTick(function () {
            $(".slick-gallery").slick({
              dots: true,
              infinite: true,
              speed: 300,
              slidesToShow: 1,
              centerMode: false,
              variableWidth: true,
            });
          });
        }
      },
      deep: true,
    },
  },

  methods: {
    initPosts: function () {
      var vm = this;

      //check lọc theo chủ đề
      var fullUrl = new URL(window.location.href);
      var tagId = fullUrl.searchParams.get("tag");
      var key = fullUrl.searchParams.get("q");
      if (tagId) vm.params.tagId = tagId;
      if (key) vm.params.text = key;
      if (key || tagId) vm.apiMode = "/api/community/posts/search"; //chuyển sang mode tìm kiếm
      vm.params.lastId = null;

      if (getCookie("order_by") == "featured")
        vm.params.orderBy = "total_likes";
      else vm.params.orderBy = "id";

      //lấy hết mygroups
      $.get(
        api + "/api/community/groups/get-user-groups",
        { page: vm.groupPage, limit: 20 },
        function (res) {
          // console.log("groups:", res);
          vm.listGroups = res;
          vm.groupPage++;
        }
      );

      //lấy page1 media
      $.get(
        api + "/api/community/media/get-list",
        { groupId: vm.groupId, page: 1 },
        function (res) {
          // console.log("media:", res);
          vm.media = res.data;
        }
      );

      //lấy hết tags
      $.get(
        api + "/api/community/tags",
        { groupId: vm.groupId, limit: 10000 },
        function (res) {
          // console.log("tags:", res);
          vm.listTags = res;

          //nếu đang lọc theo tagid -> in ra theo tagname
          if (tagId) {
            vm.currTag = _.find(vm.listTags, { id: parseInt(tagId) });
          }
        }
      );

      //nếu là vào xem detail
      if (window.location.href.includes("/groups/detail")) {
        var pid = window.location.href
          .split("/groups/detail/")[1]
          .split("-")[0];
        // alert(pid);

        // alert("detail posts");
        vm.apiMode = "/api/community/posts/post?postId=" + pid;
        vm.params = {};

        //lấy bài đăng thông thường
        $.get(api + vm.apiMode, vm.params, function (res) {
          // console.log("posts:", res);
          vm.listPosts = vm.listPosts.concat(res[0]);

          vm.showLoading = false;
          vm.theEnd = true;
        });
      } else {
        //lấy bài đăng thông thường
        $.get(api + vm.apiMode, vm.params, function (res) {
          // console.log("posts:", res);
          vm.listPosts = vm.listPosts.concat(res.data);

          vm.showLoading = false;

          if (res.data.length > 0)
            vm.params.lastId = vm.listPosts[vm.listPosts.length - 1].id;

          //nếu là mode thường -> lấy thêm pin posts
          if (!key && !tagId) {
            $.get(
              api + "/api/community/posts/get-pin-posts",
              { groupId: gId },
              function (res) {
                // console.log("pin posts:", res);
                vm.listPosts = res.data.concat(vm.listPosts); //nối vào đầu
                // console.log(vm.listPosts)
              }
            );
          }

          if (res.data.length < vm.params.limit) {
            vm.theEnd = true;
          }
        });
      }
    },

    loadMoreGroup: function () {
      var vm = this;
      vm.groupLoading = true;
      var keywordParam = vm.filterKeywords.toLowerCase();
      $.get(
        api + "/api/community/groups/get-user-groups",
        { page: vm.groupPage, limit: 20, keywords: keywordParam },
        function (res) {
          console.log("groups:", res);
          vm.listGroups = vm.listGroups.concat(res);
          vm.groupPage++;
          vm.groupLoading = false;
        }
      );
    },

    loadMorePosts: function () {
      var vm = this;

      vm.showLoading = true;

      vm.params.page++;

      setTimeout(function () {
        if (vm.theEnd == false) {
          //lấy bài đăng
          $.get(api + vm.apiMode, vm.params, function (res) {
            vm.showLoading = false;

            console.log("load more posts: page", vm.params.page);
            console.log("load more posts: res", res);
            // vm.listPosts = res;
            //nối thêm mảng tải thêm
            if (res.data.length > 0) {
              vm.listPosts = vm.listPosts.concat(res.data);
              vm.params.lastId = vm.listPosts[vm.listPosts.length - 1].id;

              vm.$forceUpdate();

              vm.$nextTick(function () {
                for (var i = 0; i < res.data.length; i++) {
                  var posts = res.data[i];
                  if (
                    posts.type == 0 &&
                    posts.data != null &&
                    posts.data.length >= 3 &&
                    $("#slick-" + posts.id)
                  ) {
                    $("#slick-" + posts.id).slick({
                      dots: true,
                      infinite: true,
                      speed: 300,
                      slidesToShow: 1,
                      centerMode: false,
                      variableWidth: true,
                    });
                  }
                }
              });
            }

            if (res.data.length < vm.params.limit) {
              vm.theEnd = true;
            }
          });
        }
      }, 1500);
    },

    //in ra thông tin có dấu cách
    renderContent: function (info) {
      var vm = this;
      var result = _.escape(info);

      result = result.replace("<", "&#60;");
      result = result.replace(">", "&#62;");

      //xử lý xuống dòng
      // result =  info.replace(new RegExp('\r?\n','g'), '<br />');

      var re =
        /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;
      return result.replace(re, function (match, lParens, url) {
        var rParens = "";
        lParens = lParens || "";

        // Try to strip the same number of right parens from url
        // as there are left parens.  Here, lParenCounter must be
        // a RegExp object.  You cannot use a literal
        //     while (/\(/g.exec(lParens)) { ... }
        // because an object is needed to store the lastIndex state.
        var lParenCounter = /\(/g;
        while (lParenCounter.exec(lParens)) {
          var m;
          // We want m[1] to be greedy, unless a period precedes the
          // right parenthesis.  These tests cannot be simplified as
          //     /(.*)(\.?\).*)/.exec(url)
          // because if (.*) is greedy then \.? never gets a chance.
          if ((m = /(.*)(\.\).*)/.exec(url) || /(.*)(\).*)/.exec(url))) {
            url = m[1];
            rParens = m[2] + rParens;
          }
        }
        return (
          lParens +
          "<a href='" +
          url +
          "' target='_blank'>" +
          url +
          "</a>" +
          rParens
        );
      });
    },

    //in ra định dạng ngày giờ đẹp
    prettyDate: function (t) {
      var event = new Date(t);
      return event.toLocaleDateString("pt-PT");
    },
    prettyTime: function (t) {
      var event = new Date(t);
      return (
        ("0" + event.getHours()).slice(-2) +
        ":" +
        ("0" + event.getMinutes()).slice(-2)
      );
    },

    //in ra ngày hết hạn
    prettyExpired: function (t) {
      return new Date(t).toLocaleDateString("pt-PT");
    },

    //in ra ngày hết hạn
    expandContent: function (pid) {
      $("#posts-content-" + pid).css("max-height", "none");
      $("#expand-content-" + pid).css("display", "none");
    },

    searchPosts: function () {
      var vm = this;

      console.log("tìm kiếm", vm.searchKeyword);

      var delayTimer;

      clearTimeout(delayTimer);

      delayTimer = setTimeout(function () {
        $.post(
          window.location.origin + "/posts/searchPosts",
          {
            key: vm.searchKeyword,
          },
          function (response) {
            console.log(response);
            vm.listPosts = response.data;
          }
        );
      }, 100);
    },

    //bắt sự kiện click chuột vào khu vực post bài
    focusTextArea: function () {
      // nới khu vực post bài rộng ra
      $(".post-bottom-area").css("display", "block");
    },

    //preview ảnh đính kèm khi đăng post mới
    previewNewPostsImage: function (event) {
      // console.log(event);

      var vm = this;

      $("#preview-image-post").html("");
      var input = event.target;

      if (input.files.length > 12) {
        vm.postnew_error = "* Bạn được chọn tối đa 12 ảnh";
        return;
      } else {
        if (input.files) {
          $("#clear-prv").css("display", "block");

          for (var i = 0; i < input.files.length; i++) {
            var reader = new FileReader();
            reader.onload = function (e) {
              if (i == 0) {
                $("#preview-image-post").html(
                  '<img src="' +
                    e.target.result +
                    '"/><i title="xóa" onclick="removeImg(' +
                    i +
                    ')" class="bx bxs-no-entry" ></i>'
                );
              } else if (i > 0)
                $("#preview-image-post").append(
                  '<img src="' +
                    e.target.result +
                    '"/><i title="xóa" onclick="removeImg(' +
                    i +
                    ')" class="bx bxs-no-entry"></i>'
                );
            };
            reader.readAsDataURL(input.files[i]);
          }
        }
      }
    },

    clearImgPreview: function () {
      $("#clear-prv").css("display", "none");
      $("#postsImagePicked").val("");
      $("#preview-image-post").html("");
    },

    //thêm bài đăng mới cho trang posts
    async addNewPost() {
      var vm = this;

      vm.postnew_btn = 1;

      setTimeout(async function () {
        //nội dung trống
        if ($("#new-posts-input-area").val() == "") {
          vm.postnew_error = "* Vui lòng nhập đủ nội dung";
          vm.postnew_btn = 0;
          return;
        }

        //hastag là bắt buộc
        if ($("#select2-hashtag").select2("data").length == 0) {
          vm.postnew_error = "* Vui lòng chọn hashtag";
          vm.postnew_btn = 0;
          return;
        }

        var formData = {
          type: vm.postnew_type,
          groupId: vm.groupId,
          content: $("#new-posts-input-area").val(),
        };
        var fileFormData = new FormData();
        fileFormData.append("object", "community");

        //nếu có kèm link
        if (vm.postnew_type != 0 && vm.postnew_type != 4) {
          //nếu bỏ trống không nhập
          if ($("#embed-url-link").val() == "") {
            vm.postnew_error = "* Vui lòng nhập link";
            vm.postnew_btn = 0;
            return;

            //check link nhập đúng
          } else {
            var link = $("#embed-url-link").val();

            //check link youtube
            if (
              vm.postnew_type == 1 &&
              !link.includes("https://www.youtube.com/") &&
              !link.includes("https://youtu.be/")
            )
              vm.postnew_error = "* Link youtube không hợp lệ";

            //check link facebook
            if (
              vm.postnew_type == 3 &&
              !link.includes("facebook.com/") &&
              !link.includes("fb.com/") &&
              !link.includes("fb.watch/")
            )
              vm.postnew_error = "* Link facebook không hợp lệ";

            //check link tiktok
            if (vm.postnew_type == 2 && !link.includes("tiktok.com/"))
              vm.postnew_error = "* Link tiktok không hợp lệ";

            if (vm.postnew_error == "")
              formData.url = $("#embed-url-link").val();
            else {
              vm.postnew_btn = 0;
              return;
            }
          }
        }

        //nếu có kèm ảnh or nhiều ảnh
        var imagePicked = $("#postsImagePicked").prop("files");
        if (vm.postnew_type == 0 && imagePicked.length) {
          for (var i = 0; i < $("#postsImagePicked").prop("files").length; i++)
            fileFormData.append(
              "images[]",
              $("#postsImagePicked").prop("files")[i]
            );
          await axios
            .post("/upload-images", fileFormData, {})
            .then((res) => {
              vm.postImages = res.data;
            })
            .catch((err) => alert());
        }

        //nếu đăng kèm hashtags
        // console.log('hashtag', $('#select2-hashtag').select2('data'));
        if ($("#select2-hashtag").select2("data").length > 0) {
          var arr = [];
          for (var i = 0; i < $("#select2-hashtag").select2("data").length; i++)
            arr.push($("#select2-hashtag").select2("data")[i].id);

          formData.tags = JSON.stringify(Object.assign({}, arr));
        }

        //nếu là video facebook
        if (vm.postnew_type == 3) {
          formData.icon =
            "https://tech12h.com/sites/default/files/field/image/facebook-video-ads-21.jpg";
        }

        //lên lịch nếu là admin
        if ($("#create-date-picker").val() && $("#create-time-picker")) {
          var t =
            $("#create-date-picker").val() +
            " " +
            $("#create-time-picker").val();
          var publishedAt = new Date(t);
          formData.publishedAt = publishedAt;
        }

        var documentData = $("#documentData").prop("files");
        if (vm.postnew_type == 4 && documentData) {
          fileFormData.append("file", documentData[0]);
          await axios
            .post("/upload-file", fileFormData, {})
            .then((res) => {
              vm.postFile = res.data;
            })
            .catch((err) => alert());
        }

        if (vm.postImages.length) {
          formData.images = vm.postImages;
        }
        if (vm.postFile) {
          formData.file = vm.postFile;
        }
        await axios
          .post(api + "/api/community/posts/create", formData, {
            headers: {
              "Content-Type": "application/json",
              token: token,
            },
          })
          .then(function (response) {
            // console.log(response);
            if (response.status == 200) {
              vm.postnew_btn = 2;

              vm.listPosts.unshift(response.data);
              vm.$forceUpdate();

              //đóng form nhập
              setTimeout(function () {
                $.fancybox.close();
              }, 1000);
              setCookie("order_by", "latest", 50);
              vm.postImages = [];
              location.reload();
            }
          })
          .catch(function (error) {
            console.log(error);
          });
      }, 1000);
    },

    //Hiển thị hộp thoại cho phép sửa ghi chú
    showEditPosts: function (item) {
      var vm = this;

      vm.postEditing = item;

      if (item.tags.length > 0) {
        var arr = [];
        for (var i = 0; i < item.tags.length; i++) {
          arr.push(item.tags[i].id);
        }
        console.log(arr);
        $("#edit-select2-hashtag").val(arr);
        $("#edit-select2-hashtag").trigger("change");
      }

      var date = new Date(vm.postEditing.published_at)
        .toISOString()
        .slice(0, 10);
      var time = new Date(vm.postEditing.published_at)
        .toISOString()
        .slice(11, 16);

      $("#edit-date-picker").val = date;
      $("#edit-time-picker").val = time;

      console.log(time);
    },

    //lưu sửa nội dung
    saveEditPosts: function (id) {
      var vm = this;

      vm.postnew_btn = 1;

      setTimeout(function () {
        //nội dung trống
        if ($("#edit-posts-input-area").val() == "") {
          vm.postnew_error = "* Vui lòng nhập đủ nội dung";
          vm.postnew_btn = 0;
          return;
        }

        //hastag là bắt buộc
        if ($("#edit-select2-hashtag").select2("data").length == 0) {
          vm.postnew_error = "* Vui lòng chọn hashtag";
          vm.postnew_btn = 0;
          return;
        }

        var formData = {
          postId: id,
          content: $("#edit-posts-input-area").val(),
        };
        var fileFormData = new FormData();
        fileFormData.append("object", "community");

        //nếu sửa kèm hashtags
        if ($("#edit-select2-hashtag").select2("data").length > 0) {
          var arr = [];
          for (
            var i = 0;
            i < $("#edit-select2-hashtag").select2("data").length;
            i++
          )
            arr.push($("#edit-select2-hashtag").select2("data")[i].id);

          formData.tags = JSON.stringify(Object.assign({}, arr));
        }

        //lên lịch nếu là admin
        if (
          $("#edit-date-picker").val() != "" &&
          $("#edit-time-picker").val() != ""
        ) {
          var t =
            $("#edit-date-picker").val() + " " + $("#edit-time-picker").val();
          var publishedAt = new Date(t);
          formData.publishedAt = publishedAt;
        }

        axios
          .post(api + "/api/community/posts/edit", formData, {
            headers: {
              "Content-Type": "application/json",
              mimeType: "multipart/form-data",
              token: token,
            },
          })
          .then(function (response) {
            console.log(response);
            if (response.status == 200) {
              vm.postnew_btn = 2;

              //đóng form nhập
              setTimeout(function () {
                $.fancybox.close();
              }, 1000);
              location.reload();
            }
          })
          .catch(function (error) {
            console.log(error);
          });
      }, 1000);
    },

    //pin bài đăng
    pinThisPosts: function (id) {
      var vm = this;

      var formData = new FormData();
      formData.append("postId", id);
      axios
        .post(api + "/api/community/posts/pin", formData, {
          headers: {
            "Content-Type": "application/json",
            mimeType: "multipart/form-data",
            token: token,
          },
        })
        .then(function (response) {
          console.log(response);
          if (response.status == 200) location.reload();
        })
        .catch(function (error) {
          console.log(error);
        });
    },

    //unpin bài đăng
    unpinThisPosts: function (id) {
      var vm = this;

      var formData = new FormData();
      formData.append("postId", id);
      axios
        .post(api + "/api/community/posts/unpin", formData, {
          headers: {
            "Content-Type": "application/json",
            mimeType: "multipart/form-data",
            token: token,
          },
        })
        .then(function (response) {
          console.log(response);
          if (response.status == 200) location.reload();
        })
        .catch(function (error) {
          console.log(error);
        });
    },

    // xóa 1 ghi chú
    deletePosts: function (id) {
      $.post(
        api + "/api/community/posts/delete",
        {
          postId: id,
        },
        function (res) {
          if (res && res.status == 1) {
            $.notify("Đã xóa nội dung", "success");
            $("#posts-" + id).fadeOut(500);
          } else $.notify("Lỗi khi xóa");
        }
      );
    },

    // báo cáo 1 ghi chú
    reportPosts: function (id) {
      $.post(
        api + "/api/community/reports/create",
        {
          postId: id,
          contentId: 2,
          note: "nội dung không hợp lệ",
        },
        function (res) {
          if (res && res.id) $.notify("Đã report thành công");
          else $.notify("report lỗi");
        }
      );
    },

    //hiển thị khung comment của bài post theo id
    showCommentBoxOfPosts: function (id) {
      var vm = this;

      console.log("showCommentBoxOfPosts :", id);

      var i = _.findIndex(vm.listPosts, { id: id });
      if (vm.listPosts[i].showBoxComment == null)
        vm.listPosts[i].showBoxComment = 1;
      else vm.listPosts[i].showBoxComment = null;
      vm.$forceUpdate();
    },

    //thích bài đăng
    likePost: function (id) {
      var vm = this;

      //nếu chưa đăng nhập
      if (authId == null) {
        $("#login-text-btn").click();
        return;
      }

      $.post(
        api + "/api/community/likes/like",
        {
          postId: id,
        },
        function (res) {
          console.log("like :", res);

          //nếu like thành công
          if (res.id) {
            var i = _.findIndex(vm.listPosts, { id: id });
            vm.listPosts[i].is_like = 1;
            vm.listPosts[i].total_likes++;
            vm.$forceUpdate();
          } else {
            $(".like-btn").notify("Lỗi");
          }
        }
      );
    },

    //bỏ thích bài đăng
    dislikePost: function (id) {
      var vm = this;

      $.post(
        api + "/api/community/likes/dislike",
        {
          postId: id,
        },
        function (res) {
          console.log("dislike :", res);

          //nếu dislikePost thành công
          if (res.status == 1) {
            var i = _.findIndex(vm.listPosts, { id: id });
            vm.listPosts[i].is_like = 0;
            vm.listPosts[i].total_likes--;
            vm.$forceUpdate();
          } else {
            $(".like-btn").notify("Lỗi");
          }
        }
      );
    },

    //follow bài đăng
    followPost: function (id) {
      var vm = this;

      //nếu chưa đăng nhập
      if (authId == null) {
        $("#login-text-btn").click();
        return;
      }

      $.post(
        api + "/api/community/follows/post",
        {
          postId: id,
        },
        function (res) {
          console.log("followPost :", res);

          //nếu follow thành công
          if (res.id) {
            var i = _.findIndex(vm.listPosts, { id: id });
            vm.listPosts[i].is_follow = 1;
            vm.$forceUpdate();
          } else {
            $(".follow-btn").notify("Lỗi");
          }
        }
      );
    },

    //follow bài đăng
    unfollowPost: function (id) {
      var vm = this;

      $.post(
        api + "/api/community/follows/unfollow-post",
        {
          postId: id,
        },
        function (res) {
          console.log("unfollowPost :", res);

          //nếu follow thành công
          if (res.status == 1) {
            var i = _.findIndex(vm.listPosts, { id: id });
            vm.listPosts[i].is_follow = 0;
            vm.$forceUpdate();
          } else {
            $(".follow-btn").notify("Lỗi");
          }
        }
      );
    },

    //hàm lưu lại giá trị sửa
    saveEditCmt: function () {
      var vm = this;

      var id = $("#edit-comment-id").val();
      var content = $("#edit-comment-area").val();
      console.log("hiện sửa cmt", id);

      $.post(
        api + "/api/community/comments/update",
        { commentId: id, comment: content },
        function (res) {
          if (res.id == id) {
            //cập nhật giao diện
            $("#cmt-content-div-" + id).html(content);
            $.fancybox.close();
          } else {
            alert("lưu thất bại");
          }
        }
      );
    },

    //cancel đóng popup nếu không muốn sửa cmt
    cancelEditCmt: function () {
      $.fancybox.close();
    },

    getExams: function () {
      var vm = this;
      var data = {
        date: moment(this.selectedDate).format("YYYY-MM-DD"),
        groupId: this.groupId,
      };
      $.post(this.url + "/community/exam/get-calendar", data, function (res) {
        vm.exams = res.data;
      });
    },
    getCurrentDateExams: function () {
      var vm = this;
      var data = {
        date: moment(this.selectedDate).format("YYYY-MM-DD"),
        groupId: this.groupId,
      };
      $.post(
        this.url + "/community/exam/get-current-date-exams",
        data,
        function (res) {
          // console.log(res.data);
          vm.selectedDateExams = res.data;
          if (vm.selectedDateExams.length > 0) {
            vm.selectExam(vm.selectedDateExams[0]);
          } else {
            vm.currentExam = {};
          }
        }
      );
    },
    selectExam: function (schedule) {
      this.$set(this.currentExam, "id", schedule.id);
      this.$set(this.currentExam, "examId", schedule.exam.id);
      this.$set(
        this.currentExam,
        "time_start",
        schedule.time_start ? schedule.time_start : this.selectedDate
      );
      this.$set(
        this.currentExam,
        "time_end",
        schedule.time_end ? schedule.time_end : this.selectedDate
      );
      this.$set(this.currentExam, "type", schedule.type);
      this.$set(this.currentExam, "counted", schedule.counted);
    },
    appendSchedule: function () {
      this.currentExam = {
        id: null,
        time_start: this.selectedDate,
        time_end: this.selectedDate,
        examId: null,
        type: 1,
        counted: 1,
      };
    },
    createExam: function () {
      var vm = this;
      var start = moment(this.currentExam.time_start);
      var end = moment(this.currentExam.time_end);
      if (!this.currentExam.examId) {
        $.notify("Chưa chọn bài thi");
        return;
      }
      if (end.isBefore(start)) {
        $.notify("Thời gian kết thúc phải sau thời gian bắt đầu");
        return;
      }
      if (moment.duration(end.diff(start)).asMinutes() < 15) {
        $.notify("Bài thi hiển thị tối thiểu 15 phút");
        return;
      }
      var data = {
        groupId: this.groupId,
        examId: this.currentExam.examId,
        time_start: this.currentExam.time_start,
        time_end: this.currentExam.time_end,
        type: this.currentExam.type,
        counted: this.currentExam.counted,
      };
      $.post(
        window.location.origin + "/community/add-exam-to-group",
        data,
        function (res) {
          var tmp = _.cloneDeep(vm.selectedDateExams);
          tmp.push(res.data);
          vm.$set(vm, "selectedDateExams", tmp);
          tmp = _.cloneDeep(vm.exams);
          tmp.push(res.data);
          vm.$set(vm, "exams", tmp);
        }
      ).then(function (res) {
        $.notify("Lên lịch thành công", "success");
      });
    },
    deleteExam: function () {
      var vm = this;
      var data = {
        id: vm.currentExam.id,
        groupId: vm.groupId,
      };
      var check = confirm("Xác nhận xoá lịch kiểm tra này");
      if (!check) return;
      $.post(
        window.location.origin + "/community/delete-schedule",
        data,
        function (res) {
          var tmp = vm.selectedDateExams.filter(function (exam) {
            return exam.id != vm.currentExam.id;
          });
          vm.$set(vm, "selectedDateExams", tmp);
          tmp = tmp = vm.exams.filter(function (exam) {
            return exam.id != vm.currentExam.id;
          });
          vm.$set(vm, "exams", tmp);
          if (vm.selectedDateExams.length > 0) {
            vm.selectExam(vm.selectedDateExams[0]);
          } else {
            vm.currentExam = {};
          }
        }
      )
        .then(function (res) {
          $.notify("Đã xoá", "success");
        })
        .catch(function (err) {
          $.notify("Lỗi: " + err);
        });
    },
    updateExam: function () {
      var vm = this;
      var data = this.currentExam;
      var start = moment(this.currentExam.time_start);
      var end = moment(this.currentExam.time_end);
      if (!this.currentExam.examId) {
        $.notify("Chưa chọn bài thi");
        return;
      }
      if (end.isBefore(start)) {
        $.notify("Thời gian kết thúc phải sau thời gian bắt đầu");
        return;
      }
      if (moment.duration(end.diff(start)).asMinutes() < 15) {
        $.notify("Bài thi hiển thị tối thiểu 15 phút");
        return;
      }
      $.post(
        window.location.origin + "/community/update-schedule",
        data,
        function (res) {
          var tmp = vm.selectedDateExams.map(function (exam) {
            if (exam.id == res.data.id) {
              exam = res.data;
            }
            return exam;
          });
          vm.$set(vm, "selectedDateExams", tmp);
          tmp = vm.exams.map(function (exam) {
            if (exam.id == res.data.id) {
              exam = res.data;
            }
            return exam;
          });
          vm.$set(vm, "exams", tmp);
        }
      )
        .then(function (res) {
          $.notify("Cập nhật thành công", "success");
        })
        .catch(function (err) {
          $.notify("Lỗi: " + err);
        });
    },
    filterGroup: function () {
      var vm = this;
      var keywordParam = vm.filterKeywords.toLowerCase();
      $.get(
        api + "/api/community/groups/get-user-groups",
        { page: 1, limit: 20, keywords: keywordParam },
        function (res) {
          vm.listGroups = res;
          vm.groupPage++;
        }
      );
    },
  },

  mounted: function () {
    var vm = this;

    vm.initPosts();
    if (this.isMod) {
      this.selectedDate = moment().format("YYYY-MM-DD HH:mm:ss");
      vm.getExams();
    }
  },
});

//cuộn trang
$(window).scroll(function () {
  if ($(window).scrollTop() == $(document).height() - $(window).height()) {
    community.loadMorePosts();
  }
});

$(document).ready(function () {
  $(".select2-hashtag").select2();
  $(".edit-select2-hashtag").select2();
});

function removeImg(i) {
  console.log("remove preview", i);
  var newFileList = Array.from(document.getElementById("postsImagePicked"));
  // newFileList.splice(i, 1);
  console.log(newFileList);
}
