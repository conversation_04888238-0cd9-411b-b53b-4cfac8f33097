const options = {
  playbackRates: [0.75, 1, 1.25, 1.5, 1.75, 2],
  preload: "auto",
  controlBar: {
    volumeMenuButton: {
      vertical: true,
      inline: false,
      volumeBar: {
        vertical: true,
      },
      volumeLevel: true,
    },
  },
};
var videoPlayer = new Vue({
  el: ".dmrx-player",

  data: function () {
    return {
      player: null,
      currentMediaName: atob(atob(atob(mdaId))),
      currentServerId: listServers[0].id,
      currentServerUrl: listServers[0].url,
      currentQuality: "720p", // 720p, 480p, 360p
      currentTime: 0,
      listServers: listServers,
      lessonInfo: lessonInfo, //chỗ có nhiều video
      courseUrl: courseUrl,
      playbackSpeed: playbackSpeed,
      cdbh: cdbh,
      progress: currentProgress,
    };
  },
  computed: {
    getVideoQuality: function () {
      if (!videoFull && this.currentQuality == "1080p") {
        return "720p";
      }
      return this.currentQuality;
    },
  },
  methods: {
    /* chọn server kh<PERSON><PERSON> di động */
    changeServerBySelectTag: function () {
      var vm = this;
      setCookie("v_location", vm.currentServerId, 30);
      location.reload();
    },

    /* chọn server khác */
    changeServerLocaltion: function (id) {
      var vm = this;
      var now = new Date();
      //đặt lại cookie cài đặt nhớ trong 30 ngày
      setCookie("v_location", id, 30);
      setCookie(
        "current_time_" + lesson_lessonDetail.id,
        vm.player.currentTime(),
        10
      );
      this.checkServerLocation();
      var src =
        vm.currentServerUrl +
        "/" +
        vm.getVideoQuality +
        "/" +
        vm.currentMediaName +
        "/index.m3u8?" +
        now.getMinutes();
      console.log('src..........70', src)

      $.ajax({
        url: "/video/get-link?url=" + src,
        type: "GET",
        success: function (res) {
          console.log('res..............75', res)
          vm.player.src({
            src: res,
            type: "application/x-mpegURL",
            withCredentials: false,
          });
        },
      });
      vm.clickPlayVideo();
      // location.reload();
    },

    /* chọn video chất lượng khác */
    changeQuality: function (quality) {
      var vm = this;
      var now = new Date();
      vm.currentQuality = quality;
      var qualityData = vm.getVideoQuality;

      //đặt lại cookie cài đặt nhớ trong 30 ngày
      setCookie("v_quality", quality, 30);
      setCookie(
        "current_time_" + lesson_lessonDetail.id,
        vm.player.currentTime(),
        10
      );

      var src =
        vm.currentServerUrl +
        "/" +
        qualityData +
        "/" +
        vm.currentMediaName +
        "/index.m3u8?" +
        now.getMinutes();
      console.log('src..........111', src)

      $.ajax({
        url: "/video/get-link?url=" + src,
        type: "GET",
        success: function (res) {
          console.log('res..............115', res)
          vm.player.src({
            src: res,
            type: "application/x-mpegURL",
            withCredentials: false,
          });
        },
      });

      vm.clickPlayVideo();
    },

    // tua lại
    fastRewind: function () {
      var vm = this;
      vm.player.currentTime(vm.player.currentTime() - 10);
      if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        )
      ) {
        $(".left-fast-icon").css("display", "inline");
        setTimeout(function () {
          $(".left-fast-icon").css("display", "none");
        }, 1200);
      }
    },

    // tua đi
    fastForward: function () {
      var vm = this;
      vm.player.currentTime(vm.player.currentTime() + 10);
      if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        )
      ) {
        $(".right-fast-icon").css("display", "inline");
        setTimeout(function () {
          $(".right-fast-icon").css("display", "none");
        }, 1200);
      }
    },

    initVideo: function () {
      var vm = this;

      var now = new Date();

      vm.player = videojs('myplayer_' + playerId, options);
      vm.player.ready(function () {
        //nếu tồn tại ghi nhớ thời gian chạy video
        if (getCookie("current_time_" + lesson_lessonDetail.id)) {
          var seektime = getCookie("current_time_" + lesson_lessonDetail.id);
          //chuyển seekbar tới vị trí chạy video
          vm.player.currentTime(seektime);
          vm.currentTime = seektime
        }

        if (this.playbackSpeed) {
          this.playbackRate(this.playbackSpeed);
        }

        this.on("timeupdate", function () {
          vm.currentTime = vm.player.currentTime();
        });

        if (vm.cdbh && vm.progress?.video_progress < 85) {
          var myPlayer = this;

          //Set initial time to 0
          var currentTime = vm.currentTime;

          //This example allows users to seek backwards but not forwards.
          //To disable all seeking replace the if statements from the next
          //two functions with myPlayer.currentTime(currentTime);

          myPlayer.on("seeking", function (event) {
            if (currentTime < myPlayer.currentTime()) {
              myPlayer.currentTime(currentTime);
            }
          });

          myPlayer.on("seeked", function (event) {
            if (currentTime < myPlayer.currentTime()) {
              myPlayer.currentTime(currentTime);
            }
          });

          setInterval(function () {
            if (!myPlayer.paused()) {
              currentTime = Math.max(currentTime, myPlayer.currentTime());
            }
          }, 1000);
        }

        this.hotkeys({
          volumeStep: 0.1,
          seekStep: 10,
          enableMute: true,
          enableFullscreen: true,
          enableNumbers: false,
          enableVolumeScroll: true,
          enableHoverScroll: true,
          alwaysCaptureHotkeys: true,
          fill: true,
          fluid: true,
          preload: "meta",
          html5: {
            hls: {
              enableLowInitialPlaylist: true,
              smoothQualityChange: true,
              overrideNative: true,
            },
          },
          // Kết hợp tiến lùi với Ctrl để tiến xa hơn
          seekStep: function (e) {
            if (e.ctrlKey && e.altKey) {
              return 5 * 60;
            } else if (e.shiftKey) {
              return 60;
            } else if (e.altKey) {
              return 30;
            } else {
              return 10;
            }
          },

          // Enhance existing simple hotkey with a complex hotkey
          fullscreenKey: function (e) {
            // fullscreen with the F key or Ctrl+Enter
            return e.which === 70 || (e.ctrlKey && e.which === 13);
          },

          // Custom Keys
          customKeys: {
            // Thêm phím custom đơn
            simpleKey: {
              key: function (e) {
                // Bắt sự kiện khi ấn S
                return e.which === 83;
              },
              handler: function (player, options, e) {
                // Example
                if (player.paused()) {
                  player.play();
                } else {
                  player.pause();
                }
              },
            },

            // Thêm tổ hợp phím custom
            complexKey: {
              key: function (e) {
                // Bắt sự kiện Ctrl + D để toggle mute
                return e.ctrlKey && e.which === 68;
              },
              handler: function (player, options, event) {
                // Example
                if (options.enableMute) {
                  player.muted(!player.muted());
                }
              },
            },

            // Sửa lại các phím số để seek theo yêu cầu
            numbersKey: {
              key: function (event) {
                // Override number keys
                return (
                  (event.which > 47 && event.which < 59) ||
                  (event.which > 95 && event.which < 106)
                );
              },
              handler: function (player, options, event) {
                // Do not handle if enableModifiersForNumbers set to false and keys are Ctrl, Cmd or Alt
                if (
                  options.enableModifiersForNumbers ||
                  !(event.metaKey || event.ctrlKey || event.altKey)
                ) {
                  var sub = 48;
                  if (event.which > 95) {
                    sub = 96;
                  }
                  var number = event.which - sub;
                  player.currentTime(player.duration() * number * 0.1);
                }
              },
            },

            emptyHotkey: {
              // Empty
            },

            withoutKey: {
              handler: function (player, options, event) {
                console.log("withoutKey handler");
              },
            },

            withoutHandler: {
              key: function (e) {
                return true;
              },
            },

            malformedKey: {
              key: function () {
                console.log(
                  "I have a malformed customKey. The Key function must return a boolean."
                );
              },
              handler: function (player, options, event) {
                //Empty
              },
            },
          },
        });

        if (vm.playbackSpeed) {
          this.playbackRate(vm.playbackSpeed);
        }
      });
      //khởi tạo video player
      // if(window.innerWidth <= 768) //nếu là mobile
      //     var src = window.innerWidth <= 768 ? vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/index.m3u8?" + now.getMinutes();
      // else
      //     var src = vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/?" + now.getMinutes();
      // var src = window.innerWidth <= 768
      //     ? vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/index.m3u8?" + now.getMinutes()
      //     : vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/?" + now.getMinutes();
      //
      // vm.player.src({ src: src, type: 'application/x-mpegURL', withCredentials: false});
      var qualityData = vm.getVideoQuality;

      var src =
        vm.currentServerUrl +
        "/" +
        qualityData +
        "/" +
        vm.currentMediaName +
        "/index.m3u8?" +
        now.getMinutes();
      console.log('src..........361', src)

      $.ajax({
        url: "/video/get-link?url=" + src,
        type: "GET",
        success: function (res) {
          console.log('res..............364', res)
          vm.player.src({
            src: res,
            type: "application/x-mpegURL",
            withCredentials: false,
          });
        },
      });
    },

    clickPlayVideo: function () {
      var vm = this;
      $(".movie-play").css("display", "none");
      $("#myplayer_" + playerId).css("display", "block");

      vm.player.play();

      //cài vòng lặp ghi nhớ current time vào cookie sau mỗi 3s, không cần nữa vì có thể bắt event khi rời trang hoặc f5
      // setInterval(function () { setCookie('current_time_'+ lesson_lessonDetail.id, player.currentTime(), 10); }, 5000);

      // bắt sự kiện rời trang hoặc f5 và lưu current time vào cookie
      window.addEventListener("beforeunload", (event) => {
        setCookie(
          "current_time_" + lesson_lessonDetail.id,
          vm.player.currentTime(),
          10
        );
      });
    },

    checkServerLocation: function () {
      var vm = this;
      //check lại cài đặt localtion
      if (getCookie("v_location")) {
        var locationId = getCookie("v_location");
        if (locationId == listServers[0].id) {
          vm.currentServerId = listServers[0].id;
          vm.currentServerUrl = listServers[0].url;
        } else {
          vm.currentServerId = listServers[1].id;
          //ramdom để chia tải server
          var ran = Math.floor(Math.random() * Math.floor(2));
          if (ran == 0) vm.currentServerUrl = listServers[1].url;
          else vm.currentServerUrl = "https://tokyo-v2.dungmori.com";
        }
      }
    },
  },

  mounted: function () {
    var vm = this;

    // hiển thị nút chọn server di động
    var width = window.innerWidth > 0 ? window.innerWidth : screen.width;
    if (width <= 768) {
      $(".select-localtion-select").css("display", "block");
      $(".server-item").css("display", "none");
    }

    // console.log("Tên video: ", vm.currentMediaName);

    //check lại cài đặt quality
    if (getCookie("v_quality")) {
      if (getCookie("v_quality") != vm.currentQuality) {
        vm.currentQuality = getCookie("v_quality");
      }
    }

    vm.checkServerLocation();
    vm.initVideo();
  },

  beforeDestroy: function () {
    if (this.player) {
      this.player.dispose();
    }
  },
});
