$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  }
});
// Tắt preview image
function closePreviewImg() {
  $('#preview-image').css('display', 'none');
  $('#input_image_mess').val('');
}

var preventMultiImage = false;

var chatbox = new Vue({
  el: '#chat-box',
  data: function () {
    return {
      url: window.location.origin,
      listConversation: [],
      title: document.title,
      socket: null,
      listMessages: [],
      user: null,
      mess: '', // Input msg content
      previewImg: false,
      loadMoreState: true,
      loading: false,
      countMess: typeof countMess == 'undefined' ? 0 : countMess,
      userId: null,
      endOfConversation: false,
      showConversationList: false,
      curConv: null,
      people: [],
      showFullPin: false,
      pinMessages: [],
      rootMessage: null,
      searchKey: '',
      notReadFilter: false,
      typeChatFilter: 'all',
      searchTimeout: null,
      oldListCon: [],
      settingOpen: false,
      groupName: '',
      isEditGroupName: false,
      searchMsgKey: '',
      listSearchMsg: [],
      endOfSearchMsg: false,
      hasNextMsg: false,
      isTag: false,
      tagName: null,
      tagSelectedIndex: 0,
      tagData: [],
      setTimeoutMsg: null,
      searchMembers: [],
      searchMembersPage: 0,
      endOfSearchmember: false,
      searchMemberKey: '',
      searchMembersChat: [],
      searchMembersChatPage: 0,
      endOfSearchmemberChat: false,
      searchMemberChatKey: '',
      oldConv: null,
      currentMemberSwitch: null,
      conversationGroups: [],
      switchConvId: null,
      histories: [],
      notSeenMsgId: null,
      tagMsgId: null,
      testerSenderId: null,
      loadingConversation: false,
      focusConversationId: null,
      chatMembers: [],
      chatMembetsPage: 0,
      admins: [],
      adminSearchKey: '',
      currentEditMessage: null,
      preventSpam: false,
      markUnreadCons: []
    };
  },

  computed: {
    testers: function () {
      return this.people.filter((p) => p.is_tester);
    },
    members: function () {
      return this.people.filter((p) => !p.is_tester);
    },
    tagMembers: function () {
      var vm = this;
      if (vm.tagName === null) {
        return [];
      }
      if (vm.tagName == '') {
        return [{ name: 'all', id: -1 }].concat(this.people);
      }
      return ([{ name: 'all', id: -1 }].concat(this.people)).filter((p) => p.name && p.name.toLowerCase().includes(vm.tagName.toLowerCase()));
    },
    adminFilter: function() {
      if (!this.adminSearchKey) {
        return this.admins;
      }
      return this.admins.filter((a) => a.name && a.email && (a.name.toLowerCase().includes(this.adminSearchKey.toLowerCase()) || a.email.toLowerCase().includes(this.adminSearchKey.toLowerCase())));
    }
  },

  watch: {
    searchKey: function (newValue) {
      var vm = this;
      if (vm.searchTimeout) {
        clearTimeout(vm.searchTimeout);
      }
      vm.searchTimeout = setTimeout(function () {
        if (newValue == '') {
          if (vm.oldListCon.length > 0) {
            vm.listConversation = vm.oldListCon;
            vm.endOfConversation = (vm.listConversation.length - 1) % 10 != 0;
            vm.oldListCon = [];
          }
        } else {
          vm.endOfConversation = false;
          if (vm.oldListCon.length == 0) {
            vm.oldListCon = vm.listConversation;
          }
          vm.listConversation = [];
          vm.loadMoreConversation();
        }
      }, 500);
    },
    notReadFilter: function () {
      var vm = this;
      if (vm.searchTimeout) {
        clearTimeout(vm.searchTimeout);
      }
      vm.searchTimeout = setTimeout(function () {
        vm.listConversation = [];
        vm.endOfConversation = false;
        vm.loadMoreConversation();
      }, 500);
    },
    typeChatFilter: function () {
      var vm = this;
      if (vm.searchTimeout) {
        clearTimeout(vm.searchTimeout);
      }
      vm.searchTimeout = setTimeout(function () {
        vm.listConversation = [];
        vm.endOfConversation = false;
        vm.loadMoreConversation();
      }, 500);
    },
    searchMsgKey: function (newValue) {
      var vm = this;
      if (vm.searchTimeout) {
        clearTimeout(vm.searchTimeout);
      }
      vm.searchTimeout = setTimeout(function () {
        vm.endOfSearchMsg = false;
        vm.listSearchMsg = [];
        if (newValue != '') {
          vm.searchMsg();
        }
      }, 500);
    },
    searchMemberKey: function () {
      var vm = this;
      if (vm.searchTimeout) {
        clearTimeout(vm.searchTimeout);
      }
      vm.searchTimeout = setTimeout(function () {
        vm.endOfSearchmember = false;
        vm.searchMembersPage = 0;
        vm.searchMembers = [];
        vm.searchMember();
      }, 500);
    },
    searchMemberChatKey: function () {
      var vm = this;
      if (vm.searchTimeout) {
        clearTimeout(vm.searchTimeout);
      }
      vm.searchTimeout = setTimeout(function () {
        vm.endOfSearchmemberChat = false;
        vm.searchMembersChatPage = 0;
        vm.searchMembersChat = [];
        vm.searchMemberChat();
      }, 500);
    },
    tagMembers: function () {
      this.tagSelectedIndex = 0;
    }
  },

  methods: {
    moveCursor(node, position) {
      var range = document.createRange();
      var selection = window.getSelection();
      range.setStart(node, position);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    },
    changeTextInput: function () {
      var inputContent = $('#mess-input').text();
      if (this.setTimeoutMsg != null) {
        clearTimeout(this.setTimeoutMsg);
      }

      this.setTimeoutMsg = setTimeout(() => {
        var aIndex = inputContent.lastIndexOf('@');
        if (!this.curConv.is_group || aIndex == -1) {
          this.tagName = null;
          this.isTag = false;
          return;
        }

        var tagName = inputContent.substring(aIndex + 1);
        if (this.tagData.find((t) => t.text == tagName)) {
          this.tagName = null;
          this.isTag = false;
          return;
        }

        this.tagName = tagName;
        this.isTag = true;
      }, 100);
    },
    onKeyDown(event) {
      if (event.key === 'ArrowUp') {
        this.upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        this.downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        this.enterHandler();
        return true;
      }

      return false;
    },
    upHandler() {
      this.tagSelectedIndex = (this.tagSelectedIndex + this.tagMembers.length - 1) % this.tagMembers.length;
    },

    downHandler() {
      this.tagSelectedIndex = (this.tagSelectedIndex + 1) % this.tagMembers.length;
    },

    enterHandler() {
      if (this.tagMembers.length === 0) {
        return;
      }
      this.applyTagMemberName(this.tagMembers[this.tagSelectedIndex]);
    },
    randomString(length = 10) {
      var result = '';
      var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      var charactersLength = characters.length;
      for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      return result;
    },
    applyTagMemberName: function (tagMember) {
      var aIndex = document.getElementById('mess-input').innerHTML.lastIndexOf('@');
      var randomId = this.randomString(8);
      document.getElementById('mess-input').innerHTML =
      document.getElementById('mess-input').innerHTML.substring(0, aIndex) +
        `<span id='${randomId}'>@${tagMember.name}</span>&nbsp`;
      this.isTag = false;
      this.tagName = null;
      this.tagData.push({ text: tagMember.name, user_id: tagMember.id });

      setTimeout(() => {
        var childNodes = document.getElementById('mess-input').childNodes;

        if (childNodes.length >= 2) {
          childNodes[childNodes.length - 2].scrollIntoView();
        }

        // Calcular the cursor of the text again after change innerHTML
        var destinationNodeIndex = null;
        // Get the index by get the current highlight node and plus by one
        for (var i = 0; i < childNodes.length; i++) {
          if (childNodes[i].id == randomId) {
            destinationNodeIndex = i + 1;
          }
        }
        this.moveCursor(childNodes[destinationNodeIndex], 1);
      }, 0);
    },
    // Ẩn thanh chatbox
    hideChatbox: function () {
      $('#chat-box').css('display', 'none');
    },

    // Bắt sự kiện thay đổi điều kiện lọc
    initChat: async function () {
      var vm = this;
      if (vm.listConversation.length > 0) {
        return;
      }

      await vm.loadMoreConversation();

      if (vm.focusConversationId) {
        // Open chat with params ?chat=id to open exactly conversation
        var conversation = vm.listConversation.find((c) => c.id == vm.focusConversationId);
        if (!conversation) {
          var conversation = await $.post(window.location.origin + '/api/chat/get-focus-conversation', {
            id: vm.focusConversationId
          });
          if (conversation.last_message) {
            conversation.last_message.created_at = vm.formatTime(conversation.last_message.created_at);
            try {
              conversation.last_message.content = JSON.parse(conversation.last_message.content);
            } catch (error) {
              console.log('error parse in load more conversation', error);
            }
          }
          if (conversation && conversation.id) {
            vm.listConversation = [conversation, ...vm.listConversation];
          }
        }
        if (conversation && conversation.id) {
          vm.selectConversation(conversation);
        } else {
          vm.selectConversation(vm.listConversation[0]);
        }
        vm.focusConversationId = null;
      } else {
        // Default case => open first
        vm.selectConversation(vm.listConversation[0]);
      }
      if (vm.listConversation.length == 1) {
        vm.showConversationList = false;
        $('#chat-box').css('display', 'flex');
      } else {
        vm.showConversationList = true;
        $('#chat-box').css('display', 'flex');
        $('#chat-box').css('width', '780px');
      }
    },

    searchMsg: async function () {
      var vm = this;
      if (vm.endOfSearchMsg) {
        return;
      }
      var lastId = vm.listSearchMsg.length > 0 ? vm.listSearchMsg[vm.listSearchMsg.length - 1].id : null;
      var response = await $.post(window.location.origin + '/api/chat/search-msg', {
        lastId: lastId,
        cId: vm.curConv.id,
        key: vm.searchMsgKey
      });
      if (response.length < 20) {
        vm.endOfSearchMsg = true;
      }
      for (var i = 0; i < response.length; i++) {
        var member = vm.people.find((p) => p.id == response[i].sender_id);
        response[i].member = member;
        try {
          response[i].content = JSON.parse(response[i].content);
        } catch (error) {
          console.log('error parse in search msg', error);
        }
      }
      vm.listSearchMsg.push(...response);
    },

    loadMoreConversation: async function () {
      var vm = this;

      if (vm.endOfConversation || vm.loadingConversation) {
        return;
      }

      vm.loadingConversation = true;
      var response = await $.post(window.location.origin + '/api/chat/get-conversations', {
        userId: vm.userId,
        ids: vm.listConversation.map((c) => c.id),
        key: vm.searchKey,
        notReadFilter: vm.notReadFilter,
        typeChatFilter: vm.typeChatFilter,
      });

      if (response && response.length < 10) {
        vm.endOfConversation = true;
      }

      // Format time
      for (var i = 0; i < response.length; i++) {
        if (response[i].last_message) {
          response[i].last_message.created_at = vm.formatTime(response[i].last_message.created_at);
          try {
            response[i].last_message.content = JSON.parse(response[i].last_message.content);
          } catch (error) {
            console.log('error parse in load more conversation', error);
          }
        }
      }

      vm.listConversation = [...vm.listConversation, ...response];
      vm.loadingConversation = false;
      return response;
    },

    formatTime(time) {
      var now = moment();
      var createdAt = moment(time);
      if (now.diff(time, 'days') == 0) {
        return createdAt.format('HH:mm');
      } else if (createdAt.year() == now.year()) {
        return createdAt.format('DD-MM HH:mm');
      }
      return createdAt.format('DD-MM-YY HH:mm');
    },
    changeFullPinState: function () {
      this.showFullPin = !this.showFullPin;
    },
    selectConversationById: function (id) {
      this.focusConversationId = id;
    },
    selectConversation: function (conversation) {
      if (this.curConv && this.curConv.id == conversation.id) {
        return;
      }

      this.removeListeners();
      this.curConv = conversation;
      this.listMessages = [];
      this.pinMessages = [];
      this.people = [];
      this.showFullPin = false;
      this.settingOpen = false;
      this.isEditGroupName = false;
      this.rootMessage = null;
      this.searchMsgKey = '';
      this.listSearchMsg = [];
      this.endOfSearchMsg = false;
      this.searchMembers = [];
      this.endOfSearchmember = false;
      this.searchMembersPage = 0;
      this.searchMemberKey = '';
      this.notSeenMsgId = null;
      this.tagMsgId = null;
      this.loading = false;
      this.loadMoreState = true;
      this.testerSenderId = null;
      this.loadMoreMsg();
      $('#mess-input').trigger('focus');

      var vm = this;
      if (!vm.listConversation.find((c) => c.id == conversation.id)) {
        vm.listConversation.unshift(conversation);
      }

      setTimeout(() => {
        $('#chat-screen').on('scroll', function () {
          if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
            if (vm.listMessages.length > 0) {
              vm.loadNextMsg();
            }
          }
        });

        var imageInput = document.getElementById("input_image_mess");
        var fileInput = document.getElementById("input_file_mess");
        window.addEventListener('paste', e => {
          if (e.clipboardData.files.length > 0) {
            if (e.clipboardData.files[0].type == "image/png") {
              if (preventMultiImage) {
                return;
              }
              preventMultiImage = true;
              imageInput.files = e.clipboardData.files;
              sendImgMess();
            } else {
              fileInput.files = e.clipboardData.files;
              sendFileMess();
            }
            e.preventDefault();
          }
        });
      }, 500);
    },

    backChat: function () {
      this.removeListeners();
      if (this.oldConv) {
        this.selectConversation(this.oldConv);
        this.oldConv = null;
      } else {
        this.curConv = null;
      }
    },

    removeListeners: function () {
      var vm = this;
      if (!vm.curConv) {
        return;
      }
      vm.socket.removeAllListeners('sent_enc_' + vm.curConv.id + '_' + vm.curConv.user_id);
      vm.socket.removeAllListeners('group_message_received_enc_' + vm.curConv.id);
      vm.socket.removeAllListeners('received_enc_' + vm.curConv.id + '_' + vm.curConv.user_id);
      vm.socket.removeAllListeners('received_' + vm.curConv.id + '_' + vm.curConv.user_id);
      vm.socket.removeAllListeners('unsend_msg_enc_' + vm.curConv.id);
      vm.socket.removeAllListeners('pin_msg_enc_' + vm.curConv.id);
      vm.socket.removeAllListeners('unpin_msg_enc_' + vm.curConv.id);
      vm.socket.removeAllListeners('group_message_readed' + vm.curConv.id);
      vm.socket.removeAllListeners('edit_msg_' + vm.curConv.id);
    },

    checkUnreadMessage: function () {
      var vm = this;

      $.post(window.location.origin + '/api/chat/check-unread-message', { id: vm.userId }, function (response) {
        if (response) {
          vm.initChat();
        }
      });
    },

    markRead: function () {
      var vm = this;
      vm.countMess = 0;
      $.post(
        window.location.origin + '/api/chat/mark-read-conversation',
        { id: vm.curConv.id },
        function (response) {
          if (!vm.curConv.is_group && vm.curConv.friend_id) {
            if (typeof notiCount != 'undefined') {
              notiCount.countUnreadMess -= response;
            }
          }
        }
      );
    },

    // Tải thêm tin nhắn cũ hơn
    loadMoreMsg: function () {
      var vm = this;

      var lastId = vm.listMessages.length > 0 ? vm.listMessages[0].id : null;
      vm.loading = true;

      var isFirstPage = vm.listMessages.length == 0;

      var data = {
        lastId: lastId,
        cId: vm.curConv.id,
        isGroup: vm.curConv.is_group
      };

      setTimeout(function () {
        $.post(window.location.origin + '/api/chat/load-more-messages', data, function (response) {
          if (response.messages.length > 0 && response.messages[0].conversation_id != vm.curConv.id) {
            return;
          }
          if (response.notSeenMsgId) {
            vm.notSeenMsgId = response.notSeenMsgId;
          }
          if (response.tagMsgId) {
            vm.tagMsgId = response.tagMsgId;
          }

          // Pin messages
          if (vm.curConv.is_group && lastId == null && response.pinMessages) {
            var pinMessages = response.pinMessages.map(function (item) {
              try {
                item.content = JSON.parse(item.content);
              } catch (error) {
                console.log('error JSON.parse message from load', error);
              }
              return item;
            });
            vm.pinMessages = pinMessages;
          }

          if (response.messages.length < 20) vm.loadMoreState = false;

          if (response.people) {
            vm.people = response.people;
          }

          if (response.messages.length > 0) {
            var messages = response.messages;
            messages.reverse();
            messages.map(function (item) {
              vm.handleMessage(item);
              return item;
            });
            vm.listMessages.unshift(...messages);
            vm.loading = false;
          }
          if (isFirstPage) {
            vm.goToBottom();
            vm.listen();
          }
          vm.markRead();

          // Thêm emoji khung chat
          setTimeout(function () {
            new EmojiPicker();
            $('#mess-input').trigger('focus');
          }, 100);
        });
      }, 600);
    },

    goToNotSeenMsg: function () {
      if (this.notSeenMsgId) {
        this.getListFocusMessages(this.notSeenMsgId);
        this.notSeenMsgId = null;
      }
    },

    goToTagMsg: function () {
      if (this.tagMsgId) {
        this.getListFocusMessages(this.tagMsgId);
        this.tagMsgId = null;
      }
    },

    loadNextMsg: function () {
      var vm = this;
      if (vm.listMessages.length === 0 || vm.hasNextMsg === false) {
        return;
      }

      var data = {
        newestId: vm.listMessages[vm.listMessages.length - 1].id,
        cId: vm.curConv.id,
        isGroup: vm.curConv.is_group
      };

      $.post(window.location.origin + '/api/chat/load-next-messages', data, function (response) {
        var messages = response;
        if (messages.length < 20) {
          vm.hasNextMsg = false;
        }
        messages.map(function (item) {
          vm.handleMessage(item);
          return item;
        });
        vm.listMessages.push(...messages);
      });
    },

    changePinState: function (i) {
      this.pinMessages = this.pinMessages.map((p, index) => {
        if (index == i) {
          p.showFull = !p.showFull;
        }
        return p;
      });
    },

    // In ra thông tin có dấu cách
    renderContent: function (msg) {
      var info = msg.content;

      if (msg.mention_data) {
        var mentionData = JSON.parse(msg.mention_data);
        if (mentionData && mentionData.length > 0) {
          for (var i = 0; i < mentionData.length; i++) {
            info = info.replace(`@${mentionData[i].text}`, `<span style="display:contents;font-weight:bold">@${mentionData[i].text}</span>`);
          }
        }
      }

      var result = info;

      result = result.replace('<', '&#60;');
      result = result.replace('>', '&#62;');

      // Xử lý xuống dòng
      result = info.replace(new RegExp('\r?\n', 'g'), '<br />');

      var re = /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;
      return result.replace(re, function (match, lParens, url) {
        var rParens = '';
        lParens = lParens || '';

        // Try to strip the same number of right parens from url
        // as there are left parens.  Here, lParenCounter must be
        // a RegExp object.  You cannot use a literal
        //     while (/\(/g.exec(lParens)) { ... }
        // because an object is needed to store the lastIndex state.
        var lParenCounter = /\(/g;
        while (lParenCounter.exec(lParens)) {
          var m;
          // We want m[1] to be greedy, unless a period precedes the
          // right parenthesis.  These tests cannot be simplified as
          //     /(.*)(\.?\).*)/.exec(url)
          // because if (.*) is greedy then \.? never gets a chance.
          if ((m = /(.*)(\.\).*)/.exec(url) || /(.*)(\).*)/.exec(url))) {
            url = m[1];
            rParens = m[2] + rParens;
          }
        }
        return lParens + "<a class='msg-link' href='" + url + "' target='_blank'>" + url + '</a>' + rParens;
      });
    },

    // Sự kiện kích đọc tin nhắn
    readMess: async function () {
      var vm = this;
      if (vm.curConv.is_group) {
        if (vm.listMessages.length > 0 && vm.listMessages[vm.listMessages.length - 1].id) {
          vm.socket.emit('readed', { conversationId: vm.curConv.id, isGroupChat: true, messageId: vm.listMessages[vm.listMessages.length - 1].id });
        }
      } else {
        vm.socket.emit('readed', { conversationId: vm.curConv.id, receiverId: 0 });
      }

      if (!vm.curConv.is_group && vm.curConv.friend_id == null) {
        if (typeof countAdminMess != 'undefined' && countAdminMess > 0) {
          notiCount.countUnreadMess -= countAdminMess;
          countAdminMess = 0;
        }
      }

      if (vm.listMessages.length > 0) {
        // If group => update last read message on server
        if (
          $('#chat-box').css('display') != 'none' &&
          vm.curConv.is_group &&
          vm.listMessages[vm.listMessages.length - 1].id
        ) {
          var response = await $.post(window.location.origin + '/api/chat/update-read-msg', {
            cId: vm.curConv.id,
            msgId: vm.listMessages[vm.listMessages.length - 1].id
          });
          if (response && response > 0) {
            notiCount.countUnreadMess -= response;
          }
        }

        // Update read at last msg in list conversation
        vm.listConversation = vm.listConversation.map(function (conversation) {
          if (conversation.id == vm.curConv.id && vm.listMessages.length > 0) {
            if (!conversation.last_message) {
              conversation.last_message = {};
            }
            conversation.last_message.status = 1;
            conversation.last_message.id = vm.listMessages[vm.listMessages.length - 1].id;
            conversation.last_message_id = vm.listMessages[vm.listMessages.length - 1].id;
          }
          return conversation;
        });
      }

      // Thay đổi số đếm thông báo
      if (typeof notiCount != 'undefined') {
        var tmp = notiCount.countUnreadMess + notiCount.countNotification;
        if (tmp > 0) {
          document.title = '(' + tmp + ') ' + vm.title;
        } else {
          document.title = vm.title;
        }
      }
    },

    readAll: async function() {
      if (this.preventSpam) {
        return;
      }
      this.preventSpam = true;
      await $.post(window.location.origin + '/api/chat/read-all');
      this.preventSpam = false;
      this.listConversation = this.listConversation.map((c) => {
        if (c.last_message) {
          if (c.is_group) {
            c.last_message_id = c.last_message.id;
          } else {
            c.last_message.status = true;
          }
        }
        return c;
      });
    },

    copyLink: function () {
      var link = window.location.origin + '?chat=' + this.curConv.id;
      navigator.clipboard.writeText(link);
      this.settingOpen = false;
    },

    updateLastMsgWhenSend: function (message) {
      var vm = this;

      vm.listConversation = vm.listConversation.map(function (conversation) {
        if (conversation.id == message.conversationId) {
          if (!conversation.last_message) {
            conversation.last_message = {};
          }
          conversation.last_message.content = message.content;
          conversation.last_message.created_at = moment().format('HH:mm');
          conversation.last_message.type = message.type;
          conversation.last_message.status = 1;
          conversation.last_message.id = -1;
          conversation.last_message_id = -1;
        }
        return conversation;
      });
    },

    // Gửi tin nhắn
    sendMess: function () {
      ga('send', 'event', 'send_message_category', 'send_message', 'send_message_label');

      var vm = this;

      if (vm.tagMembers.length > 0) {
        return;
      }

      if ($('#mess-input').text().trim().length === 0) {
        return;
      }

      if ($('#mess-input').text()) {
        if (this.currentEditMessage) {
          vm.socket.emit('edit_msg', this.encrypt({ id: this.currentEditMessage.id, content: $('#mess-input').text() }));
          $('#mess-input').text('')
          this.currentEditMessage = null;
          return;
        }

        // Data to emit socket
        var testerSender = null;
        if (vm.testerSenderId) {
          testerSender = vm.testers.find((t) => t.id == vm.testerSenderId);
        }

        var messSocket = {
          senderId: vm.testerSenderId ? vm.testerSenderId : vm.curConv.user_id,
          senderType: 'user',
          senderName: testerSender ? testerSender.name : vm.curConv.user_name,
          senderAvatar: testerSender ? testerSender.avatar : vm.curConv.user_avatar,
          conversationId: vm.curConv.id,
          receiverId: vm.curConv.fr_id ? vm.curConv.fr_id : 0,
          content: $('#mess-input').text().trim(),
          type: 'text',
          sentId: Date.now() + '_' + vm.curConv.user_id + '_' + Math.floor(Math.random() * 10000 + 10),
          mentionData: vm.tagData.length > 0 ? vm.tagData : null
        };

        if (vm.rootMessage) {
          messSocket.rootMessage = vm.rootMessage;
        }

        if (vm.curConv.is_group) {
          var res = vm.encrypt(messSocket);
          vm.socket.emit('group_message_send_enc', res);
        } else {
          if (chatUser < 0) {
            vm.socket.emit('send', messSocket);
          } else {
            var res = vm.encrypt(messSocket);
            vm.socket.emit('send_enc', res);
          }
        }

        var newMess = {
          content: $('#mess-input').text().trim(),
          sender_id: vm.testerSenderId ? vm.testerSenderId : vm.curConv.user_id,
          user_avatar: testerSender ? testerSender.avatar : vm.curConv.user_avatar,
          sender_type: 'user',
          type: 'text',
          sentId: messSocket.sentId,
          rootMessage: vm.rootMessage,
          message_id: vm.rootMessage ? vm.rootMessage.id : null,
          mentionData: vm.tagData.length > 0 ? vm.tagData : null
        };

        vm.listMessages.push(newMess);
        $('#mess-input').text('');
        vm.tagData = [];

        vm.rootMessage = null;

        // Emit sự kiện ngừng gõ
        vm.socket.emit('stopTyping', { conversationId: vm.curConv.id, senderId: vm.curConv.user_id });

        vm.goToBottom();

        // Update last msg on conversation list
        this.updateLastMsgWhenSend(messSocket);
      }
    },

    editMsg: function (msg) {
      $('#mess-input').text(msg.content);
      this.currentEditMessage = msg;
    },

    goToRootMsg: function (messageId) {
      var item = document.getElementById('item_container_' + messageId);
      if (!item) {
        this.getListFocusMessages(messageId);
        return;
      }
      $('#chat-screen').animate(
        {
          scrollTop: item.offsetTop - item.offsetHeight - 10 - $('#pin_container').height()
        },
        350
      );
      $('.content-ani').removeClass('content-ani');
      $('#content_' + messageId).addClass('content-ani');
    },

    getListFocusMessages: function (messageId) {
      var vm = this;
      vm.loading = true;

      var data = {
        focusId: messageId,
        cId: vm.curConv.id,
        isGroup: vm.curConv.is_group
      };

      vm.listMessages = [];
      $.post(window.location.origin + '/api/chat/load-focus-messages', data, function (response) {
        vm.loadMoreState = response.hasPrevious;

        var messages = response.messages;
        messages.map(function (item) {
          vm.handleMessage(item);
          return item;
        });
        vm.listMessages.unshift(...messages);
        vm.loading = false;
        setTimeout(() => {
          vm.goToRootMsg(messageId);
          vm.hasNextMsg = true;
        }, 200);
      });
    },

    focusMessage: function (messageId) {
      $('#search_msg').modal('toggle');
      this.settingOpen = false;

      if (this.listMessages.find((m) => m.id == messageId)) {
        this.goToRootMsg(messageId);
      } else {
        this.getListFocusMessages(messageId);
      }
    },

    handleMessage: function (item) {
      try {
        item.content = JSON.parse(item.content);
        if (item.rootMessage) {
          item.rootMessage.content = JSON.parse(item.rootMessage.content);
        }
        if (item.sender_id != this.curConv.user_id) {
          var people = this.people.find((p) => p.id == item.sender_id);
          item.user_avatar = people ? people.avatar : null;
          item.user_name = people ? people.name : 'Ẩn danh';
          item.is_tester = people ? people.is_tester : false;
        }
      } catch (error) {
        console.log('error JSON.parse message from load', error);
      }
      return item;
    },

    sendTemplateMess: function (msg) {
      var vm = this;

      var chatAdmin = vm.listConversation.find((c) => !c.is_group);
      if (!chatAdmin) {
        return;
      }

      if (msg) {
        // Data to emit socket
        var messSocket = {
          senderId: chatAdmin.user_id,
          senderType: 'user',
          senderName: chatAdmin.user_name,
          conversationId: chatAdmin.id,
          receiverId: 0,
          content: msg,
          type: 'text'
        };

        var res = vm.encrypt(messSocket);
        vm.socket.emit('send_enc', res);

        var newMess = {
          content: msg,
          sender_id: chatAdmin.user_id,
          sender_type: 'user',
          type: 'text'
        };

        vm.listMessages.push(newMess);
        $('#mess-input').text('');

        // Emit sự kiện ngừng gõ
        vm.socket.emit('stopTyping', { conversationId: chatAdmin.id, senderId: chatAdmin.user_id });

        vm.goToBottom();

        // Update last msg on conversation list
        this.updateLastMsgWhenSend(messSocket);
      }
    },
    // Gửi ảnh
    sendImage: function (mess) {
      var vm = this;

      var testerSender = null;
      if (vm.testerSenderId) {
        testerSender = vm.testers.find((t) => t.id == vm.testerSenderId);
      }

      // Data to emit socket
      var messSocket = {
        senderId: vm.testerSenderId ? vm.testerSenderId : vm.curConv.user_id,
        senderType: 'user',
        senderName: testerSender ? testerSender.name : vm.curConv.user_name,
        senderAvatar: testerSender ? testerSender.avatar : vm.curConv.user_avatar,
        conversationId: vm.curConv.id,
        receiverId: vm.curConv.fr_id ? vm.curConv.fr_id : 0,
        content: mess,
        type: 'image',
        sentId: Date.now() + '_' + vm.curConv.user_id + '_' + Math.floor(Math.random() * 10000 + 10)
      };

      if (vm.rootMessage) {
        messSocket.rootMessage = vm.rootMessage;
      }

      if (vm.curConv.is_group) {
        var res = vm.encrypt(messSocket);
        vm.socket.emit('group_message_send_enc', res);
      } else if (chatUser > 0) {
        var res = vm.encrypt(messSocket);
        vm.socket.emit('send_enc', res);
      } else {
        vm.socket.emit('send', messSocket);
      }

      var newMess = {
        content: mess,
        sender_id: vm.testerSenderId ? vm.testerSenderId : vm.curConv.user_id,
        user_avatar: testerSender ? testerSender.avatar : vm.curConv.user_avatar,
        sender_type: 'user',
        type: 'image',
        sentId: messSocket.sentId,
        rootMessage: vm.rootMessage,
        message_id: vm.rootMessage ? vm.rootMessage.id : null
      };

      vm.listMessages.push(newMess);
      vm.rootMessage = null;

      // Emit sự kiện ngừng gõ
      vm.socket.emit('stopTyping', { conversationId: vm.curConv.id, senderId: vm.curConv.user_id });
      vm.goToBottom();

      // Update last msg on conversation list
      this.updateLastMsgWhenSend(messSocket);
    },

    sendFile: function (mess) {
      var vm = this;

      var testerSender = null;
      if (vm.testerSenderId) {
        testerSender = vm.testers.find((t) => t.id == vm.testerSenderId);
      }

      // Data to emit socket
      var messSocket = {
        senderId: vm.testerSenderId ? vm.testerSenderId : vm.curConv.user_id,
        senderType: 'user',
        senderName: testerSender ? testerSender.name : vm.curConv.user_name,
        senderAvatar: testerSender ? testerSender.avatar : vm.curConv.user_avatar,
        conversationId: vm.curConv.id,
        receiverId: vm.curConv.fr_id ? vm.curConv.fr_id : 0,
        content: mess,
        type: 'file',
        sentId: Date.now() + '_' + vm.curConv.user_id + '_' + Math.floor(Math.random() * 10000 + 10)
      };

      if (vm.rootMessage) {
        messSocket.rootMessage = vm.rootMessage;
      }

      if (vm.curConv.is_group) {
        var res = vm.encrypt(messSocket);
        vm.socket.emit('group_message_send_enc', res);
      } else if (chatUser > 0) {
        var res = vm.encrypt(messSocket);
        vm.socket.emit('send_enc', res);
      } else {
        vm.socket.emit('send', messSocket);
      }

      var newMess = {
        content: mess,
        sender_id: vm.testerSenderId ? vm.testerSenderId : vm.curConv.user_id,
        user_avatar: testerSender ? testerSender.avatar : vm.curConv.user_avatar,
        sender_type: 'user',
        type: 'file',
        sentId: messSocket.sentId,
        rootMessage: vm.rootMessage,
        message_id: vm.rootMessage ? vm.rootMessage.id : null
      };

      vm.listMessages.push(newMess);
      vm.rootMessage = null;

      // Emit sự kiện ngừng gõ
      vm.socket.emit('stopTyping', { conversationId: vm.curConv.id, senderId: vm.curConv.user_id });
      vm.goToBottom();

      // Update last msg on conversation list
      this.updateLastMsgWhenSend(messSocket);
    },

    encrypt: function (message) {
      return emsg(JSON.stringify(message));
    },

    decrypt: function (message) {
      return JSON.parse(dmsg(message));
    },

    // Lắng nghe sự kiện tin nhắn từ admin về
    listen: function () {
      var vm = this;

      vm.socket.on('sent_enc_' + vm.curConv.id + '_' + vm.curConv.user_id, function (message) {
        var message = vm.decrypt(message);
        vm.listMessages = vm.listMessages.map((m) => {
          if (m.sentId == message.sentId) {
            m = Object.assign(m, message);
          }
          return m;
        });
      });

      vm.socket.on('like_message' + vm.curConv.id, function (message) {
        vm.listMessages = vm.listMessages.map((m) => {
          if (m.id == message.id) {
            m.reaction_data = message.reaction_data;
          }
          return m;
        });
      });

      vm.socket.on('edit_msg_' + vm.curConv.id, function (message) {
        var message = vm.decrypt(message);
        vm.listMessages = vm.listMessages.map((m) => {
          if (m.id == message.id) {
            m.content = message.content;
          }
          return m;
        });
        vm.listConversation = vm.listConversation.map((c) => {
          if (c.last_message && c.last_message.id == message.id) {
            c.last_message.content = message.content;
          }
          return c;
        });
      });

      if (vm.curConv.is_group) {
        // Nhận tin nhắn group chat
        vm.socket.on('group_message_received_enc_' + vm.curConv.id, function (message) {
          var message = vm.decrypt(message);
          if (!message || !message.id) {
            return;
          }

          var newMess = {
            content: message.content,
            sender_id: message.senderId,
            conversation_id: message.conversation_id,
            created_at: message.created_at,
            user_avatar: message.avatar,
            user_name: message.name,
            mention_data: message.mention_data,
            sender_type: 'user',
            type: message.type,
            id: message.id,
            message_id: message.message_id,
            rootMessage: message.rootMessage
          };

          vm.listMessages.push(newMess);

          // If on bottom, scroll to bottom
          if (
            $('#chat-screen') &&
            $('#chat-screen')[0] &&
            $('#chat-screen').scrollTop() + $('#chat-screen').innerHeight() >= $('#chat-screen')[0].scrollHeight - 25
          ) {
            vm.goToBottom();
          }
        });

        // Pin message
        vm.socket.on('pin_msg_enc_' + vm.curConv.id, function (message) {
          var message = vm.decrypt(message);
          vm.pinMessages.unshift(message);
          vm.listMessages = vm.listMessages.map((m) => {
            if (m.id == message.id) {
              m.pinned_at = message.pinned_at;
            }
            return m;
          });
        });

        // Unpin message
        vm.socket.on('unpin_msg_enc_' + vm.curConv.id, function (message) {
          var message = vm.decrypt(message);
          vm.pinMessages = vm.pinMessages.filter((m) => m.id != message.id);
          vm.listMessages = vm.listMessages.map((m) => {
            if (m.id == message.id) {
              m.pinned_at = null;
            }
            return m;
          });
        });

        // Read
        vm.socket.on('group_message_readed' + vm.curConv.id, function (data) {
          vm.listMessages = vm.listMessages.map((m) => {
            if (m.seenUsers) {
              m.seenUsers = m.seenUsers.filter((s) => s != data.userId);
            } else {
              m.seenUsers = [];
            }
            if (m.id == data.messageId && !m.seenUsers.includes(data.userId)) {
              m.seenUsers.push(data.userId);
            }
            return m;
          });
        });
      } else {
        // Nhận tin nhắn từ admin hoặc user thường
        if (chatUser > 0) {
          vm.socket.on('received_enc_' + vm.curConv.id + '_' + vm.curConv.user_id, function (message) {
            message = vm.decrypt(message);
            if (!message || !message.content) {
              return;
            }
            vm.receiveAdminMsg(message);
          });
        } else {
          vm.socket.on('received_' + vm.curConv.id + '_' + vm.curConv.user_id, function (message) {
            vm.receiveAdminMsg(message);
          });
        }
      }

      // Unsend message
      vm.socket.on('unsend_msg_enc_' + vm.curConv.id, function (message) {
        var message = vm.decrypt(message);
        vm.listMessages = vm.listMessages.filter((m) => m.id != message.id);
        vm.listMessages = vm.listMessages.map((m) => {
          if (m.rootMessage && m.rootMessage.id == message.id) {
            m.rootMessage = null;
          }
          return m;
        });
      });
    },

    receiveAdminMsg: function (message) {
      var vm = this;

      var newMess = {
        content: message.content,
        sender_id: message.senderId,
        conversation_id: message.conversation_id,
        created_at: message.created_at,
        sender_type: message.senderType,
        user_avatar: message.senderAvatar,
        user_name: message.senderName,
        type: message.type,
        rootMessage: message.rootMessage
      };

      if (message.id) {
        newMess.id = message.id;
      }

      vm.listMessages.push(newMess);

      // If on bottom, scroll to bottom
      if (
        $('#chat-screen') &&
        $('#chat-screen')[0] &&
        $('#chat-screen').scrollTop() + $('#chat-screen').innerHeight() >= $('#chat-screen')[0].scrollHeight - 25
      ) {
        vm.goToBottom();
      }
    },

    // Sự kiện typing
    keyTyping: function () {
      var vm = this;

      if ($('#mess-input').text() != '') {
        vm.socket.emit('typing', { conversationId: vm.curConv.id, senderId: vm.curConv.user_id });
      } else {
        vm.socket.emit('stopTyping', { conversationId: vm.curConv.id, senderId: vm.curConv.user_id });
      }
    },

    // Preview ảnh khi người dùng chọn ảnh
    previewImage: async function (event) {
      var input = event.target;
      if (input.files.length == 0) {
        return;
      }
      if (input.files.length > 12) {
        alert('Bạn chỉ có thể gửi tối đa 12 ảnh một lần');
        return;
      }

      $('#image-list').html('');
      $('#preview-image').css('display', 'flex');
      for (var i = 0; i < input.files.length; i++) {
        var reader = new FileReader();
        reader.onload = async function (e) {
          await $('#image-list').append('<img src="' + e.target.result + '"/>');
        };
        await reader.readAsDataURL(input.files[i]);
      }
    },

    // In ngày giờ đẹp
    prettyDate: function (timestamp) {
      var dateTime = new Date(timestamp);
      var currentTime = new Date();

      if (dateTime.getFullYear() == currentTime.getFullYear()) {
        if (dateTime.getMonth() == currentTime.getMonth() && dateTime.getDate() == currentTime.getDate()) {
          // Today
          return moment(timestamp).format('HH:mm');
        }

        // This year
        return moment(timestamp).format('HH:mm DD/MM');
      }
      return moment(timestamp).format('DD/MM/YYYY');
    },

    // Cuộn tới bottom đọc tin mới
    goToBottom: function () {
      var vm = this;

      setTimeout(function () {
        var objDiv = document.getElementById('chat-screen');
        if (objDiv) {
          objDiv.scrollTop = objDiv.scrollHeight;
          vm.readMess(); // Đánh dấu đã đọc
        }
      }, 100);
    },

    receiveNewMsg: function (message) {
      if (this.curConv && message.senderId == this.curConv.user_id) {
        return;
      }
      var vm = this;

      // Update list conversation
      vm.listConversation = vm.listConversation.map(function (conversation) {
        if (conversation.id == message.conversationId) {
          if (!conversation.last_message) {
            conversation.last_message = {};
          }
          conversation.last_message.sender_id = parseInt(message.senderId);
          conversation.last_message.content = message.content;
          conversation.last_message.created_at = moment().format('HH:mm');
          conversation.last_message.type = message.type;
          conversation.last_message.status = 0;
          conversation.last_message.id = 0;
        }
        return conversation;
      });
    },

    likeMessage: function (item) {
      if (item.liked) {
        return;
      }
      this.listMessages = this.listMessages.map(function (msg) {
        if (msg.id == item.id) {
          msg.liked = !msg.liked;
        }
        return msg;
      });
      this.socket.emit('like_message', { message: item, userId: this.curConv.user_id });
    },

    showListLiked: function (item) {
      var reactionData = JSON.parse(item.reaction_data);
      var result = '';
      for (var i = 0; i < reactionData.length; i++) {
        var person = this.people.find((p) => p.id == reactionData[i]);
        if (person) {
          result += `${i > 0 ? '<br>' : ''}${person.name}`;
        }
      }
      return result;
    },

    pinMsg: function (msg) {
      $('#chat-item-' + msg.id).removeClass('open');
      msg = this.encrypt(msg);
      this.socket.emit('pin_msg_enc', msg);
    },
    unPinMsg: function (msg) {
      msg = this.encrypt(msg);
      this.socket.emit('unpin_msg_enc', msg);
    },
    unsendMsg: function (msg) {
      $('#chat-item-' + msg.id).removeClass('open');
      msg = this.encrypt(msg);
      this.socket.emit('unsend_msg_enc', msg);
    },
    startReply: function (comment) {
      var person = this.people.find((p) => p.id == comment.sender_id);
      comment.senderName = person ? person.name : null;
      this.rootMessage = comment;
      $('#mess-input').trigger('focus');
    },
    clearRootMessage: function () {
      this.rootMessage = null;
    },
    editGroupName: function () {
      this.groupName = this.curConv.title;
      this.isEditGroupName = true;
      $('#edit_group_name').trigger('focus');
    },
    saveGroupName: function () {
      var vm = this;
      $.post(window.location.origin + '/api/chat/save-group-name', {
        cId: vm.curConv.id,
        groupName: vm.groupName
      });
      vm.settingOpen = false;
      vm.isEditGroupName = false;
      vm.curConv.title = vm.groupName;
    },
    changeGroupAvatar: function () {
      $('#input_group_avatar').trigger('click');
    },
    saveGroupAvatar: function () {
      var vm = this;
      var groupAvatar = new FormData($('#form_group_avatar')[0]);
      groupAvatar.append('cId', vm.curConv.id);
      $.ajax({
        url: window.location.origin + '/api/chat/change-group-avatar',
        type: 'post',
        processData: false,
        contentType: false,
        data: groupAvatar,
        success: function (response) {
          vm.settingOpen = false;
          vm.curConv.avatar = response;
        },
        error: function (error) {
          if (error.status == 500) {
            alert('Lỗi khi upload ảnh');
          } else if (error.responseJSON.error == 'imageType') {
            alert('Định dạng ảnh không hợp lệ');
          } else if (error.responseJSON.error == 'imagesize') {
            alert('Lỗi dung lượng ảnh vượt quá giới hạn cho phép 8 MB');
          }
        }
      });
    },
    showPeople: function () {
      $('#show_people').modal('toggle');
    },
    showHistory: async function () {
      var vm = this;
      $('#show_history').modal('toggle');
      var res = await $.get(window.location.origin + '/api/chat/get-member-histories', {
        cId: vm.curConv.id
      });
      vm.histories = res;
    },
    markAsUnread: async function () {
      var vm = this;
      var res = await $.post(window.location.origin + '/api/chat/mark-as-unread', {
        cId: vm.curConv.id,
        isGroup: vm.curConv.is_group,
      });

      if (vm.curConv.is_group) {
        // Nếu là chat group -> update status của last_message_id (tin đã đọc cuối cùng)
        vm.listConversation = vm.listConversation.map(function (conversation) {
          if (conversation.id == vm.curConv.id && vm.listMessages.length > 0) {
            conversation.last_message_id = res > 0 ? res : null;
          }
          return conversation;
        });
      } else {
        // Nếu không phải chat group -> update status của last_message
        vm.listConversation = vm.listConversation.map(function (conversation) {
          if (conversation.id == vm.curConv.id && vm.listMessages.length > 0) {
            if (!conversation.last_message) {
              conversation.last_message = {};
            }
            conversation.last_message.status = 0;
          }
          return conversation;
        });
      }

      if (typeof notiCount != 'undefined' && !this.markUnreadCons.includes(vm.curConv.id)) {
        notiCount.countUnreadMess += 1;
        this.markUnreadCons.push(vm.curConv.id);
      }
    },
    searchMember: async function () {
      if (this.endOfSearchmember || !this.curConv || !this.curConv.group_id) {
        return;
      }
      this.searchMembersPage++;
      var response = await $.post(window.location.origin + '/api/chat/search-member', {
        page: this.searchMembersPage,
        group_id: this.curConv.group_id,
        textSearch: this.searchMemberKey
      });
      if (response.data.items.length < 20) {
        this.endOfSearchmember = true;
      }
      this.searchMembers = [...this.searchMembers, ...response.data.items];
    },
    searchAdmin: async function () {
      var response = await $.post(window.location.origin + '/api/chat/search-admin');
      this.admins = response;
    },
    searchMemberChat: async function () {
      if (this.endOfSearchmemberChat) {
        return;
      }
      this.searchMembersChatPage++;
      var response = await $.post(window.location.origin + '/api/chat/search-member-chat', {
        page: this.searchMembersChatPage,
        textSearch: this.searchMemberChatKey
      });
      if (response.data.length < 20) {
        this.endOfSearchmemberChat = true;
      }
      this.searchMembersChat = [...this.searchMembersChat, ...response.data];
    },
    openAddPeopleModal: async function () {
      await this.searchMember();
      $('#add_people').modal('toggle');
    },
    showAdmin: async function () {
      if (this.admins.length === 0) {
        await this.searchAdmin();
      }
      $('#admin_select').modal('toggle');
    },
    async login(id) {
      await $.post(window.location.origin + '/api/chat/login', { id });
      location.reload();
    },
    toggleJoinable: async function () {
      var response = await $.post(window.location.origin + '/api/chat/toggle-joinable/' + this.curConv.id);
      this.curConv.is_joinable = response.is_joinable;
    },
    openAddChatModal: async function () {
      await this.searchMemberChat();
      $('#add_chat').modal('toggle');

    },
    addMember: function (member) {
      $.post(window.location.origin + '/api/chat/add-member-to-group-chat', {
        cId: this.curConv.id,
        userId: member.user_id
      });
      this.people.push({
        id: member.user_id,
        avatar: member.avatar,
        name: member.name,
        is_tester: member.is_tester
      });
    },
    removeMember: function (member) {
      $.post(window.location.origin + '/api/chat/remove-member-to-group-chat', {
        cId: this.curConv.id,
        userId: member.id
      });
      this.people = this.people.filter((p) => p.id != member.id);
    },
    openSearchMsgModal: function () {
      $('#search_msg').modal('toggle');
    },
    clearSearchConversation: function () {
      this.searchKey = '';
      if (this.oldListCon.length > 0) {
        this.listConversation = this.oldListCon;
        this.endOfConversation = (this.listConversation.length - 1) % 10 != 0;
        this.oldListCon = [];
      }
    },
    goToChatUser: async function (data) {
      $('#show_people').modal('hide');
      $('#add_people').modal('hide');
      $('#add_chat').modal('hide');

      if (data.user_id) {
        data.sender_id = data.user_id;
        data.user_avatar = data.avatar;
        data.user_name = data.name;
      } else if (!data.sender_id) {
        data.sender_id = data.id;
        data.user_avatar = data.avatar;
        data.user_name = data.name;
      }

      var conversation = await $.post(window.location.origin + '/api/chat/init-chat-personal', {
        userId: data.sender_id
      });
      this.oldConv = this.curConv;
      this.selectConversation({
        id: conversation.id,
        conversation_id: conversation.id,
        fr_avatar: data.user_avatar,
        fr_name: data.user_name,
        fr_id: data.sender_id,
        avatar: conversation.user_avatar,
        user_avatar: conversation.user_avatar,
        user_name: conversation.user_name,
        user_id: conversation.user_id,
        last_message: {},
        last_message_id: null,
        friend_id: conversation.friend_id,
        is_group: 0
      });
    },
    openSwitchGroupModal: async function (member) {
      this.currentMemberSwitch = member;
      if (!this.curConv.group_id) {
        return;
      }
      var conversationGroups = await $.post(window.location.origin + '/api/chat/get-conversation-list-in-group', {
        groupId: this.curConv.group_id
      });
      this.switchConvId = this.curConv.id;
      this.conversationGroups = conversationGroups;
      $('#switch_group').modal('toggle');
    },
    switchConvGroup: async function () {
      if (this.switchConvId == this.curConv) {
        return;
      }
      if (!this.switchConvId || !this.currentMemberSwitch) {
        return;
      }
      var res = await $.post(window.location.origin + '/api/chat/switch-conversation-group', {
        userId: this.currentMemberSwitch.id,
        switchConvId: this.switchConvId,
        currentConvId: this.curConv.id
      });
      if (res.code == 200) {
        this.people = this.people.filter((p) => p.id != this.currentMemberSwitch.id);
        $('#switch_group').modal('hide');
      }
    }
  },

  mounted: function () {
    var vm = this;

    vm.userId = chatUser;

    if (typeof at != 'undefined') {
      $.ajaxSetup({ headers: { token: at } });
    }

    // Connect socket
    if (socket) {
      vm.socket = socket;
    } else if (socketServer) {
      vm.socket = io.connect(socketServer);
    }

    vm.checkUnreadMessage();

    // Khởi tạo data cho chatbox
    // vm.initChat();
  }
});

// Gửi ảnh tin nhắn
function sendImgMess() {
  var imgMess = new FormData($('#send_image_form')[0]);
  $.ajax({
    url: window.location.origin + '/api/chat/upload-img',
    type: 'post',
    processData: false,
    contentType: false,
    data: imgMess,
    success: function (response) {
      closePreviewImg();
      chatbox.sendImage(response);
      preventMultiImage = false;
    },
    error: function (error) {
      if (error.status == 500) {
        alert('Lỗi khi upload ảnh');
      } else if (error.responseJSON.error == 'imageType') {
        alert('Định dạng ảnh không hợp lệ');
      } else {
        alert('Lỗi dung lượng ảnh vượt quá giới hạn cho phép 8 MB');
      }
      preventMultiImage = false;
    }
  });
}

function sendFileMess() {
  var fileMess = new FormData($('#send_file_form')[0]);
  $.ajax({
    url: window.location.origin + '/api/chat/upload-file',
    type: 'post',
    processData: false,
    contentType: false,
    data: fileMess,
    success: function (response) {
      chatbox.sendFile(response);
    },
    error: function (error) {
      if (error.status == 500 || error.status == 422) {
        alert('Lỗi khi upload file');
      } else if (error.status == 413) {
        alert('Lỗi dung lượng file vượt quá giới hạn cho phép 8 MB');
      } else if (error.responseJSON.error == 'fileType') {
        alert('Định dạng file không hợp lệ');
      } else {
        alert('Lỗi dung lượng file vượt quá giới hạn cho phép 8 MB');
      }
    }
  });
}

$(document).keydown(function (e) {
  // ESCAPE key pressed
  if (e.keyCode == 27 && document.activeElement == document.getElementById('mess-input')) {
    chatbox.hideChatbox();
  }
});

$(document).ready(function () {
  // Reach top of messages box => load more message
  $('#con-container').on('scroll', function () {
    if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight - 1) {
      if (chatbox.listConversation.length > 0) {
        chatbox.loadMoreConversation();
      }
    }
  });

  $('#search_msg_modal_body').on('scroll', function () {
    if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
      if (chatbox.listSearchMsg.length > 0) {
        chatbox.searchMsg();
      }
    }
  });

  $('#search_member_modal_body').on('scroll', function () {
    if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
      if (chatbox.searchMembers.length > 0) {
        chatbox.searchMember();
      }
    }
  });

  $('#search_member_chat_modal_body').on('scroll', function () {
    if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
      if (chatbox.searchMembersChat.length > 0) {
        chatbox.searchMemberChat();
      }
    }
  });

  $('#mess-input').focusout(function () {
    var element = $(this);
    if (!element.text().replace(' ', '').length) {
      element.empty();
    }
  });

  // Drag and drop file
  $("html").on("dragover", function(e) {
    e.preventDefault();
    e.stopPropagation();
  });
  $("html").on("drop", function (e) {
    e.preventDefault();
    e.stopPropagation();
  });
  $('.chat-box').on('dragenter', function (e) {
    e.stopPropagation();
    e.preventDefault();
  });
  // Drag over
  $('.chat-box').on('dragover', function (e) {
    e.stopPropagation();
    e.preventDefault();
  });
  $('.chat-box').on('drop', function (e) {
    e.stopPropagation();
    e.preventDefault();
    var files = e.originalEvent.dataTransfer.files;
    var imageInput = document.getElementById("input_image_mess");
    var fileInput = document.getElementById("input_file_mess");
    if (files.length > 0) {
      if (files[0].type == "image/png") {
        if (preventMultiImage) {
          return;
        }
        preventMultiImage = true;
        imageInput.files = files;
        sendImgMess();
      } else {
        fileInput.files = files;
        sendFileMess();
      }
    }
  });

  var urlParams = (new URL(document.location)).searchParams;
  if (urlParams && urlParams.get('chat')) {
    if (navigator.userAgentData.mobile) {
      // Mobile
      window.location.href = 'dungmori://app/conversation/' + urlParams.get('chat');
    } else {
      // Desktop
      showChatbox();
      chatbox.selectConversationById(urlParams.get('chat'));
    }
  }
});
