var exam = new Vue({

    el: '.main-exam-right',
    data: function () {
        return {

            onlineUsers: 0, //so luong client ket noi den
            exams: enableExams, //lấy ra bài thi hiện tại

            joinBtnState: {}, //[0,1,2,3] 0 = -, 1 = sắp thi, 2 = đang thi, 3 = đã thi xong
            currentTimes: {}, //thời gian hiện tại lấy từ server thi
            showCurrentTimes: {}, //thời gian hiển thị
            showCurrentDate: "--/--",
            vnTime: "00:00:00",
            course: {}
        }
    },

    // hàm khởi tạo
    created: function() {

        this.countDownVNTime();
        this.refreshBtn();

    },

    methods: {

        joinRoom: function(course){

            var lang = 'vi';
            if(getCookie("_lang") == 'en') lang = 'en';

            var redirect = false;

            if(enableExams != null) {
                enableExams.forEach((exam, key) => {
                    if(exam.course == course) {

                        //nếu bài kiểm tra không có cài mã truy cập
                        if(exam.access_code == null){

                            me.exam_id = exam.id;
                            me.course = course;
                            me.lang = lang;
                            redirect = true;
                            return;

                        //nếu bài kiểm tra cài mã truy cập
                        }else{

                            var code = prompt("Bài thi yêu cầu mã truy cập", "");

                            //nếu nhập đúng
                            if (code != null && code == exam.access_code) {
                                me.exam_id = exam.id;
                                me.course = course;
                                me.lang = lang;
                                redirect = true;
                                return;
                            }else{
                                alert("Mã truy cập không đúng");
                            }
                        }
                    };
                })
            }

            if(redirect == true){
                var encrypted = CryptoJS.AES.encrypt(JSON.stringify(me), "DUNGMORIJLPT");
                location.href = jlptUrl +"?"+ encrypted;
            }


            // var decrypted = CryptoJS.AES.decrypt(getCookie("uSession"), "DUNGMORIJLPT");
            // console.log(JSON.parse(decrypted.toString(CryptoJS.enc.Utf8)));

        },

        //đồng hồ đếm thời gian việt nam
        countDownVNTime: function(){

            var vm = this;
            var timeNow;

            //lấy giờ hiện tại của server
            $.get(jlptUrl +"/get-current-time", function(response){

                // console.log("giờ server: ",response);
                timeNow = parseInt(response);
                vm.showCurrentDate = new Date(timeNow).toLocaleDateString('en-GB', { timeZone: 'Asia/Ho_Chi_Minh', day:"2-digit", month:"2-digit", year:"numeric"});

                //tạo đồng hồ đếm giờ với timestamp lấy từ server
                var x = setInterval(function() {

                    vm.vnTime = new Date(timeNow).toLocaleTimeString('en-GB', { timeZone: 'Asia/Ho_Chi_Minh' });
                    timeNow += 1000;

                }, 1000);
            });

        },

        //hàm refresh trạng thái nút vào phòng thi
        refreshBtn: function(){

            var vm = this;

            //lấy giờ hiện tại của server
            $.get(jlptUrl +"/get-current-time", function(response){

                console.log("giờ server: ", response);

                var initCurrentTime = parseInt(response);
                var nowTemp = new Date(initCurrentTime);
                var date = "0" + nowTemp.getDate();
                var month = "0" + (nowTemp.getMonth()+1); //0=January, 1=February etc.
                // vm.showCurrentDate =  date.substr(-2) +"/"+ month.substr(-2);

                //giờ thi của bài kiểm tra
                // var timeStartExam = new Date(enableExams.time_start).getTime();

                //do hàm trên bị lỗi trên safari & iphone nên khởi tạo từ biến tạo bằng php
                var timeStartExam = {};
                var timeEndExam = {};
                var index = 0;
                var joinBtnStateArray = {};
                var currentTimeArray = {};
                var showCurrentTimeArray = {};

                for(var key in timeStarts) {
                    timeStartExam[key] = timeStarts[key] * 1000;
                    timeEndExam[key]   = timeEnds[key] * 1000;
                    index++;
                    currentTimeArray[key] = initCurrentTime;
                    joinBtnStateArray[key] = 1;
                    showCurrentTimeArray[key] = "00:00:00";
                }

                //tạo đồng hồ đếm giờ với timestamp lấy từ server
                var x = setInterval(function() {

                    for(var key in timeStartExam) {
                        //nếu chưa đến giờ thi -> đếm giờ
                        if (currentTimeArray[key] < timeStartExam[key]) {

                            joinBtnStateArray[key] = 1; //đặt trạng thái là đang chờ vào phòng thi

                            var now = new Date(currentTimeArray[key]);  //console.log("now: ",now);
                            var hours = now.getHours();
                            var minutes = "0" + now.getMinutes();
                            var seconds = "0" + now.getSeconds();

                            showCurrentTimeArray[key] = hours + ':' + minutes.substr(-2) + ':' + seconds.substr(-2);

                        //nếu đang trong giờ thi
                        }else if(currentTimeArray[key] >= timeStartExam[key] && currentTimeArray[key] < timeEndExam[key]){

                            // clearInterval(x);
                            joinBtnStateArray[key] = 2; //đặt trạng thái đang thi

                        //nếu đã thi xong
                        }else{

                            // clearInterval(x); //xóa vòng lặp tính thời gian
                            joinBtnStateArray[key] = 3; //đặt trạng thái đã thi xong
                        }

                        currentTimeArray[key] += 1000;
                    }

                    vm.showCurrentTimes = showCurrentTimeArray;
                    vm.currentTimes = currentTimeArray;
                    vm.joinBtnState = joinBtnStateArray;

                }, 1000);

            });
        },

        totalOnline: function(key){
            var vm = this;
            if(key != 'N0') return vm.onlineUsers[key];
            var values = Object.values(vm.onlineUsers);
            var total = 0;
            values.forEach(element => {
                total +=  element;
            });
            return total;
        },

        isEnableExam: function(course) {
            var check = false;
            if(enableExams != null) {
                enableExams.forEach((value, key) => {
                    if(value.course == course) {  check = true; return;};
                })
            };
            return check;
        }

    },
    mounted: function() {

        var vm = this;


        $(".main-exam-right").css("opacity", 1);
        // console.log("baif thi", vm.exam);

        //kết nối websocket tính số người online
        var options = { rejectUnauthorized: false}; // allow self-signed certs
        var socket = io.connect(jlptSocket, {transports: ['websocket'], query: "type=N0"}, options);
        socket.on('count', function (response) {
            vm.onlineUsers = response;
        });

    }
});
