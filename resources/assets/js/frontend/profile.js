var user = new Vue({

	el: '#user-info',
	data: {
		url: window.location.origin,
		currentField  : "",
		changeName    : false,
		changePass    : false,
		changeBirthday : false,
		changePhone   : false,
		changeNihongo : false,
		changeAddress : false,
		changeCountry : false,
		japaneseLevel : "",
		country       : "",
        allowPress    : true,
        displaySendMailBtn: false,
        isReadyToSendMail: true,

		//thông tin người dùng
		user: {

			name     : "",
			password : "",
			birthday : "",
			phone    : "",
			nihongo  : "",
			address  : "",
			country  : "",
		},

		errors: [],

		birthday: {
			day: "",
			month: "",
			year: ""
		}
	},

	methods: {

		printInfo: function(value) {
			if(value == null || value =='') return '<span class="empty-info">Chưa có thông tin</span>';
			else return value;
		},

		printCountry: function(value){
			if (value == null || value =='') {
				return '<span class="empty-info">Chưa có thông tin</span>';
			}
			switch(value) {
				case '230': 	return "Việt Nam";
				case '107': 	return "Nhật Bản";
			}
			return "Khác"
		},

        // hiển thị nút gửi mail xác thực
        mouseOver: function() {
            var vm = this;
            vm.displaySendMailBtn = !vm.displaySendMailBtn;
        },

        // gửi mail xác thực email
        sendMailVerify: function() {
            var vm = this;

            vm.displaySendMailBtn = !vm.displaySendMailBtn;

            if (!vm.isReadyToSendMail) {
                return;
            }

            vm.isReadyToSendMail = false;

            $.ajax({
                url: window.location.origin+"/gui-mail-xac-thuc-email", type:"GET",
                error: function(){
                    vm.isReadyToSendMail = true;
                },
                success: function(response){
                    vm.isReadyToSendMail = true;
                    vm.displaySendMailBtn = false;
                    console.log(response);
                }
            });
        },

		//hiện trường cần chỉnh sửa
		showEditer: function(field) {

			var vm = this;
			vm.errors = [];
			vm.currentField = field;
			switch(field){
				case 'name':     vm.changeName 		= true; break;
				case 'password': vm.changePass 		= true; break;
				case 'birthday': vm.changeBirthday 	= true; break;
				case 'phone':    vm.changePhone 	= true; break;
				case 'nihongo':  vm.changeNihongo 	= true; break;
				case 'address':  vm.changeAddress 	= true; break;
				case 'country':  vm.changeCountry 	= true; break;
				default: break;
			}
		},

		//ẩn trường cần chỉnh sửa
		hideEditer: function(field) {

			var vm = this;
			vm.errors = [];
			vm.currentField = '';
			switch(field){
				case 'name':     vm.changeName 		= false; break;
				case 'password': vm.changePass 		= false; vm.errors = []; break;
				case 'birthday': vm.changeBirthday 	= false; break;
				case 'phone':    vm.changePhone 	= false; break;
				case 'nihongo':  vm.changeNihongo 	= false; break;
				case 'address':  vm.changeAddress 	= false; break;
				case 'country':  vm.changeCountry 	= false; break;
				default: break;
			}
		},

		//bắt sự kiện lưu thay đổi
		saveChange: function(field) {

			var vm = this;

            // kiểm tra tấn công bấm submit liên tục
            if (!vm.allowPress) {
                return;
            }
			vm.errors = [];
			vm.currentField = field;
			switch(field){
				case 'name':     vm.ajaxSaveChange('name',     'account-name'); 	break;
				case 'birthday': vm.ajaxSaveChange('birthday', ''); 				break;
				case 'phone':    vm.ajaxSaveChange('phone',    'account-phone'); 	break;
				case 'nihongo':  vm.ajaxSaveChange('nihongo',  'account-nihongo'); 	break;
				case 'address':  vm.ajaxSaveChange('address',  'account-address'); 	break;
				case 'country':  vm.ajaxSaveChange('country',  'account-country'); 	break;
				case 'password': vm.ajaxChangePassWord(); 							break;
				default: break;
			}
		},

		//hàm thay đổi, lấy tham số là tên trường và id input của trường cần đổi
		ajaxSaveChange: function(field, idField){

			var vm = this;
			var token = $('#csrf_token').val();

			var newValue = '';
            // lấy data
			if (field === 'nihongo') {
				newValue = vm.japaneseLevel;
			} else if (field === 'country') {
				newValue = vm.country;
			} else if (field === 'birthday') {
				var day = parseInt(vm.birthday.day);
				vm.birthday.day = (day < 10)?('0' + day):day;
				var month = parseInt(vm.birthday.month);
				vm.birthday.month = (month < 10)?('0' + month):month;
				vm.user.birthday = vm.birthday.day + "-" + vm.birthday.month + "-" + vm.birthday.year;
				newValue = vm.birthday.year + "-" + vm.birthday.month + "-" + vm.birthday.day;
                var temp = new Date(newValue);
                // xác thực ngày tháng năm
                if (temp.getFullYear() != parseInt(vm.birthday.year) && temp.getMonth() != parseInt(month) && temp.getDate() != parseInt(day)) {
                    vm.errors = [];
                    vm.errors.push("Không thành công! Vui lòng kiểm tra lại");
                    return;
                }
			} else {
				newValue = $("#"+idField).val();
			}

			if (newValue == undefined || newValue == null || newValue == "") {

                // xác thực thông tin trống
                $(".error-list").css("color", "red");
				vm.errors = [];
				vm.errors.push("Thông tin còn trống");
				return;
			}
			if (checkspecialSymbol(newValue)) {

                // xác thực ký tự đặc biệt
                $(".error-list").css("color", "red");
				vm.errors = [];
				vm.errors.push("Thông tin không được chứa ký tự đặc biệt");
				return;
			}

	        var data = {
	            '_token' : token,
	            'field'  : field,
	            'value'  : newValue
	        };
	        //console.log(data);
            vm.allowPress = false;
	        $.ajax({
	            url: window.location.origin+"/account/change-info", type:"POST", data: data, async: true,
	            beforeSend: function (xhr) { if (token) return xhr.setRequestHeader('X-CSRF-TOKEN', token);},
	            error: function(){
                    $(".error-list").css("color", "red");
	            	vm.errors = [];
					vm.errors.push("Không thành công. Vui lòng kiểm tra lại (Lưu ý: Thông tin không được để trống và không được chứa ký tự đặc biệt)");
                    vm.allowPress = true;
	            	return false;
	            },
	            success: function(response){

	               // console.log ("Thành công : "+response);
	                if(response == "success"){

	                	$(".error-list").css("color", "#588d3f");
						vm.errors = [];
						vm.errors.push("Bạn đã đổi thông tin thành công");
						switch(field){
	                		//cập nhật lại giá trị các biến vue để cập nhật giao diện
							case 'name':     vm.user.name = newValue;    break;
							case 'phone':    vm.user.phone = newValue;   break;
							case 'nihongo':  vm.user.nihongo = newValue; break;
							case 'address':  vm.user.address = newValue; break;
							case 'country':  vm.user.country = newValue; break;
							default: break;
						}
						setTimeout(function() {
                            // ẩn form
							switch(field){
								case 'name':        vm.changeName 		= false; break;
								case 'birthday':    vm.changeBirthday 	= false; break;
								case 'phone':       vm.changePhone 		= false; break;
								case 'nihongo':     vm.changeNihongo 	= false; break;
								case 'address':     vm.changeAddress 	= false; break;
								case 'country':     vm.changeCountry  	= false; break;
								default: break;
							}
                            vm.allowPress = true;
						}, 2000);
	                }
	            }
	        });
		},

		//thay đổi mật khẩu người dùng
		ajaxChangePassWord: function(argument) {
			var vm = this;

			var token    		= $('#csrf_token').val();
			var oldPassword 	= $('#old-password').val();
			var newPassword 	= $('#new-password').val();
			var retypePassword 	= $('#retype-password').val();

			if (newPassword != retypePassword) {
                $(".error-list").css("color", "red");
				vm.errors = [];
				vm.errors.push("Xác nhận mật khẩu không chính xác");
				return;
			}

			var data = {
	            '_token' : token,
	            'old-password'  : oldPassword,
	            'new-password'  : newPassword,
	            'captcha'		: $('#captcha-input').val(),
	        };
	        // console.log(data);
            vm.allowPress = false;
	        $.ajax({
	            url: window.location.origin+"/account/change-password", type:"POST", data: data, async: true,
	            beforeSend: function (xhr) { if (token) return xhr.setRequestHeader('X-CSRF-TOKEN', token); },
	            error: function() {
                    $(".error-list").css("color", "red");
	            	vm.errors = [];
					vm.errors.push("Thông tin không chính xác. Vui lòng kiểm tra lại thông tin hoặc thay đổi mã xác nhận");
                    vm.allowPress = true;
                    vm.changeCaptcha();
	            },
	            success: function(response) {
	            	// console.log("Thành công : "+response);
	            	if (response.status === 'error') {
                        $(".error-list").css("color", "red");
	            		vm.errors = [];
						vm.errors.push(response.msg);
                        vm.allowPress = true;
                        vm.changeCaptcha();
	            	} else if (response.status === "success") {
                        $(".error-list").css("color", "#588d3f");
                        vm.errors = [];
                        vm.errors.push(response.msg);
                        setTimeout(function() {
                            vm.changePass = false;
                            vm.allowPress = true;
                        }, 2000);
	                }
	            }
	        });
		},

		// update Japanese lavel
		updateJapaneseLevel: function(newValue) {
			this.japaneseLevel = newValue;
		},

		// update country
		updateCountry: function(newValue) {
			this.country = newValue;
		},

		changeCaptcha: function() {
			const newImage = `${this.url}/account/refresh-captcha?${Date.now()}`
			$("#captcha-change").attr("src", newImage);
		}
	},

	mounted: function() {

		//looad hết dữ liệu
		var vm = this;
		vm.user.name     = user_name;
		vm.user.birthday = user_birthday;
		vm.user.phone   = user_phone;
		vm.user.nihongo = user_nihongo;
		vm.user.address = user_address;
		vm.user.country = user_country;

		if (vm.user.birthday != null && vm.user.birthday != "") {
			var date = new Date(vm.user.birthday);
			var day = date.getDate();
			vm.birthday.day = (day < 10)?('0' + day):day;
			var month = date.getMonth() + 1;
			vm.birthday.month = (month < 10)?('0' + month):month;
			vm.birthday.year = date.getFullYear();

			vm.user.birthday = vm.birthday.day + "/" + vm.birthday.month + "/" + vm.birthday.year;
		}

		//sau khi load dữ liệu sẽ hiển thị
		$("#user-info-table").css('display', 'table');
	}
});
