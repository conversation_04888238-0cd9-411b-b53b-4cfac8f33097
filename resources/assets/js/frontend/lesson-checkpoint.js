Vue.filter('time', function (value) {
    return moment(value).format('HH:mm');
});

Vue.filter('two_digits', function (value) {
    if (value.toString().length <= 1) {
        return '0'+ value.toString();
    }
    return value.toString();
});


var mainLuyenDe = new Vue({

    el: '#lesson-checkpoint',

    data: function () {
        return {

            url: window.location.origin,

            mp3: null,
            questions: [],
            numOfQuestions: 0,
            solutions: [],
            userAnswers: {},

            result: null,
            showPopup: false,
            showResult: false,

            name: "",
            email: "",
            phone: "",
            age: "",
            career: "",
            place: "",
            purpose: "",

            errors: {},

            timer: 0,

            lessonDetail: null,

            checkHastime: false,

            showAnswers: false,
        }
    },
    created() {
        deleteCookie("time_checkpoint_" + lessonDetail.id);
    },

    watch: {
        userAnswers: {
            handler(newValue) {
                var vm = this;
                setCookie('SAVE_ANSWER_' + lessonDetail.id, JSON.stringify(newValue), 5); //lưu đáp án vào cookie trong 5p
            },
            deep: true
        },
        timerValue: function (val) {
            if (val == 0) {
                this.submitDataExercise();
            }
        },
    },

    computed:   {
        currentDate: function() {
            return new Date();
        },
        timerValue: function() {
            return this.timer > 0 ? this.timer : 0;
        },
        timeText: function() {
            return {
                hour: moment.duration(this.timerValue).hours(),
                minute: moment.duration(this.timerValue).minutes(),
                second: moment.duration(this.timerValue).seconds(),
            }
        },
        showResults: function () {

            var vm = this;

            var tmp = vm.questions.map((question) => {
                if (question.answers.length > 0) {
                    var answerQuestion = vm.userAnswers[question.id];
                    question.answers = question.answers.map((answer) => {
                        if (answerQuestion && answerQuestion == answer.id) {
                            answer.user_chose = 1
                            if (answer.grade > 0) {
                                answer.is_true = 1
                            } else {
                                answer.is_true = 0
                            }
                        }
                        return answer;
                    })
                }
                return question;
            })
            return tmp;
        }
    },

    methods: {

        //khởi tạo dữ liệu
        initData: function () {

            var vm = this;

            for (var i = 0; i < lesson_tasks.length; i++) {
                if (lesson_tasks[i].type == 1 || lesson_tasks[i].type == 3) {
                    vm.questions.push(lesson_tasks[i]);
                }
                if (lesson_tasks[i].type == 5) vm.mp3 = JSON.parse(lesson_tasks[i].value).link; //lấy ra mp3
                if (lesson_tasks[i].type == 3) vm.numOfQuestions += 1; //đếm số lượng câu hỏi
            }

            if (vm.lessonDetail.max_duration > 0) {
                vm.checkHastime = true;
                var time = getCookie('time_checkpoint_' + vm.lessonDetail.id);

                if (!time) {
                    var timestamp = vm.currentDate.getTime();

                    var endTime = timestamp + this.lessonDetail.max_duration * 60000;

                    setCookie("time_checkpoint_" + this.lessonDetail.id, endTime, 1);

                    vm.timer = vm.lessonDetail.max_duration * 60000;

                } else {
                    this.timer = time - this.currentDate.getTime();
                }

                setInterval(this.updateTimer, 1000);
            } else {
                vm.checkHastime = false;
            }
        },

        //ấn nút nộp bài kiểm tra -> chấm điểm luôn
        submitDataExercise: function () {

            var vm = this;

            //nếu chưa chọn câu nào
            if (_.isEmpty(vm.userAnswers)) {
                alert("Bạn chưa chọn đáp án");
                return;
            }

            vm.totalScore = 0;

            _.forEach(vm.userAnswers, function (value, key) {
                var checkQuest = _.find(vm.questions, {id: parseInt(key)});

                var checkAns = _.find(checkQuest.answers, {id: parseInt(value)});

                //nếu phương án lựa chọn dúng -> tính điểm
                if (parseInt(checkAns.grade) > 0) {
                    vm.totalScore += parseInt(checkAns.grade);
                }

            });

            vm.result = {grade: vm.totalScore, total_grade: lessonDetail.total_marks};
            vm.showPopup = true;

        },

        //lưu thông tin bài test đầu vào
        saveUserInfo: function () {

            var vm = this;

            var timestamp = vm.currentDate.getTime();

            axios.post(window.location.origin + "/checkpoint/save", {
                user_info: JSON.stringify({
                    'name': vm.name,
                    'email': vm.email,
                    'phone': vm.phone,
                    'age': vm.age,
                    'career': vm.career,
                    'place': vm.place,
                    'purpose': vm.purpose,
                }),
                lessonId: parseInt(lessonDetail.id),
                answers: JSON.stringify(vm.userAnswers),
                grade: vm.totalScore,
                total_grade: lessonDetail.total_marks,
            })
                .then(response => {
                    if (response.status == 200) {
                        vm.$message.success("Gửi kết quả thành công!");
                        setCookie('time_checkpoint_' + this.lessonDetail.id, timestamp, 1);
                        vm.timer = 0;
                        vm.showPopup = false
                        vm.showAnswers = true;
                    }
                })
                .catch(error => {
                    vm.$message.error("Có lỗi xảy ra. Vui lòng thử lại!");
                    this.errors = error.response.data;
                });
        },

        //đóng cửa sổ popup
        closePopupResult: function () {

            var vm = this;

            vm.showPopup = false;
            vm.showResult = false;
        },

        reTest: function () {
            deleteCookie('time_checkpoint_' + lessonDetail.id);
            window.location.reload();
        },

        //xoá message lỗi trên popup khi người dùng keydown
        clearField: function (field){
            delete this.errors[field];
        },

        updateTimer: function() {
            if (this.lessonDetail.max_duration != 0){
                this.timer = this.timer - 1000;
            }
        },

        closePopup: function () {
            var vm = this;
            vm.showAnswers = false;
            vm.userAnswers = {};
        },
    },

    mounted: function () {
        var vm = this;
        vm.lessonDetail = lessonDetail;
        vm.initData();
    }
});
