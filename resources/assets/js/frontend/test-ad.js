Vue.filter('time', function (value) {
  return moment(value).format('HH:mm');
});
Vue.filter('datetime', function (value) {
  return moment(value).format('DD/MM/YYYY HH:mm');
});
Vue.filter('point', function (value) {
  return value ? value.toFixed(1) : 0;
});
Vue.filter('passed', function (value) {
  return value ? 'Đạt' : 'Không đạt';
});
new Vue({
  el: '#test-ad-home',
  data: {
    tab: '',
    tabs: [
      {
        value: 'test',
        label: 'Làm bài'
      },
      {
        label: 'Xếp hạng'
      },
      {
        value: 'history',
        label: '<PERSON>ịch sử'
      },
    ],
  },
  mounted: function() {
    this.tab = 'test';
  },
});
Vue.filter('two_digits', function (value) {
  if (value.toString().length <= 1) {
    return '0'+ value.toString();
  }
  return value.toString();
});
Vue.filter('slash_date', function (value) {
  return moment(value, 'YYYY-MM-DD').format('DD/MM/YYYY');
});
Vue.component('test-ad-list', {
  template: '#test-ad-list',
  data: function () {
    return {
      url: window.location.origin,
      api: api,
      userId: userId,
      groups: [],
      time: moment().format('YYYY-MM-DD'),
      lessonTypes: {
        N1: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
        N2: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
        N3: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
        N4: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
        N5: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
      },
    }
  },
  computed: {
    today: function() {
      return moment().format('YYYY-MM-DD');
    },
    tomorrow: function() {
      return moment().add('days', 1).format('YYYY-MM-DD');
    },
    lessonTitles: function () {
      return this.lessonTypes[this.examDetail.course];
    },
  },
  watch: {
    time: function(value) {
      this.getSchedules();
    },
  },
  methods: {
    getLessonTitle: function(exam, lesson) {
      var titles = this.lessonTypes[exam.course];
      return titles[lesson.type - 1];
    },
    getSchedules: function() {
      var vm = this;
      var data = {
        userId: this.userId,
        time: this.time,
      };
      $.get(this.url + "/dong-ho-bao-thuc-mua-thi/get-schedule", data, function(res) {
        vm.groups = res.data;
      });
    },
    goTest: function (exam, lesson) {
      if (!exam.available) return;
      if (lesson) window.location.href = exam.url + '?lesson=' + lesson.type;
      else window.location.href = exam.url;
    },
  },
  mounted: function () {
    this.getSchedules();
  },
});
Vue.component('test-ad-rank',{
  template: '#test-ad-rank',
  data: function() {
    return {
      url: window.location.origin,
      api: api,
      tab: 'day',
      date: moment().format('YYYY-MM-DD'),
      exams: [],
      exam_id: null,
      level: 'N1',
      from: '',
      to: '',
      options: {
        firstDayOfWeek: 1,
      },
      userId: userId,
      groups: [],
      ladder: [],
    };
  },
  computed: {
    userResult: function() {
      return _.find(this.ladder, ['user_id', this.userId]);
    },
    userResultIndex: function() {
      return _.findIndex(this.ladder, ['user_id', this.userId]);
    }
  },
  watch: {
    date: function (val) {
      this.getExams();
    },
    exam_id: function (value) {
      if (value) {
        this.getRanking();
      } else {
        this.ladder = [];
      }
    },
    tab: function (value) {
      if (value) {
        this.getRanking();
      }
    },
  },
  mounted: function () {
    this.getExams();
  },
  methods: {
    rankStyle: function(index) {
      var color = '';
      var fontSize = '25px;'
      switch (true) {
        case (index == 0):
          color = '#FFC700';
          break;
        case (index == 1):
          color = '#FF9900';
          break;
        case (index == 2):
          color = '#EA5400';
          break;
        case (index > 2):
          color = '#96D962';
          fontSize = '18px';
          break;
        default:
          color = '#000';
      }
      return 'color: ' + color + '; border-color: ' + color + '; font-size: ' + fontSize;
    },
    rankBg: function(index) {
      var color = '';
      switch (true) {
        case (index == 0):
          color = '#FFF9C0';
          break;
        case (index == 1):
          color = '#F9E2A1';
          break;
        case (index == 2):
          color = '#FFC896';
          break;
        case (index > 2 && index < 10):
          color = '#EDFFDF';
          break;
        default:
          color = '#FFFFFF';
      }
      var opacity = 1 - (index / 10);
      var _opacity = Math.round(Math.min(Math.max(opacity || 1, 0), 1) * 255);
      return 'background: ' + color + _opacity.toString(16).toUpperCase() + '; box-shadow:  5px 5px 6px #e3e3e3, -5px -5px 6px #ffffff;';
    },
    getExams: function () {
      var vm = this;
      var data = {
        userId: this.userId,
        date: this.date,
      };
      $.get(this.url + "/dong-ho-bao-thuc-mua-thi/get-exams", data, function(res) {
        vm.exams = res.data
        if (vm.exams.length > 0) {
          var levels = _.sortBy(res.levels)
          if (levels.length) {
            var matchExam = _.find(vm.exams, ['course', levels[0]])
            if (matchExam) {
              vm.exam_id = matchExam.id
            } else {
              vm.exam_id = vm.exams[0].id;
            }
          } else {
            vm.exam_id = vm.exams[0].id;
          }
        }
      });
    },
    getRanking: function() {
      var vm = this;
      if (this.tab == 'day') {
        var data = {
          type: this.tab,
          group_id: this.group_id,
          exam_id: this.exam_id,
          date: this.date,
        }
      } else {
        var data = {
          type: this.tab,
          level: this.level,
          from: this.from,
          to: this.to,
        }
      }
      $.get(this.url + "/dong-ho-bao-thuc-mua-thi/get-ranking", data, function(res) {
        if (vm.tab == 'day') {
          vm.ladder = res.data;
        } else {
          vm.ladder = res.data.map(function (item) {
            item.average = (item.total_point / res.exam_count);
            return item;
          });
          vm.ladder = _.orderBy(vm.ladder, 'average', 'desc');
        }
      });
    },
  },
});
Vue.component('test-ad-history', {
  template: '#test-ad-history',
  data: function () {
    return {
      url: window.location.origin,
      tab: 'test',
      history: [],
      date: moment().format('YYYY-MM-DD'),
      showResult: false,
      currentResult: {},
      lessonTypes: {
        N1: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
        N2: ['Từ vựng - Chữ Hán - Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'],
        N3: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
        N4: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
        N5: ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'],
      },
      options: {
        firstDayOfWeek: 1,
      },
    };
  },
  computed: {
    lessonTitles: function () {
      return this.lessonTypes[this.currentResult.course];
    },
    answers: function () {
      var answer1 = JSON.parse(this.currentResult.result[0].data_1);
      var answer2 = JSON.parse(this.currentResult.result[0].data_2);
      var answer3 = JSON.parse(this.currentResult.result[0].data_3);
      return _.merge(answer1, answer2, answer3);
    },
    mp3: function () {
      var exam = _.find(this.currentResult.lessons, ['type', '3']);
      return exam ? exam.mp3 : null;
    },
  },
  watch: {
    date: function (val) {
      this.getHistory();
    },
  },
  mounted: function () {
    this.getHistory();
  },
  methods: {
    validateResult(exam) {
      if (exam.result.length === 0) return false
      if (!exam.time_split) return true
      if (!exam.available) return true
      if (exam.lessons.length === 1 && exam.result[0].data_1) return true
      if (exam.lessons.length === 2 && exam.result[0].data_1 && exam.result[0].data_2) return true
      if (exam.lessons.length === 3 && exam.result[0].data_1 && exam.result[0].data_2 && exam.result[0].data_3) return true
    },
    init: function () {
      this.getHistory();
    },
    getHistory: function () {
      var vm = this;
      var data = {
        date: this.date,
      }
      $.get(vm.url + "/dong-ho-bao-thuc-mua-thi/get-history", data, function(res) {
        vm.history = res.data;
      });
    },
    isPassed: function(exam) {
      if (exam.result.length > 0) {
        return exam.result[0].is_passed;
      }
      return false;
    },
    answerLength: function (content) {
      if (!content) return 0;
      var text = />(.*?)</g.exec(content)
      return text ? text[1].length : content.length;
    },
    show: function (result) {
      this.currentResult = result;
      this.showResult = true;
    },
    closeModal: function () {
      this.showResult = false;
      this.currentResult = {};
    },
  },
});
