$.ajaxSetup({headers: {'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') } });

//đi<PERSON><PERSON> h<PERSON> từ www sang non www
if (window.location.hostname.indexOf("www") == 0) {
    window.location = window.location.href.replace("www.","");
}

//redirect trên trang web thật
if(window.location.href.indexOf("dungmori.com") !== -1){
    if(window.location.protocol != 'https:') {
        location.href = 'https:' + window.location.href.substring(window.location.protocol.length);
    }
}

// {{-- hàm setcookie  --}}
function setCookie(name, value, days) {
    var expires;
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        expires = "; expires=" + date.toGMTString();
    }
    else {
        expires = "";
    }
    document.cookie = name + "=" + value + expires + "; path=/";
}

// {{-- hàm lấy cookie --}}
function getCookie(c_name) {
    if (document.cookie.length > 0) {
        c_start = document.cookie.indexOf(c_name + "=");
        if (c_start != -1) {
            c_start = c_start + c_name.length + 1;
            c_end = document.cookie.indexOf(";", c_start);
            if (c_end == -1) {
                c_end = document.cookie.length;
            }
            return unescape(document.cookie.substring(c_start, c_end));
        }
    }
    return "";
}

// {{-- hàm lấy cookie --}}
function deleteCookie(c_name) {
    document.cookie = c_name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

//hiển thị search input
function showSearchInput() {
    $('#logo').css("display", "none");
    $('#account-container').css("display", "none");
    $('#search-input-mobile').css("display", "inline");
}

//đóng tìm kiếm mobile
function closeSearchBox() {
    $('#search-input-mobile').css("display", "none");
    $('#logo').css("display", "inline");
    $('#account-container').css("display", "block");
}

// bắt sự kiện enter cho input đăng nhập
function enterToLogin(event, t) {
    if ( event.keyCode == 13 && $("#email").val() != null && $("#email").val() != undefined && $("#email").val() != "" &&
            $("#password").val() != null && $("#password").val() != undefined && $("#password").val() != "" ) {
        $("#login-submit").click();
        return false;
    }
}

//hàm chuyển tab
function swichTab(tabname){
    $(".auth-content").addClass("hidden");
    $("#" + tabname + "-content").removeClass("hidden");
    $("#" + tabname + "-content").addClass("block");

    if(tabname == "login")  $(".auth-image").attr('src', window.location.origin+'/assets/img/new_home/06-2024/auth_co_thanh.png');
    else $(".auth-image").attr('src', window.location.origin+'/assets/img/new_home/06-2024/auth_thay_dung.png');
}

// bắt sự kiện enter cho ô search input
function search(event, t) {
    if (event.keyCode == 13) {
      var searchKey = t.value;
      searchKey = searchKey.replace("/", " ");
      searchKey = searchKey.replace("<", " ");
      searchKey = searchKey.replace(">", " ");
      searchKey = searchKey.replace("\'", " ");
      searchKey = searchKey.replace("\"", " ");
      searchKey = searchKey.replace("&", " ");
      searchKey = standardizeString(searchKey);
      if (searchKey == null || searchKey == "") { return; }
      window.location.href = window.location.origin+'/tim-kiem/'+ searchKey;
      return false;
    }
}

//hàm lưu vết người dùng nếu họ đăng nhập nhanh bằng tk mạng xã hội
function authBySocicalAccount(){

    console.log("Lưu vết kiểu đăng nhập mạng xã hội");

    // thư viện lấy ra thông tin agent
    var myclient = new ClientJS();

    setCookie("current_browser", myclient.getBrowser(), 1);
    setCookie("current_os", myclient.getOS(), 1);
    setCookie("current_fingerprint", myclient.getFingerprint(), 1);
    setCookie("url_after_social_redirect", window.location.href, 1);
}

//hàm đăng xuất sử dụng ajax
function logout() {
    console.log("Đăng xuất");
    $.get(window.location.origin+'/account/logout', function(data, status){
        if(data == "success") {
          location.reload();
        }
    });
}

//hàm xóa cache redis
function refreshCache(){

    $("#refresh-cache").addClass("fa-spin");

    setTimeout(function() {
        $.get(window.location.origin+'/api/refresh-cache', function(response){
            if(response == "success") {
                $("#refresh-done").css('display', 'inline');
                $("#refresh-cache").css('display', 'none');

                toastr.success('Đã làm mới dữ liệu dungmori.com');

                // var wnd = window.open("https://s2.dungmori.com/api/refresh-cache");
                // setTimeout(function() {
                //   wnd.close();
                //   toastr.success('Đã làm mới dữ liệu s2.dungmori.com cụm thi 2');
                // }, 10);

            }
        });
    }, 500);
}

//hàm xóa cache redis trên mjt
function refreshMjtCache(){

    $("#refresh-mjt-cache").addClass("fa-spin");

    setTimeout(function() {
        $.get('https://mjt.dungmori.com/clear-cache', function(response){
            if(response == "success") {
                $("#refresh-mjt-done").css('display', 'inline');
                $("#refresh-mjt-cache").css('display', 'none');
                toastr.success('Đã làm mới dữ liệu mjt cho app');
            }
        });
        $.get('https://baoson.dungmori.com/clear-cache', function(response){
            if(response == "success") {
                $("#refresh-mjt-done").css('display', 'inline');
                $("#refresh-mjt-cache").css('display', 'none');
                toastr.success('Đã làm mới dữ liệu mjt cho app');
            }
        });
    }, 500);
}


// các hàm yêu cầu phải load xong dom mới call
$(document).ready(function(){

    //enable tooltip boostrap
    $(function () { $('[data-toggle="tooltip"]').tooltip() })

    //bật autosize
    autosize(document.querySelectorAll('textarea'));

    $(".go-top").click(function() {
        $("html, body").animate({ scrollTop: 0 }, "slow");
    });

    //bật menu nav
    $('#nav-icon').click(function() {
        $(this).toggleClass('open');
    });

    //set chiều rộng menu mobile
    $(".col-left-menu").css({
        'width': ($(".khoa-hoc-in-menu").width() + 'px')
    });

    $(".col-right-menu").css({
        'width': ((500 - $(".khoa-hoc-in-menu").width()) + 'px')
    });

});
