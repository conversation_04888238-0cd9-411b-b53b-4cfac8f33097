// thư viện lấy ra thông tin agent
var client = new ClientJS();

var browserName = client.getBrowser();
var osName = client.getOS();
var fingerprint = client.getFingerprint();
fingerprint = fingerprint.toString();

// console.log(browserName);
// console.log(osName);
// console.log("vân tay cuối cùng: "+ lastFingerprint);
// console.log("vân tay hiện tại: "+ fingerprint);

//đóng và reload trang sau khi người dùng bị kick
function clickClosePopup(){
	location.reload();
}
function delete_cookie(name) {
	document.cookie = name +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}
// lastIp được định nghĩa ở blade default
// nếu người dũng đã đăng nhập và check ko phải do admin
// bỏ qua kích người dùng nhúng iframe cd
if(lastFingerprint && fingerprint != lastFingerprint && embed == ""){

	console.log("You are kicked out");
	// delete_cookie('dungmori_session');
	// $.get(window.location.origin+'/account/logout', function(data, status){});
	//
	// $.fancybox.open('<div class="message" id="kick-popup">'
	// 	+'<h3 style="color:#e74c3c;"><i class="zmdi zmdi-alert-triangle"></i> Cảnh báo!</h3>'
	// 	+'<p>Bạn bị <b>đăng xuất</b> do hệ thống của chúng tôi phát hiện ra một phiên làm việc khác <br/> của tài khoản này đang chạy trên trình duyệt <b>'+lastBrowser+'</b>'
	// 	+' sử dụng hệ điều hành <b>'+lastOS+'</b></p>'
	// 	+'<p style="font-size: 12px;">* Lưu ý : Điều khoản sử dụng của website dungmori.com không cho phép <br/>người dùng đăng nhập đồng thời trên nhiều thiết bị</p>'
	// 	+'<p>Vui lòng <b>đăng nhập lại</b> để có thể tiếp tục sử dụng<br/>khi đó tất cả phiên làm việc trên các thiết bị khác sẽ bị đăng xuất</p>'
	// 	+'<div class="pull-right dmr-btn" onclick="clickClosePopup()" data-fancybox-close>Đồng ý</div></div>');

}else{
	//console.log("Bạn không bị kích");
}

