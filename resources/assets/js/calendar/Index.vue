<template>
  <div id="calendar_wrap" class="px-5">
    <div id="calendar_banner">
      <div class="h-[248px] w-full rounded-[32px] bg-[#F0FFF1] mt-[40px]">
      </div>
    </div>

    <div id="calendar_content">
      <div class="w-full mt-[40px]">
        <!--        header: title - (month-picker btn-today btn-search)-->
        <div class="flex justify-between items-center mb-8">
          <div class="text-[#07403F] text-[36px] font-beanbag-medium uppercase">Lịch livestream</div>
          <div class="flex items-center gap-4">
            <div class="date-picker font-averta-bold text-[#07403F] text-[22px] cursor-pointer select-none">
              <span @click="previousMonth" class="hover:bg-gray-100 px-2 py-1 rounded">‹</span>
              {{ currentMonthYear }}
              <span @click="nextMonth" class="hover:bg-gray-100 px-2 py-1 rounded">›</span>
            </div>
            <div @click="goToToday"
                 class="btn-today rounded-full px-10 py-3 text-[#07403F] text-[22px] font-beanbag-medium border-[2px] border-[#07403F] cursor-pointer hover:bg-[#F0FFF1] transition-colors">
              Hôm nay
            </div>
            <div
                class="flex items-center bg-[#A0F8A8] rounded-full px-10 py-3 text-[#07403F] text-[22px] font-beanbag-medium cursor-pointer hover:bg-[#8FE897] transition-colors">
              <i class="fas fa-search mr-2"></i>
              Lọc
            </div>
          </div>
        </div>

        <!--        calendar -->
        <div class="calendar-container bg-white rounded-[20px] shadow-lg overflow-hidden border border-gray-100 mb-10">
          <!-- Days of week header -->
          <div class="grid grid-cols-7 ">
            <div v-for="day in daysOfWeek" :key="day"
                 class="p-4 text-center text-[#07403F] font-beanbag-medium text-[18px] border-gray-100"
                 :class="{
                'border-r-[1px] border-r': day !== 'CN',
                // 'text-[#07403F]': day !== 'CN'
              }"
            >
              {{ day }}
            </div>
          </div>

          <!-- Calendar grid -->
          <div class="grid grid-cols-7">
            <div
                v-for="(day, index) in calendarDays"
                :key="index"
                @click="openDayPopover(day, $event)"
                class="calendar-day relative border-r border-b border-gray-100 h-[120px] p-2 cursor-pointer hover:bg-[#F8F9FA] transition-colors"
                :class="{
                'bg-gray-50 text-gray-400': !day.isCurrentMonth,
                'bg-[#E8F5E8]': day.isToday,
                'text-[#07403F]': day.isCurrentMonth
              }"
            >
              <!-- Day number -->
              <div v-if="day.isToday" class="text-[16px] font-beanbag-medium mb-1 text-center flex justify-center">
                <div class="text-white bg-[#07403F] rounded-full w-6 h-6 flex items-center justify-center text-[14px]">
                  {{ day.date }}
                </div>
              </div>
              <div v-else class="text-[16px] font-beanbag-medium mb-1 text-center">
                {{ day.date }}
              </div>
              <!--              <div class="text-[16px] font-beanbag-medium mb-1 text-center" :class="{ 'text-white bg-[#07403F] rounded-full w-6 h-6 flex items-center justify-center text-[14px]': day.isToday }">-->
              <!--                {{ day.date }}-->
              <!--              </div>-->

              <!-- Events -->
              <div class="space-y-1">
                <!-- if isSubscribed is true, background is gradient, else background is #F2FFF4             -->

                <div
                    v-for="(event, eventIndex) in day.visibleEvents"
                    :key="event.id"
                    @click.stop="openEventDetail(event)"
                    class="event-item text-[14px] px-2 py-1 rounded-md truncate cursor-pointer hover:opacity-80 transition-opacity font-beanbag-medium"
                    :style="{
                    background: event.isSubscribed ? 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)' : '#F2FFF4',
                    color: getTextColor(event.color) }"
                >
                  <div class="flex items-center gap-3">
                    <div
                        class="h-[16px] w-[16px] rounded-full flex items-center justify-center text-[8px] text-white pt-[2px] font-beanbag-bold uppercase"
                        :class="`bg-${event.level}`">
                      {{ event.level }}
                    </div>
                    <div class="font-beanbag-medium text-[#07403F]">
                      {{ event.title }}
                    </div>
                  </div>
                </div>

                <!-- More events indicator -->
                <div
                    v-if="day.hiddenEventsCount > 0"
                    class="text-[11px] text-gray-600 px-2 py-1 hover:bg-gray-200 rounded-md cursor-pointer transition-colors"
                    @click.stop="openDayPopover(day, $event)"
                >
                  {{ day.hiddenEventsCount }} mục khác
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Day Events Popover -->
    <div
        v-if="showDayPopover"
        ref="dayPopover"
        class="fixed bg-white rounded-[32px] shadow-xl border border-gray-200 z-50 min-w-[400px] max-w-[500px]"
        :style="{ top: popoverPosition.top + 'px', left: popoverPosition.left + 'px' }"
    >
      <div class="p-4 border-gray-200 flex justify-between items-center">
        <h3 class="text-[18px] font-beanbag-medium text-[#07403F]">
          {{ selectedDay ? formatDate(selectedDay.fullDate) : '' }}
        </h3>
        <div class="btn-close">
          <button @click="closeDayPopover" class="text-gray-400 hover:text-gray-600 text-[24px]">&times;</button>
        </div>
      </div>
      <div class="max-h-[300px] overflow-y-auto">
        <div v-if="selectedDay && selectedDay.events.length === 0" class="p-4 text-gray-500 text-center">
          Không có sự kiện nào
        </div>
        <div v-else class="p-2">
          <div
              v-for="event in selectedDay?.events || []"
              :key="event.id"
              @click="openEventDetail(event)"
              class="event-item-day-popover p-[4px] hover:bg-[#73FF79] rounded-lg cursor-pointer transition-colors mb-2 flex justify-between items-center"
          >
            <div class="flex items-center gap-3">
              <div
                  class="h-[16px] w-[16px] rounded-full flex items-center justify-center text-[8px] text-white pt-[1px] font-beanbag-bold uppercase"
                  :class="`bg-${event.level}`">
                {{ event.level }}
              </div>
              <div class="font-beanbag-medium text-[#07403F]">
                {{ event.title }}
              </div>
            </div>
            <svg class="icon-continue" width="16" height="10" viewBox="0 0 16 10" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
              <path
                  d="M15.5 5C15.5 5.3693 15.3376 5.69838 15.2013 5.9245C15.0512 6.17351 14.8525 6.42564 14.6401 6.66602C14.2136 7.14867 13.6609 7.66305 13.1297 8.1243C12.5943 8.58914 12.0613 9.01665 11.6637 9.32694C11.4664 9.48091 11.1178 9.74423 10.9975 9.8351L10.9942 9.83761C10.6236 10.1105 10.1019 10.0314 9.829 9.66078C9.55609 9.29022 9.63524 8.7686 10.0058 8.49566C10.1167 8.41186 10.447 8.16237 10.6384 8.01302C11.022 7.71363 11.5307 7.30536 12.037 6.86579C12.4358 6.51955 12.8215 6.16385 13.1403 5.83333L1.33332 5.83333C0.873081 5.83333 0.499985 5.46024 0.499985 5C0.499985 4.53976 0.873081 4.16667 1.33332 4.16667L13.1403 4.16667C12.8215 3.83614 12.4358 3.48045 12.037 3.13421C11.5307 2.69464 11.022 2.28637 10.6384 1.98698C10.447 1.83763 10.1167 1.58814 10.0058 1.50434C9.63524 1.2314 9.55609 0.709778 9.829 0.339216C10.1019 -0.0313656 10.6236 -0.110534 10.9942 0.162389L10.9973 0.164725C11.1176 0.255601 11.4664 0.519091 11.6637 0.673058C12.0613 0.983346 12.5943 1.41086 13.1297 1.8757C13.6609 2.33695 14.2136 2.85133 14.6401 3.33398C14.8525 3.57436 15.0512 3.82649 15.2013 4.0755C15.3376 4.30163 15.5 4.63069 15.5 5C15.5 5.00001 15.5 4.99999 15.5 5Z"
                  fill="#07403F"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Detail Modal -->
    <modal v-if="showEventModal" @close="closeEventModal" class="event-detail-modal">
      <template #body>
        <div v-if="selectedEvent" class="bg-[#F4F5FA] rounded-[24px]">
          <div class="info">
            <div class="flex items-center gap-3 header flex justify-end px-2 pt-2">
              <div
                  class="subscribe rounded-full bg-[#CCF8D1] flex items-center justify-center gap-2 px-[20px] py-[9.5px]">
                <div class="font-beanbag-medium text-[#07403F] text-[12px]">
                  Đăng ký nhận thông báo
                </div>
                <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M15.4876 7.35076V12.9092H15.6749C16.2214 12.9092 16.6663 13.3539 16.6663 13.9V14.0092C16.6663 14.5554 16.2214 15.0001 15.6749 15.0001H4.32442C3.77797 15.0001 3.33301 14.5554 3.33301 14.0092V13.9C3.33301 13.3539 3.77797 12.9092 4.32442 12.9092H4.51177V7.35076C4.51177 3.29075 7.10167 1.66675 9.83282 1.66675"
                      stroke="#07403F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path
                      d="M11.6663 17.5C11.6663 18.4218 10.9215 19.1667 9.99967 19.1667C9.07789 19.1667 8.33301 18.4218 8.33301 17.5"
                      stroke="#07403F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <img
                  @click="closeEventModal"
                  src="https://dungmori.b-cdn.net/assets/img/birthday/thoat-pop-up-chuc-mung-sinh-nhat-dungmori.png"
                  alt="" class="cursor-pointer w-[40px] h-[40px]" data-dismiss="modal"
                  aria-label="Close">
            </div>
            <div class="px-[28px] space-y-3 pb-[20px] border-b border-[#D0D3DA]">
              <div class="flex items-center gap-3 tag">
                <div v-for="i in 3"
                     class="rounded-full bg-[#CEFFD8] text-[#176867] text-[16px] px-[8px] py-[4px] font-averta-bold leading-[16px]">
                  #Tag
                </div>
              </div>

              <div class="grid grid-cols-[16px_auto] gap-1 items-center">
                <div class="w-2 h-2 rounded-full col-start-1" :class="`bg-${selectedEvent.level}`"></div>
                <h3 class="text-[22px] font-beanbag-bold text-[#07403F] leading-[22px] col-start-2">{{
                    selectedEvent.title
                  }}</h3>
                <div class="time-event text-[#07403F] font-beanbag-regular text-base col-start-2">
                  {{ selectedDay ? formatDate(selectedDay.fullDate) : 'Thứ 3, 12 tháng 8' }}
                </div>
                <div class="grid grid-cols-[110px_auto] gap-1 col-start-2 mt-2 text-base">
                  <div class="col-start-1 font-beanbag-regular">
                    Giờ Việt Nam:
                  </div>
                  <div class="col-start-2 font-beanbag-medium">
                    10:00 - 11:00
                  </div>
                  <div class="col-start-1 font-beanbag-regular">
                    Giờ Nhật Bản:
                  </div>
                  <div class="col-start-2 font-beanbag-medium">
                    10:00 - 11:00
                  </div>
                </div>
              </div>

              <div class="social-platform">
                <div class="flex items-center gap-3 text-[#07403F] font-beanbag-regular text-base">
                  <div class="w-1 h-1 rounded-full bg-[#07403F]"></div>
                  Trên các nền tảng
                </div>
                <div class="flex items-center gap-3 social-platform-list text-white">
                  <div class="flex items-center gap-2 rounded-full bg-[#F63232] p-2 min-w-[148px] cursor-pointer">
                    <svg width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                          d="M10.75 0C13.2235 0 15.5767 0.159257 17.7021 0.447266C19.9647 0.753882 21.4998 2.74208 21.5 4.94824L21.5 12.5518C21.4998 14.7579 19.9647 16.7461 17.7021 17.0527C15.5767 17.3407 13.2235 17.5 10.75 17.5C8.27647 17.5 5.92329 17.3407 3.79785 17.0527C1.53526 16.7461 0.000205193 14.7579 0 12.5518L0 4.94824C0.000205193 2.74208 1.53526 0.753882 3.79785 0.447266C5.92329 0.159257 8.27647 0 10.75 0ZM9.13574 5.10645C8.90415 4.96763 8.61586 4.96467 8.38086 5.09766C8.14573 5.23079 8 5.4798 8 5.75L8 11.75C8 12.0202 8.14573 12.2692 8.38086 12.4023C8.61586 12.5353 8.90415 12.5324 9.13574 12.3936L14.1357 9.39355C14.3616 9.25801 14.5 9.01345 14.5 8.75C14.5 8.48655 14.3616 8.24199 14.1357 8.10645L9.13574 5.10645Z"
                          fill="white"/>
                    </svg>
                    <div>
                      <div class="text-sm font-beanbag-regular leading-[14px]">Youtube</div>
                      <div class="text-base font-beanbag-medium leading-[16px]">Dungmori</div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2 rounded-full bg-[#1A5EED] p-2 min-w-[148px] cursor-pointer">
                    <svg width="16" height="22" viewBox="0 0 16 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M1.30844 9.45833C0.225924 9.45833 0 9.66457 0 10.6528V12.4444C0 13.4326 0.225924 13.6389 1.30844 13.6389H3.92532V20.8056C3.92532 21.7938 4.15125 22 5.23377 22H7.85065C8.93317 22 9.15909 21.7938 9.15909 20.8056V13.6389H12.0975C12.9185 13.6389 13.13 13.4932 13.3556 12.7726L13.9163 10.9809C14.3027 9.74646 14.0646 9.45833 12.6582 9.45833H9.15909V6.47222C9.15909 5.81255 9.7449 5.27778 10.4675 5.27778H14.1916C15.2741 5.27778 15.5 5.07154 15.5 4.08333V1.69444C15.5 0.70624 15.2741 0.5 14.1916 0.5H10.4675C6.85437 0.5 3.92532 3.17386 3.92532 6.47222V9.45833H1.30844Z"
                            fill="white"/>
                    </svg>
                    <div>
                      <div class="text-sm font-beanbag-regular leading-[14px]">Facebook</div>
                      <div class="text-base font-beanbag-medium leading-[16px]">Dungmori</div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2 rounded-full bg-[#1E1E1E] p-2 min-w-[148px] cursor-pointer">
                    <svg width="17" height="20" viewBox="0 0 17 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                          d="M6 20C9.31371 20 12 17.3137 12 14V6.24537C13.0006 7.04749 14.1981 7.61412 15.5085 7.86122C15.8589 7.92728 16.0341 7.96032 16.2502 7.90446C16.507 7.83809 16.7923 7.6016 16.9051 7.36157C17 7.15952 17 6.93968 17 6.5C17 6.04137 17 5.81205 16.9499 5.65983C16.8671 5.4079 16.7952 5.31049 16.579 5.15694C16.4483 5.06416 16.1395 4.96876 15.522 4.77796C13.9492 4.29199 12.708 3.05079 12.222 1.47798C12.0312 0.860453 11.9358 0.551691 11.8431 0.421038C11.6895 0.204792 11.5921 0.132938 11.3402 0.0500687C11.1879 0 10.9586 0 10.5 0C10.0341 0 9.80109 0 9.61732 0.0761205C9.37229 0.177614 9.17761 0.372288 9.07612 0.617317C9 0.801088 9 1.03406 9 1.5V14C9 15.6569 7.65685 17 6 17C4.34315 17 3 15.6569 3 14C3 12.8644 3.63101 11.8761 4.56154 11.3667C5.25264 10.9884 5.5982 10.7992 5.69494 10.7057C5.88565 10.5214 5.89434 10.5068 5.96444 10.251C6 10.1212 6 9.91414 6 9.5C6 9.07473 6 8.86209 5.89825 8.65413C5.78169 8.4159 5.46391 8.16888 5.20429 8.1147C4.97765 8.0674 4.82349 8.10665 4.51518 8.18514C1.91964 8.84591 0 11.1988 0 14C0 17.3137 2.68629 20 6 20Z"
                          fill="white"/>
                    </svg>
                    <div>
                      <div class="text-sm font-beanbag-regular leading-[14px]">Tiktok</div>
                      <div class="text-base font-beanbag-medium leading-[16px]">Dungmori</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 px-[50px] flex flex-col gap-4 pb-[20px]">
              <div class="teachers flex flex-col gap-3">
                <div class="teacher-title flex items-center gap-2">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M14.9999 11.042C15.5598 11.042 15.9353 11.4614 16.1376 11.8711V11.8721L16.7177 13.04L16.7226 13.0449C16.7266 13.0485 16.7311 13.0528 16.7363 13.0566C16.7413 13.0604 16.7463 13.0636 16.7509 13.0664L16.7578 13.0703L17.8037 13.2461C18.2538 13.3217 18.7445 13.5624 18.9101 14.082C19.0755 14.6008 18.8161 15.0814 18.4931 15.4053V15.4062L17.6796 16.2256C17.6768 16.2314 17.6731 16.2405 17.6699 16.252C17.6663 16.2646 17.6646 16.2759 17.664 16.2832L17.8964 17.2979C18.0015 17.7573 18.0119 18.3966 17.5165 18.7607C17.0185 19.1268 16.4111 18.9205 16.0068 18.6797L15.0263 18.0947C15.0254 18.0851 15.014 18.0722 14.9746 18.0957L13.9951 18.6797C13.5893 18.9224 12.9836 19.1247 12.4863 18.7588C11.9922 18.3951 11.9996 17.759 12.1054 17.2979L12.3378 16.2832C12.3372 16.2759 12.3356 16.2647 12.332 16.252C12.3287 16.2405 12.325 16.2314 12.3222 16.2256L11.5078 15.4043C11.1867 15.0806 10.9288 14.6007 11.0927 14.083C11.2575 13.5631 11.7474 13.3209 12.1982 13.2451L13.2402 13.0713L13.246 13.0674C13.2508 13.0645 13.2564 13.0615 13.2617 13.0576C13.2668 13.0537 13.2713 13.0496 13.2753 13.0459L13.2802 13.041L13.8613 11.8691C14.0651 11.4602 14.4411 11.0422 14.9999 11.042ZM7.83002 11.1357C8.71651 11.0107 9.61834 11.0107 10.5048 11.1357C10.9044 11.1922 11.518 11.3571 11.9716 11.4883C12.0161 11.5012 12.0388 11.5076 12.0546 11.5146C12.2226 11.5898 12.2525 11.8218 12.1093 11.9375C12.0959 11.9483 12.0751 11.9607 12.0341 11.9854C12.0289 11.9885 12.0257 11.9899 12.0234 11.9912C11.9995 12.0049 11.9771 12.0139 11.9501 12.0195C11.9476 12.0201 11.9438 12.0211 11.9365 12.0225C11.2742 12.146 10.2589 12.5677 9.89838 13.7061C9.531 14.8665 10.1492 15.8122 10.6171 16.2842L10.7773 16.4453C10.872 16.5408 10.9198 16.5887 10.9374 16.6504C10.9551 16.7122 10.9392 16.7781 10.9091 16.9092L10.8847 17.0186C10.8253 17.2777 10.7594 17.7118 10.8232 18.1865C10.877 18.587 10.9038 18.7876 10.829 18.873C10.7544 18.9584 10.5849 18.958 10.246 18.958H5.49311C3.97793 18.958 2.8062 18.2132 1.83002 17.2832C1.26417 16.7441 0.982007 16.1368 1.05268 15.4902C1.11927 14.8825 1.48448 14.3862 1.86909 14.0098C2.48417 13.4077 3.3825 12.9015 3.97651 12.5664C4.11202 12.49 4.23214 12.422 4.32905 12.3643C5.41632 11.7169 6.60976 11.3078 7.83002 11.1357ZM9.16694 1.04199C11.5832 1.04199 13.5419 3.00075 13.5419 5.41699C13.5418 7.83313 11.5831 9.79199 9.16694 9.79199C6.75091 9.79183 4.79207 7.83303 4.79194 5.41699C4.79194 3.00084 6.75083 1.04215 9.16694 1.04199Z"
                        fill="#57D061"/>
                  </svg>
                  <div class="text-base font-beanbag-medium text-[#07403F]">
                    Giáo viên tham gia
                  </div>
                </div>

                <div class="list-teacher flex flex-col gap-2">
                  <div class="flex items-center gap-2">
                    <div
                        class="teacher-item rounded-full flex items-center gap-1 bg-white p-[7px] cursor-pointer shadow-md hover:shadow-lg">
                      <img src="/images/lessons/thay-dung.png" alt="teacher-1.png"
                           class="rounded-full w-[32px] h-[32px] object-cover"/>
                      <span class="text-sm">Thay Dũng Mori</span>
                    </div>
                    <div
                        class="teacher-item rounded-full flex items-center gap-1 bg-white p-[7px] cursor-pointer shadow-md hover:shadow-lg">
                      <img src="/images/lessons/co-thanh.png" alt="teacher-1.png"
                           class="rounded-full w-[32px] h-[32px] object-cover"/>
                      <span class="text-sm">Cô Phương Thanh</span>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <div
                        class="teacher-item rounded-full flex items-center gap-1 bg-white p-[7px] cursor-pointer shadow-md hover:shadow-lg">
                      <img src="/images/lessons/co-phan-ha.png" alt="teacher-1.png"
                           class="rounded-full w-[32px] h-[32px] object-cover"/>
                      <span class="text-sm">Cô Phan Hà</span>
                    </div>
                    <div
                        class="teacher-item rounded-full flex items-center gap-1 bg-white p-[7px] cursor-pointer shadow-md hover:shadow-lg">
                      <img src="/images/lessons/co-phan-ha.png" alt="teacher-1.png"
                           class="rounded-full w-[32px] h-[32px] object-cover"/>
                      <span class="text-sm">Cô Phan Hà</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="content flex flex-col gap-2">
                <div class="content-title flex items-center gap-2">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M9.56227 14.4222C12.0732 11.7356 14.4972 9.91308 17.5421 7.33212C18.4534 6.55959 18.5942 5.20547 17.8793 4.24609C17.1194 3.22647 15.6756 3.02267 14.6983 3.83326C11.9092 6.14666 9.73936 8.37413 7.68099 10.7549C7.57297 10.8798 7.51897 10.9423 7.46291 10.9775C7.32077 11.0666 7.14315 11.0676 7.00007 10.9799C6.94365 10.9453 6.88955 10.8841 6.78134 10.7616L5.68451 9.51971C4.68375 8.3866 2.88872 8.51203 2.05315 9.77346C1.46842 10.6562 1.55507 11.8261 2.26338 12.6118L3.98333 14.5197C5.26978 15.9468 5.913 16.6603 6.6852 16.6673C7.4574 16.6743 8.15902 15.9236 9.56227 14.4222Z"
                        fill="#57D061"/>
                  </svg>
                  <div class="text-base font-beanbag-medium text-[#07403F]">
                    Nội dung
                  </div>
                </div>

                <div class="content-text font-beanbag-regular text-base text-black">
                  Chữa 30 câu dễ gây nhầm lẫn nhất ở cấp độ N1. Sẵn sàng bứt tốc cháy hết mình các bạn
                  ơi!
                </div>

                <div
                    class="btn-docs-download rounded-full bg-white flex items-center justify-center gap-2 px-[28px] py-[9px] font-beanbag-regular text-sm border border-[#07403F] cursor-pointer hover:bg-[#F0FFF1] w-[140px]">
                  Tải tài liệu
                </div>
              </div>

              <div class="gift flex flex-col gap-2">
                <div class="gift-title flex items-center gap-2">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M3.15527 10.7656C3.29586 10.7893 3.42872 10.8027 3.54981 10.8115C3.84158 10.8327 4.18502 10.8321 4.53223 10.832H8.83301C8.98985 10.832 9.06835 10.8323 9.11719 10.8809C9.166 10.9297 9.16602 11.0089 9.16602 11.166V18.9111C9.16583 18.9366 9.14555 18.9569 9.12012 18.957C7.78468 18.957 6.7187 18.9571 5.88281 18.8447C5.01926 18.7286 4.30773 18.4825 3.74512 17.9199C3.18258 17.3573 2.93641 16.6457 2.82031 15.7822C2.70798 14.9464 2.70799 13.8802 2.70801 12.5449V11.1172C2.70801 10.9128 2.70786 10.8098 2.7666 10.7598C2.82565 10.7098 2.93579 10.7286 3.15527 10.7656ZM16.8438 10.7656C17.0632 10.7286 17.1734 10.7098 17.2324 10.7598C17.2914 10.8097 17.291 10.9126 17.291 11.1172V12.5449C17.291 13.8803 17.2911 14.9464 17.1787 15.7822C17.0626 16.6458 16.8165 17.3573 16.2539 17.9199C15.6913 18.4825 14.9797 18.7286 14.1162 18.8447C13.2805 18.957 12.215 18.957 10.8799 18.957C10.8543 18.957 10.8332 18.9366 10.833 18.9111V11.166C10.833 11.0089 10.833 10.9297 10.8818 10.8809C10.9306 10.8322 11.0092 10.832 11.166 10.832H15.4668C15.814 10.8321 16.1575 10.8327 16.4492 10.8115C16.5703 10.8027 16.7032 10.7893 16.8438 10.7656ZM6.78613 1.04199C8.09591 1.04202 9.26022 1.66403 10 2.62891C10.7398 1.66387 11.9049 1.04199 13.2148 1.04199H13.5127C14.7946 1.04224 15.834 2.08135 15.834 3.36328C15.834 4.03997 15.6422 4.67191 15.3105 5.20801H15.4395C15.8095 5.208 16.1204 5.20819 16.374 5.22656C16.6373 5.24565 16.8931 5.28788 17.1367 5.40039C17.4127 5.52784 17.6592 5.71886 17.8389 5.96777C18.0086 6.20289 18.073 6.45591 18.1006 6.69922C18.1255 6.91952 18.125 7.18412 18.125 7.46973V7.53027C18.125 7.81588 18.1255 8.08048 18.1006 8.30078C18.073 8.54409 18.0086 8.79711 17.8389 9.03223C17.6592 9.28114 17.4127 9.47216 17.1367 9.59961C16.8931 9.71212 16.6373 9.75435 16.374 9.77344C16.1204 9.79181 15.8095 9.792 15.4395 9.79199H4.56055C4.19054 9.792 3.87957 9.79181 3.62598 9.77344C3.36266 9.75434 3.10693 9.71212 2.86328 9.59961C2.58734 9.47215 2.3408 9.28115 2.16113 9.03223C1.99148 8.79714 1.928 8.54406 1.90039 8.30078C1.87543 8.08048 1.87497 7.81588 1.875 7.53027V7.46973C1.87497 7.18412 1.87543 6.91952 1.90039 6.69922C1.928 6.45594 1.99148 6.20286 2.16113 5.96777C2.3408 5.71886 2.58734 5.52784 2.86328 5.40039C3.10693 5.28788 3.36266 5.24565 3.62598 5.22656C3.87957 5.20819 4.19054 5.208 4.56055 5.20801H4.69043C4.35876 4.67187 4.16701 4.04003 4.16699 3.36328C4.16699 2.08124 5.20626 1.04207 6.48828 1.04199H6.78613ZM6.48828 2.70801C6.12673 2.70808 5.83399 3.00171 5.83399 3.36328C5.83404 4.38227 6.65974 5.20791 7.67871 5.20801H9.16699V5.08984C9.16699 3.77491 8.10106 2.70806 6.78613 2.70801H6.48828ZM13.2148 2.70801C11.8999 2.70801 10.834 3.77488 10.834 5.08984V5.20801H12.3213C13.3404 5.20801 14.1669 4.38233 14.167 3.36328C14.167 3.00182 13.8741 2.70826 13.5127 2.70801H13.2148Z"
                        fill="#57D061"/>
                  </svg>
                  <div class="text-base font-beanbag-medium text-[#07403F] leading-[16px]">
                    Quà tặng
                  </div>
                </div>

                <div class="gift-content ml-7">
                  <div v-for="i in 3" class="flex items-center gap-2">
                    <div class="w-1 h-1 rounded-full bg-black"></div>
                    E-book Trọn bộ Ngữ pháp N1
                  </div>
                </div>
              </div>

              <div class="attendees flex items-center gap-2 font-averta-bold text-base text-[#176867]">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M11.667 11.042C14.3084 11.0422 16.4578 13.1916 16.458 15.833C16.458 17.0996 15.4335 18.1248 14.167 18.125H5.83301C4.56649 18.1248 3.54199 17.0996 3.54199 15.833C3.54217 13.1916 5.6916 11.0422 8.33301 11.042H11.667ZM8.33301 12.292C6.38327 12.2922 4.79217 13.8833 4.79199 15.833C4.79199 16.4079 5.25816 16.8748 5.83301 16.875H14.167C14.7418 16.8748 15.208 16.4079 15.208 15.833C15.2078 13.8833 13.6167 12.2922 11.667 12.292H8.33301ZM10 1.875C12.1832 1.875 13.9578 3.64982 13.958 5.83301C13.958 8.01634 12.1833 9.79199 10 9.79199C7.81667 9.79199 6.04199 8.01634 6.04199 5.83301C6.04217 3.64982 7.81678 1.875 10 1.875ZM10 3.125C8.50844 3.125 7.29217 4.34149 7.29199 5.83301C7.29199 7.32467 8.50833 8.54199 10 8.54199C11.4917 8.54199 12.708 7.32467 12.708 5.83301C12.7078 4.34149 11.4916 3.125 10 3.125Z"
                      fill="#176867"/>
                </svg>
                {{ selectedEvent.attendees || 0 }} đã đăng ký
              </div>
            </div>
          </div>

          <div class="comment-wrap">
            <comment></comment>
          </div>

          <div class="comment-input">
            <div v-if="isReply">

            </div>


            <!-- avatar circle -->

            <!-- textarea input -->


          </div>
        </div>
      </template>
    </modal>

    <!-- Overlay to close popover -->
    <div v-if="showDayPopover" @click="closeDayPopover" class="fixed inset-0 z-40"></div>
  </div>
</template>

<script>
import Comment from "./component/Comment.vue";

export default {
  name: "Calendar",
  components: {Comment},
  data() {
    return {
      currentDate: new Date(),
      daysOfWeek: ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
      showDayPopover: false,
      showEventModal: false,
      selectedDay: null,
      selectedEvent: null,
      popoverPosition: {top: 0, left: 0},

      // Sample events data - using current dates for demo
      events: [
        {
          level: 'N5',
          id: 1,
          title: 'Livestream N5 - Bài 1',
          date: '2025-07-31',
          time: '19:00 - 20:30',
          color: '#FF6B6B',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Học từ vựng và ngữ pháp cơ bản N5. Bài học về giới thiệu bản thân và gia đình.',
          location: 'Zoom Room 1',
          attendees: 45,
          isSubscribed: true
        },
        {
          level: 'N4',
          id: 2,
          title: 'Ôn tập N4',
          date: '2025-07-29',
          time: '20:45 - 22:00',
          color: '#4ECDC4',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Ôn tập các điểm ngữ pháp quan trọng của N4',
          location: 'Zoom Room 2',
          attendees: 32,
          isSubscribed: false
        },
        {
          level: 'N3',
          id: 3,
          title: 'Luyện nghe N3',
          date: '2025-07-30',
          time: '19:30 - 21:00',
          color: '#45B7D1',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Luyện tập kỹ năng nghe hiểu với các đoạn hội thoại thực tế',
          location: 'Zoom Room 1',
          attendees: 28,
          isSubscribed: false
        },
        {
          level: 'N2',
          id: 4,
          title: 'Kanji N2',
          date: '2025-07-31',
          time: '18:00 - 19:30',
          color: '#96CEB4',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Học Kanji N2 - Phần 1: Các Kanji về thiên nhiên',
          location: 'Zoom Room 3',
          attendees: 22,
          isSubscribed: true
        },
        {
          level: 'N1',
          id: 5,
          title: 'Giao tiếp hàng ngày',
          date: '2025-07-31',
          time: '20:00 - 21:30',
          color: '#FFEAA7',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Luyện tập giao tiếp trong các tình huống hàng ngày',
          location: 'Zoom Room 1',
          attendees: 38,
          isSubscribed: false
        },
        {
          level: 'N1',
          id: 6,
          title: 'Đọc hiểu N1',
          date: '2025-08-01',
          time: '19:00 - 20:30',
          color: '#DDA0DD',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Luyện đọc hiểu các bài văn phức tạp cấp độ N1',
          location: 'Zoom Room 2',
          attendees: 15,
          isSubscribed: true
        },
        {
          level: 'N5',
          id: 7,
          title: 'Văn hóa Nhật Bản',
          date: '2025-08-02',
          time: '18:30 - 20:00',
          color: '#FFB6C1',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Tìm hiểu về văn hóa và phong tục tập quán Nhật Bản',
          location: 'Zoom Room 1',
          attendees: 42,
          isSubscribed: false
        },
        {
          level: 'N5',
          id: 8,
          title: 'Luyện thi JLPT N5',
          date: '2025-08-03',
          time: '19:00 - 21:00',
          color: '#87CEEB',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Luyện đề thi thử JLPT N5 và giải đáp thắc mắc',
          location: 'Zoom Room 1',
          attendees: 55,
          isSubscribed: false
        },
        {
          level: 'N4',
          id: 9,
          title: 'Hội thoại thực tế',
          date: '2025-08-03',
          time: '21:15 - 22:30',
          color: '#98D8C8',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Luyện tập hội thoại trong các tình huống thực tế',
          location: 'Zoom Room 2',
          attendees: 25,
          isSubscribed: false
        },
        {
          level: 'N3',
          id: 10,
          title: 'Ngữ pháp N3',
          date: '2025-08-03',
          time: '16:00 - 17:30',
          color: '#F7DC6F',
          backgroundColor: 'linear-gradient(90deg, #FFFFFF 0%, #CEFFD8 30%, #CEFFD8 70%, #FFFFFF 100%)',
          description: 'Học ngữ pháp N3 - Phần động từ',
          location: 'Zoom Room 3',
          attendees: 30,
          isSubscribed: false
        }
      ]
    };
  },
  methods: {
    previousMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
    },

    nextMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
    },

    goToToday() {
      this.currentDate = new Date();
    },

    openDayPopover(day, event) {
      if (!day.isCurrentMonth) return;

      this.selectedDay = day;
      this.showDayPopover = true;

      // Calculate popover position
      const rect = event.currentTarget.getBoundingClientRect();
      const popoverWidth = 350;
      const popoverHeight = 300;

      let left = rect.left + rect.width / 2 - popoverWidth / 2;
      let top = rect.bottom + 10;

      // Adjust if popover goes off screen
      if (left + popoverWidth > window.innerWidth) {
        left = window.innerWidth - popoverWidth - 20;
      }
      if (left < 20) {
        left = 20;
      }
      if (top + popoverHeight > window.innerHeight) {
        top = rect.top - popoverHeight - 10;
      }

      this.popoverPosition = {top, left};
    },

    closeDayPopover() {
      this.showDayPopover = false;
      this.selectedDay = null;
    },

    openEventDetail(event) {
      this.selectedEvent = event;
      this.showEventModal = true;
      this.closeDayPopover();
    },

    closeEventModal() {
      this.showEventModal = false;
      this.selectedEvent = null;
    },

    formatDate(date) {
      const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      return date.toLocaleDateString('vi-VN', options);
    },

    getTextColor(backgroundColor) {
      // Simple function to determine if text should be white or black based on background color
      const hex = backgroundColor.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      const brightness = ((r * 299) + (g * 587) + (b * 114)) / 1000;
      return brightness > 128 ? '#000000' : '#FFFFFF';
    }
  },
  mounted() {
    // Close popover when clicking outside
    document.addEventListener('click', (e) => {
      if (this.showDayPopover && this.$refs.dayPopover && !this.$refs.dayPopover.contains(e.target)) {
        this.closeDayPopover();
      }
    });
  },
  created() {
  },
  watch: {},
  computed: {
    currentMonthYear() {
      const options = {year: 'numeric', month: 'long'};
      return this.currentDate.toLocaleDateString('vi-VN', options);
    },

    calendarDays() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();

      // First day of the month
      const firstDay = new Date(year, month, 1);
      // Last day of the month
      const lastDay = new Date(year, month + 1, 0);

      // Start from Sunday of the week containing the first day
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());

      // End on Saturday of the week containing the last day
      const endDate = new Date(lastDay);
      endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));

      const days = [];
      const currentDate = new Date(startDate);
      const today = new Date();

      console.log('currentDate: ', currentDate);
      console.log('endDate: ', endDate);
      while (currentDate <= endDate) {
        console.log('currentDate=======================>: ', currentDate);
        const dayEvents = this.events.filter(event => {
          const eventDate = new Date(event.date);
          return eventDate.toDateString() === currentDate.toDateString();
        });

        let visibleEvents = [];
        let hiddenEventsCount = 0;
        if (dayEvents.length > 2) {
          visibleEvents = dayEvents.slice(0, 2);
          hiddenEventsCount = Math.max(0, dayEvents.length - 2);
        } else {
          visibleEvents = dayEvents.slice(0, 3);
          hiddenEventsCount = Math.max(0, dayEvents.length - 3);
        }

        days.push({
          date: currentDate.getDate(),
          fullDate: new Date(currentDate),
          isCurrentMonth: currentDate.getMonth() === month,
          isToday: currentDate.toDateString() === today.toDateString(),
          events: dayEvents,
          visibleEvents: visibleEvents,
          hiddenEventsCount: hiddenEventsCount
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      console.log('days: ', days);

      return days;
    }
  },
};
</script>

<style>
#calendar_wrap {
  max-width: 1320px;
  margin: 0 auto;
}

.calendar-day {
  min-height: 120px;
}

.calendar-day:hover {
  background-color: #F8F9FA;
}

.event-item {
  font-size: 11px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Modal styles */
.v-modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: table;
  transition: opacity 0.3s ease;
}

.v-modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.v-modal-container {
  margin: 0 auto;
  padding: 0 !important;
  background-color: #fff;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
  transition: all 0.3s ease;
  max-width: 872px;
  max-height: calc(100vh - 245px);
  overflow-y: auto;
  overflow-x: hidden;
}

.v-modal-body {
  margin: 0;
}

.v-modal-enter {
  opacity: 0;
}

.v-modal-leave-active {
  opacity: 0;
}

.v-modal-enter .v-modal-container,
.v-modal-leave-active .v-modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

/* Popover animation */
.popover-enter-active, .popover-leave-active {
  transition: all 0.2s ease;
}

.popover-enter, .popover-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom scrollbar for popover */
.max-h-\[300px\]::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[300px\]::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-\[300px\]::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-\[300px\]::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.icon-continue {
  display: none;
}

.event-item-day-popover:hover .icon-continue {
  display: block;
}

.bg-N1 {
  background-color: #EF2F2F;
}

.bg-N2 {
  background-color: #995EFF;
}

.bg-N3 {
  background-color: #4E87FF;
}

.bg-N4 {
  background-color: #EF6D13;
}

.bg-N5 {
  background-color: #57D061;
}
</style>