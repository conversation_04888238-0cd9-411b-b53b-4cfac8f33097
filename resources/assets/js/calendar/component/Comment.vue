<template>
  <div>
    <div v-for="comment in comments">
      <div class="comment-item">

      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Comment',
  data() {
    return {
      comments: [
        {
          id: 1,
          content: 'Hello world',
          user: {
            id: 1,
            name: '<PERSON>'
          },
          replies: [
            {
              id: 1,
              content: 'Hello world',
              user: {
                id: 1,
                name: '<PERSON>'
              }
            },
            {
              id: 2,
              content: 'Hello world',
              user: {
                id: 1,
                name: '<PERSON>'
              }
            }
          ]
        },
        {
          id: 2,
          content: 'Hello world',
          user: {
            id: 1,
            name: '<PERSON>'
          },
          replies: []
        },
        {
          id: 3,
          content: 'Hello world',
          user: {
            id: 1,
            name: '<PERSON>'
          },
          replies: []
        }
      ]
    }
  }
}
</script>
