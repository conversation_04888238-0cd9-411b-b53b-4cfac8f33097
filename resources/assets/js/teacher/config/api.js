import axios from 'axios';

let http = axios.create({
  baseURL: '/backend/school/api/v1',
  headers: {
    // 'Authorization': 'Bearer ',
    'Content-Type': 'application/json'
  },
  withCredentials: true,
});


// Response interceptor
http.interceptors.response.use(response => response, error => {
  const { status } = error.response || {}
  if (status >= 500) {}
  if (status === 401) {
    // 401 Unauthorized
    window.location.href = `${process.env.APP_URL}/teacher/login`;
  }

  return Promise.reject(error)
});
export default http;
