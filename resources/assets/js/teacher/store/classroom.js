import api from "../api";
import * as types from "./mutation-types";

const classroom = {
    namespaced: true,
    state: {
        list: [],
        classroom: [],
        offlineExams: [],
        admins: []
    },
    getters: {
        classroomList: (state) => state.list,
        classroom: (state) => state.classroom,
        offlineExams: (state) => state.offlineExams,
        admins: (state) => state.admins
    },
    mutations: {
        [types.SET_CLASSROOM_LIST](state, data) {
            state.list = data;
        },
        [types.RESET_CLASSROOM_LIST](state) {
            state.list = [];
        },
        [types.SET_CLASS_DETAIL](state, data) {
            state.classroom = data.data;
            state.offlineExams = data.offlineExams;
            state.admins = data.admins;
        },
        [types.UPDATE_STUDENT](state, data) {
            for (const student of state.classroom.students.data) {
                if (data.userId === student.id) {
                    student.group_user[0].note = data.note;
                }
            }
        },
        [types.UPDATE_COURSE_TIME](state, data) {
            for (const student of state.classroom.students.data) {
                if (data.student_id === student.id) {
                    if (student.course_time.find((ct) => ct.course_time_id === data.course_time_id)) {
                        // Update
                        student.course_time.map((ct) => {
                            if (ct.course_time_id === data.course_time_id) {
                                ct.status = data.status;
                                ct.note = data.note;
                            }
                            return ct;
                        });
                    } else {
                        // Create
                        student.course_time.push(data);
                    }
                }
            }
        },
        [types.CREATE_COURSE_TIME_STUDENT](state, data) {
            state.classroom.course_time.push(data);
            for (const student of state.classroom.students.data) {
                student.course_time.push({
                    course_time_id: data.id,
                    status: 1,
                    note: null,
                    student_id: student.id,
                });
            }
        },
        [types.UPDATE_ATTENDANCE](state, data) {
            for (const courseTime of state.classroom.course_time) {
                if (data.id === courseTime.id) {
                    if (data.note) {
                        courseTime.note = data.note;
                    }
                    if (data.note_cancel) {
                        courseTime.note_cancel = data.note_cancel;
                    }
                    if (data.password) {
                        courseTime.password = data.password;
                    }
                    if (data.link) {
                        courseTime.link = data.link;
                    }
                    if (data.date_attendance) {
                        courseTime.date_attendance = data.date_attendance;
                    }
                }
            }
        },
    },
    actions: {
        getClassroomList({commit}, data) {
            return api.classroom
                .loadList(data)
                .then((response) => commit("SET_CLASSROOM_LIST", response.data))
                .catch();
        },
        getStudentByClass({commit}, data) {
            return api.classroom
                .getStudentByClass(data)
                .then((response) => commit("SET_CLASS_DETAIL", response.data))
                .catch();
        },
        resetClassroomList({commit}) {
            commit("RESET_CLASSROOM_LIST");
        },
        saveCourseTime({commit}, data) {
            return api.classroom
                .saveCourseTime(data)
                .then((response) => commit(types.UPDATE_COURSE_TIME, response.data))
                .catch();
        },
        updateStudent({commit}, data) {
            return api.teacher
                .updateStudent(data)
                .then((response) => commit(types.UPDATE_STUDENT, data))
                .catch();
        },
        updateAttendance({commit}, data) {
            return api.teacher.updateAttendance(data)
                .then(response => {
                    if (!data.id && data.date_attendance) {
                        commit(types.CREATE_COURSE_TIME_STUDENT, response.data);
                    } else {
                        commit('UPDATE_ATTENDANCE', data);
                    }
                })
                .catch();
        },
        sendBulkMessageClassroom({commit}, data) {
            return api.student.sendBulkMessage(data)
                .then(response => commit("MESSAGE_SENT", response.data))
                .catch();
        },
    },
};

export default classroom;
