import api from './../config/api';
export default {
  loadList: () => new Promise((resolve, reject) => {
    api.get(`/payoff/list`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  store: (data) => new Promise((resolve, reject) => {
    api.post(`/payoff/store`, data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  update: (id, data) => new Promise((resolve, reject) => {
    api.put(`/payoff/${id}`, data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  delete: (id) => new Promise((resolve, reject) => {
    api.delete(`/payoff/${id}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
