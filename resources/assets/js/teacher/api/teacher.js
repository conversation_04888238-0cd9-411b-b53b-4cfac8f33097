import api from './../config/api';
export default {
  loadList: data => new Promise((resolve, reject) => {
    api.get(`/user/list?curDate=${data.curDate}&page=${data.page}&perPage=${data.perPage}&name=${data.name}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  updateTableTeacher: data => new Promise((resolve, reject) => {
    api.get(`/user/show?curDate=${data.curDate}&userId=${data.userId}&timeFrame=${data.timeFrame}&teacherType=${data.teacherType}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  updateAttendancePayoff: data => new Promise((resolve, reject) => {
    api.post('/user/set-attendance-payoff', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  updateAttendance: data => new Promise((resolve, reject) => {
    api.post('/user/set-attendance', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  getPayoff: () => new Promise((resolve, reject) => {
    api.get(`/user/payoff`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  exportExcel: data => new Promise((resolve, reject) => {
    api.post('/user/export', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  updateStudent: data => new Promise((resolve, reject) => {
    api.post('/user/edit-student-note', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
