<template>
  <div>
    <el-input v-model="filters.name" @keyup.enter.native="search" placeholder="<PERSON><PERSON><PERSON> kiếm bằng tên"></el-input>
    <router-link
      v-for="classroom in classroomList"
      :key="`class-link-${classroom.id}`"
      :to="{ name: 'classroom', params: { id: classroom.id } }"
    >
      <div
        :key="`class-${classroom.id}`"
        class="p-5 mt-2 shadow hover:shadow-lg cursor-pointer"
      >
        {{ classroom.name }}
      </div>
    </router-link>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
export default {
  data() {
    return {
      filters: {
        vip_level: undefined,
        name: undefined,
        type: undefined,
      },
    };
  },
  computed: {
    ...mapGetters("classroom", ["classroomList"]),
  },
  mounted() {
    this.filters.vip_level = this.$route.params?.level;
    this.search();
  },
  beforeD<PERSON>roy() {
    this.resetClassroomList();
  },
  methods: {
    ...mapActions("classroom", ["getClassroomList", "resetClassroomList"]),
    async search() {
      await this.getClassroomList(this.filters);
    },
  },
};
</script>
