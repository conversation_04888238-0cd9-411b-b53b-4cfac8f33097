<template>
  <el-container style="width: 100%; height: 100%; border: 1px solid #eee">
    <el-aside width="200px" style="background-color: #fff; position: fixed; height: 100vh; overflow-y: scroll;">
      <el-menu :default-openeds="['1', '3']">
        <el-submenu index="1">
          <template slot="title"><i class="el-icon-message"></i>Zoom vip</template>
          <router-link :to="{name: 'dashboard'}">
            <el-menu-item index="1-3">{{ $t('teacher.class_management') }}</el-menu-item>
          </router-link>
        </el-submenu>
      </el-menu>
      <el-menu>
        <router-link :to="{name: 'profile'}">
          <el-menu-item index="1-4"><i class="el-icon-s-custom"></i>{{ $t('teacher.teacher_information') }}</el-menu-item>
        </router-link>
      </el-menu>
    </el-aside>
    <el-container style="width: calc(100% - 200px); margin-left: 200px;">
      <el-header class="app-header">
        <div class="flex">
          <div class="flex-1">{{ title }}</div>
          <div class="flex">
            <div class="mr-3 text-sm font-normal cursor-pointer" @click="setLang('vn')">Vietnamese</div>
            <div class="mr-5 text-sm font-normal cursor-pointer" @click="setLang('jp')">日本語</div>
            <div class="text-base">{{ user.user_name }}</div>
            <div class="ml-3 text-sm font-normal cursor-pointer" @click="logout">{{ $t('teacher.logout') }}</div>
          </div>
        </div>
      </el-header>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'App',
  data() {
    return {
      user: user,
    }
  },
  computed: {
    ...mapGetters('ui', ['title'])
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    logout() {
      window.location.href = '/teacher/logout';
    },
    setLang(lang) {
      this.$i18n.locale = lang;
      this.setPage(this.$t("teacher.class_detail"));
    }
  },
  mounted() {
  }
}
</script>
<style>
.el-header {
  background-color: #B3C0D1;
  color: #333;
  line-height: 60px;
}
.el-aside {
  color: #333;
}
.app-header {
  font-size: 20px;
  font-weight: bold;
  text-align: left;
  height: 40px !important;
  line-height: 40px;
  background: #96D962;
}
</style>
