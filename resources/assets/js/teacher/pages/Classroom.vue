<template>
  <div>
    <div class="infomation-bar flex justify-between mb-5">
      <div class="group-info">
        <h1 v-cloak class="font-bold">{{ classroom.name || '--' }} | {{ `${ classroom.group_teacher?.teacher?.last_name || '--' }
        ${ classroom.group_teacher?.teacher?.first_name || '--'}` }}</h1>
        <h1 v-cloak class="font-bold">
          {{ $t('teacher.remaining_lesson') }}: {{ classroom.vip_session - course_time.length >=0 ? classroom.vip_session - course_time.length : '--' }} / {{ classroom.vip_session || '--' }}
        </h1>
      </div>
      <div class="flex justify-end w-1/2 max-h-9" v-if="classroom.links">
        <div v-if="classroom.links.groupLink"  class="bg-emerald-500 flex items-center px-4 py-[2px] rounded-full mr-2">
          <a :href="classroom.links.groupLink.url" target="_blank">Link Group</a>
        </div>
        <div v-if="classroom.links.doc" class="bg-orange-500 flex items-center px-4 rounded-full mr-2">
          <a :href="classroom.links.doc.url" target="_blank">Link Doc</a>
        </div>
        <div v-if="classroom.links.messenger" class="bg-amber-500 flex items-center px-4 rounded-full mr-2">
          <a :href="classroom.links.messenger.url" target="_blank">Messenger</a>
        </div>
        <div v-if="classroom.links.linkZoom" class="bg-gray-500 flex items-center px-4 rounded-full">
          <a :href="classroom.links.linkZoom.url" target="_blank">Link zoom</a>
          <span class="inline-block bg-teal-500 text-white text-xs px-2 rounded-full ml-2 cursor-pointer"  v-on:click="showPass(classroom.links.linkZoom.note)">P</span>
        </div>
      </div>
    </div>
    <div v-if="classroom.vip_session - course_time.length === 0" class="mb-4">
      <div class="el-button el-button--primary" @click="tab = 1">Xem điểm danh</div>
      <div v-if="this.classroom.report" class="el-button el-button--primary" @click="tab = 2">Xem báo cáo</div>
    </div>
    <template v-if="tab === 1 || classroom.vip_session - course_time.length !== 0">
      <el-table :data="tableData" style="width: 100%" max-height="700" border>
        <el-table-column
            type="index"
            label="STT"
            width="50"
            fixed
        >
        </el-table-column>
        <el-table-column fixed prop="id" label="ID">
          <template slot-scope="scope">
            <div>
              {{ scope.row.id < 0 ? '' : scope.row.id }}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed prop="name" :label="$t('teacher.username')" width="200">
        </el-table-column>
<!--        <el-table-column-->
<!--            fixed-->
<!--            prop="note"-->
<!--            :label="$t('teacher.note_sv')"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            <el-tooltip-->
<!--                class="item"-->
<!--                effect="dark"-->
<!--                placement="top-start"-->
<!--            >-->
<!--              <div slot="content" style="white-space: pre;">{{ scope.row.note }}</div>-->
<!--              <div class="truncate ... text-center" @click="editStudentNote(scope.row)">-->
<!--                {{ scope.row.note || '&nbsp;&nbsp;&nbsp;&nbsp;' }}-->
<!--              </div>-->
<!--            </el-tooltip>-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column
            v-for="day in classroom.vip_session"
            :key="`attendance-${day}`"
            :label="`${day}`"
            width="70"
            class="flex justify-center align-center"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.id === -1">
              <el-tooltip
                  v-if="scope.row[`day_${day}`]?.date_attendance"
                  class="item"
                  effect="dark"
                  :content="scope.row[`day_${day}`]?.date_attendance"
                  placement="top-start"
              >
                <div class="truncate ... text-center" @click="editDate(scope.row[`day_${day}`], day)">
                  {{ getMonthDate(scope.row[`day_${day}`]?.date_attendance) }}
                </div>
              </el-tooltip>
              <div v-else class="truncate ... text-center" @click="editDate(scope.row[`day_${day}`], day)">
                &nbsp;&nbsp;<i class="el-icon-date"></i>&nbsp;&nbsp;
              </div>
            </div>
            <div
                v-else-if="scope.row.id === -2"
                class="truncate ... cursor-pointer text-center flex items-center justify-center"
            >
              <a
                  :href="scope.row[`day_${day}`]?.link"
                  class="truncate ... block"
                  target="_blank"
                  @click="copy(scope.row[`day_${day}`]?.link)"
              >
                {{ scope.row[`day_${day}`]?.link }}
              </a>
              <i class="el-icon-edit" @click="editLink(scope.row[`day_${day}`])"></i>
            </div>
            <div v-else-if="scope.row.id === -3">
              <el-tooltip
                  v-if="scope.row[`day_${day}`]?.password"
                  class="item"
                  effect="dark"
                  :content="scope.row[`day_${day}`]?.password"
                  placement="top-start"
              >
                <div
                    class="truncate ... cursor-pointer text-center"
                    @click="editPassword(scope.row[`day_${day}`])"
                >
                  {{ scope.row[`day_${day}`]?.password }}
                </div>
              </el-tooltip>
              <div
                  v-else
                  class="truncate ... cursor-pointer text-center"
                  @click="editPassword(scope.row[`day_${day}`])"
              >
                &nbsp;&nbsp;&nbsp;&nbsp;
              </div>
            </div>

            <div v-else-if="scope.row.id === -4">
              <el-tooltip
                  v-if="scope.row[`day_${day}`]?.note"
                  class="item"
                  effect="dark"
                  :content="scope.row[`day_${day}`]?.note"
                  placement="top-start"
              >
                <div class="truncate ... text-center" @click="editNote(scope.row[`day_${day}`])">
                  {{ scope.row[`day_${day}`]?.note }}
                </div>
              </el-tooltip>
              <div v-else class="truncate ... text-center" @click="editNote(scope.row[`day_${day}`])">
                &nbsp;&nbsp;&nbsp;&nbsp;
              </div>
            </div>
            <div v-else-if="scope.row.id === -5">
              <el-tooltip
                  v-if="scope.row[`day_${day}`]?.note_cancel"
                  class="item"
                  effect="dark"
                  :content="scope.row[`day_${day}`]?.note_cancel"
                  placement="top-start"
              >
                <div class="truncate ... text-center cursor-pointer">
                  {{ scope.row[`day_${day}`]?.note_cancel || '--' }}
                </div>
              </el-tooltip>
              <div v-else class="truncate ... text-center cursor-pointer">
                {{ scope.row[`day_${day}`]?.note_cancel || '--' }}
              </div>
            </div>
            <div v-else style="width: 100%" @click="editCourseTime(scope.row, day)">
              <div v-if="!scope.row[`day_${day}`]">{{ course_time[day - 1]?.date_attendance ? '&nbsp;&nbsp;&nbsp;&nbsp;' : '' }}</div>
              <div
                  v-else-if="scope.row[`day_${day}`]?.status"
                  class="flex justify-center align-center"
              >
                <div v-if="scope.row[`day_${day}`]?.status === 2"></div>
                <div
                    v-else
                    style="
                  border: 2px solid #96d962;
                  border-radius: 50%;
                  width: 15px;
                  height: 15px;
                "
                ></div>
              </div>
              <div
                  v-else-if="scope.row[`day_${day}`]?.note"
                  class="flex justify-center align-center"
              >
                <div
                    class="truncate ..."
                    style="
                  color: black;
                  background-color: #96d962;
                  width: 40px;
                  height: 40px;
                  text-align: center;
                  line-height: 40px;
                "
                >
                  <el-tooltip
                      class="item"
                      effect="dark"
                      :content="scope.row[`day_${day}`]?.note"
                      placement="top-start"
                  >
                    <div class="truncate ... cursor-pointer text-center">
                      {{ scope.row[`day_${day}`]?.note }}
                    </div>
                  </el-tooltip>
                </div>
              </div>
              <div v-else class="flex justify-center align-center">
                <div
                    style="background-color: red; width: 40px; height: 40px"
                ></div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
            fixed="right"
            prop="presents"
            :label="$t('teacher.attendant')"
        ></el-table-column>
        <el-table-column
            fixed="right"
            prop="absences"
            :label="$t('teacher.absent')"
        ></el-table-column>
        <el-table-column
            fixed="right"
            prop="ratio"
            label="Tỷ lệ (%)"
        ></el-table-column>
      </el-table>
      <div class="p-3 shadow mt-5 bg-white w-full">
        <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size.sync="perPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="classroom.students?.total"
        >
        </el-pagination>
      </div>
    </template>

    <div v-if="tab === 2">
      <div>
        <div>Họ tên giáo viên: {{ `${classroom.group_teacher.teacher.last_name} ${classroom.group_teacher.teacher.first_name}` }}</div>
        <div>Lớp đảm nhận: {{ classroom.name }}</div>
        <div>Ngày khai giảng: {{ classroom.start_date }}</div>
        <div>Ngày kết thúc: {{ classroom.expired_at }}</div>
      </div>
      <div class="mt-6">
        <div class="mb-1"><b>Báo cáo chung về lớp</b></div>
        <el-table :data="reportTableGeneral" style="width: 100%" max-height="700" border>
          <el-table-column
            type="index"
            label="STT"
            width="50"
            fixed
          />
          <el-table-column fixed prop="content" label="Nội dung" />
          <el-table-column fixed prop="value" label="Số liệu" />
          <el-table-column fixed prop="note" label="Ghi chú" />
        </el-table>
      </div>
      <div class="mt-6">
        <div class="mb-1"><b>Cảm nhận của giáo viên về khóa học, học viên</b></div>
        <el-table :data="reportTableFeeling" style="width: 100%" max-height="700" border>
          <el-table-column
            type="index"
            label="STT"
            width="50"
            fixed
          />
          <el-table-column fixed prop="title" label="Tiêu đề" />
          <el-table-column fixed prop="content" label="Nội dung" />
        </el-table>
      </div>
      <div class="mt-6">
        <div class="mb-1"><b>Nhận xét, góp ý và những đề xuất cải thiện khóa học</b></div>
        <div><b>Ưu điểm:</b> <span class="underline decoration-gray-400">{{ reportData.advantage }}</span></div>
        <div><b>Nhược điểm:</b> <span class="underline decoration-gray-400">{{ reportData.disadvantage }}</span></div>
        <div><b>Đề xuất thay đổi:</b> <span class="underline decoration-gray-400">{{ reportData.suggest }}</span></div>
      </div>
    </div>

    <el-drawer
      :title="`Học sinh: ${rowEditing.name}`"
      :visible.sync="drawer"
      direction="ttb">
      <div class="px-10">
        <div>
          <input id="attendant" type="radio" v-model="attendant" value="1">
          <label for="attendant"> {{ $t('teacher.attendant') }}</label>
        </div>
        <div class="mt-1">
          <input id="absence" type="radio" v-model="attendant" value="0">
          <label for="absence"> {{ $t('teacher.absent') }}</label>
        </div>
        <div v-if="attendant === '0'" class="mt-1">
          <span>{{ $t('teacher.absent_reason') }}</span><br>
          <input v-model="reason" class="border w-80 p-1">
        </div>
        <div class="mt-1.5">
          <button class="el-button el-button--success" @click="save">Lưu</button>
          </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { range, find, orderBy, cloneDeep } from "lodash";
import moment from 'moment/moment';

export default {
  data() {
    return {
      page: 1,
      perPage: 100,
      filters: {
        vip_level: undefined,
        name: undefined,
        type: undefined,
      },
      tab: null,
      drawer: false,
      attendant: "attendant",
      rowEditing: {},
      reason: ''
    };
  },
  computed: {
    ...mapGetters("classroom", ["classroom", "offlineExams"]),
    students() {
      if (this.classroom?.students?.data) {
        let data = cloneDeep(this.classroom?.students?.data);
        data.sort(function(a, b) {
          return a.group_user[0].created_at > b.group_user[0].created_at ? 1 : -1
        });
        return data;
      }
      return [];
    },
    course_time() {
      return orderBy(this.classroom.course_time, ['index'], ['asc']) || [];
    },
    tableData() {
      const students = this.students.map((s) => {
        const presents = s.course_time.filter((c) => c.status);
        const tmp = {
          id: s.id,
          name: s.name,
          absences: this.classroom.course_time.length - presents.length,
          presents: presents.length,
          note: s.group_user[0]?.note,
          ratio: (
            (presents.length * 100) /
            this.classroom.course_time.length
          ).toFixed(0),
        };
        for (const day in range(1, this.classroom.vip_session + 2, 1)) {
          const courseTime = this.course_time[day - 1];
          if (!courseTime) {
            tmp[`day_${day}`] = null;
          } else {
            const studentTime = find(s.course_time, [
              "course_time_id",
              courseTime.id,
            ]);
            tmp[`day_${day}`] = studentTime || null;
          }
        }
        return {
          ...tmp,
        };
      });
      const attendance = {
        id: -1,
        name: this.$t('teacher.learn_day'),
        absences: "",
        presents: "",
        note: "",
        ratio: "",
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        attendance[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(attendance);
      const zoom = {
        id: -2,
        name: this.$t('teacher.zoom_link'),
        absences: "",
        presents: "",
        note: "",
        ratio: "",
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        zoom[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(zoom);
      const password = {
        id: -3,
        name: this.$t('teacher.password'),
        absences: "",
        presents: "",
        note: "",
        ratio: "",
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        password[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(password);
      const note = {
        id: -4,
        name: this.$t('teacher.note'),
        absences: "",
        presents: "",
        note: "",
        ratio: "",
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        note[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(note);
      const note_cancel = {
        id: -5,
        name: this.$t('teacher.note_admin'),
        absences: "",
        presents: "",
        note: "",
        ratio: "",
      };
      for (const day in range(1, this.classroom.vip_session + 2, 1)) {
        note_cancel[`day_${day}`] = this.course_time[day - 1];
      }
      students.push(note_cancel);
      return students;
    },
    reportData () {
      if (!this.classroom.report) return {};
      return JSON.parse(this.classroom.report.data);
    },
    reportTableGeneral() {
      if (!this.classroom.report) return [];
      const data = JSON.parse(this.classroom.report.data);
      return [
        {
          content: 'Số buổi giáo viên xin nghỉ trong cả khóa',
          value: data.countDayOff,
          note: data.note1
        },
        {
          content: 'Tổng sĩ số của lớp',
          value: data.totalStudent,
          note: data.note2
        },
        {
          content: 'Sĩ số học sinh tham gia ở những buổi cuối',
          value: data.avgOnline,
          note: data.note3
        },
        {
          content: 'Tỷ lệ học sinh thi thử đỗ',
          value: data.totalPass,
          note: data.note4
        },
        {
          content: 'Số học sinh lớp đảm nhận học lên cấp độ cao hơn',
          value: data.totalUpgrade,
          note: data.note5
        },
        {
          content: 'Khác',
          value: data.other,
          note: data.note6
        }
      ];
    },
    reportTableFeeling() {
      if (!this.classroom.report) return [];
      const data = JSON.parse(this.classroom.report.data);
      return [
        {
          title: 'Cảm nhận về khóa học',
          content: data.feedback1
        },
        {
          title: 'Cảm nhận về học viên',
          content: data.feedback2
        },
        {
          title: 'Ưu và khuyết điểm của bản thân trong khóa học',
          content: data.feedback3
        },
        {
          title: 'Khác',
          content: data.feedback4
        }
      ];
    }
  },
  async mounted() {
    if (this.$route.query?.focus === 'report') {
      this.tab = 2;
    }
    this.setPage(this.$t("teacher.class_detail"));
    const data = {
      classId: this.$route.params?.id,
      page: 1,
      perPage: 100,
    };
    await this.getStudentByClass(data);
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    ...mapActions("classroom", ["getStudentByClass", 'saveCourseTime', 'updateAttendance', 'updateStudent']),
    async handleSizeChange(perPage) {
      const data = {
        classId: this.$route.params?.id,
        page: this.page,
        perPage: this.perPage,
      };
      await this.getStudentByClass(data);
    },
    async handleCurrentChange(perPage) {
      const data = {
        classId: this.$route.params?.id,
        page: this.page,
        perPage: this.perPage,
      };
      await this.getStudentByClass(data);
    },
    getScope(scope) {
      return "";
    },
    getMonthDate(date) {
      const result = date ? moment(date).format('DD-MM-YYYY') : '';
      return result ? result.slice(0, 5) : "";
    },
    copy(text) {
      navigator.clipboard.writeText(text);
    },
    async editDate(attendance, day) {
      if (day > 1) {
        if (!this.classroom.name.toLowerCase().includes('kaiwa') && this.course_time[day - 2] && this.course_time[day - 2].link === null) {
          this.$message({
            type: 'error',
            message: 'Bạn phải nhập link zoom ngày hôm trước đã'
          });
          return;
        }
      }
      const value = window.prompt('Nhập ngày', attendance && attendance.date_attendance ? moment(attendance.date_attendance).format('DD-MM-YYYY') : moment().format('DD-MM-YYYY'));
      if (value === null) {
        return;
      }

      const selectDay = moment(value, 'DD-MM-YYYY').format('YYYY-MM-DD')
      const today = moment().format('YYYY-MM-DD');
      if (selectDay > today) {
        this.$message({
          type: 'error',
          message: 'Không được chọn ngày trong tương lai'
        });
        return;
      }

      const d = moment().date();
      let lastDate = '';
      // Ngày 27 chốt lương nên hôm nay trước ngày 28 thì ngày được điểm danh là 26 tháng trước đến hôm nay
      // Hôm nay > 27 thì ngày được điểm danh sẽ là 26 tháng này đến ngày hôm nay
      if (d <= 27) {
        lastDate = moment().subtract(1, 'month').format("YYYY-MM");
        lastDate += '-26';
      } else {
        lastDate = moment().format("YYYY-MM");
        lastDate += '-26';
      }

      if (selectDay < lastDate) {
        this.$message({
          type: 'error',
          message: 'Không thể chọn ngày này đã chốt lương'
        });
        return;
      }

      const data = {
        id: attendance ? attendance.id : null,
        day,
        date_attendance: selectDay,
        group_id: this.classroom.id,
        teacher_id: this.classroom.group_teacher.teacher_id,
      }
      await this.updateAttendance(data);
    },
    async editNote(attendance) {
      const value = window.prompt('Nhập ghi chú', attendance.note || '');
      if (value === null) {
        return;
      }
      const data = {
        id: attendance.id,
        note: value,
      }
      await this.updateAttendance(data);
    },
    async editPassword(attendance) {
      const value = window.prompt('Nhập password', attendance.password || '');
      if (value === null) {
        return;
      }
      const data = {
        id: attendance.id,
        password: value,
      }
      await this.updateAttendance(data);
    },
    async editLink(attendance) {
      const value = window.prompt('Nhập link', attendance.link || '');
      if (value === null) {
        return;
      }
      const data = {
        id: attendance.id,
        link: value,
      }
      await this.updateAttendance(data);
    },
    showPass(pass){
      alert('Mật khẩu:' + pass);
    },
    editCourseTime(row, day) {
      if (row.id < 0) {
        return;
      }
      this.rowEditing = { ...row, day };
      this.attendant = row[`day_${day}`] ? row[`day_${day}`].status.toString() : '1';
      this.reason = row[`day_${day}`] ? row[`day_${day}`].note : '';
      this.drawer = true;
    },
    save() {
      const courseTimeElelemt = this.course_time[this.rowEditing.day - 1];
      if (!courseTimeElelemt || !courseTimeElelemt.id) {
        return;
      }
      this.saveCourseTime({
        courseTimeId: courseTimeElelemt.id,
        studentId: this.rowEditing.id,
        status: parseInt(this.attendant),
        reason: this.reason
      });
      this.drawer = false;
      this.attendant = 'attendant';
      this.reason = '';
      this.rowEditing = {};
      this.reason = '';
    },
    editStudentNote(student) {
      this.$prompt('Nhập note', 'Nhập note', {
        inputValue: student.note || '',
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        inputType: 'textarea',
      }).then(({ value }) => {
        const data = {
          userId: student.id,
          groupId: this.classroom.id,
          note: value,
        }
        this.updateStudent(data);

        this.$message({
          type: 'success',
          message: 'Thành công'
        });
      }).catch(() => {
      });
    }
  },
};
</script>
<style scoped>
/*.el-table__cell {*/
/*  display: flex;*/
/*  justify-content: center;*/
/*  align-items: center;*/
/*}*/
.cell {
  text-align: center;
}
</style>
