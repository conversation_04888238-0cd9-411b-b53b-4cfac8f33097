<template>
  <el-tabs type="border-card">
    <el-tab-pane :label="$t('teacher.basic_information')">
      <div>
        <el-form label-width="120px">
          <el-form-item :label="$t('teacher.fullname')">
            <el-input v-model="user.user_name" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('teacher.email')">
            <el-input v-model="user.email" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('teacher.phone')">
            <el-input v-model="user.phone" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('teacher.birthday')">
            <el-input v-model="user.birth_day" disabled></el-input>
          </el-form-item>
          <el-form-item :label="$t('teacher.gender')" class="w-full">
            <el-select v-model="user.sex" disabled class="w-full">
              <el-option :value="1" label="Nam"></el-option>
              <el-option :value="0" label="Nữ"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-tab-pane>
    <el-tab-pane :label="$t('teacher.salary_report')">
      <div>
        <div v-if="user.note_by_accountant" class="my-5">
          {{ user.note_by_accountant }}
        </div>
        <div>
          <el-radio v-model="timeline" label="last_month" border>{{ $t('teacher.last_month') }}</el-radio>
          <el-radio v-model="timeline" label="now" border>{{ $t('teacher.this_month') }}</el-radio>
        </div>
        <el-table :data="user.salaries" style="width: 100%">
<!--          <el-table-column prop="id" label="Tên"></el-table-column>-->
          <el-table-column prop="group.name" :label="$t('teacher.teacher_class')"></el-table-column>
          <el-table-column prop="work_days" :label="$t('teacher.work_days')">
            <template slot-scope="scope">
              {{ scope.row.work_days.length || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="base_salary" :label="$t('teacher.total_salary')">
            <template slot-scope="scope">
              {{ ((scope.row.work_days.length || 0) * scope.row.base_salary) | decimal }}
              {{ scope.row.currency | currency }}
            </template>
          </el-table-column>
          <el-table-column prop="rewards" :label="$t('teacher.rewards')">
            <template slot-scope="scope">
              {{ scope.row.rewards | decimal }} {{ scope.row.currency | currency }}
            </template>
          </el-table-column>
          <el-table-column v-if="timeFrames === '26,25'" prop="faults" :label="$t('teacher.faults')">
            <template slot-scope="scope">
              {{ scope.row.faults | decimal }} {{ scope.row.currency | currency }}
            </template>
          </el-table-column>
          <el-table-column prop="currency" :label="$t('teacher.tax')">
            <template slot-scope="scope">
              {{ scope.row.currency === 'yen' ? Math.round(((scope.row.work_days.length * scope.row.base_salary +
                            scope.row.ref_rewards +
                            scope.row.rewards -
                            scope.row.faults) *
                        (user.tax?.value || 0)) /
                    100) :
                (Math.round(
                        ((scope.row.work_days.length * scope.row.base_salary +
                                scope.row.ref_rewards +
                                scope.row.rewards -
                                scope.row.faults) *
                            (user.tax?.value || 0)) /
                        100 /
                        1000
                    ) *
                    1000)
                | decimal
              }}
              {{ scope.row.currency | currency }}
            </template>
          </el-table-column>
          <el-table-column prop="base_salary" :label="$t('teacher.total_paid')">
            <template slot-scope="scope">
                {{
                  ((
                          scope.row.work_days.length * scope.row.base_salary +
                          scope.row.ref_rewards +
                          scope.row.rewards -
                          scope.row.faults
                      ).toFixed(0) -
                      (scope.row.currency === 'yen' ? Math.round(((scope.row.work_days.length * scope.row.base_salary +
                                      scope.row.ref_rewards +
                                      scope.row.rewards -
                                      scope.row.faults) *
                                  (user.tax?.value || 0)) /
                              100) :
                          (Math.round(
                                  ((scope.row.work_days.length * scope.row.base_salary +
                                          scope.row.ref_rewards +
                                          scope.row.rewards -
                                          scope.row.faults) *
                                      (user.tax?.value || 0)) /
                                  100 /
                                  1000
                              ) *
                              1000)
                          | decimal))
                      | decimal
                }}
                {{ scope.row.currency | currency }}
            </template>
          </el-table-column>
          <el-table-column prop="note" :label="$t('teacher.note')"></el-table-column>
        </el-table>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { find, cloneDeep, orderBy } from "lodash";
import axios from "axios";
import moment from "moment";
export default {
  name: "Profile",
  data() {
    return {
      user: user,
      timeline: 'last_month'
    };
  },
  computed: {
    ...mapGetters("ui", ["title"]),
    ...mapGetters("payoff", ["payoffList"]),
    timeFrames() {
      if (this.user.salaries.length === 0) {
        return '26,25'
      } else if (parseInt(this.user.salaries[0].type) === 1) {
        return '26,25'
      } else {
        return 'monthly'
      }
    },
  },
  watch: {
    timeline() {
      this.getTeacherSalary();
    }
  },
  methods: {
    ...mapActions("ui", ["setPage"]),
    ...mapActions("payoff", ["getPayoffList"]),
    decimal(row, column) {
      return new Intl.NumberFormat("vi-VN").format(row[column.property]);
    },
    async getTeacherSalary() {
      const res = await axios.get(
          window.location.origin + `/backend/school/api/v1/user/${this.user.id}/salary?timeline=${this.timeline}`
      );
      const user = res.data.data
      user.name = `${user.last_name} ${user.first_name}`;
      user.base_salary = 0;
      user.latest_increase = null;
      user.work_days_count = user.work_days.length || 0;
      user.work_days_salary = user.work_days_count * user.base_salary;
      user.rewards = 0;
      user.faults = 0;
      user.latest_salaries.forEach((salary) => {
        const check = find(user.salaries, function (o) {
          return o.id === salary.id;
        });
        if (!check) user.salaries.push(salary);
      });
      let workDays = cloneDeep(user.work_days);
      let salaries = orderBy(
          cloneDeep(user.salaries),
          ["level", "valid_from"],
          ["asc", "desc"]
      );
      salaries = salaries.map((salary) => {
        salary.work_days = workDays.filter((day) => {
          return (
              day.group &&
              day.group_id == salary.group_id &&
              moment(day.date_attendance).isSameOrAfter(salary.valid_from)
          );
        });
        const ids = salary.work_days.map((o) => o.id);
        workDays = workDays.filter((o) => !ids.includes(o.id));
        salary.ref_rewards = 0;
        salary.rewards = 0;
        salary.faults = 0;
        salary.work_days.forEach((day) => {
          if (day.ref_reward) {
            salary.ref_rewards += day.ref_reward;
          }
          if (day.reward) {
            const reward = find(this.payoffList, ["id", day.reward]);
            if (reward) {
              salary.rewards +=
                  salary.currency === "vnd" ? reward.price : reward.yen_price;
            }
          }
          if (day.fault) {
            const fault = find(this.payoffList, ["id", day.fault]);
            if (fault) {
              salary.faults +=
                  salary.currency === "vnd" ? fault.price : fault.yen_price;
            }
          }
        });
        return salary;
      });
      user.salaries = orderBy(
          cloneDeep(salaries),
          ["level", "valid_from"],
          ["asc", "asc"]
      );
      user.salary = user.work_days_salary + user.rewards - user.faults;
      user.fixed_salary = user.salary.toFixed(0);
      // đếm số lượng nhóm
      let countGroup = 0;
      if (user.groups) {
        user.groups.forEach((item) => {
          if (item.group) {
            countGroup += 1;
          }
        });
      }
      user.totalGroup = countGroup;
      user.joinDate = user.join_date;
      user.seniorityMonth = this.differenceTime(user.join_date);
      this.user = user

      console.log(this.user)
    },
    differenceTime(joinDate) {
      var currentTime = moment();
      var joinTime = moment(joinDate);

      var diffMonths = currentTime.diff(joinTime, "months");

      if (diffMonths) {
        return diffMonths + " Tháng";
      }
      return "--";
    },
  },
  mounted() {
    this.setPage(this.$t('teacher.teacher_information'));
    this.getPayoffList();
    this.getTeacherSalary();
  },
};
</script>
