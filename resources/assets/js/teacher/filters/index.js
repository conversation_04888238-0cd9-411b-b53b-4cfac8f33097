import Vue from "vue";
import moment from "moment/moment";

const decimal = Vue.filter("decimal", (number) => {
  return new Intl.NumberFormat("vi-VN").format(number);
});
const dateTimeToDate = Vue.filter("dateTimeToDate", (time) => {
  return moment(time).format("DD-MM-YYYY");
});

const currency = Vue.filter("currency", (currency) => {
  const currencies = [
    { value: "vnd", label: "đ" },
    { value: "yen", label: "¥" },
  ];
  return currencies.find((o) => o.value === currency)?.label || "đ";
});
export default {
  decimal,
  currency,
};
