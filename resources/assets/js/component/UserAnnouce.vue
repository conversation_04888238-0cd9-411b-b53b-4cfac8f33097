<template>
  <div>
    <div class="dropdown-toggle header-notification">
      <div class="dropdown-toggle text-white hover:text-dmr-green-dark" type="button" data-toggle="dropdown" data-auto-close="outside" aria-expanded="false" @click="toggleNotice">
        <span v-if="countNotification > 0" class="badge--red dropdown-toggle-icon" style="background: #EF6D13 !important; color: white !important">{{ countNotification }}</span>
        <svg v-on="countNotification > 0 ? {click : updateReadedAnnouce} : {}" width="18" height="20" viewBox="0 0 19 23" fill="none" xmlns="http://www.w3.org/2000/svg"
             class="dropdown-toggle-icon stroke-current"
             style="cursor: pointer;"
        >
        <path stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M10.1,1.4c3.5.3,6.2,3.2,6.2,6.7h0v7.1h.2c.7,0,1.2.5,1.2,1.2h0c0,.8-.5,1.4-1.2,1.4H2.5c-.7,0-1.2-.5-1.2-1.2h0c0-.8.5-1.4,1.2-1.4h.2v-7.1c0-3.5,2.7-6.4,6.2-6.7h1.2Z"/>
        <path stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M11.2,19.9c0,1-.8,1.7-1.7,1.7s-1.7-.8-1.7-1.7"/>
        </svg>
      </div>
      <div v-if="showNotice" class="dropdown-menu announce-menu" v-click-outside="closeNotice">
        <div class="annouce-popup">
          <div class="annouce-header">
            <div class="ann-header-left">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M15.5094 9.57517L14.4617 8.18298C14.4687 8.05642 14.4617 7.92282 14.4617 7.82439L15.5094 6.42517C15.5615 6.35707 15.5969 6.27771 15.6128 6.19346C15.6286 6.1092 15.6245 6.02239 15.6008 5.94001C15.4236 5.29177 15.1661 4.6682 14.8344 4.08376C14.7916 4.00952 14.7328 3.94576 14.6622 3.8971C14.5917 3.84845 14.5112 3.81614 14.4266 3.80251L12.7039 3.55642L12.4437 3.29626L12.1977 1.5736C12.1839 1.48993 12.1523 1.41019 12.105 1.33982C12.0577 1.26945 11.9957 1.2101 11.9234 1.16579C11.3359 0.835265 10.7102 0.577883 10.0602 0.399386C9.97777 0.375639 9.89097 0.371538 9.80671 0.387412C9.72245 0.403286 9.64309 0.438694 9.575 0.490792L8.18281 1.53142H7.81719L6.425 0.490792C6.3569 0.438694 6.27754 0.403286 6.19329 0.387412C6.10903 0.371538 6.02223 0.375639 5.93984 0.399386C5.2916 0.576587 4.66803 0.834048 4.08359 1.16579C4.00935 1.20859 3.94559 1.26741 3.89694 1.33795C3.84828 1.4085 3.81597 1.489 3.80234 1.5736L3.55625 3.29626L3.29609 3.55642L1.57344 3.80251C1.48883 3.81614 1.40833 3.84845 1.33778 3.8971C1.26724 3.94576 1.20843 4.00952 1.16562 4.08376C0.83388 4.6682 0.57642 5.29177 0.399218 5.94001C0.375471 6.02239 0.37137 6.1092 0.387244 6.19346C0.403118 6.27771 0.438526 6.35707 0.490624 6.42517L1.53828 7.81735V8.17595L0.490624 9.57517C0.438526 9.64326 0.403118 9.72262 0.387244 9.80688C0.37137 9.89113 0.375471 9.97794 0.399218 10.0603C0.57642 10.7086 0.83388 11.3321 1.16562 11.9166C1.20843 11.9908 1.26724 12.0546 1.33778 12.1032C1.40833 12.1519 1.48883 12.1842 1.57344 12.1978L3.29609 12.4439L3.55625 12.7041L3.80234 14.4267C3.81597 14.5113 3.84828 14.5918 3.89694 14.6624C3.94559 14.7329 4.00935 14.7917 4.08359 14.8345C4.66803 15.1663 5.2916 15.4237 5.93984 15.6009C5.98783 15.6148 6.03754 15.6219 6.0875 15.622C6.20946 15.6236 6.32838 15.584 6.425 15.5095L7.81719 14.4689H8.18281L9.575 15.5095C9.64309 15.5616 9.72245 15.597 9.80671 15.6129C9.89097 15.6288 9.97777 15.6247 10.0602 15.6009C10.7088 15.425 11.3326 15.1675 11.9164 14.8345C11.9906 14.7917 12.0544 14.7329 12.1031 14.6624C12.1517 14.5918 12.184 14.5113 12.1977 14.4267L12.4437 12.697C12.5281 12.6127 12.6266 12.5213 12.6969 12.4439L14.4266 12.1978C14.5112 12.1842 14.5917 12.1519 14.6622 12.1032C14.7328 12.0546 14.7916 11.9908 14.8344 11.9166C15.1661 11.3321 15.4236 10.7086 15.6008 10.0603C15.6245 9.97794 15.6286 9.89113 15.6128 9.80688C15.5969 9.72262 15.5615 9.64326 15.5094 9.57517ZM8 11.0939C7.38811 11.0939 6.78997 10.9125 6.2812 10.5725C5.77244 10.2326 5.37591 9.7494 5.14175 9.18409C4.90759 8.61878 4.84632 7.99673 4.96569 7.39661C5.08507 6.79648 5.37972 6.24522 5.81239 5.81256C6.24506 5.37989 6.79631 5.08524 7.39644 4.96586C7.99657 4.84649 8.61862 4.90776 9.18393 5.14191C9.74924 5.37607 10.2324 5.77261 10.5724 6.28137C10.9123 6.79014 11.0937 7.38828 11.0937 8.00017C11.0937 8.82068 10.7678 9.60759 10.1876 10.1878C9.60742 10.768 8.82051 11.0939 8 11.0939Z"
                    fill="#333333"/>
              </svg>
              <a>Thông báo</a>
            </div>
            <div class="ann-header-right">
              <span v-on:click="markAsReadedAll()">Đánh dấu tất cả đã đọc</span>
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M13 7C13 3.6875 10.3125 1 7 1C3.6875 1 1 3.6875 1 7C1 10.3125 3.6875 13 7 13C10.3125 13 13 10.3125 13 7Z"
                    stroke="#1A1F36" stroke-width="1.5" stroke-miterlimit="10"/>
                <path d="M9.99994 4.50049L5.79994 9.50049L3.99994 7.50049" stroke="#1A1F36" stroke-width="1.5"
                      stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <div v-if="list.length" class="annouce-container">
            <div class="annouce-item" v-for="item in list" :key="'notification' + item.id" :id="'noti-' + item.id" v-bind:class="[item.readed == 0 ? 'unread' : '']">
              <a :href="locationUrl + item.link" v-on="item.readed == 0 ?{click: () => markAsReaded (item.id)}:{}">
                <div v-if="item.sender_id != 0" class="ann-item-img">
                  <img v-if="item.avatar" :src="url + item.avatar" style="border: 1px solid gray">
                  <img v-else :src="locationUrl + 'assets/img/default-avatar.jpg'" style="border: 1px solid gray">
                </div>
                <div v-else class="ann-item-icon">
                  <img :src="locationUrl +'assets/img/dmr_annouce.png'" style="border: 1px solid gray">
                </div>
                <div class="ann-item-content">
                  <p class="ann-title">
                    <span v-if="item.table_name == 'community_posts'">
                      <span v-if="item.type != 'community_create_post'">
                        <b>{{item.userName}}</b>
                      </span>
                      <span v-if="item.type == 'community_like_owner'">
                        đã thích bài viết của bạn
                      </span>
                      <span v-else-if="item.type == 'community_comment'">
                       đã bình luận trên bài đăng của bạn
                      </span>
                      <span v-else-if="item.type == 'community_like_comment'">
                        đã thích bình luận của bạn trong một bài viết
                      </span>
                      <span v-else-if="item.type == 'community_reply'">
                       trả lời bình luận của bạn trong một bài viết
                      </span>
                      <span v-else-if="item.type == 'community_create_post'">
                        {{ item.title }}
                      </span>
                    </span>
                    <span v-else-if="item.table_name == 'invoice'">
                      Bạn đã đăng ký <b>mua sách</b> với mã đơn hàng <b>{{JSON.parse(item.data).uuid}}</b>
                    </span>
                    <span v-else>
                      {{ item.title }}
                    </span>
                  </p>
                  <span class="ann-create-time" style="display: block">{{ item.created_at }}</span>
                </div>
              </a>
            </div>
            <infinite-loading @infinite="infiniteHandler">
              <span slot="no-more" style="padding: 10px 0;display: inline-block">
                 Hết thông báo
              </span>
            </infinite-loading>
          </div>
          <div v-else class="annouce-container">
            <div style="display:flex;justify-content: center;align-items: center;height: 100%">Không có thông báo</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import InfiniteLoading from 'vue-infinite-loading';

var api = window.location.origin + '/account/announces';

export default {
  components: {
    InfiniteLoading,
  },
  data() {
    return {
      page: 1,
      list: [],
      countNotification: 0,
      countUnreadMess: 0,
      url: window.location.origin + '/cdn/avatar/small/',
      locationUrl: window.location.origin + '/',
      title: document.title,
      isActive: true,
      showNotice: false,
    };
  },
  watch: {
    showNotice: function(value) {
      if (value && !this.list.length) {
        this.infiniteHandler();
      }
    },
  },
  computed:{
    getLength(){
      return this.list.length;
    }
  },
  methods: {
    infiniteHandler($state) {
      var vm = this;
      axios.get(api, {
        params: {
          page: this.page,
        },
      }).then(function (response) {
        if (response.data.data.length) {
          vm.page += 1;

          vm.list =  vm.list.concat(response.data.data);

          if (!$state) return;
          $state.loaded();
        } else if ($state) {
          $state.complete();
        }
      });
    },

    /* đánh dấu đã đọc 1 thông báo */
    markAsReaded: function (id) {
      var vm = this;
      $.get(window.location.origin + '/api/notifications/mark-as-readed?id=' + id).then(function (res) {
        vm.list.map(function (item) {
          if (item.id == id) {
            item.readed = 1;
          }
          return item;
        })
      });
    },

    /* đánh dấu đã đọc tất cả */
    markAsReadedAll: function () {
      var vm = this;
      $.get(window.location.origin + '/api/notifications/mark-all-readed').then(function (res) {
        vm.list.map(function (item) {
            item.readed = 1;
            return item;
         })
      });
    },

    /* cập nhập thông báo trên title*/
    updateTitle: function () {
      var tmp = this.countNotification;
      if (tmp > 0) {
        document.title = '(' + this.countNotification; + ') ' + this.title;
      }
      else {
        document.title = this.title;
      }
    },

    /* cập nhập biến thông báo mới */
    updateReadedAnnouce: function (){
      var vm = this;
      axios.get(window.location.origin + '/account/update-announce').then(function (res) {
        if(res.status == 200){
          vm.countNotification = 0;
        }
      }).catch(function(error) {
        console.log('is error', error);
      });
    },
    toggleNotice: function() {
      var vm = this;
      this.showNotice = !this.showNotice;
    },
    closeNotice: function($event) {
      if ($event.target.classList.contains('dropdown-toggle-icon')) return;
      if (this.showNotice) this.showNotice = false;
    },
  },
  mounted() {
    var vm = this;
    vm.countNotification = parseInt(countNoti);
  }
}
</script>
