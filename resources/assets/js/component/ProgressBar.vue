<template>
  <div>
    <div class="progress-bar" :style="{height: height, background: background, width: widthPercent}">
      <div class="progress-bar-inner" :style="{width: computedWidth, background: color}"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    percent: {
      type: [Number, String],
      default: 0,
      validator: (value) => {
        const num = parseFloat(value);
        return !isNaN(num) && num >= 0 && num <= 100;
      },
    },
    widthPercent: {
      type: String,
      default: "120px",
    },
    color: {
      type: String,
      default: "#57D061",
    },
    height: {
      type: String,
      default: "10px",
    },
    background: {
      type: String,
      default: "white",
    },
  },
  data() {
    return {
      width: "0%",
      height: "10px",
      background: "white",
      color: "#57D061",
      widthPercent: "120px",
    };
  },
  computed: {
    computedWidth() {
      // <PERSON><PERSON><PERSON> bảo percent là số và thêm đơn vị %
      const percentValue = parseFloat(this.percent);
      return isNaN(percentValue) ? "0%" : `${percentValue}%`;
    },
  },
};
</script>

<style scoped>

.progress-bar {
  flex: 1;
  margin: 0 15px;
  position: relative;
  border-radius: 9999px;
  overflow: hidden;
  box-shadow: none;
}

.progress-bar-inner {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  border-radius: 9999px;
  transition: width 0.6s ease;
  box-shadow: none;
}
</style>
