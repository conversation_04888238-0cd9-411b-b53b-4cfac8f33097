import Vue from 'vue';
import 'es6-promise/auto'
import axios from 'axios';
import VueAxios from 'vue-axios';
import VueRouter from 'vue-router';
import ElementUI from 'element-ui'

import 'element-ui/lib/theme-chalk/index.css';
import locale from 'element-ui/lib/locale/lang/vi'
// resources
import store from './teacher/store';
import routes from './teacher/routes';
import App from './teacher/pages/App';

import filters from './teacher/filters';

import VueI18n from 'vue-i18n';
import vnMessage from './lang/vn.json';
import jpMessage from './lang/jp.json';

Vue.use(VueI18n);
const messages = {
  vn: vnMessage,
  jp: jpMessage,
};
const i18n = new VueI18n({
  locale: 'vn',
  messages,
  fallbackLocale: 'vn',
});

const router = new VueRouter({
  base: 'teacher/',
  history: true,
  mode: 'history',
  routes,
})
Vue.router = router
Vue.use(VueRouter)

// axios request
Vue.use(VueAxios, axios);
axios.defaults.baseURL = `${process.env.APP_URL}/backend/school/api/v1`;

// element ui
Vue.use(ElementUI, { locale })
// components
Vue.component('app', App);

const app = new Vue({
  el: '#teacherApp',
  i18n,
  router,
  store,
  filters
});
app.$mount('#teacherApp');
