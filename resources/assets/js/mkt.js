import Vue from 'vue';
import 'es6-promise/auto'
import axios from 'axios';
import VueAxios from 'vue-axios';
import VueRouter from 'vue-router';
import ElementUI from 'element-ui'

import 'element-ui/lib/theme-chalk/index.css';
import locale from 'element-ui/lib/locale/lang/vi'
// resources
import store from './mkt/store';
import routes from './mkt/routes';
import App from './mkt/pages/App';

const router = new VueRouter({
  base: 'backend/mkt/',
  history: true,
  mode: 'history',
  routes,
})
Vue.router = router
Vue.use(VueRouter)

// axios request
Vue.use(VueAxios, axios);
axios.defaults.baseURL = `${process.env.APP_URL}/backend/mkt/api/v1`;

// element ui
Vue.use(ElementUI, { locale })
// components
Vue.component('app', App);

const app = new Vue({
  el: '#mktApp',
  router,
  store
});
app.$mount('#mktApp');
