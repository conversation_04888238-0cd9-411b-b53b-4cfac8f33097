Vue.component('lesson-path-panel', {
    template: '#lesson-paths-panel-template',

    props: ['course', 'period', 'permission'],

    data: function () {
        return {
            url: window.location.origin,
            uploadUrl: window.location.origin + '/backend/upload-image',
            currentIllus: '',
            paths: [],
            selectedPath: {},
            loading: false,
            showModal: false,
            showImgModal: false,
            draggable: false,
            formData: {
                id: undefined,
                title: '',
                img: '',
                course_id: 0,
                period: 1,
                status: 1,
                iconImg: null
            }
        };
    },
    computed: {
        illustrators: function () {
            if (!this.selectedPath || !this.selectedPath.illustrators) return [];
            return JSON.parse(this.selectedPath.illustrators);
        },
        csrf: function() {
            return $('meta[name="csrf-token"]').attr('content');
        },
    },

    watch: {
        period: function (period) {
            if (period) {
                var vm = this;
                vm.formData.period = vm.period.id;
                vm.getPathsByPeriod(this.course.id, period.id);
            }
        },
        course: function (course) {
            if (course) {
                var vm = this;
                vm.formData.course_id = course.id;
                // vm.getPathsByPeriod(course, vm.period);
            }
        }
    },

    mounted: function () {
        var vm = this;
    },

    methods: {
        onDragEnd: function () {
            var vm = this;
            var ids = vm.paths.map(function (path) {
                return path.id;
            });
            var data = {
                course_id: vm.course.id,
                periodId: vm.period.id,
                ids: ids
            };
            $.post(vm.url + '/backend/adventure/paths/sort', data, function (res) {
                if (res.code === 200) {
                    vm.paths = res.data.map(function (path) {
                        return path;
                    })
                }
            })
        },
        onDragIllEnd: function() {
          this.saveIll();
        },
        changeStatus: function (path, status) {
            var vm = this;
            var data = {
                id: path.id,
                status: status
            };
            $.post(vm.url + '/backend/adventure/paths/change-status', data, function (res) {
                if (res.code === 200) {
                    vm.paths = vm.paths.map(function (item) {
                        if (item.id === res.data.id) {
                            item.status = res.data.status;
                        }
                        return item;
                    })
                }
            })
        },
        changeFormIcon: function (event) {
            var vm = this;
            vm.formData.iconImg = event.target.files[0];
        },
        saveForm: function () {
            var vm = this;

            var data = new FormData();

            data.append('id', vm.formData.id);
            data.append('title', vm.formData.title);
            data.append('img', vm.formData.img);
            data.append('course_id', vm.formData.course_id);
            data.append('period', vm.period.id);
            data.append('status', vm.formData.status);
            data.append('iconImg', vm.formData.iconImg);
            if (vm.formData.id) {
                vm.updatePath(data);
            } else {
                vm.addPath(data);
            }
        },
        editImg: function (path) {
            this.selectedPath = path;
            this.showImgModal = true;
        },
        editPath: function (path) {
            var vm = this;

            vm.formData = {
                ...vm.formData,
                id: path.id,
                title: path.title,
                img: path.img,
                course_id: path.course_id,
                period: vm.period.id,
                status: path.status
            };
            vm.showModal = true;
        },
        addPath: function (data) {
            var vm = this;

            $.ajax({
                url: vm.url + '/backend/adventure/paths/add',
                type: "POST",
                data: data,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.code === 200) {
                        if (res.data.period_id === vm.period.id) {
                            vm.paths.push(res.data);
                        }
                        vm.closeModal();
                    } else {
                        alert(res.msg)
                    }
                }
            });
        },
        updatePath: function (data) {
            var vm = this;

            $.ajax({
                url: vm.url + '/backend/adventure/paths/update',
                type: "POST",
                data: data,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.code === 200) {
                        vm.paths = vm.paths.map(function (path) {
                            if (path.id === res.data.id) {
                                path = res.data
                            }
                            return path;
                        });
                        vm.closeModal();
                    } else {
                        alert(res.msg)
                    }
                }
            });
        },
        deletePath: function (path) {
            var vm = this;

            var confirm = window.confirm('Xác nhận xoá chặng cùng với toàn bộ bài học trong chặng?');
            if (confirm) {
                var data = {
                    id: path.id
                };
                $.post(vm.url + '/backend/adventure/paths/destroy', data, function (res) {
                    if (res.code === 200) {
                        vm.paths = vm.paths.filter(function (item) {
                            return item.id !== path.id;
                        });
                        if (vm.selectedPath.id === path.id) {
                            if (vm.paths.length > 0) {
                                vm.selectPath(vm.paths[0])
                            } else {
                                vm.selectUncategorized();
                            }
                        }
                    }
                })
            }
        },
        setFormStatus: function (event) {
            var vm = this;
            vm.formData.status = parseInt(event.target.value);
        },
        closeModal: function () {
            var vm = this;
            vm.formData = {
                ...vm.formData,
                id: undefined,
                title: '',
                img: '',
                course_id: vm.course.id,
                period: vm.period.id,
                status: 1,
                iconImg: null
            };
            vm.showModal = false;
        },
        closeImgModal: function () {
            var vm = this;
            vm.showImgModal = false;
        },
        getPathsByPeriod: function (courseId, period) {
            var vm = this;
            var data = {
                courseId: courseId,
                periodId: period
            };
            vm.loading = true;
            $.get(vm.url + '/backend/adventure/paths', data, function (res) {
                if (res.code === 200) {
                    vm.paths = [...res.data];
                    vm.selectedPath = vm.paths[0] || {};
                    vm.$emit('update:path', vm.paths[0] || {});
                    vm.loading = false;
                }
            })
        },
        selectPath: function (path) {
            var vm = this;
            vm.selectedPath = path;
            vm.$emit('update:path', path);
        },
        selectUncategorized: function () {
            var vm = this;
            vm.selectedPath = {
                course_id: vm.course.id,
                img: "",
                id: 0,
                period: vm.period.id,
                status: 1,
                title: "Chưa được phân loại",
            };
            vm.$emit('update:path', vm.selectedPath)
        },
        addIll: function() {
            console.log(this.selectedPath.illustrator);
            this.selectedPath.illustrator.push({
                d: 'Mô tả',
                i: '',
                n: 'Hình minh hoạ',
            })
            this.saveIll();
        },
        removeIll: function (index) {
            this.selectedPath.illustrator.splice(index, 1);
            this.saveIll();
        },
        saveIll: function () {
            const data = {
                id: this.selectedPath.id,
                illustrator: this.selectedPath.illustrator,
            };
            axios.post('/backend/adventure/paths/update-illustrator', data).then(function (res) {
                console.log(toastr)
                toastr.info('Cập nhật thành công', {timeOut: 3000})
            }).catch(function () {
                toastr.error('Cập nhật thất bại', {timeOut: 3000})
            })
        },
        handleImageUploadSuccess: function(index, res, file) {
            this.$set(this.selectedPath.illustrator[index], 'i', res.data);
            this.saveIll();
        },
        beforeImageUpload: function(file) {
            const isLt5M = file.size / 1024 / 1024 < 5;

            if (!isLt5M) {
                this.$message.error('Avatar picture size can not exceed 2MB!');
            }
            return isLt5M;
        },
    },
});
