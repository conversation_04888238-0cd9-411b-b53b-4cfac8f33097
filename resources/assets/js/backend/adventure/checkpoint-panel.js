Vue.component('lesson-checkpoint-panel', {
    template: '#lesson-checkpoint-panel-template',

    props: ['course', 'path', 'permission'],

    data: function () {
        return {
            url: window.location.origin,
            groups: [],
            lessons: [],
            checkpoints: {},
            checkpointsByKey: [],
            choseLessons: [],
            choseType: undefined,
            editingKey: undefined,
            keyToChange: undefined,
            categories: [],
            selectedCategoryId: undefined,
            selectedGroupId: undefined,
            editingCheckpoint: {},
            showingGroup: {},
            loading: false,
            showModal: false,
            showCheckpointModal: false,
            draggable: false,
            playbackRate: [0.75, 1, 1.25, 1.5, 2],
            formData: {
                id: undefined,
                lesson_id: 0,
                path_id: 1,
                key: 1,
                status: 1,
            }
        };
    },
    computed: {
        checkpointList: function() {
          return _.omitBy(this.checkpoints, _.isEmpty);
        },
    },
    watch: {
        showModal: function (val) {
            if (val) {
                this.getCategories();
            }
            this.getGroupLessons();
        },
        selectedCategoryId: function(val) {
            if (val) {
                this.getGroups();
            }
        },
        selectedGroupId: function(val) {
            if (val) {
                this.getGroupLessons();
            }
        },
        path: {
            deep: true,
            immediate: true,
            handler: function(path) {
                if (path.id) {
                    this.getCheckpoints();
                }
            }
        }
    },
    mounted: function () {
        var vm = this;
    },

    methods: {
        getCategories: function() {
            var vm = this;
            var data = {
                courseId: this.course.id,
            };
          $.post(this.url + '/backend/lesson-categories', data, function (res) {
              if (res.code == 200) {
                  vm.categories = res.data;
                  vm.selectedCategoryId = res.data[0].id;
              }
          });
        },
        getCheckpoints: function() {
            var vm = this;
            var data = {
                pathId: this.path.id,
            };
            $.get(this.url + '/backend/adventure/checkpoints', data, function (res) {
                if (res.code == 200) {
                    vm.checkpoints = res.data;
                }
            });
        },
        getGroups: function() {
            var vm = this;
            var data = {
                id: this.selectedCategoryId,
            };
            $.post(this.url + '/backend/lesson-groups', data, function (res) {
                if (res.code == 200) {
                    vm.groups = res.data.groups;
                    vm.selectedGroupId = res.data.groups[0].id;
                }
            });
        },
        getGroupLessons: function() {
            var vm = this;
            var data = {
                pathId: this.path.id,
                id: this.selectedGroupId,
            };
            $.post(this.url + '/backend/lesson-groups/get-reserved-lessons', data, function (res) {
                if (res.code == 200) {
                    vm.lessons = res.data;
                    if (res.data.length > 0) vm.formData.lesson_id = res.data[0].id;
                }
            });
        },
        openCheckpointForm: function() {
            // this.formData = {
            //   id: undefined,
            //   lesson_id: undefined,
            //   path_id: this.path.id,
            //   key: 1,
            //   status: 1,
            // }
            this.showModal = true;
        },
        printLessonIcon: function (type) {
            var vm = this;
            var imgName = '';
            switch (type) {
                case 'docs':
                    imgName = 'docb.png';
                    break;
                case 'video':
                    imgName = 'videob.png';
                    break;
                case 'test':
                    imgName = 'quizb.png';
                    break;
                case 'flashcard':
                    imgName = 'fcb.png';
                    break;
                default:
                    imgName = 'docb.png';
            }
            return imgName;
        },
        changeLessonSpeed: function(event, lesson) {
            var vm = this;
            var data = {
                id: lesson.id,
                speed: event.target.value,
            };
            $.post(vm.url + '/backend/lesson-groups/change-lesson-speed', data, function (res) {
                if (res.code === 200) {
                    vm.lessons = vm.lessons.map(function (item) {
                        if (item.id === res.data.id) {
                            item.default_speed = res.data.default_speed;
                        }
                        return item;
                    })
                } else {
                    alert('Thất bại')
                }
            })
        },
        changeLessonType: function () {
            var vm = this;
            if (vm.choseType && vm.choseLessons.length > 0) {
                var data = {
                    ids: [...vm.choseLessons],
                    choseType: vm.choseType
                };
                $.post(vm.url + '/backend/lesson/change-lesson-type', data, function (res) {
                    if (res.code === 200) {
                        vm.choseLessons = [];
                        vm.choseType = undefined;
                        vm.lessons = vm.lessons.map(function (lesson, index) {
                            lesson[index].type = res.data[index].type;
                            return lesson;
                        });
                        alert('Chuyển thành công');
                    } else {
                        alert('Chuyển thất bại')
                    }
                })
            }
        },
        changeCheckpointKey: function () {
            var vm = this;
            if (vm.keyToChange && vm.choseLessons.length > 0) {
                var data = {
                    ids: [...vm.choseLessons],
                    keyToChange: vm.keyToChange
                };
                $.post(vm.url + '/backend/adventure/checkpoints/change-key', data, function (res) {
                    if (res.code === 200) {
                        vm.checkpointsByKey = vm.checkpointsByKey.filter(function (lesson) {
                            return !vm.choseLessons.includes(lesson.id)
                        });
                        var newKeyLessons = vm.checkpoints[vm.editingKey].filter(function(point) {
                            return !vm.choseLessons.includes(point.id)
                        });
                        if (newKeyLessons.length === 0) {
                            _.unset(vm.checkpoints, vm.editingKey);
                        } else {
                            _.set(vm.checkpoints, vm.editingKey, newKeyLessons);
                        }
                        if (_.has(vm.checkpoints, vm.keyToChange) && vm.checkpoints[vm.keyToChange].length > 0) {
                            newKeyLessons = vm.checkpoints[vm.keyToChange].concat(res.data);
                            _.set(vm.checkpoints, vm.keyToChange, newKeyLessons);
                        } else {
                            _.set(vm.checkpoints, vm.keyToChange, res.data);
                        }
                        this.editingKey = undefined;
                        vm.choseLessons = [];
                    } else {
                        alert('Chuyển thất bại')
                    }
                })
            }
        },
        checkAllLesson: function (event) {
            var vm = this;

            var checked = event.target.checked;
            if (checked) {
                vm.choseLessons = vm.checkpointsByKey.map(function (lesson) {
                    return lesson.id;
                })
            } else {
                vm.choseLessons = [];
            }
            console.log("array --> ", vm.choseLessons)
        },
        checkOneLesson: function (event, lessonId) {
            var vm = this;
            var checked = event.target.checked;
            if (checked) {
                vm.choseLessons.push(lessonId);
            } else {
                vm.choseLessons = vm.choseLessons.filter(function (lesson) {
                    return lesson !== lessonId;
                })
            }
            console.log("array --> ", vm.choseLessons)
        },
        toggleHideLessonTitle: function (lesson) {
            var vm = this;
            var data = {
                id: lesson.id
            };
            $.post(vm.url + '/backend/lesson/toggle-secret', data, function (res) {
                if (res.code === 200) {
                    vm.lessons = vm.lessons.map(function (item) {
                        if (item.id === res.data.id) {
                            item.is_secret = res.data.is_secret;
                        }
                        return item;
                    })
                } else {
                    alert(res.msg);
                }
            })
        },
        onChangeCheckbox: function (event, param) {
            var vm = this;
            vm.formData[param] = event.target.checked ? 1 : 0;
        },
        onDragLessonEnd: function () {
            var vm = this;
            var ids = vm.checkpointsByKey.map(function (lesson) {
                return lesson.id;
            });
            var data = {
                ids: ids
            };
            $.post(vm.url + '/backend/adventure/checkpoints/sort', data, function (res) {
                if (res.code === 200) {
                    vm.checkpointsByKey = res.data;
                }
            })
        },
        closeCheckpointModal: function () {
            var vm = this;
            vm.checkpointLessons = [];
            vm.showCheckpointModal = false;
        },
        openCheckpointModal: function (point, key) {
            var vm = this;
            vm.editingKey = key;
            var data = {
                pathId: point[0].path_id,
                key: key
            };
            vm.showCheckpointModal = false;
            $.post(vm.url + '/backend/adventure/checkpoints/get-by-key', data, function (res) {
                if (res.code === 200) {
                    vm.checkpointsByKey = res.data;
                    vm.showCheckpointModal = true;
                }
            })
        },
        onDragEnd: function () {
            var vm = this;
            var ids = vm.groups.map(function (group) {
                return group.id;
            });
            var data = {
                course_id: vm.category.course_id,
                lesson_category_id: vm.category.id,
                ids: ids
            };
            $.post(vm.url + '/backend/lesson-groups/apply-sorting', data, function (res) {
                if (res.code == 200) {
                    vm.groups = res.data.map(function (group) {
                        return group;
                    })
                }
            })
        },
        changeCheckpointStatus: function (checkpoint, status) {
            var vm = this;
            var data = {
                id: checkpoint.id,
                status: status
            };
            $.post(vm.url + '/backend/adventure/checkpoints/change-status', data, function (res) {
                if (res.code === 200) {
                    vm.checkpointsByKey = vm.checkpointsByKey.map(function (item) {
                        if (item.id === res.data.id) {
                            item.status = res.data.status;
                        }
                        return item;
                    })
                }
            })
        },
        changeStatus: function (group, show) {
            var vm = this;
            var data = {
                id: group.id,
                show: show
            };
            $.post(vm.url + '/backend/lesson-groups/change-status', data, function (res) {
                if (res.code === 200) {
                    vm.groups = vm.groups.map(function (item) {
                        if (item.id === res.data.id) {
                            item.show = res.data.show;
                        }
                        return item;
                    })
                }
            })
        },
        saveForm: function () {
            var vm = this;

            var data = new FormData();

            data.append('id', vm.formData.id);
            data.append('lesson_id', vm.formData.lesson_id);
            data.append('path_id', vm.path.id);
            data.append('key', vm.formData.key);
            data.append('status', vm.formData.status);
            // data.append('is_secret', vm.formData.is_secret);
            if (vm.formData.id) {
                vm.updateCheckpoint(data);
            } else {
                vm.addCheckpoint(data);
            }

        },
        editCheckpoint: function (checkpoint) {
            var vm = this;

            this.editingCheckpoint = checkpoint;
        },
        addCheckpoint: function (data) {
            var vm = this;

            $.ajax({
                url: vm.url + '/backend/adventure/checkpoints/add',
                type: "POST",
                data: data,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.code == 200) {
                        if (res.data.path_id == vm.path.id) {
                            if (vm.checkpoints[res.data.key]) {
                                vm.checkpoints[res.data.key].push(res.data);
                            } else {
                                vm.checkpoints = _.omitBy(Object.assign(vm.checkpoints, {[res.data.key]: [res.data]}), _.isEmpty);
                            }
                        }
                        vm.closeModal();
                    } else {
                        alert(res.msg)
                    }
                }
            });
        },
        updateCheckpoint: function (data) {
            var vm = this;

            $.ajax({
                url: vm.url + '/backend/lesson-groups/update-group',
                type: "POST",
                data: data,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.code === 200) {
                        if (res.data.lesson_category_id !== vm.category.id) {
                            vm.groups = _.remove(vm.groups, function (group) {
                                return group.id !== res.data.id;
                            });
                        } else {
                            vm.groups = vm.groups.map(function (group) {
                                if (group.id === res.data.id) {
                                    group = {...res.data}
                                }
                                return group;
                            })
                        }
                        vm.closeModal();
                    } else {
                        alert(res.msg)
                    }
                }
            });
        },
        deleteCheckpointByKey: function (value) {
            var vm = this;
            var confirm = window.confirm('Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?');
            if (confirm) {
                var data = {
                    pathId: value.point[0].path_id,
                    key: value.key
                };
                $.post(vm.url + '/backend/adventure/checkpoints/delete-by-key', data, function (res) {
                    if (res.code === 200) {
                        vm.checkpoints = _.omit(vm.checkpoints, value.key);
                    }
                })
            }
        },
        deleteCheckpoint: function (checkpoint) {
            var vm = this;

            var confirm = window.confirm('Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?');
            if (confirm) {
                var data = {
                    id: checkpoint.id
                };
                $.post(vm.url + '/backend/adventure/checkpoints/delete', data, function (res) {
                    if (res.code === 200) {
                        vm.checkpointsByKey = vm.checkpointsByKey.filter(function (item) {
                            return item.id !== checkpoint.id;
                        })
                    }
                })
            }
        },
        setFormStatus: function (event) {
            var vm = this;
            vm.formData.status = parseInt(event.target.value);
        },
        closeModal: function () {
            var vm = this;
            // vm.formData = {
            //     ...vm.formData,
            //     id: undefined,
            //     lesson_id: 0,
            //     path_id: 1,
            //     key: 1,
            //     status: 0,
            //     // is_secret: 0,
            // };
            vm.showModal = false;
        },
        getGroupByCategory: function (category) {
            var vm = this;
            vm.loading = true;
            var data = {
                course_id: category.course_id,
                id: category.id
            };
            $.post(vm.url + '/backend/lesson-groups', data, function (res) {
                if (res.code === 200) {
                    vm.groups = [...res.data.groups];
                    vm.categories = [...res.data.categories];
                    vm.loading = false;
                }
            })
        }
    },
});
