var adventureScreen = new Vue({
    el: '#adventure__screen',
    data: function () {
        return {
            url: window.location.origin,
            admin: {},
            permission: 0,
            courses: [],
            periods: [],
            lessons: [],
            flashcardGroups: [],
            // selectedCourses: [],
            selectedCourse: {},
            selectedPeriod: null,
            selectedGroup: null,
            selectedPath: {},
            loading: false,
            showModal: false,
            showFlashcardModal: false,
            formData: {
                id: null,
                name: '',
                description: '',
                duration: '',
                course_id: '',
                status: '',
                is_vip: '',
            },
            draggable: false,
            availableLessons: [],
            selectedLessons: [],
        }
    },
    watch: {
        'selectedCourse.id': function(value) {
            this.getPeriods(value);
            this.getFlashcardGroups(value);
        },
        showFlashcardModal: function(value) {
          if (value){
              this.getFlashcardGroups(this.selectedCourse.id);
          }
        },
        'selectedGroup.id': function(value) {
            this.selectedLessons = [];
            this.getAvailableLessons(value);
        },
        selectedLessons: function (value) {
            console.log(value);
        },
    },
    methods: {
        getAvailableLessons: function(groupId) {
            var vm = this;
            $.get(vm.url + '/backend/adventure/flashcards/' + groupId + '/get-available', function (res) {
                if (res.code === 200) {
                    vm.availableLessons = [...res.data.available];
                    vm.lessons = [...res.data.lessons];
                }
            })
        },
        addFlashcardLesson: function () {
            var vm = this;
            var data = {
                ids: this.selectedLessons
            }
            $.post(vm.url + '/backend/adventure/flashcards/' + this.selectedGroup.id + '/store', data, function (res) {
                if (res.code === 200) {
                    vm.lessons = [...res.data];
                    toastr.info('Cập nhật thành công', {timeOut: 3000})
                }
            })
        },
        setFormStatus: function (event) {
            var vm = this;
            vm.formData.status = parseInt(event.target.value);
        },
        setFormVip: function (event) {
            var vm = this;
            vm.formData.is_vip = parseInt(event.target.value);
        },
        pathSelected: function (event) {
            var vm = this;
            vm.selectedPath = event;
        },
        selectCourse: function (event, course) {
            var vm = this;

            if (event.target.checked) {
                vm.selectedCourse = {...course}
            }
        },
        selectPeriod: function (period) {
            var vm = this;
            vm.selectedPeriod = period;
        },
        checkAdmin: function (admin) {
            return vm.admin.permission === 1 || vm.admin.id === 36 || vm.admin.id === 4
        },
        getPeriods: function (courseId) {
            var vm = this;
            var data = {
                courseId: courseId,
            };
            vm.loading = true;
            $.get(vm.url + '/backend/adventure/periods', data, function (res) {
                if (res.code === 200) {
                    vm.periods = [...res.data];
                    vm.selectedPeriod = vm.periods[0] || {};
                    vm.loading = false;
                }
            })
        },
        getFlashcardGroups: function (courseId) {
            var vm = this;
            $.get(vm.url + '/backend/adventure/fgroups/' + courseId, function (res) {
                if (res.code === 200) {
                    vm.flashcardGroups = [...res.data];
                    vm.selectedGroup = vm.flashcardGroups[0] || {};
                }
            })
        },
        saveForm: function () {
            var vm = this;

            var data = new FormData();

            data.append('id', vm.formData.id);
            data.append('name', vm.formData.name);
            data.append('description', vm.formData.description);
            data.append('duration', vm.formData.duration);
            data.append('course_id', vm.formData.course_id);
            data.append('status', vm.formData.status);
            data.append('is_vip', vm.formData.is_vip);
            if (vm.formData.id) {
                vm.updatePeriod(data);
            } else {
                vm.addPeriod(data);
            }

        },
        closeModal: function () {
            var vm = this;
            vm.formData = {
                ...vm.formData,
                id: null,
                name: '',
                description: '',
                duration: '',
                course_id: '',
                status: '',
                is_vip: '',
            };
            vm.showModal = false;
            vm.showFlashcardModal = false;
        },
        editPeriod: function (period) {
            var vm = this;

            vm.formData = {
                ...vm.formData,
                id: period.id,
                name: period.name,
                description: period.description,
                duration: period.duration,
                course_id: period.course_id,
                status: period.status,
                is_vip: period.is_vip,
            };
            vm.showModal = true;
        },
        addPeriod: function (data) {
            var vm = this;

            $.ajax({
                url: vm.url + '/backend/adventure/periods/store',
                type: "POST",
                data: data,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.code === 200) {
                        if (res.data.course_id == vm.selectedCourse.id) {
                            vm.periods.push(res.data);
                        }
                        vm.closeModal();
                    } else {
                        alert(res.msg)
                    }
                }
            });
        },
        updatePeriod: function (data) {
            var vm = this;

            $.ajax({
                url: vm.url + '/backend/adventure/periods/update',
                type: "POST",
                data: data,
                processData: false,
                contentType: false,
                success: function (res) {
                    if (res.code === 200) {
                        vm.periods = vm.periods.map(function (period) {
                            if (period.id === res.data.id) {
                                period = res.data
                            }
                            return period;
                        });
                        vm.closeModal();
                    } else {
                        alert(res.msg)
                    }
                }
            });
        },
        deletePeriod: function (period) {
            var vm = this;
            var data = {
                course_id: period.course_id,
                id: period.id
            };
            $.post(vm.url + '/backend/adventure/periods/delete', data, function (res) {
                if (res.code === 200) {
                    vm.periods = vm.periods.filter(function (item) {
                        return item.id !== period.id;
                    });
                    if (vm.selectedPeriod.id === period.id) {
                        if (vm.periods.length > 0) {
                            vm.selectPeriod(vm.periods[0])
                        } else {
                            vm.selectUncategorized();
                        }
                    }
                }
            })
        },
        onDragGroupEnd: function() {
            var vm = this;
            var ids = vm.flashcardGroups.map(function (group) {
                return group.id;
            });
            var data = {
                ids: ids
            };
            $.post(vm.url + '/backend/adventure/fgroups/' + this.selectedCourse.id + '/sort', data, function (res) {
                if (res.code === 200) {
                    vm.flashcardGroups = res.data;
                }
            })
        },
        onDragLessonEnd: function() {
            var vm = this;
            var ids = vm.lessons.map(function (lesson) {
                return lesson.id;
            });
            var data = {
                ids: ids
            };
            $.post(vm.url + '/backend/adventure/flashcards/' + this.selectedGroup.id + '/sort', data, function (res) {
                if (res.code === 200) {
                    vm.lessons = res.data;
                }
            })
        },
        saveGroup: function (e, group) {
            var vm = this;
            if (e.key == 'Enter') {
                var value = e.target.value;
                var data = {
                    title: value,
                }
                $.post(vm.url + '/backend/adventure/fgroups/' + group.id + '/update', data, function (res) {
                    if (res.code === 200) {
                        vm.flashcardGroups = vm.flashcardGroups.map(item => {
                            if (item.id == res.data.id) {
                                item.title = res.data.title;
                            }
                            return item;
                        })
                    }
                    toastr.info('Cập nhật thành công', {timeOut: 3000});
                })
            }
        },
        deleteGroup: function (group) {
            var vm = this;
            $.post(vm.url + '/backend/adventure/fgroups/' + group.id + '/destroy', function (res) {
                if (res.code === 200) {
                    vm.flashcardGroups = vm.flashcardGroups.filter(function (item) {
                        return item.id !== group.id;
                    });
                    vm.selectedGroup = vm.flashcardGroups[0];
                    toastr.info('Cập nhật thành công', {timeOut: 3000})
                }
            })
        },
        deleteLesson: function (lesson) {
            var vm = this;
            $.post(vm.url + '/backend/adventure/flashcards/' + lesson.id + '/destroy', function (res) {
                if (res.code === 200) {
                    vm.lessons = vm.lessons.filter(function (item) {
                        return item.id !== lesson.id;
                    });
                    toastr.info('Cập nhật thành công', {timeOut: 3000})
                }
            })
        },
        createGroup: function () {
            var vm = this;
            $.post(vm.url + '/backend/adventure/fgroups/' + this.selectedCourse.id, function (res) {
                if (res.code === 200) {
                    vm.flashcardGroups.push(res.data);
                    vm.selectedGroup = vm.flashcardGroups[0];
                }
            })
        },

    },
    mounted: function () {
        var vm = this;
        vm.admin = admin;
        vm.permission = vm.admin.permission === 1 || vm.admin.id === 36 || vm.admin.id === 4

        vm.courses = _.orderBy(courses, 'name').filter(function (course) {
            if (['N1','N2','N3','N4','N5','Sơ cấp N4','Sơ cấp N5'].includes(course.name)) {
                return course;
            }
        });
        vm.selectedCourse = vm.courses[0]
    }
});
