// Theme color settings
$(document).ready(function(){
  var prefix = $('#theme').attr('href').split('css/colors')[0];


  $("*[theme]").click(function(e){
    e.preventDefault();
    var currentStyle = $(this).attr('theme');
    store('theme', currentStyle);
    $('#theme').attr({href: prefix + 'css/colors/'+currentStyle+'.css'})
  });

  var currentTheme = get('theme');
  if(currentTheme)
  {
    $('#theme').attr({href: prefix + 'css/colors/'+currentTheme+'.css'});
    $('#themecolors li a').removeClass('working');
    $('.' + currentTheme + '-theme').addClass('working')
  }

  // color selector
  $('#themecolors').on('click', 'a', function(){
    $('#themecolors li a').removeClass('working');
    $(this).addClass('working')
  });

});

function store(name, val) {
  if (typeof (Storage) !== "undefined") {
    localStorage.setItem(name, val);
  } else {
    window.alert('Please use a modern browser to properly view this template!');
  }
}

function get(name) {
  if (typeof (Storage) !== "undefined") {
    return localStorage.getItem(name);
  } else {
    window.alert('Please use a modern browser to properly view this template!');
  }
}

//săp xếp kéo thả
//update sort_order
  function sortTable(id_tbody, columnID, columnOrder, url, field, id_table){
      $( '.' + id_tbody + '' ).sortable({
          start : function(event, ui) {
              var start_pos = ui.item.index();
              ui.item.data('start_pos', start_pos);
          },
          update : function(event, ui) {
              var index = ui.item.index();
              var start_pos = ui.item.data('start_pos');
              //update the html of the moved item to the current index
              updatePosition(index+1, columnID, columnOrder, url, field, id_table);

              if (start_pos < index) {
                  //update the items before the re-ordered item
                  for(var i = index; i > 0; i--){
                      updatePosition(i ,columnID, columnOrder, url, field, id_table);
                  }
              }else {
                  //update the items after the re-ordered item
                  for(var i=index+2 ;i <= $('#'+ id_table +' tbody tr').length; i++){
                      updatePosition(i, columnID, columnOrder, url, field, id_table);
                  }
              }
          },
          axis : 'y'
      });
      $( '.'+ id_tbody +'' ).disableSelection();
  }

  function updatePosition(row_index, id_index, order_index, url, field, id_table){
      $.post(url,
          {
              id:  $('#'+ id_table +' tbody tr:nth-child('+ row_index + ') td:nth-child('+ id_index +')').html(),
              sort_order: row_index 
          },
          function(data, status){
              $('#'+ id_table +' tbody tr:nth-child('+ data[field] + ') td:nth-child('+ order_index +')').html(data[field]);
          }
      );
  }
