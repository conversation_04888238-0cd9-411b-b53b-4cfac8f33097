var hashids = new Hashids("", 6, "0123456789ABCDEF");
$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": document
      .querySelector('meta[name="csrf-token"]')
      .getAttribute("content"),
  },
});

var app = new Vue({
  el: "#chat_container",
  data: {
    currentFilter: null, // Mode lọc
    keywords: "", // Từ khóa
    currentCourses: [],
    today: today,
    tags: tags,
    senders: senders,
    page: 1,
    // total: total,
    currentSender: null,
    messages: [],
    message: "",
    socket: null,
    senderPage: 0,
    senderLastPage: null,
    messagePage: 0,
    mesageLastPage: null,
    firstMessageId: null,
    userIsTyping: false,
    imageUrl: "",
    firstUpdate: true, // after first update, list message will scroll to bottom, and then it should be lock by set to false
    messageId: null,
    loadSenderFinish: false,
    rootMessage: null,

    filter: {},
    filterTag: null,
    filterFrom: null,
    filterTo: null,

    // State xử lý ghi chú nhanh
    quickReplies: [],
    quickReplyKeyword: "",

    studentNotes: [],
    studentNoteKey: ""
  },

  created: function () {
    this.mergeTag(this.senders);
  },

  watch: {
    filterFrom: {
      handler(newValue) {
        if (newValue.length > 2) this.filterChatByTags();
      },
      deep: true,
    },

    filterTo: {
      handler(newValue) {
        if (newValue.length > 2) this.filterChatByTags();
      },
      deep: true,
    },
  },
  computed: {
    notes() {
      return this.studentNotes.filter((n) => n.content.includes(this.studentNoteKey));
    }
  },
  methods: {
    mergeTag: function (senders) {
      const vm = this;
      senders.forEach(function (mess) {
        mess.tags = [];
        if (mess.tag_ids != null) {
          var tagIds = mess.tag_ids;
          tagIds = tagIds.map(Number);
          for (var i = 0; i < tagIds.length; i++) {
            var tmp = _.find(vm.tags, ["id", tagIds[i]]);
            if (tmp) mess.tags.push(tmp);
          }
        }
      });
      vm.$forceUpdate();
    },

    // Khởi tạo hộp thoại chat đầu tiên
    initChatAtFirstTime: async function (sender) {
      var vm = this;

      // Reselect a user
      if (vm.currentSender && sender.id === vm.currentSender.id) {
        return;
      }

      // Remove old listeners
      if (vm.currentSender != null) {
        vm.socket.removeAllListeners(
          "received_enc_" + vm.currentSender.id + "_0"
        );
        vm.socket.removeAllListeners("received_" + vm.currentSender.id + "_0");
        vm.socket.removeAllListeners(
          "sent_enc_" + vm.currentSender.id + "_" + adminId
        );
        vm.socket.removeAllListeners(
          "sent_" + vm.currentSender.id + "_" + adminId
        );
        vm.socket.removeAllListeners(
          "typing_" + vm.currentSender.id + "_" + vm.currentSender.creator_id
        );
        vm.socket.removeAllListeners(
          "stopTyping_" +
            vm.currentSender.id +
            "_" +
            vm.currentSender.creator_id
        );
      }

      // Set current chatting user
      vm.messages = [];
      vm.currentSender = sender;
      vm.message = "";
      vm.messagePage = 0;
      vm.mesageLastPage = null;
      vm.firstMessageId = null;
      vm.userIsTyping = false;
      vm.firstUpdate = true;
      vm.messageId = null;

      vm.listen();

      await vm.loadByPage();

      // Check last user message was not read
      vm.senders = vm.senders.map(function (sender) {
        if (sender.id == vm.currentSender.id) {
          sender.status = 1;
        }
        return sender;
      });

      //tải thêm thông tin các khóa học của học viên
      $.post(
        window.location.origin + "/backend/chat/get-user-courses",
        { id: sender.creator_id },
        function (response) {
          vm.currentCourses = response;
          // console.log("current:", response);
        }
      );
    },

    //mở một hộp thoại chat cụ thể
    openChat: async function (sender) {
      var vm = this;

      // Reselect a user
      if (vm.currentSender && sender.id === vm.currentSender.id) {
        return;
      }

      // Remove old listeners
      if (vm.currentSender != null) {
        vm.socket.removeAllListeners(
          "received_enc_" + vm.currentSender.id + "_0"
        );
        vm.socket.removeAllListeners("received_" + vm.currentSender.id + "_0");
        vm.socket.removeAllListeners(
          "sent_enc_" + vm.currentSender.id + "_" + adminId
        );
        vm.socket.removeAllListeners(
          "sent_" + vm.currentSender.id + "_" + adminId
        );
        vm.socket.removeAllListeners(
          "typing_" + vm.currentSender.id + "_" + vm.currentSender.creator_id
        );
        vm.socket.removeAllListeners(
          "stopTyping_" +
            vm.currentSender.id +
            "_" +
            vm.currentSender.creator_id
        );
      }

      // Set current chatting user
      vm.messages = [];
      vm.currentSender = sender;
      vm.message = "";
      vm.messagePage = 0;
      vm.mesageLastPage = null;
      vm.firstMessageId = null;
      vm.userIsTyping = false;
      vm.firstUpdate = true;
      vm.messageId = null;
      vm.rootMessage = null;
      vm.getStudentNotes();

      vm.listen();

      await vm.loadByPage();

      // Check last user message was not read
      vm.senders = vm.senders.map(function (sender) {
        if (sender.id == vm.currentSender.id) {
          sender.status = 1;
        }
        return sender;
      });

      vm.emitReaded();

      if (sender.tag_ids == null) {
        // Tải thêm tags id để không bị lỗi
        $.post(
          window.location.origin + "/backend/chat/get-tag-ids",
          { creatorid: sender.creator_id },
          function (response) {
            vm.currentSender.tag_ids = response;

            var mess = vm.currentSender;
            mess.tags = [];
            if (mess.tag_ids != null) {
              var tagIds = mess.tag_ids.split(",");
              tagIds = tagIds.map(Number);
              for (var i = 0; i < tagIds.length; i++) {
                var tmp = _.find(vm.tags, ["id", tagIds[i]]);
                if (tmp) mess.tags.push(tmp);
              }
            }
            // vm.currentSender.tags   = response.tags;
            vm.$forceUpdate();
          }
        );
      }

      // Tải thêm thông tin các khóa học của học viên
      $.post(
        window.location.origin + "/backend/chat/get-user-courses",
        { id: sender.creator_id },
        function (response) {
          vm.currentCourses = response;
        }
      );
    },

    sendMessage: function () {
      var vm = this;

      // Emit read
      if ($("#screen_box")[0].scrollHeight <= $("#screen_box").height()) {
        vm.emitReaded();
      }

      // var content = vm.message.trim();
      var content = $("#input_textarea").val().trim();

      if (!content) return;

      // Emit socket
      var messageSocket = {
        content: content,
        conversationId: vm.currentSender.id,
        receiverId: vm.currentSender.creator_id,
        senderId: adminId,
        senderType: "admin",
        senderName: "Admin",
        sentId:
          Date.now() +
          "_" +
          adminId +
          "_" +
          Math.floor(Math.random() * 100) +
          "" +
          (Math.floor(Math.random() * 100) + 100),
        type: "text",
      };

      if (vm.rootMessage) {
        messageSocket.rootMessage = vm.rootMessage;
      }

      if (vm.currentSender.creator_id > 0) {
        // For normal chat
        var messageEncrypt = vm.encrypt(JSON.stringify(messageSocket));
        vm.socket.emit("send_enc", messageEncrypt);
      } else {
        // For chat incognito
        vm.socket.emit("send", messageSocket);
      }

      var newMessage = {
        content: content,
        conversation_id: vm.currentSender.id,
        created_at: new Date(),
        id: -1,
        sender_id: adminId,
        sender_type: "admin",
        status: 0,
        type: "text",
        sentId: messageSocket.sentId,
        isSent: false,
        admin_name: adminName,
        rootMessage: vm.rootMessage,
        message_id: vm.rootMessage ? vm.rootMessage.id : null,
      };
      vm.messages.push(newMessage);

      // Reset message input
      vm.message = "";
      vm.rootMessage = null;
      $("#input_textarea").css("height", "35px");

      // Scroll to bottom
      vm.goToBottom();

      // Update list sender
      vm.senders = vm.senders.map(function (sender) {
        if (sender.id == vm.currentSender.id) {
          sender.content = newMessage.content;
          sender.created_at = newMessage.created_at;
          sender.type = newMessage.type;
          sender.last_id = -1;
          sender.status = 1;
        }
        return sender;
      });
    },

    sendImage: function () {
      var vm = this;

      // Emit read
      if ($("#screen_box")[0].scrollHeight <= $("#screen_box").height()) {
        vm.emitReaded();
      }

      if (!vm.currentSender) return;
      var formData = new FormData($("#send_image_form")[0]);
      formData.append("conversation_id", app.currentSender.id);
      formData.append("user_id", app.currentSender.creator_id);
      formData.append("message_id", vm.rootMessage ? vm.rootMessage.id : null);

      $.ajax({
        url: window.location.origin + "/backend/send-image",
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        async: true,
        error: function (error) {
          console.log("Error get messages", error);
        },
        success: function (response) {
          console.log("Success send image", response);

          // Convert new image message's content
          try {
            response.content = JSON.parse(response.content);
          } catch (error) {
            console.log("error JSON.parse new message content", error);
          }
          const newMsg = {
            ...response,
            rootMessage: vm.rootMessage,
            message_id: vm.rootMessage ? vm.rootMessage.id : null,
          };
          vm.messages.push(newMsg);

          // Clear input file
          $("#input_file").val("");

          // Scroll to bottom
          vm.goToBottom();

          // Emit socket
          var messageSocket = {
            content: response.content,
            conversationId: response.conversation_id,
            receiverId: vm.currentSender.creator_id,
            senderId: adminId,
            senderType: "admin",
            senderName: "Admin",
            sentId:
              Date.now() +
              "_" +
              adminId +
              "_" +
              Math.floor(Math.random() * 100) +
              "" +
              (Math.floor(Math.random() * 100) + 100),
            type: "image",
          };

          if (vm.rootMessage) {
            messageSocket.rootMessage = vm.rootMessage;
          }

          if (vm.currentSender.creator_id > 0) {
            // For normal chat
            var messageEncrypt = vm.encrypt(JSON.stringify(messageSocket));
            vm.socket.emit("send_enc", messageEncrypt);
          } else {
            // For chat incognito
            vm.socket.emit("send", messageSocket);
          }

          vm.rootMessage = null;

          // Update list sender
          vm.senders = vm.senders.map(function (sender) {
            if (sender.id == vm.currentSender.id) {
              sender.content = response.content;
              sender.created_at = response.created_at;
              sender.type = response.type;
              sender.last_id = -1;
              sender.status = 1;
            }
            return sender;
          });
        },
      });
    },

    encrypt: function (str) {
      return CryptoJS.AES.encrypt(JSON.stringify(str), aesKey).toString();
    },

    decrypt: function (str) {
      var data = CryptoJS.AES.decrypt(str, aesKey);
      data = data.toString(CryptoJS.enc.Utf8);
      data = JSON.parse(data);
      return data;
    },

    listenReceive: function (message) {
      var vm = this;

      var newMessage = {
        content: message.content,
        conversation_id: message.conversationId,
        created_at: new Date(),
        id: message.id,
        sender_id: message.senderId,
        sender_type: "user",
        status: 0,
        type: message.type,
        rootMessage: message.rootMessage,
      };

      // Add new message
      vm.messages.push(newMessage);

      // If message box in on bottom => scroll after add new message
      if (
        $("#screen_box").scrollTop() + $("#screen_box").innerHeight() >=
        $("#screen_box")[0].scrollHeight
      ) {
        vm.goToBottom();
      }

      // Emit read
      if ($("#screen_box")[0].scrollHeight <= $("#screen_box").height()) {
        vm.emitReaded();
      }
    },

    // All listener to socket
    listen: function () {
      var vm = this;
      // Catch new message with incognito
      vm.socket.on(
        "received_" + vm.currentSender.id + "_0",
        function (message) {
          vm.listenReceive(message);
        }
      );

      // Catch new message with normal user
      vm.socket.on(
        "received_enc_" + vm.currentSender.id + "_0",
        function (message) {
          message = JSON.parse(vm.decrypt(message));
          vm.listenReceive(message);
        }
      );

      // Catch already sent message sent success
      if (vm.currentSender.creator_id > 0) {
        // For normal chat
        vm.socket.on(
          "sent_enc_" + vm.currentSender.id + "_" + adminId,
          function (message) {
            message = JSON.parse(vm.decrypt(message));
            vm.messages = vm.messages.map((item) => {
              if (item.sentId && item.sentId === message.sentId) {
                item = { ...item };
                item.isSent = true;
              }
              return item;
            });
          }
        );
      } else {
        // For incognito chat
        vm.socket.on(
          "sent_" + vm.currentSender.id + "_" + adminId,
          function (message) {
            vm.messages = vm.messages.map((item) => {
              if (item.sentId && item.sentId === message.sentId) {
                item = { ...item };
                item.isSent = true;
              }
              return item;
            });
          }
        );
      }

      // Catch typing event
      vm.socket.on(
        "typing_" + vm.currentSender.id + "_" + vm.currentSender.creator_id,
        function (message) {
          vm.userIsTyping = true;
        }
      );

      // Catch stop typing event
      vm.socket.on(
        "stopTyping_" + vm.currentSender.id + "_" + vm.currentSender.creator_id,
        function (message) {
          vm.userIsTyping = false;
        }
      );

      // Catch read message event
      vm.socket.on("readed_" + vm.currentSender.id + "_0", function () {
        vm.messages = vm.messages.map(function (item) {
          if (item.sender_type == "admin") {
            item.status = 1;
          }
          return item;
        });
      });
    },

    emitTyping: function () {
      var vm = this;
      vm.socket.emit("typing", {
        conversationId: vm.currentSender.id,
        senderId: 0,
      });
    },

    emitStopTyping: function () {
      var vm = this;
      vm.socket.emit("stopTyping", {
        conversationId: vm.currentSender.id,
        senderId: 0,
      });
    },

    emitReaded: function () {
      // Update count not read in default.blade
      chatTab && chatTab.updateCountNotRead();

      var vm = this;
      if (!vm.getLastUserMessage() || vm.getLastUserMessage().status == 0) {
        vm.socket.emit("readed", {
          conversationId: vm.currentSender.id,
          receiverId: vm.currentSender.creator_id,
        });
        vm.setRead(vm.currentSender.id);
      }
    },
    setRead: function (conversationId) {
      $.post(
        window.location.origin + "/backend/chat/mark-as-read",
        { conversationId: conversationId },
        function (response) {
          // console.log(response);
        }
      );
    },
    // Scroll to bottom of chat box
    goToBottom: function () {
      setTimeout(function () {
        var objDiv = document.getElementById("screen_box");
        objDiv.scrollTop = objDiv.scrollHeight;
      }, 50);
    },

    loadByPage: async function () {
      var vm = this;

      if (vm.mesageLastPage && vm.messagePage + 1 > vm.mesageLastPage) {
        return;
      }

      vm.messagePage++;

      await $.ajax({
        url: window.location.origin + "/backend/messages",
        type: "GET",
        data: { conversation_id: vm.currentSender.id, page: vm.messagePage },
        async: true,
        error: function (error) {
          console.log("Error get messages", error);
        },
        success: function (response) {
          // console.log('Success get messages lần 1: ', response);
          vm.mesageLastPage = response.last_page;
          var messages = response.data;
          messages.reverse();
          messages.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.messages.unshift(...messages);
        },
      });
    },

    renderContent: function (msg) {
      var info = msg.content;
      try {
        info = JSON.parse(msg.content);
      } catch (error) {
        console.log('Parse content error', error);
      }
      var result = info.toString();
      var info = info.toString();
      result = result.replace('<', '&#60;');
      result = result.replace('>', '&#62;');

      // Xử lý xuống dòng
      result = info.replace(new RegExp('\r?\n', 'g'), '<br />');

      var re = /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;
      return result.replace(re, function (match, lParens, url) {
        var rParens = '';
        lParens = lParens || '';
        var lParenCounter = /\(/g;
        while (lParenCounter.exec(lParens)) {
          var m;
          if ((m = /(.*)(\.\).*)/.exec(url) || /(.*)(\).*)/.exec(url))) {
            url = m[1];
            rParens = m[2] + rParens;
          }
        }
        return lParens + "<a class='msg-link' href='" + url + "' target='_blank'>" + url + '</a>' + rParens;
      });
    },

    // Get last message from user
    getLastUserMessage: function () {
      var vm = this;

      if (!vm.currentSender || !vm.messages) {
        return null;
      }

      for (var i = vm.messages.length - 1; i >= 0; i--) {
        if (vm.messages[i].sender_type == "user") {
          return vm.messages[i];
        }
      }
      return null;
    },

    loadSenderByPage: function () {
      var vm = this;

      if (vm.loadSenderFinish) {
        return;
      }

      this.page = this.page + 1;

      var data = {
        ...this.filter,
        page: this.page,
      };
      $.ajax({
        url: window.location.origin + "/backend/load-more-sender",
        type: "GET",
        data: data,
        async: true,
        error: function (error) {
          console.log("Error get more sender", error);
        },
        success: function (response) {
          // console.log('Success load more sender', response);

          var senders = response;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders.push(...senders);

          vm.mergeTag(vm.senders);

          if (senders.length < 20) vm.loadSenderFinish = true;
        },
      });
    },

    loadMore: function () {
      this.loadByPage();
    },

    loadMoreSender: function () {
      this.loadSenderByPage();
    },

    // Convert time
    getTime: function (timestamp) {
      var dateTime = new Date(timestamp);
      var currentTime = new Date();

      if (dateTime.getFullYear() == currentTime.getFullYear()) {
        if (
          dateTime.getMonth() == currentTime.getMonth() &&
          dateTime.getDate() == currentTime.getDate()
        ) {
          // Today
          return moment(timestamp).format("HH:mm");
        }

        // This year
        return moment(timestamp).format("DD/MM HH:mm");
      }
      return moment(timestamp).format("DD/MM/YYYY HH:mm");
    },

    // Show full image
    reviewImage: function (imageUrl) {
      var vm = this;
      vm.imageUrl = imageUrl;
      $("#show_full_image").modal("toggle");
    },

    // Show modal confirm remove message
    showModalRemoveMessage: function (messageId) {
      var vm = this;
      vm.messageId = messageId;
      $("#remove_message").modal("toggle");
    },

    // Remove message
    removeMessage: function () {
      var vm = this;
      $.ajax({
        url: window.location.origin + "/backend/remove-message",
        type: "POST",
        data: { message_id: vm.messageId },
        async: true,
        error: function (error) {
          console.log("Error remove message", error);
          $("#remove_message").modal("hide");
          alert("Có lỗi xảy ra, Vui lòng thử lại");
        },
        success: function (response) {
          console.log("Success remove message", response);
          $("#remove_message").modal("hide");

          vm.messages = vm.messages.filter(function (message) {
            return message.id != vm.messageId;
          });
        },
      });
    },

    // Tạo màu ngẫu nhiên label
    getRandomColor: function (id) {
      return "#" + hashids.encode(id);
    },

    // Tìm kiếm theo từ khóa
    filterChatByKeyword: function (keywords) {
      const vm = this;

      // console.log('từ khóa:', keywords);

      $.post(
        window.location.origin + "/backend/chat/filter",
        {
          type: "keywords",
          key: keywords,
        },
        function (response) {
          // console.log('Lọc theo tìm kiếm từ khóa', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          // console.log('kết quả tìm kiếm ', vm.senders);
        }
      );
    },

    //xóa từ khóa tìm kiếm
    clearSearch: function () {
      const vm = this;
      vm.keywords = "";
      vm.senders = senders;

      // Convert message's content in last message
      vm.senders = vm.senders.map(function (sender) {
        try {
          sender.content = JSON.parse(sender.content);
        } catch (error) {
          console.log("error JSON.parse message from load", error);
        }
        return sender;
      });

      vm.mergeTag(vm.senders);
    },

    // Tìm kiếm theo admin
    filterChatByAdmin: function (id) {
      const vm = this;
      this.page = 1;
      $.post(
        window.location.origin + "/backend/chat/filter",
        {
          page: this.page,
          type: "admin",
          id: id,
        },
        function (response) {
          // console.log('Lọc theo admin trả lời ', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },

    // Tìm kiếm theo hội thoại chưa đọc
    filterChatByUnread: function () {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        {
          type: "unread",
        },
        function (response) {
          // console.log('Các tin nhắn chưa đọc ', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },
    // Tìm kiếm theo hội thoại chưa đọc
    filterChatByLemonQuestionDog: function () {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        {
          type: "lemon_question_dog",
        },
        function (response) {
          // console.log('Các tin nhắn chưa đọc ', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },
    // Tìm kiếm theo tư vấn kiến thức
    filterChatByKnowledge: function () {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        {
          type: "knowledge",
        },
        function (response) {
          // console.log('Các tin nhắn chưa đọc ', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },

    // Tìm kiếm theo tin nhắn báo lỗi
    filterChatByBugs: function () {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        {
          type: "bug",
        },
        function (response) {
          // console.log('Các tin nhắn chưa đọc ', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },

    chooseTag: function (id) {
      const vm = this;
      vm.filterTag = _.find(vm.tags, ["id", id]);
      vm.filterChatByTags();
    },

    //tìm kiếm theo tin nhắn đánh tag
    filterChatByTags: function () {
      const vm = this;

      if (vm.filterFrom && vm.filterTo && vm.filterTag.name) {
        $.post(
          window.location.origin + "/backend/chat/filter",
          {
            type: "tag",
            filterFrom: vm.filterFrom + " 00:00:00",
            filterTo: vm.filterTo + " 23:59:59",
            id: vm.filterTag.id,
          },
          function (response) {
            // console.log("Các tin nhắn chưa đọc ", response);
            vm.filter = response.filter;
            var senders = response.senders;
            // vm.total = response.total;
            senders.map(function (item) {
              try {
                item.content = JSON.parse(item.content);
              } catch (error) {
                console.log("error JSON.parse message from load", error);
              }
              return item;
            });
            vm.senders = senders;
            vm.mergeTag(vm.senders);
            // console.log('kết quả hiển thị ', vm.senders);
          }
        );
      }
    },

    // Tìm kiếm theo url
    filterChatByUrl: function (id) {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        {
          type: "url",
          id: id,
        },
        function (response) {
          // console.log('Các tin nhắn theo url ', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          vm.openChat(senders[0]);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },

    // Tìm kiếm theo học sinh đang online
    filterChatOnline: function (id) {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        { online: true },
        function (response) {
          // console.log('Các tin nhắn theo học viên đang online', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          vm.openChat(senders[0]);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },

    filterChatIncognito() {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        { incognito: true },
        function (response) {
          // console.log('Các tin nhắn theo học viên ẩn danh', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          vm.openChat(senders[0]);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },

    filterChatIncognitoOnline() {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/filter",
        { incognito: true, online: true },
        function (response) {
          // console.log('Các tin nhắn theo học viên ẩn danh đang online', response);
          vm.filter = response.filter;
          var senders = response.senders;
          // vm.total = response.total;
          senders.map(function (item) {
            try {
              item.content = JSON.parse(item.content);
            } catch (error) {
              console.log("error JSON.parse message from load", error);
            }
            return item;
          });
          vm.senders = senders;
          vm.mergeTag(vm.senders);
          vm.openChat(senders[0]);
          // console.log('kết quả hiển thị ', vm.senders);
        }
      );
    },

    // Đánh dấu chưa đọc
    markAsUnread: function (id) {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/mark-as-unread",
        { id: id },
        function (response) {
          console.log("mark-as-unread ", response);
          if (response == "success") {
            $("#focus-" + id).removeClass("senderItem");
            $("#focus-" + id).addClass("senderItemNotRead");
          }
        }
      );
    },

    // Đánh dấu chưa đọc
    markAsKnowledge: function (id) {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/mark-as-knowledge",
        { id: id },
        function (response) {
          if (response == "success") {
            for (const con of vm.senders) {
              if (con.creator_id == id) {
                if (con.is_knowledge == false) con.is_knowledge = true;
                else con.is_knowledge = false;
                break;
              }
            }
          }
        }
      );
    },

    // Đánh dấu chưa đọc
    markAsBug: function (id) {
      const vm = this;
      $.post(
        window.location.origin + "/backend/chat/mark-as-bug",
        { id: id },
        function (response) {
          if (response == "success") {
            for (const con of vm.senders) {
              if (con.creator_id == id) {
                if (con.is_bug == false) con.is_bug = true;
                else con.is_bug = false;
                break;
              }
            }
          }
        }
      );
    },

    create_tmpID: function () {
      var dt = new Date().getTime();
      var uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          var r = (dt + Math.random() * 16) % 16 | 0;
          dt = Math.floor(dt / 16);
          return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
        }
      );
      return uuid;
    },
    getQuickReplies: function () {
      const vm = this;
      $.get(
        window.location.origin + "/backend/quick-reply",
        function (response) {
          vm.quickReplies = response.data.map(function (reply) {
            reply.tmpId = vm.create_tmpID();
            reply.editing = false;
            reply.showPicker = false;
            reply.hover = false;
            return reply;
          });
        }
      );
    },
    // Thêm reply nhanh
    addNewQuickReply: function () {
      const vm = this;
      var tmpId = vm.create_tmpID();
      vm.quickReplies.unshift({
        id: undefined,
        tmpId: tmpId,
        content: "",
        color: "red",
        editing: true,
        showPicker: false,
        hover: false,
      });
      setTimeout(function () {
        $("#note-" + tmpId).focus();
      }, 50);
    },
    confirmRemove(id, tmpId) {
      const vm = this;
      var confirm = window.confirm("Xác nhận xoá ghi chú này?");
      if (confirm) vm.removeQuickReply(id, tmpId);
    },

    removeQuickReply: function (id, tmpId) {
      const vm = this;
      if (!id) {
        vm.quickReplies = vm.quickReplies.filter(function (reply) {
          return reply.tmpId != tmpId;
        });
      } else {
        var url = window.location.origin + "/backend/quick-reply/delete";

        var data = {
          id: id,
        };

        $.post(url, data, function (response) {
          if (response.code == 200) {
            vm.quickReplies = vm.quickReplies.filter(function (rp) {
              return rp.tmpId != tmpId;
            });
          } else if (response.code == 404) {
            alert(response.data.error);
          } else {
            alert("Đã xảy ra lỗi");
          }
        });
      }
    },
    onSaveReply: function (reply) {
      const vm = this;

      var url = "";

      url = !reply.id
        ? "/backend/quick-reply/store"
        : "/backend/quick-reply/update";

      var data = {
        id: reply.id,
        content: _.trim(reply.content),
        color: reply.color,
      };
      $.post(window.location.origin + url, data, function (response) {
        if (response.code == 200) {
          vm.quickReplies = vm.quickReplies.map(function (rp) {
            if (rp.tmpId == reply.tmpId) {
              rp = response.data;
              rp.editing = false;
              rp.showPicker = false;
              rp.hover = true;
            }
            return rp;
          });
        } else {
          alert("Đã xảy ra lỗi");
        }
      });
    },
    changeColor: function (tmpId, color) {
      const vm = this;
      vm.quickReplies.forEach(function (reply) {
        if (reply.tmpId == tmpId) {
          reply.color = color;
          reply.editing = true;
          reply.showPicker = false;
        }
        return reply;
      });
    },
    renderColor: function (cl) {
      switch (cl) {
        case "yellow":
          return ({
            primary: "#f1b808",
            secondary: "#fbf3b5",
            text: "#000",
          });
        case "green":
          return ({
            primary: "#0F8904",
            secondary: "#87C481",
            text: "#000",
          });
        case "pink":
          return ({
            primary: "#D407A8",
            secondary: "#ED9BDC",
            text: "#000",
          });
        case "purple":
          return ({
            primary: "#5c239b",
            secondary: "#BEA7D7",
            text: "#000",
          });
        case "blue":
          return ({
            primary: "#0078d7",
            secondary: "#99C8EF",
            text: "#000",
          });
        case "gray":
          return ({
            primary: "#767676",
            secondary: "#D5D5D5",
            text: "#000",
          });
        default:
          return ({
            primary: "#f1b808",
            secondary: "#fbf3b5",
            text: "#000",
          });
      }
    },
    pasteQuickReply: function (reply) {
      this.message = reply.content;
    },
    searchQuickReply: function (event) {
      const vm = this;
      if (event.key == "Enter") {
        var data = {
          keyword: event.target.value,
        };
        $.post(
          window.location.origin + "/backend/quick-reply/filter",
          data,
          function (response) {
            vm.quickReplies = response.data.map(function (reply) {
              reply.tmpId = vm.create_tmpID();
              reply.editing = false;
              reply.showPicker = false;
              return reply;
            });
          }
        );
      }
    },
    getStudentNotes: function () {
      const vm = this;
      $.get(
        window.location.origin + "/backend/user-chat-note?conversation_id=" + vm.currentSender.id,
        function (response) {
          vm.studentNotes = response.data.map(function (reply) {
            reply.tmpId = vm.create_tmpID();
            reply.editing = false;
            reply.showPicker = false;
            reply.hover = false;
            return reply;
          });
        }
      );
    },
    addStudentNote: function () {
      const vm = this;
      var tmpId = vm.create_tmpID();
      vm.studentNotes.unshift({
        id: undefined,
        tmpId: tmpId,
        content: "",
        color: "red",
        editing: true,
        showPicker: false,
        hover: false,
      });
      setTimeout(function () {
        $("#note-user-" + tmpId).focus();
      }, 50);
    },
    confirmRemoveStudentNote(id, tmpId) {
      const vm = this;
      var confirm = window.confirm("Xác nhận xoá ghi chú này?");
      if (confirm) vm.removeStudentNote(id, tmpId);
    },
    removeStudentNote: function (id, tmpId) {
      const vm = this;
      if (!id) {
        vm.studentNotes = vm.studentNotes.filter(function (reply) {
          return reply.tmpId != tmpId;
        });
      } else {
        var url = window.location.origin + "/backend/user-chat-note/delete";

        var data = { id: id };

        $.post(url, data, function (response) {
          if (response.code == 200) {
            vm.studentNotes = vm.studentNotes.filter(function (rp) {
              return rp.tmpId != tmpId;
            });
          } else if (response.code == 404) {
            alert(response.data.error);
          } else {
            alert("Đã xảy ra lỗi");
          }
        });
      }
    },
    onSaveStudentNote: function (reply) {
      const vm = this;
      var url = !reply.id
        ? "/backend/user-chat-note/create"
        : "/backend/user-chat-note/update";
      var data = {
        id: reply.id,
        content: _.trim(reply.content),
        color: reply.color,
        conversation_id: vm.currentSender.id
      };
      $.post(window.location.origin + url, data, function (response) {
        if (response.code == 200) {
          vm.studentNotes = vm.studentNotes.map(function (rp) {
            if (rp.tmpId == reply.tmpId) {
              rp = response.data;
              rp.editing = false;
              rp.showPicker = false;
              rp.hover = true;
            }
            return rp;
          });
        } else {
          alert("Đã xảy ra lỗi");
        }
      });
    },
    changeNoteColor: function (tmpId, color) {
      const vm = this;
      vm.studentNotes.forEach(function (reply) {
        if (reply.tmpId == tmpId) {
          reply.color = color;
          reply.editing = true;
          reply.showPicker = false;
        }
        return reply;
      });
    },
    focusInput: function (tmpId) {
      const vm = this;
      setTimeout(function () {
        $("#note-" + tmpId).focus();
      }, 50);
    },

    pickTag: function (cid, tagid, action) {
      const vm = this;
      var data = {
        id: cid,
        tagId: tagid,
        action: action,
      };
      $.post(
        window.location.origin + "/backend/chat/update-tag",
        data,
        function (response) {
          // console.log('tag: ' + cid + ':' + tagid);
          // console.log(action + ' _ response: ' + response);

          //update giao diện
          if (response == "success") {
            var update = _.find(vm.senders, ["id", cid]);
            if (action == "add") {
              var tag = _.find(vm.tags, ["id", tagid]);
              if (!_.find(update.tags, ["id", tagid])) {
                update.tags.push(tag);
                vm.$forceUpdate();
              }
            }
            if (action == "remove") {
              _.remove(update.tags, { id: tagid });
              vm.$forceUpdate();
            }
          }
        }
      );
    },
    listenNewMessageEvent: function (message) {
      var vm = this;

      var ids = vm.senders.map(function (sender) {
        return sender.id;
      });
      if (!ids.includes(parseInt(message.conversationId))) {
        // Add new sender
        var newSender = {
          avatar: message.senderAvatar,
          content: message.content,
          created_at: new Date(),
          creator_id: message.senderId,
          id: message.conversationId,
          last_id: -1,
          name: message.senderName,
          sender_id: message.senderId,
          sender_type: "user",
          status: 0,
          type: message.type,
        };

        vm.senders.unshift(newSender);
      } else {
        // Update list sender
        vm.senders = vm.senders.map(function (sender) {
          if (sender.creator_id == message.senderId) {
            sender.content = message.content;
            sender.created_at = new Date();
            sender.type = message.type;
            sender.last_id = -1;
            sender.status = 0;

            // Check is chatting with this user and chat box is bottom
            if (
              message.senderId == vm.currentSender.creator_id &&
              $("#screen_box").scrollTop() + $("#screen_box").innerHeight() >=
                $("#screen_box")[0].scrollHeight
            ) {
              // Set to be read
              sender.status = 1;
            }
          }
          return sender;
        });
      }
    },
    getContentImage: function (content) {
      if (typeof content == 'string') {
        return JSON.parse(content);
      }
      return content;
    }
  },

  mounted: function () {
    var vm = this;

    // Convert message's content in last message
    vm.senders = vm.senders.map(function (sender) {
      try {
        sender.content = JSON.parse(sender.content);
      } catch (error) {
        console.log("error JSON.parse message from load", error);
      }
      return sender;
    });

    // Connect socket
    if (socket) {
      vm.socket = socket;
    } else if (socketServer) {
      vm.socket = io.connect(socketServer);
    }

    // For chat normal
    vm.socket.on("send_new_message_enc", function (message) {
      message = JSON.parse(vm.decrypt(message));
      if (message.receiverId != 0) {
        return;
      }
      vm.listenNewMessageEvent(message);
    });

    // For chat with incognito
    vm.socket.on("send_new_message", function (message) {
      vm.listenNewMessageEvent(message);
    });

    if (window.location.href.split("#")[1]) {
      // Nếu link có focus vào 1 conversation cụ thể

      if (window.location.href.split("#")[1] != "") {
        vm.filterChatByUrl(window.location.href.split("#")[1]);
      }
    } else if (senders.length > 0) {
      // Init chat with first member in list

      setTimeout(function () {
        vm.initChatAtFirstTime(senders[0]);

        vm.currentSender = senders[0];
        vm.senderPage = 1;
        vm.senderLastPage = senders.last_page;
        vm.getStudentNotes();
      }, 200);
    }
    vm.getQuickReplies();

    var fileInput = document.getElementById("input_file");
    window.addEventListener("paste", (e) => {
      if (e.clipboardData.files.length > 0) {
        if (e.clipboardData.files[0].type == "image/png") {
          fileInput.files = e.clipboardData.files;
          this.sendImage();
        }
        e.preventDefault();
      }
    });
  },

  updated: function () {
    // Function run when finish render view
    // => croll to last message for first load
    var vm = this;
    if (vm.firstUpdate) {
      setTimeout(function () {
        var objDiv = document.getElementById("screen_box");
        objDiv.scrollTop = objDiv.scrollHeight;
        vm.firstUpdate = false;
      }, 1500);
    }
  },
});

function auto_grow(element) {
  element.style.height = "5px";
  element.style.height = element.scrollHeight + "px";
}

$(document).ready(function () {
  // Set height of chat body box
  setTimeout(() => {
    var headerHeight = $("#wrapper").height() + $("#navbar-sidebar").height();
    $("#page-wrapper").css("height", "calc(100vh - " + headerHeight + "px)");
    $("#page-wrapper").css(
      "min-height",
      "calc(100vh - " + headerHeight + "px)"
    );
  }, 1000);

  $.ajaxSetup({
    headers: {
      "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
    },
  });

  // Reach top of messages box => load more message
  $("#sender_box").on("scroll", function () {
    if (
      $(this).scrollTop() + $(this).innerHeight() >=
      $(this)[0].scrollHeight
    ) {
      if (app.senders.length > 0) {
        app.loadMoreSender();
      }
    }
  });

  // Reach bottom of messages box => emit read
  $("#screen_box").on("scroll", function () {
    if (
      $(this).scrollTop() + $(this).innerHeight() >=
      $(this)[0].scrollHeight
    ) {
      app.emitReaded();
    }
  });

  // Reach bottom of senders list => load more sender
  $("#screen_box").on("scroll", function () {
    var scrollTop = $(this).scrollTop();
    if (scrollTop <= 0) {
      if (app.messages.length > 0) {
        app.loadMore();
      }
    }
  });

  // Submit if enter, break line if shift enter
  // $('textarea').keyup(function(event) {
  //     if (event.keyCode == 13) {
  //         if (event.shiftKey) {
  //             return;
  //         }
  //         app.sendMessage();
  //     }
  // });

  // Focus on textarea
  $("#input_textarea").focus(function () {
    app.emitTyping();
  });

  // Click out of chat textarea
  $("#input_textarea").blur(function () {
    if (!app.message) {
      app.emitStopTyping();
    }
  });

  // After select image => send immediately
  $("#input_file").change(function () {
    if (!$(this).val()) return;
    app && app.sendImage();
  });
});

function submitMess(e) {
  var code = e.keyCode ? e.keyCode : e.which;
  if (code == 13 && !e.shiftKey) {
    app.sendMessage();
    setTimeout(function () {
      app.message = "";
    }, 20);
  }
}

var delayTimer;
// Delay thời gian nhập liệu ô tìm kiếm

// Chức năng tìm kiếm
$("#search-input").keyup(function () {
  clearTimeout(delayTimer);

  delayTimer = setTimeout(function () {
    var key = $("#search-input").val();
    if (key.length > 0) app.filterChatByKeyword(key);
    if (key.length == 0) app.clearSearch(); // Xóa tìm kiếm
  }, 500);
});

// Drag and drop file
$("html").on("dragover", function(e) {
  e.preventDefault();
  e.stopPropagation();
});
$("html").on("drop", function(e) { e.preventDefault(); e.stopPropagation(); });
$('.form-message').on('dragenter', function (e) {
  e.stopPropagation();
  e.preventDefault();
});
// Drag over
$('.form-message').on('dragover', function (e) {
  e.stopPropagation();
  e.preventDefault();
});
$('.form-message').on('drop', function (e) {
  e.stopPropagation();
  e.preventDefault();
  var files = e.originalEvent.dataTransfer.files;
  var fileInput = document.getElementById("input_file");
  if (files.length > 0) {
    if (files[0].type == "image/png") {
      fileInput.files = files;
      app && app.sendImage();
    }
  }
});
