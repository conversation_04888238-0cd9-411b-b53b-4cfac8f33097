/**
 * save data
 * @param elm
 */
function saveData(elm) {
    var idTask = $(elm).data('task');
    var task = $(idTask).val();
    switch (task) {
        case '9':
            saveDataFlashCard(9);
            break;
    }
}

/**
 * edit data
 * @param elm
 */
function editData(elm) {
    var idTask = $(elm).data('info');
    var url = $(elm).data('uri');
    var idType = $(elm).data('type');
    window.EDIT_MODE = true;
    $.ajax({
        url: url,
        type: 'get',
        data: {id: idTask},
        dataType: 'json',
        success: function (response) {
            if (response.status === 'success') {
                showData(response, idTask, idType);
            }
        }
    });
}

/**
 * show data action
 * @param response
 * @param idTask
 * @param idType
 */
function showData(response, idTask, idType) {
    var type = response.detail.type;
    $(idType).val(type);
    switch (type) {
        case 9:
            showDataFlashCard(response, idTask);
            previewFlashcard(response);
            break;
    }
}

function resetData(elm)
{
    var idTask = $(elm).data('task');
    var task = $(idTask).val();
    switch (task) {
        case '9':
            resetFlashCard();
            previewFlashcard();
            break;
    }
}
function changeLesson(id) {
    bootbox.prompt({
        title: "Nhập ID bài học",
        backdrop: true,
        callback: function (lessonId) {
            if (lessonId) {
                var data = {
                    flashcardId: id,
                    lessonId: lessonId
                };
                $.post(window.location.origin + "/backend/lesson/flashcard-lesson", data, function(response, status){
                    switch (response.code) {
                        case 404:
                            $.toast({
                                text: response.error,
                                position: 'top-right',
                                stack: false,
                                icon: 'error'
                            });
                            break;
                        case 409:
                            $.toast({
                                text: response.error,
                                position: 'top-right',
                                stack: false,
                                icon: 'info'
                            });
                            break;
                        case 200:
                            $(".item" + id).fadeOut();
                            $.toast({
                                text: "Đã chuyển flashcard sang bài học <a target='blank' href=" + window.location.origin + "/backend/lesson/" + lessonId + "/edit>#" + lessonId + "</a>",
                                position: 'top-right',
                                stack: false,
                                icon: 'success'
                            });
                            break;
                        default:
                            $.toast({
                                text: "Hệ thống gặp sự cố",
                                position: 'top-right',
                                stack: false,
                                icon: 'error'
                            });
                    }
                }).fail(function () {
                        $.toast({
                            text: "Hệ thống gặp sự cố",
                            position: 'top-right',
                            stack: false,
                            icon: 'error'
                        });
                    });
            }
            if (lessonId == "") {
                $.toast({
                    text: "Không có thay đổi",
                    position: 'top-right',
                    stack: false,
                    icon: 'info'
                });
            }
        }
    })
}
function cloneToLesson(id) {
    bootbox.prompt({
        title: "Nhập ID bài học",
        backdrop: true,
        callback: function (lessonId) {
            if (lessonId) {
                var data = {
                    flashcardId: id,
                    lessonId: lessonId
                };
                $.post(window.location.origin + "/backend/lesson/clone-flashcard-to-lesson", data, function(response, status){
                    switch (response.code) {
                        case 404:
                            $.toast({
                                text: response.error,
                                position: 'top-right',
                                stack: false,
                                icon: 'error'
                            });
                            break;
                        case 409:
                            $.toast({
                                text: response.error,
                                position: 'top-right',
                                stack: false,
                                icon: 'info'
                            });
                            break;
                        case 200:
                            $.toast({
                                text: "Đã copy flashcard sang bài học <a target='blank' href=" + window.location.origin + "/backend/lesson/" + lessonId + "/edit>#" + lessonId + "</a>",
                                position: 'top-right',
                                stack: false,
                                icon: 'success'
                            });
                            break;
                        default:
                            $.toast({
                                text: "Hệ thống gặp sự cố",
                                position: 'top-right',
                                stack: false,
                                icon: 'error'
                            });
                    }
                }).fail(function () {
                    $.toast({
                        text: "Hệ thống gặp sự cố",
                        position: 'top-right',
                        stack: false,
                        icon: 'error'
                    });
                });
            }
            if (lessonId == "") {
                $.toast({
                    text: "Không có thay đổi",
                    position: 'top-right',
                    stack: false,
                    icon: 'info'
                });
            }
        }
    })
}