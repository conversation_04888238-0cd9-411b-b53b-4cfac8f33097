var myCoursesVue = new Vue({
  el: '#my-course-content',
  data: {
    myCourses: [],
    user: userCurrent,
    lastWish: latestWish,
    routeName: routeName,
  },
  methods: {
    closeModalEvent() {
      const today = new Date().toISOString().split("T")[0];
      localStorage.setItem("modal-bd-last-opened", today);
    },
    getMyCourses() {
      var vm = this;

      if (vm.myCourses.length > 0) {
        return;
      }

      $.post(window.location.origin + '/my-courses', {
        _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      }, function (data) {
        var userHasAnyCourse = false;
        let session = data.session;
        // foreach object session
        if (typeof session === "object") {
          $("#noti-active-trail-course").removeClass("hidden");
          for (var key in session) {
            if (session.hasOwnProperty(key)) {
              console.log(`key: `, key);
              console.log(`value: `, session[key]);
              $(`#${key}`).removeClass("hidden");
              $(`#${key}`).addClass("flex");
              $(`#link_active_trail_course_${key}`).attr("href", `/khoa-hoc/${session[key]}`);
            }
          }
          $("#noti-active-trail-course-wrap").removeClass("translate-y-full");
          $("#noti-active-trail-course-wrap").addClass("translate-y-0");

          setTimeout(() => {
            $("#noti-active-trail-course-wrap").removeClass("translate-y-0");
            $("#noti-active-trail-course-wrap").addClass("translate-y-full");

            setTimeout(() => {
              $("#noti-active-trail-course-wrap").addClass("hidden");
            }, 500);
          }, 5000);
        }

        console.log(`session: `, session, session.length, typeof session);
        data = data.data;
        if (data.length > 0) {
          userHasAnyCourse = true;
        }

        for (var i = 0; i < data.length; i++) {
          data[i].expired_day = moment(data[i].watch_expired_day).format("DD/MM/YYYY");
          // if (moment(data[i].expired_day, "DD/MM/YYYY").isAfter(moment("31/08/2024", "DD/MM/YYYY"))) {
          //   userHasAnyCourse = true;
          // }
        }
        // var lastOpened = localStorage.getItem("jlpt-gift-opened");

        const today = new Date().toISOString().split("T")[0];

        const lastOpenedDate = localStorage.getItem("modal-bd-last-opened");
        
        if (!vm.lastWish && vm.routeName != 'birthday.index') {
          if (lastOpenedDate !== today) {
            $("#modalEvent").modal("show");
            localStorage.setItem("modal-bd-last-opened", today);
          }
        }

        // if (!lastOpened && userHasAnyCourse && moment().isSameOrAfter(moment("20/01/2025", "DD/MM/YYYY")) && vm.user.id === 349661) {
        //   $.fancybox.open({
        //     src: "#jlpt-gift-container",
        //     type: "inline",
        //     opts: {
        //       touch: false
        //     }
        //   });
        //
        //   localStorage.setItem('jlpt-gift-opened', '1');
        // }
        
        vm.myCourses = data;
        $('#my-course-content').css('display', vm.myCourses.length > 0 ? 'grid' : 'block');
        $('#my-course-content').css('overflow-y', vm.myCourses.length > 4 ? 'scroll' : 'auto');
      });
    },
  },
  mounted() {
    this.getMyCourses();
  }
});

$(document).keydown(function(e) {
  // ESCAPE key pressed
  if (e.keyCode == 27) {
    myCourseClick(true);
  }
});
