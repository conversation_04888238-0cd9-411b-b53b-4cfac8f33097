
var vip_result = new Vue({

    el:'#vip-result',

    components:{
        paginate: VuejsPaginate,
        'v-select': VueSelect.VueSelect,
    },

    data: {
        userGroup: [],
        courseGroups: [],
        groupId: 'Chọn nhóm',
        comGrId: '',
        courseId: '',
        examGroup: '',
        userResultList: '',
        sort: 'desc',
        loading: false,
        dataCheck: false,
        keywords:'',
        today: moment().format('YYYY-MM-DD HH:mm:ss'),
        from: null,
        to: null,
        completed: null
    },

    computed: {
      filteredUserGroup: function () {
          const vm = this
          if (this.completed === null || this.completed === '') {
              return this.userGroup
          }
          return this.userGroup.filter(function (user) {
              return parseInt(user.totalResult) === parseInt(vm.completed);
          })
      }
    },
    watch:{
        courseId:function (newVal){
            if(newVal){
                this.getVipGroupByCourse(newVal);
                this.courseId = newVal;
            }else {
                this.comGrId = '';
                this.courseGroups = [];
            }
        },
        sort:function(newval){
            this.sortUserGroup(newval);
        },
    },

    methods:{
        floorPoint(point, decimal) {
            return point ? _.round(point,decimal) : 0;
        },

        sortUserGroup: function(sort){
            var vm = this;
            vm.userGroup =  _.orderBy(vm.userGroup, ['avaragePoint'], [sort]);
        },

        changePage: function (pageNumber) {
            var vm = this;
            vm.paginate.page = pageNumber;

            var filter = _.omit(_.pickBy(vm.paginate, function (value, key) {
                console.log(value)
                return value !== undefined && value !== null && value !== '';
            }), ['total_page']);

            this.getUserGroup(vm.groupId)
        },

        searchUser: function(){
            this.getUserGroup()
        },

        refreshFilter: function (){
            this.keywords = '';
            this.userGroup = [];
            this.getUserGroup();
        },

        selectedCourseGroup: function(value){
            var vm = this;
            if (value) {
                vm.comGrId = value.id
                vm.getUserGroup();
            }
            return;
        },

        getVipGroupByCourse: function (course) {
            var vm = this;
            var params = {
                course: course,
            }

            axios.get(window.location.origin + "/backend/vip/get-vip-group", {params})
                .then(function (res) {
                    if (res.data) {
                        vm.courseGroups = res.data;
                        vm.groupId = 'Chọn nhóm';
                    }
                });
        },

        getUserGroup: function () {
            var vm = this;
            vm.loading = true;
            var params = {
                group_id: vm.comGrId,
                keywords: vm.keywords,
                from: vm.from,
                to: vm.to
            }

            axios.get(window.location.origin + "/backend/vip/get-user-group", {params})
                .then(function (res) {
                    if(res.status = 200) {
                        vm.examGroup = res.data.countExamGroup;
                        vm.loading = false;

                        vm.userGroup = res.data.userExam.map(function (user) {
                            user.avaragePoint = 0;
                            user.totalResult = 0;
                            res.data.userResult.find(function (result) {
                                if (result.user_id == user.id) {
                                    user.avaragePoint = Number.parseFloat(result.avarage_point);
                                    user.totalResult = result.total_result;
                                }
                            })
                            return user;
                        })
                        vm.userGroup =  _.orderBy(vm.userGroup, ['avaragePoint'], ['desc']);
                        if (vm.userGroup.length <= 0) {
                            vm.dataCheck = true;
                        }
                        else {
                            vm.dataCheck = false;
                        }
                    }
                });
        },

        showResults: function (userId) {
            var vm = this;
            var params = {
                user_id: userId,
                group_id: vm.comGrId,
            }
            axios.get(window.location.origin + "/backend/vip/get-vipbucg", {params})
                .then(function (response) {
                    if (response.status == 200) {
                        console.log(response.data)
                        const temp = response.data;
                        for (const item of temp) {
                            item.time1 = null;
                            item.time2 = null;
                            item.time3 = null;
                            if (item.time_start) {
                                const timeStartArr = JSON.parse(item.time_start);
                                if (timeStartArr.length > 0) {
                                    item.time1 = moment(timeStartArr[0]).format('YYYY-MM-DD HH:mm:ss');
                                }
                                if (timeStartArr.length > 1) {
                                    item.time2 = moment(timeStartArr[1]).format('YYYY-MM-DD HH:mm:ss');
                                }
                                if (timeStartArr.length > 2) {
                                    item.time3 = moment(timeStartArr[2]).format('YYYY-MM-DD HH:mm:ss');
                                }
                            }
                        }
                        vm.userResultList = response.data;
                    }
                })
        },

        isPassed: function(result) {
            if (result) {
                return result.is_passed;
            }
            return false;
        },

        exportVipUserGroup: function (){
            var vm = this;

            axios.post(window.location.origin + '/backend/vip/export-user-result', {comgr_id: vm.comGrId, from: vm.from,
                to: vm.to})
                .then( function (res) {
                    if (res.status == 200){
                        window.location = '/storage/upload/excel/vip_results.xlsx';
                    }
                })
        },
    },
});

