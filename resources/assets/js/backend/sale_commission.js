$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

const router = new VueRouter({
    mode: 'history'
});

Vue.filter('currency', function (value) {
    return value ? parseFloat(value).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + ' ₫': 0;
});

var sale__commission = new Vue({
    el: '#sale__commission--screen',
    components: {
        paginate: VuejsPaginate
    },
    data() {
        return {
            currentBillImage: '',
            admins: [],
            invoices: [],
            loading: false,
            filter: {
                id: undefined,
                email: undefined,
                invoice_status: 'completed',
                time_type: 'created_at',
                time_from: undefined,
                time_to: undefined,
                page: 1,
                per_page: 20,
                total_page: 10
            }
        };
    },

    watch: {
        filter: {
            handler: function (val, oldVal) {
                var vm = this;
                vm.filter.page = parseInt(val.page);
            },
            deep: true
        }
    },
    methods: {
        // g<PERSON>i hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
        changePage: function (pageNum) {
            var vm = this;
            vm.filter.page = pageNum;
            var filter = _.omit(_.pickBy(vm.filter, _.identity), ['total_page']);
            vm.getInvoices(filter);

            router.replace(window.location.pathname + "?" + $.param(filter));
        },
        // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
        applyFilter: function () {
            var vm = this;

            vm.filter.page = 1;
            vm.filter.per_page = 20;
            var filter = _.omit(_.pickBy(vm.filter, _.identity), ['total_page', 'page', 'per_page']);
            vm.getInvoices(filter);

            router.replace(window.location.pathname + "?" + $.param(filter));
        },
        // call api lấy danh sách invoices
        // truyền vào filter
        // set các biến trạng thái tmp để có thể dùng v-if, v-model
        getInvoices: function(filter) {
            var vm = this;

            vm.loading = true;
            setTimeout(function () {
                $.get(window.location.origin +'/backend/sale-commission', filter, function (res) {
                    if(res.code == 200) {
                        vm.invoices = res.data.invoices.map(function (invoice) {
                            invoice.revenueEditing = false;
                            invoice.chatLinkEditing = false;
                            invoice.previewBill = null;
                            invoice.info_contact = JSON.parse(invoice.info_contact);

                            vm.admins.forEach(function (admin) {
                                if (admin.id == invoice.sale_1) {
                                    invoice.sale_1_info = admin;
                                }
                                if (admin.id == invoice.sale_2) {
                                    invoice.sale_2_info = admin;
                                }
                            });
                            return invoice;
                        });
                        vm.filter.total_page = res.data.total_page;
                    } else {
                        alert('Có lỗi! Liên hệ dev!!!');
                    }
                    vm.loading = false;
                });
            }, 200);
        },
        // call api lấy danh sách admin để đổ vào select
        getAdmins: function() {
            var vm = this;

            $.get(window.location.origin +'/backend/sale-commission/getAllAdmin', function (res) {
                if(res.code == 200) {
                    vm.admins = res.data;
                } else {
                    alert('Có lỗi! Liên hệ dev!! :D ')
                }
            });
        },
        // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
        setFilterByUrl: function () {
            var vm = this;
            var filterByUrl = $.deparam.querystring();
            _.forEach(filterByUrl, function (value, key) {
                vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
            });
        },
        // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
        resetFilter: function () {
            var vm = this;
            vm.filter = {
                id: undefined,
                email: undefined,
                invoice_status: 'completed',
                time_type: 'created_at',
                time_from: undefined,
                time_to: undefined,
                page: 1,
                per_page: 20,
                total_page: 10
            };

            this.applyFilter();
        },
        // thay đổi filter theo datetime
        onChangeDatetime: function (event) {
            var vm = this;
            vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm:ss');
        },
        // apply thay đổi sale_1, sale_2
        changeSale: function (id, param, event) {
            var vm = this;
            var data = {
                invoice_id: id,
                param: param,
                sale_id: _.isNumber(event) ? event : event.target.value
            };
            $.post(window.location.origin +'/backend/sale-commission/changeSale', data, function (res) {
                if (res.code == 200) {
                    vm.invoices = vm.invoices.map(function (invoice) {
                        if (invoice.id == res.data.id) {
                            invoice[param] = res.data[param];
                            if (event == 0) {
                                invoice[param] = null;
                                invoice[param + '_info'] = {};
                            }
                        }

                        vm.admins.forEach(function (admin) {
                            if (admin.id == invoice.sale_1) {
                                invoice.sale_1_info = admin;
                            }
                            if (admin.id == invoice.sale_2) {
                                invoice.sale_2_info = admin;
                            }
                        });
                        return invoice;
                    });
                } else {
                    alert('Có lỗi! Liên hệ dev!!')
                }
            });
        },
        // apply thay đổi số tiền thực nhận
        applyRevenue: function (invoiceId, event) {
            vm = this;

            var data = {
                id: invoiceId,
                revenue: event.target.value
            };

            $.post(window.location.origin +'/backend/sale-commission/applyRevenue', data, function (res) {
                if(res.code == 200) {
                    vm.invoices = vm.invoices.map(function (invoice) {
                        if (invoice.id == res.data.id) {
                            invoice.revenue = res.data.revenue;
                        }
                        return invoice;
                    });
                }
            });
        },
        focusInput: function(idx, ref) {
            vm = this;
            vm.$nextTick(() => {
                vm.$refs[ref][idx].focus();
            });
        },
        // apply thay đổi chat_link
        saveChatLink: function (invoiceId, event) {
            vm = this;

            var data = {
                id: invoiceId,
                chatLink: event.target.value
            };

            $.post(window.location.origin +'/backend/sale-commission/save-chat-link', data, function (res) {
                if(res.code == 200) {
                    vm.invoices = vm.invoices.map(function (invoice) {
                        if (invoice.id == res.data.id) {
                            invoice.chat_link = res.data.chat_link;
                        }
                        return invoice;
                    });
                }
            });
        },
        // fake thao tác click vào input hidden có id theo invoiceId
        editBillImage: function (invoiceId) {
            $("#inputBillImage" + invoiceId).click();
        },
        // convert ảnh theo base64
        readURL: function (invoiceId, input) {
            vm = this;
            if (input.target.files && input.target.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    vm.invoices = vm.invoices.map(function (invoice) {
                        if(invoice.id == invoiceId) {
                            invoice.previewBill = e.target.result;
                        }
                        return invoice;
                    });
                };
                reader.readAsDataURL(input.target.files[0]);
            }
        },
        // apply thay đổi ảnh bill
        saveBillImage: function (invoiceId, previewImage) {
            vm = this;
            var data = new FormData($("#bill-image-form-" + invoiceId)[0]);
            $.ajax({
                url: window.location.origin + '/backend/sale-commission/save-bill',
                type: 'post', processData: false, contentType: false, data: data,
                success: function (res) {
                    if (res.code == 200) {
                        vm.invoices = vm.invoices.map(function (invoice) {
                            if (invoice.id == res.data.id) {
                                invoice.previewBill = null;
                                invoice.bill_image = res.data.bill_image;
                            }
                            return invoice;
                        });
                    } else {
                        alert(res.msg);
                    }
                }
            });
        },
        // xuất file excel
        exportFile: function () {
            vm = this;
            var url = window.location.origin + "/backend/sale-commission/export" + window.location.search;
            window.open(url);
        }
    },
    mounted: function() {
        var vm = this;

        vm.setFilterByUrl();

        this.getAdmins();
        var filter = _.omit(_.pickBy(vm.filter, _.identity), ['total_page']);
        this.getInvoices(filter);
    },
});