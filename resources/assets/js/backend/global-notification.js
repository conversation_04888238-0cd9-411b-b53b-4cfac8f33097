$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

var notices = new Vue({

    el: '.notification__screen',

    data: {
        url: window.location.origin, //đường dẫn host

        notifications: [],
        courses: [],
        tableIds: [],
        // logs: [],
        previewImg: undefined,
        noticeId: undefined,
        noticeUrl: undefined,
        segments: 'Test Segment',
        isSchedule: false,
        scheduleTime: undefined,

        input: {
            id: undefined,
            title: '',
            content: '',
            bg_img: undefined,
            tableName: undefined,
            tableId: undefined,
        },

        loading: false,

        defaultInput: {
            id: undefined,
            title: '',
            content: '',
            bg_img: undefined,
            tableName: undefined,
            tableId: undefined,
        },
        tmpInput: '',
        errors: {}
    },

    methods: {
        // Lấy id của thông báo cần push set vào vm.noticeId
        setDataToPush: function(id) {
            vm = this;
            vm.noticeId = id;
        },
        // Call api push thông báo
        // segments: nhóm người dùng cần push
        pushNotice: function() {
            vm = this;

            var scheduleTime = undefined;
            if(vm.isSchedule && vm.scheduleTime) {
                scheduleTime = vm.scheduleTime;
            }
            setTimeout(function() {
                $.post(window.location.origin + "/backend/global-notification/push", {id: vm.noticeId, segments: [vm.segments], scheduleTime: scheduleTime}, function (response) {
                    vm.notifications = vm.notifications.map(function (notification) {
                        if (notification.id == response.data.id) {
                            notification = response.data;
                        }
                        return notification;
                    });
                    $('#pushModal').modal('hide');
                });
            }, 500);
        },

        // event khi thay đổi select
        onChangeSelect: function (event) {
            vm = this;
            vm[event.target.name] = event.target.value;
            if (event.target.name == 'input.tableName') {
                vm.input.tableId = undefined;
                $("#tableIdForm").empty().trigger('change')
            }
        },

        // event khi thay đổi input text
        onChangeText: function (event) {
            vm = this;
            vm[event.target.name] = event.target.value;
        },

        // event khi thay đổi input file thì preview image
        onChangeImage: function (event) {
            vm = this;
            vm.input.bg_img = event.target.files[0];
            if (event.target.files && event.target.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    $('#previewImg').attr('src', e.target.result);
                };

                reader.readAsDataURL(event.target.files[0]); // convert to base64 string
            }
        },
        onChangeDatetime: function (event) {
            vm = this;
            vm.scheduleTime = moment(event.date).format('YYYY-MM-DD HH:mm:ss');
        },

        // event submit form
        onSubmitForm: function () {
            vm = this;

            vm.loading = true;

            // clear validation
            vm.errors = {};
            vm.$forceUpdate();

            var data = new FormData();
            data.append('id', vm.input.id);
            data.append('title', vm.input.title);
            data.append('content', vm.input.content);
            data.append('bg_img', vm.input.bg_img);
            data.append('tableName', vm.input.tableName);
            data.append('tableId', vm.input.tableId);

            if (!vm.input.id) {
                vm.storeNotice(data);
            } else {
                vm.updateNotice(data);
            }
        },

        // call api tạo thông báo
        storeNotice: function(data) {
            setTimeout(function() {
                $.ajax({
                    url: window.location.origin + "/backend/global-notification/store",
                    type: "POST",
                    data: data,
                    processData: false,
                    contentType: false,
                    success: function(response){
                        if (response.hasOwnProperty('errors')) {
                            _.forEach(response.errors, function (err, key) {
                                vm.errors[key] = vm.translateError(err, key);
                            });
                        } else {
                            // reset lại form
                            vm.errors = {};
                            vm.input = vm.defaultInput;
                            vm.previewImg = undefined;
                            $('#previewImg').attr('src', vm.url + '/assets/img/icon_backend/no_image.png');

                            // thêm thông báo mới vào danh sách
                            var newNotification = response.data;
                            newNotification.info = JSON.parse(newNotification.data);
                            vm.notifications.unshift(newNotification);
                            vm.onResetForm();
                        }
                        // render lại
                        vm.loading = false;
                    }
                });
            }, 500);
        },

        updateNotice: function(data) {
            setTimeout(function() {
                $.ajax({
                    url: window.location.origin + "/backend/global-notification/update",
                    type: "POST",
                    data: data,
                    processData: false,
                    contentType: false,
                    success: function(response){
                        if (response.hasOwnProperty('errors')) {
                            _.forEach(response.errors, function (err, key) {
                                vm.errors[key] = vm.translateError(err, key);
                            });
                        } else {
                            vm.errors = {};
                            vm.input = vm.defaultInput;
                            vm.notifications = vm.notifications.map(function (notification) {
                                if (notification.id == response.data.id) {
                                    notification = response.data;
                                }
                                return notification;
                            });
                            vm.onResetForm();
                        }
                        vm.loading = false;
                        vm.$forceUpdate();
                    }
                });
            }, 500);
        },

        editNotice: function(id) {
            vm = this;

            var notification = _.find(vm.notifications, ['id', id]);

            vm.input.id = notification.id;
            vm.input.title = notification.title;
            vm.input.content = notification.content;
            vm.input.tableName = notification.table_name;
            vm.input.tableId = notification.table_id;
            vm.previewImg = notification.bg_img;

            $('#tableIdForm').val(10).trigger('change');
        },

        // xoá thông báo
        removeNotice: function(id) {
            vm = this;
            var confirm = window.confirm('Xác nhận xoá thông báo này');

            if (confirm)
            setTimeout(function() {
                $.post(window.location.origin + "/backend/global-notification/destroy", {id: id}, function (response) {
                    if (response.code == 200) {
                        vm.notifications = vm.notifications.filter(function (notification) {
                            return notification.id != response.data.id
                        })
                    }
                    if (response.code == 403) {
                        alert(response.error);
                    }
                });
            }, 500);
        },

        // Cập nhật lại form
        onResetForm: function() {
            vm = this;
            vm.courses = [];
            vm.tableIds = [];
            vm.previewImg = undefined;
            $('#previewImg').attr('src', vm.url + '/assets/img/icon_backend/no_image.png');
            vm.segments = 'Test Segment';
            vm.input = vm.defaultInput;
            vm.errors = {};
            vm.$forceUpdate();
        },
        // Hiển thị label của input nhập id đối tượng
        renderTableIdLabel: function () {
            vm = this;
            switch (vm.input.tableName) {
                case 'lesson':
                    return 'bài học';
                case 'course':
                    return 'khoá học';
                case 'combo':
                    return 'combo';
                case 'blog':
                    return 'bài viết';
                default:
                    return "";
            }
        },
        // Render lỗi
        translateError: function (err, obj) {
            var error = '';
            var object = '';

            err = _.join(err);
            switch (obj) {
                case 'title':
                    object = 'Tiêu đề';
                    break;
                case 'content':
                    object = 'Nội dung';
                    break;
                default:
                    object = ''
            }
            switch (err) {
                case "validation.required":
                    error = 'không được để trống';
                    break;
                default:
                    error = '';
            }
            return object + ' ' + error;
        },

        // Hiển thị thời gian
        printTime: function(time) {
            vm = this;
            time = moment(time * 1000).format('HH:mm DD/MM/YYYY ');
            return time;
        },

        // sortHistory: function () {
        //     vm.notifications.forEach(function (notification) {
        //         notification.logs = _.orderBy(notification.logs, 'time', 'desc')
        //     })
        // },

        // Tạo select2 để search ajax đối tượng theo tên
        renderSelect: function () {
            $(document).ready(function () {
                $('#tableIdForm').select2({
                    minimumInputLength: 3,
                    ajax: {
                        url: window.location.origin + '/api/fetch-table-id',
                        type: 'POST',
                        dataType: 'json',
                        allowClear: true,
                        data: function (params) {
                            return {
                                tableName: vm.input.tableName,
                                keyWord: params.term
                            };
                        },
                        processResults: function (data, params) {
                            return {
                                results: $.map(data.data, function (item) {
                                    return {
                                        text: '#' + item.id + ' - ' + (item.name || item.title),
                                        id: item.id,
                                        data: item
                                    };
                                })
                            };
                        }
                    }
                }).val(vm.input.tableId)
                    .trigger("change")
                    // emit event on change.
                    .on("change", function(e) {
                        vm.input.tableId = e.target.value
                    });
            });
        },

        // in ra log, đang tạm bỏ
        // printLogs: function (logs) {
        //     vm = this;
        //     vm.logs = logs;
        // },


        // Hiển thị tag table_name
        printTableName: function (tableName) {
            vm = this;
            switch (tableName) {
                case null:
                    return 'Không dẫn link';
                case undefined:
                    return 'Không dẫn link';
                case 'lesson':
                    return 'Bài học';
                case 'course':
                    return 'Khoá học';
                case 'blog':
                    return 'Bài viết';
                case 'combo':
                    return 'Combo';
                case 'sale':
                    return 'Sale';
                case 'booking':
                    return 'Booking kaiwa';
                case 'tips':
                    return 'Tips';
            }
        },
        cancelSchedule(pushId, noticeId) {
            $.ajax({
                url: '/backend/global-notification/cancel-schedule',
                type: 'POST',
                data: {
                    pushId: pushId,
                    noticeId: noticeId
                },
                success: function(response) {
                    vm.notifications = vm.notifications.map(function (notification) {
                        if (notification.id == response.data.id) {
                            notification = response.data;
                        }
                        return notification;
                    })
                }
            });
        },
        setEditingMode: function (notifications) {
            notifications.forEach(function (notification) {
                notification.headingEditing = false;
                notification.titleEditing = false;
                return notification;
            });
            return notifications;
        },

        editHeading: function (notification) {
            vm = this;
            vm.tmpInput = notification.info.heading;
            notification.headingEditing = true
        },

        cancelHeading: function (notification) {
            vm = this;
            notification.info.heading = vm.tmpInput;
            notification.headingEditing = false
        },

        editTitle: function (notification) {
            vm = this;
            vm.tmpInput = notification.title;
            notification.titleEditing = true
        },

        cancelTitle: function (notification) {
            vm = this;
            notification.title = vm.tmpInput;
            notification.titleEditing = false
        },

        saveHeading: function (notification) {
            vm = this;
            if (notification.info.logs && notification.info.logs.length != 0) {
                vm.cancelHeading(notification);
            }
            else if (notification.info.heading != '') {
                $.ajax({
                    url: '/backend/global-notification/update-heading',
                    type: 'POST',
                    data: {
                        id: notification.id,
                        heading: notification.info.heading
                    },
                    success: function(response) {
                        notification.headingEditing = false;
                        console.log(response);
                        // vm.notifications = vm.notifications.map(function (notification) {
                        //     if (notification.id == response.data.id) {
                        //         notification = response.data;
                        //     }
                        //     return notification;
                        // })
                    }
                });
            }

        },
        saveTitle: function (notification) {
            if (notification.info.logs && notification.info.logs.length != 0) {
                vm.cancelTitle(notification);
            }
            else if (notification.info.heading != '') {
                $.ajax({
                    url: '/backend/global-notification/update-content',
                    type: 'POST',
                    data: {
                        id: notification.id,
                        title: notification.title
                    },
                    success: function (response) {
                        notification.titleEditing = false;
                        console.log(response);
                        // vm.notifications = vm.notifications.map(function (notification) {
                        //     if (notification.id == response.data.id) {
                        //         notification = response.data;
                        //     }
                        //     return notification;
                        // })
                    }
                });
            }
        }
    },

    mounted: function () {
        vm = this;

        vm.notifications = vm.setEditingMode(notifications);
        // vm.sortHistory();
        vm.renderSelect();
    }
});
