var user = new Vue({
  el: "#new-dashboard",

  data: function () {
    return {
      url: window.location.origin,
      totalGender: [],
      genderByCourse: [],
      responseRate: {},
      receivedMessages: [],
      repliedMessages: [],
      sentAdminMessages: [],
      repliedAdminMessages: [],
      userAdminMessagePairs: [],

      frontendReceivedMessages: [],
      frontendRepliedMessages: [],
      frontendSentAdminMessages: [],
      frontendRepliedAdminMessages: [],
      frontendUserAdminMessagePairs: [],

      receivedComments: [],
    };
  },
  computed: {
    totalReplyTime() {
      let total = 0;
      this.userAdminMessagePairs.forEach((o) => {
        total += moment
          .duration(moment(o.admin.created_at).diff(o.user.created_at))
          .asMinutes();
      });
      return total;
    },
    repliedComments() {
      return this.receivedComments.filter((cmt) => {
        return cmt.first_admin_reply;
      });
    },
    totalReplyCommentTime() {
      let total = 0;
      this.receivedComments.forEach((cmt) => {
        if (cmt.first_admin_reply) {
          total += moment
            .duration(
              moment(cmt.first_admin_reply.created_at).diff(
                moment(cmt.created_at)
              )
            )
            .asMinutes();
        }
      });
      return total;
    },
    longestReplyTime() {
      let max = 0;
      this.userAdminMessagePairs.forEach((o) => {
        const time = moment
          .duration(moment(o.admin.created_at).diff(o.user.created_at))
          .asMinutes();
        if (time > max) {
          max = time;
        }
      });
      return max;
    },
    longestReplyCommentTime() {
      let max = 0;
      this.receivedComments.forEach((cmt) => {
        let time = 0;
        if (cmt.first_admin_reply) {
          time = moment
            .duration(
              moment(cmt.first_admin_reply.created_at).diff(cmt.created_at)
            )
            .asMinutes();
        }
        if (time > max) {
          max = time;
        }
      });
      return max;
    },
    frontendTotalReplyTime() {
      let total = 0;
      this.frontendUserAdminMessagePairs.forEach((o) => {
        total += moment
          .duration(moment(o.admin.created_at).diff(o.user.created_at))
          .asMinutes();
      });
      return total;
    },
    frontendLongestReplyTime() {
      let max = 0;
      this.frontendUserAdminMessagePairs.forEach((o) => {
        const time = moment
          .duration(moment(o.admin.created_at).diff(o.user.created_at))
          .asMinutes();
        if (time > max) {
          max = time;
        }
      });
      return max;
    },
  },
  mounted: function () {
    const vm = this;
    vm.getAllGender();
    vm.getGenderByCourse();
    vm.getResponseRate();
    vm.getCommentRate();
  },
  methods: {
    getResponseRate: function () {
      const vm = this;
      $.get(vm.url + "/backend/new-dashboard/response-rate", function (res) {
        vm.receivedMessages = res.receivedMessages;
        vm.repliedMessages = res.repliedMessages;
        vm.sentAdminMessages = res.sentAdminMessages;
        vm.repliedAdminMessages = res.repliedAdminMessages;
        vm.userAdminMessagePairs = res.userAdminMessagePairs;

        vm.frontendReceivedMessages = res.frontendReceivedMessages;
        vm.frontendRepliedMessages = res.frontendRepliedMessages;
        vm.frontendSentAdminMessages = res.frontendSentAdminMessages;
        vm.frontendRepliedAdminMessages = res.frontendRepliedAdminMessages;
        vm.frontendUserAdminMessagePairs = res.frontendUserAdminMessagePairs;
      });
    },
    getCommentRate: function () {
      const vm = this;
      $.get(vm.url + "/backend/new-dashboard/comment-rate", function (res) {
        vm.receivedComments = res.receivedComments;
      });
    },
    getAllGender: function () {
      const vm = this;
      $.get(vm.url + "/backend/new-dashboard/total-gender", function (res) {
        vm.totalGender = res;
      });
    },
    getGenderByCourse: function () {
      const vm = this;
      $.get(vm.url + "/backend/new-dashboard/gender-by-course", function (res) {
        var data = _.map(res.data, function (value, key) {
          value = _.map(value, function (value, key) {
            var gender = {
              name: key ? key : "Khác",
              y: value,
            };
            return gender;
          });
          var course = {
            name: key,
            genders: _.orderBy(value, "name", "desc"),
          };
          return course;
        });
        vm.genderByCourse = _.orderBy(data, "name", "asc");
      });
    },
  },
});
