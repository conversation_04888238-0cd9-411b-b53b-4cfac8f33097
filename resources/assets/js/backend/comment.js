$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});

var cmts = new Vue({
  el: ".main-comment",

  data: {
    url: window.location.origin, //đường dẫn host

    filterKeywords: "",
    filterSeen: "all",
    filterCategory: "all",

    previewId: null,
    previewName: null,
    previewEmail: null,

    adminLog: [],

    startDate: null,
    endDate: null,

    listComments: comments.data,
    countResults: comments.total,

    page: "1",
    filterRangeCheck: false,
  },

  methods: {
    //in ra thông tin có dấu cách
    printInfo: function (info) {
      var result = _.escape(info);

      result = result.replace("<", "&#60;");
      result = result.replace(">", "&#62;");

      //xử lý xuống dòng
      result = info.replace(new RegExp("\r?\n", "g"), "<br />");

      var re =
        /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;
      return result.replace(re, function (match, lParens, url) {
        var rParens = "";
        lParens = lParens || "";

        // Try to strip the same number of right parens from url
        // as there are left parens.  Here, lParenCounter must be
        // a RegExp object.  You cannot use a literal
        //     while (/\(/g.exec(lParens)) { ... }
        // because an object is needed to store the lastIndex state.
        var lParenCounter = /\(/g;
        while (lParenCounter.exec(lParens)) {
          var m;
          // We want m[1] to be greedy, unless a period precedes the
          // right parenthesis.  These tests cannot be simplified as
          //     /(.*)(\.?\).*)/.exec(url)
          // because if (.*) is greedy then \.? never gets a chance.
          if ((m = /(.*)(\.\).*)/.exec(url) || /(.*)(\).*)/.exec(url))) {
            url = m[1];
            rParens = m[2] + rParens;
          }
        }
        return (
          lParens +
          "<a href='" +
          url +
          "' target='_blank'>" +
          url +
          "</a>" +
          rParens
        );
      });
    },

    //loadmore
    loadMore: function (page) {
      console.log("tìm kiếm ");
      const vm = this;
      vm.page = page;
      $(".paginate-item").removeClass("active");
      $("#paginate-item-" + page).addClass("active");
      if (vm.filterRangeCheck) {
        vm.filterRange();
      } else {
        vm.updateResults();
      }
    },

    //lọc kết quả theo điều kiện được chọn
    updateResults: function () {
      // console.log("tìm kiếm ");
      const vm = this;
      vm.filterRangeCheck = false;
      $.post(
        window.location.origin + "/backend/binhluan/filter",
        {
          filterPage: vm.page,
          filterSeen: vm.filterSeen,
          filterCategory: vm.filterCategory,
          filterKeywords: vm.filterKeywords,
        },
        function (response) {
          console.log("kết quả tìm kiếm ", response);

          vm.listComments = response.results;
          vm.countResults = response.countResults;
        }
      );
    },

    // result when filter date range
    filterRange: function () {
      // console.log("tìm kiếm ");
      const vm = this;
      vm.startDate = $("input[name=daterangepicker_start]").val();
      vm.endDate = $("input[name=daterangepicker_end]").val();

      console.log($("#product-select").val());
      vm.filterRangeCheck = true;
      $.get(
        window.location.origin + "/backend/binhluan/api/filter-range",
        {
          start_date: vm.startDate,
          end_date: vm.endDate,
          level: $("#product-select").val(),
          page: vm.page,
        },
        function (response) {
          console.log("kết quả tìm kiếm ", response);

          vm.listComments = response.results;
          vm.countResults = response.countResults;
        }
      );
    },

    //preview ảnh đính kèm khi đăng reply mới
    previewImageReply: function (event) {
      // console.log("event: ", event.target.id);
      // commentImagePicked64421

      //lấy ra comment id từ event
      var cmtId = event.target.id.split("commentImagePicked")[1];
      // console.log("cmtid: ", cmtId);

      const vm = this;
      var input = event.target;
      if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = (e) => {
          $("#preview-image-cmt-" + cmtId).html(
            '<img src="' + e.target.result + '"/>'
          );
        };
        reader.readAsDataURL(input.files[0]);
      }
    },

    //đăng reply mới
    postNewAnswer: function (parent_id) {
      const vm = this;

      // bỏ qua comment rỗng
      if (
        $("#reply-input-content-" + parent_id).val() == null ||
        $("#reply-input-content-" + parent_id).val() == undefined ||
        $("#reply-input-content-" + parent_id)
          .val()
          .trim() == ""
      ) {
        alert("Vui lòng nhập nội dung");
        return;
      }

      var file_data = $("#commentImagePicked" + parent_id).prop("files")[0];
      var form_data = new FormData();
      form_data.append("parent_id", parent_id);
      form_data.append("content", $("#reply-input-content-" + parent_id).val());
      form_data.append("file", file_data);

      setTimeout(function () {
        $.ajax({
          url: window.location.origin + "/backend/binhluan/api/add-new-reply",
          type: "POST",
          data: form_data,
          contentType: false,
          cache: false,
          processData: false,
          success: function (response) {
            console.log(response);
            if (response == "imagesize")
              alert("ảnh vượt quá dung lượng cho phép");
            else if (response == "type") alert("định dạng ảnh không cho phép");
            else {
              var indexOfComment = 0; //thứ tự của comment đang reply
              for (var i = 0; i < vm.listComments.length; i++)
                if (vm.listComments[i].id == parent_id) indexOfComment = i;

              //console.log(indexOfComment);
              vm.listComments[indexOfComment].replies.push(response);

              //dánh dấu đã xem cho cmt cha
              vm.listComments[indexOfComment].readed = 1;

              $("#reply-input-content-" + parent_id).val("");
              $("#reply-input-content-" + parent_id).css("height", "42px");
              $("#preview-image-cmt-" + parent_id).html(""); //xóa ảnh preview
              $("#commentImagePicked" + parent_id).val(""); //reset lại nút chọn ảnh
            }
          },
        });
      }, 500);
    },

    //hàm đánh dấu là đã đọc
    markAsRead: function (id) {
      const vm = this;

      $.post(
        window.location.origin + "/backend/binhluan/api/mark-as-read",
        { id: id },
        function (response) {
          if (response == "success") {
            console.log("đánh dấu đã xem: " + id);
            for (var i = 0; i < vm.listComments.length; i++) {
              if (vm.listComments[i].id == id) {
                vm.listComments[i].readed = 1;
                break;
              }
            }
          } else {
            alert("đánh dấu bị lỗi");
          }
        }
      );
    },

    //xóa comment theo id
    delComment: function (id) {
      $.post(
        window.location.origin + "/backend/binhluan/api/delete-comment",
        { id: id },
        function (response) {
          if (response == "success") {
            console.log("xóa thành công: " + id);
            $(".item-" + id).fadeOut();
          } else {
            alert("xóa lỗi");
          }
        }
      );
    },

    //xóa reply theo id
    delReply: function (id) {
      $.post(
        window.location.origin + "/backend/binhluan/api/delete-reply",
        { id: id },
        function (response) {
          if (response == "success") {
            $("#reply-item-" + id).fadeOut();
          } else {
            alert("xóa lỗi");
          }
        }
      );
    },

    previewUser: function (id, name, email) {
      console.log("preview user", id);

      const vm = this;
      vm.previewId = id;
      vm.previewName = name;
      vm.previewEmail = email;
    },

    viewAdminLog: function (logs) {
      const vm = this;
      console.log("admin log", logs);
      vm.adminLog = JSON.parse(logs);
    },

    viewAdminName: function (logs) {
      if (logs != "" && logs != null) return JSON.parse(logs)[0].admin;

      return "Noname";
    },

    //hàm admin sửa cmt
    editAdminComment: function (id, content) {
      const vm = this;
      console.log("hiện sửa cmt", id);
      console.log("hiện sửa cmt content", content);

      //gán giá trị cần sửa
      $("#edit-comment-area").val(content);
      $("#edit-comment-id").val(id);
    },

    //hàm lưu lại giá trị sửa
    saveAdminComment: function () {
      const vm = this;

      var content = $("#edit-comment-area").val();
      var id = $("#edit-comment-id").val();

      console.log("lưu giá trị id", id);
      console.log("lưu giá trị mới", content);

      $.post(
        window.location.origin + "/api/comments/edit-comment",
        { id: id, content: content },
        function (response) {
          if (response == "success") {
            console.log("lưu thành công");

            //cập nhật lại giá trị trên giao diện
            $("#child-comment-content-" + id).html(vm.printInfo(content));

            $.fancybox.close();
          } else {
            alert("lưu thất bại");
          }
        }
      );
    },

    //cancel đóng popup nếu không muốn sửa cmt
    cancelAdminComment: function () {
      $.fancybox.close();
    },

    // ghim comment len dau
    pinComment: function (comment) {
      var vm = this;
      var data = {
        id: comment.id,
      };
      $.post(
        window.location.origin + "/backend/binhluan/api/pin",
        data,
        function (res) {
          if (res.code === 200) {
            vm.listComments.forEach(function (comment) {
              if (comment.id === res.data.id) {
                comment.pin = res.data.pin;
              }
            });
          }
        }
      );
    },

    //tạo cuộc hội thoại với user chưa có
    initConversation: function (userid) {
      $.ajax({
        type: "post",
        url: window.location.origin + "/backend/user/create-conversation",
        data: {
          _token: $("input[name=_token]").val(),
          id: userid,
        },
        success: function (response) {
          // console.log("Tạo hội thoại mới", response);
          $(".fa-comments-" + userid).css("color", "#00ab2e");
          window.open(
            window.location.origin + "/backend/chat#" + response,
            "_blank"
          );
        },
      });
    },
  },
  mounted: function () {
    setTimeout(function () {
      new EmojiPicker();
    }, 100);
    setTimeout(function () {
      autosize(document.querySelectorAll("textarea"));
    }, 500);
  },
});
