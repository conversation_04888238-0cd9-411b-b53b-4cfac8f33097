//const
const HOST = window.location.origin;
const FOLDER_IMG = HOST + "/cdn/flashcard/default/";
const VI_LANG = $("textarea[name=vi_lang]");
const JP_LANG = $("textarea[name=jp_lang]");
const JP_SIZE = $("input[name=jpSz]");
const EXAMPLE = $("textarea[name=example]");
const SHOW = $("input[name=flashcard_show]");
const IMG_FLC = $("input[name=image_file]");
const AUD_FLC = $("input[name=audio_file]");
const BODYFLC = $("#body_flashcard");
const LESSON_ID = $("input[name=lesson_id]");
const PAGE_MODEL = $("#pageModal");
const IMG_PREVIEW = $(".flc_img_preview");
const MP3_REVIEW = $("#mp3_file");
const PROGRESS = $("#progressBarUpload");
const TABLE_DATA = $(".body_task");
const FTYPE = $("input[name=flashcard_hint]");
const TABTYPE = $(".comment-type");
const fc_jp_word = $("#fc_jp_word");
const fc_jp_ex = $("#fc_jp_ex");
const fc_vi_img = $("#fc_vi_img");
const fc_vi_meaning = $("#fc_vi_meaning");

/**
 * preview image before upload
 * @param elm
 */
global.previewCourse = function (elm) {
    var fc_vi_img = $("#fc_vi_img");
    changeViDisplay();
    var file = $(elm)[0].files[0];
    var reader = new FileReader();
    reader.onloadend = function () {
        $(IMG_PREVIEW).html(
            `<img src="${reader.result}" width="64px" class="img-rounded">`
        );
        fc_vi_img.html(`<img src="${reader.result}">`);
    };
    if (file) {
        reader.readAsDataURL(file);
    }
};

/**
 * save data flashcard
 * @param type
 */
global.saveDataFlashCard = function (type) {
    console.log(`vao day....`, type);
    var formData = new FormData();
    formData.append("type", type);
    formData.append("vi_lang", VI_LANG.val());
    formData.append("jp_lang", JP_LANG.val());
    formData.append("jpSz", JP_SIZE.val());
    formData.append("example", EXAMPLE.val());
    formData.append("lesson_id", LESSON_ID.val());
    formData.append("flashcard_hint", FTYPE.val());

    SHOW.each(function () {
        if ($(this).prop("checked")) {
            formData.append("show", $(this).val());
        }
    });
    if (IMG_FLC[0].files.length !== 0) {
        formData.append("image", IMG_FLC[0].files[0]);
    }
    if (AUD_FLC[0].files.length !== 0) {
        formData.append("audio", AUD_FLC[0].files[0]);
    }

    var url;

    /**
     * EDIT_MODE for edit
     */
    if (window.EDIT_MODE) {
        url = BODYFLC.data("uri_edit");
        var idTask = BODYFLC.data("id_task");
        formData.append("id_task", idTask);

        // them bien check co xoa hinh anh va am thanh khi sua khong
        if (window.isDelImg) {
            formData.append("is_delImg", true);
        }
        if (window.isDelAud) {
            formData.append("is_delAud", true);
        }
    } else {
        url = BODYFLC.data("uri");
    }

    $.ajax({
        url: url,
        type: "post",
        data: formData,
        contentType: false,
        processData: false,
        dataType: "json",
        success: function (res) {
            if (res.status === "success") {
                PROGRESS.css("width", "100%");
                setTimeout(function () {
                    PAGE_MODEL.modal("hide");
                    if (!window.EDIT_MODE) {
                        fillDataFlashcard(res.detail.id, res.detail.show, res.detail.sort);
                    } else {
                        var statusElm = ".status-" + res.detail.id;
                        var status =
                            res.detail.show == 1
                                ? `<span class="label label-success">Bật</span>`
                                : `<span class="label label-danger">Tắt</span>`;
                        $(statusElm).html(status);
                    }
                    resetFlashCard();
                }, 500);
            } else {
                for (var k in res.detail) {
                    var classError = ".error_";
                    classError += k;
                    $(classError).html(res.detail[k][0]);
                }
                setTimeout(function () {
                    for (var k in res.detail) {
                        var classError = ".error_";
                        classError += k;
                        $(classError).html("");
                    }
                }, 3000);
            }
        },
    });
};

/**
 * show flashcard data when click edit
 * @param response
 * @param idTask
 */
global.showDataFlashCard = function (response, idTask) {
    var value = JSON.parse(response.detail.value);
    VI_LANG.val(value.vi);
    JP_LANG.val(value.jp);
    JP_SIZE.val(value.jpSz || "");
    EXAMPLE.val(value.ex);
    if (value.img === "") {
        IMG_PREVIEW.html("Chưa chọn ảnh");
    } else {
        IMG_PREVIEW.html(`<img src="${
            FOLDER_IMG + value.img
        }" width="64px" class="img-rounded">
        <button type="button" class="btn btn-danger" style="margin-left: 15px" onclick="deleteImageFlashcard()"><span class="glyphicon glyphicon-trash"></span></button>`);
        fc_vi_img.html(`<img src="${FOLDER_IMG + value.img}">`);
    }

    var audio;
    if (value.audio === "") {
        audio = "Chưa chọn âm thanh";
    } else {
        audio = `
                <span>${
            value.audio
        }</span><button type="button" class="btn btn-default" style="border: 0px; background: #edf1f5; font-size: 20px" onclick="openAudioFlashcard(this)" data-audio="audio_flashcard"><span class="glyphicon glyphicon-volume-up" aria-hidden="true"></span></button>
                <audio controls id="audio_flashcard" style="display: none">
                  <source src="${
            window.location.origin + "/cdn/audio/" + value.audio
        }" type="audio/mpeg">
                    Your browser does not support the audio element.
                </audio>
                <button type="button" class="btn btn-danger" style="margin-left: 15px" onclick="deleteAudioFlashcard()"><span class="glyphicon glyphicon-trash"></span></button>`;
    }

    MP3_REVIEW.html(audio);
    SHOW.each(function () {
        if ($(this).val() == response.detail.show) {
            $(this).prop("checked", true);
        }
    });
    BODYFLC.data("id_task", idTask);
    if (response.detail.lesson.flashcard_hint !== "") {
        TABTYPE.find("li").each(function () {
            $(this).removeClass("active");
            if (
                $(this).data("flashcard_hint") == response.detail.lesson.flashcard_hint
            ) {
                $(this).addClass("active");
            }
        });
    }
};

global.openAudioFlashcard = function (elm) {
    var idAudio = $(elm).data("audio");
    var audioElm = document.getElementById(idAudio);
    audioElm.play();
};

/**
 * Reset data flashcard
 */
global.resetFlashCard = function () {
    VI_LANG.val("");
    JP_LANG.val("");
    EXAMPLE.val("");
    IMG_PREVIEW.html("");
    MP3_REVIEW.text("");
    PROGRESS.css("width", 0);
    IMG_FLC.val("");
    AUD_FLC.val("");
    window.EDIT_MODE = false;
    SHOW.each(function () {
        if ($(this).val() == 1) {
            $(this).prop("checked", true);
        }
    });
    BODYFLC.data("id_task", null);
    window.isDelAud = false;
    window.isDelImg = false;
    resetFlashcardType();
};

global.resetFlashcardType = function () {
    TABTYPE.find("li").removeClass("active");
    TABTYPE.find("li").each(function () {
        if ($(this).data("flashcard_hint") == 1) {
            $(this).addClass("active");
        }
    });
    FTYPE.val(1);
};

/**
 * fill data flashcard when add data
 * @param taskId
 * @param show
 * @param sort
 */
global.fillDataFlashcard = function (taskId, show, sort) {
    var status =
        show == 1
            ? `<span class="label label-success">Bật</span>`
            : `<span class="label label-danger">Tắt</span>`;
    var html = `
    <tr class="item${taskId} ui-sortable-handle" style="">
    <td class="text-center">${sort}</td>
    <td class="text-center">${taskId}</td>
    <td class="text-center">
                                    Flash Card
                            </td>
    <td class="task_content text-left">
        <span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">

                                    </span>
    </td>
    <td class="text-center status-${taskId}">
                                    ${status}
                            </td>
    <td class="text-center">
        <button class="change-lesson btn btn-warning text-dark" type="button" style="padding: 2px 5px; border-radius: 3px;" onclick="changeLesson(${taskId})">
            <span class="glyphicon glyphicon-random"></span>
        </button>
        <button class="edit-task btn btn-info" data-info="${taskId}" data-type="#task-type" type="button" style="padding: 2px 5px; border-radius: 3px;" onclick="editData(this)" data-uri="${window.location.origin}/backend/lesson/get-flashcard">
            <span class="glyphicon glyphicon-edit"></span>
        </button>
        <button class="delete-task btn btn-danger" data-info="${taskId}" type="button" style="padding: 2px 5px; border-radius: 3px;">
            <span class="glyphicon glyphicon-trash"></span>
        </button>
    </td>
</tr>
    `;
    TABLE_DATA.append(html);
};

global.deleteImageFlashcard = function () {
    IMG_PREVIEW.html("");
    window.isDelImg = true;
    changeViDisplay();
};

global.deleteAudioFlashcard = function () {
    MP3_REVIEW.html("");
    window.isDelAud = true;
    changeJPDisplay();
};

global.previewFlashcard = function (response) {
    $(".card__word--text").css({
        "font-size": JP_SIZE.val() + "px",
    });
    fc_jp_word.html(breakLine(JP_LANG.val()));
    fc_jp_ex.html(highlightVocab(JP_LANG.val(), EXAMPLE.val()));
    fc_vi_meaning.text(VI_LANG.val());

    changeJPDisplay(response);
    changeViDisplay(response);

    JP_LANG.on("keyup", function () {
        fc_jp_word.html(breakLine(JP_LANG.val()));
        fc_jp_ex.html(highlightVocab(JP_LANG.val(), EXAMPLE.val()));
    });
    JP_SIZE.on("change", function () {
        $(".card__word--text").css({
            "font-size": JP_SIZE.val() + "px",
        });
    });
    EXAMPLE.on("keyup", function () {
        changeJPDisplay();
        fc_jp_ex.html(highlightVocab(JP_LANG.val(), EXAMPLE.val()));
    });
    VI_LANG.on("keyup", function () {
        fc_vi_meaning.text(VI_LANG.val());
    });
    AUD_FLC.on("change", function () {
        changeJPDisplay();
    });
};

global.changeJPDisplay = function (response) {
    var value = response && JSON.parse(response.detail.value);
    var noAudio = false;

    // Các điều kiện để không hiển thị icon audio
    if (AUD_FLC.get(0).files.length === 0 && window.isDelAud) noAudio = true;
    if (response && value.audio == "" && AUD_FLC.get(0).files.length === 0)
        noAudio = true;
    if (!response && AUD_FLC.get(0).files.length === 0) noAudio = true;

    if (noAudio) {
        $(".card__voice--button").hide();
    } else {
        $(".card__voice--button").show();
    }

    // Điều kiện để không hiển thị ví dụ
    if (EXAMPLE.val() == "") {
        $(".card__word--example").hide();
    } else {
        fc_jp_ex.html(highlightVocab(JP_LANG.val(), EXAMPLE.val()));
        $(".card__word--example").show();
    }
    if (
        EXAMPLE.val() == "" ||
        (AUD_FLC.get(0).files.length === 0 && window.isDelAud)
    ) {
        $("#card_comment--title-text").show();
        $(".card__word").css({
            "padding-top": "0",
            "justify-content": "center",
        });
        if (JP_SIZE.val() == "") {
            $(".card__word--text").css({
                "font-size": "100px",
            });
        }
    } else {
        $("#card_comment--title-text").hide();
        $(".card__word").css({
            "padding-top": "70px",
            "justify-content": "flex-start",
        });
        if (JP_SIZE.val() == "") {
            $(".card__word--text").css({
                "font-size": "3em",
            });
        }
    }
};

global.changeViDisplay = function (response) {
    var value = response && JSON.parse(response.detail.value);
    var noImage = false;

    // Các điều kiện để không hiển thị icon audio
    if (IMG_FLC.get(0).files.length === 0 && window.isDelAud) noImage = true;
    if (response && value.img == "" && IMG_FLC.get(0).files.length === 0)
        noImage = true;
    if (!response && IMG_FLC.get(0).files.length === 0) noImage = true;

    if (noImage) {
        $("#fc_vi_img").hide();
        $(".card_meaning").css({
            "justify-content": "center",
            "font-size": "45px",
            "line-height": "58px",
        });
        $(".meaning").css({
            "font-size": "34px",
            "line-height": "1.3",
        });
    } else {
        $(".card_meaning").css({
            "justify-content": "space-between",
            "font-size": "20px",
            "line-height": "26px",
        });
        $(".meaning").css({
            "font-size": "20px",
            "line-height": "1.3",
        });
        $("#fc_vi_img").show();
    }
};

global.mp3Ex = function (ex) {
    // Regex chuỗi bắt đầu và kết thúc bằng dấu * đồng thời trích ra chuỗi nằm giữa 2 dấu *
    var manualReplace = new RegExp(/\{\!(.+?)\!\}/, "gi");

    var newEx = ex;
    // Lấy object chứa chuỗi đầu tiên được trích ra (extract capturing group)
    var manualHighlightWord = manualReplace.exec(newEx);
    if (manualHighlightWord)
        newEx = newEx.replace(
            manualHighlightWord[0],
            `<span class='text-info' title='${manualHighlightWord[1]}'>` +
            "<i class='fa fa-volume-up'></i>" +
            "</span>"
        );
    // Kiểm tra xem còn chuỗi nào thoả mãn nữa không, nếu có thì chạy vòng lặp
    while (manualHighlightWord) {
        manualHighlightWord = manualReplace.exec(newEx);
        if (manualHighlightWord)
            newEx = newEx.replace(
                manualHighlightWord[0],
                `<span class="text-info" title='${manualHighlightWord[1]}'>` +
                '<i class="fa fa-volume-up"></i>' +
                "</span>"
            );
    }
    return newEx;
};

global.highlightVocab = function (jp, ex) {
    var replace = new RegExp(jp, "gi");
    // Regex chuỗi bắt đầu và kết thúc bằng dấu * đồng thời trích ra chuỗi nằm giữa 2 dấu *
    var manualReplace = new RegExp(/\*+(.+?)\*+/, "gi");

    // Thay các từ trùng với jp bằng html dom có class highlight
    var newEx = ex.replace(replace, '<span class="highlight">' + jp + "</span>");

    // Lấy object chứa chuỗi đầu tiên được trích ra (extract capturing group)
    var manualHighlightWord = manualReplace.exec(newEx);
    if (manualHighlightWord)
        newEx = newEx.replace(
            manualHighlightWord[0],
            '<span class="highlight">' + manualHighlightWord[1] + "</span>"
        );
    // Kiểm tra xem còn chuỗi nào thoả mãn nữa không, nếu có thì chạy vòng lặp
    while (manualHighlightWord) {
        manualHighlightWord = manualReplace.exec(newEx);
        if (manualHighlightWord)
            newEx = newEx.replace(
                manualHighlightWord[0],
                '<span class="highlight">' + manualHighlightWord[1] + "</span>"
            );
    }

    // Hiển thị xuống dòng
    newEx = breakLine(newEx);
    newEx = mp3Ex(newEx);
    return newEx;
};

function breakLine(str) {
    return str.replace(/\n/gi, "<br/>");
}

$("#fcExampleInput").select(function (event) {
    var elem = $(this);
    var oldText = elem.val();
    var start = elem.prop("selectionStart");
    var end = elem.prop("selectionEnd");
    var prefixStr = elem.val().substring(0, start);
    var sufixStr = elem.val().substring(end, elem.val().length);
    var selectedStr = elem.val().substring(start, end);

    function highlightByBtn(str) {
        return "*" + str + "*";
    }

    function mp3ByBtn(str) {
        return "{! " + str + " !}";
    }

    var formattedStr = prefixStr + highlightByBtn(selectedStr) + sufixStr;
    var mp3Str = prefixStr + mp3ByBtn(selectedStr) + sufixStr;

    $("#_hlBtn").click(function (event) {
        elem.val(formattedStr);
        changeJPDisplay();
    });
    $("#_udBtn").click(function (event) {
        elem.val(oldText);
        changeJPDisplay();
    });
});

$(document).on("click", "#_mp3Btn", function (event) {
    $("#audio_file_flashcard").trigger("click");
});

$("#audio_file_flashcard").on("change", function () {
    var file = this.files[0];
    var reader = new FileReader();
    reader.onload = function (e) {
        uploadAudio($("#audio_file_flashcard")[0].files[0], function (res) {
            $("#fcExampleInput").val(
                $("#fcExampleInput").val() + "{! " + res.data.file_path + " !}"
            );
            changeJPDisplay();
        });
    };
    if (!file) return;
    reader.readAsDataURL(file);
});

//click chuyen tab
$("#card-inner").on("click", function () {
    $(this).toggleClass("flip");
});

$(function () {
    TABTYPE.find("li").click(function () {
        TABTYPE.find("li").removeClass("active");
        $(this).addClass("active");
        var fType = $(this).data("flashcard_hint");
        FTYPE.val(fType);
    });
});

/**
 * save data
 * @param elm
 */
global.saveData = function (elm) {
    var idTask = $(elm).data("task");
    var task = $(idTask).val();
    switch (task) {
        case "9":
            saveDataFlashCard(9);
            break;
    }
};

/**
 * edit data
 * @param elm
 */
global.editData = function (elm) {
    var idTask = $(elm).data("info");
    var url = $(elm).data("uri");
    var idType = $(elm).data("type");
    window.EDIT_MODE = true;
    $.ajax({
        url: url,
        type: "get",
        data: {id: idTask},
        dataType: "json",
        success: function (response) {
            if (response.status === "success") {
                showData(response, idTask, idType);
            }
        },
    });
};

/**
 * show data action
 * @param response
 * @param idTask
 * @param idType
 */
global.showData = function (response, idTask, idType) {
    var type = response.detail.type;
    $(idType).val(type);
    switch (type) {
        case 9:
            console.log("case 9");
            showDataFlashCard(response, idTask);
            previewFlashcard(response);
            break;
    }
};

global.resetData = function (elm) {
    var idTask = $(elm).data("task");
    var task = $(idTask).val();
    switch (task) {
        case "9":
            resetFlashCard();
            previewFlashcard();
            break;
    }
};

global.changeLesson = function (id) {
    bootbox.prompt({
        title: "Nhập ID bài học",
        backdrop: true,
        callback: function (lessonId) {
            if (lessonId) {
                var data = {
                    flashcardId: id,
                    lessonId: lessonId,
                };
                $.post(
                    window.location.origin + "/backend/lesson/flashcard-lesson",
                    data,
                    function (response, status) {
                        switch (response.code) {
                            case 404:
                                $.toast({
                                    text: response.error,
                                    position: "top-right",
                                    stack: false,
                                    icon: "error",
                                });
                                break;
                            case 409:
                                $.toast({
                                    text: response.error,
                                    position: "top-right",
                                    stack: false,
                                    icon: "info",
                                });
                                break;
                            case 200:
                                $(".item" + id).fadeOut();
                                $.toast({
                                    text:
                                        "Đã chuyển flashcard sang bài học <a target='blank' href=" +
                                        window.location.origin +
                                        "/backend/lesson/" +
                                        lessonId +
                                        "/edit>#" +
                                        lessonId +
                                        "</a>",
                                    position: "top-right",
                                    stack: false,
                                    icon: "success",
                                });
                                break;
                            default:
                                $.toast({
                                    text: "Hệ thống gặp sự cố",
                                    position: "top-right",
                                    stack: false,
                                    icon: "error",
                                });
                        }
                    }
                ).fail(function () {
                    $.toast({
                        text: "Hệ thống gặp sự cố",
                        position: "top-right",
                        stack: false,
                        icon: "error",
                    });
                });
            }
            if (lessonId == "") {
                $.toast({
                    text: "Không có thay đổi",
                    position: "top-right",
                    stack: false,
                    icon: "info",
                });
            }
        },
    });
};

global.cloneToLesson = function (id) {
    bootbox.prompt({
        title: "Nhập ID bài học",
        backdrop: true,
        callback: function (lessonId) {
            if (lessonId) {
                var data = {
                    flashcardId: id,
                    lessonId: lessonId,
                };
                $.post(
                    window.location.origin + "/backend/lesson/clone-flashcard-to-lesson",
                    data,
                    function (response, status) {
                        switch (response.code) {
                            case 404:
                                $.toast({
                                    text: response.error,
                                    position: "top-right",
                                    stack: false,
                                    icon: "error",
                                });
                                break;
                            case 409:
                                $.toast({
                                    text: response.error,
                                    position: "top-right",
                                    stack: false,
                                    icon: "info",
                                });
                                break;
                            case 200:
                                $.toast({
                                    text:
                                        "Đã copy flashcard sang bài học <a target='blank' href=" +
                                        window.location.origin +
                                        "/backend/lesson/" +
                                        lessonId +
                                        "/edit>#" +
                                        lessonId +
                                        "</a>",
                                    position: "top-right",
                                    stack: false,
                                    icon: "success",
                                });
                                break;
                            default:
                                $.toast({
                                    text: "Hệ thống gặp sự cố",
                                    position: "top-right",
                                    stack: false,
                                    icon: "error",
                                });
                        }
                    }
                ).fail(function () {
                    $.toast({
                        text: "Hệ thống gặp sự cố",
                        position: "top-right",
                        stack: false,
                        icon: "error",
                    });
                });
            }
            if (lessonId == "") {
                $.toast({
                    text: "Không có thay đổi",
                    position: "top-right",
                    stack: false,
                    icon: "info",
                });
            }
        },
    });
};
