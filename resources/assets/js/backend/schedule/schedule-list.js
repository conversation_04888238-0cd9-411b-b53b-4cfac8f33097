Vue.filter('scheduleFocusOn', function (name) {
  switch (name) {
    case 'booking':
      return 'Booking Kaiwa';
    case 'sale':
      return 'Thông tin sale';
    case 'lesson':
      return 'Bài học';
    case 'course':
      return '<PERSON><PERSON><PERSON> học';
    case 'combo':
      return 'Combo';
    case 'blog':
      return 'Blog';
    case 'jlpt':
      return 'Thi thử';
    default:
      return '--';
  }
});
Vue.filter('scheduleStatus', function (schedule) {
  switch (true) {
    case (schedule.status === -1):
      return 'Đã huỷ';
    case (schedule.status === 0 && (moment(schedule.time) >= moment())):
      return 'Đang lên lịch';
    case (schedule.status === 0 && (moment(schedule.time) < moment())):
      return 'Quá hạn';
    case (schedule.status === 1 || schedule.status === 2):
      return 'Đã gửi';
    default:
      return '--';
  }
});
Vue.filter('scheduleStatusClass', function (schedule) {
  switch (true) {
    case (schedule.status === -1):
      return 'status-gray';
    case (schedule.status === 0 && (moment(schedule.time) >= moment())):
      return 'status-blue';
    case (schedule.status === 0 && (moment(schedule.time) < moment())):
      return 'status-red';
    case (schedule.status === 1 || schedule.status === 2):
      return 'status-green';
    default:
      return 'status-blue';
  }
});
var scheduleList = Vue.component('schedule-list', {
  //template dc định nghĩa ở default
  template: '#schedule-list-table',
  props: [''],
  data: function () {
    return {
      url: window.location.origin,
      schedules: []
    }
  },
  methods: {
    getScheduleList: function () {
      var vm = this;
      $.post(vm.url + '/backend/global-notification/schedule/list', {}, function (res) {
        if(res.code == 200) {
          vm.schedules = res.data;
          vm.processList();
        }
      })
    },
    processList: function () {
      var vm = this;
      vm.schedules = vm.schedules.map(function (schedule) {
        schedule.data = JSON.parse(schedule.data)
        if (!_.isNull(schedule.schedule)) {
          schedule.time = schedule.schedule;
        }
        if (!_.isNull(schedule.schedule_test)) {
          schedule.time = schedule.schedule_test;
        }
        return schedule;
      })
      vm.schedules = _.orderBy(vm.schedules, 'time', 'desc')
    },
    suspendSchedule: function (schedule) {
      var vm = this;

      var confirm = window.confirm('Xác nhận huỷ lên lịch thông báo này');

      if(confirm) {
        var data = {
          id: schedule.id
        };
        $.post(vm.url + '/backend/global-notification/schedule/suspend', data, function (res) {
          if (res.code === 200) {
            vm.schedules = vm.schedules.map(function (schedule) {
              if (schedule.id === res.data.id) {
                schedule.status = res.data.status
              }
              return schedule;
            })
          }
        })
      }
    },
    cloneSchedule: function (schedule) {
      let vm = this;
      vm.$emit('clone:schedule', schedule)
    }
  },
  mounted: function () {
    var vm = this;
    vm.getScheduleList()
  }
})
