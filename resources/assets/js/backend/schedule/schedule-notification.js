$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  }
});

var schedule_notification = new Vue({

  el: '.schedule__screen',

  data: {
    url: window.location.origin, //đường dẫn host
    activeTab: 'sent',
    directPushForm: {
      show: false,
      heading: {
        text: '',
        length: 0
      },
      title: {
        text: '',
        length: 0
      },
      tableName: undefined,
      tableId: undefined,
      segments: 'Test Segment'
    },
    timerPushForm: {
      show: false,
      heading: {
        text: '',
        length: 0
      },
      title: {
        text: '',
        length: 0
      },
      timer: null,
      tableName: undefined,
      tableId: undefined,
      segments: 'Test Segment'
    },

  },
  methods: {
    onChangeSelect: function (form, param, event) {
      var vm = this;
      vm[form][param] = event.target.value;
      if (event.target.name == 'tableNameForm') {
        vm[form].tableId = undefined;
        $("#tableIdForm").empty().trigger('change')
      }
    },
    setActiveTab: function () {
      var vm = this;
      vm.activeTab = window.location.hash.substr(1) || 'sent';
    },
    closeForm: function (form) {
      var vm = this;
      vm.resetForm(form);
      vm[form].show = false;
    },
    resetForm: function (form) {
      var vm = this;
      vm[form] = {
        ...vm[form],
        show: false,
        heading: {
          text: '',
          length: 0
        },
        title: {
          text: '',
          length: 0
        },
        tableName: undefined,
        tableId: undefined,
        segments: 'Test Segment'
      };
      if (form === 'timerPushForm')
        vm[form].timer = null;
    },
    countCharacter: function (form, param, event) {
      var vm = this;
      vm[form][param].length = event.target.value.length;
    },
    pushDirectNotification: function () {
      var vm = this;

      var data = {
        segments: [vm.directPushForm.segments],
        heading: vm.directPushForm.heading.text,
        title: vm.directPushForm.title.text,
        tableName: vm.directPushForm.tableName,
        tableId: vm.directPushForm.tableId,
      }
      $.post(vm.url + "/backend/global-notification/direct-push", data, function (res) {
        if (res.code === 200) {
          toastr.success('Đẩy thông báo thành công');
          vm.closeForm('directPushForm');
          setTimeout(function () {
            window.location.reload();
          }, 100)
        }
      });
    },
    storeSchedule: function () {
      var vm = this;
      var data = {
        segments: vm.timerPushForm.segments,
        heading: vm.timerPushForm.heading.text,
        title: vm.timerPushForm.title.text,
        tableName: vm.timerPushForm.tableName,
        tableId: vm.timerPushForm.tableId,
        timer: vm.timerPushForm.timer,
      }
      $.post(vm.url + '/backend/global-notification/schedule/store', data, function (res) {
        if (res.code === 200) {
          toastr.success('Lên lịch thành công');
          vm.closeForm('timerPushForm');
          setTimeout(function () {
            window.location.reload();
          }, 100)
        }
      })
    },
    cancel: function (form) {
      var vm = this;
      vm[form].show = false;
    },
    onChangeDatetime: function (event) {
      vm = this;
      vm.timerPushForm.timer = moment(event.date).format('YYYY-MM-DD HH:mm:ss');
    },
    initDateTimeInput: function () {
      $('#datetimepicker1').datetimepicker({
        format: 'YYYY-MM-DD HH:mm'
      }).on('dp.change', function (event) {
        vm.onChangeDatetime(event);
      });
    },
    cloneNotification: function (notification) {
      let vm = this;
      vm.directPushForm.heading.text = notification.data.heading;
      vm.directPushForm.heading.length = notification.data.heading.length;
      vm.directPushForm.title.text = notification.title;
      vm.directPushForm.title.length = notification.title.length;
      vm.directPushForm.show = true;
    },
    cloneSchedule: function (notification) {
      let vm = this;
      vm.timerPushForm.heading.text = notification.data.heading;
      vm.timerPushForm.heading.length = notification.data.heading.length;
      vm.timerPushForm.title.text = notification.title;
      vm.timerPushForm.title.length = notification.title.length;
      vm.timerPushForm.show = true;
    }
  },
  mounted: function () {
    vm = this;
    vm.setActiveTab();
    vm.initDateTimeInput();
  }
});
