Vue.filter('scheduleFocusOn', function (name) {
  switch (name) {
    case 'booking':
      return 'Booking Kaiwa';
    case 'sale':
      return 'Thông tin sale';
    case 'lesson':
      return 'B<PERSON>i học';
    case 'course':
      return '<PERSON><PERSON><PERSON> học';
    case 'combo':
      return 'Combo';
    case 'blog':
      return 'Blog';
    case 'jlpt':
      return 'Thi thử';
    default:
      return '--';
  }
});
var sentList = Vue.component('sent-list', {
  //template dc định nghĩa ở default
  template: '#sent-list-table',
  props: [''],
  data: function () {
    return {
      url: window.location.origin,
      notifications: []
    }
  },
  methods: {
    getSentList: function () {
      var vm = this;
      $.post(vm.url + '/backend/global-notification/list', {}, function (res) {
        if(res.code == 200) {
          vm.notifications = res.data;
          vm.processList();
        }
      })
    },
    processList: function () {
      var vm = this;
      vm.notifications = vm.notifications.map(function (notification) {
        notification.data = notification.info;
        return notification;
      })
    },
    removeNotification: function (notification) {
      var vm = this;

      var confirm = window.confirm('Xác nhận xoá thông báo này? Thông báo đã xoá không thể phục hồi')

      if (confirm) {
        var data = {
          id: notification.id
        }

        $.post(vm.url + '/backend/global-notification/remove', data, function (res) {
          if (res.code === 200) {
            vm.notifications = vm.notifications.filter(function (item) {
              return item.id !== notification.id
            })
          }
        })
      }
    },
    cloneNotification: function (notification) {
      let vm = this;
      vm.$emit('clone:notification', notification)
    }
  },
  mounted: function () {
    var vm = this;
    vm.getSentList()
  },

})
