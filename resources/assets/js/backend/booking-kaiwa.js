$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

const defaultCurrentObject = {
    bookingId: undefined,
    param: undefined,
    user_id: undefined,
    email: undefined,
    skype: undefined,
    exist: false
};
var kaiwa = new Vue({
    el: '.table-booking-kaiwa',
    data : {
        //manager booking
        dateView : toDay,
        classElementDate: 'input-datepicker',
        classElementDateRange: 'input-daterange-datepicker',
        dateRange: null,
        messageCreate: null,
        listBooking: null,
        date: null,
        errorMsg:   {'status': 'error', 'detail' : {'msg': 'Có lỗi xảy ra!'} },
        waitMsg:   {'status': 'sending', 'detail' : {'msg': 'Đang xử lý!'} },
        currentYear: null,
        years: {},
        months: {},
        listDays: null,
        dayInMonth: null,
        // topic
        topicName: null,
        topics: null,
        currentObject: {
            bookingId: undefined,
            param: undefined,
            user_id: undefined,
            email: undefined,
            skype: undefined,
            exist: false
        },

    },

    methods : {
        /**
         * lấy ngày cần xem lịch
         * @param event
         */
        getDate: function (event) {
            var _this = this;
            _this.dateView = document.getElementsByClassName(_this.classElementDate)[0].value;
            _this.getDataByDate();
        },

        /**
         * Tạo lịch theo một khoảng ngày
         */
        createListDate: function () {
            var _this = this;
            _this.dateRange = document.getElementsByClassName(_this.classElementDateRange)[0].value;
            var listDate = _this.dateRange.split(' - ');
            var url = urlCreateBooking;
            var data = {start: listDate[0], end : listDate[1]};
            $.ajax({
                url: url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (response) {
                    _this.setMessage(response);
                    _this.getDataByDate();
                },
                error: function () {
                    _this.setMessage(_this.errorMsg);
                }
            });
        },

        /**
         * Lấy dữ liệu theo một ngày
         */
        getDataByDate: function() {
            var _this = this;
            var url = urlGetList;
            var data = {'date' : _this.dateView};
            $.ajax({
                url: url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (response) {
                    _this.listBooking = response.detail.data;
                    _this.date = response.detail.date;
                },
                error: function () {
                    _this.setMessage(_this.errorMsg);
                }
            });
        },

        /**
         * Update một dữ liệu đặt lịch
         * @param pos
         */
        changeData: function (pos) {
            var _this = this;
            var data = _this.listBooking[pos];
            var url = urlUpdate;
            $.ajax({
                url: url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (response) {
                    _this.setMessage(response);
                },
                error: function () {
                    _this.setMessage(_this.errorMsg);
                }
            });
        },

        /**
         * gan message thong bao khi thay doi du lieu
         * @param response
         */
        setMessage: function (response) {
            var _this = this;
            _this.messageCreate = response.detail.msg;

            if (response.status === 'success') {
                $.notify(_this.messageCreate, "success");
            } else if (response.status === 'error') {
                $.notify(_this.messageCreate, "error");
            }

        },

        dateAction: function () {
            var _this = this;
            var date = new Date();
            _this.currentYear = date.getFullYear();
            _this.currentMonth = date.getMonth() + 1;
            for (var i = 0; i < 3; i++) {
                _this.years[i] = _this.currentYear + i;
            }

            for (var j = 1; j <=12; j++) {
                _this.months[j] = j;
            }
        },

        checkBooking: function () {
            var _this = this;
            var url = urlCheck;
            var data = {'year': _this.currentYear, 'month': _this.currentMonth};
            $.ajax({
                url: url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (response) {
                    _this.listDays = response.detail.data;
                }
            });
        },

        // Topic manager

        /**
         * them moi topic
         */
        addTopic: function () {
            var _this = this;
            var url = urlAddTopic;
            var data = {name: _this.topicName}
            $.ajax({
                url : url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (response) {
                    _this.setMessage(response);
                    _this.topicName = null;
                    _this.getTopicData();
                },
                error: function () {
                    _this.setMessage(_this.errorMsg);
                }
            });

        },
        /**
         * get data topics
         */
        getTopicData: function () {
            var _this = this;
            var url = urlGetTopic;
            $.ajax({
                url : url,
                type: 'get',
                dataType: 'json',
                success: function (response) {
                    _this.topics = response.detail.data;
                },
                error: function () {
                    _this.setMessage(_this.errorMsg);
                }
            });
        },

        /**
         * click nut edit topic name
         * @param topic
         */
        editTopicName: function (topic) {
            var elmTopicName = '.topic-name-' + topic.id;
            var formEditTopic = '.edit-topic-name-' + topic.id;
            $(elmTopicName).css({'display': 'none'});
            $(formEditTopic).css({'display': 'block'});
        },

        /**
         * update ten topic
         * @param topic
         * @param i
         */
        updateTopicName: function (topic, i) {
            var _this = this;
            var elmTopicName = '.topic-name-' + topic.id;
            var formEditTopic = '.edit-topic-name-' + topic.id;
            var data = _this.topics[i];
            var url = updateTopic;
            $.ajax({
                url : url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (response) {
                    _this.setMessage(response);
                    if (response.status === 'success') {
                        $(elmTopicName).css({'display': 'block'});
                        $(formEditTopic).css({'display': 'none'});
                    }
                },
                error: function () {
                    _this.setMessage(_this.errorMsg);
                }
            });
        },

        /**
         * update status cua topic
         * @param topic
         * @param i
         */
        updateTopicStatus: function (i) {
            var _this = this;
            var data = _this.topics[i];
            var url = updateTopic;
            $.ajax({
                url : url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (response) {
                    _this.getTopicData();
                    _this.setMessage(response);
                },
                error: function () {
                    _this.setMessage(_this.errorMsg);
                }
            });
        },

        /**
         * xoa topic
         * @param i
         */
        deleteTopic: function (i) {
            var _this = this;
            var data = {id: _this.topics[i].id};
            var url = urlDeleteTopic;
            var check = confirm('Bạn có muốn xóa chủ đề này');
            if (check) {
                $.ajax({
                    url : url,
                    type: 'post',
                    data: data,
                    dataType: 'json',
                    success: function (response) {
                        _this.getTopicData();
                        _this.setMessage(response);
                    },
                    error: function () {
                        _this.setMessage(_this.errorMsg);
                    }
                });
            }
        },

        /**
         * thoat chinh sua topic
         * @param topic
         * @param i
         */
        exitEditTopic: function (topic, i) {
            var elmTopicName = '.topic-name-' + topic.id;
            var formEditTopic = '.edit-topic-name-' + topic.id;
            $(elmTopicName).css({'display': 'block'});
            $(formEditTopic).css({'display': 'none'});
            this.getTopicData();
        },

        getKeyPress: function (event) {
            event.preventDefault();
        },

        processBooking: function (kaiwaId) {
            vm = this;

            var url = urlProcessBooking;
            var data = {
                id: kaiwaId
            };
            $.post(url, data, function(response){
                if (response.code == 409) {
                    var confirm = window.confirm(response.data.message);
                    if (confirm) {
                        var data2 = {
                            id: kaiwaId,
                            confirm: 1
                        };
                        $.post(url, data2, function(response){
                            if (response.code == 200) {
                                vm.listBooking = vm.listBooking.map(function (booking) {
                                    if (booking.id == response.data.id) {
                                        booking.curators = response.data.curators;
                                    }
                                    return booking;
                                })
                            }
                        });
                    }
                }
                if (response.code == 200) {
                    vm.listBooking = vm.listBooking.map(function (booking) {
                        if (booking.id == response.data.id) {
                            booking.curators = response.data.curators;
                        }
                        return booking;
                    })
                }
            });
        },
        // set các param xác định lớp kaiwa cần thay đổi và user cần thay đổi
        setModalData: function (bookingId, param) {
            vm = this;

            vm.currentObject.bookingId = bookingId;
            vm.currentObject.param = param;
        },
        // lấy thông tin user theo id và fill vào form
        getUserInfo: function(event) {
            vm = this;

            var userId = event.target.value;
            var data = {
                user_id: userId
            };
            $.post(window.location.origin + '/backend/booking/kaiwa/get-user-by-id', data, function (res) {
                if(res.code == 200) {
                    vm.currentObject.exist = true;
                    vm.currentObject.email = res.data.email;
                    vm.currentObject.skype = res.data.skype;
                }
            });
        },
        // call api thêm học viên của kaiwa theo param gửi lên
        saveBooking: function () {
            vm = this;

            if (vm.currentObject.exist) {
                var data = _.omit(vm.currentObject, ['exist']);

                $.post(window.location.origin + '/backend/booking/kaiwa/add-student', data, function (res) {
                    if (res.code == 200) {
                        vm.listBooking = vm.listBooking.map(function (booking) {
                            if (booking.id == res.data.id) {
                                booking[vm.currentObject.param] = res.data[vm.currentObject.param];
                                booking['user_one'] = res.data['user_one'];
                                booking['user_two'] = res.data['user_two'];
                            }
                            return booking;
                        });
                        vm.resetModalForm();
                        $('#manual-booking').modal('hide');
                    }
                })
            }
        },

        // reset form mỗi khi đóng modal
        resetModalForm: function () {
            vm = this;
            vm.currentObject = {
                bookingId: undefined,
                param: undefined,
                user_id: undefined,
                email: undefined,
                skype: undefined,
                exist: false
            };
        },
        removeStudent: function (bookingId, param) {
            vm = this;

            var confirm = window.confirm('Xác nhận xoá học viên khỏi lịch');
            if (confirm) {
                var data = {
                    bookingId: bookingId,
                    param: param
                };
                $.post(window.location.origin + '/backend/booking/kaiwa/remove-student', data, function (res) {
                    if (res.code == 200) {
                        vm.listBooking = vm.listBooking.map(function (booking) {
                            if (booking.id == res.data.id) {
                                booking[vm.currentObject.param] = res.data[vm.currentObject.param];
                                booking['user_one'] = res.data['user_one'];
                                booking['user_two'] = res.data['user_two'];
                            }
                            return booking;
                        });
                    }
                })
            }
        }
    },

    created: function () {
        this.getDataByDate();
        this.getTopicData();
        this.dateAction();
    },
});