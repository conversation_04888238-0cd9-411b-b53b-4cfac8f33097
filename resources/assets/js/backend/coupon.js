$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  }
});

const router = new VueRouter({
  mode: 'history'
});

Vue.filter('dateTimeToMinute', function (value) {
  return moment(value).format('HH:mm DD/MM/YYYY');
});
var user__list = new Vue({
  el: '#coupon__screen',
  components: {
    paginate: VuejsPaginate
  },
  data() {
    return {
      url: window.location.origin,
      loading: false,
      socket: null,
      results: [],
      editModal: false,
      filter: {
        code: undefined,
        sort: "",
        page: 1,
        per_page: 20,
        total_page: 10
      },
      total_result: 0,
    };
  },

  watch: {
    filter: {
      handler: function (val, oldVal) {
        var vm = this;
      },
      deep: true
    }
  },

  methods: {
    checkUsed: function (coupon) {
      var vm = this;
      var data = {
        id: coupon.id
      }
      $.post(vm.url + '/backend/coupon/check-used', data, function (res) {
        if (res.code === 200) {
          vm.results = vm.results.map(function (c) {
            if (c.id === coupon.id) {
              c.use_left = res.left;
            }
            return c;
          })
        }
      });
    },
    // gọi hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
    changePage: function (pageNum) {
      var vm = this;
      vm.filter.page = pageNum;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page']);
      vm.getList(filter);

      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
    applyFilter: function () {
      var vm = this;

      vm.filter.page = 1;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page', 'page']);
      vm.getList(filter);

      // console.log(filter)
      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // call api lấy danh sách invoices
    // truyền vào filter
    // set các biến trạng thái tmp để có thể dùng v-if, v-model
    getList: function (filter) {
      var vm = this;

      vm.loading = true;
      $.get(window.location.origin + '/backend/coupon/list', filter, function (res) {
        if (res.code === 200) {
          vm.results = res.data.results;
          vm.filter.total_page = res.data.total_page;
          vm.total_result = res.data.total_result;

          vm.loading = false;
        } else {
          alert('Có lỗi! Liên hệ dev!!!');
        }
        vm.loading = false;
      });
    },
    // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
    setFilterByUrl: function () {
      var vm = this;
      var filterByUrl = $.deparam.querystring();
      _.forEach(filterByUrl, function (value, key) {
        vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
      });
    },
    // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
    resetFilter: function () {
      var vm = this;
      vm.filter = {
        id: undefined,
        course: "",
        is_tester: "",
        watch_expired_day: "",
        completed: "",
        skype: "",
        time_from: undefined,
        time_to: undefined,
        sort: "",
        page: 1,
        per_page: 20,
        total_page: 10
      };

      this.applyFilter();
    },
    // thay đổi filter theo datetime
    onChangeDatetime: function (event) {
      var vm = this;
      vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm');
    },
  },
  mounted: function() {
    var vm = this;

    // Loại bỏ các filter rỗng khỏi request
    var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
      return value !== undefined && value !== null && value !== '';
    }), ['total_page']);
    this.getList(filter);
  },
});
