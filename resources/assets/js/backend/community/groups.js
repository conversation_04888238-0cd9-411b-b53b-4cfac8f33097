$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  }
});

const router = new VueRouter({
  mode: 'history'
});

Vue.filter('dateTimeToMinute', function (value) {
  return moment(value).format('HH:mm DD/MM/YYYY');
});
var exam__results = new Vue({
  el: '#group__screen',
  components: {
    paginate: VuejsPaginate,
  },
  data() {
    return {
      url: window.location.origin,
      loading: false,
      items: [],
      filter: {
        id: undefined,
        sort: "",
        page: 1,
        per_page: 20,
        total_page: 10
      },
      total_result: 0,
      currentModal: null,
      currentGroup: null,
      modal_titles: {
        communityGroupMembers: 'Danh sách thành viên',
        communityGroupPosts: 'Danh sách bài đăng',
        communityGroupHashtags: 'Danh sách hashtag',
      },
    };
  },
  methods: {
    // g<PERSON>i hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
    changePage: function (pageNum) {
      var vm = this;
      vm.filter.page = pageNum;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page']);
      vm.getList(filter);

      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
    applyFilter: function () {
      var vm = this;

      vm.filter.page = 1;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page', 'page']);
      vm.getList(filter);

      // console.log(filter)
      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    onChangeCheckbox: function (param, event) {
      vm = this;

      vm.filter[param] = event.target.checked ? 1 : 0;
      vm.applyFilter();
    },
    // call api lấy danh sách
    // truyền vào filter
    // set các biến trạng thái tmp để có thể dùng v-if, v-model
    getList: function(filter) {
      var vm = this;

      vm.loading = true;
      setTimeout(function () {
        $.post(window.location.origin +'/backend/community/group/list', filter, function (res) {
          if(res.code === 200) {
            vm.items = res.data.groups.map(function (item) {
              return item;
            });
            vm.filter.total_page = res.data.pagination.last_page;
            vm.total_result = res.data.pagination.total;

          } else {
            alert('Có lỗi! Liên hệ dev!!!');
          }
          vm.loading = false;
        });
      }, 200);
    },
    // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
    setFilterByUrl: function () {
      var vm = this;
      var filterByUrl = $.deparam.querystring();
      _.forEach(filterByUrl, function (value, key) {
        vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
      });
    },
    // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
    resetFilter: function () {
      var vm = this;
      vm.filter = {
        id: undefined,
        sort: "",
        page: 1,
        per_page: 20,
        total_page: 10
      };

      this.applyFilter();
    },
    // thay đổi filter theo datetime
    onChangeDatetime: function (event) {
      var vm = this;
      vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm');
    },
    openModal(component, group) {
      this.currentGroup = group;
      this.currentModal = component;
    },
    closeModal() {
      this.currentModal = null;
      this.currentGroup = null;
    },
  },
  mounted: function() {
    var vm = this;

    vm.setFilterByUrl();

    // Loại bỏ các filter rỗng khỏi request
    var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
      return value !== undefined && value !== null && value !== '';
    }), ['total_page']);
    this.getList(filter);
  },
});
