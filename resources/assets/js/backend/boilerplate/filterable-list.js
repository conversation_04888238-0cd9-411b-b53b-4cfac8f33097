$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  }
});

const router = new VueRouter({
  mode: 'history'
});

Vue.filter('dateTimeToMinute', function (value) {
  return moment(value).format('HH:mm DD/MM/YYYY');
});
Vue.filter('printStatus', function (value) {
  return value ? 'Đã in' : 'Chưa in'
});
Vue.filter('printPassedStatus', function (value) {
  return value ? 'Đỗ' : 'Trượt'
});
Vue.filter('address', function (id) {
  let province = _.find(exam__results.provinces, {"id": parseInt(id)})
  return province ? province.name : '--';
});
Vue.filter('career', function (id) {
  let career = _.find(exam__results.careers, {"id": parseInt(id)})
  return career ? career.title : '--';
});
var exam__results = new Vue({
  el: '#exam__results--screen',
  components: {
    paginate: VuejsPaginate
  },
  data() {
    return {
      url: window.location.origin,
      loading: false,
      socket: null,
      filter: {
        id: undefined,
        time_from: undefined,
        time_to: undefined,
        sort: "",
        page: 1,
        per_page: 20,
        total_page: 10
      },
      messageContent: "",
      messageContentError: undefined,
      total_result: 0,
      syncingPass: false,
      syncingPlatform: false,
      promoteSuccess: 0,
      totalPromote: 0,
      showPromoteProcess: false,
    };
  },

  watch: {
    filter: {
      handler: function (val, oldVal) {
        var vm = this;
      },
      deep: true
    }
  },

  methods: {
    sortTotalScore: function () {
      var vm = this;
      switch (vm.filter.orderBy) {
        case "":
          vm.filter.orderBy = "total_score";
          break;
        case "total_score":
          if (vm.filter.sort === 'desc') {
            vm.filter.orderBy = "";
          }
          break;
        default:
          vm.filter.orderBy = "";
      }
      switch (vm.filter.sort) {
        case "":
          vm.filter.sort = "asc";
          break;
        case "asc":
          vm.filter.sort = "desc";
          break;
        case "desc":
          vm.filter.sort = "";
          break;
        default:
          vm.filter.sort = "";
      }
      vm.applyFilter();
    },
    // gọi hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
    changePage: function (pageNum) {
      var vm = this;
      vm.filter.page = pageNum;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page']);
      vm.getList(filter);

      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
    applyFilter: function () {
      var vm = this;

      vm.filter.page = 1;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page', 'page']);
      vm.getList(filter);

      // console.log(filter)
      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    onChangeCheckbox: function (param, event) {
      vm = this;

      vm.filter[param] = event.target.checked ? 1 : 0;
      vm.applyFilter();
    },
    // call api lấy danh sách invoices
    // truyền vào filter
    // set các biến trạng thái tmp để có thể dùng v-if, v-model
    getList: function(filter) {
      var vm = this;

      vm.loading = true;
      setTimeout(function () {
        $.get(window.location.origin +'/backend/thi-thu/jlpt-results', filter, function (res) {
          if(res.code == 200) {
            vm.results = res.data.results.map(function (result) {
              return result;
            });
            vm.filter.total_page = res.data.total_page;
            vm.total_result = res.data.total_result;

          } else {
            alert('Có lỗi! Liên hệ dev!!!');
          }
          vm.loading = false;
        });
      }, 200);
    },
    // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
    setFilterByUrl: function () {
      var vm = this;
      var filterByUrl = $.deparam.querystring();
      _.forEach(filterByUrl, function (value, key) {
        vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
      });
    },
    // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
    resetFilter: function () {
      var vm = this;
      vm.filter = {
        id: undefined,
        user_id: undefined,
        time_from: undefined,
        time_to: undefined,
        course: "",
        province: "",
        mobile: "",
        career: "",
        certificate_info: "",
        platform: "",
        is_passed: "",
        // is_printed: 0,
        page: 1,
        per_page: 20,
        total_page: 10
      };

      this.applyFilter();
    },
    // thay đổi filter theo datetime
    onChangeDatetime: function (event) {
      var vm = this;
      vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm');
    },
    sendMessage: function (content, conversationId, creatorId, adminId) {
      vm = this;
      // Emit socket
      var messageSocket = {
        content: content,
        conversationId: conversationId,
        receiverId: creatorId,
        senderId: adminId,
        senderType: 'admin',
        senderName: 'Dũng Mori',
        sentId:
          Date.now() +
          '_' +
          adminId +
          '_' +
          Math.floor(Math.random() * 100) +
          '' +
          (Math.floor(Math.random() * 100) + 100),
        type: 'text'
      };
      vm.socket.emit('send', messageSocket);
    },
    //tạo cuộc hội thoại với user chưa có
    initConversation: function(userId) {
      $.ajax({
        type: 'post',
        url: '/backend/user/create-conversation',
        data: {
          'id': userId
        },
        success: function (response) {
          // console.log("Tạo hội thoại mới", response);
          $(".fa-comments-" + userId).css('color', '#00ab2e');
          window.open(window.location.origin + "/backend/chat#" + response, "_blank");
        }
      });
    },
  },
  mounted: function() {
    var vm = this;

    // Connect socket
    vm.socket = io.connect('https://chat.dungmori.com');

    vm.setFilterByUrl();

    // Loại bỏ các filter rỗng khỏi request
    var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
      return value !== undefined && value !== null && value !== '';
    }), ['total_page']);
    this.getList(filter);
  },
});