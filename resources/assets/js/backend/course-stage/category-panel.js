Vue.component("lesson-category-panel", {
  template: "#lesson-categories-panel-template",

  props: ["course", "stage", "stages", "permission"],

  watch: {
    stage: function (val) {
      var vm = this;
      vm.getCategoriesByStage(this.course.id, val);
      this.formData.stage = val;
    },
    course: function (val) {
      var vm = this;
      vm.formData.course_id = val.id;
      vm.getCategoriesByStage(val, this.stage);
    },
  },
  data: function () {
    return {
      url: window.location.origin,
      categories: [],
      selectedCategory: {},
      loading: false,
      showModal: false,
      draggable: false,
      stages: this.stages,
      formData: {
        id: undefined,
        title: "",
        type: 1,
        icon: "",
        course_id: 0,
        stage: this.stage,
        status: 1,
        iconImg: null,
      },
    };
  },

  mounted: function () {
    var vm = this;
  },

  methods: {
    onDragEnd: function () {
      var vm = this;
      var ids = vm.categories.map(function (category) {
        return category.id;
      });
      var data = {
        course_id: vm.course.id,
        stage: vm.stage,
        ids: ids,
      };
      $.post(
        vm.url + "/backend/lesson-categories/apply-sorting",
        data,
        function (res) {
          if (res.code === 200) {
            vm.categories = res.data.map(function (category) {
              return category;
            });
          }
        }
      );
    },
    changeStatus: function (category, status) {
      var vm = this;
      var data = {
        id: category.id,
        status: status,
      };
      $.post(
        vm.url + "/backend/lesson-categories/change-status",
        data,
        function (res) {
          if (res.code === 200) {
            vm.categories = vm.categories.map(function (item) {
              if (item.id === res.data.id) {
                item.status = res.data.status;
              }
              return item;
            });
          }
        }
      );
    },
    changeFormIcon: function (event) {
      var vm = this;
      vm.formData.iconImg = event.target.files[0];
    },
    saveForm: function () {
      var vm = this;

      var data = new FormData();

      data.append("id", vm.formData.id);
      data.append("title", vm.formData.title);
      data.append("type", vm.formData.type);
      data.append("icon", vm.formData.icon);
      data.append("course_id", vm.formData.course_id);
      data.append("stage", vm.formData.stage);
      data.append("status", vm.formData.status);
      data.append("iconImg", vm.formData.iconImg);
      if (vm.formData.id) {
        vm.updateCategory(data);
      } else {
        vm.addCategory(data);
      }
    },
    editCategory: function (category) {
      var vm = this;

      vm.formData = {
        ...vm.formData,
        id: category.id,
        title: category.title,
        type: category.type,
        icon: category.icon,
        course_id: category.course_id,
        stage: category.stage,
        status: category.status,
      };
      vm.showModal = true;
    },
    addCategory: function (data) {
      var vm = this;

      $.ajax({
        url: vm.url + "/backend/lesson-categories/add-category",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function (res) {
          if (res.code === 200) {
            if (res.data.stage === vm.stage) {
              vm.categories.push(res.data);
            }
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        },
      });
    },
    updateCategory: function (data) {
      var vm = this;

      $.ajax({
        url: vm.url + "/backend/lesson-categories/update-category",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function (res) {
          if (res.code === 200) {
            vm.categories = vm.categories.map(function (category) {
              if (category.id === res.data.id) {
                category = res.data;
              }
              return category;
            });
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        },
      });
    },
    deleteCategory: function (category) {
      var vm = this;

      var confirm = window.confirm(
        "Xác nhận xoá danh mục cùng với toàn bộ bài học trong danh mục?"
      );
      if (confirm) {
        var data = {
          course_id: category.course_id,
          id: category.id,
        };
        $.post(
          vm.url + "/backend/lesson-categories/delete-category",
          data,
          function (res) {
            if (res.code === 200) {
              vm.categories = vm.categories.filter(function (item) {
                return item.id !== category.id;
              });
              if (vm.selectedCategory.id === category.id) {
                if (vm.categories.length > 0) {
                  vm.selectCategory(vm.categories[0]);
                } else {
                  vm.selectUncategorized();
                }
              }
            }
          }
        );
      }
    },
    setFormStatus: function (event) {
      var vm = this;
      vm.formData.status = parseInt(event.target.value);
    },
    closeModal: function () {
      var vm = this;
      vm.formData = {
        ...vm.formData,
        id: undefined,
        title: "",
        type: 1,
        icon: "",
        course_id: vm.course.id,
        status: 1,
        iconImg: null,
      };
      vm.showModal = false;
    },
    getCategoriesByStage: function (courseId, stage) {
      var vm = this;
      var data = {
        courseId: courseId,
        stage: stage,
      };
      vm.loading = true;
      $.post(vm.url + "/backend/lesson-categories", data, function (res) {
        if (res.code === 200) {
          vm.categories = [...res.data];
          vm.selectedCategory = vm.categories[0] || {};
          vm.$emit("update:category", vm.categories[0] || {});
          vm.loading = false;
        }
      });
    },
    selectCategory: function (category) {
      var vm = this;
      vm.selectedCategory = category;
      vm.$emit("update:category", category);
    },
    selectUncategorized: function () {
      var vm = this;
      vm.selectedCategory = {
        course_id: vm.course.id,
        icon: "",
        id: 0,
        stage: vm.stage.id,
        status: 1,
        title: "Chưa được phân loại",
        type: 1,
      };
      vm.$emit("update:category", vm.selectedCategory);
    },
  },
});
