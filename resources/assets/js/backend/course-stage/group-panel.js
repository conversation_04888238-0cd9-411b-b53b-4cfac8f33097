Vue.component("lesson-group-panel", {
  template: "#lesson-groups-panel-template",

  props: ["category", "permission"],

  data: function () {
    return {
      url: window.location.origin,
      groups: [],
      lessons: [],
      choseLessons: [],
      choseType: undefined,
      choseRule: undefined,
      groupIdToChange: undefined,
      categories: [],
      showingGroup: {},
      loading: false,
      showModal: false,
      showLessonsModal: false,
      draggable: false,
      playbackRate: [0.75, 1, 1.25, 1.5, 2],
      formData: {
        id: undefined,
        course_id: 0,
        name: "",
        description: "",
        component_count: "",
        skill: "",
        lesson_category_id: 0,
        show: 0,
        // is_secret: 0,
        is_specialezed: 0,
      },
    };
  },

  watch: {
    category: function (val) {
      var vm = this;
      vm.getGroupByCategory(val);
      vm.formData.lesson_category_id = val.id;
    },
  },
  mounted: function () {
    var vm = this;
  },

  methods: {
    printLessonIcon: function (type) {
      var vm = this;
      var imgName = "";
      switch (type) {
        case "docs":
          imgName = "docb.png";
          break;
        case "video":
          imgName = "videob.png";
          break;
        case "test":
          imgName = "quizb.png";
          break;
        case "flashcard":
          imgName = "fcb.png";
          break;
        default:
          imgName = "docb.png";
      }
      return imgName;
    },
    updateLesson: function (event, lesson, field) {
      var vm = this;
      var data = {
        id: lesson.id,
        field: field,
        value: event.target.value,
      };
      $.post(
        vm.url + "/backend/lesson-groups/update-lesson",
        data,
        function (res) {
          if (res.code === 200) {
            vm.lessons = vm.lessons.map(function (item) {
              if (item.id === res.data.id) {
                item[field] = res.data[field];
              }
              return item;
            });
            alert("Thành công");
          } else {
            alert("Thất bại");
          }
        }
      );
    },
    changeLessonSpeed: function (event, lesson) {
      var vm = this;
      var data = {
        id: lesson.id,
        speed: event.target.value,
      };
      $.post(
        vm.url + "/backend/lesson-groups/change-lesson-speed",
        data,
        function (res) {
          if (res.code === 200) {
            vm.lessons = vm.lessons.map(function (item) {
              if (item.id === res.data.id) {
                item.default_speed = res.data.default_speed;
              }
              return item;
            });
          } else {
            alert("Thất bại");
          }
        }
      );
    },
    changeLessonType: function () {
      var vm = this;
      if (vm.choseType && vm.choseLessons.length > 0) {
        var data = {
          ids: [...vm.choseLessons],
          choseType: vm.choseType,
        };
        $.post(
          vm.url + "/backend/lesson/change-lesson-type",
          data,
          function (res) {
            if (res.code === 200) {
              vm.choseLessons = [];
              vm.choseType = undefined;
              vm.lessons = vm.lessons.map(function (lesson, index) {
                lesson[index].type = res.data[index].type;
                return lesson;
              });
              alert("Chuyển thành công");
            } else {
              alert("Chuyển thất bại");
            }
          }
        );
      }
    },
    addLessonRule: function () {
      const vm = this;
      if (vm.choseRule && vm.choseLessons.length > 0) {
        var data = {
          ids: [...vm.choseLessons],
          choseRule: vm.choseRule,
        };
        $.post(
          vm.url + "/backend/lesson-groups/lesson-rule/attach",
          data,
          function (res) {
            if (res.code === 200) {
              const lessons = res.data;
              res.data.forEach((o) => {
                const idx = vm.lessons.findIndex(
                  (lesson) => lesson.id === o.id
                );
                if (idx === -1) {
                  vm.lessons.push(o);
                } else {
                  Vue.set(vm.lessons, idx, o);
                }
              });
            } else {
              alert("Chuyển thất bại");
            }
          }
        );
      }
    },
    changeGroupId: function () {
      var vm = this;
      if (vm.groupIdToChange && vm.choseLessons.length > 0) {
        var data = {
          ids: [...vm.choseLessons],
          groupIdToChange: vm.groupIdToChange,
        };
        $.post(vm.url + "/backend/lesson/change-group", data, function (res) {
          if (res.code === 200) {
            vm.lessons = vm.lessons.filter(function (lesson) {
              return !vm.choseLessons.includes(lesson.id);
            });
            vm.choseLessons = [];
            alert("Chuyển thành công");
          } else {
            alert("Chuyển thất bại");
          }
        });
      }
    },
    checkAllLesson: function (event) {
      var vm = this;

      var checked = event.target.checked;
      if (checked) {
        vm.choseLessons = vm.lessons.map(function (lesson) {
          return lesson.id;
        });
      } else {
        vm.choseLessons = [];
      }
      console.log("array --> ", vm.choseLessons);
    },
    checkOneLesson: function (event, lessonId) {
      var vm = this;
      var checked = event.target.checked;
      if (checked) {
        vm.choseLessons.push(lessonId);
      } else {
        vm.choseLessons = vm.choseLessons.filter(function (lesson) {
          return lesson !== lessonId;
        });
      }
      console.log("array --> ", vm.choseLessons);
    },
    changeLessonRulePoint: function (rule) {
      const vm = this;
      var point = window.prompt("Nhập số điểm");
      $.post(
        vm.url + `/backend/lesson-groups/rule/${rule.pivot.id}/point`,
        { point: point },
        function (res) {
          if (res.code === 200) {
            rule.pivot.point = point;
          } else {
            alert(res.msg);
          }
        }
      );
    },
    detachRule: function (lesson, ruleId, idx) {
      const vm = this;
      var data = {
        lesson_id: lesson.id,
        rule_id: ruleId,
      };
      $.post(
        vm.url + `/backend/lesson-groups/lesson-rule/detach`,
        data,
        function (res) {
          if (res.code === 200) {
            lesson.experience_rules.splice(idx, 1);
          } else {
            alert(res.msg);
          }
        }
      );
    },
    toggleHideLessonTitle: function (lesson) {
      var vm = this;
      var data = {
        id: lesson.id,
      };
      $.post(vm.url + "/backend/lesson/toggle-secret", data, function (res) {
        if (res.code === 200) {
          vm.lessons = vm.lessons.map(function (item) {
            if (item.id === res.data.id) {
              item.is_secret = res.data.is_secret;
            }
            return item;
          });
        } else {
          alert(res.msg);
        }
      });
    },
    onChangeCheckbox: function (event, param) {
      var vm = this;
      vm.formData[param] = event.target.checked ? 1 : 0;
    },
    onDragLessonEnd: function () {
      var vm = this;
      var ids = vm.lessons.map(function (lesson) {
        return lesson.id;
      });
      var data = {
        group_id: vm.showingGroup.id,
        ids: ids,
      };
      $.post(
        vm.url + "/backend/lesson-groups/apply-sorting-lesson",
        data,
        function (res) {
          if (res.code === 200) {
            vm.lessons = res.data.map(function (lesson) {
              return lesson;
            });
          }
        }
      );
    },
    closeLessonsModal: function () {
      var vm = this;
      vm.lessons = [];
      vm.showLessonsModal = false;
      vm.groupIdToChange = undefined;
      vm.choseLessons = [];
    },
    showLessons: function (group) {
      var vm = this;
      var data = {
        id: group.id,
      };
      vm.showingGroup = group;
      $.post(
        vm.url + "/backend/lesson-groups/get-lessons",
        data,
        function (res) {
          if (res.code === 200) {
            vm.lessons = [...res.data];
            vm.showLessonsModal = true;
          }
        }
      );
    },
    onDragEnd: function () {
      var vm = this;
      var ids = vm.groups.map(function (group) {
        return group.id;
      });
      var data = {
        course_id: vm.category.course_id,
        lesson_category_id: vm.category.id,
        ids: ids,
      };
      $.post(
        vm.url + "/backend/lesson-groups/apply-sorting",
        data,
        function (res) {
          if (res.code == 200) {
            vm.groups = res.data.map(function (group) {
              return group;
            });
          }
        }
      );
    },
    changeLessonStatus: function (lesson, show) {
      var vm = this;
      var data = {
        id: lesson.id,
        show: show,
      };
      $.post(
        vm.url + "/backend/lesson-groups/change-lesson-status",
        data,
        function (res) {
          if (res.code === 200) {
            vm.lessons = vm.lessons.map(function (item) {
              if (item.id === res.data.id) {
                item.show = res.data.show;
              }
              return item;
            });
          }
        }
      );
    },
    changeStatus: function (group, show) {
      var vm = this;
      var data = {
        id: group.id,
        show: show,
      };
      $.post(
        vm.url + "/backend/lesson-groups/change-status",
        data,
        function (res) {
          if (res.code === 200) {
            vm.groups = vm.groups.map(function (item) {
              if (item.id === res.data.id) {
                item.show = res.data.show;
              }
              return item;
            });
          }
        }
      );
    },
    saveForm: function () {
      var vm = this;

      var data = new FormData();

      data.append("id", vm.formData.id);
      data.append("name", vm.formData.name);
      data.append("description", vm.formData.description);
      data.append("skill", vm.formData.skill);
      data.append("component_count", vm.formData.component_count);
      data.append("course_id", vm.category.course_id);
      data.append("lesson_category_id", vm.formData.lesson_category_id);
      data.append("show", vm.formData.show);
      // data.append('is_secret', vm.formData.is_secret);
      if (vm.formData.id) {
        vm.updateGroup(data);
      } else {
        vm.addGroup(data);
      }
    },
    editGroup: function (group) {
      console.log("group", group);
      var vm = this;

      vm.formData = {
        ...vm.formData,
        id: group.id,
        name: group.name,
        description: group.description,
        skill: group.skill,
        component_count: group.component_count,
        lesson_category_id: group.lesson_category_id,
        show: group.show,
        // is_secret: group.is_secret,
      };
      vm.showModal = true;
    },
    addGroup: function (data) {
      var vm = this;

      $.ajax({
        url: vm.url + "/backend/lesson-groups/add-group",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function (res) {
          if (res.code === 200) {
            if (res.data.lesson_category_id === vm.category.id) {
              vm.groups.push(res.data);
            }
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        },
      });
    },
    updateGroup: function (data) {
      var vm = this;

      $.ajax({
        url: vm.url + "/backend/lesson-groups/update-group",
        type: "POST",
        data: data,
        processData: false,
        contentType: false,
        success: function (res) {
          if (res.code === 200) {
            if (res.data.lesson_category_id !== vm.category.id) {
              vm.groups = _.remove(vm.groups, function (group) {
                return group.id !== res.data.id;
              });
            } else {
              vm.groups = vm.groups.map(function (group) {
                if (group.id === res.data.id) {
                  group = { ...res.data };
                }
                return group;
              });
            }
            vm.closeModal();
          } else {
            alert(res.msg);
          }
        },
      });
    },
    deleteGroup: function (group) {
      var vm = this;

      var confirm = window.confirm(
        "Xác nhận xoá nhóm bài học cùng với toàn bộ bài học trong nhóm?"
      );
      if (confirm) {
        var data = {
          id: group.id,
        };
        $.post(
          vm.url + "/backend/lesson-groups/delete-group",
          data,
          function (res) {
            if (res.code === 200) {
              vm.groups = vm.groups.filter(function (item) {
                return item.id !== group.id;
              });
            }
          }
        );
      }
    },
    setFormStatus: function (event) {
      var vm = this;
      vm.formData.show = parseInt(event.target.value);
    },
    closeModal: function () {
      var vm = this;
      vm.formData = {
        ...vm.formData,
        id: undefined,
        course_id: 0,
        name: "",
        description: "",
        skill: "",
        component_count: "",
        show: 0,
        // is_secret: 0,
      };
      vm.showModal = false;
    },
    getGroupByCategory: function (category) {
      var vm = this;
      vm.loading = true;
      var data = {
        course_id: category.course_id,
        id: category.id,
      };
      $.post(vm.url + "/backend/lesson-groups", data, function (res) {
        if (res.code === 200) {
          vm.groups = [...res.data.groups];
          vm.categories = [...res.data.categories];
          vm.loading = false;
        }
      });
    },
  },
});
