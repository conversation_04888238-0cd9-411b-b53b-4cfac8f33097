var courseStageScreen = new Vue({
  el: "#course-stages__screen",
  data: function () {
    return {
      url: window.location.origin,
      admin: {},
      permission: 0,
      courses: [],
      stages: [],
      stage_names: [],
      selectedCourse: {},
      selectedStage: 1,
      selectedCategory: {},
      loading: false,
      formData: {
        id: undefined,
        title: "",
        key: "",
        course_id: "",
        status: 1,
      },
    };
  },
  methods: {
    getStageName: function (stage) {
      if (this.stage_names.length === 0) {
        return;
      }
      return this.stage_names.filter((entity) => entity.stage === stage)
        .length > 0
        ? this.stage_names.filter((entity) => entity.stage === stage)[0]
            .stage_name
        : null;
    },
    getReleaseDate: function (stage) {
      if (this.stage_names.length === 0) {
        return;
      }
      return this.stage_names.filter((entity) => entity.stage === stage)
        .length > 0
        ? this.stage_names.filter((entity) => entity.stage === stage)[0]
            .release_date
        : null;
    },
    categorySelected: function (event) {
      var vm = this;
      vm.selectedCategory = event;
    },
    selectCourse: function (event, course) {
      var vm = this;

      if (event.target.checked) {
        vm.selectedCourse = { ...course };
      }
    },
    checkAdmin: function (admin) {
      return (
        vm.admin.permission === 1 ||
        vm.admin.id === 36 ||
        vm.admin.id === 4 ||
        vm.admin.id === 69
      );
    },
    addStage: function () {
      this.stages.push(this.stages.length + 1);
    },
    setMaxStage: function (maxStage) {
      this.stages = [];
      for (let i = 1; i <= maxStage; i++) {
        this.stages.push(i);
      }
    },
    removeLastStage: function () {
      const vm = this;
      const isConfirmed = confirm(
        "Bạn có chắc chắn muốn xóa chặng cuối chứ? Dữ liệu không thể khôi phục sau khi xóa!"
      );
      if (!isConfirmed) {
        return;
      }
      vm.stages.pop();

      $.post(
        vm.url + "/backend/delete-last-stage",
        {
          course_id: vm.selectedCourse.id,
        },
        function (res) {
          console.log(res);
        }
      );
    },
    getMaxStage: function () {
      const vm = this;
      if (vm.selectedCourse) {
        const courseId = vm.selectedCourse.id;
        $.get(
          vm.url + "/backend/get-max-stage?courseId=" + courseId,
          function (res) {
            vm.setMaxStage(res.max_stage);
            vm.stage_names = res.stage_list;
          }
        );
      }
    },
    changeStageName(courseId, stage) {
      const vm = this;
      const stageName = $(`#stage_${stage}`).val();

      if (
        this.stage_names.filter((entity) => entity.stage === stage).length == 0
      ) {
        alert("Bạn phải tạo chặng trước khi cập nhật tên chặng!");
        return;
      }
      $.post(
        vm.url + "/backend/update-stage-name",
        {
          course_id: courseId,
          stage: stage,
          stage_name: stageName,
        },
        function (res) {
          alert("Cập nhật tên chặng thành công!");
        }
      );
    },
    changeReleaseDate(courseId, stage) {
      const vm = this;
      const releaseDate = $(`#release_date_${stage}`).val();
      if (!releaseDate) {
        alert("Bạn phải nhập ngày ra mắt!");
        return;
      }
      if (releaseDate.length !== 10) {
        alert("Ngày ra mắt phải có định dạng dd/mm/yyyy!");
        return;
      }
      const releaseDateArray = releaseDate.split("/");
      if (releaseDateArray.length !== 3) {
        alert("Ngày ra mắt phải có định dạng dd/mm/yyyy!");
        return;
      }
      if (releaseDateArray[0].length !== 2 || releaseDateArray[1].length !== 2 || releaseDateArray[2].length !== 4) {
        alert("Ngày ra mắt phải có định dạng dd/mm/yyyy!");
        return;
      }
      if (releaseDateArray[0] > 31 || releaseDateArray[1] > 12 || releaseDateArray[2] > 2025) {
        alert("Ngày ra mắt phải có định dạng dd/mm/yyyy!");
        return;
      }
      $.post(
        vm.url + "/backend/update-release-date",
        {
          course_id: courseId,
          stage: stage,
          release_date: releaseDate,
        },
        function (res) {
          alert("Cập nhật ngày ra mắt thành công!");
        }
      );
    },
  },
  watch: {
    selectedCourse: function () {
      this.getMaxStage();
    },
  },
  mounted: function () {
    var vm = this;
    vm.admin = admin;
    vm.permission =
      vm.admin.permission === 1 ||
      vm.admin.id === 36 ||
      vm.admin.id === 4 ||
      vm.admin.id === 69;

    vm.courses = _.orderBy(courses, "name").filter(function (course) {
      if (
        [
          "N1",
          "N2",
          "N3",
          "N4",
          "N5",
          "EJU - Toán",
          "Sơ cấp N4",
          "Sơ cấp N5",
          "Luyện đề N4",
          "Luyện đề N5",
          "JLPT N1",
          "JLPT N2",
          "JLPT N3",
          "Chữ Hán N5",
        ].includes(course.name)
      ) {
        return course;
      }
    });
    vm.selectedCourse = vm.courses[0];
    vm.getMaxStage();
  },
});
