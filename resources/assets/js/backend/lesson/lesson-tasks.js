$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});
Vue.filter('show', function(value) {
    switch (value) {
        case 0:
            return 'Tắt';
        case 1:
            return 'Bật';
        case 2:
            return 'Testing';
        default:
            return 'Tắt';
    };
});
Vue.filter('taskType', function(value) {
    switch (value) {
        case 1:
            return 'Nội dung';
            break;
        case 2:
            return 'Mp4/Youtube';
            break;
        case 3:
            return 'Trắc nghiệm';
            break;
        case 4:
            return 'Kết quả';
            break;
        case 5:
            return 'Mp3';
            break;
        case 6:
            return 'Trả lời câu hỏi';
            break;
        case 7:
            return 'Kaiwa';
            break;
        case 8:
            return 'Tài liệu PDF';
            break;
        case 9:
            return 'Flashcard';
            break;
        case 10:
            return 'Quiz trắc nghiệm';
            break;
        case 11:
            return 'Sắp xếp câu';
            break;
        case 12:
            return 'Video tương tác';
            break;
        default:
            return 'Nội dung';
    }
});
Vue.component('lesson-tasks', {
    template: '#lesson-tasks-template',
    props: ['lesson', 'gradeByTask'],
    data: function () {
        var vm = this;
        return {
            url: window.location.origin,
            tasks: [],
            currentTask: {},
            is_examination: vm.lesson.is_examination,
            total_marks: vm.lesson.total_marks,
            pass_marks: vm.lesson.pass_marks,
            type: 1,
            detailLesson: vm.lesson,
            showModal: false,
            draggable: true
        };
    },

    watch: {

    },
    mounted: function () {
        var vm = this;
        vm.setToastrTimeout();
        vm.getComponentsByLesson();
    },
    methods: {
        closeModal: function() {
            var vm = this;
            vm.currentTask = {};
            setTimeout(function () {
                vm.showModal = false;
            }, 100);
        },
        setToastrTimeout: function () {
            toastr.options.timeOut = 2000;
            toastr.options.extendedTimeOut = 2000;
        },

        renderPoint: function (component) {
            if (component.grade == 0) {
                return "<span></span>";
            }
            else if (component.match_grade) {
                return "<span class='text-success font-bold'>" + component.grade + "</span>";
            }
            else {
                return "<span class='text-danger font-bold'>" + component.grade + "</span>";
            }
        },

        getChipColor: function (val) {
            switch (val) {
                case 0:
                    return '#eb3434';
                case 1:
                    return '#2d973d';
                case 2:
                    return '#4c57eb';
                default:
                    return '#eb3434';
            }
        },

        getComponentsByLesson: function() {
            var vm = this;
            var data = {
                id: vm.lesson.id
            };
            $.post(vm.url + '/backend/new-lesson/components-by-lesson', data, function (res) {
                if (res.code == 200) {
                    vm.tasks = res.data.map((task) => {
                        if (task.type === 4) {
                            console.log(JSON.parse(task.value));
                        }
                        return task;
                    });
                }
            });
        },
        save: function () {
            var data = {
                lesson_id: vm.detailLesson.id,
                is_examination: vm.is_examination ? 1 : 0,
                total_marks: vm.total_marks,
                pass_marks: vm.pass_marks
            };
            $.post(vm.url + '/backend/new-lesson/update/content', data, function (res) {
                if (res.code == 200) {
                    toastr.success('Thay đổi thành công!!')
                }
            });
        },
        cancel: function () {
            var vm = this;

            var course = _.get(vm.detailLesson, 'get_course.id', '');
            var group_id = _.get(vm.detailLesson, 'group_id', '');
            var check = vm.is_examination != vm.lesson.is_examination || vm.total_marks != vm.lesson.total_marks || vm.pass_marks != vm.lesson.pass_marks;
            if (check) {
                var confirm = window.confirm('Xác nhận chuyển trang. Tất cả thay đổi sẽ không được lưu lại');
                if (confirm) {
                    window.location.href = vm.url + '/backend/lesson/filter/' + course + '/' + group_id;
                }
            } else {
                window.location.href = vm.url + '/backend/lesson/filter/' + course + '/' + group_id;
            }
        },
        deleteTask: function (id) {
            var vm = this;
            var confirm = window.confirm('Xác nhận xoá tác vụ?');
            if (confirm) {
                var data = {
                    id: id
                };
                $.post(vm.url + '/backend/new-lesson/delete-component', data, function (res) {
                    if (res.code == 200) {
                        vm.tasks = vm.tasks.filter(function (task) {
                            return task.id != res.data.id
                        });
                        toastr.success('Đã xoá tác vụ #' + res.data.id)
                    }
                })
            }
        },
        onDragEnd: function () {
            var vm = this;
            var ids = vm.tasks.map(function (task) {
               return task.id;
            });
            var data = {
                lesson_id: vm.detailLesson.id,
                ids: ids
            };
            $.post(vm.url + '/backend/new-lesson/apply-sorting', data, function (res) {
                if (res.code == 200) {
                    vm.tasks = res.data.map(function (task) {
                        return task;
                    })
                }
            })
        },
        editTask: function (task) {
            var vm = this;
            vm.type = task.type;
            vm.currentTask = task;
            vm.showModal = true;
        },
        pushAddedTask: function(event) {
            var vm = this;
            event.show = parseInt(event.show);
            vm.tasks.push(event);
            vm.closeModal();
        },
        updateTask: function (event) {
            var vm = this;
            event.show = parseInt(event.show);
            vm.tasks = vm.tasks.map(function (task) {
                if (task.id == event.id) {
                    task = event;
                }
                return task;
            });
            vm.closeModal();
        }
    }
});
