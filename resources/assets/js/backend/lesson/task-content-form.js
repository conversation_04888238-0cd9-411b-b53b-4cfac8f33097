$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});
Vue.use(CKEditor);
Vue.component("task-content-form", {
  template: "#task-content-form-template",
  props: ["lesson_id", "type", "currentTask"],
  data: function () {
    return {
      url: window.location.origin,
      task: {},
      show: 1,
      is_quiz: 0,
      value: "",
      editorConfig: {
        minHeight: "300px",
        filebrowserBrowseUrl: this.url + '/backend/ckfinder/browser',
        filebrowserUploadUrl:
          this.url +
          '/backend/ckfinder/connector?command=QuickUpload&type=Files',
        extraPlugins: [
          "custom",
          "maximize",
          "sourcearea",
          "button",
          "panelbutton",
          "fakeobjects",
          "justify",
          "colorbutton",
          "dialogui",
          "dialog",
          "filetools",
          "popup",
          "filebrowser",
          "font",
          "table",
          "image",
          "furigana",
          "panel",
          "listblock",
          "floatpanel",
          "richcombo",
          "format",
        ],
        allowedContent: true,
      },
    };
  },
  mounted: function () {
    var vm = this;
    vm.task = vm.currentTask;
    vm.show = vm.task.hasOwnProperty("show") ? vm.task.show : 1;
    vm.is_quiz = vm.task.hasOwnProperty("is_quiz") ? vm.task.is_quiz : 0;
    vm.value = vm.task.value ? vm.task.value : "";
  },
  methods: {
    onSaveTask: function () {
      const vm = this;

      var data = {
        id: vm.currentTask.id,
        lesson_id: vm.lesson_id,
        type: vm.type,
        show: vm.show ? 1 : 0,
        is_quiz: vm.is_quiz ? 1 : 0,
        value: vm.value,
      };
      if (!data.id) {
        vm.onAddNewTask(data);
      } else {
        vm.onUpdateTask(data);
      }
    },
    onAddNewTask: function (data) {
      const vm = this;
      $.post(vm.url + "/backend/new-lesson/content/add", data, function (res) {
        if (res.code == 200) {
          vm.$emit("addedTask", res.data);
          toastr.success("Thay đổi thành công!!");
        }
      });
    },
    onUpdateTask: function (data) {
      const vm = this;
      $.ajax({
        url: vm.url + "/backend/new-lesson/content/update",
        type: "PUT",
        data: data,
        success: function (res) {
          if (res.code == 200) {
            vm.$emit("updatedTask", res.data);
            toastr.success("Thay đổi thành công!!");
          }
        },
      });
    },
    cancel: function () {
      const vm = this;
      vm.$emit("closeModal");
    },
  },
});
