$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});

Vue.component("lesson-info", {
  template: "#lesson-info-template",
  props: ["lesson", "groups", "courses", "authors"],
  data: function () {
    var vm = this;
    return {
      lessonGroups: vm.groups,
      name: _.get(vm.lesson, "name", ""),
      show: _.get(vm.lesson, "show", 0),
      author: _.get(vm.lesson, "get_author.id", ""),
      course: _.get(vm.lesson, "get_course.id", ""),
      group_id: _.get(vm.lesson, "group_id", ""),
      price_option: _.get(vm.lesson, "price_option", 0),
      feature: _.get(vm.lesson, "feature", 0),
      avatar_name: _.get(vm.lesson, "avatar_name", ""),
      avatarPreview: "",
    };
  },
  watch: {
    status: function (val) {
      this.status = val ? 1 : 0;
    },
    course: function (val) {
      const vm = this;
      vm.getGroupsByCourseId(val);
    },
  },
  mounted: function () {
    const vm = this;
    // console.log(this.authors);
  },
  methods: {
    // convert ảnh theo base64
    readURL: function (input) {
      const vm = this;
      if (input.target.files && input.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
          vm.avatarPreview = e.target.result;
        };
        reader.readAsDataURL(input.target.files[0]);
      }
    },
    submit: function () {
      const vm = this;
      var data = new FormData($("#lesson-info-form")[0]);
      data.append("id", vm.lesson.id);
      data.append("lesson", vm.name);
      data.append("course", vm.course);
      data.append("group", vm.group_id);
      data.append("feature", vm.feature);
      data.append("status", vm.show);
      data.append("teacher", vm.author);
      data.append("price", vm.price_option);
      $.ajax({
        url: window.location.origin + "/backend/new-lesson/update",
        type: "post",
        processData: false,
        contentType: false,
        data: data,
        success: function (res) {
          window.location.href =
            window.location.origin +
            "/backend/lesson/filter/" +
            vm.course +
            "/" +
            vm.group_id;
        },
      });
    },
    getGroupsByCourseId: function (courseId) {
      const vm = this;

      var data = {
        course_id: courseId,
      };

      $.post(
        window.location.origin + "/backend/group-by-course",
        data,
        function (res) {
          vm.lessonGroups = res.group;
          if (res.group.length > 0) {
            vm.group_id = res.group[0].id;
          }
        }
      );
    },
  },
});
