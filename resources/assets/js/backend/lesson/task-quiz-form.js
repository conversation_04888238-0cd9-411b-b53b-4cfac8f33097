$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});
Vue.use(CKEditor);
Vue.component("task-quiz-form", {
  template: "#task-quiz-form-template",
  props: ["lesson_id", "type", "currentTask"],
  data: function () {
    return {
      url: window.location.origin,
      task: {},
      show: 1,
      value: "",
      grade: 0,
      answers: [
        {
          id: undefined,
          sort: 1,
          value: "",
          grade: 0,
        },
        {
          id: undefined,
          sort: 2,
          value: "",
          grade: 0,
        },
      ],
      checked: undefined,
      editorConfig: {
        minHeight: "300px",
        filebrowserBrowseUrl: this.url + '/backend/ckfinder/browser',
        filebrowserUploadUrl:
          this.url +
          '/backend/ckfinder/connector?command=QuickUpload&type=Files',
        extraPlugins: [
          "maximize",
          "sourcearea",
          "button",
          "panelbutton",
          "fakeobjects",
          "justify",
          "colorbutton",
          "dialogui",
          "dialog",
          "filetools",
          "popup",
          "filebrowser",
          "font",
          "table",
          "image",
          "furigana",
          "panel",
          "listblock",
          "floatpanel",
          "richcombo",
          "format",
        ],
        allowedContent: true,
        enterMode: CKEDITOR.ENTER_BR,
      },
    };
  },
  mounted: function () {
    var vm = this;
    vm.task = vm.currentTask;
    vm.show = vm.task.hasOwnProperty("show") ? vm.task.show : 1;
    vm.value = vm.task.value ? vm.task.value : "";
    vm.grade = vm.task.grade ? vm.task.grade : "";
    if (vm.currentTask.id) {
      vm.getAnswersByTask();
    }
  },
  methods: {
    onSaveTask: function () {
      const vm = this;

      var data = {
        id: vm.currentTask.id,
        lesson_id: vm.lesson_id,
        type: vm.type,
        show: vm.show ? 1 : 0,
        value: vm.value,
        grade: parseInt(vm.grade),
        answers: vm.answers,
      };
      var validate = vm.validate();
      if (validate) {
        if (!data.id) {
          vm.onAddNewTask(data);
        } else {
          vm.onUpdateTask(data);
        }
      }
    },
    onAddNewTask: function (data) {
      const vm = this;

      $.post(vm.url + "/backend/new-lesson/quiz/add", data, function (res) {
        if (res.code == 200) {
          vm.$emit("addedTask", res.data);
          toastr.success("Thay đổi thành công!!");
        }
      });
    },
    onUpdateTask: function (data) {
      const vm = this;

      $.ajax({
        url: vm.url + "/backend/new-lesson/quiz/update",
        type: "PUT",
        data: data,
        success: function (res) {
          if (res.code == 200) {
            vm.$emit("updatedTask", res.data);
            toastr.success("Thay đổi thành công!!");
          }
        },
      });
    },
    cancel: function () {
      const vm = this;
      vm.$emit("closeModal");
    },
    addAnswers: function () {
      const vm = this;
      var answers = vm.answers;
      var answerSorts = vm.answers.map(function (answer) {
        return answer.sort;
      });
      var maxSort = answers.length > 0 ? _.max(answerSorts) : 0;
      answers.push({
        id: undefined,
        sort: maxSort + 1,
        value: undefined,
        grade: 0,
      });
      vm.answers = answers;
    },
    checkedAnswer: function () {
      const vm = this;
      vm.answers = vm.answers.map(function (answer) {
        answer.grade = answer.sort == vm.checked ? parseInt(vm.grade) : 0;
        return answer;
      });
    },
    removeRow: function (answer) {
      const vm = this;
      vm.answers = vm.answers.filter(function (item) {
        return item.sort != answer.sort;
      });
      for (let i = 0; i < vm.answers.length; i++) {
        vm.answers[i].sort = i + 1;
      }
    },
    validate: function () {
      const vm = this;
      if (_.isNil(vm.value) || vm.value == "") {
        toastr.error("Không bỏ trống nội dung câu hỏi");
        return false;
      }
      if (_.isNil(vm.grade) || vm.grade == 0) {
        toastr.error("Điểm của bài quiz phải lớn hơn 0");
        return false;
      }
      var grade = 0;
      for (let i = 0; i < vm.answers.length; i++) {
        grade += parseInt(vm.answers[i].grade);
        if (_.isNil(vm.answers[i].value) || vm.answers[i].value == "") {
          toastr.error("Không bỏ trống nội dung câu trả lời");
          return false;
        }
      }
      if (grade == 0) {
        toastr.error("Cần có câu trả lời đúng");
        return false;
      }
      return true;
    },
    getAnswersByTask: function () {
      const vm = this;
      var id = vm.currentTask.id;

      $.post(
        vm.url + "/backend/new-lesson/quiz/get-answers-by-task",
        { id: id },
        function (res) {
          if (res.code == 200) {
            vm.answers = res.data.map(function (item) {
              if (parseInt(item.grade) > 0) {
                vm.checked = item.sort;
              }
              return {
                id: item.id,
                sort: item.sort,
                value: item.value,
                grade: parseInt(item.grade),
              };
            });
          }
        }
      );
    },
  },
});
