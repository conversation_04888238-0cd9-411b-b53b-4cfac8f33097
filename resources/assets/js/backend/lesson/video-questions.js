$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});
Vue.use(CKEditor);
const options = {
  playbackRates: [0.75, 1, 1.25, 1.5, 2],
  preload: "auto",
  controlBar: {
    volumeMenuButton: {
      vertical: true,
      inline: false,
      volumeBar: {
        vertical: true,
      },
      volumeLevel: true,
    },
  },
};
Vue.filter("timeline", function (duration) {
  duration = (duration * 1000).toFixed();
  var milliseconds = duration % 1000,
    seconds = Math.floor((duration / 1000) % 60),
    minutes = Math.floor((duration / (1000 * 60)) % 60),
    hours = Math.floor((duration / (1000 * 60 * 60)) % 24);

  hours = hours < 10 ? "0" + hours : hours;
  minutes = minutes < 10 ? "0" + minutes : minutes;
  seconds = seconds < 10 ? "0" + seconds : seconds;

  return hours + ":" + minutes + ":" + seconds + "." + milliseconds;
});
Vue.filter("timeruler", function (duration) {
  duration = (duration * 1000).toFixed();
  var milliseconds = duration % 1000,
    seconds = Math.floor((duration / 1000) % 60),
    minutes = Math.floor((duration / (1000 * 60)) % 60),
    hours = Math.floor((duration / (1000 * 60 * 60)) % 24);

  hours = hours < 10 ? "0" + hours : hours;
  minutes = minutes < 10 ? "0" + minutes : minutes;
  seconds = seconds < 10 ? "0" + seconds : seconds;

  return (hours > 0 ? hours + ":" : "") + minutes + ":" + seconds;
});
Vue.filter("timeInSeconds", function (time) {
  return time.toFixed(3);
});

const initLayer = {
  id: undefined,
  uid: 1,
  type: 1,
  lesson_component_id: 41870,
  content: "Em ăn cơm chưa? Have you eaten cooked rice?",
  answers: [
    { id: 1, sort: 1, grade: 1, value: "Em ăn cơm rồi!" },
    { id: 2, sort: 2, grade: 0, value: "Ăn rồi" },
    { id: 3, sort: 3, grade: 0, value: "Seen" },
    {
      id: 4,
      sort: 4,
      grade: 0,
      value: "Bạn không thể trả lời cuộc trò chuyện này",
    },
  ],
  time_start: 146.953,
  time_end: 154.953,
  length: 8,
  a_length: 4,
  width: 100,
  height: 30,
  saved: true,
};
var interactVideoPlayer = new Vue({
  el: "#video__questions-screen",
  data: function () {
    return {
      selectedLayer: {},
      layers: [initLayer],
      player: null,
      currentTime: 0,
      videoTimelineRatio: 1,
      videoDuration: 0,
      timelineWidth: 0,
      ruler: [],
      url: window.location.origin,
      taskId: undefined,
      lessonId: undefined,
      currentServerUrl: "https://vn.dungmori.com",
      currentQuality: "720p",
      currentMediaName: "",
      questions: [],
      modal: [],
      modalOpenStatus: false,
      modalComponents: [],
      fullscreen: false,
      timeLayerBinding: false,
      editorConfig: {
        minHeight: "300px",
        height: "300px",
        filebrowserBrowseUrl:
          window.location.origin + '/backend/ckfinder/browser',
        filebrowserUploadUrl:
          window.location.origin +
          '/backend/ckfinder/connector?command=QuickUpload&type=Files',
        extraPlugins: ["panelbutton", "colorbutton", "furigana"],
        allowedContent: false,
      },
    };
  },
  watch: {
    currentTime: {
      // We have to move our method to a handler field
      handler: function (val) {
        var vm = this;

        vm.modalComponents.forEach(function (component) {
          if (!component.question.modal) {
            component.currentTime = vm.currentTime;
          }
        });

        vm.layers.forEach(function (question, index) {
          if (question.type === 1) {
            let showShortQuestion =
              question &&
              !vm.modal[index].opened_ &&
              _.inRange(
                val,
                question.time_start,
                question.time_end + question.a_length
              );
            if (showShortQuestion) {
              vm.openModal(index);
            }
            if (
              !_.inRange(
                val,
                question.time_start,
                question.time_end + question.a_length
              )
            ) {
              if (vm.modal[index].opened_) {
                vm.closeModal(index);
              }
            }
          }
          if (question.type === 2) {
            let showLongQuestion =
              question &&
              !question.open_once &&
              !vm.modal[index].opened_ &&
              _.inRange(
                val,
                question.time_start - 0.25,
                question.time_start + 0.25
              );

            if (showLongQuestion) {
              vm.openModal(index);
            }
          }
        });
      },
    },
  },
  created: function () {
    window.addEventListener("keydown", this.detectKeyDown);
    window.addEventListener("keyup", this.detectKeyUp);
  },
  mounted: function () {
    var vm = this;
    toastr.options.timeOut = 1500;
    toastr.options.positionClass = "toast-top-center";
    vm.taskId = taskId;
    vm.lessonId = lessonId;
    vm.getQuestions(function () {
      vm.getTask();
    });
    vm.getTimelineWidth();
  },
  methods: {
    getQuestionMarkerColor: function (question) {
      switch (question.type) {
        case 1:
          return "marker__orange";
        case 2:
          return "marker__orange";
        default:
          return "";
      }
    },
    getQuestions: function (callback) {
      var vm = this;

      var data = {
        id: vm.taskId,
      };

      $.post(
        vm.url + "/backend/new-lesson/interactive-video/questions",
        data,
        function (res) {
          if (res.code === 200) {
            vm.layers = res.data.map(function (layer, index) {
              layer.uid = index + 1;
              layer.answers = JSON.parse(layer.answers).map(function (answer) {
                answer.selected = false;
                answer.grade = parseInt(answer.grade);
                return answer;
              });
              layer.modal = false;
              layer.time_up = false;
              layer.height = initLayer.height;
              layer.saved = true;
              return layer;
            });
            callback();
          } else {
            toastr.warning("Xảy ra lỗi");
          }
        }
      );
    },
    validateQuestion: function (layer) {
      // if (!layer.content || layer.content === '') {
      //     toastr.warning('Nội dung câu hỏi không được bỏ trống')
      //     return false;
      // }
      return true;
    },
    saveAddLayer: function (layer) {
      var vm = this;

      if (vm.validateQuestion(layer)) {
        var data = {
          ...layer,
        };
        $.post(
          vm.url + "/backend/new-lesson/interactive-video/layer/add",
          data,
          function (res) {
            if (res.code === 200) {
              layer.id = res.data.question.id;
              layer.answers = res.data.question.answers;
              layer.saved = true;

              vm.player.markers.add([
                {
                  time: layer.time_start,
                  text: "Câu hỏi",
                  class: vm.getQuestionMarkerColor(layer),
                },
              ]);
              vm.initModal(layer);
              vm.selectedLayer = {};
              toastr.success("Thành công");
            } else {
              toastr.error("Xảy ra lỗi");
            }
          }
        );
      }
    },
    getLayerIndex: function (layer) {
      var vm = this;

      var idx = 0;
      vm.layers.forEach(function (item, index) {
        if (layer.uid === item.uid) {
          idx = index;
        }
      });
      return idx;
    },
    saveUpdateLayer: function (layer) {
      var vm = this;

      var data = {
        ...layer,
      };
      $.post(
        vm.url + "/backend/new-lesson/interactive-video/layer/update",
        data,
        function (res) {
          if (res.code === 200) {
            layer.saved = true;
            vm.selectedLayer = {};
            toastr.success("Thành công");

            // cập nhật lại thời gian hiển thị của marker
            var markers = vm.player.markers.getMarkers();
            console.log(vm.getLayerIndex(layer));
            markers[vm.getLayerIndex(layer)].time = layer.time_start;
            vm.player.markers.updateTime();
          } else {
            toastr.error("Có lỗi");
          }
        }
      );
    },
    saveDeleteLayer: function (layer) {
      var vm = this;

      if (!layer.id) {
        vm.layers = vm.layers.filter(function (item) {
          return item.uid !== layer.uid;
        });
        toastr.success("Thành công");
      } else {
        var data = {
          id: layer.id,
        };
        $.post(
          vm.url + "/backend/new-lesson/interactive-video/layer/delete",
          data,
          function (res) {
            if (res.code === 200) {
              vm.selectedLayer = {};
              vm.layers = vm.layers.filter(function (item, index) {
                if (item.uid === layer.uid || item.id === layer.id) {
                  vm.modal.splice(index, 1);
                  vm.player.markers.remove([index]);
                  vm.player.children_.filter(function (item) {
                    if (item.name_ === "modal_" + layer.uid) {
                      vm.player.contentEl().removeChild(item.el());
                    }
                    return item.name !== "modal_" + layer.uid;
                  });
                }
                return item.uid !== layer.uid;
              });
              toastr.success("Thành công");
            } else {
              toastr.error("Có lỗi. F5 lại thử xem");
            }
          }
        );
      }
    },
    addLayer: function (type) {
      var vm = this;

      var maxUID = 0;
      if (vm.layers.length === 0) {
        maxUID = 1;
      } else {
        var maxUIDLayer = _.maxBy(vm.layers, function (o) {
          return o.uid;
        });
        maxUID = maxUIDLayer.uid + 1;
      }
      var newLayer = {
        id: undefined,
        uid: maxUID,
        type: type,
        lesson_component_id: vm.taskId,
        content: "",
        answers: [
          { id: undefined, sort: 1, grade: 1, value: "" },
          { id: undefined, sort: 2, grade: 0, value: "" },
          { id: undefined, sort: 3, grade: 0, value: "" },
          { id: undefined, sort: 4, grade: 0, value: "" },
        ],
        time_start: parseFloat(vm.currentTime).toFixed(3),
        time_end: (parseFloat(vm.currentTime) + 8).toFixed(3),
        length: 8,
        a_length: 4,
        width: 100,
        height: initLayer.height,
        saved: false,
      };
      vm.layers.push(newLayer);
      vm.selectedLayer = newLayer;
    },
    selectAnswerLayer: function (event, answer, layer) {
      var vm = this;
      layer.answers.forEach(function (layer) {
        layer.grade = 0;
      });
      layer.saved = false;
      answer.grade = event.target.value ? 1 : 0;
    },
    detectKeyDown: function (e) {
      var vm = this;
      if (e.keyCode === 32 && e.target == document.body) {
        e.preventDefault();
      }

      if (e.key === "Alt") {
        vm.timeLayerBinding = true;
      }
    },
    detectKeyUp: function (e) {
      var vm = this;
      if (e.key === "Alt") {
        vm.timeLayerBinding = false;
      }
      if (
        e.code === "Space" &&
        !e.target.isContentEditable &&
        !e.target.id.startsWith("myplayer")
      ) {
        if (vm.player.paused()) {
          vm.player.play();
        } else {
          vm.player.pause();
        }
      }
      if (
        e.code === "ArrowLeft" &&
        !e.target.isContentEditable &&
        !e.target.id.startsWith("myplayer")
      ) {
        if (e.ctrlKey) {
          vm.player.currentTime(vm.player.currentTime() - 60);
        }
        if (e.altKey) {
          vm.player.currentTime(vm.player.currentTime() - 30);
        }
        vm.player.currentTime(vm.player.currentTime() - 10);
      }
      if (
        e.code === "ArrowRight" &&
        !e.target.isContentEditable &&
        !e.target.id.startsWith("myplayer")
      ) {
        if (e.ctrlKey) {
          vm.player.currentTime(vm.player.currentTime() + 60);
        }
        if (e.altKey) {
          vm.player.currentTime(vm.player.currentTime() + 30);
        }
        vm.player.currentTime(vm.player.currentTime() + 10);
      }
    },
    getVideoDuration: function () {
      var vm = this;
      vm.player.on("loadedmetadata", function () {
        vm.videoDuration = this.duration();
        vm.ruler = _.range(0, this.duration(), 15);
        vm.videoTimelineRatio = vm.videoDuration / vm.timelineWidth;
      });
    },
    getTimelineWidth: function () {
      var vm = this;
      vm.timelineWidth = document.getElementById("videoLayer").offsetWidth;
    },
    selectLayer: function (layer) {
      var vm = this;
      vm.selectedLayer = layer;
      if (vm.timeLayerBinding) {
        vm.currentTime = layer.time_start;
        vm.player.currentTime(layer.time_start);
      }
    },
    onResize: function (x, y, width, height) {
      var vm = this;
      vm.selectedLayer.length = (width * vm.videoTimelineRatio).toFixed(3);
      vm.selectedLayer.time_end = ((x + width) * vm.videoTimelineRatio).toFixed(
        3
      );
      vm.selectedLayer.saved = false;
      if (vm.timeLayerBinding) {
        vm.currentTime = (x + width) * vm.videoTimelineRatio;
        vm.player.currentTime((x + width) * vm.videoTimelineRatio);
      }
      if (!vm.timeLayerBinding) {
        vm.player.pause();
      }
    },
    onDrag: function (x, y) {
      var vm = this;
      vm.selectedLayer.time_start = (x * vm.videoTimelineRatio).toFixed(3);
      vm.selectedLayer.time_end = (
        x * vm.videoTimelineRatio +
        parseFloat(vm.selectedLayer.length)
      ).toFixed(3);
      vm.selectedLayer.saved = false;
      // nhảy đến thời gian bằng toạ độ x của layer nhân với tỉ lệ (tỉ lệ = độ dài panel / thời lượng video)
      if (vm.timeLayerBinding) {
        vm.currentTime = x * vm.videoTimelineRatio;
        vm.player.currentTime(x * vm.videoTimelineRatio);
      }
      if (!vm.timeLayerBinding) {
        vm.player.pause();
      }
    },
    getTask: function () {
      var vm = this;
      $.post(
        vm.url +
          `/backend/new-lesson/${vm.lessonId}/edit/components/${vm.taskId}`,
        function (res) {
          if (res.code === 200) {
            vm.video = res.video;
            vm.currentMediaName = res.data.video.video_name;
            vm.initVideo();
            vm.getVideoDuration();
          }
        }
      );
    },
    initVideo: function (callback) {
      var vm = this;

      var now = new Date();

      vm.player = videojs(
        "myplayer_" + playerId,
        options,
        function onPlayerReady() {
          console.log("onPlayerReady", this);
          this.hotkeys({
            volumeStep: 0.1,
            seekStep: 10,
            enableMute: true,
            enableFullscreen: true,
            enableNumbers: false,
            enableVolumeScroll: true,
            enableHoverScroll: true,
            alwaysCaptureHotkeys: true,
            // Kết hợp tiến lùi với Ctrl để tiến xa hơn
            seekStep: function (e) {
              if (e.ctrlKey && e.altKey) {
                return 5 * 60;
              } else if (e.shiftKey) {
                return 60;
              } else if (e.altKey) {
                return 30;
              } else {
                return 10;
              }
            },

            // Enhance existing simple hotkey with a complex hotkey
            fullscreenKey: function (e) {
              // fullscreen with the F key or Ctrl+Enter
              return e.which === 70 || (e.ctrlKey && e.which === 13);
            },

            // Custom Keys
            customKeys: {
              // Thêm phím custom đơn
              simpleKey: {
                key: function (e) {
                  // Bắt sự kiện khi ấn S
                  return e.which === 83;
                },
                handler: function (player, options, e) {
                  // Example
                  if (player.paused()) {
                    player.play();
                  } else {
                    player.pause();
                  }
                },
              },

              // Thêm tổ hợp phím custom
              complexKey: {
                key: function (e) {
                  // Bắt sự kiện Ctrl + D để toggle mute
                  return e.ctrlKey && e.which === 68;
                },
                handler: function (player, options, event) {
                  // Example
                  if (options.enableMute) {
                    player.muted(!player.muted());
                  }
                },
              },

              // Sửa lại các phím số để seek theo yêu cầu
              numbersKey: {
                key: function (event) {
                  // Override number keys
                  return (
                    (event.which > 47 && event.which < 59) ||
                    (event.which > 95 && event.which < 106)
                  );
                },
                handler: function (player, options, event) {
                  // Do not handle if enableModifiersForNumbers set to false and keys are Ctrl, Cmd or Alt
                  if (
                    options.enableModifiersForNumbers ||
                    !(event.metaKey || event.ctrlKey || event.altKey)
                  ) {
                    var sub = 48;
                    if (event.which > 95) {
                      sub = 96;
                    }
                    var number = event.which - sub;
                    player.currentTime(player.duration() * number * 0.1);
                  }
                },
              },

              emptyHotkey: {
                // Empty
              },

              withoutKey: {
                handler: function (player, options, event) {
                  console.log("withoutKey handler");
                },
              },

              withoutHandler: {
                key: function (e) {
                  return true;
                },
              },

              malformedKey: {
                key: function () {
                  console.log(
                    "I have a malformed customKey. The Key function must return a boolean."
                  );
                },
                handler: function (player, options, event) {
                  //Empty
                },
              },
            },
          });
          this.on("timeupdate", function () {
            vm.currentTime = vm.player.currentTime();
          });
          this.on("seeking", function () {
            vm.layers.forEach(function (item) {
              item.open_once = false;
              return item;
            });
          });
        }
      );
      //khởi tạo video player
      // if(window.innerWidth <= 768) //nếu là mobile
      //     var src = window.innerWidth <= 768 ? vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/index.m3u8?" + now.getMinutes();
      // else
      //     var src = vm.currentServerUrl + '/'+ vm.currentQuality +'/'+ vm.currentMediaName + "/?" + now.getMinutes();
      var src =
        vm.currentServerUrl +
        "/" +
        vm.currentQuality +
        "/" +
        vm.currentMediaName +
        "/?" +
        now.getMinutes();

      console.log(src);
      vm.player.src({
        src: src,
        type: "application/x-mpegURL",
        withCredentials: false,
      });
      vm.layers.forEach(function (question) {
        vm.initModal(question);
      });

      var markers = vm.layers.map(function (question) {
        return {
          time: question.time_start,
          text: "Câu hỏi",
          class: vm.getQuestionMarkerColor(question),
        };
      });
      vm.player.markers({
        markerStyle: {
          width: "6px",
          "border-radius": "0",
          "background-color": "orange",
        },
        markerTip: {
          display: true,
          text: function (marker) {
            return "Câu hỏi";
          },
        },
        markers: markers,
      });
      vm.player.currentTime(2);
    },
    openModal: function (index) {
      const vm = this;
      vm.modal[index].open();
      vm.layers[index].open_once = true;

      $(".fast-left").css("display", "none");
      $(".fast-right").css("display", "none");
    },
    triggerCloseModal: function () {
      const vm = this;
      vm.modal.forEach(function (item) {
        if (item.opened_) {
          item.close();
          $(".fast-left").css("display", "inline");
          $(".fast-right").css("display", "inline");
        }
      });
    },
    closeModal: function (index) {
      const vm = this;
      if (vm.modal[index].opened_) {
        $(".fast-left").css("display", "inline");
        $(".fast-right").css("display", "inline");
      }
      vm.modal[index].close();
    },

    selectAnswer: function (answer, question) {
      question.answers = question.answers.map(function (item) {
        item.selected = item.id === answer.id ? 1 : 0;
        return item;
      });
      if (question.type === 2) {
        question.time_up = true;
        var selectedAnswer = _.findIndex(question.answers, "selected");
        var correctAnswer = _.findIndex(question.answers, "grade");
        if (selectedAnswer !== correctAnswer) {
          vm.playAudio("sai.wav");
        } else {
          vm.playAudio("dung.mp3");
        }
      }
    },
    playAudio: function (audio) {
      const vm = this;

      vm.mp3 = new Audio(vm.url + "/assets/audio/" + audio);
      vm.mp3.play();
    },
    timeUp: function (question) {
      question.time_up = true;
      var selectedAnswer = _.findIndex(question.answers, "selected");
      var correctAnswer = _.findIndex(question.answers, "grade");
      if (selectedAnswer !== correctAnswer) {
        vm.playAudio("sai.wav");
      } else {
        vm.playAudio("dung.mp3");
      }
    },

    initModal: function (question) {
      const vm = this;
      var ModalDialog = videojs.getComponent("ModalDialog");

      var contentEl = document.createElement("div");
      contentEl.className = "video__js--overlay-quiz";

      var myexample = Vue.extend(videoQuestionContent);
      var component = new myexample({
        propsData: {
          question: question,
          currentTime: vm.currentTime,
        },
      });

      vm.modalComponents.push(component);

      // probably better to just build the entire thing via DOM methods
      contentEl.appendChild(component.$mount().$el);

      var newModal = new ModalDialog(vm.player, {
        name: "modal_" + question.uid,
        content: contentEl,
        // We don't want this modal to go away when it closes.
        temporary: false,
      });

      newModal.closeable(false);

      newModal.removeAttribute("tabindex");
      newModal.addClass("video__js--overlay");
      newModal.addClass(
        question.type === 1
          ? "video__js--overlay-transparent"
          : "video__js--overlay-dark"
      );
      newModal.addClass(
        question.type === 1
          ? "video__js--overlay-half-screen"
          : "video__js--overlay-full-screen"
      );
      newModal.addClass("p-0");

      newModal.on("modalopen", function () {
        if (question.type === 1) {
          vm.player.play();
        } else {
          vm.player.pause();
        }
        question.modal = true;
        vm.modalOpenStatus = true;
      });
      newModal.on("modalclose", function () {
        vm.player.play();
        question.modal = false;
        question.time_up = false;
        // question.answers = question.answers.map(function (answer) {
        //     answer.selected = false;
        //     return answer;
        // })
        vm.modalOpenStatus = false;
      });

      vm.modal.push(newModal);
      vm.player.addChild(newModal);
    },
    updateModalsCurrentTime: function (time) {
      var vm = this;

      vm.modalComponents.forEach(function (component) {
        component.currentTime = time;
      });
    },
  },
});
