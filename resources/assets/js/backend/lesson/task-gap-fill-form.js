$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});
Vue.use(CKEditor);
Vue.component("task-gap-fill-form", {
  template: "#task-gap-fill-form-template",
  props: ["lesson_id", "type", "currentTask"],
  data: function () {
    return {
      url: window.location.origin,
      task: {},
      show: 1,
      value: "",
      grade: 0,
      answers: [],
      editorConfig: {
        minHeight: "300px",
        filebrowserBrowseUrl: this.url + '/backend/ckfinder/browser',
        filebrowserUploadUrl:
          this.url +
          '/backend/ckfinder/connector?command=QuickUpload&type=Files',
        extraPlugins: [
          "maximize",
          "sourcearea",
          "button",
          "panelbutton",
          "fakeobjects",
          "justify",
          "colorbutton",
          "dialogui",
          "dialog",
          "filetools",
          "popup",
          "filebrowser",
          "font",
          "table",
          "image",
          "furigana",
          "panel",
          "listblock",
          "floatpanel",
          "richcombo",
          "format",
        ],
        allowedContent: true,
        enterMode: CKEDITOR.ENTER_BR,
      },
    };
  },
  watch: {
    value: {
      // This will let Vue know to look inside the array
      deep: true,

      // We have to move our method to a handler field
      handler(val) {
        var vm = this;
        vm.checkHide(val);
      },
    },
  },
  mounted: function () {
    var vm = this;
    vm.task = vm.currentTask;
    vm.show = vm.task.hasOwnProperty("show") ? vm.task.show : 1;
    vm.value = vm.task.value ? vm.task.value : "";
    vm.grade = vm.task.grade ? vm.task.grade : "";
    if (vm.currentTask.id) {
      vm.getAnswersByTask();
    }
  },
  methods: {
    checkHide: function (value) {
      const vm = this;
      var regexGap = /\[\[([^[]+)\]\]/g;
      var regexStar = /\[\*([^[]+)\*\]/g;
      var i = 0;
      var answers = [];
      do {
        m = regexGap.exec(value);
        if (m) {
          answers[i] = {
            id: undefined,
            value: m[1],
            sort: m.index,
            grade: 0,
          };
          i++;
        }
      } while (m);
      do {
        n = regexStar.exec(value);
        if (n) {
          answers[i] = {
            id: undefined,
            value: n[1],
            sort: n.index,
            grade: vm.grade,
          };
          i++;
        }
      } while (n);
      answers = _.orderBy(answers, "sort");
      vm.answers = answers.map(function (answer, index) {
        if (vm.answers[index] && vm.answers[index].id) {
          answer.id = vm.answers[index].id;
        } else {
          answer.id = undefined;
        }
        return answer;
      });
    },
    onSaveTask: function () {
      const vm = this;

      var data = {
        id: vm.currentTask.id,
        lesson_id: vm.lesson_id,
        type: vm.type,
        show: vm.show ? 1 : 0,
        value: vm.value,
        grade: parseInt(vm.grade),
        answers: vm.answers,
      };
      var validate = vm.validate();
      if (validate) {
        if (!data.id) {
          vm.onAddNewTask(data);
        } else {
          vm.onUpdateTask(data);
        }
      }
    },
    onAddNewTask: function (data) {
      const vm = this;

      $.post(vm.url + "/backend/new-lesson/gap-fill/add", data, function (res) {
        if (res.code == 200) {
          var payload = res.data;
          payload.type = parseInt(res.data.type);
          payload.show = parseInt(res.data.show);
          vm.$emit("addedTask", res.data);
          toastr.success("Thay đổi thành công!!");
        }
      });
    },
    onUpdateTask: function (data) {
      const vm = this;

      $.ajax({
        url: vm.url + "/backend/new-lesson/gap-fill/update",
        type: "PUT",
        data: data,
        success: function (res) {
          if (res.code == 200) {
            var payload = res.data;
            payload.type = parseInt(res.data.type);
            payload.show = parseInt(res.data.show);
            vm.$emit("updatedTask", payload);
            toastr.success("Thay đổi thành công!!");
          }
        },
      });
    },
    cancel: function () {
      const vm = this;
      vm.$emit("closeModal");
    },
    validate: function () {
      const vm = this;
      if (_.isNil(vm.value) || vm.value == "") {
        toastr.error("Không bỏ trống nội dung câu hỏi");
        return false;
      }
      if (_.isNil(vm.grade) || vm.grade == 0) {
        toastr.error("Điểm của bài quiz phải lớn hơn 0");
        return false;
      }
      return true;
    },
    getAnswersByTask: function () {
      const vm = this;
      var id = vm.currentTask.id;

      $.post(
        vm.url + "/backend/new-lesson/quiz/get-answers-by-task",
        { id: id },
        function (res) {
          if (res.code == 200) {
            vm.answers = res.data.map(function (item) {
              return {
                id: item.id,
                sort: item.sort,
                value: item.value,
                grade: item.grade,
              };
            });
          }
        }
      );
    },
  },
});
