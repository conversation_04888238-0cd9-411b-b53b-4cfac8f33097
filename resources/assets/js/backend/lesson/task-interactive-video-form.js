$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});
Vue.use(CKEditor);
Vue.component("task-interactive-video-form", {
  template: "#task-interactive-video-form-template",
  props: ["lesson_id", "type", "currentTask"],
  data: function () {
    return {
      url: window.location.origin,
      task: {},
      show: 0,
      video_title: "",
      video_name: "",
      editorConfig: {
        minHeight: "300px",
        filebrowserBrowseUrl: this.url + '/backend/ckfinder/browser',
        filebrowserUploadUrl:
          this.url +
          '/backend/ckfinder/connector?command=QuickUpload&type=Files',
        extraPlugins: [
          "custom",
          "maximize",
          "sourcearea",
          "button",
          "panelbutton",
          "fakeobjects",
          "justify",
          "colorbutton",
          "dialogui",
          "dialog",
          "filetools",
          "popup",
          "filebrowser",
          "font",
          "table",
          "image",
          "furigana",
          "panel",
          "listblock",
          "floatpanel",
          "richcombo",
          "format",
        ],
        allowedContent: true,
      },
    };
  },
  mounted: function () {
    var vm = this;
    vm.task = vm.currentTask;
    vm.show = vm.task.show ? vm.task.show : 0;
    vm.video_title = vm.task.video_title ? vm.task.video_title : "";
    vm.video_name = vm.task.video_name ? vm.task.video_name : "";
  },
  methods: {
    onSaveTask: function () {
      const vm = this;

      var data = {
        id: vm.currentTask.id,
        lesson_id: vm.lesson_id,
        type: vm.type,
        show: vm.show ? 1 : 0,
        video_name: vm.video_name,
        video_title: vm.video_title,
      };
      if (!data.id) {
        vm.onAddNewTask(data);
      } else {
        vm.onUpdateTask(data);
      }
    },
    onAddNewTask: function (data) {
      const vm = this;

      $.post(
        vm.url + "/backend/new-lesson/interactive-video/add",
        data,
        function (res) {
          if (res.code == 200) {
            vm.$emit("addedTask", res.data);
            toastr.success("Thay đổi thành công!!");
          }
        }
      );
    },
    onUpdateTask: function (data) {
      const vm = this;

      $.ajax({
        url: vm.url + "/backend/new-lesson/interactive-video/update",
        type: "PUT",
        data: data,
        success: function (res) {
          if (res.code == 200) {
            vm.$emit("updatedTask", res.data);
            toastr.success("Thay đổi thành công!!");
          }
        },
      });
    },
    cancel: function () {
      const vm = this;
      vm.$emit("closeModal");
    },
  },
});
