var newLesson = new Vue({
  el: "#new-lesson-detail-screen",
  data: function () {
    return {
      url: window.location.origin,
      activeTab: "tasks",
      tabs: [
        {
          value: "info",
          name: "Thông tin",
        },
        {
          value: "tasks",
          name: "Nội dung",
        },
      ],
      lessonId: 0,
    };
  },
  methods: {
    convertToQuiz: function () {
      const vm = this;
      var data = {
        lessonId: vm.lessonId,
      };
      $.post(
        vm.url + "/backend/new-lesson/convert-to-quiz",
        data,
        function (res) {
          if (res.code === 200) {
            alert("Chuyển đổi thành công. F5 để thấy kết quả");
          }
        }
      );
    },
  },
  mounted: function () {
    const vm = this;
    vm.lessonId = lesson.id;
  },
});
