// register modal component
Vue.component('backend-modal', {
    template: '#modal-template',
    props: {
        bgColor: {
            type: String,
            default: function() {
                return 'background-color: #fff;';
            },
        },
        shadow: {
            type: String,
            default: function() {
                return 'box-shadow: 0 2px 8px rgba(0, 0, 0, .33);';
            },
        }
    },
    data() {
      return {
          containerStyle: '' + this.bgColor + this.shadow
      }
    },
});
