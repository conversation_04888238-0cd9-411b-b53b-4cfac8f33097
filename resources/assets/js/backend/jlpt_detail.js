Vue.filter("lessonType", function (value) {
  switch (+value) {
    case 1:
      return "Từ vựng - <PERSON><PERSON> - <PERSON> pháp";
    case 2:
      return "<PERSON>ọc hiểu";
    case 3:
      return "<PERSON>he hiểu";
    default:
      return "<PERSON>hông xác định";
  }
});
Vue.filter("questionSkill", function (value) {
  switch (+value) {
    case 1:
      return "Từ vựng";
    case 2:
      return "Chữ Hán";
    case 3:
      return "Ngữ pháp";
    case 4:
      return "Đọc hiểu";
    case 5:
      return "Nghe hiểu";
    default:
      return "Không skill";
  }
});

import axios from "axios";


let api = axios.create({
  baseURL: "/backend",
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const { status } = error.response || {};
    if (status >= 500) {
    }
    if (status === 401) {
      // 401 Unauthorized
      window.location.href = `${process.env.APP_URL}/backend/dang-nhap`;
    }

    return Promise.reject(error);
  }
);

var vm = new Vue({
  el: "#exam-detail-lesson",

  data: {
    exam: {},
    types: {
      1: "Từ vựng - Ch<PERSON> hán - <PERSON>ữ pháp",
      2: "Đọc hiểu",
      3: "<PERSON>he hiểu",
    },
    N1Types: {
      1: "Từ vựng - Chữ hán - Ngữ pháp",
      2: "Đọc hiểu",
      3: "Nghe hiểu",
    },
    active: 0,
    lesson: {
      questions: [],
      exams: [],
    },
    points: {
      1: "",
      2: "",
      3: "",
    },
    question: {
      position: "",
      point: "",
    },
    splitTime: false,
    timeSplit: [30, 30, 30],
    answers: [],
    answersCount: 2,
    picked: "",
    minimum_point: 19,
    questionUrl: "",
    is_content: false,
    isMp3SrcValid: false,
    mp3Name: "",
    cloneModal: false,
    cloneTargetLessonId: undefined,
    questionToCloneId: {},
    showedLessons: [],
    showExplain: false,
    skill: "",
    skillTypes: {
      1: "Từ vựng",
      2: "Chữ Hán",
      3: "Ngữ pháp",
      4: "Đọc hiểu",
      5: "Nghe hiểu",
    },
  },

  computed: {
    lessons() {
      return this.exam.lessons;
    },
    mp3Lesson() {
      return _.find(this.exam.lessons, function (o) {
        return o.type == 3;
      });
    },
    level() {
      return this.exam.course;
    },
  },
  watch: {
    showedLessons(value, oldValue) {
      if (value.length > 0 && oldValue.length > 0) {
        this.toggleLesson(value);
      }
    },
    splitTime(value) {
      this.timeSplit = value
        ? this.exam.time_split
          ? this.exam.time_split
          : [30, 30, 30]
        : null;
      if (!value) this.saveLessonTime();
    },
    "exam.time_split"(value) {
      this.timeSplit = value;
      this.splitTime = value ? true : false;
    },
  },
  mounted() {
    vm = this;

    $.get(window.location.href + "/lessons", (response) => {
      vm.exam = response;
      const tmp = [];
      vm.exam.lessons.forEach((o) => {
        if (o.pivot.status == 1) {
          tmp.push(o.id);
        }
      });
      vm.showedLessons = tmp;
      vm.exam.lessons.forEach((lesson) => {
        if (lesson.type == 1) {
          vm.getLesson(lesson);
        }
        if (lesson.type == 3) {
          vm.mp3Name = lesson.mp3;
          setTimeout(function () {
            document
              .getElementById("audioSource")
              .setAttribute("src", "https://mp3-v2.dungmori.com/" + lesson.mp3);
          }, 300);
        }
      });
    });

    var questionEditor = CKEDITOR.replace("question", {
      filebrowserBrowseUrl: "/backend/ckfinder/browser",
      filebrowserUploadUrl:
        "/backend/ckfinder/connector?command=QuickUpload&type=Files",
      extraPlugins:
        "maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",
      allowedContent: true,
    });
    var explainEditor = CKEDITOR.replace("explain", {
      filebrowserBrowseUrl: "/backend/ckfinder/browser",
      filebrowserUploadUrl:
        "/backend/ckfinder/connector?command=QuickUpload&type=Files",
      extraPlugins:
        "maximize,sourcearea,button,panelbutton,fakeobjects,justify,colorbutton,dialogui,dialog,flash,filetools,popup,filebrowser,font,table,image,furigana,panel,listblock,floatpanel,richcombo,format,resize,lineheight",
      allowedContent: true,
    });
    $("#addQuestion").on("hide.bs.modal", () => {
      this.resetAddQuestionForm();
    });
    $(document).on("click", ".reloadCKeditor", function () {
      $(this).ckeditor(function () {}, {
        on: {
          blur: function () {
            this.destroy();
          },
        },
        extraPlugins: "furigana,colorbutton",
      });
    });
    if ($.fn.modal) {
      $.fn.modal.Constructor.prototype.enforceFocus = function() {
          let modal_this = this
          $(document).on('focusin.modal', function (e) {
              if (modal_this.$element[0] !== e.target && !modal_this.$element.has(e.target).length
                  && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_select')
                  && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_textarea')
                  && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_text')) {
                  modal_this.$element.focus()
              }
          })
      };
    }
  },

  methods: {
    fixFurigana() {
      if ($.fn.modal) {
        $.fn.modal.Constructor.prototype.enforceFocus = function() {
            let modal_this = this
            $(document).on('focusin.modal', function (e) {
                if (modal_this.$element[0] !== e.target && !modal_this.$element.has(e.target).length
                    && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_select')
                    && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_textarea')
                    && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_text')) {
                    modal_this.$element.focus()
                }
            })
        };
      }
    },
    getLessonName(lesson, exam) {
      switch (exam.type) {
        case 1:
          if (["N1", "N2", "N3"].includes(exam.course)) {
            switch (+lesson.type) {
              case 1:
                return "Từ vựng - Chữ Hán - Ngữ pháp";
              case 2:
                return "Đọc hiểu";
              case 3:
                return "Nghe hiểu";
              default:
                return "Không xác định";
            }
          } else {
            switch (+lesson.type) {
              case 1:
                return "Từ vựng - Chữ Hán - Ngữ pháp - Đọc hiểu";
              case 2:
                return "--";
              case 3:
                return "Nghe hiểu";
              default:
                return "Không xác định";
            }
          }

        case 2:
          switch (+lesson.type) {
            case 1:
              return "Từ vựng - Chữ Hán";
            case 2:
              return "Ngữ pháp - Đọc hiểu";
            case 3:
              return "Nghe hiểu";
            default:
              return "Không xác định";
          }
        default:
          break;
      }
    },
    setLessonTime(value, index) {
      if (value < 1) value = 1;
      // if (value > 100) value = 100;
      this.$set(this.timeSplit, index, Number(value));
    },
    saveLessonTime() {
      const vm = this;
      const xhr = $.post(
        window.location.origin + "/backend/thi-thu/exam/set-lesson-time",
        { time_split: this.timeSplit, exam_id: this.exam.id },
        function (response) {
          // console.log(response.data);
          if (response.code == 200) {
            vm.$set(vm.exam, "time_split", response.data.time_split);
            toastr.success("Thay đổi thành công!!");
          }
        }
      );
    },
    getLesson(lesson, autoHandle = true) {
      var lessonXhr = $.get(
        window.location.origin +
          "/backend/thi-thu/" +
          this.exam.id +
          "/lesson/" +
          lesson.id +
          "/detail"
      );
      if (autoHandle === true) {
        lessonXhr.done((response) => {
          this.lesson = response;
        });
      }
      return lessonXhr;
    },

    toggleLesson(lessons) {
      const data = lessons;
      const xhr = $.post(
        window.location.origin + "/backend/thi-thu/toggle-lesson",
        { exam_id: this.exam.id, lessons: lessons },
        function (response) {
          // console.log(response.data);
          window.location.reload();
        }
      );
    },
    changeTab(lesson, index) {
      this.active = index;
      this.getLesson(lesson);
    },

    addAnswer() {
      this.answers.push({ content: "" });
    },

    deleteAnswer() {
      var answer = this.answers.pop();
      if (answer.is_true === 1) {
        this.picked = "";
      }
    },

    addQuestion() {
      var answersCount = this.answers.length;
      for (var i = 0; i < answersCount; i++) {
        this.answers[i].position = i + 1;
        if (this.picked !== i) {
          this.answers[i].is_true = 0;
        } else {
          this.answers[i].is_true = 1;
        }
      }
      var data = {
        content: _.replace(
          CKEDITOR.instances.question.getData(),
          '<style type="text/css"><!--td {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}--></style>',
          ""
        ),
        explain: _.replace(
          CKEDITOR.instances.explain.getData(),
          '<style type="text/css"><!--td {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}--></style>',
          ""
        ),
        position: this.question.position,
        picked: this.picked,
        is_content: this.is_content ? 1 : 0,
        skill: this.skill,
        level: this.level,
      };
      if (!this.is_content) {
        data.answers = this.answers;
        data.point = this.question.point;
      }
      $.post(this.questionUrl, data)
        .done(() => {
          this.getLesson(this.lesson, false).done((response) => {
            this.lesson = response;
            this.exam.lessons[this.lesson.type - 1] = {
              ...this.exam.lessons[this.lesson.type - 1],
              exams: this.lesson.exams,
              questions: this.lesson.questions,
            };
            $("#addQuestion").modal("hide");
            this.resetAddQuestionForm();
          });
        })
        .fail(() => {
          this.notifyError("Bạn chưa nhập đủ thông tin!");
        });
    },

    updateText(event, key) {
      this.answers[key].content = event.target.innerHTML;
    },

    updateAnswerPosition(evt) {
      // Neu vi tri cua phan tu bi drag dang la dap an dung
      if (this.picked === evt.oldIndex) {
        this.picked = evt.newIndex;
      } else if (this.picked === evt.newIndex) {
        // Drag phan tu vao phan tu dang la dap an dung
        if (evt.oldIndex < this.picked) {
          this.picked -= 1;
        } else {
          this.picked += 1;
        }
      } else if (evt.newIndex > this.picked && evt.oldIndex < this.picked) {
        this.picked -= 1;
      } else if (evt.newIndex < this.picked && evt.oldIndex > this.picked) {
        this.picked += 1;
      }
      this.setAnswersContent();
    },

    updateQuestionsPosition() {
      var data = [];
      var questionsCount = this.lesson.questions.length;
      for (var i = 0; i < questionsCount; i++) {
        data.push({
          id: this.lesson.questions[i].id,
          position: i + 1,
        });
      }
      $.ajax({
        url:
          window.location.origin +
          "/backend/thi-thu/lesson/" +
          this.lesson.id +
          "/questions/sort",
        type: "POST",
        data: JSON.stringify(data),
        contentType: "application/json",
      }).done(() => {
        this.getLesson(this.lesson);
      });
    },

    calculatePoint(questions, index) {
      this.points[index] = questions.reduce(
        (accumulator, currentValue) => accumulator + currentValue.point,
        0
      );
      return this.points[index];
    },

    countQuestions(lessons) {
      return lessons.filter((lesson) => {
        return !lesson.is_content;
      }).length;
    },

    resetAddQuestionForm() {
      CKEDITOR.instances.question.setData("");
      CKEDITOR.instances.explain.setData("");
      this.question.point = "";
      this.question.position = "";
      this.answers = [];
      this.picked = "";
      this.is_content = false;
      this.skill = "";
      this.level = "";
    },

    setUrlAddQuestion() {
      this.questionUrl =
        window.location.origin +
        "/backend/thi-thu/lesson/" +
        this.lesson.id +
        "/question";
      for (var i = 0; i < this.answersCount; i++) {
        this.answers.push({ content: "" });
      }
    },

    setUrlEditQuestion(question) {
      this.question = JSON.parse(JSON.stringify(question));
      console.log(question);
      CKEDITOR.instances.question.setData(this.question.content);
      CKEDITOR.instances.explain.setData(this.question.explain);
      this.is_content = question.is_content;
      this.skill = question.skill;
      this.level = question.level;
      this.answers = this.question.answers;
      this.answers.forEach((answer, index) => {
        if (answer.is_true === 1) {
          this.picked = index;
        }
      });
      setTimeout(() => {
        this.setAnswersContent();
      }, 0);
      this.questionUrl =
        window.location.origin +
        "/backend/thi-thu/lesson/questions/" +
        this.question.id +
        "/edit";
      $("#addQuestion").modal("show");
      this.fixFurigana();
    },

    setAnswersContent() {
      $(".reloadCKeditor").each(function (index) {
        $(this).html(vm.answers[index].content);
      });
    },

    deleteQuestion(question) {
      bootbox.confirm({
        message: "Bạn có chắc chắn muốn xoá không?",
        callback: (result) => {
          if (result) {
            $.ajax({
              url:
                window.location.origin +
                "/backend/thi-thu/lesson/questions/" +
                question.id +
                "/delete",
              type: "delete",
              success: () => {
                this.lesson.questions = this.lesson.questions.filter(
                  (questionLesson) => {
                    return questionLesson.id !== question.id;
                  }
                );
                this.updateQuestionsPosition();
              },
            });
          }
        },
      });
    },

    cloneThisQuestion(question) {
      let vm = this;

      vm.cloneModal = true;
      vm.questionToCloneId = question.id;
    },

    cloneThisQuestionToLesson() {
      let vm = this;
      let data = {
        targetLessonId: vm.cloneTargetLessonId,
      };
      $.post(
        window.location.origin +
          "/backend/thi-thu/lesson/questions/" +
          vm.questionToCloneId +
          "/clone",
        data,
        function (res) {
          if (res.code === 200) {
            toastr.success("Thay đổi thành công!!");
            vm.closeCloneModal();
          } else {
            toastr.error(res.msg);
          }
        }
      );
    },

    closeCloneModal() {
      let vm = this;

      vm.questionToCloneId = undefined;
      vm.cloneTargetLessonId = undefined;
      vm.cloneModal = false;
    },

    updateMp3() {
      vm = this;
      if (!this.mp3Lesson) return;
      $.ajax({
        url:
          window.location.origin +
          "/backend/thi-thu/lesson/" +
          this.mp3Lesson.id +
          "/mp3",
        type: "patch",
        data: {
          mp3: this.mp3Lesson.mp3,
        },
        success: () => {
          document
            .getElementById("audioSource")
            .setAttribute(
              "src",
              "https://mp3-v2.dungmori.com/" + vm.mp3Lesson.mp3
            );
          vm.mp3Name = vm.mp3Lesson.mp3;
          $("#linkMp3").modal("hide");
        },
      }).fail((error) =>
        this.notifyError(Object.values(error.responseJSON)[0][0])
      );
    },

    checkMp3Src() {
      if (!this.mp3Lesson) return;
      var audio = new Audio();
      audio.src = "https://mp3-v2.dungmori.com/" + this.mp3Lesson.mp3;
      audio.muted = true;
      audio
        .play()
        .then(() => {
          this.isMp3SrcValid = true;
          audio.pause();
        })
        .catch(() => {
          this.isMp3SrcValid = false;
          audio.pause();
          this.notifyError("Link mp3 không chính xác!");
        });
    },

    notifyError(message) {
      $.toast({
        heading: "Lỗi",
        text: message,
        showHideTransition: "fade",
        icon: "error",
        position: "bottom-right",
      });
    },
    chooseFileAudio() {
      document.getElementById("audio_file").click();
    },
    async handleFileAudioChange(event) {
      const file = event.target.files[0];
      if (file) {
        const formData = new FormData();
        formData.append("file_upload", file);

        const token = await api.get("/video/api/get-token").then((response) => {
          return response.data;
        });

        formData.append("token", token);

        const path = await axios
          .post(`/api/admin/upload-audio`, formData, {
            withCredentials: false,
            baseURL: videoBaseURL,
            headers: {
              "Content-Type": "multipart/form-data",
            },
          })
          .then((response) => {
            return response.data.data.file_path;
          })
          .catch((error) => console.error(error));

        this.mp3Lesson.mp3 = path;
        this.updateMp3();
      }
    },
    async importTest(event) {
      const file = event.target.files[0];
      if (!file) {
          alert("Vui lòng chọn một tệp để tải lên.");
          return;
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

      try {
          
          // Replace with your API endpoint
          const response = await api.post('/thi-thu/exam/import-test', formData);

          console.log('Response:', response);

          if (response.status != 200) {
              throw new Error(`HTTP error! status: ${response.status}`);
          }

          alert('Tải lên thành công! Xin hãy chờ một lúc rồi kiểm tra lại');
      } catch (error) {
          console.error('Error:', error);
      }
    }
  },
});
