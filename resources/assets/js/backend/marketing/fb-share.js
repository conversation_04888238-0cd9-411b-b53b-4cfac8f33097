$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  }
});

const router = new VueRouter({
  mode: 'history'
});

Vue.filter('dateTimeToMinute', function (value) {
  return moment(value).format('HH:mm DD/MM/YYYY');
});
Vue.filter('printStatus', function (value) {
  return value ? 'Đã in' : 'Chưa in'
});
Vue.filter('printPassedStatus', function (value) {
  return value ? 'Đỗ' : 'Trượt'
});
var filterable__list = new Vue({
  el: '#exam__results--screen',
  components: {
    paginate: VuejsPaginate
  },
  data: function () {
    return {
      url: window.location.origin,
      results: [],
      total_result: 0,
      loading: false,
      showNoteModal: false,
      editingComment: {},
      currentResultImage: '',
      filter: {
        id: undefined,
        user: undefined,
        admin: undefined,
        link: undefined,
        status: 0,
        time_type: 'created_at',
        time_from: undefined,
        time_to: undefined,
        page: 1,
        per_page: 20,
        total_page: 10
      }
    }
  },
  methods: {
    checkDuplicateLink: function(result) {
      var vm = this;

      vm.filter.link = result.link;
      vm.applyFilter();
    },
    closeModal: function (result) {
      var vm = this;

      vm.editingComment = {};
      vm.showNoteModal = false;
    },
    editComment: function (result) {
      var vm = this;

      vm.editingComment = {...result};
      vm.showNoteModal = true;
    },

    saveComment: function () {
      var vm = this;

      var data = {
        resultId: vm.editingComment.id,
        comment: vm.editingComment.comment
      };
      $.post(vm.url + '/backend/marketing/share-for-voucher/update-comment', data, function (res) {
        if (res.code === 200) {
          vm.results = vm.results.map(function (result) {
            if (result.id === res.data.id) {
              result.comment = res.data.comment;
            }
            return result;
          });
          vm.closeModal();

        }
      })
    },
    // thay đổi filter theo datetime
    onChangeDatetime: function (event) {
      var vm = this;
      vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm');
    },
    // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
    resetFilter: function () {
      var vm = this;
      vm.filter = {
        id: undefined,
        user: undefined,
        admin: undefined,
        status: 0,
        time_type: 'created_at',
        time_from: undefined,
        time_to: undefined,
        link: undefined,
        page: 1,
        per_page: 20,
        total_page: 10
      };

      this.applyFilter();
    },
    // gọi hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
    changePage: function (pageNum) {
      var vm = this;
      vm.filter.page = pageNum;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page']);
      vm.getShares(filter);

      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
    setFilterByUrl: function () {
      var vm = this;
      var filterByUrl = $.deparam.querystring();
      _.forEach(filterByUrl, function (value, key) {
        vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
      });
    },
    // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
    applyFilter: function () {
      var vm = this;

      vm.filter.page = 1;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page', 'page']);
      vm.getShares(filter);

      // console.log(filter)
      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // call api lấy danh sách invoices
    // truyền vào filter
    // set các biến trạng thái tmp để có thể dùng v-if, v-model
    getShares: function (filter) {
      var vm = this;

      vm.loading = true;
      setTimeout(function () {
        $.post(vm.url + '/backend/marketing/share-for-voucher/list', filter, function (res) {
          if (res.code == 200) {
            vm.results = res.data.results.map(function (result) {
              result.previewImage = null;
              return result;
            });
            vm.filter.total_page = res.data.total_page;
            vm.total_result = res.data.total_result;
          } else {
            alert('Có lỗi! Liên hệ dev!!!');
          }
          vm.showPromoteProcess = false;
          vm.loading = false;
        });
      }, 200);
    },
    initDatePicker: function () {
      // hô biến input thành datepicker xịn xò
      $('#time_from').datetimepicker({
        language: 'vi'
      }).on('dp.change', function (event) {
        filterable__list.onChangeDatetime(event);
      });
      $('#time_to').datetimepicker({
        language: 'vi'
      }).on('dp.change', function (event) {
        filterable__list.onChangeDatetime(event);
      });
    },

    reConfirmLink: function (review, days) {
      var vm = this;

      var confirm = window.confirm('Yêu cầu này có đánh dấu sai phạm. Xác nhận duyệt?')

      if (confirm) {
        var data = {
          review_id: review.id,
          user_id: review.user_id,
          days: days
        };

        $.post(vm.url + '/backend/marketing/share-for-voucher/re-confirm-link', data, function (res) {
          if (res.code === 200) {
            vm.results = vm.results.map(function (result) {
              if (result.id === res.data.id) {
                result.status = res.data.status;
                result.active_time = res.data.active_time;
                result.comment = res.data.comment;
              }
              return result;
            })
          }
          if (res.code === 409) {
            alert(res.msg)
          }
          if (res.code === 422) {
            var confirm = window.confirm(res.msg);
            if (confirm) {
              vm.forceConfirmLink(review)
            }
          }
        });
      }
    },

    confirmLink: function (review, days) {
      var vm = this;

      var data = {
        review_id: review.id,
        user_id: review.user_id,
        days: days
      };

      $.post(vm.url + '/backend/marketing/share-for-voucher/confirm-link', data, function (res) {
        if (res.code === 200) {
          vm.results = vm.results.map(function (result) {
            if (result.id === res.data.id) {
              result.status = res.data.status;
              result.active_time = res.data.active_time;
              result.comment = res.data.comment;
            }
            return result;
          })
        }
        if (res.code === 409) {
          alert(res.msg)
        }
        if (res.code === 422) {
          var confirm = window.confirm(res.msg);
          if (confirm) {
            vm.forceConfirmLink(review)
          }
        }
      });
    },

    forceConfirmLink: function (review, days) {
      var vm = this;

      var data = {
        review_id: review.id,
        user_id: review.user_id,
        days: days
      };

      $.post(vm.url + '/backend/marketing/share-for-voucher/force-confirm-link', data, function (res) {
        if (res.code === 200) {
          vm.results = vm.results.map(function (result) {
            if (result.id === res.data.id) {
              result.status = res.data.status;
              result.active_time = res.data.active_time;
              result.comment = res.data.comment;
            }
            return result;
          })
        }
        if (res.code === 409) {
          alert(res.msg)
        }
        if (res.code === 422) {
          alert(res.msg)
        }
      });
    },

    rejectLink: function (review) {
      var vm = this;

      var data = {
        review_id: review.id,
        user_id: review.user_id
      };

      $.post(vm.url + '/backend/marketing/share-for-voucher/reject-link', data, function (res) {
        if (res.code === 200) {
          vm.results = vm.results.map(function (result) {
            if (result.id === res.data.id) {
              result.status = res.data.status
            }
            return result;
          })
        }
        if (res.code === 409) {
          alert(res.msg)
        }
        if (res.code === 422) {
          alert(res.msg)
        }
      });
    },
    // fake thao tác click vào input hidden có id theo invoiceId
    editResultImage: function (resultId) {
      $("#inputResultImage" + resultId).click();
    },
    // convert ảnh theo base64
    readURL: function (resultId, input) {
      var vm = this;
      if (input.target.files && input.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
          vm.results = vm.results.map(function (result) {
            if (result.id == resultId) {
              result.previewImage = e.target.result;
            }
            return result;
          });
        };
        reader.readAsDataURL(input.target.files[0]);
      }
    },
    // apply thay đổi ảnh bill
    saveResultImage: function (resultId, previewImage) {
      vm = this;
      var data = new FormData($("#result-image-form-" + resultId)[0]);
      $.ajax({
        url: window.location.origin + '/backend/marketing/share-for-voucher/save-image',
        type: 'post', processData: false, contentType: false, data: data,
        success: function (res) {
          if (res.code == 200) {
            vm.results = vm.results.map(function (result) {
              if (result.id == res.data.id) {
                result.previewImage = null;
                result.image = res.data.image;
              }
              return result;
            });
          } else {
            alert(res.msg);
          }
        }
      });
    },
    sendMessage: function (content, conversationId, creatorId, adminId) {
      vm = this;
      // Emit socket
      var messageSocket = {
        content: content,
        conversationId: conversationId,
        receiverId: creatorId,
        senderId: adminId,
        senderType: 'admin',
        senderName: 'Dũng Mori',
        sentId:
          Date.now() +
          '_' +
          adminId +
          '_' +
          Math.floor(Math.random() * 100) +
          '' +
          (Math.floor(Math.random() * 100) + 100),
        type: 'text'
      };
      vm.socket.emit('send', messageSocket);
    },
    //tạo cuộc hội thoại với user chưa có
    initConversation: function (userId) {
      $.ajax({
        type: 'post',
        url: '/backend/user/create-conversation',
        data: {
          'id': userId
        },
        success: function (response) {
          // console.log("Tạo hội thoại mới", response);
          $(".fa-comments-" + userId).css('color', '#00ab2e');
          window.open(window.location.origin + "/backend/chat#" + response, "_blank");
        }
      });
    },
  },
  mounted: function () {
    var vm = this;

    // Connect socket
    vm.socket = io.connect('https://chat.dungmori.com');

    vm.setFilterByUrl();

    var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
      return value !== undefined && value !== null && value !== '';
    }), ['total_page']);
    this.getShares(filter);

    vm.initDatePicker();
  }
})