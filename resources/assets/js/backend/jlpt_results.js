$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

const router = new VueRouter({
    mode: 'history'
});

Vue.filter('dateTimeToMinute', function (value) {
    return moment(value).format('HH:mm DD/MM/YYYY');
});
Vue.filter('printStatus', function (value) {
    return value ? 'Đã in' : 'Chưa in'
});
Vue.filter('printPassedStatus', function (value) {
    return value ? 'Đỗ' : 'Trượt'
});
Vue.filter('address', function (id) {
    let province = _.find(exam__results.provinces, {"id": parseInt(id)})
    return province ? province.name : '--';
});
Vue.filter('career', function (id) {
    let career = _.find(exam__results.careers, {"id": parseInt(id)})
    return career ? career.title : '--';
});
var exam__results = new Vue({
    el: '#exam__results--screen',
    components: {
        paginate: VuejsPaginate
    },
    data() {
        return {
            url: window.location.origin,
            certificate_url: jlptUrl + '/certificate/',
            results: [],
            loading: false,
            socket: null,
            provinces: [],
            careers: [],
            filter: {
                id: undefined,
                user_id: undefined,
                time_from: undefined,
                time_to: undefined,
                score_from: undefined,
                score_to: undefined,
                course: "",
                province: "",
                mobile: "",
                career: "",
                is_passed: "",
                certificate_info: "",
                platform: "",
                flag: "",
                orderBy: "",
                sort: "",
                // is_printed: 0,
                page: 1,
                per_page: 20,
                total_page: 10
            },
            messageContent: "",
            messageContentError: undefined,
            total_result: 0,
            syncingPass: false,
            syncingPlatform: false,
            promoteSuccess: 0,
            totalPromote: 0,
            showPromoteProcess: false,
            datacollection: null
        };
    },

    watch: {
        filter: {
            handler: function (val, oldVal) {
                var vm = this;
            },
            deep: true
        }
    },
    methods: {
        sortTotalScore: function () {
            var vm = this;
            switch (vm.filter.orderBy) {
                case "":
                    vm.filter.orderBy = "total_score";
                    break;
                case "total_score":
                    if (vm.filter.sort === 'desc') {
                        vm.filter.orderBy = "";
                    }
                    break;
                default:
                    vm.filter.orderBy = "";
            }
            switch (vm.filter.sort) {
                case "":
                    vm.filter.sort = "asc";
                    break;
                case "asc":
                    vm.filter.sort = "desc";
                    break;
                case "desc":
                    vm.filter.sort = "";
                    break;
                default:
                    vm.filter.sort = "";
            }
            vm.applyFilter();
        },
        // gọi hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
        changePage: function (pageNum) {
            var vm = this;
            vm.filter.page = pageNum;
            var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
                return value !== undefined && value !== null && value !== '';
            }), ['total_page']);
            vm.getInvoices(filter);

            router.replace(window.location.pathname + "?" + $.param(filter));
        },
        printResult: function(course_name, a, b, c, total){

            if (['N4', 'N5'].includes(course_name)) {
                if(a < 38 || c < 19) return false; //true bằng đỗ
            } else {
                if(a < 19 || b < 19 || c < 19) return false; //true bằng đỗ
            }

            if(course_name == 'N5' && total >= 80){
                return true;
            }else if(course_name == 'N4' && total >= 90){
                return true;
            }else if(course_name == 'N3' && total >= 95){
                return true;
            }else if(course_name == 'N2' && total >= 90){
                return true;
            }else if(course_name == 'N1' && total >= 100){
                return true;
            }

            return false;
        },
        // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
        applyFilter: function () {
            var vm = this;

            vm.filter.page = 1;
            var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
                return value !== undefined && value !== null && value !== '';
            }), ['total_page', 'page']);
            vm.getInvoices(filter);

            // console.log(filter)
            router.replace(window.location.pathname + "?" + $.param(filter));
        },
        onChangeCheckbox: function (param, event) {
            var vm = this;

            vm.filter[param] = event.target.checked ? 1 : 0;
            vm.applyFilter();
        },
        // call api lấy danh sách invoices
        // truyền vào filter
        // set các biến trạng thái tmp để có thể dùng v-if, v-model
        getInvoices: function(filter) {
            var vm = this;

            vm.loading = true;
            setTimeout(function () {
                $.get(window.location.origin +'/backend/thi-thu/jlpt-results', filter, function (res) {
                    if(res.code == 200) {
                        vm.results = res.data.results.map(function (result) {
                            if (result.certificate_info) {
                                result.certificate_info = JSON.parse(result.certificate_info);
                            }
                            var conversation = _.find(res.data.conversations, ['creator_id', result.user_id]);
                            result.conversation_id = conversation ? conversation.id : null;

                            return result;
                        });
                        vm.filter.total_page = res.data.total_page;
                        vm.total_result = res.data.total_result;
                        vm.careers = res.data.careers;

                    } else {
                        alert('Có lỗi! Liên hệ dev!!!');
                    }
                    vm.showPromoteProcess = false;
                    vm.loading = false;
                });
            }, 200);
        },
        // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
        setFilterByUrl: function () {
            var vm = this;
            var filterByUrl = $.deparam.querystring();
            _.forEach(filterByUrl, function (value, key) {
                vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
            });
        },
        // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
        resetFilter: function () {
            var vm = this;
            vm.filter = {
                id: undefined,
                user_id: undefined,
                time_from: undefined,
                time_to: undefined,
                course: "",
                province: "",
                mobile: "",
                career: "",
                certificate_info: "",
                platform: "",
                is_passed: "",
                // is_printed: 0,
                page: 1,
                per_page: 20,
                total_page: 10
            };

            this.applyFilter();
        },
        // thay đổi filter theo datetime
        onChangeDatetime: function (event) {
            var vm = this;
            vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm');
        },
        setPrinted: function (id, event) {
            var vm = this;

            var data = {
                id: id,
                is_printed: event ? event.target.checked ? 1 : 0 : 1
            };
            $.post(window.location.origin + '/backend/thi-thu/jlpt-results/set-printed', data, function (res) {
                if(res.code == 200) {
                    vm.results = vm.results.map(function (item) {
                        if (item.id == res.data.id) {
                            item.is_printed = parseInt(res.data.is_printed);
                        }
                        return item;
                    });
                } else {
                    alert('Có lỗi! Liên hệ dev!!!');
                }
                vm.loading = false;
            });
        },
        setWrong: function (id, event) {
            var vm = this;

            var data = {
                id: id,
                is_wrong: event ? event.target.checked ? 1 : 0 : 1
            };
            $.post(window.location.origin + '/backend/thi-thu/jlpt-results/set-wrong', data, function (res) {
                if(res.code == 200) {
                    vm.results = vm.results.map(function (item) {
                        if (item.id == res.data.id) {
                            item.is_wrong = parseInt(res.data.is_wrong);
                        }
                        return item;
                    });
                } else {
                    alert('Có lỗi! Liên hệ dev!!!');
                }
                vm.loading = false;
            });
        },
        exportExcel: function () {
            var vm = this;

            var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
                return value !== undefined && value !== null && value !== '';
            }), ['total_page']);
            var url = window.location.origin + "/backend/thi-thu/jlpt-results/export" + window.location.search;
            window.open(url);
        },
        sync: function () {
            var vm = this;
            if (!vm.syncingPass) {
                vm.syncingPass = true;
                setTimeout(function () {
                    $.post(vm.url + "/backend/thi-thu/jlpt-results/sync", {}, function (res) {
                        if (res.code == 200) {
                            alert("Đã đồng bộ xong. F5 để cập nhật kết quả");
                            vm.syncingPass = false;
                        }
                    });
                }, 100);
            }
        },
        syncPlatform: function () {
            var vm = this;
            if (!vm.syncingPlatform) {
                vm.syncingPlatform = true;
                setTimeout(function () {
                    $.post(vm.url + "/backend/thi-thu/jlpt-results/sync-platform", {}, function (res) {
                        if (res.code == 200) {
                            vm.syncingPlatform = false;
                            alert("Đã đồng bộ xong. F5 để cập nhật kết quả");
                        }
                    });
                }, 100);
            }
        },
        printAll: function () {
            var vm = this;
            window.open(vm.url + '/backend/thi-thu/jlpt-results/get-ids-for-print' + window.location.search);
        },

        validateMessageContent: function() {
            var vm = this;

            // var filterCheck = vm.filter.course !== "" && vm.filter.is_passed !== "" && vm.filter.platform === 1;
            if (vm.messageContent === "") {
                vm.messageContentError = "Không bỏ trống nội dung";
                return false;
            }
            // if (!filterCheck) {
            //     vm.messageContentError = "Đối tượng được lọc không rõ ràng";
            //     return false;
            // }
            return true;
        },

        getUsersToSendMessage: function (content) {
            var vm = this;
            if(vm.validateMessageContent()) {
                var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
                    return value !== undefined && value !== null && value !== '';
                }), ['total_page']);
                $.post(vm.url + '/backend/thi-thu/jlpt-results/get-users-to-send-message', filter, function (res) {
                    if (res.code == 200) {
                        vm.showPromoteProcess = true;
                        vm.promoteSuccess = 0;
                        vm.totalPromote = res.data.length;
                        $('#messageToAll').modal('hide');
                        vm.processSendMessage(res.data, _.trim(vm.messageContent));
                    }
                });
            }
        },

        processSendMessage: async function (results, content) {
            var vm = this;

            for (let i = 0; i < results.length; i++) {
                let data = {
                    course: results[i].course,
                    user_id: results[i].user_id,
                    result_id: results[i].id,
                    content: content,
                    is_passed: results[i].is_passed,
                    filter: _.pick(vm.filter, ['time_from', 'time_to'])
                };
                await $.post(vm.url + '/backend/thi-thu/jlpt-results/send-promote', data, function (res) {
                    if (res.code == 200) {
                        vm.sendMessage(content, res.data.conversation.id, results[i].user_id, adminId);
                        vm.promoteSuccess++;
                        setTimeout(function () {}, 500)
                    }
                });
            }
        },
        clearMessageContent: function () {
            var vm = this;
            vm.messageContent = "";
        },
        sendMessage: function (content, conversationId, creatorId, adminId) {
            var vm = this;
            // Emit socket
            var messageSocket = {
                content: content,
                conversationId: conversationId,
                receiverId: creatorId,
                senderId: adminId,
                senderType: 'admin',
                senderName: 'Dũng Mori',
                sentId:
                    Date.now() +
                    '_' +
                    adminId +
                    '_' +
                    Math.floor(Math.random() * 100) +
                    '' +
                    (Math.floor(Math.random() * 100) + 100),
                type: 'text'
            };
            vm.socket.emit('send', messageSocket);
        },
        setFlag: function (result, flag) {
            var vm = this;

            var data = {
                id: result.id,
                flag: flag
            };
            $.post(vm.url + '/backend/thi-thu/jlpt-results/checkFlag', data, function (res) {
                if(res.code == 200) {
                    $.post(vm.url + '/backend/thi-thu/jlpt-results/set-flag', data, function (res) {
                        if (res.code == 200) {
                            result.vn = res.data.vn;
                            result.jp = res.data.jp;
                            result.admin_id = res.data.admin_id;
                        }
                    })
                } else {
                    var confirm = window.confirm('Kết quả này đã được set quốc gia. Chắc chắn ghi đè?')
                    if (confirm) {
                        $.post(vm.url + '/backend/thi-thu/jlpt-results/set-flag', data, function (res) {
                            if (res.code == 200) {
                                result.vn = res.data.vn;
                                result.jp = res.data.jp;
                            }
                        })
                    }
                }
            })


        },
        filterFlag: function (flag) {
            var vm = this;
            vm.filter.flag = flag;
            vm.applyFilter();
        },
        //tạo cuộc hội thoại với user chưa có
        initConversation: function(userId) {
            $.ajax({
                type: 'post',
                url: '/backend/user/create-conversation',
                data: {
                    'id': userId
                },
                success: function (response) {
                    // console.log("Tạo hội thoại mới", response);
                    $(".fa-comments-" + userId).css('color', '#00ab2e');
                    window.open(window.location.origin + "/backend/chat#" + response, "_blank");
                }
            });
        },
    },
    mounted: function() {
        var vm = this;

        vm.setFilterByUrl();
        vm.showPromoteProcess = false;
        // Connect socket
        vm.socket = io.connect('https://chat.dungmori.com');

        vm.provinces = [{"id":0,"name": "Nhật Bản"},{"id":1,"name":"Hồ Chí Minh"},{"id":2,"name":"Hà Nội"},{"id":3,"name":"Đà Nẵng"},{"id":4,"name":"Bình Dương"},{"id":5,"name":"Đồng Nai"},{"id":6,"name":"Khánh Hòa"},{"id":7,"name":"Hải Phòng"},{"id":8,"name":"Long An"},{"id":9,"name":"Quảng Nam"},{"id":10,"name":"Bà Rịa Vũng Tàu"},{"id":11,"name":"Đắk Lắk"},{"id":12,"name":"Cần Thơ"},{"id":13,"name":"Bình Thuận"},{"id":14,"name":"Lâm Đồng"},{"id":15,"name":"Thừa Thiên Huế"},{"id":16,"name":"Kiên Giang"},{"id":17,"name":"Bắc Ninh"},{"id":18,"name":"Quảng Ninh"},{"id":19,"name":"Thanh Hóa"},{"id":20,"name":"Nghệ An"},{"id":21,"name":"Hải Dương"},{"id":22,"name":"Gia Lai"},{"id":23,"name":"Bình Phước"},{"id":24,"name":"Hưng Yên"},{"id":25,"name":"Bình Định"},{"id":26,"name":"Tiền Giang"},{"id":27,"name":"Thái Bình"},{"id":28,"name":"Bắc Giang"},{"id":29,"name":"Hòa Bình"},{"id":30,"name":"An Giang"},{"id":31,"name":"Vĩnh Phúc"},{"id":32,"name":"Tây Ninh"},{"id":33,"name":"Thái Nguyên"},{"id":34,"name":"Lào Cai"},{"id":35,"name":"Nam Định"},{"id":36,"name":"Quảng Ngãi"},{"id":37,"name":"Bến Tre"},{"id":38,"name":"Đắk Nông"},{"id":39,"name":"Cà Mau"},{"id":40,"name":"Vĩnh Long"},{"id":41,"name":"Ninh Bình"},{"id":42,"name":"Phú Thọ"},{"id":43,"name":"Ninh Thuận"},{"id":44,"name":"Phú Yên"},{"id":45,"name":"Hà Nam"},{"id":46,"name":"Hà Tĩnh"},{"id":47,"name":"Đồng Tháp"},{"id":48,"name":"Sóc Trăng"},{"id":49,"name":"Kon Tum"},{"id":50,"name":"Quảng Bình"},{"id":51,"name":"Quảng Trị"},{"id":52,"name":"Trà Vinh"},{"id":53,"name":"Hậu Giang"},{"id":54,"name":"Sơn La"},{"id":55,"name":"Bạc Liêu"},{"id":56,"name":"Yên Bái"},{"id":57,"name":"Tuyên Quang"},{"id":58,"name":"Điện Biên"},{"id":59,"name":"Lai Châu"},{"id":60,"name":"Lạng Sơn"},{"id":61,"name":"Hà Giang"},{"id":62,"name":"Bắc Kạn"},{"id":63,"name":"Cao Bằng"},{"id":-1,"name":"Khác"}]

        var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
            return value !== undefined && value !== null && value !== '';
        }), ['total_page']);
        this.getInvoices(filter);
    },
});
