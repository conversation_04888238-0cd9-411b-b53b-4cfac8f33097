$.ajaxSetup({
  headers: {
    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
  }
});

const router = new VueRouter({
  mode: 'history'
});

Vue.filter('dateTimeToMinute', function (value) {
  return moment(value).format('HH:mm DD/MM/YYYY');
});
var user__list = new Vue({
  el: '#user__screen',
  components: {
    paginate: VuejsPaginate
  },
  data() {
    return {
      url: window.location.origin,
      loading: false,
      socket: null,
      results: [],
      editModal: false,
      careers: [],
      provinces: [],
      courses: [],
      syncSuccess: 0,
      totalSync: 0,
      filter: {
        id: undefined,
        course: "",
        is_tester: "",
        watch_expired_day: "",
        completed: "",
        skype: "",
        time_from: undefined,
        time_to: undefined,
        sort: "",
        page: 1,
        per_page: 20,
        total_page: 10
      },
      formData: {
        id: undefined,
        email: undefined,
        username: undefined,
        activation: undefined,
        blocked: undefined,
        password: undefined,
      },
      messageContent: "",
      messageContentError: undefined,
      total_result: 0,
    };
  },

  watch: {
    filter: {
      handler: function (val, oldVal) {
        var vm = this;
      },
      deep: true
    }
  },

  methods: {
    setTester: function (user) {
      var vm = this;
      let data = {
        id: user.id
      }
      $.post(vm.url + '/backend/user/set-tester', data, function (res) {
        if (res.code === 200) {
          user.is_tester = !user.is_tester
        }
      });
    },
    updateUser: function () {
      var vm = this;
      let data = _.pick(vm.formData, ['id','activation','email','blocked','password']);
      $.post(vm.url + '/backend/user/edit', data, function (res) {
        if (res.code === 200) {
          vm.results = vm.results.map(function (user) {
            if (user.id === res.data.id) {
              user.email = res.data.email;
              user.activation = res.data.activation;
              user.blocked = res.data.blocked;
            }
            return user;
          })
        }
        vm.closeEditModal();
      });
    },
    openEditModal: function(user) {
      var vm = this;
      vm.formData = {
        id: user.id,
        email: user.email,
        username: user.username,
        activation: user.activation,
        blocked: user.blocked,
        password: "",
      }
      setTimeout(function () {
        vm.editModal = true;
      },100)
    },
    closeEditModal: function() {
      var vm = this;
      vm.editModal = false;
      vm.formData = {
        id: undefined,
        email: undefined,
        username: undefined,
        activation: undefined,
        blocked: undefined,
        password: undefined,
      };
    },
    sortTotalScore: function () {
      var vm = this;
      switch (vm.filter.orderBy) {
        case "":
          vm.filter.orderBy = "total_score";
          break;
        case "total_score":
          if (vm.filter.sort === 'desc') {
            vm.filter.orderBy = "";
          }
          break;
        default:
          vm.filter.orderBy = "";
      }
      switch (vm.filter.sort) {
        case "":
          vm.filter.sort = "asc";
          break;
        case "asc":
          vm.filter.sort = "desc";
          break;
        case "desc":
          vm.filter.sort = "";
          break;
        default:
          vm.filter.sort = "";
      }
      vm.applyFilter();
    },
    // gọi hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
    changePage: function (pageNum) {
      var vm = this;
      vm.filter.page = pageNum;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page']);
      vm.getList(filter);

      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
    applyFilter: function () {
      var vm = this;

      vm.filter.page = 1;
      var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== '';
      }), ['total_page', 'page']);
      vm.getList(filter);

      // console.log(filter)
      router.replace(window.location.pathname + "?" + $.param(filter));
    },
    onChangeCheckbox: function (param, event) {
      vm = this;

      vm.filter[param] = event.target.checked ? 1 : 0;
      vm.applyFilter();
    },
    onChangeCourse: function () {
      var vm = this;
      if (vm.filter.course == '') {
        vm.filter.skype = '';
        vm.filter.watch_expired_day = '';
        vm.filter.completed = '';
      }
      vm.applyFilter();
    },
    // call api lấy danh sách invoices
    // truyền vào filter
    // set các biến trạng thái tmp để có thể dùng v-if, v-model
    getList: function(filter) {
      var vm = this;

      vm.loading = true;
      setTimeout(function () {
        $.get(window.location.origin +'/backend/user/get-list', filter, function (res) {
          if(res.code === 200) {
            vm.results = res.data.results.map(function (result) {
              result.certificate_info = null;
              return result;
            });
            vm.filter.total_page = res.data.total_page;
            vm.total_result = res.data.total_result;
            let ids = vm.results.map(function (result) {
              return result.id;
            });
            vm.getCertInfoByUserIds(ids);
            vm.getRemainingSkypeByUserIds(ids);
          } else {
            alert('Có lỗi! Liên hệ dev!!!');
          }
          vm.loading = false;
        });
      }, 200);
    },
    getRemainingSkypeByUserIds: function (ids) {
      var vm = this;
      let data = {
        ids: ids
      };
      $.post(window.location.origin +'/backend/user/get-remaining-skype-by-user-ids', data, function (res) {
        if(res.code === 200) {
          vm.careers = res.careers;
          vm.results = vm.results.map(function (result) {
            res.data.user_with_skype.map(function (info) {
              if (result.id === info.id) {
                result.kaiwa_total_booking = info.course_owner[0]?.kaiwa_total_booking;
                result.booking_count = info.booking_count;
              }
            });
            // console.log(result)
            return result;
          })
        } else {
          alert('Có lỗi! Liên hệ dev!!!');
        }
      });
    },
    getCertInfoByUserIds: function (ids) {
      var vm = this;
      let data = {
        ids: ids
      };
      $.post(window.location.origin +'/backend/user/get-info-by-user-ids', data, function (res) {
        if(res.code === 200) {
          vm.careers = res.careers;
          vm.results = vm.results.map(function (result) {
            res.data.map(function (info) {
              if (result.id === info.user_id) {
                result.certificate_info = JSON.parse(info.certificate_info);
              }
            });
            return result;
          })
        } else {
          alert('Có lỗi! Liên hệ dev!!!');
        }
      });
    },
    // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
    setFilterByUrl: function () {
      var vm = this;
      var filterByUrl = $.deparam.querystring();
      _.forEach(filterByUrl, function (value, key) {
        vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
      });
    },
    // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
    resetFilter: function () {
      var vm = this;
      vm.filter = {
        id: undefined,
        course: "",
        is_tester: "",
        watch_expired_day: "",
        completed: "",
        skype: "",
        time_from: undefined,
        time_to: undefined,
        sort: "",
        page: 1,
        per_page: 20,
        total_page: 10
      };

      this.applyFilter();
    },
    // thay đổi filter theo datetime
    onChangeDatetime: function (event) {
      var vm = this;
      vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm');
    },
    sendMessage: function (content, conversationId, creatorId, adminId) {
      vm = this;
      // Emit socket
      var messageSocket = {
        content: content,
        conversationId: conversationId,
        receiverId: creatorId,
        senderId: adminId,
        senderType: 'admin',
        senderName: 'Dũng Mori',
        sentId:
          Date.now() +
          '_' +
          adminId +
          '_' +
          Math.floor(Math.random() * 100) +
          '' +
          (Math.floor(Math.random() * 100) + 100),
        type: 'text'
      };
      vm.socket.emit('send', messageSocket);
    },
    //tạo cuộc hội thoại với user chưa có
    initConversation: function(userId) {
      $.ajax({
        type: 'post',
        url: '/backend/user/create-conversation',
        data: {
          'id': userId
        },
        success: function (response) {
          // console.log("Tạo hội thoại mới", response);
          $(".fa-comments-" + userId).css('color', '#00ab2e');
          window.open(window.location.origin + "/backend/chat#" + response, "_blank");
        }
      });
    },
    getUserToSync: function () {
      const vm = this;
      const data = {
        courseId: this.filter.course,
      }
      vm.syncSuccess = 0;
      $.post(vm.url + '/backend/user/get-user-to-sync', data, function (res) {
        vm.totalSync = res.data.users.length;
        if (res.code === 200) {
          vm.syncProgress(res.data);
        }
      });
    },
    async syncProgress(payload) {
      const vm = this;
      for (let i = 0; i < payload.users.length; i++) {
        const data = {
          courseId: vm.filter.course,
          userId: payload.users[i],
          lessonIds: payload.lessonIds
        };
        await $.post(vm.url + '/backend/user/sync-progress', data, function (res) {
          if (res.code == 200) {
            vm.syncSuccess++;
          }
        });
      }
    }
  },
  mounted: function() {
    var vm = this;

    // Connect socket
    vm.socket = io.connect('https://chat.dungmori.com');
    vm.courses = courses;

    vm.setFilterByUrl();

    // Loại bỏ các filter rỗng khỏi request
    var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
      return value !== undefined && value !== null && value !== '';
    }), ['total_page']);
    this.getList(filter);
  },
});
