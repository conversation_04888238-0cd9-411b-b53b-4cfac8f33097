$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});


//webkitURL is deprecated but nevertheless
URL = window.URL || window.webkitURL;

var gumStream;                      //stream from getUserMedia()
var rec;                            //Recorder.js object
var input;                          //MediaStreamAudioSourceNode we'll be recording

// shim for AudioContext when it's not avb.
var AudioContext = window.AudioContext || window.webkitAudioContext;
var audioContext //audio context to help us record

const router = new VueRouter({
    mode: 'history'
});

var cmts = new Vue({

    el: '.main-comment',
    components: {
        paginate: VuejsPaginate,
    },
    data: {

        url: window.location.origin, //đường dẫn host

        filter: {
            id: "",
            keyword: "",
            seen: "",
            category: "",
            vocab: "",
            user: "",
            admin: "",
            time_type: "1",
            time_from: undefined,
            time_to: undefined,
            page: 1,
            per_page: 15,
            total_page: 10
        },
        total_result: 0,

        previewId: null,
        previewName: null,
        previewEmail: null,

        startDate: null,
        endDate: null,

        listComments: [],
        countResults: 0,

        page : '1',
        filterRangeCheck: false,

        blob: null,
        currentRecordId: null,

        admins: [],
        loading: false,
    },
    watch: {

    },
    methods: {

        // call api lấy danh sách invoices
        // truyền vào filter
        // set các biến trạng thái tmp để có thể dùng v-if, v-model
        getComments: function(filter) {
            var vm = this;

            vm.loading = true;
            setTimeout(function () {
                $.post(window.location.origin +'/backend/discuss/comments', filter, function (res) {
                    if(res.code == 200) {
                        vm.listComments = res.data.results.map(function (result) {
                            if(result.task) {
                                result.vocab = JSON.parse(result.task.value).name;
                            }
                            return result;
                        });
                        vm.filter.total_page = res.data.total_page;
                        vm.total_result = res.data.total_result;
                        autosize(document.querySelectorAll('textarea'));
                    } else {
                        alert('Có lỗi! Liên hệ dev!!!');
                    }
                    vm.loading = false;
                });
            }, 200);
        },
        // gọi hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
        changePage: function (pageNum) {
            var vm = this;
            vm.filter.page = pageNum;
            var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
                return value !== undefined && value !== null && value !== '';
            }), ['total_page']);
            vm.getComments(filter);

            router.replace(window.location.pathname + "?" + $.param(filter));
        },
        // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
        applyFilter: function () {
            var vm = this;

            vm.filter.page = 1;
            var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
                return value !== undefined && value !== null && value !== '';
            }), ['total_page', 'page']);
            vm.getComments(filter);

            // console.log(filter)
            router.replace(window.location.pathname + "?" + $.param(filter));
        },
        // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
        setFilterByUrl: function () {
            var vm = this;
            var filterByUrl = $.deparam.querystring();
            _.forEach(filterByUrl, function (value, key) {
                vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
            });
        },

        // thay đổi filter theo datetime
        onChangeDatetime: function (event) {
            var vm = this;
            vm.filter[event.target.name] = moment(event.date).format('YYYY-MM-DD HH:mm');
        },
        onChangeDateRange: function (time) {
            var vm = this;
            vm.filter.time_from = moment(time[0]).format('YYYY-MM-DD');
            vm.filter.time_to = moment(time[1]).format('YYYY-MM-DD');
            // vm.applyFilter();
        },
        onChangeCheckbox: function (param, event) {
            vm = this;

            vm.filter[param] = event.target.checked ? 1 : 0;
            vm.applyFilter();
        },
        filterByVocab: function (vocab) {
            vm = this;

            vm.filter.vocab = vocab;
            vm.filter.keyword = vocab;
            vm.applyFilter();
        },
        //in ra thông tin có dấu cách
        printInfo: function(info){

            var result = _.escape(info);

            result = result.replace('<', '&#60;');
            result = result.replace('>', '&#62;');

            //xử lý xuống dòng
            result =  info.replace(new RegExp('\r?\n','g'), '<br />');

            var re = /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/ig;
            return result.replace(re, function(match, lParens, url) {
                var rParens = '';
                lParens = lParens || '';

                // Try to strip the same number of right parens from url
                // as there are left parens.  Here, lParenCounter must be
                // a RegExp object.  You cannot use a literal
                //     while (/\(/g.exec(lParens)) { ... }
                // because an object is needed to store the lastIndex state.
                var lParenCounter = /\(/g;
                while (lParenCounter.exec(lParens)) {
                    var m;
                    // We want m[1] to be greedy, unless a period precedes the
                    // right parenthesis.  These tests cannot be simplified as
                    //     /(.*)(\.?\).*)/.exec(url)
                    // because if (.*) is greedy then \.? never gets a chance.
                    if (m = /(.*)(\.\).*)/.exec(url) ||
                      /(.*)(\).*)/.exec(url)) {
                        url = m[1];
                        rParens = m[2] + rParens;
                    }
                }
                return lParens + "<a href='" + url + "' target='_blank'>" + url + "</a>" + rParens;
            });
        },

        //hiên thị khung ghi âm
        showRecorder: function(cmtId){

            $("#preview-recorder-"+ cmtId).css('display', 'block');
        },

        //ấn nút thu âm
        recordButton: function(cmtId){

            vm = this;

            // console.log("record ", cmtId);
            var constraints = { audio: true, video:false }

            //disable nút record
            var recordButton = document.getElementById("recordButton"+ cmtId);
            var stopButton   = document.getElementById("stopButton"+ cmtId);
            recordButton.disabled = true;
            stopButton.disabled   = false;

            vm.currentRecordId = cmtId;

            //bắt đầu ghi âm
            navigator.mediaDevices.getUserMedia(constraints).then(function(stream) {

                audioContext = new AudioContext();
                // document.getElementById("formats"+ cmtId).innerHTML="Format: 1 channel pcm @ "+audioContext.sampleRate/1000+"kHz";
                gumStream = stream;
                input = audioContext.createMediaStreamSource(stream);
                rec = new Recorder(input,{numChannels:1});
                rec.record();
                console.log("Recording started");

                // xóa cái cũ
                $("#recordingsList"+ vm.currentRecordId).html('<li><i class="fa fa-microphone"></i> &nbsp; Đang ghi âm...</li>');

            }).catch(function(err) {
                recordButton.disabled = false;
                stopButton.disabled = true;
            });
        },

        //ấn nút stop
        stopButton: function(cmtId){

            console.log("stopButton clicked");

            vm = this;

            var recordButton = document.getElementById("recordButton"+ cmtId);
            var stopButton   = document.getElementById("stopButton"+ cmtId);
            stopButton.disabled = true;
            recordButton.disabled = false;

            rec.stop();
            gumStream.getAudioTracks()[0].stop();

            vm.currentRecordId = cmtId;

            //create the wav blob and pass it on to createDownloadLink
            rec.exportWAV(vm.createDownloadLink);
        },

        createDownloadLink: function (blob) {

            vm = this;

            vm.blob = blob;

            var url = URL.createObjectURL(blob);

            var filename = Math.round(new Date().getTime()/1000);
            filename = adminId +''+ filename +'.'+ Math.floor(Math.random() * 1000000);

            $("#recordingsList"+ vm.currentRecordId).html("<li><audio controls=true src='"+ url +"'></audio> </li>");

        },

        checkReplied: function (parent_id) {
            vm = this;

            // bỏ qua comment rỗng
            if (($("#reply-input-content-"+ parent_id).val() == null ||
              $("#reply-input-content-"+ parent_id).val() == undefined ||
              $("#reply-input-content-"+ parent_id).val().trim() == "") && vm.blob == null) {

                alert("Vui lòng nhập đủ thông tin");
                return;
            }

            $.post(window.location.origin + "/backend/discuss/api/check-replied", {id: parent_id}, function (response) {
                if (response.code == 200) {
                    vm.postNewAnswer(parent_id);
                }
                else if (response.code == 409){
                    var check = confirm(response.data.message.vi + '\n' + response.data.message.en);
                    if (check) {
                        vm.postNewAnswer(parent_id);
                    }
                } else {
                    alert('Đã xảy ra lỗi');
                }
            });
        },

        //đăng reply mới
        postNewAnswer: function(parent_id){
            vm = this;

            var is_correct = $("#is-correct-box-"+ parent_id).prop('checked') ? 1 : 0;
            // var file_data = $('#commentImagePicked'+parent_id).prop('files')[0];
            var form_data = new FormData();
            form_data.append('parent_id', parent_id);
            form_data.append('content', $("#reply-input-content-"+ parent_id).val());
            form_data.append('is_correct', is_correct);
            form_data.append('audio', vm.blob);


            setTimeout(function(){
                $.ajax({
                    url: window.location.origin +"/backend/discuss/api/add-new-reply",
                    type: "POST",
                    data: form_data,
                    contentType: false,
                    cache: false,
                    processData: false,
                    success: function(response){

                        console.log(response);
                        if(response == 'imagesize') alert("ảnh vượt quá dung lượng cho phép");
                        else if (response == 'type') alert("định dạng ảnh không cho phép");
                        else{

                            var indexOfComment = 0; //thứ tự của comment đang reply
                            for(var i=0; i<vm.listComments.length; i++) if(vm.listComments[i].id == parent_id)  indexOfComment = i;

                            //console.log(indexOfComment);
                            vm.listComments[indexOfComment].replies.push(response);

                            //dánh dấu đã xem cho cmt cha
                            vm.listComments[indexOfComment].readed = 1;

                            $("#reply-input-content-"+ parent_id).val('');
                            $("#reply-input-content-"+ parent_id).css('height', '42px');

                            // xóa cái cũ
                            $("#recordingsList"+ vm.currentRecordId).html('');
                            vm.blob = null;

                        }
                    }
                });

            }, 500);

        },

        //hàm đánh dấu là đã đọc
        markAsRead: function(id){

            vm = this;

            $.post(window.location.origin +"/backend/discuss/api/mark-as-read", {id: id}, function(response){
                if(response == "success"){
                    console.log("đánh dấu đã xem: "+ id);
                    for(var i = 0; i < vm.listComments.length; i++){
                        if(vm.listComments[i].id == id) {
                            vm.listComments[i].readed = 1;
                            break;
                        }
                    }

                }else{
                    alert("đánh dấu bị lỗi");
                }
            });
        },

        //xóa comment theo id
        delComment: function(id){

            $.post(window.location.origin +"/backend/discuss/api/delete-comment", {id: id}, function(response){
                if(response == "success"){

                    console.log("xóa thành công: "+ id);
                    $(".item-"+id).fadeOut();
                }else{
                    alert("xóa lỗi");
                }
            });
        },

        //xóa reply theo id
        delReply: function(id){

            $.post(window.location.origin +"/backend/discuss/api/delete-reply", {id: id}, function(response){
                if(response == "success"){

                    $("#reply-item-"+id).fadeOut();

                }else{
                    alert("xóa lỗi");
                }
            });
        },

        previewUser: function(id, name, email){
            console.log("preview user", id);

            vm = this;
            vm.previewId = id;
            vm.previewName = name;
            vm.previewEmail = email;
        },

        //hàm admin sửa cmt
        editAdminComment: function(id, content){

            vm = this;
            console.log("hiện sửa cmt", id);
            console.log("hiện sửa cmt content", content);

            //gán giá trị cần sửa
            $("#edit-comment-area").val(content);
            $("#edit-comment-id").val(id);

        },

        //hàm lưu lại giá trị sửa
        saveAdminComment: function(){

            vm = this;

            var content = $("#edit-comment-area").val();
            var id = $("#edit-comment-id").val();

            console.log("lưu giá trị id", id);
            console.log("lưu giá trị mới", content);

            $.post(window.location.origin +"/api/comments/edit-comment", {id: id, content: content}, function(response){
                if(response == "success"){
                    console.log("lưu thành công");

                    //cập nhật lại giá trị trên giao diện
                    $("#child-comment-content-"+ id).html(vm.printInfo(content));

                    $.fancybox.close();
                }else{
                    alert("lưu thất bại");
                }
            });
        },

        //cancel đóng popup nếu không muốn sửa cmt
        cancelAdminComment: function(){
            $.fancybox.close();
        },
        // Đánh dấu reply kaiwa là sửa phát âm
        setCorrectSpell: function (cmtId, event) {
            vm = this;

            var isCorrect = event.target.checked ? 1 : 0;

            $.post(window.location.origin +"/backend/discuss/api/set-correct", {id: cmtId, isCorrect: isCorrect}, function(response){

                if(response.code == 200){
                    // map toàn bộ comment của học viên, mảng gồm các object comment có thuộc tính replies là mảng các reply của comment đó
                    vm.listComments = vm.listComments.map(function (comment) {
                        if (_.some(comment.replies, ['id', response.data.id])) {
                            comment.replies.map(function (reply) {
                                if (reply.id == response.data.id) reply.is_correct = response.data.is_correct;
                                return reply;
                            })
                        }
                        return comment;
                    })
                } else {
                    alert('Xử lý lỗi');
                }
            });

        },
        resetFilter: function () {
            vm = this;

            vm.filter = {
                id: "",
                keyword: "",
                seen: "",
                category: "",
                vocab: "",
                user: "",
                admin: "",
                time_type: "",
                time_from: undefined,
                time_to: undefined,
                page: 1,
                per_page: 15,
                total_page: 10
            }
            vm.applyFilter();
        }

    },
    mounted: function () {
        var vm = this;
        vm.admins = admins;
        vm.setFilterByUrl();
        var filter = _.omit(_.pickBy(vm.filter, function (value, key) {
            return value !== undefined && value !== null && value !== '';
        }), ['total_page']);
        this.getComments(filter);
    }
});

//# sourceMappingURL=discuss.js.map
