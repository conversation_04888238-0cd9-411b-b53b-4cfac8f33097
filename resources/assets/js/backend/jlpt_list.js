Vue.component('date-picker', VueBootstrapDatetimePicker);

function getInitialForm() {
  return {
    name: '',
    maximum_time: '',
    time_start: '',
    time_end: '',
    course: '',
    maximum_point: '',
    passed_point: '',
    create_lessons: true,
    type: route == 'exam.tests' ? 2 : (route == 'ad-test.index' ? 3 : 1),
    has_summary: true
  };
}

var vm = new Vue({
  el: '#root',

  data: {
    exams: [],
    types: [
      'Từ vựng - Chữ hán',
      'Ngữ pháp - Đọc hiểu',
      '<PERSON>he hiểu',
    ],
    N1Types: [
      'Từ vựng - <PERSON>ữ hán - Ngữ pháp',
      '<PERSON><PERSON>c hiểu',
      '<PERSON>he hiểu'
    ],
    courses: {
      1: 'N1',
      2: 'N2',
      3: 'N3',
      4: 'N4',
      5: 'N5',
      6: 'tokutei1',
      7: 'tokutei2',
    },
    filter: {
      level: undefined,
      keyword: undefined,
    },
    form: getInitialForm(),
    datetimepickerOptions: {
      locale: 'vi',
    },
    startDatetimepickerOptions: {
      locale: 'vi',
      format: 'YYYY-MM-DD 00:00:00',
    },
    endDatetimepickerOptions: {
      locale: 'vi',
      format: 'YYYY-MM-DD 23:59:59',
    },
    examsCount: '',
    examChosen: {},
    lessons: [],
    chosenLesson1: '',
    chosenLesson2: '',
    chosenLesson3: '',
    examUrl: '',
    route: route,
    excelFile: null,
    loading: false
  },

  computed: {
    isFormNotReady: function () {
      return this.form.name === '' || this.form.course === '' || this.form.maximum_time === '' ||
        !/^\d+$/.test(this.form.maximum_time) || this.form.maximum_point.length === 0;
    }
  },
  watch: {
    'filter.level': function() {
      this.getExams();
    },
    'form.time_start': function(val) {
      // console.log(moment(val))
    }
  },
  created() {
    this.getExams();
  },

  mounted() {
    $('#addExam, #addLessons').on('hide.bs.modal', () => {
      this.form = getInitialForm();
      this.examChosen = {};
      this.chosenLesson1 = '';
      this.chosenLesson2 = '';
      this.chosenLesson3 = '';
    });
    //bootstrap toggle cần chạy sau khi đã render xong hết page
    setTimeout(() => {
      $(".toggle-processing").bootstrapToggle();
      $(".toggle-schedule").bootstrapToggle();
    }, 500);
  },

  updated() {
    if (this.exams.length > this.examsCount) {
      $(".toggle-processing").last().bootstrapToggle();
      $(".toggle-schedule").last().bootstrapToggle();
    }
    this.examsCount = this.exams.length;
  },

  filters: {
    formatDate(value) {
      if (value) {
        return moment(String(value)).format('HH:mm DD/MM/YYYY')
      }
    }
  },

  methods: {
    async getExams() {
      const type = this.route == 'exam.tests' ? 2 : (this.route == 'ad-test.index' ? 3 : 1);
      const levelParam = this.filter.level ? `&level=${this.filter.level}` : '';
      const keyword = this.filter.keyword ? `&keyword=${this.filter.keyword}` : '';
      try {
        this.exams = await $.get(`/backend/thi-thu/exam/list?type=${type}${levelParam}${keyword}`);
        this.examsCount = this.exams.length;
      } catch (e) {}
    },

    triggerExcelInput() {
      this.$refs.file.click()
    },
    uploadExcel() {
      const vm = this
      const formData = new FormData()
      this.loading = true
      formData.append('file', this.$refs.file.files[0])
      $.ajax({
        type: "POST",
        url: '/backend/ad-test/import',
        success: function (data) {
          // your callback here
          vm.loading = false
          alert('Xong')
        },
        error: function (error) {
          // handle error
        },
        async: true,
        data: formData,
        cache: false,
        contentType: false,
        processData: false,
        timeout: 60000
      });
    },
    addExam() {
      var data = Object.assign({}, this.form);
      data.create_lessons = data.create_lessons ? 1 : 0;
      if (data.time_start === '') {
        delete data.time_start;
      }
      if (data.time_end === '') {
        delete data.time_end;
      }
      $.post(this.examUrl, data, (res) => {
        if (res.code != 200) {
          alert(res.msg)
        }
        this.getExams();
        if (res.code == 200) {
          $("#addExam").modal('hide');
        }
      }).fail(e => {
        Object.values(e.responseJSON).forEach(error => {
          this.$toastr('error', {
            msg: error[0],
            timeout: 4000,
            position: 'toast-top-left'
          });
        });
      });
    },

    changeProcessing(element) {

      // console.log(element);

      var examId = $(element).attr('id');
      $.ajax({
        url: '/backend/thi-thu/exam/' + examId + '/status',
        type: 'PATCH',
        data: {
          isProcessing: ($(element).prop('checked')) ? 1 : 0
        },
        success: function(data) {
          if (data.code == 200) {
            location.reload();
          }
        }
      });

      var inputElements = document.getElementsByClassName('toggle-processing');
      for(var i=0; inputElements[i]; ++i){
        if(inputElements[i].checked && inputElements[i] != element){

          console.log("tắt: ", inputElements[i]);
          // var el = $(inputElements[i]).attr('id');
          // $('#'+el).bootstrapToggle('off')
        }
      }

    },
    changeScheduleNotification(element) {
      var examId = $(element).attr('id');
      $.ajax({
        url: '/backend/thi-thu/exam/' + examId + '/notice',
        type: 'PATCH',
        data: {
          notice: ($(element).prop('checked')) ? 1 : 0
        },
        success: function(response) {
          if (response.code == 200) {
            location.reload();
          } else {
            alert(response.data.error);
          }
        }
      });

      var inputElements = document.getElementsByClassName('toggle-schedule');
      for(var i=0; inputElements[i]; ++i){
        if(inputElements[i].checked && inputElements[i] != element){

          console.log("tắt: ", inputElements[i]);
          // var el = $(inputElements[i]).attr('id');
          // $('#'+el).bootstrapToggle('off')
        }
      }

    },

    chooseLessons(exam) {
      this.examChosen = exam;
      $.get('/backend/thi-thu/exam/' + exam.id + '/lessons/available', response => this.lessons = response);
    },

    addLessons() {
      var data = {
        lessons: [this.chosenLesson1, this.chosenLesson2, this.chosenLesson3]
      };
      $.post('/backend/thi-thu/exam/' + this.examChosen.id + '/lessons/add', data, () => {
        this.getExams();
        $('#addLessons').modal('hide');
      });
    },

    setAddExamUrl() {
      this.examUrl = '/backend/thi-thu/exam';
    },

    setEditExamUrl(exam) {
      this.examChosen = exam;
      this.form.name = exam.name;
      this.form.access_code = exam.access_code;
      this.form.maximum_time = exam.maximum_time;
      this.form.time_start = exam.time_start ? moment(exam.time_start) : '';
      this.form.time_end = exam.time_end ? moment(exam.time_end) : '';
      this.form.course = exam.course;
      this.form.maximum_point = exam.maximum_point ? exam.maximum_point : '';
      this.form.passed_point = exam.passed_point ? exam.passed_point : '';
      this.form.has_summary = !!exam.has_summary;
      this.examUrl = '/backend/thi-thu/exam/' + exam.id + '/edit';
      $('#addExam').modal('show');
    },

    deleteExam(exam) {
      bootbox.confirm({
        message: 'Bạn có chắc chắn muốn xoá không?',
        callback: (result) => {
          if (result) {
            $.ajax({
              type: 'delete',
              url: '/backend/thi-thu/exam/' + exam.id
            }).done(() => {
              this.getExams();
            });
          }
        }
      });
    },
    /**
     * an nut thi thu
     * @param exam
     */
    hiddenExam: function (exam) {
      var check = window.confirm('Bạn thực sự muốn ẩn bài này?');
      var _this = this;
      if (check) {
        _this.updateData(exam.id);
      }
    },
    /**
     * update du lieu
     * @param id
     */
    updateData : function (id) {
      var _this = this;
      var url = urlUpdate;
      var data = {id: id};
      $.ajax({
        url: url,
        type: 'post',
        data: data,
        dataType: 'json',
        success: function (response) {
          _this.getExams();
        }
      });
    },
    // reset kết quả thi thử
    resetExam: function (id) {
      var url = urlReset;
      var confirm = window.confirm('Xác nhận reset kết quả thi thử?');
      if (confirm) {
        var data = {id: id};
        $.ajax({
          url: url,
          type: 'post',
          data: data,
          dataType: 'json',
          success: function (response) {
            alert('Reset thành công');
          }
        });
      }
    }
  }
});
