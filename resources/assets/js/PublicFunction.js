// <PERSON><PERSON><PERSON> tra ký tự đặc biệt
function checkspecialSymbol(text) {
    var format = /[#$%^&*()_\[\]{};':"\\|<>\/]/;
    return format.test(text);
}

// Chuẩn hóa string
function standardizeString(text) {
    newString = text.replace(/\s+/g,'');
    newString = text.replace(/\s+/g,' ').trim();
    return newString;
}

// Check email format
function validateEmail(email) {
    var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}
