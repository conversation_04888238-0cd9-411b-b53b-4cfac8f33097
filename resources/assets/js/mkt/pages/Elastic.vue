<template>
  <span>{{ userIds.length - ids.length }} / {{ userIds.length }}</span>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import { chunk } from 'lodash';
export default {
  data() {
    return {
      ids: []
    };
  },
  async mounted() {
    await this.getUserIds();
    this.ids = this.userIds;
    await this.sync();
  },
  computed: {
    ...mapGetters('ui', ['userIds']),
  },
  methods: {
    ...mapActions('ui', ['getUserIds', 'syncElastic']),
    async sync() {
      console.log('start');
      const that = this;
      const ids = chunk(this.ids, 50);
      for (const id of ids) {
        await that.syncElastic({ id: id });
        this.ids = this.ids.filter(o => {
          return !id.includes(o);
        })
      }
    }
  }
}
</script>
