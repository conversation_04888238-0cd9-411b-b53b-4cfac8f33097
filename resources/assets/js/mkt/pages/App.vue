<template>
  <el-container style="width: 100%; height: 100%; border: 1px solid #eee">
    <el-aside width="200px" style="background-color: #fff; position: fixed; height: 100vh; overflow-y: scroll;">
      <el-menu :default-openeds="['1', '3']">
        <el-submenu index="1">
          <template slot="title"><i class="el-icon-message"></i>Báo cáo</template>
          <router-link :to="{name: 'dashboard'}">
            <el-menu-item index="1-1">T<PERSON><PERSON><PERSON> tr<PERSON>nh học</el-menu-item>
          </router-link>
        </el-submenu>
      </el-menu>
    </el-aside>
    <el-container style="width: calc(100% - 200px); margin-left: 200px;">
      <el-header style="text-align: right; font-size: 12px; height: 50px;background: #96D962"></el-header>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'App',
  data() {
    const item = {
      date: '2016-05-02',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles'
    };
    return {
      tableData: Array(20).fill(item)
    }
  },
  computed: {
    ...mapGetters('ui', ['count'])
  },
  methods: {
    // ...mapActions('ui', ['getProducts'])
  },
  mounted() {
    // this.getProducts(1);
  }
}
</script>
<style>
.el-header {
  background-color: #B3C0D1;
  color: #333;
  line-height: 60px;
}

.el-aside {
  color: #333;
}
</style>
