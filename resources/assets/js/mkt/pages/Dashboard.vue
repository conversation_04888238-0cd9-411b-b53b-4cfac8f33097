<template>
  <div>
    <div class="statistic flex items-center mb-3" v-loading="statisticLoading">
      <div class="p-3 mr-3  border">Số học viên: {{ totalResult }}</div>
      <div class="p-3 mr-3  border">% học trung bình: {{ averageProgress }}%</div>
      <div class="p-3 mr-3  border">Số học viên >= %TB: {{ aboveProgress }}</div>
      <div class="p-3 mr-3  border">Số học viên < %TB: {{ belowProgress }}</div>
      <el-button type="primary" @click="getStatistic">L<PERSON>y số li<PERSON></el-button>
    </div>
    <div class="filter mb-3 flex items-center">
      <span class="h1 mr-3">Khoá</span>
      <el-select v-model="filters.courseId" @change="changeCourse" placeholder="Select" class="mr-3" style="width: 100px;">
        <el-option label="N1" :value="17"></el-option>
        <el-option label="N2" :value="16"></el-option>
        <el-option label="N3" :value="3"></el-option>
        <el-option label="N4" :value="4"></el-option>
        <el-option label="N5" :value="5"></el-option>
      </el-select>
      <el-input
          class="mr-3"
          placeholder="Email"
          prefix-icon="el-icon-search"
          v-model="filters.email"
          @keyup.enter.native="getList"
          style="max-width: 300px;"
      >
      </el-input>
      <el-date-picker
        v-model="filters.createdAt"
        type="daterange"
        range-separator="-"
        start-placeholder="Tham gia từ ngày"
        end-placeholder="Đến ngày"
        value-format="yyyy-MM-dd"
        class="mr-3"
      >
      </el-date-picker>
      <el-button type="primary" @click="getList">Tìm kiếm</el-button>
      <el-button type="primary" @click="exportExcel">Xuất Excel</el-button>
      <div class="ml-3">Tìm thấy <strong>{{ totalResult }}</strong> kết quả</div>
    </div>
    <el-slider
      v-model="progressRange"
      range
      show-stops
      :marks="marks"
      :max="100"
      style="max-width: 50%"
    >
    </el-slider>
    <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%; margin-top: 30px;">
      <el-table-column
          prop="id"
          label="ID"
          width="80"
      >
      </el-table-column>
      <el-table-column
          prop="name"
          label="Họ tên"
          width="100"
      >
      </el-table-column>
      <el-table-column
          prop="email"
          label="Email"
          width="180"
      >
      </el-table-column>
      <el-table-column
          prop="totalPercent"
          label="Tổng"
      >
      </el-table-column>
      <el-table-column
          v-for="category in categories"
          :key="`column-category-${category.id}`"
          :prop="`category-${category.id}`"
          :label="category.title"
      >
      </el-table-column>
    </el-table>
    <div style="margin-top: 10px;">
      <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size.sync="perPage"
          layout="prev, pager, next"
          :total="totalResult">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import { find, sumBy, groupBy, map } from 'lodash';
import axios from 'axios';

export default {
  data() {
    return {
      results: [],
      page: 1,
      perPage: 20,
      loading: false,
      statisticLoading: false,
      progressRange: [0, 100],
      vendor: 'dmr',
      filters: {
        courseId: 17,
        email: '',
        createdAt: '',
      },
      marks: {
        0: '0%',
        10: '10%',
        20: '20%',
        30: '30%',
        40: '40%',
        50: '50%',
        60: '60%',
        70: '70%',
        80: '80%',
        90: '90%',
        100: '100%',
      }
    };
  },
  computed: {
    ...mapGetters('ui', ['users', 'categories', 'courseLessonCount', 'totalResult', 'averageProgress', 'aboveProgress', 'belowProgress']),
    tableData() {
      return this.users.map((user) => {
        user.total = sumBy(user.progress, function (o) {
          return parseInt(o.progress);
        })
        const groups = map(groupBy(user.progress, function (o) {
          return o.group_id;
        }), function (value, key) {
          return {
            id: parseInt(key),
            total: sumBy(value, function (o) {
              return parseInt(o.progress);
            })
          };
        })
        user.categories = this.categories.map((item) => {
          item.total = 0;
          item.groups.forEach((group) => {
            const foundItem = find(groups, {id: group.id});
            if (foundItem) {
              item.total += group.lessons_stat.length ? foundItem.total / group.lessons_stat.length : 0;
            }
          })
          item.average = item.groups.length ? item.total / item.groups.length : 0;
          user[`category-${item.id}`] = `${item.average.toFixed(2)}%`;
          return {...item, total: item.average};
        })
        user.totalPercent = `${(user.total / this.courseLessonCount).toFixed(2)}%`;
        return user;
      })
    },
  },
  methods: {
    ...mapActions('ui', ['getUserList', 'getUserStatistic']),
    async getList() {
      this.loading = true;
      const data = {
        ...this.filters,
        progressFrom: this.progressRange[0],
        progressTo: this.progressRange[1],
        vendor: this.vendor,
        page: this.page,
        perPage: this.perPage,
      }
      console.log(data)
      await this.getUserList(data);
      this.loading = false;
    },
    async getStatistic() {
      this.statisticLoading = true;
      const data = {
        ...this.filters,
        progressFrom: this.progressRange[0],
        progressTo: this.progressRange[1],
        createdAt: this.filters.createdAt.split(','),
        vendor: this.vendor,
        page: this.page,
        perPage: this.perPage,
      }
      await this.getUserStatistic(data);
      this.statisticLoading = false;
    },
    async handleCurrentChange(page) {
      await this.getList();
    },
    async handleSizeChange(perPage) {
      await this.getList();
    },
    changeCourse(courseId) {
      this.getList();
    },
    async exportExcel() {
      this.loading = true;
      const data = {
        ...this.filters,
        progressFrom: this.progressRange[0],
        progressTo: this.progressRange[1],
        createdAt: this.filters.createdAt.split(','),
        vendor: this.vendor,
        page: this.page,
        perPage: this.perPage,
      }
      axios.post(window.location.origin + '/backend/mkt/api/v1/export', data, {
        responseType: 'blob'
      }).then((response) => {
        const url = URL.createObjectURL(new Blob([response.data], {
          type: 'application/vnd.ms-excel'
        }))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', 'tien_trinh_hoc_' + Date.now() + '.xlsx')
        document.body.appendChild(link)
        link.click()
      });
      this.loading = false;
    }
  },
  mounted() {
    this.getList();
  },
}
</script>
<style>
.el-table .cell {
  word-break: break-word;
}
</style>
