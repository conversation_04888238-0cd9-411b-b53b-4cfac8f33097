import Vue from 'vue';
import api from '../api'
import * as types from './mutation-types';
const ui = {
  namespaced: true,
  state: {
    categories: [],
    users: [],
    userIds: [],
    courseLessonCount: 0,
    totalResult: undefined,
    averageProgress: 0,
    aboveProgress: 0,
    belowProgress: 0,
  },
  getters: {
    categories: state => state.categories,
    users: state => state.users,
    userIds: state => state.userIds,
    courseLessonCount: state => state.courseLessonCount,
    totalResult: state => state.totalResult,
    averageProgress: state => state.averageProgress,
    aboveProgress: state => state.aboveProgress,
    belowProgress: state => state.belowProgress,
  },
  mutations: {
    [types.SET_USER_ID_LIST](state, data) {
      state.userIds = data.data;
    },
    [types.SYNCED_ELASTIC](state, data) {

    },
    [types.SET_USER_LIST](state, data) {
      state.categories = data.categories;
      state.users = data.progresses.data;
      state.courseLessonCount = data.courseLessonCount;
    },
    [types.SET_STATISTIC](state, data) {
      state.averageProgress = data.average.toFixed(2);
      state.aboveProgress = parseInt(data.above);
      state.belowProgress = parseInt(data.below);
      state.totalResult = data.total;
    },
  },
  actions: {
    getUserIds({ commit }, data) {
      return api.progress.getUserIds(data)
          .then(response => commit('SET_USER_ID_LIST', response))
          .catch();
    },
    syncElastic({ commit }, data) {
      return api.progress.syncElastic(data)
          .then(response => commit('SYNCED_ELASTIC', response))
          .catch();
    },
    getUserList({ commit }, data) {
      return api.progress.loadList(data)
          .then(response => commit('SET_USER_LIST', response))
          .catch();
    },
    getUserStatistic({ commit }, data) {
      return api.progress.loadStatistic(data)
          .then(response => commit('SET_STATISTIC', response))
          .catch();
    },
  }
};

export default ui;
