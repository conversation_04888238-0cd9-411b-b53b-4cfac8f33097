import api from './../config/api';
export default {
  me: () => new Promise((resolve, reject) => {
    api.get('/me')
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  getUserIds: data => new Promise((resolve, reject) => {
    api.get(`/get-user-ids`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  loadList: data => new Promise((resolve, reject) => {
    let params = `vendor=${data.vendor}&page=${data.page}&perPage=${data.perPage}&courseId=${data.courseId}&email=${data.email}&progressFrom=${data.progressFrom}&progressTo=${data.progressTo}`
    if (data.createdAt) {
      params += `&createdAt[]=${data.createdAt[0]}&createdAt[]=${data.createdAt[1]}`
    }
    api.get(`/get-users?${params}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  loadStatistic: data => new Promise((resolve, reject) => {
    let params = `vendor=${data.vendor}&page=${data.page}&perPage=${data.perPage}&courseId=${data.courseId}&email=${data.email}&progressFrom=${data.progressFrom}&progressTo=${data.progressTo}`
    if (data.createdAt) {
      params += `&createdAt[]=${data.createdAt[0]}&createdAt[]=${data.createdAt[1]}`
    }
    api.get(`/get-statistic?${params}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  exportExcel: data => new Promise((resolve, reject) => {
    let params = `vendor=${data.vendor}&page=${data.page}&perPage=${data.perPage}&courseId=${data.courseId}&email=${data.email}&progressFrom=${data.progressFrom}&progressTo=${data.progressTo}`
    if (data.createdAt) {
      params += `&createdAt[]=${data.createdAt[0]}&createdAt[]=${data.createdAt[1]}`
    }
    api.post('/export', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  syncElastic: data => new Promise((resolve, reject) => {
    api.post('/sync-elastic', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
