import Vue from "vue";
import "es6-promise/auto";
import axios from "axios";
import VueAxios from "vue-axios";
import VueRouter from "vue-router";
import ElementUI from "element-ui";

import "element-ui/lib/theme-chalk/index.css";
import locale from "element-ui/lib/locale/lang/vi";
// resources
import store from "./video/store";
import routes from "./video/routes";
import App from "./video/pages/App";

import filters from "./video/filters";

const router = new VueRouter({
  base: "backend/video/",
  history: true,
  mode: "history",
  routes,
});
Vue.router = router;
Vue.use(VueRouter);

// axios request
Vue.use(VueAxios, axios);

// element ui
Vue.use(ElementUI, { locale });
// components
Vue.component("app", App);

const app = new Vue({
  el: "#videoApp",
  router,
  store,
  filters,
});
app.$mount("#videoApp");
