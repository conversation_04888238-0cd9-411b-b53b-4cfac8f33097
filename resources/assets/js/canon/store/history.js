import api from '../api'
import * as types from './mutation-types';

const history = {
  namespaced: true,
  state: {
    list: [],
  },
  getters: {
    histories: state => state.list,
  },
  mutations: {
    [types.SET_USER_HISTORY](state, data) {
      state.list = data.data;
    },
  },
  actions: {
    getUserHistories({ commit }, data) {
      return api.history.getUserHistories(data)
          .then(response => commit('SET_USER_HISTORY', response))
          .catch();
    },
  }
};

export default history;
