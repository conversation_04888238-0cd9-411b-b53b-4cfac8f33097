import Vue from 'vue';
import api from '../api'
import * as types from './mutation-types';
const ui = {
  namespaced: true,
  state: {
    me: {},
    groups: [],
    categories: [],
    lessons: [],
    exerciseComponents: [],
    users: [],
    userRoadmap: [],
    courseLessonCount: 0,
    totalResult: undefined,
    averageProgress: 0,
    aboveProgress: 0,
    belowProgress: 0,
  },
  getters: {
    me: state => state.me,
    groups: state => state.groups,
    categories: state => state.categories,
    lessons: state => state.lessons,
    exerciseComponents: state => state.exerciseComponents,
    users: state => state.users,
    userRoadmap: state => state.userRoadmap,
    courseLessonCount: state => state.courseLessonCount,
    totalResult: state => state.totalResult,
    averageProgress: state => state.averageProgress,
    aboveProgress: state => state.aboveProgress,
    belowProgress: state => state.belowProgress,
  },
  mutations: {
    [types.SET_USER_LIST](state, data) {
      state.categories = data.categories;
      state.users = data.progresses.data;
      state.courseLessonCount = data.courseLessonCount;
      state.exerciseComponents = data.exerciseComponents;
      state.lessons = data.lessons;
    },
    [types.SET_STATISTIC](state, data) {
      state.averageProgress = data.average.toFixed(2);
      state.aboveProgress = parseInt(data.above);
      state.belowProgress = parseInt(data.below);
      state.totalResult = data.total;
    },
    [types.SET_ME](state, data) {
      state.me = data.data.me;
      state.groups = data.data.groups;
    },
  },
  actions: {
    getMe({ commit }) {
      return api.progress.me()
          .then(response => commit('SET_ME', response))
          .catch();
    },
    getUserList({ commit }, data) {
      return api.progress.loadList(data)
          .then(response => commit('SET_USER_LIST', response))
          .catch();
    },
    getUserRoadmap({ commit }, data) {
      return api.progress.getUserRoadmap(data)
          .then(response => commit('SET_USER_ROADMAP', response))
          .catch();
    },
    getUserStatistic({ commit }, data) {
      return api.progress.loadStatistic(data)
          .then(response => commit('SET_STATISTIC', response))
          .catch();
    },
  }
};

export default ui;
