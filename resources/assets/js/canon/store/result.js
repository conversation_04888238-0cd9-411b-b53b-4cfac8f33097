import api from '../api'
import * as types from './mutation-types';

const result = {
  namespaced: true,
  state: {
    list: [],
    exams: [],
  },
  getters: {
    results: state => state.list,
    exams: state => state.exams,
  },
  mutations: {
    [types.SET_USER_RESULT](state, data) {
      state.list = data.data.users;
      state.exams = data.data.exams;
    },
  },
  actions: {
    getResults({ commit }, data) {
      return api.result.getResults(data)
          .then(response => commit('SET_USER_RESULT', response))
          .catch();
    },
  }
};

export default result;
