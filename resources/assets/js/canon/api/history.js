import api from './../config/api';
export default {
  getUserHistories: data => new Promise((resolve, reject) => {
    let query = `vendor=${data.vendor}`;
    if (data.courseId) query += `&courseId=${data.courseId}`;
    if (data.groupId) query += `&groupId=${data.groupId}`;
    if (data.email) query += `&email=${data.email}`;
    if (data.from) query += `&from=${data.from}`;
    if (data.to) query += `&to=${data.to}`;
    api.get(`/get-user-histories?${query}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
