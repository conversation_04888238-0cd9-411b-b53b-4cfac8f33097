import api from './../config/api';
export default {
  me: () => new Promise((resolve, reject) => {
    api.get('/me')
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  loadList: data => new Promise((resolve, reject) => {
    api.get(`/get-users?vendor=${data.vendor}&page=${data.page}&perPage=${data.perPage}&courseId=${data.courseId}&groupId=${data.groupId}&email=${data.email}&progressFrom=${data.progressFrom}&progressTo=${data.progressTo}&periodId=${data.periodId}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  loadStatistic: data => new Promise((resolve, reject) => {
    api.get(`/get-statistic?vendor=${data.vendor}&page=${data.page}&perPage=${data.perPage}&courseId=${data.courseId}&email=${data.email}&progressFrom=${data.progressFrom}&progressTo=${data.progressTo}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
  exportExcel: data => new Promise((resolve, reject) => {
    api.post('/export', data)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
