import api from './../config/api';
export default {
  getResults: data => new Promise((resolve, reject) => {
    let query = `vendor=${data.vendor}`;
    if (data.courseId) query += `&courseId=${data.courseId}`;
    if (data.groupId) query += `&groupId=${data.groupId}`;
    if (data.email) query += `&email=${data.email}`;
    api.get(`/get-user-results?${query}`)
        .then(response => resolve(response.data))
        .catch(error => reject(error.response.data));
  }),
};
