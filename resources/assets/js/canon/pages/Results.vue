<template>
  <div>
    <div class="flex items-center justify-start mb-3">
      <span class="h1 mr-3"><PERSON><PERSON><PERSON></span>
      <el-select v-model="filters.courseId" @change="getList" placeholder="Select" class="mr-3" style="width: 100px;">
        <el-option label="N1" :value="17"></el-option>
        <el-option label="N2" :value="16"></el-option>
        <el-option label="N3" :value="3"></el-option>
        <el-option label="N4" :value="4"></el-option>
        <el-option label="N5" :value="5"></el-option>
        <el-option label="SC N5" :value="39"></el-option>
        <el-option label="SC N4" :value="40"></el-option>
      </el-select>
      <el-input
        class="mr-3"
        placeholder="Email"
        prefix-icon="el-icon-search"
        v-model="filters.email"
        @keyup.enter.native="getList"
        style="max-width: 300px;"
      >
      </el-input>
      <el-select v-if="groups.length" v-model="filters.groupId" @change="getList" placeholder="Chọn nhóm lớp" class="mr-3" style="width: 100px;">
        <el-option v-for="group in groups" :key="group.id" :label="group.name" :value="group.id"></el-option>
      </el-select>
    </div>
    <i class="el-icon-document-copy cursor-pointer hover:text-blue-500" @click="copyText"></i>
    <el-table
      ref="table"
      id="table"
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%; margin-top: 10px;"
      header-row-class-name="select-all"
    >
      <el-table-column
        fixed="left"
        type="index"
        label="ID"
        width="40"
        align="center"
      >
      </el-table-column>
      <el-table-column
        fixed="left"
        prop="name"
        label="Họ tên"
        width="200"
      >
        <template slot-scope="scope">
          <div class="flex">{{ scope.row.name }}<a :href="loginUser(scope.row)" target="_blank"><i class="fa fa-user-circle text-md" style="font-size: 16px; color: #96D962; margin-left: 2px"></i></a></div>
        </template>
      </el-table-column>
      <el-table-column
        fixed="left"
        prop="email"
        label="Email"
        width="200"
      >
      </el-table-column>
      <el-table-column
        v-for="exam in exams"
        :key="`column-exam-${exam.id}`"
        width="150px"
      >
        <template slot="header" slot-scope="scope">
          <div>
            <p>{{ exam.exam?.name }}</p>
            <p>{{ moment(exam.time_start).format('DD/MM') }} - {{ moment(exam.time_end).format('DD/MM') }}</p>
          </div>
        </template>
        <template slot-scope="scope">
          <div v-if="scope.row[`exam-${exam.id}`]?.total_score === undefined" class="text-red-500">Không làm</div>
          <div v-else>{{ scope.row[`exam-${exam.id}`]?.total_score || 0 }} / {{ exam.exam?.maximum_point }}</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import {mapActions, mapGetters} from 'vuex';
import moment from 'moment/moment';
moment.updateLocale('vn', {
  week : {
    dow : 1,
  }
});
export default {
  data() {
    return {
      page: 1,
      perPage: 20,
      loading: false,
      vendor: 'canon',
      moment: moment,
      tableData: [],
      filters: {
        courseId: 17,
        email: '',
        groupId: '',
      },
    };
  },
  computed: {
    ...mapGetters('ui', ['me', 'groups']),
    ...mapGetters('result', ['results', 'exams']),
  },
  methods: {
    ...mapActions('ui', ['getMe']),
    ...mapActions('result', ['getResults']),
    copyText() {
      var el = document.getElementById('table');

      var body = document.body,

        range,

        sel;

      if (document.createRange && window.getSelection) {

        range = document.createRange();

        sel = window.getSelection();

        sel.removeAllRanges();

        try {

          range.selectNodeContents(el);

          sel.addRange(range);

        } catch (e) {

          range.selectNode(el);

          sel.addRange(range);

        }

      } else if (body.createTextRange) {

        range = body.createTextRange();

        range.moveToElementText(el);

        range.select();

      }

      document.execCommand("Copy");
    },
    loginUser(row) {
      return window.location.origin + '/business/login-user/' + row.id;
    },
    async getList() {
      this.tableData = []
      this.loading = true;
      const data = {
        ...this.filters,
        vendor: this.vendor,
      }
      await this.getResults(data);
      this.tableData = this.results.map((user) => {
        this.exams.forEach(e => {
          user[`exam-${e.id}`] = user.results.filter(o => o.community_exam_id === e.id)[0]
        })
        return user;
      })
      this.loading = false;
    },
  },
  async mounted() {
    await this.getMe()
    this.filters.groupId = this.groups[0]?.id
    await this.getList();
  },
}
</script>
<style>
th.el-table__cell {
  user-select: text !important;
}
</style>
