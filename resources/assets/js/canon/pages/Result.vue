<template>
  <div>
    <div class="flex items-center justify-start mb-3">
      <span class="h1 mr-3"><PERSON><PERSON><PERSON></span>
      <el-select v-model="filters.courseId" @change="getList" placeholder="Select" class="mr-3" style="width: 100px;">
        <el-option label="N1" :value="17"></el-option>
        <el-option label="N2" :value="16"></el-option>
        <el-option label="N3" :value="3"></el-option>
        <el-option label="N4" :value="4"></el-option>
        <el-option label="N5" :value="5"></el-option>
      </el-select>
      <el-input
        class="mr-3"
        placeholder="Email"
        prefix-icon="el-icon-search"
        v-model="filters.email"
        @keyup.enter.native="getList"
        style="max-width: 300px;"
      >
      </el-input>
      <el-select v-if="groups.length" v-model="filters.groupId" @change="getList" placeholder="Chọn nhóm lớp" class="mr-3" style="width: 100px;">
        <el-option v-for="group in groups" :key="group.id" :label="group.name" :value="group.id"></el-option>
      </el-select>
      <el-date-picker
        v-model="date"
        type="daterange"
        align="right"
        unlink-panels
        range-separator="-"
        start-placeholder="Từ ngày"
        end-placeholder="Đến ngày"
        :picker-options="pickerOptions"
        format="dd/MM/yyyy"
        value-format="yyyy-MM-dd"
        @change="getList"
      >
      </el-date-picker>
    </div>
    <i class="el-icon-document-copy cursor-pointer hover:text-blue-500" @click="copyText"></i>
    <el-table
      ref="table"
      id="table"
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%; margin-top: 10px;"
      header-row-class-name="select-all"
    >
      <el-table-column
        type="index"
        label="ID"
        width="60"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="name"
        label="Họ tên"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="email"
        label="Email"
        width="180"
      >
      </el-table-column>
      <el-table-column
        prop="total"
        label="Số bài"
        width="70"
        align="center"
      >
      </el-table-column>
      <el-table-column
        v-for="date in dates"
        :key="`column-date-${date}`"
        :label="moment(date).format('DD/MM')"
        width="150px"
      >
        <template slot-scope="scope">
          <div>({{ scope.row[`date-${date}`]?.length }})</div>
          <ul v-for="item in scope.row[`date-${date}`]">
            <li>
              <i class="el-icon-check mr-1"></i>{{ item.lesson?.name }}
            </li>
          </ul>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import {mapActions, mapGetters} from 'vuex';
import moment from 'moment/moment';
moment.updateLocale('vn', {
  week : {
    dow : 1,
  }
});
export default {
  data() {
    return {
      page: 1,
      perPage: 20,
      loading: false,
      vendor: 'canon',
      moment: moment,
      tableData: [],
      date: [moment().startOf('week').format('yyyy-MM-DD'), moment().endOf('week').format('yyyy-MM-DD')],
      filters: {
        courseId: 17,
        email: '',
        groupId: '',
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '7 ngày trước',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '30 ngày trước',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: 'Tuần này',
            onClick(picker) {
              const start = moment().startOf('week').format('yyyy-MM-DD');
              const end = moment().endOf('week').format('yyyy-MM-DD');
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: 'Tuần trước',
            onClick(picker) {
              const start = moment().subtract(1, 'weeks').startOf('week').format('yyyy-MM-DD');
              const end = moment().subtract(1, 'weeks').endOf('week').format('yyyy-MM-DD');
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: 'Tháng này',
            onClick(picker) {
              const start = moment().startOf('month').format('yyyy-MM-DD');
              console.log(start)
              const end = moment().endOf('month').format('yyyy-MM-DD');
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: 'Tháng trước',
            onClick(picker) {
              const start = moment().subtract(1, 'months').startOf('month').format('yyyy-MM-DD');
              const end = moment().subtract(1, 'months').endOf('month').format('yyyy-MM-DD');
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
    };
  },
  computed: {
    ...mapGetters('ui', ['me', 'groups']),
    ...mapGetters('history', ['histories']),
    dates() {
      if (!this.date) return []
      const startDate = moment(this.date[0])
      const endDate = moment(this.date[1])
      let now = startDate.clone()
      let dates = [];

      while (now.isSameOrBefore(endDate)) {
        dates.push(now.format('yyyy-MM-DD'));
        now.add(1, 'days');
      }
      return dates;
    }
  },
  methods: {
    ...mapActions('ui', ['getMe']),
    ...mapActions('history', ['getUserHistories']),
    copyText() {
      var el = document.getElementById('table');

      var body = document.body,

        range,

        sel;

      if (document.createRange && window.getSelection) {

        range = document.createRange();

        sel = window.getSelection();

        sel.removeAllRanges();

        try {

          range.selectNodeContents(el);

          sel.addRange(range);

        } catch (e) {

          range.selectNode(el);

          sel.addRange(range);

        }

      } else if (body.createTextRange) {

        range = body.createTextRange();

        range.moveToElementText(el);

        range.select();

      }

      document.execCommand("Copy");
    },
    async getList() {
      this.tableData = []
      this.loading = true;
      const data = {
        ...this.filters,
        from: this.date ? this.date[0] : '',
        to: this.date ? this.date[1] : '',
        vendor: this.vendor,
      }
      await this.getUserHistories(data);
      this.tableData = this.histories.map((user) => {
        user.total = user.histories.length
        this.dates.forEach(date => {
          if (user.histories && user.histories.length) {
            user[`date-${date}`] = user.histories?.filter(item => moment(item.created_at).format('yyyy-MM-DD') === date)
          } else {
            user[`date-${date}`] = []
          }
          return date
        })
        return user;
      })
      this.loading = false;
    },
  },
  async mounted() {
    await this.getList();
    await this.getMe()
  },
}
</script>
<style>
th.el-table__cell {
  user-select: text !important;
}
</style>
