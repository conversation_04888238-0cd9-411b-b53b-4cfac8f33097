<template>
  <el-container style="width: 100%; height: 100%; border: 1px solid #eee">
    <el-aside width="200px" style="background-color: #fff; position: fixed; height: 100vh; overflow-y: scroll;">
      <el-menu :default-openeds="['1', '3']">
        <el-submenu index="1">
          <template slot="title"><i class="el-icon-message"></i>Báo cáo</template>
          <router-link :to="{name: 'dashboard'}">
            <el-menu-item index="1-1">Tiến trình học</el-menu-item>
          </router-link>
<!--          <router-link :to="{name: 'tracking'}">-->
<!--            <el-menu-item index="1-1">Thời gian học</el-menu-item>-->
<!--          </router-link>-->
          <router-link :to="{name: 'histories'}">
            <el-menu-item index="1-2">L<PERSON> tr<PERSON>nh học</el-menu-item>
          </router-link>
          <router-link :to="{name: 'results'}">
            <el-menu-item index="1-3">Bài kiểm tra</el-menu-item>
          </router-link>
        </el-submenu>
      </el-menu>
    </el-aside>
    <el-container style="width: calc(100% - 200px); margin-left: 200px;">
      <el-header style="text-align: right; font-size: 12px; height: 50px; line-height: 50px; background: #CC0000">
        <el-button icon="el-icon-switch-button" circle @click="logout"></el-button>
      </el-header>
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'App',
  data() {
    return {
      url: window.location.href,
    }
  },
  methods: {
    logout() {
      window.location.href = `${this.url}/logout`;
    },
  },
  mounted() {
    // this.getProducts(1);
  }
}
</script>
<style>
.el-header {
  background-color: #B3C0D1;
  color: #333;
  line-height: 60px;
}

.el-aside {
  color: #333;
}
</style>
