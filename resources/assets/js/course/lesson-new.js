// import Vue from "vue";
var player = null;

$(function () {
  if ($("#my-video").length) {
    // lưu thời gian cộng dồn thời gian xem video vào localStorage
    let totalTime = parseInt(localStorage.getItem(`totalTime-${course.id}`)) || 0;
    var intervalId = null;

    // On init load video server and quality from cookie
    const cookieVideoKey = "dungmori-video-server";
    const cookieVideoQualityKey = "dungmori-video-quality";
    const server =
      getCookie(cookieVideoKey).length > 0 ? getCookie(cookieVideoKey) : "jp";

    let quality =
      getCookie(cookieVideoQualityKey).length > 0
        ? getCookie(cookieVideoQualityKey)
        : "720p";

    let videoServer = server == "jp" ? jpServer : vnServer;

    $("#server-" + server).addClass("active");
    $("#video-quality-" + quality).addClass("active");

    const isVideoPlaying = (video) =>
      !!(
        video.currentTime() > 0 &&
        !video.paused() &&
        !video.ended() &&
        video.readyState() > 2
      );

    let previousPercent = 0;

    const timeSaveLessonProgress = setInterval(() => {
      const percetage = Math.round(
        (player.currentTime() / player.duration()) * 100
      );
      if (isVideoPlaying(player) && percetage > previousPercent) {
        previousPercent = percetage;
        $.post(window.location.origin + "/khoa-hoc/upsert-lesson-progress", {
          lesson_id: currentLesson.id,
          video_progress: percetage,
        }).then(res => {
          if (res.show_popup_done_stage) {
            $("#modalDoneProgressGroup").modal("show");
          }
        });
      }
    }, 10000); // 10s

    var options, currentTime;
    options = {
      playbackRates: [0.75, 1, 1.25, 1.5, 1.75, 2],
      preload: "auto",
      controlBar: {
        children: [
          "playToggle",
          "progressControl",
          "volumePanel",
          "qualitySelector",
          "playbackRateMenuButton",
          "fullscreenToggle",
        ],
      },
    };

    function loadLastTime() {
      const lastTime = getCookie(`video-${videoName}`);
      if (currentTime) {
        player.currentTime(currentTime);
        return;
      }
      if (lastTime) {
        player.currentTime(lastTime);
      }
    }

    function loadSrc() {
      if (videoName) {
        const src = `${videoServer}/${quality}/${videoName}/index.m3u8`;
        $.ajax({
          url: "/video/get-link?url=" + src,
          type: "GET",
          success: function (res) {
            player.src({
              src: res,
              type: "application/x-mpegURL",
              withCredentials: false,
            });
          },
        });
      }
    }

    player = videojs("my-video", options);

    // Bắt sự kiện play
    player.on('play', () => {
      intervalId = setInterval(() => {
        totalTime += 1;
        localStorage.setItem(`totalTime-${course.id}`, totalTime);
      }, 1000);
    });

    // Bắt sự kiện pause
    player.on("pause", () => {
      clearInterval(intervalId);
    });

    // function formatTime(seconds) {
    //   const minutes = Math.floor(seconds / 60);
    //   const remainingSeconds = Math.floor(seconds % 60);
    //   return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
    // }

    let is_pass_lesson = 0;
    const setAchievement = async () => {
      let count_lesson_complete = 0;
      await $.post(window.location.origin + "/khoa-hoc/set-achievement", {
        lesson_id: currentLesson.id,
        course_id: course.id,
        progress_before: currentLesson.percent,
      }).then((res) => {
        count_lesson_complete = res.data.lessonComplete;
      });

      return count_lesson_complete;
    };

    const saveProgress = async () => {
      let currentTime = player.currentTime();
      let duration = player.duration();

      let viewPercentage = Math.round((currentTime / duration) * 100);

      await $.post(
        window.location.origin + "/khoa-hoc/upsert-lesson-progress",
        {
          lesson_id: currentLesson.id,
          video_progress: viewPercentage,
          course_id: course.id,
        }
      ).then((res) => {
        if (res.show_popup_done_stage) {
          $("#modalDoneProgressGroup").modal("show");
        }
      });
    };

    const endedVideo = async () => {
      await saveProgress();
      let count_lesson_complete = await setAchievement();
    };
    $("#button_tab_exercise").on("click", function () {
      player.pause(); // Tạm dừng video
    });

    // Bắt sự kiện ended (khi video kết thúc)
    player.on("ended", () => {
      clearInterval(intervalId);
      endedVideo();

      player.poster(
        "https://dungmori.com/cdn/lesson/default/1733795973_120450165_85801.jpeg"
      );
    });

    loadSrc();

    player.on("loadedmetadata", function () {
      loadLastTime();
    });

    var isHasPause = false;
    player.on("timeupdate", function () {
      setCookie(`video-${videoName}`, player.currentTime());
      if (parseInt(localStorage.getItem(`totalTime-${course.id}`)) === 180 && !isHasPause) {
        isHasPause = true;
        player.pause();
      }
    });

    $(document).on("click", ".server-video", function () {
      $(this).addClass("active").siblings().removeClass("active");
      setCookie(cookieVideoKey, $(this).data("index"));
      videoServer = $(this).data("index") == "jp" ? jpServer : vnServer;
      currentTime = player.currentTime();
      loadSrc();
      player.play();
    });

    $(document).on("click", ".quality-video", function () {
      $(this).addClass("active").siblings().removeClass("active");
      setCookie(cookieVideoQualityKey, $(this).data("index"));
      quality = $(this).data("index");
      currentTime = player.currentTime();
      loadSrc();
      player.play();
    });
  }

  $(".logo3").attr("href", "javascript:void(0)");
  $(document).on("click", ".logo3", function () {
    setRedirectModal("/");
  });

  async function setRedirectModal(forceUrl = null) {
    let courseUrl = "/khoa-hoc/" + course.SEOurl;
    if (forceUrl) {
      courseUrl = forceUrl;
    }
    if (!userLesson) {
      window.location.href = courseUrl;
      return;
    }
    await $.get(
      window.location.origin + "/get-lesson-percent/" + currentLesson.id
    ).then((res) => {
      if (currentLesson.type !== "last_exam" && currentLesson.require && res < 85) {
        $("#modal-confirm-leave")
          .find(".confirm-leave__title")
          .text("Bạn muốn rời đi?");
        $("#modal-confirm-leave").find(".btn-confirm-leave").text("Thoát");
        $("#modal-confirm-leave").modal("show");
        $("#modal-confirm-leave").data("url", courseUrl);
      } else {
        window.location.href = courseUrl;
      }
    });
  }

  $(document).on("click", ".btn-download", function () {
    $(this).find(".download-icon").addClass("downloaded");
  });

  window.addEventListener("beforeunload", function (event) {
    clearInterval(intervalId);
  });
});

$(function () {
  setLinksToOpenInNewTab("lesson-content");
});

function setLinksToOpenInNewTab(elementId) {
  const links = document.querySelectorAll(`#${elementId} a`);

  links.forEach((link) => {
    if (link.href.includes(window.location.origin)) {
      return;
    }
    link.setAttribute("target", "_blank");
  });
}

global.setupTutorial = function () {
  if (document.getElementById("current-lesson")) {
    const currentLessonElement = document.getElementById("current-lesson");
    currentLessonElement.scrollIntoView({
      behavior: "auto",
      block: "center",
      inline: "center",
    });
    const positionTut1 = currentLessonElement.getBoundingClientRect().top - 210;
    document.getElementById("tut1-lesson").style.top = `${positionTut1}px`;
    const clone = currentLessonElement.cloneNode(true);
    clone.id = "tut1-content-clone";
    $("#tut1-content-2").html(clone);
  }
  if (document.getElementById("tabList")) {
    const tabElement = document.getElementById("tabList");
    $(".tablist-clone").html(tabElement.cloneNode(true));
  }
};

$(document).ready(function () {
  $("#button_redirect_tab_exercise").on("click", function () {
    $("#button_tab_exercise").click();
  });
});

const TYPE_LESSON = {
  FILL_IN_BLANK: 13,
  MULTIPLE_CHOICE: 3,
  SENTENCE_JUMBLE: 14,
  WORD_PAIR: 15,
  SPEAKING: 16,
};

const STEP_TYPE_SPEAK = {
  DEFAULT: 0,
  SPEAK_DEFAULT: 1,
  SPEAK_SLOW: 2,
  RECORD_SLOW: 3,
  RECORD_DEFAULT: 4,
  FREEDOM: 5
};

const optionsColorWaveSurferDefault = {
  waveColor: "#B3B3B3",
  progressColor: "#4E87FF",
};
const optionsColorWaveSurferDisable = {
  waveColor: "#B3B3B3",
  progressColor: "#838383",
};
const optionsColorWaveRecord = {
  waveColor: "#B3B3B3",
  progressColor: "#07403F",
};

const constStatusSpeak = {
  audioActiveTypeSpeaking: "default",
  stepViewSpeaking: STEP_TYPE_SPEAK.DEFAULT, // speakDefault: run file mp3 default -> speakSlow: run file mp3 slow -> recordSlow: record slow -> recordDefault: record default -> freedom: freedom
  recordSlowStatus: null, // null: default; mic: dang ghi am; recorded: da ghi am xong
  recordDefaultStatus: null, // null: default; mic: dang ghi am; recorded: da ghi am xong
  recordingTimeout: null,
  recordDefaultBlob: null,
  recordSlowBlob: null,
  autoNextRecordSlow: 1,  // Tự động nhảy từ RECORD_SLOW
  autoNextRecordDefault: 1, // Tự động nhảy từ RECORD_DEFAULT
  autoNextStepSetInterval: null,
  autoNextStepSetTimeout: null,
  currentSpeaking: null, // null, default, slow
};

// Vue app for lesson list
let apiLessonExercise = axios.create({
  baseURL: "/khoa-hoc",
  headers: {
    // "Content-Type": "application/json",
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
    "Content-Type": "multipart/form-data",
  },
  withCredentials: true,
});

import WaveSurfer from "wavesurfer.js";
import RecordPlugin from "wavesurfer.js/plugins/record";
import {createFFmpeg} from "@ffmpeg/ffmpeg";
import debounce from "lodash/debounce";

const ffmpeg = createFFmpeg({
  corePath: "/ffmpeg-core.js", // Đường dẫn chính xác
  log: true
});
// const ffmpeg = createFFmpeg({ log: true });

new Vue({
  el: "#lesson-basic-container",
  data: {
    wavesurferDefaultTypeSpeaking: null,
    wavesurferSlowTypeSpeaking: null,
    recordSlowTextKaraoke: null,
    recordDefaultTextKaraoke: null,
    isPlayingTypeSpeaking: false,
    currentTimeTypeSpeaking: "00:00",
    durationTypeSpeaking: "00:00",
    recordDefault: null,
    recordSlow: null,
    recordDefaultMic: null,
    recordSlowMic: null,
    statusSpeak: constStatusSpeak,
    stepTypeSpeak: STEP_TYPE_SPEAK,
    currentPlaying: null, // theo doi am thanh dang phat
    micStreams: new Map(), // luu MediaStream cho tung componentData
    intervalRecordProgress: null,
    audioContext: null,
    analyser: null,
    dataArray: null,
    animationFrameId: null,

    currentLesson: currentLesson,
    indexCurrentLesson: 1,
    userLesson: userLesson,
    isUnlock: isUnlock,
    lesson: lesson,
    course: course,
    dataLesson: dataLesson,
    currentAnswer: currentAnswer,
    idCurrentAnswer: idCurrentAnswer,
    duration: 0, // Tổng thời gian của file âm thanh (giây)
    timeRemaining: 0, // Thời gian còn lại (giây)
    timeDurationCurrentAnswer: "",
    isPlaying: false,
    intervalId: null,
    audio: null,
    isCheckAnswer: false,
    statusAnswer: "doing", // doing, test, next
    isShowExplainButton: false,
    isShowExplanationPopupFullScreen: false,
    isAnswerSuccess: true,
    isShowExplainIconAudio: true,
    playingAnswerId: null, // ID của đáp án đang được phát âm thanh
    userResult: {
      lesson_id: dataLesson.id,
      total_grade: dataLesson.total_marks,
      grade_pass: dataLesson.pass_marks,
      grade: 0,
      data: {},
      course_id: dataLesson.course_id,
      correct_answer: 0,
      total_answer: dataLesson.component.length,
      ratio_correct_answer: 0,
      text_minimum_correct: `Điểm đạt: ≥85% (${Math.ceil(
        0.85 * dataLesson.component.length
      )}/${dataLesson.component.length} câu)`,
      isExerciseSpeak: true,
    },
    dialogUserResultVisible: false,
    dialogInstruction: false,
    windowWidth: screen.width,
    tabActive: "tabLesson",
    disableInput: false,
    examStage: 0,
    isActive: false,
    isFirstTabExercise: true,
    pairColors: ["#CFF7FF", "#FFF8C7", "#E2E3FC", "#FFDFC9", "#FCDAFF"],
    pairs: {},
    pairCounter: 0,
    currentPairIndex: 0,
    shuffledPairs: [],
    arrUserResultSentenceJumble: [],
    showTooltip: 0,
    timeout: null,
    audioInstances: new Map(), // Lưu các instance audio
    audioTimes: {}, // Lưu thời gian audio
    recordInstances: {}, // luu link mp3 ghi am
    dialogVisible: false,
    initTime: 0,
    showPopupRate: false,
    intervalCheckTimeWatchVideo: null,
  },
  created() {
    let component = this.lesson.components.find((item) => {
      return [
        TYPE_LESSON.FILL_IN_BLANK,
        TYPE_LESSON.MULTIPLE_CHOICE,
        TYPE_LESSON.WORD_PAIR,
        TYPE_LESSON.SENTENCE_JUMBLE,
        TYPE_LESSON.SPEAKING,
      ].includes(item.type);
    });

    if (component && !(this.lesson.documents.length || this.lesson.video)) {
      this.tabActive = "tabExercise";
    }

    this.shuffledPairs = this.dataLesson.component
      .filter((item) => item.type === TYPE_LESSON.WORD_PAIR)
      .map((item) => {
        return {
          id: item.id,
          question: {
            left: _.shuffle(item.value.question.left),
            right: _.shuffle(item.value.question.right),
          },
        };
      });
    this.userResult.shuffledPairs = this.shuffledPairs;
    localStorage.setItem("tab-active-lesson", this.tabActive);
    localStorage.setItem(
      `userResult-lesson-${this.dataLesson.id}`,
      JSON.stringify(this.userResult)
    );

    let params = new URLSearchParams(document.location.search);
    let tab_active = params.get("tab_active");
    if (tab_active && ["tabExercise", "tabLesson"].includes(tab_active)) {
      this.tabActive = tab_active;
    }
  },
  components: {
    container: require("./components/Container.vue").default,
    LessonExam: require("./components/LessonExam.vue").default,
    FlashCard: require("./components/FlashCard.vue").default,
    ImageWithFallback: require("./components/ImageWithFallback.vue").default,
    PopupRate: require("./components/PopupRate.vue").default,
  },
  watch: {
    tabActive(newValue, oldValue) {
      localStorage.setItem("tab-active-lesson", newValue);
      if (newValue === "tabExercise") {
        this.isFirstTabExercise = false;
        this.showTooltip += 1;
        this.checkShowPopupInstructionSpeaking();
      }

      if (newValue === "tabExercise" && player) {
        player.pause();
      }
    },
    playingAnswerId(newValue, oldValue) {
      localStorage.setItem("playingAnswerId", newValue);
    },
    dialogUserResultVisible(newValue, oldValue) {
      if (!newValue) {
        this.resetAudioState();
      } else {
        this.$nextTick(() => {
          setTimeout(() => this.renderResultWaveSurfers(), 1000);
        });
      }
    },
    showTooltip(newValue, oldValue) {
      if (newValue === 1) {
        this.timeout = setTimeout(() => {
          this.showTooltip += 1;
        }, 5000);
      }
    },
    pairs: {
      handler(newValue, oldValue) {
        // nếu chưa có pair nào được chọn, trả về 0
        if (!newValue.hasOwnProperty(Number(this.idCurrentAnswer))) {
          this.currentPairIndex = 0;
        } else if (
            newValue[Number(this.idCurrentAnswer)].some(
                (pair) =>
                    (pair.left !== null && pair.right === null) ||
                    (pair.left === null && pair.right !== null)
            )
        ) {
          this.currentPairIndex = newValue[
              Number(this.idCurrentAnswer)
              ].findIndex(
              (pair) =>
                  (pair.left !== null && pair.right === null) ||
                  (pair.left === null && pair.right !== null)
          );
        }
        // nếu có pair được chọn nhưng tất cả nều null hoặc rỗng
        else if (
            newValue[Number(this.idCurrentAnswer)].every(
                (pair) => pair.left === null && pair.right === null
            )
        ) {
          this.currentPairIndex = 0;
        }

        // nếu tất cả pair được chọn và tất cả pair đều khác null
        else if (
            newValue[Number(this.idCurrentAnswer)].every(
                (pair) => pair.left !== null && pair.right !== null
          )
        ) {
          this.currentPairIndex = newValue[Number(this.idCurrentAnswer)].length;
        } else if (
          newValue[Number(this.idCurrentAnswer)].some(
            (pair) => pair.left === null || pair.right === null
          )
        ) {
          this.currentPairIndex = newValue[
            Number(this.idCurrentAnswer)
          ].findIndex((pair) => pair.left === null || pair.right === null);
        } else {
          this.currentPairIndex = 0;
        }
      },
      deep: true,
    },
    "currentAnswer.value.audio": {
      handler() {
        if (this.currentAnswer) {
          this.timeRemaining = 0;
          this.getDurationAudio();
        }
      },
      immediate: true
    },
    statusSpeak: {
      handler(newValue, oldValue) {
      },
      deep: true
    },
    "statusSpeak.autoNextRecordSlow"(newVal) {
      if (!newVal) {
        clearTimeout(this.statusSpeak.autoNextStepSetTimeout);
        clearInterval(this.statusSpeak.autoNextStepSetInterval);
        $(".noti-auto-next-step").addClass("hidden");
      }
    },
    "statusSpeak.autoNextRecordDefault"(newVal) {
      if (!newVal) {
        clearTimeout(this.statusSpeak.autoNextStepSetTimeout);
        clearInterval(this.statusSpeak.autoNextStepSetInterval);
        $(".noti-auto-next-step").addClass("hidden");
      }
    },
    audioTimes: {
      handler(newValue, oldValue) {
      },
      deep: true
    },
    currentPlaying(newVal, oldVal) {
    }
  },
  methods: {
    openCommentTab(eventData = {}) {

      // Tạo dữ liệu để truyền đi
      const data = {
        handle: 'open',
        ...eventData
      };

      // Tạo và gửi sự kiện tùy chỉnh
      const event = new CustomEvent('open-comment-tab', { detail: data });
      document.dispatchEvent(event);
    },
    initTimer() {
      const vm = this;
      this.initTime = Date.now();
      document.addEventListener("visibilitychange", function () {
        if (document.visibilityState === "hidden") {
          vm.saveTimer();
        } else {
          vm.initTime = Date.now();
        }
      });
    },
    saveTimer() {
      const checkpointTime = Date.now() - this.initTime;
      const data = new FormData();
      data.append("joinedTime", this.initTime);
      data.append("time", checkpointTime);
      data.append("lessonId", this.lesson.id);
      data.append("_token", $('meta[name="csrf-token"]').attr("content"));
      navigator.sendBeacon("/save-timer", data);
    },
    resetAudioState() {
      this.stopAllAudios();
    },

    renderResultWaveSurfers() {
      const $defaultWaveform = document.querySelector(".default-waveform-result");
      if (!$defaultWaveform || $defaultWaveform.children.length > 0) return;

      this.audioInstances.forEach((instance, questionId) => {
        if (instance.type === TYPE_LESSON.SPEAKING) {
          this.setupSpeakingResultWaveSurfers(questionId, instance);
        }
      });
    },

    setupSpeakingResultWaveSurfers(questionId, instance) {
      const waveConfigs = [
        { key: "slowWave", ref: `${questionId}-slow-waveform-result`, url: instance.link_audio_slow },
        { key: "defaultWave", ref: `${questionId}-default-waveform-result`, url: instance.link_audio_default },
        { key: "recordSlowWave", ref: `${questionId}-recordSlow-result`, url: URL.createObjectURL(instance.recordSlow), options: { height: 30, progressColor: "#07403F" } },
        { key: "recordDefaultWave", ref: `${questionId}-recordDefault-result`, url: URL.createObjectURL(instance.recordDefault), options: { height: 30, progressColor: "#07403F" } },
      ];

      waveConfigs.forEach(({ key, ref, url, options }) => {
        instance[key] = this.createWaveSurfer(ref, null, url, options);
      });

      this.setupWaveSurferResultEvents(questionId, instance);
    },

    setupWaveSurferResultEvents(questionId, instance) {
      const { defaultWave, slowWave, recordSlowWave, recordDefaultWave } = instance;

      defaultWave.on("ready", () => this.$set(this.audioTimes[questionId], "duration", this.formatTime(defaultWave.getDuration())));
      defaultWave.on("play", () => slowWave.setTime(0));
      defaultWave.on("audioprocess", () => this.updateAudioTime(questionId, defaultWave));
      defaultWave.on("finish", () => {
        slowWave.playPause();
        this.currentPlaying = { id: questionId, type: "questionResult", componentData: "slowWave" };
      });

      slowWave.on("audioprocess", () => this.updateAudioTime(questionId, slowWave));
      slowWave.on("finish", () => this.currentPlaying = null);
      recordSlowWave.on("ready", () => $(`.recordSlowTime-${questionId}`).text(this.formatTime(recordSlowWave.getDuration())));
      recordDefaultWave.on("ready", () => $(`.recordDefaultTime-${questionId}`).text(this.formatTime(recordDefaultWave.getDuration())));
    },

    updateAudioTime(questionId, waveSurfer) {
      this.$set(this.audioTimes[questionId], "formattedTime", this.formatTime(waveSurfer.getCurrentTime()));
      this.$set(this.audioTimes[questionId], "duration", this.formatTime(waveSurfer.getDuration()));
    },

    toggleMp3(id, type, componentData = null) {
      const audioInstance = this.getAudioInstance(id, type, componentData);
      const current = this.currentPlaying;
      if (this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_SLOW) {
        this.statusSpeak.autoNextRecordSlow = 0;
      }
      if (this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_DEFAULT) {
        this.statusSpeak.autoNextRecordDefault = 0;
      }
      if (!audioInstance) {
        return;
      }

      if (!current) {
        this.playAudio(audioInstance);
        this.currentPlaying = { id, type, componentData };
        return;
      }

      if (current.id === id && current.type === type && current.componentData === componentData) {
        this.stopAudio(audioInstance);
        this.currentPlaying = null;
        return;
      }

      if (componentData === "wavesurferDefaultTypeSpeaking" && this.currentPlaying.componentData === "wavesurferSlowTypeSpeaking") {
        this.stopAllAudios();
        this.currentPlaying = null;
        return;
      }

      this.stopAudio(this.getAudioInstance(current.id, current.type, current.componentData));
      this.playAudio(audioInstance);
      this.currentPlaying = { id, type, componentData };
    },

    stopAllAudios() {
      const audioKeys = ["audio", "explain_mp3", "recordSlowWave", "recordDefaultWave", "slowWave", "defaultWave"];
      const waveSurferKeys = ["recordSlow", "recordDefault", "wavesurferSlowTypeSpeaking", "wavesurferDefaultTypeSpeaking"];

      this.audioInstances.forEach(instance => audioKeys.forEach(key => this.stopAudio(instance[key])));
      waveSurferKeys.forEach(key => this.stopAudio(this[key]));
      this.currentPlaying = null;

      // Dừng MediaStream nếu còn
      this.micStreams.forEach(stream => stream.getTracks().forEach(track => track.stop()));
      this.micStreams.clear();
    },

    getAudioInstance(id, type, componentData) {
      if ((typeof id === "string" && (id.includes("_flashcard_")) || !this.audioInstances.has(id))) {
        let audioUrl = "";

        // componentData la url tu fc)
        if (componentData && typeof componentData === "string" && (componentData.startsWith("http") || componentData.startsWith("/"))) {
          audioUrl = componentData;
        }

        if (audioUrl && !this.audioInstances.has(id)) {
          const flashcard = new Audio(audioUrl);
          this.audioInstances.set(id, { flashcard });
        }
      }

      const instance = this.audioInstances.get(id);
      const instances = {
        flashcard: instance?.flashcard,
        question: instance?.audio,
        option: instance?.audio,
        explain_mp3: instance?.explain_mp3,
        record: this[componentData],
        recordResult: instance?.[`${componentData}Wave`],
        questionResult: instance?.[componentData],
        waveQuestion: this[componentData],
      };

      return instances[type] || null;
    },

    playAudio(instance) {
      if (instance) instance.play();
    },

    stopAudio(instance) {
      if (!instance) return;
      if (instance instanceof Audio) {
        instance.pause();
        instance.currentTime = 0;
      } else if (typeof instance.setTime === "function") {
        instance.pause();
        instance.setTime(0);
      }
    },

    getWaveSurferConfig(container, { waveColor = "#B3B3B3", progressColor = "#4E87FF", barWidth = 2, barGap = 3, height = 40, barRadius = 100, interact = false } = {}) {
      return {
        container,
        waveColor,
        progressColor,
        barWidth,
        barGap,
        height,
        barRadius,
        responsive: true,
        cursorWidth: 0,
        interact,
        width: "100%",
        normalize: false,
      };
    },

    createWaveSurfer(ref, componentData = null, url = null, options = {}) {
      const container = Array.isArray(this.$refs[ref]) ? this.$refs[ref][0] : this.$refs[ref];
      if (!container) return null;

      if (componentData && this[componentData]) this[componentData].destroy();
      const waveSurfer = WaveSurfer.create(this.getWaveSurferConfig(container, options));
      if (url) waveSurfer.load(url);
      if (componentData) this[componentData] = waveSurfer;
      return waveSurfer;
    },

    initWaveSurfer(url = null) {
      const baseUrl = `${window.location.origin}/cdn/audio/`;
      const urlDefault = url || `${baseUrl}${this.currentAnswer.value.audio_speaking_speed_default}`;
      const urlSlow = url || `${baseUrl}${this.currentAnswer.value.audio_speaking_speed_slow}`;

      this.initWaveSurferDefault(urlDefault);
      this.initWaveSurferSlow(urlSlow);
      this.setupWaveSurferEvents(this.currentAnswer);
      this.initWaveRecord("recordSlow");
      this.initWaveRecord("recordDefault");
      this.updateKaraokeText();
      this.updateAnswerStatus();
    },

    initWaveSurferDefault(url) {
      this.createWaveSurfer(`${this.currentAnswer.id}-default-waveform`, "wavesurferDefaultTypeSpeaking", url);
    },

    initWaveSurferSlow(url) {
      this.createWaveSurfer(`${this.currentAnswer.id}-slow-waveform`, "wavesurferSlowTypeSpeaking", url);
    },

    setupWaveSurferEvents(currentAnswer) {
      const { wavesurferDefaultTypeSpeaking: defaultWave, wavesurferSlowTypeSpeaking: slowWave } = this;

      defaultWave.on("ready", () => this.durationTypeSpeaking = this.formatTime(defaultWave.getDuration()));
      defaultWave.on("play", () => slowWave.setTime(0));
      defaultWave.on("audioprocess", () => {
        this.currentTimeTypeSpeaking = this.formatTime(defaultWave.getCurrentTime());
        this.durationTypeSpeaking = this.formatTime(defaultWave.getDuration());
      });
      defaultWave.on("finish", () => {
        this.statusSpeak.audioActiveTypeSpeaking = "slow";
        this.currentTimeTypeSpeaking = "00:00";
        slowWave.playPause();
        this.durationTypeSpeaking = this.formatTime(slowWave.getDuration());
        if (this.statusSpeak.stepViewSpeaking <= STEP_TYPE_SPEAK.SPEAK_DEFAULT) {
          this.statusSpeak.stepViewSpeaking = STEP_TYPE_SPEAK.SPEAK_SLOW;
        }
        this.statusSpeak.currentSpeaking = "slow";
        this.currentPlaying = { id: currentAnswer.id, type: "waveQuestion", componentData: "wavesurferSlowTypeSpeaking" };
      });

      slowWave.on("audioprocess", () => {
        this.currentTimeTypeSpeaking = this.formatTime(slowWave.getCurrentTime());
        this.durationTypeSpeaking = this.formatTime(slowWave.getDuration());
      });
      slowWave.on("finish", () => {
        this.statusSpeak.audioActiveTypeSpeaking = "default";
        this.currentTimeTypeSpeaking = "00:00";
        this.isPlayingTypeSpeaking = false;
        if (this.statusSpeak.stepViewSpeaking <= STEP_TYPE_SPEAK.SPEAK_SLOW) {
          this.statusSpeak.stepViewSpeaking = STEP_TYPE_SPEAK.RECORD_SLOW;
        }
        this.setupAutoNextStep();
        this.statusSpeak.currentSpeaking = "done";
        this.currentPlaying = null;
      });
    },

    initWaveRecord(componentData) {
      $(`.${componentData}Time`).text("00:00");
      const wavesurferRecord = this.createWaveSurfer(
          `mic-${this.currentAnswer.id}-${componentData}Mic`,
          null,
          null,
          { waveColor: "#f8b079", progressColor: "#EF6D13", height: 20, interact: true }
      );
      if (!wavesurferRecord) return;

      this[`${componentData}Mic`] = wavesurferRecord.registerPlugin(RecordPlugin.create({
        renderRecordedAudio: false,
        scrollingWaveform: false,
        continuousWaveform: true,
        continuousWaveformDuration: 30,
      }));

      this.setupRecordEvents(componentData);
      this.setupKaraoke(componentData);
      document.getElementById("checkButton")?.removeAttribute("disabled");
    },

    setupRecordEvents(componentData) {
      const mic = this[`${componentData}Mic`];
      let timeDuration;

      mic.on("record-start", () => {
        ["wavesurferSlowTypeSpeaking", "wavesurferDefaultTypeSpeaking"].forEach(key => this[key]?.setOptions(optionsColorWaveSurferDisable));
        if (componentData === "recordDefault" && this.recordSlow) this.recordSlow.setOptions(optionsColorWaveSurferDisable);
        if (componentData === "recordSlow" && this.recordDefault) this.recordDefault.setOptions(optionsColorWaveSurferDisable);
      });

      mic.on("record-end", blob => {
        this.statusSpeak[`${componentData}Blob`] = blob;
        document.querySelector(".noti-remind")?.classList.add("hidden");

        const recordedUrl = URL.createObjectURL(blob);
        this.createWaveSurfer(`mic-${this.currentAnswer.id}-${componentData}`, componentData, recordedUrl, { height: 20, progressColor: "#07403F" });

        this.setupPlaybackEvents(componentData);
        this.updateRecordStatus(componentData, timeDuration);
        clearInterval(this.intervalRecordProgress);
        this.intervalRecordProgress = null;

        setTimeout(() => URL.revokeObjectURL(recordedUrl), 1000);
      });
    },

    setupPlaybackEvents(componentData) {
      const wave = this[componentData];
      wave.on("play", () => {
        ["wavesurferSlowTypeSpeaking", "wavesurferDefaultTypeSpeaking"].forEach(key => this[key]?.setOptions(optionsColorWaveSurferDisable));
        if (componentData === "recordDefault" && this.recordSlow) this.recordSlow.setOptions(optionsColorWaveSurferDisable);
        if (componentData === "recordSlow" && this.recordDefault) this.recordDefault.setOptions(optionsColorWaveSurferDisable);
      });

      wave.on("pause", () => {
        ["wavesurferSlowTypeSpeaking", "wavesurferDefaultTypeSpeaking"].forEach(key => this[key]?.setOptions(optionsColorWaveSurferDefault));
        if (componentData === "recordDefault" && this.recordSlow) this.recordSlow.setOptions(optionsColorWaveRecord);
        if (componentData === "recordSlow" && this.recordDefault) this.recordDefault.setOptions(optionsColorWaveRecord);
      });

      wave.on("finish", () => this.currentPlaying = null);
    },

    updateRecordStatus(componentData, timeDuration) {
      this.statusSpeak[`${componentData}Status`] = "recorded";

      if (componentData === "recordDefault" && this.statusSpeak.stepViewSpeaking <= STEP_TYPE_SPEAK.RECORD_DEFAULT) {
        this.statusSpeak.stepViewSpeaking = STEP_TYPE_SPEAK.FREEDOM;
      } else if (componentData === "recordSlow" && this.statusSpeak.stepViewSpeaking <= STEP_TYPE_SPEAK.RECORD_SLOW) {
        this.statusSpeak.stepViewSpeaking = STEP_TYPE_SPEAK.RECORD_DEFAULT;
        this.setupAutoNextStep();
      }

      if (this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.FREEDOM) {
        document.querySelector(".noti-done-record")?.classList.remove("hidden");
      }

      document.querySelector(`.runText-${componentData}-${this.currentAnswer.id}`)?.classList.remove("active");
      clearTimeout(this.statusSpeak.recordingTimeout);

      ["wavesurferSlowTypeSpeaking", "wavesurferDefaultTypeSpeaking"].forEach(key => this[key]?.setOptions(optionsColorWaveSurferDefault));
      ["recordSlow", "recordDefault"].forEach(key => this[key]?.setOptions(optionsColorWaveRecord));

      const instance = this.audioInstances.get(this.currentAnswer.id) || { componentPlaying: "defaultWave" };
      instance[componentData] = this.statusSpeak[`${componentData}Blob`];
      instance[`${componentData}Duration`] = timeDuration;
      this.audioInstances.set(this.currentAnswer.id, instance);

      this.resetTextKaraoke(componentData);
    },

    setupKaraoke(componentData) {
      const karaokeText = document.getElementById(`runText-${componentData}-${this.currentAnswer.id}`);
      if (!karaokeText || !this.currentAnswer.value.title_question) return;

      karaokeText.style.whiteSpace = "normal"; // cho phep xuong dong
      karaokeText.style.wordWrap = "break-word"; // ngat tu qua dai
      karaokeText.style.display = "inline-block"; // giu layout nhung wrap dc
      karaokeText.style.width = "100%";

      const text = new DOMParser()
          .parseFromString(this.currentAnswer.value.title_question, "text/html")
          .querySelector("p")?.textContent || "";

      const splitText = (inputText) => {
        const words = inputText.split(" ").filter(word => word.trim());
        const maxLength = 20; // do dai toi da neu khong co dau cach
        const result = [];

        words.forEach(word => {
          if (word.length <= maxLength) {
            result.push(word);
          } else {
            // chia nho tu neu ko co dau cach
            for (let i = 0; i < word.length; i += maxLength) {
              result.push(word.slice(i, i + maxLength));
            }
          }
        });
        return result;
      };

      const textSegments = splitText(text);
      karaokeText.innerHTML = textSegments
          .map(segment => `<span data-text="${segment}">${segment}</span>`)
          .join(" ");

      const spans = karaokeText.querySelectorAll("span");
      if (this[`${componentData}TextKaraoke`]) {
        this[`${componentData}TextKaraoke`].kill(); // huy timeline
      }
      this[`${componentData}TextKaraoke`] = gsap.timeline({ paused: true });

      const waveSurfer = componentData === "recordSlow" ? this.wavesurferSlowTypeSpeaking : this.wavesurferDefaultTypeSpeaking;
      waveSurfer.on("ready", () => {
        const totalDuration = waveSurfer.getDuration() || 10;
        const slideDuration = totalDuration / spans.length;
        spans.forEach((span, index) => {
          this[`${componentData}TextKaraoke`].to(
              span,
              { "--width": "100%", duration: slideDuration, ease: "linear" },
              index * slideDuration
          );
        });
      });
    },
    updateKaraokeText() {
      const textElement = document.querySelector(`.runText-${this.currentAnswer.id} > p`);
      if (textElement) textElement.dataset.text = document.querySelector(`.runText-${this.currentAnswer.id}`).dataset.text;
    },

    updateAnswerStatus() {
      const currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
      this.statusAnswer = currentIndex >= 0 && currentIndex < this.dataLesson.component.length - 1 ? "next" : "end";
    },

    formatTime(time) {
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
    },

    async runRecord(componentData, type_action = null) {
      const micComponent = `${componentData}Mic`;

      if (this[componentData]) {
        this[componentData].destroy();
        this[componentData] = null;
      }

      if (this[micComponent]?.isRecording() || this[micComponent]?.isPaused()) {
        this.stopRecording(componentData);
        this.stopPulseEffect();
        return;
      }

      this.stopAllAudios();
      clearTimeout(this.statusSpeak.recordingTimeout);

      if (type_action === "click" && this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_SLOW) {
        this.statusSpeak.autoNextRecordSlow = 0;
      }
      if (type_action === "click" && this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_DEFAULT) {
        this.statusSpeak.autoNextRecordDefault = 0;
      }

      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        this.micStreams.set(componentData, stream); // Lưu stream
        this.statusSpeak[`${componentData}Status`] = "mic";

        this.setupPulseEffect(stream);

        this.startRecording(componentData, micComponent);
      } catch (err) {
        console.error("Micro error:", err);
        alert("Vui lòng cấp quyền micro và tải lại trang!");
      }
    },

    startRecording(componentData, micComponent) {
      this[micComponent].startRecording().then(() => {
        $(".pulse-container").removeClass("hidden");

        const elements = {
          text: document.querySelector(`.runText-${componentData}-${this.currentAnswer.id}`),
          mic: document.querySelector(`.mic-${this.currentAnswer.id}-${componentData}Mic`),
          remind: document.querySelector(".noti-remind"),
          done: document.querySelector(".noti-done-record"),
        };

        if (elements.text) elements.text.classList.add("active");
        if (elements.mic) elements.mic.classList.remove("dot-container");
        if (elements.remind) elements.remind.classList.remove("hidden");
        if (elements.done) elements.done.classList.add("hidden");

        this.statusSpeak.recordingTimeout = setTimeout(() => {
          this.stopRecording(componentData);
          this.showNotActionPopup();
          this.statusSpeak.autoNextRecordDefault = 0;
        }, 30000);

        let time = 0;
        let timeDuration = this.formatTime(time);
        $(`.${componentData}Time`).text(timeDuration);
        this.intervalRecordProgress = setInterval(() => {
          time += 1;
            let timeDuration = this.formatTime(time);
            $(`.${componentData}Time`).text(timeDuration);
        }, 1000);

        if (this[`${componentData}TextKaraoke`]) this[`${componentData}TextKaraoke`].restart();
      }).catch(err => console.error("Start recording failed:", err));
    },

    stopRecording(componentData) {
      $(".pulse-container").addClass("hidden");
      const micComponent = `${componentData}Mic`;
      if (this[micComponent]?.isRecording()) {
        this[micComponent].stopRecording();
        // this[micComponent].un("record-progress");
      }

      // dung MediaStream de tat micro
      const stream = this.micStreams.get(componentData);
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
        this.micStreams.delete(componentData); // xoa stream khoi Map
      }

      clearTimeout(this.statusSpeak.recordingTimeout);
    },

    setupAutoNextStep() {
      if ((!this.statusSpeak.autoNextRecordSlow && !this.statusSpeak.autoNextRecordDefault)
          || (!this.statusSpeak.autoNextRecordSlow && this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_SLOW)
          || (!this.statusSpeak.autoNextRecordDefault && this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_DEFAULT)) return;

      let time = 3;
      const noti = document.querySelector(".noti-auto-next-step");
      const timeElement = document.querySelector(".time-auto-next-step");

      if (noti) noti.classList.remove("hidden");
      timeElement.textContent = `${time}s`;

      this.statusSpeak.autoNextStepSetInterval = setInterval(() => {
        time--;
        if (timeElement) timeElement.textContent = `${time}s`;
      }, 1000);

      this.statusSpeak.autoNextStepSetTimeout = setTimeout(() => {
        clearInterval(this.statusSpeak.autoNextStepSetInterval);
        if (noti) noti.classList.add("hidden");
        if (this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_SLOW) {
          this.runRecord("recordSlow");
        } else if (this.statusSpeak.stepViewSpeaking === STEP_TYPE_SPEAK.RECORD_DEFAULT) {
          this.runRecord("recordDefault");
        }
      }, 3000);
    },

    resetInitSpeaking() {
      const waveSurfers = ["wavesurferDefaultTypeSpeaking", "wavesurferSlowTypeSpeaking", "recordDefault", "recordSlow", "recordDefaultMic", "recordSlowMic"];
      waveSurfers.forEach(key => this[key]?.destroy());

      // Dừng tất cả MediaStream còn lại
      this.micStreams.forEach(stream => stream.getTracks().forEach(track => track.stop()));
      this.micStreams.clear();

      Object.assign(this, {
        wavesurferDefaultTypeSpeaking: null,
        wavesurferSlowTypeSpeaking: null,
        isPlayingTypeSpeaking: false,
        currentTimeTypeSpeaking: "00:00",
        durationTypeSpeaking: "00:00",
        recordDefault: null,
        recordSlow: null,
        recordDefaultMic: null,
        recordSlowMic: null,
      });

      this.statusSpeak = {
        audioActiveTypeSpeaking: "default",
        stepViewSpeaking: STEP_TYPE_SPEAK.DEFAULT,
        recordSlowStatus: null,
        recordDefaultStatus: null,
        recordingTimeout: null,
        recordDefaultBlob: null,
        recordSlowBlob: null,
        autoNextRecordSlow: 1,  // Tự động nhảy từ RECORD_SLOW
        autoNextRecordDefault: 1, // Tự động nhảy từ RECORD_DEFAULT
        autoNextStepSetInterval: null,
        autoNextStepSetTimeout: null,
        currentSpeaking: null,
      };
    },

    resetTextKaraoke(componentData) {
      const karaokeText = document.getElementById(`runText-${componentData}-${this.currentAnswer.id}`);
      if (!karaokeText) return;

      const spans = karaokeText.querySelectorAll("span");
      gsap.set(spans, { "--width": "0%" });
      if (this[`${componentData}TextKaraoke`]) {
        this[`${componentData}TextKaraoke`].pause();
        // this[`${componentData}TextKaraoke`].kill();
        // this[`${componentData}TextKaraoke`] = null;
      }
    },

    setupPulseEffect(stream) {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

      const source = this.audioContext.createMediaStreamSource(stream);

      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 2048;
      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      source.connect(this.analyser);

      this.animatePulse();
    },

    animatePulse() {
      this.analyser.getByteFrequencyData(this.dataArray);
      const average = this.getAverageVolume(this.dataArray);
      const scale = 1 + average / 70;
      const pulseCircle = this.$refs.pulseCircle;
      if (pulseCircle) {
        pulseCircle.style.transform = `scale(${scale})`;
      }
      this.animationFrameId = requestAnimationFrame(this.animatePulse);
    },

    getAverageVolume(array) {
      let sum = 0;
      for (let i = 0; i < array.length; i++) {
        sum += array[i];
      }
      return sum / array.length;
    },

    stopPulseEffect() {
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = null;
      }

      if (this.audioContext) {
        this.audioContext.close();
        this.audioContext = null;
      }

      const pulseCircle = this.$refs.pulseCircle;
      if (pulseCircle) {
        pulseCircle.style.transform = 'scale(1)';
      }
    },

    getResultPairQuestion(questionId) {
      const pairs = this.shuffledPairs.find((pair) => pair.id === questionId);
      if (!pairs) {
        return null;
      }
      return pairs.question;
    },
    getLeftPairIndex(uuid, counter, questionId) {
      questionId = Number(questionId);
      if (!this.pairs.hasOwnProperty(questionId)) {
        return -1;
      }
      return this.pairs[questionId].findIndex((pair) => pair.left === uuid);
    },
    getRightPairIndex(uuid, counter, questionId) {
      questionId = Number(questionId);
      if (!this.pairs.hasOwnProperty(questionId)) {
        return -1;
      }
      return this.pairs[questionId].findIndex((pair) => pair.right === uuid);
    },
    getLeftPairColor(uuid, counter, questionId) {
      questionId = Number(questionId);
      if (this.getLeftPairIndex(uuid, counter, questionId) === -1) {
        return this.generateRandomPastelColor();
      }
      return this.pairColors[this.getLeftPairIndex(uuid, counter, questionId)];
    },
    getRightPairColor(uuid, counter, questionId) {
      questionId = Number(questionId);
      if (this.getRightPairIndex(uuid, counter, questionId) === -1) {
        return this.generateRandomPastelColor();
      }
      return this.pairColors[this.getRightPairIndex(uuid, counter, questionId)];
    },
    setPairLeft(uuid, counter, questionId, countPair) {
      if (!["doing", "test"].includes(this.statusAnswer)) {
        return;
      }
      questionId = Number(questionId);
      if (!this.pairs.hasOwnProperty(questionId)) {
        this.$set(this.pairs, questionId, [
          {
            left: null,
            right: null,
            is_correct: false,
          },
        ]);
      }

      if (this.pairs.hasOwnProperty(questionId)) {
        const selectedIndex = this.pairs[questionId].findIndex(
          (pair) => pair.left === uuid
        );
        if (!this.pairs[questionId][this.currentPairIndex]) {
          if (selectedIndex !== -1) return;
          if (selectedIndex !== -1 && selectedIndex !== this.currentPairIndex) {
            this.$set(this.pairs[questionId][selectedIndex], "left", null);
          } else {
            this.$set(this.pairs[questionId], this.currentPairIndex, {
              left: uuid,
              right: null,
              is_correct: false,
            });
          }
        } else if (!this.pairs[questionId][this.currentPairIndex].left) {
          if (selectedIndex !== -1) return;
          if (selectedIndex !== -1 && selectedIndex !== this.currentPairIndex) {
            this.$set(this.pairs[questionId][selectedIndex], "left", null);
          } else {
            this.$set(
              this.pairs[questionId][this.currentPairIndex],
              "left",
              uuid
            );
          }
        } else {
          if (selectedIndex !== -1) return;
          if (selectedIndex !== -1 && selectedIndex !== this.currentPairIndex) {
            this.$set(this.pairs[questionId][selectedIndex], "left", null);
          } else if (selectedIndex === -1) {
          } else {
            this.$set(
              this.pairs[questionId][this.currentPairIndex],
              "left",
              null
            );
          }
        }
      }

      this.pairCounter++;
      const cond =
        this.pairs.hasOwnProperty(questionId) &&
        this.pairs[questionId].length === Number(countPair) &&
        this.pairs[questionId].every(
          (pair) => pair.left !== null && pair.right !== null
        );
      if (cond) {
        this.statusAnswer = "test";
        const checkButton = document.getElementById("checkButton");
        checkButton.disabled = false;
      } else {
        this.statusAnswer = "doing";
        const checkButton = document.getElementById("checkButton");
        checkButton.disabled = true;
      }
    },
    setPairRight(uuid, counter, questionId, countPair) {
      if (!["doing", "test"].includes(this.statusAnswer)) {
        return;
      }
      questionId = Number(questionId);

      if (!this.pairs.hasOwnProperty(questionId)) {
        this.$set(this.pairs, questionId, [
          {
            left: null,
            right: null,
            is_correct: false,
          },
        ]);
      }

      if (this.pairs.hasOwnProperty(questionId)) {
        const selectedIndex = this.pairs[questionId].findIndex(
          (pair) => pair.right === uuid
        );

        if (!this.pairs[questionId][this.currentPairIndex]) {
          if (selectedIndex !== -1) return;
          if (selectedIndex !== -1 && selectedIndex !== this.currentPairIndex) {
            this.$set(this.pairs[questionId][selectedIndex], "right", null);
          } else {
            this.$set(this.pairs[questionId], this.currentPairIndex, {
              left: null,
              right: uuid,
              is_correct: false,
            });
          }
        } else if (!this.pairs[questionId][this.currentPairIndex].right) {
          if (selectedIndex !== -1) return;
          if (selectedIndex !== -1 && selectedIndex !== this.currentPairIndex) {
            this.$set(this.pairs[questionId][selectedIndex], "right", null);
          } else {
            this.$set(
              this.pairs[questionId][this.currentPairIndex],
              "right",
              uuid
            );
          }
        } else {
          if (selectedIndex !== -1) return;
          if (selectedIndex !== -1 && selectedIndex !== this.currentPairIndex) {
            this.$set(this.pairs[questionId][selectedIndex], "right", null);
          } else if (selectedIndex === -1) {
          } else {
            this.$set(
              this.pairs[questionId][this.currentPairIndex],
              "right",
              null
            );
          }
        }
      }

      this.pairCounter++;
      const cond =
        this.pairs.hasOwnProperty(questionId) &&
        this.pairs[questionId].length === Number(countPair) &&
        this.pairs[questionId].every(
          (pair) => pair.left !== null && pair.right !== null
        );
      if (cond) {
        this.statusAnswer = "test";
        const checkButton = document.getElementById("checkButton");
        checkButton.disabled = false;
      } else {
        this.statusAnswer = "doing";
        const checkButton = document.getElementById("checkButton");
        checkButton.disabled = true;
      }
    },
    clearPairs() {
      if (!["doing", "test"].includes(this.statusAnswer)) {
        return;
      }
      if (this.pairs.hasOwnProperty(Number(this.idCurrentAnswer))) {
        this.$set(this.pairs, Number(this.idCurrentAnswer), []);
        this.statusAnswer = "doing";
        const checkButton = document.getElementById("checkButton");
        checkButton.disabled = true;
      }
    },
    handleStageChange(stage) {
      this.examStage = stage;
    },
    handleContainerActive(active) {
      this.isActive = active;
    },
    handleResize() {
      this.windowWidth = window.innerWidth;
    },
    checkAnswerAudio(currentAnswer) {
      let str = currentAnswer.value;

      if (typeof currentAnswer.value !== "string") {
        return 0;
      }

      let regexMp3 = /{! (.+?\.mp3) !}/;
      let regexImg = /<img[^>]*src="([^"]+)"[^>]*>/;
      let regexMp3Option = /([A-Za-z0-9/-]+\.mp3)/;

      let matchMp3 = str.match(regexMp3);
      let imgTagMatch = str.match(regexImg);
      let matchMp3Option = str.match(regexMp3Option);

      let imgTag = imgTagMatch ? imgTagMatch[0] : null;
      let res = 0;

      if (matchMp3 && imgTag != null) {
        res = 1; // có cả ảnh và audio
      } else if (matchMp3 && imgTag == null) {
        res = 2; // có audio nhưng không có ảnh
      } else if (matchMp3Option) {
        res = 3; // audio đáp án
      } else if (!matchMp3 && imgTag) {
        res = 4; // có ảnh nhưng không có audio
      } else {
        res = 0; // không có audio
      }

      return res;
    },

    checkComponentQuestion(currentAnswer) {
      // function kiểm tra trong đoạn text có những thành phần nào
      let str = currentAnswer.value;
      let result = 0; // mặc định chỉ có text

      // --- Kiểm tra thành phần trong chuỗi ---
      // 1. Kiểm tra thành phần audio
      let hasAudio = /\{\!.*?\!\}/.test(str); // Tìm {! ... !}

      // 2. Kiểm tra thành phần hình ảnh
      let hasImage = /<img\s[^>]*src="[^"]*"/.test(str); // Tìm thẻ <img>

      // 3. Kiểm tra thành phần văn bản/thẻ khác không chứa audio
      let hasTextOrOtherTags =
        /<p[^>]*>(?!\{\!.*?\!\})[^<]*<\/p>|<span[^>]*>.*?<\/span>|<div[^>]*>.*?<\/div>/.test(
          str
        );

      // const audioRegex = /\{\!.*?\!\}/; // Tìm link audio giữa {! ... !}
      // const imgRegex = /<img\s[^>]*src="[^"]*"/; // Tìm thẻ <img> trong thẻ <p>
      // const textRegex = /<p[^>]*>(?!\{\!.*?\!\})[^<]*<\/p>|<span[^>]*>.*?<\/span>|<div[^>]*>.*?<\/div>/; // Tìm thẻ <p> không chứa các thành phần audio hoặc <img>

      if (hasAudio && (hasImage || hasTextOrOtherTags)) {
        result = 1; // có audio và có các thành phần khác
      } else if (hasAudio && !hasImage && !hasTextOrOtherTags) {
        result = 2; // chỉ có audio
      } else if (!hasAudio && hasImage && hasTextOrOtherTags) {
        result = 3; // có ảnh và thành phần khác
      } else if (!hasAudio && hasImage && !hasTextOrOtherTags) {
        result = 4; // chỉ có ảnh
      }

      return result;
    },

    getComponentFromQuestionStr(currentAnswer, type = 1) {
      /* Component: Audio, Img, TagOther
       Type:
        1: Bo tag audio, return string // default
        2: Lay tung thanh phan, return obj
        3: Bo tag Img, return string
        4: Bo tag Other, return string
       */
      let str = currentAnswer.value;

      if (type === 2) {
        return {
          audio: "",
          img: "",
          textOrOtherTags: "",
        };
      }

      return str.replace(/<p[^>]*>\s*\{\!.*?\!\}\s*<\/p>/g, "");
    },

    getTagImg(currentAnswer) {
      const str = currentAnswer.value;

      const regexImg = /<p><img[^>]*src="([^"]+)"[^>]*><\/p>/;

      // Kiểm tra và lấy toàn bộ thẻ <p><img ... /></p>
      const imgTagMatch = str.match(regexImg);
      return imgTagMatch ? imgTagMatch[0] : null;
    },
    getLinkMp3(str) {
      // const str = currentAnswer.value;

      const regexMp3 = /{! (.+?\.mp3) !}/;
      const regexMp3Option = /([A-Za-z0-9/-]+\.mp3)/;

      const match = str.match(regexMp3);
      const matchOption = str.match(regexMp3Option);

      let mp3Path = null;
      if (match != null) {
        mp3Path = match[1];
      }
      if (matchOption != null) {
        mp3Path = matchOption[1];
      }

      return mp3Path;
    },
    selectOption(answer) {
      if (this.statusAnswer === "next" || this.statusAnswer === "end") {
        return;
      }

      this.playingAnswerId = answer.id;

      this.toggleMp3(answer.id, "option");

      let id = answer.id;
      let allSpanOption = $(".span-option");
      let optionSelected = $(`#span-option-${id}`);

      allSpanOption.removeClass("border-[#B2EEFA]");
      optionSelected.addClass("border-[#B2EEFA]");

      let checkButton = document.getElementById("checkButton");
      checkButton.disabled = false;
      this.statusAnswer = "test";
    },
    showExplanationPopup() {
      this.setupStyleExplanationPopup();
      // Hiển thị overlay và popup
      document.getElementById("popupOverlay").classList.remove("hidden");
      // $(".main-container-wrap").addClass("opacity-30");
      document.getElementById("explanationPopup").classList.remove("hidden");
      // Xóa translate-y-full để trượt popup lên
      setTimeout(() => {
        document
          .getElementById("explanationPopup")
          .classList.remove("translate-y-full");
      }, 10);
    },
    showNotActionPopup() {
      // Hiển thị overlay và popup
      $("#popupOverlayNotAction").removeClass("hidden");
      $("#notActionPopup").removeClass("hidden");
      setTimeout(() => {
        $("#notActionPopup").removeClass("translate-y-[300%]");
      }, 10);
    },
    progressSetupNextQuestion(currentIndex = null) {
      if (currentIndex === null) {
        currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
      }
      $("#progress_bar").css("width", `${((currentIndex + 1) * 100) / this.dataLesson.component.length}%`);
      $("#progress_text").text(`${currentIndex + 1}/${this.dataLesson.component.length}`);
    },
    checkAnswer() {
      let currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
      this.progressSetupNextQuestion(currentIndex);

      if (currentIndex >= 0 && currentIndex < this.dataLesson.component.length - 1) {
        this.statusAnswer = "next";
      } else {
        this.statusAnswer = "end";
      }
      let currentAnswer = this.dataLesson.component.find(t => t.id === this.idCurrentAnswer);
      if (currentAnswer.type === TYPE_LESSON.MULTIPLE_CHOICE) {
        this.checkAnswerMultiChoice(currentAnswer);
      } else if (currentAnswer.type === TYPE_LESSON.FILL_IN_BLANK) {
        this.checkAnswerFillIn();
      } else if (currentAnswer.type === TYPE_LESSON.WORD_PAIR) {
        this.checkAnswerWordPair();
      } else if (currentAnswer.type === TYPE_LESSON.SENTENCE_JUMBLE) {
        this.checkAnswerSentenceJumble();
      }

      this.showExplainButton(currentAnswer);
    },
    showExplainButton(currentAnswer) {
      this.isShowExplainButton =
        (currentAnswer.type === TYPE_LESSON.FILL_IN_BLANK && currentAnswer.value.explain != null) ||
        currentAnswer.explain_mp3 != null ||
        (currentAnswer.type === TYPE_LESSON.MULTIPLE_CHOICE && currentAnswer.explain != null) ||
        (currentAnswer.type === TYPE_LESSON.WORD_PAIR && currentAnswer.value.explain != null) ||
        (currentAnswer.type === TYPE_LESSON.SENTENCE_JUMBLE && currentAnswer.value.explain != null)
      ;
    },
    questionSuccess(currentAnswer) {
      if (parseInt(currentAnswer.grade) > 0) {
        this.userResult.grade += parseInt(currentAnswer.grade);
      }
      this.userResult.correct_answer += 1;
      this.userResult.ratio_correct_answer = parseFloat(
          (
              this.userResult.correct_answer / this.userResult.total_answer
          ).toFixed(2)
      );
    },
    async nextQuestion() {
      this.indexCurrentLesson += 1;
      this.isShowExplanationPopupFullScreen = false;
      $("#checkButton").addClass("pointer-events-none");
      this.statusAnswer = "doing";

      if (this.currentAnswer.type === TYPE_LESSON.SPEAKING) {

        this.userResult.data[this.currentAnswer.id] = {
          slow: await this.convertWebMtoMP3(this.statusSpeak.recordSlowBlob),
          default: await this.convertWebMtoMP3(this.statusSpeak.recordDefaultBlob),
        };
        this.questionSuccess(this.currentAnswer);
        this.resetInitSpeaking();
        this.progressSetupNextQuestion();
      }
      $("#checkButton").removeClass("pointer-events-none");
      $(".noti-done-record").addClass("hidden");

      this.audio = null;
      // Tìm index của câu hỏi hiện tại
      this.pairCounter = 0;
      this.currentPairIndex = 0;
      let currentIndex = this.dataLesson.component.findIndex(
        (q) => q.id === this.idCurrentAnswer
      );

      // Kiểm tra xem có phần tử tiếp theo không
      if (
        currentIndex >= 0 &&
        currentIndex < this.dataLesson.component.length - 1
      ) {
        $(`#wrap-question-${this.idCurrentAnswer}`).addClass("hidden");
        this.idCurrentAnswer = this.dataLesson.component[currentIndex + 1].id;
        this.currentAnswer = this.dataLesson.component[currentIndex + 1];
        this.showExplainButton(this.currentAnswer);

        $(`#wrap-question-${this.idCurrentAnswer}`).removeClass("hidden");
        this.resetCheckButton();
        if (this.currentAnswer.type !== TYPE_LESSON.SPEAKING) {
          this.toggleMp3(this.currentAnswer.id, "question");
        }

        if (this.currentAnswer.type === TYPE_LESSON.SENTENCE_JUMBLE) {
          this.showTooltip += 1;
        }
        if (this.currentAnswer.type === TYPE_LESSON.SPEAKING) {
          this.initWaveSurfer();
        }
      } else {
        this.statusAnswer = "end";
      }
      this.arrUserResultSentenceJumble = [];
      this.checkShowPopupInstructionSpeaking();
    },
    async saveResultExerciseUser() {
      const checkButton = document.getElementById("checkButton");
      checkButton.disabled = true;
      if (this.currentAnswer.type === TYPE_LESSON.SPEAKING) {
        this.userResult.data[this.currentAnswer.id] = {
          slow: await this.convertWebMtoMP3(this.statusSpeak.recordSlowBlob),
          default: await this.convertWebMtoMP3(this.statusSpeak.recordDefaultBlob),
        };
        this.questionSuccess(this.currentAnswer);
      }
      let currentIndex = this.dataLesson.component.findIndex(
        (q) => q.id === this.idCurrentAnswer
      );
      $("#progress_bar").css(
        "width",
        `${((currentIndex + 1) * 100) / this.dataLesson.component.length}%`
      );
      $("#progress_text").text(
        `${currentIndex + 1}/${this.dataLesson.component.length}`
      );

      this.checkLineOption();

      const formData = new FormData();

      for (let property in this.userResult) {
        if (property === "data") {
          for (let questionId in this.userResult[property]) {
            if (this.userResult[property][questionId].slow) {
              formData.append(`audio[${questionId}][slow]`, this.userResult[property][questionId].slow, `${questionId}_slow.mp3`);
              formData.append(`audio[${questionId}][default]`, this.userResult[property][questionId].default, `${questionId}_default.mp3`);
            }
          }
        }
        if (Array.isArray(this.userResult[property]) || (typeof this.userResult[property] === "object" && this.userResult[property] !== null && !Array.isArray(this.userResult[property]))) {
          formData.append(property, JSON.stringify(this.userResult[property]));
        } else {
          formData.append(property, this.userResult[property]);
        }
      }

      let save_result = apiLessonExercise
        .post("/list-course/save-result-exercise-user", formData)
        .then((res) => {
          checkButton.disabled = false;
          if (res.data.error_code === 0) {
            this.$message.success(res.data.msg);
            this.statusAnswer = "result";
            if (res.data.show_popup_achievement === 1) {
              $("#img-achievement-stamp").attr(
                  "src",
                  "/images/lessons/svg/achievement/stamp-" +
                  res.data.course_name +
                  ".svg"
              );
              $("#img-achievement-popup").attr(
                "src",
                "/images/lessons/svg/achievement/" +
                  res.data.name_popup_achievement +
                  ".svg"
              );
              $("#img-achievement-popup").css("display", "block");
              $("#text-achievement-popup").text(
                res.data.text_content_achievement
              );
              if (res.data.name_popup_achievement.startsWith('complete_stage_')) {
                $("#text-top-detail-popup").css("display", "none");
                $("#text-bottom-detail-popup").css("display", "block");
              } else {
                $("#text-top-detail-popup").css("display", "block");
                $("#text-bottom-detail-popup").css("display", "none");
              }
              $("#modalAchievement").modal("show");
            }

            if (res.data.show_popup_done_stage) {
              $("#modalDoneProgressGroup").modal("show");
            }

            if (parseInt(this.userResult.ratio_correct_answer) > 0.85) {
              $("#checkButton").removeClass("bg-[#FFF193]");
            } else {
              $("#checkButton").removeClass("bg-white");
            }
          } else {
            this.$message.error(res.data.msg);
          }
        });
    },
    actionCheckButton() {
      this.stopAllAudios();
      if (this.statusAnswer === "test") {
        this.disableInput = true;
        this.checkAnswer();
      } else if (this.statusAnswer === "next") {
        this.disableInput = false;
        this.nextQuestion();
      } else if (this.statusAnswer === "end") {
        this.disableInput = false;
        this.saveResultExerciseUser();
      } else {
        this.disableInput = false;
        return;
      }
    },
    checkInput(event) {
      const dataId = event.target.getAttribute("data-id");
      const checkButton = document.getElementById("checkButton");

      $(`#${event.target.id}`).css(
        "width",
        `${Math.max(73, event.target.value.length * 15)}px`
      ); // 10 là tỉ lệ có thể thay đổi theo font-size

      let valueMissingWordArray = $(`input[data-id="${dataId}"]`)
        .map(function () {
          return this.value;
        })
        .get();

      // Kiểm tra nếu có giá trị trong ô điền từ
      if (!valueMissingWordArray.includes("")) {
        this.statusAnswer = "test";
        checkButton.disabled = false;
      } else {
        this.statusAnswer = "doing";
        checkButton.disabled = true;
      }
    },
    resetCheckButton() {
      const checkButton = document.getElementById("checkButton");
      this.statusAnswer = "doing";
      checkButton.disabled = true;
      this.isCheckAnswer = false;
      this.isAnswerSuccess = true;
    },
    setupStyleExplanationPopup() {
      $("#content_explain").html(
        [TYPE_LESSON.FILL_IN_BLANK, TYPE_LESSON.WORD_PAIR, TYPE_LESSON.SENTENCE_JUMBLE].includes(this.currentAnswer.type)
          ? this.currentAnswer.value.explain
          : this.currentAnswer.explain
      );
      if (
        this.currentAnswer.explain_mp3 !== null &&
        this.currentAnswer.explain_mp3 !== ""
      ) {
        this.isShowExplainIconAudio = true;
        $("#audio").attr("src", `/cdn/audio/${this.currentAnswer.explain_mp3}`);
      } else {
        this.isShowExplainIconAudio = false;
      }
    },
    hideExplanationPopup() {
      $("#audio").attr("src", "");
      document
        .getElementById("explanationPopup")
        .classList.add("translate-y-full");
      setTimeout(() => {
        document.getElementById("popupOverlay").classList.add("hidden");
        // $(".main-container-wrap").removeClass("opacity-30");
        document.getElementById("explanationPopup").classList.add("hidden");
      }, 300);
      this.isShowExplanationPopupFullScreen = false;
    },
    toggleExplanationPopupFullScreen() {
      this.isShowExplanationPopupFullScreen = !this.isShowExplanationPopupFullScreen;
    },
    hideNotActionPopup() {
      $("#notActionPopup").addClass("translate-y-[300%]");
      setTimeout(() => {
        $("#popupOverlayNotAction").addClass("hidden");
        $("#notActionPopup").addClass("hidden");
      }, 300);
    },
    checkAnswerMultiChoice(currentAnswer) {
      let options = $(`.span-option[data-question="${currentAnswer.id}"]`);
      let userResult = this.userResult.data;
      let isAnswerSuccess = true;

      let question = this.dataLesson.component.find(
        (item) => item.id === currentAnswer.id
      );

      options.each(function () {
        if ($(this).prev().is(":checked")) {
          userResult = {
            ...userResult,
            [currentAnswer.id]: $(this).data("answer"),
          };
          question.answers.map((item) => {
            item.user_choice = $(this).data("answer") === item.id ? 1 : 0;
          });
        }

        if (parseInt($(this).data("result")) !== 0) {
          $(this).removeClass("border-[#B2EEFA]");
          $(this).removeClass("border-[#F4F5FA]");
          $(this).addClass("border-[#57D061] text-black bg-[#95FF99]");
        } else if ($(this).prev().is(":checked")) {
          isAnswerSuccess = false;
          $(this).removeClass("border-[#F4F5FA]");
          $(this).removeClass("border-[#B2EEFA]");
          $(this).addClass("border-[#FF7C79] text-black bg-[#FDD3D0]");
        }
      });
      this.isAnswerSuccess = isAnswerSuccess;
      if (this.isAnswerSuccess) {
        this.questionSuccess(currentAnswer);
      }
      this.userResult.data = userResult;
    },
    checkAnswerFillIn() {
      let isAnswerSuccess = true;
      let inputMissingWord = $(
        `.missingWord[data-id="${this.idCurrentAnswer}"]`
      );
      let dataResult = [];
      let question = this.dataLesson.component.find(
        (item) => item.id === this.idCurrentAnswer
      );

      inputMissingWord.each(function () {
        $(this).removeClass("bg-[#D9D9D9]");
        question.value.question[$(this).data("index")].user_result = $(this)
          .val()
          .trim();
        dataResult.push($(this).val().trim());
        if (
          (typeof $(this).data("result") === "string" &&
            $(this).data("result").toLowerCase() ===
              $(this).val().trim().toLowerCase()) ||
          (typeof $(this).data("result") === "number" &&
            $(this).data("result").toString() === $(this).val().trim())
        ) {
          // this.isAnswerSuccess = true
          $(this).css({
            border: "3px solid #57D061",
            background: "#95FF99",
          });
        } else {
          isAnswerSuccess = false;
          $(this).css({
            border: "3px solid #FF7C79",
            background: "#FDD3D0",
          });
        }
      });
      this.isAnswerSuccess = isAnswerSuccess;
      if (this.isAnswerSuccess) {
        this.questionSuccess(this.currentAnswer);
      }
      this.userResult.data = {
        ...this.userResult.data,
        [this.idCurrentAnswer]: dataResult,
      };
    },
    checkPairAnswer(uuid, side, questionId) {
      if (!this.pairs.hasOwnProperty(questionId)) {
        return false;
      }
      const pair = this.pairs[questionId].find((pair) => pair[side] === uuid);
      if (!pair) {
        return false;
      }
      return pair.is_correct;
    },
    checkAnswerWordPair() {
      let userResult = this.userResult.data;
      let isAnswerSuccess = true;
      let question = this.dataLesson.component.find(
        (item) => item.id === this.idCurrentAnswer
      );
      const combinedQuestion = question.value.question.left.map(
        (item, index) => ({
          left: item.uuid,
          right: question.value.question.right[index].uuid || null, // Fallback in case array2 has fewer items
        })
      );

      if (this.pairs.hasOwnProperty(this.idCurrentAnswer)) {
        this.pairs[this.idCurrentAnswer].forEach((pair, idx) => {
          const questionPair = combinedQuestion.find(
            (item) => item.left === pair.left
          );
          if (questionPair.right !== pair.right) {
            isAnswerSuccess = false;
            pair.is_correct = false;
            this.$set(
              this.pairs[this.idCurrentAnswer][idx],
              "is_correct",
              false
            );
          } else {
            this.$set(
              this.pairs[this.idCurrentAnswer][idx],
              "is_correct",
              true
            );
          }
        });
      }

      if (isAnswerSuccess) {
        this.userResult.grade += parseInt(question.grade);
      }
      this.isAnswerSuccess = isAnswerSuccess;
      if (this.isAnswerSuccess) {
        this.questionSuccess(this.currentAnswer);
      }

      this.userResult.data = {
        ...this.userResult.data,
        [this.idCurrentAnswer]: this.pairs[this.idCurrentAnswer],
      };
    },
    checkAnswerSentenceJumble() {
      let answer = this.currentAnswer.value.question;
      let indexMinWidthAnswer = 0;
      this.currentAnswer.value.question.forEach((item, index) => {
        if (item.result.length < this.currentAnswer.value.question[indexMinWidthAnswer].result.length) {
          indexMinWidthAnswer = index;
        }
      });

      if (this.currentAnswer.value.question.length > this.arrUserResultSentenceJumble.length) {
        this.currentAnswer.value.question.forEach((item, index) => {
          let itemHtml = $(`.sentence-jumble-item-result[data-uuid=${this.arrUserResultSentenceJumble[index]}]`);
          itemHtml.removeClass("bg-[#F5F5F5]");

          if (item.uuid === this.arrUserResultSentenceJumble[index]) {
            itemHtml.addClass("bg-[#95FF99] border-[#57D061]");
          } else {
            if (!this.arrUserResultSentenceJumble[index]) {
              let htmlItemEmpty = `<p
                                                                        data-uuid="${item.uuid}"
                                                                        class="sentence-jumble-item-result bg-[#FDD3D0] border-[#FF7C79] select-none border-[3px] rounded-[12px] m-2 p-3 font-gen-jyuu-gothic font-medium text-2xl cursor-pointer"
                                                                >
                                                                    <span class="opacity-0">${this.currentAnswer.value.question[indexMinWidthAnswer].result}</span>
                                                                </p>`;
              $(`.wrapper-user-result[data-id=${this.currentAnswer.id}]`).append(htmlItemEmpty);
              this.isAnswerSuccess = false;
            } else {
              this.isAnswerSuccess = false;
              itemHtml.addClass("bg-[#FDD3D0] border-[#FF7C79]");
            }
          }
        });
      } else {
        this.arrUserResultSentenceJumble.forEach((item, index) => {
          let itemHtml = $(`.sentence-jumble-item-result[data-uuid=${item}]`);
          itemHtml.removeClass("bg-[#F5F5F5]");
          if (answer[index]) {
            if (item === answer[index].uuid) {
              itemHtml.addClass("bg-[#95FF99] border-[#57D061]");
            } else {
              this.isAnswerSuccess = false;
              itemHtml.addClass("bg-[#FDD3D0] border-[#FF7C79]");
            }
          } else {
            this.isAnswerSuccess = false;
            itemHtml.addClass("bg-[#FDD3D0] border-[#FF7C79]");
          }
        });
      }

      if (this.isAnswerSuccess) {
        this.questionSuccess(this.currentAnswer);
      }

      this.userResult.data = {...this.userResult.data, [this.idCurrentAnswer]: this.arrUserResultSentenceJumble};
    },
    handleClose(done) {
      this.$confirm("Are you sure to close this dialog?")
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    renderStatusOptionResult(answer) {
      if (answer.user_choice === 0 && answer.grade === "0") {
        return "bg-white border-[F4F5FA]";
      } else if (answer.grade !== "0") {
        return "bg-[#95FF99] border-[#57D061]";
      } else if (answer.user_choice === 1 && answer.grade === "0") {
        return "bg-[#FDD3D0] border-[#FF7C79]";
      }
    },
    renderStatusFillInBlankResult(element, keyElement, questionId) {
      if (element.type === "question" && element.user_result !== undefined) {
        this.$nextTick(() => {
          const input = $(`#missingWord-${keyElement}-${questionId}`);
          input.css(
            "width",
            `${Math.max(73, element.user_result.length * 15)}px`
          );
        });

        if (
          element.result.toLowerCase() === element.user_result.toLowerCase()
        ) {
          return `border-[3px] border-[#57D061] bg-[#95FF99]`;
        } else {
          return `border-[3px] border-[#FF7C79] bg-[#FDD3D0]`;
        }
      }
      return "";
    },
    reload() {
      location.reload();
      const params = new URLSearchParams(window.location.search);

      // Thêm hoặc cập nhật query parameters
      params.set("tab_active", "tabExercise");

      // Reload trang với params
      window.location.search = params.toString();
    },
    nextLesson(event) {
      if (
        this.currentLesson.percent >= 85 ||
        this.currentLesson.require !== 1 ||
        this.userResult.ratio_correct_answer >= 0.85
      ) {
        window.location.href = event.target.getAttribute("href");
      } else {
        this.setRedirectModal(event.target.getAttribute("href"));
      }
    },
    renderWidthTooltipSuggest(currentAnswer) {
      let suggest =
        currentAnswer.type === TYPE_LESSON.FILL_IN_BLANK
          ? currentAnswer.value.suggest
          : currentAnswer.suggest;
      if (suggest === null || suggest === "") {
        return "700px";
      }
      return `${Math.min(700, suggest.length * 10)}px`;
    },
    checkDevice() {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;

      if (/iPhone|iPad|iPod/i.test(userAgent)) {
        return "ios";
      } else if (/Android/i.test(userAgent)) {
        return "android";
      }
      return "other";
    },
    checkLineOption() {
      let spanElements = $(".span-option-default");
      spanElements.each(function () {
        const $this = $(this);
        if ($this[0].offsetHeight > 72) {
          $(this).removeClass("rounded-full");
          $(this).addClass("rounded-[32px]");
          // $(this).css("height", ($this[0].offsetHeight + 32) + "px");
        }
      });
    },
    async setRedirectModal(forceUrl = null, resquest = {}, type = "switch") {
      let courseUrl = "/khoa-hoc/" + this.course.SEOurl;
      // if (!this.userLesson || (!this.isUnlock && (this.userLesson || this.lesson.price_option))) {
      //   window.location.href = courseUrl;
      //   return;
      // }

      if (forceUrl) {
        courseUrl = forceUrl;
      }
      if (!this.userLesson) {
        window.location.href = courseUrl;
        return;
      }
      let params = new URLSearchParams(resquest);
      courseUrl += "?" + params.toString();

      let leaveTitle = type === "switch" ? "Bạn muốn chuyển bài?" : "Bạn muốn rời đi?";
      let leaveText = "Cố gắng học tiếp để hoàn thành tiến trình bạn iu nha!";
      let leaveButton = type === "switch" ? "Chuyển" : "Thoát";

      if (this.currentLesson.type === "last_exam") {
        window.location.href = courseUrl;
        return;
      }

      await $.get(
        window.location.origin + "/get-lesson-percent/" + this.currentLesson.id
      ).then((res) => {
        if (["exam", "last_exam"].includes(this.currentLesson.type)) {
           if (this.examStage > 0 && this.examStage < 4) {
            $("#modal-confirm-leave-exam")
              .find(".confirm-leave__title")
              .text(leaveTitle);
            $("#span_text_exercise").text(leaveText);
            $("#modal-confirm-leave-exam")
              .find(".btn-confirm-leave")
              .text(leaveButton);
            $("#modal-confirm-leave-exam").modal("show");
            $("#modal-confirm-leave-exam").data("url", courseUrl);
            return;
          } else if (this.currentLesson.require && res < 85) {
            leaveText =
              "Bài học có điểm số đạt < 85% là bài học chưa đạt định mức hoàn thành";
            $("#modal-confirm-leave")
              .find(".confirm-leave__title")
              .text(leaveTitle);

            $("#span_text_exercise").text(leaveText);
            $("#modal-confirm-leave")
              .find(".btn-confirm-leave")
              .text(leaveButton);
            $("#modal-confirm-leave").modal("show");
            $("#modal-confirm-leave").data("url", courseUrl);
            return;
          } else {
            window.location.href = courseUrl;
          }
        }

        if (this.dataLesson.require && res < 85) {
          leaveText =
            "Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành";
          $("#modal-confirm-leave")
            .find(".confirm-leave__title")
            .text(leaveTitle);

          $("#span_text_exercise").text(leaveText);
          $("#modal-confirm-leave").find(".btn-confirm-leave").text(leaveButton);
          $("#modal-confirm-leave").modal("show");
          $("#modal-confirm-leave").data("url", courseUrl);
          return;
        } else {
          window.location.href = courseUrl;
        }
      });
    },
    buyNow(courseId) {
      var text = "buy_n" + (this.course.id === 39 ? "5" : "4") + "_0";
      ga("send", "event", "hoc_thu_cate", text, "buy_label");
      let link =
        "/payment?buy=" + btoa("course") + "&item=" + btoa(this.course.id);
      window.open(link, "_blank");
    },
    charCode(number = 0) {
      return String.fromCharCode(65 + number);
    },
    onImageError(file_name) {
      // event.target.src = event.target.alt;
    },
    checkVisibility() {
      const box = this.$refs.box; // Giả sử kiểm tra phần tử đầu tiên
      if (!box) {
        return;
      }

      const rect = box.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      if (
        (rect.top > windowHeight || rect.bottom < 0) &&
        window.innerWidth > 640
      ) {
        $("#tabList_header").css("display", "block");
      } else {
        $("#tabList_header").css("display", "none");
      }
    },
    generateRandomPastelColor() {
      // Generate random RGB values and blend them with white for a pastel effect
      const r = Math.floor((Math.random() * 256 + 255) / 2);
      const g = Math.floor((Math.random() * 256 + 255) / 2);
      const b = Math.floor((Math.random() * 256 + 255) / 2);

      // Convert the RGB values to a hex color string
      const toHex = (value) => value.toString(16).padStart(2, "0");
      return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    },
    selectWordSentenceJumble(uuid) {
      let itemExists = this.arrUserResultSentenceJumble.find(item => item === uuid);
      if (itemExists || ["next", "end"].includes(this.statusAnswer)) {
        return;
      }
      this.statusAnswer = "test";
      this.arrUserResultSentenceJumble.push(uuid);
      if (this.userResult.data[this.currentAnswer.id] === undefined) {
        this.userResult.data[this.currentAnswer.id] = [uuid];
      } else {
        this.userResult.data[this.currentAnswer.id].push(uuid);
      }
      $(`.sentence-jumble-item[data-uuid="${uuid}"]`).addClass(" bg-[#D9D9D9] border-[#D9D9D9] text-[#D9D9D9] select-none");
      $(`.sentence-jumble-item[data-uuid="${uuid}"]`).removeClass("bg-[#F5F5F5] text-black");
      $(`.sentence-jumble-item[data-uuid="${uuid}"]`).find("p").css("opacity", "0");
    },
    removeWordSentenceJumble(uuid) {
      if (["next", "end"].includes(this.statusAnswer)) {
        return;
      }
      this.arrUserResultSentenceJumble = this.arrUserResultSentenceJumble.filter(item => item !== uuid);
      $(`.sentence-jumble-item[data-uuid="${uuid}"]`).addClass(" bg-[#F5F5F5] text-black");
      $(`.sentence-jumble-item[data-uuid="${uuid}"]`).removeClass("bg-[#D9D9D9] border-[#D9D9D9] text-[#D9D9D9] select-none");
      $(`.sentence-jumble-item[data-uuid="${uuid}"]`).find("p").css('opacity', "1");
      this.userResult.data[this.currentAnswer.id] = this.userResult.data[this.currentAnswer.id].filter(item => item !== uuid);
      if (this.arrUserResultSentenceJumble.length === 0) {
        this.statusAnswer = "doing";
      }
    },
    renderSelectWordSentenceJumble(uuid, question_id = null) {
      let currentAnswer = this.currentAnswer;
      if (question_id !== null) {
        currentAnswer = this.dataLesson.component.find(item => item.id === question_id);
      }
      if (currentAnswer.type !== TYPE_LESSON.SENTENCE_JUMBLE) {
        return ;
      }
      let text = "";
      currentAnswer.value.items.forEach(item => {
        if (item.uuid === uuid) text = item.value;
      });
      // currentAnswer.value.item_false.forEach(item => {
      //   if (item.uuid === uuid) text = item.value;
      // });
      return text;
    },
    async getDurationAudio() {
      let audio = new Audio(`/cdn/audio/${this.currentAnswer.value.audio}`);
      await new Promise((resolve) => {
        audio.onloadedmetadata = resolve;
      });

      let time = audio.duration;
      this.timeDurationCurrentAnswer = this.formatTime(time);
    },
    async convertWebMtoMP3(webmBlob) {
      return webmBlob;
      // console.log(`webmBlob: `, webmBlob);
      // if (!ffmpeg.isLoaded()) {
      //   await ffmpeg.load();
      // }
      // console.log(typeof SharedArrayBuffer !== "undefined");
      //
      // // Load file WebM vào ffmpeg
      // ffmpeg.FS("writeFile", "input.webm", await fetchFile(webmBlob));
      //
      // // Chuyển đổi sang MP3
      // await ffmpeg.run("-i", "input.webm", "output.mp3");
      //
      // // Lấy file MP3 từ bộ nhớ ffmpeg
      // const mp3Data = ffmpeg.FS("readFile", "output.mp3");
      //
      // // Tạo Blob MP3
      // return new Blob([mp3Data.buffer], { type: "audio/mp3" });
    },
    nextStepSlidePopupInstruction() {
      let currentSlide = $(".slide-dialog-instruction").slick("slickCurrentSlide");
      let totalSlides = $(".slide-dialog-instruction").slick("getSlick").slideCount - 1;

      if (currentSlide < totalSlides - 1) {
        $(".slide-dialog-instruction").slick("slickNext");
      } else if (currentSlide === totalSlides - 1) {
        $(".slide-dialog-instruction").slick("slickNext");
        $("#btn-next-step-dialog-instruction").html("Bắt đầu").css({"background-color": "#57D061", "color": "#07403F", "padding-left": "34px", "padding-right": "34px"})
      } else {
        this.dialogInstruction = false;
      }
    },
    checkShowPopupInstructionSpeaking() {
      if (localStorage.getItem("isUsedOpenDialogInstruction") !== "1" && this.currentAnswer.type === TYPE_LESSON.SPEAKING) {
        this.dialogInstruction = true;
        localStorage.setItem("isUsedOpenDialogInstruction", "1");
      }
    },
    goBack(url = null) {
      const referrer = document.referrer;

      let defaultPage = "/khoa-hoc/so-cap-n5";
      if (parseInt(this.dataLesson.course_id) === 39) {
        defaultPage = `/khoa-hoc/so-cap-n5/group/${this.dataLesson.group_id}`;
      } else if (parseInt(this.dataLesson.course_id) === 40) {
        defaultPage = `/khoa-hoc/so-cap-n4/group/${this.dataLesson.group_id}`;
      }

      if (referrer && referrer !== window.location.href) {
        window.history.back();
      } else {
        window.location.href = defaultPage;
      }
    },

    loadAudioDuration(question) {
      if (!this.audioTimes[question.id]) {
        this.$set(this.audioTimes, question.id, { formattedTime: "00:00", duration: "00:00" });
      }

      if (!this.audioInstances) {
        this.audioInstances = new Map();
      }

      this.audioInstances.set(question.id, { type: question.type });

      if (question.type !== TYPE_LESSON.SPEAKING) {
        this.userResult.isExerciseSpeak = false;
      }

      if (question.type === TYPE_LESSON.MULTIPLE_CHOICE) {
        this.handleMultipleChoice(question);
      } else if (question.type === TYPE_LESSON.SPEAKING) {
        this.handleSpeaking(question);
      } else {
        this.handleDefaultAudio(question);
      }
    },
    initAudioInstance(id, url, events = {}) {
      const audio = new Audio(url);
      Object.entries(events).forEach(([event, handler]) => {
        audio[event] = handler;
      });
      return audio;
    },
    handleMultipleChoice(question) {
      this.audioInstances.get(question.id).componentPlaying = "defaultWave";

      let instance = this.audioInstances.get(question.id) || {};
      const linkMp3 = this.getLinkMp3(question.value);
      if (linkMp3) {
        const audio = this.initAudioInstance(question.id, `https://mp3-vn.dungmori.com/${linkMp3}`, {
          onloadedmetadata: () => {
            const duration = this.formatTime(Math.floor(audio.duration));
            this.$set(this.audioTimes[question.id], "duration", duration);
            this.$set(this.audioTimes[question.id], "formattedTime", duration);
            if (this.indexCurrentLesson === 1) {
              this.timeRemaining = Math.floor(audio.duration);
            }
          },
          ontimeupdate: () => {
            const remaining = this.formatTime(Math.floor(audio.duration - audio.currentTime));

            this.$set(this.audioTimes[question.id], "formattedTime", remaining);
            this.timeRemaining = Math.floor(audio.duration - audio.currentTime);
          },
          onended: () => {
            this.$set(this.audioTimes[question.id], "formattedTime", this.audioTimes[question.id].duration);
            this.playingAnswerId = null;
            this.timeRemaining = Math.floor(audio.duration - audio.currentTime);
          },
        });
        instance = { ...instance, audio };
      }

      if (question.explain_mp3) {
        const explain_mp3 = this.initAudioInstance(question.id, `/cdn/audio/${question.explain_mp3}`);
        instance = { ...instance, explain_mp3 };
      }
      this.audioInstances.set(question.id, instance);

      question.answers.forEach(option => {
        const optionLinkMp3 = this.getLinkMp3(option.value);
        if (optionLinkMp3) {
          const audio = this.initAudioInstance(option.id, `https://mp3-vn.dungmori.com/${optionLinkMp3}`);
          this.audioInstances.set(option.id, { audio });
        }
      });
    },
    handleSpeaking(question) {
      const audioSlowUrl = `/cdn/audio/${question.value.audio_speaking_speed_slow}`;
      const audioDefaultUrl = `/cdn/audio/${question.value.audio_speaking_speed_default}`;
      const instance = this.audioInstances.get(question.id);

      instance.link_audio_slow = audioSlowUrl;
      instance.link_audio_default = audioDefaultUrl;

      this.audioInstances.set(question.id, {
        ...instance,
        audioSlow: null,
        audioDefault: null,
      });
    },
    handleDefaultAudio(question) {
      let instance = this.audioInstances.get(question.id) || {};

      if (question.value?.audio) {
        const audio = this.initAudioInstance(question.id, `/cdn/audio/${question.value.audio}`, {
          onloadedmetadata: () => {
            const duration = this.formatTime(Math.floor(audio.duration));
            this.$set(this.audioTimes[question.id], "duration", duration);
            this.$set(this.audioTimes[question.id], "formattedTime", duration);
          },
          ontimeupdate: () => {
            const remaining = this.formatTime(Math.floor(audio.duration - audio.currentTime));

            this.$set(this.audioTimes[question.id], "formattedTime", remaining);
            this.timeRemaining = Math.floor(audio.duration - audio.currentTime);
          },
          onended: () => {
            this.$set(this.audioTimes[question.id], "formattedTime", this.audioTimes[question.id].duration);
            this.playingAnswerId = null;
            this.timeRemaining = Math.floor(audio.duration - audio.currentTime);
          },
        });
        instance = { ...instance, audio };
      }

      if (question.explain_mp3) {
        const explain_mp3 = this.initAudioInstance(question.id, `/cdn/audio/${question.explain_mp3}`);
        instance = { ...instance, explain_mp3 };
      }

      this.audioInstances.set(question.id, instance);
    },
  },
  computed: {
    currentQuestionPairs() {
      const questionId = this.idCurrentAnswer;
      const pairs = this.shuffledPairs.find((pair) => pair.id === questionId);
      if (!pairs) {
        return null;
      }
      return pairs.question;
    },
    // Chuyển đổi giây thành định dạng mm:ss
    formattedTime() {
      const minutes = Math.floor(this.timeRemaining / 60);
      const seconds = this.timeRemaining % 60;
      return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
    },
    getMinimumPossiblePairIndex() {
      // nếu chưa có pair nào được chọn, trả về 0
      if (!this.pairs.hasOwnProperty(Number(this.idCurrentAnswer))) {
        return 0;
      }
      // nếu có pair được chọn nhưng tất cả nều null hoặc rỗng
      else if (
        this.pairs[Number(this.idCurrentAnswer)].every(
          (pair) => pair.left === null && pair.right === null
        )
      ) {
        return 0;
      }

      // nếu tất cả pair được chọn và tất cả pair đều khác null
      else if (
        this.pairs[Number(this.idCurrentAnswer)].every(
          (pair) => pair.left !== null && pair.right !== null
        )
      ) {
        return this.pairs[Number(this.idCurrentAnswer)].length;
      } else {
        return this.pairs[Number(this.idCurrentAnswer)].findIndex(
          (pair) =>
            (pair.left !== null && pair.right === null) ||
            (pair.left === null && pair.right !== null)
        );
      }
      // if (!this.pairs.hasOwnProperty(Number(this.idCurrentAnswer))) {
      //   return 0;
      // } else {
      //   return this.pairs[Number(this.idCurrentAnswer)].findIndex((pair) => pair.left === null || pair.right === null);
      // }
    },
  },
  mounted() {
    window.addEventListener("resize", this.handleResize);
    this.checkLineOption();
    localStorage.setItem("show_modal_promotion", "1");

    window.addEventListener("scroll", this.checkVisibility);
    this.checkVisibility();



    if (this.dataLesson.type !== "flashcard") {
      this.dataLesson.component.forEach((question) => {
        this.loadAudioDuration(question);
      });
    }

    if (this.dataLesson?.component[0]?.type === TYPE_LESSON.SPEAKING) {
      setTimeout(() => {
        this.initWaveSurfer();
      }, 1000);
    }

    // $("#modalDoneProgressGroup").modal("show");
    // $("#modalAchievement").modal("show");
    this.initTimer();

    //kiểm tra có phần tử có id my-video
    if ($("#my-video").length) {
      this.intervalCheckTimeWatchVideo = setInterval(() => {
        let time_watch_video = localStorage.getItem(`totalTime-${this.dataLesson.course_id}`);
        if (parseInt(time_watch_video) === 180) {
          clearInterval(this.intervalCheckTimeWatchVideo);
          this.showPopupRate = true;
          localStorage.setItem(`totalTime-${this.dataLesson.course_id}`, parseInt(time_watch_video) + 1);
        } else if (time_watch_video > 180) {
          clearInterval(this.intervalCheckTimeWatchVideo);
        }
      }, 1000);
    }
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    window.removeEventListener("scroll", this.checkVisibility);
    this.stopPulseEffect();

    // Clear interval khi component bị huỷ
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    if (this.intervalCheckTimeWatchVideo) {
      clearInterval(this.intervalCheckTimeWatchVideo);
    }
  },
});
