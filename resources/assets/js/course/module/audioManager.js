// audioManager.js
import WaveSurfer from "wavesurfer.js";

class AudioManager {
    constructor() {
        this.audioInstances = new Map(); // <PERSON><PERSON><PERSON> các instance của Audio
        this.waveSurferInstances = new Map(); // <PERSON><PERSON>u các instance của WaveSurfer
        this.currentPlaying = null; // <PERSON> âm thanh đang phát
    }

    // Thêm một instance Audio
    addAudio(id, audio) {
        this.audioInstances.set(id, audio);
        audio.onplay = () => this.handlePlay(id, "audio");
    }

    // Thêm một instance WaveSurfer
    addWaveSurfer(id, waveSurfer) {
        this.waveSurferInstances.set(id, waveSurfer);
        waveSurfer.on("play", () => this.handlePlay(id, "wavesurfer"));
    }

    // Xử lý khi một âm thanh được phát
    handlePlay(id, type) {
        if (this.currentPlaying && this.currentPlaying.id !== id) {
            this.stopCurrent();
        }
        this.currentPlaying = { id, type };
    }

    // Dừng âm thanh hiện tại
    stopCurrent() {
        if (!this.currentPlaying) return;

        const { id, type } = this.currentPlaying;
        if (type === "audio") {
            const audio = this.audioInstances.get(id);
            if (audio) {
                audio.pause();
                audio.currentTime = 0; // Reset về đầu nếu cần
            }
        } else if (type === "wavesurfer") {
            const waveSurfer = this.waveSurferInstances.get(id);
            if (waveSurfer) {
                waveSurfer.pause();
                waveSurfer.setTime(0); // Reset về đầu nếu cần
            }
        }
        this.currentPlaying = null;
    }

    // Phát một file MP3 bằng Audio
    playAudio(id, url) {
        let audio = this.audioInstances.get(id);
        if (!audio) {
            audio = new Audio(url);
            this.addAudio(id, audio);
        }
        this.stopCurrent();
        audio.play();
    }

    // Phát một file MP3 bằng WaveSurfer
    playWaveSurfer(id, container, url) {
        let waveSurfer = this.waveSurferInstances.get(id);
        if (!waveSurfer) {
            waveSurfer = WaveSurfer.create({
                container,
                waveColor: "#f8b079",
                progressColor: "#EF6D13",
                barWidth: 2,
                height: 20,
            });
            this.addWaveSurfer(id, waveSurfer);
        }
        this.stopCurrent();
        waveSurfer.load(url);
        waveSurfer.on("ready", () => waveSurfer.play());
    }

    // Dừng tất cả âm thanh
    stopAll() {
        this.audioInstances.forEach(audio => {
            audio.pause();
            audio.currentTime = 0;
        });
        this.waveSurferInstances.forEach(waveSurfer => {
            waveSurfer.pause();
            waveSurfer.setTime(0);
        });
        this.currentPlaying = null;
    }
}

export default new AudioManager();