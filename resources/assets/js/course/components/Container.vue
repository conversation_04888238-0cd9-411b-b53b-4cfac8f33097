<template>
  <div>
    <div>
      <button
          class="btn-show-menu fixed duration-500 top-20 sp:top-[72px] w-[46px] h-[44px] border-none rounded-l-full hover:w-[75px] bg-white text-left z-50"
          :class="isActive ? 'right-[-200px]' : 'right-0'"
          style="box-shadow: 0px 2px 4px 0px #4c5d703d"
          @click="isActive = !isActive"
      >
        <img
            class="h-[14px] rotate-180 relative left-[15px]"
            src="/images/icons/hide.png"
            alt="hide.png"
        />
        <span
            class="tooltip-menu absolute text-[#07403F] bg-[#C1EACA] px-3 whitespace-nowrap top-[8px] py-1 text-sm font-averta-regular rounded-full duration-500"
        >Mở danh mục bên</span
        >
      </button>
      <div
          id="container-list-course"
          class="fixed duration-500 top-[76px] sp:top-[72px] bg-white w-[450px] h-[calc(100vh-70px)] sp:w-full z-50 sp:bottom-0"
          :class="isActive ? 'right-0' : 'right-[-1500px]'"
      >
        <div class="p-4 relative z-50 menu-content">
          <SearchBar
              v-on:update-search="(value) => (search = value)"
              v-on:hide-menu="hideMenu"
              :current-tab="currentTab"
          />
          <template v-if="currentTab == 'lesson'">
            <LessonList
                v-show="search.length <= 0"
                :stage-name="stageName"
                :is-unlock="isUnlock"
                :exam-stage="examStage"
            />
            <SearchList :stage-name="stageName" :current-lesson="currentLesson" :search="search"
                        v-show="search.length > 0"/>
          </template>
          <template v-if="currentTab == 'comment'">
            <Comment
                :auth="authUser"
                :current-flashcard="currentFlashcard"
                v-on:update-unread="getTotalUnread"
                v-on:comment-added="handleCommentAdded"
            />
          </template>
        </div>
        <Footer
            :current-tab="currentTab"
            :total-unread="totalUnread"
            :auth="authUser"
            :type="type"
            v-on:update-tab="(value) => (currentTab = value)"
        />
      </div>
    </div>
  </div>
</template>
<script>
import SearchBar from "./SearchBar.vue";
import SearchList from "./SearchList.vue";
import LessonList from "./LessonList.vue";
import Comment from "./Comment.vue";
import Footer from "./Footer.vue";
import axios from "axios";

export default {
  name: "container",
  props: ["isUnlock", "stageName", "type", "examStage"],
  data() {
    return {
      isActive: !(window.screen.width < 1024 || ['exam', 'last_exam', 'flashcard'].includes(this.type)),
      search: "",
      currentTab: "lesson",
      totalUnread: 0,
      // Data from lesson basic new blade
      authUser: authUser,
      currentLesson: currentLesson,
      currentFlashcard: null, // Thẻ flashcard đang hiển thị
    };
  },
  components: {
    SearchBar,
    SearchList,
    LessonList,
    Comment,
    Footer,
  },
  mounted() {
    this.getTotalUnread();
    let fixedElement = document.getElementById('container-list-course');
    let popup = document.getElementById('explanationPopup');

    function alignPopup() {
      const fixedRect = fixedElement.getBoundingClientRect();
      if (parseInt($("#container-list-course").css("right"), 10) === 0) {
        popup.style.right = `${window.innerWidth - fixedRect.right}px`;
        popup.style.maxWidth = `${fixedRect.right - fixedRect.width}px`;
      } else {
        popup.style.right = '0';
        popup.style.maxWidth = '100vw';
      }
    }

    alignPopup();
    window.addEventListener('resize', alignPopup);

    document.addEventListener('open-comment-tab', (event) => {
      const eventData = event.detail || {};
      const handle = eventData.handle || 'open';
      const flashcard = eventData.flashcard;

      if (flashcard) {
        this.setCurrentFlashcard(flashcard);
      }

      if (handle === 'open') {
        this.isActive = true;
        this.currentTab = 'comment';
      } else if (handle === 'close') {
        this.isActive = false;
        this.currentTab = 'lesson';
      } else if (handle === 'toggle') {
        this.isActive = !this.isActive;
        if (this.isActive) {
          this.currentTab = 'comment';
        }
      }
    });
  },
  watch: {
    isActive(newVal, oldVal) {
      if (newVal) {
        $("#lesson-main").removeClass("menu-hidden");
        $("#explanationPopup").css("max-width", `${screen.width - 465}px`);
        $("#explanationPopup").css("right", '15px');
        $("#screen-mobile-lesson").removeClass('w-full')
        $("#screen-mobile-lesson").addClass('w-2/3')


        if (this.currentTab === 'comment' && this.currentLesson.type === 'flashcard') {
          this.sendEventGA('flc_comment');
        }
      } else {
        $("#lesson-main").addClass("menu-hidden");
        $("#explanationPopup").css("max-width", `${screen.width}px`);
        $("#explanationPopup").css("right", 0);
        $("#screen-mobile-lesson").addClass('w-full')
        $("#screen-mobile-lesson").removeClass('w-2/3')
      }
      this.$emit("active-changed", this.isActive);
    },
    currentFlashcard: {
      handler(newVal, oldVal) {
        if (newVal && (!oldVal || newVal.id !== oldVal.id)) {
          this.getTotalUnread()
        }
      },
      deep: true
    },
    currentTab(newVal, oldVal) {
      if (newVal === 'comment' && this.isActive && this.currentLesson.type === 'flashcard') {
        this.sendEventGA('flc_comment');
      }
    },
  },
  methods: {
    hideMenu() {
      this.isActive = false;
    },

    // Cập nhật thẻ flashcard hiện tại
    setCurrentFlashcard(flashcard) {
      this.currentFlashcard = flashcard;
    },

    // Xử lý khi có bình luận mới được thêm
    handleCommentAdded(data) {
      const customEvent = new CustomEvent('comment-added', {
        detail: {
          flashcardId: data.flashcardId,
          comment: data.comment
        }
      });
      document.dispatchEvent(customEvent);

      // this.getTotalUnread();
    },

    getTotalUnread() {
      if (this.currentLesson.type === 'flashcard' && this.isActive === 0) return;
      const self = this;
      axios
          .get(
              `/api/comment/total-unread-replies?lesson_id=${currentLesson.id}&type=${this.currentLesson.type}&flashcard_id=${this.currentFlashcard?.id}`,
              {
                withCredentials: true,
              }
          )
          .then((response) => {
            self.totalUnread = response.data;
          })
          .catch((error) => {
            console.log(error);
          });
    },

    async sendEventGA(event = null) {
      let version = process.env.NODE_ENV;

      if (![39, 40, 44, 45, 46].includes(parseInt(this.currentLesson.course_id)) || parseInt(this.authUser.isTester) === 1 || !['production', 'prod'].includes(version)) {
        return;
      }
      let course_name = '';
      switch (parseInt(this.currentLesson.course_id)) {
        case 39:
          course_name = 'n5';
          break;
        case 40:
          course_name = 'n4';
          break;
        case 44:
          course_name = 'n3';
          break;
        case 45:
          course_name = 'n2';
          break;
        case 46:
          course_name = 'n1';
          break;
        default:
          break;
      }
      if (!event) {
        event = `${course_name}_flc_behind`;
      } else {
        event = `${course_name}_${event}`;
      }
      ga("send", "event", `nx_${event}`, event, event);
    },
  },
};
</script>
