<template>
  <div class="relative">
    <Reply
      v-model="val"
      class="bg-[#F5F5F5] text-[#1E1E1E] placeholder:text-[#757575] p-3 pr-[35px] rounded"
      style="width: calc(100% - 10px)"
    />
    <img
      class="cursor-pointer w-[20px] h-[20px] absolute right-[15px] -translate-y-1/2"
      style="top: calc(50% - 3px)"
      :src="
        val.length > 0
          ? '/images/icons/send-available.png'
          : '/images/icons/send.png'
      "
      @click="sendReply"
      alt="send"
    />
  </div>
</template>
<script>
import Reply from "./Textarea.vue";
import axios from "axios";
export default {
  props: ["comment", "auth", "currentFlashcard"],
  data() {
    return {
      // Data from lesson basic new blade
      currentLesson: currentLesson,
      val: "",
      isLoading: false,
    };
  },
  components: {
    Reply,
  },
  methods: {
    sendReply() {
      const self = this;
      if (self.isLoading || self.val.length == 0) return;
      self.isLoading = true;
      axios
        .post("/api/comments/add-new-reply", {
          tbid: self.currentLesson.type === 'flashcard' ? self.currentFlashcard.id : self.currentLesson.id,
          tbname: this.currentLesson.type === 'flashcard' ? 'flashcard' : "lesson",
          parent_id: self.comment.id,
          content: self.val,
        })
        .then((response) => {
          self.val = "";
          self.$emit("update-reply", response.data);
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          self.isLoading = false;
        });
    },
  },
};
</script>
