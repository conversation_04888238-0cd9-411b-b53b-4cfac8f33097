<template>
  <div>
<!--    <div class="flex items-center justify-center pb-4">-->
<!--      <div class="font-averta-bold text-[16px] text-[#EF6D13] underline text-bold cursor-pointer"-->
<!--           onclick="swichTab('login')">Đăng nhập-->
<!--      </div>-->
<!--      <div class="text-#1E1E1E text-[16px] font-averta-regular">-->
<!--        để thêm bình luận-->
<!--      </div>-->
<!--    </div>-->
    <div
        class="flex items-center p-4"
        style="box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1)"
    >
      <div
          class="rounded-full w-[44px] h-[44px] flex-none flex items-center justify-center"
          :class="tab === 'lesson' ? 'bg-[#C1EACA]' : ''"
          @click="tab = 'lesson'"
          id="menuTabLessonList"
      >
        <img
            class="w-[22px] h-[22px]"
            src="/images/icons/lesson-list.png"
            alt="lesson list"
        />
      </div>
      <div
          v-if="!['exam', 'last_exam'].includes(type)"
          class="rounded-full w-[44px] h-[44px] flex-none flex items-center justify-center relative mr-3"
          :class="tab === 'comment' ? 'bg-[#C1EACA]' : ''"
          @click="tab = 'comment'"
          id="menuTabComment"
      >
        <img
            class="w-[22px] h-[22px]"
            src="/images/icons/comment.png"
            alt="comment"
        />
        <span
            v-if="auth.id"
            class="text-white font-averta-regular text-xs px-[5px] leading-[14px] absolute top-[6px] right-[4px] rounded-[5px]"
            :class="totalUnread > 0 ? 'bg-[#EF6D13]' : 'bg-[#D9D9D9]'"
        >{{ totalUnread }}</span
        >
      </div>
      <div v-if="nextLesson" class="ml-auto w-full">
        <a
            :href="`/khoa-hoc/${nextLesson.course_slug}/lesson/${nextLesson.id}-${nextLesson.SEOurl}`"
            class="ml-auto w-[99%] max-w-[219px] border-2 border-[#57D061] text-[#57D061] hover:text-[#57D061] font-averta-regular text-sm h-[44px] px-10 flex items-center justify-center rounded-full"
        >
          <div class="truncate">
            {{ nextLesson.stage_name }} >>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ["currentTab", "totalUnread", "auth", "type"],
  data() {
    return {
      nextLesson: nextLesson,
      user: userLesson,
    };
  },
  computed: {
    tab: {
      get() {
        return this.currentTab;
      },
      set(value) {
        this.$emit("update-tab", value);
      },
    },
  },
  mounted() {
    var urlParams = new URLSearchParams(window.location.search);
    var myParam = urlParams.get('ref');
    if (myParam === 'notice') {
      this.tab = 'comment';
    }
  }
};
</script>
