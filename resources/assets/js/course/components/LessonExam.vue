<template>
  <div
    v-if="windowWidth <= 640"
    id="screen-mobile"
    class="text-center flex flex-wrap justify-center"
  >
    <img src="/images/lessons/exercise-mobile.png" class="object-contain" />
    <a
      id="downloadApp"
      :href="
        checkDevice() === 'ios'
          ? 'https://apps.apple.com/us/app/id1486123836'
          : 'https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp'
      "
      class="mt-[80px] font-beanbag text-[20px] text-white bg-[#EF6D13] text-bold px-6 pt-[14px] pb-[11px] rounded-full w-[260px] uppercase drop-shadow-2xl"
    >
      tải app ngay
    </a>
  </div>
  <div
    v-else
    class="examApp"
    :class="[
      active ? 'w-[calc(100vw-465px)]' : 'w-screen',
      lesson.type === 'exam' && stage === 5 ? 'h-full' : 'h-auto',
    ]"
  >
    <div
      v-if="stage === 0"
      class="w-full h-[68px] fixed -top-[190px] left-0 -z-1"
    >
      <div
        class="w-[1000px] h-[230px] mx-auto rounded-[50%] bg-[#57D06180] blur-3xl"
      ></div>
    </div>
    <div
      v-if="stage > 0 && stage < 4"
      class="fixed top-0 left-[calc(50%-500px)] w-[1000px] mx-auto flex justify-center items-center h-[68px] z-[110]"
      v-cloak
    >
      <div v-if="!isWaiting" class="flex items-center gap-3">
        <div
          class="font-averta-bold text-[40px]"
          :class="[diff <= 300000 ? 'text-[#C00F0C]' : 'text-[#757575]']"
          v-cloak
          @click="savePassedTime()"
        >
          {{ timerDisplay.minute }}:{{ timerDisplay.second }}
        </div>
        <svg
          v-if="diff <= 300000"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          v-cloak
        >
          <path
            opacity="0.4"
            d="M12.0001 22.0002C16.7884 22.0002 20.6701 18.1185 20.6701 13.3302C20.6701 8.54185 16.7884 4.66016 12.0001 4.66016C7.21177 4.66016 3.33008 8.54185 3.33008 13.3302C3.33008 18.1185 7.21177 22.0002 12.0001 22.0002Z"
            fill="#EC221F"
          />
          <path
            d="M12 13.75C11.59 13.75 11.25 13.41 11.25 13V8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V13C12.75 13.41 12.41 13.75 12 13.75Z"
            fill="#EC221F"
          />
          <path
            d="M14.8899 3.45H9.10989C8.70989 3.45 8.38989 3.13 8.38989 2.73C8.38989 2.33 8.70989 2 9.10989 2H14.8899C15.2899 2 15.6099 2.32 15.6099 2.72C15.6099 3.12 15.2899 3.45 14.8899 3.45Z"
            fill="#EC221F"
          />
        </svg>
        <svg
          v-else
          width="40"
          height="40"
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          v-cloak
        >
          <path
            opacity="0.4"
            d="M19.9998 36.6666C27.9803 36.6666 34.4498 30.1971 34.4498 22.2166C34.4498 14.2361 27.9803 7.7666 19.9998 7.7666C12.0193 7.7666 5.5498 14.2361 5.5498 22.2166C5.5498 30.1971 12.0193 36.6666 19.9998 36.6666Z"
            fill="#5A5A5A"
          />
          <path
            d="M20 22.9163C19.3167 22.9163 18.75 22.3497 18.75 21.6663V13.333C18.75 12.6497 19.3167 12.083 20 12.083C20.6833 12.083 21.25 12.6497 21.25 13.333V21.6663C21.25 22.3497 20.6833 22.9163 20 22.9163Z"
            fill="#5A5A5A"
          />
          <path
            d="M24.8167 5.74967H15.1834C14.5167 5.74967 13.9834 5.21634 13.9834 4.54967C13.9834 3.88301 14.5167 3.33301 15.1834 3.33301H24.8167C25.4834 3.33301 26.0167 3.86634 26.0167 4.53301C26.0167 5.19967 25.4834 5.74967 24.8167 5.74967Z"
            fill="#5A5A5A"
          />
        </svg>
      </div>
    </div>
    <div v-if="stage === 0" class="w-full pt-[85px] pb-[100px] bg-[#F4F5FA]">
      <div
        class="min-w-[768px] max-w-[1320px] rounded-[30px] mx-auto shadow-[0px_-22px_31.5px_-19px_rgba(33,33,33,0.11)] py-[20px] flex flex-col justify-center items-center z-2 bg-gradient-to-b from-white from-10% via-transparent via-30% to-transparent to-90% to-transparent"
      >
        <img src="/assets/img/exam-overview.svg" />
        <div class="flex items-start gap-1 mt-[28px]">
          <div class="font-zuume-semibold uppercase text-[38px] text-[#07403F]">
            {{ lesson.name }}
          </div>

          <svg
            v-if="lesson.require"
            width="16"
            height="15"
            viewBox="0 0 16 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="mt-2"
          >
            <path
              d="M6.09789 1.8541C6.69659 0.0114813 9.30341 0.0114808 9.90211 1.8541L10.2451 2.90983C10.5129 3.73387 11.2808 4.2918 12.1473 4.2918H13.2573C15.1948 4.2918 16.0003 6.77103 14.4329 7.90983L13.5348 8.56231C12.8339 9.07159 12.5405 9.97433 12.8083 10.7984L13.1513 11.8541C13.75 13.6967 11.6411 15.229 10.0736 14.0902L9.17557 13.4377C8.4746 12.9284 7.5254 12.9284 6.82443 13.4377L5.92638 14.0902C4.35895 15.229 2.24999 13.6967 2.84869 11.8541L3.19172 10.7984C3.45947 9.97433 3.16615 9.07159 2.46517 8.56231L1.56712 7.90983C-0.000308037 6.77103 0.805244 4.2918 2.74269 4.2918H3.85275C4.7192 4.2918 5.48711 3.73387 5.75486 2.90983L6.09789 1.8541Z"
              fill="#EC6E23"
            />
          </svg>
        </div>
        <div
          v-if="lesson.type === 'last_exam'"
          class="font-averta-regular text-[14px] text-[#57D061]"
          v-cloak
        >
          Mô phỏng đề thi JLPT thực tế
        </div>
        <div
          v-if="!loading && lesson.type === 'exam'"
          class="flex gap-8 font-averta-regular mt-[50px] text-[#1E1E1E]"
          v-cloak
        >
          <div class="flex items-center gap-2">
            <svg
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.8332 9.33333C13.8332 12.5533 11.2198 15.1667 7.99984 15.1667C4.77984 15.1667 2.1665 12.5533 2.1665 9.33333C2.1665 6.11333 4.77984 3.5 7.99984 3.5C11.2198 3.5 13.8332 6.11333 13.8332 9.33333Z"
                stroke="#57D061"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M8 5.83301V9.16634"
                stroke="#57D061"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M6 1.83301H10"
                stroke="#57D061"
                stroke-width="1.5"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <div v-if="examData?.exam_parts" class="text-[#000000]">
              {{ examData?.exam_parts[0].duration }} phút
            </div>
          </div>
          <div class="flex items-center gap-2">
            <svg
              width="16"
              height="17"
              viewBox="0 0 16 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="1.3335"
                y="1.83301"
                width="13.3333"
                height="13.3333"
                rx="4"
                stroke="#57D061"
                stroke-width="1.5"
              />
              <path
                d="M11.7466 6.91309H8.24658C7.97325 6.91309 7.74658 6.68642 7.74658 6.41309C7.74658 6.13975 7.97325 5.91309 8.24658 5.91309H11.7466C12.0199 5.91309 12.2466 6.13975 12.2466 6.41309C12.2466 6.68642 12.0266 6.91309 11.7466 6.91309Z"
                fill="#57D061"
              />
              <path
                d="M4.74687 7.42021C4.6202 7.42021 4.49354 7.37354 4.39354 7.27354L3.89354 6.77354C3.7002 6.58021 3.7002 6.26021 3.89354 6.06688C4.08687 5.87354 4.40687 5.87354 4.6002 6.06688L4.74687 6.21354L5.89353 5.06688C6.08687 4.87354 6.40687 4.87354 6.6002 5.06688C6.79353 5.26021 6.79353 5.58021 6.6002 5.77354L5.1002 7.27354C5.00687 7.36687 4.8802 7.42021 4.74687 7.42021Z"
                fill="#57D061"
              />
              <path
                d="M11.7466 11.5801H8.24658C7.97325 11.5801 7.74658 11.3534 7.74658 11.0801C7.74658 10.8067 7.97325 10.5801 8.24658 10.5801H11.7466C12.0199 10.5801 12.2466 10.8067 12.2466 11.0801C12.2466 11.3534 12.0266 11.5801 11.7466 11.5801Z"
                fill="#57D061"
              />
              <path
                d="M4.74687 12.0862C4.6202 12.0862 4.49354 12.0396 4.39354 11.9396L3.89354 11.4396C3.7002 11.2462 3.7002 10.9262 3.89354 10.7329C4.08687 10.5396 4.40687 10.5396 4.6002 10.7329L4.74687 10.8796L5.89353 9.73289C6.08687 9.53956 6.40687 9.53956 6.6002 9.73289C6.79353 9.92622 6.79353 10.2462 6.6002 10.4396L5.1002 11.9396C5.00687 12.0329 4.8802 12.0862 4.74687 12.0862Z"
                fill="#57D061"
              />
            </svg>
            <div class="text-[#000000]">
              {{
                tasks.filter((task) => questionTypes.includes(task.type)).length
              }}
              câu
            </div>
          </div>
        </div>
        <div
          v-if="lesson.type === 'last_exam' && examData"
          class="grid grid-cols-3 gap-[50px] mt-[50px]"
          v-cloak
        >
          <div v-for="(part, idx) in examLevels[level]">
            <div class="font-beanbag text-[#07403F] text-[14px] uppercase">
              {{ part.label }}
            </div>
            <div class="flex gap-8 font-averta-regular mt-3">
              <div class="flex items-center gap-2">
                <svg
                  width="16"
                  height="17"
                  viewBox="0 0 16 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M13.8332 9.33333C13.8332 12.5533 11.2198 15.1667 7.99984 15.1667C4.77984 15.1667 2.1665 12.5533 2.1665 9.33333C2.1665 6.11333 4.77984 3.5 7.99984 3.5C11.2198 3.5 13.8332 6.11333 13.8332 9.33333Z"
                    stroke="#57D061"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8 5.83301V9.16634"
                    stroke="#57D061"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6 1.83301H10"
                    stroke="#57D061"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <div class="text-[#000000]">
                  {{ examData.exam_parts[idx].duration }} phút
                </div>
              </div>
              <div class="flex items-center gap-2">
                <svg
                  width="16"
                  height="17"
                  viewBox="0 0 16 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    x="1.3335"
                    y="1.83301"
                    width="13.3333"
                    height="13.3333"
                    rx="4"
                    stroke="#57D061"
                    stroke-width="1.5"
                  />
                  <path
                    d="M11.7466 6.91309H8.24658C7.97325 6.91309 7.74658 6.68642 7.74658 6.41309C7.74658 6.13975 7.97325 5.91309 8.24658 5.91309H11.7466C12.0199 5.91309 12.2466 6.13975 12.2466 6.41309C12.2466 6.68642 12.0266 6.91309 11.7466 6.91309Z"
                    fill="#57D061"
                  />
                  <path
                    d="M4.74687 7.42021C4.6202 7.42021 4.49354 7.37354 4.39354 7.27354L3.89354 6.77354C3.7002 6.58021 3.7002 6.26021 3.89354 6.06688C4.08687 5.87354 4.40687 5.87354 4.6002 6.06688L4.74687 6.21354L5.89353 5.06688C6.08687 4.87354 6.40687 4.87354 6.6002 5.06688C6.79353 5.26021 6.79353 5.58021 6.6002 5.77354L5.1002 7.27354C5.00687 7.36687 4.8802 7.42021 4.74687 7.42021Z"
                    fill="#57D061"
                  />
                  <path
                    d="M11.7466 11.5801H8.24658C7.97325 11.5801 7.74658 11.3534 7.74658 11.0801C7.74658 10.8067 7.97325 10.5801 8.24658 10.5801H11.7466C12.0199 10.5801 12.2466 10.8067 12.2466 11.0801C12.2466 11.3534 12.0266 11.5801 11.7466 11.5801Z"
                    fill="#57D061"
                  />
                  <path
                    d="M4.74687 12.0862C4.6202 12.0862 4.49354 12.0396 4.39354 11.9396L3.89354 11.4396C3.7002 11.2462 3.7002 10.9262 3.89354 10.7329C4.08687 10.5396 4.40687 10.5396 4.6002 10.7329L4.74687 10.8796L5.89353 9.73289C6.08687 9.53956 6.40687 9.53956 6.6002 9.73289C6.79353 9.92622 6.79353 10.2462 6.6002 10.4396L5.1002 11.9396C5.00687 12.0329 4.8802 12.0862 4.74687 12.0862Z"
                    fill="#57D061"
                  />
                </svg>
                <div class="text-[#000000]">
                  {{ examData.exam_parts[idx].questions.length }} câu
                </div>
              </div>
            </div>
            <div
              v-if="last_result && !last_result?.submit_at"
              class="mt-[57px] font-averta-regular text-[14px] flex items-center gap-2"
              :class="[
                last_result.passed_time[idx] !== -1 &&
                ((last_result.time_start[idx] > 0 &&
                  last_result.time_start[idx + 1] === 0) ||
                  (last_result.time_start[idx] > 0 && idx === 2) ||
                  (last_result.passed_time[idx] === 0 &&
                    last_result.passed_time[idx - 1] === -1))
                  ? 'text-[#BF6A02]'
                  : 'text-[#757575]',
              ]"
            >
              <div
                v-if="
                  last_result.passed_time[idx] !== -1 &&
                  (
                    (last_result.time_start[idx] > 0 && last_result.time_start[idx + 1] === 0) ||
                    (last_result.time_start[idx] > 0 && idx === 2) ||
                    (last_result.passed_time[idx] === 0 && last_result.passed_time[idx - 1] === -1)
                  )
                "
                class="ring-container mr-2"
              >
                <div class="ringring"></div>
                <div class="circle"></div>
              </div>
              {{
                last_result[`data_${idx + 1}`]
                  ? Object.keys(JSON.parse(last_result[`data_${idx + 1}`]))
                      .length
                  : 0
              }}/{{ examData.exam_parts[idx].questions.length }}
            </div>
            <!--            <div-->
            <!--              class="flex items-center gap 2 text-[#EF6D13] font-averta-regular mt-2"-->
            <!--            >-->
            <!--              {{-->
            <!--                part.type === 3-->
            <!--                  ? "— Kết thúc bài thi —"-->
            <!--                  : "— Nghỉ 10 phút giữa giờ —"-->
            <!--              }}-->
            <!--            </div>-->
          </div>
        </div>
        <div
          v-if="lesson.type === 'exam' && !result"
          class="font-averta-regular text-[#757575] mt-[90px]"
        >
          Điểm đạt: ≥85% ({{
            Math.ceil(
              tasks.filter((task) => [3, 13, 14, 15].includes(task.type))
                .length * 0.85
            )
          }}
          /
          {{
            tasks.filter((task) => [3, 13, 14, 15].includes(task.type)).length
          }}
          câu)
        </div>
        <div
          v-if="lesson.type === 'last_exam' && !result"
          class="font-averta-regular text-[#757575] mt-[90px]"
        >
          - Điểm đạt: {{ lesson.pass_marks }} / {{ lesson.total_marks }} -
        </div>
        <div v-if="result" v-cloak class="w-[700px]">
          <div
            class="rounded-[24px] bg-[#FFF9F9] w-[700px] flex flex-col items-center pt-[18px] mt-[93px] shadow-lg"
          >
            <div class="w-[281px]">
              <div class="flex justify-between items-center">
                <div class="font-averta-semibold text-[16px] text-[#000000]">
                  Kết quả
                </div>
                <div
                  class="font-averta-regular text-[16px] px-[32px] py-1 rounded-full"
                  :class="[
                    result && result?.is_passed
                      ? 'bg-[#C1EACA]'
                      : 'bg-[#E8B931]',
                  ]"
                  v-cloak
                >
                  {{ result && result?.is_passed ? "Đạt" : "Chưa đạt" }}
                </div>
              </div>
              <div class="flex justify-between items-center">
                <div class="font-averta-semibold text-[16px] text-[#000000]">
                  Điểm của bạn
                </div>
                <div
                  v-if="lesson.type === 'exam'"
                  class="font-averta text-[16px] py-1"
                  v-cloak
                >
                  {{ resultStatistics?.correctQuestions.length }} /
                  {{ resultStatistics?.questions?.length }} ({{
                    result?.is_passed ? ">=85%" : "<85%"
                  }})
                </div>
                <div
                  v-else
                  class="font-averta text-[16px] py-1 text-[#000000]"
                  v-cloak
                >
                  {{ result?.total_score || 0 }} / {{ lesson.total_marks || 0 }}
                </div>
              </div>
              <div class="flex justify-start items-center gap-2">
                <div class="font-averta-regular text-[14px] text-[#757575]">
                  Điểm đạt:
                </div>
                <div
                  v-if="lesson.type === 'exam'"
                  class="font-averta-regular text-[14px] text-[#757575]"
                  v-cloak
                >
                  {{
                    Math.ceil(
                      tasks.filter((task) => questionTypes.includes(task.type))
                        .length * 0.85
                    )
                  }}
                  /
                  {{
                    tasks.filter((task) => questionTypes.includes(task.type))
                      .length
                  }}
                </div>
                <div
                  v-else
                  class="font-averta-regular text-[14px] text-[#757575]"
                  v-cloak
                >
                  {{ lesson?.pass_marks || 0 }} / {{ lesson.total_marks || 0 }}
                </div>
              </div>
            </div>
            <div
              class="w-full rounded-full flex justify-center items-center py-[14px] border-[#57D061] border-[2px] mt-[14px] font-beanbag text-[#57D061] uppercase cursor-pointer bg-[#F4F5FA]"
              @click="stage = 5"
            >
              Xem bài làm gần đây nhất
            </div>
          </div>
        </div>
        <div
          v-if="
            last_result &&
            (last_result?.time_start[0] > 0 ||
              last_result?.passed_time[0] === -1) &&
            !last_result.submit_at
          "
          class="flex items-center gap-8"
          v-cloak
        >
          <div
            class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center bg-white text-[#07403F] rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
            @click="startExam()"
            v-cloak
          >
            Bắt đầu lại
          </div>
          <div
            class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center bg-[#57D061] text-[#07403F] rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
            @click="continueExam()"
          >
            Tiếp tục
          </div>
        </div>
        <div v-else-if="result" class="flex items-center justify-center gap-8">
          <div
            class="select-none w-[335px] font-beanbag text-[16px] py-[16px] flex justify-center items-center text-[#07403F] rounded-full mt-[28px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
            :class="[!result?.is_passed ? 'bg-[#FFF193]' : 'bg-white']"
            @click="startExam()"
          >
            Làm lại
          </div>
          <div
            v-if="nextLesson"
            class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center bg-[#57D061] text-[#07403F] rounded-full mt-[28px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase hover:text-[#07403F]"
            @click="goToNextLesson()"
          >
            Bài tiếp theo
          </div>
        </div>
        <div
          v-else-if="!last_result"
          class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center bg-[#57D061] text-[#07403F] rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
          v-cloak
          @click="startExam()"
        >
          <i v-if="start_loading" class="fa fa-spinner fa-spin"></i>
          <span v-else v-cloak>Bắt đầu</span>
        </div>
        <div
          v-if="
            (last_result ||
              (last_result && result) ||
              (!last_result && !result)) &&
            nextLesson
          "
          class="mt-[28px]"
          @click="goToNextLesson()"
          ><u class="font-beanbag text-[#07403F] cursor-pointer uppercase"
            >Bài tiếp theo ></u
          ></div
        >
        <i
          class="font-averta-regular text-[16px] text-center max-w-[270px] mt-8"
        >
          *Tính năng <b>bình luận</b> sẽ không được sử dụng trong lúc làm bài
        </i>
      </div>
    </div>

    <div
      v-if="stage > 0 && stage < 4 && !isWaiting"
      id="examWrapper"
      v-cloak
      class="bg-[#F4F5FA] w-full"
    >
      <div
        class="h-[88px] w-full fixed bottom-0 left-0 bg-white shadow-md z-50"
      >
        <div
          :class="[
            active ? 'w-[calc(100vw-465px)]' : 'w-full max-w-[1200px] mx-auto',
          ]"
          class="h-[88px] flex items-center justify-between px-5 xl:px-0"
        >
          <div
            class="w-[100px] rounded-full border-2 cursor-pointer inline-flex justify-center items-center p-3"
            :class="[
              currentMondai === 0
                ? 'border-[#D9D9D9] text-[#D9D9D9]'
                : 'border-[#57D061] text-[#57D061] hover:brightness-110',
            ]"
            @click="prevMondai()"
          >
            <svg
              width="9"
              height="16"
              viewBox="0 0 9 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="stroke-current"
            >
              <path
                d="M7.86918 14.0398L1.92627 7.96177L7.86918 1.88379"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div class="font-beanbag text-[20px] text-[#757575]">
            もんだい {{ currentMondai + 1 }}：{{ mondaiQuestion.first }}→{{
              mondaiQuestion.last
            }}
          </div>
          <div
            class="w-[100px] rounded-full border-2 cursor-pointer rotate-180 inline-flex justify-center items-center p-3"
            :class="[
              currentMondai === currentMondais.length - 1
                ? 'border-[#D9D9D9] text-[#D9D9D9]'
                : 'border-[#57D061] text-[#57D061] hover:brightness-110',
            ]"
            @click="nextMondai()"
          >
            <svg
              width="9"
              height="16"
              viewBox="0 0 9 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              class="stroke-current"
            >
              <path
                d="M7.86918 14.0398L1.92627 7.96177L7.86918 1.88379"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div
            v-if="nextLesson && active"
            class="h-full ml-auto absolute bottom-0 right-5 flex items-center min-w-[250px]"
          >
            <a
              :href="`/khoa-hoc/${nextLesson.course_slug}/lesson/${nextLesson.id}-${nextLesson.SEOurl}`"
              class="ml-auto w-[99%] max-w-[219px] border-2 border-[#57D061] text-[#57D061] hover:text-[#57D061] font-averta-regular text-sm h-[44px] px-10 flex items-center justify-center rounded-full"
            >
              <div class="truncate">{{ nextLesson.stage_name }} >></div>
            </a>
          </div>
        </div>
      </div>
      <div
        :class="[active ? 'w-[calc(100vw-465px)] px-5' : 'w-full max-w-[1200px] pl-2 pr-4']"
        class="pt-[75px] pb-[200px] mx-auto"
      >
        <div
          class="flex items-center gap-4 sticky top-[80px] z-10 transition-all"
          :class="[sticky ? 'bg-white p-1 rounded-full' : '']"
        >
          <div
            class="flex items-center gap-2 py-2 px-3 rounded-full border border-[#757575] font-beanbag text-[14px] flex-shrink-0 text-[#757575]"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14.6831 8.0166H10.3081C9.96644 8.0166 9.68311 7.73327 9.68311 7.3916C9.68311 7.04993 9.96644 6.7666 10.3081 6.7666H14.6831C15.0248 6.7666 15.3081 7.04993 15.3081 7.3916C15.3081 7.73327 15.0331 8.0166 14.6831 8.0166Z"
                fill="#757575"
              />
              <path
                d="M5.93346 8.65026C5.77513 8.65026 5.6168 8.59193 5.4918 8.46693L4.8668 7.84193C4.62513 7.60026 4.62513 7.20026 4.8668 6.95859C5.10846 6.71693 5.50846 6.71693 5.75013 6.95859L5.93346 7.14193L7.3668 5.70859C7.60846 5.46693 8.00846 5.46693 8.25013 5.70859C8.4918 5.95026 8.4918 6.35026 8.25013 6.59193L6.37513 8.46693C6.25846 8.58359 6.10013 8.65026 5.93346 8.65026Z"
                fill="#757575"
              />
              <path
                d="M14.6836 13.8496H10.3086C9.96693 13.8496 9.68359 13.5663 9.68359 13.2246C9.68359 12.8829 9.96693 12.5996 10.3086 12.5996H14.6836C15.0253 12.5996 15.3086 12.8829 15.3086 13.2246C15.3086 13.5663 15.0336 13.8496 14.6836 13.8496Z"
                fill="#757575"
              />
              <path
                d="M5.93346 14.4833C5.77513 14.4833 5.6168 14.4249 5.4918 14.2999L4.8668 13.6749C4.62513 13.4333 4.62513 13.0333 4.8668 12.7916C5.10846 12.5499 5.50846 12.5499 5.75013 12.7916L5.93346 12.9749L7.3668 11.5416C7.60846 11.2999 8.00846 11.2999 8.25013 11.5416C8.4918 11.7833 8.4918 12.1833 8.25013 12.4249L6.37513 14.2999C6.25846 14.4166 6.10013 14.4833 5.93346 14.4833Z"
                fill="#757575"
              />
            </svg>
            <div>
              {{ mondaiQuestion.first }}-{{ mondaiQuestion.last }}/{{
                currentQuestions.length
              }}
            </div>
          </div>
          <div class="w-full bg-[#D9D9D9] rounded-full h-2.5 flex-grow-1">
            <div
              class="bg-[#2CD868] h-2.5 rounded-full"
              :style="{
                width: `${
                  (Object.keys(currentStageAnswers).length * 100) /
                  currentQuestions.length
                }%`,
              }"
            ></div>
          </div>
          <div
            class="w-[100px] p-3 font-beanbag text-[14px] text-white uppercase bg-[#57D061] text-center rounded-full flex-shrink-0 cursor-pointer hover:shadow-md"
            @click="showSubmitExam()"
          >
            Nộp bài
          </div>
        </div>
        <div class="mt-[30px]">
          <div class="grid grid-cols-4 gap-3 relative">
            <div v-if="sticky" class="col-span-1"></div>
            <div
              class="col-span-1 top-[138px] flex flex-col items-start justify-start self-start z-10 h-[calc(100vh-238px)]"
              :class="[sticky ? (active ? 'fixed w-[calc(((100vw-465px)/4)-12px)]' : 'fixed w-[290px]') : 'static w-full']"
              id="examStickyTop"
            >
              <VuePerfectScrollbar
                :id="`examScroll-${stage}`"
                class="w-full h-full relative h-full"
                :settings="scrollBarSettings"
              >
                <div
                  v-if="lesson.type === 'last_exam'"
                  class="font-beanbag uppercase text-[#07403F] text-[14px]"
                >
                  {{
                    ["Từ vựng - Chữ Hán", "Ngữ pháp - Đọc hiểu", "Nghe hiểu"][
                      stage - 1
                    ]
                  }}
                </div>
                <div
                  class="bg-[#C1EACA] text-[14px] font-medium text-black rounded-[10px] px-4 py-1 inline-flex items-center justify-center font-averta-regular mt-2"
                >
                  Đã làm
                  <b
                    class="ml-1"
                    :class="[
                      Object.keys(currentStageAnswers).length ===
                      currentQuestions.length
                        ? 'text-[#009951]'
                        : 'text-[#EF6D13]',
                    ]"
                    >{{ Object.keys(currentStageAnswers).length }}</b
                  ><b class="text-[#009951] mr-1">
                    /{{ currentQuestions.length }}</b
                  >
                  câu
                </div>
                <div v-for="(mondai, idx) in currentMondais">
                  <div
                    class="px-2 py-1 flex items-center gap-1 font-gen-jyuu-gothic-medium mt-10 text-[#212121]"
                    :class="[
                      idx === currentMondai
                        ? 'border-l-2 border-[#EF6D13]'
                        : 'border-l border-black',
                    ]"
                  >
                    もんだい
                    <div
                      class="bg-black w-4 h-4 rounded-full inline-flex justify-center items-center text-white font-beanbag text-[14px]"
                    >
                      {{ idx + 1 }}
                    </div>
                  </div>
                  <div class="mt-3 flex flex-wrap gap-2 w-[175px]">
                    <div
                      v-for="(question, qIdx) in mondai.questions"
                      :key="question.id"
                      class="rounded-full w-[28px] h-[28px] inline-flex justify-center items-center text-[#07403F] font-beanbag text-[16px] cursor-pointer"
                      :class="[
                        examQuestionState(
                          question.type,
                          currentStageAnswers[question.id]
                        )
                          ? 'bg-[#B2EEFA]'
                          : 'bg-white',
                      ]"
                      @click="jumpToQuestion(idx, question.id)"
                    >
                      {{
                        stage === 3
                          ? qIdx + 1
                          : stageData.questions.findIndex(
                              (q) => q.id === question.id
                            ) + 1
                      }}
                    </div>
                  </div>
                </div>
              </VuePerfectScrollbar>
            </div>
            <div class="col-span-2">
              <div v-if="lesson.type == 'last_exam' && stageData && stageData.mp3">
                <div class="text-[#C00F0C]">
                  *Lưu ý: File nghe sẽ phát 1 lần duy nhất
                </div>
                <svg class="hidden">
                  <symbol
                    id="backward"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"
                    ></path>
                    <path d="M5 15V19"></path>
                    <path
                      d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523
            10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"
                    ></path>
                  </symbol>

                  <symbol id="play" viewBox="0 0 24 24">
                    <path
                      d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                      fill="#4E87FF"
                    />
                  </symbol>

                  <symbol id="pause" viewBox="0 0 24 24">
                    <path
                      d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                      fill="#4E87FF"
                    />
                    <path
                      d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                      fill="#4E87FF"
                    />
                  </symbol>

                  <symbol id="forward" viewBox="0 0 24 24">
                    <path
                      d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                    <path
                      d="M13 15V19"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                    <path
                      d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18
                          19H17C16.4477 19 16 18.5523 16 18Z"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                  </symbol>

                  <symbol id="high" viewBox="0 0 24 24">
                    <path
                      d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                      fill="#4E87FF"
                    />
                    <path
                      d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                      fill="#4E87FF"
                    />
                    <path
                      opacity="0.4"
                      d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                      fill="#4E87FF"
                    />
                    <path
                      d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                      fill="#4E87FF"
                    />
                  </symbol>

                  <symbol id="off" viewBox="0 0 24 24">
                    <path
                      opacity="0.4"
                      d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                      fill="#4E87FF"
                    />
                    <path
                      d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                      fill="#4E87FF"
                    />
                  </symbol>
                </svg>
                <div
                  class="my-5 px-5 py-[10px] bg-[#E1EBFF] rounded-full items-center shadow-xl shadow-black/5 w-full flex items-center justify-between gap-3"
                >
                  <svg
                    v-if="!mp3Playing"
                    width="19"
                    height="20"
                    viewBox="0 0 19 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="cursor-pointer"
                    @click="togglePlay()"
                  >
                    <path
                      d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                      fill="#4E87FF"
                    />
                  </svg>
                  <svg
                    v-else
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="cursor-pointer"
                    @click="togglePlay()"
                  >
                    <path
                      d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                      fill="#4E87FF"
                    />
                    <path
                      d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                      fill="#4E87FF"
                    />
                  </svg>
                  <div
                    class="text-[#4E87FF] font-averta-regular text-[14px] w-[60px]"
                  >
                    {{ formatTime(mp3CurrentTime) }}
                  </div>
                  <div
                    class="relative bg-white flex-grow h-[4px] w-full rounded-full"
                  >
                    <div
                      class="absolute h-[4px] bg-[#4E87FF] rounded-full"
                      :style="{
                        width: `${(mp3CurrentTime * 100) / mp3Duration}%`,
                      }"
                    ></div>
                  </div>
                  <div class="text-[#4E87FF] font-averta-regular text-[14px]">
                    {{ formatTime(mp3Duration) }}
                  </div>
                  <svg
                    width="29"
                    height="28"
                    viewBox="0 0 29 28"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                      fill="#4E87FF"
                    />
                    <path
                      d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                      fill="#4E87FF"
                    />
                    <path
                      opacity="0.4"
                      d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                      fill="#4E87FF"
                    />
                    <path
                      d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                      fill="#4E87FF"
                    />
                  </svg>
                </div>
              </div>
              <div v-if="currentMondais.length">
                <div v-if="lesson.type == 'exam' && currentMondais[currentMondai].mp3">
                  <svg class="hidden">
                    <symbol
                      id="backward"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path
                        d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"
                      ></path>
                      <path d="M5 15V19"></path>
                      <path
                        d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523
            10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"
                      ></path>
                    </symbol>

                    <symbol id="play" viewBox="0 0 24 24">
                      <path
                        d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                        fill="#4E87FF"
                      />
                    </symbol>

                    <symbol id="pause" viewBox="0 0 24 24">
                      <path
                        d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                        fill="#4E87FF"
                      />
                      <path
                        d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                        fill="#4E87FF"
                      />
                    </symbol>

                    <symbol id="forward" viewBox="0 0 24 24">
                      <path
                        d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      ></path>
                      <path
                        d="M13 15V19"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      ></path>
                      <path
                        d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18
                          19H17C16.4477 19 16 18.5523 16 18Z"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      ></path>
                    </symbol>

                    <symbol id="high" viewBox="0 0 24 24">
                      <path
                        d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                        fill="#4E87FF"
                      />
                      <path
                        d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                        fill="#4E87FF"
                      />
                      <path
                        opacity="0.4"
                        d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                        fill="#4E87FF"
                      />
                      <path
                        d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                        fill="#4E87FF"
                      />
                    </symbol>

                    <symbol id="off" viewBox="0 0 24 24">
                      <path
                        opacity="0.4"
                        d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                        fill="#4E87FF"
                      />
                      <path
                        d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                        fill="#4E87FF"
                      />
                    </symbol>
                  </svg>
                  <div
                    class="my-5 px-5 py-[10px] bg-[#E1EBFF] rounded-full shadow-xl shadow-black/5 w-full flex items-center justify-between gap-3"
                  >
                    <svg
                      v-if="!mp3Playing"
                      width="19"
                      height="20"
                      viewBox="0 0 19 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      class="cursor-pointer"
                      @click="togglePlay()"
                    >
                      <path
                        d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                        fill="#4E87FF"
                      />
                    </svg>
                    <svg
                      v-else
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      class="cursor-pointer"
                      @click="togglePlay()"
                    >
                      <path
                        d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                        fill="#4E87FF"
                      />
                      <path
                        d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                        fill="#4E87FF"
                      />
                    </svg>
                    <div
                      class="text-[#4E87FF] font-averta-regular text-[14px] w-[60px]"
                    >
                      {{ formatTime(mp3CurrentTime) }}
                    </div>
                    <div
                      class="relative bg-white flex-grow h-[4px] w-full rounded-full"
                    >
                      <div
                        class="absolute h-[4px] bg-[#4E87FF] rounded-full"
                        :style="{
                        width: `${(mp3CurrentTime * 100) / mp3Duration}%`,
                      }"
                      ></div>
                    </div>
                    <div class="text-[#4E87FF] font-averta-regular text-[14px]">
                      {{ formatTime(mp3Duration) }}
                    </div>
                    <svg
                      width="29"
                      height="28"
                      viewBox="0 0 29 28"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                        fill="#4E87FF"
                      />
                      <path
                        d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                        fill="#4E87FF"
                      />
                      <path
                        opacity="0.4"
                        d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                        fill="#4E87FF"
                      />
                      <path
                        d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                        fill="#4E87FF"
                      />
                    </svg>
                  </div>
                </div>
                <div v-for="task in currentMondais[currentMondai].tasks">
                  <div
                    v-if="task.type === 1"
                    class="font-gen-jyuu-gothic text-[16px] mb-[26px] text-[#212121] leading-[2.2]"
                    v-html="task.value"
                  ></div>
                  <div class="flex items-center justify-between">
                    <div></div>
                    <div
                      class="relative group mt-10"
                      v-if="
                        (task.type === 13 &&
                          isValidJSON(task.value) &&
                          JSON.parse(task.value).suggest) ||
                        (task.type === 3 && task.suggest != null)
                      "
                    >
                      <button
                        id="button_suggest"
                        class="bg-[#F4F5FA] w-auto text-[14px] text-[#57D061] border-[2px] border-[#57D061] text-bold px-4 py-1 rounded-full mr-2.5"
                      >
                        <svg
                          width="16"
                          height="14"
                          viewBox="0 0 16 14"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            opacity="0.4"
                            d="M3.73935 9.33317C3.81101 9.04734 3.68071 8.639 3.45268 8.43484L1.8695 7.01734C1.37435 6.574 1.17889 6.1015 1.32222 5.69317C1.47207 5.28484 1.93465 5.00484 2.62526 4.89984L4.65798 4.5965C4.95116 4.54984 5.3095 4.3165 5.44632 4.07734L6.56692 2.06484C6.89268 1.48734 7.33571 1.1665 7.81783 1.1665C8.29995 1.1665 8.74298 1.48734 9.06874 2.06484L10.1893 4.07734C10.274 4.229 10.45 4.37484 10.6389 4.474L3.62207 10.7565C3.53086 10.8382 3.3745 10.7623 3.40056 10.6457L3.73935 9.33317Z"
                            fill="#57D061"
                          />
                          <path
                            d="M12.1832 8.4351C11.9487 8.6451 11.8184 9.0476 11.8966 9.33344L12.3461 11.0893C12.535 11.8184 12.4178 12.3668 12.0138 12.6293C11.8509 12.7343 11.6555 12.7868 11.4275 12.7868C11.0952 12.7868 10.7043 12.6759 10.2743 12.4484L8.36534 11.4334C8.06564 11.2759 7.57049 11.2759 7.2708 11.4334L5.36186 12.4484C4.63867 12.8276 4.01974 12.8918 3.62231 12.6293C3.47246 12.5301 3.36171 12.3959 3.29004 12.2209L11.2125 5.1276C11.5122 4.85927 11.9356 4.73677 12.3461 4.80094L13.0041 4.9001C13.6947 5.0051 14.1573 5.2851 14.3072 5.69344C14.4505 6.10177 14.255 6.57427 13.7599 7.0176L12.1832 8.4351Z"
                            fill="#57D061"
                          />
                        </svg>
                        Gợi ý
                      </button>
                      <!-- Tooltip -->
                      <div
                        :class="`max-w-[350px] 2xl:max-w-[500px] absolute right-[100%] top-1/2 transform -translate-y-1/2 rounded-full text-black text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 overflow-hidden`"
                      >
                        <div class="flex items-center rounded-full">
                          <div
                            :class="`px-4 py-2 bg-[#57D061] bg-green-300 rounded-full font-gen-jyuu-gothic break-words`"
                            :style="{ width: renderWidthTooltipSuggest(task) }"
                            v-html="
                              task.type === 13
                                ? JSON.parse(task.value).suggest
                                : task.suggest
                            "
                          ></div>
                          <div class="top right-[-9px] top-[50%]">
                            <svg
                              fill="#57D061"
                              width="12"
                              height="12"
                              viewBox="8 -2.56 37.12 37.12"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              stroke="#57D061"
                              transform="matrix(1, 0, 0, 1, 0, 0)rotate(0)"
                              stroke-width="0.00032"
                            >
                              <g id="SVGRepo_iconCarrier">
                                <title>play</title>
                                <path
                                  d="M5.92 24.096q0 1.088 0.928 1.728 0.512 0.288 1.088 0.288 0.448 0 0.896-0.224l16.16-8.064q0.48-0.256 0.8-0.736t0.288-1.088-0.288-1.056-0.8-0.736l-16.16-8.064q-0.448-0.224-0.896-0.224-0.544 0-1.088 0.288-0.928 0.608-0.928 1.728v16.16z"
                                />
                              </g>
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <template v-if="task.type === 3">
                    <svg class="hidden">
                      <symbol
                        id="backward"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"
                        ></path>
                        <path d="M5 15V19"></path>
                        <path
                          d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523
            10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"
                        ></path>
                      </symbol>

                      <symbol id="play" viewBox="0 0 24 24">
                        <path
                          d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                          fill="#4E87FF"
                        />
                        <path
                          d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                          fill="#4E87FF"
                        />
                        <path
                          opacity="0.4"
                          d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                          fill="#4E87FF"
                        />
                        <path
                          d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                          fill="#4E87FF"
                        />
                      </symbol>

                      <symbol id="pause" viewBox="0 0 24 24">
                        <path
                          opacity="0.4"
                          d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                          fill="#4E87FF"
                        />
                        <path
                          d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                          fill="#4E87FF"
                        />
                      </symbol>

                      <symbol id="forward" viewBox="0 0 24 24">
                        <path
                          d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        ></path>
                        <path
                          d="M13 15V19"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        ></path>
                        <path
                          d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18
                          19H17C16.4477 19 16 18.5523 16 18Z"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        ></path>
                      </symbol>

                      <symbol id="high" viewBox="0 0 24 24"></symbol>

                      <symbol id="off" viewBox="0 0 24 24"></symbol>
                    </svg>
                    <div
                      :id="`question_${task.id}`"
                      class="py-5 px-4 bg-white border border-[#D9D9D9] rounded-[16px] font-gen-jyuu-gothic mt-5 text-[16px] text-[#000000] leading-[1.80]"
                    >
                      <div
                        v-if="task.audio"
                        class="my-5 w-full flex items-center justify-between gap-3"
                      >
                        <svg
                          v-if="mp3[task.id] && mp3[task.id].playing && mp3[task.id].object && mp3[task.id].object.src === `https://mp3-v2.dungmori.com/${task.audio}`"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="cursor-pointer"
                          @click.stop="togglePlayAnswer(task.audio, task.id)"
                        >
                          <path
                            d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                            fill="#4E87FF"
                          />
                          <path
                            d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                            fill="#4E87FF"
                          />
                        </svg>
                        <svg
                          v-else
                          width="19"
                          height="20"
                          viewBox="0 0 19 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="cursor-pointer"
                          @click.stop="togglePlayAnswer(task.audio, task.id)"
                        >
                          <path
                            d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                            fill="#4E87FF"
                          />
                        </svg>
                        <div
                          class="text-[#4E87FF] font-averta-regular text-[14px] w-[60px]"
                        >
                          {{ formatTime(mp3[task.id] ? mp3[task.id].currentTime : 0) }}
                        </div>
                        <div
                          class="relative bg-white border flex-grow h-[4px] w-full rounded-full"
                        >
                          <div
                            class="absolute h-[4px] bg-[#4E87FF] rounded-full"
                            :style="{
                        width: `${((mp3[task.id] ? mp3[task.id].currentTime : 0) * 100) / (mp3[task.id] ? mp3[task.id].duration : 0)}%`,
                      }"
                          ></div>
                        </div>
                        <div class="text-[#4E87FF] font-averta-regular text-[14px]">
                          {{ formatTime(mp3[task.id] ? mp3[task.id].duration : 0) }}
                        </div>
                      </div>
                      <div class="flex items-start">
                        <div
                          v-if="lesson.type === 'exam'"
                          class="text-[16px] mr-2"
                        >
                          {{
                            stageData.questions.findIndex(
                              (q) => q.id === task.id
                            ) + 1
                          }}.
                        </div>
                        <div class="text-[16px]" v-html="task.value"></div>
                      </div>
                    </div>
                    <div
                      class="mt-[68px] grid gap-7 mb-[102px]"
                      :class="[
                        task.answers.filter(
                          (a) => isLong(a)
                        ).length
                          ? 'grid-cols-1'
                          : 'grid-cols-2',
                      ]"
                    >
                      <div
                        v-for="(answer, aIdx) in task.answers"
                        class="flex gap-2 group hover:bg-[#B2EEFA] cursor-pointer py-1 px-1"
                        :class="[
                          Number(currentStageAnswers[task.id]) ===
                          Number(answer.id)
                            ? 'bg-[#B1EEFA]'
                            : 'bg-white',
                          answer.value.includes('img')
                            ? 'items-start rounded-[10px]'
                            : 'items-center rounded-full',
                        ]"
                        @click="selectAnswer(task, answer)"
                      >
                        <div
                          class="group-hover:border-[#B1EEFA] w-10 h-10 border-[3px] rounded-full bg-white text-[#757575] font-beanbag text-[16px] flex justify-center items-center shadow-[0px 0px 18px 0px #21212114] font-bold flex-shrink-0"
                          :class="[
                            Number(currentStageAnswers[task.id]) ===
                            Number(answer.id)
                              ? 'border-[#B1EEFA]'
                              : 'border-[#D9D9D9]',
                          ]"
                        >
                          {{ aIdx + 1 }}
                        </div>
                        <svg class="hidden">
                          <symbol
                            id="backward"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          >
                            <path
                              d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"
                            ></path>
                            <path d="M5 15V19"></path>
                            <path
                              d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523
            10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"
                            ></path>
                          </symbol>
                          <symbol id="play" viewBox="0 0 24 24">
                            <path
                              d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                              fill="#4E87FF"
                            />
                            <path
                              d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                              fill="#4E87FF"
                            />
                            <path
                              opacity="0.4"
                              d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                              fill="#4E87FF"
                            />
                            <path
                              d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                              fill="#4E87FF"
                            />
                          </symbol>

                          <symbol id="pause" viewBox="0 0 24 24">
                            <path
                              opacity="0.4"
                              d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                              fill="#4E87FF"
                            />
                            <path
                              d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                              fill="#4E87FF"
                            />
                          </symbol>

                          <symbol id="forward" viewBox="0 0 24 24">
                            <path
                              d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            ></path>
                            <path
                              d="M13 15V19"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            ></path>
                            <path
                              d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18
                          19H17C16.4477 19 16 18.5523 16 18Z"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            ></path>
                          </symbol>

                          <symbol id="high" viewBox="0 0 24 24"></symbol>

                          <symbol id="off" viewBox="0 0 24 24"></symbol>
                        </svg>
                        <template v-if="answer.audio">
                          <svg
                            v-if="answerMp3Playing && answerMp3Object && answerMp3Object.src === `https://mp3-v2.dungmori.com/${answer.audio}`"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="cursor-pointer"
                            @click.stop="togglePlayAnswer(answer.audio)"
                          >
                            <path
                              d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                              fill="#4E87FF"
                            />
                            <path
                              d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                              fill="#4E87FF"
                            />
                          </svg>
                          <svg
                            v-else
                            width="19"
                            height="20"
                            viewBox="0 0 19 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="cursor-pointer"
                            @click.stop="togglePlayAnswer(answer.audio)"
                          >
                            <path
                              d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                              fill="#4E87FF"
                            />
                          </svg>

                        </template>
                        <div
                          v-else
                          v-html="answer.value"
                          class="font-gen-jyuu-gothic text-[16px] text-wrap text-[#212121]"
                        ></div>
                      </div>
                    </div>
                  </template>
                  <template v-if="task.type === 13">
                    <div
                      class="border border-[#D9D9D9] rounded-[24px] px-[24px] py-[40px] my-5 bg-white shadow"
                    >
                      <svg class="hidden">
                        <symbol
                          id="backward"
                          viewBox="0 0 24 24"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <path
                            d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"
                          ></path>
                          <path d="M5 15V19"></path>
                          <path
                            d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523
            10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"
                          ></path>
                        </symbol>

                        <symbol id="play" viewBox="0 0 24 24">
                          <path
                            d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                            fill="#4E87FF"
                          />
                          <path
                            d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                            fill="#4E87FF"
                          />
                          <path
                            opacity="0.4"
                            d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                            fill="#4E87FF"
                          />
                          <path
                            d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                            fill="#4E87FF"
                          />
                        </symbol>

                        <symbol id="pause" viewBox="0 0 24 24">
                          <path
                            opacity="0.4"
                            d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                            fill="#4E87FF"
                          />
                          <path
                            d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                            fill="#4E87FF"
                          />
                        </symbol>

                        <symbol id="forward" viewBox="0 0 24 24">
                          <path
                            d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188"
                            stroke-width="1.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          ></path>
                          <path
                            d="M13 15V19"
                            stroke-width="1.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          ></path>
                          <path
                            d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18
                          19H17C16.4477 19 16 18.5523 16 18Z"
                            stroke-width="1.5"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          ></path>
                        </symbol>

                        <symbol id="high" viewBox="0 0 24 24"></symbol>

                        <symbol id="off" viewBox="0 0 24 24"></symbol>
                      </svg>
                      <template v-if="JSON.parse(task.value)?.audio">
                        <svg
                          v-if="answerMp3Playing && answerMp3Object && answerMp3Object.src === `https://mp3-v2.dungmori.com/${JSON.parse(task.value)?.audio}`"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="cursor-pointer"
                          @click.stop="togglePlayAnswer(JSON.parse(task.value)?.audio)"
                        >
                          <path
                            d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                            fill="#4E87FF"
                          />
                          <path
                            d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                            fill="#4E87FF"
                          />
                        </svg>
                        <svg
                          v-else
                          width="19"
                          height="20"
                          viewBox="0 0 19 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="cursor-pointer"
                          @click.stop="togglePlayAnswer(JSON.parse(task.value)?.audio)"
                        >
                          <path
                            d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                            fill="#4E87FF"
                          />
                        </svg>
                      </template>
                      <img
                        v-if="JSON.parse(task.value)?.img"
                        :src="`/cdn/lesson/default/${
                          JSON.parse(task.value)?.img
                        }`"
                        class="mt-5 mx-auto"
                      />
                      <div
                        :id="`question_${task.id}`"
                        class="font-beanbag mt-5 text-[16px]"
                      >
                        {{ task.text_vi }}
                      </div>
                      <div class="flex items-end flex-wrap gap-2 mt-4">
                        <div
                          class="font-gen-jyuu-gothic text-[16px] translate-y-[-10px] text-[#1E1E1E]"
                        >
                          {{
                            stageData.questions.findIndex(
                              (q) => q.id === task.id
                            ) + 1
                          }}.
                        </div>
                        <template
                          v-for="(block, bIdx) in JSON.parse(task.value)
                            .question"
                        >
                          <div
                            v-if="block.type === 'default'"
                            v-html="block.value"
                            class="font-gen-jyuu-gothic text-[16px] translate-y-[-10px] text-[#1E1E1E]"
                          ></div>
                          <input
                            v-model="task.blockAnswers[bIdx]"
                            v-if="block.type === 'question'"
                            class="bg-[#D9D9D9] font-gen-jyuu-gothic text-[16px] text-[#1E1E1E] p-3 rounded-[12px] text-center focus:outline-[3px] focus:outline-[#4E87FF]"
                            :style="{
                              width: `${block.result.length * 20 + 24}px`,
                            }"
                            @blur="submitBlocks(task, bIdx, $event)"
                          />
                        </template>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="stage > 0 && stage < 3 && isWaiting"
      v-cloak
      class="bg-[#F4F5FA] w-full pt-[68px] flex justify-center items-center flex-col"
    >
      <div class="font-beanbag text-[20px] text-[#07403F]">
        Phần thi tiếp theo
      </div>
      <div class="font-beanbag text-[40px] text-[#57D061]">
        {{ ["Từ vựng - Chữ Hán", "Ngữ pháp - Đọc hiểu", "Nghe hiểu"][stage] }}
      </div>
      <img
        :src="`/images/lessons/nghi-${Math.floor(Math.random() * 9) + 1}.png`"
        class="my-[97px]"
      />
      <div
        class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center bg-[#57D061] text-[#07403F] rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
        @click="nextStage()"
      >
        Tiếp tục làm bài
      </div>
    </div>
    <div v-cloak v-if="stage === 4" class="w-full pt-[68px]">
      <div
        class="w-[700px] flex justify-center items-center flex-col rounded-[20px] shadow-lg mx-auto px-4 bg-[#fff] py-10"
      >
        <div class="font-averta-regular text-[#C00F0C] text-[14px]">
          Hết giờ
        </div>
        <div
          class="flex items-center gap-1 font-averta-semibold text-[#C00F0C] text-[20px] mt-[10px]"
        >
          <svg
            width="21"
            height="21"
            viewBox="0 0 21 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              opacity="0.4"
              d="M10.4998 19.2496C14.6896 19.2496 18.0861 15.8532 18.0861 11.6634C18.0861 7.47363 14.6896 4.07715 10.4998 4.07715C6.31005 4.07715 2.91357 7.47363 2.91357 11.6634C2.91357 15.8532 6.31005 19.2496 10.4998 19.2496Z"
              fill="#C92326"
            />
            <path
              d="M10.5 12.0312C10.1413 12.0312 9.84375 11.7337 9.84375 11.375V7C9.84375 6.64125 10.1413 6.34375 10.5 6.34375C10.8587 6.34375 11.1562 6.64125 11.1562 7V11.375C11.1562 11.7337 10.8587 12.0312 10.5 12.0312Z"
              fill="#C92326"
            />
            <path
              d="M13.0288 3.01875H7.97131C7.62131 3.01875 7.34131 2.73875 7.34131 2.38875C7.34131 2.03875 7.62131 1.75 7.97131 1.75H13.0288C13.3788 1.75 13.6588 2.03 13.6588 2.38C13.6588 2.73 13.3788 3.01875 13.0288 3.01875Z"
              fill="#C92326"
            />
          </svg>
          <div>00:00:00</div>
        </div>
        <hr class="w-full" />
        <img src="/assets/img/otsukare.svg" class="mt-[28px]" />
        <div
          class="text-[#07403F] font-averta-regular text-[14px] mt-[12px] text-center"
        >
          Bạn đã vất vả rồi!<br />
          Cùng xem kết quả nào
        </div>
        <div
          class="bg-[#57D061] text-center text-white font-beanbag text-[16px] rounded-full px-[50px] py-[14px] shadow-md cursor-pointer mt-[25px] min-w-[218px] uppercase"
          @click="stage = 5"
        >
          Xem kết quả
        </div>
      </div>
    </div>
    <div
      v-if="stage === 5"
      v-cloak
      class="bg-gradient-to-br w-full flex justify-start items-center flex-col relative pb-[10px] relative"
      :class="[
        result?.is_passed
          ? 'from-[#E6FFE9] via-[#F3F5F9] to-[#E5FFE9]'
          : 'from-[#FFFDF0] via-[#FFFFFF] to-[#FFFDF0]',
        lesson.type === 'exam' && stage === 5 ? 'h-full' : 'h-auto',
      ]"
    >
      <div
        class="bg-gradient-to-r w-screen h-[76px] absolute top-[-76px] left-0 z-0"
        :class="[
          result?.is_passed
            ? 'from-[#E6FFE9] to-[#F3F5F9]'
            : 'from-[#FFFDF0] to-[#FFFFFF]',
        ]"
      ></div>
      <template v-if="result && lesson.type === 'exam'">
        <div
          class="font-beanbag text-[#07403F] text-[24px] mt-[40px] mb-[80px]"
        >
          <span
            class="inline-flex px-2 py-0 rounded-full bg-[#CEFFD8]"
            :class="result.is_passed ? 'bg-[#CEFFD8]' : 'bg-[#FFE8A3]'"
            >{{ resultStatistics?.correctQuestions?.length }}/{{
              resultStatistics.questions?.length
            }}</span
          >
          câu đúng! <br />
          <span v-if="result.is_passed" v-cloak>Bạn làm tốt lắm!</span>
          <span v-else v-cloak>Cố gắng lên nhé!</span>
        </div>
        <img
          v-if="result.is_passed"
          src="/assets/img/exam_passed.svg"
          v-cloak
        />
        <img v-else src="/assets/img/exam_failed.svg" v-cloak />
      </template>
      <template v-else>
        <img
          v-if="result?.total_score === lesson.total_marks"
          src="/assets/img/perfect_flower.svg"
          class="select-none w-full object-cover absolute top-0 left-0 z-1 opacity-30"
        />
        <img
          v-else-if="result?.is_passed"
          src="/assets/img/passed_flower.svg"
          class="select-none w-full object-cover absolute top-0 left-0 z-[-1] opacity-30"
        />
        <img
          v-else="result?.is_passed"
          src="/assets/img/failed_flower.svg"
          class="select-none absolute bottom-0 right-0 -z-1 opacity-30"
        />
        <div
          class="font-beanbag text-[36px] text-[#07403F] uppercase flex items-center justify-center gap-1 my-10"
        >
          {{ lesson.name }}
          <svg
            v-if="lesson.require"
            width="16"
            height="15"
            viewBox="0 0 16 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.09789 1.8541C6.69659 0.0114813 9.30341 0.0114808 9.90211 1.8541L10.2451 2.90983C10.5129 3.73387 11.2808 4.2918 12.1473 4.2918H13.2573C15.1948 4.2918 16.0003 6.77103 14.4329 7.90983L13.5348 8.56231C12.8339 9.07159 12.5405 9.97433 12.8083 10.7984L13.1513 11.8541C13.75 13.6967 11.6411 15.229 10.0736 14.0902L9.17557 13.4377C8.4746 12.9284 7.5254 12.9284 6.82443 13.4377L5.92638 14.0902C4.35895 15.229 2.24999 13.6967 2.84869 11.8541L3.19172 10.7984C3.45947 9.97433 3.16615 9.07159 2.46517 8.56231L1.56712 7.90983C-0.000308037 6.77103 0.805244 4.2918 2.74269 4.2918H3.85275C4.7192 4.2918 5.48711 3.73387 5.75486 2.90983L6.09789 1.8541Z"
              fill="#EC6E23"
            />
          </svg>
        </div>
        <div class="w-[950px] flex justify-end">
          <div class="inline-flex items-center gap-2 bg-[#CCF8D1] rounded-[10px] p-2 cursor-pointer mb-5" @click="saveResultImage()">
            <div class="font-averta-regular text-[16px] text-[#07403F]">{{ saveImageLoading ? 'Đang lưu' : 'Lưu ảnh kết quả' }}</div>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15.26 22.2503H8.73998C3.82998 22.2503 1.72998 20.1503 1.72998 15.2403V15.1103C1.72998 10.6703 3.47998 8.53027 7.39998 8.16027C7.79998 8.13027 8.17998 8.43027 8.21998 8.84027C8.25998 9.25027 7.95998 9.62027 7.53998 9.66027C4.39998 9.95027 3.22998 11.4303 3.22998 15.1203V15.2503C3.22998 19.3203 4.66998 20.7603 8.73998 20.7603H15.26C19.33 20.7603 20.77 19.3203 20.77 15.2503V15.1203C20.77 11.4103 19.58 9.93027 16.38 9.66027C15.97 9.62027 15.66 9.26027 15.7 8.85027C15.74 8.44027 16.09 8.13027 16.51 8.17027C20.49 8.51027 22.27 10.6603 22.27 15.1303V15.2603C22.27 20.1503 20.17 22.2503 15.26 22.2503Z" fill="#07403F"/>
              <path d="M12 15.63C11.59 15.63 11.25 15.29 11.25 14.88V2C11.25 1.59 11.59 1.25 12 1.25C12.41 1.25 12.75 1.59 12.75 2V14.88C12.75 15.3 12.41 15.63 12 15.63Z" fill="#07403F"/>
              <path d="M11.9998 16.7498C11.8098 16.7498 11.6198 16.6798 11.4698 16.5298L8.11984 13.1798C7.82984 12.8898 7.82984 12.4098 8.11984 12.1198C8.40984 11.8298 8.88984 11.8298 9.17984 12.1198L11.9998 14.9398L14.8198 12.1198C15.1098 11.8298 15.5898 11.8298 15.8798 12.1198C16.1698 12.4098 16.1698 12.8898 15.8798 13.1798L12.5298 16.5298C12.3798 16.6798 12.1898 16.7498 11.9998 16.7498Z" fill="#07403F"/>
            </svg>
          </div>
        </div>

        <div
          id="resultCanvas"
          class="w-[950px] bg-white shadow-[0_0_1.35px_0_#4C5D7052] shadow-[0_21.68px_32.52px_0_#617C9A52] rounded-[22px] relative px-[25px] py-[10px]"
        >
          <img
            src="/assets/img/logo-bang.svg"
            class="absolute bottom-0 left-0 h-[80%]"
          />
          <div class="px-4 bg-white absolute bottom-2 left-2 text-[#07403F]">
            {{
              result?.submit_at
                ? moment(result?.submit_at).format("DD/MM/YYYY")
                : "--"
            }}
          </div>

          <div
            class="flex items-center justify-between border-b-2 border-[#C1EACA] pb-2"
          >
            <img src="/assets/img/logo-bang-nho.svg" class="" />
            <div class="flex flex-col items-end">
              <div>
                <ruby class="font-gen-jyuu-gothic text-[15px] text-[#07403F]"
                  ><rb>試験</rb><rp>(</rp><rt>しけん</rt><rp>)</rp></ruby
                >
                <ruby class="font-gen-jyuu-gothic text-[15px] text-[#07403F]"
                  ><rb>結果</rb><rp>(</rp><rt>けっか</rt><rp>)</rp></ruby
                >
                <ruby class="font-gen-jyuu-gothic text-[15px] text-[#07403F]"
                  ><rb>発表</rb><rp>(</rp><rt>はっぴょう</rt><rp>)</rp></ruby
                >
              </div>
              <div class="font-averta-regular text-[18px] text-[#07403F]">
                Kết quả bài thi
              </div>
            </div>
          </div>
          <div
            class="py-4 border-b-2 border-[#C1EACA] flex flex-col gap-1 select-none"
          >
            <div class="flex items-center justify-between">
              <div class="flex flex-col items-start">
                <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]"
                  ><rb>氏名</rb><rp>(</rp><rt>しめい</rt><rp>)</rp></ruby
                >
                <div class="font-averta-regular text-[16px] text-[#07403F]">
                  Họ tên
                </div>
              </div>
              <div
                class="w-[750px] min-h-[54px] flex items-center justify-center border-2 border-[#07403F] font-beanbag text-[20px] uppercase rounded-[22px] py-3 z-10 text-[#07403F]"
              >
                {{ result?.user?.name || "" }}
              </div>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex flex-col items-start">
                <div class="font-gen-jyuu-gothic text-[20px] text-[#07403F]">
                  レベル
                </div>
                <div class="font-averta-regular text-[16px] text-[#07403F]">
                  Cấp độ
                </div>
              </div>
              <div
                class="w-[750px] min-h-[54px] flex items-center justify-center border-2 border-[#07403F] font-beanbag text-[20px] uppercase rounded-[22px] py-3 z-10 text-[#07403F]"
              >
                {{ result.course.slice(-2) }}
              </div>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex flex-col items-start">
                <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]">
                  <rb>試験</rb><rp>(</rp><rt>しけん</rt><rp>)</rp> <rb>結果</rb
                  ><rp>(</rp><rt>けっか</rt><rp>)</rp>
                </ruby>
                <div class="font-averta-regular text-[16px] text-[#07403F]">
                  Kết quả thi
                </div>
              </div>
              <div
                class="w-[750px] min-h-[54px] flex items-center justify-center border-2 border-[#07403F] rounded-[22px] py-0 z-10"
                :class="[result?.is_passed ? 'bg-[#CEFFD8]' : 'bg-[#FFF1C2]']"
              >
                <div
                  class="font-gen-jyuu-gothic text-[20px] text-[#07403F] mr-[20px]"
                >
                  <ruby v-if="!result?.is_passed"
                    ><rb>不</rb><rp>(</rp><rt>ふ</rt><rp>)</rp></ruby
                  >
                  <ruby
                    ><rb>合格</rb><rp>(</rp><rt>ごうかく</rt><rp>)</rp></ruby
                  >
                </div>
                <div class="font-beanbag text-[20px] text-[#07403F] mr-[10px]">
                  {{ result?.is_passed ? "Đỗ" : "Không Đỗ" }}
                </div>
                <img
                  v-if="result.total_score === 180"
                  src="/assets/img/do-full.svg"
                  class="mix-blend-multiply"
                />
                <img
                  v-else-if="result.is_passed"
                  src="/assets/img/do.svg"
                  class="mix-blend-multiply"
                />
                <img
                  v-else
                  src="/assets/img/khong-do.svg"
                  class="mix-blend-multiply"
                />
              </div>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex flex-col items-start gap-16 mt-16">
                <div class="flex flex-col items-start">
                  <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]"
                    ><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby
                  >
                  <div class="font-averta-regular text-[16px] text-[#07403F]">
                    Điểm
                  </div>
                </div>
              </div>
              <div
                class="w-[750px] min-h-[54px] h-[316px] items-center justify-center border-2 border-[#07403F] rounded-[22px] py-0 z-10 grid grid-cols-4 grid-rows-5 gap-0"
              >
                <div
                  class="col-span-3 row-span-2 flex flex-col justify-center items-center py-6 border-b border-r border-[#A6CCCB] h-full w-full"
                >
                  <div class="text-[#07403F] text-[20px]">
                    <ruby
                      ><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby
                    ><ruby
                      ><rb>区分別</rb><rp>(</rp><rt>くぶんべつ</rt
                      ><rp>)</rp></ruby
                    ><ruby
                      ><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby
                    >
                  </div>
                  <div class="font-averta-regular text-[16px] text-[#07403F]">
                    Điểm đạt được từng phần
                  </div>
                </div>
                <div
                  class="row-span-2 col-start-4 py-6 flex flex-col justify-center items-center border-b border-l border-[#A6CCCB] h-full w-full"
                >
                  <div class="text-[#07403F] text-[20px]">
                    <ruby
                      ><rb>総合</rb><rp>(</rp><rt>そうごう</rt><rp>)</rp></ruby
                    ><ruby
                      ><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby
                    >
                  </div>
                  <div class="font-averta-regular text-[16px] text-[#07403F]">
                    Tổng điểm
                  </div>
                </div>
                <div
                  class="row-span-3 col-start-4 row-start-3 flex flex-col justify-center items-center border-t border-l border-[#A6CCCB] py-6 h-full w-full rounded-br-[22px]"
                  :class="[result?.is_passed ? 'bg-[#CEFFD8]' : 'bg-[#FFF1C2]']"
                >
                  <div
                    class="font-beanbag text-[20px]"
                    :class="[
                      Number(result?.score_1) + Number(result?.score_2) < 38 ||
                      Number(result?.total_score) < Number(lesson.pass_marks) ||
                      Number(result?.score_3) < 19
                        ? 'text-[#975102]'
                        : 'text-[#07403F]',
                    ]"
                  >
                    {{ result?.total_score }}/180
                  </div>
                  <div
                    class="font-averta-regular text-[16px] mt-[35px] text-[#757575]"
                  >
                    • Điểm đỗ {{ lesson.pass_marks }}/180
                  </div>
                </div>
                <div
                  class="col-span-2 row-span-2 col-start-1 row-start-3 flex flex-col justify-center items-center border-t border-b border-r border-[#A6CCCB] py-6 h-full w-full"
                >
                  <div class="text-[#07403F] text-[20px]">
                    <ruby><rb>言語</rb><rp>(</rp><rt>げんご</rt><rp>)</rp></ruby
                    ><ruby
                      ><rb>知識</rb><rp>(</rp><rt>ちしき</rt><rp>)</rp></ruby
                    >(<ruby><rb>文字</rb><rp>(</rp><rt>もじ</rt><rp>)</rp></ruby
                    ><font _mstmutation="1">・</font
                    ><ruby><rb>語彙</rb><rp>(</rp><rt>ごい</rt><rp>)</rp></ruby
                    ><font _mstmutation="1">・</font
                    ><ruby
                      ><rb>文法</rb><rp>(</rp><rt>ぶんぽう</rt><rp>)</rp></ruby
                    ><font _mstmutation="1">)・</font
                    ><ruby
                      ><rb>読解</rb><rp>(</rp><rt>どっかい</rt><rp>)</rp></ruby
                    >
                  </div>
                  <div
                    class="font-averta-regular text-[16px] text-[#07403F] text-center"
                  >
                    Kiến thức ngôn ngữ <br />
                    (Từ vựng/Ngữ pháp) & Đọc hiểu
                  </div>
                </div>
                <div
                  class="col-span-2 col-start-1 row-start-5 flex justify-center items-end border-t border-r border-[#A6CCCB] py-6 gap-1 h-full w-full"
                >
                  <div class="text-[#07403F] text-[20px] translate-y-[20px]">
                    <ruby
                      ><rb>聴解</rb><rp>(</rp><rt>ちょうかい</rt><rp>)</rp></ruby
                    >
                  </div>
                  <div
                    class="font-averta-regular text-[16px] text-[#07403F] translate-y-[10px]"
                  >
                    Nghe hiểu
                  </div>
                </div>
                <div
                  class="row-span-2 col-start-3 row-start-3 flex flex-col gap-[20px] justify-center items-center border-t border-b border-l border-r border-[#A6CCCB] py-6 gap-1 h-full w-full"
                >
                  <div
                    class="font-beanbag text-[20px]"
                    :class="[
                      Number(result?.score_1) + Number(result?.score_2) < 38
                        ? 'text-[#975102]'
                        : 'text-[#07403F]',
                    ]"
                  >
                    {{ Number(result?.score_1) + Number(result?.score_2) }}/120
                  </div>
                  <div class="font-averta-regular text-[16px] text-[#757575]">
                    • Điểm liệt < 38
                  </div>
                </div>
                <div
                  class="col-start-3 row-start-5 flex flex-col gap-5 justify-center items-center border-l border-t border-r border-[#A6CCCB] py-6 gap-1 h-full w-full"
                >
                  <div
                    class="font-beanbag text-[20px]"
                    :class="[
                      Number(result?.score_3) < 19
                        ? 'text-[#975102]'
                        : 'text-[#07403F]',
                    ]"
                  >
                    {{ result?.score_3 }}/60
                  </div>
                  <div class="font-averta-regular text-[16px] text-[#757575]">
                    • Điểm liệt < 19
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div
        class="w-[514px] py-[14px] font-beanbag uppercase text-[14px] text-[#57D061] border-2 border-[#57D061] rounded-full mt-[56px] text-center cursor-pointer z-10"
        @click="openResultModal"
      >
        Xem chi tiết kết quả
      </div>
      <div
        class="w-[514px] py-[14px] font-beanbag uppercase text-[14px] text-[#57D061] flex items-center gap-3 z-10"
      >
        <div
          class="w-full py-[14px] text-[#07403F] rounded-full text-center cursor-pointer"
          :class="[!result?.is_passed ? 'bg-[#FFF193]' : 'bg-white']"
          @click="startExam()"
        >
          Làm lại
        </div>
        <div
          v-if="nextLesson"
          class="w-full py-[14px] text-[#07403F] bg-[#57D061] rounded-full text-center cursor-pointer"
          @click="goToNextLesson()"
        >
          Học tiếp
        </div>
      </div>
      <modal
        v-if="showModal"
        @close="showModal = false"
        v-cloak
        :bgColor="lesson.type === 'exam' ? '#F4F5FA' : 'white'"
      >
        <h3 slot="header" class="flex items-center justify-between">
          <span> Chi tiết bài làm </span>
          <svg
            width="48"
            height="48"
            viewBox="0 0 48 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            @click="showModal = false"
            class="cursor-pointer"
          >
            <circle cx="24" cy="24" r="24" fill="white" />
            <path
              d="M16.5 31.5L31.5 16.5"
              stroke="#1E1E1E"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M31.5 31.5L16.5 16.5"
              stroke="#1E1E1E"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </h3>
        <div slot="body" class="flex mt-[24px]">
          <div class="w-[400px] pr-[40px] border-r-[3px] border-[#D0D3DA]">
            <div class="p-5 bg-white shadow-md rounded-[20px]">
              <div class="flex items-center justify-between">
                <div class="font-averta-semibold text-[16px] text-[#000000]">
                  Kết quả
                </div>
                <div
                  class="p-[10px] rounded-full font-averta-regular text-[16px] leading-none text-[#000000]"
                  :class="[result?.is_passed ? 'bg-[#57D061]' : 'bg-[#E8B931]']"
                >
                  {{ result?.is_passed ? "Đạt" : "Chưa đạt" }}
                </div>
              </div>
              <div class="flex items-center justify-between mt-5">
                <div class="font-averta-semibold text-[16px] text-[#000000]">
                  Điểm của bạn
                </div>
                <div
                  class="font-averta-regular text-[16px] leading-none mr-[20px]"
                  :class="[
                    result?.is_passed ? 'text-[#009951]' : 'text-[#C00F0C]',
                  ]"
                >
                  <template v-if="lesson.type === 'last_exam'"
                    >{{ result.total_score }}/{{ lesson.total_marks }}</template
                  >
                  <template v-else
                    >{{ resultStatistics?.correctQuestions.length }}/{{
                      resultStatistics?.questions?.length
                    }}</template
                  >
                </div>
              </div>
              <div class="flex items-center gap-2 mt-5">
                <div class="font-averta-regular text-[14px] text-[#000000]">
                  <template v-if="lesson.type === 'last_exam'">
                    Điểm đạt: ({{ lesson.pass_marks }}/{{ lesson.total_marks }})
                  </template>
                  <template v-else>
                    Điểm đạt: >= 85% ({{
                      Math.ceil(resultStatistics?.questions?.length * 0.85)
                    }}/{{ resultStatistics?.questions?.length }} câu)
                  </template>
                </div>
              </div>
            </div>
            <VuePerfectScrollbar v-if="examData" class="h-[55vh] relative mt-2 pb-[100px]" :settings="scrollBarSettings">
              <div
                v-for="part in examData.exam_parts"
                class="mt-[30px] pb-[32px] border-b border-[#D9D9D9]"
              >
                <div
                  v-if="lesson.type === 'last_exam'"
                  class="font-beanbag text-[16px] text-[#07403F] uppercase"
                >
                  {{
                    ["Từ vựng - Chữ Hán", "Ngữ pháp - Đọc hiểu", "Nghe hiểu"][
                    part.type - 1
                      ]
                  }}
                </div>
                <div
                  class="bg-[#C1EACA] text-[14px] font-medium text-black rounded-[10px] px-4 py-1 inline-flex items-center justify-center font-averta-regular"
                >
                  Đúng
                  <b
                    class="ml-1"
                    :class="[
                      resultStatistics.partStats[part.type - 1].correct ===
                      part.questions.length
                        ? 'text-[#009951]'
                        : 'text-[#EF6D13]',
                    ]"
                  >{{ resultStatistics.partStats[part.type - 1].correct }}</b
                  >/<b class="text-[#009951] mr-1">
                  {{ part.questions.length }}</b
                >
                  câu
                </div>
                <div v-for="(mondai, idx) in part.mondais">
                  <div
                    class="mt-8 px-2 py-1 flex items-center gap-1 font-gen-jyuu-gothic-medium border-l-2"
                    :class="[
                      currentResultMondais.includes(mondai.id)
                        ? 'border-[#EF6D13]'
                        : 'border-[#757575]',
                    ]"
                  >
                    もんだい
                    <div
                      class="bg-black w-4 h-4 rounded-full inline-flex justify-center items-center text-white font-beanbag text-xs"
                    >
                      {{ idx + 1 }}
                    </div>
                  </div>
                  <div class="mt-3 flex flex-wrap gap-2">
                    <div
                      v-for="(question, qIdx) in mondai.questions"
                      :key="question.id"
                      class="rounded-full w-[28px] h-[28px] inline-flex justify-center items-center text-[#07403F] font-beanbag text-[16px] cursor-pointer border-[3px]"
                      :class="[
                        !questionState(question)
                          ? 'bg-white border-[#D9D9D9]'
                          : questionState(question) === 'correct'
                          ? 'bg-[#95FF99] border-[#57D061]'
                          : 'bg-[#FDD3D0] border-[#FF7C79]',
                      ]"
                      @click="jumpToResultQuestion(mondai.id, question.id)"
                    >
                      {{
                        part.type === 3
                          ? qIdx + 1
                          : part.questions.findIndex(
                          (q) => q.id === question.id
                        ) + 1
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </VuePerfectScrollbar>
          </div>
          <VuePerfectScrollbar
            class="relative flex-grow-1 w-full h-[70vh] pr-5 pl-[30px] pt-[2px]"
            :settings="scrollBarSettings"
          >
            <template v-if="lesson.type === 'last_exam'">
              <div
                class="p-[35px] bg-white rounded-[30px] shadow-[0_0_1px_0_#4C5D7052] shadow-[0_16px_24px_0_#617C9A52] relative"
                :class="[showFeedback ? 'h-auto' : 'h-[830px] overflow-hidden']"
              >
                <div
                  v-if="!showFeedback && result?.total_score < lesson.total_marks"
                  class="w-full h-[150px] bg-gradient-to-t from-white via-white to-transparent absolute bottom-0 left-0 cursor-pointer font-averta-regular text-[20px] text-[#EF6D13] text-center flex items-end justify-center pb-[30px]"
                  @click="showFeedback = true"
                >
                  <u>Xem thêm nhận xét của giáo viên</u>
                </div>
                <div class="text-center relative">
                  <img
                    v-if="result && result.is_passed"
                    src="/assets/img/result-passed.svg"
                    class="mx-auto"
                  />
                  <img
                    v-else
                    src="/assets/img/result-failed.svg"
                    class="mx-auto"
                  />
                  <div
                    class="absolute top-0 left-0 h-full w-full flex justify-center items-center font-averta-semibold text-[20px] text-[#1E1E1E] ml-[5%] mb-[5%] translate-x-[-3px] translate-y-[-5px]"
                  >
                    <span v-if="result && result.is_passed"
                      >Bạn đã sẵn sàng thi JLPT
                      {{ result.course.slice(-2) }}!</span
                    >
                    <span v-else>Cố gắng thêm bạn nha!</span>
                  </div>
                </div>
                <div
                  class="flex justify-center items-center mt-[80px] mb-[80px]"
                >
                  <div class="w-[350px] h-[350px] relative select-none">
                    <canvas
                      id="resultChart"
                      class="max-w-full max-h-full"
                    ></canvas>
                    <img
                      src="/assets/img/radar-chart.svg"
                      class="mx-auto absolute top-[-1.5%] left-[-2%] w-[104%]"
                    />
                    <img
                      src="/assets/img/radar-axis.svg"
                      class="mx-auto absolute top-[-3%] left-[46%] h-[50%]"
                    />
                    <img
                      src="/assets/img/radar-label.svg"
                      class="mx-auto absolute top-[-14%] left-[-30%] w-[155%]"
                    />
                  </div>
                </div>
                <div class="flex gap-[28px] items-start">
                  <img
                    src="/assets/img/giao-vien.svg"
                    class="flex-shrink-0 w-[48px]"
                  />
                  <div class="flex flex-col gap-3">
                    <div
                      v-if="resultStatistics.goodSkills?.length"
                      class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]"
                      v-html="
                        sample(feedbacks._83).replace('{skills}', resultStatistics.goodSkills.join(', '))
                      "
                    ></div>
                    <div
                      v-if="resultStatistics.normalSkills?.length"
                      class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]"
                      v-html="
                        sample(feedbacks._58).replace('{skills}', resultStatistics.normalSkills.join(', '))
                      "
                    ></div>
                    <div
                      v-for="skill in [
                        { name: 'Từ vựng', key: 'vocabulary' },
                        { name: 'Chữ Hán', key: 'kanji' },
                        { name: 'Ngữ pháp', key: 'grammar' },
                        { name: 'Đọc hiểu', key: 'reading' },
                        { name: 'Nghe hiểu', key: 'listening' },
                      ]"
                      :key="`skill-57-${skill.key}`"
                      v-if="
                        (resultStatistics[`${skill.key}Correct`] * 100) /
                          resultStatistics[skill.key].length <=
                        57
                      "
                      class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]"
                      v-html="sample(feedbacks[skill.key]._58)"
                    ></div>
                  </div>
                </div>
              </div>
              <div v-if="examData" class="mt-[40px] grid grid-cols-3 gap-5">
                <div
                  v-for="part in examData.exam_parts"
                  class="p-5 bg-white border border-[#D9D9D9] rounded-[20px]"
                >
                  <div
                    class="font-beanbag text-[#07403F] text-[14px] uppercase"
                  >
                    {{
                      ["Từ vựng - Chữ Hán", "Ngữ pháp - Đọc hiểu", "Nghe hiểu"][
                        part.type - 1
                      ]
                    }}
                  </div>
                  <div class="font-averta-regular text-[#000]">
                    Đúng:
                    {{ resultStatistics.partStats[part.type - 1].correct }}/{{
                      part.questions.length
                    }}
                  </div>
                  <div class="mt-5 flex flex-col gap-1 w-full">
                    <div
                      v-for="(mondai, mIdx) in part.mondais"
                      class="flex justify-between items-center w-full"
                    >
                      <div
                        class="bg-[#E6E6E6] p-1 w-[90%] rounded-full font-beanbag uppercase text-[10px] relative"
                        :class="[
                          resultStatistics.partStats[part.type - 1].mondais[
                            mIdx
                          ].correct === mondai.questions.length
                            ? 'text-[#57D061]'
                            : resultStatistics.partStats[part.type - 1].mondais[
                                mIdx
                              ].correct > 0
                            ? 'text-[#E8B931]'
                            : 'text-[#B3B3B3]',
                        ]"
                      >
                        <div
                          class="absolute top-0 left-0 h-full w-full rounded-full"
                          :class="[
                            resultStatistics.partStats[part.type - 1].mondais[
                              mIdx
                            ].correct === mondai.questions.length
                              ? 'bg-[#CEFFD8]'
                              : resultStatistics.partStats[part.type - 1]
                                  .mondais[mIdx].correct > 0
                              ? 'bg-[#FFF1BB]'
                              : 'bg-transparent',
                          ]"
                          :style="{
                            width: `${
                              (resultStatistics.partStats[part.type - 1]
                                .mondais[mIdx].correct *
                                100) /
                              mondai.questions.length
                            }%`,
                          }"
                        ></div>
                        <div class="opacity-0">
                          {{ partMondais[part.type - 1][mIdx] }}
                        </div>
                        <div class="absolute top-0 left-0 z-10 p-1">
                          {{ partMondais[part.type - 1][mIdx] }}
                        </div>
                      </div>
                      <div class="font-beanbag text-[#1E1E1E] text-[10px]">
                        {{
                          resultStatistics.partStats[part.type - 1].mondais[
                            mIdx
                          ].correct
                        }}/{{ mondai.questions.length }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <div
              class="relative pt-5"
              :class="[lesson.type === 'exam' ? '' : 'mt-[44px]']"
            >
              <svg class="hidden">
                <symbol
                  id="backward"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"
                  ></path>
                  <path d="M5 15V19"></path>
                  <path
                    d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523
            10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"
                  ></path>
                </symbol>

                <symbol id="play" viewBox="0 0 24 24">
                  <path
                    d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                    fill="#4E87FF"
                  />
                </symbol>

                <symbol id="pause" viewBox="0 0 24 24">
                  <path
                    d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                    fill="#4E87FF"
                  />
                  <path
                    d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                    fill="#4E87FF"
                  />
                </symbol>

                <symbol id="forward" viewBox="0 0 24 24">
                  <path
                    d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></path>
                  <path
                    d="M13 15V19"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></path>
                  <path
                    d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18
                          19H17C16.4477 19 16 18.5523 16 18Z"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></path>
                </symbol>

                <symbol id="high" viewBox="0 0 24 24">
                  <path
                    d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z"
                    fill="#4E87FF"
                  />
                  <path
                    d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z"
                    fill="#4E87FF"
                  />
                  <path
                    opacity="0.4"
                    d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                    fill="#4E87FF"
                  />
                  <path
                    d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                    fill="#4E87FF"
                  />
                </symbol>

                <symbol id="off" viewBox="0 0 24 24">
                  <path
                    opacity="0.4"
                    d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z"
                    fill="#4E87FF"
                  />
                  <path
                    d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z"
                    fill="#4E87FF"
                  />
                </symbol>
              </svg>
              <div
                class="absolute top-[14px] w-full h-[1px] bg-black -z-1"
              ></div>
              <div
                v-if="lesson.type === 'last_exam'"
                class="absolute top-0 inline-flex font-averta-regular text-[20px] text-[#1E1E1E] bg-white pr-2 z-10"
              >
                Chi tiết đáp án
              </div>
              <div v-for="part in examData.exam_parts" class="mt-[22px]">
                <div
                  v-if="lesson.type === 'last_exam'"
                  class="bg-[#F4F5FA] py-2 px-[14px] font-beanbag text-[14px] text-[#07403F] mb-5 uppercase rounded-[4px]"
                >
                  {{
                    ["Từ vựng - Chữ Hán", "Ngữ pháp - Đọc hiểu", "Nghe hiểu"][
                      part.type - 1
                    ]
                  }}
                </div>
                <div
                  v-for="mondai in part.mondais"
                  :id="`result_mondai_${mondai.id}`"
                >
                  <AppIntersect
                    @triggerIntersected="currentResultMondais.push(mondai.id)"
                    @triggerOutersected="
                      currentResultMondais = currentResultMondais.filter(
                        (m) => m !== mondai.id
                      )
                    "
                  />
                  <template v-for="task in mondai.tasks">
                    <div
                      v-if="task.type === 1"
                      class="font-gen-jyuu-gothic text-[14px] mb-[26px] text-[#212121]"
                      v-html="task.value"
                    ></div>
                    <div
                      v-if="lesson.type !== 'exam' && task.type === 5"
                      class="multimedia-item w-full mb-[40px]"
                    >
                      <label>{{ task.name }}</label>
                      <media-controller
                        audio
                        style="
                          --media-background-color: transparent;
                          --media-control-background: transparent;
                          --media-control-hover-background: transparent;
                          --media-control-width: 100%;
                        "
                        class="@container block w-full mt-5 mb-[30px]"
                      >
                        <audio slot="media" controls="true" class="w-full">
                          <source
                            :src="`https://mp3-v2.dungmori.com/${part.mp3}`"
                            type="audio/mpeg"
                          />
                          Your browser does not support the audio element.
                        </audio>
                        <media-control-bar
                          class="px-5 py-[10px] bg-[#E1EBFF] rounded-full items-center shadow-xl shadow-black/5 w-full"
                        >
                          <media-play-button
                            class="h-10 w-10 p-2 mx-3 rounded-full bg-slate-700"
                          >
                            <svg
                              slot="play"
                              aria-hidden="true"
                              class="relative left-px"
                            >
                              <use href="#play" />
                            </svg>
                            <svg slot="pause" aria-hidden="true">
                              <use href="#pause" />
                            </svg>
                          </media-play-button>
                          <media-time-display
                            class="text-[#4E87FF] text-[14px] font-averta"
                          ></media-time-display>
                          <media-time-range
                            class="block h-2 min-h-0 p-0 m-2 rounded-md bg-slate-50"
                            style="
                              --media-range-track-background: white;
                              --media-time-range-buffered-color: rgb(
                                0 0 0 / 0.02
                              );
                              --media-range-bar-color: #4e87ff;
                              --media-range-track-border-radius: 4px;
                              --media-range-track-height: 0.5rem;
                              --media-range-thumb-background: #4e87ff;
                              --media-range-thumb-box-shadow: 0 0 0 2px
                                rgb(255 255 255 / 0.9);
                              --media-range-thumb-width: 0.25rem;
                              --media-range-thumb-height: 1rem;
                              --media-preview-time-text-shadow: transparent;
                            "
                          >
                            <media-preview-time-display
                              slot="preview"
                              class="text-[#4E87FF] text-[14px] font-averta"
                            ></media-preview-time-display>
                          </media-time-range>
                          <media-duration-display
                            class="text-[#4E87FF] text-[14px] font-averta"
                          ></media-duration-display>
                          <media-mute-button>
                            <svg
                              slot="high"
                              aria-hidden="true"
                              class="h-5 w-5 fill-slate-500"
                            >
                              <use href="#high" />
                            </svg>
                            <svg
                              slot="medium"
                              aria-hidden="true"
                              class="h-5 w-5 fill-slate-500"
                            >
                              <use href="#high" />
                            </svg>
                            <svg
                              slot="low"
                              aria-hidden="true"
                              class="h-5 w-5 fill-slate-500"
                            >
                              <use href="#high" />
                            </svg>
                            <svg
                              slot="off"
                              aria-hidden="true"
                              class="h-5 w-5 fill-slate-500"
                            >
                              <use href="#off" />
                            </svg>
                          </media-mute-button>
                        </media-control-bar>
                      </media-controller>
                    </div>
                    <template v-if="task.type === 3">
                      <div
                        v-if="!questionState(task)"
                        class="text-[#C00F0C] font-averta-regular text-[14px] mt-3"
                      >
                        * Câu chưa làm
                      </div>
                      <div
                        :id="`result_question_${task.id}`"
                        class="py-5 px-4 bg-white border border-[#D9D9D9] rounded-[10px] font-gen-jyuu-gothic mt-5 leading-[1.80] text-[#000]"
                      >
                        <template v-if="task.audio">
                          <svg
                            v-if="answerMp3Playing && answerMp3Object && answerMp3Object.src === `https://mp3-v2.dungmori.com/${task.audio}`"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="cursor-pointer"
                            @click.stop="togglePlayAnswer(task.audio)"
                          >
                            <path
                              d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                              fill="#4E87FF"
                            />
                            <path
                              d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                              fill="#4E87FF"
                            />
                          </svg>
                          <svg
                            v-else
                            width="19"
                            height="20"
                            viewBox="0 0 19 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="cursor-pointer"
                            @click.stop="togglePlayAnswer(task.audio)"
                          >
                            <path
                              d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                              fill="#4E87FF"
                            />
                          </svg>

                        </template>
                        <div class="text-[16px]" v-html="task.value"></div>
                      </div>

                      <div class="mt-[68px] mb-[25px] grid grid-cols-2 gap-7">
                        <div
                          v-for="(answer, aIdx) in task.answers"
                          class="flex items-center gap-2 cursor-pointer py-1 px-0 rounded-full"
                        >
                          <div
                            class="w-10 h-10 border-[3px] rounded-full bg-white text-[#757575] font-beanbag text-[16px] flex justify-center items-center shadow-[0px 0px 18px 0px #21212114] font-bold flex-shrink-0"
                            :class="[
                              answer.grade > 0
                                ? 'bg-[#95FF99] border-[#57D061]'
                                : Object.values(userAnswers).includes(answer.id)
                                ? 'bg-[#FDD3D0] border-[#FF7C79]'
                                : 'bg-white border-[#D9D9D9]',
                            ]"
                          >
                            {{ aIdx + 1 }}
                          </div>
                          <template v-if="answer.audio">
                            <svg
                              v-if="answerMp3Playing && answerMp3Object && answerMp3Object.src === `https://mp3-v2.dungmori.com/${answer.audio}`"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              class="cursor-pointer"
                              @click.stop="togglePlayAnswer(answer.audio)"
                            >
                              <path
                                d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                                fill="#4E87FF"
                              />
                              <path
                                d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                                fill="#4E87FF"
                              />
                            </svg>
                            <svg
                              v-else
                              width="19"
                              height="20"
                              viewBox="0 0 19 20"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              class="cursor-pointer"
                              @click.stop="togglePlayAnswer(answer.audio)"
                            >
                              <path
                                d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                                fill="#4E87FF"
                              />
                            </svg>
                          </template>
                          <div
                            v-else
                            v-html="answer.value"
                            class="font-gen-jyuu-gothic text-[14px] text-wrap text-[#212121]"
                          ></div>
                        </div>
                      </div>
                    </template>
                    <template v-if="task.type === 13">
                      <div
                        v-if="!questionState(task)"
                        class="text-[#C00F0C] font-averta-regular text-[14px] mt-3"
                      >
                        * Câu chưa làm
                      </div>
                      <div
                        :id="`result_question_${task.id}`"
                        class="border border-[#D9D9D9] rounded-[8px] px-[24px] py-[60px] bg-white shadow mt-5"
                      >
                        <img
                          v-if="JSON.parse(task.value)?.img"
                          :src="`/cdn/lesson/default/${
                            JSON.parse(task.value)?.img
                          }`"
                          class="mt-5 mx-auto"
                        />
                        <div class="font-beanbag mt-5 text-[14px]">
                          {{ task.text_vi }}
                        </div>
                        <div class="flex items-end flex-wrap gap-2 mt-5">
                          <template
                            v-for="(block, bIdx) in JSON.parse(task.value)
                              .question"
                          >
                            <div
                              v-if="block.type === 'default'"
                              v-html="block.value"
                              class="font-gen-jyuu-gothic-medium text-[16px] translate-y-[-10px]"
                            ></div>
                            <div
                              v-if="block.type === 'question'"
                              class="font-beanbag text-[16px] p-3 rounded-[12px] text-center border-[3px] min-h-[52px]"
                              :class="[
                                !blockState(task, block, bIdx)
                                  ? 'bg-[#FDD3D0] border-[#FF7C79]'
                                  : blockState(task, block, bIdx) === 'correct'
                                  ? 'bg-[#95FF99] border-[#57D061]'
                                  : 'bg-[#FDD3D0] border-[#FF7C79]',
                              ]"
                              :style="{
                                width: task.blockAnswers[bIdx]
                                  ? 'auto'
                                  : `${block.result.length * 20 + 24}px`,
                              }"
                            >
                              <div v-html="task.blockAnswers[bIdx]"></div>
                            </div>
                          </template>
                        </div>
                      </div>
                    </template>
                    <div
                      v-if="
                        task.explain ||
                        (isValidJSON(task.value) &&
                          JSON.parse(task.value) &&
                          JSON.parse(task.value).explain)
                      "
                      class="bg-[#F0FFF1] p-3 rounded-[20px] mb-[30px] mt-5"
                    >
                      <div class="font-beanbag uppercase font-[12px]">
                        * Giải thích
                      </div>
                      <template v-if="task.explain_mp3">
                        <svg
                          v-if="answerMp3Playing && answerMp3Object && answerMp3Object.src === `https://mp3-v2.dungmori.com/${task.explain_mp3}`"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="cursor-pointer"
                          @click.stop="togglePlayAnswer(task.explain_mp3)"
                        >
                          <path
                            d="M10.65 19.11V4.89C10.65 3.54 10.08 3 8.64 3H5.01C3.57 3 3 3.54 3 4.89V19.11C3 20.46 3.57 21 5.01 21H8.64C10.08 21 10.65 20.46 10.65 19.11Z"
                            fill="#4E87FF"
                          />
                          <path
                            d="M21.0001 19.11V4.89C21.0001 3.54 20.4301 3 18.9901 3H15.3601C13.9301 3 13.3501 3.54 13.3501 4.89V19.11C13.3501 20.46 13.9201 21 15.3601 21H18.9901C20.4301 21 21.0001 20.46 21.0001 19.11Z"
                            fill="#4E87FF"
                          />
                        </svg>
                        <svg
                          v-else
                          width="19"
                          height="20"
                          viewBox="0 0 19 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          class="cursor-pointer"
                          @click.stop="togglePlayAnswer(task.explain_mp3)"
                        >
                          <path
                            d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z"
                            fill="#4E87FF"
                          />
                        </svg>

                      </template>

                      <div
                        v-if="
                          task.explain ||
                          (isValidJSON(task.value) &&
                            JSON.parse(task.value).explain)
                        "
                        class="mt-[20px] text-[16px]"
                        v-html="
                          isValidJSON(task.value)
                            ? JSON.parse(task.value).explain
                            : task.explain
                        "
                      ></div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </VuePerfectScrollbar>
        </div>
      </modal>
    </div>
    <modal
      v-if="showTimeUpModal"
      @close="showTimeUpModal = false"
      v-cloak
      width="568px"
    >
      <div slot="body" class="flex flex-col items-center">
        <div class="font-averta-regular text-[#C00F0C] text-[14px]">
          Hết giờ
        </div>
        <div
          class="flex items-center gap-1 font-averta-semibold text-[#C00F0C] text-[20px] mt-[10px]"
        >
          <svg
            width="21"
            height="21"
            viewBox="0 0 21 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="animate-shake"
          >
            <path
              opacity="0.4"
              d="M10.4998 19.2496C14.6896 19.2496 18.0861 15.8532 18.0861 11.6634C18.0861 7.47363 14.6896 4.07715 10.4998 4.07715C6.31005 4.07715 2.91357 7.47363 2.91357 11.6634C2.91357 15.8532 6.31005 19.2496 10.4998 19.2496Z"
              fill="#C92326"
            />
            <path
              d="M10.5 12.0312C10.1413 12.0312 9.84375 11.7337 9.84375 11.375V7C9.84375 6.64125 10.1413 6.34375 10.5 6.34375C10.8587 6.34375 11.1562 6.64125 11.1562 7V11.375C11.1562 11.7337 10.8587 12.0312 10.5 12.0312Z"
              fill="#C92326"
            />
            <path
              d="M13.0288 3.01875H7.97131C7.62131 3.01875 7.34131 2.73875 7.34131 2.38875C7.34131 2.03875 7.62131 1.75 7.97131 1.75H13.0288C13.3788 1.75 13.6588 2.03 13.6588 2.38C13.6588 2.73 13.3788 3.01875 13.0288 3.01875Z"
              fill="#C92326"
            />
          </svg>
          <div>00:00:00</div>
        </div>
        <hr class="w-full" />
        <img src="/assets/img/otsukare.svg" class="mt-[28px]" />
        <div
          v-if="
            lesson.type === 'exam' ||
            (lesson.type === 'last_exam' && stage === 3)
          "
          class="text-[#07403F] font-averta-regular text-[14px] mt-[12px] text-center"
        >
          Bạn đã vất vả rồi!<br />
          Cùng xem kết quả nào
        </div>
        <div
          v-else
          class="text-[#07403F] font-averta-regular text-[14px] mt-[12px] text-center"
        >
          Hãy cố gắng hơn ở <br />
          phần tiếp theo nhé!
        </div>
        <div
          v-if="
            lesson.type === 'exam' ||
            (lesson.type === 'last_exam' && stage === 3)
          "
          class="bg-[#57D061] text-center text-white font-beanbag text-[16px] rounded-full px-[50px] py-[14px] shadow-md cursor-pointer mt-[25px] min-w-[218px] uppercase"
          @click="closeTimeUpModal()"
        >
          Xem kết quả
        </div>
        <div
          v-else
          class="bg-[#57D061] text-center text-white font-beanbag text-[16px] rounded-full px-[50px] py-[14px] shadow-md cursor-pointer mt-[25px] min-w-[218px] uppercase"
          @click="nextStage()"
        >
          Đi tới phần tiếp
        </div>
      </div>
    </modal>
    <modal
      v-if="showSubmitModal"
      @close="showSubmitModal = false"
      v-cloak
      width="568px"
      padding="0px"
    >
      <div slot="body" class="flex flex-col items-center p-0 m-0">
        <div
          class="bg-[#FFF193] flex items-center px-4 py-3 rounded-t-3xl w-full"
        >
          <svg
            width="29"
            height="28"
            viewBox="0 0 29 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24.8585 10.0711L24.8149 17.8766C24.8078 19.1547 24.1164 20.3377 23.0059 20.982L16.2055 24.8583C15.095 25.4912 13.7255 25.4835 12.6108 24.8382L5.85414 20.8861C4.75078 20.2409 4.07271 19.0502 4.07992 17.7607L4.12355 9.95518C4.1307 8.67708 4.82204 7.49405 5.9326 6.84977L12.733 2.97348C13.8435 2.34062 15.2129 2.34828 16.3277 2.99358L23.0843 6.94565C24.1876 7.6023 24.8657 8.78157 24.8585 10.0711Z"
              fill="#FFF193"
              stroke="#303030"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14.5015 9.06543L14.468 15.0565"
              stroke="#303030"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14.4468 18.709L14.4461 18.8235"
              stroke="#303030"
              stroke-width="3"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>

          <div
            class="confirm-leave__title text-2xl font-averta-semibold ml-3 w-full"
          >
            Nộp bài?
          </div>
          <svg
            width="17"
            height="17"
            viewBox="0 0 17 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="cursor-pointer"
            @click="showSubmitModal = false"
          >
            <path
              d="M9.87663 8.5L16.7476 1.62901C16.9156 1.44362 17.0059 1.20072 16.9997 0.95062C16.9935 0.700517 16.8914 0.46236 16.7145 0.285457C16.5376 0.108553 16.2995 0.00645342 16.0494 0.000295582C15.7993 -0.00586225 15.5564 0.0843936 15.371 0.252376L8.5 7.12337L1.62901 0.252376C1.44362 0.0843936 1.20072 -0.00586225 0.95062 0.000295582C0.700517 0.00645342 0.46236 0.108553 0.285457 0.285457C0.108553 0.46236 0.00645342 0.700517 0.000295582 0.95062C-0.00586225 1.20072 0.0843936 1.44362 0.252376 1.62901L7.12337 8.5L0.252376 15.371C0.0843936 15.5564 -0.00586225 15.7993 0.000295582 16.0494C0.00645342 16.2995 0.108553 16.5376 0.285457 16.7145C0.46236 16.8914 0.700517 16.9935 0.95062 16.9997C1.20072 17.0059 1.44362 16.9156 1.62901 16.7476L8.5 9.87663L15.371 16.7476C15.5564 16.9156 15.7993 17.0059 16.0494 16.9997C16.2995 16.9935 16.5376 16.8914 16.7145 16.7145C16.8914 16.5376 16.9935 16.2995 16.9997 16.0494C17.0059 15.7993 16.9156 15.5564 16.7476 15.371L9.87663 8.5Z"
              fill="#212121"
            />
          </svg>
        </div>
        <div class="px-4 w-full">
          <div class="text-[24px] text-[#757575] py-[98px] px-[30px] w-full">
            Bạn đã làm
            <b
              :class="[
                Object.keys(currentStageAnswers).length ===
                currentQuestions.length
                  ? 'text-[#009951]'
                  : 'text-[#975102]',
              ]"
              >{{ Object.keys(currentStageAnswers).length }}/{{
                currentQuestions.length
              }}</b
            >
            câu.
          </div>
          <div
            class="flex items-center justify-end sp:justify-center py-5 border-t"
          >
            <button
              @click="submitExam()"
              class="btn-confirm-leave bg-white text-xl text-center py-2 w-[169px] border border-[#D9D9D9] rounded-full"
            >
              Nộp bài
            </button>
            <button
              class="bg-[#FFF193] text-xl text-center py-2 w-[169px] ml-2 rounded-full"
              @click="showSubmitModal = false"
            >
              Ở lại
            </button>
          </div>
        </div>
      </div>
    </modal>
  </div>
</template>
<script>
import moment from "moment";
import VuePerfectScrollbar from "vue-perfect-scrollbar";
import domtoimage from 'dom-to-image';
import { sample, orderBy } from "lodash";
import AppIntersect from "./AppIntersect.vue";

export default {
  name: "LessonExam",
  components: { AppIntersect, VuePerfectScrollbar },
  props: ["lesson", "tasks", "unlock", "active"],
  data() {
    return {
      saveImageLoading: false,
      scrollBarSettings: {
        // maxScrollbarLength: 60
      },
      currentPlaying: null,
      mp3Object: null,
      mp3CurrentTime: 0,
      mp3Duration: 0,
      mp3Playing: false,
      answerMp3Object: null,
      answerMp3CurrentTime: 0,
      answerMp3Duration: 0,
      answerMp3Playing: false,
      mp3: {},
      currentResultMondais: [],
      questionTypes: [3, 13, 14, 15],
      nextLesson: nextToLesson,
      showSubmitModal: false,
      showModal: false,
      showTimeUpModal: false,
      stage: 0,
      end_time: null,
      level: "N5",
      exam: {},
      currentMondai: 0,
      moment: moment,
      sample: sample,
      showFeedback: false,
      partMondais: [
        [
          "Cách đọc Kanji",
          "Nhớ Kanji",
          "Biểu hiện từ",
          "Từ đồng nghĩa",
          "Cách dùng từ",
        ],
        [
          "Dạng ngữ pháp",
          "Thành lập câu",
          "Ngữ pháp tổng hợp",
          "Đoạn văn ngắn",
          "Đoạn văn trung bình",
          "Tìm thông tin",
        ],
        [
          "Nghe hiểu chủ đề",
          "Hiểu điểm chính",
          "Diễn đạt bằng lời nói",
          "Phản hồi tức thời",
        ],
      ],
      examLevels: {
        N1: [
          {
            type: 1,
            label: "Từ vựng - Chữ Hán - Ngữ pháp - Đọc hiểu",
            point: 120,
            duration: 110,
            question: 60,
          },
          {
            type: 3,
            label: "Nghe hiểu",
            point: 60,
            duration: 55,
            question: 60,
          },
        ],
        N2: [
          {
            type: 1,
            label: "Từ vựng - Chữ Hán - Ngữ pháp - Đọc hiểu",
            point: 120,
            duration: 105,
            question: 60,
          },
          {
            type: 3,
            label: "Nghe hiểu",
            point: 60,
            duration: 50,
            question: 60,
          },
        ],
        N3: [
          {
            type: 1,
            label: "Từ vựng - Chữ Hán",
            point: 60,
            duration: 30,
            question: 60,
          },
          {
            type: 2,
            label: "Ngữ pháp - Đọc hiểu",
            point: 60,
            duration: 70,
            question: 60,
          },
          {
            type: 3,
            label: "Nghe hiểu",
            point: 60,
            duration: 40,
            question: 60,
          },
        ],
        N4: [
          {
            type: 1,
            label: "Từ vựng - Chữ Hán",
            point: 60,
            duration: 25,
            question: 60,
          },
          {
            type: 2,
            label: "Ngữ pháp - Đọc hiểu",
            point: 60,
            duration: 55,
            question: 60,
          },
          {
            type: 3,
            label: "Nghe hiểu",
            point: 60,
            duration: 35,
            question: 60,
          },
        ],
        N5: [
          {
            type: 1,
            label: "Từ vựng - Chữ Hán",
            point: 60,
            duration: 20,
            question: 60,
          },
          {
            type: 2,
            label: "Ngữ pháp - Đọc hiểu",
            point: 60,
            duration: 40,
            question: 60,
          },
          {
            type: 3,
            label: "Nghe hiểu",
            point: 60,
            duration: 30,
            question: 60,
          },
        ],
      },
      resultLevels: {
        N1: [
          { type: 1, label: "Từ vựng - Chữ Hán - Ngữ pháp" },
          { type: 2, label: "Đọc hiểu" },
          { type: 3, label: "Nghe hiểu" },
        ],
        N2: [
          { type: 1, label: "Từ vựng - Chữ Hán - Ngữ pháp" },
          { type: 2, label: "Đọc hiểu" },
          { type: 3, label: "Nghe hiểu" },
        ],
        N3: [
          { type: 1, label: "Từ vựng - Chữ Hán - Ngữ pháp" },
          { type: 2, label: "Đọc hiểu" },
          { type: 3, label: "Nghe hiểu" },
        ],
        N4: [
          { type: 1, label: "Từ vựng - Chữ Hán - Ngữ pháp - Đọc hiểu" },
          { type: 3, label: "Nghe hiểu" },
        ],
        N5: [
          { type: 1, label: "Từ vựng - Chữ Hán - Ngữ pháp - Đọc hiểu" },
          { type: 3, label: "Nghe hiểu" },
        ],
      },
      courseLevels: {
        3: "N3",
        4: "N4",
        5: "N5",
        16: "N2",
        17: "N1",
        39: "N5",
        40: "N4",
        30: "N4",
        41: "N5",
      },
      result: null,
      last_result: null,
      loading: false,
      start_loading: false,
      examData: null,
      timer: null,
      waitTimer: null,
      passedTimer: null,
      passedTime: 0,
      timerDisplay: {
        minute: "--",
        second: "--",
      },
      waitTimerDisplay: {
        minute: "10",
        second: "00",
      },
      isWaiting: false,
      sticky: false,
      timeUp: false,
      diff: 0,
      audioReady: false,
      audioEnded: false,
      tabVisible: true,
      feedbacks: {
        _83: [
          "Chúc mừng! Bạn đã xuất sắc hoàn thành phần {skills}.",
          "Wow!!! Bạn thực sự đang làm rất tốt phần {skills}.",
        ],
        _58: [
          "Phần {skills} của bạn đang làm khá tốt. Hãy tiếp tục luyện tập để tăng điểm số phần này nhé!",
          "Phần {skills} của bạn đang làm khá tốt. Hãy cố gắng phát huy để đạt kết quả cao hơn nhé!",
          "Điểm số phần {skills} của bạn đang khá tốt. Hãy duy trì luyện tập để đạt điểm số cao hơn nữa nhé!",
        ],
        kanji: {
          _58: [
            "Có vẻ bạn đang gặp khó khăn với phần Chữ Hán nhỉ? Đừng nản lòng bạn nhé!<br/>Có một mẹo nhỏ khi học chữ Hán đó là: thay vì chỉ viết đi viết lại và học đơn lẻ 1 chữ Hán, chúng ta hãy học cả các từ vựng có chứa chữ Hán đó để có thể nhớ tốt hơn cách đọc và trường nghĩa của nó nhé!",
            'Có vẻ Kanji đang là "chướng ngại vật" trong bài thi của bạn. Bạn đã thử học chữ Hán theo phương pháp câu chuyện chưa? Nó sẽ giúp bạn nhớ chữ Hán lâu hơn đấy!',
            "Có vẻ bạn đang gặp khó khăn với phần Chữ Hán nhỉ? Hãy ghi lại những từ đã bị sai, kiểm tra phần bộ thủ mình đã bị nhầm và viết đi viết lại nhiều lần để tránh bị lặp lại lỗi nhé!",
          ],
        },
        vocabulary: {
          _58: [
            'Trong bài thi, có một số câu phần Từ vựng bạn đã "đánh rơi điểm" khá đáng tiếc. Mình hãy tranh thủ ôn tập lại và cố gắng đọc kỹ đề khi làm bài thi bạn nhé!<br/>Tip: Hiểu đúng cách sử dụng của từ vựng hơn qua việc ghi nhớ các ví dụ đi kèm từ vựng đó.',
            "Trong bài thi, có một số câu phần Từ vựng bạn đã mất điểm khá đáng tiếc. Nếu bạn đang theo học khóa học Dũng Mori, hãy vào phần flashcards từ vựng và xem đi xem lại mỗi khi có thời gian, bạn nhé!",
          ],
        },
        grammar: {
          _58: [
            "Tiếc quá, phần Ngữ pháp của bạn trong lần thi này đang chưa được tốt lắm!<br/>So với các phần khác, ngữ pháp là phần có phạm vi học khá rõ ràng nên bạn hãy cố gắng cải thiện phần này để không bị bỏ lỡ điểm một cách đáng tiếc nhé!",
            "Tiếc quá, phần Ngữ pháp của bạn trong lần thi này đang chưa được tốt lắm! Một mẹo để tăng điểm phần Ngữ pháp đó là: bạn nên có một danh sách ngữ pháp của cấp độ mình đang học. Trước mỗi lần làm đề, hãy đọc lướt lại một lượt các ngữ pháp và chú trọng ở phần mình còn chưa vững. Lặp đi lặp lại như vậy chắc chắn bạn sẽ nhớ được nhiều ngữ pháp hơn đó.",
            "Thật tiếc vì lần này bạn chưa phát huy được tốt phần Ngữ pháp. Một mẹo để bạn nhớ được ngữ pháp tốt hơn, đó là: bạn hãy nhớ 1 hoặc 2 ví dụ điển hình của ngữ pháp đó. Điều này sẽ giúp bạn nhớ được cách sử dụng và cả cách kết hợp với các từ loại khác đấy.",
          ],
        },
        reading: {
          _58: [
            "Điểm số phần Đọc hiểu lần này rất đáng để quan tâm đấy. Phần Đọc hiểu cũng đang là nỗi ám ảnh của nhiều bạn thi JLPT lắm nè. Bạn nhớ tìm các từ nối và từ khóa đúng để không bị bài đọc đánh lừa nhé!",
            "Điểm số phần Đọc hiểu lần này rất đáng để quan tâm đấy. Nhiều bạn cũng đang gặp khó khăn với phần Đọc hiểu này. Trong quá trình làm bài, hãy phân bổ thời gian làm các dạng bài một cách hợp lý. Tránh việc dành quá nhiều thời gian ở 1 bài tập mà không kịp thời gian để làm các câu tiếp theo bạn nhé!",
            "Điểm số phần Đọc hiểu lần này rất đáng để quan tâm đấy. Trong thời gian ôn thi này, chúng mình hãy luyện tập phân tích câu văn, đoạn văn như: tập xác định chủ ngữ - vị ngữ, từ nối, từ khóa.... thật nhiều để có thể hiểu một cách chính xác các bài đọc nhé!",
            "Điểm số phần Đọc hiểu lần này rất đáng để quan tâm đấy. Phần Đọc hiểu cũng đang là nỗi ám ảnh của nhiều bạn thi JLPT lắm nè! Một trong những tips quan trọng khi làm đọc hiểu đó là: bạn nên đọc câu hỏi trước khi đọc cả bài văn. Việc này giúp bạn định hướng được nội dung cần tìm kiếm trong đoạn văn, từ đó giúp bạn nâng cao hiệu quả làm bài.",
          ],
        },
        listening: {
          _58: [
            "Hãy cố gắng cải thiện phần Nghe hiểu của mình thêm bạn nhé. Bạn thử thay đổi cách ghi chép các thông tin quan trọng khi nghe xem sao! Chúng ta chỉ nên ghi chép hoặc vẽ những ý chính quan trọng để gợi nhớ thông tin, tránh viết quá nhiều khiến bản thân bị phân tâm khi nghe bài thi.",
            "Ở bài thi này, phần thi Nghe hiểu của mình chưa đạt được phong độ tốt lắm nhỉ. Nếu được, bạn nên thử làm lại các đề thi JLPT cũ để nắm được cấu trúc các dạng bài thi nghe hiểu. Tránh việc bối rối với dạng bài thi mà để lỡ phần nghe quan trọng nha!",
            "Phần Nghe hiểu đang là một phần cần cải thiện thêm trong lần thi này của bạn.<br/>Có một tip nhỏ chúng ta nên lưu ý đó là: khi làm bài nghe bạn hãy tập trung cao độ, làm phần nào tập trung phần đó. Dù những câu trước bạn cảm thấy mình làm chưa ổn lắm thì cũng hãy dứt khoát bỏ qua để tránh bị ảnh hưởng sang bài tiếp theo nhé!",
          ],
        },
      },
    };
  },
  computed: {
    windowWidth() {
      return window.innerWidth;
    },
    mondaiQuestion() {
      console.log('currentMondai', this.currentMondai);
      console.log('currentMondais', this.currentMondais);
      const first =
        this.stageData.questions.findIndex(
          (q) =>
            q.id === this.currentMondais[this.currentMondai].questions[0].id
        ) + 1;
      const last =
        this.stageData.questions.findIndex(
          (q) =>
            q.id ===
            this.currentMondais[this.currentMondai].questions[
              this.currentMondais[this.currentMondai].questions.length - 1
            ].id
        ) + 1;
      return {
        first,
        last,
      };
    },
    resultStatistics() {
      // 1: từ vựng 2: chữ hán 3: ngữ pháp 4: đọc hiểu 5: nghe hiểu
      if (!this.examData) return {};
      if (!this.result) return {};
      let questions = [];

      this.examData.exam_parts.forEach((part) => {
        questions.push(...part.questions);
      });

      const vocabulary = questions.filter((question) => question.skill === 1);
      const kanji = questions.filter((question) => question.skill === 2);
      const grammar = questions.filter((question) => question.skill === 3);
      const reading = questions.filter((question) => question.skill === 4);
      const listening = questions.filter((question) => question.skill === 5);

      let vocabularyCorrect = 0;
      let kanjiCorrect = 0;
      let grammarCorrect = 0;
      let readingCorrect = 0;
      let listeningCorrect = 0;
      let badSkills = [];
      let normalSkills = [];
      let goodSkills = [];

      vocabulary.forEach((question) => {
        if (question.type === 3) {
          const correctAnswer = question.answers.find(
            (answer) => answer.grade > 0
          );
          if (correctAnswer && this.userAnswers[question.id] === correctAnswer.id) {
            vocabularyCorrect++;
          }
        }
        if (question.type === 13) {
          // console.log(this.userAnswers[question.id])
        }
      });

      if (vocabulary.length > 0) {
        const vocabularyPercent = (vocabularyCorrect / vocabulary.length) * 100;
        if (vocabularyPercent < 58) {
          badSkills.push("Từ vựng");
        } else if (vocabularyPercent < 83) {
          normalSkills.push("Từ vựng");
        } else {
          goodSkills.push("Từ vựng");
        }
      }

      kanji.forEach((question) => {
        const correctAnswer = question.answers.find(
          (answer) => answer.grade > 0
        );
        if (correctAnswer && this.userAnswers[question.id] === correctAnswer.id) {
          kanjiCorrect++;
        }
      });

      if (kanji.length > 0) {
        const kanjiPercent = (kanjiCorrect / kanji.length) * 100;
        if (kanjiPercent < 58) {
          badSkills.push("Chữ Hán");
        } else if (kanjiPercent < 83) {
          normalSkills.push("Chữ Hán");
        } else {
          goodSkills.push("Chữ Hán");
        }
      }

      grammar.forEach((question) => {
        const correctAnswer = question.answers.find(
          (answer) => answer.grade > 0
        );
        if (correctAnswer && this.userAnswers[question.id] === correctAnswer.id) {
          grammarCorrect++;
        }
      });

      if (grammar.length > 0) {
        const grammarPercent = (grammarCorrect / grammar.length) * 100;
        if (grammarPercent < 58) {
          badSkills.push("Ngữ pháp");
        } else if (grammarPercent < 83) {
          normalSkills.push("Ngữ pháp");
        } else {
          goodSkills.push("Ngữ pháp");
        }
      }

      reading.forEach((question) => {
        const correctAnswer = question.answers.find(
          (answer) => answer.grade > 0
        );
        if (correctAnswer && this.userAnswers[question.id] === correctAnswer.id) {
          readingCorrect++;
        }
      });

      if (reading.length > 0) {
        const readingPercent = (readingCorrect / reading.length) * 100;
        if (readingPercent < 58) {
          badSkills.push("Đọc hiểu");
        } else if (readingPercent < 83) {
          normalSkills.push("Đọc hiểu");
        } else {
          goodSkills.push("Đọc hiểu");
        }
      }

      listening.forEach((question) => {
        const correctAnswer = question.answers.find(
          (answer) => answer.grade > 0
        );
        if (correctAnswer && this.userAnswers[question.id] === correctAnswer.id) {
          listeningCorrect++;
        }
      });

      if (listening.length > 0) {
        const listeningPercent = (listeningCorrect / listening.length) * 100;
        if (listeningPercent < 58) {
          badSkills.push("Nghe hiểu");
        } else if (listeningPercent < 83) {
          normalSkills.push("Nghe hiểu");
        } else {
          goodSkills.push("Nghe hiểu");
        }
      }
      const partStats = this.examData.exam_parts.map((part) => {
        let correct = 0;
        part.questions.forEach((question) => {
          if (question.type === 3) {
            const correctAnswer = question.answers.find(
              (answer) => answer.grade > 0
            );
            if (correctAnswer && this.userAnswers[question.id] === correctAnswer.id) {
              correct++;
            }
            return correctAnswer ? this.userAnswers[question.id] === correctAnswer.id : false;
          }
          if (question.type === 13) {
            const chk =
              this.userAnswers[question.id] &&
              this.userAnswers[question.id].join(",") ===
                JSON.parse(question.value)
                  .question.filter((q) => q.type === "question")
                  .map((q) => q.result)
                  .join(",");
            if (chk) {
              correct++;
            }
          }
        });
        part.mondais = part.mondais.map((mondai) => {
          let correct = 0;
          mondai.questions.forEach((question) => {
            if (question.type === 3) {
              const correctAnswer = question.answers.find(
                (answer) => answer.grade > 0
              );
              if (correctAnswer && this.userAnswers[question.id] === correctAnswer.id) {
                correct++;
              }
              return correctAnswer ? this.userAnswers[question.id] === correctAnswer.id : false;
            }
            if (question.type === 13) {
              const chk =
                this.userAnswers[question.id] &&
                this.userAnswers[question.id].join(",") ===
                  JSON.parse(question.value)
                    .question.filter((q) => q.type === "question")
                    .map((q) => q.result)
                    .join(",");
              if (chk) {
                correct++;
              }
            }
          });
          return {
            ...mondai,
            correct,
          };
        });
        return {
          ...part,
          correct,
        };
      });
      const correctQuestions = questions.filter((question) => {
        if (question.type === 3) {
          const correctAnswer = question.answers.find(
            (answer) => answer.grade > 0
          );
          return correctAnswer ? this.userAnswers[question.id] === correctAnswer.id : false;
        }
        if (question.type === 13) {
          return (
            this.userAnswers[question.id] &&
            this.userAnswers[question.id].join(",") ===
              JSON.parse(question.value)
                .question.filter((q) => q.type === "question")
                .map((q) => q.result)
                .join(",")
          );
        }
      });
      return {
        questions,
        correctQuestions,
        vocabulary,
        kanji,
        grammar,
        reading,
        listening,
        partStats,
        vocabularyCorrect,
        kanjiCorrect,
        grammarCorrect,
        readingCorrect,
        listeningCorrect,
        badSkills,
        normalSkills,
        goodSkills,
      };
    },
    stageData() {
      if (this.stage < 1 || this.stage > 3) {
        return null;
      }
      if (!this.examData) return null;
      console.log('stage', this.stage);
      return this.examData.exam_parts.find((part) => part.type === this.stage);
    },
    currentQuestions() {
      if (!this.stageData) {
        return [];
      }
      return this.stageData?.questions || [];
    },
    currentMondais() {
      console.log(this.stageData);
      if (!this.stageData) {
        return [];
      }
      return (
        this.stageData?.mondais.map((mondai) => {
          return {
            ...mondai,
            mp3: mondai.tasks.find(task => task.type === 5),
            tasks: mondai.tasks.map((task) => {
              if (task.type === 13) {
                const data = this.last_result[`data_${this.stage}`];
                if (this.last_result && data && JSON.parse(data)) {
                  if (JSON.parse(data).hasOwnProperty(task.id)) {
                    let idx = 0;
                    const ansArr = JSON.parse(data)[task.id];
                    task.blockAnswers = JSON.parse(task.value).question.map(
                      (q) => {
                        if (q.type === "question" && idx < ansArr.length) {
                          return ansArr[idx++];
                        }
                        return "&&&&";
                      }
                    );
                  } else {
                    task.blockAnswers = JSON.parse(task.value).question.map(
                      (q) => (q.type === "default" ? "&&&&" : "")
                    );
                  }
                } else {
                  task.blockAnswers = JSON.parse(task.value).question.map((q) =>
                    q.type === "default" ? "&&&&" : ""
                  );
                }
              }
              return task;
            }),
          };
        }) || []
      );
    },
    currentStageAnswers() {
      if (!this.last_result) {
        return {};
      }
      if (!this.last_result[`data_${this.stage}`]) {
        return {};
      }
      return JSON.parse(this.last_result[`data_${this.stage}`]) || {};
    },
    userAnswers() {
      let data_1 = {};
      let data_2 = {};
      let data_3 = {};
      if (!this.result) return {};
      if (this.result.data_1) {
        data_1 = JSON.parse(this.result.data_1) || {};
      }
      if (this.result.data_2) {
        data_2 = JSON.parse(this.result.data_2) || {};
      }
      if (this.result.data_3) {
        data_3 = JSON.parse(this.result.data_3) || {};
      }
      return Object.assign(data_1, data_2, data_3);
    },
  },
  watch: {
    stage: {
      handler: function (val) {
        const that = this;
        if (val > 0 || (val < 4 && this.result)) {
          this.$emit("stage-changed", val);
          setTimeout(() => {
            this.initTimer();
          }, 1000);

          if (this.lesson.type === 'exam') {
            if (this.currentMondais[this.currentMondai].mp3) {
              this.mp3Object = new Audio(`https://mp3-v2.dungmori.com/${JSON.parse(this.currentMondais[this.currentMondai].mp3.value)?.link}`);

              this.mp3Object.addEventListener("timeupdate", () => {
                that.mp3CurrentTime = that.mp3Object.currentTime;
              });

              this.mp3Object.addEventListener("loadedmetadata", () => {
                that.mp3Duration = that.mp3Object.duration;
              });

              this.mp3Object.addEventListener("ended", () => {
                that.mp3Playing = false;
                that.mp3Object.pause();
              });
            }
          }

          if (this.stage === 3) {
            this.mp3Object = new Audio();
            this.mp3Object.src = `https://mp3-v2.dungmori.com/${this.stageData.mp3}`;
            let lastResultMp3Time = this.last_result?.mp3_time || 0;
            let localMp3Time = localStorage.getItem(
              `last_result_mp3_time_${that.last_result?.user?.id}_${that.lesson.id}`
            );

            const time = Math.max(localMp3Time || 0, lastResultMp3Time || 0);
            this.mp3Object.currentTime = time / 1000;

            this.mp3Object.addEventListener("timeupdate", () => {
              that.mp3CurrentTime = that.mp3Object.currentTime;
            });

            this.mp3Object.addEventListener("loadedmetadata", () => {
              that.mp3Duration = that.mp3Object.duration;
            });

            this.mp3Object.addEventListener("ended", () => {
              that.mp3Playing = false;
              that.mp3Object.pause();
              // that.mp3CurrentTime = 0;
            });
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    const that = this;
    this.level = this.courseLevels[this.lesson.course_id];
    this.getThisLesson();
    const offset = 100; // Adjust this offset as needed
    window.addEventListener("scroll", () => {
      that.sticky = window.scrollY >= offset;
    });
    document.addEventListener("visibilitychange", function () {
      if (document.visibilityState === "hidden") {
        that.tabVisible = false;
        that.savePassedTime();
        if (that.stage === 3 && that.mp3Object) {
          that.mp3Object.pause();
          that.mp3Playing = false;
          that.saveMp3Time();
        }
      } else {
        that.tabVisible = true;
        if (
          that.stage === 3 &&
          that.mp3Object &&
          that.mp3CurrentTime < that.mp3Duration
        ) {
          that.mp3Object.play();
          that.mp3Playing = true;
        }
      }
    });
  },
  methods: {
    isLong(a) {
      if (a.value.includes('img')) return false;
      if (this.getAnswerLength(a.value) > 14 ||
        a.value.includes('ruby')) return true;
      return false;
    },
    async goToNextLesson() {
      const that = this;
      const url = `/khoa-hoc/${this.nextLesson.course_slug}/lesson/${this.nextLesson.id}-${this.nextLesson.SEOurl}`;
      if (this.lesson.type === 'last_exam') {
        window.location.href = url;
        return;
      }
      await $.get(
        window.location.origin + "/get-lesson-percent/" + this.lesson.id
      ).then((res) => {
        if (that.lesson.type === 'exam') {
          if (that.examStage > 0 && that.examStage < 4) {
            $("#modal-confirm-leave-exam")
              .find(".confirm-leave__title")
              .text("Bạn muốn chuyển bài?");

            $("#span_text_exercise").text("Cố gắng học tiếp để hoàn thành tiến trình bạn iu nha!");
            $("#modal-confirm-leave-exam").find(".btn-confirm-leave").text("Chuyển");
            $("#modal-confirm-leave-exam").modal("show");
            $("#modal-confirm-leave-exam").data("url", url);
            return;
          } else if (that.lesson.require && res < 85) {
            $("#modal-confirm-leave")
              .find(".confirm-leave__title")
              .text("Bạn muốn chuyển bài?");

            $("#span_text_exercise").text("Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành");
            $("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển");
            $("#modal-confirm-leave").modal("show");
            $("#modal-confirm-leave").data("url", url);
            return;
          } else {
            window.location.href = url;
          }
        }
      });
    },
    async saveResultImage() {
      const that = this;
      this.saveImageLoading = true;

      await domtoimage.toPng(document.getElementById('resultCanvas'))
        .then(function (dataUrl) {
          var link = document.createElement('a');
          link.download = 'ket-qua-thi.png';
          link.href = dataUrl;
          link.click();
          that.saveImageLoading = false;
        });

    },
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    },
    togglePlayAnswer(audio, id) {
      const that = this;
      if (id) {
        if (!this.mp3[id]) {
          const audioObject = new Audio(`https://mp3-v2.dungmori.com/${audio}`);

          this.$set(this.mp3, id, {
            object: audioObject,
            src: audioObject.src,
            playing: false,
            currentTime: 0,
            duration: 0,
          });

          audioObject.addEventListener("timeupdate", () => {
            that.$set(this.mp3[id], 'currentTime', audioObject.currentTime);
          });

          audioObject.addEventListener("loadedmetadata", () => {
            that.$set(this.mp3[id], 'duration', audioObject.duration);
          });

          audioObject.addEventListener("ended", () => {
            that.$set(this.mp3[id], 'playing', false);
            audioObject.pause();
          });
        }

        // Toggle play/pause
        if (this.mp3[id].playing) {
          this.mp3[id].object.pause();
          this.$set(this.mp3[id], 'playing', false);
        } else {
          this.mp3[id].object.play();
          this.$set(this.mp3[id], 'playing', true);
        }
      } else {
        if (this.answerMp3Object) {
          this.answerMp3Object.pause();
          this.answerMp3Object = null;
          this.answerMp3CurrentTime = 0;
          this.answerMp3Duration = 0;
          this.answerMp3Playing = false;
        }

        this.answerMp3Object = new Audio(`https://mp3-v2.dungmori.com/${audio}`);
        this.answerMp3CurrentTime = 0;
        this.answerMp3Duration = 0;
        this.answerMp3Playing = false;

        this.answerMp3Object.addEventListener("timeupdate", () => {
          that.answerMp3CurrentTime = that.answerMp3Object.currentTime
        });

        this.answerMp3Object.addEventListener("loadedmetadata", () => {
          that.answerMp3Duration = that.answerMp3Object.duration
        });

        this.answerMp3Object.addEventListener("ended", () => {
          that.answerMp3Playing = false
          that.answerMp3Object.pause();
        });
        // Toggle play/pause
        if (this.answerMp3Playing) {
          this.answerMp3Object.pause();
          this.answerMp3Playing = false
        } else {
          this.answerMp3Object.play();
          this.answerMp3Playing = true
        }
      }

    },
    togglePlay() {
      if (this.lesson.type === 'last_exam' && !this.mp3Playing && this.mp3CurrentTime >= this.mp3Duration) return;
      this.mp3Playing ? this.mp3Object.pause() : this.mp3Object.play();
      this.mp3Playing = !this.mp3Playing;
    },
    playMp3() {
      let vid = document.getElementById("listeningAudio");
      if (vid) {
        vid.play();
      }
      this.mp3Playing = true;
    },
    getAnswerLength(str) {
      return new DOMParser().parseFromString(str, "text/html").documentElement
        .textContent.length;
    },
    async savePassedTime() {
      const that = this;
      const passedTime = that.passedTime;

      let localPassedTime = localStorage.getItem(
        `last_result_passed_time_${that.last_result?.user?.id}_${that.lesson.id}`
      );
      if (localPassedTime && localPassedTime.length) {
        localPassedTime = JSON.parse(localPassedTime);
        localPassedTime[that.stage - 1] = passedTime;
      } else {
        localPassedTime = [0, 0, 0];
        localPassedTime[that.stage - 1] = passedTime;
      }
      localStorage.setItem(
        `last_result_passed_time_${that.last_result?.user?.id}_${that.lesson.id}`,
        JSON.stringify(localPassedTime)
      );

      var data = new FormData();
      data.append("_token", $('meta[name="csrf-token"]').attr("content"));
      if (that.stage > 0 && that.stage < 4) {
        navigator.sendBeacon(
          `/api/lesson/save-passed-time/${that.last_result?.id}/${passedTime}/${that.stage}`,
          data
        );
      }
    },

    async saveMp3Time() {
      const that = this;
      let localPassedTime = localStorage.getItem(
        `last_result_mp3_time_${that.last_result?.user?.id}_${that.lesson.id}`
      );
      if (that.mp3CurrentTime * 1000 > localPassedTime) {
        localStorage.setItem(
          `last_result_mp3_time_${that.last_result?.user?.id}_${that.lesson.id}`,
          JSON.stringify(that.mp3CurrentTime * 1000)
        );
      }
      var data = new FormData();
      data.append("_token", $('meta[name="csrf-token"]').attr("content"));
      data.append("mp3Time", that.mp3CurrentTime);
      if (that.stage === 3) {
        navigator.sendBeacon(
          `/api/lesson/save-mp3-time/${that.last_result?.id}`,
          data
        );
      }
    },
    checkDevice() {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;

      if (/iPhone|iPad|iPod/i.test(userAgent)) {
        return "ios";
      } else if (/Android/i.test(userAgent)) {
        return "android";
      }
      return "other";
    },
    isValidJSON(str) {
      try {
        JSON.parse(str);
        return true;
      } catch (e) {
        return false;
      }
    },
    renderWidthTooltipSuggest(task) {
      let suggest =
        task.type === 13 ? JSON.parse(task.value).suggest : task.suggest;
      if (suggest === null || suggest === "") {
        return "700px";
      }
      return `${Math.min(700, suggest.length * 10)}px`;
    },
    initTimer() {
      const that = this;
      if (this.stageData) {
        let duration = this.stageData.duration * 60 * 1000;
        // duration = 10 * 1000;
        let localPassedTime = localStorage.getItem(
          `last_result_passed_time_${that.last_result?.user?.id}_${that.lesson.id}`
        );
        const cond =
          this.last_result?.passed_time &&
          this.last_result?.passed_time[this.stage - 1] &&
          this.last_result?.passed_time[this.stage - 1] !== -1;
        if (cond) {
          if (localPassedTime && localPassedTime.length) {
            localPassedTime = JSON.parse(localPassedTime);
            if (
              localPassedTime[this.stage - 1] === -1 ||
              localPassedTime[this.stage - 1] >
                this.last_result?.passed_time[this.stage - 1]
            ) {
              this.passedTime = localPassedTime[this.stage - 1];
            } else {
              this.passedTime = this.last_result?.passed_time[this.stage - 1];
            }
          } else {
            this.passedTime = this.last_result?.passed_time[this.stage - 1];
          }
        }

        that.timer = setInterval(() => {
          let diff = moment.duration(duration - that.passedTime);
          that.timerDisplay.minute =
            diff.minutes() < 0
              ? "00"
              : diff.minutes().toString().padStart(2, "0");
          that.timerDisplay.second =
            diff.seconds() < 0
              ? "00"
              : diff.seconds().toString().padStart(2, "0");
          that.diff = diff.asMilliseconds();
          if (that.tabVisible) {
            that.passedTime += 1000;
          }
          if (that.passedTime > duration) {
            clearInterval(that.timer);
            that.timer = null;
            that.timerDisplay.minute = "00";
            that.timerDisplay.second = "00";
            that.submitExam();
            that.passedTime = 0;
            if (that.lesson.type === "last_exam" && that.stage < 3) {
              that.showTimeUpModal = true;
            }
          }
        }, 1000);
      }
    },
    async getThisLesson() {
      const that = this;
      this.loading = true;
      await axios
        .get(`/api/lesson/get-exam-lesson/${this.lesson.id}`, {
          headers: { "Content-Type": "application/json" },
        })
        .then((response) => {
          that.examData = {
            ...response.data,
            exam_parts: response.data.exam_parts.map((part, pIdx) => {
              part = {
                ...part,
                mondais: part.mondais.map((mondai) => {
                  return {
                    ...mondai,
                    tasks: mondai.tasks.map((task) => {
                      const match = task.value.match(/\{\!\s*(.*?)\s*\!\}/);

                      if (match && match[1]) {
                        task.audio = match[1];
                        task.value = task.value
                          .replace(/\{\!.*?\!\}/, "")
                          .trim();
                      }
                      task.answers = task.answers.map((answer) => {
                        const match = answer.value.match(/\{\!\s*(.*?)\s*\!\}/);

                        if (match && match[1]) {
                          answer.audio = match[1];
                          answer.value = answer.value
                            .replace(/\{\!.*?\!\}/, "")
                            .trim();
                        }

                        return answer;
                      });
                      if (task.type === 13) {
                        if (
                          response.data.exam_result &&
                          response.data.exam_result[`data_${pIdx + 1}`]
                        ) {
                          if (
                            JSON.parse(
                              JSON.stringify(
                                response.data.exam_result[`data_${pIdx + 1}`]
                              )
                            ).hasOwnProperty(task.id)
                          ) {
                            let idx = 0;
                            const ansArr = JSON.parse(
                              JSON.stringify(
                                response.data.exam_result[`data_${pIdx + 1}`]
                              )
                            )[task.id];
                            task.blockAnswers = JSON.parse(
                              task.value
                            ).question.map((q) => {
                              if (
                                q.type === "question" &&
                                idx < ansArr.length
                              ) {
                                return ansArr[idx++];
                              }
                              return "&&&&";
                            });
                          } else {
                            task.blockAnswers = JSON.parse(
                              task.value
                            ).question.map((q) =>
                              q.type === "default" ? "&&&&" : ""
                            );
                          }
                        } else {
                          task.blockAnswers = JSON.parse(
                            task.value
                          ).question.map((q) =>
                            q.type === "default" ? "&&&&" : ""
                          );
                        }
                      }
                      return task;
                    }),
                  };
                }),
              };
              part.mondais = orderBy(part.mondais, ['title'], ['asc'])
              return part;
            }),
          };
          if (that.examData.exam_result) {
            that.result = {
              ...that.examData.exam_result,
              data_1: JSON.stringify(that.examData.exam_result.data_1),
              data_2: JSON.stringify(that.examData.exam_result.data_2),
              data_3: JSON.stringify(that.examData.exam_result.data_3),
            };
          }
          if (that.examData.last_exam_result) {
            that.last_result = {
              ...that.examData.last_exam_result,
              data_1: JSON.stringify(
                that.examData.last_exam_result.data_1
                  ? that.examData.last_exam_result.data_1
                  : {}
              ),
              data_2: JSON.stringify(
                that.examData.last_exam_result.data_2
                  ? that.examData.last_exam_result.data_2
                  : {}
              ),
              data_3: JSON.stringify(
                that.examData.last_exam_result.data_3
                  ? that.examData.last_exam_result.data_3
                  : {}
              ),
            };
          }
          that.loading = false;
        })
        .catch((error) => {
          console.log(error);
        });
    },
    async initChart() {
      const that = this;
      const ctx = document.getElementById("resultChart").getContext("2d");
      const kanjiPercent =
        (that.resultStatistics.kanjiCorrect * 100) /
        that.resultStatistics.kanji.length;
      const vocabularyPercent =
        (that.resultStatistics.vocabularyCorrect * 100) /
        that.resultStatistics.vocabulary.length;
      const grammarPercent =
        (that.resultStatistics.grammarCorrect * 100) /
        that.resultStatistics.grammar.length;
      const readingPercent =
        (that.resultStatistics.readingCorrect * 100) /
        that.resultStatistics.reading.length;
      const listeningPercent =
        (that.resultStatistics.listeningCorrect * 100) /
        that.resultStatistics.listening.length;
      let color = "rgba(87, 208, 97, 0.3)";
      let borderColor = "#57D061";
      if (!that.result?.is_passed) {
        color = "rgba(232, 185, 49, 0.5)";
        borderColor = "#E8B931";
      }
      const resultChart = new Chart(ctx, {
        type: "radar",
        data: {
          labels: ["CHỮ HÁN", "TỪ VỰNG", "NGỮ PHÁP", "ĐỌC HIỂU", "NGHE HIỂU"],
          datasets: [
            {
              label: "# of Votes",
              data: [
                kanjiPercent,
                vocabularyPercent,
                grammarPercent,
                readingPercent,
                listeningPercent,
              ],
              backgroundColor: color,
              borderColor: borderColor,
              borderWidth: 2,
              pointBackgroundColor: "transparent",
              pointBorderColor: "transparent",
              pointHoverBackgroundColor: "transparent",
              pointHoverBorderColor: "transparent",
            },
          ],
        },
        options: {
          plugins: {
            legend: {
              display: false,
            },
          },
          scale: {
            min: 0,
            max: 100,
            stepSize: 20,
          },
          legend: {
            display: true,
            labels: {
              fontSize: 50,
            },
          },
          scales: {
            r: {
              pointLabels: {
                font: {
                  size: 0,
                  weight: "bold",
                  family: "Beanbag_Dungmori_Rounded",
                },
              },
              grid: {
                display: false,
              },
              ticks: {
                display: false,
              },
            },
            x: {
              display: false,
              grid: {
                display: false,
              },
              pointLabels: {
                display: false,
              },
            },
            y: {
              display: false,
              beginAtZero: true,
              grid: {
                display: false,
              },
              pointLabels: {
                display: false,
              },
            },
          },
        },
      });
    },
    getCookie(name) {
      let value = `; ${document.cookie}`;
      let parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop().split(";").shift();
    },
    async startExam() {
      const that = this;
      that.start_loading = true;
      clearInterval(that.timer);
      that.timer = null;
      that.timerDisplay.minute = "00";
      that.timerDisplay.second = "00";
      that.timeUp = false;
      that.isWaiting = 0;
      that.passedTime = 0;

      localStorage.setItem(
        `last_result_passed_time_${that.last_result?.user?.id}_${that.lesson.id}`,
        JSON.stringify([0, 0, 0])
      );
      localStorage.setItem(
        `last_result_mp3_time_${that.last_result?.user?.id}_${that.lesson.id}`,
        0
      );

      await axios
        .get(`/api/lesson/start-exam/${this.lesson.id}`)
        .then((response) => {
          that.last_result = {
            ...response.data.data,
            data_1: JSON.stringify(
              response.data.data.data_1 ? response.data.data.data_1 : {}
            ),
            data_2: JSON.stringify(
              response.data.data.data_2 ? response.data.data.data_2 : {}
            ),
            data_3: JSON.stringify(
              response.data.data.data_3 ? response.data.data.data_3 : {}
            ),
          };
          that.start_loading = false;
          that.stage = 1;
          that.currentMondai = 0;
          setTimeout(() => {
            window.scrollTo({ top: 0, behavior: "smooth" });
          }, 100);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    async selectAnswer(question, answer) {
      const that = this;
      that.start_loading = true;
      const data = {
        question_id: question.id,
        answer_id: answer.id,
        stage: this.stage,
        _token: $('meta[name="csrf-token"]').attr("content"),
      };
      await axios
        .post(`/api/lesson/select-answer/${this.lesson.id}`, data)
        .then((response) => {
          that.last_result = {
            ...response.data.data,
            data_1: JSON.stringify(
              response.data.data.data_1 ? response.data.data.data_1 : {}
            ),
            data_2: JSON.stringify(
              response.data.data.data_2 ? response.data.data.data_2 : {}
            ),
            data_3: JSON.stringify(
              response.data.data.data_3 ? response.data.data.data_3 : {}
            ),
          };
        })
        .catch((error) => {
          console.log(error);
        });
    },
    updateBlocks(task, idx, event) {
      if (!task.blockAnswers) {
        task.blockAnswers = [];
      }
      task.blockAnswers[idx] = event.target.value;
    },
    async submitBlocks(task, idx, event) {
      const that = this;
      if (event.target.value === "") {
        return;
      }
      const data = {
        question_id: task.id,
        answer_id: task.blockAnswers
          .filter((a) => a !== "&&&&")
          .map((a) => (a ? a : "")),
        stage: this.stage,
      };
      await axios
        .post(`/api/lesson/select-answer/${this.lesson.id}`, data)
        .then((response) => {
          that.last_result = {
            ...response.data.data,
            data_1: JSON.stringify(
              response.data.data.data_1 ? response.data.data.data_1 : {}
            ),
            data_2: JSON.stringify(
              response.data.data.data_2 ? response.data.data.data_2 : {}
            ),
            data_3: JSON.stringify(
              response.data.data.data_3 ? response.data.data.data_3 : {}
            ),
          };
        })
        .catch((error) => {
          console.log(error);
        });
    },
    showSubmitExam() {
      this.showSubmitModal = true;
    },
    resetMp3() {
      this.mp3Object = null;
      this.mp3Playing = false;
      this.mp3Duration = 0;
      this.mp3CurrentTime = 0;
    },
    async setAchievement() {
      let that = this;
      let count_lesson_complete = 0;
      await $.post(window.location.origin + "/khoa-hoc/set-achievement", {
        lesson_id: that.lesson.id,
        course_id: that.lesson.course_id,
        progress_before: that.lesson.percent || 0,
      }).then((res) => {
        count_lesson_complete = res.data.lessonComplete;
      });

      return count_lesson_complete;
    },
    async submitExam() {
      if (this.timeUp) return;
      ga("send", "event", "hoc_thu_cate", "submit_test", "submit_test_label");
      const that = this;
      if (that.lesson.type === "exam") {
        that.timeUp = true;
      }
      const data = {
        stage: this.stage,
        _token: $('meta[name="csrf-token"]').attr("content"),
      };

      let localPassedTime = localStorage.getItem(
        `last_result_passed_time_${that.last_result?.user?.id}_${that.lesson.id}`
      );
      if (localPassedTime && localPassedTime.length) {
        localPassedTime = JSON.parse(localPassedTime);
        localPassedTime[that.stage - 1] = -1;
      } else {
        localPassedTime = [0, 0, 0];
        localPassedTime[that.stage - 1] = -1;
      }
      localStorage.setItem(
        `last_result_passed_time_${that.last_result?.user?.id}_${that.lesson.id}`,
        JSON.stringify(localPassedTime)
      );

      if (this.mp3Object) {
        this.mp3Object.pause();
        this.mp3Playing = false;
      }

      await axios
        .post(`/api/lesson/submit-exam/${this.lesson.id}`, data)
        .then((response) => {
          that.last_result = {
            ...response.data.data,
            data_1: JSON.stringify(
              response.data.data.data_1 ? response.data.data.data_1 : {}
            ),
            data_2: JSON.stringify(
              response.data.data.data_2 ? response.data.data.data_2 : {}
            ),
            data_3: JSON.stringify(
              response.data.data.data_3 ? response.data.data.data_3 : {}
            ),
          };
          that.result = {
            ...response.data.data,
            data_1: JSON.stringify(
              response.data.data.data_1 ? response.data.data.data_1 : {}
            ),
            data_2: JSON.stringify(
              response.data.data.data_2 ? response.data.data.data_2 : {}
            ),
            data_3: JSON.stringify(
              response.data.data.data_3 ? response.data.data.data_3 : {}
            ),
          };
          localStorage.setItem(
            `last_result_mp3_time_${that.last_result?.user?.id}_${that.lesson.id}`,
            0
          );
          this.resetMp3()
          if (response.data.data.submit_at) {
            if (that.timer) {
              that.stage = 5;
            } else {
              that.stage = 4;
            }
            that.timeUp = true;
          }
          clearInterval(that.timer);
          that.showSubmitModal = false;
          if (that.stage !== 3) {
            that.isWaiting = true;
          }
          that.setAchievement();
        })
        .catch((error) => {
          console.log(error);
        });
    },
    openResultModal() {
      const that = this;
      this.showModal = true;
      setTimeout(() => {
        that.initChart();
      }, 500);
    },
    closeTimeUpModal() {
      if (this.lesson.type === "exam") {
        this.stage = 4;
      } else {
        this.stage = this.stage + 1;
        this.currentMondai = 0;
        this.isWaiting = false;
        this.passedTime = 0;
      }
      this.showTimeUpModal = false;
    },
    continueExam() {
      if (["N1", "N2"].includes(this.last_result?.course)) {
        if (this.last_result?.passed_time[0] === -1) {
          this.stage = 3;
        } else {
          this.stage = 1;
        }
      } else {
        if (this.last_result?.passed_time[1] === -1) {
          this.stage = 3;
        } else if (this.last_result?.passed_time[0] === -1) {
          this.stage = 2;
        } else {
          this.stage = 1;
        }
      }
      window.scrollTo({ top: 0, behavior: "smooth" });
    },
    async nextStage() {
      const that = this;
      if (this.stage < 3) {
        const data = {
          stage: this.stage,
          _token: $('meta[name="csrf-token"]').attr("content"),
        };
        await axios
          .post(`/api/lesson/next-stage/${this.lesson.id}`, data)
          .then((response) => {
            that.last_result = {
              ...response.data.data,
              data_1: JSON.stringify(
                response.data.data.data_1 ? response.data.data.data_1 : {}
              ),
              data_2: JSON.stringify(
                response.data.data.data_2 ? response.data.data.data_2 : {}
              ),
              data_3: JSON.stringify(
                response.data.data.data_3 ? response.data.data.data_3 : {}
              ),
            };
            clearInterval(that.timer);
            that.timer = null;
            that.timerDisplay.minute = "00";
            that.timerDisplay.second = "00";
            clearInterval(that.waitTimer);

            if (["N1", "N2"].includes(that.result?.course)) {
              if (that.stage === 1) {
                that.stage = 3;
              } else {
                that.stage = that.stage + 1;
              }
            } else {
              that.stage = that.stage + 1;
            }
            that.currentMondai = 0;
            that.isWaiting = false;
            that.passedTime = 0;
          })
          .catch((error) => {
            console.log(error);
          });
      }
      console.log('stage --> ', that.stage)
      that.showTimeUpModal = false;
    },
    nextMondai() {
      const that = this;
      if (
        this.currentMondais.length &&
        this.currentMondai < this.currentMondais.length - 1
      ) {
        this.currentMondai = this.currentMondai + 1;
        window.scrollTo({ top: 0, behavior: "smooth" });
      }
      if (this.lesson.type === 'exam') {
        if (this.currentMondais[this.currentMondai].mp3) {
          this.mp3Object = new Audio(`https://mp3-v2.dungmori.com/${JSON.parse(this.currentMondais[this.currentMondai].mp3.value)?.link}`);

          this.mp3Object.addEventListener("timeupdate", () => {
            that.mp3CurrentTime = that.mp3Object.currentTime;
          });

          this.mp3Object.addEventListener("loadedmetadata", () => {
            that.mp3Duration = that.mp3Object.duration;
          });

          this.mp3Object.addEventListener("ended", () => {
            that.mp3Playing = false;
            that.mp3Object.pause();
          });
        }
      }
    },
    prevMondai() {
      if (this.currentMondai > 0) {
        this.currentMondai = this.currentMondai - 1;
        window.scrollTo({ top: 0, behavior: "smooth" });
      }
      if (this.lesson.type === 'exam') {
        if (this.currentMondais[this.currentMondai].mp3) {
          this.mp3Object = new Audio(`https://mp3-v2.dungmori.com/${JSON.parse(this.currentMondais[this.currentMondai].mp3.value)?.link}`);

          this.mp3Object.addEventListener("timeupdate", () => {
            that.mp3CurrentTime = that.mp3Object.currentTime;
          });

          this.mp3Object.addEventListener("loadedmetadata", () => {
            that.mp3Duration = that.mp3Object.duration;
          });

          this.mp3Object.addEventListener("ended", () => {
            that.mp3Playing = false;
            that.mp3Object.pause();
          });
        }
      }
    },
    jumpToQuestion(mondaiIdx, questionId) {
      this.currentMondai = mondaiIdx;
      setTimeout(() => {
        if (document.getElementById(`question_${questionId}`)) {
          document.getElementById(`question_${questionId}`).scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "center",
          });
        }
      }, 100);
    },
    jumpToResultQuestion(mondaiId, questionId) {
      if (document.getElementById(`result_question_${questionId}`)) {
        document
          .getElementById(`result_question_${questionId}`)
          .scrollIntoView();
      }
    },
    examQuestionState(type, answer) {
      if (type === 3) return answer;
      if (type === 13)
        return answer && answer.length && answer.filter((a) => a !== "").length;
    },
    blockState(question, block, idx) {
      let data_1 = {};
      let data_2 = {};
      let data_3 = {};
      if (!this.result) return {};
      if (this.result.data_1) {
        data_1 = JSON.parse(this.result.data_1) || {};
      }
      if (this.result.data_2) {
        data_2 = JSON.parse(this.result.data_2) || {};
      }
      if (this.result.data_3) {
        data_3 = JSON.parse(this.result.data_3) || {};
      }
      const resultData = Object.assign(data_1, data_2, data_3);

      if (!block || !resultData.hasOwnProperty(question.id)) {
        return null;
      }

      // get index in question blocks
      if (
        question.blockAnswers[idx] ===
        JSON.parse(question.value).question.map((q) => q.result)[idx]
      ) {
        return "correct";
      } else {
        return "wrong";
      }
    },
    questionState(question) {
      let data_1 = {};
      let data_2 = {};
      let data_3 = {};
      if (!this.result) return {};
      if (this.result.data_1) {
        data_1 = JSON.parse(this.result.data_1) || {};
      }
      if (this.result.data_2) {
        data_2 = JSON.parse(this.result.data_2) || {};
      }
      if (this.result.data_3) {
        data_3 = JSON.parse(this.result.data_3) || {};
      }
      const resultData = Object.assign(data_1, data_2, data_3);

      const correctAnswer = question.answers.find((answer) => answer.grade > 0);
      if (!resultData.hasOwnProperty(question.id)) {
        return null;
      } else {
        if (question.type === 3) {
          if (resultData[question.id] === correctAnswer.id) {
            return "correct";
          } else {
            return "wrong";
          }
        }
        if (question.type === 13) {
          if (
            resultData[question.id].join(",") ===
            JSON.parse(question.value)
              .question.filter((q) => q.type === "question")
              .map((q) => q.result)
              .join(",")
          ) {
            return "correct";
          } else {
            return "wrong";
          }
        }
      }
    },
  },
};
</script>
<style lang="scss">
.examApp {
  td {
    border: 1px solid #000 !important;
    padding: 2px !important;
  }
  table {
    line-height: 2.2;
    border-width: 2px;
  }
  table[border="1"] {
    border: 1px solid black;
    border-collapse: collapse;
  }
  .hide-scroll::-webkit-scrollbar {
    display: none;
  }
  img {
    max-width: unset !important;
  }

  .ring-container {
    position: relative;
    width: 8px;
    height: 8px;
  }

  .circle {
    width: 6px;
    height: 6px;
    background-color: #bf6a02;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 0;
  }

  .ringring {
    border: 1px solid #bf6a02;
    -webkit-border-radius: 30px;
    height: 12px;
    width: 12px;
    position: absolute;
    left: -3px;
    top: -1px;
    -webkit-animation: pulsate 1s ease-out;
    -webkit-animation-iteration-count: infinite;
    opacity: 0;
  }
  @-webkit-keyframes pulsate {
    0% {
      -webkit-transform: scale(0.1, 0.1);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      -webkit-transform: scale(1.2, 1.2);
      opacity: 0;
    }
  }
}
</style>
