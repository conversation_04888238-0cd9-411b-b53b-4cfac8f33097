<template>
  <div class="flex items-center">
    <button
      class="flex-none flex items-center justify-between border-none bg-[#F5F5F5] w-[85px] h-[40px] px-4 rounded-full"
      @click="hideMenu"
    >
      <img class="h-[14px]" src="/images/icons/hide.png" alt="hide.png" />
      <span class="text-sm font-averta-regular">Ẩn</span>
    </button>
    <div v-if="currentTab != 'comment'" class="relative w-full ml-5">
      <input
        type="text"
        v-model="searchField"
        placeholder="Tìm kiếm bài học với từ khoá bất kì"
        class="pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full sp:hidden"
      />
      <input
        type="text"
        v-model="searchField"
        placeholder="T<PERSON><PERSON> kiếm bài học"
        class="pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full desktop:hidden"
      />
      <div></div>
      <img
        @click="searchField = ''"
        :class="`${searchField.length > 0 ? 'block' : 'hidden'}`"
        class="absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]"
        src="/images/icons/clear.png"
        alt="clear.png"
      />
      <img
        :class="`${searchField.length === 0 ? 'block' : 'hidden'}`"
        class="absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]"
        src="/images/icons/search.png"
        alt="search.png"
      />
    </div>
  </div>
</template>
<script>
export default {
  props: ["currentTab"],
  data() {
    return {
      searchField: "",
    };
  },
  watch: {
    searchField(value) {
      this.$emit("update-search", value);
    },
  },
  methods: {
    hideMenu() {
      this.$emit("hide-menu");
    },
  },
};
</script>
