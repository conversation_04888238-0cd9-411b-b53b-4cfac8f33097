<template>
  <div class="relative ml-auto transform scale-x-[-1]">
    <svg class="w-[36px] h-[36px] sp:scale-75">
      <circle
          class="text-[#F5F5F5]"
          stroke-width="3"
          stroke="currentColor"
          fill="transparent"
          r="15"
          cx="18"
          cy="18"
      />
      <circle
          :class="Number(percent) < 85 ? 'text-[#E8B931]' : 'text-[#57D061]'"
          stroke-width="3"
          stroke-dasharray="96"
          v-bind:stroke-dashoffset="((100 - Number(percent)) * 96) / 100"
          stroke-linecap="round"
          stroke="currentColor"
          fill="transparent"
          transform="rotate(-90 18 18)"
          r="15"
          cx="18"
          cy="18"
      />
    </svg>
    <div class="absolute inset-0 flex items-center justify-center transform scale-x-[-1]">
      <span class="text-[#414348] sp:text-xs font-averta-semibold text-[10px]">
        {{ Number(percent) }}%
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: ["percent"],
};
</script>
