<template>
  <div>
    <img
        class="h-[90%] w-auto object-cover"
        :alt="alt"
        :src="currentSrc"
        v-on:error="onImageError()"
    />
  </div>

</template>

<script>
export default {
  props: {
    src: {
      type: String,
      required: true, // Đường dẫn ưu tiên 1
    },
    fallbackSrc: {
      type: String,
      default: "", // Đường dẫn fallback
    },
    defaultSrc: {
      type: String,
      default: "/cdn/lesson/default/default.jpg", // Đường dẫn mặc định
    },
    alt: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      currentSrc: this.src,
      fallbackTried: false,
    };
  },
  methods: {
    onImageError(event) {
      console.log(`this.fallbackSrc: `, this.fallbackSrc)
      if (!this.fallbackTried && this.fallbackSrc) {
        // Thử đường dẫn fallback nếu chưa thử
        this.currentSrc = this.fallbackSrc;
        this.fallbackTried = true;
      } else {
        // Nếu fallback lỗi, <PERSON><PERSON><PERSON><PERSON> sang đường dẫn mặc định
        this.currentSrc = this.defaultSrc;
      }
    },
  },
  mounted() {
  }
};
</script>
