<template>
  <span
    ref="trigger"
    data-test-id="sh-app-intersect"
  />
</template>

<script>
/**
 * using the Intersection Observer API to calculate when the component is visible on the viewport and emit an event.
 * The component is using the Options API to make refs Object easily accessible.
 *
 * @fires triggerIntersected
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
 * @see https://stackoverflow.com/questions/61578205/problem-with-intersection-observer-in-vue-js
 */
export default {
  emits: ['triggerIntersected'],
  data(){
    return{
      observer: null,
      options: {
        root: null,
        threshold: '0',
      },
    };
  },
  mounted(){
    this.observer = new IntersectionObserver(entries => {
      this.handleIntersect(entries[0]);
    }, this.options);
    this.observer.observe(this.$refs.trigger);
  },
  unmounted(){
    this.observer.disconnect();
  },
  methods:{
    handleIntersect(entry){
      if (entry.isIntersecting) {
        this.$emit('triggerIntersected');
      } else {
        this.$emit('triggerOutersected');
      }
    },
  },
};
</script>
