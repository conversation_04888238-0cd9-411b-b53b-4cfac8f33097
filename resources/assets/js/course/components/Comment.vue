<template>
  <div class="relative h-[calc(100vh-245px)]">
    <template v-if="isLoading && comments.length == 0">
      <div
          class="mt-5 flex items-center justify-center h-[430px] sp:h-[calc(100vh-300px)]"
      >
        <img
            class="w-[60px] h-[60px]"
            src="/images/icons/loading.gif"
            alt="loading"
        />
      </div>
    </template>
    <div
        class="mt-5 font-averta-regular h-[calc(100vh-300px)] pb-10 overflow-y-auto font-averta-regular relative"
        ref="commentContainer"
    >
      <template v-if="comments.length > 0">
        <div
            v-for="comment in comments"
            :key="`comment-${comment.id}`"
            class="border-b mb-2 pb-2"
        >
          <div class="flex items-start">
            <div class="flex-none">
              <img
                  class="w-[40px] h-[40px] rounded-full"
                  :src="comment.user_info.avatar"
                  alt="avatar"
              />
            </div>
            <div class="ml-3 w-full relative">
              <div
                  v-if="openningReplies.includes(comment.id)"
                  class="absolute h-[calc(100%-72px)] w-[1px] top-[60px] left-[-32px] bg-[#D0D3DA]"
              ></div>
              <div class="relative">
                <div class="font-averta-semibold text-xl">
                  {{ comment.user_info.name }}
                </div>
                <div class="text-[15px]">
                  <span>{{ comment.created_at }}</span>
                  <span v-if="comment.is_owner" class="ml-1">
                    <img
                        class="w-[16px] h-[16px]"
                        src="/images/icons/pin.png"
                        alt="pin"
                    />
                  </span>
                </div>
                <div v-if="comment.is_owner" class="absolute right-0 top-0">
                  <img
                      class="w-[36px] h-[36px] cursor-pointer"
                      src="/images/icons/delete.png"
                      alt="delete.png"
                      @click="onDeleteComment(comment)"
                  />
                </div>
              </div>
              <div
                  class="mt-3 text-base whitespace-pre-line"
                  v-html="escapeHtml(comment.content)"
              ></div>
              <div v-if="comment.image" class="pr-2 mb-1">
                <a
                    data-fancybox="images"
                    :href="comment.image.replace('small', 'default')"
                >
                  <img :src="comment.image"/>
                </a>
              </div>
              <div>
                <div
                    class="text-[#009951] cursor-pointer flex items-center"
                >
                  <svg @click="likeCmt(comment)" data-v-7dd4604c="" width="13" height="12" viewBox="0 0 13 12" fill="none"
                       xmlns="http://www.w3.org/2000/svg" class="mr-1 noFlip">
                    <path data-v-7dd4604c=""
                          d="M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z"
                          :fill="comment.comment_like.includes(parseInt(auth.id)) ? '#009951' : 'none'" stroke="#009951"></path>
                  </svg>
                  <div class="count-like">
                    {{ comment.comment_like.length }}
                  </div>
                  <div @click="toogleReply(comment)">
                    ・{{ comment.replies.length }} phản hồi
                  </div>
                </div>
                <div class="my-3" v-if="openningReplies.includes(comment.id)">
                  <div
                      v-for="reply in comment.replies"
                      :key="`reply-${reply.id}`"
                      class="my-2"
                  >
                    <div class="flex items-start">
                      <div class="image flex-none">
                        <img
                            class="w-[40px] h-[40px] rounded-full"
                            :src="reply.user_info.avatar"
                            alt="avatar"
                        />
                      </div>
                      <div class="ml-2 relative w-full group mb-2">
                        <div
                            v-if="reply.is_owner"
                            class="absolute right-0 top-0 hidden group-hover:inline-flex"
                        >
                          <img
                              class="w-[36px] h-[36px] cursor-pointer"
                              src="/images/icons/delete.png"
                              alt="delete.png"
                              @click="onDeleteReply(comment, reply)"
                          />
                        </div>
                        <div class="font-averta-semibold text-xl">
                          {{ reply.user_info.name }}
                        </div>
                        <div class="text-[15px]">
                          {{ reply.created_at }}
                        </div>
                        <div class="text-base mt-2">
                          <div
                              class="whitespace-pre-line"
                              v-html="escapeHtml(reply.content)"
                          ></div>
                        </div>
                        <div class="flex items-center">
                          <svg @click="likeCmt(reply, 'reply')" data-v-7dd4604c="" width="13" height="12" viewBox="0 0 13 12" fill="none"
                               xmlns="http://www.w3.org/2000/svg" class="mr-1 noFlip cursor-pointer">
                            <path data-v-7dd4604c=""
                                  d="M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z"
                                  :fill="reply.comment_like.includes(parseInt(auth.id)) ? '#009951' : 'none'" stroke="#009951"></path>
                          </svg>
                          <div class="count-like text-[#009951]">
                            {{ reply.comment_like.length }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-if="auth.id" class="flex items-start">
                    <div class="flex-none">
                      <img
                          class="w-[40px] h-[40px] rounded-full"
                          :src="auth.avatar"
                          alt="avatar"
                      />
                    </div>
                    <div v-if="auth.id" class="w-full ml-2">
                      <div class="relative">
                        <Reply
                            :comment="comment"
                            :auth="auth"
                            :current-flashcard="currentFlashcard"
                            v-on:update-reply="onUpdateReply"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-if="isLoading && comments.length > 0">
        <div class="flex items-center justify-center mt-3">
          <img
              class="w-[40px] h-[40px]"
              src="/images/icons/loading.gif"
              alt="loading"
          />
        </div>
      </template>
      <div
          v-if="page < last_page && comments.length > 0 && isLoading == false"
          class="text-[#57D061] text-base w-full flex items-center justify-center mt-3 py-2 border border-dashed border-[#57D061] rounded cursor-pointer"
          @click="onLoadMore"
      >
        Tải thêm bình luận
      </div>
    </div>
    <div
        v-if="auth.id"
        class="absolute bottom-0 left-0 w-full flex items-start"
    >
      <div class="flex-none">
        <img
            class="w-[40px] h-[40px] rounded-full"
            :src="auth.avatar"
            alt="avatar"
        />
      </div>
      <div class="relative ml-3 w-full">
        <CommentForm
            v-model="comment_content"
            class="bg-[#F5F5F5] text-[#1E1E1E] placeholder:text-[#757575] p-3 rounded pr-[55px] w-[calc(100%-10px)]"
            :has-file="files.length > 0"
        />
        <div class="absolute left-[16px] bottom-[10px]">
          <div class="relative flex items-center justify-start gap-x-4">
            <div
                v-for="(file, index) in files"
                :key="`image-key-${index}`"
                class="w-[46px] h-[46px] rounded relative bg-cover bg-center"
                :style="`background-image: url(/cdn/comment/small/${file})`"
                alt="image"
            >
              <img
                  class="w-[20px] absolute right-0.5 top-0.5 cursor-pointer"
                  src="/images/icons/delete2.png"
                  alt="delete2.png"
                  @click="files.splice(index, 1)"
              />
            </div>
          </div>
        </div>
        <input
            type="file"
            ref="imageRef"
            @change="onChangeImage"
            style="display: none"
        />
        <img
            class="cursor-pointer w-[20px] h-[20px] absolute right-[50px] -translate-y-1/2"
            style="top: calc(50%)"
            src="/images/icons/image.png"
            @click="onImageClick"
            alt="image click"
        />
        <img
            class="cursor-pointer w-[20px] h-[20px] absolute right-[20px] -translate-y-1/2"
            style="top: calc(50%)"
            :src="
            comment_content.length > 0
              ? '/images/icons/send-available.png'
              : '/images/icons/send.png'
          "
            @click="onSendComment"
            alt="send"
        />
      </div>
    </div>
    <div
        v-else
        class="absolute bottom-0 left-0 w-full flex items-items justify-center h-[50px] font-averta-regular text-base items-center"
    >
      <a
          data-fancybox
          data-animation-duration="300"
          data-src="#auth-container"
          class=""
      >
        <span
            class="text-[#EF6D13] cursor-pointer underline font-averta-bold"
            onclick="swichTab('login')"
        >Đăng nhập</span
        >
      </a>
      <span class="ml-1">để thêm bình luận</span>
    </div>
  </div>
</template>
<script>
import axios from "axios";
import Reply from "./ReplyContainer.vue";
import CommentForm from "./Textarea.vue";

export default {
  props: ["auth", "currentFlashcard"],
  components: {
    Reply,
    CommentForm,
  },
  data() {
    return {
      // Data from lesson basic new blade
      currentLesson: currentLesson,
      isLoading: false,
      page: 1,
      adminName: "Dũng Mori",
      openningReplies: [],
      comments: [],
      comment_content: "",
      last_page: 1,
      isSubmitting: false,
      files: [],
      loadingDeleteReply: false
    };
  },
  mounted() {
    this.getComments();
  },

  watch: {
    currentFlashcard: {
      handler(newVal, oldVal) {
        if (newVal && (!oldVal || newVal.id !== oldVal.id)) {
          console.log(`newVal: `, newVal);
          console.log('Flashcard changed, getting new comments');
          this.getComments();
        }
      },
      deep: true
    }
  },
  methods: {
    getComments(name = 'lesson', id = this.currentLesson.id) {
      if (this.currentLesson.type === 'flashcard') {
        this.$emit('update-unread');
      }
      let data = {
        page: this.page,
        lesson_id: this.currentLesson.id,
      };

      if (this.currentLesson.type === 'flashcard') {
        data.id = this.currentFlashcard?.id;
        data.name = 'flashcard';
      }
      const urlParams = new URLSearchParams(window.location.search);
      const focusFlashcardId = urlParams.get('focus_fc');

      if (focusFlashcardId) {
        data.id = focusFlashcardId;
      }

      const self = this;
      if (self.isLoading) {
        return;
      }
      self.isLoading = true;
      axios
        .get("/api/comment/list", {
          withCredentials: true,
          params: data,
        })
        .then((response) => {
          const res = response.data;
          console.log(`res getComments(): `, res);
          self.comments = [...self.comments, ...res.data];
          self.last_page = res.paginate.last_page;
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          self.isLoading = false;
        });
    },
    escapeHtml(html) {
      var text = document.createElement("textarea");
      text.textContent = html;
      return text.innerHTML;
    },
    toogleReply(comment) {
      if (this.openningReplies.includes(comment.id)) {
        this.openningReplies = this.openningReplies.filter(
          (item) => item !== comment.id
        );
      } else {
        this.openningReplies.push(comment.id);

        if (comment.is_owner && comment.replies.length > 0) {
          axios.post("/api/comment/update-read-reply", {
            comment_id: comment.id,
          });
          this.$emit("update-unread");
        }
      }
    },
    onUpdateReply(reply) {
      const self = this;
      const data = {
        id: reply.id,
        is_admin: false,
        is_owner: true,
        image: reply.img,
        content: reply.content,
        created_at: reply.time_created,
        user_info: {
          name: self.auth.name,
          avatar: self.auth.avatar,
        },
        parent_id: reply.parent_id,
        count_like: 0,
        comment_like: []
      };

      self.comments = self.comments.map((item) => {
        if (item.id === reply.parent_id) {
          return {
            ...item,
            replies: [...item.replies, data],
          };
        }
        return item;
      });
    },
    onLoadMore() {
      if (this.page >= this.last_page) {
        return;
      }
      this.page++;
      this.getComments();
    },
    onImageClick() {
      if (this.files.length >= 3) {
        alert("Bạn chỉ được phép đăng tối đa 3 ảnh");
        return;
      }
      this.$refs.imageRef.click();
    },
    onChangeImage(e) {
      const self = this;
      const file = e.target.files[0];

      if (self.files.length >= 3) {
        alert("Bạn chỉ được phép đăng tối đa 3 ảnh");
        return;
      }
      if (file) {
        const formData = new FormData();
        formData.append("image", file);
        formData.append("object", "comment");
        axios
          .post("/upload-image", formData, {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          })
          .then((response) => {
            self.files.push(response.data);
            self.$refs.imageRef.value = "";
          })
          .catch((error) => {
            console.log(error);
          });
      }
    },
    onSendComment() {
      if (window.location.href.includes("n4")) {
        ga("send", "event", "hoc_thu_cate", "comment_new_n4", "comment_label");
      } else if (window.location.href.includes("n5")) {
        ga("send", "event", "hoc_thu_cate", "comment_new_n5", "comment_label");
      }

      const self = this;
      if (self.isSubmitting) {
        return;
      }
      self.isSubmitting = true;
      const images = self.files.join(",");
      axios.post("/api/comments/add-new-comment", {
          tbid: self.currentLesson.type === 'flashcard' ? self.currentFlashcard.id : self.currentLesson.id,
          tbname: self.currentLesson.type === 'flashcard' ? 'flashcard' : "lesson",
          content: self.comment_content,
          images: images,
        })
        .then((response) => {
          self.comment_content = "";
          const comment = {
            id: response.data.id,
            is_admin: false,
            is_owner: true,
            image: response.data.img,
            content: response.data.content,
            created_at: response.data.time_created,
            replies: [],
            user_info: {
              name: self.auth.name,
              avatar: self.auth.avatar,
            },
            comment_like: []
          };
          self.files = [];
          self.comments = [comment, ...self.comments];

          comment.count_like = 0; // Bình luận mới có 0 like
          comment.pin = false; // Bình luận mới không phải là ghim

          self.$emit('comment-added', {
            flashcardId: self.currentFlashcard ? self.currentFlashcard.id : null,
            comment: comment
          });

          const customEvent = new CustomEvent('comment-added', {
            detail: {
              flashcardId: self.currentFlashcard ? self.currentFlashcard.id : null,
              comment: comment
            }
          });
          document.dispatchEvent(customEvent);
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          self.isSubmitting = false;
        });
    },
    onDeleteComment(comment) {
      self = this;
      axios
        .post("/api/comments/delete-comment", {
          id: comment.id,
        })
        .then((response) => {
          self.comments = self.comments.filter(
            (item) => item.id !== comment.id
          );
        })
        .catch((error) => {
          console.log(error);
        });
    },
    async onDeleteReply(comment, reply) {
      if (self.loadingDeleteReply) return;
      self = this;
      self.loadingDeleteReply = true
      await axios
        .post("/api/comments/delete-comment", {
          id: reply.id,
        })
        .then(async (response) => {
          const cIdx = self.comments.findIndex((c) => c.id === comment.id);
          self.$set(self.comments[cIdx], 'replies', [...comment.replies].filter((r) => r.id !== reply.id));
          self.loadingDeleteReply = false
          await self.$nextTick();
        })
        .catch((error) => {
          console.log(error);
        });
    },


    likeCmt(cmt, type = 'comment') {
      console.log(`cmt: `, cmt);
      console.log(`this.auth.id: `, this.auth.id);
      console.log(`this.comments: `, this.comments);
      let data = {
        id: cmt.id,
        lesson_id: this.currentLesson.id,
      }

      console.log(`this.comments: `, this.comments);

      axios.post('/api/comments/like-comment', data).then(res => {
        this.comments = this.comments.map((item) => {
          if (type === 'comment') {
            if (item.id === cmt.id) {
              if (item.comment_like.includes(parseInt(this.auth.id))) {
                item.comment_like = item.comment_like.filter((id) => id !== parseInt(this.auth.id));
              } else {
                item.comment_like.push(parseInt(this.auth.id));
              }
            }
          } else {
            if (item.id === cmt.parent_id) {
              item.replies = item.replies.map((reply) => {
                if (reply.id === cmt.id) {
                  if (reply.comment_like.includes(parseInt(this.auth.id))) {
                    reply.comment_like = reply.comment_like.filter((id) => id !== parseInt(this.auth.id));
                  } else {
                    reply.comment_like.push(parseInt(this.auth.id));
                  }
                }
                return reply;
              });
            }
          }
          return item;
        });
        console.log(res);
      }).catch(err => {
        console.log(err);
      });
    }
  },
};
</script>
