<template>
  <div>
    <!-- Popup <PERSON><PERSON><PERSON> g<PERSON> bài học -->
    <div v-if="!isSendRating" id="lesson-rating-popup"
         :class="{'hidden': !showPopup}"
         class="fixed inset-0 z-[200] flex items-center justify-center bg-black bg-opacity-40 h-screen w-screen">
      <div class="bg-white rounded-3xl shadow-lg w-[90%] max-w-[600px] p-8 relative animate-slide-in">

        <!-- Tiêu đề -->
        <div class="mb-6 flex gap-10 items-start justify-between">
          <div class="flex items-center gap-2 justify-start">
            <div>
              <svg v-if="rating === 0 || rating >=4" width="52" height="51" viewBox="0 0 52 51" fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M4.49325 23.9268C2.48318 33.4315 7.51639 45.4648 24.448 46.6017C36.3625 47.4034 46.5037 42.834 48.1292 28.2488C49.4182 16.6854 40.1352 5.08789 27.6528 4.58755C15.7223 4.10873 7.05882 11.8003 4.49325 23.9268Z"
                    fill="white"/>
                <path
                    d="M27.0372 47.1395C26.1862 47.1395 25.3155 47.109 24.42 47.0498C16.7553 46.5352 10.8034 43.7304 7.21058 38.9404C4.0646 34.7458 2.91624 29.2403 4.05926 23.8334C6.72987 11.2083 15.7779 3.66019 27.671 4.1408C33.7084 4.3829 39.265 7.13746 43.3208 11.8934C47.3 16.5614 49.2139 22.5404 48.5712 28.3006C47.7932 35.282 45.0424 40.4181 40.3938 43.5672C36.89 45.9416 32.4033 47.1395 27.0336 47.1395H27.0372ZM4.92988 24.0199C3.84027 29.1721 4.92988 34.4122 7.92096 38.3988C11.3536 42.9754 17.0794 45.6564 24.4805 46.155C34.084 46.8006 45.9059 44.2092 47.6899 28.2001C48.3041 22.6874 46.4668 16.9559 42.6478 12.478C38.754 7.91218 33.4235 5.2688 27.6372 5.03746C16.2088 4.57837 7.50434 11.8521 4.92988 24.0199Z"
                    fill="#0C403F"/>
                <path
                    d="M34.6836 3.93945C34.6836 3.93945 55.2099 10.5766 49.5802 32.3458C49.5802 32.3458 37.2598 27.789 34.6836 3.93945Z"
                    fill="#57D061"/>
                <path
                    d="M49.5789 32.7933C49.5272 32.7933 49.4756 32.7843 49.4257 32.7664C49.2958 32.718 46.1943 31.5379 42.7546 27.372C39.6104 23.5648 35.5831 16.4184 34.2389 3.98697C34.2228 3.83633 34.2834 3.68749 34.4009 3.59065C34.5184 3.4956 34.6751 3.46511 34.8175 3.51174C35.0311 3.58168 40.1 5.25486 44.5528 9.65928C47.1771 12.256 49.0269 15.2599 50.0507 18.5901C51.329 22.7452 51.3148 27.4115 50.0097 32.4579C49.9777 32.5799 49.8975 32.6839 49.7854 32.7413C49.7213 32.7753 49.6501 32.7933 49.5789 32.7933ZM35.2056 4.61105C36.5926 16.3305 40.4062 23.1057 43.3919 26.7426C45.9308 29.8361 48.3094 31.2349 49.2762 31.7173C51.3664 23.0967 49.5753 15.9001 43.951 10.321C40.5682 6.9657 36.719 5.21361 35.2038 4.61105H35.2056Z"
                    fill="#0C403F"/>
                <path
                    d="M34.7534 16.7258C34.6787 16.7258 34.6003 16.7061 34.5309 16.6649C34.5131 16.6541 33.4894 16.0766 32.5867 16.4066C32.3552 16.4909 32.1006 16.3708 32.017 16.1376C31.9333 15.9045 32.0526 15.648 32.284 15.5638C33.5855 15.0867 34.9226 15.8561 34.9796 15.8883C35.1914 16.0121 35.2644 16.2865 35.1398 16.5017C35.0579 16.6451 34.9083 16.724 34.7552 16.724L34.7534 16.7258Z"
                    fill="#0C403F"/>
                <path
                    d="M19.1175 16.7977C18.9626 16.7977 18.8113 16.7152 18.7294 16.57C18.6083 16.3548 18.6849 16.0804 18.8985 15.9584C20.0504 15.3039 21.4908 15.7074 21.5513 15.7253C21.7881 15.7934 21.9252 16.0409 21.8575 16.2794C21.7899 16.5179 21.5442 16.6542 21.3092 16.5879C21.2932 16.5843 20.159 16.2723 19.3383 16.7385C19.2688 16.778 19.1941 16.7959 19.1193 16.7959L19.1175 16.7977Z"
                    fill="#0C403F"/>
                <path
                    d="M17.8879 26.5062C19.0585 26.4738 19.9956 26.0086 19.9809 25.4671C19.9661 24.9255 19.0052 24.5127 17.8345 24.545C16.6639 24.5773 15.7268 25.0425 15.7415 25.5841C15.7563 26.1257 16.7172 26.5385 17.8879 26.5062Z"
                    fill="#F6AEBA"/>
                <path
                    d="M36.8823 25.7569C36.8977 25.2154 35.9612 24.749 34.7906 24.7153C33.62 24.6815 32.6585 25.0931 32.6431 25.6347C32.6277 26.1762 33.5642 26.6426 34.7348 26.6764C35.9055 26.7101 36.8669 26.2985 36.8823 25.7569Z"
                    fill="#F6AEBA"/>
                <path
                    d="M30.0703 23.7959C30.0703 23.7959 27.6792 22.5155 24.5938 24.0327C24.5938 24.0327 24.6258 35.0742 27.1023 34.2905C29.5789 33.5068 30.0703 23.7959 30.0703 23.7959Z"
                    fill="#EF544A"/>
                <path
                    d="M26.8547 34.7773C26.6001 34.7773 26.3544 34.6948 26.1318 34.5298C24.2303 33.1274 24.152 24.9588 24.1484 24.0317C24.1484 23.8595 24.2446 23.7035 24.3977 23.6282C27.6505 22.0285 30.1716 23.343 30.2784 23.3986C30.4315 23.4811 30.5223 23.6443 30.5134 23.8165C30.5134 23.8416 30.3834 26.3002 29.9526 28.8234C29.3401 32.4029 28.4499 34.3308 27.2357 34.7163C27.1075 34.7576 26.9811 34.7773 26.8547 34.7773ZM25.0422 24.315C25.076 27.6667 25.53 32.975 26.6588 33.8053C26.7621 33.8824 26.8529 33.8986 26.9704 33.8609C28.514 33.3713 29.3935 27.7313 29.6107 24.0783C28.9769 23.8308 27.2303 23.3376 25.044 24.315H25.0422Z"
                    fill="#0C403F"/>
                <path
                    d="M17.6005 4.09766C17.6005 4.09766 -5.50916 11.1347 3.32521 33.5746C3.32521 33.5746 17.1358 28.7578 17.6005 4.09766Z"
                    fill="#57D061"/>
                <path
                    d="M3.32303 34.0232C3.14498 34.0232 2.97763 33.9156 2.90997 33.7398C0.267848 27.0292 0.185949 21.0036 2.66606 15.8298C6.91054 6.97074 17.0393 3.79833 17.4684 3.66742C17.6037 3.62617 17.7514 3.65307 17.8654 3.73915C17.9793 3.82523 18.0452 3.96152 18.0416 4.10499C17.7995 16.9722 13.8773 24.4019 10.628 28.367C7.0779 32.7015 3.61323 33.9461 3.46724 33.9963C3.41917 34.0124 3.36932 34.0214 3.32125 34.0214L3.32303 34.0232ZM17.1372 4.73086C14.9099 5.55221 6.96396 8.9165 3.46368 16.2243C1.15093 21.0538 1.18832 26.6902 3.57228 32.983C4.56397 32.5293 7.27019 31.0785 9.98353 27.7465C13.069 23.9572 16.7847 16.904 17.1354 4.73086H17.1372Z"
                    fill="#0C403F"/>
                <g style="mix-blend-mode:multiply" opacity="0.18">
                  <path
                      d="M3.00504 16.1543C3.00504 16.1543 0.914842 26.0427 5.40681 32.488L3.32373 33.5747C3.32373 33.5747 -0.443609 24.8502 3.00504 16.1561V16.1543Z"
                      fill="#221F1F"/>
                </g>
                <path
                    d="M22.9751 20.4433C21.8356 20.9831 21.3994 21.4207 20.8635 22.5702C20.3276 21.4225 19.8932 20.9831 18.752 20.4433C19.8914 19.9035 20.3276 19.4659 20.8635 18.3164C21.3994 19.4641 21.8338 19.9035 22.9751 20.4433Z"
                    fill="#FFDC10"/>
                <path
                    d="M20.8653 23.0176C20.6926 23.0176 20.5359 22.9172 20.4629 22.7594C19.9697 21.7013 19.6154 21.3445 18.565 20.8477C18.4083 20.7742 18.3086 20.6164 18.3086 20.4424C18.3086 20.2685 18.4083 20.1106 18.565 20.0371C19.6136 19.5404 19.9697 19.1835 20.4629 18.1254C20.5359 17.9676 20.6926 17.8672 20.8653 17.8672C21.038 17.8672 21.1946 17.9676 21.2676 18.1254C21.7608 19.1817 22.1151 19.5404 23.1655 20.0371C23.3222 20.1106 23.4219 20.2685 23.4219 20.4424C23.4219 20.6164 23.3222 20.7742 23.1655 20.8477C22.1169 21.3445 21.7608 21.7013 21.2676 22.7594C21.1946 22.9172 21.038 23.0176 20.8653 23.0176ZM19.6973 20.4442C20.2029 20.7616 20.5483 21.1113 20.8653 21.6206C21.1804 21.1113 21.5276 20.7634 22.0332 20.4442C21.5293 20.1268 21.1822 19.7771 20.8653 19.2678C20.5501 19.7771 20.2029 20.125 19.6973 20.4442Z"
                    fill="#0C403F"/>
                <path
                    d="M35.9106 20.4433C34.7712 20.9831 34.335 21.4207 33.7991 22.5702C33.2632 21.4225 32.8287 20.9831 31.6875 20.4433C32.827 19.9035 33.2632 19.4659 33.7991 18.3164C34.335 19.4641 34.7694 19.9035 35.9106 20.4433Z"
                    fill="#FFDC10"/>
                <path
                    d="M33.7969 23.0176C33.6242 23.0176 33.4675 22.9172 33.3945 22.7594C32.9014 21.7013 32.5471 21.3445 31.4966 20.8477C31.3399 20.7742 31.2402 20.6164 31.2402 20.4424C31.2402 20.2685 31.3399 20.1106 31.4966 20.0371C32.5453 19.5404 32.9014 19.1835 33.3945 18.1254C33.4675 17.9676 33.6242 17.8672 33.7969 17.8672C33.9696 17.8672 34.1263 17.9676 34.1993 18.1254C34.6924 19.1817 35.0467 19.5404 36.0972 20.0371C36.2539 20.1106 36.3536 20.2685 36.3536 20.4424C36.3536 20.6164 36.2539 20.7742 36.0972 20.8477C35.0485 21.3445 34.6924 21.7013 34.1993 22.7594C34.1263 22.9172 33.9696 23.0176 33.7969 23.0176ZM32.629 20.4442C33.1346 20.7616 33.48 21.1113 33.7969 21.6206C34.112 21.1113 34.4592 20.7634 34.9648 20.4442C34.461 20.1268 34.1138 19.7771 33.7969 19.2678C33.4818 19.7771 33.1346 20.125 32.629 20.4442Z"
                    fill="#0C403F"/>
              </svg>
              <svg v-else width="52" height="51" viewBox="0 0 52 51" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M4.42093 25.3389C3.13316 34.9453 9.09445 46.5227 26.1413 46.3528C38.1372 46.2329 47.9456 40.9084 48.459 26.2797C48.8667 14.6809 38.6827 3.86362 26.1413 4.32686C14.1543 4.76685 6.06462 13.0819 4.42093 25.3389Z"
                    fill="white"/>
                <path
                    d="M25.776 46.8005C18.2318 46.8005 12.1918 44.4736 8.28917 40.0576C4.81757 36.1299 3.24721 30.7428 3.97873 25.2787C5.69039 12.5191 14.1771 4.31853 26.1265 3.87855C32.1862 3.6514 37.9686 5.9658 42.3953 10.3836C46.7379 14.719 49.1114 20.5176 48.9075 26.2946C48.6606 33.2969 46.298 38.616 41.8838 42.1055C38.0294 45.1532 32.7335 46.7325 26.1462 46.7987C26.0228 46.7987 25.8976 46.8005 25.7742 46.8005H25.776ZM4.86586 25.3986C4.16832 30.6051 5.65998 35.7329 8.95988 39.4674C12.7481 43.7546 18.6843 45.9796 26.139 45.9062C35.8062 45.8096 47.4498 42.3237 48.015 26.266C48.2099 20.7358 45.9313 15.1787 41.7639 11.0185C37.5161 6.77781 31.9715 4.56178 26.1605 4.77462C14.6743 5.19672 6.51491 13.0986 4.86586 25.3986Z"
                    fill="#0C403F"/>
                <path
                    d="M21.2967 25.1351C20.0967 25.1033 19.099 26.0487 19.0672 27.2464C19.0354 28.4463 19.9558 28.7781 21.1558 28.8099C22.3534 28.8417 23.3761 28.5645 23.4102 27.3668C23.4443 26.1691 22.5011 25.1692 21.3012 25.1328H21.2989L21.2967 25.1351Z"
                    fill="#0C403F"/>
                <path
                    d="M21.6171 25.7753C20.9376 25.6049 19.9944 25.9526 19.8263 26.6344C19.6558 27.3139 20.5308 27.2094 21.2103 27.3798C21.8898 27.5503 22.6125 28.0571 22.7829 27.3798C22.9534 26.7026 22.2989 25.9503 21.6194 25.7799L21.6171 25.7753Z"
                    fill="white"/>
                <path
                    d="M20.8408 28.5549C21.184 28.6413 21.659 28.464 21.7453 28.1208C21.8317 27.7777 21.3885 27.8299 21.0454 27.7458C20.7022 27.6595 20.3386 27.4027 20.2522 27.7458C20.1659 28.089 20.4954 28.4685 20.8386 28.5549H20.8408Z"
                    fill="white"/>
                <path
                    d="M31.697 25.1351C30.4971 25.1033 29.4994 26.0487 29.4676 27.2464C29.4358 28.4463 30.3562 28.7781 31.5561 28.8099C32.7538 28.8417 33.7765 28.5645 33.8106 27.3668C33.8447 26.1691 32.9015 25.1692 31.7016 25.1328H31.6993L31.697 25.1351Z"
                    fill="#0C403F"/>
                <path
                    d="M32.0175 25.7753C31.338 25.6049 30.3948 25.9526 30.2266 26.6344C30.0562 27.3139 30.9312 27.2094 31.6107 27.3798C32.2902 27.5503 33.0129 28.0571 33.1833 27.3798C33.3538 26.7026 32.6993 25.9503 32.0198 25.7799L32.0175 25.7753Z"
                    fill="white"/>
                <path
                    d="M31.2412 28.5549C31.5844 28.6413 32.0594 28.464 32.1457 28.1208C32.2321 27.7777 31.7889 27.8299 31.4458 27.7458C31.1026 27.6595 30.739 27.4027 30.6526 27.7458C30.5662 28.089 30.8958 28.4685 31.2389 28.5549H31.2412Z"
                    fill="white"/>
                <path
                    d="M33.8105 4.58398C33.8105 4.58398 58.0903 9.84952 49.6787 32.5804C49.6787 32.5804 35.1484 28.8333 33.8105 4.58398Z"
                    fill="#57D061"/>
                <path
                    d="M49.6811 33.0271C49.6436 33.0271 49.606 33.0217 49.5702 33.0128C49.4164 32.9735 45.7695 31.9987 41.9259 27.9834C38.406 24.3079 34.0652 17.2699 33.3658 4.60683C33.3587 4.46732 33.4159 4.33318 33.5214 4.24196C33.627 4.15074 33.77 4.11497 33.906 4.14538C34.3621 4.24375 45.1274 6.66546 49.8099 15.2076C52.4927 20.1029 52.5911 25.9998 50.0996 32.7338C50.0335 32.9126 49.8635 33.0253 49.6811 33.0253V33.0271ZM34.2959 5.16844C35.0793 17.1375 39.1876 23.8142 42.5286 27.3216C45.4941 30.4373 48.388 31.6624 49.4075 32.0237C51.6539 25.7137 51.5269 20.2049 49.0301 15.6441C45.1507 8.56134 36.596 5.79801 34.2959 5.16844Z"
                    fill="#0C403F"/>
                <path
                    d="M18.9533 4.58398C18.9533 4.58398 -5.32471 9.84952 3.08512 32.5804C3.08512 32.5804 17.6154 28.8333 18.9533 4.58398Z"
                    fill="#57D061"/>
                <path
                    d="M3.08458 33.0273C2.90215 33.0273 2.73223 32.9146 2.66606 32.7357C0.174586 26.0018 0.271169 20.1049 2.9558 15.2096C7.63648 6.66742 18.4018 4.2457 18.8579 4.14733C18.9939 4.11692 19.1369 4.1527 19.2425 4.24391C19.348 4.33513 19.407 4.46927 19.3981 4.60878C18.6987 17.2718 14.3579 24.3098 10.838 27.9853C6.99438 31.9989 3.3475 32.9736 3.19368 33.0148C3.15612 33.0237 3.11856 33.0291 3.08279 33.0291L3.08458 33.0273ZM18.4698 5.1686C16.1697 5.79817 7.61501 8.56151 3.73562 15.6442C1.23878 20.2051 1.11179 25.7138 3.35823 32.0239C4.37771 31.6626 7.27161 30.4374 10.237 27.3218C13.5763 23.8144 17.6864 17.1377 18.4698 5.1686Z"
                    fill="#0C403F"/>
                <path
                    d="M35.0404 22.3227C33.2179 22.3227 31.7387 21.0815 31.669 21.0224C31.4812 20.8615 31.4579 20.5807 31.6189 20.3929C31.7781 20.2051 32.0607 20.1836 32.2485 20.3428C32.2664 20.3589 33.8528 21.6771 35.5394 21.3873C35.7845 21.3462 36.0134 21.5089 36.0545 21.7522C36.0957 21.9954 35.9329 22.2261 35.6897 22.2673C35.4697 22.3048 35.2515 22.3227 35.0386 22.3227H35.0404Z"
                    fill="#0C403F"/>
                <path
                    d="M17.7678 21.9112C17.2545 21.9112 16.9039 21.8361 16.8682 21.8289C16.6267 21.7753 16.4747 21.5356 16.5283 21.296C16.582 21.0563 16.8199 20.9025 17.0613 20.9561C17.081 20.9597 18.9626 21.3496 20.3219 20.0565C20.5008 19.8866 20.7833 19.8937 20.955 20.0726C21.125 20.2514 21.1178 20.534 20.9389 20.7057C19.8783 21.7145 18.6049 21.9112 17.7678 21.9112Z"
                    fill="#0C403F"/>
                <path
                    d="M17.7025 30.6144C18.8785 30.5822 19.8199 30.1182 19.8051 29.578C19.7903 29.0379 18.8249 28.6262 17.6489 28.6584C16.4729 28.6907 15.5315 29.1547 15.5463 29.6948C15.5611 30.2349 16.5265 30.6466 17.7025 30.6144Z"
                    fill="#F6AEBA"/>
                <path
                    d="M36.2222 29.7006C36.2376 29.1605 35.2968 28.6954 34.1209 28.6617C32.9449 28.628 31.979 29.0386 31.9635 29.5787C31.9481 30.1188 32.8889 30.5839 34.0649 30.6176C35.2408 30.6513 36.2067 30.2407 36.2222 29.7006Z"
                    fill="#F6AEBA"/>
                <path
                    d="M24.715 32.1087C24.6023 32.1087 24.4896 32.0657 24.402 31.9817C24.2249 31.81 24.2231 31.5256 24.3948 31.3485C24.4145 31.3271 26.4892 29.2649 29.655 31.082C29.8696 31.2054 29.9429 31.4791 29.8195 31.6919C29.6961 31.9066 29.4225 31.9799 29.2096 31.8565C26.6377 30.3791 25.0512 31.9566 25.0351 31.9727C24.9475 32.0622 24.8312 32.1069 24.715 32.1069V32.1087Z"
                    fill="#0C403F"/>
                <path
                    d="M21.2993 31.1582C21.2993 31.1582 18.6003 34.4402 21.2081 34.4402C23.8158 34.4402 21.2993 31.1582 21.2993 31.1582Z"
                    fill="#6EEBF4"/>
                <path
                    d="M21.21 34.8872C20.4606 34.8872 19.9669 34.6476 19.7433 34.1736C19.2229 33.0701 20.6609 31.2314 20.956 30.8737C21.0418 30.7682 21.1778 30.7109 21.3083 30.7109C21.4443 30.7127 21.573 30.7771 21.6553 30.8862C21.9272 31.2404 23.2525 33.0611 22.7088 34.1629C22.546 34.4938 22.1543 34.8872 21.21 34.8872ZM21.2851 31.9218C20.8111 32.6122 20.3908 33.4546 20.55 33.7908C20.6358 33.9733 21.0401 33.993 21.2082 33.993C21.4264 33.993 21.8073 33.9643 21.9039 33.7676C22.0792 33.4152 21.7036 32.5889 21.2851 31.9218Z"
                    fill="#0C403F"/>
                <path
                    d="M41.7655 16.6694C41.5187 16.6694 41.3184 16.4691 41.3184 16.2222V4.6073C41.3184 4.36048 41.5187 4.16016 41.7655 4.16016C42.0123 4.16016 42.2126 4.36048 42.2126 4.6073V16.2222C42.2126 16.4691 42.0123 16.6694 41.7655 16.6694Z"
                    fill="#0C403F"/>
                <path
                    d="M40.0331 15.976C39.7863 15.976 39.5859 15.7757 39.5859 15.5289V3.91394C39.5859 3.66712 39.7863 3.4668 40.0331 3.4668C40.2799 3.4668 40.4802 3.66712 40.4802 3.91394V15.5289C40.4802 15.7757 40.2799 15.976 40.0331 15.976Z"
                    fill="#0C403F"/>
                <path
                    d="M43.4999 17.4877C43.2531 17.4877 43.0527 17.2874 43.0527 17.0406V5.42566C43.0527 5.17883 43.2531 4.97852 43.4999 4.97852C43.7467 4.97852 43.947 5.17883 43.947 5.42566V17.0406C43.947 17.2874 43.7467 17.4877 43.4999 17.4877Z"
                    fill="#0C403F"/>
                <path
                    d="M45.2343 19.062C44.9874 19.062 44.7871 18.8616 44.7871 18.6148V6.99988C44.7871 6.75305 44.9874 6.55273 45.2343 6.55273C45.4811 6.55273 45.6814 6.75305 45.6814 6.99988V18.6148C45.6814 18.8616 45.4811 19.062 45.2343 19.062Z"
                    fill="#0C403F"/>
              </svg>

            </div>
            <div class="text-[12px] sm:text-[16px] md:text-xl font-bold text-[#07403F] font-beanbag">
              Hãy cho DUNGMORI biết cảm nhận của bạn về khóa học mới này nhé!
            </div>
          </div>
          <!-- Close button -->
          <button onclick="document.getElementById('lesson-rating-popup').classList.add('hidden')"
                  class="text-black hover:text-gray-700">
            <svg width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
        <!-- Chọn số sao -->
        <div class="flex justify-between items-center mb-6 gap-4">
          <div class="flex justify-center gap-4" id="rating-stars">
            <template v-for="n in 5">
              <div class="w-[36px] sm:w-[48px] md:w-[60px]">
                <svg class="cursor-pointer" @click="rating = n" v-if="n > rating" width="100%" height="48"
                     viewBox="0 0 60 48"
                     fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M34.4008 2.43967C36.1771 6.81075 37.8274 11.2044 39.3516 15.6207C45.013 15.9144 50.6629 16.4791 56.2899 17.3036C60.473 17.9361 61.3325 22.4879 57.7683 23.8772C52.955 25.8086 48.325 27.9433 43.8899 30.2587C45.1161 34.7089 46.2163 39.1703 47.1905 43.643C47.8552 46.9298 43.8784 49.1323 41.2999 46.975C37.7472 44.0158 33.9767 41.1921 30 38.5152C26.0233 41.1921 22.2528 44.0158 18.7001 46.975C16.1216 49.1323 12.1448 46.9298 12.8095 43.643C13.7722 39.1703 14.8724 34.7089 16.1101 30.2587C11.675 27.9433 7.045 25.8086 2.23168 23.8772C-1.33248 22.4879 -0.472956 17.9361 3.71006 17.3036C9.32561 16.4791 14.9755 15.9144 20.6484 15.6207C22.1611 11.2044 23.8114 6.81075 25.5992 2.43967C26.8943 -0.813224 33.1172 -0.813224 34.4122 2.43967H34.4008Z"
                      fill="#D9D9D9"/>
                </svg>

                <svg class="cursor-pointer" @click="rating = n" v-else width="100%" height="48" viewBox="0 0 60 48"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M34.4008 2.43967C36.1771 6.81075 37.8274 11.2044 39.3516 15.6207C45.013 15.9144 50.6629 16.4791 56.2899 17.3036C60.473 17.9361 61.3325 22.4879 57.7683 23.8772C52.955 25.8086 48.325 27.9433 43.8899 30.2587C45.1161 34.7089 46.2163 39.1703 47.1905 43.643C47.8552 46.9298 43.8784 49.1323 41.2999 46.975C37.7472 44.0158 33.9767 41.1921 30 38.5152C26.0233 41.1921 22.2528 44.0158 18.7001 46.975C16.1216 49.1323 12.1448 46.9298 12.8095 43.643C13.7722 39.1703 14.8724 34.7089 16.1101 30.2587C11.675 27.9433 7.045 25.8086 2.23168 23.8772C-1.33248 22.4879 -0.472956 17.9361 3.71006 17.3036C9.32561 16.4791 14.9755 15.9144 20.6484 15.6207C22.1611 11.2044 23.8114 6.81075 25.5992 2.43967C26.8943 -0.813224 33.1172 -0.813224 34.4122 2.43967H34.4008Z"
                      fill="url(#paint0_radial_7201_5768)"/>
                  <defs>
                    <radialGradient id="paint0_radial_7201_5768" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse"
                                    gradientTransform="translate(29.9885 23.9224) scale(33.6704 33.1841)">
                      <stop offset="0.12" stop-color="#E9FF6B"/>
                      <stop offset="0.92" stop-color="#FF9D00"/>
                    </radialGradient>
                  </defs>
                </svg>
              </div>
            </template>
          </div>
          <div class="text-[32px] md:text-[44px] text-[#FDAA0F] font-beanbag-medium">
            {{ rating }}/5
          </div>
        </div>

        <!-- Chọn thẻ tag -->
        <div class="mb-6">
          <div class="flex flex-wrap gap-2" id="rating-tags">
            <template v-for="tag in tags">
              <button @click="toggleTag(tag)" type="button" class="rating-tag text-[12px] md:text-[14px]" :data-tag="tag">
                {{ tag }}
              </button>
            </template>
          </div>
        </div>
        <!-- Textarea ý kiến riêng -->
        <div class="mb-6">
        <textarea id="rating-comment" rows="3" v-model="comment"
                  class="w-full border border-gray-300 rounded-xl p-3 focus:outline-none focus:border-[#57D061] resize-none font-averta-regular text-base max-h-[300px] overflow-y-auto"
                  placeholder="Cảm nghĩ của mình về khóa học mới này là..."></textarea>
        </div>
        <!-- Button gửi -->
        <div class="text-center">
          <button id="rating-submit" @click="sendRating()" v-loading="loading"
                  class="cursor-pointer bg-[#CCF8D1] text-bold rounded-full py-[8px] px-[55px] font-beanbag-medium text-[#07403F] text-[12px] md:text-2xl drop-shadow-2xl uppercase">
            gửi tới dungmori
          </button>
        </div>
      </div>
    </div>
  </div>

</template>

<script>

import axios from "axios";

const tags = [
  "Thầy cô truyền cảm hứng",
  "Chất lượng video tốt",
  "Nội dung bài học hay",
  "Giao diện đẹp",
  "Tài liệu đầy đủ",
];

export default {
  name: "PopupRate",
  props: {
    showPopup: {
      type: Boolean,
      default: true,
    },
    courseId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      rating: 5,
      tags: tags,
      selectedTags: [],
      comment: "",
      loading: false,
      isSendRating: false,
    }
  },
  methods: {
    toggleTag(tag) {
      const index = this.selectedTags.indexOf(tag);
      if (index > -1) {
        this.selectedTags.splice(index, 1);
        document.querySelector(`[data-tag="${tag}"]`).classList.remove("active");
      } else {
        // add class active
        document.querySelector(`[data-tag="${tag}"]`).classList.add("active");
        this.selectedTags.push(tag);
      }
    },
    sendRating() {
      this.loading = true;
      // TODO: send data to server
      console.log(`Đánh giá: ${this.rating} sao\nTags: ${this.selectedTags.join(', ')}\nÝ kiến: ${this.comment}`);
      // this.showPopup = false;
      axios.post('/khoa-hoc/save-rating', {
        course_id: this.courseId,
        rating: this.rating,
        message: this.selectedTags.join('. ') + '. ' + this.comment, // nối chuỗi select tag với comment phân cách nhau bởi .
      }).then((response) => {
        this.loading = false;
        this.showPopup = false;
        this.isSendRating = true;
      }).catch((error) => {
        this.loading = false;
        // this.showPopup = false;
        console.log(error);
      });
    }
  }
};
</script>

<style scoped>
#lesson-rating-popup .star {
  color: #FFD600;
  transition: color 0.2s;
}

#lesson-rating-popup .star.text-gray-300 {
  color: #D1D5DB;
}

#lesson-rating-popup .rating-tag {
  border: 2px solid #D1D5DB;
  color: #757575;
  border-radius: 16px;
  padding: 10px 12px;
  font-family: 'Averta-Regular', Arial, sans-serif;
  cursor: pointer;
  transition: border-color 0.2s, color 0.2s, background 0.2s;
}

#lesson-rating-popup .rating-tag.active {
  border-color: #14AE5C;
  color: #14AE5C;
}

#lesson-rating-popup textarea:focus {
  border-color: #57D061;
}

@keyframes slide-in {
  from {
    transform: translateY(40px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slide-in 0.3s;
}

</style>
