<template>
  <div class="relative">
    <div v-if="currentStep === STEP_TYPE.FIRST_VISIT">
      <div
          :class="[active ? 'w-[calc(100vw-465px)]' : 'w-full']"
          class="h-[calc(100vh-60px)] md:h-[calc(100vh-80px)] flex justify-center relative"
      >
        <div class="mt-15 z-20">
          <div
              style="background-image: url('/images/lessons/svg/bg-multi-icon-dmr-gray.svg'), linear-gradient(to top, #75F190, #C6FFD2);"
              class="flex rounded-[40px] border-8 border-white shadow-lg shadow-[#57D061]"
          >
            <div class="mt-[158px] ml-[36px] mr-[20px]">
              <img src="/images/lessons/svg/icon-view-first-visit-flcard.svg"/>
            </div>
            <div class="text-end mt-[64px] mr-[60px]">
              <div
                  style="font-size: clamp(24px , 6.5vw, 80px)"
                  class="uppercase text-[#07403F] font-zuume-semibold"
              >
                <span class="inline-block whitespace-nowrap">{{ dataFlashCard.length }} từ vựng mới</span>
              </div>
              <button
                  style="font-size: clamp(12px , 2vw, 24px)"
                  class="bg-[#EF6D13] uppercase font-averta-bold text-white py-3 px-4 rounded-full"
              >
                <span class="inline-block whitespace-nowrap" @click="currentStep = STEP_TYPE.STACKED_CARD"
                      :disabled="dataFlashCard.length === 0">bắt đầu Học ngay</span>
              </button>
            </div>
          </div>
          <div class="flex items-center justify-center mt-8">
            <div class="text-[#176867] font-beanbag text-bold text-2xl">
              Trải nghiệm học flashcard siêu tiện lợi trên app Dũng Mori
            </div>
            <div>
              <img src="/images/lessons/svg/hold-phone.svg"/>
            </div>
          </div>
        </div>
        <div
            style="background: linear-gradient(#FFDBCE, #FFFFFF); background-clip: text; -webkit-text-fill-color: transparent; font-size: clamp(50px , 10vw, 248px)"
            class="z-10 fixed bottom-0 text-[248px] font-beanbag uppercase font-corporate-rounded"
        >
          フラッシュカード
        </div>
      </div>
    </div>
    <div v-else-if="currentStep === STEP_TYPE.OVERVIEW">
      <div class="pt-15 z-20 relative">
        <div class="max-w-[862px] mx-auto">
          <div class="flex justify-center">
            <div
                :style="`background-image: url('/images/lessons/svg/bg-multi-icon-dmr-gray.svg'), linear-gradient(to top, ${currentLevelResult === LEVEL_RESULT.BAD ? '#FFE7BA' : (currentLevelResult === LEVEL_RESULT.GOOD ? '#FFE5B4' : '#FFD586')}, ${currentLevelResult === LEVEL_RESULT.BAD ? '#FFF9E4' : '#FFF1C2'});`"
                class="container-word-not-remember rounded-[40px] border-8 border-white shadow-lg shadow-[#EDDAB8] flex items-center justify-center max-h-[364px] max-w-[424px] min-h-[300px] min-w-[300px] mr-2"
            >
              <div class="text-center mx-[88px] my-[120px]">
                <div class="text-2xl text-[#07403F] font-beanbag">Từ chưa nhớ</div>
                <div class="text-2xl text-[#07403F] font-beanbag-regular">({{ dataResult.non.length }} từ)</div>
                <div class="mt-4">
                  <button class="w-[230px] bg-[#EF6D13] uppercase font-beanbag text-white py-6 rounded-full text-2xl"
                          @click="studyVocabularyByType('non')">
                    học ngay
                  </button>
                </div>
              </div>
            </div>
            <div
                :style="`background-image: url('/images/lessons/svg/bg-multi-icon-dmr-gray.svg'), linear-gradient(to top, ${currentLevelResult === LEVEL_RESULT.BAD ? '#92FFA9' : (currentLevelResult === LEVEL_RESULT.GOOD ? '#75F190' : '#43EA67')}, ${'#C6FFD2'});`"
                class="container-word-not-remember rounded-[40px] border-8 border-white shadow-lg shadow-[#92FFA9] flex items-center justify-center max-h-[364px] max-w-[424px] min-h-[300px] min-w-[300px] ml-2"
            >
              <div class="text-center mx-[103px] my-[120px]">
                <div class="text-2xl text-[#07403F] font-beanbag">Từ đã học</div>
                <div class="text-2xl text-[#07403F] font-beanbag-regular">({{ dataResult.remember.length }} từ)</div>
                <div class="mt-4">
                  <button class="w-[230px] bg-[#07403F] uppercase font-beanbag text-white py-6 rounded-full text-2xl"
                          @click="studyVocabularyByType('remember')" :disabled="dataResult.remember.length === 0">
                    Học lại
                  </button>
                </div>
              </div>
            </div>
          </div>
          <button
              class="w-full bg-white text-2xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12"
              @click="studyVocabularyByType('all')"
          >
            học Tất cả từ vựng ({{ dataLesson.component.length }} từ)
          </button>
        </div>
      </div>
    </div>
    <div v-else-if="currentStep === STEP_TYPE.STACKED_CARD">
      <div class="content-wrap flashcards-wrap flex flex-col justify-stretch relative">
        <div class="flex justify-between items-center w-[90%] mx-auto">
          <div class="opacity-0">
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 44H30C40 44 44 40 44 30V18C44 8 40 4 30 4H18C8 4 4 8 4 18V30C4 40 8 44 18 44Z"
                    stroke="#757575"
                    stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M31.1401 37V29.2" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path d="M31.1401 14.9V11" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path
                  d="M31.1399 25.2999C34.0118 25.2999 36.3399 22.9718 36.3399 20.0999C36.3399 17.228 34.0118 14.8999 31.1399 14.8999C28.2681 14.8999 25.9399 17.228 25.9399 20.0999C25.9399 22.9718 28.2681 25.2999 31.1399 25.2999Z"
                  stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round"/>
              <path d="M16.8599 37.0001V33.1001" stroke="#757575" stroke-width="3" stroke-miterlimit="10"
                    stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16.8599 18.8V11" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path
                  d="M16.8602 33.0999C19.732 33.0999 22.0602 30.7718 22.0602 27.9C22.0602 25.0281 19.732 22.7 16.8602 22.7C13.9883 22.7 11.6602 25.0281 11.6602 27.9C11.6602 30.7718 13.9883 33.0999 16.8602 33.0999Z"
                  stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="number-card-wrap flex items-center justify-center font-beanbag text-[30px] m-4">
            <div class="word-not-belong text-[#975102] px-[14px]">{{ notBelongedCount }}</div>
            <div class="arrow-to-left px-[14px]">
              <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M17 7C17.5523 7 18 7.44772 18 8C18 8.55228 17.5523 9 17 9V7ZM0.292892 8.70711C-0.0976315 8.31658 -0.0976315 7.68342 0.292892 7.29289L6.65685 0.928932C7.04738 0.538408 7.68054 0.538408 8.07107 0.928932C8.46159 1.31946 8.46159 1.95262 8.07107 2.34315L2.41421 8L8.07107 13.6569C8.46159 14.0474 8.46159 14.6805 8.07107 15.0711C7.68054 15.4616 7.04738 15.4616 6.65685 15.0711L0.292892 8.70711ZM17 9H1V7H17V9Z"
                    fill="#07403F"
                />
              </svg>
            </div>
            <div class="card-number px-[14px] text-[#07403F]">{{
                typeVocabulary.type === 'all' ? dataLesson.component.length - belongedCount - notBelongedCount : dataFlashCard.length - belongedCount - notBelongedCount
              }}
            </div>
            <div class="arrow-to-right px-[14px]">
              <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M1 7C0.447715 7 0 7.44772 0 8C0 8.55228 0.447715 9 1 9V7ZM17.7071 8.70711C18.0976 8.31658 18.0976 7.68342 17.7071 7.29289L11.3431 0.928932C10.9526 0.538408 10.3195 0.538408 9.92893 0.928932C9.53841 1.31946 9.53841 1.95262 9.92893 2.34315L15.5858 8L9.92893 13.6569C9.53841 14.0474 9.53841 14.6805 9.92893 15.0711C10.3195 15.4616 10.9526 15.4616 11.3431 15.0711L17.7071 8.70711ZM1 9H17V7H1V9Z"
                    fill="#07403F"
                />
              </svg>
            </div>
            <div class="word-belonged text-[#009951] px-[14px]">{{ belongedCount }}</div>
          </div>
          <div class="card_setting cursor-pointer popover_setting relative group z-50">
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 44H30C40 44 44 40 44 30V18C44 8 40 4 30 4H18C8 4 4 8 4 18V30C4 40 8 44 18 44Z"
                    stroke="#757575"
                    stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M31.1401 37V29.2" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path d="M31.1401 14.9V11" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path
                  d="M31.1399 25.2999C34.0118 25.2999 36.3399 22.9718 36.3399 20.0999C36.3399 17.228 34.0118 14.8999 31.1399 14.8999C28.2681 14.8999 25.9399 17.228 25.9399 20.0999C25.9399 22.9718 28.2681 25.2999 31.1399 25.2999Z"
                  stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round"/>
              <path d="M16.8599 37.0001V33.1001" stroke="#757575" stroke-width="3" stroke-miterlimit="10"
                    stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16.8599 18.8V11" stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
              <path
                  d="M16.8602 33.0999C19.732 33.0999 22.0602 30.7718 22.0602 27.9C22.0602 25.0281 19.732 22.7 16.8602 22.7C13.9883 22.7 11.6602 25.0281 11.6602 27.9C11.6602 30.7718 13.9883 33.0999 16.8602 33.0999Z"
                  stroke="#757575" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round"
                  stroke-linejoin="round"/>
            </svg>
            <div
                class="popover_setting_content hidden group-hover:block absolute right-[-30px] top-full bg-white shadow-lg rounded-[40px] p-[21px] min-w-[225px] z-10">
              <div class="relative z-10 bg-white">
                <div class="popover_setting_content_item flex justify-between items-center py-2">
                  <div class="popover_setting_content_item_title font-averta-regular text-[#07403F] text-base">
                    Xáo trộn thẻ
                  </div>
                  <label class="shuffle-switch">
                    <input type="checkbox" v-model="isShuffle" @change="handleShuffleChange">
                    <span class="shuffle-slider round">
                    <span class="slider-circle"></span>
                  </span>
                  </label>
                </div>
                <div class="popover_setting_content_item flex justify-between items-center py-2">
                  <div class="popover_setting_content_item_title font-averta-regular text-[#07403F] text-base">
                    Mặt trước thẻ
                  </div>
                  <label class="language-switch">
                    <input type="checkbox" v-model="isJapanese" @change="handleLanguageChange">
                    <span class="language-slider round">
                    <span class="slider-text">{{ isJapanese ? 'JA' : 'VN' }}</span>
                    <span class="flag" :class="isJapanese ? 'ja' : 'vi'"></span>
                  </span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="cards-wrap content mx-auto w-[80%] max-w-[648px]">
          <div class="content-wrap mx-auto mb-6">
            <div id="stacked-cards-block" class="stackedcards stackedcards--animatable a_cursor--pointer">
              <div class="stackedcards-container" style="margin-bottom: 20px;">
                <div
                    v-for="(card, index) in dataFlashCard"
                    :key="card.id"
                    :data-id="card.id"
                    class="card-item"
                    :class="{
                  'stackedcards-active': index === 0,
                  'stackedcards-top': true,
                  'stackedcards--animatable': true,
                  'stackedcards-origin-top': true
                }"
                >
                  <div class="card-inner" :class="{ 'flip': !isJapanese }" :data-id="card.id">
                    <div class="card__face card__face--jp card__face--front">
                      <div class="card-wrap p-4 h-[90%] overflow-y-auto relative">
                        <div class="card_header flex items-center">
                          <div v-if="card.value.audio"
                               class="card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer"
                               @click="playAudio(card.id, card.value.audio)"
                          >
                            <svg class="noFlip" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                                  fill="#4E87FF"/>
                              <path
                                  d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                                  fill="#4E87FF"/>
                              <path opacity="0.4"
                                    d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                    fill="#4E87FF"/>
                              <path
                                  d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                                  fill="#4E87FF"/>
                            </svg>
                          </div>
                          <div class="card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]"
                               v-html="card.value.word_stress">
                          </div>
                        </div>
                        <div class="card_content min-h-[calc(100%-34px)] flex flex-col">
                          <div
                              class="content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex"
                              style="flex-grow: 1;"
                              v-html="card.value.word">
                          </div>
                          <div v-if="card.value.front_image" class="content-img text-center p-[40px]">
                            <img
                                :src="`https://video-test.dungmori.com/images/${card.value.front_image}`">
                          </div>
                          <div v-if="card.value.example.length" class="example-wrap">
                            <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                              Ví dụ
                            </p>
                            <div class="list-example">
                              <template v-for="(example, index) in card.value.example">
                                <div class="example-item flex items-start mb-1">
                                  <svg v-if="example.audio" class="w-[36px] h-[36px] noFlip cursor-pointer" width="24"
                                       height="24" viewBox="0 0 24 24" fill="none"
                                       xmlns="http://www.w3.org/2000/svg"
                                       @click="playAudio(card.id + '_example_' + index, example.audio)">
                                    <path
                                        d="M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z"
                                        fill="#4E87FF"/>
                                    <path
                                        d="M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z"
                                        fill="#4E87FF"/>
                                    <path opacity="0.4"
                                          d="M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z"
                                          fill="#4E87FF"/>
                                    <path
                                        d="M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z"
                                        fill="#4E87FF"/>
                                  </svg>
                                  <div class="ml-2 font-beanbag-regular text-2xl flex items-start w-[90%]">
                                    {{ index + 1 }}. <span class="ml-2" v-html="example.example"></span>
                                  </div>
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]">
                        <svg class="m-3 noFlip" width="33" height="28" viewBox="0 0 33 28" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                          <path
                              d="M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z"
                              fill="#FF7C79" stroke="#FF7C79"/>
                        </svg>
                      </div>
                    </div>
                    <div class="card__face card__face--vi card__face--back">
                      <div class="card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative">
                        <div
                            class="card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5"
                            style="flex-grow: 1"
                        >
                          <div class="text-center" v-html="card.value.meaning"></div>
                        </div>
                        <div class="font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]"
                             v-html="card.value.kanji_meaning"></div>
                        <div class="card_img_back p-[40px] content-img text-center" v-if="card.value.back_image">
                          <img
                              :src="`https://video-test.dungmori.com/images/${card.value.back_image}`">
                        </div>
                        <div v-if="card.value.meaning_example.length" class="example-wrap">
                          <p class="w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2">
                            Ví dụ
                          </p>
                          <div class="list-example">
                            <template v-for="(meaning_example, index) in card.value.meaning_example">
                              <div class="example-item flex items-center mb-1">
                                <div class="ml-2 font-averta-regular text-2xl flex items-start">
                                  {{ index + 1 }}. <span class="ml-1" v-html="meaning_example"></span>
                                </div>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                      <div class="how-remember-wrap px-4 h-[40%] flex flex-col">
                        <div class="how-remember-wrap-header flex  mb-5">
                          <div class="font-beanbag-medium text-[#757575] text-xl">
                            Cách nhớ
                          </div>
                          <div class="border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"></div>
                        </div>
                        <div v-if="card.comment && card.comment.user_info"
                             class="how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"
                             style="">
                          <div class="how-remember-wrap-avatar w-[28px]">
                            <img class="rounded-full"
                                 :src="`/cdn/avatar/small/${card.comment.user_info.avatar}`">
                          </div>
                          <div class="how-remember-wrap-info flex text-[#073A3B]">
                          <span class="font-averta-bold">
                            {{ card.comment.user_info.name }}・
                          </span>
                            <span class="font-averta-regular">
                            {{ card.comment.time_created }}
                          </span>
                            <svg v-if="card.comment.pin" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z"
                                  fill="#B3B3B3"/>
                            </svg>
                          </div>
                          <div class="col-start-2 font-averta-regular text-[#07403F]" style="display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;"
                          >
                            {{ card.comment.content }}
                          </div>
                          <div class="col-start-2 flex justify-between items-center">
                            <div class="font-averta-regular text-[#009951] flex">
                              <div class="flex items-center mr-5">
                                <svg class="mr-1 noFlip" width="13" height="12" viewBox="0 0 13 12" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                  <path
                                      d="M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z"
                                      :fill="card.comment.comment_like.length ? '#009951' : 'none'" stroke="#009951"/>
                                </svg>
                                {{ card.comment.count_like }}
                              </div>
                              <div v-if="card.comment && card.comment.replies">
                                {{ card.comment.replies.length }} Trả lời
                              </div>
                            </div>
                            <div class="underline decoration-solid text-[#009951] cursor-pointer noFlip"
                                 @click="toggleCommentTab('open')">
                              Xem thêm >>
                            </div>
                          </div>
                        </div>
                        <div v-else
                             class="items-center flex grow "
                             style="flex-grow: 1;">
                          <div
                              class="underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip"
                              @click="toggleCommentTab('open')">
                            Đóng góp cách nhớ của bạn >>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                  class="stackedcards--animatable stackedcards-overlay left stackedcards-origin-top font-averta-bold text-[#975102]">
                Chưa nhớ
              </div>
              <div
                  class="stackedcards--animatable stackedcards-overlay right stackedcards-origin-top font-averta-bold text-[#02542D]">
                Đã học
              </div>
            </div>
          </div>
          <div class="group-actions flex justify-between items-center">
            <button
                :disabled="!actionBtn"
                :class="{'opacity-50': !actionBtn}"
                class="w-[37%] left-action flex justify-center items-center a-cursor-pointer bg-[#FFF1BB] rounded-full py-[21px] px-[43px] font-beanbag-medium text-[#07403F] text-2xl drop-shadow-2xl"
                @click="swipeLeft"
            >
              <svg class="mr-2" width="23" height="18" viewBox="0 0 23 18" fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M21.2842 7.80392C21.9448 7.80392 22.4803 8.33943 22.4803 9C22.4803 9.66057 21.9448 10.1961 21.2842 10.1961V7.80392ZM1.30128 9.84575C0.834185 9.37866 0.834185 8.62134 1.30128 8.15425L8.91306 0.542469C9.38015 0.0753727 10.1375 0.0753727 10.6046 0.542469C11.0717 1.00957 11.0717 1.76688 10.6046 2.23398L3.83854 9L10.6046 15.766C11.0717 16.2331 11.0717 16.9904 10.6046 17.4575C10.1375 17.9246 9.38015 17.9246 8.91306 17.4575L1.30128 9.84575ZM21.2842 10.1961H2.14703V7.80392H21.2842V10.1961Z"
                    fill="#07403F"/>
              </svg>
              Chưa nhớ
            </button>
            <button
                :class="{'opacity-50': !actionBtn}"
                :disabled="!actionBtn"
                class="rounded-full bg-white drop-shadow-2xl p-4 cursor-pointer" @click="undo">
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M11.8833 30.5168H25.2166C29.8166 30.5168 33.5499 26.7834 33.5499 22.1834C33.5499 17.5834 29.8166 13.8501 25.2166 13.8501H6.88327"
                    stroke="#07403F" stroke-width="2.5" stroke-miterlimit="10" stroke-linecap="round"
                    stroke-linejoin="round"/>
                <path d="M10.7166 18.0167L6.44993 13.7501L10.7166 9.4834" stroke="#07403F" stroke-width="2.5"
                      stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button
                :class="{'opacity-50': !actionBtn}"
                :disabled="!actionBtn"
                class="w-[37%] right-action flex justify-center items-center a-cursor-pointer bg-[#CEFFD8] rounded-full py-[21px] px-[43px] font-beanbag-medium text-[#07403F] text-2xl drop-shadow-2xl"
                @click="swipeRight"
            >
              Đã học
              <svg class="ml-2" width="22" height="18" viewBox="0 0 22 18" fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M1.19994 7.8C0.537194 7.8 -6.41346e-05 8.33726 -6.41346e-05 9C-6.41346e-05 9.66274 0.537194 10.2 1.19994 10.2V7.8ZM21.2485 9.84853C21.7171 9.3799 21.7171 8.6201 21.2485 8.15147L13.6117 0.514718C13.1431 0.0460892 12.3833 0.0460892 11.9147 0.514718C11.446 0.983348 11.446 1.74315 11.9147 2.21177L18.7029 9L11.9147 15.7882C11.446 16.2569 11.446 17.0167 11.9147 17.4853C12.3833 17.9539 13.1431 17.9539 13.6117 17.4853L21.2485 9.84853ZM1.19994 10.2H20.3999V7.8H1.19994V10.2Z"
                    fill="#07403F"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
      <!--    <div class="get-cmt p-8 bg-[#57D061] font-beanbag-medium text-2xl text-white cursor-pointer" @click="getResult()">-->
      <!--      lay cmt-->
      <!--    </div>-->

    </div>
    <div v-else-if="currentStep === STEP_TYPE.RESULT">
      <!--    <div class="get-cmt p-8 bg-[#57D061] font-beanbag-medium text-2xl text-white cursor-pointer" @click="getResult()">-->
      <!--      lay cmt-->
      <!--    </div>-->
      <div class="pt-15 z-20 relative">
        <div class="w-[424px] mx-auto mb-[100px]">
          <div
              class="mb-[24px] bg-[#FFF193] rounded-full py-3 text-[#07403F] font-beanbag-medium text-2xl w-full text-center cursor-pointer drop-shadow-2xl"
              @click="openResultModal('non')">Chưa nhớ ({{ cumulativeResult.non.length }}) >
          </div>
          <div
              class="mb-[24px] bg-[#CCF8D1] rounded-full py-3 text-[#07403F] font-beanbag-medium text-2xl w-full text-center cursor-pointer drop-shadow-2xl"
              @click="openResultModal('remember')">Đã học ({{ cumulativeResult.remember.length }}) >
          </div>
          <div
              class="mb-[24px] bg-[#CCF8D1] rounded-full py-3 text-[#07403F] font-beanbag-medium text-2xl w-full text-center cursor-pointer drop-shadow-2xl"
              @click="openResultModal('all')">Tất cả từ ({{ dataLesson.component.length }}) >
          </div>
        </div>
        <div
            @click="goToNextLesson()"
            class="btn-next-lesson uppercase font-beanbag-medium text-2xl text-[#07403F] bg-[#57D061] py-3 px-12 rounded-full w-[560px] mx-auto text-center cursor-pointer drop-shadow-2xl">
          {{ nextLesson ? 'bài tiếp theo' : 'Trở lại danh sách bài học' }}
        </div>
      </div>

      <el-dialog
          :visible.sync="dialogUserResultVisible"
          max-width="1097px"
          width="80%"
          height="80vh"
          center
          :show-close="false"
          :close-on-click-modal="true"
          class="dialog-user-result-lesson relative">
        <!-- Slot tùy chỉnh tiêu đề -->
        <template #title>
          <div
              class="custom-title pt-[29px] pl-[86px] pr-[23px] bg-[#F4F5FA] flex justify-between items-end rounded-[32px]">
            <div class="text-[#07403F] font-beanbag text-[20px] ">
              {{
                typeVocabulary.type === 'remember' ? `Từ vựng đã học (${dataResult.remember.length} từ)` : (typeVocabulary.type === 'non' ? `Từ vựng chưa nhớ (${dataResult.non.length} từ)` : `Tất cả từ vựng (${dataLesson.component.length} từ)`)
              }}
            </div>
            <el-button icon="el-icon-close" circle @click="dialogUserResultVisible = false"></el-button>
          </div>
        </template>

        <div class="dialog-wrapper pl-[43px] py-[12px] pr-[38px] mx-[43px] my-[12px] custom-scrollbar bg-[#F4F5FA]">
          <div v-for="item in listVocabulary">
            <div class="border-b-[1px] border-[#D0D3DA] py-4">
              <div class="font-gen-jyuu-gothic text-2xl text-black text-result" v-html="item.value.word">
              </div>
              <div class="font-beanbag-regular text-[#757575] text-xl meaning-result" v-html="item.value.meaning">
              </div>
            </div>
          </div>
        </div>
        <div class="bottom-[60px] w-full flex justify-center w-full left-0">
          <div
              class="px-8 py-4 flex items-center justify-center bg-[#FFF193] rounded-full cursor-pointer drop-shadow-2xl"
              :style="`background: ${typeVocabulary.type === 'non' ? '#FFF193' : '#CCF8D1'}`"
          >
            <div class="text-btn font-beanbag-medium text-[#07403F] text-xl mr-1"
                 @click="studyVocabularyByType(typeVocabulary.type)">
              {{
                typeVocabulary.type === 'remember' ? 'Học lại nhóm từ bạn đã học' : (typeVocabulary.type === 'non' ? 'Học lại nhóm từ chưa nhớ' : 'Học lại tất cả từ vựng')
              }}
            </div>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                  d="M13.0505 19.4198C12.5105 19.4198 11.9405 19.3698 11.3205 19.2698L6.63048 18.5298C5.15048 18.2998 4.01048 17.8898 3.16048 17.2898C0.970475 15.7698 1.10048 13.1598 1.38048 11.3298L2.12048 6.63981C2.80048 2.33981 5.02048 0.729807 9.32048 1.39981L14.0105 2.13981C16.2205 2.48981 19.1905 3.37981 19.3805 7.10981C19.4205 7.74981 19.3805 8.47981 19.2405 9.33981L18.5105 14.0298C17.9305 17.6998 16.2205 19.4198 13.0505 19.4198ZM7.53048 2.74981C5.15048 2.74981 4.07048 3.95981 3.60048 6.87981L2.86048 11.5698C2.40048 14.5198 3.24048 15.5198 4.02048 16.0698C4.68048 16.5398 5.61048 16.8598 6.86048 17.0498L11.5505 17.7898C15.0405 18.3398 16.4805 17.2898 17.0305 13.7898L17.7605 9.09981C17.8805 8.34981 17.9205 7.71981 17.8805 7.18981V7.17981C17.7705 5.07981 16.5805 4.04981 13.7705 3.60981L9.09048 2.87981C8.51048 2.78981 8.00048 2.74981 7.53048 2.74981Z"
                  fill="#07403F"/>
              <path
                  d="M14.6803 22.75C13.8303 22.75 12.8703 22.57 11.7603 22.2L7.25025 20.7C4.68025 19.8499 3.29025 18.6299 2.87025 16.8499C2.80025 16.5499 2.92025 16.2299 3.18025 16.0599C3.44025 15.8899 3.78025 15.8899 4.03025 16.0699C4.69025 16.5399 5.61025 16.8599 6.86025 17.0499L11.5503 17.79C15.0403 18.34 16.4803 17.2899 17.0303 13.7899L17.7603 9.09995C17.8803 8.34995 17.9203 7.71995 17.8803 7.18995C17.8703 6.92995 18.0003 6.66995 18.2303 6.51995C18.4603 6.36995 18.7503 6.35995 18.9903 6.48995C21.6703 7.91995 22.3803 10.2099 21.2203 13.7099L19.7203 18.2199C19.0103 20.3399 18.0803 21.64 16.7803 22.29C16.1503 22.6 15.4603 22.75 14.6803 22.75ZM5.73025 18.36C6.21025 18.6799 6.85025 18.99 7.72025 19.28L12.2303 20.78C13.9503 21.3499 15.1803 21.4 16.1003 20.95C17.0203 20.49 17.7203 19.4699 18.2903 17.7499L19.7903 13.2399C20.6103 10.7599 20.2903 9.43995 19.3403 8.54995C19.3203 8.79995 19.2803 9.05995 19.2403 9.33995L18.5103 14.0299C17.8303 18.33 15.6103 19.9399 11.3103 19.28L6.62025 18.54C6.31025 18.4799 6.01025 18.42 5.73025 18.36Z"
                  fill="#07403F"/>
              <path
                  d="M8.24 9.72023C6.87 9.72023 5.75 8.60023 5.75 7.23023C5.75 5.86023 6.87 4.74023 8.24 4.74023C9.61 4.74023 10.73 5.86023 10.73 7.23023C10.73 8.60023 9.61 9.72023 8.24 9.72023ZM8.24 6.25023C7.7 6.25023 7.25 6.69023 7.25 7.24023C7.25 7.79023 7.69 8.23023 8.24 8.23023C8.78 8.23023 9.23 7.79023 9.23 7.24023C9.23 6.69023 8.78 6.25023 8.24 6.25023Z"
                  fill="#07403F"/>
            </svg>
          </div>
        </div>

      </el-dialog>

    </div>

    <div v-if="currentStep === STEP_TYPE.OVERVIEW || currentStep === STEP_TYPE.RESULT"
         class="pulse-container-flashcard z-0" ref="pulseContainer">
      <div class="pulse-circle-flashcard"
           :style="`background: ${currentLevelResult === LEVEL_RESULT.BAD ? '#E1EBFF' : (currentLevelResult === LEVEL_RESULT.GOOD ? '#FDD7D7' : '#CEFFD8')}`"></div>
    </div>

    <div v-if="currentStep === STEP_TYPE.OVERVIEW"
         class="fixed bottom-0 left-0 z-10 w-full text-center card-img-bottom">
      <img
          :src="`/images/lessons/svg/${currentLevelResult === LEVEL_RESULT.BAD ? 'bot-flcard-less-than-50.svg' : (currentLevelResult === LEVEL_RESULT.GOOD ? 'bot-flcard-overview-more-than-50.svg' : 'bot-flcard-overview-more-than-85.svg')}`"/>
    </div>
    <div v-if="currentStep === STEP_TYPE.RESULT" class="fixed bottom-0 left-0 z-10 w-full text-center card-img-bottom">
      <img
          :src="`/images/lessons/svg/${currentLevelResult === LEVEL_RESULT.BAD ? 'bot-flcard-result-less-than-50.svg' : (currentLevelResult === LEVEL_RESULT.GOOD ? 'bot-flcard-result-50-85.svg' : 'bot-flcard-result-more-than-85.svg')}`"/>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import StackedCards from "../../module/flashcard";

const STEP_TYPE = {
  FIRST_VISIT: 0,
  OVERVIEW: 1,
  STACKED_CARD: 2,
  RESULT: 3,
}

const LEVEL_RESULT = {
  BAD: 0, // < 50%
  GOOD: 1, // >= 50% && < 85%
  EXCELLENT: 2, // >= 85%
}

export default {
  name: "FlashCard",
  props: ["active", "dataLesson"],
  data() {
    return {
      STEP_TYPE: STEP_TYPE,
      LEVEL_RESULT: LEVEL_RESULT,
      currentLevelResult: LEVEL_RESULT.BAD,
      currentStep: STEP_TYPE.FIRST_VISIT,
      dataFlashCard: this.dataLesson.component,
      dataResult: {
        non: [],
        remember: [],
      },
      cumulativeResult: { // Thêm biến để lưu kết quả cộng dồn
        remember: [],
        non: [],
      },
      listComments: [],
      stackedCardsInstance: null,
      notBelongedCount: 0,
      belongedCount: 0,
      isJapanese: true,
      isShuffle: false,
      dialogUserResultVisible: false,
      listVocabulary: [],
      typeVocabulary: {
        belonged: {},
        notBelonged: {},
        type: 'all',
      },
      swipedCards: [],
      nextLesson: nextLessonCurrentGroup,
      actionBtn: 1,
      firstCmt: {},
      authUser: authUser,
      isStatistical: false,
      intervalSendGa: null,
      nextCardWithAudio: null, // Store next card's audio information
    };
  },
  mounted() {
    // Kiểm tra xem có tham số focus_fc trong URL không
    const urlParams = new URLSearchParams(window.location.search);
    const focusFlashcardId = urlParams.get('focus_fc');

    if (focusFlashcardId) {
      // Tìm thẻ flashcard có id tương ứng
      const focusedCard = this.dataFlashCard.find(card => card.id.toString() === focusFlashcardId.toString());

      if (focusedCard) {
        this.actionBtn = 0;

        // Lọc danh sách thẻ flashcard để chỉ hiển thị thẻ có id tương ứng
        this.dataFlashCard = [focusedCard];
        this.$nextTick(() => {
          this.updateCurrentFlashcard();
        });
      } else {
      }
    }


    // Đảm bảo số lượng thẻ luôn được cập nhật chính xác
    this.updateCardCounts();

    // Khởi tạo cumulativeResult với dữ liệu ban đầu từ dataResult
    // Nếu dataResult có dữ liệu (từ API hoặc từ lần học trước)
    if (this.dataResult && (this.dataResult.remember.length > 0 || this.dataResult.non.length > 0)) {
      // Lấy danh sách ID của các thẻ đã học và chưa thuộc
      let rememberIds = [];
      let nonIds = [];

      // Nếu dataResult.remember và dataResult.non là mảng các ID
      if (typeof this.dataResult.remember[0] === 'number' || typeof this.dataResult.remember[0] === 'string') {
        rememberIds = [...this.dataResult.remember];
        nonIds = [...this.dataResult.non];
      }
      // Nếu dataResult.remember và dataResult.non là mảng các đối tượng
      else {
        rememberIds = this.dataResult.remember.map(card => card.id);
        nonIds = this.dataResult.non.map(card => card.id);
      }

      // Cập nhật cumulativeResult
      this.cumulativeResult = {
        remember: rememberIds,
        non: nonIds
      };
    }

    // Lắng nghe sự kiện thêm bình luận mới
    document.addEventListener('comment-added', this.handleCommentAdded);

    let vm = this;

    document.addEventListener('DOMContentLoaded', () => {
      const cardWrap = document.querySelector('.card-wrap');
      if (cardWrap) {
        cardWrap.addEventListener('scroll', () => {
          const isAtBottom =
              cardWrap.scrollHeight - cardWrap.scrollTop <= cardWrap.clientHeight;

          if (isAtBottom) {
            cardWrap.classList.add('fade-hidden');
          } else {
            cardWrap.classList.remove('fade-hidden');
          }
        });
      }
    });

    document.onkeydown = function (e) {
      e = e || window.event;
      if (parseInt(e.keyCode) === 37) {
        const leftButton = document.querySelector('.left-action');
        leftButton.classList.add('active');
        setTimeout(() => {
          leftButton.classList.remove('active');
        }, 300);
        vm.swipeLeft();
      } else if (parseInt(e.keyCode) === 39) {
        const rightButton = document.querySelector('.right-action');
        rightButton.classList.add('active');
        setTimeout(() => {
          rightButton.classList.remove('active');
        }, 300);
        vm.swipeRight();
      } else if (parseInt(e.keyCode) === 40) {
        vm.undo();
      }
    };

    window.addEventListener('beforeunload', this.handleBeforeUnload);

    this.getFirstCmt();
    if (this.isStatistical && [39, 40].includes(parseInt(this.dataLesson.course_id))) {
      this.intervalSendGa = setInterval(() => {
        let event = parseInt(this.dataLesson.course_id) === 39 ? 'n5_flc_time_m' : 'n4_flc_time_m';
        ga("send", "event", "nx_flc_time_m", event, event);
      }, 60000);
    }

    $(document).ready(function () {
      window.onfocus = function () {
        if (this.isStatistical && [39, 40].includes(parseInt(course_id))) {
          this.intervalSendGa = setInterval(() => {
            let event = parseInt(course_id) === 39 ? 'n5_flc_time_m' : 'n4_flc_time_m';
            ga("send", "event", "nx_flc_time_m", event, event);
          }, 60000);
        }
      };
      window.onblur = function () {
        clearInterval(this.intervalSendGa);
        this.intervalSendGa = null;
      };
    });
  },
  methods: {
    handleBeforeUnload(event) {
      if (this.currentStep === STEP_TYPE.STACKED_CARD) {
        // Cập nhật cumulativeResult trước khi lưu
        this.updateCumulativeResult();
        this.savePartialResult();

        if (this.isStatistical && [39, 40].includes(parseInt(this.dataLesson.course_id))) {

          // tỷ lệ số thẻ đã next
          let ratio = this.cumulativeResult.remember.length / this.dataLesson.component.length;

          if (ratio < 0.5) {
            let event = parseInt(this.dataLesson.course_id) === 39 ? 'n5_flc_not_complete' : 'n4_flc_not_complete';
            ga("send", "event", "nx_flc_not_complete", event, event);
          }
        }
      }
      // event.returnValue = 'Bạn có chắc muốn thoát? Tiến trình học sẽ được lưu.';
    },
    playAudio(id, audioUrl) {
      if (this.$root.toggleMp3) {
        // lay APP_ENV xem là bản prod hay dev hay local
        if (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'prod') {
          this.$root.toggleMp3(id, 'flashcard', `https://tokyo-v2.dungmori.com/audio/${audioUrl}`);
        } else {
          this.$root.toggleMp3(id, 'flashcard', `https://video-test.dungmori.com/audio/${audioUrl}`);
        }
        // this.$root.toggleMp3(id, 'flashcard', `https://video-test.dungmori.com/audio/${audioUrl}`);
      } else {
        console.error('toggleMp3 method not found in root instance');
      }
    },
    toggleCommentTab(handle = 'open', extraData = {}) {
      const currentPos = this.stackedCardsInstance ? this.stackedCardsInstance.currentPosition : 0;
      const currentCard = this.dataFlashCard[currentPos];

      const eventData = {
        handle: handle,
        flashcard: currentCard,
        ...extraData
      };

      this.$emit('open-comment-tab', eventData);

      const customEvent = new CustomEvent('open-comment-tab', {detail: eventData});
      document.dispatchEvent(customEvent);
    },
    initStackedCards() {
      this.$nextTick(() => {
        this.stackedCardsInstance = new StackedCards({
          visibleItems: 3,
          margin: 22,
          rotate: true,
          useOverlays: true
        });
      });

      this.updateCurrentFlashcard();
    },

    swipeCard(direction) {
      if (!this.stackedCardsInstance) return;

      const currentPos = this.stackedCardsInstance.currentPosition;
      const currentCard = this.dataFlashCard[currentPos];

      if (currentCard) {
        const type = direction === 'left' ? 'notBelonged' : 'belonged';
        const cardKey = `card_${currentCard.id}_${currentPos}`;

        this.swipedCards.push({
          position: currentPos,
          card: currentCard,
          key: cardKey,
          type: type
        });

        this.typeVocabulary[type] = {
          ...this.typeVocabulary[type],
          [cardKey]: currentCard
        };

        this.updateCumulativeResult();
      }

      if (direction === 'left') {
        this.stackedCardsInstance.swipeLeft();
      } else {
        this.stackedCardsInstance.swipeRight();
      }

      this.updateCardCounts()

      this.updateCurrentFlashcard();

      this.toggleCommentTab('close');

      if (this.isStatistical && [39, 40].includes(parseInt(this.dataLesson.course_id))) {
        let event = parseInt(this.dataLesson.course_id) === 39 ? 'n5_flc_word' : 'n4_flc_word';
        ga("send", "event", "nx_flc_word", event, event);
      }
    },
    swipeLeft() {
      this.swipeCard('left');
    },
    swipeRight() {
      if (this.stackedCardsInstance) {
        const currentPos = this.stackedCardsInstance.currentPosition;
        const nextPos = currentPos + 1;

        if (nextPos < this.dataFlashCard.length) {
          this.nextCardWithAudio = {
            id: this.dataFlashCard[nextPos].id,
            audio: this.dataFlashCard[nextPos].value.audio
          };
        }
      }

      this.swipeCard('right');

      this.$nextTick(() => {
        if (this.nextCardWithAudio && this.nextCardWithAudio.audio) {
          this.playAudio(this.nextCardWithAudio.id, this.nextCardWithAudio.audio);
          this.nextCardWithAudio = null;
        }
      });

      if (this.isStatistical && [39, 40].includes(parseInt(this.dataLesson.course_id))) {
        let event = parseInt(this.dataLesson.course_id) === 39 ? 'n5_flc_right' : 'n4_flc_right';
        ga("send", "event", "nx_flc_right", event, event);
      }
    },

    undo() {
      if (!this.stackedCardsInstance) return;

      if (this.swipedCards.length === 0) return;

      const lastSwipedCard = this.swipedCards.pop();
      const {type, key} = lastSwipedCard;
      const cardElement = document.querySelector(`.card-item[data-id="${lastSwipedCard.card.id}"]`);
      if (cardElement) {
        cardElement.classList.remove('opacity-0-important');
      }

      if (this.typeVocabulary[type] && this.typeVocabulary[type][key]) {
        delete this.typeVocabulary[type][key];
      }

      this.stackedCardsInstance.undo();
      this.updateCardCounts();
      this.updateCurrentFlashcard();
      this.toggleCommentTab('close');
      this.updateCumulativeResult();

    },

    updateCumulativeResult() {
      let updatedRemember = [...new Set(this.cumulativeResult.remember)]; // Loại bỏ trùng lặp
      let updatedNon = [...new Set(this.cumulativeResult.non)]; // Loại bỏ trùng lặp

      for (let key in this.typeVocabulary.belonged) {
        const cardId = this.typeVocabulary.belonged[key].id;
        if (!updatedRemember.includes(cardId)) {
          updatedRemember.push(cardId);
        }
        updatedNon = updatedNon.filter(id => id !== cardId);
      }

      for (let key in this.typeVocabulary.notBelonged) {
        const cardId = this.typeVocabulary.notBelonged[key].id;
        if (!updatedNon.includes(cardId)) {
          updatedNon.push(cardId);
        }
        updatedRemember = updatedRemember.filter(id => id !== cardId);
      }

      this.cumulativeResult = {
        remember: updatedRemember,
        non: updatedNon
      };

      this.dataResult = {
        remember: updatedRemember,
        non: updatedNon
      };
    },

    updateCurrentFlashcard() {
      // Đợi một chút để đảm bảo stackedCardsInstance đã cập nhật currentPosition
      this.$nextTick(() => {
        // Nếu danh sách thẻ flashcard đã được lọc và chỉ có 1 thẻ
        if (this.dataFlashCard.length === 1) {
          const currentCard = this.dataFlashCard[0];
          // Tìm component Container và gọi phương thức setCurrentFlashcard
          const containerComponent = this.$parent.$children.find(child => child.$options.name === 'container');
          if (containerComponent) {
            containerComponent.setCurrentFlashcard(currentCard);
          } else {
            console.error('Container component not found');
          }

          return;
        }

        // Xử lý bình thường nếu có nhiều thẻ
        if (this.stackedCardsInstance) {
          const currentPos = this.stackedCardsInstance.currentPosition;
          const currentCard = this.dataFlashCard[currentPos];

          if (currentCard) {
            // Tìm component Container và gọi phương thức setCurrentFlashcard
            const containerComponent = this.$parent.$children.find(child => child.$options.name === 'container');
            if (containerComponent) {
              containerComponent.setCurrentFlashcard(currentCard);
            } else {
              console.error('Container component not found');
            }
          }
        }
      });
    },

    handleLanguageChange(isJapanese) {
      this.dataFlashCard.forEach((card) => {
        const cardElement = document.querySelector(`.card-inner[data-id="${card.id}"]`);
        if (cardElement) {
          if (!this.isJapanese) {
            cardElement.classList.add('flip');
          } else {
            cardElement.classList.remove('flip');
          }
        }
      });

    },
    handleShuffleChange() {
      if (this.isShuffle) {
        this.shuffleCards();
      } else {
        this.restoreShuffleOrder();
      }
    },

    shuffleCards() {
      const currentPosition = this.stackedCardsInstance ? this.stackedCardsInstance.currentPosition : 0;

      let {dataFlashCardLearned, dataFlashCardRemaining} = this.getFlashcardIds();

      dataFlashCardRemaining = dataFlashCardRemaining.sort(() => Math.random() - 0.5);

      this.dataFlashCard = [...dataFlashCardLearned, ...dataFlashCardRemaining];

      this.$nextTick(() => {
        this.stackedCardsInstance = null;
        this.initStackedCards();
        setTimeout(() => {
          this.stackedCardsInstance.updateUI();
          this.stackedCardsInstance.updateActiveCard();
          this.stackedCardsInstance.currentPosition = currentPosition;

          dataFlashCardLearned.forEach((card) => {
            const cardElement = document.querySelector(`.card-item[data-id="${card.id}"]`);
            if (cardElement) {
              cardElement.classList.add('opacity-0-important');
            }
          });

        }, 100);
      });

      this.updateCardCounts();
    },

    restoreShuffleOrder() {
      const currentPosition = this.stackedCardsInstance ? this.stackedCardsInstance.currentPosition : 0;

      let idsCardLearned = [];
      for (let key of Object.keys(this.typeVocabulary.belonged)) {
        idsCardLearned.push(this.typeVocabulary.belonged[key].id);
      }

      let cardRemaining = this.dataLesson.component.filter(card => !idsCardLearned.includes(card.id));
      let cardLearned = this.dataLesson.component.filter(card => idsCardLearned.includes(card.id));

      this.dataFlashCard = [...cardLearned, ...cardRemaining];

      this.$nextTick(() => {
        this.stackedCardsInstance = null;
        this.initStackedCards();
        setTimeout(() => {
          this.stackedCardsInstance.updateUI();
          this.stackedCardsInstance.updateActiveCard();
          this.stackedCardsInstance.currentPosition = currentPosition;

          cardLearned.forEach((card) => {
            const cardElement = document.querySelector(`.card-item[data-id="${card.id}"]`);
            if (cardElement) {
              cardElement.classList.add('opacity-0-important');
            }
          });

        }, 100);
      });
      this.updateCardCounts();
    },

    getFlashcardIds() {
      let learnedIds = [];
      for (let key in this.typeVocabulary.belonged) {
        learnedIds.push(this.typeVocabulary.belonged[key].id);
      }
      for (let key in this.typeVocabulary.notBelonged) {
        learnedIds.push(this.typeVocabulary.notBelonged[key].id);
      }
      let dataFlashCardLearned = [];
      let dataFlashCardRemaining = [];

      this.dataFlashCard.forEach((card) => {
        if (learnedIds.includes(card.id)) {
          dataFlashCardLearned.push(card);
        } else {
          dataFlashCardRemaining.push(card);
        }
      });

      return {dataFlashCardLearned, dataFlashCardRemaining};
    },

    openResultModal(type = 'all') {
      if (type === 'all') {
        this.listVocabulary = this.dataLesson.component;
      } else if (type === 'remember') {
        this.listVocabulary = this.dataLesson.component.filter(card => this.cumulativeResult.remember.includes(card.id));
      } else if (type === 'non') {
        this.listVocabulary = this.dataLesson.component.filter(card => this.cumulativeResult.non.includes(card.id));
      }

      this.typeVocabulary = {...this.typeVocabulary, type: type};
      this.updateCardCounts();
      this.dialogUserResultVisible = true;
    },
    updateCardCounts() {
      this.belongedCount = Object.keys(this.typeVocabulary.belonged).length;
      this.notBelongedCount = Object.keys(this.typeVocabulary.notBelonged).length;
    },

    getFirstCmt() {
      let ids = [];
      this.dataFlashCard.forEach((item) => {
        ids.push(item.id);
      });
      axios.post('/api/flashcard/get-first-comments', {
        ids: ids,
        name: 'flashcard',
        ref: 'notice',
      }).then((res) => {
        this.firstCmt = res.data.comments;
        this.dataFlashCard.map(card => {
          card.comment = this.firstCmt[card.id];
          return card;
        });
        this.updateCurrentFlashcard();
        this.$forceUpdate();
      }).catch(err => {
      });
    },
    getResult() {
      let dataPost = {
        lesson_id: this.dataLesson.id,
        name: "flashcard",
      };
      axios.post("/api/flashcard/get-result", dataPost).then((res) => {
        if (res.data.result) {
          this.dataResult = JSON.parse(res.data.result.flashcard);
          // lặp qua rememberIds và nonIds xem có id nào không có trong mảng component thì xóa id đó
          this.dataResult.remember = this.dataResult.remember.filter(id => this.dataLesson.component.find(card => card.id === id));
          this.dataResult.non = this.dataResult.non.filter(id => this.dataLesson.component.find(card => card.id === id));

          let rememberIds = [];
          let nonIds = [];
          if (typeof this.dataResult.remember[0] === 'number' || typeof this.dataResult.remember[0] === 'string' || typeof this.dataResult.non[0] === 'number' || typeof this.dataResult.non[0] === 'string') {
            rememberIds = [...this.dataResult.remember];
            nonIds = [...this.dataResult.non];
          } else {
            rememberIds = this.dataResult.remember.map(card => card.id);
            nonIds = this.dataResult.non.map(card => card.id);
          }

          this.cumulativeResult = {
            remember: rememberIds,
            non: nonIds
          };

          rememberIds.forEach((id, index) => {
            this.typeVocabulary.belonged[`card_${id}_${index}`] = this.dataLesson.component.find(card => card.id === id);
          });
          nonIds.forEach((id, index) => {
            this.typeVocabulary.notBelonged[`card_${id}_${index + rememberIds.length}`] = this.dataLesson.component.find(card => card.id === id);
          });

          this.updateCardCounts();


          const learnedIds = [...rememberIds, ...nonIds];
          this.dataFlashCard = this.dataLesson.component.filter(card => !learnedIds.includes(card.id));

          let ratio = rememberIds.length / this.dataLesson.component.length;
          if (ratio < 0.5) {
            this.currentLevelResult = LEVEL_RESULT.BAD;
          } else if (ratio >= 0.5 && ratio < 0.85) {
            this.currentLevelResult = LEVEL_RESULT.GOOD;
          } else {
            this.currentLevelResult = LEVEL_RESULT.EXCELLENT;
          }

          // if (rememberIds.length > 0 || nonIds.length > 0) {
          //   this.$message.info('Đã khôi phục tiến trình học trước đó. Hiển thị các thẻ chưa học.');
          // }

          const urlParams = new URLSearchParams(window.location.search);
          const focusFlashcardId = urlParams.get('focus_fc');

          if (focusFlashcardId) {
            const focusedCard = this.dataLesson.component.find(card => card.id.toString() === focusFlashcardId.toString());
            if (focusedCard) {
              this.dataFlashCard = [focusedCard];
              this.currentStep = STEP_TYPE.STACKED_CARD;
            } else {
              console.error(`Flashcard with id ${focusFlashcardId} not found`);
              this.currentStep = STEP_TYPE.STACKED_CARD;
            }
          } else {
            if (nonIds.length + rememberIds.length < this.dataLesson.component.length) {
              this.currentStep = STEP_TYPE.STACKED_CARD;
            } else {
              this.currentStep = STEP_TYPE.OVERVIEW;
            }
          }
        } else {
          this.currentStep = STEP_TYPE.FIRST_VISIT;
        }
        this.dataFlashCard.map(card => {
          card.comment = this.firstCmt[card.id];
          return card;
        });
      }).catch(err => {
      });
    },
    saveResult() {
      let flashcard_ids = {
        remember: [],
        non: [],
      };

      for (let key in this.typeVocabulary.belonged) {
        flashcard_ids.remember.push(this.typeVocabulary.belonged[key].id);
      }
      for (let key in this.typeVocabulary.notBelonged) {
        flashcard_ids.non.push(this.typeVocabulary.notBelonged[key].id);
      }

      let updatedRemember = [...this.cumulativeResult.remember];
      let updatedNon = [...this.cumulativeResult.non];

      flashcard_ids.remember.forEach(id => {
        if (!updatedRemember.includes(id)) {
          updatedRemember.push(id);
        }
        updatedNon = updatedNon.filter(nonId => nonId !== id);
      });

      flashcard_ids.non.forEach(id => {
        if (!updatedNon.includes(id)) {
          updatedNon.push(id);
        }
        updatedRemember = updatedRemember.filter(rememberId => rememberId !== id);
      });

      this.cumulativeResult = {
        remember: updatedRemember,
        non: updatedNon,
      };

      this.dataResult = {
        remember: updatedRemember,
        non: updatedNon,
      };

      let ratio = updatedRemember.length / this.dataLesson.component.length;
      if (ratio < 0.5) {
        this.currentLevelResult = LEVEL_RESULT.BAD;
      } else if (ratio >= 0.5 && ratio < 0.85) {
        this.currentLevelResult = LEVEL_RESULT.GOOD;
      } else {
        this.currentLevelResult = LEVEL_RESULT.EXCELLENT;
      }

      if (this.isStatistical && [39, 40].includes(parseInt(this.dataLesson.course_id))) {
        let course_name = parseInt(this.dataLesson.course_id) === 39 ? 'n5' : 'n4';

        if (ratio >= 0.7) {
          ga("send", "event", "nx_flc_pass_70", `${course_name}_flc_pass_70`, `${course_name}_flc_pass_70`);
        }

        if (0.5 <= ratio && ratio < 0.7) {
          ga("send", "event", "nx_flc_pass_50_70", `${course_name}_flc_pass_50_70 `, `${course_name}_flc_pass_50_70`);
        }

        if (ratio < 0.5) {
          ga("send", "event", "nx_flc_pass_under_50", `${course_name}_flc_pass_under_50`, `${course_name}_flc_pass_under_50`);
        }
      }
      axios.post("/api/flashcard/save-result", {
        lesson_id: this.dataLesson.id,
        flashcard_ids: {
          remember: updatedRemember,
          non: updatedNon,
        },
      }).then((res) => {
        this.currentStep = STEP_TYPE.RESULT;
        this.dataResult = JSON.parse(res.data.result.flashcard);
        console.log('res.data.result.flashcard:', res.data);

        // Emit event to update lesson progress in sidebar
        this.$root.$emit('flashcard-progress-updated', this.dataLesson.id, res.data.result.example_progress);

        console.log('Flashcard result saved, progress update event emitted for lesson:', this.dataLesson.id);
      }).catch(err => {
        this.$message.error(err);
      });
    },
    savePartialResult() {
      console.log('savePartialResult:');
      axios.post("/api/flashcard/save-result", {
        lesson_id: this.dataLesson.id,
        flashcard_ids: {
          remember: this.cumulativeResult.remember,
          non: this.cumulativeResult.non
        },
      }).then((res) => {
        console.log('Partial flashcard result saved:', res.data);
        // Emit event to update lesson progress in sidebar
        this.$root.$emit('flashcard-progress-updated', this.dataLesson.id);

        console.log('Partial flashcard result saved, progress update event emitted for lesson:', this.dataLesson.id);
      }).catch(err => {
        console.error('Error saving partial result:', err);
        this.$message.error('Lỗi khi lưu tiến trình.');
      });
    },
    studyVocabularyByType(type) {
      let filteredCards = [];
      if (type === 'non') {
        // Sử dụng cumulativeResult thay vì dataResult
        filteredCards = this.dataLesson.component.filter(card =>
            this.cumulativeResult.non.includes(card.id)
        );
      } else if (type === 'remember') {
        // Sử dụng cumulativeResult thay vì dataResult
        filteredCards = this.dataLesson.component.filter(card =>
            this.cumulativeResult.remember.includes(card.id)
        );
      } else if (type === 'all') {
        filteredCards = [...this.dataLesson.component];
      }

      if (filteredCards.length === 0) {
        this.$message.warning(`Không có từ vựng nào trong nhóm "${type === 'non' ? 'Chưa nhớ' : type === 'remember' ? 'Đã nhớ' : 'Tất cả'}".`);
        return;
      }

      // Lưu kết quả hiện tại vào cumulativeResult trước khi reset
      let currentRemember = [];
      let currentNon = [];

      for (let key in this.typeVocabulary.belonged) {
        currentRemember.push(this.typeVocabulary.belonged[key].id);
      }
      for (let key in this.typeVocabulary.notBelonged) {
        currentNon.push(this.typeVocabulary.notBelonged[key].id);
      }

      // Cập nhật cumulativeResult
      let updatedRemember = [...this.cumulativeResult.remember];
      let updatedNon = [...this.cumulativeResult.non];

      currentRemember.forEach(id => {
        if (!updatedRemember.includes(id)) {
          updatedRemember.push(id);
        }
        updatedNon = updatedNon.filter(nonId => nonId !== id);
      });

      currentNon.forEach(id => {
        if (!updatedNon.includes(id)) {
          updatedNon.push(id);
        }
        updatedRemember = updatedRemember.filter(rememberId => rememberId !== id);
      });

      this.cumulativeResult = {
        remember: updatedRemember,
        non: updatedNon,
      };

      // Reset trạng thái cho lần học mới
      this.dataFlashCard = filteredCards;
      this.dataFlashCard.map(card => {
        card.comment = this.firstCmt[card.id];
        return card;
      });

      this.swipedCards = [];
      this.typeVocabulary = {
        belonged: {},
        notBelonged: {},
        type: type,
      };
      this.updateCardCounts();
      this.stackedCardsInstance = null;

      this.currentStep = STEP_TYPE.STACKED_CARD;
      this.dialogUserResultVisible = false;
    },

    handleCommentAdded(event) {

      const eventData = event.detail || {};
      const flashcardId = eventData.flashcardId;
      const newComment = eventData.comment;

      if (!flashcardId || !newComment) {
        return;
      }

      const flashcard = this.dataFlashCard.find(card => card.id === flashcardId);
      if (!flashcard) {
        return;
      }

      if (!flashcard.comment || (flashcard.comment && !flashcard.comment.pin && (flashcard.comment.count_like || 0) <= (newComment.count_like || 0))) {
        flashcard.comment = newComment;
        this.$forceUpdate();
      }

      if (flashcard.comments) {
        flashcard.comments = [newComment, ...flashcard.comments];
      } else {
        flashcard.comments = [newComment];
      }
    },
    goToNextLesson() {
      let url = '';
      if (this.nextLesson) {
        url = `/khoa-hoc/${this.nextLesson.course_slug}/lesson/${this.nextLesson.id}-${this.nextLesson.SEOurl}`;
      } else {
        url = `/khoa-hoc/${course.SEOurl}/group/${this.dataLesson.group_id}`;
      }
      window.location.href = url;
    },

    async sendTelegramMessage(chatId = '-1002337007216', message = null, event = null) {
      setTimeout(() => {
        ga("send 12312", "event 123 12", "hoc_thu_cate 123123 ", " 1231 23", "submit_test_label 12312 3");
      }, 2000);

      if (!chatId) {
        chatId = '-1002337007216';
      }
      if (!message) {
        return;
      }
      const token = '7986490238:AAHdkY1XrXUh35Q2U89lbCx93OshWxS3Cw0';

      const url = `https://api.telegram.org/bot${token}/sendMessage?chat_id=${chatId}&text=${encodeURIComponent(message)}`;

      try {
        const response = await axios.get(url);
        if (response.data.ok) {
          console.log('Telegram message sent successfully!')
        } else {
          console.error('Failed to send Telegram message:', response.data);
        }
      } catch (error) {
        console.error('Error sending Telegram message:', error);
      }
    },
  },

  beforeDestroy() {
    document.removeEventListener('comment-added', this.handleCommentAdded);
    document.removeEventListener('DOMContentLoaded', this.handleScroll);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);

    if (this.stackedCardsInstance) {
      this.stackedCardsInstance.destroy();
    }
  },
  watch: {
    stackedCardsInstance: {
      handler(newVal, OldVal) {
        if (newVal && newVal.currentPosition === newVal.maxElements) {
          this.saveResult();
        }
      },
      deep: true,
    },
    currentStep(newVal) {
      if (newVal === STEP_TYPE.STACKED_CARD) {
        this.initStackedCards();
      }
      if (newVal === STEP_TYPE.RESULT && this.isStatistical && [39, 40].includes(parseInt(this.dataLesson.course_id))) {
        let event = parseInt(this.dataLesson.course_id) === 39 ? 'n5_flc_complete' : 'n4_flc_complete';
        ga("send", "event", "nx_flc_complete", event, event);
      }
    },
    currentLevelResult(newVal) {
    },
    dialogUserResultVisible(newVal, oldVal) {
      if (newVal) {
        setTimeout(() => {
          document.querySelectorAll('.text-result p span').forEach(span => {
            span.style.fontSize = '24px';
          });
          document.querySelectorAll('.meaning-result p span').forEach(rt => {
            rt.style.fontSize = '20px';
          });
        }, 200);
      }
    },
  },
  created() {
    if (parseInt(this.authUser.isTester) === 0 && ["production", "prod"].includes(process.env.NODE_ENV)) {
      this.isStatistical = true;
    }
    this.getResult();
    if (this.isStatistical && [39, 40].includes(parseInt(this.dataLesson.course_id))) {
      setTimeout(() => {
        let event = parseInt(this.dataLesson.course_id) === 39 ? 'n5_flc_lesson_count' : 'n4_flc_lesson_count';
        ga("send", "event", "nx_flc_lesson_count", event, event);
      }, 1000);

      if (!sessionStorage.getItem('isFlashcard')) {
        sessionStorage.setItem('isFlashcard', true);

        let event = parseInt(this.dataLesson.course_id) === 39 ? 'n5_go_to_flc' : 'n4_go_to_flc';
        setTimeout(() => {
          ga("send", "event", "nx_go_to_flc", event, event);
        }, 1000);
      }
    }
  }
};
</script>