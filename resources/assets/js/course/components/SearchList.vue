<template>
  <div class="mt-5 font-averta-regular h-[calc(100vh-245px)] overflow-y-auto">
    <div
        v-for="item in searchedList"
        :key="`search-item-${item.id}`"
        class="py-4 border-b cursor-pointer"
        @click="gotoLesson(item)"
    >
      <div class="flex items-center">
        <div class="flex-none">
          <img
              class="w-[16px]"
              src="/images/icons/play2.png"
              alt="play button"
          />
        </div>
        <div
            class="text-base font-averta-semibold text-[#1E1E1E] ml-5 relative"
        >
          <span
          >{{ item.name }}
            <img
                v-if="item.require"
                class="w-[12px]"
                :src="`/images/icons/require.png`"
                alt="require.png"
            />
          </span>
        </div>
      </div>
      <div class="text-[#757575] text-xs mt-1">
        {{ item.stage_name }} - {{ item.category_name }} - {{ item.group_name }}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ["search", "currentLesson", "stageName"],
  data() {
    return {
      // Data from lesson basic new blade
      allLesson: allLesson,
      searchedList: [],
    };
  },
  watch: {
    search() {
      this.searchedList = [];
      if (this.search.length == 0) {
        return;
      }
      this.searchedList = this.allLesson.filter((lesson) =>
          lesson.name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase().includes(this.search.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase())
      );
    },
  },
  methods: {
    gotoLesson(item) {
      // window.location.href = `/khoa-hoc/${item.course_slug}/lesson/${item.id}-${item.slug}`;

      const url = `/khoa-hoc/${item.course_slug}/lesson/${item.id}-${item.slug}`;

      let tabActive = localStorage.getItem('tab-active-lesson')
      let userResult = localStorage.getItem(`userResult-lesson-${this.currentLesson.id}`)
      console.log(`tabActive: `, tabActive)
      if (userResult) {
        userResult = JSON.parse(userResult);
      }
      console.log(`userResult: `, userResult)
      console.log(`currentLesson: `, this.currentLesson)
      if (['exam', 'last_exam'].includes(this.currentLesson.type)) {
        if (this.examStage > 0 && this.examStage < 4) {
          $("#modal-confirm-leave-exam")
              .find(".confirm-leave__title")
              .text("Xác nhận chuyển bài?");

          $("#span_text_exercise").text("Thời gian vẫn sẽ tiếp tục chạy khi chuyển bài");
          $("#modal-confirm-leave-exam").find(".btn-confirm-leave").text("Chuyển");
          $("#modal-confirm-leave-exam").modal("show");
          $("#modal-confirm-leave-exam").data("url", url);
          return;
        } else {
          window.location.href = url;
        }
      } else {
        if (!this.currentLesson.require) {
          window.location.href = url;
        }

        if (this.currentLesson.require && userResult.ratio_correct_answer < 0.85 && this.currentLesson.percent < 85) {
          $("#modal-confirm-leave")
              .find(".confirm-leave__title")
              .text("Xác nhận chuyển bài?");

          $("#span_text_exercise").text("Bài học có điểm số đạt <85% là bài học chưa đạt định mức hoàn thành");
          $("#modal-confirm-leave").find(".btn-confirm-leave").text("Chuyển");
          $("#modal-confirm-leave").modal("show");
          $("#modal-confirm-leave").data("url", url);
          return;
        } else {
          window.location.href = url;
        }
      }
    }
  },
};
</script>
