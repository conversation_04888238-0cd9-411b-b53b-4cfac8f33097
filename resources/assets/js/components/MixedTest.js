Vue.filter('dateTime', function(value) {
    return moment(value).format('HH:mm DD/MM/YYYY');
});
Vue.filter('isPassed', function(value) {
    return value ? "Đạt" : "Chưa đạt";
});
Vue.filter('mark', function(value) {
    return value < 10 && value > 0 ? '0' + value : value;
});
Vue.component('mixed-test', {
    template: '#mixed-test-template',
    props: ['tasks','lesson'],
    data: function () {
        return {
            url: window.location.origin,
            user_id: 0,
            questions: [],
            titles: [],
            result: {
                passed: [],
                failed: []
            },
            results: [],
            resultQuestions: [],
            selectedResult: {},
            viewHistory: 0,
            current: 0,
            currentQuestion: {},
            loading: false,
            answered: 0,
            mp3: null,
            playingMp3: false,
            state: "",
            autoplay: false,
            showOldResult: 0,
            showExplain: false,
            playingQuestionAudio: false,
            playingAnswerAudio: false,
        };
    },
    mounted: function () {
        var vm = this;
        vm.userId = user_id;
        vm.nextWhenEnter();
        vm.checkAutoplay();
        vm.getQuestions();
    },
    watch: {
        current: function (val) {
            var vm = this;
            if (vm.mp3) {
                vm.pauseAudio();
                if (vm.autoplay && vm.questions[val].question_audio) {
                    setTimeout(function () {
                        vm.playAudio(vm.questions[val].question_audio);
                    }, 30);
                }
            }
        },
    },
    computed: {
        explain: function () {
            var explain = _.find(this.tasks, {type: 4});
            if (explain) {
                return JSON.parse(_.find(this.tasks, {type: 4}).value)[0];
            } else {
                return null;
            }
        },
    },
    methods: {
        getResults: function() {
            var vm = this;

            var lessonId = vm.tasks[0].lesson_id;
            var data = {
                lessonId: lessonId
            };
            $.post(vm.url + "/api/quiz/get-results", data, function (res) {
                if (res.code === 200) {
                    vm.resultQuestions = res.questions.map(function (question) {
                        return question;
                    });
                    vm.results = res.data.map(function (result) {
                        result.data = JSON.parse(result.data);
                        return result;
                    });
                    vm.processResults(vm.resultQuestions);
                    vm.showSelectedResult(vm.results[0]);
                    vm.showOldResult = 1;
                }
            });
        },
        showSelectedResult: function(result) {
            var vm = this;
            vm.selectedResult = result;
            vm.showOldResult = 1;
            vm.resultQuestions = vm.resultQuestions.map(function (question) {
                if (question.type === 11) {
                    question.sortedAnswers = [];
                    vm.selectedResult.data[question.id].forEach(function (id) {
                        if (_.find(question.answers, {'id': parseInt(id)})) {
                            question.sortedAnswers.push(_.find(question.answers, {'id': parseInt(id)}));
                        }
                    });
                }
                return question;
            });
        },
        checkAutoplay: function() {
            var vm = this;
            var settings = {};
            var cookies = localStorage.getItem('quiz_settings_' + vm.userId);
            if (cookies) {
                settings = JSON.parse(cookies);
                vm.autoplay = settings.autoplay;
            } else {
                settings = {
                    autoplay: false
                };
                // setCookie("quiz_settings", JSON.stringify(settings), 30);
                localStorage.setItem("quiz_settings_" + vm.userId, JSON.stringify(settings));
            }
        },
        setProcessCookie: function () {
            var vm = this;
            var setCookieQuestions = vm.questions.map(function (question) {
                return {
                    a: question.answer, // answer
                    ad: question.answered, // answered
                    ca: { // currentAnswer
                        id: question.currentAnswer.id,
                        grade: question.currentAnswer.grade
                    },
                    bs: question.blocks, // block
                    abls: question.answerBlocks, // answerBlocks
                    i: question.id, // id
                    c: question.correct, // correct
                    r: question.resultId // result
                };
            });
            // setCookie("quiz_process_" + vm.tasks[0].lesson_id, JSON.stringify(setCookieQuestions), 30);
            localStorage.setItem("quiz_process_"  + vm.userId + "_" + vm.tasks[0].lesson_id, JSON.stringify(setCookieQuestions));
        },
        nextQuestion: function () {
            var vm = this;
            vm.current++;
            vm.currentQuestion = vm.questions[vm.current];
        },
        getQuestions: function () {
            var vm = this;
            vm.loading = true;
            var lessonId = vm.tasks[0].lesson_id;

            $.post(window.location.origin + "/api/quiz/questions", {lesson_id: lessonId}, function (response) {

                vm.questions = response.data.map(function (question) {
                    question.currentAnswer = {};
                    question.currentAnswers = [];
                    question.answers.forEach(function (answer) {
                        question.currentAnswers.push(null);
                    });
                    question.answered = 0;
                    question.longgggg = 0;
                    question.correct = 0;
                    question.answer = {};
                    question.answers.forEach(function (answer) {
                        if (answer.value.length > 17) {
                            question.longgggg = 1;
                        }
                        return answer;
                    });
                    return question;
                });

                vm.nextLessonUrl = response.next_url;
                vm.titles = response.titles;
                vm.current = 0;
                vm.currentQuestion = vm.questions[vm.current];
                vm.processQuestions(vm.questions);
                vm.getCurrentProcess();
                vm.state = 'testing';
                vm.loading = false;
                if (vm.autoplay && vm.questions[0].question_audio) {
                    setTimeout(function () {
                        vm.playAudio(vm.questions[0].question_audio);
                    },30);
                }
            });
        },
        getCurrentProcess: function () {
            var vm = this;
            var processCookie = localStorage.getItem('quiz_process_'  + vm.userId + '_' + vm.tasks[0].lesson_id);
            if (processCookie) {
                var process = JSON.parse(processCookie);
                vm.questions = vm.questions.map(function (question, index) {
                    var match = _.find(process, {"i": question.id});
                    if (match) {
                        question.answer = match.a ? match.a : {};
                        question.answered = match.ad;
                        question.currentAnswer = match.ca;
                        question.currentAnswer.grade = match.ca.grade;
                        question.blocks = match.bs;
                        question.answerBlocks = match.abls;
                        question.correct = match.c;
                        question.resultId = match.r;
                    }
                    if (question.answered) {
                        vm.current = index;
                    }
                    return question;
                });
            }
        },

        processResults: function(questions) {
            var regexAnswerAudio = /\{\!(.*?)\!\}/;
            var regexQuestionAudio = /\{\?(.*?)\?\}/;
            var regexHidden = /\{\*(.*?)\*\}/;
            var regexGap = /\[\[(.*?)\]\]/;
            var regexStar = /\[\*(.*?)\*\]/;
            questions.forEach(function (question) {
                question.mask = question.value;
                question.answered = 0;
                if (regexAnswerAudio.exec(question.mask)) {
                    question.answer_audio = _.trim(regexAnswerAudio.exec(question.value)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexAnswerAudio, "");
                }
                while (regexAnswerAudio.exec(question.mask)) {
                    question.answer_audio = _.trim(regexAnswerAudio.exec(question.mask)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexAnswerAudio, "");
                }
                if (regexQuestionAudio.exec(question.mask)) {
                    question.question_audio = _.trim(regexQuestionAudio.exec(question.value)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexQuestionAudio, "");
                }
                while (regexQuestionAudio.exec(question.mask)) {
                    question.question_audio = _.trim(regexQuestionAudio.exec(question.mask)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexQuestionAudio, "");
                }
                if (regexHidden.exec(question.mask)) {
                    question.hidden = _.trim(regexHidden.exec(question.value)[1]);
                    question.mask = question.mask.replace(regexHidden, `<span class="text-success text-bold">${question.hidden}</span>`);
                }
                while (regexHidden.exec(question.mask)) {
                    question.hidden = _.trim(regexHidden.exec(question.mask)[1]);
                    question.mask = question.mask.replace(regexHidden, `<span class="text-success text-bold">${question.hidden}</span>`);
                }
                if (regexGap.exec(question.mask)) {
                    question.hidden = _.trim(regexGap.exec(question.value)[1]);
                    question.mask = question.mask.replace(regexGap, `<span class="text-success text-bold">${question.hidden}</span>`);
                }
                while (regexGap.exec(question.mask)) {
                    question.hidden = _.trim(regexGap.exec(question.mask)[1]);
                    question.mask = question.mask.replace(regexGap, `<span class="text-success text-bold">${question.hidden}</span>`);
                }
                if (regexStar.exec(question.mask)) {
                    question.hidden = _.trim(regexStar.exec(question.value)[1]);
                    question.mask = question.mask.replace(regexStar, `<span class="text-success text-bold">${question.hidden}</span>`);
                }
                while (regexStar.exec(question.mask)) {
                    question.hidden = _.trim(regexStar.exec(question.mask)[1]);
                    question.mask = question.mask.replace(regexStar, `<span class="text-success text-bold">${question.hidden}</span>`);
                }
                // if (question.type === 11) {
                //     question.blocks = question.mask.split(/\s+/);
                //     question.answerBlocks = question.mask.split(/\s+/);
                //     question.gapMap = [];
                //     question.blocks.forEach(function (val, index) {
                //         if (val === "____") {
                //             question.gapMap.push(index);
                //         }
                //     });
                // }
                return question;
            });
        },

        processQuestions: function (questions) {
            var vm = this;
            vm.titles.forEach(function (title) {
                questions.forEach(function (question) {
                    if (question.sort > title.sort) {
                        question.section = title.value;
                    }
                    return question;
                });
            });
            var regexAnswerAudio = /\{\!(.*?)\!\}/;
            var regexQuestionAudio = /\{\?(.*?)\?\}/;
            var regexHidden = /\{\*(.*?)\*\}/;
            questions.forEach(function (question) {
                question.mask = question.value;
                question.answered = 0;
                if (regexAnswerAudio.exec(question.mask)) {
                    question.answer_audio = _.trim(regexAnswerAudio.exec(question.value)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexAnswerAudio, "");
                }
                while (regexAnswerAudio.exec(question.mask)) {
                    question.answer_audio = _.trim(regexAnswerAudio.exec(question.mask)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexAnswerAudio, "");
                }
                if (regexQuestionAudio.exec(question.mask)) {
                    question.question_audio = _.trim(regexQuestionAudio.exec(question.value)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexQuestionAudio, "");
                }
                while (regexQuestionAudio.exec(question.mask)) {
                    question.question_audio = _.trim(regexQuestionAudio.exec(question.mask)[1].replace(/&nbsp;/gi, '').replace(/%20/g, ''));
                    question.mask = question.mask.replace(regexQuestionAudio, "");
                }
                if (regexHidden.exec(question.mask)) {
                    question.hidden = _.trim(regexHidden.exec(question.value)[1]);
                    question.mask = question.mask.replace(regexHidden, "____");
                }
                while (regexHidden.exec(question.mask)) {
                    question.hidden = _.trim(regexHidden.exec(question.mask)[1]);
                    question.mask = question.mask.replace(regexHidden, "____");
                }
                if (question.type === 11) {
                    question.blocks = question.mask.split(/\s+/);
                    question.answerBlocks = question.mask.split(/\s+/);
                    question.gapMap = [];
                    question.blocks.forEach(function (val, index) {
                        if (val === "____" || val === "__★__") {
                            question.gapMap.push(index);
                        }
                        if (val === "__★__") {
                            question.starIndex = index;
                        }
                    });
                }
                return question;
            });
        },
        checkQuestion: function (question) {
            var vm = this;
            var lessonId = vm.tasks[0].lesson_id;
            if (question.type === 10 && question.currentAnswer.id) {
                var data = {
                    qid: question.id,
                    aid: question.currentAnswer.id,
                    lid: lessonId
                };
                $.post(vm.url + '/api/quiz/check-answer-quiz', data, function (res) {
                    if (res.code == 200) {
                        question.answers = question.answers.map(function (answer) {
                            res.data.forEach(function (item) {
                                if (answer.id === item.id) {
                                    answer.grade = parseInt(item.grade);
                                }
                                if (parseInt(item.grade) > 0) {
                                    vm.currentQuestion.answer = item;
                                    question.answer = item;
                                }
                            });
                            return answer;
                        });
                        question.correct = (question.currentAnswer.id === question.answer.id) ? 1 : 0;
                        vm.saveResult(data);
                        // console.log(question);
                        question.answered = 1;
                        vm.setProcessCookie();
                    }
                });

            }
            if (question.type === 11 && !question.currentAnswers.includes(null)) {
                var data = {
                    qid: question.id,
                    as: question.currentAnswers,
                    lid: lessonId,
                    sid: question.starId
                };
                $.post(vm.url + '/api/quiz/check-answer-gap-fill', data, function (res) {
                    if (res.code === 200) {
                        question.currentAnswers.forEach(function (a, index) {
                            var block = question.blocks[question.gapMap[index]];
                            var blockClass = (a === res.data[index].id) ? "quiz__body--content-block-correct" : "quiz__body--content-block-wrong";
                            question.blocks[question.gapMap[index]] = `<span class="quiz__body--content-block ${blockClass}">${block}</span>`;
                        });
                        question.correct = 1;
                        res.data.forEach(function (item, index) {
                            question.answerBlocks[question.gapMap[index]] = item.value;
                            if (item.id !== question.currentAnswers[index]) {
                                question.correct = 0;
                            }
                        });
                        vm.saveResult(data);
                        question.answered = 1;
                        vm.setProcessCookie();
                    }
                });
            }
        },

        saveResult: function(payload) {
            var vm = this;

            var rid = undefined;
            vm.questions.forEach(function (q) {
                if (!_.isNil(q.resultId)) {
                    rid = q.resultId;
                }
            });
            var data = {
                ...payload,
                rid: rid
            };
            $.post(vm.url + '/api/quiz/save-result', data, function (res) {
                vm.questions = vm.questions.map(function (question) {
                    question.resultId = res.data;
                    return question;
                });
                vm.setProcessCookie();
            });
        },

        setAnswer: function (question, answer) {
            var vm = this;

            switch (question.type) {
                case 10:
                    vm.questions = vm.questions.map(function (q) {
                        if (question.id === q.id) {
                            q.currentAnswer = answer;
                            q.currentAnswer.grade = parseInt(answer.grade);
                        }
                        return q;
                    });
                    break;
                case 11:
                    vm.questions = vm.questions.map(function (q) {
                        if (question.id === q.id) {
                            var idx = 0;
                            if (q.currentAnswers.indexOf(answer.id) > -1) {
                                var answerIdx = q.currentAnswers.indexOf(answer.id);
                                q.currentAnswers[answerIdx] = null;
                                idx = answerIdx;
                                vm.fillTheGap(q, idx, answer);
                            } else {
                                var nullIdx = q.currentAnswers.indexOf(null);
                                q.currentAnswers[nullIdx] = answer.id;
                                idx = nullIdx;
                                vm.fillTheGap(q, idx, answer);
                            }

                        }
                        return q;
                    });
                    break;
                default:
                    vm.questions = vm.questions.map(function (q) {
                        if (question.id === q.id) {
                            q.currentAnswer = answer;
                            q.currentAnswer.grade = parseInt(answer.grade);
                        }
                        return q;
                    });
            }
            vm.checkQuestion(question)
        },

        fillTheGap: function (question, index, answer) {
            if (question.blocks[question.gapMap[index]] === "__★__"){
                question.starId = answer.id;
            }
            if (question.blocks[question.gapMap[index]] === "____" || question.blocks[question.gapMap[index]] === "__★__"){
                question.blocks[question.gapMap[index]] = ` ${answer.value} `;
            } else {
                if (question.gapMap[index] === question.starIndex) {
                    question.blocks[question.gapMap[index]] = "__★__";
                } else {
                    question.blocks[question.gapMap[index]] = "____";
                }
            }

        },

        setAutoplay: function(autoplay) {
            var vm = this;

            if(!autoplay) {
                vm.pauseAudio();
            }
            var settings = {
                autoplay: autoplay
            };
            vm.autoplay = autoplay;
            localStorage.setItem("quiz_settings_"  + vm.userId, JSON.stringify(settings));
        },

        playAudio: function (audio, param) {
            // console.log("click play audio", audio);
            var vm = this;

            vm.playingQuestionAudio = false;
            vm.playingAnswerAudio = false;
            vm[param] = true;
            // if(vm.mp3 == null) vm.mp3 = new Audio(vm.url + '/cdn/audio/' + audio);
            if(vm.mp3 == null) vm.mp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
            else{
                vm.mp3.pause();
                vm.mp3.currentTime = 0;
                vm.mp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
                // vm.mp3 = new Audio(vm.url + '/cdn/audio/' + audio);
            }
            vm.mp3.play();
            vm.playingMp3 = true;
        },

        // pause audio trong flashcards
        pauseAudio: function () {
            var vm = this;
            vm.mp3.pause();
            vm.mp3.currentTime = 0;
            vm.playingMp3 = false;
        },

        clearCookie: function () {
            var vm = this;
            var setCookieQuestions = [];
            localStorage.removeItem("quiz_process_" +  + vm.userId + '_' + vm.tasks[0].lesson_id);
        },

        showResult: function () {
            var vm = this;
            var passed = [];
            var failed = [];

            vm.clearCookie();
            vm.getResults();
            vm.saveExamProgress();

            vm.viewHistory = 0;
            var resultText = '';
            passed = vm.questions.filter(function (question) {
                return question.correct === 1;
            });
            failed = vm.questions.filter(function (question) {
                return question.correct === 0;
            });
            var sentences = {
                low: [
                    "Mọi sai lầm đều phải trả giá. Nếu vấp ngã phải tự đứng lên. Lục lọi lại kiến thức bài học để sẵn sàng thử sức một lần nữa bạn nhé!",
                    "Phía sau một lần thành công là cả ngàn thất bại. Xem lại bài học một lượt để tự tin khi làm bài hơn bạn nha!!"
                ],
                medium: [
                    "Bạn đã nắm được kha khá kiến thức rồi đó! Tuy nhiên cần phải nỗ lực hơn nữa. Xem lại bài học một lượt để xem bạn có bỏ lỡ điều gì không nhé!",
                    "Kiến thức là kết quả của sự vận dụng và lặp lại liên tục. Mỗi lần xem lại bài học bạn sẽ khám phá ra nhiều thứ đó!"
                ],
                high: [
                    "Tiếc quá! Một chút nữa thôi là bạn đã đạt điểm tuyệt đối rồi! Hít một hơi thật sâu và thử thêm một lần nữa nhé?",
                    "Kiến thức của bạn rất tuyệt vời! Bạn đã sẵn sàng chinh phục đỉnh cao vào lần sau chưa?",
                ],
                done: [
                    "Chúc mừng! Chúc mừng! Hãy tự thưởng cho bản thân và sẵn sàng cho những kiến thức mới nào!",
                    "Bạn đã có một bước tiến rất dài đó! Liệu bạn có khao khát khám phá thêm kiến thức cùng Dũng Mori không nào?",
                ]
            };
            var process = passed.length/vm.questions.length;
            switch (true) {
                case process < 0.5:
                    resultText = sentences.low[_.random(0, sentences.low.length - 1)];
                    break;
                case 0.5 <= process && process < 0.9:
                    resultText = sentences.medium[_.random(0, sentences.medium.length - 1)];
                    break;
                case 0.9 <= process && process < 1:
                    resultText = sentences.high[_.random(0, sentences.high.length - 1)];
                    break;
                case process == 1:
                    resultText = sentences.done[_.random(0, sentences.done.length - 1)];
                    break;
                default:
                    resultText = "Chúc mừng bạn đã hoàn thành bài test!";
            }

            vm.result = {
                passed: passed,
                failed: failed,
                resultText: resultText
            };
            vm.state = 'finished';
        },

        reviewAnswer: function () {
            var vm = this;

            vm.state = 'testing';
            vm.current = 0;
        },
        viewExplain: function () {
            var vm = this;

            vm.state
        },
        testAgain: function () {
            var vm = this;

            vm.clearCookie();
            vm.getQuestions();
        },

        end: function () {
            vm  = this;

            vm.state = 'end';
        },

        nextWhenEnter: function () {
            var vm = this;
            window.addEventListener('keyup', function(event) {
                if (event.key == 'Enter') {
                    vm.$refs.navBtn[0].click();
                }
            });
        },

        saveExamProgress: function () {
            var passed = this.questions.filter(function (question) {
                return question.correct === 1;
            });

            var saveProgressData = {
                id: this.lesson.id,
                current_video_progress: 100,
                current_exam_progress: 100,
                passed: passed.length
            };
            $.post(window.location.origin +"/api/lesson/progress/save", saveProgressData)
        },

        closeOldResult: function () {
            var vm = this;
            setTimeout(function () {
                vm.showOldResult = false;
            }, 100);
        },
        closeExplain: function () {
            var vm = this;
            setTimeout(function () {
                vm.showExplain = false;
            }, 100);
        },
        deleteResult: function (result) {
            var vm = this;


            Swal.fire({
                title: 'Xác nhận xoá kết quả',
                text: "Kết quả sẽ bị xoá và không thể khôi phục lại",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Xoá',
                cancelButtonText: 'Huỷ'
            }).then((kq) => {
                if (kq.value) {
                    var data = {
                        resultId: result.id
                    };
                    $.post(vm.url + "/api/quiz/delete-result", data, function (res) {
                        if (res.code === 200) {
                            vm.results = vm.results.filter(function (r) {
                                return r.id !== result.id;
                            });
                            Swal.fire(
                                'Đã xong!',
                                'Kết quả đã được xoá',
                                'success'
                            );
                        }
                    });
                }
            });


        }
    },
});
