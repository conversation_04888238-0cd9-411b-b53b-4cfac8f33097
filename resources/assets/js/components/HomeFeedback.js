Vue.component('home-feedback', {

    template: '#home-feedback-template',

    props: ['feedbacks', 'users'],

    data: function () {
        return {
            url: window.location.origin,
            listFeedbacks: this.initFeedbacks()
        };
    },

    methods: {

        //khởi tạo lại giá trị biến groups, gán lessons
        initFeedbacks: function(){

            var vm = this;
            var feedbacks = [];
            for (var i = 0; i < vm.feedbacks.length; i++) {
                var userId = vm.feedbacks[i].user_id;
                vm.feedbacks[i].user = vm.findUser(userId);
                feedbacks.push(vm.feedbacks[i]); 
            }
            return feedbacks;
        },

        //tìm kiếm user dựa trên id
        findUser: function(id){

            var vm = this;
            for (var i = 0; i < vm.users.length; i++){
                if (vm.users[i]['id'] == id){
                    return vm.users[i];
                }
            }
            return;
        }
    },

    mounted: function () {

        var vm = this;
        
        // console.log("listFeedbacks", vm.listFeedbacks);
        // console.log("users", vm.users);
    }

})
