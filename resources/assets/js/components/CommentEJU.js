$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

//tạo hàm truyền tham trị
function copyUserEJU(){

  var newUserEju = {
    id: 0,
    avatar: null,
    name: "<PERSON><PERSON>",
    email: "yoshi<PERSON><EMAIL>",
    username: null,
    birday: null,
    phone: "************",
    nihongo: "N1",
    address: "19 Nguyễn Trãi <PERSON>, HN",
    country: "Việt Nam"
  };

  return newUserEju;

}

//lấy ra giá trị params của url
function getParramValue(variable){
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
           var pair = vars[i].split("=");
           if(pair[0] == variable){return pair[1];}
    }
    return(false);
}

//hàm merge cmt và replies
function mergeCmt(cmt, replies){

    var countReplies = replies.length;
    for (var i = 0; i < cmt.length; i++) {

        //khởi tạo danh sách reply rỗng cho mỗi cmt
        var tmpReplies = [];
        var cmtid = cmt[i].id;
        for (var n = 0; n < countReplies; n++){
            if (replies[n]['parent_id'] == cmtid)
                tmpReplies.push(replies[n]);
        }
        cmt[i].replies = tmpReplies;
    }
    return cmt;
}

//components hiển thị comments
Vue.component('comments-eju', {

    template: '#comment-template-eju',

    props: ['meid', 'avatar', 'tbid', 'tbname', 'numPosts', 'background'],

    data: function () {
        return {

            url: window.location.origin, //đường dẫn host
            listComments: [],    //sanh sách các comments
            page: 1,             //trang thứ mấy
            numPost: 15,
            ref: null, //nguồn chuyển hướng (notice hoặc 0)

            showLoading: false,  //trạng thái hiển thị button tải thêm
            theEnd: false,       //thông báo hết danh sách

            showLoadingUser: false,  //trạng thái tải thông tin người dùng
            currentUser: copyUserEJU(), //preview profile người dùng

            adminActive: enableFIV, //kiểm tra có phiên làm việc của admin hay không true\false
            adminCurrentEditId: null,
            adminCurrentEditContent: null,

            showLoadingNewComment: false,

            previewImg: null, //đường dẫn xem trước ảnh sẽ up
        };
    },

    methods: {

        //in ra thông tin email dạng nửa kín nửa hở
        printPrivateEmail: function(email){

          // console.log("Biến permission", enableFIV);

          if(email != "<EMAIL>" && email != "<EMAIL>"){

            //nếu biến cho phép hiển thị = true
            if(enableFIV && enableFIV == true) return email;
            else return "****"+ email.slice(4);
          }
          else
            return "<EMAIL>";
        },

        //in ra thông tin mobile dạng nửa kín nửa hở
        printPrivatePhone: function(phone){

          if(phone != "0969.86.84.85"){

             //nếu biến cho phép hiển thị = true
            if(enableFIV && enableFIV == true) return phone;
            else return "*******"+ phone.slice(-5);
          }
          else
            return "0969.86.84.85";

        },

        //in ra thông tin có dấu cách
        printInfo: function(info){

            var result = _.escape(info);

          result = result.replace('<', '&#60;');
          result = result.replace('>', '&#62;');

          //xử lý xuống dòng
          result =  info.replace(new RegExp('\r?\n','g'), '<br />');

          var re = /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/ig;
          return result.replace(re, function(match, lParens, url) {
              var rParens = '';
              lParens = lParens || '';

              // Try to strip the same number of right parens from url
              // as there are left parens.  Here, lParenCounter must be
              // a RegExp object.  You cannot use a literal
              //     while (/\(/g.exec(lParens)) { ... }
              // because an object is needed to store the lastIndex state.
              var lParenCounter = /\(/g;
              while (lParenCounter.exec(lParens)) {
                  var m;
                  // We want m[1] to be greedy, unless a period precedes the
                  // right parenthesis.  These tests cannot be simplified as
                  //     /(.*)(\.?\).*)/.exec(url)
                  // because if (.*) is greedy then \.? never gets a chance.
                  if (m = /(.*)(\.\).*)/.exec(url) ||
                          /(.*)(\).*)/.exec(url)) {
                      url = m[1];
                      rParens = m[2] + rParens;
                  }
              }
              return lParens + "<a href='" + url + "' target='_blank'>" + url + "</a>" + rParens;
          });
        },

        //tải về các comments cho lần tải đầu tiên
        fetchlistComments: function() {

          var vm = this;

          //focus vào comment được đánh dấu
          if(getParramValue("ref") != null) vm.ref = "notice";

          const data = {
            id: vm.tbid,
            name: vm.tbname,
            numpost: vm.numPost,
            ref: vm.ref
          };
          $.post(window.location.origin +"/api/comments/comments-load-first", data, function(response, status){

            //gắn replies vào cmt cha
            vm.listComments = mergeCmt(response.comments, response.replies);

            // console.log("lần đầu load", vm.listComments);

            //nếu đã hết danh sách
            if(response.comments.length < vm.numPost) vm.theEnd = true;

            //ẩn biểu tượng loading
            vm.showLoading = false;

            //bật autosize
            setTimeout(function(){
              autosize(document.querySelectorAll('textarea'));
            }, 100);

            //thêm emoji
            setTimeout(function(){new EmojiPicker(); }, 100);

            // //focus vào comment được đánh dấu
            // var focus = getParramValue("focus");
            // if(focus != null){
            //   console.log("focus: "+ focus);
            //   setTimeout(function(){

            //     $("#answer-reply-"+ focus).click();

            //     //cuộn trang tới vị trí dc focus
            //     $('html, body').animate({
            //         scrollTop: $("#answer-reply-"+ focus).offset().top - 120;
            //     }, 200);

            //   }, 500);
            // }



          });
        },

        //tải các phản hồi
        fetchMoreComments: function(){

          var vm = this;

          //hiện biểu tượng loading
          vm.showLoading = true;

          setTimeout(function(){
            data = {
              id  : vm.tbid,
              name: vm.tbname,
              numpost: vm.numPost,
              page: vm.page++
            };
            //console.log(data);
            $.post(window.location.origin +"/api/comments/comments-load-more", data, function(response, status){

              var tmp = mergeCmt(response.comments, response.replies);
              console.log("lần sau", tmp);

              //nối thêm mảng tải thêm
              vm.listComments = vm.listComments.concat(tmp);

              //nếu đã hết danh sách
              if(response.comments.length < vm.numPost) vm.theEnd = true;

              //ẩn biểu tượng loading
              vm.showLoading = false;

              //bật autosize
              setTimeout(function(){
                autosize(document.querySelectorAll('textarea'));
              }, 100);


              //thêm emoji
              setTimeout(function(){new EmojiPicker(); }, 100);

            });

          }, 500);

          //console.log('tải thêm các bình luận');

        },

        //tải thông tin user
        fetchUserInfo: function(id){

          console.log('preview thông tin người dùng '+ id);
          var vm = this;
          vm.showLoadingUser = false;

          //console.log(defaultUser);
          setTimeout(function(){
            if(id == 0) {

                vm.currentUser = copyUserEJU();
                console.log(vm.currentUser);
                vm.showLoadingUser = true;
            }
            else{
              $.post(window.location.origin +"/api/profile/get-profile-by-id", {id: id}, function(response, status){

                console.log(response);
                vm.currentUser.id     = response.id;
                vm.currentUser.avatar = response.avatar;
                vm.currentUser.name   = response.name;
                vm.currentUser.email  = response.email;
                vm.currentUser.username = response.username;
                vm.currentUser.birday   = response.birday;
                vm.currentUser.phone    = response.phone;
                vm.currentUser.nihongo  = response.japanese_level;
                vm.currentUser.address  = response.address;
                vm.currentUser.country  = "Việt Nam";

                vm.showLoadingUser = true;

              });
            }
          }, 600);
        },

        //in ra định dạng ngày giờ đẹp
        prettyDate: function(t){

            var d = new Date(t);
            // return d.getDate() + "/" +(d.getMonth()+1)+ "/" + d.getFullYear() + " " +d.getHours() + ":" + d.getMinutes();
            return d.toLocaleDateString('en-GB', { timeZone: 'Asia/Ho_Chi_Minh', day:"2-digit", month:"2-digit", year:"numeric"}) +" "+ t.substring(11,16);
        },

        //preview ảnh đính kèm khi đăng bình luận mới
        previewImage: function(event) {
            var input = event.target;
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $("#preview-image-cmt").html('<img src="' + e.target.result + '"/>');
                };
                reader.readAsDataURL(input.files[0]);
            }
        },

        //preview ảnh đính kèm khi đăng reply mới
        previewImageReply: function(event) {

            // console.log("event: ", event.target.id);
            // commentImagePicked64421

            //lấy ra comment id từ event
            var cmtId = event.target.id.split("commentImagePicked")[1];
            // console.log("cmtid: ", cmtId);

            var input = event.target;
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $("#preview-image-cmt-"+ cmtId).html('<img src="'+e.target.result+'"/>');
                };
                reader.readAsDataURL(input.files[0]);
            }
        },

        //đăng bình luận mới
        postNewComment: function(){

            var vm = this;

            // bỏ qua comment rỗng
            if ($("#comment-content").val() == null || $("#comment-content").val() == undefined || $("#comment-content").val().trim() == "") {

                alert("Vui lòng nhập nội dung");
                return;
            }

            var file_data = $('#commentImagePicked').prop('files')[0];
            var form_data = new FormData();
            form_data.append('tbid', vm.tbid);
            form_data.append('tbname', vm.tbname);
            form_data.append('content', $("#comment-content").val());
            form_data.append('file', file_data);

            vm.showLoadingNewComment = true;

            setTimeout(function(){
                $.ajax({
                    url: window.location.origin +"/api/comments/add-new-comment",
                    type: "POST",
                    data: form_data,
                    contentType: false,
                    cache: false,
                    processData:false,
                    success: function(response){

                        console.log(response);
                        if(response == 'imagesize') alert("ảnh vượt quá dung lượng cho phép");
                        else if (response == 'type') alert("định dạng ảnh không cho phép");
                        else{

                            response.replies = [];
                            vm.listComments.unshift(response);
                            $("#comment-content").val('');
                            $("#comment-content").css('height', '42px');
                            $("#preview-image-cmt").html(''); //xóa ảnh preview
                            $('#commentImagePicked').val(''); //reset lại nút chọn ảnh
                        }
                        vm.showLoadingNewComment = false;
                    }
                });
            }, 500);
        },

        //xóa comment theo id
        delComment: function(id){
          setTimeout(function(){
            $.post(window.location.origin +"/api/comments/delete-comment", {id: id}, function(response, status){
              if(response == "success"){
                $("#cmt-item-"+id).fadeOut();
              }else{
                alert("thao tác không hợp lệ");
              }
            });
          }, 500);
        },

        //đăng reply mới
        postNewAnswer: function(parent_id){

            var vm = this;

            // bỏ qua comment rỗng
            if ($("#reply-input-content-"+ parent_id).val() == null || $("#reply-input-content-"+ parent_id).val() == undefined || $("#reply-input-content-"+ parent_id).val().trim() == "") {
                alert("Vui lòng nhập nội dung");
                return;
            }

            var file_data = $('#commentImagePicked'+parent_id).prop('files')[0];
            var form_data = new FormData();
            form_data.append('tbid', vm.tbid);
            form_data.append('tbname', vm.tbname);
            form_data.append('parent_id', parent_id);
            form_data.append('content', $("#reply-input-content-"+ parent_id).val());
            form_data.append('file', file_data);

            setTimeout(function(){
                $.ajax({
                    url: window.location.origin +"/api/comments/add-new-reply",
                    type: "POST",
                    data: form_data,
                    contentType: false,
                    cache: false,
                    processData:false,
                    success: function(response){

                        console.log("reply mới ", response);
                        if(response == 'imagesize') alert("ảnh vượt quá dung lượng cho phép");
                        else if (response == 'type') alert("định dạng ảnh không cho phép");
                        else{

                            var indexOfComment = 0; //thứ tự của comment đang reply
                            for(var i=0; i<vm.listComments.length; i++) if(vm.listComments[i].id == parent_id)  indexOfComment = i;

                            //console.log(indexOfComment);
                            vm.listComments[indexOfComment].replies.push(response);
                            $("#reply-input-content-"+ parent_id).val('');
                            $("#reply-input-content-"+ parent_id).css('height', '42px');
                            $("#preview-image-cmt-"+ parent_id).html(''); //xóa ảnh preview
                            $('#commentImagePicked'+ parent_id).val(''); //reset lại nút chọn ảnh
                        }
                    }
                });
            }, 500);


              // setTimeout(function(){
              //   data = {
              //     tbid    : vm.tbid,
              //     tbname  : vm.tbname,
              //     parent_id : parent_id,
              //     content   : $("#reply-input-content-"+ parent_id).val()
              //   };
              //   $.post(window.location.origin +"/api/comments/add-new-reply", data, function(response, status){

              //     var indexOfComment = 0; //thứ tự của comment đang reply
              //     for(var i=0; i<vm.listComments.length; i++) if(vm.listComments[i].id == parent_id)  indexOfComment = i;

              //     //console.log(indexOfComment);
              //     vm.listComments[indexOfComment].replies.push(response);
              //     $("#reply-input-content-"+ parent_id).val('');

              //     // $("#comment-content").css('height', '42px');
              //     // vm.showLoadingNewComment = false;

              //   });
              // }, 500);
        },

        //xóa reply theo id
        delReply: function(id){
          setTimeout(function(){
            $.post(window.location.origin +"/api/comments/delete-comment", {id: id}, function(response, status){
              if(response == "success"){
                $("#reply-item-"+id).fadeOut();
              }else{
                alert("thao tác không hợp lệ");
              }
            });
          }, 500);
        },

        //hàm admin sửa cmt
        editAdminComment: function(id, content){
            // console.log("hiện sửa cmt", id);
            // console.log("hiện sửa cmt content", content);

            //gán giá trị cần sửa
            $("#edit-comment-area").text(content);
            $("#edit-comment-id").val(id);

        },

        //hàm lưu lại giá trị sửa
        saveAdminComment: function(){

            var vm = this;

            var content = $("#edit-comment-area").val();
            var id = $("#edit-comment-id").val();

            console.log("lưu giá trị id", id);
            console.log("lưu giá trị mới", content);

            $.post(window.location.origin +"/api/comments/edit-comment", {id: id, content: content}, function(response){
                if(response == "success"){
                    console.log("lưu thành công");

                    //cập nhật lại giá trị trên giao diện
                    $("#child-comment-content-"+ id).html(vm.printInfo(content));

                    $.fancybox.close();
                }else{
                    alert("lưu thất bại");
                }
            });
        },

        //cancel đóng popup nếu không muốn sửa cmt
        cancelAdminComment: function(){
            $.fancybox.close();
        },


    },

    mounted: function () {

        console.log('eju')
        //nếu là giao diện mobile chỉ load 4 items
        if(screen.width < 800) this.numPost = 4;

        this.fetchlistComments();

        // //khi cây DOM ảo đã load xong
        // this.$nextTick(function () {

        //     //bật autosize
        //     setTimeout(function(){
        //       autosize(document.querySelectorAll('textarea'));
        //     }, 500);
        // })
    }

})
