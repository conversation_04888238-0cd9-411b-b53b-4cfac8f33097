var delayTimer;
// var driver = window.driver.js.driver;

function replaceAscent(str){
  if (str === null || str === undefined) return str;
  str = str.toLowerCase();
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  return str;
};

var groupMenu = Vue.component('course-group-premium', {

    //template dc định nghĩa ở default
    template: '#course-group-premium',
    props: ['categories', 'groups', 'lessons', 'lprogress', 'courseurl', 'auth', 'ul', 'price', 'focus'],

    data: function () {
        return {

            url: window.location.origin,
            currentStage: 1, //chặng mặc định

            pickedCategory: null, //category đang được chọn
            listCategories: [], //danh sách category theo chặng được chọn
            listGroups: [], //danh sách group load theo chặng và category
            guideLessons: [], //danh sách bài học kiểu hướng dẫn

            totalPrecent: 0,
            progress: this.lprogress,

            key: '', //key tìm kiếm
            results: [], //mảng chứa kết quả tìm kiếm
            unlock: this.ul,
            countAllLessons: 0,
            showSearch: false
        };
    },

    methods: {

        //khởi tạo lại giá trị biến groups, gán lessons
        initData: function() {

            var vm = this;

            // console.log("focus", vm.focus);
            // console.log("progress", vm.progress);
            // console.log("all groups", vm.groups);

            if(vm.focus == null)
                vm.changeStage(1); //chọn chặng 1

            //vấn đề focus vào bài học
            else{
                vm.changeStage(vm.focus['stage']); //bấm vào chặng
                setTimeout(function(){
                    $("#ct-focus-"+ vm.focus['category']).click();
                    setTimeout(function(){
                        $("#click-group-"+ vm.focus['group']).click();
                        $(".lesson-item-"+ vm.focus['lesson']).addClass('active');
                    }, 10);
                }, 10);

            }

            //tạo bài cho hướng dẫn
            vm.initGuideList();

        },

        //tìm kiếm bài học theo tên
        searchLessons: function () {
            var vm = this;


            clearTimeout(delayTimer);
            delayTimer = setTimeout(function() {

                // console.log("Tìm kiếm: ", vm.key);

                if(vm.key.length == 0){

                    vm.results = [];
                } else if (vm.key.length > 0) {

                    vm.results = [];

                    for (var i = 0; i < vm.listGroups.length; i++) {
                        for (var n = 0; n < vm.listGroups[i].lessons.length; n++) {

                            var lowerName = replaceAscent(vm.listGroups[i].lessons[n].name.toLowerCase());
                            var lowerkey = replaceAscent(vm.key.toLowerCase());

                            if( (lowerName.indexOf(lowerkey) !== -1)) {
                                vm.listGroups[i].lessons[n].group_name = vm.listGroups[i].name;
                                vm.results.push(vm.listGroups[i].lessons[n]);
                            }
                        }
                    }

                    // console.log(vm.results);
                }


            }, 500);

        },

        //xóa từ khóa tìm kiếm
        clearSearch: function () {
            // var driverObj = driver();

            var vm = this;
            vm.key = "";
            vm.results = [];
            // driverObj.reset();

        },

        //tính % của tất cả group
        calcGroupProgress: function(){

            var vm = this;

            // console.log("focus", vm.focus);
            // console.log("progress", vm.progress);
            // console.log("all groups", vm.groups);



            //tính luôn % hoàn thành của tất cả group
            for (var i = 0; i < vm.groups.length; i++) {
                var sum = 0;
                for (var m = 0; m < vm.progress.length; m++) {
                    if(vm.groups[i].id == vm.progress[m].lesson.group_id){
                        sum += vm.progress[m].video_progress;
                        sum += vm.progress[m].example_progress;
                    }
                }

                vm.countAllLessons += vm.groups[i].get_all_lesson.length;
                vm.countAllLessons -= vm.guideLessons.length;
                //tránh trường hợp chia cho 0 NaN
                if(vm.groups[i].get_all_lesson.length > 0)
                    sum = Math.round(sum/vm.groups[i].get_all_lesson.length/2); //tính ra %

                vm.groups[i].progress = sum;

                if(vm.groups[i].progress > 100) vm.groups[i].progress = 100;

            }
            // console.log("all countAllLessons", vm.countAllLessons);
        },

        //chuyển chặng
        changeStage: function(stage){

            var vm = this;

            //thay đổi giá trị stage
            vm.currentStage = stage;

            //khởi tạo nhóm category theo stage mới
            var cts = [];
            for (var i = 0; i < vm.categories.length; i++)
                if(vm.categories[i].stage == stage) {
                    cts.push(vm.categories[i]);
                }
            vm.listCategories = cts;
            //chọn category được active theo index
            if(vm.currentStage == 1) vm.focusCategory(1);
            else vm.focusCategory(0);
            //thay đổi % tiến trình của các category
            if(vm.unlock && vm.auth) vm.updateCategoryProgress();

        },

        //chuyển category
        focusCategory: function(index){

            var vm = this;

            //chọn category được active
            vm.pickedCategory = vm.listCategories[index];

            //khởi tạo lại group progress
            if(vm.unlock && vm.auth) vm.calcGroupProgress();

            //khởi tạo danh sách groups mới theo category được active
            var currentGroups = [];
            for (var i = 0; i < vm.groups.length; i++) {
                //khởi tạo danh sách lessons cho mỗi group
                var lessons = [];
                var groupId = vm.groups[i].id;
                var countLesson = vm.lessons.length;
                for (var n = 0; n < countLesson; n++){
                    if (vm.lessons[n]['group_id'] == groupId)
                        lessons.push(vm.lessons[n]);
                }
                vm.groups[i].lessons = lessons;
                if(vm.groups[i].lesson_category_id == vm.pickedCategory.id){
                    currentGroups.push(vm.groups[i]); //thêm group vào mảng
                }
            }

            vm.listGroups = currentGroups;
            // console.log('danh sách ct', vm.listCategories);
            // console.log('ct được chọn ', vm.pickedCategory.title);
            // console.log("listGroups ", vm.listGroups);

            return;

        },

        initGuideList: function(){
            var vm = this;
            var categoryGuide = vm.categories[0];
            var groupGuide = null; //nhóm của các bài hướng dẫn
            for (var i = 0; i < vm.groups.length; i++){
                if(vm.groups[i].lesson_category_id == categoryGuide.id){
                    groupGuide = vm.groups[i];
                    break
                }
            }

            var countLesson = vm.lessons.length;
            for (var i = 0; i < countLesson; i++){
                if (vm.lessons[i]['group_id'] == groupGuide.id)
                    vm.guideLessons.push(vm.lessons[i]);
            }

            //focus bài học nếu bài học nằm trong guide list -> expand chỗ hướng dẫn
            if(vm.focus && vm.categories[0].id == vm.focus['category']){
                $(".guide-click").click();
            }

        },


        printContent: function(text){
            return text.replace(/\*new/gi, '<span class="new-group">mới</span>').replace(/\*free/gi, '<span class="new-group">free</span>');
        },

        printNameLesson: function(is_secret, price_option, name){
            var vm = this;
            // Nếu bài học đó có option ẩn tiêu đề và học viên chưa mua khoá học và không phải bài học thử
            if (price_option > 0 && !vm.unlock && is_secret == 1)
                return '<span style="opacity: .7">Nội dung bị ẩn</span>';
            return name;

        },

        //in ra trạng thái bài học của khóa học khác N5
        printLessonStatus: function(price_option){

            var vm = this;

            if (vm.unlock == 0){
                if (price_option == 0)
                    return "<span class='free'>Học thử</span>";
                else
                    return "<i class='fa fa-lock pull-right'></i>";
            }

            return;

        },

        displayCheckMark: function(lid) {

            var vm = this;

            if (vm.unlock == 0) {
                return "";
            }

            var progress = _.find(vm.progress, ['lesson_id', lid]);
            if (progress) {
                var check =  progress.video_progress + progress.example_progress;
                if (check >= 200) {
                    return "<i class='fa fa-check' style='color: #41A336;'></i>";
                }
                return "";
            }
            return "";
        },

        //in ra lộ trình chặng
        updateCategoryProgress: function(){

            var vm = this;

            // console.log('vm.listCategories', vm.listCategories);

            var html = "<h4>Chặng "+ vm.currentStage +"</h4>";

            //bỏ qua category hướng dẫn
            var x = 0;
            if(vm.currentStage == 1) x=1;

            // in ra % của các category
            for (var i = x; i < vm.listCategories.length; i++) {

                var sum = 0;
                var totalGroup = 0;

                for (var n = 0; n < vm.groups.length; n++) {
                    if(vm.listCategories[i].id == vm.groups[n].lesson_category_id){
                        sum += vm.groups[n].progress;
                        totalGroup++;
                    }
                }
                // console.log('stage '+ vm.listCategories[i].title + ':'+ sum/totalGroup);

                //tránh trường hợp chia cho 0 NaN
                if(totalGroup > 0)
                    sum =  Math.round(sum/totalGroup); //tính ra %


                html += '<div class="ct-progress-item"><p>'+vm.listCategories[i].title+'</p><div class="pg100"><div class="pgdata" style="width: '+sum+'%;"></div></div><div class="pgpercent">'+sum+'%</div></div>';
            }

            // console.log(html);
            $("#stage-bar").html(html);
        },

        totalProgressOfStage: function(stage){
            var vm = this;
            var tmpCT = [];

            // lấy ra các ct thuộc chặng
            for (var i = 0; i < vm.categories.length; i++)
                if(vm.categories[i].stage == stage) tmpCT.push(vm.categories[i]);

            var total = 0;

            // in ra % của các category
            for (var i = 0; i < tmpCT.length; i++) {

                var sum = 0;
                var countCtGroup = 0; //tổng group trong mỗi category

                for (var n = 0; n < vm.groups.length; n++) {
                    if(tmpCT[i].id == vm.groups[n].lesson_category_id){
                        sum += vm.groups[n].progress;
                        countCtGroup++;
                    }
                }

                //tránh trường hợp chia cho 0 NaN
                if(countCtGroup > 0){
                    sum =  Math.round(sum/countCtGroup); //tính ra %
                    total += sum;
                }
            }

            // console.log("stage:"+ stage + " ---%:"+ total + "length:"+ tmpCT.length);

            if(tmpCT.length == 0) return 0;

            return Math.round(total/tmpCT.length);
        },


        //tính tổng progress
        totalProgress: function(){

            var vm = this;

            var sum = 0;
            for (var n = 0; n < vm.progress.length; n++) {
                sum += vm.progress[n].video_progress;
                sum += vm.progress[n].example_progress;
            }

            vm.totalPrecent = Math.round(sum/vm.countAllLessons/2); //tính ra %
            // console.log("vm.progress.length", vm.progress.length);
            // console.log("vm.lessons.length", vm.lessons.length);

            // console.log("tính % tổng ~", vm.totalPrecent);
            $("#ldbar-total").attr("data-value", vm.totalPrecent);

        },
        getCategoryVideoCount: function (category) {
            var vm = this;
            var groups = vm.groups.filter(function (group) {
                return group.lesson_category_id == category.id;
            }).map(function (group) {
                var lessons = vm.lessons.filter(function (lesson) {
                   return lesson.group_id == group.id;
                });
                group.videoLessonCount = _.reduce(lessons, function (count, lesson) {
                    return count + (lesson.type == "video" ? 1 : 0);
                }, 0);
                return group;
            });
            return _.sumBy(groups, 'videoLessonCount');
        }
    },

    mounted: function () {
        var vm = this;

        vm.initData();

        //chỉ bật progress cho người dùng đã mua & đăng nhập
        if(vm.unlock && vm.auth) vm.totalProgress();

    }

})
