Vue.component('course-group', {

    //template dc định nghĩa ở default
    template: '#course-group-template',
    props: ['groups', 'lessons', 'courseurl', 'auth', 'ul', 'type', 'lessonprogress', 'price', 'ejulimit'],

    data: function () {
        return {
            url: window.location.origin,
            stateType: this.type,
            listGroups: this.initGroups()
        };
    },

    methods: {

        //khởi tạo lại giá trị biến groups, gán lessons
        initGroups: function(){

            var vm = this;
            for (var i = 0; i < vm.groups.length; i++) {

                //khởi tạo danh sách lessons rỗng cho mỗi group
                var lessons = [];
                var groupId = vm.groups[i].id;
                var countLesson = vm.lessons.length;

                for (var n = 0; n < countLesson; n++){
                    if (vm.lessons[n]['group_id'] == groupId) {
                        var newLesson = vm.lessons[n];
                        // Nếu bài học đó có option ẩn tiêu đề và học viên chưa mua khoá học và không phải bài học thử
                        if (vm.lessons[n].price_option > 0 && !vm.ul && vm.lessons[n].is_secret == 1) {
                            var prefixTitle = vm.lessons[n].name.split(':')[0];
                            newLesson.name = prefixTitle + ': Nội dung bị ẩn';
                        }
                        lessons.push(newLesson);
                    }
                }

                vm.groups[i].lessons = lessons;
            }

            return vm.groups;
        },

        printContent: function(text){
            return text.replace(/\*new/gi, '<span class="new-group">mới</span>').replace(/\*free/gi, '<span class="new-group">free</span>');
        },

        //in ra trạng thái bài học của khóa học khác N5
        printLessonStatus: function(price_option){

            var vm = this;
            if (price_option == 0) return "<span class='free'>Học thử</span>";
            else if (vm.ul == 0) return "<i class='fa fa-lock pull-right'></i>";
        },

        //in ra trạng thái của riêng N5
        printN5Status: function(feature){

            var vm = this;
            if (feature == 1) return "<span class='free'>Học thử</span>";
            else if(vm.auth == 1) return "<span class='free'>Miễn phí</span>";
            else return "<i class='zmdi zmdi-info-outline pull-right' title='yêu cầu đăng nhập'></i>";
        },
        // Tính toán tiến độ hoàn thành bài học
        // Làm tròn đến số thập phân thứ 2
        calcProgress: function () {
            var vm = this;
            var progressIds = _.map(vm.lessonprogress, 'lesson_id');
            vm.groups.map(function (group) {
                var groupProgress = 0;
                var lessons = _.get(group, 'lessons', []);
                if (!group.is_step) {
                    if (lessons.length > 0) {
                        lessons.forEach(function (lesson) {
                            if (progressIds.includes(lesson.id)) {
                                var progress = _.find(vm.lessonprogress, ['lesson_id', lesson.id]);
                                groupProgress += _.floor((progress.video_progress + progress.example_progress)/200, 2);
                            }
                        });
                        group.progress = _.floor(groupProgress/group.lessons.length, 2);
                    } else {
                        group.progress = 0;
                    }


                }
                return group;
            });
        },
        displayCheckMark: function(id) {
            var vm = this;

            var progress = _.find(vm.lessonprogress, ['lesson_id', id]);
            if (progress) {
                var lessonProgress = 0;
                lessonProgress += _.floor((progress.video_progress + progress.example_progress)/200, 2);
                if (lessonProgress == 1) {
                    return "<span style='display: block; width: 10px; height: 10px; border-radius: 100%; background: #00FF00; box-shadow: 0px 0px 10px 0px #0ff; margin-right: 10px;'></span>"
                } else {
                    return "<span style='display: block; width: 10px; height: 10px; border-radius: 100%; background: #eee; margin-right: 10px;'></span>"
                }
            } else {
                return "<span style='display: block; width: 10px; height: 10px; border-radius: 100%; background: #eee; margin-right: 10px;'></span>"
            }
            // if (lesson == 1) {
            //     return "<i class='fa fa-lock pull-right'></i>"
            // }
        },
        // Hiển thị progress bar
        displayProgress: function () {
            var vm = this;
            var totalProgress =_.sumBy(vm.groups, function (obj) {
                return obj.progress
            })/vm.groups.length;

            // Hiển thị học viên đã có học bài học
            if (_.floor(totalProgress, 3) != 0 && _.floor(totalProgress, 2) == 0) totalProgress = 0.01;
            else totalProgress = _.floor(totalProgress, 2);

            // Test hiển thị của thanh tiến độ
            // totalProgress = 0.12;

            // Tiến trình tổng, dạng đường thẳng
            var bar = new ProgressBar.Line('#lesson__progress--bar', {
                strokeWidth: 4,
                easing: 'bounce',
                duration: 1400,
                color: '#00A338',
                trailColor: 'rgb(239, 255, 211)',
                trailWidth: 4,
                marginBottom: 0,
                text: {
                    style: {
                        // Text color.
                        // Default: same as stroke color (options.color)
                        color: '#FFF',
                        // position: 'absolute',
                        // right: '0',
                        // top: '0',
                        // padding: 0,
                        // margin: 0,
                        // transform: null
                    },
                    autoStyleContainer: true
                },
                from: {color: '#FFF'},
                to: {color: '#2b8202'},
                step: function (state, bar) {
                    bar.setText(Math.round(bar.value() * 100) + '%');
                }
            });
            bar.animate(totalProgress);  // Value from 0.0 to 1.0
            // Tiến trình của từng bài học, dạng đường tròn
            vm.groups.forEach(function(group, index) {
                if (!group.is_step) {
                    var circleColor = '#EEE';
                    if (group.progress > 0 && group.progress < 0.25) {
                        circleColor = '#FF4500';
                    }
                    if (group.progress >= 0.25 && group.progress < 0.5) {
                        circleColor = '#FFD700';
                    }
                    if (group.progress >= 0.5 && group.progress < 0.75) {
                        circleColor = '#1E90FF';
                    }
                    if (group.progress >= 0.75) {
                        circleColor = '#ADFF2F';
                    }
                    var circle = new ProgressBar.Circle('#lesson__progress--circle-' + group.id, {
                        color: '#124896',
                        // This has to be the same size as the maximum width to
                        // prevent clipping
                        strokeWidth: 8,
                        trailColor: '#4F92E4',
                        trailWidth: 4,
                        easing: 'bounce',
                        duration: 1400,
                        text: {
                            autoStyleContainer: false,
                        },
                        from: { color: '#124896', width: 8 },
                        to: { color: '#124896', width: 8 },
                        // Set default step function for all animate calls
                        step: function(state, circle) {
                            circle.path.setAttribute('stroke', state.color);
                            circle.path.setAttribute('stroke-width', state.width);

                            var value = Math.round(circle.value() * 100);
                            circle.setText(value + '%');
                        }
                    });
                    circle.text.style.fontSize = '10px';
                    circle.text.style.color = '#124896';
                    circle.set(group.progress);
                }
            })
        },
    },

    mounted: function () {
        var vm = this;
        if (!vm.stateType) {
            vm.stateType = "";
        }

        if((vm.ul || vm.price == 0) && vm.auth) {
            vm.calcProgress();
            vm.displayProgress();
        }
        // console.log("ejulimit", vm.ejulimit);
        // console.log("groups", vm.groups);
        // console.log("lessonprogress", vm.lessonprogress);
        // console.log("lessons", vm.lessons);
        // console.log("courseurl", vm.courseurl);
        // console.log("auth", vm.auth);
        // console.log("unlock", vm.ul);
    }

})
