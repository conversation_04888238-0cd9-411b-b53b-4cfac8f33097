Vue.component('course-group-ld', {

    //template dc định nghĩa ở default
    template: '#course-group-ld',
    props: ['groups', 'lessons', 'courseurl', 'auth', 'ul', 'type', 'lessonprogress', 'price', 'lessontab'],

    data: function () {
        return {
            url: window.location.origin,
            stateType: this.type,
            listDeKyNang: [],
            listDeTongHop: [],
            tab: 1,
            lessonDetail: {},
        };
    },

    created: function() {
        // this.$root.$refs.luyende = this;
        if (typeof lessonDetail != 'undefined') {
            this.lessonDetail = lessonDetail;
        }
    },

    methods: {

        //khởi tạo lại giá trị biến groups, gán lessons
        initGroups: function(){

            var vm = this;
            // console.log(vm.groups);

            //thêm lessons vào các groups
            for (var i = 0; i < vm.groups.length; i++) {

                //khởi tạo danh sách lessons rỗng cho mỗi group
                var lessons = [];
                var groupId = vm.groups[i].id;
                var countLesson = vm.lessons.length;

                for (var n = 0; n < countLesson; n++){
                    if (vm.lessons[n]['group_id'] == groupId) {
                        var newLesson = vm.lessons[n];
                        // Nếu bài học đó có option ẩn tiêu đề và học viên chưa mua khoá học và không phải bài học thử
                        if (vm.lessons[n].price_option > 0 && !vm.ul && vm.lessons[n].is_secret == 1) {
                            var prefixTitle = vm.lessons[n].name.split(':')[0];
                            newLesson.name = prefixTitle + ': Nội dung bị ẩn';
                        }
                        var ldknResult = _.find(ldResults, { lesson_id: vm.lessons[n].id });
                        newLesson.score = ldknResult ? ldknResult.grade : null;
                        lessons.push(newLesson);
                    }
                }

                vm.groups[i].lessons = lessons;

                if(vm.groups[i].type_ld == "ldkn"){
                    vm.listDeKyNang.push(vm.groups[i]);
                }
                if(vm.groups[i].type_ld == "ldth"){ //chỉ load luyện đề kỹ năng
                    vm.listDeTongHop = [];
                    vm.listDeTongHop = vm.groups[i].lessons;
                }
            }

            // console.log("vm.listDeKyNang", vm.listDeKyNang);
            // console.log("vm.listDeTongHop", vm.listDeTongHop);

        },

        changeTab: function(tab){

            var vm = this;
            vm.tab = tab;
            // console.log("switchTab component", vm.tab);
            vm.$forceUpdate(); //update lại giao diện khi gọi từ component ngoài
        },

        printContent: function(text){
            return text.replace(/\*new/gi, '<span class="new-group">mới</span>').replace(/\*free/gi, '<span class="new-group">free</span>');
        },

        //in ra trạng thái bài học của khóa học khác N5
        printLessonStatus: function(price_option){

            var vm = this;
            if (price_option == 0) return "<span class='free'>Học thử</span>";
            else if (vm.ul == 0) return "<i class='fa fa-lock pull-right'></i>";
        },

        //in ra trạng thái của riêng N5
        printN5Status: function(feature){

            var vm = this;
            if (feature == 1) return "<span class='free'>Học thử</span>";
            else if(vm.auth == 1) return "<span class='free'>Miễn phí</span>";
            else return "<i class='zmdi zmdi-info-outline pull-right' title='yêu cầu đăng nhập'></i>";
        },

        displayCheckMark: function(id) {
            var vm = this;

            var progress = _.find(vm.lessonprogress, ['lesson_id', id]);
            if (progress) {
                var lessonProgress = 0;
                lessonProgress += _.floor((progress.video_progress + progress.example_progress)/200, 2);
                if (lessonProgress == 1) {
                    return "<span style='display: block; width: 10px; height: 10px; border-radius: 100%; background: #00FF00; box-shadow: 0px 0px 10px 0px #0ff; margin-right: 10px;'></span>"
                } else {
                    return "<span style='display: block; width: 10px; height: 10px; border-radius: 100%; background: #eee; margin-right: 10px;'></span>"
                }
            } else {
                return "<span style='display: block; width: 10px; height: 10px; border-radius: 100%; background: #eee; margin-right: 10px;'></span>"
            }
            // if (lesson == 1) {
            //     return "<i class='fa fa-lock pull-right'></i>"
            // }
        },
    },

    mounted: function () {

        var vm = this;

        if (this.lessontab) {
            vm.tab = vm.lessontab;
            // console.log(vm.lessontab)
        }
        if (!vm.stateType) {
            vm.stateType = "";
        }

        if((vm.ul || vm.price == 0) && vm.auth) {
            // vm.calcProgress();
            // vm.displayProgress();
        }

        vm.initGroups();
    }

})
