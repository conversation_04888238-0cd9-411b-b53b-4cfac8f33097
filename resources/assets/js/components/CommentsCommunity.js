$.ajaxSetup({ headers: { token: token } });

//tạo hàm truyền tham trị
function copyUser() {
  var newUser = {
    id: 0,
    avatar: null,
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    username: null,
    birday: null,
    phone: "0969.86.84.85",
    nihongo: "N1",
    address: "19 Nguyễn Trãi Thanh Xuân Hà Nội",
    country: "Việt Nam",
  };

  return newUser;
}

//lấy ra giá trị params của url
function getParramValue(variable) {
  var query = window.location.search.substring(1);
  var vars = query.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    if (pair[0] == variable) {
      return pair[1];
    }
  }
  return false;
}

//components hiển thị comments
Vue.component("comments-community", {
  template: "#comment-community-template",

  props: ["meid", "avatar", "name", "postid", "numposts", "background"],

  data: function () {
    return {
      cdn: cdn,
      url: window.location.origin, //đường dẫn host
      listComments: [], //sanh sách các comments
      // page: 1,             //trang thứ mấy
      // numPost: 5,
      ref: null, //nguồn chuyển hướng (notice hoặc 0)

      showLoading: false, //trạng thái hiển thị button tải thêm
      theEnd: false, //thông báo hết danh sách

      showLoadingUser: false, //trạng thái tải thông tin người dùng
      currentUser: copyUser(), //preview profile người dùng

      adminActive: enableFIV, //kiểm tra có phiên làm việc của admin hay không true\false
      adminCurrentEditId: null,
      adminCurrentEditContent: null,

      showLoadingNewComment: false,

      previewImg: null, //đường dẫn xem trước ảnh sẽ up

      params: {
        postId: this.postid,
        page: 1,
        limit: 8,
      },
      commentImage: null,
    };
  },

  methods: {
    //in ra thông tin email dạng nửa kín nửa hở
    printPrivateEmail: function (email) {
      // console.log("Biến permission", enableFIV);

      if (email != "<EMAIL>" && email != "<EMAIL>") {
        //nếu biến cho phép hiển thị = true
        if (enableFIV && enableFIV == true) return email;
        else return "****" + email.slice(4);
      } else return "<EMAIL>";
    },

    //in ra thông tin mobile dạng nửa kín nửa hở
    printPrivatePhone: function (phone) {
      if (phone != "0969.86.84.85") {
        //nếu biến cho phép hiển thị = true
        if (enableFIV && enableFIV == true) return phone;
        else return "*******" + phone.slice(-5);
      } else return "0969.86.84.85";
    },

    //in ra thông tin có dấu cách
    printInfo: function (info) {
      var result = info;

      result = result.replace("<", "&#60;");
      result = result.replace(">", "&#62;");

      //xử lý xuống dòng
      result = info.replace(new RegExp("\r?\n", "g"), "<br />");

      var re =
        /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;
      return result.replace(re, function (match, lParens, url) {
        var rParens = "";
        lParens = lParens || "";

        // Try to strip the same number of right parens from url
        // as there are left parens.  Here, lParenCounter must be
        // a RegExp object.  You cannot use a literal
        //     while (/\(/g.exec(lParens)) { ... }
        // because an object is needed to store the lastIndex state.
        var lParenCounter = /\(/g;
        while (lParenCounter.exec(lParens)) {
          var m;
          // We want m[1] to be greedy, unless a period precedes the
          // right parenthesis.  These tests cannot be simplified as
          //     /(.*)(\.?\).*)/.exec(url)
          // because if (.*) is greedy then \.? never gets a chance.
          if ((m = /(.*)(\.\).*)/.exec(url) || /(.*)(\).*)/.exec(url))) {
            url = m[1];
            rParens = m[2] + rParens;
          }
        }
        return (
          lParens +
          "<a href='" +
          url +
          "' target='_blank'>" +
          url +
          "</a>" +
          rParens
        );
      });
    },

    //tải về các comments cho lần tải đầu tiên
    fetchlistComments: function () {
      var vm = this;

      $.get(
        api + "/api/community/comments/get-list",
        vm.params,
        function (res) {
          vm.listComments = res.comment;

          //lastid = id của bài cuối
          if (res.comment.length > 0)
            vm.params.lastId = res.comment[res.comment.length - 1].id;

          console.log("lần đầu load", vm.listComments);

          // //nếu đã hết danh sách
          if (res.comment.length < vm.params.limit) vm.theEnd = true;

          //ẩn biểu tượng loading
          vm.showLoading = false;

          //bật autosize
          setTimeout(function () {
            autosize(document.querySelectorAll("textarea"));
          }, 100);

          //thêm emoji
          setTimeout(function () {
            new EmojiPicker();
          }, 100);
        }
      );
    },

    //tải các phản hồi
    fetchMoreComments: function () {
      var vm = this;

      //hiện biểu tượng loading
      vm.showLoading = true;

      setTimeout(function () {
        vm.params.page++;

        $.get(
          api + "/api/community/comments/get-list",
          vm.params,
          function (res) {
            //nối thêm mảng tải thêm
            vm.listComments = vm.listComments.concat(res.comment);

            //lastid = id của bài cuối
            if (res.comment.length > 0)
              vm.params.lastId = res.comment[res.comment.length - 1].id;

            //nếu đã hết danh sách
            if (res.comment.length < vm.params.limit) vm.theEnd = true;

            //ẩn biểu tượng loading
            vm.showLoading = false;

            //bật autosize
            setTimeout(function () {
              autosize(document.querySelectorAll("textarea"));
            }, 100);

            //thêm emoji
            setTimeout(function () {
              new EmojiPicker();
            }, 100);
          }
        );
      }, 500);

      //console.log('tải thêm các bình luận');
    },

    likeCmt: function (cmtid) {
      var vm = this;

      //nếu chưa đăng nhập
      if (vm.meid == null) {
        $("#login-text-btn").click();
        return;
      }

      var i = _.findIndex(vm.listComments, { id: cmtid });
      var thiscmt = vm.listComments[i];

      setTimeout(function () {
        var data = {
          commentId: cmtid,
        };

        if (thiscmt.liked == false) {
          $.post(api + "/api/community/likes/like", data, function (res) {
            // console.log(res);
            if (res.comment_id == cmtid) {
              // console.log("liked", cmtid);
              thiscmt.liked = true;
              thiscmt.count_like++;
              vm.$forceUpdate();
            }
          });
        } else {
          $.post(api + "/api/community/likes/dislike", data, function (res) {
            // console.log(res);
            if (res.status == 1) {
              // console.log("dislike", cmtid);
              thiscmt.liked = false;
              thiscmt.count_like--;
              vm.$forceUpdate();
            }
          });
        }
      }, 100);

      // console.log("bấm like", vm.listComments[i].ulikes);
    },

    likeReply: function (cmtid, repid) {
      var vm = this;

      var i = _.findIndex(vm.listComments, { id: cmtid });
      var j = _.findIndex(vm.listComments[i].reply, { id: repid });

      var thisRep = vm.listComments[i].reply[j];

      console.log("thisRep", thisRep);

      setTimeout(function () {
        var data = {
          commentId: repid,
        };

        if (thisRep.liked == false) {
          $.post(api + "/api/community/likes/like", data, function (res) {
            // console.log(res);
            if (res.comment_id == repid) {
              // console.log("liked", cmtid);
              thisRep.liked = true;
              thisRep.count_like++;
              vm.$forceUpdate();
            }
          });
        } else {
          $.post(api + "/api/community/likes/dislike", data, function (res) {
            // console.log(res);
            if (res.status == 1) {
              // console.log("dislike", cmtid);
              thisRep.liked = false;
              thisRep.count_like--;
              vm.$forceUpdate();
            }
          });
        }
      }, 100);

      // console.log("bấm like", vm.listComments[i].ulikes);
    },

    //tải thông tin user
    fetchUserInfo: function (id) {
      console.log("preview thông tin người dùng " + id);
      var vm = this;
      vm.showLoadingUser = false;

      //console.log(defaultUser);
      setTimeout(function () {
        if (id == 0) {
          vm.currentUser = copyUser();
          console.log(vm.currentUser);
          vm.showLoadingUser = true;
        } else {
          // console.log(data);
          $.post(
            window.location.origin + "/api/profile/get-profile-by-id",
            { id: id },
            function (response, status) {
              console.log(response);
              vm.currentUser.id = response.id;
              vm.currentUser.avatar = response.avatar;
              vm.currentUser.name = response.name;
              vm.currentUser.email = response.email;
              vm.currentUser.username = response.username;
              vm.currentUser.birday = response.birday;
              vm.currentUser.phone = response.phone;
              vm.currentUser.nihongo = response.japanese_level;
              vm.currentUser.address = response.address;
              vm.currentUser.country = "Việt Nam";

              vm.showLoadingUser = true;
            }
          );
        }
      }, 600);
    },

    //in ra định dạng ngày giờ đẹp
    prettyDate: function (t) {
      const event = new Date(t);
      return (
        event.getHours() +
        ":" +
        event.getMinutes() +
        " " +
        event.toLocaleDateString("pt-PT")
      );
    },

    //preview ảnh đính kèm khi đăng bình luận mới
    previewImage: function (event) {
      var input = event.target;
      if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
          $("#preview-image-cmt").html('<img src="' + e.target.result + '"/>');
        };
        reader.readAsDataURL(input.files[0]);
      }
    },

    //preview ảnh đính kèm khi đăng reply mới
    previewImageReply: function (event) {
      // console.log("event: ", event.target.id);
      // commentImagePicked64421

      //lấy ra comment id từ event
      var cmtId = event.target.id.split("commentImagePicked")[1];
      // console.log("cmtid: ", cmtId);

      var input = event.target;
      if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
          $("#preview-image-cmt-" + cmtId).html(
            '<img src="' + e.target.result + '"/>'
          );
        };
        reader.readAsDataURL(input.files[0]);
      }
    },

    //đăng bình luận mới
    postNewComment: async function () {
      var vm = this;

      var postid = vm.postid;
      var content = $("#comment-content-" + postid).val();

      // bỏ qua comment rỗng
      if (content == null || content == "") {
        alert("Vui lòng nhập nội dung");
        return;
      } else {
        var file_data = $("#commentImagePicked").prop("files")[0];
        if (file_data) {
          var fileFormData = new FormData();
          fileFormData.append("object", "comment");
          fileFormData.append("image", file_data);
          await axios
            .post("/upload-image", fileFormData, {})
            .then((res) => {
              vm.commentImage = res.data;
            })
            .catch((err) => alert());
        }

        const formData = {
          postId: postid,
          comment: content,
          tagData: null,
          image: vm.commentImage,
        };

        // var file_data = $('#commentImagePicked').prop('files')[0];

        await axios
          .post(api + "/api/community/comments/comment", formData, {
            headers: {
              "Content-Type": "application/json",
              token: token,
            },
          })
          .then(function (response) {
            console.log("vm.listComments", vm.listComments);
            console.log(response);
            if (response.status == 200) {
              var newItem = response.data;
              newItem.avatar = vm.avatar;
              newItem.name = vm.name;
              newItem.countReply = 0;
              newItem.count_like = 0;
              newItem.liked = false;
              newItem.reply = [];
              newItem.created_at = new Date().toISOString();

              vm.listComments.unshift(response.data);
              // console.log("vm.listComments then", vm.listComments);

              //thay đổi đếm số cmt ỏ posts
              $("#count-cmt-of-" + vm.postid).html(vm.listComments.length);

              $("#comment-content-" + postid).val("");
              $("#comment-content-" + postid).css("height", "42px");
              $("#preview-image-cmt").html(""); //xóa ảnh preview
              $("#commentImagePicked").val(""); //reset lại nút chọn ảnh
              vm.commentImage = null;
              vm.$forceUpdate();
            }
          })
          .catch(function (error) {
            console.log(error);
          });
      }
    },

    //xóa comment theo id
    delComment: function (id) {
      var vm = this;

      setTimeout(function () {
        $.post(
          api + "/api/community/comments/delete",
          { commentId: id },
          function (response, status) {
            console.log("response", response);
            if (response.status == 1) {
              //ẩn hiển thị
              _.remove(vm.listComments, function (cmt) {
                return cmt.id === id;
              });

              // console.log("#count-cmt-of-"+ vm.postid);

              //thay đổi đếm số cmt ỏ posts
              $("#count-cmt-of-" + vm.postid).html(vm.listComments.length);

              vm.$forceUpdate();
            } else {
              alert("thao tác không hợp lệ");
            }
          }
        );
      }, 500);
    },

    //báo cáo cmt
    reportCmt: function (id) {
      $.post(
        api + "/api/community/reports/create",
        {
          commentId: id,
          contentId: 2,
          note: "nội dung không hợp lệ",
        },
        function (res) {
          if (res && res.id) $.notify("Đã report thành công");
          else $.notify("report lỗi");
        }
      );
    },

    //đăng reply mới
    postNewAnswer: async function (parent_id) {
      var vm = this;

      var rep = $("#reply-input-content-" + parent_id).val();
      // bỏ qua reply rỗng
      if (rep == null || rep == "") {
        alert("Vui lòng nhập nội dung");
        return;
      }
      var file_data = $("#commentImagePicked" + parent_id).prop("files")[0];

      if (file_data) {
        var fileFormData = new FormData();
        fileFormData.append("object", "comment");
        fileFormData.append("image", file_data);
        await axios
          .post("/upload-image", fileFormData, {})
          .then((res) => {
            vm.commentImage = res.data;
          })
          .catch((err) => alert());
      }

      const formData = {
        postId: vm.postid,
        parentId: parent_id,
        comment: rep,
        tagData: null,
        image: vm.commentImage,
      };

      // var file_data = $('#commentImagePicked').prop('files')[0];

      await axios
        .post(api + "/api/community/comments/comment", formData, {
          headers: {
            "Content-Type": "application/json",
            token: token,
          },
        })
        .then(function (response) {
          // console.log("reply thành công");
          // console.log("vm.listComments", vm.listComments);
          // console.log(response);
          if (response.status == 200) {
            var newItem = response.data;
            newItem.avatar = vm.avatar;
            newItem.name = vm.name;
            newItem.countReply = 0;
            newItem.count_like = 0;
            newItem.liked = false;
            newItem.reply = [];
            newItem.parent_id = parseInt(newItem.parent_id);
            newItem.created_at = new Date().toISOString();

            var replies = _.find(vm.listComments, { id: parent_id }).reply;
            replies.push(response.data);
            // replies.unshift(response.data);

            $(".input-comment").val("");
            $(".input-comment").css("height", "42px");
            $("#preview-image-cmt-" + parent_id).html(""); //xóa ảnh preview
            $("#commentImagePicked").val(""); //reset lại nút chọn ảnh

            console.log("thêm reply: ", newItem);
            vm.commentImage = null;
            $("#commentImagePicked" + parent_id).val("");
            vm.$forceUpdate();
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },

    //xóa reply theo id
    delReply: function (id, parentId) {
      console.log("xóa reply: ", parentId);
      var vm = this;

      setTimeout(function () {
        $.post(
          api + "/api/community/comments/delete",
          { commentId: id },
          function (response, status) {
            console.log("response", response);
            if (response.status == 1) {
              // $("#reply-item-"+id).fadeOut();
              var replies = _.find(vm.listComments, { id: parentId }).reply;
              _.remove(replies, { id: id });

              vm.$forceUpdate();
            } else {
              alert("thao tác không hợp lệ");
            }
          }
        );
      }, 500);
    },

    //hàm admin sửa cmt
    showEditCmt: function (cmt) {
      //gán giá trị cần sửa
      $("#edit-comment-id").val(cmt.id);
      $("#edit-comment-area").text(cmt.content);
    },
  },

  mounted: function () {
    //nếu là giao diện mobile chỉ load 4 items
    if (screen.width < 800) this.numPost = 4;

    this.fetchlistComments();
  },
});
