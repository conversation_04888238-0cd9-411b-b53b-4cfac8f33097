Vue.component("ht-select", {
  template: '#ht-template',
  props: {
    init_options: {
      required: true
    },
    init_selected: null,
    init_other_value: null,
  },
  watch: {
    selected: function(new_value, old_value) {
      if (new_value !== this.init_other_value) {
        this.$emit("change", new_value);
      }
    },
    other_value: function(new_value, old_value) {
      this.$emit("change", new_value);
    },
    init_selected:  function(new_value, old_value) {
      this.selected = new_value;
    }
  },
  data: function() {
    return {
      options: null,
      selected: null,
      other_value: null,
    };
  },
  methods: {
    getElement: function(value) {
      if (this.options == null) {
        return {text: ""}
      }
      for (var i = 0; i < this.options.length; i++) {
        if (this.options[i].value === value) return this.options[i];
      }
      return null;
    },
    isContainValueIn: function(value) {
      for (var i = 0; i < this.options.length; i++) {
        if (this.options[i].value === value) return true;
      }
      return false;
    }
  },
  mounted: function (){
    var vm = this;
    // Neu gia tri khoi tao khong nam trong danh sach cac key dua vao thi bat hien tai la
    // other va gan gia tri other do chinh la gia tri moi.
    this.options = JSON.parse(this.init_options);
    if(this.isContainValueIn(this.init_selected)) {
      this.selected = this.init_selected;
    } else {
      this.other_value = this.init_selected;
      this.selected = this.init_other_value;
    }
  }
})