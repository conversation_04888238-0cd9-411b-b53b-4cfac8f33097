@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON> thông tin truy cập của admin @stop
@section('keywords') log access @stop
@section('author') dungmori.com @stop
@section('title') Admin | Log Access @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script> 
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
@stop

@section('content') 
	<div class="row bg-title">
        <h4 class="page-title pull-left">
            Thông tin truy cập của admin
        </h4>
        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <select class="form-control mb-2 mr-sm-2" name="admin">
                <option value="0">Tất cả</option>
                @foreach($admin as $item)
                    <option value={{$item->id}}>{{$item->username}}</option>
                @endforeach
            </select>
            <input type="text" class="form-control mb-4 mr-sm-4" name="key" placeholder="Nhập url, ip, trình duyệt">
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_from" id="date_from" placeholder="Từ ngày">
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_to" id="date_to" placeholder="Đến ngày">
            <a type="button" class="search btn btn-info mb-2">Tìm kiếm</a>
            <a href={{url('backend/log')}} class="btn btn-info mb-2">Làm Lại</a>
        </form> 
    </div>
    <div class="log">  
        @include('backend.logAccess.logDetail')
    </div>
    
	<script>
    	$(document).ready(function() {
    	  	$.ajaxSetup({
    		    headers: {
    		      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    		    }
    	  	});	  
    	});
        //datetime picker
        $('#date_from').datepicker({ 
            dateFormat: 'dd/mm/yy'
        });
        $('#date_to').datepicker({ 
            dateFormat: 'dd/mm/yy'
        });

        $(document).on('click', '.search', function() {
            $.ajax({
                type: 'get',
                url: '/backend/log/find',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'admin': $('select[name=admin]').val(),
                    'key': $('input[name=key]').val(),
                    'date_to': $('input[name=date_to]').val(),
                    'date_from': $('input[name=date_from]').val(),
                },
                success: function(data) {
                    $('.log').empty().html(data);
                    $(document).on('click', '.log .pagination a', function(e) {
                        var date_from = $('#date_from').val();
                        var date_to = $('#date_to').val();
                        var admin = $('select[name=admin]').val();
                        var key = $('input[name=key]').val();
                        e.preventDefault();
                        var page = $(this).attr('href').split('page=')[1];
                        resultPage(page,'/backend/log/find/?page=', '.log', date_from, date_to, key, admin);
                    });
                }
            });
        });

        function resultPage(page,link, div_id, date_from, date_to, key, admin){
            $.ajax({
                type: 'get',
                url: link + page,
                cache: false,
                data: {
                    date_from : date_from,
                    date_to : date_to,
                    key : key,
                    admin : admin
                }
            }).done(function(data){
                $(div_id).empty().html(data)
            })
        }

    </script>
@stop