@extends('backend._default.dashboard')

@section('description') Quản lý booking @stop
@section('keywords') booking @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý booking @stop
@section('assets')
    <link rel="stylesheet" href="{{ asset('plugin/bootstrap-daterangepicker/daterangepicker.css') }}">
@endsection
@section('content')
    <div class="container table-booking-kaiwa" style="width: 100vw">
        <div class="row" style="margin-bottom: 20px">
            <div class="col-xs-12">
                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-5">
                                    <h3>Tạo lịch Kaiwa</h3>
                                    <input class="form-control input-daterange-datepicker form-inline" type="button" name="daterange" style="width: 250px; display: inline"/>
                                    <input type="button" value="Tạo lịch" class="btn btn-info" style="display: inline" v-on:click="createListDate">
                                </div>
                                <div class="col-xs-5">
                                    <h3>Kiểm tra những ngày đã tạo</h3>
                                    <select name="" v-model="currentYear" class="form-control" style="width: 100px; display: inline">
                                        <option v-for="year in years" v-bind:value="year">@{{ year }}</option>
                                    </select>

                                    <select name="" v-model="currentMonth" class="form-control" style="width: 70px; display: inline">
                                        <option v-for="month in months" v-bind:value="month">@{{ month }}</option>
                                    </select>

                                    <button class="btn btn-info" data-toggle="modal" data-target="#myModal" v-on:click="checkBooking">Kiểm tra</button>
                                    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                                    <h4 class="modal-title" id="myModalLabel">@{{ currentMonth }}/@{{ currentYear }}</h4>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="backend-booking-calendar">
                                                        <ul class="days">
                                                            <li v-for="item in listDays" v-if="item.booked == true"><span class="active">@{{ item.day }}</span></li>
                                                            <li v-else="">@{{ item.day }}</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-2">
                                    @include('backend.booking.topic')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12">
                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-5">
                                    <h4 class="booking-title">Quản lý lịch Kaiwa (@{{ date }})</h4>
                                </div>
                                <div class="col-xs-5">
                                    <button class="btn btn-success" style="float: right" v-on:click="getDate">Xem lịch</button>
                                    <input type="button" class="btn btn-default btn-cal-booking input-datepicker">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <table class="table table-striped">
                                        <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Thời gian</th>
                                            <th>Giáo viên</th>
                                            <th>Chủ đề</th>
                                            <th>Học viên 01</th>
                                            <th>Học viên 02</th>
                                            <th>Admin phụ trách</th>
                                            <th>Đầy ảo</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="(item, i) in listBooking" >
                                            <td>@{{ i+1 }}</td>
                                            <td width="110px">@{{ item.time }}</td>
                                            <td width="230px">
                                                <select name="" :key="item.id" v-model="listBooking[i].teacher" v-on:change="changeData(i)" class="form-control">
                                                    <option value="0">-------------</option>
                                                    @foreach($teachers as $teacher)
                                                        <option value="{{ $teacher->id }}" style="font-weight: bold">{{ $teacher->name }}</option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td width="260px">
                                                <select name="" :key="item.id" class="form-control" v-model="listBooking[i].topic_id" v-on:change="changeData(i)">
                                                    <option value="">-------------</option>
                                                    <option v-for="(topic, j) in topics" v-if="topic.status == 1" v-bind:value="topic.id">@{{ topic.name }}</option>
                                                </select>
                                            </td>
                                            <td>
                                                <a v-if="item.user_one != null">
                                                    <div style="float: left;">
                                                        <img v-if="item.user_one.avatar == null" v-bind:src="'<?= asset('assets/img/default-avatar.jpg') ?>'" width="36" alt="user-img" class="img-circle">
                                                        <img v-else v-bind:src="'<?= asset('cdn/avatar/small') ?>/' + item.user_one.avatar" width="36" alt="user" class="img-circle">
                                                    </div>
                                                    <div style="float: left; padding-left: 10px;">
                                                        <span class="hidden-xs">@{{ item.user_one.id }} • @{{ item.user_one.email }}</span><br/>
                                                        <span class="hidden-xs"><i class="fa fa-skype"></i> @{{ item.user_one.skype }}</span>
                                                    </div>
                                                    <div style="float: left; padding-left: 10px;">
                                                        <span style="cursor: pointer; color: #9f041b" @click="removeStudent(item.id, 'user_1')"><i class="fa fa-times"></i></span>
                                                    </div>
                                                </a>
                                                <a v-else >
                                                    <span style="cursor: pointer" class="text-info small" @click="setModalData(item.id, 'user_1')" data-toggle="modal" data-target="#manual-booking"><i class="fa fa-plus"></i>Thêm thủ công</span>
                                                </a>
                                            </td>
                                            <td>
                                                <a v-if="item.user_two != null">
                                                    <div style="float: left;">
                                                        <img v-if="item.user_two.avatar == null" v-bind:src="'<?= asset('assets/img/default-avatar.jpg') ?>'" width="36" alt="user-img" class="img-circle">
                                                        <img v-else v-bind:src="'<?= asset('cdn/avatar/small') ?>/' + item.user_two.avatar" width="36" alt="user" class="img-circle">
                                                    </div>
                                                    <div style="float: left; padding-left: 10px;">
                                                        <span class="hidden-xs">@{{ item.user_two.id }} • @{{ item.user_two.email }}</span><br/>
                                                        <span class="hidden-xs"><i class="fa fa-skype"></i> @{{ item.user_two.skype }}</span>
                                                    </div>
                                                    <div style="float: left; padding-left: 10px;">
                                                        <span style="cursor: pointer; color: #9f041b" @click="removeStudent(item.id, 'user_2')"><i class="fa fa-times"></i></span>
                                                    </div>
                                                </a>
                                                <a v-else >
                                                    <span style="cursor: pointer" class="text-info small" @click="setModalData(item.id, 'user_2')" data-toggle="modal" data-target="#manual-booking"><i class="fa fa-plus"></i>Thêm thủ công</span>
                                                </a>
                                            </td>
                                            <td>
                                                <div v-if="item.curators && item.curators.name" style="font-weight: bold; color: #cf5a00">@{{ item.curators.name || '--' }}</div>
                                                <span style="color: #0d95e8; font-size: 12px; cursor:pointer;" v-if="item.curators != 'you'" v-on:click="processBooking(item.id)"><i class="fa fa-user-plus"></i> Tiếp nhận</span>
                                                <span v-if="item.curators == 'you'" style="font-weight: bold; color: #9f041b">Bạn đang phụ trách</span>
                                            </td>
                                            <td>
                                                <label class="switch">
                                                    <input type="checkbox" v-model="listBooking[i].is_full" v-on:change="changeData(i)">
                                                    <span class="slider round"></span>
                                                </label>
                                            </td>
                                            <td>
                                                <label class="switch">
                                                    <input type="checkbox" v-model="listBooking[i].status" v-on:change="changeData(i)">
                                                    <span class="slider round"></span>
                                                </label>
                                            </td>
                                        </tr>
                                        </tbody>
                                        <!-- Modal -->
                                        <div class="modal fade" id="manual-booking" tabindex="-1" role="dialog" aria-labelledby="manual-booking" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-body">
                                                        <form>
                                                            <div class="form-group">
                                                                <label>ID User</label>
                                                                <input class="form-control" @blur="getUserInfo" v-model="currentObject.user_id">
                                                            </div>
                                                            <div class="form-group">
                                                                <label>Email</label>
                                                                <input class="form-control" disabled v-model="currentObject.email">
                                                            </div>
                                                            <div class="form-group">
                                                                <label>Skype</label>
                                                                <input class="form-control" v-model="currentObject.skype"/>
                                                            </div>
                                                        </form>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                        <button type="button" class="btn btn-primary" @click="saveBooking">Save changes</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection

@section('foot-js')
    <script src="{{ asset('plugin/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ asset('plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('plugin/bootstrap-daterangepicker/daterangepicker.js') }}"></script>
    <script src="{{ asset('plugin/notifyjs/notify.min.js') }}"></script>
    <script>
        var urlCreateBooking = "<?= route('booking.create') ?>";
        var toDay = "<?= date('m/d/Y') ?>";
        var urlGetList = "<?= route('booking.list') ?>";
        var urlUpdate = "<?= route('booking.update') ?>";
        var urlCheck = "<?= route('booking.check') ?>";
        var urlAddTopic = "<?= route('topic.add') ?>";
        var urlGetTopic = "<?= route('topic.get') ?>";
        var updateTopic = "<?= route('topic.edit') ?>";
        var urlDeleteTopic = "<?= route('topic.delete') ?>";
        var urlProcessBooking = "<?= route('booking.process') ?>";
    </script>
    <script src="{{ asset('assets/backend/js/booking-kaiwa.js') }}?{{filemtime('assets/backend/js/booking-kaiwa.js')}}"></script>
    <script>
        $('.input-datepicker').datepicker("setDate", new Date());
        $('.input-daterange-datepicker').daterangepicker({
            format: 'DD/MM/YYYY',
            buttonClasses: ['btn', 'btn-sm'],
            applyClass: 'btn-default',
            cancelClass: 'btn-white',
        });
    </script>
    <script>
        $('#manual-booking').on('hidden.bs.modal', function (e) {
            kaiwa.resetModalForm();
        })
    </script>
@endsection