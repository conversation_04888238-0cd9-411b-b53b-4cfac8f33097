<h3><PERSON><PERSON> đề</h3>
<button class="btn btn-info" data-toggle="modal" data-target="#topic" ><PERSON><PERSON><PERSON><PERSON> lý</button>
<div class="modal fade" id="topic" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Quản lý chủ đề</h4>
            </div>
            <div class="modal-body">
                <div class="topic-content">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-inline">
                                <div class="form-group">
                                    <label style="">Thêm mới chủ đề</label>
                                    <input type="text" class="form-control" placeholder="Tên chủ đề" v-model="topicName" v-on:keypress.enter="addTopic">
                                </div>
                                <button type="button" class="btn btn-success" v-on:click="addTopic">Lưu</button>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-xs-12">
                            <table class="table table-striped">
                                <thead>
                                <tr>
                                    <th>Tên chủ đề</th>
                                    <th>Trạng thái</th>
                                    <th>Hành động</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="(topic, i) in topics">
                                    <th >
                                        <span v-bind:class="'topic-name-'+topic.id">@{{ topic.name }}</span>
                                        <div v-bind:class="'form-inline edit-topic-name-'+topic.id" style="display: none">
                                            <div class="form-group">
                                                <input type="text" class="form-control" placeholder="Tên chủ đề" v-bind:value="topic.name" v-model="topics[i].name" v-on:keypress.enter="updateTopicName(topic, i)">
                                            </div>
                                            <button type="button" class="btn btn-success" v-on:click="updateTopicName(topic, i)">Lưu</button>
                                            <button type="button" class="btn btn-default" v-on:click="exitEditTopic(topic, i)">Hủy</button>
                                        </div>
                                    </th>
                                    <td>
                                        <label class="switch">
                                            <input type="checkbox" v-model="topics[i].status" v-on:change="updateTopicStatus(i)">
                                            <span class="slider round"></span>
                                        </label>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-danger round" v-on:click="deleteTopic(i)">Xóa</button>
                                        <button type="button" class="btn btn-info round" v-on:click="editTopicName(topic)">Sửa</button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>