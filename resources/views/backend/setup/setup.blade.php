@extends('backend._default.dashboard')

@section('description') setup @stop
@section('keywords') setup @stop
@section('author') dungmori.com @stop
@section('title') Admin | setup @stop

@section('assets')
@stop

@section('content') 
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Cài đặt
        </h4> 
       
    </div>
    <div class="table_server">
        <table class="table table-borderless" id="table_server">
        <thead>
            <tr>
                <th class="text-center">Tên</th>
                <th class="text-center">Hành động</th>
            </tr>
        </thead>
            <tbody>
            <tr>
                <td class="text-center">Bảng InvoiceOrder</td>
                <td class="text-center">
                    <button class="invoice btn btn-info">
                        Created
                    </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng LessonCourse</td>
                <td class="text-center">
                    <button class="c_created btn btn-info">
                        Created
                    </button>
                    <button class="c_expired btn btn-info">
                        Expired
                    </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng Combo</td>
                <td class="text-center">
                    <button class="com_created btn btn-info">
                        Created
                    </button>
                    <button class="com_expried_from btn btn-info">
                        Expired_from
                    </button>
                    <button class="com_expried_to btn btn-info">
                        Expired_to
                    </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng Lesson</td>
                <td class="text-center">
                    <button class="lesson btn btn-info">
                        Created
                     </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng Voucher</td>
                <td class="text-center">
                    <button class="v_created btn btn-info">
                        Created
                     </button>
                     <button class="v_expired btn btn-info">
                        Expired
                     </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng LesonOwner</td>
                <td class="text-center">
                    <button class="o_created btn btn-info">
                        Created
                    </button>
                    <button class="o_expired btn btn-info">
                        Expired
                    </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng Blog</td>
                <td class="text-center">
                    <button class="blog btn btn-info">
                        Created
                    </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng CategoryBlog</td>
                <td class="text-center">
                    <button class="category btn btn-info">
                        Created
                     </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng Page</td>
                <td class="text-center">
                    <button class="page btn btn-info">
                        Created
                     </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Bảng Comment</td>
                <td class="text-center">
                    <button class="comment btn btn-info">
                        Created
                     </button>
                </td>
            </tr>
            <tr>
                <td class="text-center">Convert IP</td>
                <td class="text-center">
                    <button class="ip btn btn-info">
                        Convert
                     </button>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    
    
<script>
    $(document).ready(function() {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });   
    });
    $(document).on('click', '.invoice', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/invoice',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.c_created', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/course-created',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.c_expired', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/course-expired',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.com_created', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/com-created',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.com_expried_from', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/com-expried-from',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.com_expried_to', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/com-expried-to',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.lesson', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/lesson',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.v_created', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/voucher-created',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.v_expired', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/voucher-expired',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.o_created', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/owner-created',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.o_expired', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/owner-expired',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.blog', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/blog',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.category', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/category',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.page', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/page',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.comment', function() {
       $.ajax({
            type: 'get',
            url: '/backend/setup/comment',
            success: function(data) {
                alert(data);
            }
        });
    });
    $(document).on('click', '.ip', function() {
        console.log('1');
        $.ajax({
            type: 'get',
            url: '/backend/setup/ip',
            success: function(data) {
                alert(data);
            }
        });
    });
  
</script>
@stop