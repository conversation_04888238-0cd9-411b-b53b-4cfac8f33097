@extends('backend._default.dashboard')

@section('title') Admin | Quản lý tài liệu @stop
@section('description') Quản lý tài liệu @stop
@section('keywords') document @stop
@section('author') dungmori.com @stop

@section('assets')
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script> 
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script> 
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet">  
    <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
@stop

@section('content')

<div id="feedback-manager" class="row bg-title-1">
    <h4 class="page-title pull-left document-manager-title">Quản lý tài liệu khóa học</h4><br><br><br>

	<div class="tab">
		<button class="tablinks" v-for="(course, index) in courses" :id="'btn' + course.id"  v-on:click="openTab(course.id)">@{{ course.name }}</button>
	</div>

  	<div v-for="(course, index) in courses" :id="course.id" class="tabcontent">
  		<h3><b>@{{ course.name }}</b>
  			@if(json_decode(Auth::guard('admin')->user()->matrix)->document->add != null)
  				<img class="add-icon" src="{{url('assets/img/icons8-add-50.png')}}" data-toggle="modal" data-target="#addOrEdit" v-on:click="startAddNewDocument()">
  			@endif
  		</h3>
  		<table class="table" :id="'table_course_' + course.id">
			<thead>
				<tr>
					<th>Id</th>
					<th>Tên</th>
					<th>File</th>
					<th>Trạng thái</th>
					<th>Hành động</th>
					<th>Index</th>
				</tr>
			</thead>
			<tbody :class="'table_course_' + course.id">
				<tr v-for="(document, index) in documents" v-if="document.course_id == course.id">
					<td>@{{ document.id }}</td>
					<td>@{{ document.name }}</td>
					<td>@{{ document.file_name }}</td>
					<td>
						<input type='checkbox' class='ios8-switch ios8-switch-lg' :id="'checkbox' + document.id" v-model="document.show"><label style="cursor: pointer;" :for="'checkbox' + document.id" v-on:click="setStatus(index, document.id)"></label>
					</td>
					<td>
						@if(json_decode(Auth::guard('admin')->user()->matrix)->document->edit != null)
						<button class="btn btn-info" data-toggle="modal" data-target="#addOrEdit" v-on:click="startEditDocument(index, document.id)">Sửa</button>
						@endif
						@if(json_decode(Auth::guard('admin')->user()->matrix)->document->delete != null)
	    				<button class="btn btn-danger" data-toggle="modal" data-target="#deletModal" v-on:click="startRemoveDocument(index, document.id)">Xóa</button>
	    				@endif
	    			</td>
	    			<td>@{{ document.order_index }}</td>
				</tr>
			</tbody>
	  	</table>
  	</div>

	<div class="modal fade" id="addOrEdit" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="title-model"></h4>
				</div>
				<div class="modal-body">
                    <form class="form-horizontal" role="form" id="document_form">
                    	<ul class="error-area">
                    		<li class="error-item" v-for="error in errors">@{{ error }}</li>
                    	</ul>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Tên</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="name" v-model="name">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">File</label>
                            <div class="col-sm-10">
                            	<span>@{{ file_name }}</span>
								<input type='file' id="file_upload" name="file_upload"/>
                            </div>
                        </div>
                        {{-- <div class="form-group">
                            <label class="control-label col-sm-2">Bật/tắt</label>
                            <div class="col-sm-10">
                                <input type='checkbox' class='ios8-switch ios8-switch-lg' id="checkbox" v-model="show"><label style="cursor: pointer;" for="checkbox">
                            </div>
                        </div> --}}
                    </form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="editDocument()" id="btn-action-model"></button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>

		</div>
	</div>

	<div id="deletModal" class="modal fade" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Xóa</h4>
			</div>
			<div class="modal-body">
				<p>Bạn có muốn xóa tài liệu này không?</p>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-success" v-on:click="removeDocument()">Xóa</button>
				<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
			</div>
		</div>

		</div>
	</div>

</div>

<script>

$('#table').DataTable();

$(document).ready(function() {
    $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });   
});

var user = new Vue({
	el: '#feedback-manager',

	data: function () {
        return {
        	url			: window.location.origin + "/cdn/avatar/default",
			documents	: {!! json_encode($documents) !!},
			courses		: {!! json_encode($courses) !!},
			name		: "",
			file_name	: "",
			show		: true,
			currentId	: -1,			// document id đang chọn
			currentIndex: -1,			// index của document đang chọn
			currentCourseId: -1,		// course id đang hiển thị
			errors		: []
		}
	},

	methods: {
		// mở popup xóa 1 document
		startRemoveDocument(index, id) {
			var vm = this;
			vm.currentId = id;
			vm.currentIndex = index;
		},

		// mở popup sửa 1 document
		startEditDocument(index, id) {
			var vm = this;
			vm.errors = [];
			vm.name = vm.documents[index].name;
			vm.file_name = vm.documents[index].file_name;
			vm.show = vm.documents[index].show;
			vm.currentId = id;
			vm.currentIndex = index;
			$("#title-model").text("Chỉnh sửa");
			$("#btn-action-model").text("Sửa");

			// xóa data input file
			var $el = $('#file_upload');
			$el.wrap('<form>').closest('form').get(0).reset();
			$el.unwrap();
		},

		// mở popup để thêm mới 1 document
		startAddNewDocument() {
			var vm = this;
			vm.errors = [];
			vm.name = "";
			vm.file_name = "";
			vm.currentId = -1;
			vm.currentIndex = -1;
			vm.show = true;
			$("#title-model").text("Thêm mới");
			$("#btn-action-model").text("Thêm");

			// xóa data input file
			var $el = $('#file_upload');
			$el.wrap('<form>').closest('form').get(0).reset();
			$el.unwrap();
		},

		// mở tab tương ứng
		openTab(id) {
		    var i, tabcontent, tablinks;
		    tabcontent = $(".tabcontent");
		    for (i = 0; i < tabcontent.length; i++) {
		        tabcontent[i].style.display = "none";
		    }
		    tablinks = $(".tablinks");
		    for (i = 0; i < tablinks.length; i++) {
		        tablinks[i].className = tablinks[i].className.replace(" active", "");
		    }
		    $("#" + id).css("display", "block");
		    $("#btn" + id).addClass("active");
		    var vm = this;
		    vm.currentCourseId = id;
		    $('#table_course_' + id).css('width', '100%');
		},

		setStatus(index, id) {
			var vm = this;

			var data = {
	            'id'		: id,
	            'show'		: vm.documents[index].show
			};
			$.ajax({
	            url: window.location.origin + "/backend/document/active", type:"POST", data: data, async: true,
	            error: function() {
	            	console.log("Có lỗi xảy ra");
	            },
	            success: function(response) {
	            	// console.log(response);
	            }
	        });
		},

		editDocument() {
			var vm = this;
			var dataDoc = new FormData($("#document_form")[0]);
			dataDoc.append('id', vm.currentId);
			dataDoc.append('course_id', vm.currentCourseId);
			dataDoc.append('show', vm.show);
			dataDoc.append('order_index', vm.getDocumentIndexMax(vm.currentCourseId) + 1);
	        $.ajax({
                type: 'post',
                url: '/backend/document/edit',
                processData: false,
                contentType: false,
                data : dataDoc,
                success: function(response) {
                    // console.log(response);
	            	if (response == "blank_data") {
	            		vm.errors = [];
	            		vm.errors.push("Dữ liệu còn trống");
	            	} else if (response.type == "new") {
	            		const newDocument = response.document;
		            	vm.documents.push(newDocument);
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
		            } else if (response == "invalid_file") {
		            	vm.errors = [];
	            		vm.errors.push("Hệ thống chỉ chấp nhận file doc, docx, ppt, pptx, pdf");
	            	} else {
		            	// console.log("Thành công : " + response);
		            	vm.documents[vm.currentIndex].name = response.document.name;
		            	vm.documents[vm.currentIndex].file_name = response.document.file_name;
		            	vm.documents[vm.currentIndex].show = response.document.show;
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
		            }
                }
            });
		},

		// trả về document index max của 1 course
		getDocumentIndexMax(courseId) {
			var vm = this;
			var maxIndex = 0;
			for (var i = 0; i < vm.documents.length; i++) {
				if (vm.documents[i].course_id == courseId && vm.documents[i].order_index > maxIndex) {
					maxIndex = vm.documents[i].order_index;
				}
			}
			return parseInt(maxIndex);
		},

		// xóa 1 document
		removeDocument() {
			var vm = this;
			var data = {
	            'id'		: vm.currentId
	        };
	        // console.log(data);
	        $.ajax({
	            url: window.location.origin + "/backend/document/delete", type:"DELETE", data: data, async: true,
	            error: function() {
	            	vm.errors = [];
	            },
	            success: function(response) {
	            	vm.documents.splice(vm.currentIndex, 1);
	            	$('#deletModal').modal('toggle');
	            }
	        });
		}
	},

	mounted() {
		var vm = this;

		// hiển thị tab đầu tiên
		vm.openTab(vm.courses[0].id);

		for (var i = 0; i < vm.courses.length; i++) {
			sortTable('table_course_' + vm.courses[i].id, 1, 6, '/backend/document/sort', 'order_index', 'table_course_' + vm.courses[i].id);
			$('#table_course_' + vm.courses[i].id).DataTable({
				"pageLength": 9,
				"order": [[ 5, "asc" ]],
				"stateSave": true
			});
		}
		
	}
});

</script>

@stop