@extends('backend._default.dashboard')

@section('description')
    Quản lý flashcard
@stop
@section('keywords')
    flashcard
@stop
@section('author')
    dungmori.com
@stop
@section('title')
    Admin | Quản lý flashcard
@stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link href="https://vjs.zencdn.net/8.16.1/video-js.css" rel="stylesheet"/>
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <link rel="stylesheet" href="//unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css">
@stop

@section('content')
    <div id="flashcard-app">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header mb-1">
                            <h3 class="card-title">Quản lý Flashcard</h3>
                            <a href="{{ url('/backend/flashcard/create') }}" class="btn btn-primary">
                                <i class="fa fa-plus"></i> Thêm flashcard
                            </a>
                            <button type="button" class="btn btn-success ml-2" id="btnImportExcel">
                                <i class="fa fa-file"></i> Import từ Excel
                            </button>
                            <a href="{{ url('assets/excel/flashcard.xlsm') }}" class="btn btn-warning ml-2">
                                <i class="fa fa-download"></i> Download Mẫu Excel
                            </a>
                            <input type="file" id="fileImportExcel" accept=".xlsx,.xls,.xlsm" style="display: none;">
                        </div>
                        <div class="search-box flex my-2">
                            <select id="course-select" class="form-control w-25 mr-2" name="course_id">
                                <option value="">--Chọn khóa học--</option>
                                @foreach ($listCourse as $course)
                                    <option value="{{ $course['id'] }}">{{ $course['name'] }}</option>
                                @endforeach
                            </select>
                            <input id="lesson-id" type="text" class="form-control w-25 ml-2" name="lesson_id"
                                   placeholder="Lesson ID">
                            <select id="show-select" class="form-control w-25" name="show">
                                <option value="">--Chọn trạng thái--</option>
                                <option value="1">Hiển thị</option>
                                <option value="0">Ẩn</option>
                            </select>
                            <button type="button" class="btn btn-info ml-2" id="btnSearch" onclick="searchFlashcard()">
                                <i class="fa fa-search"></i> Tìm kiếm
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Lesson ID</th>
                                        <th>Từ vựng</th>
                                        <th>Trọng âm</th>
                                        <th>Nghĩa</th>
                                        <th>Loại từ</th>
                                        <th>Ví dụ</th>
                                        <th>Nghĩa ví dụ</th>
                                        <th>Câu hỏi</th>
                                        <th>Nghĩa phụ chữ Hán</th>
                                        <th>Hiển thị</th>
                                        <th>Thao tác</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach ($flashcards as $component)
                                        @php
                                            $flashcard = json_decode($component->value);
                                        @endphp
                                        <tr>
                                            <td>{{ $component->id }}</td>
                                            <td>{{ $component->lesson_id }}
                                                {{ $component->lesson ? ' - ' . $component->lesson->name : '' }}</td>
                                            <td>
                                                @if ($flashcard->audio)
                                                    <div class="text-center mb-2">
                                                        <div class="icon-speaker mr-2 cursor-pointer"
                                                             data-audio="{{ $flashcard->audio }}"
                                                             data-audio-id="{{ $component->id }}_word"
                                                             onclick="playAudio(this)">
                                                            <img src="{{ asset('assets/img/speaker.png') }}"
                                                                 alt=""/>
                                                        </div>
                                                        <audio controls
                                                               id="audio_{{ $component->id }}_word"
                                                               class="hidden">
                                                            <source src="https://tokyo-v2.dungmori.com/audio/{{ $flashcard->audio }}"
                                                                    type="audio/mpeg">
                                                            Your browser does not support the audio element.
                                                        </audio>
                                                    </div>
                                                @endif
                                                {!! htmlspecialchars_decode($flashcard->word) !!}
                                            </td>
                                            <td>{!! htmlspecialchars_decode(isset($flashcard->word_stress) ? $flashcard->word_stress : '') !!}</td>
                                            <td>{!! htmlspecialchars_decode($flashcard->meaning) !!}</td>
                                            <td>{{ $flashcard->word_type }}</td>
                                            <td>
                                                @php
                                                    $examples = $flashcard->example;
                                                @endphp
                                                @if ($examples)
                                                    @foreach ($examples as $key => $example)
                                                        <div>
                                                            @if ($example->audio)
                                                                <div class="flex align-items-center items-center">
                                                                    <div class="icon-speaker mr-2 cursor-pointer"
                                                                         data-audio="{{ $example->audio }}"
                                                                         data-audio-id="{{ $component->id }}_{{ $key }}"
                                                                         onclick="playAudio(this)">
                                                                        <img src="{{ asset('assets/img/speaker.png') }}"
                                                                             alt=""/>
                                                                    </div>
                                                                    <div>Audio: {{ $example->audio }}</div>
                                                                    <audio controls
                                                                           id="audio_{{ $component->id }}_{{ $key }}"
                                                                           class="hidden">
                                                                        <source src="https://tokyo-v2.dungmori.com/audio/{{ $example->audio }}"
                                                                                type="audio/mpeg">
                                                                        Your browser does not support the audio element.
                                                                    </audio>
                                                                </div>
                                                            @endif
                                                            <div>
                                                                Example: {!! htmlspecialchars_decode($example->example) !!}
                                                            </div>
                                                        </div>
                                                        <hr>
                                                    @endforeach
                                                @endif
                                            </td>
                                            <td>
                                                @foreach ($flashcard->meaning_example as $example)
                                                    <div>{!! htmlspecialchars_decode($example) !!}</div>
                                                @endforeach
                                            </td>
                                            <td>
                                                @if ($flashcard->quiz_question)
                                                    @foreach ($flashcard->quiz_question as $question)
                                                        <div>{!! htmlspecialchars_decode($question) !!}</div>
                                                    @endforeach
                                                @endif
                                            </td>
                                            <td>
                                                {!! htmlspecialchars_decode($flashcard->kanji_meaning) !!}
                                            </td>
                                            <td>
                                                @if ($component->show == 1)
                                                    <span class="badge badge-success">Hiển thị</span>
                                                @else
                                                    <span class="badge badge-danger">Ẩn</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ url('/backend/flashcard/' . $component->id . '/edit') }}"
                                                   class="btn btn-primary btn-sm">
                                                    <i class="fa fa-edit"></i> Sửa
                                                </a>
                                                <button type="button" class="btn btn-danger btn-sm delete-modal"
                                                        data-id="{{ $component->id }}"
                                                        data-word="{{ $flashcard->word }}">
                                                    <i class="fa fa-trash"></i> Xóa
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-center">
                                {{ $flashcards->appends(request()->query())->links() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Xóa -->
        <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel"
             aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">Xác nhận xóa</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        Bạn có chắc chắn muốn xóa flashcard "<span id="deleteWord"></span>"?
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                        <button type="button" class="btn btn-danger" id="confirmDelete">Xóa</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('foot-js')
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>

    <script>
        $(document).ready(function () {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            // Xử lý sự kiện click nút xóa
            $('.delete-modal').click(function () {
                var id = $(this).data('id');
                var word = $(this).data('word');
                $('#deleteWord').html(word);
                $('#deleteWord').attr('data-id', id);
                $('#deleteModal').modal('show');
            });

            // Xử lý sự kiện xác nhận xóa
            $('#confirmDelete').click(function () {
                var id = $('#deleteWord').attr('data-id');
                $.ajax({
                    url: '/backend/flashcard/delete/' + id,
                    type: 'DELETE',
                    success: function (response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Có lỗi xảy ra khi xóa flashcard!');
                        }
                    },
                    error: function () {
                        alert('Có lỗi xảy ra khi xóa flashcard!');
                    }
                });
                $('#deleteModal').modal('hide');
            });

            $('#btnImportExcel').click(function () {
                $('#fileImportExcel').click();
            });

            $('#fileImportExcel').change(function () {
                var file = this.files[0];
                if (file) {
                    var formData = new FormData();
                    formData.append('file', file);
                    $.ajax({
                        url: '/backend/flashcard/import-excel',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            alert('Quá trình import đang chạy, có thể mất một chút thời gian!');
                            location.reload();
                        },
                        error: function () {
                            alert('Có lỗi xảy ra khi import!');
                        }
                    });
                }
            });

            // auto select theo param
            var courseId = '{{ request()->query("course_id") }}';
            var show = '{{ request()->query("show") }}';
            var lessonId = '{{ request()->query("lesson_id") }}';
            if (courseId) {
                $('#course-select').val(courseId);
            }
            if (show) {
                $('#show-select').val(show);
            }
            if (lessonId) {
                $('#lesson-id').val(lessonId);
            }
        });

        function searchFlashcard() {
            var courseId = $('#course-select').val();
            var show = $('#show-select').val();
            var lessonId = $('#lesson-id').val();
            window.location.href = '/backend/flashcard?course_id=' + courseId + '&show=' + show + '&lesson_id=' + lessonId;
        }

        function playAudio(element) {
            var audioId = $(element).data('audio-id');
            var audioElement = $('#audio_' + audioId);
            audioElement[0].play();
        }
    </script>
@stop
