@extends('backend._default.dashboard')
@section('description') Quản lý flashcard @stop
@section('assets')
    <link rel="stylesheet" href="{{ url('css/backend/flashcard.css') }}?v={{ filemtime('css/backend/flashcard.css') }}">
    <script src="{{ asset('/plugin/ckeditor4/ckeditor.js') }}"></script>
    <script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}">
@stop
@section('content')
    <div class="container-fluid" id="flashcardEdit">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">{{ isset($component) ? 'Sửa Flashcard' : 'Thêm Flashcard Mới' }}</h3>
                    </div>
                    <div class="card-body">
                        <form id="flashcardForm" method="POST" enctype="multipart/form-data">
                            @csrf
                            <input type="hidden" name="id" value="{{ isset($component) ? $component->id : '' }}">

                            <!-- Lesson ID -->
                            <div class="form-group">
                                <label>Lesson ID</label>
                                <input type="number" class="form-control" name="lesson_id" id="lesson_id"
                                    value="{{ isset($component) ? $component->lesson_id : request()->get('lesson_id') }}">
                            </div>

                            @php
                                $flashcard = !empty($component->value) ? json_decode($component->value) : null;
                            @endphp

                            <!-- 1. Từ vựng (Có hỗ trợ Furigana) -->
                            <div class="form-group">
                                <label>1. Từ vựng (Có hỗ trợ Furigana)</label>
                                <textarea class="form-control editor" name="word" id="word" rows="3">{{ isset($flashcard) ? $flashcard->word : '' }}</textarea>
                            </div>

                            <!-- 2. Trọng âm -->
                            <div class="form-group">
                                <label>2. Trọng âm</label>
                                <textarea class="form-control editor" name="word_stress" id="word_stress" rows="3">{{ isset($flashcard) && !empty($flashcard->word_stress) ? $flashcard->word_stress : '' }}</textarea>
                            </div>

                            <!-- 3. Phân loại từ -->
                            <div class="form-group">
                                <label>3. Phân loại từ</label>
                                <v-select
                                    v-model="formData.word_type"
                                    :options="wordTypes"
                                    label="text"
                                    :multiple="true"
                                    :reduce="(teachers) => teachers.id"
                                ></v-select>
                            </div>

                            <!-- 4. Audio từ vựng -->
                            <div class="form-group">
                                <label>4. Audio từ vựng</label>
                                <div class="input-group">
                                    <div class="input-group my-2 flex">
                                        <div class="form-group">
                                            <div class="col-sm-8">
                                                <input type="text" name="audio" v-model="formData.audio"
                                                    value="{{ isset($flashcard) ? $flashcard->audio : '' }}"
                                                    placeholder="Nhấp để chọn file" readonly id="audio"
                                                    class="form-control">
                                                <input type="file" style="display: none" accept="audio/*"
                                                    class="upload-file" data-id="audio" data-type="audio" id="audio_file">
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="flex">
                                                    <button type="button" class="btn btn-primary btn-upload-file"
                                                        data-id="audio_file">Tải
                                                        lên</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 5. Tải ảnh mặt trước -->
                            <div class="form-group">
                                <label>5. Tải ảnh mặt trước</label>
                                <div class="input-group">
                                    <div class="input-group my-2 flex">
                                        <div class="form-group">
                                            <div class="col-sm-8">
                                                <input type="text" name="front_image" v-model="formData.front_image"
                                                    value="{{ isset($flashcard) ? $flashcard->front_image : '' }}"
                                                    placeholder="Nhấp để chọn file" readonly id="front_image"
                                                    class="form-control">
                                                <input type="file" style="display: none" class="upload-file"
                                                    accept="image/*" data-id="front_image" data-type="image"
                                                    id="front_image_file">
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="flex">
                                                    <button type="button" class="btn btn-primary btn-upload-file"
                                                        data-id="front_image_file">Tải
                                                        lên</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 6. Câu ví dụ Tiếng Nhật -->
                            <div class="form-group">
                                <label>6. Câu ví dụ Tiếng Nhật (Có hỗ trợ Furigana)</label>
                                <div id="examples-container">
                                    @if (isset($flashcard) && $flashcard->example)
                                        @php
                                            $examples = $flashcard->example;
                                        @endphp
                                        @foreach ($examples as $index => $example)
                                            <div class="example-group mb-3" data-id="{{ $index + 1 }}">
                                                <label>Ví dụ {{ $index + 1 }}:</label>
                                                <textarea class="form-control mb-2 editor" id="example-{{ $index + 1 }}" name="examples[]">{{ $example->example }}</textarea>
                                                <div class="input-group my-2 flex">
                                                    <div class="form-group">
                                                        <label class="col-sm-2 control-label">Audio</label>
                                                        <div class="col-sm-8">
                                                            <input type="text" name="audio_examples[]"
                                                                value="{{ $example->audio }}"
                                                                placeholder="Nhấp để chọn file" readonly
                                                                id="audio_example_{{ $index + 1 }}"
                                                                class="form-control">
                                                            <input type="file" style="display: none" accept="audio/*"
                                                                class="upload-file"
                                                                id="audio_example_{{ $index + 1 }}_file"
                                                                data-id="audio_example_{{ $index + 1 }}"
                                                                data-type="audio">
                                                        </div>
                                                        <div class="col-sm-2">
                                                            <div class="flex">
                                                                <button type="button"
                                                                    class="btn btn-primary btn-upload-file"
                                                                    data-id="audio_example_{{ $index + 1 }}_file">Tải
                                                                    lên</button>
                                                                <button type="button"
                                                                    class="ml-1 btn btn-danger btn-remove-row">Xóa</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                                <button type="button" class="btn btn-success" id="add-example">
                                    <i class="fa fa-plus"></i> Thêm câu ví dụ và audio
                                </button>
                            </div>

                            <!-- 7. Nghĩa Từ vựng / Âm chữ Hán -->
                            <div class="form-group">
                                <label>7. Nghĩa Từ vựng / Âm chữ Hán</label>
                                <textarea class="form-control editor" name="meaning" id="meaning" rows="3">{{ isset($flashcard) ? $flashcard->meaning : '' }}</textarea>
                            </div>

                            <!-- 8. Tải Ảnh mặt sau -->
                            <div class="form-group">
                                <label>8. Tải Ảnh mặt sau</label>
                                <div class="input-group">
                                    <div class="input-group my-2 flex">
                                        <div class="form-group">
                                            <div class="col-sm-8">
                                                <input type="text" name="back_image" v-model="formData.back_image"
                                                    value="{{ isset($flashcard) ? $flashcard->back_image : '' }}"
                                                    placeholder="Nhấp để chọn file" readonly id="back_image"
                                                    class="form-control">
                                                <input type="file" style="display: none" class="upload-file"
                                                    accept="image/*" data-id="back_image" data-type="image"
                                                    id="back_image_file">
                                            </div>
                                            <div class="col-sm-2">
                                                <div class="flex">
                                                    <button type="button" class="btn btn-primary btn-upload-file"
                                                        data-id="back_image_file">Tải
                                                        lên</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 9. Câu ví dụ Tiếng Việt -->
                            <div class="form-group">
                                <label>9. Câu ví dụ Tiếng Việt</label>
                                <div id="meaning-examples-container">
                                    @if (isset($flashcard) && $flashcard->meaning_example)
                                        @php
                                            $meaningExamples = $flashcard->meaning_example;
                                        @endphp
                                        @foreach ($meaningExamples as $index => $example)
                                            <div class="meaning-example-group mb-3" data-id="{{ $index + 1 }}">
                                                <label>Ví dụ {{ $index + 1 }}:</label>
                                                <textarea class="form-control editor" id="meaning-example-{{ $index + 1 }}" name="meaning_examples[]">{{ $example }}</textarea>
                                                <button type="button"
                                                    class="btn btn-danger btn-remove-meaning">Xóa</button>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                                <button type="button" class="btn btn-success" id="add-meaning-example">
                                    <i class="fa fa-plus"></i> Thêm câu ví dụ
                                </button>
                            </div>

                            <!-- 10. Đáp án trắc nghiệm -->
                            <div class="form-group">
                                <label>10. Đáp án trắc nghiệm</label>
                                <div class="alert alert-danger">Hệ thống sẽ tự xáo trộn đáp án khi làm bài. Mặc định đáp án
                                    đúng là đáp án đầu tiên</div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" name="quiz_questions[]"
                                            v-model="formData.quiz_questions[0]" placeholder="Đáp án đúng"
                                            value="{{ isset($flashcard) && !empty($flashcard->quiz_question) && !empty($flashcard->quiz_question[0]) ? $flashcard->quiz_question[0] : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" name="quiz_questions[]"
                                            v-model="formData.quiz_questions[1]" placeholder="Đáp án sai"
                                            value="{{ isset($flashcard) && !empty($flashcard->quiz_question) && !empty($flashcard->quiz_question[1]) ? $flashcard->quiz_question[1] : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" name="quiz_questions[]"
                                            v-model="formData.quiz_questions[2]" placeholder="Đáp án sai"
                                            value="{{ isset($flashcard) && !empty($flashcard->quiz_question) && !empty($flashcard->quiz_question[2]) ? $flashcard->quiz_question[2] : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" name="quiz_questions[]"
                                            v-model="formData.quiz_questions[3]" placeholder="Đáp án sai"
                                            value="{{ isset($flashcard) && !empty($flashcard->quiz_question) && !empty($flashcard->quiz_question[3]) ? $flashcard->quiz_question[3] : '' }}">
                                    </div>
                                </div>
                            </div>

                            <!-- 11. Nghĩa phụ chữ Hán -->
                            <div class="form-group">
                                <label>11. Nghĩa phụ chữ Hán</label>
                                <input type="text" class="form-control" name="kanji_meaning" id="kanji_meaning"
                                    v-model="formData.kanji_meaning"
                                    value="{{ isset($flashcard) && !empty($flashcard->kanji_meaning) ? $flashcard->kanji_meaning : '' }}">
                            </div>

                            <!-- 12. Hiển thị -->
                            <div class="form-group">
                                <label>11. Hiển thị</label>
                                <select class="form-control" name="show" v-model="formData.show">
                                    <option value="1"
                                        {{ isset($component) && $component->show == 1 ? 'selected' : '' }}>Hiển thị
                                    </option>
                                    <option value="0"
                                        {{ isset($component) && $component->show == 0 ? 'selected' : '' }}>Ẩn</option>
                                </select>
                            </div>

                            <div class="form-group mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-save"></i> {{ isset($flashcard) ? 'Cập nhật' : 'Thêm mới' }}
                                </button>
                                <a href="{{ url('/backend/flashcard') }}" class="btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i> Quay lại
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <div class="flashcard-preview">
                            <h4>Mặt trước</h4>
                            <div class="flashcard-front">
                                <span class="audio" @click="playAudio(formData.audio)"><img
                                        src="{{ asset('assets/img/speaker.png') }}" /></span>
                                <div class="flashcard-word" v-html="formData.word"></div>
                                <div class="flashcard-image-front">
                                    <img :src="getImageUrl(formData.front_image)" />
                                </div>
                                <div class="flashcard-examples">
                                    <div class="flashcard-examples-title">
                                        <span>Ví dụ: </span>
                                    </div>
                                    <div class="flashcard-examples-list">
                                        <template v-for="(item, index) in formData.examples">
                                            <div :key="index">
                                                <div class="flex align-items-center">
                                                    <span class="mr-2" @click="playAudio(item.audio)"><img
                                                            src="{{ asset('assets/img/speaker.png') }}" /></span>
                                                    <span class="mr-2">@{{ index + 1 }}. </span>
                                                    <span v-html="item.example"></span>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <h4>Mặt sau</h4>
                            <div class="flashcard-back">
                                <div class="flashcard-meaning" v-html="formData.meaning"></div>
                                <div class="flashcard-kanji-meaning" v-html="formData.kanji_meaning"></div>
                                <div class="flashcard-image-back">
                                    <img :src="getImageUrl(formData.back_image)" />
                                </div>
                                <div class="flashcard-examples">
                                    <div class="flashcard-examples-title">
                                        <span>Ví dụ: </span>
                                    </div>
                                    <div class="flashcard-examples-list">
                                        <template v-for="(example, index) in formData.meaning_examples">
                                            <div :key="index">
                                                <div class="flex align-items-center">
                                                    <span class="mr-2">@{{ index + 1 }}. </span>
                                                    <span v-html="example.content"></span>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endsection

    @section('foot-js')
    
        <script>
            const videoBaseURL = "{{ env('RENDER_APP_URL') }}";
            const videoServerURL = "{{ env('VIDEO_SERVER_URL') }}";
        </script>
        <script>
            const component = @json(isset($component) ? $component : null);
            const flashcard = @json(isset($flashcard) ? $flashcard : null);
            const cdnUrl = "{{ env('VIDEO_SERVER_URL') }}/";

            function getImageUrl(image) {
                return cdnUrl + 'images/' + image;
            }

            function getAudioUrl(audio) {
                return cdnUrl + 'audio/' + audio;
            }

            function playAudio(audio) {
                const audioElement = new Audio(getAudioUrl(audio));
                audioElement.play();
            }
        </script>
        <script src="{{ url('/assets/js/flashcard/flashcard.js') }}?v={{ filemtime('assets/js/flashcard/flashcard.js') }}">
        </script>
    @endsection
