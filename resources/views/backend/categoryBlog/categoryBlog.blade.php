@extends('backend._default.dashboard')

@section('description') Q<PERSON>ản l<PERSON> lo<PERSON> tin tức @stop
@section('keywords') blog @stop
@section('author') dungmori.com @stop
@section('title') Admin | Tin tức @stop

@section('assets')
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script> 
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script> 
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet">  
@stop

@section('content') 
	<div class="row bg-title">
        <h4 class="page-title pull-left">
            Loại tin tức
        </h4> 
        @if(json_decode(Auth::guard('admin')->user()->matrix)->category->add != null)
            <button class="add-modal btn btn-success"
    				style="right: 26px;position: absolute;width: 146px;">
    				<span class="glyphicon glyphicon-plus"></span>Thêm mới
    		</button>
        @endif
    </div>
    <div class="category_blog_area">  
        @include('backend.categoryBlog.detailCategory')
	</div>
    <div id="myModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form">
                        <li class="global_error text-left hidden"></li>
                        <div class="form-group maso">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="fid">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="category-blog">Loại tin tức</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="category-blog">
                            </div>
                        </div>
                        <div class="form-group create">
                            <label class="control-label col-sm-2" for="created_at">Ngày tạo</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="created_at">
                            </div>
                        </div>
                    </form>
                    <!--delete form-->
                    <div class="deleteContent">
                        Có muốn xóa tin tức <span class="blog_name"></span> này đi không? <span
                            class="hidden blog_id"></span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn" >
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>

	<script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                  'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });   
        });
        //sắp xếp kéo thả
        sortTable('body_blog', 1, 3, '/backend/category-sort-order', 'sort_order', 'table_blog');
        //thao tac them, xoa, sua
        $(document).on('click', '.add-modal', function() {
            $('.maso').hide();
            $('.create').hide();
            $('#footer_action_button').text(" Thêm");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').addClass('add');
            $('.modal-title').text('Thêm');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            fillmodalData('')
            $('#myModal').modal('show');
        });

            //edit
        $(document).on('click', '.edit-modal', function() {
            $('.maso').show();
            $('.create').show();
            $('#fid').attr('disabled', true);
            $('#created_at').attr('disabled', true);
            $('#footer_action_button').text(" Sửa");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('edit');
            $('.modal-title').text('Sửa');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            var blog = $(this).data('info');
            fillmodalData(blog);
            $('#myModal').modal('show');
        });
        //delete
        $(document).on('click', '.delete-modal', function() {
            $('#footer_action_button').text(" Xóa");
            $('#footer_action_button').removeClass('glyphicon-check');
            $('#footer_action_button').addClass('glyphicon-trash');
            $('.actionBtn').removeClass('btn-success');
            $('.actionBtn').addClass('btn-danger');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('delete');
            $('.modal-title').text('Xóa');
            $('.deleteContent').show();
            $('.form-horizontal').hide();
            var blog = $(this).data('info');
            $('.blog_id').text(blog.id);
            $('.blog_name').html(blog.name);
            $('#myModal').modal('show');
        });
        //
        function fillmodalData(blog){
            if(blog == ''){
                $('#category-blog').val('');
                $('.global_error').addClass('hidden');
            }else{
                $('#fid').val(blog.id);
                $('#category-blog').val(blog.name);
                $('#created_at').val(blog.created_at);
                $('.global_error').addClass('hidden');
            }
        }

        $('.modal-footer').on('click', '.add', function() {
            $.ajax({
                type: 'post',
                url: '/backend/category/create',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'name': $('#category-blog').val(),
                    'sort_order': ($("#table_blog tbody tr").length + 1),
                },
                success: function(data) {
                    if (data.errors){
                        if(data.errors.name) {
                            $('#myModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    }else {
                        $('#myModal').modal('hide');
                        $('.category_blog_area').html(data);
                        sortTable('body_blog', 1, 3, '/backend/category-sort-order', 'sort_order', 'table_blog');
                    }
                }
            });
        });
        $('.modal-footer').on('click', '.edit', function() {
            $.ajax({
                type: 'post',
                url: '/backend/category/edit',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('#fid').val(),
                    'name': $('#category-blog').val()
                },
                 success: function(data) {
                    if (data.errors){
                        if(data.errors.name) {
                            $('#myModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    }else {
                        $('#myModal').modal('hide');
                        $('.category_blog_area').html(data);
                        sortTable('body_blog', 1, 3, '/backend/category-sort-order', 'sort_order', 'table_blog');
                    }
                }
            });
        });
        $('.modal-footer').on('click', '.delete', function() {
            $.ajax({
                type: 'post',
                url: '/backend/category/delete',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('.blog_id').text()
                },
                success: function(data) {
                    $('#myModal').modal('hide');
                    $('.item' + $('.blog_id').text()).remove();
                }
            });
        });

    </script>

@stop