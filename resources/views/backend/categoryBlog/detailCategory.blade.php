<table class="table table-borderless" id="table_blog">
    <thead>
        <tr>
            <th class="text-center"><PERSON><PERSON></th>
            <th class="text-center"><PERSON><PERSON><PERSON> tin tức</th>
            <th class="text-center"><PERSON><PERSON> tiên</th>
            <th class="text-center"><PERSON><PERSON><PERSON></th>
            <th class="text-center"><PERSON><PERSON><PERSON> đ<PERSON></th>
        </tr>
    </thead>
    <tbody class="body_blog">
        @foreach($data as $item)
        <tr class="item{{$item->id}}">
            <td class="text-center">{{$item->id}}</td>
            <td class="text-center">
                <a href="{{url('/bai-viet/cm/'.$item->friendly_url)}}" target="_blank">{{$item->name}}</a><br>
            </td>
            <td class="text-center">{{$item->sort_order}}</td>
            <td class="text-center">{{$item->created_at}}</td>
            <td class="text-center">
                @if(json_decode(Auth::guard('admin')->user()->matrix)->category->edit != null)
                    <button class="edit-modal btn btn-info"
                        data-info="{{$item}}">
                        <span class="glyphicon glyphicon-edit"></span> Sửa
                    </button>
                @endif

                @if(json_decode(Auth::guard('admin')->user()->matrix)->category->delete != null)
                    <button class="delete-modal btn btn-danger"
                        data-info="{{$item}}">
                        <span class="glyphicon glyphicon-trash"></span> Xóa
                    </button>
                @endif
            </td>
        </tr>
        @endforeach
    </tbody>
</table>