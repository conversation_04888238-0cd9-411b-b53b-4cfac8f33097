@extends('backend._default.dashboard')

@section('title') Admin | Quản lý hoạt động @stop
@section('description') Quản lý hoạt động @stop
@section('keywords') activity @stop
@section('author') dungmori.com @stop

@section('assets')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
@stop

@section('content')
  <div id="activity-management">
    <h2 class="font-bold">Quản lý thưởng chuỗi hoạt động</h2>
    <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-6">
      <li
          v-for="(activity, idx) in activities"
          class="col-span-1 rounded-lg bg-white shadow p-5 flex items-center justify-start cursor-pointer hover:shadow-lg transition-all"
          @click="location.href=`${url}/backend/gamification/activity/${activity.id}/point`"
      >
        <div class="mr-4 flex justify-center items-center p-5 rounded-full bg-lime-500 w-[50px] h-[50px] font-bold text-white text-lg">@{{ idx + 1 }}</div>
        <div class="flex-1">
          <div>@{{ activity.name }}</div>
          <div>@{{ activity.description }}</div>
        </div>
      </li>
    </ul>
    <h2 class="font-bold">Quản lý quy tắc thưởng</h2>
    <el-table
        :data="rules"
        style="width: 100%"
        class="mt-5"
    >
      <el-table-column
          prop="description"
          label="Mô tả">
      </el-table-column>
      <el-table-column
          prop="point"
          label="Điểm mặc định">
      </el-table-column>
      <el-table-column
          prop="status"
          label="Trạng thái">
      </el-table-column>
    </el-table>
  </div>
@stop
@section('foot-js')
  <script type="text/javascript">
      new Vue({
          el: '#activity-management',
          data() {
              return {
                  url: window.location.origin,
                  activities: @json($activities),
                  rules: @json($experienceRules),
              }
          },
          mounted() {
              $.ajaxSetup({
                  headers: {
                      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                  }
              });
          },
          methods: {
          }
      })
  </script>
@stop
