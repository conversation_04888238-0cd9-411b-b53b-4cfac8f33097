@extends('backend._default.dashboard')

@section('title') Admin | Quản lý hoạt động @stop
@section('description') Quản lý hoạt động @stop
@section('keywords') activity @stop
@section('author') dungmori.com @stop

@section('assets')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
@stop

@section('content')
  <div id="activity-management">
    <div class="flex items-center gap-3">
      <el-button @click="window.location.href = '{{ route('backend.gamification.activity.index') }}'" size="small">Quay lại</el-button>
      <h2 class="font-bold">@{{ name }}</h2>
      <el-button @click="addRule" type="primary" size="small">Thêm</el-button>
      <el-dialog title="Thêm chuỗi thưởng" :visible.sync="dialogFormVisible">
        <el-form :model="form">
          <el-form-item label="Chuỗi" :label-width="'120px'">
            <el-input v-model="form.count" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="Điểm" :label-width="'120px'">
            <el-input v-model="form.point" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">Huỷ</el-button>
          <el-button type="primary" @click="saveRule">Lưu</el-button>
        </span>
      </el-dialog>
    </div>
    <el-table
      :data="rules"
      style="width: 100%"
    >
      <el-table-column
          prop="count"
          label="Số lần liên tiếp">
      </el-table-column>
      <el-table-column
          prop="point"
          label="Điểm thưởng">
      </el-table-column>
    </el-table>
  </div>
@stop
@section('foot-js')
  <script type="text/javascript">
      const initialRule = {
          count: 1,
          point: 100
      }
      new Vue({
          el: '#activity-management',
          data() {
              return {
                  url: window.location.origin,
                  rules: @json($activity->rules),
                  id: '{{ $activity->id }}',
                  name: '{{ $activity->name }}',
                  form: {
                      count: '',
                      point: ''
                  },
                  dialogFormVisible: false,
              }
          },
          mounted() {
              $.ajaxSetup({
                  headers: {
                      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                  }
              });
          },
          methods: {
              addRule() {
                  // this.rules.unshift({
                  //     ...initialRule,
                  // })
                  this.form = {
                      count: '',
                      point: ''
                  }
                  this.dialogFormVisible = true
              },
              saveRule() {
                  const vm = this;
                  $.post(vm.url + `/backend/gamification/activity/${vm.id}/point/store`, vm.form, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã lưu thay đổi');
                          vm.rules.push({...res.data})
                      }
                  });
                  this.dialogFormVisible = false
              }
          }
      })
  </script>
@stop
