@extends('backend._default.dashboard')

@section('title') Admin | <PERSON>uản lý cấp độ @stop
@section('description') Quản lý cấp độ @stop
@section('keywords') Level @stop
@section('author') dungmori.com @stop

@section('assets')

@stop

@section('content')
  <h2 class="font-bold"><PERSON><PERSON><PERSON><PERSON> lý cấp độ</h2>
  <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-6">
    @foreach($levels as $level)
      <li class="col-span-1 rounded-lg bg-white shadow p-5 flex items-center justify-start">
        <div class="mr-4 flex justify-center items-center p-5 rounded-full bg-lime-500 w-[50px] h-[50px] font-bold text-white text-lg">{{ $level->level }}</div>
        <div>
          <div>Số người đạt: {{ $level->users->count() }}</div>
          <form class="relative mt-2 rounded-md shadow-sm" @submit="updateLevelExperience">
            <input
              type="number"
              name="point"
              id="level-point-{{ $level->level }}"
              class="block w-full rounded-md border-0 p-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-300 focus:ring-2 focus:ring-inset focus:ring-gray-500 sm:text-sm sm:leading-6"
              placeholder="Điểm để đạt level"
              value="{{ $level->next_level_experience }}"
              aria-invalid="true"
              aria-describedby="email-error"
              @disabled($level->level === 1)
            >
          </form>
        </div>
      </li>
    @endforeach
  </ul>
@stop
