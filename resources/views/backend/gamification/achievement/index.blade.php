@extends('backend._default.dashboard')

@section('title') Admin | Quản lý thành tựu @stop
@section('description') Quản lý thành tựu @stop
@section('keywords') Level @stop
@section('author') dungmori.com @stop

@section('assets')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
@stop

@section('content')
  <div id="achievement-management">
    <div class="flex items-center gap-3">
      <h2 class="font-bold">Quản lý thành tựu</h2>
      <button @click="addAchievement" type="button" class="rounded bg-green-600 px-5 py-1 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
        Thêm
      </button>
    </div>
    <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <li v-for="(achievement, idx) in achievements" class="col-span-1 rounded-lg bg-white shadow px-5 pb-8 pt-3">
          <div class="pb-2 flex justify-end items-center gap-2 text-right text-gray-300 text-xs font-bold">
            @{{ achievement.created_at }} #@{{ achievement.id }}
            <span class="w-3 h-3 rounded-full bg-green-500"></span>
          </div>
          <form action="" class="flex flex-1 items-start justify-start gap-2">
            <div class="flex flex-col gap-3 items-center">
              <input type="file" :ref="`imageInput${idx}`" class="hidden" @change="uploadImage(idx, achievement.id, $event)" />
              <img class="flex h-12 w-12 cursor-pointer" :src="achievement.image_name" alt="" @click="toggleInputFile(idx)"/>
              <button
                  type="button"
                  class="shrink-0 w-10 h-10 rounded-full bg-green-600 p-2 text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  @click="saveAchievement(achievement, idx)"
              >
                <i class="fa fa-floppy-o"></i>
              </button>
              <button
                  type="button"
                  class="shrink-0 w-10 h-10 rounded-full bg-red-600 p-2 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  @click="deleteAchievement(achievement, idx)"
              >
                <i class="fa fa-trash"></i>
              </button>
            </div>
            <div class="w-full flex flex-col gap-3">
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Tên thành tựu</label>
                <input
                    type="text"
                    name="name"
                    id="name"
                    class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    v-model="achievement.name"
                >
              </div>
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Mô tả</label>
                <input
                    type="text"
                    name="name"
                    id="name"
                    class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    v-model="achievement.description"
                >
              </div>
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Lặp lại</label>
                <select v-model="achievement.trigger_repeat" id="leocation" name="location" class="block w-full rounded-md border-0 p-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                  <option selected value="">Chọn số lần lặp lại</option>
                  <option v-for="({ value, label }) in repeatOptions" :value="value">@{{ label }}</option>
                </select>
              </div>
              <div class="grid grid-cols-3 gap-2">
                <div class="col-span-1 relative">
                  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Hành động</label>
                  <select v-model="achievement.trigger_condition" id="location" name="location" class="block w-full rounded-md border-0 p-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    <option selected value="">Chọn hành động</option>
                    <option v-for="({ value, label }) in conditionOptions" :value="value">@{{ label }}</option>
                  </select>
                </div>
                <div class="col-span-1 relative">
                  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Logic</label>
                  <select v-model="achievement.trigger_operator" id="location" name="location" class="block w-full rounded-md border-0 p-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    <option selected value="">Chọn toán tử</option>
                    <option v-for="({ value, label }) in operatorOptions" :value="value">@{{ label }}</option>
                  </select>
                </div>
                <div class="col-span-1 relative">
                  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Giá trị</label>
                  <input
                      v-model="achievement.trigger_value"
                      type="text"
                      name="name"
                      id="name"
                      class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  >
                </div>
              </div>
            </div>
          </form>
        </li>
    </ul>
  </div>
@stop
@section('foot-js')
  <script type="text/javascript">
      new Vue({
          el: '#achievement-management',
          data() {
              return {
                  url: window.location.origin,
                  achievements: @json($achievements),
                  repeatOptions: [
                      { value: 'once', label: 'Một lần' },
                      { value: 'daily', label: 'Hàng ngày' },
                      { value: 'weekly', label: 'Hàng tuần' },
                      { value: 'monthly', label: 'Hàng tháng' },
                      { value: 'annually', label: 'Hàng năm' },
                  ],
                  conditionOptions: [
                      { value: 'register', label: 'Đăng ký' },
                      { value: 'lesson', label: 'Hoàn thành bài học' },
                      { value: 'lesson_count', label: 'Số bài học đã hoàn thành' },
                      { value: 'exam', label: 'Bài test' },
                      { value: 'exam_count', label: 'Số bài test hoàn thành' },
                      { value: 'login_count', label: 'Chuỗi ngày học' },
                      { value: 'point', label: 'Số điểm' },
                      { value: 'leaderboard', label: 'Top BXH' },
                      // { value: 'active_course', label: 'Kích hoạt khoá học' },
                      // { value: 'active_course_count', label: 'Số khoá học đã kích hoạt' },
                  ],
                  operatorOptions: [
                      { value: 'eq', label: '=' },
                      { value: 'neq', label: '<>' },
                      { value: 'lt', label: '<' },
                      { value: 'lte', label: '<=' },
                      { value: 'gt', label: '>' },
                      { value: 'gte', label: '>=' },
                  ],
                  form: {
                      name: ''
                  }
              }
          },
          mounted() {
              $.ajaxSetup({
                  headers: {
                      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                  }
              });
          },
          methods: {
              toggleInputFile(idx) {
                  // console.log(this.$refs[`imageInput${idx}`])
                  this.$refs[`imageInput${idx}`][0].click()
              },
              uploadImage(idx, id, file) {
                  const vm = this
                  if (file) {
                      var formData = new FormData();
                      formData.append('image', event.target.files[0]);
                      $.ajax({
                          url: vm.url + `/backend/gamification/achievement/${id}/image`,
                          type: 'post', processData: false, contentType: false, data : formData,
                          success: function(response) {
                              Vue.set(vm.achievements, idx, { ...response.data })
                              this.$message.success('Thay ảnh thành công r đại vương ơi');
                          },
                          error: function(response) {
                              this.$message.error('Có lỗi khi thay ảnh');
                          }
                      });
                  }
              },
              addAchievement() {
                if (this.achievements.length && !this.achievements[0].id) return
                this.achievements.unshift({
                    "name": "Thành tựu mới",
                    "is_secret": 0,
                    "description": "Mô tả",
                    "image": "sun.png",
                    "trigger_repeat": undefined,
                    "trigger_condition": undefined,
                    "trigger_operator": undefined,
                    "trigger_value": undefined,
                })
              },
              saveAchievement(achievement, idx) {
                  const vm = this;
                  $.post(vm.url + '/backend/gamification/achievement', { id: achievement.id, data: _.pick(achievement, ['name', 'description', 'trigger_repeat', 'trigger_condition', 'trigger_operator', 'trigger_value'])}, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã lưu thay đổi');
                          Vue.set(vm.achievements, idx, { ...res.data })
                      }
                  });
              },
              deleteAchievement(achievement, idx) {
                  const vm = this
                  this.$confirm('Xác nhận xoá và ẩn thành tích khỏi danh sách thành tích của học viên?', {
                      confirmButtonText: 'OK boy',
                      cancelButtonText: 'Quay xe',
                      type: 'warning'
                  }).then(() => {
                      vm.handleDelete(achievement, idx)
                  }).catch(() => {
                      this.$message({
                          type: 'info',
                          message: 'Đã xoá'
                      });
                  });

              },
              handleDelete(achievement, idx) {
                  const vm = this
                  if (achievement.id) {
                      $.ajax({
                          url: vm.url + `/backend/gamification/achievement/${achievement.id}`,
                          type: 'DELETE',
                          success: function(result) {
                              vm.$message('Xoá thành công');
                              vm.achievements.splice(idx, 1)
                          }
                      });
                  } else {
                      vm.achievements.splice(idx, 1)
                  }
              }
          }
      })
  </script>
@stop
