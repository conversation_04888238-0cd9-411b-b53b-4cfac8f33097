@extends('backend._default.dashboard')

@section('title') Admin | Quản lý phần thưởng @stop
@section('description') Quản lý phần thưởng @stop
@section('keywords') Reward @stop
@section('author') dungmori.com @stop

@section('assets')
  <!-- import CSS -->
  <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
  <!-- import JavaScript -->
  <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
  <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>

  <script>
      ELEMENT.locale(ELEMENT.lang.vi)
  </script>
@stop

@section('content')
  <div id="reward-management">
    <div class="flex items-center gap-3">
      <h2 class="font-bold">Quản lý phần thưởng</h2>
      <button @click="addReward" type="button" class="rounded bg-green-600 px-5 py-1 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
        Thêm
      </button>
    </div>
    <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <li v-for="(reward, idx) in rewards" class="col-span-1 rounded-lg bg-white shadow px-5 pb-8 pt-3">
          <div class="pb-2 flex justify-end items-center gap-2 text-right text-gray-300 text-xs font-bold">
            @{{ reward.created_at }} #@{{ reward.id }}
            <span class="w-3 h-3 rounded-full" :class="[reward.status ? 'bg-green-500' : 'bg-gray-500']"></span>
          </div>
          <form action="" class="flex flex-1 items-start justify-start gap-2">
            <div class="flex flex-col gap-3 items-center">
              <input type="file" :ref="`imageInput${idx}`" class="hidden" @change="uploadImage(idx, reward.id, $event)" />
              <img class="flex h-12 w-12 cursor-pointer" :src="reward.image_name" alt="" @click="toggleInputFile(idx)"/>
              <button
                  type="button"
                  class="shrink-0 w-10 h-10 rounded-full bg-green-600 p-2 text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  @click="saveReward(reward, idx)"
              >
                <i class="fa fa-floppy-o"></i>
              </button>
              <button
                  type="button"
                  class="shrink-0 w-10 h-10 rounded-full bg-red-600 p-2 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  @click="deleteReward(reward, idx)"
              >
                <i class="fa fa-trash"></i>
              </button>
            </div>
            <div class="w-full flex flex-col gap-3">
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Tên phần thưởng</label>
                <input
                    type="text"
                    name="name"
                    id="name"
                    class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    v-model="reward.name"
                >
              </div>
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Mô tả</label>
                <input
                    type="text"
                    name="name"
                    id="name"
                    class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    v-model="reward.description"
                >
              </div>
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Loại</label>
                <select v-model="reward.type" id="reward-type-select" name="location" class="block w-full rounded-md border-0 p-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                  <option selected value="">Chọn loại phần thưởng</option>
                  <option v-for="({ value, label }) in rewardTypes" :value="value">@{{ label }}</option>
                </select>
              </div>
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Số điểm cần đổi</label>
                <input
                    type="text"
                    name="name"
                    id="name"
                    class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    v-model="reward.price"
                >
              </div>
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Giá trị</label>
                <input
                    type="text"
                    name="name"
                    id="name"
                    class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    v-model="reward.value"
                >
              </div>
              <div class="relative">
                <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900 z-99">Thời gian hiệu lực (giờ)</label>
                <input
                    type="number"
                    name="expired_in"
                    min="0"
                    max="10000"
                    step="0.5"
                    id="expired_in"
                    class="block w-full rounded-md border-0 p-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-200 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    v-model="reward.expired_in"
                >
              </div>
              <div class="relative">
                <el-date-picker
                    v-model="reward.expired_at"
                    type="datetime"
                    placeholder="Có hiệu lực tới"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :clearable="false"
                >
                </el-date-picker>
              </div>
              <div class="relative">
                <label for="is_auto_claim" class="flex items-center gap-2">
                  <el-switch
                      v-model="reward.is_auto_claim"
                      active-color="#13ce66"
                      :active-value="1"
                      :inactive-value="0"
                  >
                  </el-switch>
                  Tự động đổi thưởng
                </label>
              </div>
              <div class="relative">
                <label for="is_auto_claim" class="flex items-center gap-2">
                  <el-switch
                      v-model="reward.status"
                      active-color="#13ce66"
                      :active-value="1"
                      :inactive-value="0"
                  >
                  </el-switch>
                  Trạng thái
                </label>
              </div>
            </div>
          </form>
        </li>
    </ul>
  </div>
@stop
@section('foot-js')
  <script type="text/javascript">
      new Vue({
          el: '#reward-management',
          data() {
              return {
                  url: window.location.origin,
                  rewards: @json($rewards),
                  rewardTypes: [
                      { value: 'point', label: 'Điểm' },
                      { value: 'multiplier', label: 'Nhân điểm' },
                      { value: 'discount_money', label: 'Giảm giá đơn hàng (đ)' },
                      { value: 'discount_percent', label: 'Giảm giá đơn hàng (%)' },
                      { value: 'course_time', label: 'Gia hạn khoá học (giờ)' },
                      { value: 'streak_freeze', label: 'Bảo vệ chuỗi (ngày)' },
                      { value: 'good', label: 'Quà' },
                  ],
              }
          },
          mounted() {
              $.ajaxSetup({
                  headers: {
                      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                  }
              });
          },
          methods: {
              toggleInputFile(idx) {
                  // console.log(this.$refs[`imageInput${idx}`])
                  this.$refs[`imageInput${idx}`][0].click()
              },
              uploadImage(idx, id, file) {
                  const vm = this
                  if (file) {
                      var formData = new FormData();
                      formData.append('image', event.target.files[0]);
                      $.ajax({
                          url: vm.url + `/backend/gamification/reward/${id}/image`,
                          type: 'post', processData: false, contentType: false, data : formData,
                          success: function(response) {
                              Vue.set(vm.rewards, idx, { ...response.data })
                              this.$message.success('Thay ảnh thành công r đại vương ơi');
                          },
                          error: function(response) {
                              this.$message.error('Có lỗi khi thay ảnh');
                          }
                      });
                  }
              },
              addReward() {
                if (this.rewards.length && !this.rewards[0].id) {
                    this.$message.error('Nhập mới chưa xong kìa fen');
                    return
                }
                this.rewards.unshift({
                    "name": "Phần thưởng mới",
                    "price": 0,
                    "value": 0,
                    "type": 'point',
                    "description": "Mô tả",
                    "is_auto_claim": 0,
                    "status": 1,
                    "expired_at": null,
                    "expired_in": null,
                })
              },
              saveReward(reward, idx) {
                  const vm = this;
                  $.post(vm.url + '/backend/gamification/reward', { id: reward.id, data: _.pick(reward, ['name', 'description', 'price', 'type', 'value', 'is_auto_claim', 'status', 'expired_at', 'expired_in'])}, function (res) {
                      if (res.code === 200) {
                          vm.$message.success('Đã lưu thay đổi');
                          Vue.set(vm.rewards, idx, { ...res.data })
                      }
                  });
              },
              deleteReward(reward, idx) {
                  const vm = this
                  this.$confirm('Xác nhận xoá và ẩn khỏi danh sách phần thưởng của học viên?', {
                      confirmButtonText: 'OK boy',
                      cancelButtonText: 'Quay xe',
                      type: 'warning'
                  }).then(() => {
                      vm.handleDelete(reward, idx)
                  }).catch(() => {
                      this.$message.success({
                          type: 'info',
                          message: 'Đã xoá'
                      });
                  });

              },
              handleDelete(reward, idx) {
                  const vm = this
                  if (reward.id) {
                      $.ajax({
                          url: vm.url + `/backend/gamification/reward/${reward.id}`,
                          type: 'DELETE',
                          success: function(result) {
                              vm.$message.success('Xoá thành công');
                              vm.rewards.splice(idx, 1)
                          }
                      });
                  } else {
                      vm.rewards.splice(idx, 1)
                  }
              }
          }
      })
  </script>
@stop
