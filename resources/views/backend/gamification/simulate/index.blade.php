@extends('backend._default.dashboard')

@section('title') Admin | Quản lý phakè @stop
@section('description') Quản lý phakè @stop
@section('keywords') Level @stop
@section('author') dungmori.com @stop

@section('assets')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
  <script>
      ELEMENT.locale(ELEMENT.lang.vi)
  </script>
@stop

@section('content')
  <div id="simulate-screen">
    <div class="flex items-center gap-3">
      <h2 class="font-bold">Phakè dữ liệu</h2>
    </div>
    <input class="form-control" placeholder="ID" id="id" name="id" v-model="id" />
    <div class="flex items-center gap-3 mt-3">
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="pruneAll">Xoá hết</button>
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="prunePoint">Xoá hết điểm</button>
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="pruneStreak">Xoá hết chuỗi</button>
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="pruneAchievement">Xoá hết thành tích</button>
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="pruneReward">Xoá hết phần thưởng</button>
    </div>
    <h2 class="font-bold">Phakè chuỗi ngày học</h2>
    <div class="flex items-center gap-3">
      <input class="form-control" placeholder="Chuỗi đang có" id="streak_count" name="streak_count" v-model="streak_count" />
      <el-date-picker
          v-model="activity_at"
          type="datetime"
          placeholder="Ngày hoạt động cuối cùng. VD: 2023-12-30 12:29:00"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 500px"
      >
      </el-date-picker>
      <input class="form-control" placeholder="Bảo vệ mấy ngày" id="freeze_days" name="freeze_days" v-model="freeze_days" />
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="simulateStreak">Phakè</button>
    </div>
    <h2 class="font-bold">Du hành thời gian</h2>
    <div class="flex items-center gap-3">
      <el-date-picker
          v-model="now_time"
          type="datetime"
          placeholder="Fake thời gian hiện tại của user. VD: 2023-12-30 12:29:00"
          value-format="yyyy-MM-dd HH:mm:ss"
      >
      </el-date-picker>
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="simulateNow">Phakè</button>
    </div>
    <h2 class="font-bold">Pha kè điểm</h2>
    <div class="flex items-center gap-3">
      <el-select v-model="courseId" filterable placeholder="Select">
        <el-option
            v-for="item in courses"
            :key="item.id"
            :label="item.name"
            :value="item.id">
        </el-option>
      </el-select>
      <button type="button" class="rounded bg-indigo-50 px-2 py-1 text-xs font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100" @click="simulateLessonRules">
        @{{ loading.lessonRules ? 'Chờ xíu' : 'Phakè' }}
      </button>
    </div>
  </div>
@stop
@section('foot-js')
  <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
  <script type="text/javascript">
      new Vue({
          el: '#simulate-screen',
          data: function() {
              return {
                  url: window.location.origin,
                  courses: @json($courses),
                  id: '',
                  streak_count: '',
                  activity_at: '2023-12-30 22:22:00',
                  freeze_days: '',
                  now_time: '',
                  courseId: '',
                  loading: {
                      lessonRules: false
                  }
              }
          },

          mounted: function() {
              $.ajaxSetup({
                  headers: {
                      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                  }
              });
          },
          methods: {
              simulateStreak() {
                  const vm = this;
                  const data = {
                      streak_count: this.streak_count,
                      activity_at: this.activity_at,
                      freeze_days: this.freeze_days,
                  }
                  $.post(vm.url + `/backend/gamification/simulate/${this.id}/simulate-streak`, data, function (res) {
                      if (res.code === 200) {
                          vm.$message('Xong!');
                      }
                  });
              },
              pruneAll() {
                  const vm = this;
                  $.post(vm.url + `/backend/gamification/simulate/${this.id}/prune-all`, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã xoá hết');
                      }
                  });
              },
              prunePoint() {
                  const vm = this;
                  $.post(vm.url + `/backend/gamification/simulate/${this.id}/prune-point`, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã xoá điểm');
                      }
                  });
              },
              pruneStreak() {
                  const vm = this;
                  $.post(vm.url + `/backend/gamification/simulate/${this.id}/prune-streak`, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã xoá chuỗi');
                      }
                  });
              },
              pruneAchievement() {
                  const vm = this;
                  $.post(vm.url + `/backend/gamification/simulate/${this.id}/prune-achievement`, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã xoá thành tích');
                      }
                  });
              },
              pruneReward() {
                  const vm = this;
                  $.post(vm.url + `/backend/gamification/simulate/${this.id}/prune-reward`, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã xoá phần thưởng');
                      }
                  });
              },
              simulateNow() {
                  const vm = this;

                  $.post(vm.url + `/backend/gamification/simulate/${this.id}/simulate-now`, { now_time: this.now_time }, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã set thời gian test');
                      }
                  });
              },
              simulateLessonRules() {
                  const vm = this;
                  if (vm.loading.lessonRules) return;
                  Vue.set(vm.loading, 'lessonRules', true)

                  $.post(vm.url + `/backend/gamification/simulate/${this.courseId}/simulate-lesson-rules`, function (res) {
                      if (res.code === 200) {
                          vm.$message('Đã set điểm cho bài học');
                      }
                      Vue.set(vm.loading, 'lessonRules', false)
                  });
              },
          }
      })
  </script>
@stop
