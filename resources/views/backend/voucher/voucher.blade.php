@extends('backend._default.dashboard')

@section('description') Q<PERSON>ản lý voucher @stop
@section('keywords') voucher @stop
@section('author') dungmori.com @stop
@section('title') Admin | Voucher @stop

@section('assets')

    <link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />
    <script src="{{asset('plugin/select2-4.1.0/js/select2.min.js')}}"></script>
    <script src="{{asset('plugin/bootstrap-datepicker/js/bootstrap-datepicker.min.js')}}"></script>
    <link href="{{asset('plugin/bootstrap-datepicker/css/bootstrap-datepicker.min.css')}}" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .form-horizontal .form-group {margin-left: 22.5px; margin-right: -151.5px; margin-bottom: 25px; }
        .row{font-family: Myriad Pro,sans-serif; font-weight: 500;}
        .btn{padding: 4px 6px 2px 6px; border-radius: 3px; margin: 0 0 0 3px; font-size: 12px;}
        .label {padding: 4px 6px 4px 8px; margin-right: 4px; cursor: pointer; border-radius: 3px; }
        .label-success{background: #10a31a; }
        .label-danger, .btn-danger {background: #e74c3c; }
        .table{background-color: #fff;}
        .text-left{text-align: left;}
        .dropdown-toggle{height: 40px;}
    </style>
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Quản lý voucher
        </h4>
        @if(json_decode(Auth::guard('admin')->user()->matrix)->voucher->add != null)
            <button class="add-modal btn btn-success" style="float: right; padding: 6px; margin: 2px;">
                <span class="glyphicon glyphicon-plus"></span> Thêm
            </button>
        @endif
        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <input type="text" class="form-control mb-2 mr-sm-2" name="id" placeholder="Nhập Key">
            <select type="text" class="form-control mb-2 mr-sm-2" name="status">
                <option value="0">Chưa sử dụng</option>
                <option value="1">Đã sử dụng</option>
            </select>
            <select type="text" class="form-control mb-2 mr-sm-2" name="expired">
                <option value="0">Còn hạn</option>
                <option value="1">Hết hạn</option>
            </select>
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_from" id="date_from" placeholder="Từ ngày">
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_to" id="date_to" placeholder="Đến ngày">
            <a type="button" class="search btn btn-info mb-2">Tìm kiếm</a>
            <a href={{url('backend/voucher')}} class="btn btn-default mb-2">refresh</a>
            @if(json_decode(Auth::guard('admin')->user()->matrix)->voucher->export != null)
                <button type="button" class="export btn btn-info mb-2">Xuất Excel</button>
            @endif
        </form>
    </div>
    <div class="table_voucher">
        @include('backend.voucher.detailVoucher')
    </div>
    <div class="result"></div>

    <div id="pageModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form">
                         <li class="global_error text-left hidden"></li>
                        <input name="_token" type="hidden" value="{{ csrf_token() }}">
                        <div class="form-group maso">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-7">
                                <input type="text" class="form-control" id="fid">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="buyout_apply">Kiểu voucher</label>
                            <div class="col-sm-7">
                                <input type="radio" name="buyout_apply" value="lesson_course"> Khóa học
                                <input type="radio" name="buyout_apply" value="combo">  Combo
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="course">Áp dụng cho</label>
                            <div class="col-sm-5">
                                <select class="form-control" id="each_course" name="each_course" style="width: 100%">
                                    <option value=""></option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group amount">
                            <label class="control-label col-sm-2" for="amount">Số lượng voucher</label>
                            <div class="col-sm-4">
                                <input type="number" class="form-control" id="amount">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="expired">Ngày hết hạn </label>
                            <div class="col-sm-4">
                                <input type="date" class="form-control" id="expired">
                            </div>
                        </div>

                        <div class="form-group prefix">
                            <label class="control-label col-sm-2" for="prefix">Tiền tố voucher</label>
                            <div class="col-sm-3">
                                <input type="name" class="form-control" id="prefix">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="note">Ghi Chú</label>
                            <div class="col-sm-4">
                                <textarea type="text" class="form-control" id="note"></textarea>
                            </div>
                        </div>

                    </form>
                    <!--delete form-->
                    <div class="deleteContent"> Xác nhận xóa voucher "<span class="vname"></span>" này không ?
                            <span class="hidden id_voucher "> </span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript" src="{{asset('plugin/clientjs/client.min.js')}}"></script>

    <script type="text/javascript">
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $('#each_course').select2({dropdownParent: $('#pageModal')});
        });
    </script>

    <script type="text/javascript">
        var client = new ClientJS();
        $.getJSON("http://ip-api.com/json/?callback=?", function(response) {

            var data = {
                browserName: client.getBrowser(),
                browserVersion: client.getBrowserVersion(),
                osName: client.getOS(),
                osVersion: client.getOSVersion(),
                fingerprint: client.getFingerprint(),
                screenPrint: client.getScreenPrint(),
                plugins: client.getPlugins(),
                url: window.location.href,
                userIp: response.query,
                userNation: response.country,
                userCity: response.city,
            };

            // console.log(data);

            $.ajax({
                type: 'get',
                url: '/backend/course-active/l-a-a',
                data: data,
                success: function(response2) {
                    // console.log(response2);
                }
            });

        });
    </script>

    <script type="text/javascript">
     //datetime picker
    $('#date_from').datepicker({ dateFormat: 'dd/mm/yy' });
    $('#date_to').datepicker({ dateFormat: 'dd/mm/yy' });
    $(document).on('click', '.table_voucher .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page);
     });
     function readPage(page){
            $.ajax({
                url: '/backend/voucher/page?page=' + page
           }).done(function(data){
                $('.table_voucher').html(data)
                $('.result').empty()
           })
     }
     $(function(){
        $('input:radio').change(function(){
            getCourseApply(1);
        });
    });
     function getCourseApply(id){
        var buyout_apply = $('input[name=buyout_apply]:checked').val();
        $('#each_course').empty();
        $.ajax({
            type: 'post',
            url: '/backend/voucher/apply',
            data: {
                '_token': $('input[name=_token]').val(),
                'buyout_apply' : buyout_apply
            },
            success: function(data) {
                if (data.errors){
                    //chua xui li
                }else {
                    var option = [];

                    $.each(data, function(item, value) {
                        option += '<option value="'+ value.id + '">' + value.name + '</option>';
                    });

                    $('#each_course').append(option);
                    $('#each_course').val(id);
                    $('#each_course').trigger('change');
                }
            }
        });
     }
     //add
     $(document).on('click', '.add-modal', function(e) {
        $('.maso').hide();
        $('.amount').show();
        $('.prefix').show();
        $('#footer_action_button').text(" Thêm");
        $('#footer_action_button').addClass('glyphicon-check');
        $('#footer_action_button').removeClass('glyphicon-trash');
        $('.actionBtn').addClass('btn-success');
        $('.actionBtn').removeClass('btn-danger');
        $('.actionBtn').removeClass('delete');
        $('.actionBtn').removeClass('edit');
        $('.actionBtn').addClass('add');
        $('.modal-title').text('Thêm');
        $('.deleteContent').hide();
        $('.form-horizontal').show();
        fillmodalData('');
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '59%');
        $('#pageModal').css('left', '22%');
    });
    //edit
    $(document).on('click', '.edit-modal', function(e) {
        $('.maso').show();
        $('#fid').attr('disabled', true);
        $('.amount').hide();
        $('.prefix').hide();
        $('#footer_action_button').text(" Sửa");
        $('#footer_action_button').addClass('glyphicon-check');
        $('#footer_action_button').removeClass('glyphicon-trash');
        $('.actionBtn').addClass('btn-success');
        $('.actionBtn').removeClass('btn-danger');
        $('.actionBtn').removeClass('delete');
        $('.actionBtn').removeClass('add');
        $('.actionBtn').addClass('edit');
        $('.modal-title').text('Sửa');
        $('.deleteContent').hide();
        $('.form-horizontal').show();
        var voucher = $(this).data('info');
        fillmodalData(voucher);
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '59%');
        $('#pageModal').css('left', '22%');
    });
    //delete
    $(document).on('click', '.delete-modal', function() {
        $('#footer_action_button').text(" Xóa");
        $('.modal-title').text('Xóa');
        $('#footer_action_button').removeClass('glyphicon-check');
        $('#footer_action_button').addClass('glyphicon-trash');
        $('.actionBtn').removeClass('btn-success');
        $('.actionBtn').addClass('btn-danger');
        $('.actionBtn').removeClass('edit');
        $('.actionBtn').removeClass('add');
        $('.actionBtn').addClass('delete');
        $('.deleteContent').show();
        $('.form-horizontal').hide();
        var voucher =  $(this).data('info');
        $('.id_voucher').text(voucher.id);
        $('.vname').html(voucher.key);
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '28%');
        $('#pageModal').css('left', '37%');
    });
    //
    function fillmodalData(voucher){
        if(voucher == ''){
            $('input[name="buyout_apply"][value="lesson_course"]').prop('checked', true);
            getCourseApply(1);
            $('#expired').val(null);
            $('#note').val('');
            $('.global_error').addClass('hidden');
        }else{
            var setting = JSON.parse(voucher.setting);
            $('#fid').val(voucher.id);
            $('#type').val(voucher.type);
            if(setting.course_id != ''){
                $('input[name="buyout_apply"][value="lesson_course"]').prop('checked', true);
                getCourseApply(setting.course_id);
            }else{
                $('input[name="buyout_apply"][value="combo"]').prop('checked', true);
                 getCourseApply(setting.combo_id);
            }
            $('#expired').val(voucher.expired_day);
            $('#note').val(voucher.comment);
            $('.global_error').addClass('hidden');
        }
    }
    //add
    $('.modal-footer').on('click', '.add', function() {
        $.ajax({
            type: 'post',
            url: '/backend/voucher/add',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $("#fid").val(),
                'type': $('#type').val(),
                'buyout_apply': $('input[name=buyout_apply]:checked').val(),
                'amount' : $('#amount').val(),
                'prefix' : $('#prefix').val(),
                'each_course': $('#each_course').val(),
                'expired': $('#expired').val(),
                'note': $('#note').val()
            },
            success: function(data) {
                if (data.errors){
                    if(Object.keys(data.errors).length > 0){
                        $('#pageModal').modal('show');
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Dữ liệu còn trống !");
                    }
                }
                else {
                    $('#pageModal').modal('hide');
                    $('.table_voucher').html(data);
                    $('.result').empty()
                }
            }
        });
    });
    //edit
    $('.modal-footer').on('click', '.edit', function() {
        $.ajax({
            type: 'post',
            url: '/backend/voucher/edit',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $("#fid").val(),
                'type': $('#type').val(),
                'buyout_apply': $('input[name=buyout_apply]:checked').val(),
                'each_course': $('#each_course').val(),
                'expired': $('#expired').val(),
                'note': $('#note').val(),
                'page' : $(".pagination .active span").html()
            },
            success: function(data) {
                if (data.errors){
                   if(Object.keys(data.errors).length > 0){
                        $('#pageModal').modal('show');
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Dữ liệu còn trống !");
                    }
                }
                else {
                    $('#pageModal').modal('hide');
                    // $('.table_voucher').html(data);
                    // $('.result').empty()
                    callBack(data);
                }
            }
        });
    });
    //delete
    $('.modal-footer').on('click', '.delete', function() {
        $.ajax({
            type: 'post',
            url: '/backend/voucher/delete',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $('.id_voucher').text(),
            },
            success: function(data) {
                $('#pageModal').modal('hide');
                $('.item' + $('.id_voucher').text()).remove();
            }
        });
    });
    //tim kiem
    $(document).on('click', '.search', function() {
        callBack(1);
    });

    $(document).keypress(function(e) {
        if (e.which == 13) {
            callBack();
            e.preventDefault();
        }
    });

    function callBack(page){
        $.ajax({
            type: 'get',
            url: '/backend/voucher/find',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $('input[name=id]').val(),
                'status': $('select[name=status]').val(),
                'expired': $('select[name=expired]').val(),
                'date_from': $('input[name=date_from]').val(),
                'date_to': $('input[name=date_to]').val(),
                'page': page
            },
            success: function(data) {
                $('.table_voucher').empty();
                $('.result').empty().html(data);
                $(document).on('click', '.result .pagination a', function(e) {
                    var date_from = $('input[name=date_from]').val();
                    var date_to =  $('input[name=date_to]').val();
                    var expired = $('select[name=expired]').val();
                    var status = $('select[name=status]').val();
                    e.preventDefault();
                    var page = $(this).attr('href').split('page=')[1];
                    resultPage(page,'/backend/voucher/find/?page=', '.result', date_from, date_to, expired, status);
                });
            }
        });
    }

    function resultPage(page,link, div_id, date_from, date_to, expired, status ){
        $.ajax({
            type: 'get',
            url: link + page,
            cache: false,
            data: {
                date_from : date_from,
                date_to : date_to,
                expired : expired,
                status : status
            }
        }).done(function(data){
            $(div_id).empty().html(data)
        })
    }
      $(document).on('click', '.export', function() {
            $.ajax({
                type: 'get',
                url: '/backend/voucher/export',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'status': $('select[name=status]').val(),
                    'expired': $('select[name=expired]').val(),
                    'date_from': $('input[name=date_from]').val(),
                    'date_to': $('input[name=date_to]').val(),
                },
                success: function(data) {
                    window.location = '/storage/upload/excel/voucher.xlsx';
                }
            });
        });

</script>

@stop
