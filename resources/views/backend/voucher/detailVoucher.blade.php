<table class="table table-borderless" id="table">
	<thead>
		<tr>
			<th class="text-center"><PERSON><PERSON></th>
			<th class="text-center" style="width: 60px;"><PERSON><PERSON><PERSON></th>
			<th class="text-center"><PERSON><PERSON> Key</th>
            <th class="text-center"><PERSON><PERSON> chú</th>
			<th class="text-center">Trạng thái</th>
            <th class="text-center"><PERSON><PERSON><PERSON> hết hạn</th>
            <th class="text-center">Ng<PERSON>y tạo</th>
			<th class="text-center">Hành động</th>
		</tr>
	</thead>
    <tbody>
		@foreach($voucher as $item)
    		<tr class="item{{$item->id}}">
    			<td class="text-center">{{$item->id}}</td>
                <td class="text-center">
                    @if(strpos($item->setting, 'combo_id":""') !== false) 
                        Course
                    @else
                        Combo
                    @endif
                </td>
    			<td class="text-center">{{$item->key}}</td>
                <th class="text-center">{{$item->comment}}</th>
                <td class="text-center">
                    @if($item->status == 0)
                        <span class="label label-success">New</span>
                    @else
                        <span class="label label-danger">Đã dùng</span>
                    @endif
                </td>
    			<td class="text-center">
                    @if($item->expired_day < date('Y-m-d'))
                        <spam class="label label-danger">Hết hạn</spam> {{ date_format( date_create($item->expired_day),"d/m/Y") }}
                    @else
                        <spam class="label label-success">Còn hạn</spam> {{ date_format( date_create($item->expired_day),"d/m/Y") }}
                    @endif
                </td>
                <td class="text-center">{{$item->created_at}}</td>
    			<td class="text-center">
                    @if(json_decode(Auth::guard('admin')->user()->matrix)->voucher->edit != null)
        				<button class="edit-modal btn btn-info"
        					data-info="{{$item}}">
        					<span class="glyphicon glyphicon-edit"></span> Sửa
        				</button>
                    @endif

                    {{-- @if(json_decode(Auth::guard('admin')->user()->matrix)->voucher->delete != null)
        				<button class="delete-modal btn btn-danger"
        					data-info="{{$item}}">
        					<span class="glyphicon glyphicon-trash"></span> Xóa
        				</button>
                    @endif --}}
    			</td>
    		</tr>
		@endforeach
    </tbody>
</table>
{{ $voucher->render() }}