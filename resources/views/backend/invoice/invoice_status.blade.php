<table class="table table-borderless">
    <thead>
        <tr>
            <th class="text-center"> <PERSON><PERSON> số </th>
            <th class="text-center" style="text-align: left;"><PERSON><PERSON>n phẩm</th>
            <th class="text-center"><PERSON><PERSON><PERSON> thức TT</th>
            <th class="text-center">Th<PERSON><PERSON> gian</th>
            <th class="text-center"><PERSON><PERSON><PERSON><PERSON> hàng</th>
            <th class="text-center">Gói Plus</th>
            <th class="text-center">Mã giảm giá</th>
            <th class="text-center">Trạng thái</th>
            <th class="text-center">Số tiền</th>
{{--            <th class="text-center">Thực nhận</th>--}}
{{--            <th class="text-center">Ảnh hóa đơn</th>--}}
            <th class="text-center">Hành động</th>
        </tr>
    </thead>
    <tbody>
        @foreach($invoice as $item)
            <tr class="item{{$item->id}}">
                <td class="text-center">{{$item->id}}</td>
                <td class="text-center" style="text-align: left;">
                    <b>
                    @if($item->product_type == "course")
                        Khóa học
                    @else
                        Combo
                    @endif
                    </b>
                    {{$item->product_name}}<br/>
                    <a href="{{ url('checkout') }}/{{$item->uuid}}" target="_blank" ><b>{{$item->uuid}}</b></a>
                </td>
                <td class="text-center" style="width: 150px; text-align: left;">

                    @if($item->payment_method_id == '5')
                        <img src="{{url('/assets/img/jp.gif')}}" style="border: 1px solid #CCC;">
                    @elseif($item->payment_method_id == '6')
                        <i class="fa fa-mobile" aria-hidden="true"></i>
                    @else
                        <img src="{{url('/assets/img/vn.gif')}}">
                    @endif

                    @if($item->payment_method_id == '1')
                        TT paypal <br>
                    @elseif(in_array($item->payment_method_id, ['2', '7']))
                        CKNH Việt Nam <br>
                    @elseif($item->payment_method_id == '3')
                        Nộp tại VP <br>
                    @elseif($item->payment_method_id == '4')
                        Ship mã thẻ
                        @if($item->invoice_status == 'completed')
                            <i class="fa fa-credit-card" aria-hidden="true"></i>
                            <br><span style="font-size: 12px;">{{ $item->voucher_key }}</span>
                        @endif
                    @elseif(in_array($item->payment_method_id, ['5', '8']))
                    CKNH tại Nhật <br>
                    @elseif($item->payment_method_id == '6')
                    In app purchase <br>
                    @else
                       ---Chưa chọn--- <br>
                    @endif
                </td>
                <td class="text-center" style="font-size: 13px; line-height: 1.1;">
                    &nbsp;&nbsp;Tạo: {{ $item->getFriendlyTime() }}<br/>

                    @if($item->admin_active_name != null)
                        Active: {{friendlyTime($item->active_time)}} <br/>
                        <span style="font-size: 11px;">Admin: {{$item->admin_active_name}}</span>
                    @endif

                </td>
                <td class="text-center">
                    {{$item->user_id}} • <b>{!! (is_null($item->user_id) && isset(json_decode($item->info_contact)->guestName)) ? json_decode($item->info_contact)->guestName : json_decode($item->info_contact)->name !!}</b> • {!! (is_null($item->user_id) && isset(json_decode($item->info_contact)->guestName)) ? json_decode($item->info_contact)->guestPhone : json_decode($item->info_contact)->phone !!} <br>
                    {!! (is_null($item->user_id) && isset(json_decode($item->info_contact)->guestEmail)) ? json_decode($item->info_contact)->guestEmail : $item->user['email'] !!}
                    &nbsp;@if (!is_null($item->user_id))
                        <a class="login_user label label-success" style="padding: 1px 4px 2px 4px; border-radius: 3px; background: #0F4C82;"
                           href='{{url('/backend/user/login/'. $item->user_id)}}' target="_blank">Đăng nhập
                        </a>
                    @endif
                </td>
                <td class="text-center">
                    @if($item->extra_days)
                        @if($item->extra_days >= 30)
                            {{round(( $item->extra_days / 30))}} tháng
                        @else
                            {{$item->extra_days}} ngày
                        @endif
                            <br>
                    @endif
                    @if($item->extra_price)
                            {{number_format($item->extra_price)}}₫
                        @endif
                </td>
                <td class="text-center">
                    @if (isset($item->coupon))
                        <div>
                            Giảm {{ $item->coupon->value}}% - {{ $item->coupon->code}}</div>
                        <div>{{ $item->coupon->email }}</div>
                    @else
                        <div>--</div>
                    @endif

                </td>
                <td class="text-center">
                    @if($item->invoice_status == 'completed')
                        <span class="label new-label green-label" >Hoàn thành</span>
                    @elseif($item->invoice_status == 'new')
                        <span class="label new-label orange-label">Chờ xử lý...</span>
                    @else
                        <span class="label new-label red-label">Đã hủy</span>
                    @endif

                </td>
                <td class="text-center">
                    {{ number_format($item->price) }}₫
                </td>
{{--                <td class="text-center">--}}
{{--                    ------------}}
{{--                </td>--}}
{{--                <td class="text-center">--}}
{{--                    ------------}}
{{--                </td>--}}
                <td class="text-center">
                    @if($item->invoice_status == 'new')
                        @if (isset($item->product->type) && $item->product->type == 'book')
                            <a class="label new-label green-label" onclick="activeInvoice({{$item->id}}, 'book')">
                                Kích hoạt sách<i class="fa fa-caret-right" aria-hidden="true"></i>
                            </a>
                        @else
                            <a class="label new-label green-label" onclick="activeInvoice({{$item->id}})">
                                Kích hoạt <i class="fa fa-caret-right" aria-hidden="true"></i>
                            </a>
                        @endif
                        <a class="label" style="color: red;" onclick="cancelInvoice({{$item->id}})">Hủy</a>
                    @else
                        ---
                    @endif
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
{{ $invoice->render() }}

<script type="text/javascript">

    $(document).ready(function() {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    });

    function activeInvoice(id, type = 'course'){

        var r = confirm("Bạn có chắc chắn muốn kích hoạt đơn hàng này ???");
        if (r == true){
            $.ajax({
                type: 'post',
                url: '/backend/invoice/do-active',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': id,
                    'type': type,
                },
                success: function(data) {
                    if(data == 'success'){
                        alert('Kích hoạt thành công');
                        $(".item"+ id).fadeOut();
                    }else alert('Kích hoạt lỗi');
                }
            });
        }
    }

    function cancelInvoice(id){

        var r = confirm("Bạn có chắc chắn muốn hủy đơn hàng này ?");
        if (r == true) {

            $.ajax({
                type: 'post',
                url: '/backend/invoice/do-cancel',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': id
                },
                success: function(data) {
                    if(data == 'success') {
                        $(".item"+ id).fadeOut();
                    }
                    else alert('Không hủy được');
                }
            });
        }
    }

</script>
