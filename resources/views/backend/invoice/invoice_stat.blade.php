@extends('backend._default.dashboard')

@section('description') Thống kê đơn hàng @stop
@section('keywords') invoice @stop
@section('author') dungmori.com @stop
@section('title') Admin | Thống kê đơn hàng @stop

@section('assets')
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="//unpkg.com/element-ui/lib/umd/locale/vi.js"></script>
    <script type="text/javascript" src="https://cdn.sheetjs.com/xlsx-0.20.3/package/dist/xlsx.full.min.js"></script>
    <script>
        ELEMENT.locale(ELEMENT.lang.vi)
    </script>
@stop

@section('content')
    <div id="invoice-stat-screen">
        <div class="mt-5 w-full flex items-center gap-3">
            <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="-"
                start-placeholder="Ngày bắt đầu"
                end-placeholder="Ngày kết thúc"
                format="dd/MM/yyyy"
                value-format="yyyy-MM-dd"
                @change="fetchInvoices"
            >
            </el-date-picker>
            <el-checkbox-group 
                v-model="showTypes"
                @change="fetchInvoices"
            >
                <el-checkbox v-for="item in types" :label="item.value" :key="item.value" :value="item.value">@{{item.label}}</el-checkbox>
            </el-checkbox-group>
            <!-- <el-select v-model="type" placeholder="Loại đơn hàng" clearable @change="fetchInvoices">
                <el-option
                    v-for="item in types"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
            <el-select v-model="paymentMethod" placeholder="Hình thức thanh toán" clearable @change="fetchInvoices">
                <el-option
                    v-for="item in paymentMethods"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id">
                </el-option>
            </el-select> -->
        </div>

        <div class="mt-5">
            <i
                class="el-icon-document-copy cursor-pointer hover:text-blue-500"
                @click="copyText"
            ></i>
        </div>
        <div>
            <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">Tổng doanh thu theo từng đơn chi tiết các sản phẩm</div>
            <el-table :data="revenueDetails" class="mt-5" id="table" border="true">
                <el-table-column prop="label" label="" width="150"></el-table-column>
                <el-table-column v-for="group in filteredProductGroups" :label="group.label" :key="group.value">
                    <template v-if="group.products">
                        <el-table-column
                            v-for="product in group.products"
                            :label="product.label"
                            width="150">
                            <template #default="{ row, $index }">
                                <span v-if="$index < 4">@{{ row[`${product.id.join(',')}`] ? row[`${product.id.join(',')}`].toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0 }}</span>
                                <span v-if="$index === 4">@{{ row[`${product.id.join(',')}`] }}%</span>
                                <span v-if="$index === 5">@{{row[`${product.id.join(',')}`] ? row[`${product.id.join(',')}`].toLocaleString('ja-JP', { style: 'currency', currency: 'JPY' }) : 0}}</span>
                            </template>
                        </el-table-column>
                    </template>
                </el-table-column>
                <el-table-column prop="total" label="Tổng" width="150">
                    <template #default="{ row, $index }">
                        <span v-if="$index < 4">@{{ row.total ? row.total.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0 }}</span>
                        <span v-if="$index === 4">@{{ row.total }}%</span>
                        <span v-if="$index === 5">@{{ row.total ? row.total.toLocaleString('ja-JP', { style: 'currency', currency: 'JPY' }) : 0 }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">Tổng số đơn  chi tiết các sản phẩm</div>
            <el-table :data="countDetails" class="mt-5">
                <el-table-column prop="label" label="" width="150"></el-table-column>
                <el-table-column v-for="group in filteredProductGroups" :label="group.label" :key="group.value">
                    <template v-if="group.products">
                        <el-table-column
                            v-for="product in group.products"
                            :label="product.label"
                            width="150">
                            <template #default="{ row, $index }">
                                <span v-if="$index === 4">@{{ row[`${product.id.join(',')}`] }}%</span>
                                <span v-else>@{{ row[`${product.id.join(',')}`] || 0 }}</span>
                            </template>
                        </el-table-column>
                    </template>
                </el-table-column>
                <el-table-column prop="total" label="Tổng" width="150">
                    <template #default="{ row, $index }">
                        <span v-if="$index === 4">@{{ row.total }}%</span>
                        <span v-else>@{{ row.total || 0 }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">Tổng doanh thu theo từng đơn theo nhóm </div>
            <el-table :data="revenueDetails" class="mt-5">
                <el-table-column prop="label" label=""></el-table-column>
                <el-table-column v-for="group in filteredProductGroups" :label="group.label" :key="group.value">
                    <template #default="{ row, $index }">
                        <span v-if="$index < 4">@{{ row[`${group.value}`] ? row[`${group.value}`].toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0 }}</span>
                        <span v-if="$index === 4">@{{ row[`${group.value}`] }}%</span>
                        <span v-if="$index === 5">@{{row[`${group.value}`] ? row[`${group.value}`].toLocaleString('ja-JP', { style: 'currency', currency: 'JPY' }) : 0}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="total" label="Tổng" width="150">
                    <template #default="{ row, $index }">
                        <span v-if="$index < 4">@{{ row.total ? row.total.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0 }}</span>
                        <span v-if="$index === 4">@{{ row.total }}%</span>
                        <span v-if="$index === 5">@{{ row.total ? row.total.toLocaleString('ja-JP', { style: 'currency', currency: 'JPY' }) : 0 }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">Tổng số đơn</div>
            <el-table :data="countDetails" class="mt-5">
                <el-table-column prop="label" label=""></el-table-column>
                <el-table-column v-for="group in filteredProductGroups" :label="group.label" :key="group.value">
                    <template #default="{ row, $index }">
                        <span v-if="$index === 4">@{{ row[`${group.value}`] }}%</span>
                        <span v-else>@{{ row[`${group.value}`] || 0 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="total" label="Tổng" width="150">
                    <template #default="{ row, $index }">
                        <span v-if="$index === 4">@{{ row.total }}%</span>
                        <span v-else>@{{ row.total || 0 }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        
        <!-- <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">TỔNG HỢP DOANH THU ONLINE THEO NHÓM SẢN PHẦM</div>
        <el-table class="mt-5">
            <el-table-column prop="id" label="ID"></el-table-column>
            <el-table-column prop="product_id" label="Mã sản phẩm"></el-table-column>
            <el-table-column prop="product_type" label="Loại sản phẩm"></el-table-column>
            <el-table-column prop="payment_method_id" label="Hình thức thanh toán"></el-table-column>
        </el-table> -->
        <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">CHI TIẾT DOANH THU OFFLINE</div>
        <el-table :data="offlineDetails" class="mt-5">
            <el-table-column prop="label" label="" width="150"></el-table-column>
            <el-table-column v-for="group in offlineGroups" :label="group.label" :key="group.value">
                <template v-if="group.products">
                    <el-table-column
                        v-for="product in group.products"
                        :label="product.label"
                        width="150">
                        <template #default="{ row, $index }">
                            <span v-if="$index === 6">@{{ row[`_${product.id.join(',')}`] ? row[`_${product.id.join(',')}`].toLocaleString('a-JP', { style: 'currency', currency: 'JPY' }) : 0 }}</span>
                            <span v-else>@{{ row[`_${product.id.join(',')}`] ? row[`_${product.id.join(',')}`].toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0 }}</span>

                        </template>
                    </el-table-column>
                </template>
            </el-table-column>
            <el-table-column prop="total" label="Tổng" width="150">
                <template #default="{ row, $index }">
                    <span v-if="$index === 6">@{{ row.total ? row.total.toLocaleString('ja-JP', { style: 'currency', currency: 'JPY' }) : 0 }}</span>
                    <span v-else>@{{ row.total ? row.total.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : 0 }}</span>
                </template>
            </el-table-column>
        </el-table>

        <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">CHI TIẾT SỐ ĐƠN OFFLINE</div>
        <el-table :data="offlineCountDetails" class="mt-5">
            <el-table-column prop="label" label="" width="150"></el-table-column>
            <el-table-column v-for="group in offlineGroups" :label="group.label" :key="group.value">
                <template v-if="group.products">
                    <el-table-column
                        v-for="product in group.products"
                        :label="product.label"
                        width="150">
                        <template #default="{ row, $index }">
                            <span>@{{ row[`_${product.id.join(',')}`] || 0 }}</span>
                        </template>
                    </el-table-column>
                </template>
            </el-table-column>
            <el-table-column prop="total" label="Tổng" width="150">
                <template #default="{ row, $index }">
                    <span>@{{ row.total || 0 }}</span>
                </template>
            </el-table-column>
        </el-table>


        <div>
            <div class="text-bold mt-5 font-montserrat h3 text-left uppercase">Đơn hàng cần kiểm tra lại</div>
            <el-table :data="fraudInvoices" class="mt-5">
                <el-table-column prop="id" label="ID" width="120"></el-table-column>
                <el-table-column prop="product_name" label="Tên sản phẩm"></el-table-column>
                <el-table-column prop="payment_method.name" label="Hình thức thanh toán"></el-table-column>
                <el-table-column prop="currency" label="Tiền tệ"></el-table-column>
                <el-table-column prop="paid_money" label="Số tiền đã thanh toán"></el-table-column>
            </el-table>
        </div>
    </div>


    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    <script type="text/javascript">
        const paymentMethods = @json($paymentMethods);
    </script>
    <script>
        new Vue({
            el: '#invoice-stat-screen',
            data: {
                moment: moment(),
                invoices: [],
                offlineInvoices: [],
                loading: true,
                error: null,
                dateRange: [],
                showTypes: ['combo'],
                type: 'combo',
                types: [
                    { value: 'vip_combo', label: 'VIP' },
                    { value: 'combo', label: 'Online' },
                    // { value: 'offline', label: 'Offline' },
                    { value: 'book', label: 'Sách' }
                ],
                paymentMethod: 'cknhvn',
                paymentMethods: [
                    { id: 'cknhvn', label: 'CKNHVN' },
                    { id: 'tt', label: 'TT' },
                    { id: 'cknb', label: 'CKNB' }
                ],
                invoiceDetails: {
                    cknhvn: {},
                    tt: {},
                    cknb: {},
                    jpy: {},
                    count: {}
                },
                revenueDetails: [
                    { type: 'cknhvn', label: 'CKNHVN', total: 0 },
                    { type: 'tt', label: 'TT', total: 0 },
                    { type: 'cknb', label: 'CKNB', total: 0 },
                    { type: 'count', label: 'Tổng', total: 0 },
                    { type: 'ratio', label: 'Tỷ lệ', total: 100 },
                    { type: 'jpy', label: 'Doanh thu tiền Nhật', total: 0 },
                ],
                countDetails: [
                    { type: 'cknhvn', label: 'CKNHVN', total: 0 },
                    { type: 'tt', label: 'TT', total: 0 },
                    { type: 'cknb', label: 'CKNB', total: 0 },
                    { type: 'count', label: 'Tổng', total: 0 },
                    { type: 'ratio', label: 'Tỷ lệ', total: 100 },
                ],
                offlineDetails: [
                    { type: 'nt', label: 'Nguyễn Trãi', total: 0 },
                    { type: 'hqv', label: 'Hoàng Quốc Việt', total: 0 },
                    { type: 'q1', label: 'Quận 1', total: 0 },
                    { type: 'hd', label: 'Hà Đông', total: 0 },
                    { type: 'gv', label: 'Gò Vấp', total: 0 },
                    { type: 'count', label: 'Tổng', total: 0 },
                    { type: 'jpy', label: 'Doanh thu tiền Nhật', total: 0 },

                ],
                offlineCountDetails: [
                    { type: 'nt', label: 'Nguyễn Trãi', total: 0 },
                    { type: 'hqv', label: 'Hoàng Quốc Việt', total: 0 },
                    { type: 'q1', label: 'Quận 1', total: 0 },
                    { type: 'hd', label: 'Hà Đông', total: 0 },
                    { type: 'gv', label: 'Gò Vấp', total: 0 },
                    { type: 'count', label: 'Tổng', total: 0 },
                ],
                productGroups: [
                    { type: 'book', value: 'book', label: 'Sách', products: [
                        { id: [9990], label: 'Sách' },
                    ]},
                    { type: 'offline', value: 'offline', label: 'Offline', products: [
                        {id: [88], label: 'N5'},
                        {id: [89], label: 'N4'},
                        {id: [90], label: 'N3'},
                        {id: [94], label: 'N2'},
                        {id: [95], label: 'N5+N4'},
                        {id: [96], label: 'N4+N3'},
                        {id: [98], label: 'N3+N2'},
                        {id: [100], label: 'N5+N4+N3'},
                        {id: [102], label: 'N4+N3+N2'},
                        {id: [103], label: 'N5+N4+N3+N2'}
                    ]},
                    { type: 'combo', value: 'basic', label: 'Basic', products: [
                        {id: [2,281,275,255], label: 'N5'},
                        {id: [1,282,256], label: 'N4'},
                        {id: [3], label: 'N3'},
                        {id: [19], label: 'N2'},
                        {id: [29], label: 'N1'},
                        {id: [83,162,77,78,76,79,264,276,265,277,266,278,267,279,268,280], label: 'Gia hạn N5'},
                        {id: [84, 82, 161, 107, 24, 26, 108, 208, 269, 283, 270, 284, 271, 285, 272, 286, 273, 287], label: 'Gia hạn N4'},
                        {id: [85, 215, 246, 236, 160, 109, 216, 110, 217, 11, 218, 111, 219, 240, 241, 47], label: 'Gia hạn N3'},
                        {id: [86, 220, 247, 235, 159, 112, 221, 113, 222, 114, 223, 115, 224, 242, 243], label: 'Gia hạn N2'},
                        {id: [81, 87, 225, 248, 158, 116, 226, 117, 227, 118, 228, 119, 229, 244, 207, 245], label: 'Gia hạn N1'},
                        {id: [257, 4], label: 'N5+N4'},
                        {id: [261, 178], label: 'N4+N3'},
                        {id: [20, 179], label: 'N3+N2'},
                        {id: [33, 177], label: 'N2+N1'},
                        {id: [262, 184], label: 'N4+N3+N2'},
                        {id: [263, 181], label: 'N4+N3+N2+N1'},
                        {id: [34, 180], label: 'N3+N2+N1'},
                        {id: [258, 182], label: 'N5+N4+N3'},
                        {id: [259, 185], label: 'N5+N4+N3+N2'},
                        {id: [260, 183], label: 'N5+N4+N3+N2+N1'},
                    ]},
                    { type: 'vip_combo', value: 'plus', label: 'PLUS', products: [
                        {id: [123], label: 'N5 Plus'},
                        {id: [124], label: 'N4 Plus'},
                        {id: [65], label: 'N3 Plus'},
                        {id: [66], label: 'N2 Plus'},
                        {id: [67], label: 'N1 Plus'},
                        {id: [125], label: 'N5+N4 Plus'},
                        {id: [126], label: 'N4+N3 Plus'},
                        {id: [69], label: 'N3+N2 Plus'},
                        {id: [68], label: 'N2+N1 Plus'},
                        {id: [70], label: 'N3+N2+N1 Plus'},
                    ]},
                    { type: 'combo', value: 'ld_online', label: 'Luyện đề online', products: [
                        {id: [274], label: 'N5 LĐ online'},
                        {id: [57], label: 'N4 LĐ online'},
                        {id: [58], label: 'N3 LĐ online'},
                        {id: [59], label: 'N2 LĐ online'},
                        {id: [186], label: 'N1 LĐ online'},
                    ]},
                    { type: 'vip_combo', value: 'ld_livestream', label: 'Luyện đề livestream', products: [
                        {id: [121], label: 'N5 LĐ vip'},
                        {id: [122], label: 'N4 LĐ vip'},
                        {id: [25], label: 'N3 LĐ vip'},
                        {id: [26], label: 'N2 LĐ vip'},
                        {id: [27], label: 'N1 LĐ vip'},
                    ]},
                    { type: 'vip_combo', value: 'tokutei', label: 'Tokutei', products: [
                        {id: [120], label: 'Tokutei'},
                    ]},
                    { type: 'vip_combo', value: 'vip15', label: 'Vip 15', products: [
                        {id: [47], label: 'VIP15 N5 cơ bản'},
                        {id: [42], label: 'VIP15 N4'},
                        {id: [40], label: 'VIP15 N3'},
                        {id: [37], label: 'VIP15 N2'},
                        {id: [79], label: 'VIP15 N1'},
                        {id: [48], label: 'VIP15 N5 + N4 (Cơ bản)'},
                        {id: [41], label: 'VIP15 N4 + N3'},
                        {id: [38], label: 'VIP15 N3 + N2'},
                        {id: [78], label: 'VIP15 N2 + N1'},
                        {id: [49], label: 'VIP15 N5 + N4 + N3 (Cơ bản)'},
                        {id: [50], label: 'VIP15 N5 + N4 + N3 + N2 (Cơ bản)'},
                        {id: [39], label: 'VIP15 N4 + N3 + N2'},
                        {id: [113], label: 'VIP 15 N3+N2+N1'},
                        {id: [114], label: 'VIP15 N3 (73 buổi)'},
                        {id: [115], label: 'VIP15 N2 (73 buổi)'},
                    ]},
                    { type: 'vip_combo', value: 'vip9', label: 'Vip 9', products: [
                        {id: [51], label: 'VIP9 N5'},
                        {id: [52], label: 'VIP9 N4'},
                        {id: [53], label: 'VIP9 N3'},
                        {id: [54], label: 'VIP9 N2'},
                    ]},
                    { type: 'vip_combo', value: 'vip1', label: 'Vip 1:1', products: [
                        {id: [9999], label: 'VIP9 N5'},
                        {id: [9998], label: 'VIP9 N4'},
                        {id: [9997], label: 'VIP9 N3'},
                        {id: [9996], label: 'VIP9 N2'},
                    ]},
                    { type: 'vip_combo', value: 'captoc', label: 'Cấp tốc', products: [
                        {id: [117], label: 'N5CT'},
                        {id: [110], label: 'N4CT'},
                        {id: [111], label: 'N3CT'},
                        {id: [112], label: 'N2CT'},
                        {id: [118], label: 'N4+N5 CT'},
                        {id: [119], label: 'N4+N3 CT'},
                    ]},
                    { type: 'vip_combo', value: 'vip500', label: 'Vip 500', products: [
                        {id: [30], label: 'VIP500 N1'},
                        {id: [36], label: 'VIP500 + Online N1'},
                        {id: [75], label: 'VIP500 Nghe Đọc Hiểu N1'},
                    ]},
                    { type: 'vip_combo', value: 'tongon', label: 'Tổng ôn', products: [
                        {id: [108], label: 'N4'},
                        {id: [107], label: 'N5'},
                    ]},
                    { type: 'combo', value: 'kaiwa_online', label: 'Kaiwa online', products: [
                        {id: [55], label: 'Kaiwa Sơ Cấp'},
                        {id: [64], label: 'Kaiwa Trung Cấp'},
                    ]},
                    { type: 'vip_combo', value: 'kaiwa_vip', label: 'Kaiwa Vip', products: [
                        {id: [64], label: 'Kaiwa Cơ Bản 1:4'},
                        {id: [63], label: 'Kaiwa Cơ Bản 1:2'},
                        {id: [71], label: 'Kaiwa Cơ Bản 1:1'},
                        {id: [72,80], label: 'Kaiwa Sơ cấp 1:4'},
                        {id: [73,104], label: 'Kaiwa Sơ cấp 1:2'},
                        {id: [74], label: 'Kaiwa Sơ cấp 1:1'},
                        {id: [81], label: 'Kaiwa Trung Cấp 1 - 1:4'},
                        {id: [82], label: 'Kaiwa Trung Cấp 1 - 1:2'},
                        {id: [83], label: 'Kaiwa Trung Cấp 1 - 1:1'},
                        {id: [84], label: 'Kaiwa Trung Cấp 2 - 1:4'},
                        {id: [85], label: 'Kaiwa Trung Cấp 2 - 1:2'},
                        {id: [86], label: 'Kaiwa Trung Cấp 2 - 1:1'},
                    ]},
                    { type: 'combo', value: 'eju', label: 'EJU Tiếng Nhật', products: [
                        {id: [7], label: 'EJU Tiếng Nhật'},
                        {id: [48, 89], label: 'EJU - Toán'},
                        {id: [49], label: 'EJU - XHTH'},
                        {id: [91], label: 'EJU Vật lý'},
                        {id: [90], label: 'EJU Hóa học'},
                        {id: [52], label: 'EJU TN - XHTH'},
                        {id: [51], label: 'EJU TN - Toán'},
                        {id: [50], label: 'EJU Toán + XHTH'},
                        {id: [53], label: 'EJU TN - XHTH - Toán'},
                        {id: [97], label: 'EJU Tiếng Nhật + Lý'},
                        {id: [98], label: 'EJU Tiếng Nhật + Hóa'},
                        {id: [102], label: 'EJU TN + Toán 2 + Lý + Hóa'},
                        {id: [101], label: 'EJU Tiếng Nhật + Lý + Hóa'},
                    ]},
                ],
            },
            computed: {
                start() {
                    return this.dateRange.length > 0 ? this.dateRange[0] : null;
                },
                end() {
                    return this.dateRange.length > 0 ? this.dateRange[1] : null;
                },
                filteredProductGroups() {
                    return this.productGroups.filter(group => this.showTypes.includes(group.type));
                },
                offlineGroups() {
                    return this.productGroups.filter(group => ['offline', 'book'].includes(group.type));
                },
                fraudInvoices() {
                    return this.invoices.filter(invoice => invoice.fraud);
                }
            },
            created() {
                const now = moment;
                this.dateRange = [
                    moment().startOf('month').format('YYYY-MM-DD'),
                    moment().endOf('month').format('YYYY-MM-DD')
                ];
                // this.dateRange = ['2024-10-01', '2024-10-31'];
                this.fetchInvoices();
            },
            methods: {
                copyText() {
                    var el = document.getElementById("table");

                    var body = document.body,
                        range,
                        sel;

                    if (document.createRange && window.getSelection) {
                        range = document.createRange();

                        sel = window.getSelection();

                        sel.removeAllRanges();

                        try {
                        range.selectNodeContents(el);

                        sel.addRange(range);
                        } catch (e) {
                        range.selectNode(el);

                        sel.addRange(range);
                        }
                    } else if (body.createTextRange) {
                        range = body.createTextRange();

                        range.moveToElementText(el);

                        range.select();
                    }

                    document.execCommand("Copy");
                },
                resetDetails() {
                    this.revenueDetails = [
                        { type: 'cknhvn', label: 'CKNHVN', total: 0 },
                        { type: 'tt', label: 'TT', total: 0 },
                        { type: 'cknb', label: 'CKNB', total: 0 },
                        { type: 'count', label: 'Tổng', total: 0 },
                        { type: 'ratio', label: 'Tỷ lệ', total: 100 },
                        { type: 'jpy', label: 'Doanh thu tiền Nhật', total: 0 },
                    ];
                    this.countDetails = [
                        { type: 'cknhvn', label: 'CKNHVN', total: 0 },
                        { type: 'tt', label: 'TT', total: 0 },
                        { type: 'cknb', label: 'CKNB', total: 0 },
                        { type: 'count', label: 'Tổng', total: 0 },
                        { type: 'ratio', label: 'Tỷ lệ', total: 100 },
                    ];
                    this.offlineDetails = [
                        { id: 1, type: 'nt', label: 'Nguyễn Trãi', total: 0 },
                        { id: 2, type: 'hqv', label: 'Hoàng Quốc Việt', total: 0 },
                        { id: 19, type: 'q1', label: 'Quận 1', total: 0 },
                        { id: 6, type: 'hd', label: 'Hà Đông', total: 0 },
                        { id: 25, type: 'gv', label: 'Gò Vấp', total: 0 },
                        { type: 'count', label: 'Tổng', total: 0 },
                        { type: 'jpy', label: 'Doanh thu tiền Nhật', total: 0 },
                    ];
                    this.offlineCountDetails = [
                        { id: 1, type: 'nt', label: 'Nguyễn Trãi', total: 0 },
                        { id: 2, type: 'hqv', label: 'Hoàng Quốc Việt', total: 0 },
                        { id: 19, type: 'q1', label: 'Quận 1', total: 0 },
                        { id: 6, type: 'hd', label: 'Hà Đông', total: 0 },
                        { id: 25, type: 'gv', label: 'Gò Vấp', total: 0 },
                        { type: 'count', label: 'Tổng', total: 0 },
                    ];
                },
                async fetchInvoices() {
                    const formData = new FormData();
                    const data = {};
                    if (this.start) {
                        data.start = this.start;
                    }
                    if (this.end) {
                        data.end = this.end;
                    }
                    if (this.type) {
                        data.type = this.type;
                    }
                    if (this.paymentMethod) {
                        data.paymentMethod = this.paymentMethod;
                    }
                    const queryString = new URLSearchParams(data).toString();

                    const response = await fetch(`/backend/invoice-stat/get-stat?${queryString}`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    this.resetDetails();

                    const result = await response.json();
                    this.invoices = result.data;
                    this.offlineInvoices = result.offline_invoices;
                    // Doanh thu theo phương thức thanh toán
                    let cknhvnTotal = 0;
                    let ttTotal = 0;
                    let cknbTotal = 0;
                    let vndTotal = 0;
                    let ratioTotal = 0;
                    let jpyTotal = 0;

                    // Số đơn theo phương thức thanh toán
                    let cknhvnCount = 0;
                    let ttCount = 0;
                    let cknbCount = 0;
                    

                    // Doanh thu offline theo phương thức thanh toán
                    let offlineTotal = {
                        nt: 0,
                        hqv: 0,
                        q1: 0,
                        hd: 0,
                        gv: 0,
                        count: 0,
                        jpy: 0
                    };
                    // Số đơn offline theo phương thức thanh toán
                    let offlineCount = {
                        nt: 0,
                        hqv: 0,
                        q1: 0,
                        hd: 0,
                        gv: 0,
                        count: 0
                    };

                    this.invoices.forEach(invoice => {
                        if (invoice.currency === 'jpy') {
                            this.filteredProductGroups.forEach(group => {
                                if (group.type.includes(invoice.product_type)) {
                                    group.products.forEach(product => {
                                        if (product.id.includes(invoice.category_product_id)) {
                                            let tmp = this.revenueDetails[5][product.id.join(',')] || 0;
                                            this.$set(this.revenueDetails[5], product.id.join(','), tmp + Number(invoice.paid_money));
                                            this.$set(this.revenueDetails[5], group.value, tmp + Number(invoice.paid_money));
                                            jpyTotal += Number(invoice.paid_money);

                                            if (invoice.payment_method_group === 'cknhvn') {
                                                let tmp = this.countDetails[0][product.id.join(',')] || 0;
                                                this.$set(this.countDetails[0], product.id.join(','), tmp + 1);
                                                this.$set(this.countDetails[0], group.value, tmp + 1);
                                                cknhvnCount += 1;
                                            } else if (invoice.payment_method_group === 'tt') {
                                                let tmp = this.countDetails[1][product.id.join(',')] || 0;
                                                this.$set(this.countDetails[1], product.id.join(','), tmp + 1);
                                                this.$set(this.countDetails[1], group.value, tmp + 1);
                                                ttCount += 1;
                                            } else if (invoice.payment_method_group === 'cknb') {
                                                let tmp = this.countDetails[2][product.id.join(',')] || 0;
                                                this.$set(this.countDetails[2], product.id.join(','), tmp + 1);
                                                this.$set(this.countDetails[2], group.value, tmp + 1);
                                                cknbCount += 1;
                                            }
                                            let tmpCount = this.countDetails[3][product.id.join(',')] || 0;
                                            this.$set(this.countDetails[3], product.id.join(','), tmpCount + 1);
                                            this.$set(this.countDetails[3], group.value, tmpCount + 1);
                                        }
                                    });
                                    
                                }
                            });
                        } else {
                            this.filteredProductGroups.forEach(group => {
                                group.products.forEach(product => {
                                    if (group.type.includes(invoice.product_type)) {
                                        if (product.id.includes(invoice.category_product_id)) {
                                            if (invoice.payment_method_group === 'cknhvn') {
                                                let tmp = this.revenueDetails[0][product.id.join(',')] || 0;
                                                let groupTmp = this.revenueDetails[0][group.value] || 0;
                                                this.$set(this.revenueDetails[0], product.id.join(','), tmp + Number(invoice.paid_money));
                                                this.$set(this.revenueDetails[0], group.value, groupTmp + Number(invoice.paid_money));
                                                cknhvnTotal += Number(invoice.paid_money);
                                            } else if (invoice.payment_method_group === 'tt') {
                                                let tmp = this.revenueDetails[1][product.id.join(',')] || 0;
                                                let groupTmp = this.revenueDetails[1][group.value] || 0;
                                                this.$set(this.revenueDetails[1], product.id.join(','), tmp + Number(invoice.paid_money));
                                                this.$set(this.revenueDetails[1], group.value, groupTmp + Number(invoice.paid_money));
                                                ttTotal += Number(invoice.paid_money);
                                            } else if (invoice.payment_method_group === 'cknb') {
                                                let tmp = this.revenueDetails[2][product.id.join(',')] || 0;
                                                let groupTmp = this.revenueDetails[2][group.value] || 0;
                                                this.$set(this.revenueDetails[2], product.id.join(','), tmp + Number(invoice.paid_money));
                                                this.$set(this.revenueDetails[2], group.value, groupTmp + Number(invoice.paid_money));
                                                cknbTotal += Number(invoice.paid_money);
                                            }

                                            if (invoice.payment_method_group === 'cknhvn') {
                                                let tmp = this.countDetails[0][product.id.join(',')] || 0;
                                                let groupTmp = this.countDetails[0][group.value] || 0;
                                                this.$set(this.countDetails[0], product.id.join(','), tmp + 1);
                                                this.$set(this.countDetails[0], group.value, groupTmp + 1);
                                                cknhvnCount += 1;
                                            } else if (invoice.payment_method_group === 'tt') {
                                                let tmp = this.countDetails[1][product.id.join(',')] || 0;
                                                let groupTmp = this.countDetails[1][group.value] || 0;
                                                this.$set(this.countDetails[1], product.id.join(','), tmp + 1);
                                                this.$set(this.countDetails[1], group.value, groupTmp + 1);
                                                ttCount += 1;
                                            } else if (invoice.payment_method_group === 'cknb') {
                                                let tmp = this.countDetails[2][product.id.join(',')] || 0;
                                                let groupTmp = this.countDetails[2][group.value] || 0;
                                                this.$set(this.countDetails[2], product.id.join(','), tmp + 1);
                                                this.$set(this.countDetails[2], group.value, groupTmp + 1);
                                                cknbCount += 1;
                                            }
                                            
                                            if (['cknhvn', 'tt', 'cknb'].includes(invoice.payment_method_group)) {
                                                let totalTmp = this.revenueDetails[3][product.id.join(',')] || 0;
                                                let tmpCount = this.countDetails[3][product.id.join(',')] || 0;
                                                let groupTotalTmp = this.revenueDetails[3][group.value] || 0;
                                                let groupTmpCount = this.countDetails[3][group.value] || 0;

                                                this.$set(this.revenueDetails[3], product.id.join(','), totalTmp + Number(invoice.paid_money));
                                                this.$set(this.revenueDetails[3], group.value, groupTotalTmp + Number(invoice.paid_money));
                                                this.$set(this.countDetails[3], product.id.join(','), tmpCount + 1);
                                                this.$set(this.countDetails[3], group.value, groupTmpCount + 1);
                                                vndTotal += Number(invoice.paid_money);
                                            }
                                        }
                                    }
                                });
                            });
                        }
                    });

                    const uniqueProductIds = [...new Set(this.offlineInvoices.map(inv => inv.product_id))];

                    this.offlineDetails = this.offlineDetails.map((detail, index) => {
                        uniqueProductIds.forEach(productId => {
                            if (productId && !detail.hasOwnProperty(`${productId}`)) {
                                this.$set(this.offlineDetails[index], `_${productId}`, 0);
                            }
                        });
                        return detail;
                    });
                    this.offlineCountDetails = this.offlineCountDetails.map((detail, index) => {
                        uniqueProductIds.forEach(productId => {
                            if (productId && !detail.hasOwnProperty(`_${productId}`)) {
                                this.$set(this.offlineCountDetails[index], `_${productId}`, 0);
                            }
                        });
                        return detail;
                    });
                    this.offlineInvoices.forEach(invoice => {
                        if (invoice.currency === 'jpy') {
                            this.offlineGroups.forEach(group => {
                                if (group.type.includes(invoice.product_type)) {
                                    group.products.forEach(product => {
                                        if (product.id.includes(invoice.category_product_id)) {
                                            let tmp = this.offlineDetails[5][`_${product.id.join(',')}`] || 0;
                                            this.$set(this.offlineDetails[5], `_${product.id.join(',')}`, tmp + Number(invoice.paid_money));
                                            offlineTotal.jpy += Number(invoice.paid_money);

                                            let tmpCount = this.offlineCountDetails[5][`_${product.id.join(',')}`] || 0;
                                            this.$set(this.offlineCountDetails[5], `_${product.id.join(',')}`, tmpCount + 1);
                                        }
                                    });
                                }
                            });
                        } else {
                            this.offlineGroups.forEach(group => {
                                group.products.forEach(product => {
                                    if (group.type.includes(invoice.product_type)) {
                                        if (product.id.includes(invoice.category_product_id)) {
                                            const departmentIndex = this.offlineDetails.findIndex(d => d.id === invoice.department_id);
                                            if (departmentIndex !== -1) {
                                                let tmp = this.offlineDetails[departmentIndex][`_${product.id.join(',')}`] || 0;
                                                this.$set(this.offlineDetails[departmentIndex], `_${product.id.join(',')}`, tmp + Number(invoice.paid_money));

                                                let countTmp = this.offlineCountDetails[departmentIndex][`_${product.id.join(',')}`] || 0;
                                                this.$set(this.offlineCountDetails[departmentIndex], `_${product.id.join(',')}`, countTmp + 1);

                                                const key = Object.keys(offlineTotal)[departmentIndex];
                                                offlineTotal[key] += Number(invoice.paid_money);
                                                offlineCount[key] += 1;
                                                
                                                let totalTmp = this.offlineDetails[5][`_${product.id.join(',')}`] || 0;
                                                let tmpCount = this.offlineCountDetails[5][`_${product.id.join(',')}`] || 0;

                                                this.$set(this.offlineDetails[5], `_${product.id.join(',')}`, totalTmp + Number(invoice.paid_money));
                                                this.$set(this.offlineCountDetails[5], `_${product.id.join(',')}`, tmpCount + 1);
                                                offlineTotal.count += Number(invoice.paid_money);
                                                offlineCount.count += 1;
                                            }
                                        }
                                    }
                                });
                            });
                        }
                    });

                    this.$set(this.revenueDetails[0], 'total', this.revenueDetails[0].total + Number(cknhvnTotal));
                    this.$set(this.revenueDetails[1], 'total', this.revenueDetails[1].total + Number(ttTotal));
                    this.$set(this.revenueDetails[2], 'total', this.revenueDetails[2].total + Number(cknbTotal));
                    this.$set(this.revenueDetails[3], 'total', this.revenueDetails[3].total + Number(vndTotal));
                    this.$set(this.revenueDetails[5], 'total', this.revenueDetails[5].total + Number(jpyTotal));

                    this.$set(this.countDetails[0], 'total', this.countDetails[0].total + Number(cknhvnCount));
                    this.$set(this.countDetails[1], 'total', this.countDetails[1].total + Number(ttCount));
                    this.$set(this.countDetails[2], 'total', this.countDetails[2].total + Number(cknbCount));
                    this.$set(this.countDetails[3], 'total', this.countDetails[3].total + Number(cknhvnCount + ttCount + cknbCount));

                    console.log('offline total', offlineTotal);
                    for (const [key, value] of Object.entries(offlineTotal)) {
                        const index = this.offlineDetails.findIndex(detail => detail.type === key);
                        if (index !== -1) {
                            this.$set(this.offlineDetails[index], 'total', value);
                        }
                    }

                    for (const [key, value] of Object.entries(offlineCount)) {
                        const index = this.offlineCountDetails.findIndex(detail => detail.type === key);
                        if (index !== -1) {
                            this.$set(this.offlineCountDetails[index], 'total', value);
                        }
                    }


                    console.log('offline details', this.offlineDetails);
                    this.filteredProductGroups.forEach(group => {
                        group.products.forEach(product => {
                            this.$set(this.revenueDetails[4], product.id.join(','), (this.revenueDetails[3][product.id.join(',')] * 100 / this.revenueDetails[3].total).toFixed(2));
                            this.$set(this.countDetails[4], product.id.join(','), (this.countDetails[3][product.id.join(',')] * 100 / this.countDetails[3].total).toFixed(2));
                            this.$set(this.revenueDetails[4], group.value, ((this.revenueDetails[3][group.value] || 0) * 100 / this.revenueDetails[3].total).toFixed(2));
                            this.$set(this.countDetails[4], group.value, ((this.countDetails[3][group.value] || 0) * 100 / this.countDetails[3].total).toFixed(2));
                        });
                    });
                }
            }
        });
    </script>
@stop
