@extends('backend._default.dashboard')

@section('description') Q<PERSON>ản lý đơn hàng @stop
@section('keywords') invoice @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý đơn hàng @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/sale_commission.css') }}?{{filemtime('assets/backend/css/sale_commission.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
@stop

@section('content')
    <div class="sale__commission--screen" id="sale__commission--screen">
        <div class="sale__commission--filter">
            <input type="text" class="form-control" placeholder="Mã số" v-model="filter.id" @keyup.enter="applyFilter">
            <input type="text" class="form-control" placeholder="Email" v-model="filter.email" @keyup.enter="applyFilter">
            <input type="text" class="form-control" name="time_from" id="time_from" placeholder="Từ ngày" v-model="filter.time_from" @change="onChangeDate($event)">
            <input type="text" class="form-control" name="time_to" id="time_to" placeholder="Đến ngày" v-model="filter.time_to">
            <select class="form-control" v-model="filter.time_type">
                <option value="created_at">Lọc theo ngày tạo</option>
                <option value="active_time">Lọc theo ngày kích hoạt</option>
            </select>

            <div style="display: flex; align-items: center">
                <span style="margin-right: 10px; cursor: pointer" @click="resetFilter"><i class="fa fa-refresh"></i></span>
                <button class="btn btn-info btn-block" @click="applyFilter">Lọc</button>
            </div>
            <button @click="exportFile">Xuất Excel</button>
        </div>
        <div class="sale__commission--list">
            <table>
                <thead>
                    <tr>
                        <th width="6%">Mã số</th>
{{--                        <th>Hình thức</th>--}}
                        <th>Thời gian</th>
                        <th width="15%">TVV 1</th>
                        <th width="15%">TVV 2</th>
                        <th width="10%">Người kích hoạt</th>
{{--                        <th>Trạng thái</th>--}}
                        <th width="10%" style="text-align: right">Số tiền</th>
                        <th width="15%" style="text-align: right">Thực nhận</th>
                        <th width="10%" class="text-center">Ảnh hoá đơn</th>
                        <th width="6%"class="text-center">Link</th>
                    </tr>
                </thead>
                <tbody v-if="!loading">
                    <tr v-for="(invoice, index) in invoices">
                        <td>
                            <div>@{{ invoice.id }}</div>
                            <div>@{{ invoice.product_name }}</div>
                        </td>
{{--                        <td>@{{ invoice.payment_method_id }}</td>--}}
                        <td>
                            <div v-if="invoice.info_contact.name">@{{ invoice.info_contact.name }} - @{{ invoice.info_contact.phone }}</div>
                            <div v-if="invoice.info_contact.email">@{{ invoice.info_contact.email }}</div>
                        </td>
                        <td>
                            <button class="btn btn-info btn-sm" v-if="invoice.sale_1 == null && {{Auth::user()->permission}} == 0" @click="changeSale(invoice.id, 'sale_1', {{Auth::user()->id}})">Nhận</button>
                            <div style="display: flex; align-items: center">
                                <select class="form-control" v-model="invoice.sale_1" v-if="{{Auth::user()->permission}} == 1" @change="changeSale(invoice.id, 'sale_1', $event)">
                                    <option v-for="admin in admins" :key="invoice.id + '#1#' + admin.id" :value="admin.id">@{{ admin.name }}</option>
                                </select>
                                <span v-if="invoice.sale_1 && {{Auth::user()->permission}} == 1" style="color: #9f041b; cursor: pointer; margin-left: 5px" @click="changeSale(invoice.id, 'sale_1', 0)">
                                <i class="fa fa-times"></i>
                            </span>
                            </div>
                            <span v-if="invoice.sale_1_info && {{Auth::user()->permission}} != 1">@{{ invoice.sale_1_info.name }}</span>
                            <span v-if="invoice.sale_1 && invoice.sale_1 == {{Auth::user()->id}}" style="color: #9f041b; cursor: pointer" @click="changeSale(invoice.id, 'sale_1', 0)"><i class="fa fa-times"></i></span>
                        </td>
                        <td>
                            <button class="btn btn-info btn-sm" v-if="invoice.sale_2 == null && {{Auth::user()->permission}} == 0" @click="changeSale(invoice.id, 'sale_2', {{Auth::user()->id}})">Nhận</button>
                            <div style="display: flex; align-items: center">
                                <select class="form-control" v-model="invoice.sale_2" v-if="{{Auth::user()->permission}} == 1" @change="changeSale(invoice.id, 'sale_2', $event)">
                                    <option v-for="admin in admins" :key="invoice.id + '#2#' + admin.id" :value="admin.id">@{{ admin.name }}</option>
                                </select>
                                <span v-if="invoice.sale_2 && {{Auth::user()->permission}} == 1" style="color: #9f041b; cursor: pointer; margin-left: 5px" @click="changeSale(invoice.id, 'sale_2', 0)">
                                    <i class="fa fa-times"></i>
                                </span>
                            </div>

                            <span v-if="invoice.sale_2_info && {{Auth::user()->permission}} != 1">@{{ invoice.sale_2_info.name }}</span>
                            <span v-if="invoice.sale_2 && invoice.sale_2 == {{Auth::user()->id}}" style="color: #9f041b; cursor: pointer" @click="changeSale(invoice.id, 'sale_2', 0)">
                                <i class="fa fa-times"></i>
                            </span>
                        </td>
                        <td>
                            <div v-if="invoice.admin_active != 0" class="text-success">@{{ invoice.admin_active_name }}</div>
                            <div v-if="invoice.admin_active == 0" class="text-danger">@{{ invoice.admin_active_name }}</div>
                            <div v-if="invoice.active_time">@{{ invoice.active_time }}</div>
                        </td>
{{--                        <td>@{{ invoice.invoice_status}}</td>--}}
                        <td style="text-align: right">@{{ invoice.price | currency}}</td>
                        <td style="text-align: right">
                            <input ref="editRevenue" maxlength="8" name="revenue" v-if="invoice.revenueEditing" class="form-control" type="text" v-model="invoice.revenue" @keyup.esc="invoice.revenueEditing = false" v-on:keydown.enter="invoice.revenueEditing = false; applyRevenue(invoice.id, $event)"/>
                            <span v-if="!invoice.revenueEditing">@{{ invoice.revenue | currency}}</span>
                            <span class="text-info" style="cursor: pointer" v-if="!invoice.revenueEditing" @click="invoice.revenueEditing = 1; focusInput(index, 'editRevenue')"><i class="fa fa-edit"></i></span>
                        </td>
                        <td class="text-center">
                            <span @click="editBillImage(invoice.id)" v-if="!invoice.previewBill && !invoice.bill_image" class="sale__commission--bill-image-add text-info">Thêm</span>
                            <div class="sale__commission--bill-image" v-if="invoice.previewBill || invoice.bill_image">
                                <img
                                        v-if="invoice.bill_image || invoice.previewBill"
                                        :src="!invoice.previewBill ? '{{url('cdn/invoice_img_bill/small')}}' + '/' + invoice.bill_image : invoice.previewBill"
                                        data-toggle="modal"
                                        data-target="#invoiceBillImage"
                                        @click="currentBillImage = !invoice.previewBill ? '{{url('cdn/invoice_img_bill/default')}}' + '/' + invoice.bill_image : invoice.previewBill">
                                <span @click="editBillImage(invoice.id)" v-if="!invoice.previewBill" class="sale__commission--bill-image-edit">Sửa</span>
                                <div v-if="invoice.previewBill" class="sale__commission--bill-image-save">
                                    <span class="text-success" @click="saveBillImage(invoice.id, invoice.previewBill)"><i class="fa fa-check"></i></span>
                                    <span class="text-danger" @click="invoice.previewBill = null"><i class="fa fa-times"></i></span>
                                </div>
                            </div>

                            <form :id="'bill-image-form-'+invoice.id">
                                <input type="text" name="inputInvoiceId" v-model="invoice.id" style="display: none;"/>
                                <input type='file' :id="'inputBillImage'+invoice.id" name="inputBillImage" style="display: none;" @change="readURL(invoice.id, $event)" />
                            </form>
                        </td>
                        <td class="text-center">
                            <a v-if="invoice.chat_link && invoice.chatLinkEditing == false" :href="invoice.chat_link" target="_blank">
                                <i class="fa fa-external-link"></i>
                            </a>
                            <div v-if="invoice.chatLinkEditing == false" class="text-info" style="cursor: pointer" @click="invoice.chatLinkEditing = true; focusInput(index, 'editChatLink')">Sửa</div>
                            <input ref="editChatLink" v-model="invoice.chat_link" v-show="invoice.chatLinkEditing == true" @blur="invoice.chatLinkEditing = false" type="text" class="form-control expand-on-focus" @keyup.esc="invoice.chatLinkEditing = false" @keyup.enter="invoice.chatLinkEditing = false; saveChatLink(invoice.id, $event)"/>
                        </td>
                    </tr>
                </tbody>
                <tbody v-if="loading">
                    <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                        <td colspan="11"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="modal fade" id="invoiceBillImage" tabindex="-1" role="dialog" aria-labelledby="invoiceBillImage" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <img :src="currentBillImage" class="sale__commission--bill-image-default">
                    </div>
                </div>
            </div>
        </div>
        <div class="sale__commission--paginate">
            <paginate
{{--                    v-model="filter.page"--}}
                    :page-count="filter.total_page"
                    :page-range="4"
                    :margin-pages="3"
                    :click-handler="changePage"
                    :prev-text="'&laquo;'"
                    :next-text="'&raquo;'"
                    :container-class="'pagination'"
                    :page-class="'page-item'"
                    :force-page="filter.page - 1"
            >
            </paginate>
        </div>
    </div>

    <script>
        jQuery.browser = {};
        (function () {
            jQuery.browser.msie = false;
            jQuery.browser.version = 0;
            if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
                jQuery.browser.msie = true;
                jQuery.browser.version = RegExp.$1;
            }
        })();

    </script>
    {{--    Local plugins--}}
    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
    <script>
        $(function () {
            // hô biến input thành datepicker xịn xò
            $('#time_from').datetimepicker({
                language: 'vi'
            }).on('dp.change', function (event) {
                sale__commission.onChangeDatetime(event);
            });
            $('#time_to').datetimepicker({
                language: 'vi'
            }).on('dp.change', function (event) {
                sale__commission.onChangeDatetime(event);
            });
        });
    </script>
{{--     CDN--}}
    <script src="{{ asset('/plugin/vuejs-paginate/vuejs-paginate.js') }}"></script>
    <script src="{{ asset('/plugin/vue-router/vue-router.js') }}"></script>
    <script src="{{ asset('assets/backend/js/sale_commission.js') }}?{{ filemtime('assets/backend/js/sale_commission.js') }}"></script>

@stop
