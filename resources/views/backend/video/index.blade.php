<!doctype html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Quản lý video</title>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.1/css/all.css"
        integrity="sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr" crossorigin="anonymous" />
    <link rel="stylesheet" href="{{ asset('plugin/element-ui/element-ui.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/base.css') }}?{{ filemtime('css/base.css') }}">
    <link rel="stylesheet" href="{{ asset('css/video/index.css') }}?{{ filemtime('css/video/index.css') }}">
    <script>
        const videoBaseURL = "{{ env('RENDER_APP_URL') }}";
        const videoServerURL = "{{ env('VIDEO_SERVER_URL') }}";
    </script>
</head>

<body>
    <div id="videoApp">
        <app></app>
    </div>
</body>
<script src="{{ asset('js/video/video.js') }}?{{ filemtime('js/video/video.js') }}"></script>
<script src="{{ asset('plugin/element-ui/element-ui.min.js') }}" type="text/javascript"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.3/jquery.min.js"
    integrity="sha512-STof4xm1wgkfm7heWqFJVn58Hm3EtS31XFaagaa8VMReCXAkQnJZ+jEy8PCC/iT18dFy95WcExNHFTqLyp72eQ=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<link type="text/css" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://js.pusher.com/6.0/pusher.min.js"></script>
<script>
    //lắng nghe tin nhắn từ pusher
    Pusher.logToConsole = false;
    var pusher = new Pusher("{{ env('PUSHER_APP_KEY') }}", {
        cluster: 'ap3'
    });
    var channel = pusher.subscribe('render');
    channel.bind('render_completed', function(data) {
        toastr.info(data.message, {
            timeOut: 3000
        })
    });
</script>

</html>
<script></script>
