@extends('backend._default.dashboard')

@section('title') Admin | Quản lý phản hổi @stop
@section('description') Quản lý phản hồi @stop
@section('keywords') feedback @stop
@section('author') dungmori.com @stop

@section('assets')
    {{-- scss: resources/assets/sass/backend/feedback_manager.scss --}}
    <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
@stop

@section('content') 

<input type="hidden" id="csrf_token" value="{{ csrf_token() }}">

<div id="feedback-manager" class="row bg-title">
    <div class="new-feedback-area">
    	<h4 class="page-title pull-left">Quản lý feedback</h4>
    	@if(json_decode(Auth::guard('admin')->user()->matrix)->feedback->add != null)
    		<button class="btn btn-success new-feedback-btn" data-toggle="modal" data-target="#addOrEdit" v-on:click="startAddNewFeedback()">Thêm mới</button>
    	@endif
    </div>

    <table class="table" id="feedback-table">
		<thead>
			<tr>
				<th>Ảnh</th>
				<th>Tên</th>
                <th>Thông tin</th>
				<th>Đang học</th>
				<th>Nội dung</th>
				<th>Ngày phản hồi (hiển thị)</th>
				<th>Ảnh</th>
				<th>Trạng thái</th>
				<th>Hành động</th>
			</tr>
		</thead>
		<tbody>
			<tr v-for="(feedback, index) in feedbacks">
				<td>
					<img v-if="feedback.avatar != null && feedback.avatar != ''" :src="url + feedback.avatar" class="feedback-user-avatar">
					<img v-if="feedback.avatar == null || feedback.avatar == ''" src="{{url('assets/img')}}/default-avatar.jpg" class="feedback-user-avatar">
				</td>
				<td>@{{ feedback.username }}</td>
                <td>@{{ feedback.user_job }}</td>
				<td>@{{ feedback.current_course }}</td>
				<td>@{{ feedback.content }}</td>
				<td>@{{ feedback.date }}</td>
				<td><img :src="'{{ asset('cdn/feedback/default/') }}/' + feedback.image" alt="" style="width: 70px"></td>
				<td>
					<input type='checkbox' class='ios8-switch ios8-switch-lg' :id="'feedback' + feedback.id" v-model="feedback.show"><label style="cursor: pointer;" :for="'feedback' + feedback.id" v-on:click="setStatus(index, feedback.id)"></label>
				</td>
				<td>
					@if(json_decode(Auth::guard('admin')->user()->matrix)->feedback->edit != null)
						<button class="btn btn-info" data-toggle="modal" data-target="#addOrEdit" v-on:click="startEditFeedback(index, feedback.id)">Sửa</button>
					@endif
					@if(json_decode(Auth::guard('admin')->user()->matrix)->feedback->delete != null)
	    				<button class="btn btn-danger" data-toggle="modal" data-target="#deletModal" v-on:click="startRemoveFeedback(index, feedback.id)">Xóa</button>
	    			@endif
	    		</td>
			</tr>
		</tbody>
  	</table>

	<div class="modal fade" id="addOrEdit" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="title-model"></h4>
				</div>
				<div class="modal-body">
                    <div class="form-horizontal">
                    	<ul class="error-area">
                    		<li class="error-item" v-for="error in errors">@{{ error }}</li>
                    	</ul>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Email</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" v-model="email" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Nghề nghiệp</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" v-model="user_job" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Đang học</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" v-model="current_course" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Phản hồi</label>
                            <div class="col-sm-10">
                                <textarea class="form-control" style="height: 150px;" v-model="content" required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Thời gian</label>
                            <div class="col-sm-10">
                            <input type="text" class="form-control" v-model="date" placeholder="Thời gian hiển thị">
                            </div>
                        </div>
						<div class="form-group">
							<label class="control-label col-sm-2" for="course">Ảnh</label>
							<div class="col-sm-8">
								<div class="upload_complete" style="display: block;">
									<div class="file_image_single">
										<div class="file_image_single_img">
											<img id ="image_course" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 100px; max-height: 100px;" data-default="{{url('assets/img/icon_backend')}}/no_image.png">
										</div>
									</div>
								</div>
								<div class="upload_action" style="margin-top:2px;">
									<input type="file" name="img_course" id="img_course" onchange="previewCourse()">
									<div class="f11 formNote" style="color: red;">
										CHÚ Ý - chỉ cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>gif,jpg,jpeg,png,svg</b>
									</div>
								</div>
							</div>
						</div>
                        <div class="form-group" >
                            <label class="control-label col-sm-2">Bật/tắt</label>
                            <div class="col-sm-10">
                                <input type="checkbox" v-model="show" style="margin: 12px;" required>
                            </div>
                        </div>
                    </div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="editFeedback()" id="btn-action-model"></button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>

		</div>
	</div>

	<div id="deletModal" class="modal fade" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Xóa</h4>
			</div>
			<div class="modal-body">
				<p>Bạn có muốn xóa feedback này không?</p>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-success" v-on:click="removeFeedback()">Xóa</button>
				<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
			</div>
		</div>

		</div>
	</div>
  
</div>
<script>

var user = new Vue({
	el: '#feedback-manager',

	data: function () {
        return {
        	url			: window.location.origin + "/cdn/avatar/default/",
			feedbacks	: {!! json_encode($feedbacks) !!},
			email		: "",
			user_job	: "",
            current_course: "",
			content		: "",
			date: "",
			show		: false,
			currentId	: -1,
			currentIndex: -1,
			errors		: []
		}
	},

	methods: {
		// mở popup xóa 1 feedback
		startRemoveFeedback(index, id) {
			var vm = this;
			vm.currentId = id;
			vm.currentIndex = index;
		},

		// mở popup sửa 1 feedback
		startEditFeedback(index, id) {
			var vm = this;
			vm.errors = [];
			vm.email = vm.feedbacks[index].email;
            vm.user_job = vm.feedbacks[index].user_job;
			vm.current_course = vm.feedbacks[index].current_course;
			vm.content = vm.feedbacks[index].content;
			vm.date = vm.feedbacks[index].date;
			vm.show = vm.feedbacks[index].show;
			vm.image = vm.feedbacks[index].image;
			vm.currentId = id;
			vm.currentIndex = index;
			if (vm.image != null) {
				$("#image_course").attr('src', window.location.origin + '/cdn/feedback/default/' + vm.image);
			}

			$("#title-model").text("Chỉnh sửa");
			$("#btn-action-model").text("Sửa");
		},

		// mở popup để thêm mới 1 feedback
		startAddNewFeedback() {
			var vm = this;
			vm.errors = [];
			vm.email = "";
            vm.user_job = "";
			vm.current_course = "";
			vm.content = "";
			vm.date = "";
			vm.currentId = -1;
			vm.currentIndex = -1;
			vm.show = true;
			vm.image = "";
			$("#title-model").text("Thêm mới");
			$("#btn-action-model").text("Thêm");
		},

		// sửa 1 feedback - Khi nhấn nút sửa ở popup
		editFeedback() {
			var vm = this;
			var token = $('#csrf_token').val();
			var data = new FormData($("#course_form")[0]);
			data.append('_token', token);
			data.append('id', vm.currentId);
			data.append('email', vm.email);
			data.append('show', vm.show);
			data.append('user_job', vm.user_job);
			data.append('current_course', vm.current_course);
			data.append('content', vm.content);
			data.append('date', vm.date);
			data.append('img_course', $('#img_course')[0].files[0]);

	        $.ajax({
	            url: window.location.origin + "/backend/feedback/edit",
				type:"POST",
				data: data,
				async: true,
				contentType: false,
				processData: false,
				beforeSend: function (xhr) { if (token) return xhr.setRequestHeader('X-CSRF-TOKEN', token); },
	            error: function() {
	            	// vm.errors = [];
	            },
	            success: function(response) {
	            	if (response == "blank_data") {
	            		vm.errors = [];
	            		vm.errors.push("Dữ liệu còn trống");
	            	} else if (response == "invalid_email") {
	            		vm.errors = [];
	            		vm.errors.push("Email không tồn tại trong hệ thống");
	            	} else if (response.type == "new") {
	            		const newFeedback = response.feedback;
		            	vm.feedbacks.push(newFeedback);
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
	            	} else {
		            	// console.log("Thành công : "+response);
		            	vm.feedbacks[vm.currentIndex].avatar = response.feedback.avatar;
		            	vm.feedbacks[vm.currentIndex].username = response.feedback.username;
		            	vm.feedbacks[vm.currentIndex].content = response.feedback.content;
		            	vm.feedbacks[vm.currentIndex].date = response.feedback.date;
		            	vm.feedbacks[vm.currentIndex].email = response.feedback.email;
                        vm.feedbacks[vm.currentIndex].user_job = response.feedback.user_job;
		            	vm.feedbacks[vm.currentIndex].current_course = response.feedback.current_course;
		            	vm.feedbacks[vm.currentIndex].show = response.feedback.show;
		            	vm.feedbacks[vm.currentIndex].image = response.feedback.image;
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
		            }
	            }
	        });
		},

		setStatus(index, id) {
			var vm = this;
			var token = $('#csrf_token').val();

			var data = {
				'_token' 	: token,
	            'id'	: id,
	            'show'	: vm.feedbacks[index].show
			};
			$.ajax({
	            url: window.location.origin + "/backend/feedback/active", type:"POST", data: data, async: true,
	            beforeSend: function (xhr) { if (token) return xhr.setRequestHeader('X-CSRF-TOKEN', token); },
	            error: function() {
	            	console.log("Có lỗi xảy ra");
	            },
	            success: function(response) {
	            	// console.log(response);
	            }
	        });
		},

		// xóa 1 feedback - Khi nhấn nút xóa ở popup
		removeFeedback() {
			var vm = this;
			var token = $('#csrf_token').val();
			var data = {
	            '_token' 	: token,
	            'id'		: vm.currentId
	        };
	        // console.log(data);
	        $.ajax({
	            url: window.location.origin + "/backend/feedback/delete", type:"DELETE", data: data, async: true,
	            beforeSend: function (xhr) { if (token) return xhr.setRequestHeader('X-CSRF-TOKEN', token); },
	            error: function() {
	            	// vm.errors = [];
	            },
	            success: function(response) {
	            	vm.feedbacks.splice(vm.currentIndex, 1);
	            	$('#deletModal').modal('toggle');
	            }
	        });
		}
	},

	mounted() {
		var vm = this;

		$('#feedback-table').DataTable({
			"pageLength": 10,
			"stateSave": true
		});
	}
});

</script>
<script>
	function previewCourse() {
		var preview = document.querySelector('#image_course');
		var file    = document.querySelector('#img_course').files[0];
		var reader  = new FileReader();
		reader.onloadend = function () {
			preview.src = reader.result;
		}
		if (file) {
			reader.readAsDataURL(file);
		} else {
			preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
		}
	}
</script>

@stop