@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON>ản lý thông tin tuyển dụng @stop
@section('keywords') tuyển dụng @stop
@section('author') dungmori.com @stop
@section('title') Admin | Tuyển dụng @stop

@section('assets')
  <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
  <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
  <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
  <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script> 
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script> 
  <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet">  
@stop

@section('content')
  <div class="row bg-title">
    <h4 class="page-title pull-left">
      Danh sách
    </h4>
    <button class="add-modal btn btn-success"
      style="right: 26px;position: absolute;width: 146px;">
      <span class="glyphicon glyphicon-plus"></span>Thêm mới
    </button>
  </div>

  <div class="data-posts">
    @include('backend.recruitment.detailPost')
  </div>
  <div id="myModal" class="modal fade" >
    <div class="modal-dialog" role="document">
      <!-- Modal content-->
      <div class="modal-content" style="height: 80%">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title"></h4>
        </div>

        <form class="form-horizontal pt-10" role="form">
          <div class="form-group">
            <label class="control-label col-sm-2" for="title">Tiêu đề</label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="title">
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-2" for="title">Lương</label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="salary">
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-2" for="title">Địa điểm</label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="place">
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-2" for="title">Link</label>
            <div class="col-sm-8">
              <input type="text" class="form-control" id="link">
            </div>
          </div>
          <div class="form-group">
            <label class="control-label col-sm-2" for="public">Trạng thái</label>
            <div class="col-sm-8 pt-3">
              <label class="tcb-inline">
                <input type="radio" class="tc" id="status_off" name="public" value="0">
                <span class="labels">Tắt</span>
              </label>
              <label class="tcb-inline">
                <input type="radio" class="tc" id="status_on" name="public" value="1">
                <span class="labels">Bật</span>
              </label>
            </div>
          </div>
        </form>

        <div class="modal-body">
          <div class="deleteContent">
            Có muốn xóa thông tin này đi không? <span
                class="hidden did"></span>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn actionBtn">
              <span id="footer_action_button" class='glyphicon'> </span>
            </button>
            <button type="button" class="btn btn-warning" data-dismiss="modal">
              <span class='glyphicon glyphicon-remove'></span> Đóng
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    $(document).ready(function() {
      $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
      });
    });

    var posts = {!! json_encode($posts) !!};
    var currentPost = null;

    // Sắp xếp kéo thả
    sortTable('body_post', 1, 3, '/backend/recruitment/sort', 'order', 'table_post');

    // Thao tac them, xoa, sua
    $(document).on('click', '.add-modal', function() {
      $('#footer_action_button').text(" Thêm");
      $('#footer_action_button').addClass('glyphicon-check');
      $('#footer_action_button').removeClass('glyphicon-trash');
      $('.actionBtn').addClass('btn-success');
      $('.actionBtn').removeClass('btn-danger');
      $('.actionBtn').removeClass('delete');
      $('.actionBtn').removeClass('edit');
      $('.actionBtn').addClass('add');
      $('.modal-title').text('Thêm');
      $('.deleteContent').hide();
      $('.form-horizontal').show();
      fillmodalData('')
      $('#myModal').modal('show');
    });

    // Edit
    $(document).on('click', '.edit-modal', function() {
      $('#fid').attr('disabled', true);
      $('#footer_action_button').text(" Sửa");
      $('#footer_action_button').addClass('glyphicon-check');
      $('#footer_action_button').removeClass('glyphicon-trash');
      $('.actionBtn').addClass('btn-success');
      $('.actionBtn').removeClass('btn-danger');
      $('.actionBtn').removeClass('delete');
      $('.actionBtn').removeClass('add');
      $('.actionBtn').addClass('edit');
      $('.modal-title').text('Sửa');
      $('.deleteContent').hide();
      $('.form-horizontal').show();
      var postId = $(this).data('info');
      currentPost = postId;
      var post = posts.find((p) => p.id == postId);
      fillmodalData(post);
      $('#myModal').modal('show');
    });

    // Delete
    $(document).on('click', '.delete-modal', function() {
      $('#footer_action_button').text(" Xóa");
      $('#footer_action_button').removeClass('glyphicon-check');
      $('#footer_action_button').addClass('glyphicon-trash');
      $('.actionBtn').removeClass('btn-success');
      $('.actionBtn').addClass('btn-danger');
      $('.actionBtn').removeClass('edit');
      $('.actionBtn').removeClass('add');
      $('.actionBtn').addClass('delete');
      $('.modal-title').text('Xóa');
      $('.deleteContent').show();
      $('.form-horizontal').hide();
      var postId = $(this).data('info');
      currentPost = postId;
      $('#myModal').modal('show');
    });

    function fillmodalData(post) {
      if (post == '') {
      } else {
        $('#fid').val(post.id);
        $('#title').val(post.title);
        $('#salary').val(post.salary);
        $('#place').val(post.place);
        $('#link').val(post.link);
        (post.public == 0) ? $('#status_off').prop('checked', true) : $('#status_on').prop('checked', true) ;
      }
    }

    $('.modal-footer').on('click', '.add', function() {
      $.ajax({
        type: 'post',
        url: '/backend/recruitment/create',
        data: {
          '_token': $('input[name=_token]').val(),
          'title': $('#title').val(),
          'salary': $('#salary').val(),
          'place': $('#place').val(),
          'link': $('#link').val(),
          'public': $('input[name=public]:checked').val(),
          'order': ($("#table_post tbody tr").length + 1)
        },
        success: function(data) {
          $('#myModal').modal('hide');
          window.location.reload();
          // sortTable('body_post', 1, 3, '/backend/category-sort-order', 'order', 'table_post');
        }
      });
    });
    $('.modal-footer').on('click', '.edit', function() {
      $.ajax({
        type: 'post',
        url: '/backend/recruitment/edit',
        data: {
          '_token': $('input[name=_token]').val(),
          'id': currentPost,
          'title': $('#title').val(),
          'salary': $('#salary').val(),
          'place': $('#place').val(),
          'link': $('#link').val(),
          'public': $('input[name=public]:checked').val()
        },
        success: function(data) {
          $('#myModal').modal('hide');
          currentPost = null;
          window.location.reload();
          // sortTable('body_post', 1, 3, '/backend/category-sort-order', 'order', 'table_post');
        }
      });
    });
    $('.modal-footer').on('click', '.delete', function() {
      console.log('delete', currentPost);
      $.ajax({
        type: 'post',
        url: '/backend/recruitment/delete',
        data: {
          '_token': $('input[name=_token]').val(),
          'id': currentPost
        },
        success: function(data) {
          $('#myModal').modal('hide');
          currentPost = null;
          window.location.reload();
        }
      });
    });
  </script>
@stop
