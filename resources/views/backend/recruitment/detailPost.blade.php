<table class="table table-borderless" id="table_post">
  <thead>
    <tr>
      <th class="text-center"><PERSON><PERSON></th>
      <th class="text-center">Ti<PERSON><PERSON> đ<PERSON></th>
      <th class="text-center"><PERSON><PERSON><PERSON><PERSON></th>
      <th class="text-center"><PERSON><PERSON><PERSON></th>
      <th class="text-center">Link</th>
      <th class="text-center">Trạng thái</th>
      <th class="text-center">Hành động</th>
    </tr>
  </thead>
  <tbody class="body_post">
    @foreach($posts as $item)
    <tr class="item{{ $item->id }}">
      <td class="text-center">{{ $item->id }}</td>
      <td class="text-center">{{ $item->title }}</td>
      <td class="text-center">{{ $item->salary }}</td>
      <td class="text-center">{{ $item->place }}</td>
      <td class="text-center">{{ $item->link }}</td>
      <td class="text-center">
        @if($item->public == 1)
          <span class="label label-success">Bật</span>
        @else
          <span class="label label-danger">Tắt</span>
        @endif
      </td>
      <td class="text-center">
        @if(json_decode(Auth::guard('admin')->user()->matrix)->page->edit != null)
          <button class="edit-modal btn btn-info"
            data-info="{{$item->id}}">
            <span class="glyphicon glyphicon-edit"></span> Sửa
          </button>
        @endif

        @if(json_decode(Auth::guard('admin')->user()->matrix)->page->delete != null)
          <button class="delete-modal btn btn-danger"
            data-info="{{$item->id}}">
            <span class="glyphicon glyphicon-trash"></span> Xóa
          </button>
        @endif
      </td>
    </tr>
    @endforeach
  </tbody>
</table>