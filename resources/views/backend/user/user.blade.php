@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON>n lý người dùng @stop
@section('keywords') user @stop
@section('author') dungmori.com @stop
@section('title') Admin | Ngư<PERSON>i dùng @stop

@section('assets')
<link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />
<script src="{{asset('plugin/select2-4.1.0/js/select2.min.js')}}"></script>
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Ngư<PERSON>i dùng
        </h4>


        <select class="private-groups" style="margin: 5px 0 0 20px; padding: 2px 0; position: relative !important; width: 300px">
            <option value="">---Nhóm cộng đồng---</option>
            @foreach($groups as $group)
                <option value="{{ $group->id }}" @if(isset($_GET['gid']) &&  $_GET['gid'] == $group->id) selected @endif >{{ $group->name }}</option>
            @endforeach
        </select>
        <select class="own-course" style="margin: 5px 0 0 20px; padding: 2px 0; position: relative !important; width: 300px">
            <option value="">---Khoá học---</option>
            @foreach($courses as $course)
                <option value="{{ $course->id }}" @if(isset($_GET['cid']) &&  $_GET['cid'] == $course->id) selected @endif >{{ $course->name }}</option>
            @endforeach
        </select>
        @if( isset($_GET['gid']) || isset($_GET['cid']) ) &nbsp; <b>{{ $total }} thành viên</b> @endif

        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <label>
                <input type="radio" class="form-control mb-2 mr-sm-2" name="is_tester" value="" checked>
                Tất cả users&nbsp;
            </label>
            {{-- <label>
                <input type="radio" class="form-control mb-2 mr-sm-2" name="is_tester" value="0">
                Người dùng
            </label> --}}
            <label>
                <input type="radio" class="form-control mb-2 mr-sm-2" name="is_tester" value="1" placeholder="Tester">
                Tester &nbsp;
            </label>
            <input type="text" class="form-control mb-2 mr-sm-2" name="id" style="width: 130px;" placeholder="Tìm theo mã số">
            <input type="text" class="form-control mb-4 mr-sm-4" name="email" style="width: 200px;" placeholder="Tìm theo email hoặc tên...">
            <a type="button" class="search btn btn-info mb-2">Tìm kiếm</a>
        </form>
    </div>
    <div class="table_user">
        @include('backend.user.userPage')
    </div>
    <div class="result_user"></div>

    <div id="userModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Sửa</h4>

                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form">
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="fid">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="email">Mail Khách hàng</label>
                            <div class="col-sm-10">
                                <input type="email" class="form-control" id="email">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="uname">Tên đăng nhập</label>
                            <div class="col-sm-10">
                                <input type="name" class="form-control" id="uname">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="activation">Trạng thái</label>
                            <div class="col-sm-10">
                                <select class="form-control" id="activation" name="activation">
                                    <option value="1">Kích hoạt tài khoản</option>
                                    <option value="0">Hủy kích hoạt tài khoản</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="block">Khóa tài khoản</label>
                            <div class="col-sm-10">
                                <select class="form-control" id="block" name="block">
                                    <option value="0">Mở tài khoản</option>
                                    <option value="1">Khóa tài khoản</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="password">Password(*)</label>
                            <div class="col-sm-10">
                                <input type="password" class="form-control" name="password" id="password" placeholder="Chỉ áp dụng cho trường hợp đổi mật khẩu học viên">
                            </div>
                        </div>
                    </form>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success actionBtn" data-dismiss="modal">
                            <span id="footer_action_button" class='glyphicon glyphicon-check'>Sửa</span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
     $(document).ready(function() {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $('.private-groups').select2();
        $('.own-course').select2();
    });
     $(document).on('click', '.table_user .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page);
     });

    $('.private-groups').on('select2:select', function (e) {
      var href = new URL(window.location.href);
      href.searchParams.set('gid', e.params.data.id);
      var data = e.params.data;
      if(data.id && data.id != ''){
        // alert("lọc theo nhóm"+  data.text);
        window.location.href = href;
      }
    });

     $('.own-course').on('select2:select', function (e) {
       var href = new URL(window.location.href);
       href.searchParams.set('cid', e.params.data.id);
       var data = e.params.data;
       if(data.id && data.id != ''){
         // alert("lọc theo nhóm"+  data.text);
         window.location.href = href;
       }
     });
     function readPage(page){
           const queryString = window.location.search;
           const urlParams = new URLSearchParams(queryString);
           urlParams.set('page', page);
            $.ajax({
                url: '/backend/user/page?' + urlParams.toString()
           }).done(function(data){
                $('.result_user').empty();
                $('.table_user').html(data)
           })
     }
    //edit
    $(document).on('click', '.edit-modal', function(e) {
        $('#fid').attr('disabled', true);
        @if(Auth::user()->permission != 1)
        $('#email').attr('disabled', true);
        @endif
        $('#uname').attr('disabled', true);
        $('.actionBtn').addClass('edit');
        var user = $(this).data('info');
        fillmodalData(user);
        $('#userModal').modal('show');
    });
    //
    function fillmodalData(user){
        $('#fid').val(user.id);
        $('#email').val(user.email);
        $('#uname').val(user.username);
        $('#block').val(user.blocked);
        $('#activation').val(parseInt(user.activation));
        $('#password').val('');
    }
    //edit
    $('.modal-footer').on('click', '.edit', function() {
        $.ajax({
            type: 'post',
            url: '/backend/user/edit',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $("#fid").val(),
                'activation': $('#activation').val(),
                'block': $('#block').val(),
                'password': $('#password').val(),
                'email' : $("#email").val()
            },
            success: function(data) {
                if (data.errors){
                    //no thing
                }else {
                    $('.result_user').empty();
                    $('.table_user').html(data);
                }
            }
        });
    });
    //tim kiem
    $(document).on('click', '.search', function() {
        callBack();
    });

    $(document).keypress(function(e) {
        if (e.which == 13) {
            callBack();
            e.preventDefault();
        }
    });

    //tạo cuộc hội thoại với user chưa có
    function initConversation(userid){
        $.ajax({
            type: 'post',
            url: '/backend/user/create-conversation',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': userid
            },
            success: function(response) {
                // console.log("Tạo hội thoại mới", response);
                $(".fa-comments-"+ userid).css('color', '#00ab2e');
                window.open(window.location.origin+ "/backend/chat#"+ response, "_blank");
            }
        });
    }

    //tạo cuộc hội thoại với user chưa có
    function activeZoom(userId, courseId, level){
        var today = new Date();
        var expriedDate = prompt("Nhập ngày hết hạn (năm-tháng-ngày)", today.getFullYear()+"-"+   ("0" + today.getMonth()+1).slice(-2) +"-"+ ("0" + today.getDate()).slice(-2) );
        if (expriedDate != null) {
            $.ajax({
                type: 'post',
                url: '/backend/user/active-zoom',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'user_id': userId,
                    'course_id': courseId,
                    'watch_expired_day': expriedDate + ' 00:00:00'
                },
                success: function(response) {
                    alert('kích hoạt khóa zoom '+ level + ' thành công !!!');
                    $(".zoom-"+ userId).css('background', '#10a31a');
                    $(".zoom-"+ userId).css('color', '#fff');
                }
            });
        }
    }

    function callBack(){
            $.ajax({
                type: 'get',
                url: '/backend/user/find',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('input[name=id]').val(),
                    'email': $('input[name=email]').val(),
                    'is_tester': $('input[name=is_tester]:checked').val(),
                },
                success: function(data) {
                    $('.table_user').empty();
                    $('.result_user').html(data);
                    $(document).on('click', '.result_user .pagination a', function(e) {
                        var id = $('input[name=id]').val(),
                            email =  $('input[name=email]').val();
                        is_tester =  $('input[name=is_tester]').val();
                        e.preventDefault();
                        var page = $(this).attr('href').split('page=')[1];
                        resultPage(page,'/backend/user/find/?page=', id, email);
                    });
                }
            });
    }
    function resultPage(page, link, id, email){
         $.ajax({
            type: 'get',
            url: link + page,
            cache: false,
            data: {
                id : id,
                email : email,
            }
        }).done(function(data){
            $('.table_user').empty();
            $('.result_user').html(data);
        })
    }
     // set tester
     $(document).on('click', '.set_tester', function() {
         var data = {
             id: $(this).attr("data-id")
         };
         $.post(window.location.origin + '/backend/user/set-tester', data, function (res) {
             if (res.code == 200) {
                 location.reload();
             }
         })
     });
</script>

@stop
