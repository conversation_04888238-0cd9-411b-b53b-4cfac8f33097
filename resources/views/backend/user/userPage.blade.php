<table class="table table-borderless" id="table">
	<thead>
		<tr>
			<th class="text-center"><PERSON><PERSON></th>
            <th class="text-center" style="text-align: left; padding-left: 25px;">ID MXH ~ Email ~ Phone</th>
			<th class="text-center">Người dùng</th>
			<th class="text-center">Username/Phone</th>
            <th class="text-center">Đ<PERSON><PERSON> chỉ</th>
            <th class="text-center" style="width: 185px;">Kh<PERSON>a học</th>
            {{-- @if( isset($_GET['gid']) ) --}}
            <th class="text-center" style="width: 185px;">Cộng đồng</th>
            {{-- @endif --}}
            <th class="text-center"><PERSON><PERSON><PERSON> đ<PERSON>ng ký</th>
            <th class="text-center">Trạng thái</th>
			<th class="text-center"><PERSON><PERSON><PERSON> động</th>
		</tr>
	</thead>
    <tbody>
		@foreach($user as $item)
		<tr class="item{{$item->id}}">
			<td class="text-center">{{$item->id}}</td>
            <td class="text-center" style="text-align: left !important; padding-left: 15px; word-wrap: break-word;">
                @if($item->provider == 'google')
                    <i style="color: #dc4938;" class="fa fa-google-plus-square" ></i>
                @elseif($item->provider == 'facebook')
                    <i style="color: #377acc;" class="fa fa-facebook-square" ></i> @if(!$item->email) {{$item->social_id}} @endif
                @elseif($item->provider == 'apple')
                    <i style="color: #AAA;" class="fa fa-apple" ></i>
                @endif

                @if($item->social_id == null)
                    @if($item->phone_number != null)
                        <i class="fa fa-phone" ></i> {{$item->phone_number}}<br/>
                    @else
                        <i class="fa fa-envelope"></i>
                    @endif
                @endif

                {{$item->email}}

                <br/>
                <div style="margin: 1px 0 0 0; color:#7d06c7;"> <b>zoom <i class="fa fa-caret-right"></i></b>

                    @if($item->zoomN3)
                    <div class="label" title="đang học" style="padding: 2px 6px; border-radius: 3px; background: #10a31a; margin: 2px 0 1px 2px;">N3</div>
                    @else
                    <div class="label zoom-{{$item->id}}" title="kích hoạt khóa zoom" onclick="activeZoom('{{$item->id}}', '27', 'N3')" style="padding: 2px 6px; border-radius: 3px; background: #ffff; color: #333; border: 1px solid #888; margin: 2px 0 1px 2px; cursor: pointer;">N3</div>
                    @endif
                    <div class="label" style="padding: 2px 6px; border-radius: 3px; background: #DDD; color: #CCC; margin: 2px 0 1px 2px;">N2</div>
                    <div class="label" style="padding: 2px 6px; border-radius: 3px; background: #DDD; color: #CCC; margin: 2px 0 1px 2px;">N1</div>
                </div>

            </td>

			<td class="text-center" style="text-align: left !important; min-width: 270px;">

                @if($item->avatar == NULL || $item->avatar == '')
                    <img src="{{url('/assets/img/default-avatar.jpg')}}" style="height: 35px; width: 35px; margin-right: 15px;">
                @else
                    <img src="{{url('/cdn/avatar/small/'.$item->avatar)}}" style="height: 35px; width: 35px;margin-right: 15px;">
                @endif

                <strong>{{$item->name}}</strong>

                @if($item->conversation)
                    <a href="{{url('/backend/chat#')}}{{$item->conversation->id}}" target="_blank">
                        <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;" title="nhắn tin"></i>
                    </a>
                @else
                    <i class="fa fa-comments fa-comments-{{$item->id}}" style="cursor: pointer; font-size: 18px;" title="nhắn tin" onclick="initConversation({{$item->id}})"></i>
                @endif
            </td>
			<td class="text-center">{{$item->phone}}@if($item->username) <br/><span style="font-weight: 400; opacity: 0.7;">{{ '@'.$item->username}}</span> @endif</td>
            <td class="text-center">{{$item->address}}</td>
            <td class="text-center">
                <?php $now = new DateTime(); $today = $now->getTimestamp(); ?>
                @foreach($item->listActiveCourse as $course)

                    @if($today > strtotime($course->watch_expired_day))
                        <span class="label label-danger" style="padding: 2px 6px; border-radius: 3px; background: #e74c3c; margin: 2px 0 1px 3px;" data-toggle="tooltip" title="{{ date("d/m/Y", strtotime($course->watch_expired_day)) }}">
                            {{ $course->title }}
                        </span>
                    @else
                        <span class="label label-success" style="padding: 2px 6px; border-radius: 3px; background: #10a31a; margin: 2px 0 1px 3px;" data-toggle="tooltip" title="{{ date("d/m/Y", strtotime($course->watch_expired_day)) }}">
                            {{ $course->title }}
                        </span>
                    @endif

                @endforeach
            </td>

            {{-- @if( isset($_GET['gid']) ) --}}
            <td class="text-center" style="padding: 0 !important;">
                @foreach($item->communities() as $groupItem)
                    <a href="{{url('/groups')}}/{{$groupItem->id}}-preview">
                        <p style="margin: 0; font-size: 11px;">{{ $groupItem->name }} <small>({{ $groupItem->expried }})</small></p>
                    </a>
                @endforeach
            </td>
            {{-- @endif --}}

            <td class="text-center" style="width: 130px; font-size: 12px;">{{ friendlyTime($item->created_at) }}</td>
            <td class="text-center" style="font-size: 12px; min-width: 105px;">
                @if($item->activation == 1)
                    <span style="color: #69aa00;"><i class="fa fa-check-square" ></i> Xác thực</span>
                    @if($item->blocked == '0')
                    @else
                       &nbsp; <i class="fa fa-lock"  title="Tài khoản bị khóa"></i> Bị khóa
                    @endif
                @else
                    <span style="color: #e74c3c;"><i class="fa fa-warning" ></i> Chưa</span>
                    @if($item->blocked == '0')
                    @else
                        &nbsp; <i class="fa fa-lock"  title="Tài khoản bị khóa"></i> Bị khóa
                    @endif
                @endif

                @if(json_decode(Auth::guard('admin')->user()->matrix)->user->login != null)
                    @if (!$item->is_tester)
                            <span
                                    data-id="{{$item->id}}"
                                    class="set_tester label label-danger"
                                    style="padding: 4px 6px; border-radius: 3px; background: #0F4C82; margin: 0 4px 3px 0; cursor: pointer"
                            >Set tester</span>
                    @else
                            <span
                                    data-id="{{$item->id}}"
                                    class="set_tester label label-danger"
                                    style="padding: 4px 6px; border-radius: 3px; background: #9f041b; margin: 0 4px 3px 0; cursor: pointer"
                            >Unset tester</span>
                    @endif

                @endif

            </td>
			<td class="text-center">
                @if(json_decode(Auth::guard('admin')->user()->matrix)->user->edit != null)
					<span class="edit-modal label label-default" data-info="{{$item}}" style="padding: 4px 6px 4px 8px; margin: 0 4px 3px 0; float: left; cursor: pointer; border-radius: 3px;">
						<i class="fa fa-pencil" ></i> Sửa
					</span>
                @endif

                @if(json_decode(Auth::guard('admin')->user()->matrix)->user->login != null)
                    <a class="login_user label label-danger" style="padding: 4px 6px; margin: 0 4px 3px 0; border-radius: 3px; background: #e74c3c; float: left;"
                        href='{{url('/backend/user/login/'. $item->id)}}' target="_blank">Đăng nhập
                    </a>
                @endif

			</td>
		</tr>
		@endforeach
    </tbody>
</table>
{{ $user->render() }}