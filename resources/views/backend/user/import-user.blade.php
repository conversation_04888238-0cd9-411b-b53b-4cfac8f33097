@extends('backend._default.dashboard')

@section('title') Admin | Quản lý Popup @stop
@section('description') Quản lý Popup @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop

@section('assets')
    <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
@stop

@section('content')

<div id="popup-manager" class="row bg-title">
	<div class="popup-manager-title-area">
		<h4 class="page-title pull-left popup-manager-title">Import users</h4>
	</div>

    <div>
        <input type="file" accept=".xls,.xlsx" />
        <label for="" class="font-bold mt-4">Chọn một khóa học</label>
        <select name="" id="course-select" class="form-control mt-1 w-[500px]" placeholder="Chọn một khóa học (bắt buộc)">
            <option value=""></option>
            @foreach ($courses as $course)
                <option value="{{ $course->id }}">{{ $course->name }}</option>
            @endforeach
        </select>
        <button class="btn btn-success mt-4" onclick="importUsers()">Import</button>
        <a href="{{ asset('assets/demo/mau-file-nhap-user.xlsx') }}" class="btn bg-lime-400 text-black font-base mt-4">
            Mẫu Excel
        </a>
    </div>
</div>

<div id="popup-manager" class="row bg-title">
    <div class="popup-manager-title-area">
        <h4 class="page-title pull-left popup-manager-title">Import teachers</h4>
    </div>

    <div>
        <input type="file" accept=".xls,.xlsx" />
        <button class="btn btn-success mt-4" onclick="importTeachers()">Import</button>
        <a href="{{ asset('assets/demo/mau_file_nhap_giao_vien.xlsx') }}" class="btn bg-lime-400 text-black font-base mt-4">
            Mẫu Excel
        </a>
    </div>
</div>

<script>
    $(document).ready(function() {
        $.ajaxSetup({
            headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    });

    function importUsers() {
        var file = $('input[type=file]')[0].files[0];
        if (!file) {
            return;
        }
        const data = new FormData();

        if (!$('#course-select').val()) {
            alert('Chọn một khóa học');
            return;
        }

        data.append('course_id', $('#course-select').val());
        data.append('file', $('input[type=file]')[0].files[0]);
        $.ajax({
            url : window.location.origin + '/backend/import-users',
            type: 'POST',
            data: data,
            cache : false,
            processData: false,
            contentType: false,
            success: function (response) {
                alert('Thành công');
            },
            error: function (error) {
                console.log(error)
                alert('Có lỗi xảy ra, vui lòng liên hệ IT');
            }
        });
    }

    function importTeachers() {
        var file = $('input[type=file]')[1].files[0];
        if (!file) {
            return;
        }
        const data = new FormData();
        data.append('file', $('input[type=file]')[1].files[0]);
        $.ajax({
            url : window.location.origin + '/backend/import-teachers',
            type: 'POST',
            data: data,
            cache : false,
            processData: false,
            contentType: false,
            success: function (response) {
                alert('Thành công');
            },
            error: function (error) {
                alert('Có lỗi xảy ra, vui lòng liên hệ IT');
            }
        });
    }
</script>

@stop
