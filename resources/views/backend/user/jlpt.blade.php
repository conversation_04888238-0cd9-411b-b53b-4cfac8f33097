@extends('backend._default.dashboard')

@section('description')
    Quản lý điểm thi JLPT
@stop
@section('keywords')
    jlpt
@stop
@section('author')
    dungmori.com
@stop
@section('title')
    Admin | Quản lý điểm thi JLPT
@stop

@section('assets')
    <script src="{{ asset('/plugin/vuejs-paginate/vuejs-paginate.js') }}"></script>
@stop

@section('content')
    <div id="jlptScore">
        <div class="flex justify-end mt-5">
            <div class="form-group">
                <label>Tìm kiếm</label>
                <input type="text" class="form-control" v-model="key" placeholder="Email, tên, sđt">
            </div>
            <div class="form-group ml-4 mr-4">
                <label>Level (Bài thi)</label>
                <select v-model="levelSearch" class="form-control">
                    <option value="all" selected>Tất cả</option>
                    <option value="N5">N5</option>
                    <option value="N4">N4</option>
                    <option value="N3">N3</option>
                    <option value="N2">N2</option>
                    <option value="N1">N1</option>
                </select>
            </div>
            <div class="form-group ml-4 mr-4">
                <label>Kết quả</label>
                <select v-model="resultSearch" class="form-control">
                    <option value="all" selected>Tất cả</option>
                    <option value="1">Đỗ</option>
                    <option value="0">Trượt</option>
                </select>
            </div>
            <div class="form-group ml-4 mr-4">
                <label>Loại hình</label>
                <select v-model="learnTypeSearch" class="form-control">
                    <option value="all" selected>Tất cả</option>
                    <option value="offline">Offline</option>
                    <option value="basic">Online basic</option>
                    <option value="vip">Online VIP</option>
                </select>
            </div>
            <div class="form-group ml-4 mr-4">
                <label>Trạng thái</label>
                <select v-model="statusSearch" class="form-control">
                    <option value="all" selected>Tất cả</option>
                    <option value="1">Chưa xử lý</option>
                    <option value="2">Đã thưởng</option>
                    <option value="3">Từ chối</option>
                </select>
            </div>
            <div class="form-group">
                <label>Lớp</label>
                <select v-model="classSearch" class="form-control">
                    <option value="all" selected>Tất cả</option>
                    <option value="5">N5</option>
                    <option value="4">N4</option>
                    <option value="3">N3</option>
                    <option value="16">N2</option>
                    <option value="17">N1</option>
                    <option v-for="group in groups" :value="group.group_id">@{{ group.name }}</option>
                </select>
            </div>
            <div class="form-group ml-4">
                <label>&nbsp;</label>
                <button type="button" class="btn btn-primary form-control" style="color: #fff"
                        v-on:click="exportResultExcelFile()">Xuất Excel
                </button>
            </div>
            <div class="form-group ml-4">
                <label>&nbsp;</label>
                <button type="button" class="btn btn-primary form-control" style="color: #fff"
                        v-on:click="openSendMessage">Gửi tin nhắn
                </button>
            </div>
        </div>

        <div>
            <table class="table">
                <tr>
                    <th class="">ID</th>
                    <th class="text-center">Tài khoản</th>
                    <th class="text-center">Level (Bài thi)</th>
                    <th class="text-center">Điểm</th>
                    <th class="text-center">Ảnh</th>
                    <th class="text-center">Khóa học</th>
                    <th class="text-right">Trạng thái</th>
                    <th class="text-right">Note</th>
                    <th class="text-right">Thao tác</th>
                </tr>
                <tr v-for="user in userByPage">
                    <td class="">@{{ user.id }}</td>
                    <td class="text-center">
                        <strong>@{{ user.name }}</strong>
                        <a v-if="user.conversation_id" :href="'{{url('/backend/chat#')}}' + user.conversation_id"
                           target="_blank">
                            <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;"
                               title="nhắn tin"></i>
                        </a>
                        <i v-else :class="'fa fa-comments fa-comments-' + user.id"
                           style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                           @click="initConversation(user.id)"
                        ></i>
                        <br>
                        @{{ user.email }}<br>
                        @{{ user.phone }}
                    </td>
                    <td class="text-center">@{{ user.level }}</td>
                    <td class="text-center">
                        <span style="font-size: 18px; font-weight: bold">@{{ user.score }}</span> <span
                                v-if="user.is_passed"
                                style="padding: 2px 6px; border-radius: 3px; color: #fff; background-color: #10a31a">Đỗ</span>
                        <span v-else style="padding: 2px 6px; border-radius: 3px; color: #fff; background-color: red">Trượt</span>
                        <div v-if="!user.is_passed">
{{--                            Được mở thêm 1 tháng khóa online và giảm 10% khi đăng ký lại cùng cấp độ--}}
                            @{{ user.gift_value == 1 ? 'Tặng 1 tháng' : 'Giảm 10%' }}
                        </div>
                        <div v-else>
                            @{{ getGiftText(user.level, user.score, user.gift_value) }}
                        </div>
                    </td>
                    <td class="text-center">
                        <img style="width: 100px" :src="'{{asset("cdn/achivement/small")}}/' + user.image"
                             v-on:click="reviewImage(user.image)"/>
                    </td>
                    <td class="text-center" style="max-width: 200px; font-size: 13px">
                        <div v-for="course in user.listCourses"
                             style="display: inline-block; padding: 0 6px; border-radius: 3px; margin: 2px; color: #fff; background-color: #10a31a">
                            @{{ course.name }}
                        </div>
                        <div v-for="vipClass in user.listVips"
                             style="display: inline-block; padding: 0 6px; border-radius: 3px; margin: 2px; color: #fff; background-color: #fec107">
                            @{{ vipClass.name }}
                        </div>
                        <div v-for="offClass in user.listOfflines"
                             style="display: inline-block; padding: 0 6px; border-radius: 3px; margin: 2px; color: #fff; background-color: #d31acd">
                            @{{ offClass.product_name }}
                        </div>
                    </td>
                    <td class="text-right">
                        <select :id="'ar-status-' + user.archievement_id" v-model="user.status"
                                @change="onChangeStatus(user.archievement_id)">
                            <option value="1">Chưa xử lý</option>
                            <option value="2">Đã thưởng</option>
                            <option value="3">Từ chối</option>
                        </select>
                    </td>
                    <td class="text-right">
                        <span>@{{ user.note }}</span><i class="fa fa-pencil-square-o" style="padding: 8px"
                                                        aria-hidden="true" @click="showEditNote(user)"></i>
                    </td>
                    <td class="text-right">
                        <div v-if="!user.is_passed && user.gift_value === 1">
                            <select name="" id="" v-model="addMonthCourses">
                                <option v-for="course in user.listCourses" :value="course.course_id">@{{ course.name
                                    }}
                                </option>
                            </select>
                            <button class="btn btn-sm btn-primary" @click="addMonth(user.user_id)">Gia hạn</button>
                        </div>
                    </td>
                </tr>
            </table>
            <paginate
                    v-if="paginator.pageCount > 1"
                    v-model="paginator.curPage"
                    :page-count="paginator.pageCount"
                    :click-handler="changePage"
                    :prevText="'Prev'"
                    :nextText="'Next'"
                    :container-class="'pagination'">
            </paginate>
        </div>

        <div id="show_full_image_archievement" class="modal fade" role="dialog" tabindex="-1"
             style="text-align: center;">
            <img style="display: inline; max-height: 80vh; margin-top: 5vh;" class="image-review"
                 :src="imageUrl && (window.location.origin + '/cdn/achivement/default/' + imageUrl)"/>
        </div>

        <div id="archievement_note" class="modal fade" role="dialog" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Note</h4>
                    </div>
                    <div class="modal-body">
                        <textarea v-model="note" class="form-control"></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                        <button type="button" class="btn btn-primary" v-on:click="saveNote()">Save</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="send_message" class="modal fade show-people" role="dialog" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header flex">
                        <h4 class="modal-title flex-1">Gửi tin nhắn</h4>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <textarea id="mess_content" class="w-full min-h-[80px]"></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-warning" data-dismiss="modal" @click="sendMessage">
                            Gửi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        var users = {!! $users !!};
        var groups = {!! $groups !!};
        Vue.component('paginate', VuejsPaginate);
        var itemPerPage = 10;
        var jlptScore = new Vue({
            el: '#jlptScore',
            data: {
                users: users,
                groups: groups,
                paginator: {
                    pageCount: 0,
                    curPage: 1
                },
                key: '',
                levelSearch: 'all',
                resultSearch: 'all',
                statusSearch: 'all',
                learnTypeSearch: 'all',
                classSearch: 'all',
                imageUrl: '',
                currentArchievementId: 0,
                note: '',
                addMonthCourses: [],
            },
            watch: {
                key() {
                    var vm = this;
                    vm.paginator.curPage = 1;
                    vm.paginator.pageCount = Math.ceil(vm.userBPFilter.length / itemPerPage);
                },
                statusSearch() {
                    var vm = this;
                    vm.paginator.curPage = 1;
                    vm.paginator.pageCount = Math.ceil(vm.userBPFilter.length / itemPerPage);
                },
                learnTypeSearch() {
                    var vm = this;
                    vm.paginator.curPage = 1;
                    vm.paginator.pageCount = Math.ceil(vm.userBPFilter.length / itemPerPage);
                },
                classSearch() {
                    var vm = this;
                    vm.paginator.curPage = 1;
                    vm.paginator.pageCount = Math.ceil(vm.userBPFilter.length / itemPerPage);
                },
                levelSearch() {
                    var vm = this;
                    vm.paginator.curPage = 1;
                    vm.paginator.pageCount = Math.ceil(vm.userBPFilter.length / itemPerPage);
                },
                resultSearch() {
                    var vm = this;
                    vm.paginator.curPage = 1;
                    vm.paginator.pageCount = Math.ceil(vm.userBPFilter.length / itemPerPage);
                }
            },
            computed: {
                userBPFilter() {
                    var vm = this;
                    var temp = [...this.users];

                    if (vm.statusSearch !== 'all') {
                        temp = temp.filter((u) => u.status == vm.statusSearch);
                    }

                    if (vm.learnTypeSearch !== 'all') {
                        switch (vm.learnTypeSearch) {
                            case 'vip':
                                temp = temp.filter((u) => u.listVips.length > 0);
                                break;
                            case 'offline':
                                temp = temp.filter((u) => u.listOfflines.length > 0);
                                break;
                            case 'basic':
                                temp = temp.filter((u) => u.listCourses.length > 0);
                                break;
                            default:
                                break;
                        }
                    }

                    if (vm.levelSearch !== 'all') {
                        temp = temp.filter((u) => u.level == vm.levelSearch);
                    }

                    if (vm.resultSearch !== 'all') {
                        temp = temp.filter((u) => u.is_passed == vm.resultSearch);
                    }

                    var courseId = parseInt(vm.classSearch);
                    if (vm.classSearch !== 'all') {
                        temp = temp.filter((u) => {
                            if (courseId <= 17) {
                                if (u.listCourses && u.listCourses.length > 0 && u.listCourses.find((c) => c.course_id === courseId)) {
                                    return true;
                                }
                            } else if (u.listVips && u.listVips.length > 0 && u.listVips.find((c) => c.group_id === courseId)) {
                                return true;
                            }
                            return false;
                        });
                    }

                    temp = temp.filter((u) => {
                        if (!vm.key) {
                            return true;
                        }
                        if ((u.id + '').includes(vm.key) || (u.name && u.name.toLowerCase().includes(vm.key.toLowerCase())) || (u.email && u.email.toLowerCase().includes(vm.key.toLowerCase())) || (u.phone && u.phone.includes(vm.key))) {
                            return true;
                        }
                        return false;
                    });
                    return temp;
                },
                userByPage() {
                    var temp = [...this.userBPFilter];
                    return temp.splice((this.paginator.curPage - 1) * itemPerPage, itemPerPage);
                }
            },
            mounted() {
                this.paginator.pageCount = Math.ceil(this.users.length / itemPerPage);
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            },
            methods: {
                addMonth(userId) {
                    const vm = this;
                    $.ajax({
                        type: 'post',
                        url: '/backend/archievement/add-month',
                        data: {
                            ids: [vm.addMonthCourses],
                            userId: userId
                        },
                        success: function (response) {
                            alert('Đã gia hạn. Hãy chuyển người dùng này về trạng thái Đã xử lý')
                        }
                    });
                },
                getGiftText(level, point, gift_value) {
                    console.log(`level: `, level);
                    console.log(`point: `, point);
                    console.log(`gift_value: `, gift_value);
                    let gift = '';
                    // if (['N4', 'N5'].includes(level)) {
                    //     if (point <= 129) {
                    //         gift = 'Voucher giảm 200.000đ khi đăng ký khoá học';
                    //     }
                    //     if (point >= 130 && point <= 149) {
                    //         gift = 'Thưởng 200.000đ tiền mặt';
                    //     }
                    //     if (point >= 150) {
                    //         gift = 'Thưởng 600.000đ tiền mặt';
                    //     }
                    // }
                    // if (['N1', 'N2', 'N3'].includes(level)) {
                    // gift_value
                    // 0: giảm 10%
                    // 1: tặng 1 tháng
                    // 2: tặng tiền (đỗ)
                    // 3: voucher (đỗ)
                    if (point < 130) {
                        gift = 'Voucher giảm 200.000đ khi đăng ký khoá học';
                    }
                    if (point >= 130 && point < 150 && gift_value === 2) {
                        gift = 'Thưởng 200.000đ tiền mặt';
                    }
                    if (point >= 130 && point < 150 && gift_value === 3) {
                        gift = 'Voucher giảm 400.000 VND học phí';
                    }
                    if (point >= 150 && point <= 175 && gift_value === 2) {
                        gift = 'Thưởng 500.000đ tiền mặt';
                    }
                    if (point >= 150 && point <= 175 && gift_value === 3) {
                        gift = 'Voucher giảm 700.000 VND học phí';
                    }
                    if (point > 175 && gift_value === 2) {
                        gift = 'Thưởng 1.000.000đ tiền mặt';
                    }
                    if (point > 175 && gift_value === 3) {
                        gift = 'Voucher giảm 1.200.000 VND học phí';
                    }
                    // }
                    return gift;
                },
                changePage(pageNum) {
                    this.paginator.curPage = pageNum;
                },
                initConversation(userId) {
                    $.ajax({
                        type: 'post',
                        url: '/backend/user/create-conversation',
                        data: {
                            'id': userId
                        },
                        success: function (response) {
                            $(".fa-comments-" + userId).css('color', '#00ab2e');
                            window.open(window.location.origin + "/backend/chat#" + response, "_blank");
                        }
                    });
                },
                reviewImage(imageUrl) {
                    this.imageUrl = imageUrl;
                    $('#show_full_image_archievement').modal('toggle');
                },
                showEditNote(user) {
                    this.currentArchievementId = user.archievement_id;
                    this.note = user.note;
                    $('#archievement_note').modal('toggle');
                },
                saveNote() {
                    var vm = this;
                    $.ajax({
                        type: 'post',
                        url: '/backend/archievement/update',
                        data: {
                            'id': vm.currentArchievementId,
                            'note': vm.note
                        },
                        success: function (response) {
                            var temp = vm.users.find((u) => u.archievement_id === vm.currentArchievementId);
                            temp.note = vm.note;
                            $('#archievement_note').modal('toggle');
                        }
                    });
                },
                onChangeStatus(archievementId) {
                    var vm = this;
                    $.ajax({
                        type: 'post',
                        url: '/backend/archievement/update',
                        data: {
                            'id': archievementId,
                            'status': $('#ar-status-' + archievementId).val()
                        },
                        success: function (response) {
                        }
                    });
                },
                exportResultExcelFile() {
                    var vm = this;
                    if (vm.userBPFilter.length === 0) {
                        alert('Không có user nào thỏa mãn');
                        return;
                    }
                    $.post(
                        window.location.origin + '/backend/archievement/export-jlpt-result',
                        {
                            key: vm.key,
                            levelSearch: vm.levelSearch,
                            resultSearch: vm.resultSearch,
                            statusSearch: vm.statusSearch,
                            classSearch: vm.classSearch
                        },
                        function (response, status) {
                            if (response.code == 200) {
                                window.location = '/storage/upload/excel/jlpt_results.xlsx';
                            }
                        }
                    );
                },
                openSendMessage() {
                    if (this.userBPFilter.length === 0) {
                        alert('Không có user nào thỏa mãn');
                        return;
                    }
                    $('#send_message').modal('toggle');
                    setTimeout(() => {
                        $('#mess_content').focus();
                    }, 500);
                },
                sendMessage() {
                    if (this.userBPFilter.length === 0) {
                        alert('Không có user nào thỏa mãn');
                        return;
                    }
                    var ids = this.userBPFilter.map((u) => u.id);
                    $.post(
                        window.location.origin + '/backend/archievement/send-message',
                        {
                            message: $('#mess_content').val(),
                            ids: ids
                        },
                        function (response, status) {
                        }
                    );
                }
            },
        });

        $(document).keydown(function (event) {
            if (event.keyCode == 27) {
                $('#show_full_image_archievement').modal('hide');
                $('#archievement_note').modal('hide');
            }
        });
    </script>
@stop
