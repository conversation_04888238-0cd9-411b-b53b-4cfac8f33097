@extends('backend._default.dashboard')

@section('title') Admin | Quản lý <PERSON> @stop
@section('description') Quản lý <PERSON> @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script> 
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
@stop

@section('content') 

<div id="image-manager" class="row bg-title">
	<div class="image-manager-title">
		<h4 class="page-title pull-left">Quản lý ảnh</h4>
	</div>
    <div class="col-md-3 mage-manager-left">
    	<span id="all" class="folder" v-on:click="switchFolder('all')">Tất cả</span>
    	<div class="subfolder">
    		<span id="blog" class="folder" v-on:click="switchFolder('blog')">Blog</span><br>
    		<span id="combo" class="folder" v-on:click="switchFolder('combo')">Combo</span><br>
    		<span id="course" class="folder" v-on:click="switchFolder('course')">Khóa học</span><br>
    		<span id="lesson" class="folder" v-on:click="switchFolder('lesson')">Bài học</span><br>
    		<span id="teacher" class="folder" v-on:click="switchFolder('teacher')">Giáo viên</span><br>
    		<span id="teacher_info" class="folder" v-on:click="switchFolder('teacher_info')">Giới thiệu giáo viên</span><br>
    		<span id="ckeditor_info" class="folder" v-on:click="switchFolder('ckeditor_info')">Ảnh upload trên Ckeditor</span><br>
    	</div>
    </div>
    <p class="link">@{{ link }}</p>
    <button class="delete-image btn btn-danger" v-on:click="deleteImage">Xóa Ảnh</button>
    <div class="col-md-9 image-manager-content">
		<img v-if="currentFolder == 'blog' || currentFolder == 'all'" v-for="image in listBlogImgs" class="image-manager-item" {{-- src="{{url('assets/img/icon_backend/no_image.png')}}" --}} :src="url + '/'+ image" v-on:click="showLink(image)">
		<img v-if="currentFolder == 'combo' || currentFolder == 'all'" v-for="image in listComboImgs" class="image-manager-item" {{-- src="{{url('assets/img/icon_backend/no_image.png')}}" --}} :src="url + '/'+ image" v-on:click="showLink(image)">
		<img v-if="currentFolder == 'course' || currentFolder == 'all'" v-for="image in listCourseImgs" class="image-manager-item" {{-- src="{{url('assets/img/icon_backend/no_image.png')}}" --}} :src="url + '/'+ image" v-on:click="showLink(image)">
		<img v-if="currentFolder == 'lesson' || currentFolder == 'all'" v-for="image in listLessonImgs" class="image-manager-item" {{-- src="{{url('assets/img/icon_backend/no_image.png')}}" --}} :src="url + '/'+ image" v-on:click="showLink(image)">
		<img v-if="currentFolder == 'teacher' || currentFolder == 'all'" v-for="image in listTeacherImgs" class="image-manager-item" {{-- src="{{url('assets/img/icon_backend/no_image.png')}}" --}} :src="url + '/'+ image" v-on:click="showLink(image)">
		<img v-if="currentFolder == 'teacher_info' || currentFolder == 'all'" v-for="image in listTeacherInfoImgs" class="image-manager-item" {{-- src="{{url('assets/img/icon_backend/no_image.png')}}" --}} :src="url + '/'+ image" v-on:click="showLink(image)">
		<img v-if="currentFolder == 'ckeditor_info' || currentFolder == 'all'" v-for="image in listCkeditorImgs" class="image-manager-item" {{-- src="{{url('assets/img/icon_backend/no_image.png')}}" --}} :src="url + '/'+ image" v-on:click="showLink(image)">
    </div>
</div>
<script>

var user = new Vue({
	el: '#image-manager',
	data: function () {
        return {
			url: window.location.origin,
			listBlogImgs		:	{!! json_encode($listBlogImgs) !!},
			listComboImgs		:	{!! json_encode($listComboImgs) !!},
			listCourseImgs		:	{!! json_encode($listCourseImgs) !!},
			listLessonImgs		:	{!! json_encode($listLessonImgs) !!},
			listTeacherImgs 	:	{!! json_encode($listTeacherImgs) !!},
			listTeacherInfoImgs	:	{!! json_encode($listTeacherInfoImgs) !!},
			listCkeditorImgs	:	{!! json_encode($listCkeditorImgs) !!},
			currentFolder		:	"all",
			link				:	""
		}
	},

	methods: {
		switchFolder(folder) {
			var vm = this;
			vm.currentFolder = folder;
			vm.link = "";

			$('#blog').removeClass("avtive-folder");
			$('#combo').removeClass("avtive-folder");
			$('#course').removeClass("avtive-folder");
			$('#lesson').removeClass("avtive-folder");
			$('#teacher').removeClass("avtive-folder");
			$('#teacher_info').removeClass("avtive-folder");
			$('#ckeditor_info').removeClass("avtive-folder");
			$('#all').removeClass("avtive-folder");
			$('#' + folder).addClass("avtive-folder");

		},
		deleteImage(){
			var vm = this;
			if(vm.link != ''){
				$.ajax({
		            url: window.location.origin + "/backend/image/delete", type:"GET", data:{'name' : vm.link.replace(vm.url,'')}, async: true,
		            error: function() {
		            	console.log("Có lỗi xảy ra");
		            },
		            success: function(response) {
		            	alert('Xóa thành công !');
		            	location.reload();
		            }
		        });
			}
		},
		showLink(link) {
			var vm = this;
			vm.link = vm.url + "/" + link;
		}
	},

	mounted() {
		var vm = this;

		vm.listAll = vm.listBlogImgs.concat(vm.listComboImgs, vm.listCourseImgs, vm.listLessonImgs, vm.listTeacherImgs, vm.listTeacherInfoImgs);
		$('#all').addClass("avtive-folder");

		// lazy load - hiện tại chưa cần vì chỉ load ảnh ở blog, course, ... ==> ít ảnh
		// let images = document.querySelectorAll(".image-manager-item");
		// lazyload(images);
	}
});



</script>

@stop