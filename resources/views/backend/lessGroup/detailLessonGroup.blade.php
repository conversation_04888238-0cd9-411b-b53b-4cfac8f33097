<table class="table table-borderless" id="table">
    <thead>
        <tr>
            <th class="text-center"><PERSON><PERSON></th>
            <th class="text-center"><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> học</th>
            <th class="text-center"><PERSON><PERSON><PERSON><PERSON> học</th>
            <th class="text-center">Trạng thái</th>
            <th class="text-center">Hành động</th>
        </tr>
    </thead>
    @foreach ($lessGroup as $item)
        <tr class="item{{ $item->id }}">
            <td class="text-center">{{ $item->id }}</td>
            <td class="text-center text-left">
                {{ $item->name }}
                @if ($item->is_step == 1)
                    <span class="label label-success">G<PERSON>i đo<PERSON>n</span>
                @elseif($item->is_specialezed == 1)
                    <span class="label label-info">chuyên ngành</span>
                @endif
            </td>
            <td class="text-center">
                <a href="{{ url('/khoa-hoc/' . $item->SEOurl) }}" target="_blank"> {{ $item->course }} </a>
            </td>
            <td class="text-center">
                @if ($item->show == 1)
                    <span class="label label-success">Bật</span>
                @elseif ($item->show == 2)
                    <span class="label label-info">Testing</span>
                @else
                    <span class="label label-danger">Tắt</span>
                @endif
            </td>

            <td class="text-center">
                @if (json_decode(Auth::guard('admin')->user()->matrix)->group->edit != null)
                    <button class="edit-modal btn btn-info" data-info="{{ json_encode($item) }}">
                        <span class="glyphicon glyphicon-edit"></span> Sửa
                    </button>
                @endif

                @if (json_decode(Auth::guard('admin')->user()->matrix)->group->delete != null)
                    <button class="delete-modal btn btn-danger" data-info="{{ json_encode($item) }}">
                        <span class="glyphicon glyphicon-trash"></span> Xóa
                    </button>
                @endif

                @if (json_decode(Auth::guard('admin')->user()->matrix)->group->sort != null)
                    <button class="lesson-list btn btn-success" data-info="{{ $item->id }}">
                        <span class="glyphicon glyphicon-th-list"></span> Danh sách bài học
                    </button>
                @endif
            </td>
        </tr>
    @endforeach
</table>
