@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON>n lý n<PERSON>óm b<PERSON><PERSON> h<PERSON> @stop
@section('keywords') lesson group @stop
@section('author') dungmori.com @stop
@section('title') Admin | Nhóm bài học @stop

@section('assets')
    <link type="text/css" rel="stylesheet"
        href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}">
    </script>
    <script type="text/javascript"
        src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/css/bootstrap-select.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/js/bootstrap-select.min.js"></script>
    <style type="text/css">
        .row {
            font-family: Myriad Pro, sans-serif;
            font-weight: 500;
        }

        .btn {
            padding: 4px 6px 2px 6px;
            border-radius: 3px;
            margin: 0 0 0 3px;
            font-size: 12px;
        }

        .label {
            padding: 4px 6px 4px 8px;
            margin-right: 4px;
            cursor: pointer;
            border-radius: 3px;
        }

        .label-success {
            background: #10a31a;
        }

        .label-danger,
        .btn-danger {
            background: #e74c3c;
        }

        .table {
            background-color: #fff;
        }

        .text-left {
            text-align: left;
        }

        .dropdown-toggle {
            height: 40px;
        }
    </style>
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Nhóm bài học
        </h4>
        @if (json_decode(Auth::guard('admin')->user()->matrix)->group->add != null)
            <button class="add-modal btn btn-success" style="right: 26px;position: absolute;width: 146px;">
                <span class="glyphicon glyphicon-plus"></span>Thêm mới
            </button>
        @endif
    </div>

    <div class="less_group_table">
        @include('backend.lessGroup.detailLessonGroup')
    </div>
    <div id="pageModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form">
                        <li class="global_error text-left hidden"></li>
                        <div class="form-group maso">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="fid">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="course">Khóa học</label>
                            <div class="col-sm-8">
                                <select class="selectpicker form-control" multiple data-live-search="false"
                                    name="multiCourse" id="multiCourse">
                                    @foreach ($listCourse as $course)
                                        <option value="{{ $course->id }}">{{ $course->name }}</option>
                                    @endforeach
                                </select>
                                <select class="form-control" name="course" id="course">
                                    @foreach ($listCourse as $course)
                                        <option value="{{ $course->id }}">{{ $course->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="lesson_group">Nhóm bài học</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="lesson_group">
                            </div>*new ≈ <span class="label label-danger">new</span>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="description">Mô tả</label>
                            <div class="col-sm-8">
                                <textarea type="text" rows="3" class="form-control" id="description"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="description">Loại</label>
                            <div class="col-sm-8 pt-1.5">
                                <select name="skill" id="skill">
                                    <option value="">-Loại-</option>
                                    <option value="tuvung">Từ vựng</option>
                                    <option value="chuhan">Chữ hán</option>
                                    <option value="nguphap">Ngữ pháp</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="description">Số lượng thành phần</label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" id="component_count">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="lesson_group">Kiểu nhóm</label>
                            <div class="col-sm-8">
                                {{-- <div class="form-group is_type" style="display: inline-flex;padding-left: 135px;"> --}}
                                <input type="checkbox" id="is_step">&nbsp;&nbsp;
                                <label class="d-inline" for="is_step" style="cursor: pointer;">Là giai
                                    đoạn</label>&nbsp;&nbsp;
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="lesson_group">Luyện đề</label>
                            <div class="col-sm-8">
                                <select name="nhomld" id="nhomld">
                                    <option value="">-Nhóm thường-</option>
                                    <option value="ldkn">Luyện đề kỹ năng</option>
                                    <option value="ldth">Luyện đề tổng hợp</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="status">Trạng thái</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0">
                                    <span class="labels">Tắt</span>
                                </label>&nbsp;&nbsp;
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1">
                                    <span class="labels">Bật</span>
                                </label>&nbsp;&nbsp;
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_testing" name="status"
                                        value="2">
                                    <span class="labels">Testing</span>
                                </label>
                            </div>
                        </div>
                    </form>

                    <div class="deleteContent">
                        Có muốn xóa nhóm khóa học "<span class="dname"></span>" này đi không? <span
                            class="hidden did"></span>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div id="sortLesson" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4>Sắp xếp bài học theo nhóm bài học</h4>
                </div>
                <div class="modal-body" id="body-lesson">
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        });
        //set cho datatable mac dinh
        setDataTable();

        function setDataTable() {
            $('#table').DataTable({
                "order": [
                    [0, "desc"]
                ],
                "stateSave": true
            });
        }
        //add
        $(document).on('click', '.add-modal', function() {
            $('.maso').hide();
            $('#course').hide();
            $('#multiCourse').selectpicker('show');
            $('#footer_action_button').text(" Thêm");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').addClass('add');
            $('.modal-title').text('Thêm');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            fillmodalData('');
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '70%');
            $('#pageModal').css('left', '15%');
        });

        //edit
        $(document).on('click', '.edit-modal', function() {
            $('.maso').show();
            $('#course').show();
            $('#multiCourse').selectpicker('hide');
            $('#fid').attr('disabled', true);
            $('#footer_action_button').text(" Sửa");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('edit');
            $('.modal-title').text('Sửa');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            var group = $(this).data('info');
            fillmodalData(group);
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '70%');
            $('#pageModal').css('left', '15%');
        });
        //delete
        $(document).on('click', '.delete-modal', function() {
            $('#footer_action_button').text(" Xóa");
            $('#footer_action_button').removeClass('glyphicon-check');
            $('#footer_action_button').addClass('glyphicon-trash');
            $('.actionBtn').removeClass('btn-success');
            $('.actionBtn').addClass('btn-danger');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('delete');
            $('.modal-title').text('Xóa');
            $('.deleteContent').show();
            $('.form-horizontal').hide();
            var group = $(this).data('info');
            $('.did').text(group.id);
            $('.dname').html(group.name);
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '50%');
            $('#pageModal').css('left', '25%');
        });

        //gan du lieu vào field
        function fillmodalData(group) {

            console.log(group);

            //nếu là add -> reset trạng thái các field
            if (group == '') {
                $('#course').val(3);
                $('#description').val('');
                $('#component_count').val('');
                $('#lesson_group').val('');
                $('#status_on').prop('checked', true);
                $('#is_step').prop('checked', false);
                $("#nhomld select").val('');
                $("#skill").val('');

                //nếu là sửa -> fill giá trị
            } else {
                $('#fid').val(group.id);
                $('#course').val(group.course_id);
                $('#lesson_group').val(group.name);
                $('#description').val(group.description);
                $('#component_count').val(group.component_count);
                switch (group.show) {
                    case 0:
                        $('#status_off').prop('checked', true);
                        break;
                    case 1:
                        $('#status_on').prop('checked', true);
                        break;
                    case 2:
                        $('#status_testing').prop('checked', true);
                        break;
                    default:
                        $('#status_off').prop('checked', true);
                        break;
                }
                (group.is_step == 0) ? $('#is_step').prop('checked', false): $('#is_step').prop('checked', true);
                (group.type_ld == null) ? $("#nhomld").val(''): $("#nhomld").val(group.type_ld);
                (group.skill == null) ? $("#skill").val(''): $("#skill").val(group.skill);
            }
            $('.global_error').addClass('hidden');
        }

        $('.modal-footer').on('click', '.add', function() {

            console.log("thêm nhóm ld: ", $('#nhomld').find(":selected").val());

            $.ajax({
                type: 'post',
                url: '/backend/lesson-group/create',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'lesson_group': $('#lesson_group').val(),
                    'description': $('#description').val(),
                    'component_count': $('#component_count').val(),
                    'is_step': $('#is_step').is(":checked"),
                    'type_ld': $('#nhomld').find(":selected").val(),
                    'skill': $('#skill').find(":selected").val(),
                    'show': $('input[name=status]:checked').val(),
                    'multiCourse': $('#multiCourse').val(),
                },
                success: function(data) {
                    if (data.errors) {
                        if (Object.keys(data.errors).length > 0) {
                            $('#pageModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    } else {
                        $('#pageModal').modal('hide');
                        $('.less_group_table').html(data);
                        setDataTable();
                    }
                }
            });
        });

        $('.modal-footer').on('click', '.edit', function() {

            console.log("sửa nhóm ld: ", $('#nhomld').find(":selected").val());
            $.ajax({
                type: 'post',
                url: '/backend/lesson-group/edit',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('#fid').val(),
                    'course': $('#course').val(),
                    'lesson_group': $('#lesson_group').val(),
                    'description': $('#description').val(),
                    'component_count': $('#component_count').val(),
                    'is_step': $('#is_step').is(":checked"),
                    'type_ld': $('#nhomld').find(":selected").val(),
                    'skill': $('#skill').find(":selected").val(),
                    'show': $('input[name=status]:checked').val()
                },
                success: function(data) {
                    if (data.errors) {
                        if (Object.keys(data.errors).length > 0) {
                            $('#pageModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    } else {
                        $('#pageModal').modal('hide');
                        $('.less_group_table').html(data);
                        setDataTable();
                    }
                }
            });
        });

        $('.modal-footer').on('click', '.delete', function() {
            $.ajax({
                type: 'post',
                url: '/backend/lesson-group/delete',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('.did').text()
                },
                success: function(data) {
                    $('#pageModal').modal('hide');
                    $('.item' + $('.did').text()).remove();
                }
            });
        });

        $(document).on('click', '.lesson-list', function() {
            var id = $(this).data('info');
            $.ajax({
                type: 'post',
                url: '/backend/lesson-list',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': id
                },
                success: function(data) {
                    $('#body-lesson').html(data);
                    sortTable('lesson', 1, 5, '/backend/lesson-sort-order', 'sort_order',
                        'lesson_table');
                    $('#sortLesson').modal('show');
                }
            });
        });
    </script>

@stop
