<table>
    <thead>
    <tr>
        <td>M<PERSON></td>
        <td>Họ và tên</td>
        <td><PERSON><PERSON><PERSON> vào làm</td>
        <td>Thâm niên</td>
        <td>Tổng số lớp</td>
        <td>C<PERSON><PERSON> độ</td>
        <td>Lương cơ bản</td>
        <td>Lớ<PERSON></td>
        <td>Áp dụng từ</td>
        <td>Loại tiền</td>
        <td>Số ngày công</td>
        <td>Lương theo số ngày công</td>
        <td>Thưởng HVLL</td>
        <td>Thưởng</td>
        <td>Lỗi phạt</td>
        <td>Lương thực nhận</td>
        <td>Thuế</td>
        <td>Lương thực trả</td>
        <td>Tổng lương</td>
        <td><PERSON><PERSON> chú</td>
        <td><PERSON><PERSON> chú của kế toán</td>
    </tr>
    </thead>
    <tbody>
    @foreach ($users as $user)
        <tr>
            <td>{{ $user['id'] }}</td>
            <td>{{ $user['last_name'] }} {{ $user['first_name'] }}</td>
            <td>{{ $user['join_date'] }}</td>
            <td>
                @if(isset($user['join_time']))
                    <span>{{ $user['join_time'] }} Tháng</span>
                @else
                    <span>--</span>
                @endif
            </td>
            <td>{{ $user['total_group'] }}</td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['level'] }}</li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['base_salary'] }}</li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['group'] ? $salary['group']['name'] : '--' }}</li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['valid_from'] }}</li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['currency'] }}</li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ count($salary['work_days']) ?? '--' }}</li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>
                            {{ count($salary['work_days']) * $salary['base_salary'] ?? '--'}}
                        </li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['ref_reward'] ?? '--'}} </li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['rewards'] ?? '--'}} </li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ $salary['faults'] ?? '--'}} </li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>{{ count($salary['work_days']) * $salary['base_salary'] + $salary['ref_reward'] + $salary['rewards'] - $salary['faults'] ?? '--'}}</li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>
                            {{ $salary['currency'] == 'vnd'
                                ? round(((count($salary['work_days']) * $salary['base_salary'] + $salary['ref_reward'] + $salary['rewards'] - $salary['faults']) * ($user['tax']['value'] ?? 0) / 100) / 1000)*1000
                            : round(((count($salary['work_days']) * $salary['base_salary'] + $salary['ref_reward'] + $salary['rewards'] - $salary['faults']) * ($user['tax']['value'] ?? 0) / 100)) }}
                        </li>
                    @endforeach
                </ul>
            </td>
            <td>
                <ul>
                    @foreach ($user['salaries'] as $salary)
                        <li>
                            {{
                                round(count($salary['work_days']) * $salary['base_salary'] + $salary['ref_reward'] + $salary['rewards'] - $salary['faults']) -
                                ($salary['currency'] == 'vnd'
                                ? round(((count($salary['work_days']) * $salary['base_salary'] + $salary['ref_reward'] + $salary['rewards'] - $salary['faults']) * ($user['tax']['value'] ?? 0) / 100) / 1000)*1000
                                : round(((count($salary['work_days']) * $salary['base_salary'] + $salary['ref_reward'] + $salary['rewards'] - $salary['faults']) * ($user['tax']['value'] ?? 0) / 100)))
                                 ?? '--'
                             }}
                        </li>
                    @endforeach
                </ul>
            </td>
            <td>
                {{ $user['total_salary'] }}
            </td>
            <td>{{ $user['note'] }}</td>
            <td>{{ $user['note_by_accountant'] }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
