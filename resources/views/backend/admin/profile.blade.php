@extends('backend._default.dashboard')

@section('description') Quản lý tài kho<PERSON>n @stop
@section('keywords') account @stop
@section('author') dungmori.com @stop
@section('title') Admin | quản lý tài khoản @stop

@section('assets')
@stop

@section('content')

	<div class="row bg-title">
        <h4 class="page-title pull-left">
            T<PERSON>i khoản Admin
        </h4> 
    </div>
    <div class="row">
        <div class="col-md-4 col-xs-12">
            <div class="white-box">
                <div class="user-bg" style="top:15px;">
                    <img src="{{url('/assets/img/profile-menu.png')}}" width="100%" alt="user">
                    <div class="overlay-box">
                        <div class="user-content" style="text-align: center">
                            <div style="margin-bottom: 10px;">
                                <img class="thumb-lg img-circle" alt="img" id="user-avatar-preview"
                                      @if(Auth::user()->avatar == null)
                                      src="{{url('assets/img/default-avatar.jpg')}}"
                                      @else
                                      src="{{url('cdn/avatar/default')}}/{{ Auth::user()->avatar}}"
                                        @endif
                                />
                            </div>

                            <span class="btn btn-info change-avatar" >
                                Thay ảnh đại diện
                            </span>

                            <span class="clear-preview-upload btn btn-youtube" style="display: none;">Reset</span>

                            <span class="btn btn-success save-admin-avatar" style="display: none;">
                                <i class="zmdi zmdi-cloud-upload"></i> Lưu ảnh
                            </span>

                            <form id="avatar-form">
                                @if(Auth::check())
                                    <input name="_token" type="hidden" id="csrf_token" value="{{ csrf_token() }}">
                                @endif
                                <input type='file' id="inputAvatar" name="inputAvatar" style="display: none;" onchange="readURL(this);" />
                            </form>
                            <h4 class="text-white"></h4>
                            <h5 class="text-white" style="text-shadow:-1px -1px 0 #000,1px -1px 0 #000,-1px 1px 0 #000,1px 1px 0 #000;">{{$admin[0]->email}}</h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8 col-xs-12">
            <div class="white-box">
                <ul class="nav nav-tabs tabs customtab">
                    <li class="active tab">
                        <a href="#information" data-toggle="tab"> 
                            <span class="visible-xs"><i class="fa fa-home"></i></span> 
                            <span class="hidden-xs">Thông tin tài khoản</span> 
                        </a> 
                    </li>
                    <li class="tab">
                        <a href="#settings" data-toggle="tab" aria-expanded="false"> 
                            <span class="visible-xs"><i class="fa fa-cog"></i></span> 
                            <span class="hidden-xs">Cài đặt</span> 
                        </a> 
                    </li>
                </ul>
                <div class="tab-content">
                    <div class="tab-pane active" id="information">
                        <div class="row">
                            <div class="col-md-3 col-xs-6 b-r"> <strong>Tên đầy đủ</strong> <br>
                                <p class="text-muted">{{ $admin[0]->name }}</p>
                            </div>
                            <div class="col-md-3 col-xs-6 b-r"> <strong>Điện thoại</strong> <br>
                                <p class="text-muted">{{ $admin[0]->phone }}</p>
                            </div>
                            <div class="col-md-3 col-xs-6 b-r"> <strong>Email</strong> <br>
                                <p class="text-muted">{{ $admin[0]->email }}</p>
                            </div>
                            <div class="col-md-3 col-xs-6"> <strong>Ngày sinh</strong> <br>
                                <p class="text-muted">{{ $admin[0]->birthday }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane" id="settings">
                        <form method="POST" action="{{url('backend/account/update')}}" accept-charset="UTF-8" class="form-horizontal form-material">
                            <input name="_token" type="hidden" value="{{ csrf_token() }}">
                            <input type="hidden" name="id" value="{{ $admin[0]->id }}">
                            <div class="form-group" >
                                <label for="first_name">Tên đăng nhập</label>
                                <input class="form-control" placeholder="Họ" name="username" type="text" value="{{ $admin[0]->username }}" required>                                
                            </div>
                            
                            <div class="form-group" >
                                <label for="last_name">Tên đầy đủ</label>
                                <input class="form-control" placeholder="Tên" name="name" type="text" value="{{ $admin[0]->name }}" required>
                            </div>
                            <div class="form-group" >
                                <label for="birthday">Ngày sinh</label>
                                <input class="form-control datepicker-autoclose" data-mask="99/99/9999" placeholder="Ngày sinh" name="birthday" type="text" value="{{ $admin[0]->birthday }}" required>
                            </div>
                        
                            <div class="form-group" >
                                <label for="email">Email</label>
                                <input class="form-control" placeholder="Email" name="email" type="email" value="{{ $admin[0]->email }}" required>
                            </div>

                            <div class="form-group" >
                                <label for="phone">Số điện thoại</label>
                                <input class="form-control" placeholder="số điện thoại" name="phone" type="text" value="{{ $admin[0]->phone }}" required>
                            </div>

                            <div class="form-group" >
                                <label for="address">Địa chỉ</label>
                                <input class="form-control" placeholder="Địa chỉ" name="address" type="text" value="{{ $admin[0]->address }}" required>
                            </div>

                            <div class="form-group" >
                                <label for="description">Mô tả</label>
                                <input class="form-control" placeholder="Mô tả" name="description" type="text" value="{{ $admin[0]->desc }}" required>
                            </div>

                            <div class="form-group" >
                                <label class="form-control  glyphicon glyphicon-hand-down" style="color: red;">Nếu muốn đổi mật khẩu hãy nhập dưới đây !</label>
                            </div>

                            <div class="form-group" >
                                <label for="password">Mật khẩu mới</label>
                                <input pattern=".{6,20}" class="form-control" placeholder="CHÚ Ý phải nhập mật khẩu từ 6 kí tự trở lên" name="password" type="password" id="password">
                            </div>

                            <div class="form-group" >
                                <label for="confirm_password">Xác nhận mật khẩu mới</label>
                                <input pattern=".{6,20}" class="form-control" placeholder="Xác nhận mật khẩu mới" name="confirm_password" type="password" id="confirm_password">
                                <span id='message'></span>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-outline fcbtn btn-1e  btn-primary" id="enter">
                                    <i class="ti-save m-r-10 vertical-middle"></i><span>Cập nhật</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        $('#password, #confirm_password').on('keyup', function () {
            if ($('#password').val() == $('#confirm_password').val()) {
                $('#message').html('Mật khẩu khớp').css('color', 'green');
            } else 
                $('#message').html('Mật khẩu không khớp, xin hãy nhập lại').css('color', 'red');
            });
    </script>
    <script type="text/javascript">
        var userAvatar = "";
        @if(Auth::user()->avatar == null)
            userAvatar ="{{url('assets/img/default-avatar.jpg')}}";
        @else
            userAvatar ="{{url('cdn/avatar/default')}}/{{ Auth::user()->avatar}}";
        @endif
    </script>
    <script src="{{asset('assets/js/avatar.js')}}?{{filemtime('assets/js/avatar.js')}}"></script>
@stop
