<table class="table table-borderless" id="table">
    <thead>
        <tr>
            <th class="text-center"><PERSON><PERSON></th>
            <th class="text-center">Tên</th>
            <th class="text-center">Email</th>
            <th class="text-center"><PERSON><PERSON><PERSON><PERSON> hạn</th>
            <th class="text-center">Hành động</th>
        </tr>
    </thead>
    @foreach($admin as $item)
    <tr class="item{{$item->id}}">
        <td class="text-center">{{$item->id}}</td>
        <td class="text-center">{{$item->name}}</td>
        <td class="text-center">{{$item->email}}</td>
        <td class="text-center">
            @if($item->permission == 1)
                <span class="label label-success">supper admin</span>
            @else
                <span class="label label-danger">admin</span>
            @endif
        </td>
        <td class="text-center">
            <button class="edit-modal btn btn-info" 
                onclick="location.href='{{url('backend/account/profile/'.$item->id)}}'">
                <span class="glyphicon glyphicon-edit"></span> Sửa
            </button>
            @if($item->permission == 0)
                <button class="delete-modal btn btn-danger"
                    data-info="{{$item}}">
                    <span class="glyphicon glyphicon-trash"></span> Xóa
                </button>
                <button class="permission-modal btn btn-success"
                    data-info="{{$item}}">
                    <span class="glyphicon glyphicon-grain"></span> Phân quyền
                </button>
            @endif
        </td>
    </tr>
    @endforeach
</table>