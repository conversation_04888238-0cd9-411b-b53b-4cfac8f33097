@extends('backend._default.dashboard')

@section('description') Quản lý lịch sử hoạt động @stop
@section('keywords') history @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý lịch sử hoạt động @stop

@section('assets')
    <script src="{{ asset('/plugin/vuejs-paginate/vuejs-paginate.js') }}"></script>
@stop

@section('content')
    <div id="root-history">
        <div class="row bg-title">
            <h4 class="page-title pull-left">  Quản lý lịch sử hoạt động</h4>
            <form class="form-inline" style="float: right;" @submit.prevent>
                {{ csrf_field() }}
                <div class="form-group">
                    <input type="text" class="form-control mb-2 mr-sm-2" placeholder=" Nhập Mã đơn hàng" v-model="filters.voucherId" @keydown.enter="filterHistory">
                </div>
                <button type="button" class="btn btn-success" @click="filterHistory">Tìm</button>
                <button type="button" class="btn btn-primary" @click="resetFilter">Clear</button>
            </form>
        </div>

        <table class="table table-borderless">
            <thead>
                <tr>
                    <th>Tên admin</th>
                    <th>Email</th>
                    <th>Trang truy cập</th>
                    <th>Thời gian truy cập</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="action in actions">
                    <td>@{{ (action.admin) ? action.admin.name : 'Admin đã xóa' }}</td>
                    <td>@{{ (action.admin) ? action.admin.email : 'Admin đã xóa' }}</td>
                    <td>@{{ action.url }}</td>
                    <td>@{{ action.created_at }}</td>
                </tr>
            </tbody>
        </table>

        <paginate
                v-if="paginator.pageCount > 1"
                v-model="paginator.curPage"
                :page-count="paginator.pageCount"
                :click-handler="changePage"
                :prevText="'Prev'"
                :nextText="'Next'"
                :container-class="'pagination'">
        </paginate>
    </div>

    <script>
        $.ajaxSetup({
            headers: {

            }
        });

        Vue.component('paginate', VuejsPaginate);

        new Vue({
            el: '#root-history',

            data: {
                actions: [],
                paginator: {
                    pageCount: 0,
                    curPage: 1
                },
                filters: {
                    voucherId: ''
                }
            },

            mounted() {
                this.getHistory();
            },

            methods: {
                getHistory(pageNum = 1) {
                    var queryString = {
                        page: pageNum,
                        voucherId: this.filters.voucherId,
                        limit: 20,
                        _token: '{{ csrf_token() }}'
                    };
                    $.get(window.location.origin + '/backend/admin/history/list', queryString , (response) => {
                        this.actions = response.data;
                        this.paginator.pageCount = response.last_page;
                    });
                },

                changePage(pageNum) {
                    this.paginator.curPage = pageNum;
                    this.getHistory(pageNum);
                },

                filterHistory() {
                    this.getHistory();
                },

                resetFilter() {
                    this.filters.voucherId = '';
                    this.paginator.curPage = 1;
                    this.getHistory();
                }
            }
        });
    </script>
@stop
