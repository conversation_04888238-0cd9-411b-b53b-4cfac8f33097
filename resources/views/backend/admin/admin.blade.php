@extends('backend._default.dashboard')

@section('description') Quản lý admin @stop
@section('keywords') admin @stop
@section('author') dungmori.com @stop
@section('title') Admin @stop

@section('assets')
@stop

@section('content') 
	<div class="row bg-title">
        <h4 class="page-title pull-left">
            Quản lý admin
        </h4>
        <button class="add-modal btn btn-success"
                style="float: right">
            <span class="glyphicon glyphicon-plus"></span>Thêm mới
        </button>
        <a href="{{ route('backend.history') }}" class="btn btn-primary"
                style="float: right; margin-right: 1%">
            <span class="glyphicon glyphicon-list-alt"></span> Lịch sử hành động
        </a>
    </div>
    <div class="admin_area">   
        @include('backend.admin.detailAdmin')
	</div>
    <div id="myModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Xóa</h4>
                </div>
                <div class="modal-body">
                    <div class="deleteContent">
                        Có muốn xóa admin "<span class="admin_name"></span>" đi không? <span
                            class="hidden admin_id"></span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-danger delAdmin" data-dismiss="modal">
                            <span class='glyphicon glyphicon-trash'>Xóa</span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('backend.admin.permission')
    @include('backend.admin.adminAdditon')
	<script>
    	$(document).ready(function() {
    	  	$.ajaxSetup({
    		    headers: {
    		      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    		    }
    	  	});	  
    	});
        //password
        $('#password, #confirm_password').on('keyup', function () {
            if ($('#password').val() == $('#confirm_password').val()) {
                $('#message').html('Mật khẩu khớp').css('color', 'green');
            } else {
                $('#message').html('Mật khẩu không khớp, xin hãy nhập lại').css('color', 'red');
            }
        });
        //delete admin
        $(document).on('click', '.delete-modal', function() {
            var admin = $(this).data('info');
            $('.admin_name').text(admin.name);
            $('.admin_id').text(admin.id);
            $('#myModal').modal('show');
        });
        $('.modal-footer').on('click', '.delAdmin', function() {
            $.ajax({
                type: 'post',
                url: '/backend/admin/delete',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('.admin_id').text()
                },
                success: function(data) {
                    $('.item' + $('.admin_id').text()).remove();
                }
            });
        });
        // them admin
        $(document).on('click', '.add-modal', function() {
            $('#adminModal').modal('show');
            $('.global_error').addClass('hidden');
        });
        $('.modal-footer').on('click', '.adAdmin', function() {
            var content = new FormData($("#form_admin")[0]);
            $.ajax({
                type: 'post',
                url: '/backend/admin/add',
                processData: false,
                contentType: false,
                data : content,
                success: function(data) {
                    if (data.errors){
                        if(Object.keys(data.errors).length > 0){
                            $('#adminModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    }else if(data == 'fail'){
                        $('#adminModal').modal('show');
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Mật khẩu phải lớn hơn 6 kí tự !");
                    }else {
                        $('#adminModal').modal('hide');
                        $('.admin_area').html(data);
                    }
                }
            });
        });
        //phan quyen
        $(document).on('click', '.permission-modal', function() {
            var admin = $(this).data('info');
            $('#admin_id').val(admin.id);
            var permission = JSON.parse(admin.matrix);
            setValueCheckbox(permission.course.add, 'course1');
            setValueCheckbox(permission.course.edit, 'course2');
            setValueCheckbox(permission.course.delete, 'course3');
            setValueCheckbox(permission.course.sort, 'course4');
            //
            setValueCheckbox(permission.group.add, 'group1');
            setValueCheckbox(permission.group.edit, 'group2');
            setValueCheckbox(permission.group.delete, 'group3');
            setValueCheckbox(permission.group.sort, 'group4');
            //
            setValueCheckbox(permission.lesson.add, 'lesson1');
            setValueCheckbox(permission.lesson.edit, 'lesson2');
            setValueCheckbox(permission.lesson.delete, 'lesson3');
            //
            setValueCheckbox(permission.combo.add, 'combo1');
            setValueCheckbox(permission.combo.edit, 'combo2');
            setValueCheckbox(permission.combo.delete, 'combo3');
            //
            setValueCheckbox(permission.invoice.active, 'invoice1');
            setValueCheckbox(permission.invoice.cancel, 'invoice2');
            setValueCheckbox(permission.invoice.export, 'invoice3');
            //
            setValueCheckbox(permission.voucher.add, 'voucher1');
            setValueCheckbox(permission.voucher.edit, 'voucher2');
            setValueCheckbox(permission.voucher.delete, 'voucher3');
            setValueCheckbox(permission.voucher.export, 'voucher4');
            //
            setValueCheckbox(permission.active.edit, 'active1');
            setValueCheckbox(permission.active.delete, 'active2');
            //
            setValueCheckbox(permission.blog.add, 'blog1');
            setValueCheckbox(permission.blog.edit, 'blog2');
            setValueCheckbox(permission.blog.delete, 'blog3');
            //
            setValueCheckbox(permission.category.add, 'category1');
            setValueCheckbox(permission.category.edit, 'category2');
            setValueCheckbox(permission.category.delete, 'category3');
            //
            setValueCheckbox(permission.page.add, 'page1');
            setValueCheckbox(permission.page.edit, 'page2');
            setValueCheckbox(permission.page.delete, 'page3');
            //
            setValueCheckbox(permission.user.edit, 'user1');
            setValueCheckbox(permission.user.login, 'user2');
            //
            setValueCheckbox(permission.teacher.add, 'teacher1');
            setValueCheckbox(permission.teacher.edit, 'teacher2');
            setValueCheckbox(permission.teacher.delete, 'teacher3');
            //
            setValueCheckbox(permission.comment.reply, 'comment1');
            setValueCheckbox(permission.comment.delete, 'comment2');
            //
            setValueCheckbox(permission.payment.add, 'payment1');
            setValueCheckbox(permission.payment.edit, 'payment2');
            setValueCheckbox(permission.payment.delete, 'payment3');
            //
            setValueCheckbox(permission.server.add, 'server1');
            setValueCheckbox(permission.server.edit, 'server2');
            setValueCheckbox(permission.server.delete, 'server3');
            //
            setValueCheckbox(permission.feedback.add, 'feedback1');
            setValueCheckbox(permission.feedback.edit, 'feedback2');
            setValueCheckbox(permission.feedback.delete, 'feedback3');
            //
            setValueCheckbox(permission.document.add, 'document1');
            setValueCheckbox(permission.document.edit, 'document2');
            setValueCheckbox(permission.document.delete, 'document3');
            //
            setValueCheckbox(permission.popup.add, 'popup1');
            setValueCheckbox(permission.popup.edit, 'popup2');
            setValueCheckbox(permission.popup.delete, 'popup3');
            //
            setValueCheckbox(permission.slider.add, 'slider1');
            setValueCheckbox(permission.slider.edit, 'slider2');
            setValueCheckbox(permission.slider.delete, 'slider3');
            
            $('#pageModal').modal('show');
        });

        function setValueCheckbox(value, name){
            if(value == 0){
                $('input:checkbox[name='+ name +']').prop('checked', true);
            }else{
                $('input:checkbox[name='+ name +']').prop('checked', false);
            }
        }

        $('.modal-footer').on('click', '.permiss-admin', function() {
            var content = new FormData($("#form_permission")[0]);
            content.append('id',  $('#admin_id').val());
            $.ajax({
                type: 'post',
                url: '/backend/admin/permission',
                processData: false,
                contentType: false,
                data : content,
                success: function(data) {
                    if (data.errors){
                        //báo lỗi
                    }
                    else {
                        $('.admin_area').html(data);
                    }
                }
            });
        });
        //chek all checkbox

        $("#checkAll").click(function(){
            $('input:checkbox').not(this).prop('checked', this.checked);
        });
        $("#checkCourse").click(function(){
            for($x = 1; $x <= 4; $x++){
                $('input:checkbox[name=course'+$x+']').not(this).prop('checked', this.checked);
            }
        });
         $("#checkGroup").click(function(){
            for($x = 1; $x <= 4; $x++){
                $('input:checkbox[name=group'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkLesson").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=lesson'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkCombo").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=combo'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkInvoice").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=invoice'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkVoucher").click(function(){
            for($x = 1; $x <= 4; $x++){
                $('input:checkbox[name=voucher'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkActive").click(function(){
            for($x = 1; $x <= 2; $x++){
                $('input:checkbox[name=active'+$x+']').not(this).prop('checked', this.checked);
            }
        });

        $("#checkBlog").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=blog'+$x+']').not(this).prop('checked', this.checked);
            }
        });

        $("#checkCategory").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=category'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        
        $("#checkPage").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=page'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkUser").click(function(){
            for($x = 1; $x <= 2; $x++){
                $('input:checkbox[name=user'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkTeacher").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=teacher'+$x+']').not(this).prop('checked', this.checked);
            }
        });
         $("#checkComment").click(function(){
            for($x = 1; $x <= 2; $x++){
                $('input:checkbox[name=comment'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkPayment").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=payment'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkServer").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=server'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkFeedback").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=feedback'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkDocument").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=document'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkPopup").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=popup'+$x+']').not(this).prop('checked', this.checked);
            }
        });
        $("#checkSlider").click(function(){
            for($x = 1; $x <= 3; $x++){
                $('input:checkbox[name=slider'+$x+']').not(this).prop('checked', this.checked);
            }
        });
    </script>

@stop