@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON><PERSON> lý kh<PERSON><PERSON> học đ<PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON> @stop
@section('keywords') course active @stop
@section('author') dungmori.com @stop
@section('title') Admin | <PERSON><PERSON><PERSON><PERSON> học kích ho<PERSON> @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clockpicker/0.0.7/bootstrap-clockpicker.js"></script> 
    <link href="https://cdnjs.cloudflare.com/ajax/libs/clockpicker/0.0.7/bootstrap-clockpicker.css" rel="stylesheet" type="text/css" />
    <style type="text/css">
       .form-horizontal .form-group {
            margin-left: 22.5px;
            margin-right: -151.5px;
            margin-bottom: 25px;
        }
    </style>
@stop

@section('content') 
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            <PERSON><PERSON><PERSON><PERSON> lý kh<PERSON><PERSON> học đ<PERSON> k<PERSON>ch ho<PERSON>
        </h4> 
        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <input type="text" class="form-control mb-2 mr-sm-2" name="id" placeholder="ID học viên">
            <input type="text" class="form-control mb-2 mr-sm-2" name="email" placeholder="Nhập email user">
            <a type="button" class="search btn btn-info mb-2">Tìm kiếm</a>
            <a href={{url('backend/course-active')}} class="btn btn-info mb-2">Làm Lại</a>
        </form>
    </div>
    <div class="table_voucher">
        @include('backend.courseActive.detailCourseActive')
    </div>
    <div id="pageModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form">
                        <input name="_token" type="hidden" value="{{ csrf_token() }}">
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-5">
                                <input type="text" class="form-control" id="fid">
                            </div>
                        </div>
                       
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="uname">Thành viên</label>
                            <div class="col-sm-5">
                                <input type="text" class="form-control" id="uname">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="course">Tên khóa học</label>
                            <div class="col-sm-5">
                                <input type="text" class="form-control" id="course">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="expired">Ngày hết hạn </label>
                            <div class="col-sm-5">
                                <input type="text" class="form-control clockpicker" id="expired_time" style="width: 100px; display: inline-block;">
                                <input type="date" class="form-control" id="expired_date" style="width: 191px; display: inline-block;">
                            </div>
                        </div>

                        <div class="form-group" id="kaiwa-form">
                            <label class="control-label col-sm-2" for="kaiwa">Số buổi kaiwa </label>
                            <div class="col-sm-5">
                                <input type="text" class="form-control clockpicker" id="kaiwa-total" style="width: 100px; display: inline-block;">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="specialized">Chuyên ngành</label>
                            <div class="col-sm-5">
                                <select class="form-control" id="specialized"></select>
                            </div>
                        </div>
                    </form>
                    <!--delete form-->
                    <div class="deleteContent"> Xác nhận xóa khóa học "<span class="cname"></span>" đang hoạt động này không ?
                            <span class="hidden id_course "> </span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn" data-dismiss="modal">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
    $(document).ready(function() {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });   
    });
    
    //datetime picker
    $('#expired_time').clockpicker();

    //pagination
    $(document).on('click', '.table_voucher .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page);
     });
     function readPage(page){
            $.ajax({
                type: 'get',
                url: '/backend/course-active/page',
                data: {
                    page: page,
                    id: $('input[name=id]').val(),
                    email: $('input[name=email]').val(),
                }
           }).done(function(data){
                $('.table_voucher').html(data)
           })
     }
    
    //edit
    $(document).on('click', '.edit-modal', function(e) {
        $('#fid').attr('disabled', true);
        $('#uname').attr('disabled', true);
        $('#course').attr('disabled', true);
        $('#footer_action_button').text(" Sửa");
        $('#footer_action_button').addClass('glyphicon-check');
        $('#footer_action_button').removeClass('glyphicon-trash');
        $('.actionBtn').addClass('btn-success');
        $('.actionBtn').removeClass('btn-danger');
        $('.actionBtn').removeClass('delete');
        $('.actionBtn').addClass('edit');
        $('.modal-title').text('Sửa');
        $('.deleteContent').hide();
        $('.form-horizontal').show();

        var course = $(this).data('info');

        //chỉ hiện ô sửa và fill data nếu là kaiwa
        if(course.course_id != 21)
            $('#kaiwa-form').css('display', 'none');
        else
            $('#kaiwa-form').css('display', 'block');

        //Lay nhom chuyen nganh theo khoa hoc
        $.ajax({
            type: 'get',
            url: '/backend/specialized/' + course.course_id ,
            success: function(data) {
                $('#specialized').empty();
                var option = '<option value="0">Reset lại cho học viên</option>';
                $.each(data, function(key, value) {
                    if(course.specialized_id == value.id){
                        option += '<option value="'+ value.id + '" selected>' + value.name + '</option>';
                    }
                    else{
                        option += '<option value="'+ value.id + '">' + value.name + '</option>';
                    }
                });
                $('#specialized').append(option);
            }
        });
        fillmodalData(course);
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '40%');
        $('#pageModal').css('left', '31%');
    });
    //delete
    $(document).on('click', '.delete-modal', function() {
        $('#footer_action_button').text(" Xóa");
        $('.modal-title').text('Xóa');
        $('#footer_action_button').removeClass('glyphicon-check');
        $('#footer_action_button').addClass('glyphicon-trash');
        $('.actionBtn').removeClass('btn-success');
        $('.actionBtn').addClass('btn-danger');
        $('.actionBtn').removeClass('edit');
        $('.actionBtn').addClass('delete');
        $('.deleteContent').show();
        $('.form-horizontal').hide();
        var course =  $(this).data('info');
        $('.id_course').text(course.id);
        $('.cname').html(course.title);
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '28%');
        $('#pageModal').css('left', '37%');
    });
    //set value for fields
    function fillmodalData(course){

        $date = course.watch_expired_day;
        $('#fid').val(course.id);
        $('#uname').val(course.user);
        $('#course').val(course.title);
        $('#expired_time').val($date.substring(11,19));
        $('#expired_date').val($date.substring(0,10));

        //chỉ hiện ô sửa và fill data nếu là kaiwa
        if(course.course_id == 21)
            $('#kaiwa-total').val(course.kaiwa_total_booking);
    }
    //edit
    $('.modal-footer').on('click', '.edit', function() {
        $.ajax({
            type: 'post',
            url: '/backend/course-active/edit',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $("#fid").val(),
                'expired_date': $('#expired_date').val(),
                'expired_time': $('#expired_time').val(),
                'specialized' : $('#specialized').val(),
                'kaiwa_total' : $('#kaiwa-total').val(),
            },
            success: function(data) {
                if (data.errors){
                    //báo lỗi
                }else {
                    $('.table_voucher').html(data);
                }
            }
        });
    });
    //delete
    $('.modal-footer').on('click', '.delete', function() {
        $.ajax({
            type: 'post',
            url: '/backend/course-active/delete',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $('.id_course').text(),
            },
            success: function(data) {
                $('.item' + $('.id_course').text()).remove();
            }
        });
    });
    //tim kiem
    $(document).on('click', '.search', function() {
        callBack();
    });

    $(document).keypress(function(e) {
        if (e.which == 13) {
            callBack();
            e.preventDefault();
        }
    });

    function callBack(){
        if($('input[name=id]').val() != '' || $('input[name=email]').val() != ''){
            $.ajax({
                type: 'get',
                url: '/backend/course-active/find',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('input[name=id]').val(),
                    'email': $('input[name=email]').val(),
                },
                success: function(data) {
                    $('.table_voucher').html(data);
                }
            });
        }
    }

</script>

@stop