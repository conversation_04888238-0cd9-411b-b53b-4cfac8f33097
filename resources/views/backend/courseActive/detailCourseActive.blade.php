<table class="table table-borderless" id="table">
	<thead>
		<tr>
			<th class="text-center"><PERSON><PERSON></th>
			<th class="text-center">Khóa</th>
			<th class="text-center">Th<PERSON><PERSON> viên</th>
			<th class="text-center">Thời hạn xem</th>
            <th class="text-center">Gói Plus</th>
            <th class="text-center" style="text-align: left;">Người kích ho<PERSON></th>
            <th class="text-center" style="text-align: left;">Cập nhật</th>
			<th class="text-center">Hành động</th>
		</tr>
	</thead>
    <tbody>
		@foreach($items as $item)
    		<tr class="item{{$item->id}}">
    			<td class="text-center">{{$item->id}}</td>
    			<td class="text-center">{{$item->title}}</td>
                <td class="text-center">
                    {{$item->user}}<br>
                    #{{$item->user_id}} | 
                    <i class="glyphicon glyphicon-earphone" style="font-size: 12px;"></i> {{$item->phone}}
                </td>
    			<td class="text-center">

                    {{-- Tổng số buổi kaiwa --}}
                    @if($item->course_id == 21)
                        <label class="label new-label red-label"> Kaiwa {{$item->kaiwa_total_booking ? $item->kaiwa_total_booking: 6}} buổi</label>
                    @endif

                    @if($item->days < 0)
                        <label class="label new-label red-label ">Đã hết hạn</label><br>
                    @else
                        <label class="label new-label green-label">Còn hạn: <b>{{$item->days}}</b> ngày</label><br>
                    @endif
                    {{friendlyTime($item->watch_expired_day)}}

                </td>
                <td class="text-center">
                    @if($item->extra_days)
                        + {{ $item->extra_days }} ngày
                    @endif
                   Ngày hết hạn: {{$item->extra_expired_day}}<br>
                </td>
                <td class="text-center" style="text-align: left;">
                    Người kích: @if($item->admin_active) {{$item->admin_active}} @endif<br/>
                    Người sửa:  @if($item->admin_update) {{$item->admin_update}} @endif
                </td>
                <td class="text-center" style="text-align: left;">
                    Ngày kích: {{friendlyTime($item->created_at)}}<br/>
                    Ngày sửa: {{friendlyTime($item->last_update)}}
                </td>
    			<td class="text-center">
                    @if(json_decode(Auth::guard('admin')->user()->matrix)->active->edit != null)
                        <button class="edit-modal btn btn-info new-label"
                            data-info="{{json_encode($item)}}">
                            <span class="glyphicon glyphicon-edit"></span> Sửa
                        </button>
                    @endif   
                    
                    @if(json_decode(Auth::guard('admin')->user()->matrix)->active->delete != null)
        				<button class="delete-modal btn btn-danger new-label"
        					data-info="{{json_encode($item)}}">
        					<span class="glyphicon glyphicon-trash"></span> Xóa
        				</button>
                    @endif
    			</td>
    		</tr>
		@endforeach
    </tbody>
</table>
{{ $items->render() }}