@extends('backend._default.dashboard')

@section('description') Quản lý kết quả JLPT @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý kết quả JLPT @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
@stop

@section('content')
    <div class="filterable-list__screen" id="user__screen">
        <div class="filterable-list__filter">
            <div class="form-group">
                <label>ID</label>
                <input type="text" class="form-control" placeholder="ID User" v-model="filter.id" @keyup.enter="applyFilter">
            </div>
            <div class="form-group">
                <label>Vai trò</label>
                <select v-model="filter.is_tester" class="form-control" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option value="0">Người dùng</option>
                    <option value="1">Tester</option>
                </select>
            </div>
            <div class="form-group">
                <label>Khoá đang học</label>
                <select v-model="filter.course" class="form-control" @change="onChangeCourse">
                    <option value="">Tất cả</option>
                    <option :value="course.id" :key="course.id" v-for="course in courses">@{{ course.name }}</option>
                </select>
            </div>
            <div class="form-group" v-if="filter.course">
                <label>Hoàn thành</label>
                <select v-model="filter.completed" class="form-control" @change="applyFilter">
                    <option value=""></option>
                    <option value="100">Hoàn thành</option>
                    <option value="gte70">Trên 70%</option>
                    <option value="st70">Dưới 70%</option>
                </select>
            </div>
            <div class="form-group" v-if="filter.course == 21">
                <label>Còn lượt skype</label>
                <select v-model="filter.skype" class="form-control" @change="applyFilter">
                    <option value=""></option>
                    <option value="1">Còn</option>
                    <option value="0">Hết</option>
                </select>
            </div>
            <div class="form-group" v-if="filter.course">
                <label>Hết hạn vào</label>
                <select v-model="filter.watch_expired_day" class="form-control" @change="applyFilter">
                    <option value="">Chưa hết hạn</option>
                    <option :value="'2022-01-01'">01/2022</option>
                    <option :value="'2022-02-01'">02/2022</option>
                    <option :value="'2022-03-01'">03/2022</option>
                    <option :value="'2022-04-01'">04/2022</option>
                    <option :value="'2022-05-01'">05/2022</option>
                    <option :value="'2022-06-01'">06/2022</option>
                    <option :value="'2022-07-01'">07/2022</option>
                    <option :value="'2022-08-01'">08/2022</option>
                    <option :value="'2022-09-01'">09/2022</option>
                    <option :value="'2022-10-01'">10/2022</option>
                    <option :value="'2022-11-01'">11/2022</option>
                    <option :value="'2022-12-01'">12/2022</option>
                </select>
            </div>
            <div class="form-group">
                <label>Tham gia từ ngày</label>
                <input type="text" class="form-control" name="time_from" id="time_from" placeholder="Chọn thời gian" v-model="filter.time_from" @change="onChangeDate($event)">
            </div>
            <div class="form-group">
                <label>Đến ngày</label>
                <input type="text" class="form-control" name="time_to" id="time_to" placeholder="Chọn thời gian" v-model="filter.time_to" @change="onChangeDate($event)">
            </div>
        </div>
        <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
            <span>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</span>
            <div>
                <!-- Nơi này để chứa các button thao tác -->
                <span style="margin-right: 10px; cursor: pointer" @click="resetFilter"><i class="fa fa-refresh"></i></span>
                <span>@{{ syncSuccess  }} / @{{ totalSync }}</span>
                <button class="btn btn-info" @click="getUserToSync">Đồng bộ tiến trình</button>
                <button class="btn btn-info" @click="applyFilter">Lọc</button>
                {{--                <button class="btn btn-success" @click="exportExcel" style="margin-left: 10px;">Xuất Excel</button>--}}
            </div>
        </div>
        <div class="filterable-list__list">
            <table>
                <thead>
                <th width="4%">ID</th>
                {{--                <th class="text-center" @click="sortTotalScore" style="cursor: pointer">Đỗ/Trượt <i class="fa fa-sort-up" v-if="filter.sort === 'asc'"></i> <i class="fa fa-sort-down" v-if="filter.sort === 'desc'"></i></th>--}}
                <th>Contact (Dungmori)</th>
                <th>Contact (MJT)</th>
                <th>Tài khoản</th>
                <th>Username/Phone</th>
                {{--                <th>Địa chỉ</th>--}}
                <th class="text-center">Khoá học</th>
                <th v-if="filter.course == 21">Skype</th>
                <th>Ngày đăng ký</th>
                <th>Trạng thái</th>
                <th>Hành động</th>
                </thead>
                <tbody v-if="!loading">
                <tr v-if="results.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(user, index) in results" :key="user.id" v-if="results.length != 0">
                    <td>
                        <div>@{{ user.id }}</div>
                    </td>
                    <td>
                        <span v-if="user.provider == 'google'"><i style="color: #dc4938;" class="fa fa-google-plus-square" ></i> @{{ user.social_id }}</span>
                        <span v-if="user.provider == 'facebook'"><i style="color: #377acc;" class="fa fa-facebook-square" ></i> @{{ user.social_id }}</span>
                        <span v-if="user.provider == 'apple'"><i style="color: #AAA;" class="fa fa-apple" ></i></span>
                        <div v-if="user.social_id == null">
                            <div v-if="user.phone_number"><i class="fa fa-phone" ></i> @{{user.phone_number}}</div>
                            <span v-else><i class="fa fa-envelope"></i> @{{ user.email }}</span>
                        </div>
                    </td>
                    <td>
                        <div v-if="user.certificate_info">
                            <div class="text-uppercase">@{{ user.certificate_info.fullname }}</div>
                            <div>@{{ user.certificate_info.mobile }}</div>
                            <div>@{{ user.certificate_info.email }}</div>
                        </div>
                        <div v-else>------</div>

                    </td>
                    <td>
                        <img v-if="user.avatar == null || user.avatar == ''" :src="'{{url('/assets/img/default-avatar.jpg')}}'" style="height: 35px; width: 35px; margin-right: 15px;"/>
                        <img v-else :src="'{{url('/cdn/avatar/small/')}}/' + user.avatar" style="height: 35px; width: 35px; margin-right: 15px;"/>
                        <strong>@{{user.name || '--'}}</strong>
                        <a v-if="user.conversation" :href="'{{url('/backend/chat#')}}' + user.conversation.id" target="_blank">
                            <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;" title="nhắn tin"></i>
                        </a>
                        <i v-else :class="'fa fa-comments fa-comments-' + user.id" style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                           @click="initConversation(user.id)"
                        ></i>

                    </td>
                    <td>
                        <div>@{{ user.phone || '--' }}</div>
                        <span v-if="user.username" style="font-weight: 400; opacity: 0.7;">@@{{ user.username }}</span>
                    </td>
                    {{--                    <td>--}}
                    {{--                        @{{ user.address || '--' }}--}}
                    {{--                    </td>--}}
                    <td class="text-center" width="10%">
                        <span :title="course.pivot.watch_expired_day"
                              class="badge mr-2 a-cursor-pointer hover-able hover-darken" :class="{'badge-danger': course.is_expired, 'badge-success': !course.is_expired, 'badge-info': filter.course == course.id}"
                              v-for="course in user.courses">@{{ course.name }}</span>
                        <div v-if="filter.course" style="font-size: 11px">
                            <span v-for="course in user.courses">
                                <span v-if="filter.course == course.id">Hết hạn: @{{ course.pivot.watch_expired_day }}</span>
                            </span>
                        </div>
                    </td>
                    <td v-if="filter.course == 21">
                        @{{ user.kaiwa_total_booking - user.booking_count }}/@{{ user.kaiwa_total_booking }}
                    </td>
                    <td>
                        @{{ user.created_at }}
                    </td>
                    <td>
                        <span v-if="user.activation" style="color: #69aa00;"><i class="fa fa-check-square" ></i> Xác thực</span>
                        <span v-else style="color: #e74c3c;"><i class="fa fa-warning" ></i> Chưa</span>
                        <span v-if="user.blocked != '0'"><i class="fa fa-lock" title="Tài khoản bị khóa"></i> Bị khoá</span>
                        @if(json_decode(Auth::guard('admin')->user()->matrix)->user->login != null)
                            <div>
                                <span
                                        :data-id="user.id"
                                        class="set_tester label label-danger"
                                        style="padding: 4px 6px; border-radius: 3px; margin: 0 4px 3px 0; cursor: pointer"
                                        :style="{'background': !user.is_tester ? '#0F4C82' : '#E74C3C'}"
                                        @click="setTester(user)"
                                >@{{ !user.is_tester ? 'Set tester' : 'Unset tester' }}</span>
                            </div>
                        @endif
                    </td>
                    <td>
                        @if(json_decode(Auth::guard('admin')->user()->matrix)->user->edit != null)
                            <span class="edit-modal label label-default" @click="openEditModal(user)" style="padding: 4px 6px 4px 8px; margin: 0 4px 3px 0; float: left; cursor: pointer; border-radius: 3px;">
                                <i class="fa fa-pencil" ></i> Sửa
                            </span>
                        @endif
                        @if(json_decode(Auth::guard('admin')->user()->matrix)->user->login != null)
                            <a class="login_user label label-danger" style="padding: 4px 6px; margin: 0 4px 3px 0; border-radius: 3px; background: #e74c3c; float: left;"
                               :href="'{{url('/backend/user/login/')}}' + '/' + user.id" target="_blank">Đăng nhập
                            </a>
                        @endif
                    </td>
                </tr>
                </tbody>
                <tbody v-if="loading">
                <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                    <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                </tr>
                </tbody>
            </table>
        </div>
        <backend-modal v-if="editModal" @close="closeEditModal">
            <h3 slot="header">Sửa thông tin người dùng</h3>
            <div slot="body">
                <div class="form-group" v-if="formData.id">
                    <label>ID</label>
                    <input class="form-control" :value="formData.id" disabled/>
                </div>
                <div class="form-group" v-if="formData.id">
                    <label>Email</label>
                    <input class="form-control" :value="formData.email" disabled/>
                </div>
                <div class="form-group" v-if="formData.id">
                    <label>Username</label>
                    <input class="form-control" :value="formData.username" disabled/>
                </div>
                <div class="form-group" v-if="formData.id">
                    <label>Trạng thái</label>
                    <select v-model="formData.activation" class="form-control">
                        <option value="1">Kích hoạt tài khoản</option>
                        <option value="0">Huỷ kích hoạt tài khoản</option>
                    </select>
                </div>
                <div class="form-group" v-if="formData.id">
                    <label>Khoá tài khoản</label>
                    <select v-model="formData.blocked" class="form-control">
                        <option value="0">Mở tài khoản</option>
                        <option value="1">Khoá tài khoản</option>
                    </select>
                </div>
                <div class="form-group" v-if="formData.id">
                    <label>Password (<span class="required">*</span>)</label>
                    <input class="form-control" v-model="formData.password" placeholder="Chỉ áp dụng cho trường hợp đổi mật khẩu học viên"/>
                </div>
                <span class="btn btn-block btn-success p-5 mt-5" @click="updateUser">Sửa</span>
            </div>
        </backend-modal>
        <div class="filterable-list__paginate">
            <div>
                Hiển thị
                <select v-model="filter.per_page" @change="applyFilter">
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                </select>
                trong số @{{ loading ? '----' : total_result}} kết quả
            </div>
            <paginate
                    {{--                    v-model="filter.page"--}}
                    :page-count="filter.total_page"
                    :page-range="4"
                    :margin-pages="3"
                    :click-handler="changePage"
                    :prev-text="'&laquo;'"
                    :next-text="'&raquo;'"
                    :container-class="'pagination'"
                    :page-class="'page-item'"
                    :force-page="filter.page - 1"
            >
            </paginate>
        </div>
    </div>

    <script type="text/javascript">
        var courses = {!!  json_encode($courses) !!};
    </script>
    <script type="text/javascript">
      jQuery.browser = {};
      (function () {
        jQuery.browser.msie = false;
        jQuery.browser.version = 0;
        if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
          jQuery.browser.msie = true;
          jQuery.browser.version = RegExp.$1;
        }
      })();
    </script>
    {{--    Local plugins--}}
    <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
    <script>
      $(function () {
        // hô biến input thành datepicker xịn xò
        $('#time_from').datetimepicker({
          language: 'vi'
        }).on('dp.change', function (event) {
          filterable-list.onChangeDatetime(event);
        });
        $('#time_to').datetimepicker({
          language: 'vi'
        }).on('dp.change', function (event) {
          filterable-list.onChangeDatetime(event);
        });
      });
    </script>
    <script src="{{asset('plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
    <script src="{{asset('plugin/vue-router/vue-router.js')}}"></script>
    <script src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}"></script>
    <script src="{{asset('assets/backend/js/user/user.list.js')}}?{{filemtime('assets/backend/js/user/user.list.js')}}"></script>
@stop
