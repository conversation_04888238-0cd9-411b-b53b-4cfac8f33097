@extends('backend._default.dashboard')

@section('description') Quản lý kết quả JLPT @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý kết quả JLPT @stop

@section('assets')
    <link type="text/css" rel="stylesheet"
          href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}">
    <link rel="stylesheet" href="https://uicdn.toast.com/tui.pagination/latest/tui-pagination.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://uicdn.toast.com/tui.pagination/latest/tui-pagination.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue-toast-notification@0.6.2/dist/theme-sugar.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
    <script src="{{ asset('plugin/vue-toast-notification/index.min.js') }}"></script>

@stop

@section('content')
    <style type="text/css">
        .filterable-list__filter .form-group {
            float: left;
            margin-right: 15px;
        }

        .filterable-list__list td {
            padding: 4px 10px;
        }

        #user__screen {
            width: 100%;
            float: left;
            background: #fff;
            padding: 5px 10px;
        }

        .mess-content {
            width: 100%;
            min-height: 80px;
            border: solid 1px #ddd;
        }
        .pagination {
            float: left !important;
        }
        .avt{height: 35px; width: 35px; margin-right: 15px;}
        #loading { color: red;display: none}
    </style>
    <div class="filterable-list__screen" id="user__screen">

        <div class="row mt-5">
            <div class="col-md-8">
                <div class="flex">
                    <div style="flex: 1">
                        <label for="one" class="mr-3"> <input type="radio" id="one" value="1" v-model="filterBy"> Tất cả</label>
                        <br>
                        <label for="two" class="mr-3"> <input type="radio" id="two" value="2" v-model="filterBy">Chưa mua khóa học nào</label>
                        <br>
                        <label for="three"><input type="radio" id="three" value="3" v-model="filterBy"> Đã mua khóa học</label>
                        <label for="five"><input type="radio" id="five" value="5" v-model="filterBy"> Online Basic</label>
                    </div>
                    <div>
                        <div>
                            <input v-model="sendToGruopChat" type="checkbox" id="group_chat_check">
                            <label for="group_chat_check">Gửi theo group chat</label>
                        </div>
                        <div class="flex align-items-center mb-3">
                            Gửi bằng quyền
                            <v-select
                                :options="admins"
                                label="name"
                                :reduce="course => course.id"
                                v-model="selectedAdmin"
                                placeholder="Chọn một admin"
                                class="ml-2"
                                style="width: 150px">
                            </v-select>
                        </div>
                        <div>
                            <input v-model="sendAllUser" type="checkbox" id="send_all_user">
                            <label for="send_all_user">Gửi cho tất cả</label>
                        </div>
                        <div>
                            <input v-model="message_pin" type="checkbox" id="message_pin">
                            <label for="message_pin">Ghim tin nhắn</label>
                        </div>
                    </div>
                </div>
                <div style="display: grid; grid-gap: 10px; grid-template-columns: repeat(3, 1fr)">
                    <div class="col">
                        <v-select :options="courses"
                                  label="name"
                                  :reduce="course => course.id"
                                  multiple
                                  v-model="selectedCourse"
                                  placeholder="Chọn một hoặc nhiều khóa học"
                                  class="mb-3">
                        </v-select>
                    </div>
                    <div class="col" v-if="selectedCourse.length === 1">
                        <input class="form-control" v-model="completed_from" type="number" placeholder="Hoàn thành từ (%)" />
                    </div>
                    <div class="col" v-if="selectedCourse.length === 1">
                        <input class="form-control" v-model="completed_to" type="number" placeholder="Đến (%)" />
                    </div>
                    <div class="col" v-if="selectedCourse.length === 1">
                        <vuejs-datepicker placeholder="Kích hoạt từ ngày" v-model="boughtFrom" format="dd/MM/yyyy"></vuejs-datepicker>
                    </div>
                    <div class="col" v-if="selectedCourse.length === 1">
                        <vuejs-datepicker placeholder="Đến ngày" v-model="boughtTo" format="dd/MM/yyyy"></vuejs-datepicker>
                    </div>
                    <div class="col">
                        <v-select :options="option_date_diff"
                                  v-model="dateDiff"
                                  placeholder="Chọn ngày còn lại">
                        </v-select>
                    </div>
                    <div class="col" v-if="dateDiff && dateDiff.value === 0">
                        <vuejs-datepicker placeholder="Hết hạn từ ngày" v-model="expiredFrom" format="dd/MM/yyyy"></vuejs-datepicker>
                    </div>
                    <div class="col" v-if="dateDiff && dateDiff.value === 0">
                        <vuejs-datepicker placeholder="Đến ngày" v-model="expiredTo" format="dd/MM/yyyy"></vuejs-datepicker>
                    </div>
                    <div class="col">
                        <v-select :options="groups"
                                  label="name"
                                  :reduce="course => course.id"
                                  multiple
                                  v-model="selectedGroup"
                                  placeholder="Chọn nhóm cộng đồng">
                        </v-select>
                    </div>
                    <div class="col"> </div>
                    <div class="col"> </div>
                    <div v-if="selectedGroup.length == 1" class="col">
                        <div
                            v-if="selectedGroupChat && groupChats && selectedGroupChat.length < groupChats.length"
                            style="cursor: pointer;"
                            @click="selectedGroupChat = groupChats.map((c) => c.id)"
                        >Chọn tất cả</div>
                        <div
                            v-else-if="selectedGroupChat && groupChats"
                            style="cursor: pointer;"
                            @click="selectedGroupChat = []"
                        >Bỏ chọn tất cả</div>
                        <v-select
                            v-if="selectedGroup.length > 0"
                            :options="groupChats"
                            multiple
                            label="title"
                            :reduce="course => course.id"
                            v-model="selectedGroupChat"
                            placeholder="Chọn nhóm chat">
                        </v-select>
                    </div>
                    <div class="col">
                        <button type="button" class="btn btn-success" @click="findUser()">Lọc</button>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <input type='text' id="datetimepicker1" class="form-control scheduleTime" name="scheduleTime" v-model="scheduleAt" />
                <textarea class="mess-content mt-5" v-model="messContent"></textarea>

                <form id="message-image" style="margin-bottom: 10px" title="Dung lượng file tối đa 8MB">
                    <input id="user-mess-image" type="file" name="images[]"
                           accept=".png, .jpg, .jpeg, .gif, .pdf, .doc, .docx, .xlsx, .xlsm, .xlsb, .xltx"
                           multiple
                           @change="onFileChange"
                    >
                </form>
                <p>Nhắn tin cho <b>@{{checkedUserIds.length}}</b> người</p>
                <button class="btn btn-success" style="float: right; margin-top: -30px;"
                        v-on:click="sendMessForListUser">Send
                </button>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <paginate
                        :page-count="pagination.last_page"
                        :prev-text="'«'"
                        :next-text="'»'"
                        :click-handler="changePage"
                        :container-class="'pagination'"
                >
                </paginate>

                <div class="col-md-3 user-mess-search" style="display: flex; grap: 5px;">
                    <input type="text" v-model="keywords" class="form-control" @keyup.13="findUser()"
                           placeholder="Từ khoá">
                    <button type="button" class="btn-success" @click="findUser()">Tìm kiếm</button>
                    <button type="button" class="btn-primary" @click="refreshSearch()">refresh</button>
                    <button type="button" class="btn-primary" @click="downloadExcel()">Export Excel</button>
                </div>
            </div>
        </div>
        <div>
            Tổng số @{{ pagination.total }}
        </div>
        <div class="filterable-list__list">
            <div class="text-center">
                <p id="loading"><strong>Loading...</strong></p>
            </div>
            <table id="user">
                <thead>
                <th width="30px"><input type="checkbox" v-model="checkAll"></th>
                <th width="4%">ID</th>
                <th style="width: 350px">Tài khoản</th>
                <th>Tin nhắn cuối</th>
                <th style="width: 100px;">Khoá học</th>
                <th style="width: 150px">Ngày đăng ký</th>
                <th style="width: 150px">Ngày hết hạn</th>
                <th style="width: 150px">Ngày còn lại</th>
                <th style="width: 100px">Hành động</th>
                </thead>
                <tbody>
                <tr v-if="results.length === 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(user, index) in results" :key="user.id" v-if="results.length !== 0">
                    <td>
                        <input type="checkbox" class="pick-user" :id="'cb-'+ user.id" :value="user.id"
                               v-model="checkedUserIds">
                    </td>
                    <td>
                        <div>@{{ user.id }}</div>
                    </td>
                    <td>
                        <img :src="'{{url('/assets/img/default-avatar.jpg')}}'" class="avt"/>
{{--                         <img v-if="user.avatar === null || user.avatar === ''" :src="'{{url('/assets/img/default-avatar.jpg')}}'" class="avt"/>--}}
{{--                        <img v-else :src="'{{url('/cdn/avatar/small/')}}/' + user.avatar" class="avt"/>--}}
                        <strong style="margin-top: -3px;">@{{user.name || '--'}}</strong>
                        <a v-if="user.conversation" :href="'{{url('/backend/chat#')}}' + user.conversation.id"
                           target="_blank">
                            <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;"
                               title="nhắn tin"></i>
                        </a>
                        <i v-else :class="'fa fa-comments fa-comments-' + user.id"
                           style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                           @click="initConversation(user.id)"
                        ></i><br/>
                        <div style="margin-left: 55px; margin-top: -10px; font-size: 11px;">
                            <span v-if="user.provider === 'google'"><i style="color: #dc4938;"
                                                                       class="fa fa-google-plus-square"></i> @{{ user.social_id }}</span>
                            <span v-if="user.provider === 'facebook'"><i style="color: #377acc;"
                                                                         class="fa fa-facebook-square"></i> @{{ user.social_id }}</span>
                            <span v-if="user.provider === 'apple'"><i style="color: #AAA;"
                                                                      class="fa fa-apple"></i></span>
                            <div v-if="user.social_id === null">
                                <div v-if="user.phone_number"><i class="fa fa-phone"></i> @{{user.phone_number}}</div>
                                <span v-else><i class="fa fa-envelope"></i> @{{ user.email }}</span>
                            </div>
                        </div>

                    </td>
                    <td>
                        @{{ user.lass_message | filtermess }}
                        {{-- <span v-if="user.lassMessage <> NULL">@{{ user.lassMessage }}</span>
                        <span v-if="user.lassMessage === NULL">Chưa bao giờ nhắn tin</span> --}}
                    </td>
                    <td>
                        <div v-if="user.course_owner">
                            <div v-for="course in user.course_owner">
                                @{{ course.title }}
                            </div>
                        </div>
                        <span v-else v-text="user.course_name ? user.course_name : '-'"></span>
                    </td>
                    <td>
                        <div v-if="user.course_owner">
                            <div v-for="course in user.course_owner">
                                @{{ course.created_at }}
                            </div>
                        </div>
                        <span v-else>@{{ user.created_at }}</span>
                    </td>
                    <td>
                        <div v-if="user.course_owner">
                            <div v-for="course in user.course_owner">
                                @{{ course.watch_expired_day }}
                            </div>
                        </div>
                        <span v-else>@{{ user.watch_expired_day }}</span>
                    </td>
                    <td>
                        <div v-if="user.course_owner">
                            <div v-for="course in user.course_owner">
                                @{{ timeLeft(course.watch_expired_day) }}
                            </div>
                        </div>
                        <span v-else>@{{  user.date_diff ? user.date_diff : 0}}</span>
                    </td>
                    <td>
                        @if(json_decode(Auth::guard('admin')->user()->matrix)->user->login !== null)
                            <a class="login_user label label-danger"
                               style="padding: 4px 6px; margin: 0 4px 3px 0; border-radius: 3px; background: #e74c3c; float: left;"
                               :href="'{{url('/backend/user/login/')}}' + '/' + user.id" target="_blank">Đăng nhập
                            </a>
                        @endif
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <script src="{{asset('/plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
    <script src="{{ asset('/plugin/vuejs-datepicker/vuejs-datepicker.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
    <script src="{{asset('plugin/jquery/axios.min.js')}}"></script>
    <script>
        $(function () {
            $('#datetimepicker1').datetimepicker({
                format: 'YYYY-MM-DD HH:mm'
            }).on('dp.change', function (event) {
                filterUserMess.onChangeDatetime(event);
            });
        });
    </script>
    <script type="text/javascript">
        $.ajaxSetup({headers: {'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')}});
        // Vue.component('paginate', window['vuejs-paginate'])
        Vue.component('v-select', VueSelect.VueSelect);
        Vue.component('paginate', VuejsPaginate)
        Vue.use(VueToast);
        Vue.filter('filtermess', function (value) {
            try {
               var newValue =  JSON.parse(value);
               return newValue;
            }
            catch(err) {
                return '';
            }
        });
        var filterUserMess = new Vue({
            el: '#user__screen',
            components: {
              vuejsDatepicker
            },
            data: function () {
                return {
                    url: window.location.origin,
                    filterBy: '1', // 1: tat ca, 2: chưa mua hàng bao h, 3: đã mua khóa
                    checkAll: 0,
                    checkedUserIds: [],
                    results: [],
                    courses: [],
                    groups: [],
                    completeStatuses: [
                        {id: '70', name: '> 70%'},
                        {id: '50', name: '> 50%'},
                        {id: '30', name: '> 30%'},
                    ],
                    selectedCourse: [],
                    selectedAdmin: null,
                    selectedGroupChat: [],
                    selectedGroup: [],
                    dateDiff: null,
                    completed_from: undefined,
                    completed_to: undefined,
                    boughtFrom: null,
                    boughtTo: null,
                    expiredFrom: null,
                    expiredTo: null,
                    messContent: '',
                    disableCourses: true,
                    pagination: {
                        current_page: 1,
                        last_page: 100,
                        per_page: 1,
                        total:1,
                    },
                    scheduleAt: null,
                    modelConfig: {
                        type: 'string',
                        mask: 'YYYY-MM-DD HH:mm:00', // Uses 'iso' if missing
                    },
                    sendToGruopChat: false,
                    images: [],
                    admins: [],
                    groupChats: [],
                    keywords: '',
                    sendAllUser: false,
                    message_pin: false,
                    option_date_diff: [{value: '>', label: "Còn hạn"}, {label: "Hết hạn", value: 0}, ..._.range(1, 241)]
                }
            },
            computed: {
              boughtTimeFrom() {
                return this.boughtFrom ? moment(this.boughtFrom).format('YYYY-MM-DD') : undefined;
              },
              boughtTimeTo() {
                return this.boughtTo ? moment(this.boughtTo).format('YYYY-MM-DD') : undefined;
              },
              expiredTimeFrom() {
                return this.expiredFrom ? moment(this.expiredFrom).format('YYYY-MM-DD') : undefined;
              },
              expiredTimeTo() {
                return this.expiredTo ? moment(this.expiredTo).format('YYYY-MM-DD') : undefined;
              },
            },
            watch: {
                filterBy: {
                    handler(newValue) {
                        // Nếu chọn mua khóa học
                        if (newValue === '3') {
                            this.disableCourses = false;
                        } else {
                            this.disableCourses = true;
                            this.selectedCourse = [];
                            this.selectedGroup = [];
                            this.dateDiff = null;
                            this.completed_from = undefined;
                            this.completed_to = undefined;
                            this.boughtFrom = null;
                            this.boughtTo = null;
                            this.expiredFrom = null;
                            this.expiredTo = null;
                        }
                    },
                    deep: true
                },
                checkedUserIds: {
                    handler(newValue) {
                        // console.log('checkedUserIds', this.checkedUserIds);
                    },
                    deep: true
                },
                checkAll: {
                    handler(newValue) {
                        this.checkedUserIds = [];
                        // Nếu pick tất cả
                        if (newValue === true) {
                            for (user in this.results) {
                                this.checkedUserIds.push(this.results[user].id);
                            }
                        }
                    },
                    deep: true
                },
                selectedGroup(newValue) {
                    const vm = this;
                    if (newValue.length > 0) {
                        $.post(window.location.origin + "/backend/user-send-mess/get-admin-list",
                            { groupIds: newValue },
                            function (response) {
                                vm.admins = response.users;
                                vm.groupChats = response.conversations;
                            }
                        );
                    }
                }
            },
            mounted: function () {
                const vm = this
                this.initData();
                $.post(window.location.origin + "/backend/user-send-mess/get-admin-list",
                    function (response) {
                        vm.admins = response.users;
                        vm.groupChats = response.conversations;
                    }
                );
            },
            methods: {
                onChangeDatetime: function (event) {
                    var vm = this;
                    vm.scheduleAt = moment(event.date).format('YYYY-MM-DD HH:mm:ss');
                  },
                // Khởi tạo dữ liệu
                initData: async function () {
                    const vm = this;
                    await vm.getCourseList();
                    await vm.getGroupList();
                    await vm.getUserList();
                },
                findUser(page = 1){

                    const vm = this;
                    vm.checkedUserIds = [];
                    vm.pagination.current_page = page;
                    vm.getUserList();
                },
                timeLeft(time) {
                    var eventdate = moment(time);
                    var todaysdate = moment();
                    return eventdate.diff(todaysdate, 'days') >= 0 ? eventdate.diff(todaysdate, 'days') : 0;
                },
                getUserList: function () {
                    $('#loading').show()
                    const vm = this;
                    const formData = {
                        filterBy: vm.filterBy,
                        page: vm.pagination.current_page,
                        date_diff: vm.dateDiff,
                        course: vm.selectedCourse,
                        group: vm.selectedGroup,
                        completed_from: vm.completed_from ? vm.completed_from : undefined,
                        completed_to: vm.completed_to ? vm.completed_to : undefined,
                        boughtTimeFrom: vm.boughtTimeFrom,
                        boughtTimeTo: vm.boughtTimeTo,
                        expiredTimeFrom: vm.expiredTimeFrom,
                        expiredTimeTo: vm.expiredTimeTo,
                        selectedGroupChat: vm.selectedGroupChat,
                        keywords: vm.keywords,
                    };
                    $.get(window.location.origin + "/backend/user-send-mess/get-list", formData, function (response) {
                        $('#loading').hide()
                        vm.results = _.uniqBy(response.data, 'id');
                        vm.pagination.current_page = response.current_page;
                        vm.pagination.per_page = response.per_page;
                        vm.pagination.last_page = response.last_page;
                        vm.pagination.total = response.total
                    });
                },
                async downloadExcel() {
                    let vm = this;
                    let currentUrl = (new URL(window.location.origin + `/backend/user-send-mess/export-excel`));

                    let searchParams = currentUrl.searchParams;
                    searchParams.append('filterBy', vm.filterBy)
                    searchParams.append('date_diff', vm.date_diff)
                    searchParams.append('course', vm.selectedCourse)
                    searchParams.append('group', vm.selectedGroup)
                    searchParams.append('completed_from', vm.completed_from ? vm.completed_from : undefined)
                    searchParams.append('completed_to', vm.completed_to ? vm.completed_to : undefined)
                    searchParams.append('boughtTimeFrom', vm.boughtTimeFrom)
                    searchParams.append('boughtTimeTo', vm.boughtTimeTo)
                    searchParams.append('selectedGroupChat', vm.selectedGroupChat)
                    searchParams.append('keywords', vm.keywords)

                    currentUrl.search = searchParams.toString();

                    window.open(currentUrl, '_blank');
                },
                getCourseList: function () {
                    const vm = this;
                    $.get(window.location.origin + "/backend/course/list", function (response) {
                        vm.courses = response
                    });
                },
                getGroupList: function () {
                    const vm = this;
                    $.get(window.location.origin + "/backend/community/group/list-all", function (response) {
                        vm.groups = response.data;
                    });
                },

                // Gửi tin nhắn cho danh sách user được chọn
                sendMessForListUser: function () {

                    var vm = this;
                    if (vm.sendToGruopChat) {
                        if (vm.messContent === '') {
                            alert("Tin nhắn không được bỏ trống");
                            return;
                        }

                        const formImage = document.getElementById("message-image");

                        let formData = new FormData(formImage);

                        formData.append('filterBy', vm.filterBy);
                        formData.append('page', vm.pagination.current_page);
                        if (typeof(vm.dateDiff) == 'object' && vm.dateDiff != null) {
                            formData.append('date_diff[label]', vm.dateDiff.label);
                            formData.append('date_diff[value]', vm.dateDiff.value);
                        } else {
                            formData.append('date_diff', vm.dateDiff ?? []);
                        }
                        formData.append('course', vm.selectedCourse);
                        formData.append('group', vm.selectedGroup);
                        formData.append('completed_from', vm.completed_from ? vm.completed_from : []);
                        formData.append('completed_to', vm.completed_to ? vm.completed_to : []);
                        formData.append('boughtTimeFrom', vm.boughtTimeFrom ?? []);
                        formData.append('boughtTimeTo', vm.boughtTimeTo ?? []);
                        formData.append('expiredTimeFrom', vm.expiredTimeFrom ?? []);
                        formData.append('expiredTimeTo', vm.expiredTimeTo ?? []);
                        formData.append('selectedGroupChat', vm.selectedGroupChat);
                        formData.append('keywords', vm.keywords);
                        formData.append('selectedGroup', vm.selectedGroup);
                        formData.append('selectedGroupChat', vm.selectedGroupChat);
                        formData.append('message',  vm.messContent);
                        formData.append('scheduleAt',  vm.scheduleAt ?? []);
                        formData.append('selectedAdmin',  vm.selectedAdmin ?? []);
                        formData.append('message_pin', this.message_pin ? 1 : 0);
                        axios.post(window.location.origin + "/backend/user-send-mess/send-to-group-chat", formData).then(function (response) {
                            if (response.status == 200) {
                                vm.$forceUpdate();
                                Vue.$toast.open('Gửi tin nhắn thành công !');
                            }
                        });
                        return;
                    }

                    const formImage = document.getElementById("message-image");

                    let formData = new FormData(formImage);

                    formData.append('filterBy', vm.filterBy);
                    formData.append('page', vm.pagination.current_page);
                    formData.append('course', vm.selectedCourse);
                    formData.append('group', vm.selectedGroup);
                    formData.append('completed_from', vm.completed_from ? vm.completed_from : []);
                    formData.append('completed_to', vm.completed_to ? vm.completed_to : []);
                    formData.append('boughtTimeFrom', vm.boughtTimeFrom ?? []);
                    formData.append('boughtTimeTo', vm.boughtTimeTo ?? []);
                    formData.append('expiredTimeFrom', vm.expiredTimeFrom ?? []);
                    formData.append('expiredTimeTo', vm.expiredTimeTo ?? []);
                    formData.append('selectedGroupChat', vm.selectedGroupChat);
                    formData.append('keywords', vm.keywords);
                    formData.append('listIds',  vm.checkedUserIds);
                    formData.append('message',  vm.messContent);
                    formData.append('scheduleAt',  vm.scheduleAt ?? []);
                    formData.append('selectedAdmin',  vm.selectedAdmin ?? []);
                    formData.append('sendAllUser', this.sendAllUser ? 1 : 0);
                    formData.append('message_pin', this.message_pin ? 1 : 0);
                    if (typeof(vm.dateDiff) == 'object' && vm.dateDiff != null) {
                        formData.append('date_diff[label]', vm.dateDiff.label);
                        formData.append('date_diff[value]', vm.dateDiff.value);
                    } else {
                        formData.append('date_diff', vm.dateDiff ?? []);
                    }

                    if (vm.checkedUserIds.length === 0 && !vm.sendAllUser) alert("chưa chọn danh sách người gửi");
                    else if (vm.messContent === '') alert("Tin nhắn không được bỏ trống");
                    else {
                        var url = `${window.location.origin}/backend/user-send-mess/send`;

                        axios.post(url, formData).then(function (response) {
                            if (response.status == 200) {
                                vm.checkedUserIds.forEach(function (id) {
                                    var user = _.find(vm.results, ['id', id]);
                                    user.lassMessage = vm.messContent;
                                });
                                vm.$forceUpdate();
                                Vue.$toast.open('Gửi tin nhắn thành công !');
                            }
                        });
                    }
                },
                changePage(page) {
                    this.pagination.current_page = page;
                    this.checkAll = 0;
                    this.checkedUserIds = [];
                    this.getUserList()
                },
                onFileChange(e){
                    var files = e.target.files;
                    if(files){
                        for (let i=0; i< files.length; i++){
                            this.images.push(files[i]);
                        }
                    }
                },
                refreshSearch(){
                    var vm = this;
                    vm.pagination.current_page = 1;
                    vm.keywords = '';
                    vm.getUserList();
                }
            },
        });
    </script>
    <style>
        .vdp-datepicker input,
        .vdp-datepicker > div:first-child {
            width: 100%;
        }
        .vdp-datepicker input {
            border: 1px solid #ccc;
            border-radius: 3px;
            padding: 5px 8px;
            font-size: 13px;
        }
        .form-control {
            height: 30px;
            border-radius: 3px;
            border: 1px solid #ccc;
            font-size: 12px;
        }
        #user__screen .pagination {
            margin: 0;
        }
        .user-mess-search input,button {
            height: 35px;
        }
        .user-mess-search button {
            padding: 0 10px;
            white-space: nowrap;
            margin: 0 5px;
            border-radius: 5px;
        }
    </style>
@stop
