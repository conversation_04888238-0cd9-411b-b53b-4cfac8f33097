@extends('backend._default.dashboard')

@section('description') Quản lý người dùng @stop
@section('keywords') user @stop
@section('author') dungmori.com @stop
@section('title') Admin | Combo Vip @stop

@section('assets')
    <link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}">
@stop

@section('content')

    <div id="support_schedule">
        <div class="vip-combo-data">
            <h3 style="font-size: 20px;font-weight: 500;text-transform: uppercase;color: #000000">Nhắn tin tư vấn tự động</h3>
            <div class="header-fillter" style="display: flex;justify-content: space-between;align-items: center;margin: 20px 0">
                <div class="button-left" style="width: 50%">
                    <button type="button" class="vip-iv-create" v-on:click="showModal = true">
                        <i class="fa fa-plus" aria-hidden="true"></i>
                        <PERSON><PERSON><PERSON> lịch mới
                    </button>
                </div>
            </div>

            <table class="table">
                <thead>
                <tr>
                    <th>Khóa học</th>
                    <th>Thời gian nhắn sau kích hoạt (ngày)</th>
                    <th>Tin nhắn</th>
                    <th>Trạng thái</th>
                    <th>Hành động</th>
                </tr>
                </thead>
                <tbody>
                    <tr v-for="item in supports">
                        <td>@{{ item.course.name }}</td>
                        <td>@{{ item.time_after_start }}</td>
                        <td>@{{ item.message }}</td>
                        <td>
                            <span v-if="item.status == 1" class="bg-green-500 px-3 py-1 rounded-full text-white">Bật</span>
                            <span v-else class="bg-red-500 text-white px-3 py-1 rounded-full">Tắt</span>
                        </td>
                        <td>
                            <span class="btn btn-success" v-on:click="editSupportSchedule(item)">Sửa</span>
                            <span class="btn btn-danger" v-on:click="removeSupportSchedule(item.id)">Xoá</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <backend-modal v-if="showModal" @close="closeModal">
            <div slot="body">
                <form @submit="saveForm" enctype="multipart/form-data">
                    <div class="vip-combo-form">
                        <div class="course-combo" v-if="courses.length > 0 || formData.id != null">
                            <label class="vip-label">Áp dụng cho khoá</label>
                            <v-select class="course-combo-select"
                                v-model="formData.course_id"
                                :options="courses"
                                label="name"
                                :reduce="(course) => course.id"
                                placeholder="Chọn khoá">
                            </v-select>
                        </div>
                        <div class="course-combo mt-5">
                            <label class="vip-label">Thời gian nhắn sau kích hoạt (ngày)</label>
                            <input type="number" name="time_after_start" class="form-control" v-model="formData.time_after_start" min="0">
                        </div>
                        <div class="vip-combo-course-status mt-5">
                            <label class="vip-label">Tin nhắn</label>
                            <textarea class="border w-full min-h-[100px]" v-model="formData.message"></textarea>
                        </div>
                        <div class="vip-combo-course-status mt-5">
                            <label class="vip-label">Trạng Thái</label>
                            <select v-model="formData.status" class="form-control">
                                <option value="1">Bật</option>
                                <option value="0">Tắt</option>
                            </select>
                        </div>
                    </div>
                    <div class="vip-combo-bar mt-5">
                        <div class="vip-combo-submit">
                            <button class="btn btn-success">Lưu lại</button>
                        </div>
                    </div>
                </form>
            </div>
        </backend-modal>

    </div>

    <!-- inport plugin, libs -->
    <script src="{{asset('/plugin/ckeditor4-vue/dist/ckeditor.js')}}"></script>
    <script src="{{ asset('/plugin/jquery/axios.min.js') }}"></script>
    <script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>

    <!-- inport components -->
    <script src="{{ asset('/assets/backend/js/modal.js') }}"></script>

    <script>
        var supports = <?php echo $supports; ?>;
        var courses = <?php echo $courses; ?>;
        console.log('supports', supports)
        var supportSchedule = new Vue({
            el: "#support_schedule",
            components: {
                ckeditor: CKEditor.component,
                'v-select': VueSelect.VueSelect,
            },
            data() {
                return {
                    supports: supports,
                    courses: courses,
                    formData:{
                        id: null,
                        course_id: null,
                        time_after_start: 1,
                        message: '',
                        status: 1,
                    },
                    showModal: false,
                }
            },
            watch: {
            },
            methods: {
                closeModal() {
                    this.showModal = false;
                    this.formData = {
                        id: null,
                        course: null,
                        mesage: '',
                        time_after_start: 1,
                        status: 1,
                    }
                },
                saveForm: function (e) {
                    e.preventDefault();

                    console.log(this.formData);

                    if (this.formData.id) {
                        this.updateSupportSchedule();
                    } else {
                        this.addSupportSchedule();
                    }
                },
                addSupportSchedule: function () {
                   var vm = this;
                    axios.post(window.location.origin + '/backend/support-schedule/add', this.formData)
                        .then(function (response) {
                            if (response.status = 200) {
                                vm.closeModal;
                                window.location.reload();
                            }
                        });
                },
                editSupportSchedule: function (sc) {
                    this.formData = {
                        id: sc.id,
                        course_id: sc.course_id,
                        time_after_start: sc.time_after_start,
                        message: sc.message,
                        status: sc.status,
                    }
                    this.showModal = true;
                },
                updateSupportSchedule: function () {
                    var vm = this;
                    axios.post(window.location.origin + '/backend/support-schedule/update', this.formData)
                        .then(function (response) {
                            if (response.status = 200) {
                                vm.closeModal;
                                window.location.reload();
                            }
                        });
                },
                removeSupportSchedule: function (id) {
                    if (confirm('Xác nhận xoá')) {
                        axios.post(window.location.origin + '/backend/support-schedule/remove', { id: id })
                            .then(function (res) {
                                if (res.status = 200) {
                                    window.location.reload();
                                }
                            })
                            .then(function (error) {
                                console.log(error)
                            });
                    }
                },
            },
            mounted: function () {
                console.log('supports', supports)
            }
        });
    </script>
@stop
