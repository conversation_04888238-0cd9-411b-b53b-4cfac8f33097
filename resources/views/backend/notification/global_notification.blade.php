@extends('backend._default.dashboard')

@section('description') Quản lý thông báo app @stop
@section('keywords') notification @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý thông báo app @stop

@section('assets')
    <link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
@stop
@section('content')
    <div class="wrapper notification__screen">
        <div class="left-panel notification__form">
            <form>
                <h2>@{{ !input.id ? 'Thêm mới' : 'Sửa'}}</h2>

                <div class="form-group">
                    <label>Tiêu đề <span class="required-asterisk">*</span> (tối đa 50 ký tự)</label>
                    <input class="form-control" name="input.title" maxlength="50" @keyUp="onChangeText($event)" v-model="input.title"/>
                    <span class="form-error">@{{ errors.title }}</span>
                </div>
                <div class="form-group">
                    <label>Nội dung <span class="required-asterisk">*</span> (tối đa 200 ký tự)</label>
                    <textarea rows="7" maxlength="200" class="form-control" name="input.content" @keyUp="onChangeText($event)" v-model="input.content"></textarea>
                    <span class="form-error">@{{ errors.content }}</span>

                </div>
                <div class="form-group">
                    <label>Hình ảnh</label>
                    <input type="file" name="image" class="form-control" @change="onChangeImage($event)"/>
                    <img v-if="previewImg" id ="previewImg" :src="url + '/cdn/global_notification/default/' + previewImg" style="width: 100%;">
                    <img v-if="!previewImg" id ="previewImg" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 100%;">
                </div>
                <div class="form-group">
                    <label>Link đến</label>
                    <select class="form-control" name="input.tableName" @change="onChangeSelect($event)" v-model="input.tableName">
                        <option value="sale">
                            Thông tin sale
                        </option>
                        <option value="booking">
                            Booking kaiwa
                        </option>
{{--                        <option value="tips">--}}
{{--                            Tips--}}
{{--                        </option>--}}
                        <option value="lesson">
                            Bài học
                        </option>
                        <option value="course">
                            Khoá học
                        </option>
                        <option value="combo">
                            Combo
                        </option>
                        <option value="blog">
                            Bài viết
                        </option>
                    </select>
                </div>
                <div class="form-group" v-show="input.tableName && input.tableName != 'sale' && input.tableName !== 'booking' && input.tableName !== 'tips'">
                    <label>@{{ 'Chọn ' + renderTableIdLabel()}}</label>
{{--                    <input v-if="input.tableName == 'lesson'" class="form-control" name="input.tableId" @change="onChangeText($event)" v-model="input.tableId"/>--}}
{{--                    <select v-if="input.tableName != 'lesson'" class="form-control" name="input.tableId" @change="onChangeSelect($event)" v-model="input.tableId">--}}
{{--                        <option :value="table.id" v-for="table in tableIds" :key="table.id">--}}
{{--                            @{{ table.name || table.title }}--}}
{{--                        </option>--}}
{{--                    </select>--}}
                    <select name='input.tableId' class="form-control" id="tableIdForm" style="width: 100%;" v-on:change="onChangeSelect($event)" v-model="input.tableId"></select>
                </div>
                <div class="form-group" style="text-align: right">
                    <span class="btn btn-danger" @click="onResetForm()">Reset</span>
                    <span class="btn btn-info" @click="onSubmitForm()">@{{ input.id ? 'Lưu' : 'Tạo'}}</span>
                </div>
            </form>
        </div>
        <div class="right-panel">
            <div style="margin: 10px 20px 0 20px;">
                <a :href="url + '/backend/global-notification/schedule'">
                    <span style="display: inline-block; padding: 10px 20px; border: 2px solid #4c62ff; font-weight: bold; color: #4c62ff; cursor: pointer">Giao diện mới</span>
                </a>
            </div>
            <ul class="notification__list">
                <li class="notification__item" v-for="notification in notifications" :key="notification.id">
                    <div class="notification__item--image">
                        <img :src="url + '/cdn/global_notification/default/' + notification.info.bg_img" width="100px" v-if="notification.info.bg_img">
                        <img v-if="!notification.info.bg_img" src="{{url('assets/img/icon_backend')}}/no_image.png" width="100px">
                    </div>
                    <div class="notification__item--content">
                        <div>
                            <span @dblClick="editHeading(notification)" style="font-weight: bold" v-if="!notification.headingEditing">@{{notification.info.heading}}</span>
                            <input style="min-width: 900px" @keydown.enter.exact.prevent @keyup.esc="cancelHeading(notification)" @keyup.enter.exact="saveHeading(notification)" v-if="notification.headingEditing" v-model="notification.info.heading"/>
                            <span v-if="!notification.headingEditing" style="cursor:pointer;color: #0d95e8" v-on:click="editHeading(notification)"><i class="fa fa-edit"></i></span>
                            <span v-if="notification.headingEditing" style="cursor:pointer;color: #9f041b" v-on:click="cancelHeading(notification)"><i class="fa fa-close"></i></span>
                            <span v-if="notification.headingEditing" style="cursor:pointer;color: #008000" v-on:click="saveHeading(notification)"><i class="fa fa-check"></i></span>

                            <br/>
                            <span v-if="!notification.titleEditing" style="white-space: pre-wrap">@{{notification.title}}</span>
                            <textarea @keydown.enter.exact.prevent @keyup.esc="cancelTitle(notification)" @keyup.enter.exact="saveTitle(notification)" rows=5 style="width: 500px;" v-if="notification.titleEditing" v-model="notification.title"></textarea>
                            <span v-if="!notification.titleEditing" style="cursor:pointer;color: #0d95e8" v-on:click="editTitle(notification)"><i class="fa fa-edit"></i></span>
                            <span v-if="notification.titleEditing" style="cursor:pointer;color: #9f041b" v-on:click="cancelTitle(notification)"><i class="fa fa-close"></i></span>
                            <span v-if="notification.titleEditing" style="cursor:pointer;color: #008000" v-on:click="saveTitle(notification)"><i class="fa fa-check"></i></span>
                            <div class="notification__item--tags">
                                <span v-if="notification.table_name" class="notification__item--tag notification__item--tag-blue">@{{ printTableName(notification.table_name) }}</span>
                                <span v-if="!notification.table_name"class="notification__item--tag notification__item--tag-red">@{{ printTableName(notification.table_name) }}</span>
{{--                                <span v-if="notification.table_name"class="notification__item--tag notification__item--tag-yellow">--}}
{{--                                    <a :href="window.location.origin + '/' " target="blank">@{{ notification.target_obj.name }}</a>--}}
{{--                                </span>--}}
                                <span v-if="notification.table_name && notification.table_name != 'booking' && notification.table_name != 'sale' && input.tableName !== 'tips'" class="notification__item--tag notification__item--tag-yellow">
                                    <a :href="window.location.origin + '/' + notification.url" target="blank">Link</a>
                                </span>
{{--                                <span v-if="notification.data.logs" v-for="log in notification.data.logs" style="cursor:pointer;" class="notification__item--tag notification__item--tag-green" data-toggle="modal" data-target="#logsModal" v-on:click="printLogs(notification.logs)">--}}
{{--                                    <i class="fa fa-check"></i> @{{ printTime(log.time) || '--' }}--}}
{{--                                </span>--}}
                                <span v-if="notification.info.logs" v-for="log in notification.info.logs" style="cursor:pointer;" class="notification__item--tag" v-bind:class="{'notification__item--tag-green': log.is_pushed, 'notification__item--tag-yellow': !log.is_pushed}">
                                    <i class="fa fa-check" v-if="log.is_pushed"></i> @{{ printTime(log.time) || '--' }}<i class="fa fa-times" v-if="!log.is_pushed" v-on:click="cancelSchedule(log.id, notification.id)"></i>
                                </span>
                            </div>
                        </div>

                    </div>
                    <div class="notification__item--actions">
                        <div class="notification__item--push notification__item--action" data-toggle="modal" data-target="#pushModal" v-on:click="setDataToPush(notification.id)">
                            <a class="btn" id="pushNotice"><i class="fa fa-paper-plane"></i></a>
                        </div>
{{--                        <div class="notification__item--edit notification__item--action" v-on:click="editNotice(notification.id)">--}}
{{--                            <span class="btn"><i class="fa fa-edit"></i></span>--}}
{{--                        </div>--}}
                        <div
                                class="notification__item--remove notification__item--action"
                                v-on:click="removeNotice(notification.id)"
                        >
                            <i class="fa fa-trash"></i>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <!-- Modal target-->
        <div class="modal fade" id="pushModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLongTitle">Chọn đối tượng push thông báo</h5>
                    </div>
                    <div class="modal-body">
                        <select name="segments" class="form-control" @change="onChangeSelect($event)" v-model="segments">
                            <option value="Test Segment">Người dùng test</option>
                            <option value="Subscribed Users">Tất cả người dùng</option>
{{--                            <option value="Active Users">Nhóm đang hoạt động</option>--}}
{{--                            <option value="Engaged Users">Nhóm tích cực</option>--}}
{{--                            <option value="Inactive Users">Nhóm dừng hoạt động</option>--}}
                        </select>
                        <div>
                            <label>Hẹn giờ gửi</label>
                            <input type="checkbox" name="isSchedule" v-model="isSchedule"/>
                        </div>
                        <input v-show="isSchedule" type='text' id='datetimepicker1' class="form-control" name="scheduleTime" v-model="scheduleTime"/>
                        <div style="text-align: right; margin-top: 10px">
                            <span class="btn btn-success" v-on:click="pushNotice()">Gửi thông báo</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
{{--        <div class="modal fade" id="logsModal" tabindex="-1" role="dialog" aria-labelledby="logsModal" aria-hidden="true">--}}
{{--            <div class="modal-dialog" role="document">--}}
{{--                <div class="modal-content">--}}
{{--                    <div class="modal-header">--}}
{{--                        <h4 class="modal-title" id="exampleModalLongTitle"><b>Logs</b></h4>--}}
{{--                    </div>--}}
{{--                    <div class="modal-body">--}}
{{--                        <ul class="notification__item--logs">--}}
{{--                            <li v-for="log in logs">--}}
{{--                                Được--}}
{{--                                <b>@{{ log.type == 'create' ? 'tạo' : 'push'}}</b> bởi--}}
{{--                                <b>@{{ log.name }}</b> vào @{{ printTime(log.time) }}--}}
{{--                            </li>--}}
{{--                        </ul>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
    </div>
@stop
@section('footer')
    <script>
        var notifications = {!! json_encode($notifications) !!};
    </script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
    <script src="{{ asset('assets/backend/js/global-notification.js') }}?{{ filemtime('assets/backend/js/global-notification.js') }}"></script>
    <script>
        $(function () {
            $('#datetimepicker1').datetimepicker({
                format: 'YYYY-MM-DD HH:mm'
            }).on('dp.change', function (event) {
                notices.onChangeDatetime(event);
            });
        });
    </script>
@stop
