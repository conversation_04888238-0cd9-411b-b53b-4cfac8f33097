@extends('backend._default.dashboard')

@section('description') Quản lý thông báo app @stop
@section('keywords') notification @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý thông báo app @stop

@section('assets')
    <link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />
@stop
@section('content')
    <div class="wrapper schedule__screen">
        <div class="schedule__screen--navigation flex justify-between items-center">
            <span>Thông báo</span>
            <div class="flex flex-row justify-center items-center">
                <a :class="{'active': activeTab == 'sent'}" @click="activeTab = 'sent'" :href="url + '/backend/global-notification/schedule#sent'">Đ<PERSON> gửi</a>
                <a :class="{'active': activeTab == 'scheduling'}" @click="activeTab = 'scheduling'" :href="url + '/backend/global-notification/schedule#scheduling'">Lên lịch</a>
            </div>
            <span>
                <span @click="directPushForm.show = true" v-if="activeTab == 'sent'" class="hover-able hover-bg-red" style="padding: 10px 20px;"><i class="fa fa-paper-plane"></i> &nbsp; Gửi trực tiếp</span>
                <span  @click="timerPushForm.show = true" v-if="activeTab == 'scheduling'" class="hover-able hover-bg-green" style="padding: 10px 20px;"><i class="fa fa-clock-o"></i> Lên lịch gửi</span>
            </span>
        </div>
        <div class="schedule__screen--list">
            <sent-list v-on:clone:notification="cloneNotification($event)" v-if="activeTab == 'sent'"></sent-list>
            <schedule-list v-on:clone:schedule="cloneSchedule($event)"v-if="activeTab == 'scheduling'"></schedule-list>
        </div>
        <backend-modal v-show="directPushForm.show" @close="closeForm('directPushForm')">
            <h3 slot="header">Gửi thông báo trực tiếp</h3>
            <div slot="body">
                <div class="form-group">
                    <label>Tiêu đề [<span :style="{'color': directPushForm.heading.length > 50 ? '#a83330': '#111'}">@{{ directPushForm.heading.length }}</span>/50 ký tự]</label>
                    <input @keyup="countCharacter('directPushForm', 'heading', $event)" class="form-control" v-model="directPushForm.heading.text"/>
                </div>
                <div class="form-group">
                    <label>Nội dung [<span :style="{'color': directPushForm.title.length > 200 ? '#a83330': '#111'}">@{{ directPushForm.title.length }}</span>/200 ký tự]</label>
                    <textarea @keyup="countCharacter('directPushForm', 'title', $event)" rows="4" class="form-control" v-model="directPushForm.title.text"></textarea>
                </div>

                <div class="form-group">
                    <label>Đối tượng push</label>
                    <select class="form-control" @change="onChangeSelect('directPushForm', 'segments', $event)" v-model="directPushForm.segments">
                        <option :value="'Test Segment'">Người dùng Test</option>
                        <option :value="'Subscribed Users'">Người dùng thật</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trỏ đến</label>
                    <select class="form-control" @change="onChangeSelect('directPushForm', 'tableName', $event)" v-model="directPushForm.tableName">
                        <option :value="undefined">Không dẫn link</option>
                        <option :value="'sale'">Thông tin sale</option>
                        <option :value="'course'">Khoá học</option>
                        <option :value="'lesson'">Bài học</option>
{{--                        <option :value="'blog'">Bài viết</option>--}}
                        <option :value="'jlpt'">Thi thử</option>
                    </select>
                </div>
                <div class="form-group" v-if="directPushForm.tableName && !['sale','booking','jlpt'].includes(directPushForm.tableName)">
                    <label>ID cần trỏ</label>
                    <input class="form-control" style="width: 100%" v-model="directPushForm.tableId"/>
                </div>
                <div style="text-align: center">
                    <span class="btn btn-success" @click="pushDirectNotification">Gửi thông báo</span>
                    <span class="btn btn-danger" @click="cancel('directPushForm')">Huỷ</span>
                </div>
            </div>
        </backend-modal>
        <backend-modal v-show="timerPushForm.show" @close="closeForm('timerPushForm')">
            <h3 slot="header">Lên lịch gửi thông báo</h3>
            <div slot="body">
                <div class="form-group">
                    <label>Tiêu đề [<span :style="{'color': timerPushForm.heading.length > 50 ? '#a83330': '#111'}">@{{ timerPushForm.heading.length }}</span>/50 ký tự]</label>
                    <input @keyup="countCharacter('timerPushForm', 'heading', $event)" class="form-control" v-model="timerPushForm.heading.text"/>
                </div>
                <div class="form-group">
                    <label>Nội dung [<span :style="{'color': timerPushForm.title.length > 200 ? '#a83330': '#111'}">@{{ timerPushForm.title.length }}</span>/200 ký tự]</label>
                    <textarea @keyup="countCharacter('timerPushForm', 'title', $event)" rows="4" class="form-control" v-model="timerPushForm.title.text"></textarea>
                </div>
                <div class="form-group">
                    <label>Giờ hẹn</label>
                    <input type='text' id='datetimepicker1' class="form-control" v-model="timerPushForm.timer"/>
                </div>
                <div class="form-group">
                    <label>Đối tượng push</label>
                    <select class="form-control" @change="onChangeSelect('timerPushForm', 'segments', $event)" v-model="timerPushForm.segments">
                        <option :value="'Test Segment'">Người dùng Test</option>
                        <option :value="'Subscribed Users'">Người dùng thật</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trỏ đến</label>
                    <select class="form-control" @change="onChangeSelect('timerPushForm', 'tableName', $event)" v-model="timerPushForm.tableName">
                        <option :value="undefined">Không dẫn link</option>
                        <option :value="'sale'">Thông tin sale</option>
                        <option :value="'course'">Khoá học</option>
                        <option :value="'lesson'">Bài học</option>
{{--                        <option :value="'blog'">Bài viết</option>--}}
                        <option :value="'jlpt'">Thi thử</option>
                    </select>
                </div>
                <div class="form-group" v-if="timerPushForm.tableName && !['sale','booking','jlpt'].includes(timerPushForm.tableName)">
                    <label>ID cần trỏ</label>
                    <input class="form-control" style="width: 100%" v-model="timerPushForm.tableId"/>
                </div>
                <div style="text-align: center">
                    <span class="btn btn-success" @click="storeSchedule">Lên lịch</span>
                    <span class="btn btn-danger" @click="cancel('timerPushForm')">Huỷ</span>
                </div>
            </div>
        </backend-modal>
    </div>

@stop
@section('footer')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
    <script src="{{ asset('assets/backend/js/modal.js') }}?{{ filemtime('assets/backend/js/modal.js') }}"></script>
    <script src="{{ asset('assets/backend/js/schedule/sent-list.js') }}?{{ filemtime('assets/backend/js/schedule/sent-list.js') }}"></script>
    <script src="{{ asset('assets/backend/js/schedule/schedule-list.js') }}?{{ filemtime('assets/backend/js/schedule/schedule-list.js') }}"></script>
    <script src="{{ asset('assets/backend/js/schedule/schedule-notification.js') }}?{{ filemtime('assets/backend/js/schedule/schedule-notification.js') }}"></script>
    <script>
      $(function () {
        $('#datetimepicker1').datetimepicker({
          format: 'YYYY-MM-DD HH:mm'
        }).on('dp.change', function (event) {
          schedule_notification.onChangeDatetime(event);
        });
      });
    </script>
@stop
