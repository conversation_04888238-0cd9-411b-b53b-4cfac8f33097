@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON>n lý phương thức thanh toán @stop
@section('keywords') payment method @stop
@section('author') dungmori.com @stop
@section('title') Admin | Phương thức thanh toán @stop

@section('assets')
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet">
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            <PERSON><PERSON><PERSON><PERSON> lý phương thức thanh toán
        </h4>
        @if(json_decode(Auth::guard('admin')->user()->matrix)->payment->add != null)
            <button class="add-modal btn btn-success"
                    style="right: 26px;position: absolute;width: 146px;">
                    <span class="glyphicon glyphicon-plus"></span>Thêm mới
            </button>
        @endif
    </div>
    <div class="table_method">
        @include('backend.payment_method.detailMethod')
    </div>

    <div id="pageModal" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>

                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form">
                        <li class="global_error text-left hidden"></li>
                        <div class="form-group maso">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="fid">
                            </div>
                        </div>

                         <div class="form-group">
                            <label class="control-label col-sm-2" for="name">Phương thức</label>
                            <div class="col-sm-10">
                                <textarea type="text" class="form-control" id="name"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="description">Mô tả</label>
                            <div class="col-sm-10">
                                <textarea type="text" class="form-control" id="description"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="status">Trạng thái</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1" >
                                    <span class="labels">Bật</span>
                                </label>
                            </div>
                        </div>

                    </form>
                    <!--delete form-->
                    <div class="deleteContent"> Xác nhận xóa phương thức thanh thoán này không ?
                            <span class="hidden id_payment "> </span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
     $(document).ready(function() {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    });
    var description = CKEDITOR.replace( 'description' , {
        filebrowserBrowseUrl: '/backend/ckfinder/browser',
    });
    //add
    $(document).on('click', '.add-modal', function() {
        $('.maso').hide();
        $('#footer_action_button').text("Thêm");
        $('#footer_action_button').addClass('glyphicon-check');
        $('#footer_action_button').removeClass('glyphicon-trash');
        $('.actionBtn').addClass('btn-success');
        $('.actionBtn').removeClass('btn-danger');
        $('.actionBtn').removeClass('delete');
        $('.actionBtn').removeClass('edit');
        $('.actionBtn').addClass('add');
        $('.modal-title').text('Thêm');
        $('.deleteContent').hide();
        $('.form-horizontal').show();
        fillmodalData('');
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '60%');
        $('#pageModal').css('left', '21%');
    });
    //edit
    $(document).on('click', '.edit-modal', function(e) {
        $('.maso').show();
        $('#fid').attr('disabled', true);
        $('#footer_action_button').text(" Sửa");
        $('#footer_action_button').addClass('glyphicon-check');
        $('#footer_action_button').removeClass('glyphicon-trash');
        $('.actionBtn').addClass('btn-success');
        $('.actionBtn').removeClass('btn-danger');
        $('.actionBtn').removeClass('delete');
        $('.actionBtn').removeClass('add');
        $('.actionBtn').addClass('edit');
        $('.modal-title').text('Sửa');
        $('.deleteContent').hide();
        $('.form-horizontal').show();
        var id = $(this).data('info');
        $.ajax({
            type: 'get',
            url: '/backend/payment/' + id ,
            success: function(data) {
                fillmodalData(data[0]);
            }
        });
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '60%');
        $('#pageModal').css('left', '21%');
    });
    //delete
    $(document).on('click', '.delete-modal', function() {
        $('#footer_action_button').text("Xóa");
        $('.modal-title').text('Xóa');
        $('#footer_action_button').removeClass('glyphicon-check');
        $('#footer_action_button').addClass('glyphicon-trash');
        $('.actionBtn').removeClass('btn-success');
        $('.actionBtn').addClass('btn-danger');
        $('.actionBtn').removeClass('edit');
        $('.actionBtn').removeClass('add');
        $('.actionBtn').addClass('delete');
        $('.deleteContent').show();
        $('.form-horizontal').hide();
        var id = $(this).data('info');
        $('.id_payment').text(id);
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '36%');
        $('#pageModal').css('left', '33%');
    });
    //
    function fillmodalData(payment){
        if(payment == ''){
            $('#fid').val('');
            $('#name').val('');
            description.setData('');
            $('#status_off').prop('checked', true) ;
            $('.global_error').addClass('hidden');
        }else{
            $('#fid').val(payment.id);
            $('#name').val(payment.name);
            description.setData(payment.description);
            (payment.status == 1) ? $('#status_on').prop('checked', true) : $('#status_off').prop('checked', true) ;
            $('.global_error').addClass('hidden');
        }
    }
    //add
    $('.modal-footer').on('click', '.add', function() {
        $.ajax({
            type: 'post',
            url: '/backend/payment/create',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $("#fid").val(),
                'name': $('#name').val(),
                'description': description.getData(),
                'status': $('input[name=status]:checked').val()
            },
            success: function(data) {
                if (data.errors){
                    if(Object.keys(data.errors).length > 0){
                        $('#pageModal').modal('show');
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Dữ liệu còn trống !");
                    }
                }
                else {
                    $('#pageModal').modal('hide');
                    $('.table_method').html(data);
                }
            }
        });
    });
    //edit
    $('.modal-footer').on('click', '.edit', function() {
        $.ajax({
            type: 'post',
            url: '/backend/payment/edit',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $("#fid").val(),
                'name': $('#name').val(),
                'description': description.getData(),
                'status': $('input[name=status]:checked').val()
            },
            success: function(data) {
                if (data.errors){
                   if(Object.keys(data.errors).length > 0){
                        $('#pageModal').modal('show');
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Dữ liệu còn trống !");
                    }
                }
                else {
                    $('#pageModal').modal('hide');
                    $('.table_method').html(data);
                }
            }
        });
    });
    //delete
    $('.modal-footer').on('click', '.delete', function() {
        $.ajax({
            type: 'post',
            url: '/backend/payment/delete',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $('.id_payment').text(),
            },
            success: function(data) {
                $('#pageModal').modal('hide');
                $('.item' + $('.id_payment').text()).remove();
            }
        });
    });
</script>

@stop
