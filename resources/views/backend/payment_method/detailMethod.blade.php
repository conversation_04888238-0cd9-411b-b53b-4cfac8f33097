<table class="table table-borderless" id="table">
	<thead>
		<tr>
			<th class="text-center"><PERSON><PERSON></th>
			<th class="text-center"><PERSON><PERSON><PERSON><PERSON> thức</th>
			<th class="text-center">Tr<PERSON><PERSON> thái</th>
			<th class="text-center">Hành động</th>
		</tr>
	</thead>
    <tbody>
		@foreach($payment as $item)
		<tr class="item{{$item->id}}">
			<td class="text-center">{{$item->id}}</td>
			<td class="text-center">{{$item->name}}</td>
            <td class="text-center">
                @if($item->status == 1)
                    <span class="label label-success">Bật</span>
                @else
                     <span class="label label-danger">Tắt</span>
                @endif
            </td>
			<td class="text-center">
				@if(json_decode(Auth::guard('admin')->user()->matrix)->payment->edit != null)
					<button class="edit-modal btn btn-info"
						data-info="{{$item->id}}">
						<span class="glyphicon glyphicon-edit"></span> Sửa
					</button>
                @endif

                @if(json_decode(Auth::guard('admin')->user()->matrix)->payment->delete != null)
					<button class="delete-modal btn btn-danger"
						data-info="{{$item->id}}">
	                    <span class="glyphicon glyphicon-trash"></span> Xóa
					</button>
				@endif
			</td>
		</tr>
		@endforeach
    </tbody>
</table>
