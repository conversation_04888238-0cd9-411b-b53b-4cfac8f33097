<table>
    <thead>
    <tr>
        <td>Mã Gia</td>
        <td>Họ và tên</td>
        <td>Email</td>
        <td>SĐT</td>
        <td>Tiế<PERSON> độ học</td>
        <td>Th<PERSON><PERSON> gian họ<PERSON></td>
        @foreach($weeks as $key => $value)
            <td>{{ $key }}</td>
        @endforeach
    </tr>
    </thead>
    <tbody>
    @foreach ($users as $user)
    <tr>
        <td>{{ $user['id'] }}</td>
        <td>{{ $user['name'] }}</td>
        <td>{{ $user['email'] }}</td>
        <td>{{ $user['phone'] }}</td>
        <td>{{ isset($user['progress'][0]) ? round((int) $user['progress'][0]['total'] / $lessonCount, 2) : 0 }}%</td>
        <td>{{ isset($user['lesson_tracking_stat'][0]) ? round((int) $user['lesson_tracking_stat'][0]['total']) : 0 }}</td>
        @foreach($weeks as $value)
            <td>{{ $value }}</td>
        @endforeach
    </tr>
    @endforeach
    </tbody>
</table>
