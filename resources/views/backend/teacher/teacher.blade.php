@extends('backend._default.dashboard')

@section('description') Quản lý gi<PERSON>o viên @stop
@section('keywords') teacher @stop
@section('author') dungmori.com @stop
@section('title') Admin | Gi<PERSON><PERSON> viên @stop

@section('assets')
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet">
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Quản lý gi<PERSON>o viên
        </h4>
        @if(json_decode(Auth::guard('admin')->user()->matrix)->teacher->add != null)
            <button class="add-modal btn btn-success"
                style="right: 26px;position: absolute;width: 146px;">
                <span class="glyphicon glyphicon-plus"></span>Thêm mới
            </button>
        @endif
    </div>
    <div class="table_teacher">
        @include('backend.teacher.detailTeacher')
    </div>

    <div id="pageModal" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" role="form" id="teacher_form">
                        <div class="form-group" style="display: none;">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-10">
                                <input type="number" class="form-control" name="id" id="fid">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="name">Tên tác giả</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="teacher" id="name">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="info">Thông tin</label>
                            <div class="col-sm-10">
                                <textarea type="text" class="form-control" name="infomation" id="info"></textarea>
                            </div>
                        </div>

                         <div class="form-group">
                            <label class="control-label col-sm-2" for="link">Link youtube</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="link" id="link">
                            </div>
                        </div>

                         <div class="form-group">
                            <label class="control-label col-sm-2" for="name">Ảnh nền youtube</label>
                            <div class="col-sm-10">
                                <div class="upload_complete" style="display: block;">
                                    <div class="file_image_single">
                                        <div class="file_image_single_img">
                                            <img id ="image_intro" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 100px; max-height: 100px;">
                                        </div>
                                    </div>
                                </div>
                                <div class="upload_action" style="margin-top:2px;">
                                    <input type="file" name="img_intro" id="img_intro" onchange="previewIntro()">
                                    <div class="f11 formNote" style="color: red;">
                                        CHÚ Ý - chỉ cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>png</b>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="image">Ảnh banner</label>
                            <div class="col-sm-10">
                                <div class="upload_complete" style="display: block;">
                                    <div class="file_image_single">
                                        <div class="file_image_single_img">
                                            <img id ="image_teacher" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 100px; max-height: 100px;">
                                        </div>
                                    </div>
                                </div>
                                <div class="upload_action" style="margin-top:2px;">
                                    <input type="file" name="img_teacher" id="img_teacher" onchange="previewTeacher()">
                                    <div class="f11 formNote" style="color: red;">
                                        CHÚ Ý - chỉ cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>png</b>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="image">Ảnh chi tiết</label>
                            <div class="col-sm-10">
                                <div class="upload_complete" style="display: block;">
                                    <div class="file_image_single">
                                        <div class="file_image_single_img">
                                            <img id ="image_detail" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 100px; max-height: 100px;">
                                        </div>
                                    </div>
                                </div>
                                <div class="upload_action" style="margin-top:2px;">
                                    <input type="file" name="img_detail" id="img_detail" onchange="previewDetail()">
                                    <div class="f11 formNote" style="color: red;">
                                        CHÚ Ý - chỉ cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>png</b>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="description">Giới thiệu</label>
                            <div class="col-sm-10">
                                <textarea type="text" class="form-control" name="introduction" id="introduction"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="description">Tiểu sử</label>
                            <div class="col-sm-10">
                                <textarea type="text" class="form-control" name="description" id="description"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="story_description">Hoạt động tiêu biểu</label>
                            <div class="col-sm-10">
                                <textarea type="text" class="form-control" name="story_description" id="story_description"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="new">Tiêu biểu</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="new_off" name="new" value="0">
                                    <span class="labels">Không</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="new_on" name="new" value="1" >
                                    <span class="labels">Có</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="status">Trạng thái</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1" >
                                    <span class="labels">Bật</span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="status">Là giáo viên</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="teacher_cooperate" name="type" value="3">
                                    <span class="labels">Hợp tác</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="teacher_feature" name="type" value="2" >
                                    <span class="labels">Nổi bật</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="teacher_normal" name="type" value="1" >
                                    <span class="labels">Thường</span>
                                </label>
                            </div>
                        </div>
                        <li class="global_error text-left hidden"></li>
                    </form>
                    <!--delete form-->
                    <div class="deleteContent"> Xác nhận xóa giáo viên này không ?
                            <span class="hidden id_teacher "> </span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
<script>
    $(document).ready(function() {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    });
    var description = CKEDITOR.replace( 'description' , {
            filebrowserBrowseUrl: '/backend/ckfinder/browser',
        });
    var story_description = CKEDITOR.replace( 'story_description' , {
            filebrowserBrowseUrl: '/backend/ckfinder/browser',
        });
    var introduction = CKEDITOR.replace( 'introduction' , {
        filebrowserBrowseUrl: '/backend/ckfinder/browser',
    });
    sortTable('bodyTeacher', 1, 7, '/backend/teacher-sort-order', 'sort_order', 'table_teacher');

    //chang image
    function previewTeacher() {
        var preview = document.querySelector('#image_teacher');
        var file    = document.querySelector('#img_teacher').files[0];
        console.log(file);
        var reader  = new FileReader();
        reader.onloadend = function () {
            preview.src = reader.result;
        }
        if(file) {
            reader.readAsDataURL(file);
        }else {
            preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
        }
    }
    function previewIntro() {
        var preview = document.querySelector('#image_intro');
        var file    = document.querySelector('#img_intro').files[0];
        var reader  = new FileReader();
        reader.onloadend = function () {
            preview.src = reader.result;
        }
        if(file) {
            reader.readAsDataURL(file);
        }else {
            preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
        }
    }
    function previewDetail() {
        var preview = document.querySelector('#image_detail');
        var file    = document.querySelector('#img_detail').files[0];
        var reader  = new FileReader();
        reader.onloadend = function () {
            preview.src = reader.result;
        }
        if(file) {
            reader.readAsDataURL(file);
        }else {
            preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
        }
    }

    //add
    $(document).on('click', '.add-modal', function() {
        $('#footer_action_button').text("Thêm");
        $('#footer_action_button').addClass('glyphicon-check');
        $('#footer_action_button').removeClass('glyphicon-trash');
        $('.actionBtn').addClass('btn-success');
        $('.actionBtn').removeClass('btn-danger');
        $('.actionBtn').removeClass('delete');
        $('.actionBtn').removeClass('edit');
        $('.actionBtn').addClass('add');
        $('.modal-title').text('Thêm');
        $('.deleteContent').hide();
        $('.form-horizontal').show();
        fillmodalData('');
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '60%');
        $('#pageModal').css('left', '21%');
    });
    //edit
    $(document).on('click', '.edit-modal', function(e) {
        $('#footer_action_button').text(" Sửa");
        $('#footer_action_button').addClass('glyphicon-check');
        $('#footer_action_button').removeClass('glyphicon-trash');
        $('.actionBtn').addClass('btn-success');
        $('.actionBtn').removeClass('btn-danger');
        $('.actionBtn').removeClass('delete');
        $('.actionBtn').removeClass('add');
        $('.actionBtn').addClass('edit');
        $('.modal-title').text('Sửa');
        $('.deleteContent').hide();
        $('.form-horizontal').show();
        var id = $(this).data('info');
        $.ajax({
            type: 'get',
            url: '/backend/teacher/' + id ,
            success: function(data) {
                fillmodalData(data[0]);
            }
        });
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '60%');
        $('#pageModal').css('left', '21%');
    });
    //delete
    $(document).on('click', '.delete-modal', function() {
        $('#footer_action_button').text("Xóa");
        $('.modal-title').text('Xóa');
        $('#footer_action_button').removeClass('glyphicon-check');
        $('#footer_action_button').addClass('glyphicon-trash');
        $('.actionBtn').removeClass('btn-success');
        $('.actionBtn').addClass('btn-danger');
        $('.actionBtn').removeClass('edit');
        $('.actionBtn').removeClass('add');
        $('.actionBtn').addClass('delete');
        $('.deleteContent').show();
        $('.form-horizontal').hide();
        var id = $(this).data('info');
        $('.id_teacher').text(id);
        $('#pageModal').modal('show');
        $('#pageModal').css('width', '36%');
        $('#pageModal').css('left', '33%');
    });
    //
    function fillmodalData(teacher){
        if (teacher == '') {
            $('#fid').val(0);
            $('#name').val('');
            $('#info').val('');
            $('#link').val('');
            $('#image_intro').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
            $('#image_teacher').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
            $('#image_detail').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
            $('#img_intro').val('');
            $('#img_teacher').val('');
            description.setData('');
            story_description.setData('');
            introduction.setData('');
            $('#status_off').prop('checked', true) ;
            $('#new_off').prop('checked', true) ;
            $('#teacher_normal').prop('checked', true) ;
            $('.global_error').addClass('hidden');
        } else {
            $('#fid').val(teacher.id);
            $('#name').val(teacher.name);
            $('#info').val(teacher.information);
            $('#link').val(teacher.youtube_intro);
            if (teacher.image_intro == null || teacher.image_intro == "") {
                $('#image_intro').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
            } else {
                $('#image_intro').attr('src', '{{url('cdn/teacher_info/default')}}/' + teacher.image_intro);
            }
            if (teacher.avatar_name == null || teacher.avatar_name == "") {
                $('#image_teacher').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
            } else {
                $('#image_teacher').attr('src', '{{url('cdn/teacher/default')}}/' + teacher.avatar_name);
            }
            if (teacher.avartar_detail == null || teacher.avartar_detail == "") {
                $('#image_detail').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
            } else {
                $('#image_detail').attr('src', '{{url('cdn/teacher_detail/default')}}/' + teacher.avartar_detail);
            }
            $('#img_intro').val('');
            $('#img_teacher').val('');
            $('#img_detail').val('');
            description.setData(teacher.description);
            story_description.setData(teacher.story_description);
            introduction.setData(teacher.introduction);
            (teacher.feature == 1) ? $('#new_on').prop('checked', true) : $('#new_off').prop('checked', true) ;
            (teacher.show == 1) ? $('#status_on').prop('checked', true) : $('#status_off').prop('checked', true) ;
            switch (teacher.type) {
                case 1:
                    $('#teacher_normal').prop('checked', true);
                    $('#teacher_feature').prop('checked', false);
                    $('#teacher_cooperate').prop('checked', false);
                    break;
                case 2:
                    $('#teacher_normal').prop('checked', false);
                    $('#teacher_feature').prop('checked', true);
                    $('#teacher_cooperate').prop('checked', false);
                    break;
                case 3:
                    $('#teacher_normal').prop('checked', false);
                    $('#teacher_feature').prop('checked', false);
                    $('#teacher_cooperate').prop('checked', true);
                    break;
                default:
                    $('#teacher_normal').prop('checked', false);
                    $('#teacher_feature').prop('checked', false);
                    $('#teacher_cooperate').prop('checked', true);
            }
            $('.global_error').addClass('hidden');
        }
    }
    //add
    $('.modal-footer').on('click', '.add', function() {
        var dataTeacher = new FormData($("#teacher_form")[0]);
        dataTeacher.append('description', CKEDITOR.instances['description'].getData());
        dataTeacher.append('story_description', CKEDITOR.instances['story_description'].getData());
        dataTeacher.append('introduction', CKEDITOR.instances['introduction'].getData());
        $.ajax({
            type: 'post',
            url: '/backend/teacher/create',
            processData: false,
            contentType: false,
            data : dataTeacher,
            success: function(data) {
                if (data.errors){
                    if(Object.keys(data.errors).length > 0){
                        $('#pageModal').modal('show');
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Dữ liệu còn trống !");
                    }
                }
                else {
                    if(data == 'imagetype'){
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Ảnh không đúng định dạng");
                    }else if(data == 'imagesize'){
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Ảnh vượt quá 3MB");
                    }else{
                        $('#pageModal').modal('hide');
                        $('.table_teacher').html(data);
                    }
                }
            }
        });
    });
    //edit
    $('.modal-footer').on('click', '.edit', function() {
        var dataTeacher = new FormData($("#teacher_form")[0]);
        dataTeacher.append('description', CKEDITOR.instances['description'].getData());
        dataTeacher.append('story_description', CKEDITOR.instances['story_description'].getData());
        dataTeacher.append('introduction', CKEDITOR.instances['introduction'].getData());
        $.ajax({
            type: 'post',
            url: '/backend/teacher/edit',
            processData: false,
            contentType: false,
            data : dataTeacher,
            success: function(data) {
                if (data.errors){
                    if(Object.keys(data.errors).length > 0){
                        $('#pageModal').modal('show');
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Dữ liệu còn trống !");
                    }
                }
                else {
                    if(data == 'imagetype'){
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Ảnh không đúng định dạng");
                    }else if(data == 'imagesize'){
                        $('.global_error').removeClass('hidden');
                        $('.global_error').text("Ảnh vượt quá 3MB");
                    }else{
                        $('#pageModal').modal('hide');
                        $('.table_teacher').html(data);
                    }
                }
            }
        });
    });
    //delete
    $('.modal-footer').on('click', '.delete', function() {
        $.ajax({
            type: 'post',
            url: '/backend/teacher/delete',
            data: {
                '_token': $('input[name=_token]').val(),
                'id': $('.id_teacher').text(),
            },
            success: function(data) {
                $('#pageModal').modal('hide');
                $('.item' + $('.id_teacher').text()).remove();
            }
        });
    });
</script>
@stop
