<table class="table table-borderless" id="table_teacher">
	<thead>
		<tr>
			<th class="text-center"><PERSON><PERSON>ố</th>
			<th class="text-center">Ảnh đại diện</th>
			<th class="text-center">Tên giáo viên</th>
			<th class="text-center">Thông tin</th>
			<th class="text-center">Trạng thái</th>
			<th class="text-center">Thứ tự</th>
			<th class="text-center">Hành động</th>
		</tr>
	</thead>
    <tbody class="bodyTeacher">
		@foreach($teacher as $item)
			<tr class="item{{$item->id}}">
				<td class="text-center">{{$item->id}}</td>
				<td class="text-center">
					<img
					@if (is_null($item) || $item->avatar_name == null || $item->avatar_name == "")
						src="{{url('assets/img/icon_backend')}}/no_image.png"
					@else
						src="{{url('/cdn/teacher/default/'.$item->avatar_name)}}"
					@endif
					height="40px" width="200px">
				</td>
	            <td class="text-center">
	            	<a href="{{url('/giao-vien/'.$item->SEOurl)}}" target="_blank">{{$item->name}}</a>
					@if ($item->type == 2)
						<span title="Giáo viên hợp tác với trung tâm hiện trong slide" style="font-size: 10px; padding: 2px 5px; background: red; color: white; font-weight: bold; border-radius: 3px">Nổi bật</span>
					@endif
					@if ($item->type == 3)
						<span title="Giáo viên nổi bật hiện đầu danh sách" style="font-size: 10px; padding: 2px 5px; background: #0F4C82; color: white; font-weight: bold; border-radius: 3px">Hợp tác</span>
					@endif
	            </td>
	            <td class="text-center">{{$item->information}}</td>
	            <td class="text-center">
	            	@if($item->show == 0)
	            		<span class="label label-danger">Tắt</span>
	            	@else
	            		<span class="label label-success">Bật</span>
	            	@endif
	            </td>
	            <td class="text-center">{{$item->sort_order}}</td>
				<td class="text-center">
					@if($item->protected == 1)
						@if(json_decode(Auth::guard('admin')->user()->matrix)->teacher->edit != null)
							<button class="edit-modal btn btn-info"
								data-info="{{$item->id}}">
								<span class="glyphicon glyphicon-edit"></span> Sửa
							</button>
						@endif
					@else
						@if(json_decode(Auth::guard('admin')->user()->matrix)->teacher->edit != null)
							<button class="edit-modal btn btn-info"
								data-info="{{$item->id}}">
								<span class="glyphicon glyphicon-edit"></span> Sửa
							</button>
						@endif

	                	@if(json_decode(Auth::guard('admin')->user()->matrix)->teacher->delete != null)
							<button class="delete-modal btn btn-danger"
								data-info="{{$item->id}}">
			                    <span class="glyphicon glyphicon-trash"></span> Xóa
							</button>
						@endif
					@endif
				</td>
			</tr>
		@endforeach
    </tbody>
</table>
