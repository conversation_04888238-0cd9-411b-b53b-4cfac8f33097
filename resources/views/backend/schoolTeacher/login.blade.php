<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta id="token" name="token" content="{{ csrf_token() }}"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="description" content="Trang quản trị">
        <meta name="author" content="dungmori.com">

        <title> Đăng nhập </title>
        <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('/assets/img/admin-fav.png') }}" />
        <link media="all" type="text/css" rel="stylesheet" href="{{ asset('/plugin/bootstrap/css/bootstrap.min.css') }}">
        <link media="all" type="text/css" rel="stylesheet" href="{{ asset('/plugin/bootstrap/css/style.css') }}">
        <link type="text/css" rel="stylesheet" href="{{asset('plugin/font-awesome/css/font-awesome.min.css')}}">
        <script src="{{ asset('/plugin/jquery/jquery.min.js') }}"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.js"></script>
        <style type="text/css">
            .row{font-family: Myriad Pro,sans-serif; font-weight: 500;}
            .btn{padding: 10px 6px 8px 6px; border-radius: 3px; margin: 0 0 0 3px; font-size: 12px;}
            .label {padding: 4px 6px 4px 8px; margin-right: 4px; cursor: pointer; border-radius: 3px; }
            .label-success{background: #10a31a; }
            .label-danger, .btn-danger {background: #e74c3c; }
            .table{background-color: #fff;}
            .text-left{text-align: left;}
            .dropdown-toggle{height: 40px;}
            .login-box{border-radius: 4px;}
        </style>
    </head>

    <body>
        <div class="preloader">
        </div>
        <section id="wrapper" class="login-register" style="background: #EEE !important;">
            <div class="login-box">
                <div class="white-box">
                    <div id="loginform">
                        <h3 class="login-box-msg"><i class="fa  fa-heart"></i> Login</h3>
                        <form accept-charset="UTF-8" class="form-horizontal form-material" id="admin-login">
                            <input name="_token" type="hidden" value="{{ csrf_token() }}">
                            <label id="error" style="color: red;"></label>
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <input type="email"
                                        class="form-control"
                                        name="email"
                                        id="email"
                                        placeholder="Email"
                                        required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <input type="password"
                                        class="form-control"
                                        name="password"
                                        id="password"
                                        placeholder="Mật khẩu"
                                        required/>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="checkbox checkbox-primary">
                                        <input type="checkbox" name="remember" id="remember">
                                        <label for="checkbox-signup"> Nhớ mật khẩu </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group text-center">
                                <div class="col-xs-12">
                                    <button class="btn btn-info btn-lg btn-block text-uppercase login" type="button">Đăng nhập</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <script src="{{ asset('/plugin/jquery/jquery-ui.min.js') }}"></script>
        <script src="{{ asset('/plugin/jquery/bootstrap.min.js') }}"></script>
        <script src="{{ asset('/plugin/jquery/sidebar-nav.min.js') }}"></script>
        <script src="{{ asset('/plugin/clientjs/client.min.js') }}"></script>
        <script src="{{ asset('/plugin/jquery/jquery.slimscroll.js') }}"></script>
        <script src="{{ asset('/plugin/jquery/waves.js') }}"></script>
        <script src="{{ asset('/plugin/jquery/custom.min.js') }}"></script>
        <script src="https://www.google.com/recaptcha/api.js?render=6Lee7cYUAAAAAAJTcnpf3F54qeoZIAqG3H7G_wDK"></script>

        <script>
            $(document).ready(function(){
                var remember = $.cookie('remember');
                if(remember == 'true'){
                    var email = $.cookie('email');
                    var password = $.cookie('password');
                    $('#email').val(email);
                    $('#password').val(password);
                    $('#remember').prop('checked', true);
                }
                if ($.cookie('verifyDenied')) {
                    $("#error").text($.cookie('verifyDenied'));
                    $.removeCookie('verifyDenied');
                }
            });
            $(document).keypress(function(e) {
                if (e.which == 13) {
                    callBack();
                }
            });
            $('.login').click(function() {
                callBack();
            });
            function callBack(){
                var content = new FormData($("#admin-login")[0]);
                var client = new ClientJS();
                $checkEnv = checkEnv();
                if ($checkEnv) {
                    grecaptcha.ready(function() {
                        // google recaptcha v3
                        grecaptcha.execute('6Lee7cYUAAAAAAJTcnpf3F54qeoZIAqG3H7G_wDK', {action: 'login'}).then(function(token) {
                            // add token to form
                            content.append('token', token);
                            $.ajax({
                                type: 'post',
                                url: '/teacher/login',
                                processData: false,
                                contentType: false,
                                data : content,
                                success: function(data){
                                    if (data.status === 'error') {
                                        $("#error").text(data.msg);
                                    } else if (data.status === 'success') {
                                        document.location = data.redirect;
                                    }
                                },
                                error: function(jqXHR, exception) {
                                    $('#error').html('Mật khẩu, email hoặc captcha bị sai');
                                    $("#changeCaptcha").find("img").click();
                                }
                            });
                        });
                    });
                } else {

                    $.ajax({
                        type: 'post',
                        url: '/teacher/login',
                        processData: false,
                        contentType: false,
                        data : content,
                        success: function(data) {
                            if (data.status === 'error') {
                                $("#error").text(data.msg);
                            } else if (data.status === 'success') {
                                document.location = data.redirect;
                            }
                        },
                        error: function(jqXHR, exception) {
                            $('#error').html('Mật khẩu, email hoặc captcha bị sai');
                            $("#changeCaptcha").find("img").click();
                        }
                    });
                }
            }

            /**
             * Check ENV if local return false, if production return true
             * @returns {boolean}
             */
            function checkEnv() {
                var host = window.location.host;
                var regex = new RegExp('dungmori.com');
                var matches = host.match(regex);
                if (matches !== null) {
                    return true;
                } else {
                    return false;
                }
            }
        </script>
    </body>
</html>