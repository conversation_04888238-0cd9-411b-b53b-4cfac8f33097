@extends('backend._default.dashboard')

@section('description') Quản lý server @stop
@section('keywords') server @stop
@section('author') dungmori.com @stop
@section('title') Admin | server @stop

@section('assets')
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script> 
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script> 
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet"> 
@stop

@section('content') 
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Danh sách server videos
        </h4> 
    </div>
    <div class="table_server">
        <table class="table table-borderless" id="table_server">
    <thead>
        <tr>
            <th class="text-center"><PERSON><PERSON> số</th>
            <th class="text-center">Tên server</th>
            <th class="text-center">Link</th>
            <th class="text-center">Trạng thái</th>
        </tr>
    </thead>
    <tbody class="body_server">
        @foreach($server as $item)
            <tr class="item{{$item->id}}">
                <td class="text-center">{{$item->id}}</td>
                <td class="text-center">{{$item->name}}</td>
                <td class="text-center">{{$item->url}}</td>
                <td class="text-center">
                    @if($item->default == 1)
                        <span class="label label-success">Mặc định</span>
                    @endif

                </td>
            </tr>
        @endforeach
    </tbody>
</table>

    </div>
@stop