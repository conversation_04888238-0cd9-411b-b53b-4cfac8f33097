
<table>
    <thead>
    <tr>
        <td>Mã nhóm</td>
        <td>Tên nhóm</td>
        <td>Tên gi<PERSON>o viên</td>
        <td><PERSON><PERSON><PERSON> độ</td>
        <td>Loại</td>
        <td><PERSON><PERSON><PERSON> khai giảng</td>
        <td><PERSON><PERSON><PERSON> kết thúc</td>
        <td><PERSON><PERSON><PERSON> hết hạn</td>
        <td>Trạng thái</td>
        <td>Số học viên trong nhóm</td>
        <td>Tổng số buổi học</td>
        <td>Số buổi học trong tháng {{ $currentMonth . '/' . $currentYear }}</td>
    </tr>
    </thead>
    <tbody>
    @foreach ($data as $group)
        <tr>
            <td>{{ $group->id }}</td>
            <td>{{ $group->name }}</td>
            <td>{{ isset($group->teacher->teacher->user_name) ? $group->group_teacher->teacher->user_name : '' }}</td>
            <td>{{ $group->vip_level }}</td>
            <td>{{ $group->type }}</td>
            <td>{{ $group->start_date }}</td>
            <td>{{ $group->end_at }}</td>
            <td>{{ $group->expired_at }}</td>
            <td>{{ $group->status == 1 ? 'Active' : 'UnActive' }}</td>
            <td>{{ $group->users->count() }}</td>
            <td>{{ $group->vip_session }}</td>
            <td>
                {{
                    $group->course_time->filter(function ($q) use ($currentMonth, $currentYear) {
                        return \Carbon\Carbon::parse($q->date_attendance)->month == $currentMonth && \Carbon\Carbon::parse($q->date_attendance)->year == $currentYear;
                    })->count()
                }}
            </td>
        </tr>
    @endforeach
    </tbody>
</table>
