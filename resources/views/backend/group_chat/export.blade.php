
<table>
  <thead>
  <tr>
    <td>ID</td>
    <td>Họ và tên</td>
    <td>Email</td>
    <td>Facebook</td>
    <td>Link chăm sóc</td>
    <td>Sale TV</td>
    <td>SĐT</td>
    <td><PERSON><PERSON><PERSON> sinh</td>
    <td>Giới tính</td>
    <td>Địa chỉ</td>
    <td>Trình độ</td>
    <td>Ghi chú</td>
    <td>Ghi chú CĐ</td>
    <td>Thiết bị</td>
    <td>Nợ học phí</td>
  </tr>
  </thead>
  <tbody>
  @foreach ($conversations as $conversation)
    <tr>
      <td>{{ $conversation['title'] ?? '' }}</td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
    @foreach ($conversation['group_chat_users'] as $user)
      <tr>
        <td>{{ $user['id'] ?? '' }}</td>
        <td>{{ $user['group_user'][0]['invoice']['info_contact']['name'] ?? $user['name'] }}</td>
        <td>{{ $user['group_user'][0]['invoice']['info_contact']['email'] ?? $user['email'] }}</td>
        <td>
          @if (isset($user['group_user'][0]['invoice']['info_contact']['facebook']))
            <a href="{{ $user['group_user'][0]['invoice']['info_contact']['facebook'] ? $user['group_user'][0]['invoice']['info_contact']['facebook'] : '#' }}">{{ $user['group_user'][0]['invoice']['info_contact']['facebook'] ?? ''}}</a>
          @endif
        </td>
        <td>
          @if (isset($user['group_user'][0]['invoice']['info_contact']['chat']))
            <a href="{{ $user['group_user'][0]['invoice']['info_contact']['chat'] ? $user['group_user'][0]['invoice']['info_contact']['chat'] : '#'}}">{{ $user['group_user'][0]['invoice']['info_contact']['chat'] ?? ''}}</a>
          @endif
        </td>
        <td>{{ $user['group_user'][0]['invoice']['sale']['name'] ?? ''}}</td>
        <td>{{ $user['group_user'][0]['invoice']['info_contact']['phone'] ?? $user['phone'] }}</td>
        <td>{{ $user['birth'] ?? '' }}</td>
        <td>{{ $user['gender'] ?? '' }}</td>
        <td>{{ $user['address'] ?? '' }}</td>
        <td>{{ $user['japanese_level'] ?? '' }}</td>
        <td>{{ $user['group_user'][0]['invoice']['note'] ?? ''}}</td>
        <td>{{ $user['group_user']['note'] ?? ''}}</td>
        <td>{{ $user['last_os'] ?? '' }}</td>
        <td>
          @if($user['group_user'][0]['invoice'] != null && $user['group_user'][0]['invoice']['children'] == null)
            {{ $user['group_user'][0]['invoice']['invoice_status'] != 'canceled' && ($user['group_user'][0]['invoice']['payment_status'] != 'paid' || $user['group_user'][0]['invoice']['price'] - $user['group_user'][0]['invoice']['discount_money'] - $user['group_user'][0]['invoice']['paid_money'] > 0) ? 'Đang nợ' : '' }}
          @elseif($user['group_user'][0]['invoice'] != null && $user['group_user'][0]['invoice']['children'] != null)
            {{ $user['group_user'][0]['invoice']['children'][0]['invoice_status'] != 'canceled' && ($user['group_user'][0]['invoice']['children'][0]['payment_status'] != 'paid' || $user['group_user'][0]['invoice']['children'][0]['price'] - $user['group_user'][0]['invoice']['children'][0]['discount_money'] - $user['group_user'][0]['invoice']['children'][0]['paid_money'] > 0) ? 'Đang nợ' : '' }}
          @endif
        </td>
      </tr>
    @endforeach
  @endforeach
  </tbody>
</table>
