<!DOCTYPE html>
<html lang="en">

<head>
    <!--Header-->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="@yield('description')">
    <meta name="keywords" content="@yield('keywords')" />
    <meta name="author" content="@yield('author')">
    <title>@yield('title')</title>

    <script>
        window.Laravel = <?php echo json_encode([
            'csrfToken' => csrf_token(),
        ]); ?>
    </script>

    <!--icon-->
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('/assets/img/admin-fav.png') }}" />
    <!--css-->
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('/plugin/bootstrap/css/bootstrap.min.css') }}">
    <link rel="preload" href="{{ asset('css/base.css') }}?{{ filemtime('css/base.css') }}" as="style"
        onload="this.onload=null;this.rel='stylesheet'" media="screen">

    <link media="all" type="text/css" rel="stylesheet"
        href="{{ asset('/plugin/bootstrap/css/sidebar-nav.min.css') }}">

    <link media="all" type="text/css" rel="stylesheet"
        href="{{ asset('/assets/backend/css/dashboard.css') }}?{{ filemtime('assets/backend/css/dashboard.css') }}">

    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('/plugin/bootstrap/css/animate.css') }}">

    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('/plugin/bootstrap/css/style.css') }}">

    <link id="theme" media="all" type="text/css" rel="stylesheet" href="{{ asset('/css/colors/blue.css') }}">

    <!-- js -->
    <script src="{{ asset('/plugin/jquery/jquery.min.js') }}"></script>
    <script src="{{ asset('/plugin/toastr/toastr.min.js') }}"></script>

    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>

    <script src="{{ asset('/assets/backend/js/dashboard.js') }}"></script>

    <link type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/image_manager.css') }}">

    <link type="text/css" rel="stylesheet" href="{{ asset('plugin/font-awesome/css/font-awesome.min.css') }}">

    <link type="text/css" rel="stylesheet" href="{{ asset('plugin/simple-line-icons/css/simple-line-icons.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap" rel="stylesheet">
    <link type="text/css" rel="stylesheet" href="{{ asset('plugin/themify-icons/themify-icons.css') }}">
    <link type="text/css" rel="stylesheet" href="{{ asset('plugin/font-awesome/css/font-awesome.min.css') }}">
    <link type="text/css" rel="stylesheet" href="{{ asset('plugin/linea-icons/linea.css') }}">

    <style>
        #page-wrapper {
            background: #f6f6f6 !important;
        }

        .new-label {
            padding: 6px 8px;
            margin-right: 4px;
            cursor: pointer;
            border-radius: 3px;
            transition: 0.075s ease-in-out
        }

        .new-label:hover {
            filter: saturate(1.2);
            box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5)
        }

        .green-label {
            background: #10a31a;
        }

        .red-label {
            background: #e74c3c;
        }

        .orange-label {
            background: #ff8a23;
        }

        .blue-label {
            background: #3392ff;
        }

        .refresh-cache {
            position: fixed;
            z-index: 999;
            cursor: pointer;
            right: -3px;
            top: 42px;
            background: #fff;
            border-radius: 4px;
            padding: 2px 10px;
            box-shadow: 0 3px 5px 0 rgba(52, 52, 52, 0.1);
        }

        .navbar-top-links>li>a {
            line-height: 30px;
            min-height: 30px;
        }

        .navbar {
            min-height: 30px;
        }

        .bg-title {
            margin-bottom: 0;
        }
    </style>

    <script src="{{ asset('plugin/vue/vue.min.js') }}"></script>
    <script src="{{ asset('/assets/backend/js/modal.js') }}?{{ filemtime('assets/backend/js/modal.js') }}"></script>
    <script src="{{ asset('plugin/lazyload/lazyload.min.js') }}"></script>

    <script>
        const videoBaseURL = "{{ env('RENDER_APP_URL') }}";
        const videoServerURL = "{{ env('VIDEO_SERVER_URL') }}";
    </script>

    @yield('assets')

</head>

<!--BODY-->

<body>
    @if (Auth::guard('admin')->check())
        @yield('dashboard')
    @else
        <script type="text/javascript">
            $(document).ready(function() {
                // Handler for .ready() called.
                window.setTimeout(function() {
                    location.href = "{!! url('/backend/login') !!}";
                });
            });
        </script>
    @endif

    <script src="{{ asset('/plugin/jquery/jquery-ui.min.js') }}"></script>

    <script src="{{ asset('/plugin/jquery/bootstrap.min.js') }}"></script>

    <script src="{{ asset('/plugin/jquery/sidebar-nav.min.js') }}"></script>

    <script src="{{ asset('/plugin/jquery/jquery.slimscroll.js') }}"></script>

    <script src="{{ asset('/plugin/jquery/waves.js') }}"></script>

    <script src="{{ asset('/plugin/jquery/custom.min.js') }}"></script>
    @yield('foot-js')

</body>

</html>
