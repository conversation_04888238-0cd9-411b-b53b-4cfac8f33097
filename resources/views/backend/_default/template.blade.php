<script type="text/x-template" id="modal-template">
    <transition name="v-modal">
        <div class="v-modal-mask">
            <div class="v-modal-wrapper" @mousedown="$emit('close')">
                <div class="v-modal-container" @mousedown.stop :style="containerStyle">
                    <div class="v-modal-header">
                        <slot name="header">

                        </slot>
                    </div>

                    <div class="v-modal-body">
                        <slot name="body">

                        </slot>
                    </div>

{{--                    <div class="v-modal-footer">--}}
{{--                        <slot name="footer">--}}
{{--                            default footer--}}
{{--                            <button class="v-modal-default-button" @click="$emit('close')">--}}
{{--                                OK--}}
{{--                            </button>--}}
{{--                        </slot>--}}
{{--                    </div>--}}
                </div>
            </div>
        </div>
    </transition>
</script>
{{-- template của test tổng hợp --}}
<script type="text/x-template" id="lesson-info-template">
    <div class="lesson-detail__info">
        <form id="lesson-info-form">
            <div class="grid-1-3">
                <label class="text-right">Bài học :</label>
                <input type="text" class="form-control" v-model="name"/>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Ảnh bài học :</label>
                <div style="display: grid; grid-template-columns: 4fr 1fr; align-items: center; grid-gap: 20px">
                    <input type="file" name="img_lesson" id="lesson-avatar-input" class="form-control" @change="readURL($event)"/>
                    <img :src="!avatarPreview ? (avatar_name == '' ? '{{asset('assets/img/icon_backend/no_image.png')}}' : '{{url('cdn/lesson/small')}}/' + avatar_name) : avatarPreview" width="100%"/>
                </div>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Khoá học :</label>
                <select type="text" class="form-control" v-model="course">
                    <option v-for="course in courses" :value="course.id">@{{ course.name }}</option>
                </select>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Nhóm bài học :</label>
                <select type="text" class="form-control" v-model="group_id">
                    <option v-for="group in lessonGroups" :value="group.id">@{{ group.name }}</option>
                </select>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Tính phí :</label>
                <select type="text" class="form-control" v-model="price_option">
                    <option value="0">Miễn phí</option>
                    <option value="2">Tính phí</option>
                </select>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Nổi bật :</label>
                <select type="text" class="form-control" v-model="feature">
                    <option value="0">Ẩn</option>
                    <option value="1">Nổi bật</option>
                </select>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Giáo viên :</label>
                <select type="text" class="form-control" v-model="author">
                    <option v-for="author in authors" :value="author.id">@{{ author.name }}</option>
                </select>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Trạng thái :</label>
                <div>
                    <label>
                        <input type="radio" value="0" name="show" v-model="show"/>
                        Tắt
                    </label>
                    <label>
                        <input type="radio" value="1" name="show" v-model="show"/>
                        Bật
                    </label>
                    <label>
                        <input type="radio" value="2" name="show" v-model="show"/>
                        Testing
                    </label>
                </div>
            </div>
            <div class="text-center">
                <span class="btn btn-info btn-block" @click="submit">Lưu lại</span>
            </div>
        </form>
    </div>
</script>
<script type="text/x-template" id="lesson-tasks-template">
    <div class="lesson-detail__tasks">
        <div class="lesson-detail__tasks-form">
            <div class="grid-1-3">
                <label class="text-right">Làm bài kiểm tra :</label>
                <label class="checkbox__switch">
                    <input type="checkbox" v-model="is_examination"/>
                    <span class="checkbox__slider checkbox__round"></span>
                </label>
            </div>
            <div class="grid-1-3" v-if="is_examination">
                <label class="text-right">Điểm tối đa của cả bài :</label>
                <input type="text" class="form-control" v-model="total_marks"/>
            </div>
            <div class="grid-1-3" v-if="is_examination">
                <label class="text-right">Điểm đạt :</label>
                <input type="text" class="form-control" v-model="pass_marks"/>
            </div>
            <div class="grid-1-3">
                <label class="text-right">Tác vụ :</label>
                <div style="display: grid; grid-template-columns: 5fr 1fr; grid-gap: 20px">
                    <select v-model="type" class="form-control">
                        <option value="1">Nội dung</option>
                        <option value="10">Quiz trắc nghiệm</option>
                        <option value="11">Sắp xếp câu</option>
                        <option value="12">Video tương tác</option>
                    </select>
                    <span class="btn btn-info" @click="showModal = true"><i class="fa fa-plus"></i> Thêm tác vụ</span>
                </div>
            </div>
            <div class="lesson-detail__tasks-table" id="lesson-detail__tasks-table">
                <table class="table bg-white">
                    <thead>
                        <tr>
                            <th width="4%"></th>
                            <th width="3%" class="text-center">/</th>
                            <th width="2%">ID</th>
                            <th width="10%">Loại</th>
                            <th>Nội dung</th>
                            <th width="5%" class="text-center">Điểm (<span class="text-bold" :class="{'text-danger': gradeByTask !== lesson.total_marks }">@{{ gradeByTask }})</span></th>
                            <th width="10%" class="text-center">Trạng thái</th>
                            <th width="130" class="text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <draggable tag="tbody" @end="onDragEnd" v-model="tasks" @start="draggable=true" @end="draggable=false" :handle="'.hamburger'">
                        <tr v-for="task in tasks" :key="task.id + '_' + task.sort" >
                            <td class="hamburger text-center"><i class="fa fa-bars fa-lg"></i></td>
                            <td scope="row" class="text-center">@{{ task.sort }}</td>
                            <td>@{{ task.id }}</td>
                            <td>
                                @{{ task.type | taskType}}
                            </td>
                            <td >
                                <span v-if="task.type != 4" v-html="task.value"></span>
                                <span v-else></span>
                            </td>
                            <td class="text-center"><div v-html="renderPoint(task)"></div></td>
                            <td class="text-center">
                                <span class="lesson-detail__tasks-chip" v-bind:style="{'background': getChipColor(task.show)}">@{{ task.show | show}}</span>
                            </td>
                            <td>
                                <div>
                                    <span class="btn btn-sm btn-info" @click="editTask(task)"><i class="fa fa-edit"></i></span>
                                    <span v-if="task.type === 12" class="btn btn-sm btn-success"><a :href="url + '/backend/new-lesson/' + detailLesson.id + '/edit/components/' + task.id"><i class="fa fa-film"></i></a></span>
                                    <span class="btn btn-sm btn-danger" @click="deleteTask(task.id)"><i class="fa fa-trash"></i></span>
                                </div>
                            </td>
                        </tr>
                    </draggable>
                </table>
            </div>
            <backend-modal v-if="showModal" @close="closeModal">
                <h3 slot="header">@{{ currentTask.id ? 'Sửa task' : 'Thêm task' }}</h3>
                <div slot="body">
                    <task-content-form v-if="type == 1" :currentTask="currentTask" :lesson_id="lesson.id" :type="type" @addedTask="pushAddedTask" @updatedTask="updateTask" @closeModal="closeModal"></task-content-form>
                    <task-quiz-form v-if="type == 10" :currentTask="currentTask" :lesson_id="lesson.id" :type="type" @addedTask="pushAddedTask" @updatedTask="updateTask" @closeModal="closeModal"></task-quiz-form>
                    <task-gap-fill-form v-if="type == 11" :currentTask="currentTask" :lesson_id="lesson.id" :type="type" @addedTask="pushAddedTask" @updatedTask="updateTask" @closeModal="closeModal"></task-gap-fill-form>
                    <task-interactive-video-form v-if="type == 12" :currentTask="currentTask" :lesson_id="lesson.id" :type="type" @addedTask="pushAddedTask" @updatedTask="updateTask" @closeModal="closeModal"></task-interactive-video-form>
                </div>
            </backend-modal>
            <div style="text-align: center">
                <span class="btn btn-success" @click="save">Thực hiện</span>
                <span class="btn btn-danger" @click="cancel">Quay lại danh sách</span>
            </div>
        </div>
    </div>
</script>

<!-- Khu vực các template form thêm mới/sửa tác vụ (tasks) trong modal-->
<script type="text/x-template" id="task-content-form-template">
    <form>
        <ckeditor v-model="value" :config="editorConfig" style="margin-bottom: 10px"></ckeditor>
        <div style="display: flex; flex-flow: row; align-items: center">
            <label>Trạng thái</label>
            <label class="checkbox__switch" style="margin-left: 10px">
                <input type="checkbox" v-model="show"/>
                <span class="checkbox__slider checkbox__round"></span>
            </label>
        </div>
        <div style="display: flex; flex-flow: row; align-items: center">
            <label>Là bài quiz  </label>
            <label class="checkbox__switch" style="margin-left: 10px">
                <input type="checkbox" v-model="is_quiz"/>
                <span class="checkbox__slider checkbox__round"></span>
            </label>
        </div>
        <div class="text-center">
            <span class="btn btn-success" @click="onSaveTask">@{{ task.id ? 'Lưu' : 'Thêm' }}</span>
            <span class="btn btn-danger" @click="cancel">Huỷ</span>
        </div>
    </form>
</script>
<script type="text/x-template" id="task-quiz-form-template">
    <form>
        <ckeditor v-model="value" :config="editorConfig" class="mb-5"></ckeditor>
        <div class="flex flex-row items-center justify-start mb-5">
            <input type="number" placeholder="Điểm số câu hỏi" class="form-control mr-5" style="width: 200px" v-model="grade"/>
            <span class="btn btn-info p-3" @click="addAnswers"><i class="fa fa-plus"></i> Thêm đáp án</span>
        </div>
        <div class="lesson-detail__tasks-answers mb-5">
            <div class="lesson-detail__tasks-answer" v-for="answer in answers">
                <input type="radio" name="answer" v-model="checked" :value="answer.sort" @change="checkedAnswer"/>
                <input type="text" class="form-control" v-model="answer.value" :placeholder="'Nội dung đáp án ' + answer.sort"/>
                <span class="text-danger ml-2 a-cursor-pointer" @click="removeRow(answer)"><i class="fa fa-minus-circle fa-lg"></i></span>
            </div>
        </div>
        <div class="flex flex-row items-center justify-between">
            <div class="flex flex-row items-center">
                <label>Trạng thái</label>
                <label class="checkbox__switch" style="margin-left: 10px">
                    <input type="checkbox" v-model="show"/>
                    <span class="checkbox__slider checkbox__round"></span>
                </label>
            </div>
        </div>
        <div>

        </div>
        <div class="text-center">
            <span class="btn btn-success" @click="onSaveTask">@{{ task.id ? 'Lưu' : 'Thêm' }}</span>
            <span class="btn btn-danger" @click="cancel">Huỷ</span>
        </div>
    </for
</script>
<script type="text/x-template" id="task-gap-fill-form-template">
    <form>
        <ckeditor v-model="value" :config="editorConfig" class="mb-5"></ckeditor>
        <div class="flex flex-row items-center justify-start mb-5">
            <input type="number" placeholder="Điểm số câu hỏi" class="form-control mr-5" style="width: 200px" v-model="grade"/>
        </div>
        <div class="lesson-detail__tasks-answers mb-5">
            <div class="lesson-detail__tasks-answer" v-for="(answer, index) in answers">
                <span class="mr-5 text-bold">@{{ index + 1 }}.</span>
                <span v-html="answer.value" class="form-control flex items-center"></span>
            </div>
        </div>
        <div class="flex flex-row items-center justify-between">
            <div class="flex flex-row items-center">
                <label>Trạng thái</label>
                <label class="checkbox__switch" style="margin-left: 10px">
                    <input type="checkbox" v-model="show"/>
                    <span class="checkbox__slider checkbox__round"></span>
                </label>
            </div>
        </div>
        <div>

        </div>
        <div class="text-center">
            <span class="btn btn-success" @click="onSaveTask">@{{ task.id ? 'Lưu' : 'Thêm' }}</span>
            <span class="btn btn-danger" @click="cancel">Huỷ</span>
        </div>
    </form>
</script>
<script type="text/x-template" id="task-interactive-video-form-template">
    <div>
        <div class="form-group">
            <label>Tên file video</label>
            <input v-model="video_name" class="form-control" placeholder="Ví dụ: n4-bai15-tuvung.mp4"/>
        </div>
        <div class="form-group">
            <label>Tiêu đề video</label>
            <input v-model="video_title" class="form-control" placeholder="Ví dụ: Từ vựng"/>
        </div>
        <div class="flex flex-row items-center justify-between">
            <div class="flex flex-row items-center">
                <label>Trạng thái</label>
                <label class="checkbox__switch" style="margin-left: 10px">
                    <input type="checkbox" v-model="show"/>
                    <span class="checkbox__slider checkbox__round"></span>
                </label>
            </div>
        </div>

        <div class="text-center">
            <span class="btn btn-success" @click="onSaveTask">@{{ task.id ? 'Lưu' : 'Thêm' }}</span>
            <span class="btn btn-danger" @click="cancel">Huỷ</span>
        </div>
    </div>
</script>
{{-- template của modal video --}}
<script type="text/x-template" id="video-modal-template">
    <div class="video__js--overlay-quiz-wrapper">
        <div class="video__js--overlay-quiz-timer" v-if="question.type === 1">
            <pie-timer :timeStart="modalQuestion.time_start" :length="modalQuestion.length" :aLength="modalQuestion.a_length" :currentTime='currentTime' v-on:timeUp="timeUp()" v-if="modalQuestion.modal && !question.time_up"></pie-timer>
        </div>
        <div class="video__js--overlay-quiz-content">
            <div class="video__js--overlay-quiz-qa">
                <div v-if="modalQuestion.content" class="video__js--overlay-question" v-html="modalQuestion.content"></div>
                <div class="video__js--overlay-answers"
                     :class="{
                     'h-100': question.type === 1 && modalQuestion.answers.length === 4,
                     'h-50': question.type === 1 && modalQuestion.answers.length < 4,
                     'grid-3-1': question.type === 1 && modalQuestion.answers.length % 3 == 0,
                     'grid-2-1': question.type === 1 && modalQuestion.answers.length % 2 == 0,
                     'grid-1-1': question.type === 2 && modalQuestion.longgg
                     }">
                    <div class="video__js--overlay-answer" v-for="(answer, index) in modalQuestion.answers" :key="answer.key" @click="selectAnswer(answer, modalQuestion)"
                         :class="{'video__js--overlay-answer-blink' : modalQuestion.time_up && answer.grade}"
                         :style="{background: !modalQuestion.time_up ? (answer.selected ? '#F3933D' : '#7ECADE') : (answer.grade ? '#93CA8C' : (answer.selected ? '#F3933D' : '#7ECADE'))}">
                        <div class="flex justify-center items-center">
                            <span class="flex justify-center items-center" style="font-size: 1em; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; width: 1.3em; height: 1.3em; border-radius: 50%; background: rgba(0,0,0,0.3)">@{{ index + 1 }}</span>
                        </div>
                        <span style="text-align: justify; padding: 5px"> @{{answer.value}}</span>
    {{--                    <span>--}}
    {{--                        <i v-if="modalQuestion.time_up && !answer.grade && answer.selected" class="fa fa-lg fa-times-circle" style="background: #FFF; line-height: 16px; border-radius: 50%; color: rgba(255, 0, 0, 0.7)"></i>--}}
    {{--                        <i v-if="!modalQuestion.time_up && answer.selected" class="fa fa-circle fa-lg" style="color: rgba(21, 255, 59, 0.6)"></i>--}}
    {{--                        <i v-if="(modalQuestion.time_up && !answer.selected & !answer.grade) || (!modalQuestion.time_up && !answer.selected)" class="fa fa-circle-o fa-lg"></i>--}}
    {{--                        <i v-if="modalQuestion.time_up && answer.grade " class="fa fa-check fa-lg" style="color: #FFF"></i>--}}
    {{--                    </span>--}}
                    </div>
                </div>
            </div>
            <div class="video__js--overlay-navigation" v-if="modalQuestion.type === 2">
                <span class="video__js--overlay-navigation-button" @click="triggerCloseModal">@{{ question.time_up ? 'Tiếp tục' : 'Bỏ qua' }}</span>
            </div>
        </div>
    </div>
</script>

<script type="text/x-template" id="pie-timer-template">
    <div id="countdown">
        <div id="countdown-number">@{{ timeCount | floorTime}}</div>
        <svg>
            <circle :style="{animationDuration: remainingLength + 's'}" r="18" cx="20" cy="20"></circle>
        </svg>
    </div>
</script>

<script type="text/x-template" id="lesson-categories-panel-template">
    <div class="lesson-categories__panel course-stages__screen--panel">
        <div v-if="!loading">
            <div class="lesson-categories__panel--header">
                <span v-if="stage !== 0 && permission" class="btn btn-sm btn-success" @click="showModal = true">Thêm</span>
                <span v-if="stage !== 0" class="text-bold">Chặng @{{ stage }}</span>
            </div>
            <div class="lesson-categories__panel--list">
                <div class="lesson-categories__panel--item" @click="selectUncategorized" :class="{'lesson-categories__panel--item-selected': selectedCategory.id === 0}">
                    <div class="lesson-categories__panel--item-info">
                        <div class="flex justify-center items-center">

                        </div>
                        <div class="flex flex-column ml-5">
                            <span>Chưa được phân loại</span>
                            {{--                            <span>@{{ category.type === 1 ? 'Thường' : 'Đặc biệt'}}</span>--}}
                        </div>
                    </div>
                    <div class="lesson-categories__panel--item-action">
                    </div>
                </div>
                <draggable tag="lesson-categories__panel--real-items" @end="onDragEnd" v-model="categories" @start="draggable=true" @end="draggable=false" :handle="'.lesson-categories__panel--item-icon'">
                    <div class="lesson-categories__panel--item" v-for="category in categories" :class="{'lesson-categories__panel--item-selected': selectedCategory.id === category.id}" @click="selectCategory(category)">
                        <div class="lesson-categories__panel--item-info flex justify-start items-center">
                            <div class="lesson-categories__panel--item-icon flex justify-center items-center" style="min-height: 30px; min-width: 40px">
                                <img :src="'{{asset("cdn/lesson_category/")}}/' + category.icon" />
                            </div>
                            <div class="flex flex-column ml-5">
                                <span>@{{ category.title }}
                                    <i v-if="category.status === 2 && permission" @click="changeStatus(category, 1)" class="fa fa-flask text-info"></i>
                                    <i v-if="category.status === 1 && permission" @click="changeStatus(category, 0)" class="fa fa-eye text-success"></i>
                                    <i v-if="category.status === 0 && permission" @click="changeStatus(category, 2)" class="fa fa-eye-slash text-dark"></i>
                                </span>
    {{--                            <span>@{{ category.type === 1 ? 'Thường' : 'Đặc biệt'}}</span>--}}
                            </div>
                        </div>
                        <div class="lesson-categories__panel--item-action">
                            <span v-if="permission" @click="editCategory(category)" class="btn btn-sm btn-warning mb-2 text-dark" title="Sửa"><i class="fa fa-edit"></i></span>
                            <span v-if="permission" @click="deleteCategory(category)" class="btn btn-sm btn-danger mb-2" title="Xoá"><i class="fa fa-trash"></i></span>
                            {{--                    <span class="btn btn-sm btn-info mb-2" title="Danh sách "><i class="fa fa-list"></i></span>--}}
                        </div>
                    </div>
                </draggable>
            </div>
        </div>
        <backend-modal v-if="showModal" @close="closeModal">
            <h3 slot="header">@{{ formData.id ? 'Sửa danh mục' : 'Thêm danh mục' }}</h3>
            <div slot="body">
                <div class="form-group">
                    <label>Tiêu đề</label>
                    <input class="form-control" v-model="formData.title"/>
                </div>
                <div class="form-group">
                    <label>Loại danh mục</label>
                    <select class="form-control" v-model="formData.type">
                        <option value="1">Thường</option>
                        <option value="2">Đặc biệt</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Icon hoặc ảnh</label>
                    <div class="flex justify-between items-center">
                        <input type="file" class="form-control mr-5"  @change="changeFormIcon"/>
                        <img :src="'{{asset("cdn/lesson_category/")}}/' + formData.icon" width="200"/>
                    </div>
                </div>
                <div class="form-group">
                    <label>Thuộc khoá học</label>
                    <select class="form-control" v-model="formData.course_id" disabled>
                        <option :value="course.id">@{{ course.name }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Thuộc chặng</label>
                    <select class="form-control" v-model="formData.stage">
                        <option v-for="st in stages" :value="st">Chặng @{{ st }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trạng thái</label>
                    <div>
                        <label><input :checked="formData.status === 0" type="radio" value="0" name="category_status" @change="setFormStatus"/>Tắt</label>
                        <label><input :checked="formData.status === 1" type="radio" value="1" name="category_status" @change="setFormStatus"/>Bật</label>
                        <label><input :checked="formData.status === 2" type="radio" value="2" name="category_status" @change="setFormStatus"/>Testing</label>
                    </div>
                </div>
                <div class="flex justify-start">
                    <span class="btn btn-lg btn-success" @click="saveForm">@{{ formData.id ? 'Lưu' : 'Thêm' }}</span>
                </div>
            </div>
        </backend-modal>
        <div class="course-stages__screen--panel-loading" v-if="loading">
            <div class="fa-3x">
                <i class="fa fa-spinner fa-spin"></i>
            </div>
        </div>
    </div>
</script>

<script type="text/x-template" id="lesson-groups-panel-template">
    <div class="lesson-groups__panel course-stages__screen--panel">
        <div v-if="!loading">
            <div class="lesson-groups__panel--header" >
                <span v-if="typeof category.id !== 'undefined' && permission" class="btn btn-sm btn-success" @click="showModal = true">Thêm</span>
                <span class="text-bold">@{{ category.title }}</span>
            </div>
            <div class="lesson-groups__panel--list">
                <draggable tag="lesson-groups__panel--real-items" @end="onDragEnd" v-model="groups" @start="draggable=true" @end="draggable=false" :handle="'.sorting-controller'">
                    <div class="lesson-groups__panel--item" v-for="group in groups">
                        <div class="lesson-groups__panel--item-info">
                            <span class="sorting-controller mr-5"><i class="fa fa-bars"></i></span>
                            <span><span style="font-size: 12px; padding: 2px 5px; background: rgba(255,21,0,0.99); color: #fff; font-weight: bold; border-radius: 5px">@{{ group.id }}</span> @{{ group.name }}
                                <i v-if="group.show === 2 && permission" @click="changeStatus(group, 1)" class="fa fa-flask text-info"></i>
                                <i v-if="group.show === 1 && permission" @click="changeStatus(group, 0)" class="fa fa-eye text-success"></i>
                                <i v-if="group.show === 0 && permission" @click="changeStatus(group, 2)" class="fa fa-eye-slash text-dark"></i>
                            </span>
                        </div>
                        <div class="lesson-groups__panel--item-action">
                            <span @click="showLessons(group)" class="btn btn-sm btn-info mb-2" title="Danh sách bài học"><i class="fa fa-bars"></i></span>
                            <span v-if="permission" @click="editGroup(group)" class="btn btn-sm btn-warning mb-2 text-dark" title="Sửa"><i class="fa fa-edit"></i></span>
                            <span v-if="permission" @click="deleteGroup(group)" class="btn btn-sm btn-danger mb-2" title="Xoá"><i class="fa fa-trash"></i></span>
                            {{--                    <span class="btn btn-sm btn-info mb-2" title="Danh sách "><i class="fa fa-list"></i></span>--}}
                        </div>
                    </div>
                </draggable>
            </div>
        </div>
        <div class="course-stages__screen--panel-loading" v-if="loading">
            <div class="fa-3x">
                <i class="fa fa-spinner fa-spin"></i>
            </div>
        </div>
        <backend-modal v-if="showLessonsModal" @close="closeLessonsModal">
            <h3 slot="header">Danh sách bài học</h3>
            <div slot="body">
                <div>
                    <div>
                        <input v-model="groupIdToChange" type="text" placeholder="Nhập mã nhóm bài học" style="padding: 0 10px"/>
                        <span
                            class="btn btn-danger"
                            :class="{'disabled': !groupIdToChange || choseLessons.length === 0}"
                            @click="changeGroupId"
                            style="border: none; "
                            :style="{'background': groupIdToChange && choseLessons.length > 0 ? 'red' : '#ccc', 'cursor': groupIdToChange && choseLessons.length > 0 ? 'pointer' : 'not-allowed'}">Chuyển</span>
                        <select v-model="choseType">
                            <option value="docs">Tài liệu</option>
                            <option value="video" selected="">Video</option>
                            <option value="video_test">Video + Test</option>
                            <option value="test">Bài tập</option>
                            <option value="exam">Bài thi</option>
                            <option value="last_exam">Bài thi cuối kỳ</option>
                            <option value="flashcard">Flashcard</option>
                            <option value="checkpoint">Bài test đầu vào</option>
                            <option value="guide">Hướng dẫn học</option>
                            <option value="short_test">Bài test ngắn</option>
                            <option value="long_test">Bài test dài</option>
                        </select>
                        <span
                            class="btn btn-danger"
                            :class="{'disabled': !choseType || choseLessons.length === 0}"
                            @click="changeLessonType"
                            style="border: none; "
                            :style="{'background': choseType && choseLessons.length > 0 ? 'red' : '#ccc', 'cursor': choseType && choseLessons.length > 0 ? 'pointer' : 'not-allowed'}">Áp dụng</span>

                        <select v-model="choseRule">
                            <option value="1">Hoàn thành bài học</option>
                            <option value="2">Hoàn thành video dài</option>
                            <option value="3">Hoàn thành bài test</option>
                        </select>
                        <span
                            class="btn btn-danger"
                            :class="{'disabled': !choseRule || choseLessons.length === 0}"
                            @click="addLessonRule"
                            style="border: none; "
                            :style="{'background': choseRule && choseLessons.length > 0 ? 'red' : '#ccc', 'cursor': choseRule && choseLessons.length > 0 ? 'pointer' : 'not-allowed'}">Thêm</span>

                    </div>
                    <table class="table bg-white">
                        <thead>
                        <tr>
                            <th width="50"></th>
                            <th width="50" class="text-center"><input :checked="choseLessons.length === lessons.length" type="checkbox" @click="checkAllLesson($event)"/></th>
                            <th width="150">ID</th>
                            <th width="150">Nhóm bài học</th>
                            <th width="150">Mã khoá học</th>
                            <th>Tên bài học</th>
                            <th>Điểm thưởng</th>
                            <th style="width: 100px">Tốc độ phát</th>
                            <th>Trạng thái</th>
                            <th>Ẩn tiêu đề</th>
                        </tr>
                        </thead>
                        <draggable tag="tbody" @end="onDragLessonEnd" v-model="lessons" @start="draggable=true" @end="draggable=false" :handle="'.hamburger'">
                            <tr v-for="lesson in lessons" :key="lesson.id + '_' + lesson.sort_order" >
                                <td class="hamburger text-center"><i class="fa fa-bars fa-lg"></i></td>
                                <td class="text-center">
                                    <input type="checkbox" @click="checkOneLesson($event, lesson.id)" :checked="choseLessons.length > 0 && choseLessons.some((element) => element == lesson.id)"/>
                                </td>
                                <td><a target="_blank" :href="url + '/backend/lesson/' + lesson.id + '/edit'">@{{ lesson.id }}</a></td>
                                <td>@{{ lesson.group_id }}</td>
                                <td>@{{ lesson.course_id }}</td>
                                <td><img width="15px" :src="'{{asset('assets/img/premium')}}' + '/' + printLessonIcon(lesson.type)" class="mr-3"/>  @{{ lesson.name}}</td>
                                <td>
                                    <div v-for="(rule, idx) in lesson.experience_rules" class="text-sm text-red-500 flex items-center gap-2">
                                        <div @click="changeLessonRulePoint(rule)" class="cursor-pointer hover:text-red-600">@{{ rule.description }} - @{{ rule.pivot.point }}đ</div>
                                        <i class="fa fa-times cursor-pointer" @click="detachRule(lesson, rule.id, idx)"></i>
                                    </div>
{{--                                    <input class="form-control" :value="lesson.reward_point" type="number" @keyup.enter="updateLesson($event, lesson, 'reward_point')">--}}
                                </td>
                                <td>
                                    <select @change="changeLessonSpeed($event, lesson)" class="form-control" :value="lesson.default_speed">
                                        <option v-for="rate in playbackRate" :value="rate">@{{ rate }}</option>
                                    </select>
                                </td>
                                <td class="text-center" width="100px">
                                    <span>
                                        <i v-if="lesson.show === 2" @click="changeLessonStatus(lesson, 1)" class="fa fa-flask text-info"></i>
                                        <i v-if="lesson.show === 1" @click="changeLessonStatus(lesson, 0)" class="fa fa-eye text-success"></i>
                                        <i v-if="lesson.show === 0" @click="changeLessonStatus(lesson, 2)" class="fa fa-eye-slash text-dark"></i>
                                    </span>
                                </td>
                                <td class="text-center" width="100px">
                                    <span class="a-cursor-pointer" :class="{'text-danger': lesson.is_secret}" @click="toggleHideLessonTitle(lesson)"><i class="fa-lg fa fa-question"></i></span>
                                </td>
                            </tr>
                        </draggable>
                    </table>
                </div>
            </div>
        </backend-modal>
        <backend-modal v-if="showModal" @close="closeModal">
            <h3 slot="header">@{{ formData.id ? 'Sửa nhóm bài học' : 'Thêm nhóm bài học' }}</h3>
            <div slot="body">
                <div class="form-group" v-if="formData.id">
                    <label>ID</label>
                    <input class="form-control" :value="formData.id" disabled/>
                </div>
                <div class="form-group">
                    <label>Tiêu đề</label>
                    <input class="form-control" v-model="formData.name"/>
                </div>
                <div class="form-group">
                    <label>Mô tả</label>
                    <textarea class="form-control" rows="3" v-model="formData.description"/>
                </div>
                <div class="form-group">
                    <label>Loại</label>
                    <select class="form-control" v-model="formData.skill">
                        <option value="">-Loại-</option>
                        <option value="tuvung">Từ vựng</option>
                        <option value="chuhan">Chữ hán</option>
                        <option value="nguphap">Ngữ pháp</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Số lượng thành phần</label>
                    <input class="form-control" type="number" v-model="formData.component_count">
                </div>
{{--                <div class="form-group">--}}
{{--                    <label>Ẩn tiêu đề</label>--}}
{{--                    <div>--}}
{{--                        <label><input :checked="formData.is_secret === 1" type="checkbox" value="0" @change="onChangeCheckbox($event, 'is_secret')"/> @{{ formData.is_secret ? 'Bật' : 'Tắt' }}</label>--}}
{{--                    </div>--}}
{{--                </div>--}}
                <div class="form-group">
                    <label>Thuộc danh mục</label>
                    <select class="form-control" v-model="formData.lesson_category_id">
                        <option value="0">Chưa được phân loại</option>
                        <option :value="category.id" v-for="category in categories">Chặng @{{ category.stage }} - @{{ category.title }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trạng thái</label>
                    <div>
                        <label><input :checked="formData.show === 0" type="radio" value="0" name="category_status" @change="setFormStatus"/>Tắt</label>
                        <label><input :checked="formData.show === 1" type="radio" value="1" name="category_status" @change="setFormStatus"/>Bật</label>
                        <label><input :checked="formData.show === 2" type="radio" value="2" name="category_status" @change="setFormStatus"/>Testing</label>
                    </div>
                </div>
                <div class="flex justify-start">
                    <span class="btn btn-lg btn-success" @click="saveForm">@{{ formData.id ? 'Lưu' : 'Thêm' }}</span>
                </div>
            </div>
        </backend-modal>
    </div>
</script>
<script type="text/x-template" id="lesson-paths-panel-template">
    <div class="lesson-categories__panel course-stages__screen--panel">
        <div v-if="!loading">
            <div class="lesson-categories__panel--header">
                <span v-if="period.id !== 0" class="btn btn-sm btn-success" @click="showModal = true">Thêm</span>
                <span v-if="period.id !== 0" class="text-bold">@{{ period.name }}</span>
            </div>
            <div class="lesson-categories__panel--list">
{{--                <div class="lesson-categories__panel--item" @click="selectUncategorized" :class="{'lesson-categories__panel--item-selected': selectedPath.id === 0}">--}}
{{--                    <div class="lesson-categories__panel--item-info">--}}
{{--                        <div class="flex justify-center items-center">--}}

{{--                        </div>--}}
{{--                        <div class="flex flex-column ml-5">--}}
{{--                            <span>Chưa được phân loại</span>--}}
{{--                            --}}{{--                            <span>@{{ path.type === 1 ? 'Thường' : 'Đặc biệt'}}</span>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    <div class="lesson-categories__panel--item-action">--}}
{{--                    </div>--}}
{{--                </div>--}}
                <draggable tag="lesson-categories__panel--real-items" @end="onDragEnd" v-model="paths" @start="draggable=true" @end="draggable=false" :handle="'.lesson-categories__panel--item-icon'">
                    <div class="lesson-categories__panel--item" v-for="path in paths" :class="{'lesson-categories__panel--item-selected': selectedPath.id === path.id}" @click="selectPath(path)">
                        <div class="lesson-categories__panel--item-info flex justify-start items-center">
                            <div class="lesson-categories__panel--item-icon flex justify-center items-center" style="min-height: 30px; min-width: 40px">
                                <img :src="'{{asset("cdn/adventure/default")}}/' + path.img" />
                            </div>
                            <div class="flex flex-column ml-5">
                                <span>@{{ path.title }}
                                    <i v-if="path.status === 2 && permission" @click="changeStatus(path, 1)" class="fa fa-flask text-info"></i>
                                    <i v-if="path.status === 1 && permission" @click="changeStatus(path, 0)" class="fa fa-eye text-success"></i>
                                    <i v-if="path.status === 0 && permission" @click="changeStatus(path, 2)" class="fa fa-eye-slash text-dark"></i>
                                </span>
                                {{--                            <span>@{{ path.type === 1 ? 'Thường' : 'Đặc biệt'}}</span>--}}
                            </div>
                        </div>
                        <div class="lesson-categories__panel--item-action">
                            <span @click="editPath(path)" class="btn btn-sm btn-warning mb-2 text-red" title="Sửa"><i class="fa fa-edit"></i></span>
                            <span @click="editImg(path)" class="btn btn-sm btn-info mb-2 text-white" title="Sửa"><i class="fa fa-image"></i></span>
                            <span @click="deletePath(path)" class="btn btn-sm btn-danger mb-2" title="Xoá"><i class="fa fa-trash"></i></span>
                            {{--                    <span class="btn btn-sm btn-info mb-2" title="Danh sách "><i class="fa fa-list"></i></span>--}}
                        </div>
                    </div>
                </draggable>
            </div>
        </div>
        <backend-modal v-if="showModal" @close="closeModal">
            <h3 slot="header">@{{ formData.id ? 'Sửa danh mục' : 'Thêm danh mục' }}</h3>
            <div slot="body">
                <div class="form-group">
                    <label>Tiêu đề</label>
                    <input class="form-control" v-model="formData.title"/>
                </div>
                <div class="form-group">
                    <label>Icon hoặc ảnh</label>
                    <div class="flex justify-between items-center">
                        <input type="file" class="form-control mr-5"  @change="changeFormIcon"/>
                        <img :src="'{{asset("cdn/adventure/default")}}/' + formData.img" width="200"/>
                    </div>
                </div>
                <div class="form-group">
                    <label>Khoá học</label>
                    <select class="form-control" v-model="formData.course_id" disabled>
                        <option :value="course.id">@{{ course.name }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trạng thái</label>
                    <div>
                        <label><input :checked="formData.status === 0" type="radio" value="0" name="path_status" @change="setFormStatus"/>Tắt</label>
                        <label><input :checked="formData.status === 1" type="radio" value="1" name="path_status" @change="setFormStatus"/>Bật</label>
                        <label><input :checked="formData.status === 2" type="radio" value="2" name="path_status" @change="setFormStatus"/>Testing</label>
                    </div>
                </div>
                <div class="flex justify-start">
                    <span class="btn btn-lg btn-success" @click="saveForm">@{{ formData.id ? 'Lưu' : 'Thêm' }}</span>
                </div>
            </div>
        </backend-modal>
        <backend-modal v-if="showImgModal" @close="closeImgModal">
            <h3 slot="header">
                <div class="flex justify-between">
                    <div>Hình minh hoạ</div>
                    <div class="btn btn-success" @click="addIll">Thêm</div>
                </div>
            </h3>
            <div slot="body">
                <table class="table bg-white">
                    <thead>
                    <tr>
                        <th width="50"></th>
                        <th width="150">Ảnh</th>
                        <th width="250">Tên</th>
                        <th>Mô tả</th>
                        <th width="100">Thao tác</th>
                    </tr>
                    </thead>
                    <draggable tag="tbody" @end="onDragIllEnd" v-model="selectedPath.illustrator" @start="draggable=true" @end="draggable=false" :handle="'.hamburger'">
                        <tr v-for="(item, index) in selectedPath.illustrator" :key="'row-' + index">
                            <td class="hamburger text-center"><i class="fa fa-bars fa-lg"></i></td>
                            <td>
                                <div v-if="item.i" class="flex items-center" style="position: relative">
                                    <img style="height: 150px; max-width: 100px;" :src="url + '/cdn/adventure/default/' + item.i" />
                                    <i
                                       class="fa fa-times fa-lg"
                                       style="position:absolute;top:2px;left:2px;cursor:pointer;color:red"
                                       @click="item.i = null"
                                    ></i>
                                </div>
                                <el-upload
                                    v-else
                                    class="avatar-uploader m-r-10"
                                    :headers="{ 'X-CSRF-TOKEN': csrf }"
                                    :action="`${url}/backend/adventure/upload-image`"
                                    :show-file-list="false"
                                    :on-success="handleImageUploadSuccess.bind(this, index)"
                                    :before-upload="beforeImageUpload"
                                >
                                    <i class="el-icon-plus avatar-uploader-icon"></i>
                                </el-upload>
                            </td>
                            <td>
                                <el-input v-model="item.n" placeholder="Nhập tên"></el-input>
                            </td>
                            <td>
                                <el-input v-model="item.d" type="textarea" :rows="3" placeholder="Nhập mô tả chi tiết"></el-input>
                            </td>
                            <th>
                                <div>
                                    <span @click="saveIll(item)" class="btn btn-sm btn-info mb-2" title="Xoá"><i class="fa fa-save"></i></span>
                                    <span @click="removeIll(index)" class="btn btn-sm btn-danger mb-2" title="Xoá"><i class="fa fa-trash"></i></span>
                                </div>
                            </th>
                        </tr>
                    </draggable>
                </table>
            </div>
        </backend-modal>
        <div class="course-stages__screen--panel-loading" v-if="loading">
            <div class="fa-3x">
                <i class="fa fa-spinner fa-spin"></i>
            </div>
        </div>
    </div>
</script>
<script type="text/x-template" id="lesson-checkpoint-panel-template">
    <div class="lesson-categories__panel course-stages__screen--panel">
        <div v-if="!loading" class="p-5 adventure-map">
            <div class="adventure-checkpoint ml-2" @click="openCheckpointForm">
                <div class="adventure-checkpoint__inner">
                    <i class="fa fa-plus"></i>
                </div>
            </div>
            <el-dropdown trigger="hover" v-for="(point, key) in checkpoints" @command="deleteCheckpointByKey">
                <div class="adventure-checkpoint ml-2 mb-2 a-cursor-pointer" @click="openCheckpointModal(point, key)">
                    <div class="adventure-checkpoint__inner">@{{ key }}</div>
                </div>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item icon="el-icon-delete" :command="{point, key}">Xoá</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
        <backend-modal v-if="showModal" @close="closeModal">
            <h3 slot="header">@{{ formData.id ? 'Sửa bài học' : 'Thêm bài học' }}</h3>
            <div slot="body">
                <div class="form-group">
                    <label>Ngày</label>
                    <input class="form-control" type="number" v-model="formData.key"/>
                </div>
                <div class="form-group">
                    <label>Danh mục</label>
                    <select class="form-control" v-model="selectedCategoryId">
                        <option v-for="category in categories" :value="category.id">@{{ category.title }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Nhóm bài học</label>
                    <select class="form-control" v-model="selectedGroupId">
                        <option v-for="group in groups" :value="group.id">@{{ group.name }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Bài học</label>
                    <select class="form-control" v-model="formData.lesson_id">
                        <option v-for="lesson in lessons" :value="lesson.id">@{{ lesson.name }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trạng thái</label>
                    <div>
                        <label><input :checked="formData.status === 0" type="radio" value="0" name="path_status" @change="setFormStatus"/>Tắt</label>
                        <label><input :checked="formData.status === 1" type="radio" value="1" name="path_status" @change="setFormStatus"/>Bật</label>
                        <label><input :checked="formData.status === 2" type="radio" value="2" name="path_status" @change="setFormStatus"/>Testing</label>
                    </div>
                </div>
                <div class="flex justify-start">
                    <span class="btn btn-lg btn-success" @click="saveForm">@{{ formData.id ? 'Lưu' : 'Thêm' }}</span>
                </div>
            </div>
        </backend-modal>
        <backend-modal v-show="showCheckpointModal" @close="closeCheckpointModal">
            <h3 slot="header">Danh sách bài học</h3>
            <div slot="body">
                <div>
                    <input v-model="keyToChange" type="number" placeholder="Nhập ngày" style="padding: 0 10px"/>
                    <span
                            class="btn btn-danger"
                            :class="{'disabled': !keyToChange || choseLessons.length === 0}"
                            @click="changeCheckpointKey"
                            style="border: none; "
                            :style="{'background': keyToChange && choseLessons.length > 0 ? 'red' : '#ccc', 'cursor': keyToChange && choseLessons.length > 0 ? 'pointer' : 'not-allowed'}">Chuyển</span>
{{--                    <select v-model="choseType">--}}
{{--                        <option value="docs">Mặc định</option>--}}
{{--                        <option value="video">Video</option>--}}
{{--                        <option value="test">Test</option>--}}
{{--                        <option value="flashcard">Flashcard</option>--}}
{{--                    </select>--}}
{{--                    <span--}}
{{--                            class="btn btn-danger"--}}
{{--                            :class="{'disabled': !choseType || choseLessons.length === 0}"--}}
{{--                            @click="changeLessonType"--}}
{{--                            style="border: none; "--}}
{{--                            :style="{'background': choseType && choseLessons.length > 0 ? 'red' : '#ccc', 'cursor': choseType && choseLessons.length > 0 ? 'pointer' : 'not-allowed'}">Áp dụng</span>--}}
                </div>
                <table class="table bg-white">
                    <thead>
                    <tr>
                        <th width="50"></th>
                        <th width="50" class="text-center"><input :checked="choseLessons.length === checkpointsByKey.length" type="checkbox" @click="checkAllLesson($event)"/></th>
                        <th width="150">ID</th>
                        <th>Bài học</th>
{{--                        <th width="100">Trạng thái</th>--}}
                        <th width="100">Thao tác</th>
                    </tr>
                    </thead>
                    <draggable tag="tbody" @end="onDragLessonEnd" v-model="checkpointsByKey" @start="draggable=true" @end="draggable=false" :handle="'.hamburger'">
                        <tr v-for="checkpoint in checkpointsByKey" :key="checkpoint.id + '_' + checkpoint.sort_order" >
                            <td class="hamburger text-center"><i class="fa fa-bars fa-lg"></i></td>
                            <td class="text-center">
                                <input type="checkbox" @click="checkOneLesson($event, checkpoint.id)" :checked="choseLessons.length > 0 && choseLessons.some((element) => element == checkpoint.id)"/>
                            </td>
                            <td>@{{ checkpoint.id }}</td>
{{--                            <td>@{{ lesson.group_id }}</td>--}}
                            <td>
                                <a target="_blank" :href="url + '/backend/lesson/' + checkpoint.lesson_id + '/edit'">
                                    <b>@{{ checkpoint.lesson.get_group_of_lesson.category.title }}:</b> @{{ checkpoint.lesson.name}}
                                </a>
                            </td>
                            <td>
                                    <span>
                                        <i v-if="checkpoint.status === 1" @click="changeCheckpointStatus(checkpoint, 0)" class="fa fa-eye text-success"></i>
                                        <i v-if="checkpoint.status === 0" @click="changeCheckpointStatus(checkpoint, 2)" class="fa fa-eye-slash text-dark"></i>
                                        <i v-if="checkpoint.status === 2" @click="changeCheckpointStatus(checkpoint, 1)" class="fa fa-flask text-dark"></i>
                                    </span>
                            </td>
                            <th>
                                <div>
                                    <span @click="deleteCheckpoint(checkpoint)" class="btn btn-sm btn-danger mb-2" title="Xoá"><i class="fa fa-trash"></i></span>
                                </div>
                            </th>
                        </tr>
                    </draggable>
                </table>
            </div>
        </backend-modal>
    </div>
</script>
<script type="text/x-template" id="sent-list-table">
    <table class="table table-striped">
        <thead>
        <tr>
            <th class="text-bold" scope="col">ID</th>
            <th class="text-bold" scope="col">Tiêu đề</th>
            <th class="text-bold" scope="col">Nội dung</th>
            <th class="text-bold" scope="col">Mục tiêu</th>
            <th class="text-bold" scope="col">ID mục tiêu</th>
            <th class="text-bold" scope="col">Thời gian gửi</th>
            <th class="text-bold" scope="col">Trạng thái</th>
            <th class="text-bold text-center" scope="col">Thao tác</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="notification in notifications">
            <td class="text-bold">@{{ notification.id }}</td>
            <td class="text-bold">@{{ notification.data.heading || '--' }}</td>
            <td style="width: 700px; white-space: pre-wrap" v-html="notification.title"></td>
            <td>@{{ notification.table_name | scheduleFocusOn }}</td>
            <td>@{{ notification.table_id || '--' }}</td>
            <td>@{{ notification.created_at }}</td>
            <td>@{{ notification.visible ? 'Hiển thị' : 'Ẩn' }}</td>
            <td class="text-center">
                {{--                    <span class="mr-2 hover-able hover-green"><i class="fa fa-paper-plane fa-lg"></i></span>--}}
                <span @click="removeNotification(notification)" title="Xoá" class="mr-2 hover-able hover-red"><i class="fa fa-trash fa-lg"></i></span>
                <span @click="cloneNotification(notification)" title="Clone" class="mr-2 hover-able hover-red"><i class="fa fa-copy fa-lg"></i></span>
            </td>
        </tr>
        </tbody>
    </table>
</script>

<script type="text/x-template" id="schedule-list-table">
    <table class="table table-striped">
        <thead>
            <tr>
                <th class="text-bold text-center" scope="col">ID</th>
                <th class="text-bold" scope="col">Tiêu đề</th>
                <th class="text-bold" scope="col">Nội dung</th>
                <th class="text-bold text-center" scope="col">Trạng thái</th>
                <th class="text-bold" scope="col">Thời gian gửi</th>
                <th class="text-bold" scope="col">Đối tượng</th>
                <th class="text-bold" scope="col">Trỏ đến</th>
                <th class="text-bold" scope="col">Trỏ đến ID</th>
                <th class="text-center text-bold" scope="col">Thao tác</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="schedule in schedules">
                <td class="text-bold text-center">@{{ schedule.id }}</td>
                <td class="text-bold">@{{ schedule.data.heading || '--' }}</td>
                <td style="width: 700px; white-space: pre-wrap"><span v-html="schedule.title"></span></td>
                <td class="text-center"><span class="status " :class="schedule | scheduleStatusClass">@{{ schedule | scheduleStatus }}</span></td>
                <td>@{{ schedule.time || '--' }}</td>
                <td>@{{ schedule.segments == 'Subscribed Users' ? 'Người dùng thật' : 'Người dùng test' }}</td>
                <td>@{{ schedule.table_name | scheduleFocusOn }}</td>
                <td>@{{ schedule.table_id || '--' }}</td>


                <td class="text-center">
{{--                    <span class="mr-2 hover-able hover-green"><i class="fa fa-paper-plane fa-lg"></i></span>--}}
                    <span v-if="schedule.status === 0 && (moment(schedule.time) >= moment())" title="Huỷ" @click="suspendSchedule(schedule)" class="mr-2 hover-able hover-red"><i class="fa fa-ban fa-lg"></i></span>
                    <span title="Clone" @click="cloneSchedule(schedule)" class="mr-2 hover-able hover-red"><i class="fa fa-copy fa-lg"></i></span>
                </td>
            </tr>
        </tbody>
    </table>
</script>

<script type="text/x-template" id="schedule-list-table">
    <div class="exam__results--screen" id="group__screen">
        <div class="exam__results--filter flex justify-between">
            <strong class="h4">Quản lý nhóm cộng đồng</strong>
            <div class="flex justify-end items-center">
                <strong class="mr-5">Tìm kiếm</strong>
                <div class="form-group">
                    <input type="text" class="form-control" placeholder="Nhập từ khoá" v-model="filter.id" @keyup.enter="applyFilter">
                </div>
                <span class="btn btn-info">Thêm group</span>
            </div>
        </div>
        <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
            <span>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</span>
        </div>
        <div class="exam__results--list">
            <table>
                <thead>
                <th width="4%"><span @click="sortBy('id')">ID</span></th>
                <th width="10%" class="text-center">Tuỳ chọn</th>
                </thead>
                <tbody v-if="!loading">
                <tr v-if="items.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(group, index) in items" :key="'gr-' + group.id" v-if="items.length != 0">
                    <td>
                        <div>@{{ group.id }}</div>
                    </td>
                    <td class="text-center">
                        <span class="btn btn-danger" title="Xoá nhóm"><i class="fa fa-trash"></i></span>
                        <span class="btn btn-warning" title="Khoá nhóm"><i class="fa fa-lock"></i></span>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="loading">
                <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                    <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="exam__results--paginate">
            <div>
                Hiển thị
                <select v-model="filter.per_page" @change="applyFilter">
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                </select>
                trong số @{{ loading ? '----' : total_result}} kết quả
            </div>
            <paginate
                {{--                    v-model="filter.page"--}}
                :page-count="filter.total_page"
                :page-range="4"
                :margin-pages="3"
                :click-handler="changePage"
                :prev-text="'&laquo;'"
                :next-text="'&raquo;'"
                :container-class="'pagination'"
                :page-class="'page-item'"
                :force-page="filter.page - 1"
            >
            </paginate>
        </div>
    </div>
</script>
<script type="text/x-template" id="groupMemberTable">
    <div class="exam__results--screen" id="group-members__screen">
        <div class="exam__results--filter flex justify-between">
            <strong class="h4"></strong>
            <div class="flex justify-end items-center">
                <strong class="mr-5">Tìm kiếm</strong>
                <div class="form-group">
                    <input type="text" class="form-control" placeholder="Nhập từ khoá" v-model="filter.id" @keyup.enter="applyFilter">
                </div>
                <span class="btn btn-info">Thêm thành viên</span>
            </div>
        </div>
        <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
            <span>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</span>
        </div>
        <div class="exam__results--list">
            <table>
                <thead>
                <th width="1%" class="text-right"><span @click="sortBy('id')">ID</span></th>
                <th width="20%"><span @click="sortBy('name')">Thành viên</span></th>
                <th width="10%"><span @click="sortBy('created_at')">Lịch sử đăng bài</span></th>
                <th width="10%" class="text-center">Tuỳ chọn</th>
                </thead>
                <tbody v-if="!loading">
                <tr v-if="items.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(member, index) in items" :key="'gr-' + group.id" v-if="items.length != 0">
                    <td>
                        <div class="text-right">@{{ member.id }}</div>
                    </td>
                    <td>
                        <div>@{{ member.name }}</div>
                    </td>
                    <td>
                        <div>Xem</div>
                    </td>
                    <td class="text-center">
                        <span class="btn btn-danger" title="Xoá khỏi nhóm"><i class="fa fa-trash"></i></span>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="loading">
                <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                    <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="exam__results--paginate">
            <div>
                Hiển thị
                <select v-model="filter.per_page" @change="applyFilter">
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                </select>
                trong số @{{ loading ? '----' : total_result}} kết quả
            </div>
            <paginate
                    {{--                    v-model="filter.page"--}}
                    :page-count="filter.total_page"
                    :page-range="4"
                    :margin-pages="3"
                    :click-handler="changePage"
                    :prev-text="'<'"
                    :next-text="'>'"
                    :container-class="'pagination'"
                    :page-class="'page-item'"
                    :force-page="filter.page - 1"
            >
            </paginate>
        </div>
    </div>
</script>
