@extends('backend._default.app')

@section('description') dashboard layout @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | dashboard @stop

@section('dashboard')
    {{-- import các template của vue component để hỗ trợ các version cũ  --}}
    @include('backend._default.template')
    <script
        src="{{ asset('plugin/socket-io-4.1.2/socket.io.min.js') }}?{{ filemtime('plugin/socket-io-4.1.2/socket.io.min.js') }}">
    </script>
    <script>
        var socketServer = "{{ config('app.socket_server') }}";
        var socket = io.connect(socketServer, {
            query: {
                type: 'admin',
                from: 'web',
                userId: {{ Auth::guard('admin')->user()->id }}
            }
        });
    </script>
    <style>
        .refresh-cache .refresh-text {
            display: none;
        }

        .refresh-cache:hover .refresh-text {
            display: inline;
        }
    </style>

    <div class="preloader">
        <div class="cssload-speeding-wheel"></div>
    </div>
    <div id="wrapper">
        <!-- Navigation -->
        <nav class="navbar navbar-default navbar-static-top m-b-0">
            <div class="navbar-header" style="height: 30px !important;">
                {{--                <a class="navbar-toggle hidden-sm hidden-md hidden-lg " href="javascript:void(0)" data-toggle="collapse" --}}
                {{--                    data-target=".navbar-collapse"> --}}
                {{--                    <i class="ti-menu"></i> --}}
                {{--                </a> --}}
                <div class="top-left-part" style="height: 30px;">
                    <a class="logo" href="{{ url('/backend') }}" style="margin-top: 0">
                        <img src="{{ asset('/assets/img/favicon.png') }}" alt="home" type="image/png"
                            style="width: 60px; margin-left: 20px;">
                        <span class="hidden-xs"></span>
                    </a>
                </div>
                <ul class="nav navbar-top-links navbar-right pull-right" id="admin-notifications">

                    <li class="dropdown">
                        <a class="dropdown-toggle profile-pic" data-toggle="dropdown" href="#">
                            @if (Auth::guard('admin')->user()->avatar == null)
                                <img src="{{ asset('assets/img/default-avatar.jpg') }}" class="img-circle" width="20">
                            @else
                                <img src="{{ asset('cdn/avatar/small', Auth::guard('admin')->user()->avatar) }}"
                                    class="img-circle" width="20">
                            @endif
                            <b class="hidden-xs">{{ Auth::guard('admin')->user()->username }}</b>
                        </a>
                        <ul class="dropdown-menu dropdown-user animated flipInY">
                            <li>
                                <a href="{{ url('backend/account/profile/' . Auth::guard('admin')->user()->id) }}">
                                    <i class="ti ti-user"></i> Hồ sơ
                                </a>
                            </li>
                            <li role="separator" class="divider"></li>
                            <li>
                                <a href="{{ url('backend/logout') }}">
                                    <i class="fa fa-power-off"></i> Đăng xuất
                                </a>
                            </li>
                        </ul>
                    </li>
                    {{--                    @can('isSuperAdmin', \App\Http\Models\Admin::class) --}}
                    {{--                        <li> --}}
                    {{--                            <a href="{{ url('backend/dashboard/normal-admins') }}"> --}}
                    {{--                                <i class="fa fa-check"></i> --}}
                    {{--                                <span class="hide-menu"> Kiểm duyệt </span> --}}
                    {{--                                <div class="notify" id="has-verify-request" v-if="hasVerify"> --}}
                    {{--                                    <span class="heartbit"></span> --}}
                    {{--                                    <span class="point"></span> --}}
                    {{--                                </div> --}}
                    {{--                            </a> --}}
                    {{--                        </li> --}}
                    {{--                    @endcan --}}
                    <li class="right-side-toggle">
                        <a class="waves-effect waves-light" href="javascript:void(0)">
                            <i class="ti-settings"></i>
                        </a>
                    </li>
                </ul>
                <!--choice color for footer-->
                <div class="right-sidebar">
                    <div class="slimscrollright">
                        <div class="rpanel-title"> Bảng điều khiển màu sắc
                            <span><i class="ti-close right-side-toggle"></i>
                            </span>
                        </div>
                        <div class="r-panel-body">
                            <ul>
                                <li><b>Tùy chọn bố cục</b></li>
                            </ul>

                            <ul id="themecolors" class="m-t-20">
                                <li><b>Thanh bên sáng</b></li>
                                <li><a href="javascript:void(0)" theme="default" class="default-theme">1</a></li>
                                <li><a href="javascript:void(0)" theme="green" class="green-theme">2</a></li>
                                <li><a href="javascript:void(0)" theme="gray" class="yellow-theme">3</a></li>
                                <li><a href="javascript:void(0)" theme="blue" class="blue-theme working">4</a></li>
                                <li><a href="javascript:void(0)" theme="purple" class="purple-theme">5</a></li>
                                <li><a href="javascript:void(0)" theme="megna" class="megna-theme">6</a></li>
                                <li><b>Thanh bên tối</b></li>
                                <br />
                                <li><a href="javascript:void(0)" theme="default-dark" class="default-dark-theme">7</a></li>
                                <li><a href="javascript:void(0)" theme="green-dark" class="green-dark-theme">8</a></li>
                                <li><a href="javascript:void(0)" theme="gray-dark" class="yellow-dark-theme">9</a></li>
                                <li><a href="javascript:void(0)" theme="blue-dark" class="blue-dark-theme">10</a></li>
                                <li><a href="javascript:void(0)" theme="purple-dark" class="purple-dark-theme">11</a></li>
                                <li><a href="javascript:void(0)" theme="megna-dark" class="megna-dark-theme">12</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
        @php
            $teacherRoles = ['admin','Super Admin','sale','sale_offline','sale_online','teacher'];
            $kaiwaRoles = ['admin','Super Admin','sale','sale_offline','sale_online','kaiwa'];
            $roles = ['admin','Super Admin','sale','sale_offline','sale_online'];
        @endphp
        <div class="w-screen">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <button class="navbar-toggler sm:hidden flex justify-center items-center w-10 h-10 hover:bg-gray-50"
                    type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                    aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fa fa-bars fa-lg"></i>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="nav flex items-start sm:items-center flex-col sm:flex-row flex-wrap" id="side-menu"
                        style="margin-left: 0; width: 100vw;">
                        @if (auth()->guard('admin')->check() && in_array(auth()->guard('admin')->user()->desc, $roles))
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-home" style="color: #03a9f3"></i>
                                        <span class="hide-menu"> Home <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/') }}">Bảng điều khiển</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/analytics') }}">Google Analytics</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/analytics/online') }}">Thống kê online</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/new-dashboard') }}">Số liệu thống kê (mới)</a> </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-leanpub" style="color: #03a9f3"></i>
                                        <span class="hide-menu"> Khóa học <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i> </span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/combo') }}"> Quản lý Combo </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/course') }}">Quản lý khóa học </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/course-stages') }}">Quản lý chặng </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/lesson-group') }}">Quản lý nhóm bài học </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/lesson') }}">Quản lý bài học </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/flashcard') }}">Quản lý flashcard </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/book') }}"> Quản lý sách </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/adventure') }}"> Quản lý lộ trình </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/extend-info') }}"> Quản lý gia hạn </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/book-inventory') }}"> Quản lý kho sách</a> </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                @if (Auth::guard('admin')->user()->id != 44)
                                    <div class="flex items-center justify-between bg-white">
                                        <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                            <i class="fa fa-shopping-cart" style="color: #03a9f3"></i>
                                            <span class="hide-menu"> Đơn hàng <i class="fa fa-chevron-down"
                                                    aria-hidden="true"></i></span>
                                        </a>
                                    </div>
                                    <div
                                        class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                        <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('backend/invoice') }}">Đơn hàng </a> </div>
                                        <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('backend/voucher') }}">Voucher </a> </div>
                                        <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('backend/coupon') }}">Mã giảm giá </a> </div>
                                        <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('backend/code') }}">Mã giới thiệu </a> </div>
                                        <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('backend/code/analytic') }}">Thống kê mã giới thiệu </a> </div>
                                        <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('backend/course-active') }}">Khóa học đã kích hoạt</a> </div>
                                        <!-- <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('backend/invoice-stat') }}">Thống kê đơn hàng</a> </div> -->
                                        @if (in_array(\Auth::guard('admin')->user()->email, [
                                                '<EMAIL>',
                                                '<EMAIL>',
                                                '<EMAIL>',
                                            ]))
                                            <div>
                                                <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                    href="{{ url('backend/vip/sales-statistic') }}">Thống kê sale offline</a>
                                            </div>
                                        @endif
                                    </div>
                                @else
                                    <a href="{{ url('backend/thanhtoan') }}">
                                        <i class="fa fa-shopping-cart"></i>
                                        <span style="color: #2b2b2b; font-weight: 500;"> Đơn hàng </span>
                                    </a>
                                @endif
                            </li>
                        @endif
                        @if (auth()->guard('admin')->check() && in_array(auth()->guard('admin')->user()->desc, $teacherRoles))
                            <li class="group relative cursor-pointer p-3 sm:p-0" id="chat_tab">
                                <a href="{{ url('/backend/chat') }}">
                                    <i class="fa fa-comments" style="color: #03a9f3;"></i>
                                    <span style="color: #2b2b2b; font-weight: 500;">
                                        Tin nhắn <span
                                            style="background-color: #fa3e3e;  border-radius: 4px; text-align: center;
                                    font-size: 11px; padding: 1px 2px 0 2px; color: #fff;"
                                            v-if="nbOfNotifications > 0"
                                            class="count-message-red">@{{ nbOfNotifications }}</span>
                                    </span>
                                </a>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <a href="{{ url('backend/binhluan') }}">
                                    <i class="fa fa-commenting-o" style="color: #03a9f3;"></i>
                                    <span style="color: #2b2b2b; font-weight: 500;"> Comment </span>
                                </a>
                            </li>
                        @endif
                        @if (auth()->guard('admin')->check() && in_array(auth()->guard('admin')->user()->desc, ['teacher']))
                        <li class="group relative cursor-pointer p-3 sm:p-0">
                            <a href="{{ url('/backend/discuss/flashcard') }}">
                                <i class="fa fa-facebook-square" style="color: #03a9f3;"></i>
                                <span style="color: #2b2b2b; font-weight: 500;"> Comment flashcard</span>
                            </a>
                        </li>
                        @endif
                        @if (auth()->guard('admin')->check() && in_array(auth()->guard('admin')->user()->desc, $kaiwaRoles))
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-calculator" style="color: #03a9f3;"></i>
                                        <span class="hide-menu"> Kaiwa <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/discuss') }}">Kaiwa</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/discuss/list-test') }}">Quản lý lịch test</a> </div>
                                </div>
                            </li>
                        @endif
                        @if (auth()->guard('admin')->check() && in_array(auth()->guard('admin')->user()->desc, $roles))
                            
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <a href="{{ route('booking.kaiwa') }}">
                                    <i class="fa fa-calendar" style="color: #03a9f3;"></i>
                                    <span style="color: #2b2b2b; font-weight: 500;"> Booking</span>
                                </a>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <a href="{{ route('community') }}">
                                    <i class="fa fa-users" style="color: #03a9f3;"></i>
                                    <span style="color: #2b2b2b; font-weight: 500;"> Community</span>
                                </a>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-vine" style="color: #03a9f3;"></i>
                                        <span style="color: #2b2b2b; font-weight: 500;">VIP <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/vip/statistic') }}">Thống kê</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/vip/ratio') }}">Tỷ lệ học lên</a> </div>
                                    {{-- <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black" href="{{ url('/backend/vip/invoice') }}">Tỷ lệ học lên theo đơn hàng (tham khảo)</a> </div> --}}
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/vip/vip-user-result') }}">Quản lý kết quả</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/vip/get-view-combo') }}">Quản lý combo VIP</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/vip/get-course') }}">Quản lý khoá VIP</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/vip/study-statistic-page') }}">Thống kê buổi và sĩ số</a>
                                    </div>
                                    <div>
                                        <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/vip/ti-le-gia-han?from=' . now()->startOfMonth()->format('Y-m-d')) . '&to=' . now()->endOfMonth()->format('Y-m-d') }}">
                                            Tỉ lệ gia hạn</a>
                                    </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-building-o" style="color: #03a9f3;"></i>
                                        <span style="color: #2b2b2b; font-weight: 500;">Offline <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/offline') }}">Danh sách lớp</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/offline/ti-le-gia-han?from=' . now()->startOfMonth()->format('Y-m-d')) . '&to=' . now()->endOfMonth()->format('Y-m-d') }}">Tỷ
                                            lệ gia hạn</a> </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-gamepad" style="color: #03a9f3;"></i>
                                        <span style="color: #2b2b2b; font-weight: 500;">Gamification <i
                                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/gamification/level') }}">
                                            Quản lý cấp độ</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/gamification/achievement') }}">
                                            Quản lý thành tựu</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/gamification/activity') }}">
                                            Quản lý hoạt động</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/gamification/reward') }}">
                                            Quản lý phần thưởng</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/gamification/simulate') }}">
                                            Giả lập cột sống</a> </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <a href="{{ url('backend/binhluan/facebook?param=all') }}">
                                    <i class="fa fa-facebook-square" style="color: #03a9f3;"></i>
                                    <span style="color: #2b2b2b; font-weight: 500;"> FB</span>
                                </a>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-clock-o" style="color: #03a9f3;"></i>
                                        <span style="color: #2b2b2b; font-weight: 500;"> Thi thử <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ route('exam.index') }}">Quản lý bài thi </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ route('exam.tests') }}">Quản lý bài kiểm tra </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ route('ad-test.index') }}">Quản lý test ad </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ route('exam.results') }}">Quản lý kết quả </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ route('exam.input-results') }}">Quản lý kết quả đầu vào</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ route('exam.tests.schedule', ['groupId' => 1]) }}">Lịch test</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ route('exam.tests.results') }}">KQ test</a> </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-user-o" style="color: #03a9f3;"></i>
                                        <span style="color: #2b2b2b; font-weight: 500;"> Users <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/user') }}">Danh sách </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/user/list') }}">Danh sách (mới) </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/user-send-mess/') }}">Nhắn tin hàng loạt </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/support-schedule/') }}">Tư vấn tự động </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/user/jlpt-score') }}">Điểm thi JLPT</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/reset-progress') }}">Tiến trình học</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/survey') }}">Khảo sát</a> </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-newspaper-o" style="color: #03a9f3;"></i>
                                        <span class="hide-menu"> Trang tin <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/blog') }}">Quản lý tin tức </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/category-blog') }}">Quản lý category </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/tips') }}">Quản lý bài kiến thức </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/series') }}">Quản lý series </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('backend/page') }}">Thông tin website</a> </div>
                                </div>
                            </li>
                            
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-reply" style="color: #03a9f3;"></i>
                                        <span class="hide-menu"> Phản hồi <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/comment') }}">Quản lý bình luận </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/discuss/flashcard') }}">Quản lý comment flashcard</a>
                                    </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/feedback') }}">Quản lý feedback</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/online-advisory') }}">Tư Vấn học viên online</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/offline-advisory') }}">Tư Vấn học viên offline</a> </div>
                                </div>
                            </li>
                            <li class="group relative cursor-pointer p-3 sm:p-0">
                                <div class="flex items-center justify-between bg-white">
                                    <a class="menu-hover text-small font-medium text-black lg:mx-2">
                                        <i class="fa fa-cog" style="color: #03a9f3;"></i>
                                        <span class="hide-menu"> Hệ thống <i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span>
                                    </a>
                                </div>
                                <div
                                    class="invisible absolute z-50 flex min-w-[300px] flex-col bg-white py-1 px-4 text-gray-800 shadow-xl group-hover:visible">
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/teacher') }}">Quản lý giáo viên </a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/global-notification/schedule') }}">Quản lý thông báo app
                                        </a>
                                    </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/payment') }}">Cổng thanh toán</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/server') }}">Quản lý server video</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/video') }}">Quản lý video</a> </div>
                                    <div><a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/advertise') }}"> <span> Banner trang chủ </span> </a></div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/popup') }}">Popup trang chủ</a> </div>
                                    @if (Auth::guard('admin')->user()->permission == 1)
                                        <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                                href="{{ url('/backend/admin') }}">Quản lý admin</a> </div>
                                    @endif
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/slider') }}">Quản lý ảnh nền trang chủ</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/recruitment') }}">Quản lý tin tuyển dụng</a> </div>
                                    <div> <a class="my-2 bg-white block py-1 text-gray-500 hover:text-black"
                                            href="{{ url('/backend/import-page') }}">Import users</a> </div>
                                </div>
                            </li>
                        @endif
                    </ul>
                </div>
            </nav>
        </div>
    </div>

    <!-- Nội dung cần quản lý -->
    {{--    <div id="navbar-sidebar" class="navbar-default sidebar" role="navigation"> --}}
    {{--        <div class="sidebar-nav navbar-collapse slimscrollsidebar"> --}}
    {{--            <div class="container-fluid" style="padding-bottom: 0;"> --}}

    {{--            </div> --}}
    {{--        </div> --}}
    {{--    </div> --}}

    <script type="text/javascript">
        //hàm xóa cache redis
        function refreshCache() {

            $("#refresh-cache").addClass("fa-spin");

            setTimeout(function() {
                $.get(window.location.origin + '/api/refresh-cache', function(response) {
                    if (response == "success") {
                        $("#refresh-done").css('display', 'inline');
                        $("#refresh-cache").css('display', 'none');

                        toastr.success('Đã làm mới dữ liệu dungmori.com');
                    }
                });
            }, 500);
        }

        function refreshApiCache() {
            $("#refresh-api-cache").addClass("fa-spin");

            var apiUrl = "{{ config('app.api_url') }}";
            var apiKey = "{{ config('app.api_key') }}";

            setTimeout(function() {
                $.ajax({
                    url: apiUrl + '/api/private/clear-cache',
                    headers: {
                        apiKey: apiKey
                    },
                    type: "GET",
                    success: function() {
                        $("#refresh-api-done").css('display', 'inline');
                        $("#refresh-api-cache").css('display', 'none');

                        toastr.success('Đã làm mới dữ liệu App');
                    }
                });
            }, 500);
        }

        //hàm xóa cache redis trên mjt
        function refreshMjtCache() {
            var jlptUrl = "http://localhost:3333";
            var jlptSocket = "http://localhost:8008";

            //nếu là deploy trên web test
            if (window.location.href.indexOf("web-test") != -1) {
                jlptUrl = "https://jlpt-test.dungmori.com";
                jlptSocket = "https://count-test.dungmori.com";
            }

            //nếu là deploy trên web thật
            if (window.location.href.indexOf("dungmori.com") != -1 && window.location.href.indexOf("web-test") == -1) {
                jlptUrl = "https://mjt.dungmori.com";
                jlptSocket = "https://mjt-count.dungmori.com";
            }

            $("#refresh-mjt-cache").addClass("fa-spin");

            console.log(jlptUrl + '/clear-cache');
            setTimeout(function() {
                $.get(jlptUrl + '/clear-cache', function(response) {
                    if (response == "success") {
                        $("#refresh-mjt-done").css('display', 'inline');
                        $("#refresh-mjt-cache").css('display', 'none');
                        toastr.success('Đã làm mới dữ liệu mjt cho app');
                    }
                });
            }, 500);
        }
    </script>
    <div class="refresh-cache" onclick="refreshCache()" style="top: auto; bottom: 95px;">
        <i class="fa fa-refresh" id="refresh-cache"></i>
        <i class="fa fa-check-circle" id="refresh-done" style="display: none; color: green;"></i><span
            class="refresh-text"> refresh</span>
    </div>
    @if (Auth::guard('admin')->user()->email == '<EMAIL>' ||
            Auth::guard('admin')->user()->email == '<EMAIL>')
        <div class="refresh-cache" onclick="refreshMjtCache()" style="top: auto; bottom: 65px;">
            <i class="fa fa-refresh" id="refresh-mjt-cache"></i>
            <i class="fa fa-check-circle" id="refresh-mjt-done" style="display: none; color: green;"></i><span
                class="refresh-text"> refresh mjt</span>
        </div>
    @endif
    <div class="refresh-cache" onclick="refreshApiCache()" style="top: auto; bottom: 35px;">
        <i class="fa fa-refresh" id="refresh-api-cache"></i>
        <i class="fa fa-check-circle" id="refresh-api-done" style="display: none; color: green;"></i><span
            class="refresh-text"> refresh api</span>
    </div>

    <!-- Từn nội dung được hiển thị cụ thể-->
    <div id="page-wrapper">
        <div class="container-fluid">
            @yield('content')
        </div>
    </div>

    @yield('footer')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.2/rollups/aes.js"></script>
    <script type="text/javascript">
        var admin = {!! Auth::guard('admin')->user() !!};
        var aesKey = '5927d42f6be3c971472aaeac626853ef';
        var notificationCount = new Vue({
            el: '#admin-notifications',
            data: {
                nbOfNotifications: 0,
                nbOfOrders: 0,
                nbOfComments: 0,
                hasVerify: false
            },
            methods: {

                printNumber(number) {
                    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                },

                checkVerifyAdminNormal() {
                    // $.ajax({
                    //     url: window.location.origin+"/backend/dashboard/normal-admins/list", type:"GET", async: true,
                    //     error:function(){ return false; },
                    //     success: (response) => {
                    //         this.hasVerify = response.length > 0;
                    //     }
                    // });
                }
            },
            mounted() {
                var vm = this;
                {{--                @can('isSuperAdmin', \App\Http\Models\Admin::class) --}}
                {{--                    this.checkVerifyAdminNormal(); --}}
                {{--                    setInterval(this.checkVerifyAdminNormal, 5000); --}}
                {{--                @endcan --}}
            }
        });

        var chatTab = new Vue({
            el: '#chat_tab',
            data: {
                nbOfNotifications: 0
            },
            methods: {
                updateCountNotRead: function() {
                    // var vm = this;
                    // // Phần lấy số tin nhắn chưa đọc chỉ chạy cho nick của Quỳnh
                    // // Vì phần này query khá nặng, đặc biệt khi data đã lớn
                    // if (admin.email === '<EMAIL>') {
                    //     $.ajax({
                    //         url: window.location.origin + "/backend/count-not-read-message",
                    //         type: "GET",
                    //         async: true,
                    //         error: function(error) {
                    //             console.log("Error get number of not read message", error);
                    //         },
                    //         success: function(response) {
                    //             vm.nbOfNotifications = response;
                    //         }
                    //     });
                    // }
                }
            },
            mounted() {
                var vm = this;
                // vm.updateCountNotRead();

                // socket.on('send_new_message', function(message) {
                //     vm.nbOfNotifications = message.countNotRead;
                // });

                // socket.on('send_new_message_enc', function(message) {
                //     message = CryptoJS.AES.decrypt(message, aesKey);
                //     message = message.toString(CryptoJS.enc.Utf8);
                //     message = JSON.parse(message);
                //     message = JSON.parse(message);
                //     if (message.receiverId == 0) {
                //         vm.nbOfNotifications = message.countNotRead;
                //     }
                // });
            }
        });
    </script>

    {{-- handle phần thông báo render --}}
    <script src="{{ asset('plugin/toastr/toastr.min.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('plugin/toastr/toastr.min.css') }}" />
    <script src="{{ asset('plugin/pusher/pusher.min.js') }}"></script>

    <script>
        //config toast
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": false,
            "positionClass": "toast-bottom-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "0",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        }

        //lắng nghe tin nhắn từ pusher
        Pusher.logToConsole = false;
        var pusher = new Pusher('7d39d4954f600d3bb86c', {
            cluster: 'ap3'
        });
        var channel = pusher.subscribe('render');
        channel.bind('render', function(data) {
            toastr.info(data.message, {
                timeOut: 3000
            })
        });
    </script>

@stop
