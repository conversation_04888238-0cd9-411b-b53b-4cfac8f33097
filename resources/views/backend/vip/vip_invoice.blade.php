@extends('backend._default.dashboard')

@section('description') Q<PERSON>ản l<PERSON> Đơ<PERSON> hàng @stop
@section('keywords') Invoices @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý Đơn hàng @stop

@section('assets')
  <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
  <link media="all" type="text/css" rel="stylesheet" href="{{ asset('plugin/datepicker/vue2-datepicker.min.css') }}?{{filemtime('plugin/datepicker/vue2-datepicker.min.css')}}">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
  <link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
  <style rel="stylesheet">
    .w-half {width: 49%;}
    .w-full {width: 100%;}
  </style>
@stop

@section('content')
  <div class="filterable-list__screen" id="vip-invoice--screen">
    <div class="filterable-list__filter">
      <div class="form-group">
        <label>ID</label>
        <input type="text" class="form-control" placeholder="Nhập ID" v-model="filter.id" @keyup.enter="applyFilter">
      </div>
      <div class="form-group">
        <label>UUID</label>
        <input type="text" class="form-control" placeholder="Nhập UUID" v-model="filter.uuid" @keyup.enter="applyFilter">
      </div>
      <div class="form-group">
        <label>Email</label>
        <input type="text" class="form-control" placeholder="Nhập email" v-model="filter.email" @keyup.enter="applyFilter">
      </div>
      <div class="form-group">
        <label>Tên</label>
        <input type="text" class="form-control" placeholder="Nhập tên" v-model="filter.name" @keyup.enter="applyFilter">
      </div>
      <div class="form-group">
        <label>Loại</label>
        <select class="form-control" name="filterIsVip" v-model="filter.isVip">
          <option :value="undefined">Tất cả</option>
          <option :value="0">Đơn Online</option>
          <option :value="1">Đơn VIP</option>
          <option :value="4">Đơn Sách</option>
          <option :value="3">Đơn Offline</option>
          <option :value="2">Đơn Sách Cũ</option>
        </select>
      </div>
      <div v-if="filter.isVip == 3" class="form-group">
        <label>Cơ sở</label>
        <select v-model="filter.department" class="form-control">
          <option value="">Chọn cơ sở</option>
          <option v-for="department in departmentList" :value="department.id">@{{ department.name }}</option>
        </select>
      </div>

      <div class="form-group" v-if="filter.isVip !== undefined">
        <label>Combo</label>
        <select v-if="filter.isVip" class="form-control" name="vipComboList" id="vipComboList" v-model="filter.comboId">
          <option :value="undefined">Lọc theo combo</option>
          <option v-for="combo in vipComboList" :value="combo.id">@{{ combo.name }}</option>
        </select>
        <select v-if="!filter.isVip" class="form-control" name="vipComboList" id="vipComboList" v-model="filter.comboId">
          <option :value="undefined">Lọc theo combo</option>
          <option v-for="combo in comboList" :value="combo.id">@{{ combo.name }}</option>
        </select>
      </div>
      <div class="form-group" v-if="filter.isVip">
        <label>Khoá học</label>
        <select class="form-control" name="vipComboList" id="vipComboList" v-model="filter.courseId">
          <option :value="undefined">Lọc theo khoá học</option>
          <option v-for="course in courseList" :value="course.id">@{{ course.name }}</option>
        </select>
      </div>
      <div class="form-group">
        <label>Sale</label>
        <select class="form-control" name="vipComboList" id="vipComboList" v-model="filter.sale">
          <option :value="undefined">Lọc theo sale</option>
          <option v-for="sale in saleList" :value="sale.id">@{{ sale.name }}</option>
        </select>
      </div>
      {{--      <div class="form-group">--}}
      {{--        <label>Người tạo</label>--}}
      {{--        <select class="form-control" v-model="filter.admin_create">--}}
      {{--          <option :value="undefined">Bỏ lọc</option>--}}
      {{--          <option value="user">User</option>--}}
      {{--          <option value="admin">Admin</option>--}}
      {{--        </select>--}}
      {{--      </div>--}}
      <div class="form-group" v-if="filter.time_by">
        <label>Từ ngày</label>
        <date-picker v-model="filter.time_from" input-class="form-control w-full" lang="en" type="date" format="YYYY-MM-DD" value-type="YYYY-MM-DD"></date-picker>
      </div>
      <div class="form-group" v-if="filter.time_by">
        <label>Đến ngày</label>
        <date-picker v-model="filter.time_to" input-class="form-control w-full" lang="en" type="date" format="YYYY-MM-DD" value-type="YYYY-MM-DD"></date-picker>
      </div>
      <div class="form-group">
        <label>Lọc theo thời gian</label>
        <select class="form-control" v-model="filter.time_by">
          <option :value="undefined">Bỏ lọc</option>
          <option value="created_at">Ngày tạo</option>
          <option value="active_time">Ngày kích hoạt</option>
          <option value="book_info->refundDate">Ngày hoàn sách</option>
        </select>
      </div>
      <div class="form-group">
        <label>Trạng thái thêm lớp</label>
        <select class="form-control" v-model="filter.isAttached">
          <option :value="undefined">Bỏ lọc</option>
          <option :value="0">Chưa thêm lớp</option>
          <option :value="1">Đã thêm lớp</option>
        </select>
      </div>
      <div class="form-group">
        <label>SĐT</label>
        <input type="text" class="form-control" placeholder="Nhập SĐT" v-model="filter.phone" @keyup.enter="applyFilter">
      </div>
      <div class="form-group">
        <label>Lọc tiền Việt / Nhật</label>
        <select class="form-control" v-model="filter.currency">
          <option :value="undefined">Bỏ lọc</option>
          <option value="vnd">VNĐ</option>
          <option value="jpy">Yen</option>
        </select>
      </div>
      <div class="form-group">
        <label>Trạng thái kích hoạt</label>
        <select class="form-control" v-model="filter.isActivated">
          <option :value="undefined">Bỏ lọc</option>
          <option :value="0">Chưa kích hoạt</option>
          <option :value="1">Đã kích hoạt</option>
        </select>
      </div>
      <div class="form-group">
        <label>Trạng thái cọc</label>
        <select class="form-control" v-model="filter.depositStatus">
          <option :value="undefined">Bỏ lọc</option>
          <option :value="1">Đóng hết một lần</option>
          {{--          <option :value="2">Cọc một phần</option>--}}
          <option :value="3">Đã hoàn thành học phí</option>
          <option :value="4">Chưa hoàn thành học phí</option>
        </select>
      </div>
      <div class="form-group">
        <label>Voucher</label>
        <input type="text" class="form-control" placeholder="Nhập voucher" v-model="filter.voucher" @keyup.enter="applyFilter">
      </div>
      <div class="form-group" v-if="filter.isVip == 4">
        <label>Lỗi hoàn</label>
        <select class="form-control" v-model="filter.refund">
          <option :value="''">Tất cả</option>
          <option :value="'sale'">Do Sale</option>
          <option :value="'post'">Do Bưu Điện</option>
          <option :value="'student'">Do Học Viên</option>
          <option :value="'sender'">Do Người Gửi</option>
        </select>
      </div>
      <div class="form-group" v-if="filter.isVip == 4">
        <label>Vị trí</label>
        <select class="form-control" v-model="filter.location">
          <option :value="''">Tất cả</option>
          <option :value="'vn'">Việt Nam</option>
          <option :value="'jp'">Nhật Bản</option>
        </select>
      </div>
      <div class="form-group" v-if="filter.isVip == 4">
        <label>Phí chuyển</label>
        <select class="form-control" v-model="filter.payer">
          <option value="">Tất cả</option>
          <option value="hv">Học viên</option>
          <option value="dmr">Dungmori</option>
          <option value="sale">Sale</option>
        </select>
      </div>
      <div class="form-group" v-if="filter.isVip == 4">
        <label>Đơn sửa</label>
        <select class="form-control" v-model="filter.modifyStatus">
          <option value="">Tất cả</option>
          <option value="none">Chưa sửa</option>
          <option value="modify">Đã sửa</option>
        </select>
      </div>
      <div class="form-group" v-if="filter.isVip == 4">
        <label>Trạng thái in</label>
        <select class="form-control" v-model="filter.exportStatus">
          <option value="">Tất cả</option>
          <option value="none">Chưa in</option>
          <option value="print">Đã in</option>
        </select>
      </div>
      <div class="form-group">
        <label>Phương thức thanh toán</label>
        <select class="form-control" v-model="filter.paymentMethod">
          <option value="">Tất cả</option>
          <option value="" v-for="method in paymentMethodList" :value="method.id">
            @{{ method.name }}
          </option>
        </select>
      </div>
      <div style="display: flex; align-items: flex-end">
        <span style="margin-right: 10px; cursor: pointer" @click="resetFilter"><i class="fa fa-refresh"></i></span>
        <button class="btn btn-info" @click="applyFilter">Lọc</button>
        <button class="btn btn-success" @click="exportExcel()" style="margin-left: 10px;">Xuất Excel</button>
        <button class="btn bg-neutral-300" @click="exportBookExcel()" style="margin-left: 10px;" v-if="adminRole">Excel Sách</button>
      </div>
    </div>
    <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
      <div class="flex justify-start items-center">
        <div class="filterable-list__tab" :class="{active: filter.status === 'new'}" @click="toggleStatus('new')">Đơn hàng chờ xử lý
          {{--          <div class="badge badge-red px-2">100</div>--}}
        </div>
        <div class="filterable-list__tab" :class="{active: filter.status === 'completed'}" @click="toggleStatus('completed')">Đơn hàng hoàn thành</div>
        <div class="filterable-list__tab" :class="{active: filter.status === 'canceled'}" @click="toggleStatus('canceled')">Đơn hàng đã huỷ</div>
        <div class="filterable-list__tab"  :class="{active: filter.status === 'refund'}" @click="toggleStatus('refund')">Đơn sách hoàn</div>
        <div>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</div>
      </div>
      <div>
        <!-- Nơi này để chứa các button thao tác -->
        <button class="btn btn-success" @click="openCreate(true, true)"><i class="fa fa-plus mr-2"></i>Tạo đơn VIP</button>
        <button class="btn btn-danger" @click="openCreate(false, true)"><i class="fa fa-plus mr-2"></i>Tạo đơn Online</button>
        <button class="btn btn-info" @click="openCreate(true, false)"><i class="fa fa-plus mr-2"></i>Tạo đơn Offline</button>
      </div>
    </div>
    <div id="invoice_tbl_box" class="filterable-list__list">
      <table id="invoice_tbl">
        <thead>
        <th width="50px">Mã số</th>
        <th class="text-center">Sản phẩm</th>
        <th width="200px">Hình thức TT</th>
        <th>Khách hàng</th>
        <th>Gói plus</th>
        <th class="text-center">Lớp</th>
        <th>Sale</th>
        <th class="text-left">Phí sách</th>
        <th class="text-right">G.trị đơn hàng</th>
        <th class="text-right">Thực thu</th>
        <th class="text-right">Nợ</th>
        <th>Trạng thái</th>
        <th>Thời gian</th>
        <th class="text-center" width="100px"></th>
        </thead>
        <tbody v-if="!loading">
        <tr v-if="results.length == 0">
          <td colspan="12" class="text-center"> Không có dữ liệu</td>
        </tr>
        <tr v-for="(result, index) in results" :key="result.id" v-if="results.length != 0">
          <td>@{{ result.id }}</td>
          <td class="text-center">
            <div>
              <span class="font-bold label new-label green-label" v-if="result.book_info || result.book"><i class="fa fa-book" aria-hidden="true"></i></span>
              <span class="font-bold" v-if="result.product_type === 'course'">Khoá học</span>
              <span class="font-bold" v-if="result.product_type === 'combo'">Combo</span>
              <span v-if="result.product_type === 'vip_course'"><span class="font-bold label new-label red-label">VIP</span> Khoá học</span>
              <span v-if="result.product_type === 'vip_combo'"><span class="font-bold label new-label red-label">VIP</span>  Combo</span>
              <span v-if="result.product_type === 'offline'"><span class="font-bold label new-label blue-label">Offline</span>  Combo</span>
              @{{ result.product_name || '--' }}
            </div>
            <div v-for="bonus in result.bonuses">
              Tặng @{{ bonus?.course?.name || '--' }} @{{ bonus?.days || '--' }} ngày
            </div>
            <div>
              <a :href="`${url}/checkout/${result.uuid}`" target="_blank" class="font-bold">@{{ result.uuid }}</a>
            </div>
          </td>
          <td style="font-size: 12px; font-weight: bold">
            <div>@{{ result.payment_method_id ? result.payment_method?.name : '--' }}</div>
            <div>@{{ result.voucher ? result.voucher?.key : '--' }}</div>
          </td>
          <td>
            <div>@{{ result.user?.id || '--' }} • @{{ result.info_contact ? result.info_contact.name : (result.user ? result.user.name : '--') }}</div>
            <div>
              @{{ result.user ? result.user.email : (result.info_contact?.email || '--') }}
              <a v-if="result.user_id && (result.combo || result.vip_combo)" class="login_user"
                 :href="`${url}/backend/user/login/${result.user_id}`" target="_blank"><i class="fa fa-sign-in"></i>
              </a>
            </div>
            <div>@{{ result.department?.name }}</div>
            <div v-if="result.info_contact && result.info_contact.phone">
              <a :href="`tel:${result.info_contact.phone}`">
                <i class="fa fa-phone mr-3"></i>@{{ result.info_contact ? result.info_contact.phone : '--' }}
              </a>
            </div>
            <div>
              <template v-if="result.info_contact && result.info_contact.address">
                @{{ result.info_contact.address }}
              </template>
              <template v-else-if="result.book_info && result.book_info.address">
                @{{ result.book_info.address }}
              </template>
            </div>
            <div class="flex items-center">
              <a v-if="result.info_contact && result.info_contact.facebook" :href="result.info_contact ? result.info_contact.facebook : ''" target="_blank" class="mr-2">
                <i class="fa fa-facebook-square"></i>
              </a>
              <div v-if="result.user && (result.combo || result.vip_combo)">
                <a v-if="result.user.conversation" :href="'{{url('/backend/chat#')}}'+ result.user.conversation.id" target="_blank" class="mr-2">
                  <i class="fa fa-comment"></i>
                </a>
                <i v-else class="fa fa-comment mr-2" style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                   @click="initConversation(result.user.id)"
                ></i>
              </div>
              <a v-if="result.info_contact && result.info_contact.chat" :href="result.info_contact.chat" target="_blank" class="mr-2">
                <i class="fa fa-codiepie"></i>
              </a>
            </div>
            <div class="font-bold text-red text-md" @click="showNote(result.note)">
              @{{ result.note?.slice(0,40) }} <span v-if="result.note?.length > 40">...</span>
            </div>
            <div v-if="result.info_contact && result.info_contact.platform" class="font-bold text-red text-md">
              @{{ result.info_contact.platform }}
            </div>
          </td>
          <td>
            <div v-if="result.extra_days">
              <div v-if="result.extra_days >= 30">@{{ result.extra_days / 30 }} tháng</div>
              <div v-else>@{{ result.extra_days }} ngày</div>
            </div>
            <div v-if="result.extra_price">
              @{{ new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(result.extra_price) }}
            </div>
          </td>
          <td class="text-center">
            <div
              v-if="
                (['vip_combo', 'vip_course', 'offline'].includes(result.product_type) && result.vip_combo && result.product_id !== result.book_id)
              ">
              <span
                    v-for="course in result.vip_combo.courses"
                    class="badge a-cursor-pointer mr-1"
                    :class="[getLevelStatus(result, course.level) === -1 ? 'badge-gray' : 'badge-info']"
                    @click="showAddGroup(result, result.user_id, course.level)"
              >
                @{{ course.name }}
              </span>
            </div>
            <div
              v-else-if="
                result.product_type === 'combo' && result.combo && result.combo.services
              ">
              <template v-for="course in JSON.parse(result.combo.services).courses">
                <span
                  v-if="getCourseFromService(course)"
                  class="badge a-cursor-pointer mr-1"
                  :class="[getLevelStatus(result, getCourseFromService(course).name) === -1 ? 'badge-gray' : 'badge-info']"
                  @click="showAddGroup(result, result.user_id, getCourseFromService(course).name)"
                >
                  @{{ getCourseFromService(course).name }}
                </span>
              </template>

            </div>
            <div
              v-else-if="
                result.product_type === 'course' && result.course && validCourses.find(course => course.id === result.course.id)
              ">
              <span
                class="badge a-cursor-pointer mr-1"
                :class="[getLevelStatus(result, result.course.name) === -1 ? 'badge-gray' : 'badge-info']"
                @click="showAddGroup(result, result.user_id, validCourses.find(course => course.id === result.course.id))"
              >
                @{{ validCourses.find(course => course.id === result.course.id).name }}
              </span>
            </div>
          </td>
          <td>@{{ result.sale ? result.sale.name : '--' }}</td>
          <td class="text-left">
            <div v-if="result.book_info">
              <div>Phí chuyển: <span class="text-success"> @{{ result.book_info.price | decimal }}</span></div>
              <div>Phí hoàn: <span class="text-success"> @{{ result.book_info.refundShip | decimal }}</span></div>
            </div>
            <div v-else>--</div>
          </td>
          <template>
            <td class="text-right">
              <div class="text-blue-500 font-semibold">
                @{{ result.price | decimal }}
                @{{ getCurrencySymbol(result.currency) }}
              </div>
            </td>
          </template>
          <td class="text-right font-bold text-success">@{{ result.paid_money | decimal }} @{{ getCurrencySymbol(result.currency) }}</td>
          <template>
            <td class="text-right">
              <div class="text-red-500 font-semibold" @click="depositInvoice = result; showInvoiceDept = true">
                @{{ result.price - result.discount_money - result.paid_money | decimal }}
                @{{ getCurrencySymbol(result.currency) }}
              </div>
            </td>
          </template>
          {{--          <td class="font-bold">--}}
          {{--            <div v-if="['course', 'combo'].includes(result.product_type)">--}}
          {{--              <div v-if="result.coupon">--}}
          {{--                <div>Giảm @{{ result.coupon.value }}% - @{{ result.coupon.code}}</div>--}}
          {{--                <div>@{{ result.coupon.email }}</div>--}}
          {{--              </div>--}}
          {{--              <div v-else>@{{ result.coupon_id || '--' }}</div>--}}
          {{--            </div>--}}
          {{--            <div v-else>@{{ result.coupon_id || '--' }}</div>--}}
          {{--          </td>--}}
          <td>
            <div v-if="['vip_combo', 'offline'].includes(result.product_type)">
              <div v-if="result.invoice_status == 'completed' && result.payment_status == 'paid' && result.active_time" class="label new-label green-label" >Hoàn thành</div>
              <div v-else-if="result.invoice_status == 'new' && result.payment_status == 'unpaid' && !result.active_time" class="label new-label orange-label">Chờ xử lý...</div>
              <div v-else-if="result.invoice_status == 'canceled'" class="label new-label orange-label">Chờ xử lý...</div>
              <div v-else class="flex flex-column">
                <div v-if="!result.active_time" class="label new-label orange-label mt-2" >Chờ kích hoạt</div>
                <div v-else class="label new-label green-label mt-2" >Đã kích hoạt</div>
                <div v-if="result.invoice_status != 'completed'" class="label new-label orange-label mt-2" >Chờ thêm lớp</div>
                <div v-else class="label new-label green-label mt-2" >Đã thêm lớp</div>
                <div v-if="result.payment_status == 'unpaid'" class="label new-label orange-label mt-2" >Chờ thanh toán</div>
                <div v-else class="label new-label green-label mt-2" >Đã chuyển tiền</div>
              </div>
            </div>
            <div v-else>
              <span v-if="result.invoice_status == 'completed' && result.payment_status == 'paid'" class="label new-label green-label" >Hoàn thành</span>
              <span v-else-if="result.invoice_status != 'completed'" class="label new-label orange-label" >Chờ kích hoạt</span>
              <span v-else-if="result.invoice_status == 'completed' && result.payment_status == 'unpaid'" class="label new-label blue-label" >Chờ thanh toán</span>
              <span v-else-if="result.invoice_status == 'new' && result.payment_status == 'unpaid'" class="label new-label orange-label">Chờ xử lý...</span>
              <span v-else-if="result.invoice_status == 'new' && result.payment_status == 'paid'" class="label new-label orange-label">Đã chuyển tiền</span>
              <span v-else class="label new-label red-label">Đã hủy</span>
            </div>
          </td>
          <td style="font-size: 12px; font-weight: bold;">
            <div>Tạo: @{{ result.created_at | dateTimeToMinute }}</div>
            <div>Thanh toán: @{{ result.paid_at | dateTimeToMinute }}</div>
            <div>Kích hoạt: @{{ result.active_time | dateTimeToMinute }}</div>
          </td>
          <td class="text-left">
            <div class="flex flex-column">
              <template v-if="['combo', 'course'].includes(result.product_type)">
                <template v-if="result.invoice_status == 'new' || (result.invoice_status == 'completed' && result.payment_status == 'unpaid')">
                  <button :disabled="activeInvoiceLoading" v-if="result.product?.type && result.product?.type == 'book' && result.invoice_status == 'new'" class="label new-label green-label" @click="activeInvoice(result.id, 'book')">
                    Kích hoạt sách<i class="fa fa-caret-right" aria-hidden="true"></i>
                  </button>
                  <button
                          v-if="
                      result.product_type == 'course' ||
                      (result.product?.type &&
                      result.product?.type != 'book' &&
                      result.invoice_status == 'new')
                    "
                          :disabled="activeInvoiceLoading"
                          class="label new-label green-label"
                          @click="activeInvoice(result.id)"
                  >
                    Kích hoạt <i class="fa fa-caret-right" aria-hidden="true"></i>
                  </button>

                </template>
              </template>
              <button
                      v-if="result.bonus"
                      :disabled="createInvoiceLoading || !result.bonus?.status"
                      class="label new-label green-label mt-2"
                      @click="activeBonusCourse(result)"
              >
                @{{ result.bonus?.status ? 'Tặng khoá học' : 'Đã tặng' }} <i class="fa fa-caret-right" aria-hidden="true"></i>
              </button>
              <div class="label green-label new-label cursor-pointer mt-2" @click="invoiceDetail(result)" v-if="result.product_type == 'offline'">
                Hoá đơn
              </div>
              <div class="label green-label new-label cursor-pointer mt-2"
                   @click="makeInvoicePaidMore(result)"
                   v-if="checkPaidMore(result)">
                Nộp thêm
              </div>
              <div
                      v-if="[3, 14, 107, 69, 89, 144, 67, 66].includes(adminId) || ((result.admin_active === 0 || adminId === result.admin_active)
                    && (result.invoice_status !== 'cancel'))"
                      class="label new-label blue-label cursor-pointer mt-2" @click="editInvoice(result)"
              >
                Sửa
              </div>
              <div v-if="result.invoice_status === 'new'" class="label red-label new-label cursor-pointer mt-2" style="color: white;" @click="cancelInvoice(result.id)">Huỷ</div>
              <div
                      v-if="result.logs && result.logs.length"
                      class="label new-label orange-label cursor-pointer mt-2" @click="showLog(result.logs)"
              >
                Lịch sử
              </div>
              @if(auth()->guard('admin')->check() && auth()->guard('admin')->user()->email == '<EMAIL>')
                <div
                        class="label new-label orange-label cursor-pointer mt-2" @click="deleteInvoice(result.id)"
                >
                  Xoá
                </div>
              @endif
            </div>
          </td>
        </tr>
        </tbody>
        <tbody v-if="loading">
        <tr style="height: 60vh; padding: 20px 0; text-align: center" >
          <td colspan="12">
            <i class="fa fa-spinner fa-spin fa-3x"></i>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="filterable-list__paginate">
      <div>
        Hiển thị
        <select v-model="filter.per_page" @change="applyFilter">
          <option value="20">20</option>
          <option value="50">50</option>
          <option value="100">100</option>
          <option value="500">500</option>
          <option value="1000">1000</option>
          <option value="2000">2000</option>
        </select>
        trong số @{{ loading ? '----' : total_result}} kết quả
      </div>
      <paginate
              v-model="filter.page"
              :page-count="filter.total_page"
              :page-range="4"
              :margin-pages="3"
              :click-handler="changePage"
              :prev-text="'&laquo;'"
              :next-text="'&raquo;'"
              :container-class="'pagination'"
              :page-class="'page-item'"
              :force-page="filter.page - 1"
      >
      </paginate>
    </div>
    <backend-modal v-if="showAddGroupModal" @close="closeAddGroupModal">
      <div slot="body" class="text-lg">
        <label class="font-bold mt-3">Nhóm đã tham gia</label>
        <div v-for="group in joinedList"
             style="padding: 10px; border: 1px solid #ccc; margin-bottom: 5px;border-radius: 4px;"
             class="flex justify-between items-center"
        >
          <template v-if="currentAddCourseData.invoice.product_type === 'offline'">
            <div v-if="group" class="w-full flex justify-between items-center">
              <span :id="`group-${group.id}`">
                <b v-for="teacher in group.teachers" :key="`teacher-${teacher.id}`">@{{ teacher.last_name }} @{{ teacher.first_name }} | </b>
                <span><b>N@{{ 6 - group?.level_of_n }}</b> | </span>
                <span><b>@{{ group?.info[0]?.room?.room_name }}</b> | </span>
                <span><b>@{{ group?.info[0]?.periods?.map(o => o.period_code).join(',') }}</b> | </span>
                <span><b>@{{ group?.info.map(o => o.day_id + 2).join(',') }}</b> | </span>
                <span>Sĩ số: <b>@{{ group.students.length }}</b> | </span>
                <span>Số buổi: <b>@{{ group.total }}</b> | </span>
                <span>Giá: <b>@{{ group.cost * 1000 | decimal}}</b> | </span>
                <span>Từ <b class="text-success">@{{ group.date_start | readableDate }}</b> đến <b class="text-danger">@{{ group.date_end | readableDate }}</b></span>
              </span>
              <div>
                <button class="btn btn-danger mr-3 " @click="kick(currentAddCourseData.userId, group.id)"><i class="fa fa-trash mr-2"></i>Xoá</button>
                <button :disabled="activeLoading" v-if="!getGroupWatchExpiredStatus" class="btn btn-info" @click="activeOnline(currentAddCourseData.userId, group.id, currentAddCourseData.invoice.id)">
                  <i v-if="activeLoading" class="fa fa-spinner fa-spin"></i>
                  <i v-else class="fa fa-unlock mr-2"></i>
                  Kích khoá online
                </button>
                <button
                        class="badge mr-5"
                        style="cursor: pointer"
                        title="Copy to link chat"
                        @click="copyLinkChat(group.group_chat_id)"
                        v-if="group.group_chat_id != null"
                >
                  Copy link chat
                  <i class="el-icon-document-copy"></i>
                </button>
              </div>
            </div>
          </template>
          <template v-else>
            <span>@{{ group.name }} | <span v-if="group.group_teacher && group.group_teacher.teacher">
              @{{ `${group.group_teacher.teacher.last_name} ${group.group_teacher.teacher.first_name}` }}</span>
            </span>
            <div class="flex justify-between items-center">
              <div class="font-bold mr-5" :class="[group.userCount < group.size ? 'text-success' : 'text-red']">@{{ group.userCount }} / @{{ group.size }}</div>
              <div class="font-bold mr-5 a-cursor-pointer" @click="setPoint(getGroupUser(currentAddCourseData.userId, group.group_user))">Điểm @{{ getGroupUser(currentAddCourseData.userId, group.group_user).entry_point }}</div>
              <button v-if="@if (Auth::guard('admin')->user()->permission == 1) true @else false @endif || group.start_date > moment().add(5, 'days').format('YYYY-MM-DD') || group.start_date < moment().subtract(14, 'days').format('YYYY-MM-DD')" class="btn btn-danger mr-3 " @click="kick(currentAddCourseData.userId, group.id)"><i class="fa fa-trash mr-2"></i>Xoá</button>
              <button :disabled="activeLoading" v-if="!getGroupWatchExpiredStatus" class="btn btn-info" @click="activeOnline(currentAddCourseData.userId, group.id, currentAddCourseData.invoice.id)">
                <i v-if="activeLoading" class="fa fa-spinner fa-spin"></i>
                <i v-else class="fa fa-unlock mr-2"></i>
                Kích khoá online</button>
              <button
                      class="badge mr-5"
                      style="cursor: pointer"
                      title="Copy to link chat"
                      @click="copyLinkChat(group.group_chat_id)"
                      v-if="group.group_chat_id != null"
              >
                Copy link chat
                <i class="el-icon-document-copy"></i>
              </button>
            </div>
          </template>
        </div>
        <div v-if="!joinedGroupListByUser.length" class="text-danger mb-5">Chưa tham gia nhóm nào</div>
        <input v-model="currentAddCourseData.search" class="form-control" placeholder="Tìm kiếm nhóm" @keyup.enter="searchGroup" style="border-radius: 4px;" />
        <div class="flex justify-between items-center">
          <label class="font-bold mt-5 mb-5">Nhóm khả dụng</label>
          <label v-if="getGroupWatchExpiredStatus" class="font-bold mt-5 mb-5 text-red">Khoá online còn hạn tới @{{ getGroupWatchExpiredStatus }}</label>
        </div>
        <div v-for="group in groupList"
             style="padding: 5px 15px; border: 1px solid #ccc; margin-bottom: 5px; border-radius: 4px;"
             class="flex justify-between items-center"
        >
          <template v-if="currentAddCourseData.invoice.product_type === 'offline'">
            <div v-if="group" class="w-full flex justify-between items-center">
              <span :id="`group-${group.id}`">
                <b v-for="teacher in group.teachers" :key="`teacher-${teacher.id}`">@{{ teacher.last_name }} @{{ teacher.first_name }} | </b>
                <span><b>N@{{ 6 - group?.level_of_n }}</b> | </span>
                <span><b>@{{ group?.info[0]?.room?.room_name }}</b> | </span>
                <span><b>@{{ _.uniq(group?.info[0]?.periods).map(o => o.period_code).join(',') }}</b> | </span>
                <span><b>@{{ group?.info.map(o => o.day_id + 2).join(',') }}</b> | </span>
                <span>Sĩ số: <b>@{{ group.students.length }}</b> | </span>
                <span>Số buổi: <b>@{{ group.total }}</b> | </span>
                <span>Giá: <b>@{{ group.cost * 1000 | decimal}}</b> | </span>
                <span>Từ <b class="text-success">@{{ group.date_start | readableDate }}</b> đến <b class="text-danger">@{{ group.date_end | readableDate }}</b></span>
              </span>
              <div>
                <button :disabled="addUserLoading" class="btn btn-success mr-2" @click="addUser(currentAddCourseData.invoice.id, currentAddCourseData.userId, group.id)">
                  <i v-if="addUserLoading" class="fa fa-spinner fa-spin"></i>
                  Thêm
                </button>
                <button :disabled="addUserLoading" v-if="!getGroupWatchExpiredStatus" class="btn btn-info mr-2" @click="addUser(currentAddCourseData.invoice.id, currentAddCourseData.userId, group.id, true)">
                  <i v-if="addUserLoading" class="fa fa-spinner fa-spin"></i>
                  Thêm
                  <i class="fa fa-plus ml-2 mr-2"></i>
                  <span>Kích hoạt</span>
                </button>
                <button
                        class="badge mr-5"
                        style="cursor: pointer"
                        title="Copy to link chat"
                        @click="copyLinkChat(group.group_chat_id)"
                        v-if="group.group_chat_id != null"
                >
                  Copy link chat
                  <i class="el-icon-document-copy"></i>
                </button>
              </div>
            </div>
          </template>
          <template v-else>
            <span :id="`group-${group.id}`">@{{ group.name }} <span v-if="group.group_teacher && group.group_teacher.teacher"> |
              @{{ `${group.group_teacher.teacher.last_name} ${group.group_teacher.teacher.first_name}` }}</span>
              <span v-if="group.start_date"> | @{{ group.start_date }}</span>
              <span v-if="group.shift_type" class="font-weight-bold"> | @{{ `${group.shift_type}`.split('').join(', ') }}</span>
              <span v-if="checkDuplicate(group.group_user)?.length" class="font-bold text-red-600"> | Trùng @{{ checkDuplicate(group.group_user).join(', ') }}</span>
            </span>

            <div class="flex justify-between items-center">
              <div class="font-bold mr-5" :class="[group.userCount < group.size ? 'text-success' : 'text-red']">@{{ group.userCount }} / @{{ group.size }}</div>
              <div class="badge mr-5" :class="getGroupStatusClass(group.status)">@{{ getGroupStatus(group.status) }}</div>
              {{--            <div v-if="joinedGroupListByUser.length" class="text-red font-bold">Học viên đang có lớp ở cấp độ này</div>--}}
              <div v-if="group.userCount >= group.size" class="text-red font-bold">Nhóm đã đầy</div>
              <div v-else-if="@if (Auth::guard('admin')->user()->permission == 1) true @else false @endif || checkDidAdd(group)" class="flex">
                <button :disabled="addUserLoading" class="btn btn-success mr-2" @click="addUser(currentAddCourseData.invoice.id, currentAddCourseData.userId, group.id)">
                  <i v-if="addUserLoading" class="fa fa-spinner fa-spin"></i>
                  Thêm</button>
                <button :disabled="addUserLoading" v-if="!getGroupWatchExpiredStatus" class="btn btn-info mr-2" @click="addUser(currentAddCourseData.invoice.id, currentAddCourseData.userId, group.id, true)">
                  <i v-if="addUserLoading" class="fa fa-spinner fa-spin"></i>
                  Thêm
                  <i class="fa fa-plus ml-2 mr-2"></i>
                  <span>Kích hoạt</span>
                </button>
                {{--              <button v-else class="btn btn-info mr-2" @click="addUser(currentAddCourseData.invoice.id, currentAddCourseData.userId, group.id, true)">--}}
                {{--                Thêm--}}
                {{--                <i class="fa fa-plus ml-2 mr-2"></i>--}}
                {{--                <span >Gia hạn</span>--}}
                {{--              </button>--}}
              </div>
              <button
                      class="badge mr-5"
                      style="cursor: pointer"
                      title="Copy to link chat"
                      @click="copyLinkChat(group.group_chat_id)"
                      v-if="group.group_chat_id != null"
              >
                Copy link chat
                <i class="el-icon-document-copy"></i>
              </button>
            </div>
          </template>
        </div>
      </div>
    </backend-modal>
    <backend-modal v-if="showNoteModal" @close="showNoteModal = false">
      <h3 slot="header"></h3>
      <div slot="body" class="text-center text-lg">
        @{{ currentNote }}
      </div>
    </backend-modal>
    <backend-modal v-if="showLogModal" @close="showLogModal = false">
      <h3 slot="header">Lịch sử đơn hàng</h3>
      <div slot="body">
        <div>
          <ul v-if="currentLog && currentLog.length">
            <li v-for="(log, idx) in currentLog" :key="`log-${log.idx}`">
              <b>@{{ log.timestamp | dateTimeToMinute }}</b> (<b>@{{ log.created_by }}</b>):  <b> @{{ subjects[log.subject] }}</b>
              <template v-if="log.subject !== 'invoice_creation'">
                <template v-if="log.subject == 'user_attachment'">
                  <a :href="`${url}/backend/community/users?group_id=${log.to}`" target="_blank"><b>@{{ log.to }}</b></a>
                </template>
                <template v-else-if="log.subject == 'user_detachment'">
                  <a :href="`${url}/backend/community/users?group_id=${log.from}`" target="_blank"><b>@{{ log.from }}</b></a>
                </template>
                <template v-else-if="log.subject == 'online_course_activation'">
                  <b>@{{ log.target }}</b>, ngày hết hạn: <b>@{{ log.to | dateTimeToMinute }}</b>
                </template>
                <template v-else>
                  trường <b>@{{ getColumn(log.column) }}</b> từ <b>@{{ getLogFrom(log) }}</b> thành <b>@{{ getLogTo(log) }}</b>
                </template>
              </template>
            </li>
          </ul>
        </div>
      </div>
    </backend-modal>
    <backend-modal v-if="showAddInvoiceForm" @close="closeAddInvoiceForm">
      <h3 slot="header">Tạo đơn @{{ formData.isVip ? formData.isOnline ? 'VIP' : 'Offline' : 'Online' }}</h3>
      <div slot="body">
        <div>
          <h4 class="text-green-700">Thông tin khách hàng</h4>
          <div class="flex justify-between" style="flex-wrap: wrap">
            <div class="form-group w-half">
              <label>Email<span v-if="currentCombo" class="text-red">*</span></label>
              <input class="form-control" v-model="formData.email" placeholder="VD: <EMAIL>" @blur="getUser(null)" :disabled="isReservationCompleted(formData.logs) || (!formData.isOnline && formData.id)" />
              <div v-for="error in formError.email" v-if="formError.email" class="text-red">
                @{{ error }} <span v-if="error == 'Người dùng không tồn tại'" class="a-cursor-pointer text-info font-bold" @click="createUser">Tạo mới</span>
              </div>
            </div>
            <div class="form-group w-half" v-if="currentAddCourseData.isCreateUser">
              <label>Password<span class="text-red">*</span></label>
              <input class="form-control" type="password" v-model="formData.password" />
              <div v-for="error in formError.password" v-if="formError.password" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half">
              <label>Họ và tên<span v-if="formData.isVip && formData.isOnline || hasBook" class="text-red">*</span></label>
              <input class="form-control" v-model="formData.fullname" placeholder="VD: Nguyễn Văn A" />
              <div v-for="error in formError.fullname" v-if="formError.fullname" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half">
              <label>Số điện thoại<span v-if="!formData.isOnline || hasBook && bookData.location != 'jp'" class="text-red">*</span></label>
              <input class="form-control" v-model="formData.phone" placeholder="VD: 0912113114" :disabled="!formData.isOnline && formData.id" />
              <div v-for="error in formError.phone" v-if="formError.phone" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half">
              <label>Link Facebook<span v-if="formData.isVip && formData.isOnline" class="text-red">*</span></label>
              <input class="form-control" v-model="formData.facebook" placeholder="VD: https://www.facebook.com/nguoimevotoi/" />
              <div v-for="error in formError.facebook" v-if="formError.facebook" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half">
              <label>Link chăm sóc (Facebook, Pancakes,...)<span v-if="formData.isOnline" class="text-red">*</span></label>
              <input class="form-control" v-model="formData.chat" placeholder="" />
              <div v-for="error in formError.chat" v-if="formError.chat" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="!hasBook">
              <label>Địa chỉ</label>
              <input class="form-control" v-model="formData.address" placeholder="" />
              <div v-for="error in formError.address" v-if="formError.address" class="text-red">@{{ error }}</div>
            </div>
          </div>
          <h4 class="text-green-700">Sản phẩm</h4>
          <div class="flex justify-between" style="flex-wrap: wrap">
            <div class="form-group w-half">
              <label>Combo<span class="text-red">*</span></label>
              <v-select
                      v-if="!formData.id || (['vip_combo', 'offline'].includes(formData.product_type) && !formData.active_time)"
                      :options="comboes"
                      v-on:input="priceCalculate"
                      label="name"
                      v-model="formData.comboId"
              ></v-select>
              <div v-else>
                <div>
                  <span>@{{ currentCombo?.name }}</span>
                </div>
              </div>
              <div v-for="error in formError.comboId" v-if="formError.comboId" class="text-red">@{{ error }}</div>
            </div>
            <!-- Start the book data block-->
            <div class="form-group w-half">
              <label class="text-black">Sách<span class="text-red">*</span></label>
              <div class="border border-gray-200 border-dashed rounded">
                <v-select
                        :disabled="formData.id && formData.paidAt && !adminRole"
                        v-on:input="priceCalculate"
                        multiple
                        :options="bookList"
                        label="name"
                        v-model="formData.bookIds">
                </v-select>
                <div class="book-container-multitrip">
                  <div class="flex mt-2 w-full text-black bg-gray-200 p-1 rounded" v-if="hasBook">
                    <div class="w-8/12 mr-1">
                      <label class="m-0">Tên sách</label>
                    </div>
                    <div class="w-2/12 mr-1">
                      <label class="m-0">Kho</label>
                    </div>
                    <div class="w-2/12 mr-1">
                      <label class="m-0">Giá sách</label>
                    </div>
                    <div class="w-2/12">
                      <label class="m-0">SL</label>
                    </div>
                  </div>
                  <div class="flex mt-2 w-full" v-for="(book, key) in formData.bookIds">
                    <div class="w-8/12 mr-1">
                      <input type="text" :value="book.name" disabled class="form-control w-full">
                    </div>
                    <div class="w-2/12 mr-1">
                      <input type="text" :value="book.inventory" disabled class="form-control w-full">
                    </div>
                    <div class="w-2/12 mr-1">
                      <input type="text" :value="book.price" disabled class="form-control w-full">
                    </div>
                    <div class="w-2/12">
                      <input type="number" v-model="formData.bookIds[key].quantity" class="form-control w-full" min="1"
                             :disabled="formData.id && formData.paidAt && !adminRole"
                             @change="priceCalculate">
                      {{--                    <!-- :disabled="formData.id && formData.paidAt -->--}}
                    </div>
                  </div>
                </div>
              </div>
              <div v-for="error in formError.bookIds" v-if="formError.bookIds" class="text-red">@{{ error }}</div>
            </div>
            <!-- Book refund -->
            <div class="form-group w-half" v-if="formData.id && formData.bookIds.length">
              <label class="text-black">Sách hoàn lại</label>
              <v-select
                      v-on:input="changeBookStatus()"
                      multiple
                      :options="bookList"
                      label="name"
                      v-model="formData.refundBooks">
              </v-select>
              <div class="flex mt-2 w-full" v-for="(refund, fkey) in formData.refundBooks">
                <div class="w-10/12 mr-1">
                  <input type="text" :value="refund.name" disabled class="form-control w-full">
                </div>
                <div class="w-2/12">
                  <input type="number" v-model="formData.refundBooks[fkey].quantity" class="form-control w-full" min="1">
                </div>
              </div>
              <div class="flex mt-2 w-full">
                <div class="w-6/12 mr-1">
                  <label class="text-black">Phí ship hoàn</label>
                  <input type="number" v-model="bookData.refundShip" class="form-control w-full">
                </div>
                <div class="w-6/12">
                  <label class="text-black">Lỗi do</label>
                  <select class="form-control" v-model="bookData.refundFault">
                    <option value="">Chọn lỗi</option>
                    <option value="sale">Do Sale</option>
                    <option value="post">Do Bưu Điện</option>
                    <option value="student">Do Học Viên</option>
                    <option value="sender">Do Người Gửi</option>
                  </select>
                  <div v-for="error in formError['bookData.refundFault']" v-if="formError['bookData.refundFault']" class="text-red">@{{ error }}</div>
                </div>
              </div>
            </div>
            <!-- End book refund -->
            <div class="form-group w-half" v-if="hasBook">
              <label class="text-black">Địa chỉ<span class="text-red">*</span></label>
              <input class="form-control" v-model="bookData.address" placeholder="VD: 20 Lê Văn Lương ..." />
              <div v-for="error in formError['bookData.address']" v-if="formError['bookData.address']" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="hasBook">
              <label class="text-black">Mã bưu điện</label>
              <input class="form-control" v-model="bookData.postCode" placeholder="VD: 204-009" />
              <div v-for="error in formError['bookData.postCode']" v-if="formError['bookData.postCode']" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="hasBook">
              <label class="text-black">Phí vận chuyển</label>
              <input class="form-control" v-model="bookData.price" placeholder="Phí vận chuyển" v-on:input="priceCalculate"/>
              <div v-for="error in formError['bookData.price']" v-if="formError['bookData.price']" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="hasBook">
              <label class="text-black">Người trả phí(Vận chuyển)</label>
              <select class="form-control" v-model="bookData.payer">
                <option value="">Chọn</option>
                <option value="hv">Học viên</option>
                <option value="dmr">Dungmori</option>
                <option value="sale">Sale</option>
              </select>
              <div v-for="error in formError['bookData.payer']" v-if="formError['bookData.payer']" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="hasBook">
              <label class="text-black">Vị trí</label>
              <select class="form-control" v-model="bookData.location">
                <option value="">Chọn</option>
                <option value="vn">Việt Nam</option>
                <option value="jp">Nhật Bản</option>
              </select>
              <div v-for="error in formError['bookData.location']" v-if="formError['bookData.location']" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="hasBook">
              <label class="text-black">Trạng thái sách</label>
              <select class="form-control" v-model="bookData.status">
                <option value="packing">Chuẩn bị</option>
                <option value="sended">Đã gửi</option>
                <option value="received">Đã nhận</option>
                <option value="return">Hoàn lại</option>
              </select>
              <div v-for="error in formError['bookData.status']" v-if="formError['bookData.status']" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="hasBook && formData.id">
              <label class="text-black">Đơn sửa</label>
              <select class="form-control" v-model="bookData.modifyStatus" :disabled="formData.id && formData.paidAt && !adminRole">
                <option value="">Chọn</option>
                <option value="none">Chưa sửa</option>
                <option value="modify">Đã sửa</option>
              </select>
              <div v-for="error in formError['bookData.modifyStatus']" v-if="formError['bookData.modifyStatus']" class="text-red">@{{ error }}</div>
            </div>
            <!-- End the book data block-->
          </div>
          <h4 class="text-green-700">Thanh toán</h4>
          <div class="flex justify-between" style="flex-wrap: wrap">
            <div class="form-group w-half" v-if="formData.comboId || formData.bookIds">
              <label>Giá<span class="text-red">*</span></label>
              <input class="form-control" v-model="formData.price" placeholder="Giá combo" />
            </div>
            <div class="form-group w-half">
              <label>Đơn vị<span class="text-red">*</span></label>
              <select v-model="formData.currency" class="form-control">
                <option value="">Chọn một hình thức thanh toán</option>
                <option value="vnd">đ</option>
                <option value="jpy">¥</option>
              </select>
            </div>
            <div class="form-group w-half">
              <label>Tiền chiết khấu</label>
              <input class="form-control" v-model="formData.discountMoney"/>
            </div>
            {{--          <div class="form-group w-half">--}}
            {{--            <label>Số tiền giảm giá (bỏ trống nếu không giảm)</label>--}}
            {{--            <input class="form-control" v-model="formData.coupon" placeholder="" />--}}
            {{--            <div v-for="error in formError.coupon" v-if="formError.coupon" class="text-red">@{{ error }}</div>--}}
            {{--          </div>--}}
            <div class="form-group w-half">
              <label>Hình thức thanh toán<span class="text-red">*</span></label>
              <select v-model="formData.paymentMethod" class="form-control">
                <option value="">Chọn một hình thức thanh toán</option>
                <option v-for="paymentMethod in paymentMethodList" :value="paymentMethod.id">@{{ paymentMethod.name }}</option>
              </select>
              <div v-for="error in formError.paymentMethod" v-if="formError.paymentMethod" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half">
              <label>Tiền thực thu</label>
              <input class="form-control" v-model="formData.paidMoney"/>
              <div v-for="error in formError.paidMoney" v-if="formError.paidMoney" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half" v-if="!formData.isOnline">
              <label>Cơ sở<span class="text-red">*</span></label>
              <select v-model="formData.department" class="form-control">
                <option value="">Chọn cơ sở</option>
                <option v-for="department in departmentList" :value="department.id">@{{ department.name }}</option>
              </select>
              <div v-for="error in formError.department" v-if="formError.department" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half">
              <label>Ngày thanh toán</label>
              <date-picker v-model="formData.paidAt" input-class="form-control w-full" :confirm="true" lang="en" type="datetime" format="YYYY-MM-DD HH:mm:ss" value-type="YYYY-MM-DD HH:mm:ss"></date-picker>

              <div v-for="error in formError.paidAt" v-if="formError.paidAt" class="text-red">@{{ error }}</div>
            </div>
            {{--          <div class="form-group w-half">--}}
            {{--            <label>Sale tư vấn<span class="text-red">*</span></label>--}}
            {{--            <select v-model="formData.sale" class="form-control">--}}
            {{--              <option value="">Chọn một sale</option>--}}
            {{--              <option v-for="sale in saleList" :value="sale.id">@{{ sale.name }}</option>--}}
            {{--            </select>--}}
            {{--            <div v-for="error in formError.sale" v-if="formError.sale" class="text-red">@{{ error }}</div>--}}
            {{--          </div>--}}



          </div>
          <h4 class="text-green-700">Quà tặng</h4>
          <div class="form-group w-half" v-if="!formData.isOnline">
            <label for="giftBag" class="m-0">Tặng túi</label>
            <input type="checkbox" v-model="offlineData.gift.bag" id="giftBag" class="mr-5">
            <label for="giftBook" class="m-0">Tặng sách</label>
            <input type="checkbox" v-model="offlineData.gift.book" id="giftBook">
          </div>
          <div v-if="formData.product_type !== 'offline'" class="form-group w-half">
            <template v-if="!formData.id || (['vip_combo', 'offline'].includes(formData.product_type) && !formData.active_time)">
              <label>Tặng khóa học theo combo</label>
              <v-select
                  :options="comboList"
                  v-on:input="comboSelected"
                  label="name"
                  v-model="formData.bonusComboId"
              ></v-select>
            </template>
            <label class="mt-2">Tặng khoá học</label>
            <div v-for="bonus in bonuses" :key="`invoice-bonus-${bonus.courseId}`" class="flex items-center gap-3 mt-2">
              <select class="form-control w-3/4" name="courseList" id="courseList" v-model="bonus.courseId">
                <option value=""></option>
                <option v-for="course in courses" :value="course.id">@{{ course.name }}</option>
              </select>
              <div class="w-1/4 flex items-center gap-2">
                <input type="number" class="form-control" min="0" max="1000" v-model="bonus.days">
                Ngày
              </div>
            </div>
            <div v-for="error in formError.bonuses" v-if="formError.bonuses" class="text-red mt-2">@{{ error }}</div>
          </div>
          <h4 class="text-green-700">Thông tin khác</h4>
          <div class="flex justify-between" style="flex-wrap: wrap">
            <div class="form-group w-half">
              <label>Ghi chú</label>
              <input class="form-control" v-model="formData.note" placeholder="Ghi chú về thanh toán, đặt cọc, tài khoản, điểm đầu vào" />
              <div v-for="error in formError.note" v-if="formError.note" class="text-red">@{{ error }}</div>
            </div>
            <div class="form-group w-half">
              <label>Thành tiền</label>
              <div class="flex items-center font-bold">@{{ parseInt(formData.price || 0) - parseInt(formData.coupon || 0) }}@{{ getCurrencySymbol(formData.currency) }}</div>
              <div v-if="!formData.applied_reward" class="flex flex-wrap items-center gap-2">
                <div class="text-xs leading-0">Voucher khả dụng: </div>
                <div
                        v-for="reward in userAvailableRewards"
                        class="py-0 px-1 border border-red-500 text-red-500 text-xs leading-0 cursor-pointer hover:bg-red-50 select-none"
                        :class="[
                    formData.rewardId == reward.pivot.id ? 'bg-red-100' : ''
                  ]"
                        @click="useReward(reward)">
                  -@{{ reward.type === 'discount_money' ? reward.value / 1000 : reward.value }}@{{ reward.type === 'discount_money' ? 'k' : '%' }}
                </div>
              </div>
              <div v-else class="text-xs">
                <span class="font-semibold">Đã áp dụng voucher:</span>
                <span class="py-0 px-1 border border-red-500 text-red-500 text-xs leading-0 cursor-pointer hover:bg-red-50 select-none">-@{{ formData.applied_reward?.reward?.type === 'discount_money' ? formData.applied_reward?.reward?.value / 1000 : formData.applied_reward?.reward?.value }}@{{ formData.applied_reward?.reward?.type === 'discount_money' ? 'k' : '%' }}</span>
              </div>
            </div>
            <div class="form-group w-full" v-if="!formData.id && formData.isVip && !formData.isOnline">
              <div class="flex justify-between items-center">
                <label>Xếp lớp </label>
                <label v-if="formData.isVip" for="activeOnline" class="font-bold">
                  <div class="flex items-center">
                    <input type="checkbox" id="activeOnline" name="activeOnline" v-model="formData.activeOnline" class="mr-2">
                    Kích hoạt khoá online
                  </div>
                  <div v-for="owner in currentOwnerWatchExpired" class="text-red">(@{{ owner.title }} đang còn hạn tới @{{ owner.watch_expired_day }})</div>
                </label>
              </div>
              <div style="max-height: 170px;overflow-y: scroll;margin-top: 5px;">
                <div v-for="group in offlineGroups"
                     style="padding: 5px 15px; margin-bottom: 5px; border-radius: 4px;cursor: pointer;"
                     :style="{border: formData.groupId.includes(group.id) ? '2px solid red' : '1px solid #ccc'}"
                     class="flex justify-between items-center select-none"
                     @click="toggleAddUser(group.id)"
                >
                <span :id="`group-${group.id}`">
                  <b v-for="teacher in group.teachers" :key="`teacher-${teacher.id}`">@{{ teacher?.last_name }} @{{ teacher?.first_name }} | </b>
                  <span><b>N@{{ 6 - group?.level_of_n }}</b> | </span>
                  <span><b>@{{ group?.info[0]?.room?.room_name }}</b> | </span>
                  <span><b>@{{ _.uniq(group?.info[0]?.periods).map(o => o.period_code).join(',') }}</b> | </span>
                  <span><b>@{{ group?.info.map(o => o.day_id + 2).join(',') }}</b> | </span>
                  <span>Sĩ số: <b>@{{ group?.students?.length }}</b> | </span>
                  <span>Số buổi: <b>@{{ group?.total }}</b> | </span>
                  <span>Giá: <b>@{{ group?.cost * 1000 | decimal}}</b> | </span>
                  <span>Từ <b class="text-success">@{{ group?.date_start | readableDate }}</b> đến <b class="text-danger">@{{ group?.date_end | readableDate }}</b></span>
                </span>
                </div>
              </div>
            </div>
            <div class="form-group w-full" v-if="!formData.id">
              <div class="flex justify-between items-center">
                <label>Xếp lớp <b>(@{{ currentFormEntryPoint ? `Điểm đầu vào: ${currentFormEntryPoint.grade}`: 'Chưa có điểm đầu vào' }})</b></label>
                <label v-if="formData.isVip" for="activeOnline" class="font-bold">
                  <div class="flex items-center">
                    <input type="checkbox" id="activeOnline" name="activeOnline" v-model="formData.activeOnline" class="mr-2">
                    Kích hoạt khoá online
                  </div>
                  <div v-for="owner in currentOwnerWatchExpired" class="text-red">(@{{ owner.title }} đang còn hạn tới @{{ owner.watch_expired_day }})</div>
                </label>
              </div>
              <input v-model="search" class="form-control" placeholder="Tìm kiếm nhóm" @keyup.enter="searchGroupByCourse" style="border-radius: 4px;" />
              <div style="max-height: 170px;overflow-y: scroll;margin-top: 5px;">
                <div v-for="group in groupListCombo"
                     style="padding: 5px 15px; margin-bottom: 5px; border-radius: 4px;cursor: pointer;"
                     :style="{border: formData.groupId.includes(group.id) ? '2px solid red' : '1px solid #ccc'}"
                     class="flex justify-between items-center select-none"
                     @click="toggleAddUser(group.id)"
                >
              <span :id="`group-${group.id}`">@{{ group.name }} <span v-if="group.group_teacher && group.group_teacher.teacher"> |
              @{{ `${group.group_teacher.teacher.last_name} ${group.group_teacher.teacher.first_name}` }}</span>
              <span v-if="group.start_date"> | @{{ group.start_date }}</span>
              <span v-if="group.shift_type" class="font-weight-bold"> | @{{ `${group.shift_type}`.split('').join(', ') }}</span></span>
                  <div class="flex justify-between items-center">
                    <div class="font-bold mr-5" :class="[group.userCount < group.size ? 'text-success' : 'text-red']">@{{ group.userCount }} / @{{ group.size }}</div>
                    <div class="badge mr-5" :class="getGroupStatusClass(group.status)">@{{ getGroupStatus(group.status) }}</div>
                    {{--            <div v-if="joinedGroupListByUser.length" class="text-red font-bold">Học viên đang có lớp ở cấp độ này</div>--}}
                    <div v-if="group.userCount >= group.size" class="text-red font-bold">Nhóm đã đầy</div>
                    <button
                            class="badge mr-5"
                            style="cursor: pointer"
                            title="Copy to link chat"
                            @click="copyLinkChat(group.group_chat_id)"
                            v-if="group.group_chat_id != null"
                    >
                      Copy link chat
                      <i class="el-icon-document-copy"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div v-for="error in formError.note" v-if="formError.note" class="text-red">@{{ error }}</div>
            </div>
          </div>
        </div>
        <div>
          <button :disabled="createInvoiceLoading" v-if="!formData.id" class="btn btn-block btn-success p-5 mt-5 w-full" @click="createInvoice">
            <i v-if="createInvoiceLoading" class="fa fa-spinner fa-spin"></i>
            Tạo đơn hàng
          </button>
          <button :disabled="createInvoiceLoading" v-else class="btn btn-block btn-success p-5 mt-5 w-full" @click="updateInvoice">
            <i v-if="createInvoiceLoading" class="fa fa-spinner fa-spin"></i>
            Sửa đơn hàng
          </button>
        </div>
      </div>
    </backend-modal>
    <backend-modal v-if="showInvoiceDept" @close="closeInvoiceDept">
      <h3 slot="header">Đơn #@{{ depositInvoice.id }}</h3>
      <div slot="body">
        <table style="width: 100%">
          <thead>
          <th width="50px">Mã số</th>
          <th class="text-center">Sản phẩm</th>
          <th width="100px">Hình thức TT</th>
          <th>Khách hàng</th>
          <th class="text-center">Lớp</th>
          <th>Sale</th>
          <th class="text-left">Phí sách</th>
          <th class="text-center">G.trị đơn hàng</th>
          <th class="text-center">Thực thu</th>
          <th class="text-center">Nợ</th>
          <th class="text-center">Trạng thái</th>
          <th>Thời gian</th>
          <th class="text-center" width="100px"></th>
          </thead>
          <tbody v-if="!loading">
          <tr v-if="depositInvoice.relevant_invoices.length == 0">
            <td colspan="12" class="text-center"> Không có dữ liệu</td>
          </tr>
          <tr v-for="(result, index) in relevantInvoices" :key="result.id" v-if="results.length != 0" style="border-bottom: 1px solid gray; padding-bottom: 5px;">
            <td>@{{ result.id }}</td>
            <td class="text-center">
              <div>
                <span class="font-bold label new-label green-label" v-if="result.book_info || result.book"><i class="fa fa-book" aria-hidden="true"></i></span>
                <span class="font-bold" v-if="result.product_type === 'course'">Khoá học</span>
                <span class="font-bold" v-if="result.product_type === 'combo'">Combo</span>
                <span v-if="result.product_type === 'vip_course'"><span class="font-bold label new-label red-label">VIP</span> Khoá học</span>
                <span v-if="result.product_type === 'vip_combo'"><span class="font-bold label new-label red-label">VIP</span>  Combo</span>
                <span v-if="result.product_type === 'offline'"><span class="font-bold label new-label blue-label">Offline</span>  Combo</span>
                @{{ result.product_name || '--' }}
              </div>
              <div v-if="result.bonus">
                Tặng @{{ result.bonus?.course?.name || '--' }} @{{ result.bonus?.days || '--' }} ngày
              </div>
              <div>
                <a :href="`${url}/checkout/${result.uuid}`" target="_blank" class="font-bold">@{{ result.uuid }}</a>
              </div>
            </td>
            <td style="font-size: 12px; font-weight: bold">
              <div>@{{ result.payment_method_id ? result.payment_method?.name : '--' }}</div>
              <div>@{{ result.voucher ? result.voucher?.key : '--' }}</div>
            </td>
            <td>
              <div>@{{ result.user?.id || '--' }} • @{{ result.info_contact ? result.info_contact.name : (result.user ? result.user.name : '--') }}</div>
              <div>
                @{{ result.user ? result.user.email : (result.info_contact?.email || '--') }}
                <a v-if="result.user_id && (result.combo || result.vip_combo)" class="login_user"
                   :href="`${url}/backend/user/login/${result.user_id}`" target="_blank"><i class="fa fa-sign-in"></i>
                </a>
              </div>
              <div>@{{ result.department?.name }}</div>
              <div v-if="result.info_contact && result.info_contact.phone">
                <a :href="`tel:${result.info_contact.phone}`">
                  <i class="fa fa-phone mr-3"></i>@{{ result.info_contact ? result.info_contact.phone : '--' }}
                </a>
              </div>
              <div>
                <template v-if="result.info_contact && result.info_contact.address">
                  @{{ result.info_contact.address }}
                </template>
                <template v-else-if="result.book_info && result.book_info.address">
                  @{{ result.book_info.address }}
                </template>
              </div>
              <div class="flex items-center">
                <a v-if="result.info_contact && result.info_contact.facebook" :href="result.info_contact ? result.info_contact.facebook : ''" target="_blank" class="mr-2">
                  <i class="fa fa-facebook-square"></i>
                </a>
                <div v-if="result.user && (result.combo || result.vip_combo)">
                  <a v-if="result.user.conversation" :href="'{{url('/backend/chat#')}}'+ result.user.conversation.id" target="_blank" class="mr-2">
                    <i class="fa fa-comment"></i>
                  </a>
                  <i v-else class="fa fa-comment mr-2" style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                     @click="initConversation(result.user.id)"
                  ></i>
                </div>
                <a v-if="result.info_contact && result.info_contact.chat" :href="result.info_contact.chat" target="_blank" class="mr-2">
                  <i class="fa fa-codiepie"></i>
                </a>
              </div>
              <div class="font-bold text-red text-md" @click="showNote(result.note)">
                @{{ result.note?.slice(0,40) }} <span v-if="result.note?.length > 40">...</span>
              </div>
            </td>
            <td class="text-center">
              <div v-if="['vip_combo', 'vip_course', 'offline'].includes(result.product_type) && result.vip_combo && result.product_id !== result.book_id">
                <span
                        v-for="course in result.vip_combo.courses"
                        class="badge a-cursor-pointer mr-1"
                        :class="[getLevelStatus(result, course.level) === -1 ? 'badge-gray' : 'badge-info']"
                        @click="showAddGroup(result, result.user_id, course.level)"
                >
                  @{{ course.name }}
                </span>
              </div>
            </td>
            <td>@{{ result.sale ? result.sale.name : '--' }}</td>
            <td class="text-left">
              <div v-if="result.book_info">
                <div>Phí chuyển: <span class="text-success"> @{{ result.book_info.price | decimal }}</span></div>
                <div>Phí hoàn: <span class="text-success"> @{{ result.book_info.refundShip | decimal }}</span></div>
              </div>
              <div v-else>--</div>
            </td>
            <template>
              <td class="text-center">
                <div class="text-blue-500 font-semibold">
                  @{{ result.price | decimal }}
                  @{{ getCurrencySymbol(result.currency) }}
                </div>
              </td>
            </template>
            <td class="text-center font-bold text-success">@{{ result.paid_money | decimal }} @{{ getCurrencySymbol(result.currency) }}</td>
            <template>
              <td class="text-center">
                <div class="text-red-500 font-semibold" @click="depositInvoice = result; showInvoiceDept = true">
                  @{{ result.price - result.discount_money - result.paid_money | decimal }}
                  @{{ getCurrencySymbol(result.currency) }}
                </div>
              </td>
            </template>
            {{--          <td class="font-bold">--}}
            {{--            <div v-if="['course', 'combo'].includes(result.product_type)">--}}
            {{--              <div v-if="result.coupon">--}}
            {{--                <div>Giảm @{{ result.coupon.value }}% - @{{ result.coupon.code}}</div>--}}
            {{--                <div>@{{ result.coupon.email }}</div>--}}
            {{--              </div>--}}
            {{--              <div v-else>@{{ result.coupon_id || '--' }}</div>--}}
            {{--            </div>--}}
            {{--            <div v-else>@{{ result.coupon_id || '--' }}</div>--}}
            {{--          </td>--}}
            <td>
              <div v-if="['vip_combo', 'offline'].includes(result.product_type)">
                <div v-if="result.invoice_status == 'completed' && result.payment_status == 'paid' && result.active_time" class="label new-label green-label" >Hoàn thành</div>
                <div v-else-if="result.invoice_status == 'new' && result.payment_status == 'unpaid' && !result.active_time" class="label new-label orange-label">Chờ xử lý...</div>
                <div v-else-if="result.invoice_status == 'canceled'" class="label new-label orange-label">Chờ xử lý...</div>
                <div v-else class="flex flex-column">
                  <div v-if="!result.active_time" class="label new-label orange-label mt-2" >Chờ kích hoạt</div>
                  <div v-else class="label new-label green-label mt-2" >Đã kích hoạt</div>
                  <div v-if="result.invoice_status != 'completed'" class="label new-label orange-label mt-2" >Chờ thêm lớp</div>
                  <div v-else class="label new-label green-label mt-2" >Đã thêm lớp</div>
                  <div v-if="result.payment_status == 'unpaid'" class="label new-label orange-label mt-2" >Chờ thanh toán</div>
                  <div v-else class="label new-label green-label mt-2" >Đã chuyển tiền</div>
                </div>
              </div>
              <div v-else>
                <span v-if="result.invoice_status == 'completed' && result.payment_status == 'paid'" class="label new-label green-label" >Hoàn thành</span>
                <span v-else-if="result.invoice_status != 'completed'" class="label new-label orange-label" >Chờ kích hoạt</span>
                <span v-else-if="result.invoice_status == 'completed' && result.payment_status == 'unpaid'" class="label new-label blue-label" >Chờ thanh toán</span>
                <span v-else-if="result.invoice_status == 'new' && result.payment_status == 'unpaid'" class="label new-label orange-label">Chờ xử lý...</span>
                <span v-else-if="result.invoice_status == 'new' && result.payment_status == 'paid'" class="label new-label orange-label">Đã chuyển tiền</span>
                <span v-else class="label new-label red-label">Đã hủy</span>
              </div>
            </td>
            <td style="font-size: 12px; font-weight: bold;">
              <div>Tạo: @{{ result.created_at | dateTimeToMinute }}</div>
              <div>Thanh toán: @{{ result.paid_at | dateTimeToMinute }}</div>
              <div>Kích hoạt: @{{ result.active_time | dateTimeToMinute }}</div>
            </td>
            <td class="text-left">
              <div class="flex flex-column">
                <template v-if="['combo', 'course'].includes(result.product_type)">
                  <template v-if="result.invoice_status == 'new' || (result.invoice_status == 'completed' && result.payment_status == 'unpaid')">
                    <button :disabled="activeInvoiceLoading" v-if="result.product?.type && result.product?.type == 'book' && result.invoice_status == 'new'" class="label new-label green-label" @click="activeInvoice(result.id, 'book')">
                      Kích hoạt sách<i class="fa fa-caret-right" aria-hidden="true"></i>
                    </button>
                    <button
                            v-if="
                        result.product_type == 'course' ||
                        (result.product?.type &&
                        result.product?.type != 'book' &&
                        result.invoice_status == 'new')
                      "
                            :disabled="activeInvoiceLoading"
                            class="label new-label green-label"
                            @click="activeInvoice(result.id)"
                    >
                      Kích hoạt <i class="fa fa-caret-right" aria-hidden="true"></i>
                    </button>

                  </template>
                </template>
                <button
                        v-if="result.bonus"
                        :disabled="createInvoiceLoading || result.bonuses.findIndex(bonus => bonus.status == 1) == -1"
                        class="label new-label green-label mt-2"
                        @click="activeBonusCourse(result)"
                >
                  @{{ result.bonuses.findIndex(bonus => bonus.status == 1) == -1 ? 'Tặng khoá học' : 'Đã tặng' }} <i class="fa fa-caret-right" aria-hidden="true"></i>
                </button>
                <div class="label green-label new-label cursor-pointer mt-2" @click="invoiceDetail(result)" v-if="result.product_type == 'offline'">
                  Hoá đơn
                </div>
                <div class="label green-label new-label cursor-pointer mt-2"
                     @click="makeInvoicePaidMore(result)"
                     v-if="checkPaidMore(result)">
                  Nộp thêm
                </div>
                <div
                        v-if="[3, 14, 107, 69, 89, 144, 66].includes(adminId) || ((result.admin_active === 0 || adminId === result.admin_active)
                      && (result.invoice_status !== 'cancel'))"
                        class="label new-label blue-label cursor-pointer mt-2" @click="editInvoice(result)"
                >
                  Sửa
                </div>
                <div v-if="result.invoice_status === 'new'" class="label red-label new-label cursor-pointer mt-2" style="color: white;" @click="cancelInvoice(result.id)">Huỷ</div>
                <div
                        v-if="result.logs && result.logs.length"
                        class="label new-label orange-label cursor-pointer mt-2" @click="showLog(result.logs)"
                >
                  Lịch sử
                </div>
                @if(auth()->guard('admin')->check() && auth()->guard('admin')->user()->email == '<EMAIL>')
                  <div
                          class="label new-label orange-label cursor-pointer mt-2" @click="deleteInvoice(result.id)"
                  >
                    Xoá
                  </div>
                @endif
              </div>
            </td>
          </tr>
          </tbody>
          <tbody v-if="loading">
          <tr style="height: 60vh; padding: 20px 0; text-align: center" >
            <td colspan="14">
              <i class="fa fa-spinner fa-spin fa-3x"></i>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </backend-modal>
    <backend-modal v-if="showInvoiceDetail" @close="closeInvoiceDetail">
      <div id="main-print-invoice" slot="body">
        <div id="print-invoice">
          <img src="{{asset('assets/img/new_home/logo.png')}}" alt="logo" class="mt-4">
          <h2 class="text-center text-bold mt-4">PHIẾU THU BÁN HÀNG</h2>
          <div class="flex justify-between text-black">
            <div>
              <span><b>Họ tên khách hàng: </b>@{{ printInvoice.fullname }}</span>
              <span class="block mt-2 "><b>SĐT Khách hàng: </b>@{{printInvoice.phone}}</span>
            </div>
            <div class="w-1/2">
              <span><b>Ngày mua hàng: </b>@{{ printInvoice.createdAt | dateTimeToMinute }}</span>
              <span class="block mt-2 "><b>Mã đơn: </b>@{{ printInvoice.id }}</span>
            </div>
          </div>
          <div class="mt-5">
            <i> Lời đầu tiên, xin trân trọng cảm ơn quý khách hàng đã lựa chọn khóa học của Dũng Mori.
              Chúng tôi xin gửi đến quý khách hàng thông tin chi tiết của đơn hàng như sau:</i>
          </div>
          <table width="100%" class="text-center mt-8">
            <thead>
            <tr class="bg-gray-200 text-black uppercase text-bold">
              <td class="py-3">STT</td>
              <td class="py-3">Sản phẩm</td>
              <td class="py-3">SL</td>
              <td class="py-3">Thành tiền</td>
            </tr>
            </thead>
            <tbody class="text-black">
            <tr>
              <td class="py-2">1</td>
              <td class="py-2">@{{ printInvoice.product_name }}</td>
              <td class="py-2">1</td>
              <td class="py-2 text-right pr-2">@{{ printInvoice.price | decimal }}đ</td>
            </tr>
            <tr v-if="printInvoice.gift.book">
              <td class="py-2">2</td>
              <td class="py-2">Sách</td>
              <td class="py-2">1</td>
              <td class="py-2 text-right pr-2">0đ</td>
            </tr>
            <tr v-if="printInvoice.gift.bag">
              <td class="py-2">
                <span v-if="printInvoice.gift.book">3</span>
                <span v-else>2</span>
              </td>
              <td class="py-2">Túi</td>
              <td class="py-2">1</td>
              <td class="py-2 text-right pr-2">0đ</td>
            </tr>
            </tbody>
          </table>
          <div class="flex justify-end mt-2">
            <div class="w-80 text-black">
              <div class="flex justify-between"><b>Tổng thành tiền: </b><span>@{{ printInvoice.price | decimal }} đ</span></div>
              <div class="flex justify-between"><b>Tiền chiết khấu: </b><span>@{{ printInvoice.discountMoney | decimal }} đ</span></div>
              <div class="flex justify-between"><b>Thực thu: </b><span>@{{ printInvoice.paidMoney | decimal }} đ</span></div>
              <div class="flex justify-between"><b>Còn phải thu: </b><span>@{{printInvoice.oweMoney | decimal }} đ</span></div>
            </div>
          </div>
          <div class="text-black mt-10">
            <span class="block"><b>Số tiền bằng chữ: </b><i style="text-transform: capitalize;" v-if="printInvoice.paidMoney > 0">@{{ printInvoice.paidMoneyText }} Đồng</i></span>
            <span class="block"><b>Ghi chú: </b>@{{ printInvoice.note }}</span>
            <span class="block mt-2"><b>Lưu ý:</b> <i>Các khoản cọc và học phí sẽ không hoàn trả với lý do cá nhân.</i></span>
          </div>
          <div class="flex justify-between text-black w-[60%]" style="margin: 40px auto; ">
            <div class="text-center">
              <span><b>Người mua:</b></span>
              <span class="block">(Ký, họ tên)</span>
            </div>
            <div class="text-center">
              <span><b>Người lập phiếu:</b></span>
              <span class="block">(Ký, họ tên, đóng dấu)</span>
              <span>@{{ printInvoice.adminCreate }}</span>
            </div>
          </div>
        </div>
        <div class="mt-10">
          <div class="btn green-label text-white" @click="downloadInvoice()">Tải xuống & In</div>
        </div>
      </div>
    </backend-modal>
  </div>

  <script>
    const adminId = {!! Auth::guard('admin')->user()->id !!};
    const adminRole = {!! Auth::guard('admin')->user()->permission !!};
    const comboList = {!! $comboList !!};
    {{--const bookList = {!! $bookList !!};--}}
    const vipComboList = {!! $vipComboList !!};
    const courseList = {!! $courseList !!};
    const paymentMethodList = {!! $paymentMethodList !!};
    const saleList = {!! $saleList !!};
    const departmentList = {!! $departmentList !!};
  </script>
  <script>
    jQuery.browser = {};
    (function () {
      jQuery.browser.msie = false;
      jQuery.browser.version = 0;
      if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
        jQuery.browser.msie = true;
        jQuery.browser.version = RegExp.$1;
      }
    })();

    function setScrollContent() {
      if ($('#invoice_tbl_box') && $('#invoice_tbl')) {
        if ($('#invoice_tbl_box').width() < $('#invoice_tbl').width()) {
          $('#invoice_tbl_box').addClass('overflow-x-scroll');
          $('#invoice_tbl_box').height(window.innerHeight - 100);
        } else {
          $('#invoice_tbl_box').css('height','auto')
        }
      }
    }

    $(document).ready(function() {
      setTimeout(() => {
        setScrollContent();
      }, 1000);
      $(window).resize(function() {
        setScrollContent();
      });
    });
  </script>
  {{--    Local plugins--}}
  <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
  <script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
  <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
  <script src="{{ asset('plugin/vue/vue.min.js') }}"></script>
  <script src="{{ asset('plugin/datepicker/vue2-datepicker.min.js') }}"></script>
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
  {{--  <script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>--}}
  {{--  <link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}">--}}

  <script src="https://cdnjs.cloudflare.com/ajax/libs/vue-select/3.13.0/vue-select.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vue-select/3.13.0/vue-select.css">

  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://js.pusher.com/6.0/pusher.min.js"></script>
  <script src="{{ asset('/assets/backend/js/modal.js') }}?{{filemtime('assets/backend/js/modal.js')}}"></script>
  <script src="{{asset('plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
  <script src="{{asset('plugin/vue-router/vue-router.js')}}"></script>
  <script src="{{asset('assets/backend/js/vip/invoice_list.js')}}?{{filemtime('assets/backend/js/vip/invoice_list.js')}}&cache_js"></script>
  <style>
    h4 {
      margin: 2px auto;
    }
    .vs__dropdown-toggle {
      min-height: 39px;
    }
    .vs__search {
      font-size: 14px !important;
    }
    .form-group {
      margin-bottom: 5px;
    }
    .mx-datepicker {
      width: 100%;
    }
    .v-modal-body {
      margin-top: 0;
    }
    .mx-datepicker-popup {
      z-index: 9999;
    }
    .filterable-list__screen .form-control {
      border-radius: 6px;
    }
    .filterable-list__screen .btn {
      border-radius: 6px;
    }
    .filterable-list__tab {
      border-radius: 6px;
    }
    #print-invoice table td{
      border: 1px solid black;
      border-collapse:  collapse;
    }
    .v-modal-container {border-radius: 10px;}
    .v-modal-container::-webkit-scrollbar {display: none;}
    .v-modal-container {
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  </style>
@stop
