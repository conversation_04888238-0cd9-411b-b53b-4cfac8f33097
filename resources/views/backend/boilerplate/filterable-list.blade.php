@extends('backend._default.dashboard')

@section('description') Quản lý kết quả JLPT @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý kết quả JLPT @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
@stop

@section('content')
    <div class="filterable-list__screen" id="user--screen">
        <div class="filterable-list__filter">
            <div class="form-group">
                <label>ID Bài thi</label>
                <input type="text" class="form-control" placeholder="ID Bài thi" v-model="filter.id" @keyup.enter="applyFilter">
            </div>
            <div class="form-group">
                <label>SĐT</label>
                <select v-model="filter.mobile" class="form-control" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option value="1">Có</option>
                    <option value="-1">Không</option>
                </select>
            </div>
            <div style="display: flex; align-items: flex-end">
                <span style="margin-right: 10px; cursor: pointer" @click="resetFilter"><i class="fa fa-refresh"></i></span>
                <button class="btn btn-info" @click="applyFilter">Lọc</button>
                <button class="btn btn-success" @click="exportExcel" style="margin-left: 10px;">Xuất Excel</button>
            </div>

            <div class="form-group">
                <label>Thông tin</label>
                <select v-model="filter.certificate_info" class="form-control" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option value="1">Có thông tin</option>
                    <option value="-1">Vô danh tiểu tốt</option>
                </select>
            </div>
            <div class="form-group">
                <label>Từ ngày</label>
                <input type="text" class="form-control" name="time_from" id="time_from" placeholder="Chọn thời gian" v-model="filter.time_from" @change="onChangeDate($event)">
            </div>
            <div class="form-group">
                <label>Đến ngày</label>
                <input type="text" class="form-control" name="time_to" id="time_to" placeholder="Chọn thời gian" v-model="filter.time_to" @change="onChangeDate($event)">
            </div>
        </div>
        <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
            <span>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</span>
            <div>
                <!-- Nơi này để chứa các button thao tác -->
            </div>
        </div>
        <div class="filterable-list__list">
            <table>
                <thead>
                <th width="4%">ID</th>
                <th class="text-center" @click="sortTotalScore" style="cursor: pointer">Đỗ/Trượt <i class="fa fa-sort-up" v-if="filter.sort === 'asc'"></i> <i class="fa fa-sort-down" v-if="filter.sort === 'desc'"></i></th>
                </thead>
                <tbody v-if="!loading">
                <tr v-if="results.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(result, index) in results" :key="result.id" v-if="results.length != 0">
                    <td>
                        <div>@{{ result.id }}</div>
                    </td>

                </tr>
                </tbody>
                <tbody v-if="loading">
                <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                    <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="filterable-list__paginate">
            <div>
                Hiển thị
                <select v-model="filter.per_page" @change="applyFilter">
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                </select>
                trong số @{{ loading ? '----' : total_result}} kết quả
            </div>
            <paginate
                    {{--                    v-model="filter.page"--}}
                    :page-count="filter.total_page"
                    :page-range="4"
                    :margin-pages="3"
                    :click-handler="changePage"
                    :prev-text="'&laquo;'"
                    :next-text="'&raquo;'"
                    :container-class="'pagination'"
                    :page-class="'page-item'"
                    :force-page="filter.page - 1"
            >
            </paginate>
        </div>
    </div>

    <script>
      jQuery.browser = {};
      (function () {
        jQuery.browser.msie = false;
        jQuery.browser.version = 0;
        if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
          jQuery.browser.msie = true;
          jQuery.browser.version = RegExp.$1;
        }
      })();
    </script>
    {{--    Local plugins--}}
    <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
    <script>
      $(function () {
        // hô biến input thành datepicker xịn xò
        $('#time_from').datetimepicker({
          language: 'vi'
        }).on('dp.change', function (event) {
          filterable-list.onChangeDatetime(event);
        });
        $('#time_to').datetimepicker({
          language: 'vi'
        }).on('dp.change', function (event) {
          filterable-list.onChangeDatetime(event);
        });
      });
    </script>
    <script src="{{asset('plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
    <script src="{{asset('plugin/vue-router/vue-router.js')}}"></script>
    <script src="{{asset('assets/backend/js/user.list.js')}}?{{filemtime('assets/backend/js/user.list.js')}}"></script>

@stop
