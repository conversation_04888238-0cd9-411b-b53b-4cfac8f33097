@extends('backend._default.dashboard')

@section('title') Admin | Quản lý ảnh nền trang chủ @stop
@section('description') Qu<PERSON>n lý ảnh nền trang chủ @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop

@section('assets')
    <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
@stop

@section('content')

<div id="slider-manager" class="row bg-title">
	<div class="slider-manager-title-area">
		<h4 class="page-title pull-left slider-manager-title">Quản lý ảnh nền trang chủ</h4>
		@if(json_decode(Auth::guard('admin')->user()->matrix)->slider->add != null)
    		<button class="btn btn-success" style="float: right;" data-toggle="modal" data-target="#addOrEdit" v-on:click="startAddNewSlider()">Thêm mới</button>
    	@endif
	</div>

    <table class="table" id="slider-table">
		<thead>
			<tr>
				<th>Id</th>
				<th>Ảnh</th>
				<th>Tên</th>
				<th>Trạng thái</th>
				<th>Hành động</th>
			</tr>
		</thead>
		<tbody>
			<tr v-for="(slider, index) in sliders">
				<td>@{{ slider.id }}</td>
				<td>
					<img v-if="slider.image_name != null && slider.image_name != ''" :src="url + slider.image_name" style="height: 100px; max-width: 170px;">
					<img v-if="slider.image_name == null || slider.image_name == ''" src="{{url('assets/img/icon_backend')}}/no_image.png" style="height: 100px; max-width: 170px;">
				</td>
				<td>@{{ slider.name }}</td>
				<td>
					<input type='checkbox' class='ios8-switch ios8-switch-lg' :id="'slider' + slider.id" v-model="slider.show"><label style="cursor: pointer;" :for="'slider' + slider.id" v-on:click="setStatus(index, slider.id)"></label>
				</td>
				<td>
					@if(json_decode(Auth::guard('admin')->user()->matrix)->slider->edit != null)
					<button class="btn btn-info" data-toggle="modal" data-target="#addOrEdit" v-on:click="startEditSlider(index, slider.id)">Sửa</button>
					@endif
					@if(json_decode(Auth::guard('admin')->user()->matrix)->slider->delete != null)
	    			<button class="btn btn-danger" data-toggle="modal" data-target="#deletModal" v-on:click="startRemoveSlider(index, slider.id)">Xóa</button>
	    			@endif
	    		</td>
			</tr>
		</tbody>
  	</table>

	<div class="modal fade" id="addOrEdit" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="title-model"></h4>
				</div>
				<div class="modal-body">
                    <form class="form-horizontal" role="form" id="slider_form">
                    	<ul class="error-area">
                    		<li class="error-item" v-for="error in errors">@{{ error }}</li>
                    	</ul>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Tên</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="name" v-model="currentSlider.name">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Ảnh</label>
                            <div class="col-sm-10">
                            	<img v-if="currentSlider.image_name != null && currentSlider.image_name != ''" id="image_preview" :src="url + currentSlider.image_name" style="width: 150px; max-height: 150px;">
                            	<img v-if="currentSlider.image_name == null || currentSlider.image_name == ''" id="image_preview" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 150px; max-height: 150px;">
                                <input type='file' id="image_file" name="image_file" onchange="previewImage()"/>
                                <div class="col-sm-10" style="color: red;">
                                	Cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>gif,jpg,jpeg,png,svg</b>
                            	</div>
                            </div>
                        </div>
                    </form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="editSlider()" id="btn-action-model"></button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>

		</div>
	</div>

	<div id="deletModal" class="modal fade" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Xóa</h4>
				</div>
				<div class="modal-body">
					<p>Bạn có muốn xóa popup này không?</p>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="removeSlider()">Xóa</button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>
		</div>
	</div>

</div>

<script>

function previewImage() {
    var preview = document.querySelector('#image_preview');
    var file    = document.querySelector('#image_file').files[0];
    var reader  = new FileReader();
    reader.onloadend = function () {
        preview.src = reader.result;
    }
    if (file) {
        reader.readAsDataURL(file);
    } else {
        preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
    }
}

$(document).ready(function() {
    $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });   
});

var user = new Vue({
	el: '#slider-manager',

	data: function () {
        return {
        	url				: window.location.origin + "/cdn/slider/small/",
        	sliders			: {!! json_encode($sliders) !!},
        	currentSlider	: {id: -1, name: "", image_name: "", show: false},
        	errors			: [],
        	currentId		: -1,
        	currentIndex	: -1
		}
	},

	methods: {
		// mở popup sửa 1 slider
		startEditSlider(index, id) {
			// console.log(index + ", " + id);
			var vm = this;
			vm.errors = [];

			// Bỏ tham chiếu
			// const temp = Object.assign({}, vm.sliders[index]);
			vm.currentSlider = vm.sliders[index];
			vm.currentId = id;
			vm.currentIndex = index;

			$("#title-model").text("Chỉnh sửa");
			$("#btn-action-model").text("Sửa");
		},

		// mở popup xóa 1 slider
		startRemoveSlider(index, id) {
			var vm = this;
			vm.currentId = id;
			vm.currentIndex = index;
		},

		// Chuyển trạng thái TẮT <=> BẬT
		setStatus(index, id) {
			var vm = this;
			var data = {
	            'id'		: id,
	            'show'		: vm.sliders[index].show
			};
			// console.log(data);
			$.ajax({
	            url: window.location.origin + "/backend/slider/active", type:"POST", data: data, async: true,
	            error: function() {
	            	console.log("Có lỗi xảy ra");
	            },
	            success: function(response) {
	            	// Nếu active một slider thì tất tất cả những slider khác
	            	if (vm.sliders[index].show == false) return;
	            	for (var i = 0; i < vm.sliders.length; i++) {
	            		if (i != index) {
	            			vm.sliders[i].show = false;
	            		}
	            	}
	            }
	        });
		},

		// mở popup để thêm mới 1 slider
		startAddNewSlider() {
			var vm = this;
			vm.errors = [];
			vm.currentId = -1;
			vm.currentIndex = -1;
			$("#title-model").text("Thêm mới");
			$("#btn-action-model").text("Thêm");

			vm.currentSlider = {id: -1, name: "", image_name: "", show: true};
		},

		// Sủa 1 slider
		editSlider() {
			var vm = this;
			var dataSlider = new FormData($("#slider_form")[0]);
			dataSlider.append('id', vm.currentId);
	        $.ajax({
                type: 'post',
                url: '/backend/slider/edit',
                processData: false,
                contentType: false,
                data : dataSlider,
                success: function(response) {
                    // console.log(response);
	            	if (response == "blank_data") {
	            		vm.errors = [];
	            		vm.errors.push("Dữ liệu còn trống");
	            	} else if (response.type == "new") {
	            		const newSlider = response.slider;
		            	vm.sliders.push(newSlider);
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
	            	}else if(response == 'imagetype'){
	            		vm.errors = [];
	            		vm.errors.push("Ảnh không đúng định dạng");
	            	} else if(response == 'imagesize'){
	            		vm.errors = [];
	            		vm.errors.push("Ảnh vượt quá kích thước 3MB");
	            	} else {
		            	// console.log("Thành công : " + response);
		            	vm.sliders[vm.currentIndex].name = response.slider.name;
		            	vm.sliders[vm.currentIndex].image_name = response.slider.image_name;
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
		            }
                }
            });
		},

		// xóa 1 slider
		removeSlider() {
			var vm = this;
			var data = {
	            'id' : vm.currentId
	        };
	        // console.log(data);
	        $.ajax({
	            url: window.location.origin + "/backend/slider/delete", type:"DELETE", data: data, async: true,
	            error: function() {
	            	vm.errors = [];
	            },
	            success: function(response) {
	            	if (response == "invalid_id") {
	            		vm.errors = [];
	            		vm.errors.push("Có lỗi xảy ra. Vui lòng thử lại");
	            	} else if (response == "success") {
		            	vm.sliders.splice(vm.currentIndex, 1);
		            	$('#deletModal').modal('toggle');
		            }
	            }
	        });
		}
	},

	mounted() {
		var vm = this;
		
		$('#slider-table').DataTable({
			"pageLength": 10,
			"stateSave": true
		});
		
	}
});

</script>

@stop