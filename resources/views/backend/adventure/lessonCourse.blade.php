@extends('backend._default.dashboard')

@section('description') Q<PERSON>ản lý lo<PERSON> họ<PERSON> @stop
@section('keywords') kh<PERSON><PERSON> học @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý k<PERSON> họ<PERSON> @stop

@section('assets')
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/css/bootstrap-select.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/js/bootstrap-select.min.js"></script>
    <style type="text/css">
        .row{font-family: Myriad Pro,sans-serif; font-weight: 500;}
        .btn{padding: 4px 6px 2px 6px; border-radius: 3px; margin: 0 0 0 3px; font-size: 12px;}
        .label {padding: 4px 6px 4px 8px; margin-right: 4px; cursor: pointer; border-radius: 3px; }
        .label-success{background: #10a31a; }
        .label-danger, .btn-danger {background: #e74c3c; }
        .table{background-color: #fff;}
        .text-left{text-align: left;}
        .dropdown-toggle{height: 40px;}
    </style>
@stop

@section('content')
	<div class="row bg-title">
        <h4 class="page-title pull-left">
            Quản lý khóa học
        </h4>
        @if (json_decode(Auth::guard('admin')->user()->matrix)->course->add != null)
            <button class="add-modal btn btn-success"
                style="right: 26px;position: absolute;width: 146px;">
                <span class="glyphicon glyphicon-plus"></span>Thêm mới
            </button>
        @endif
    </div>
    <div class="data-course">
        @include('backend.course.detailLessonCourse')
	</div>
    <div id="pageModal" class="modal fade" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>

                <div class="modal-body">
                    <form class="form-horizontal" role="form" id="course_form">
                        <div class="form-group" style="display: none;">
                            <label class="control-label col-sm-2" for="id">Mã số</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="id" id="fid">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="course">Tên khóa học</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="course" id="course">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="course">Ảnh đại diện</label>
                            <div class="col-sm-8">
                                <div class="upload_complete" style="display: block;">
                                    <div class="file_image_single">
                                        <div class="file_image_single_img">
                                            <img id ="image_course" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 100px; max-height: 100px;">
                                        </div>
                                    </div>
                                </div>
                                <div class="upload_action" style="margin-top:2px;">
                                    <input type="file" name="img_course" id="img_course" onchange="previewCourse()">
                                    <div class="f11 formNote" style="color: red;">
                                        CHÚ Ý - chỉ cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>gif,jpg,jpeg,png,svg</b>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="teacher">Giảng viên</label>
                            <div class="col-sm-8">
                                <select class="selectpicker form-control" multiple data-live-search="true" name="author_id" id="teacher">
                                    @foreach($author as $teacher)
                                        <option value="{{$teacher->id}}">{{$teacher->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="use_course">Sử dụng</label>
                            <div class="col-sm-8">
                                <select class="form-control" name="use_course" id="use_course">
                                    <option value="0">Miễn phí</option>
                                    <option value="1">Giá premium</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group price">
                            <label class="control-label col-sm-2" for="price">Giá</label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" name="vnd" id="vnd" placeholder="Giá Việt Nam Đồng">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="video">Số video</label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" name="video" id="video">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="watch_expired">Giới hạn số ngày</label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" name="watch_expired" id="watch_expired">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="numberOfMonth">Số tháng</label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" name="numberOfMonth" id="numberOfMonth">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="numberOfLesson">Tổng số bài học</label>
                            <div class="col-sm-8">
                                <input type="number" class="form-control" name="numberOfLesson" id="numberOfLesson">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="link">Video giới thiệu khóa học</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="link" id="link">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="allow_special">Khoá học chuyên ngành</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="allow_special_off" name="allow_special" value="0">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="allow_special_on" name="allow_special" value="1" >
                                    <span class="labels">Bật</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="status">Trạng thái</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1" >
                                    <span class="labels">Bật</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="brief">Giới thiệu</label>
                            <div class="col-sm-8">
                                <textarea type="text" class="form-control" name="brief" id="brief"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="notification">Mô tả</label>
                            <div class="col-sm-8">
                                <textarea type="text" class="form-control" name="notification" id="notification"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2" for="openNoti">Hiển thị thông báo</label>
                            <div class="col-sm-8">
                                <input type="checkbox" name="openNoti" id="openNoti">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="status">Giao diện menu</label>
                            <div class="col-sm-3">
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="premium_off" name="premium" value="0">
                                    <span class="labels">Cũ</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="premium_on" name="premium" value="1" >
                                    <span class="labels">Mới</span>
                                </label>
                            </div>
                        </div>
                        <li class="global_error text-left hidden"></li>
                    </form>
                    <!--delete form-->
                    <div class="deleteContent">
                        Có muốn xóa khóa học này đi không? <span
                            class="hidden did"></span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- @include('backend.lessonGroupSort') --}}

    <div id="sortGroup" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4>Sắp xếp nhóm bài học theo khóa học</h4>
                </div>
                <div class="modal-body less">
                </div>
            </div>
        </div>
    </div>
	<script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                  'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        });
        sortTable('lessonCourse', 1, 5, '/backend/course-sort-order', 'sort_order', 'course_table');

        var brief = CKEDITOR.replace('brief', {
            filebrowserBrowseUrl: '/backend/ckfinder/browser',
        });

        var notification = CKEDITOR.replace('notification', {
            filebrowserBrowseUrl: '/backend/ckfinder/browser',
        });

        // Change image
        function previewCourse() {
            var preview = document.querySelector('#image_course');
            var file    = document.querySelector('#img_course').files[0];
            var reader  = new FileReader();
            reader.onloadend = function () {
                preview.src = reader.result;
            }
            if (file) {
                reader.readAsDataURL(file);
            } else {
                preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
            }
        }

        // Thao tac them, xoa, sua
        $(document).on('click', '.add-modal', function() {
            $('#footer_action_button').text(" Thêm");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').addClass('add');
            $('.modal-title').text('Thêm');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            fillmodalData('');
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '60%');
            $('#pageModal').css('left', '19%');
        });

        // Edit
        $(document).on('click', '.edit-modal', function() {
            $('#footer_action_button').text(" Sửa");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('edit');
            $('.modal-title').text('Sửa');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            var id = $(this).data('info');
            $.ajax({
                type: 'get',
                url: '/backend/course/' + id ,
                success: function(data) {
                    console.log(data);
                    fillmodalData(data[0]);
                }
            });
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '60%');
            $('#pageModal').css('left', '19%');
        });

        // Delete
        $(document).on('click', '.delete-modal', function() {
            $('#footer_action_button').text(" Xóa");
            $('#footer_action_button').removeClass('glyphicon-check');
            $('#footer_action_button').addClass('glyphicon-trash');
            $('.actionBtn').removeClass('btn-success');
            $('.actionBtn').addClass('btn-danger');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('delete');
            $('.modal-title').text('Xóa');
            $('.deleteContent').show();
            $('.form-horizontal').hide();
            var id = $(this).data('info');
            $('.did').text(id);
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '26%');
            $('#pageModal').css('left', '36%');
        });

        // Thay đổi giá khi lựa chọn
        function changePrice(vnd, jpy) {
            if ($('#use_course').val() == 0) {
                $('.price').hide();
                $('#vnd').val(0);
                $('#jpy').val(0);
            } else {
                $('.price').show();
                $('#vnd').val(vnd);
                $('#jpy').val(jpy);
            }
        }

        // Thay đổi deadline khi lựa chọn
        function changeExpired($expired) {
            if ($('#deadline').val() == 0) {
                $('.expired').show();
                $('#expired_date').val($expired);
            } else {
                $('.expired').hide();
                $('#expired_date').val(null);
            }
        }

        // Thay đổi giá trị khi thi
        function changeExam(exam, exam_hours, minutes, minutes2, minutes3) {
            if (exam == 0 || exam_hours == null || minutes == 0 || minutes2 == 0 || minutes3 == 0) {
                $('#minutes').val(0);
                $('#exam_time').val(null);
                $('#exam_date').val(null);
                $('#minutes2').val(0);
                $('#minutes3').val(0);
            } else {
                $('#minutes').val(minutes);
                $('#minutes2').val(minutes2);
                $('#minutes3').val(minutes3);
                $('#exam_time').val(exam_hours.substr(11, 5));
                $('#exam_date').val(exam_hours.substr(0, 10));
            }
        }

        // Fill data
        function fillmodalData(course) {
            if (course == '') {
                $('#course').val('');
                $('#teacher').selectpicker('val','');
                $('#use_course').val(0);
                $('.price').hide();
                $('#vnd').val(0);
                $('#jpy').val(0);
                $('#use_course').change(function() {
                    changePrice(0, 0);
                });
                $('#deadline').val(1);
                $('.expired').hide();
                $('#expired').val('');
                $('#deadline').change(function() {
                    changeExpired(null);
                });
                $('#video').val(0),
                $('#watch_expired').val(0),
                $('#link').val('');
                brief.setData('');
                notification.setData('');
                $('#status_off').prop('checked', true);
                $('#premium_off').prop('checked', true);
                $('#allow_special_off').prop('checked', true);
                $('#image_course').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
                $('#img_course').val('');
                $('#numberOfMonth').val(0);
                $('#numberOfLesson').val(0);

                $('#exam_off').prop('checked', true);
                changeExam(0, null, 0, 0, 0);

                $('input[type=radio][name=exam]').change(function() {
                    changeExam(0, null, 0, 0, 0);
                });

                $('#openNoti').prop('checked', false);

                $('.global_error').addClass('hidden');

            } else {
                $('#fid').val(course.id);
                $('#course').val(course.name);
                $('#teacher').selectpicker('val', JSON.parse(course.author_id));
                $('#use_course').val(course.price_option);
                changePrice(course.price, course.jpy_price);
                $('#use_course').change(function() {
                    changePrice(course.price, course.jpy_price);
                });
                $('#deadline').val(course.expired_option);
                var expired_day = course.expired_day;
                changeExpired(expired_day);
                $('#deadline').change(function() {
                    changeExpired(expired_day);
                });
                $('#video').val(JSON.parse(course.stats_data).video),
                $('#numberOfMonth').val(JSON.parse(course.stats_data).time),
                $('#numberOfLesson').val(JSON.parse(course.stats_data).lesson),
                $('#watch_expired').val(course.watch_expired),
                $('#link').val(course.link);
                (course.allow_special == 0) ? $('#allow_special_off').prop('checked', true) : $('#allow_special_on').prop('checked', true);
                (course.show == 0) ? $('#status_off').prop('checked', true) : $('#status_on').prop('checked', true);
                (course.premium == 0) ? $('#premium_off').prop('checked', true) : $('#premium_on').prop('checked', true);
                if (course.avatar_name == null || course.avatar_name == '') {
                    $('#image_course').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
                } else {
                    $('#image_course').attr('src', '{{url('cdn/course/default')}}/' + course.avatar_name);
                }
                $('#img_course').val('');

                (course.noindex == 0) ? $('#exam_off').prop('checked', true) : $('#exam_on').prop('checked', true);
                changeExam( $('input[type=radio][name=exam]:checked').val(), course.exam_hours, course.exam_minute, course.exam_minute_2, course.exam_minute_3);

                $('input[type=radio][name=exam]').change(function() {
                    changeExam( $('input[type=radio][name=exam]:checked').val(), course.exam_hours, course.exam_minute, course.exam_minute_2, course.exam_minute_3);
                });

                brief.setData(course.brief);
                notification.setData(course.description);

                if (course.feature == 0) {
                    $('#openNoti').prop('checked', false);
                } else {
                    $('#openNoti').prop('checked', true);
                }

                $('.global_error').addClass('hidden');
            }
        }

        $('.modal-footer').on('click', '.add', function() {
            var dataCourse = new FormData($("#course_form")[0]);
            dataCourse.append('listTeacher', $('#teacher').val());
            dataCourse.append('brief', CKEDITOR.instances['brief'].getData());
            dataCourse.append('notification', CKEDITOR.instances['notification'].getData());
            dataCourse.append('sort_order', ($("#course_table tbody tr").length+1));
            $.ajax({
                type: 'post',
                url: '/backend/course/create',
                processData: false,
                contentType: false,
                data : dataCourse,
                success: function(data) {
                    if (data.errors) {
                        if (Object.keys(data.errors).length > 0) {
                            $('#pageModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    } else {
                        if (data == 'imagetype') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh không đúng định dạng");
                        } else if (data == 'imagesize') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh vượt quá 3MB");
                        } else {
                            $('#pageModal').modal('hide');
                            $('.data-course').html(data);
                            sortTable('lessonCourse', 1, 5, '/backend/course-sort-order', 'sort_order', 'course_table');
                        }
                    }
                }
            });
        });
        $('.modal-footer').on('click', '.edit', function() {
            var dataCourse = new FormData($("#course_form")[0]);
            dataCourse.append('listTeacher', $('#teacher').val());
            dataCourse.append('brief', CKEDITOR.instances['brief'].getData());
            dataCourse.append('notification', CKEDITOR.instances['notification'].getData());
            $.ajax({
                type: 'post',
                url: '/backend/course/edit',
                processData: false,
                contentType: false,
                data : dataCourse,
                success: function(data) {
                    if (data.errors) {
                        if (Object.keys(data.errors).length > 0) {
                            $('#pageModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    } else {
                        if (data == 'imagetype') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh không đúng định dạng");
                        } else if (data == 'imagesize') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh vượt quá 3MB");
                        } else {
                            $('#pageModal').modal('hide');
                            $('.data-course').html(data);
                            sortTable('lessonCourse', 1, 5, '/backend/course-sort-order', 'sort_order', 'course_table');
                        }
                    }
                }
            });
        });
        $('.modal-footer').on('click', '.delete', function() {
            $.ajax({
                type: 'post',
                url: '/backend/course/delete',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('.did').text()
                },
                success: function(data) {
                    $('#pageModal').modal('hide');
                    $('.item' + $('.did').text()).remove();
                }
            });
        });

        // Them
        $(document).on('click', '.lesson-group', function() {
            var id = $(this).data('info');
            $.ajax({
                type: 'post',
                url: '/backend/lesson-group-table',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': id
                },
                success: function(data) {
                    $('.less').html(data);
                    sortTable('lessonGroup', 1, 4, '/backend/lesson-group-sort', 'sort', 'group_table');
                    $('#sortGroup').modal('show');
                }
            });
        });

    </script>

@stop
