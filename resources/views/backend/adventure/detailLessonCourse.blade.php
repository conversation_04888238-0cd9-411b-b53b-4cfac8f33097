<table class="table table-borderless" id="course_table">
    <thead>
        <tr>
            <th class="text-center"><PERSON><PERSON></th>
            <th class="text-center">Tên kh<PERSON>a học</th>
            <th class="text-center"><PERSON><PERSON><PERSON>(vnđ)</th>
            <th class="text-center"><PERSON><PERSON> <PERSON>u tiên</th>
            <th class="text-center">Trạng thái</th>
            <th class="text-center"><PERSON><PERSON><PERSON> t<PERSON></th>
            <th class="text-center"><PERSON><PERSON><PERSON> động</th>
        </tr>
    </thead>
    <tbody class="lessonCourse">
        @foreach($course as $item)
        <tr class="item{{$item->id}}">
            <td class="text-center">{{$item->id}}</td>
            <td class="text-left">
                <a style="float:left; margin-right: 5px;">
                    <img
                    @if ($item->avatar_name == null || $item->avatar_name == '')
                        src="{{url('assets/img/icon_backend')}}/no_image.png"
                    @else
                        src="{{url('/cdn/course/default/'.$item->avatar_name)}}"
                    @endif
                    height="57px" width="50px">
                </a>
                <a href="{{url('/khoa-hoc/'.$item->SEOurl)}}" target="_blank">{{$item->name}}</a><br>
                <i class="fa fa-eye" title="Số lượt xem"> {{$item->count_view}}</i><br>
            </td>
            <td class="text-center">{{ number_format($item->price) }}</td>
            <td class="text-center">{{$item->sort_order}}</td>
            <td class="text-center">
                @if ($item->show == 1)
                    <span class="label label-success">Bật</span>
                @else
                    <span class="label label-danger">Tắt</span>
                @endif
            </td>
            <td class="text-center">{{$item->created_at}}</td>
            <td class="text-center">

                @if (json_decode(Auth::guard('admin')->user()->matrix)->course->edit != null)
                    <button class="edit-modal btn btn-info"
                        data-info="{{$item->id}}">
                        <span class="glyphicon glyphicon-edit"></span> Sửa
                    </button>
                @endif

                @if (json_decode(Auth::guard('admin')->user()->matrix)->course->delete != null)
                    <button class="delete-modal btn btn-danger"
                        data-info="{{$item->id}}">
                        <span class="glyphicon glyphicon-trash"></span> Xóa
                    </button>
                @endif

                @if (json_decode(Auth::guard('admin')->user()->matrix)->course->sort != null)
                    <button class="lesson-group btn btn-success"
                        data-info="{{$item->id}}">
                        <span class="glyphicon glyphicon-th-list"></span> Nhóm bài học
                    </button>
                @endif
            </td>
        </tr>
        @endforeach
    </tbody>
</table>