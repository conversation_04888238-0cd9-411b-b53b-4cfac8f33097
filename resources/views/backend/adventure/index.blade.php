@extends('backend._default.dashboard')

@section('description') Q<PERSON>ản lý lộ trình @stop
@section('keywords') group stage @stop
@section('author') dungmori.com @stop
@section('title') Admin | <PERSON><PERSON> trình @stop

@section('assets')
    <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
    <link href="{{asset('assets/backend/css/course-stage.css')}}?{{filemtime('assets/backend/css/course-stage.css')}}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/css/bootstrap-select.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/js/bootstrap-select.min.js"></script>
    <style type="text/css">
        .row{font-family: Myriad Pro,sans-serif; font-weight: 500;}
        .btn{padding: 4px 6px 2px 6px; border-radius: 3px; margin: 0 0 0 3px; font-size: 12px;}
        .label {padding: 4px 6px 4px 8px; margin-right: 4px; cursor: pointer; border-radius: 3px; }
        .label-success{background: #10a31a; }
        .label-danger, .btn-danger {background: #e74c3c; }
        .table{background-color: #fff;}
        .text-left{text-align: left;}
        .dropdown-toggle{height: 40px;}
    </style>
@stop

@section('content')
    <div class="course-stages__screen" id="adventure__screen">
        <div class="course-stages__panel course-stages__screen--panel">
            <div class="course-stages__panel--header flex justify-between items-center">
                <span class="text-bold">Lộ trình</span>
                <div class="flex items-center">
                    <span class="btn btn-sm btn-info" @click="showFlashcardModal = !showFlashcardModal">@{{ showFlashcardModal ? 'Lộ trình' : 'Flashcard'}}</span>
                    <span class="btn btn-sm btn-success" @click="showModal = true">Thêm</span>
                </div>
            </div>
            <div class="course-stages__panel--course-list mb-3">
                <label v-for="(course, index) in courses" v-if="['N1','N2','N3','N4','N5','Sơ cấp N4','Sơ cấp N5'].includes(course.name)">
                    <input type="radio" :checked="course.id === selectedCourse.id" @change="selectCourse($event, course)"/>
                    @{{ course.name }}
                </label>
{{--                <span class="btn btn-info" @click="applyFilter">Lọc</span>--}}
            </div>
            <div class="course-stages__panel--stage-list" v-if="!loading">
                <div class="course-stages__panel--stage-item" :class="{'course-stages__panel--stage-item-selected': selectedPeriod === period}" v-for="period in periods" @click.self="selectedPeriod = period">
                    <div class="course-stages__panel--stage-item-info">
                        <span>@{{ period.name }}
                            <i v-if="period.status === 1" class="fa fa-eye text-success"></i>
                            <i v-if="period.status === 0" class="fa fa-eye-slash text-dark"></i>
                        </span>
{{--                        <span>@{{ stage.key }}</span>--}}
                    </div>
                    <div class="course-stages__panel--stage-item-action">
                        <span @click="editPeriod(period)" class="btn btn-sm btn-warning mb-2 text-dark" title="Sửa"><i class="fa fa-edit"></i></span>
                        <el-popconfirm
                          title="Xác nhận xoá lộ trình?"
                          confirm-button-text='Tất nhin'
                          cancel-button-text='Nope'
                          icon="el-icon-info"
                          icon-color="red"
                          @confirm="deletePeriod(period)"
                        >
                          <span slot="reference" class="btn btn-sm btn-danger mb-2" title="Xoá"><i class="fa fa-trash"></i></span>
                        </el-popconfirm>
                        {{--                    <span class="btn btn-sm btn-info mb-2" title="Danh sách "><i class="fa fa-list"></i></span>--}}
                    </div>
                </div>
            </div>
            <div class="course-stages__screen--panel-loading" v-if="loading">
                <div class="fa-3x">
                    <i class="fa fa-spinner fa-spin"></i>
                </div>
            </div>
        </div>
        <div v-show="showFlashcardModal" class="pt-5 lesson-categories__panel course-stages__screen--panel">
            <div class="flex justify-start mb-5">
                <span class="btn btn-lg btn-success" @click="createGroup">Thêm</span>
            </div>
            <draggable tag="div" @end="onDragGroupEnd" v-model="flashcardGroups" @start="draggable=true" @end="draggable=false" :handle="'.hamburger'">
                <div
                    v-for="group in flashcardGroups"
                    :key="'group_' + group.id"
                    class="flex course-stages__panel--stage-item p-5"
                    :class="{'course-stages__panel--stage-item-selected': selectedGroup === group}"
                    @click="selectedGroup = group"
                >
                    <div class="flex items-center">
                        <i class="fa fa-bars fa-lg hamburger"></i>
                        <input :value="group.title" @keypress="saveGroup($event, group)" class="ml-5 form-control" style="min-width: 300px;"/>
                    </div>
                    <i
                        class="fa fa-times fa-lg ml-5"
                        style="cursor:pointer;color:red"
                        @click="deleteGroup(group)"
                    ></i>
                </div>
            </draggable>
        </div>
        <div v-show="showFlashcardModal" class="pt-5 lesson-categories__panel course-stages__screen--panel">
            <div class="flex justify-start items-center mb-5">
                <el-select
                    v-model="selectedLessons"
                    multiple
                    filterable
                    collapse-tags
                    placeholder="Chọn bài học flashcard"
                    style="width: 50%"
                >
                    <el-option
                        v-for="lesson in availableLessons"
                        :key="'lesson-' + lesson.lesson.id"
                        :label="lesson.lesson.name"
                        :value="lesson.lesson.id">
                    </el-option>
                </el-select>
                <span class="btn btn-lg btn-success" @click="addFlashcardLesson">Thêm</span>
            </div>
            <draggable tag="div" @end="onDragLessonEnd" v-model="lessons" @start="draggable=true" @end="draggable=false" :handle="'.hamburger'">
                <div
                    v-for="lesson in lessons"
                    :key="'lesson_' + lesson.id"
                    class="flex course-stages__panel--stage-item p-5"
                >
                    <div class="flex justify-between items-center" style="width: 100%;">
                        <div class="flex items-center">
                            <i class="fa fa-bars fa-lg hamburger"></i>
                            <span class="ml-5">@{{ lesson.lesson.name }}</span>
                        </div>
                        <i
                            class="fa fa-times fa-lg ml-5"
                            style="cursor:pointer;color:red"
                            @click="deleteLesson(lesson)"
                        ></i>
                    </div>
                </div>
            </draggable>
        </div>
        <lesson-path-panel v-show="!showFlashcardModal" :permission="permission" :course="selectedCourse" :period="selectedPeriod" v-on:update:path="pathSelected($event)"></lesson-path-panel>
        <lesson-checkpoint-panel v-show="!showFlashcardModal" :permission="permission" :course="selectedCourse" :path="selectedPath"></lesson-checkpoint-panel>
        <backend-modal v-if="showModal" @close="closeModal">
            <h3 slot="header">@{{ formData.id ? 'Sửa lộ trình' : 'Thêm lộ trình' }}</h3>
            <div slot="body">
                <div class="form-group">
                    <label>Tên</label>
                    <input class="form-control" v-model="formData.name"/>
                </div>
                <div class="form-group">
                    <label>Mô tả</label>
                    <input class="form-control" v-model="formData.description"/>
                </div>
                <div class="form-group">
                    <label>Thời gian (ngày)</label>
                    <input type="number" class="form-control" v-model="formData.duration" />
                </div>
                <div class="form-group">
                    <label>Thuộc khoá học</label>
                    <select class="form-control" v-model="formData.course_id">
                        <option :value="course.id" v-for="course in courses">@{{ course.name }}</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trạng thái</label>
                    <div>
                        <label><input :checked="formData.status === 0" type="radio" value="0" name="path_status" @change="setFormStatus"/>Tắt</label>
                        <label><input :checked="formData.status === 1" type="radio" value="1" name="path_status" @change="setFormStatus"/>Bật</label>
                        <label><input :checked="formData.status === 2" type="radio" value="2" name="path_status" @change="setFormStatus"/>Testing</label>
                    </div>
                </div>
                <div class="form-group">
                    <label>VIP</label>
                    <div>
                        <label><input :checked="formData.is_vip === 0" type="radio" value="0" name="path_vip" @change="setFormVip"/>Tắt</label>
                        <label><input :checked="formData.is_vip === 1" type="radio" value="1" name="path_vip" @change="setFormVip"/>Bật</label>
                    </div>
                </div>
                <div class="flex justify-start">
                    <span class="btn btn-lg btn-success" @click="saveForm">@{{ formData.id ? 'Lưu' : 'Thêm' }}</span>
                </div>
            </div>
        </backend-modal>
    </div>
@stop
@section('footer')
    <script>
        var courses = {!! json_encode($courses) !!};
        var admin = {!! json_encode(Auth::guard('admin')->user()) !!};
    </script>
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>
    <script src="{{ asset('/plugin/toastr/toastr.min.js') }}"></script>

    <!-- CDNJS :: Sortable (https://cdnjs.com/) -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.8.4/Sortable.min.js"></script>
    <!-- CDNJS :: Vue.Draggable (https://cdnjs.com/) -->
    <!-- import CSS -->
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script>
      ELEMENT.locale(ELEMENT.lang.vi);
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Vue.Draggable/2.20.0/vuedraggable.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="{{ asset('/assets/backend/js/modal.js') }}?{{filemtime('assets/backend/js/modal.js')}}"></script>
    <script src="{{ asset('/assets/backend/js/adventure.js') }}?{{filemtime('assets/backend/js/adventure.js')}}"></script>
    <style>
        .avatar-uploader .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .avatar-uploader .el-upload:hover {
            border-color: #409EFF;
        }
        .el-upload__input {
            display: none !important;
        }
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 150px;
            height: 150px;
            line-height: 150px;
            text-align: center;
        }
        .avatar {
            width: 150px;
            height: 150px;
            display: flex;
        }
    </style>
@stop
