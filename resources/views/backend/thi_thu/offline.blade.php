@extends('backend._default.dashboard')

@section('description') Nh<PERSON><PERSON> điểm tele offline @stop
@section('keywords') Tele @stop
@section('author') dungmori.com @stop
@section('title') Admin | Tele @stop

@section('assets')

@stop

@section('content')
	<div class="p-5" id="offline-data">
    <div class="btn btn-info" @click="handleInput">Nhập điểm</div>
    <input type="file" ref="fileInput" @change="upload" style="display: none">
    <table class="table table-striped mt-5" style="background-color: white">
      <thead>
      <tr>
        <th scope="col">Họ tên</th>
        <th scope="col">Mail</th>
        <th scope="col">Cấp độ</th>
        <th scope="col">Thi tại</th>
        <th scope="col">Điểm 1</th>
        <th scope="col">Điểm 2</th>
        <th scope="col">Đ<PERSON>ểm 3</th>
        <th scope="col"><PERSON><PERSON><PERSON><PERSON> tổng</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="item in list">
        <th>@{{ item.name }}</th>
        <th>@{{ item.email }}</th>
        <th>@{{ item.level }}</th>
        <th>@{{ item.location }}</th>
        <th>@{{ item.mark_1 }}</th>
        <th>@{{ item.mark_2 }}</th>
        <th>@{{ item.mark_3 }}</th>
        <th>@{{ item.mark_total }}</th>
      </tr>
      </tbody>
    </table>
  </div>
  <script>
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });
    new Vue({
        el: '#offline-data',
        data() {
            return {
                list: [],
                loading: false
            }
        },
        methods: {
            initData() {
              const vm = this
              $.post(window.location.origin + '/backend/nhap-diem-offline/list').then(res => {
                  vm.list = res.data
              });
            },
            handleInput() {
                this.$refs.fileInput.click()
            },
            upload(event) {
                this.loading = true
                const data = new FormData()
                data.append('file', event.target.files[0])
                data.append('_token', $('meta[name="csrf-token"]').attr("content"))
                $.ajax({
                    url : window.location.origin + '/backend/nhap-diem-offline/import',
                    type: 'POST',
                    data: data,
                    cache : false,
                    processData: false,
                    contentType: false,
                }).done(function(res) {
                    // console.log(res)
                });
            }
        },
        mounted() {
            this.initData()
        },
    })
  </script>
@stop
