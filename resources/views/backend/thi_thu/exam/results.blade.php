@extends('backend._default.dashboard')

@section('description') Quản lý kết quả JLPT @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý kết quả JLPT @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/jlpt_results.css') }}?{{filemtime('assets/backend/css/jlpt_results.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
@stop

@section('content')
    <div class="exam__results--screen" id="exam__results--screen">
        <div class="exam__results--filter">
            <div class="form-group" style="width: 90px;">
                {{-- <label>ID Bài thi</label> --}}
                <input type="text" class="form-control" placeholder="ID Bài thi" v-model="filter.id" @keyup.enter="applyFilter">
            </div>
            <div class="form-group"  style="width: 90px;">
                {{-- <label>ID User</label> --}}
                <input type="text" class="form-control" placeholder="User ID" v-model="filter.user_id" @keyup.enter="applyFilter">
            </div>
            <div class="form-group" style="display: none;">
                <label>Địa chỉ</label>
                <select v-model="filter.province" class="form-control" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option v-for="province in provinces" :value="province.id">@{{ province.name }}</option>
                </select>
            </div>
            <div class="form-group"  style="width: 120px;">
                {{-- <label>Trình độ</label> --}}
                <select v-model="filter.course" class="form-control" @change="applyFilter">
                    <option value="">-Trình Độ-</option>
                    <option value="N1">N1</option>
                    <option value="N2">N2</option>
                    <option value="N3">N3</option>
                    <option value="N4">N4</option>
                    <option value="N5">N5</option>
                </select>
            </div>
            <div class="form-group"  style="display: none;">
                <label>Nghề nghiệp</label>
                <select v-model="filter.career" class="form-control" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option v-for="career in careers" :value="career.id">@{{ career.title }}</option>
                </select>
            </div>
            <div class="form-group"  style="display: none;">
                <label>SĐT</label>
                <select v-model="filter.mobile" class="form-control" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option value="1">Có</option>
                    <option value="-1">Không</option>
                </select>
            </div>

            <div class="form-group" style="width: 120px;">
                {{-- <label>Đỗ/Trượt</label> --}}
                <select v-model="filter.is_passed" class="form-control" @change="applyFilter">
                    <option value="">-Đỗ/Trượt-</option>
                    <option value="1">Đỗ</option>
                    <option value="0">Trượt</option>
                </select>
            </div>
            <div class="form-group"  style="display: none;">
                <label>Thông tin</label>
                <select v-model="filter.certificate_info" class="form-control" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option value="1">Có thông tin</option>
                    <option value="-1">Vô danh tiểu tốt</option>
                </select>
            </div>
            <div class="form-group">
                {{-- <label>Nguồn</label> --}}
                <select v-model="filter.platform" class="form-control" @change="applyFilter">
                    <option value="">-Nền tảng-</option>
                    <option value="-1">Web</option>
                    <option value="1">App</option>
                </select>
            </div>
            <div class="form-group">
                {{-- <label>Thi từ ngày</label> --}}
                <input type="text" class="form-control" name="time_from" id="time_from" placeholder="Thi từ ngày" v-model="filter.time_from" @change="onChangeDate($event)">
            </div>
            <div class="form-group">
                {{-- <label>Đến ngày</label> --}}
                <input type="text" class="form-control" name="time_to" id="time_to" placeholder="Đến ngày" v-model="filter.time_to" @change="onChangeDate($event)">
            </div>

            <div class="form-group"  style="width: 100px;">
                {{-- <label>Thi từ ngày</label> --}}
                <input type="number" class="form-control" name="score_from" id="score_from" placeholder="Điểm min" v-model="filter.score_from" @keyup.enter="applyFilter">
            </div>
            <div class="form-group"  style="width: 110px;">
                {{-- <label>Đến ngày</label> --}}
                <input type="number" class="form-control" name="score_to" id="score_to" placeholder="Điểm max" v-model="filter.score_to" @keyup.enter="applyFilter">
            </div>

            <div style="display: flex; align-items: flex-end">
                <span style="margin-right: 10px; cursor: pointer" @click="resetFilter"><i class="fa fa-refresh"></i></span>
                <button class="btn btn-info" @click="applyFilter">Lọc</button>
                <button class="btn btn-success" @click="exportExcel" style="margin-left: 10px;">Xuất Excel</button>
            </div>

{{--            <span class="btn btn-default" @click="filterFlag('no')">Chưa xử lý</span>--}}
{{--            <span class="btn btn-danger" @click="filterFlag('vn')">Việt Nam</span>--}}
{{--            <span class="btn btn-info" @click="filterFlag('jp')">Nhật Bản</span>--}}




{{--            <input type="checkbox" class="form-control" name="is_printed" id="is_printed" :checked="filter.is_printed" @change="onChangeCheckbox('is_printed', $event)">--}}
        </div>
        <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
            <span>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</span>
            <div>
                <span v-if="showPromoteProcess && totalPromote != 0">Gửi thành công <b>@{{ promoteSuccess }}</b>/<b>@{{ totalPromote }}</b> tin nhắn  </span>
                <span v-if="showPromoteProcess && totalPromote == 0">Tất cả user được lọc đã nhận được tin nhắn  </span>
                <span class="btn btn-sm btn-info" data-toggle="modal" data-target="#messageToAll" ><i class="fa fa-comments"></i> Nhắn tin HÀNG LOẠT!!</span>
                <span class="btn btn-sm btn-danger" @click="sync"><i class="fa fa-spinner fa-spin" v-if="syncingPass"></i> Đồng bộ kết quả</span>
{{--                <span class="btn btn-sm btn-danger" @click="syncPlatform"><i class="fa fa-spinner fa-spin" v-if="syncingPlatform"></i> Đồng bộ thiết bị</span>--}}
                <span class="btn btn-sm btn-success" v-if="filter.is_passed == 1 && filter.certificate_info == 1" @click="printAll"><i class="fa fa-print"></i> In toàn bộ</span>
            </div>
        </div>
        <div class="exam__results--list">
            <table>
                <thead>
                <th width="4%">ID</th>
                <th width="7%">User ID</th>
{{--                <th>Email</th>--}}
{{--                <th width="7%">Mã bưu điện</th>--}}
                <th width="15%">Địa chỉ</th>
                <th width="15%">Họ và tên</th>
                <th width="10%">Nghề nghiệp</th>
                <th width="10%">SĐT</th>
                <th width="5%">Admin</th>
                <th width="5%">Cấp độ</th>
{{--                <th width="7%">Quốc gia</th>--}}
                <th class="text-center" @click="sortTotalScore" style="cursor: pointer">Đỗ/Trượt <i class="fa fa-sort-up" v-if="filter.sort === 'asc'"></i> <i class="fa fa-sort-down" v-if="filter.sort === 'desc'"></i></th>
                <th class="text-center">Chat</th>
                <th width="5%" style="text-align: right">Tổng điểm</th>
                <th width="5%" style="text-align: right">文字・語彙</th>
                <th width="5%" style="text-align: right">言語知識・読解</th>
                <th width="5%" style="text-align: right">聴解</th>
{{--                <th width="15%">Thời gian thi</th>--}}
{{--                <th width="5%" class="text-center">Hoàn thành</th>--}}
{{--                <th width="5%" class="text-center">Sai</th>--}}
                <th width="10%" class="text-center">Thao tác</th>
                </thead>
                <tbody v-if="!loading">
                <tr v-if="results.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(result, index) in results" :key="result.id" v-if="results.length != 0">
                    <td>
                        <div>@{{ result.id }}</div>
                    </td>
                    <td>
{{--                        <div><a :href="window.location.origin + '/backend/chat#' + result.user_id" target="_blank">@{{ result.user_id  }}</a></div>--}}
                        <span>@{{ result.user_id  }}</span>
                        <a :href="url + '/backend/chat#' + result.conversation_id" target="_blank" v-if="result.conversation_id">
                            <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;" title="nhắn tin"></i>
                        </a>
                        <i v-else :class="'fa fa-comments fa-comments-' + result.user_id" style="cursor: pointer; font-size: 18px;" title="nhắn tin" @click="initConversation(result.user_id)"></i>
                    </td>
{{--                    <td>--}}
{{--                        <div v-if="!result.certificate_info">@{{ result.user_info ? result.user_info.email : '--'}}</div>--}}
{{--                        <div v-if="result.certificate_info">@{{ result.certificate_info ? result.certificate_info.email : '--'}}</div>--}}
{{--                    </td>--}}
{{--                    <td>--}}
{{--                        <div v-if="result.certificate_info">@{{ result.certificate_info ? result.certificate_info.postalcode : '--' }}</div>--}}
{{--                    </td>--}}

                    <td>
                        <div v-if="result.certificate_info">@{{ result.certificate_info ? result.certificate_info.address : '--' | address}}</div>
                    </td>
                    <td>
                        <div v-if="result.certificate_info">@{{ result.certificate_info ? result.certificate_info.fullname : '--'}}</div>
                        <div v-if="!result.certificate_info">@{{ result.user_info ? result.user_info.name : '--'}}</div>
                    </td>
                    <td>
                        <div v-if="result.certificate_info">@{{ result.certificate_info ? result.certificate_info.career : '--' | career}}</div>
                    </td>
                    <td>
                        <div v-if="result.certificate_info">@{{ result.certificate_info ? result.certificate_info.mobile : '--' }}</div>
                        <div v-if="!result.certificate_info">@{{ result.user_info ? result.user_info.phone ? result.user_info.phone : result.user_info.phone_number : '--' }}</div>
                    </td>
                    <th width="5%">@{{ result.admin_id }}</th>
                    <td>
                        <div>@{{ result.course_name }}</div>
                    </td>
{{--                    <td>--}}
{{--                        <div>--}}
{{--                            <span class="btn" :class="{'btn-danger': result.vn != null, 'btn-default': result.vn == null}" @click="setFlag(result, 'vn')">VN</span>--}}
{{--                            <span class="btn" :class="{'btn-info': result.jp != null, 'btn-default': result.jp == null}" @click="setFlag(result, 'jp')">JP</span>--}}
{{--                        </div>--}}
{{--                    </td>--}}
                    <td class="text-center">
                        <div v-bind:style="{color: printResult(result.course, result.score_1, result.score_2, result.score_3, result.total_score) ? '#5bb500' : '#c90000'}">@{{ result.total_score }} / @{{ printResult(result.course, result.score_1, result.score_2, result.score_3, result.total_score) ? 'Đỗ' : 'Trượt'}}</div>
                    </td>
                    <td class="text-center text-success">
                        <span v-if="result.promoted"><i class="fa fa-check"></i></span>
                    </td>
                    <td>
                        <div style="text-align: right">@{{ result.total_score }}</div>
                    </td>
                    <td>
                        <div style="text-align: right">@{{ result.grade_1 }}</div>
                    </td>
                    <td>
                        <div style="text-align: right">@{{ result.grade_2 }}</div>
                    </td>
                    <td>
                        <div style="text-align: right">@{{ result.grade_3 }}</div>
                    </td>
{{--                    <td>--}}
{{--                        <div>@{{ result.created_at | dateTimeToMinute }}</div>--}}
{{--                    </td>--}}
{{--                    <td>--}}
{{--                        <div class="text-center">--}}
{{--                            <label class="checkbox__switch">--}}
{{--                                <input type="checkbox" v-model="result.is_printed" @change="setPrinted(result.id, $event)"/>--}}
{{--                                <span class="checkbox__slider checkbox__round"></span>--}}
{{--                            </label>--}}
{{--                            <span>@{{ result.is_printed | printStatus }}</span>--}}
{{--                        </div>--}}
{{--                    </td>--}}
{{--                    <td>--}}
{{--                        <div class="text-center">--}}
{{--                            <label class="checkbox__switch">--}}
{{--                                <input type="checkbox" v-model="result.is_wrong" @change="setWrong(result.id, $event)"/>--}}
{{--                                <span class="checkbox__slider checkbox__round"></span>--}}
{{--                            </label>--}}
{{--                            <span>@{{ result.is_printed | printStatus }}</span>--}}
{{--                        </div>--}}
{{--                    </td>--}}
                    <td class="text-center">
                        <a :href="certificate_url + result.print_code" target="_blank" @click="setPrinted(result.id)"><i class="fa fa-print"></i> Xem bằng</a>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="loading">
                <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                    <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="exam__results--paginate">
            <div>
                Hiển thị
                <select v-model="filter.per_page" @change="applyFilter">
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                </select>
                trong số @{{ loading ? '----' : total_result}} kết quả
            </div>
            <paginate
                    {{--                    v-model="filter.page"--}}
                    :page-count="filter.total_page"
                    :page-range="4"
                    :margin-pages="3"
                    :click-handler="changePage"
                    :prev-text="'&laquo;'"
                    :next-text="'&raquo;'"
                    :container-class="'pagination'"
                    :page-class="'page-item'"
                    :force-page="filter.page - 1"
            >
            </paginate>
        </div>
        <div class="modal fade" id="messageToAll" tabindex="-1" role="dialog" aria-labelledby="messageToAllLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="messageToAllLabel">Nhắn tin cho học viên</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <textarea class="form-control" rows="10" v-model="messageContent"></textarea>
                            <span class="exam__results--note" v-if="messageContentError">@{{ messageContentError }}</span>
                        </form>
                        <div class="exam__results--note">
                            <div><b>Lưu ý:</b></div>
                            <ul>
                                <li><span>Lọc đầy đủ Cấp độ - Tình trạng Đỗ/Trượt - Thiết bị - [Ngày thi] trước khi nhắn tin</span></li>
                                <li><span>Học viên chỉ nhận được 1 tin nhắn cho mỗi cấp độ tham gia thi</span></li>
                                <li><span>Tin nhắn đã được gửi không thể thu hồi. Cần check kỹ trước khi gửi</span></li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Huỷ</button>
                        <button type="button" class="btn btn-primary" @click="getUsersToSendMessage">Gửi</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        jQuery.browser = {};
        (function () {
            jQuery.browser.msie = false;
            jQuery.browser.version = 0;
            if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
                jQuery.browser.msie = true;
                jQuery.browser.version = RegExp.$1;
            }
        })();
    </script>
    {{--    Local plugins--}}
    <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
    <script>
        $(function () {
            // hô biến input thành datepicker xịn xò
            $('#time_from').datetimepicker({
                language: 'vi'
            }).on('dp.change', function (event) {
                exam__results.onChangeDatetime(event);
            });
            $('#time_to').datetimepicker({
                language: 'vi'
            }).on('dp.change', function (event) {
                exam__results.onChangeDatetime(event);
            });
        });
    </script>
    {{--     CDN--}}
    <script>
        $(document).on('show.bs.modal','#messageToAll', function () {
            exam__results.clearMessageContent();
        });
    </script>
    <script>
        var adminId = {!! json_encode(Auth::guard('admin')->user()->id) !!};
        var jlptUrl = "http://localhost:3333";
        var jlptSocket = "http://localhost:8008";

        //nếu là deploy trên web test
        if(window.location.href.indexOf("web-test") != -1){
            jlptUrl = "https://jlpt-test.dungmori.com";
            jlptSocket = "https://count-test.dungmori.com";
        }

        //nếu là deploy trên web thật
        if(window.location.href.indexOf("dungmori.com") != -1 && window.location.href.indexOf("web-test") == -1){
            jlptUrl = "https://mjt.dungmori.com";
            jlptSocket = "https://mjt-count.dungmori.com";
        }
    </script>
{{--    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.1/Chart.min.js"></script>--}}
{{--    <script src="https://unpkg.com/vue-chartjs/dist/vue-chartjs.min.js"></script>--}}
    <script src="{{asset('plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
    <script src="{{asset('plugin/vue-router/vue-router.js')}}"></script>
    <script src="{{asset('assets/backend/js/jlpt_results.js')}}?{{filemtime('assets/backend/js/jlpt_results.js')}}"></script>

@stop
