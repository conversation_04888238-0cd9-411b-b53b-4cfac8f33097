@extends('backend._default.dashboard')

@section('description')
    <PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON>ển
@stop
@section('keywords')
    dashboard
@stop
@section('author')
    dungmori.com
@stop
@section('title')
    Admin | Qu<PERSON>n lý bài thi
@stop

@section('assets')
    <script type="text/javascript" src="{{ asset('/plugin/ckeditor4/ckeditor.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/ckeditor4/adapters/jquery.js') }}"></script>
    <script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.11.1/jquery.validate.min.js"></script>
    <script type="text/javascript" src="{{ url('/js/ckfinder/ckfinder.js') }}"></script>
    <script>
        CKFinder.config({
            connectorPath: '/backend/ckfinder/connector'
        });
    </script>
    <script src="//cdn.jsdelivr.net/npm/sortablejs@1.8.4/Sortable.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/Vue.Draggable/2.20.0/vuedraggable.umd.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-toast-plugin/1.3.2/jquery.toast.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-toast-plugin/1.3.2/jquery.toast.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/4.4.0/bootbox.min.js"></script>
    <style>
        #page-wrapper {
            background: #fff !important;
        }

        .container-fluid {
            background: #fff;
            border-top: 1px solid #EEE;
        }

        .page-title {
            font-family: Arial, serif;
        }

        .mp3-audio {
            width: 450px;
            float: left;
        }

        .edit-mp3 {
            float: left;
            margin: 10px 0 0 20px;
        }

        .add-question {
            float: right;
            margin: 10px 0 0 20px;
        }

        .questions-container {
            width: 800px;
            margin: 30px auto 10px auto;
        }

        .nav-bar {
            width: 800px;
            margin: 0 auto;
        }

        .nav-tabs {
            width: 100%;
            float: left;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        .modal-dialog {
            width: 70%;
            height: 90%;
        }

        .row {
            margin: 0;
        }

        .modal-content {
            height: auto;
            border-radius: 0;
        }

        .reloadCKeditor {
            min-width: 80%;
        }

        .margin-top {
            margin-top: 1%;
        }

        span > p {
            display: inline;
        }

        th,
        td {
            vertical-align: middle !important;
        }

        td table {
            width: 100% !important;
        }

        td img {
            display: none;
        }

        th.fixed-width-content {
            width: 60%;
        }

        .box-answer {
            width: 40%;
            border: 1px dotted #CCC;
            background: #eaedf1;
            margin: 0 20px 25px 10px;
            padding: 3px 5px;
        }

        .table-bordered > tbody > tr > td {
            border-color: #fff;
        }

        .action {
            cursor: pointer;
        }

        .del {
            color: #f06;
        }

        .label {
            border-radius: 3px;
            padding: 3px 5px;
            margin-bottom: 10px;
        }

        .question-item:hover {
            border: 0.5px dashed #EEE;
        }
    </style>
@stop

@section('content')
    <div id="exam-detail-lesson">
        <div class="row" style="width: 100%; float: left;">
            <div class="nav-bar flex items-center justify-between">
                <h4 class="page-title pull-left">
                    <a class="btn btn-default" style="float: left;margin-left: -120px;"
                       href="{{ $exam->type == 1 ? route('exam.index') : route('exam.tests') }}">
                        <i class="glyphicon glyphicon-arrow-left"></i> Quay lại
                    </a>
                    @{{ exam.name }}
                    (@{{ exam.maximum_point }} điểm)
                </h4>
                <div class="flex items-center gap-2">
                    <input type="file" id="fileImportInput" ref="fileImportInput" style="display: none;" @change="importTest($event)" />
                    <a
                        href="{{ url('assets/excel/test-online.xlsx') }}"
                        class="btn btn-primary">Tải mẫu nhập</a>
                    <button
                        @click="$refs.fileImportInput.click()"
                        class="btn btn-success"
                    >Nhập trắc nghiệm</button>
                    
                </div>
            </div>
        </div>

        <div class="questions-container">

            <div style="width: 100%; float: left; margin-top: 30px;">
                <el-checkbox-group v-model="showedLessons" class="mb-5">
                    <el-checkbox-button v-for="(lesson, index) in lessons" :label="lesson.id" :key="`lessons-${index}`"
                                        name="">@{{ getLessonName(lesson, exam) }}
                    </el-checkbox-button>
                </el-checkbox-group>
                <el-switch style="display: block" v-model="splitTime" active-text="Tách thời gian (phút)"
                           inactive-text="Bài test gộp" class="mb-5">
                </el-switch>
                <div class="flex mt-2 mb-5" v-if="splitTime">
                    <el-input v-for="(time, index) in timeSplit" type="number" min="1" max="999"
                              :value="time" :placeholder="lessons[index].type | lessonType" class="mr-3"
                              @input="setLessonTime($event, index)" @keyup.native.enter="saveLessonTime"/>
                </div>
                <div style="width: 530px; float: left;" v-if="mp3Lesson">
                    <audio controls="controls" class="mp3-audio" id="audioSource" style="margin-bottom: 10px;">
                        <source type="audio/mpeg">
                        Your browser does not support the audio element.
                    </audio>
                    <br/>

                    <div class="audio-player" style=" overflow: hidden; width: 305px; float: left;">
                        Vietnam
                        <iframe
                                :src="'https://hlsplayer.stream/radio/skin4/index.html?title=&bg=000000&url=https://vn.dungmori.com/mp3/' +
                            mp3Name +
                                '/index.m3u8&facebook=&twitter='"
                                frameborder="0" marginheight="0" marginwidth="0" scrolling="no" width="630"
                                height="80"></iframe>
                    </div>
                    <br/>

                    <div class="audio-player"
                         style=" overflow: hidden; width: 305px; float: right; margin-right: -100px">
                        Japan
                        <iframe
                                :src="'https: //hlsplayer.stream/radio/skin4/index.html?title=&bg=000000&url=https://tokyo-v2.dungmori.com/mp3/' +
                                mp3Name + '/index.m3u8&facebook=&twitter='"
                                frameborder="0" marginheight="0" marginwidth="0" scrolling="no" width="630"
                                height="80"></iframe>
                    </div>
                </div>

                <a v-if="mp3Lesson" class="btn-default new-label edit-mp3" data-toggle="modal" data-target="#linkMp3"
                   @click="checkMp3Src">Edit mp3</a>

                <button class="btn-success new-label add-question" data-toggle="modal" data-target="#addQuestion"
                        @click="setUrlAddQuestion">
                    <span class="glyphicon glyphicon-plus"></span>Thêm câu hỏi
                </button>
            </div>


            <ul class="nav nav-tabs">
                <li v-for="(lesson, index) in exam.lessons" :class="{ active: index === active }">
                    <a href="javascript:void(0)" @click="changeTab(lesson, index)" :title="lesson.id"
                       v-if="lesson.pivot.status">
                        @{{ getLessonName(lesson, exam) }}
                        (@{{ countQuestions(lesson.questions) }} câu - <b>@{{ calculatePoint(lesson.questions, index +
                            1) }}đ</b>)
                    </a>
                </li>
                {{-- <li style="float: right;pointer-events: none;cursor:default" @click="return false"><a href="javascript:void(0)"><b>Tổng: @{{ this.points[1] + this.points[2] + this.points[3] }} điểm</b></a></li> --}}
            </ul>

            <draggable-table :lesson="lesson" @question-dragged="updateQuestionsPosition"
                             @clone-question="cloneThisQuestion" @edit-question="setUrlEditQuestion"
                             @delete-question="deleteQuestion"></draggable-table>

            <backend-modal v-if="cloneModal" @close="closeCloneModal">
                <h3 slot="header">Clone câu hỏi kèm câu trả lời</h3>
                <div slot="body">
                    <input class="form-control" v-on:keyup.enter="cloneThisQuestionToLesson"
                           v-model="cloneTargetLessonId"
                           placeholder="Nhập ID bài thi đích"/>
                    <span class="btn btn-success mt-5" @click="cloneThisQuestionToLesson">Clone</span>
                </div>
            </backend-modal>

            <div id="addQuestion" class="modal fade" role="dialog">
                <div class="modal-dialog">
                    <!-- Modal content-->
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                            <h4 class="modal-title">Thêm câu hỏi</h4>
                        </div>
                        <div class="modal-body">
                            <form class="form-inline" role="form" @submit.prevent>
                                <div class="row">
                                    <div class="col-sm-12 form-group">
                                        <textarea id="question" rows="10" cols="80"></textarea>
                                    </div>
                                </div>
                                <div class="btn" @click="showExplain = !showExplain">Giải thích</div>
                                <div class="row" v-show="showExplain || question.explain">
                                    <div class="col-sm-12 form-group">
                                        <textarea id="explain" rows="10" cols="80"></textarea>
                                    </div>
                                </div>
                                <div class="row margin-top">
                                    <div class="form-group col-sm-6 flex items-center">
                                        <input id="is_content" type="checkbox" v-model="is_content">
                                        <label for="is_content">Là nội dung</label>
                                    </div>
                                    <div class="form-group col-sm-6" v-show="!is_content">
                                        <input class="form-control" v-model="question.point" placeholder="Điểm">
                                    </div>
                                </div>
                                <div class="row margin-top">
                                    <div class="form-group col-sm-6 flex items-center">
                                        <select name="skill" id="" class="form-control" v-model="skill"
                                                placeholder="test">
                                            <option value="">Chọn kỹ năng</option>
                                            <option value="1">Từ vựng</option>
                                            <option value="2">Chữ hán</option>
                                            <option value="3">Ngữ pháp</option>
                                            <option value="4">Đọc hiểu</option>
                                            <option value="5">Nghe hiểu</option>
                                        </select>
                                    </div>
                                </div>
                                <div v-if="!is_content">
                                    <div class="row margin-top">
                                        <label>Nhập đáp án</label>
                                    </div>
                                    <div class="row">
{{--                                        <div class="form-control reloadCKeditor" contenteditable="true"--}}
{{--                                             data-text="dap an"></div>--}}
                                        <draggable v-model="answers" @start="drag=true" @end="drag=false"
                                                   @update="updateAnswerPosition">
                                            <div class="col-sm-6 box-answer" v-for="(answer, index) in answers"
                                                 :key="answer.id">
                                                <div class="form-control reloadCKeditor" contenteditable="true"
                                                     @input="updateText($event, index)"
                                                     @blur="updateText($event, index)"
                                                     data-text="dap an">
                                                </div>
                                                Đúng <input class="form-control" type="radio" :value="index"
                                                            v-model="picked">
                                            </div>
                                        </draggable>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 3%">
                                    <button class="btn btn-info" @click="addAnswer" v-show="!is_content">
                                        <i class="fa fa-plus-circle"></i> Thêm đáp án
                                    </button>
                                    <button class="btn btn-danger" v-if="Object.keys(answers).length > 2"
                                            @click="deleteAnswer">
                                        <i class="fa fa-minus-circle"></i> Bớt đáp án
                                    </button>
                                    <button type="button" class="btn btn-primary" @click="addQuestion"
                                            style="float: right; margin-right: 1%">Lưu
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div id="linkMp3" class="modal fade" role="dialog">
                <div class="modal-dialog">
                    <!-- Modal content-->
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                            <h4 class="modal-title">Link Mp3</h4>
                        </div>
                        <div class="modal-body" v-if="Object.keys(exam).length > 0">
                            <div class="flex" v-if="mp3Lesson">
                                <input class="form-control" @keyup.enter="updateMp3" v-model="mp3Lesson.mp3"
                                       @blur="checkMp3Src" placeholder="Nhập link mp3">
                                <button class="btn btn-primary" id="btn-upload-audio" @click="chooseFileAudio">Tải lên
                                    mp3
                                </button>
                                <input type="file" accept="audio/mp3" id="audio_file" @change="handleFileAudioChange"
                                       style="display: none">

                            </div>

                            <div class="row" style="margin-top: 3%">
                                <button type="button" class="btn btn-primary" style="float: right; margin-right: 1%"
                                        @click="updateMp3">Lưu
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script src="{{ asset('assets/js/modal.js') }}?{{ filemtime('assets/js/modal.js') }}"></script>
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        var editor = CKEDITOR.replace('content', {});
        CKFinder.setupCKEditor(editor);
        Vue.component('draggable-table',
            {
            props: ['lesson'],

            template: `
              <table v-if="lesson.questions.length > 0" class="table table-bordered m-0">
                <draggable v-model="lesson.questions" @start="drag=true" @end="drag=false" tag="tbody"
                           @update="$emit('question-dragged')">
                  <tr class="question-item" v-for="(question, index) in lesson.questions">
                    <td>
                      <div v-html="question.content" style="font-weight: bold; font-size: 16px; width: 90%;"></div>
                      <div class="col-sm-12">
                        <template v-for="(answer, idx) in question.answers">
                          <div class="col-sm-6" style="line-height: 2.3"
                               :class="{'del text-bold': _.filter(question.answers, function(item) {return _.replace(item.content, /[0-9](\\.)/, '') === _.replace(answer.content, /[0-9](\\.)/, '')}).length > 1}">
                            <i v-if="answer.is_true != true" class="fa fa-square-o" aria-hidden="true"></i>
                            <i v-if="answer.is_true == true" class="fa fa-check-square" aria-hidden="true"></i>
                            <span>@{{ stripTags(answer.content) }}</span>
                          </div>
                        </template>
                      </div>
                    </td>
                    <td class="text-center" style="text-align: right; width: 100px;">
                      <input type="checkbox" :checked="selectedQuestion.includes(index)" :ref="'checkbox' + index"
                             @click="toggleCheck($event, index)"/>
                      <br/>
                      <select v-model="question.skill" name="" id="" class="form-control text-sm"
                              @change="selectSkill(question.id, $event)">
                        <option :value="null">Không skill</option>
                        <option v-for="(value, name) in  skillTypes" :value="name">@{{ value }}</option>
                      </select> <br/>
                      <label v-if="!question.is_content" class="label label-default">@{{ question.point }}
                        điểm</label><br/>
                      <span class="action edit" @click="$emit('clone-question', question)">Clone</span> &nbsp;
                      <span class="action edit" @click="$emit('edit-question', question)">Edit</span> &nbsp;
                      <span class="action del" @click="$emit('delete-question', question)">Delete</span>
                    </td>
                  </tr>
                </draggable>
              </table>`,
            data: function () {
                return {
                    selectedQuestion: [],
                    beginCheckbox: null,
                    skillTypes: {
                        1: 'Từ vựng',
                        2: 'Chữ Hán',
                        3: 'Ngữ pháp',
                        4: 'Đọc hiểu',
                        5: 'Nghe hiểu',
                    }
                }
            },
            computed: {
                selectedQuestionIds: function () {
                    return this.selectedQuestion.map(q => this.lesson.questions[q].id)
                }
            },
            methods: {

                //lấy ra text từ html
                stripTags: function (text) {

                    if (text == null) return "";
                    return text.replace(/(<([^>]+)>)/ig, "");
                },
                toggleCheck: function (event, idx) {
                    event.stopPropagation();
                    if (!event.shiftKey || this.beginCheckbox.idx === undefined) {
                        this.beginCheckbox = {
                            idx: idx,
                            checked: event.target.checked
                        }
                    } else {
                        this.selectedQuestion = []
                        for (let i = this.beginCheckbox.idx; i <= idx; i++) {
                            if (this.beginCheckbox.checked) {
                                this.selectedQuestion.push(i)
                            } else {
                                this.selectedQuestion = this.selectedQuestion.filter(o => o !== i);
                            }
                        }
                    }
                    this.$forceUpdate()
                },
                selectSkill: function (id, event) {
                    let ids = []
                    if (this.selectedQuestionIds.includes(id)) {
                        ids = this.selectedQuestionIds
                    } else {
                        ids = [id]
                    }
                    const data = {
                        ids: ids,
                        skill: event.target.value
                    }
                    $.post(window.location.origin + '/backend/thi-thu/lesson/questions/change-skill', data,
                        function (res) {
                            if (res == 1) {
                                toastr.success('Thay đổi thành công!!');
                            } else {
                                toastr.error(res.msg);
                            }
                        })
                }
            }
        });
    </script>
    <script src="{{ asset('/assets/backend/js/jlpt_detail.js') }}"></script>
@stop
