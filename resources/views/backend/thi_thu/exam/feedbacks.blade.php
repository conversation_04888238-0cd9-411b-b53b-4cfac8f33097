@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON>ển @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | Qu<PERSON>n lý bài thi @stop

@section('assets')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.css">
    <link href="https://gitcdn.github.io/bootstrap-toggle/2.2.2/css/bootstrap-toggle.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('/plugin/vue-toastr/vue-toastr.min.css') }}">

    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="{{ asset('plugin/bootstrap-datetimepicker/vue-bootstrap-datetimepicker.min.js') }}"></script>
    <script src="https://gitcdn.github.io/bootstrap-toggle/2.2.2/js/bootstrap-toggle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/4.4.0/bootbox.min.js"></script>

    <style type="text/css">
        .new-label{font-size: 12px;}
        .toggle.ios, .toggle-on.ios, .toggle-off.ios { border-radius: 4px; height: 25px !important; }
        .toggle.ios .toggle-handle { background-color: #fff !important; }
        .table>tbody>tr>td{padding: 5px 10px; vertical-align: middle;}
        .form-horizontal .control-label {
            padding-top: 0;
        }
    </style>
@stop

@section('content')
<div class="grid grid-cols-3 gap-3 w-screen ">
    @foreach ($feedbacks as $skill => $group)
      <div class="bg-white p-10 shadow rounded-lg">
        <div class="font-semibold text-lg">{{ SKILL_LABEL[$skill] }}</div>
        <div class="flex flex-col gap-3">
          @foreach($group as $feedback)
            <form method="POST" action="{{ route('exam.feedbacks-update', ['id' => $feedback->id]) }}" id="form-{{ $feedback->id }}">
              @csrf
              @method('PUT')
              <div class="font-semibold">{{ $feedback->min_point }}% - {{ $feedback->max_point }}%</div>
              <textarea name="content" id="" cols="30" rows="2" class="form-control">{{ $feedback->content }}</textarea>
              <input type="submit" class="btn btn-success mt-2" value="Lưu" name="submit-btn">
            </form>
          @endforeach
        </div>
      </div>
    @endforeach
</div>

@stop
