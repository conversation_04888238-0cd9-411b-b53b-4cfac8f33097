<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>In bài thi</title>
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('/plugin/bootstrap/css/bootstrap.min.css')}}">
    <link type="text/css" rel="stylesheet" href="{{ asset('plugin/font-awesome/css/font-awesome.min.css') }}">
    <style>
        .exam-container {width: 900px; margin: 30px auto 10px auto;}
        span > p {display: inline; }
        table {
            width: 800px !important;
        }
        .title {
            margin-bottom: 40px !important;
        }
        .title:not(:first-child) {
            margin-top: 50px !important;
            page-break-before: always;

        }
        .logo{
            display: block;
            margin: 0 auto;
            padding: 20px;
            width: 25%;
        }
    </style>
</head>
<body>
    <div class="exam-container" id="exam-container">

        <div onclick="window.print();" style="text-align: center; font-size: 30px; font-weight: bold; ">
            <img src="{{asset('/assets/img/new_home/footer-logo.svg')}}" alt="" class="logo">
            {{ $exam->name }}
        </div>
        <div class="questions-container" style="line-height: 32px">
            @foreach($lessons as $lesson)
                @foreach ($lesson->questions as $question)
                    @if(!$question->is_content)
                        <div class="question-item" style="page-break-after: auto;">
                            <div style="font-weight: bold; display: flex; align-items: center; gap: 5px;">
                                <div class="text-black">Số người làm: {{ $question->totalAttempt }} | </div>
                                <div class="text-warning">Bỏ qua: {{ $results->count() - $question->totalAttempt }} | </div>
                                <div class="text-success">Làm đúng: {{ $question->correctAttempt }} | </div>
                                <div class="text-danger">Làm sai: {{ $question->falseAttempt }}</div>
                            </div>
                            <div style="font-weight: bold; font-size: 16px; width: 90%;">{!!html_entity_decode($question->content)!!}</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr">
                                @foreach($question->answers as $answer)
                                    <div>
                                        <input type="radio" disabled/>&nbsp;
                                        <span>{!! html_entity_decode($answer->content) !!}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                @endforeach
            @endforeach
        </div>
    </div>
<script type="text/javascript" >
    function printLessonName() {

    }
    var types =  {
        1: 'Từ vựng - Chữ hán',
        2: 'Ngữ pháp - Đọc hiểu',
        3: 'Nghe hiểu'
    };
    var N1Types = {
        1: 'Từ vựng - Chữ hán - Ngữ pháp',
        2: 'Đọc hiểu',
        3: 'Nghe hiểu'
    };
</script>
</body>
</html>
