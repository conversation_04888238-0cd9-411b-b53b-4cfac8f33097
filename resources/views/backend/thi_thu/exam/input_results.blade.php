@extends('backend._default.dashboard')

@section('description') Quản lý kết quả đầu vào @stop
@section('keywords') Kết quả đầu vào @stop
@section('author') dungmori.com @stop
@section('title') Admin | Kết quả đầu vào @stop

@section('content')
<div id="input-results">

    <div class="row" style="margin: 20px 0;">
        <h4 class="page-title pull-left">
            Quản lý kết quả đầu vào
        </h4>
        <div class="form-inline" style="display: flex; justify-content: flex-end; gap: 5px">
            <input type="text" class="form-control mr-sm-2"
                   v-model="keywords"
                   @keyup.enter="applySearch()"
                   placeholder="Nhập từ khóa cần tìm"
            >
            <vue-datepicker placeholder="Từ ngày" v-model="dateFrom" format="dd/MM/yyyy" class="form-control" clear-button="true"></vue-datepicker>
            <vue-datepicker placeholder="Đến ngày" v-model="dateTo" format="dd/MM/yyyy" class="form-control" clear-button="true"></vue-datepicker>
            <button class="btn btn-primary" @click="inputExcelExport()">Export</button>
            <a type="button" class="btn btn-info" @click="applySearch()">Tìm kiếm</a>
        </div>
    </div>

    <div class="input-table" v-if="results.length > 0">
        <table class="table table-borderless" id="data-table">
            <thead>
            <tr>
                <th class="text-center" style="width: 60px;">Mã số</th>
                <th class="text-center">Bài học</th>
                <th class="text-center">Điểm</th>
                <th>Người dùng</th>
                <th>Điện thoại</th>
                <th>Email</th>
                {{-- <th>Địa chỉ</th> --}}
                <th>Tuổi</th>
                <th>Nghề nghiệp</th>
                <th>Nơi ở</th>
                <th>Mục tiêu</th>
                <th class="text-center">Thời gian test</th>
            </tr>
            </thead>
            <tbody>
                <tr class="item" v-for="result in results">
                    <td class="text-center">
                        @{{result.id}}
                    </td>
                    <td class="text-center">
                        <span class="label label-success">
                            @{{result.lesson_name}}
                        </span>
                    </td>
                    <td class="text-center">
                        @{{result.grade}}/@{{result.total_grade}}
                    </td>
                    <td>
                        <p><i class="fa fa-user"></i>@{{ result.user_info.name }}</p>
                    </td>
                    <td>
                        <p><i class="fa fa-phone"></i>@{{ result.user_info.phone }}</p>
                    </td>
                    <td>
                        <p><i class="fa fa-envelope"></i>@{{ result.user_info.email }}</p>
                    </td>
                    {{-- <td>
                        <p v-if="result.user_info.address">
                            <i class="fa fa-location-arrow"></i>
                            @{{result.user_info.address}}
                        </p>
                        <p v-else><i class="fa fa-location-arrow"></i> N/A </p>
                    </td> --}}
                    <td>
                        <p v-if="result.user_info.age">
                            @{{result.user_info.age}}
                        </p>
                        <p v-else><i class="fa fa-location-arrow"></i> N/A </p>
                    </td>
                    <td>
                        <p v-if="result.user_info.career">
                            @{{
                                result.user_info.career == 'student' ? 'Học sinh, sinh viên' :
                                result.user_info.career == 'staff' ? 'Nhân viên văn phòng' :
                                result.user_info.career == 'it' ? 'IT & kỹ thuật' :
                                result.user_info.career == 'teacher' ? 'Giáo viên/Nghiên cứu' :
                                result.user_info.career == 'business' ? 'Tự kinh doanh/Làm tự do' :
                                result.user_info.career == 'export' ? 'Xuất khẩu lao động/Du học sinh' :
                                result.user_info.career == 'study' ? 'Chưa đi làm' : 'Khác'
                            }}
                        </p>
                        <p v-else><i class="fa fa-location-arrow"></i> N/A </p>
                    </td>
                    <td>
                        <p v-if="result.user_info.place">
                            @{{result.user_info.place == 'vn' ? 'VN' : result.user_info.place == 'jp' ? 'JP' : 'Khác' }}
                        </p>
                        <p v-else><i class="fa fa-location-arrow"></i> N/A </p>
                    </td>
                    <td>
                        <p v-if="result.user_info.purpose">
                            @{{
                                result.user_info.purpose == 'certificate' ? 'Thi chứng chỉ' :
                                result.user_info.purpose == 'communication' ? 'Giao tiếp hàng ngày' :
                                result.user_info.purpose == 'hobby' ? 'Sở thích' :
                                result.user_info.purpose == 'teaching' ? 'Giảng dạy&Nghiên cứu' : 'Khác'
                            }}
                        </p>
                        <p v-else><i class="fa fa-location-arrow"></i> N/A </p>
                    </td>
                    <td class="text-center">
                        @{{result.created_at}}
                    </td>
                </tr>
            </tbody>
        </table>

        <paginate
                :page-count="paginator.pageCount"
                :prev-text="'«'"
                :next-text="'»'"
                :click-handler="changePage"
                :container-class="'pagination'"
        >
        </paginate>
    </div>
    <div v-else>
        <span>Không tìm thấy dữ liệu nào</span>
    </div>
</div>

<script src="{{ asset('/plugin/vuejs-datepicker/vuejs-datepicker.min.js') }}"></script>
<script src="{{asset('plugin/vue/vue.min.js')}}"></script>
<script src="{{asset('plugin/jquery/axios.min.js')}}"></script>
<script src="{{asset('plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
<script type="text/javascript">
    var inputResults =  new Vue({
        el: '#input-results',
        components:{
            paginate: VuejsPaginate,
            'vue-datepicker': vuejsDatepicker
        },
        data(){
            return {
                results: [],
                dateFrom: null,
                dateTo: null,
                paginator: {
                    pageCount: 5,
                    page: 1,
                    perPage: 20
                },
                keywords: '',
            }
        },
        computed: {
            dateTimeFrom() {
                return this.dateFrom ? moment(this.dateFrom).format('YYYY-MM-DD') : undefined;
            },
            dateTimeTo() {
                return this.dateTo ? moment(this.dateTo).format('YYYY-MM-DD') : undefined;
            },
        },
        methods:{
            initTable(){
                let vm = this;
                let url = window.location.origin+'/backend/thi-thu/input-results/get-data';
                let data = {
                    dateFrom: vm.dateTimeFrom,
                    dateTo: vm.dateTimeTo,
                    keywords: vm.keywords,
                    page: vm.paginator.page,
                    perPage: vm.paginator.perPage,
                }
                axios.get(url, {params: data}).then((res) => {
                    if (res.status == 200) {
                        vm.results = res.data.data;
                        vm.paginator.pageCount = res.data.last_page
                        vm.results = vm.results.map((result) => {
                            result.user_info = JSON.parse(result.user_info);
                            return result;
                        });
                    }
                });
            },
            changePage(e) {
                let vm = this;
                vm.paginator.page = e;
                vm.initTable();
            },
            applySearch() {
                this.paginator.page = 1
                this.initTable();
            },
            inputExcelExport() {
                let vm = this;
                let url = window.location.origin + '/backend/thi-thu/input-results/export-data';
                let data = {
                    dateFrom: vm.dateFrom,
                    dateTo: vm.dateTo,
                    keywords: vm.keywords,
                    page: vm.paginator.page,
                }
                axios.post(url, data).then((res) => {
                    if (res.data.code == 200) {
                        console.log('true');
                        window.location = '/storage/upload/excel/inputResults.xlsx';
                    }
                });
            },
        },
        mounted: function () {
            this.initTable();
        }
    })
</script>
@stop
