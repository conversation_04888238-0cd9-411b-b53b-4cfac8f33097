@extends('backend._default.dashboard')

@section('description') Quản lý kết quả đầu vào @stop
@section('keywords') kết quả đầu vào @stop
@section('author') dungmori.com @stop
@section('title') Admin | Kết quả đầu vào @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
@stop

@section('content')
    <div class="flex items-center gap-4">
        <input placeholder="Nhập ID lịch test" class="form-control w-[200px]" id="communityExamId" />
        <input placeholder="Nhập ID người dùng" class="form-control w-[200px]" id="userId" />
        <input placeholder="Nhập ID bài test" class="form-control w-[200px]" id="examId" />
        <button class="btn btn-primary" onclick="search()">Tìm kiếm</button>
    </div>

    <table class="table">
        <thead class="thead-light">
        <tr>
            <th scope="col">ID</th>
            <th scope="col">ID bài</th>
            <th scope="col">ID lịch</th>
            <th scope="col">ID user</th>
            <th scope="col">Điểm 1</th>
            <th scope="col">Điểm 2</th>
            <th scope="col">Điểm 3</th>
            <th scope="col">Tổng</th>
            <th scope="col">Trượt/Đỗ</th>
            <th scope="col">Data 1</th>
            <th scope="col">Data 2</th>
            <th scope="col">Data 3</th>
            <th scope="col">Làm trên</th>
            <th scope="col">Tgian làm bài</th>
            <th scope="col">Tgian nộp bài</th>
            <th scope="col">Tgian bắt đầu</th>
        </tr>
        </thead>
        <tbody>
        @foreach($results as $result)
            <tr>
                <td>{{ $result->id }}</td>
                <td>{{ $result->exam_id }}</td>
                <td>{{ $result->community_exam_id }}</td>
                <td>{{ $result->user_id }}</td>
                <td>{{ $result->score_1 }}</td>
                <td>{{ $result->score_2 }}</td>
                <td>{{ $result->score_3 }}</td>
                <td>{{ $result->total_score }}</td>
                <td>{{ $result->is_passed }}</td>
                <td>{{ is_null($result->data_1) || $result->data_1 == '{}' ? $result->data_1 : 'Có làm' }}</td>
                <td>{{ is_null($result->data_2) || $result->data_2 == '{}' ? $result->data_2 : 'Có làm' }}</td>
                <td>{{ is_null($result->data_3) || $result->data_3 == '{}' ? $result->data_3 : 'Có làm' }}</td>
                <td>{{ $result->platform }}</td>
                <td>{{ $result->time_start }}</td>
                <td>{{ $result->submit_at }}</td>
                <td>{{ $result->created_at }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
    <script type="text/javascript">
        function search() {
            let communityExamId = document.querySelector('#communityExamId').value;
            let userId = document.querySelector('#userId').value;
            let examId = document.querySelector('#examId').value;
            window.location.href = `/backend/thi-thu/tests/test-result?communityExamId=${communityExamId}&userId=${userId}&examId=${examId}`;
        }
    </script>
@endsection
