@extends('backend._default.dashboard')

@section('description') Quản lý kết quả đầu vào @stop
@section('keywords') kết quả đầu vào @stop
@section('author') dungmori.com @stop
@section('title') Admin | Kết quả đầu vào @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Kết quả thi Tuyển sinh (Có code)
        </h4>

        <div class="excel-box" style="float: left; margin-left: 30px;">
            <select class="form-control" id="exam-id">
                @foreach($exams as $exam)
                    <option value="{{$exam->exam_id}}">Bài {{$exam->exam_id}} - {{$exam->course}}, thi ngày {{date('d-m-Y', strtotime($exam->created_at))}}</option>
                @endforeach
            </select>
            <a class="search btn btn-info" style="float: right; margin: -37px -100px 0 0;" onclick="exportExcel()">Xuất Excel</a>
        </div>

        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <input type="text" class="form-control mb-2 mr-sm-2" name="keyword" placeholder="Tìm sinh viên">
            <a type="button" class="search btn btn-info mb-2">Tìm kiếm</a>
            <a href='{{url('backend/thi-thu/baoson-results')}}' class="btn btn-default mb-2">refresh</a>
        </form>
    </div>
    <div class="table_voucher">
        <table class="table table-borderless" id="data-table">
            <thead>
            <tr>
                <th style="width: 60px;" class="text-center">#</th>
                <th style="width: 80px;"  class="text-center">Bài thi</th>
                <th style="width: 150px;" class="text-center">Điểm thành phần</th>
                <th style="width: 90px;" class="text-center">Tổng điểm</th>
                <th style="width: 90px;" class="text-center">Đỗ/Trượt</th>
                <th style="width: 230px;">Họ và tên</th>
                <th style="width: 120px;">Email</th>
                <th style="width: 100px;">Ngày sinh</th>
                <th>Số điện thoại</th>
                <th>Địa chỉ</th>
                <th style="width: 200px;" class="text-center">Ngày thi</th>
            </tr>
            </thead>
            <tbody>
            @foreach($data as $item)
                <tr class="item{{$item->id}}">
                    <td class="text-center">{{$item->id}}</td>
                    <td class="text-center"> {{$item->getExamName()}} </td>
                    <td class="text-center">{{$item->score_1}} / {{$item->score_2}} / {{$item->score_3}} </td>
                    <td class="text-center">{{$item->total_score}}</td>
                    <td class="text-center"> {{ $item->passOrNotPass() == true ? 'Đạt ✓✓✓': 'Trượt' }} </td>
                    @if($item->certificate_info == null)
                        <td>-</td><td>-</td><td>-</td><td>-</td><td>-</td>
                    @else
                        <?php $user = json_decode($item->certificate_info); ?>
                        <td> <p>{{$user->fullname}} </td>
                        <td> <p>{{$user->email}} </p> </td>
                        <td> <p>{{$user->dob}}</p> </td>
                        <td> <p>{{$user->mobile}}</p> </td>
                        <td> <p>{{$user->address}} </p> </td>
                    @endif
                    <td class="text-center">{{$item->created_at}}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
        {{ $data->render() }}
    </div>
    
    <script src="{{ asset('/plugin/jquery/jquery.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $.ajaxSetup({headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') } });
        });
        
        //xuất excel
        function exportExcel(){
            console.log("xuất excel", $("#exam-id").val() );
            var url = window.location.origin + "/backend/thi-thu/baoson-results/export/" + $("#exam-id").val();
            window.open(url);
        }

    </script>

@stop