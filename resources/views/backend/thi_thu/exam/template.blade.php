<table>
    <thead>
    <tr>
        <td>ID</td>
{{--        <td>Đ<PERSON><PERSON> chỉ</td>--}}
        <td>User ID</td>
{{--        <td>Email</td>--}}
{{--        <td>Info</td>--}}
        <th>Đ<PERSON>a chỉ</th>
        <th>Tên</th>
        <th>SĐT</th>
        <th>Admin</th>
        <td>Cấp độ</td>
        <td>Quốc gia</td>
{{--        <td>Hoàn thành</td>--}}
{{--        <td>Sai thông tin</td>--}}
        <td>Bài 1</td>
        <td>Bài 2</td>
        <td>Bài 3</td>
        <td>Tổng điểm</td>
        <td>Đỗ/Trượt</td>
{{--        <td>文字・語彙</td>--}}
{{--        <td>言語知識・読解</td>--}}
{{--        <td>聴解</td>--}}
        <td><PERSON><PERSON><PERSON> thi</td>
    </tr>
    </thead>
    <tbody>
    @foreach ($results as $result)
        <tr>
            <td>{{$result->id}}</td>
{{--            <td>--}}
{{--                @if (!is_null($result->certificate_info))--}}
{{--                    <div>{{ json_decode($result->certificate_info)->address }}</div>--}}
{{--                @endif--}}
{{--            </td>--}}
            <td>{{$result->user_id}}</td>
{{--            <td>--}}
{{--                @if (is_null($result->certificate_info))--}}
{{--                    <div>{{$result->user_info['email']}}</div>--}}
{{--                @else--}}
{{--                    <div>{{ json_decode($result->certificate_info)->email}}</div>--}}
{{--                @endif--}}
{{--            </td>--}}
            <td><div>{{ $result->province }}</div></td>
            <td><div>{{ $result->certificate_info ? json_decode($result->certificate_info)->fullname : '--' }}</div></td>
            <td><div>{{ $result->certificate_info ? json_decode($result->certificate_info)->mobile : '--' }}</div></td>
            <td><div>{{ $result->admin_id }}</div></td>
{{--            <td>--}}
{{--                @if (is_null($result->certificate_info))--}}
{{--                    <div>{{ $result->user_info['name'] }} - </div>--}}
{{--                    <div>{{ $result->user_info['phone_number'] ? $result->user_info['phone_number'] : ($result->user_info['phone'] ? $result->user_info['phone'] : '--') }}</div>--}}
{{--                @else--}}
{{--                    <div>{{ json_decode($result->certificate_info)->fullname }} - </div>--}}
{{--                    <div>{{ json_decode($result->certificate_info)->mobile }} - </div>--}}
{{--                    <div>{{ json_decode($result->certificate_info)->postalcode }}</div>--}}
{{--                @endif--}}
{{--            </td>--}}

            <td>{{ $result->course_name }}</td>
            <td>{{ !is_null($result->vn) ? 'VN' : (!is_null($result->jp) ? 'JP' : 'Chưa xử lý') }}</td>
{{--            <td>{{ $result->is_printed == 1 ? 'Xong' : '' }}</td>--}}
{{--            <td>{{ $result->is_wrong == 1 ? 'Có' : '' }}</td>--}}
            <td>{{ $result->grade_1 }}</td>
            <td>{{ $result->grade_2 }}</td>
            <td>{{ $result->grade_3 }}</td>
            <td>{{ $result->total_score }}</td>
            <td>{{ $result->is_passed ? 'Đỗ' : 'Trượt'}}</td>

            <td>{{ $result->created_at }}</td>
        </tr>
    @endforeach
    </tbody>
</table>