@extends('backend._default.dashboard')

@section('description') Quản lý kết quả đầu vào @stop
@section('keywords') kết quả đầu vào @stop
@section('author') dungmori.com @stop
@section('title') Admin | Kết quả đầu vào @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
@stop

@section('content')
    <div class="flex items-center gap-4">
        <input placeholder="Nhập ID lớp" class="form-control w-[200px]" id="groupId" />
        <button class="btn btn-primary" onclick="search()">Tìm kiếm</button>
    </div>

    <table class="table">
        <thead class="thead-light">
        <tr>
            <th scope="col">ID</th>
            <th scope="col">ID nhóm</th>
            <th scope="col">ID bài test</th>
            <th scope="col">Tiêu đề</th>
            <th scope="col">Bắt đầu</th>
            <th scope="col">Kết thúc</th>
            <th scope="col">Tính điểm</th>
            <th scope="col">Tgian tạo</th>
        </tr>
        </thead>
        <tbody>
        @foreach($schedules as $schedule)
            <tr>
                <th scope="row">{{ $schedule->id }}</th>
                <td>{{ $schedule->group_id }}</td>
                <td>{{ $schedule->exam_id }}</td>
                <td>{{ $schedule->title }}</td>
                <td>{{ $schedule->time_start }}</td>
                <td>{{ $schedule->time_end }}</td>
                <td>{{ $schedule->counted }}</td>
                <td>{{ $schedule->created_at }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
    <script type="text/javascript">
        function search() {
            let id = document.querySelector('#groupId').value;
            window.location.href = '/backend/thi-thu/tests/schedule/' + id;
        }
    </script>
@endsection
