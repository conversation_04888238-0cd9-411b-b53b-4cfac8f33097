@extends('backend._default.dashboard')

@section('description') Quản l<PERSON> kết quả JLPT @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý kết quả JLPT @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/jlpt_results.css') }}?{{filemtime('assets/backend/css/jlpt_results.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
    <style type="text/css">
        .table>tbody>tr>td{padding: 8px;}
    </style>
@stop

@section('content')

    <div class="top-container" style="background: #fff; width: 1000px; margin-top: 20px; margin: 20px auto;">
      
      <p style="padding: 10px 0 0 10px; margin-bottom: 0;"><b>300 anh em điểm cao</b></p>            
      <table class="table" style="color: #222;">
        <thead>
          <tr>
            <th style="width: 40px">#</th>
            <th style="width: 60px">Id</th>
            <th style="width: 60px">Level</th>
            <th style="width: 130px">Điểm</th>
            <th style="width: 60px">UserId</th>
            <th style="width: 260px">User</th>
            <th>Địa chỉ</th>
            <th>#</th>
          </tr>
        </thead>
        <tbody>
            @foreach($arr as $index => $nx)
                @foreach($nx as $key => $item)
                <tr style="background: @if($index%2 != 0) #daebe4; @else #EEE; @endif" class="row-{{$item->id}}">
                    <td>#{{$key+1}}</td>
                    <td>{{$item->id}}</td>
                    <td>{{$item->course}}</td>
                    <td>{{$item->score_1}}+{{$item->score_2}}+{{$item->score_3}} = <b>{{$item->total_score}}</b></td>
                    <td class="userid">{{$item->user_id}}</td>
                    <td>
                        {{-- <img width="30px" height="30px" src="/cdn/avatar/small/{{$item->user->avatar}}"/> --}}
                        <b>{{$item->user->name}} </b>

                        {{-- {{$item->conversation}} --}}

                        @if($item->conversation)
                            <a href="{{url('/backend/chat#')}}{{$item->conversation->id}}" target="_blank">
                                <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;" title="nhắn tin"></i>
                            </a>
                        @else
                            <i class="fa fa-comments fa-comments-{{$item->user_id}}" style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                             onclick="initConversation({{$item->user_id}})"></i>
                        @endif
                        
                    </td>
                    <td><span id="address-{{$item->id}}">{{$item->address}}</span> <a onclick="editAddress('{{$item->id}}', '{{ trim(preg_replace('/\s\s+/', ' ', $item->address)) }}')">Sửa</a></td>
                    <td>pdf</td>
                </tr>
                @endforeach
            @endforeach

        </tbody>
      </table>
    </div>


    <script type="text/javascript">
    $.ajaxSetup({headers: {'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') } });

    //tạo cuộc hội thoại với user chưa có
    function initConversation(userid){
        $.ajax({
            type: 'post',
            url: '/backend/user/create-conversation',
            data: {
                'id': userid
            },
            success: function(response) {
                window.open(window.location.origin+ "/backend/chat#"+ response, "_blank");
            }
        });
    }

    function editAddress(id, address){

        var newA = prompt("Nhập địa chỉ mới", address);
        if (newA != null && newA != '' && newA != "") {

            //console.log(data);
            $.post(window.location.origin +"/backend/thi-thu/changeAddress", { id: id, address: newA }, function(response){
                if(response == "success") $("#address-"+ id).text(newA);
                else alert("lỗi update");
            });

            
        }
    }


</script>

@stop


