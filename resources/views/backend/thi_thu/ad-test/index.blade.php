@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON>ển @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | Qu<PERSON>n lý bài thi @stop

@section('assets')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.css">
    <link href="https://gitcdn.github.io/bootstrap-toggle/2.2.2/css/bootstrap-toggle.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('/plugin/vue-toastr/vue-toastr.min.css') }}">

    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="{{ asset('plugin/bootstrap-datetimepicker/vue-bootstrap-datetimepicker.min.js') }}"></script>
    <script src="https://gitcdn.github.io/bootstrap-toggle/2.2.2/js/bootstrap-toggle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/4.4.0/bootbox.min.js"></script>

    <style type="text/css">
        .new-label{font-size: 12px;}
        .toggle.ios, .toggle-on.ios, .toggle-off.ios { border-radius: 4px; height: 25px !important; }
        .toggle.ios .toggle-handle { background-color: #fff !important; }
        .table>tbody>tr>td{padding: 5px 10px; vertical-align: middle;}
    </style>
@stop

@section('content')


    <div id="root">
        <div class="bg-title flex justify-between">
            <h4 class="page-title pull-left"> Quản lý bài kiểm tra </h4>
            <div class="flex items-center gap-3">
                <select class="form-control" v-model="filter.level">
                    <option :value="undefined">Chọn cấp độ</option>
                    <option :value="`N${level}`" v-for="level in 5">@{{ `N${level}` }}</option>
                </select>
                <input class="form-control" v-model="filter.keyword" placeholder="Nhập tên bài test" @keypress.enter="getExams" />
                <button class="btn btn-primary" data-toggle="modal"
                        data-target="#addExam" @click="setAddExamUrl">
                    <span class="glyphicon glyphicon-plus"></span> Thêm bài kiểm tra
                </button>
                <button class="btn bg-blue-500 text-white font-base hover:text-white"@click="triggerExcelInput">
                    <i v-if="loading" class="fa fa-spinner fa-spin"></i> Nhập Excel
                </button>
                <a href="{{ asset('assets/demo/ad-test.xlsx') }}" class="btn bg-lime-400 text-black font-base">
                    Mẫu Excel
                </a>
                <input type="file" class="hidden" @change="uploadExcel" ref="file"/>
            </div>
        </div>

        <table v-if="exams.length" class="table table-bordered m-0">
            <thead>
            <tr>
                <th class="text-center" style="width: 90px;">#Mã số</th>
                <th class="text-center">Tên bài</th>
                <th class="text-center">T.gian làm bài</th>
                <th class="text-center">T.gian mở</th>
                <th class="text-center">T.gian đóng</th>
                <th class="text-center" style="width: 230px;">Hành động</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(exam, index) in exams">
                <td class="text-center" scope="row">@{{ exam.id }}</td>
                <td class="text-center" scope="row" style="text-align: left;">
                    <label class="btn-default new-label">@{{ exam.course }}</label>
                    @{{ exam.name }}
                </td>
                <td class="text-center" scope="row"><b>@{{ exam.maximum_time }}</b> phút</td>
                <td class="text-center" scope="row">@{{ exam.time_start | formatDate }}</td>
                <td class="text-center" scope="row">@{{ exam.time_end | formatDate }}</td>
{{--                <td class="text-center" scope="row">--}}
{{--                    <input data-style="ios" data-onstyle="success" data-on="Bật" data-off="Tắt" data-style="slow" data-size="mini"--}}
{{--                     type="checkbox" class="toggle-processing" :id="exam.id" :checked="exam.isProcessing" onchange="vm.changeProcessing(this)"/>--}}
{{--                </td>--}}
{{--                <td class="text-center" scope="row">--}}
{{--                    <input data-style="ios" data-onstyle="success" data-on="Bật" data-off="Tắt" data-style="slow" data-size="mini"--}}
{{--                           type="checkbox" class="toggle-schedule" :id="exam.id" :checked="exam.notice" onchange="vm.changeScheduleNotification(this)"/>--}}
{{--                </td>--}}
{{--                <td class="text-center" scope="row">@{{ exam.created_at }}</td>--}}
                <td class="text-center" scope="row" width="300px">
                    <template v-if="exam.lessons_count > 0">
                        <a class="btn btn-success new-label" :href="'/backend/thi-thu/exam/' + exam.id">
                            Quản lý câu hỏi
                        </a>
                    </template>
                    <template v-if="exam.lessons_count > 0">
                        <a class="btn btn-success new-label" :href="'/backend/thi-thu/print/' + exam.id">
                            In
                        </a>
                    </template>
                    <template v-else>
                        <button type="button" class="btn-default new-label" @click="chooseLessons(exam)" data-toggle="modal"
                                data-target="#addLessons">Chọn lessons
                        </button>
                    </template>
                    <button type="button" class="btn-default new-label" @click="setEditExamUrl(exam)">sửa</button>
{{--                    <button type="button" class="btn-default new-label" @click="hiddenExam(exam)">Ẩn</button>--}}
{{--                    <button type="button" class="btn-default new-label" @click="resetExam(exam.id)">Reset</button>--}}
                    {{-- <button type="button" class="btn-danger new-label" @click="deleteExam(exam)">xóa</button> --}}
                </td>
            </tr>
            </tbody>
        </table>
        <div id="addExam" class="modal fade" role="dialog">
            <div class="modal-dialog">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">@{{ Object.keys(examChosen).length > 0 ? 'Sửa' : 'Thêm mới' }} bài thi</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal" role="form" @submit.prevent>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="type">Khóa:</label>
                                <div class="col-sm-10">
                                    <select class="col-sm-2 form-control" id="type" v-model="form.course">
                                        <option value="">Chọn khóa học</option>
                                        <option v-for="course in courses" :value="course">
                                            @{{ course }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="name">Tên</label>
                                <div class="col-sm-10">
                                    <input class="form-control" id="name" placeholder="Tên bài thi" v-model="form.name">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="maximum_time">Thời gian</label>
                                <div class="col-sm-10">
                                    <input class="form-control" id="maximum_time" placeholder="Thời gian làm bài (phút)"
                                           v-model="form.maximum_time">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="maximum_point">Điểm tối đa</label>
                                <div class="col-sm-10">
                                    <input class="form-control" id="maximum_point" v-model="form.maximum_point">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="passed_point">Điểm đạt</label>
                                <div class="col-sm-10">
                                    <input class="form-control" id="passed_point" v-model="form.passed_point">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="time_start">Thời gian thi</label>
                                <div class="col-sm-10">
                                    <date-picker v-model="form.time_start"
                                                 :config="startDatetimepickerOptions"></date-picker>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="time_end">Thời gian đóng</label>
                                <div class="col-sm-10">
                                    <date-picker v-model="form.time_end"
                                                 :config="endDatetimepickerOptions"></date-picker>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <button type="button" :disabled="isFormNotReady" class="btn btn-success"
                                            @click="addExam">Lưu
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Đóng</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="addLessons" class="modal fade" role="dialog">
            <div class="modal-dialog">
                <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Chọn Lesson</h4>
                    </div>
                    <div class="modal-body">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th v-for="(type, index) in types">@{{ examChosen.course === 'N1' ? N1Types[index] : type
                                    }}
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>
                                    <div v-for="lesson in lessons" v-if="lesson.type == 1">
                                        <input :id="lesson.id" type="radio" :value="lesson.id" v-model="chosenLesson1">
                                        <label :for="lesson.id">@{{ lesson.exams[lesson.exams.length - 1].name
                                            }}</label>
                                    </div>
                                </td>
                                <td>
                                    <div v-for="lesson in lessons" v-if="lesson.type == 2">
                                        <input :id="lesson.id" type="radio" :value="lesson.id" v-model="chosenLesson2">
                                        <label :for="lesson.id">@{{ lesson.exams[lesson.exams.length - 1].name
                                            }}</label>
                                    </div>
                                </td>
                                <td>
                                    <div v-for="lesson in lessons" v-if="lesson.type == 3">
                                        <input :id="lesson.id" type="radio" :value="lesson.id" v-model="chosenLesson3">
                                        <label :for="lesson.id">@{{ lesson.exams[lesson.exams.length - 1].name
                                            }}</label>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" @click="addLessons">Lưu</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        var urlUpdate = '{{ route('exams.update') }}';
        var urlReset = '{{ route('exams.reset') }}';
        const route = "{{ Route::currentRouteName() }}";
    </script>
    <script src="{{ asset('/plugin/vue-toastr/vue-toastr.cjs.min.js') }}"></script>
    <script>$.ajaxSetup( {headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') } });</script>
{{--    <script src="{{ asset('/assets/backend/js/jlpt-list.js') }}?{{filemtime('assets/backend/js/jlpt-list.js')}}"></script>--}}
    <script src="{{ asset('/assets/backend/js/jlpt_list.js') }}?{{filemtime('assets/backend/js/jlpt_list.js')}}"></script>

    <script type="text/javascript">
        function saveSchedule(id){
            $.post(window.location.origin +"/backend/thi-thu/update-schedule", {
                    id: id,
                    value: $('#schedule-'+ id).val(),
                }, function(response) {

                    if (response = "success") alert("Cập nhật thành công");
                }
            );
        }
    </script>

@stop
