<table class="table table-borderless" id="table_comment">
	<thead>
		<tr>
			<th class="text-center"><PERSON><PERSON></th>
			<th class="text-center">Tên</th>
			<th class="text-center">Nội dung</th>
            <th class="text-center">Trạng thái</th>
			<th class="text-center">Hành động</th>
		</tr>
	</thead>
    <tbody>
		@foreach($comment as $item)
			<tr class="item{{$item->id}}">
				<td class="text-center">{{$item->id}}</td>
				<td class="text-center">
					@if($item->table_name == 'course')
						<a href="{{url('/khoa-hoc/'.$item->table_info['SEOurl'])}}" target="_blank">{{$item->table_info['name']}}</a>
					@endif
					@if($item->table_name == 'lesson')
						<a href="{{url('/khoa-hoc/'.$item->table_info['course_url'].'/'.$item->table_info['id'].'-'.$item->table_info['SEOurl'])}}" target="_blank">{{$item->table_info['name']}}</a>
					@endif
					@if($item->table_name == 'teacher')
						<a href="{{url('/giao-vien/'.$item->table_info['SEOurl'])}}" target="_blank">{{$item->table_info['name']}}</a>
					@endif
					@if($item->table_name == 'combo')
						<a href="{{url('/khoa-hoc/combo/'.$item->table_info['id'])}}" target="_blank">{{$item->table_info['name']}}</a>
					@endif	
				</td>
				<td class="text-left">
                    <div>
                    	<span style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
                    		<b class="name" style="color:#eba119">{{ $item->user_info['name'] }}: </b> 
                    		{{$item->content}}
                    	</span>
                    </div>
                    <div>
                    	<b>Trả lời:</b>
                    </div>
                    <ul class="list-group more_list" data-item=".list-group-item">
        				<div class="more_block_list" id="reply{{ $item->id }}">
        					@foreach($item->replies as $reply)
	                            <li class="list-group-item"  style="display: none;" id="item_reply{{ $reply->id }}">
		                   			<b class="name" style="color:#eba119">
		                   			 	<span style="color:red">{{ ($reply->user_info['userId'] != 0) ? $reply->user_info['name'] : 'Admin' }}: </span>
		                   			</b>
	                    			<span>
	                    				[{{ $reply->created_at }}]<a class="sub_reply_{{$reply->id}}">{{ $reply->content }}</a>
	                    			</span>
		                    		@if(json_decode(Auth::guard('admin')->user()->matrix)->comment->delete != null)
			                    		<button class="btn btn-danger btn-xs reply_delete pull-right" data-info={{ $reply->id }}>Xóa</button>
			                    		<button class="btn btn-info btn-xs reply_edit pull-right" data-info={{ $reply->id }}>Sửa</button>
			                    	@endif
	                			</li>
	                    	@endforeach
                    	</div>
				        <div class="mt5">
				            <div id ="more{{$item->id}}" style="display: inline; color:green; cursor:pointer;">+ Thêm >></div>
				            <div id ="less{{$item->id}}" style="display: none; color:red; cursor:pointer;">+ Bớt <<</div>
				        </div>
    				</ul>
                </td>
				<td class="text-center">
                    @if($item->readed == 1)
                        <span class="label label-success">Đã xem</span><br>
                    @else 
                        <span class="label label-danger">Chưa xem</span><br>
                    @endif
                     	{{ $item->created_at }}
                </td>
				<td class="text-center" style="width: 97px;">
					@if(json_decode(Auth::guard('admin')->user()->matrix)->comment->reply != null)
		            	<button class="edit-modal btn btn-info" 
							data-info="{{json_encode($item)}}">
							<i class="fa fa-eye" ></i>
						</button>
		            @endif   
                    
                    @if(json_decode(Auth::guard('admin')->user()->matrix)->comment->delete != null)
						<button class="delete-modal btn btn-danger"
							data-info="{{$item->id}}">
	                            <i class="fa fa-remove" ></i>
						</button>
					@endif
				</td>
			</tr>
		@endforeach
    </tbody>
</table>
<script type="text/javascript">
	$( function() {
		$('#table_comment > tbody  > tr').each(function() {
			var class_tr = $(this). closest('tr').attr('class');
			var id_item = class_tr.replace("item", "");
		    var index = 1;
		    $('#reply'+ id_item+ ' li:lt('+ index +')').show();
		    if($("#reply"+ id_item + " li").size() > 1){
		    	$('#more'+id_item).show();
		        $('#less'+id_item).hide();
		    }else{
		    	$('#more'+id_item).hide();
		        $('#less'+id_item).hide();
		    }
		    $(document).on('click', '#more'+id_item, function() {
		    	while(index + 1 <= ($("#reply"+ id_item + " li").size())){
		        	index = index + 1;
		    	}
		        $('#reply'+ id_item +' li:lt('+ index + ')').show();
		        $('#more'+id_item).hide();
		        $('#less'+id_item).show();
		    });
		    $(document).on('click', '#less'+id_item, function() {
		        while(index - 1 >= 1){
		         	index = index - 1;
		         }
		        $('#reply'+ id_item +' li').not(':lt('+ index +')').hide();
		        $('#more'+id_item).show();
		        $('#less'+id_item).hide();
		    });
	    });
	});
</script>
{{ $comment->links() }}

