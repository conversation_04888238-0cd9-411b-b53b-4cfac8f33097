@extends('backend._default.dashboard')

@section('description') Quản lý bình luận @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý bình luận @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script> 
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
@stop

@section('content') 
    <div class="row bg-title">
        <h4 class="page-title pull-left">  Quản lý bình luận</h4>
        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <input type="text" class="check_url" name="url" style="display: none;">
            <select type="text" class="form-control mb-2 mr-sm-2" name="course">
                <option></option>
                <option value="5">N5</option>
                <option value="4">N4</option>
                <option value="3">N3</option>
                <option value="16">N2</option>
                <option value="17">N1</option>
            </select>
            <input type="text" class="form-control mb-2 mr-sm-2" name="id" placeholder="Nhập mã bình luận">
            <select type="text" class="form-control mb-2 mr-sm-2" name="readed">
                <option value="0">
                    Chưa Xem
                </option>
                <option value="1">
                    Đã xem
                </option>
            </select> 
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_from" id="date_from" placeholder="Từ ngày">
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_to" id="date_to" placeholder="Đến ngày">
            <a type="button" class="search btn btn-info mb-2">Tìm kiếm</a>
            <a href={{url('backend/comment')}} class="btn btn-info mb-2">Làm Lại</a>
        </form>
    </div>

    <div id="tabs">
       <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"> 
                <a href="{{url('backend/comment/lesson')}}" role="tab" data-toggle="tab">
                    Bình luận bài học
                </a>
            </li>
             <li role="presentation">
                <a href="{{url('backend/comment/course')}}" role="tab" data-toggle="tab">
                    Bình luận khóa học
                </a>
            </li>
            <li role="presentation"> 
                <a href="{{url('backend/comment/combo')}}" role="tab" data-toggle="tab">
                    Bình luận combo
                </a>
            </li>
            <li role="presentation"> 
                <a href="{{url('backend/comment/teacher')}}" role="tab" data-toggle="tab">
                    Bình luận giáo viên
                </a>
            </li>
        </ul>
    </div>
    {{-- dialog --}}
    <div id="pageModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>

                <div class="modal-body">
                    <table class=" form-horizontal table table-bordered table-striped table-hover tc-table">
                        <tbody>
                            <tr>
                                <td class="row_label">Mã số</td>
                                <td id="fid"></td>
                            </tr>
                            <tr style="display: none;">
                                <td class="row_label">Mã Số</td>
                                <td id="id_course"></td>
                            </tr>
                            <tr  style="display: none;">
                                 <td class="row_label">Tên bảng</td>
                                <td id="table_name"></td>
                            </tr>
                            <tr>
                                <td class="row_label">Tên</td>
                                <td id="name"></td>
                            </tr>

                            <tr>
                                <td class="row_label">Thành viên</td>
                                <td id="student"></td>
                            </tr>
                            <tr>
                                <td class="row_label">Ngày tạo</td>
                                <td id="created"></td>
                            </tr>
                            <tr>
                                <td class="row_label">Đánh giá ★</td>
                                <td id="rate"></td>
                            </tr>
                            <span style="display: none;" id="users_id"></span>
                            <tr>
                                <td colspan="2">
                                    <b class=" mb20">Nội dung bình luận:</b><br>
                                    <label id="question"></label>
                                    <textarea style="width:100%; margin-bottom:10px" id="reply" placeholder="Nhập phản hồi của bạn"></textarea>
                                </td>
                                <li class="global_error text-left hidden"></li>
                            </tr>
                            <tr>
                                <td class="row_label">Đánh dấu đã xem</td>
                                <td>
                                    <input type="checkbox" name="viewed" id="viewed">
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!--delete form-->
                    <div class="deleteContent">
                       Xác nhận xóa bình luận này ? 
                       <span class="hidden id_comment"></span>
                       <span class="hidden type_comment"></span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
   
    <script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                  'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });   
        });

        //view
        $(document).on('click', '.edit-modal', function() {
            $('#footer_action_button').text(" Trả lời");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').addClass('edit');
            $('.modal-title').text('Quản lý bình luận');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            $('input:checkbox').removeAttr('checked');
            var comment = $(this).data('info');
            var allContent =  '<b style="color: red;">Question:</b> ' + comment.content + '</br>';
            $.ajax({
                type: 'GET',
                url: '/backend/comment/get-reply/' + comment.id,
                success: function(data) {
                    if(data.length > 0){
                        for(var i = 0 ; i < data.length; i++){
                            allContent+= (data[i].user_id == 0) ? '<b style="color: red;">Admin:</b> '+ data[i].content + '</br>' : '<b style="color: red;">'+ comment.user_info['name'] +'</b> '+ data[i].content + '</br>';
                        }
                    }
                    fillmodalData(comment, allContent);
                },
                error: function(error){
                    console.log(error);
                }
            });
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '71%');
            $('#pageModal').css('left', '15%');
        });

        //Xóa question
        $(document).on('click', '.delete-modal', function() {
            $('#footer_action_button').text(" Xóa");
            $('#footer_action_button').removeClass('glyphicon-check');
            $('#footer_action_button').addClass('glyphicon-trash');
            $('.actionBtn').removeClass('btn-success');
            $('.actionBtn').addClass('btn-danger');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').addClass('delete');
            $('.modal-title').text('Xóa');
            $('.deleteContent').show();
            $('.form-horizontal').hide();
            var comment = $(this).data('info');
            $('.id_comment').text(comment);
            $('.type_comment').text('question');
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '28%');
            $('#pageModal').css('left', '37%');
            $('.global_error').addClass('hidden');
        });

        //xoa reply
            $(document).on('click', '.reply_delete', function() {
            $('#footer_action_button').text(" Xóa");
            $('#footer_action_button').removeClass('glyphicon-check');
            $('#footer_action_button').addClass('glyphicon-trash');
            $('.actionBtn').removeClass('btn-success');
            $('.actionBtn').addClass('btn-danger');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').addClass('delete');
            $('.modal-title').text('Xóa');
            $('.deleteContent').show();
            $('.form-horizontal').hide();
            var comment = $(this).data('info');
            $('.id_comment').text(comment);
            $('.type_comment').text('reply');
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '28%');
            $('#pageModal').css('left', '37%');
            $('.global_error').addClass('hidden');
        });
        //fill data vao field
        function fillmodalData(comment, content){
            $('#fid').html(comment.id);
            $('#name').html(comment.table_info['name']);
            $('#student').html(comment.user_info['name']);
            $('#created').html(comment.created_at);
            $('#rate').html(comment.rate);
            $('#question').html(content);
            $('#id_course').html(comment.table_id);
            $('#table_name').html(comment.table_name);
            $('#users_id').val(comment.user_id);

            $('#reply').val('');
            $('.global_error').addClass('hidden');
        }
        // tra loi cai hoi
        $('.modal-footer').on('click', '.edit', function() {
            $.ajax({
                type: 'post',
                url: '/backend/comment/reply',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id' : $('#fid').html(),
                    'table_id' : $('#id_course').html(),
                    'table_name' : $('#table_name').html(),
                    'rate' : $('#rate').html(),
                    'reply': $('#reply').val(),
                    'users_id': $('#users_id').val(),
                    'viewed': $('input[name="viewed"]:checked').val(),
                    'page' : $(".pagination .active span").html()
                },
                 success: function(data) {
                    if (data.errors){
                        if(Object.keys(data.errors).length > 0){
                            $('#pageModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    }else {
                        $('#pageModal').modal('hide');
                        // if($(".check_url").val() == 'course'){

                        //     $('#ui-id-2').html(data);

                        // }else if($(".check_url").val() == 'lesson'){

                        //     $('#ui-id-4').html(data);

                        // }else{

                        //     $('#ui-id-6').html(data);
                        // }
                        callBack(data);
                    }
                }
            });
        });
        //xoa binh luan
        $('.modal-footer').on('click', '.delete', function() {
            $.ajax({
                type: 'post',
                url: '/backend/comment/delete',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('.id_comment').text(),
                },
                success: function(data) {
                    if (data.errors){
                        //chua xui li
                    }else {
                        $('#pageModal').modal('hide');
                        if( $('.type_comment').text() == 'question'){

                            $('.item' + $('.id_comment').text()).remove();

                        }else{

                            $('#item_reply' + $('.id_comment').text()).remove();
                        }
                    }
                }
            });
        });

        //datetime picker
        $('#date_from').datepicker({ 
            dateFormat: 'dd/mm/yy'
        });
        $('#date_to').datepicker({ 
            dateFormat: 'dd/mm/yy'
        });

        //tabs load url
        $( function() {
            $( "#tabs" ).tabs({
                beforeLoad: function( event, ui ) {
                    $( ".check_url" ).val((ui.ajaxSettings.url).split('/')[5]);
                    //if fail
                    ui.jqXHR.fail(function() {
                      ui.panel.html("Đang load ...");
                    });
                }
            });
        });
        //Tìm kiếm
        $(document).on('click', '.search', function() {
            callBack(1);
        });

        // $(document).keypress(function(e) {
        //     if (e.which == 13) {
        //         callBack();
        //         e.preventDefault();
        //     }
        // });

        function callBack(page){
            $.ajax({
                type: 'get',
                url: '/backend/comment/find',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('input[name=id]').val(),
                    'tabs': $('input[name=url]').val(),
                    'date_to': $('input[name=date_to]').val(),
                    'date_from': $('input[name=date_from]').val(),
                    'readed': $('select[name=readed]').val(),
                    'course': $('select[name=course]').val(),
                    'page': page
                },
                success: function(data) {
                    $('#tabs').empty().html(data);
                    $(document).on('click', '#tabs .pagination a', function(e) {
                        var date_from = $('#date_from').val();
                        var date_to = $('#date_to').val();
                        var tabs = $('input[name=url]').val();
                        var readed = $('select[name=readed]').val();
                        var course = $('select[name=course]').val();
                        e.preventDefault();
                        var page = $(this).attr('href').split('page=')[1];
                        resultPage(page,'/backend/comment/find/?page=', '#tabs', date_from, date_to, tabs, readed, course);
                    });
                }
            });
        }
        //phan trang khi ket qua tim kiem tra ve
        function resultPage(page,link, div_id, date_from, date_to, tabs, readed, course){
            $.ajax({
                type: 'get',
                url: link + page,
                cache: false,
                data: {
                    date_from : date_from,
                    date_to : date_to,
                    tabs : tabs,
                    readed : readed,
                    page : page,
                    course: course
                }
            }).done(function(data){
                $(div_id).empty().html(data)
            })
        }
        //pagination bai hoc
        $(document).on('click', '#ui-id-2 .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page, '/backend/comment/lesson?page=', '#ui-id-2');
        });
        // pagination khoa hoc
        $(document).on('click', '#ui-id-4 .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page, '/backend/comment/course?page=',  '#ui-id-4');
        });
        // pagination combo
        $(document).on('click', '#ui-id-6 .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page, '/backend/comment/combo?page=', '#ui-id-6');
        });
        // pagination giao vien
        $(document).on('click', '#ui-id-8 .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page, '/backend/comment/teacher?page=', '#ui-id-8');
        });
        function readPage(page, link, div_id){
            $.ajax({
                url: link + page
            }).done(function(data){
                $(div_id).html(data)
            })
        }
        // sua comment 
        $(document).on('click', '.reply_edit', function(e) {
            e.stopPropagation();
            var replyId = $(this).data('info');
            editComment(replyId);
        });
        //sua comment cho tung binh luan
        function editComment(replyId) {
            var element = '.sub_reply_'+replyId;
            var value = $(element).text();
            if(value != ''){
                $(element).html('<input class="val_'+replyId+'" type="text" value="' + value + '" />');
                $(".val_" + replyId).css('width',(value.length)*7);
                $(".val_" + replyId).focus();
                $(".val_" + replyId).keyup(function (event) {
                    if (event.keyCode == 13) {
                        $(element).html($(".val_" + replyId).val());
                        $.ajax({
                            type: 'post',
                            url: '/backend/comment/edit',
                            data: {
                                'id' : replyId,
                                'content' : $(element).text(),
                            },
                            success: function(data) {
                                console.log(data);
                            }
                        })
                    }
                });
            }
        }
    </script>

@stop