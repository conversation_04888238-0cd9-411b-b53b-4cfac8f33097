@extends('backend._default.dashboard')

@section('title') Admin | Quản lý Popup @stop
@section('description') Quản lý Popup @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop

@section('assets')
    <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
@stop

@section('content')

<div id="popup-manager" class="row bg-title">
	<div class="popup-manager-title-area">
		<h4 class="page-title pull-left popup-manager-title">Quản lý popup</h4>
		@if(json_decode(Auth::guard('admin')->user()->matrix)->popup->add != null)
    		<button class="btn btn-success" style="float: right;" data-toggle="modal" data-target="#addOrEdit" v-on:click="startAddNewPopup()">Thêm mới</button>
    	@endif
	</div>

    <table class="table" id="popup-table">
		<thead>
			<tr>
				<th>Id</th>
				<th>Ảnh</th>
				<th>Tên</th>
				<th>link</th>
				<th>Ngày hết hạn</th>
				<th>Trạng thái</th>
				<th>Hành động</th>
				<th style="display: none;">Index</th>
			</tr>
		</thead>
		<tbody>
			<tr v-for="(popup, index) in popups">
				<td>@{{ popup.id }}</td>
				<td>
					<img v-if="popup.image_name != null && popup.image_name != ''" :src="url + popup.image_name" style="height: 100px; max-width: 170px;">
					<img v-if="popup.image_name == null || popup.image_name == ''" src="{{url('assets/img/icon_backend')}}/no_image.png" style="height: 100px; max-width: 170px;">
				</td>
				<td>@{{ popup.name }}</td>
				<td>@{{ popup.link }}</td>
				<td>@{{ popup.expired_date }}</td>
				<td>
					<input type='checkbox' class='ios8-switch ios8-switch-lg' :id="'popup' + popup.id" v-model="popup.show"><label style="cursor: pointer;" :for="'popup' + popup.id" v-on:click="setStatus(index, popup.id)"></label>
				</td>
				<td>
					@if(json_decode(Auth::guard('admin')->user()->matrix)->popup->edit != null)
					<button class="btn btn-info" data-toggle="modal" data-target="#addOrEdit" v-on:click="startEditPopup(index, popup.id)">Sửa</button>
					@endif
					@if(json_decode(Auth::guard('admin')->user()->matrix)->popup->delete != null)
	    			<button class="btn btn-danger" data-toggle="modal" data-target="#deletModal" v-on:click="startRemovePopup(index, popup.id)">Xóa</button>
	    			@endif
	    		</td>
    			<td style="display: none;">document.order_index</td>
			</tr>
		</tbody>
  	</table>

	<div class="modal fade" id="addOrEdit" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="title-model"></h4>
				</div>
				<div class="modal-body">
                    <form class="form-horizontal" role="form" id="popup_form">
                    	<ul class="error-area">
                    		<li class="error-item" v-for="error in errors">@{{ error }}</li>
                    	</ul>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Tên</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="name" v-model="currentPopup.name">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Ảnh</label>
                            <div class="col-sm-10">
                            	<img v-if="currentPopup.image_name != null && currentPopup.image_name != ''" id="image_preview" :src="url + currentPopup.image_name" style="width: 150px; max-height: 150px;">
                            	<img v-if="currentPopup.image_name == null || currentPopup.image_name == ''" id="image_preview" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 150px; max-height: 150px;">
                                <input type='file' id="image_file" name="image_file" onchange="previewImage()"/>
                                <div class="col-sm-10" style="color: red;">
                                	Cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>gif,jpg,jpeg,png,svg</b>
                            	</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Link</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="link" v-model="currentPopup.link">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Ngày hết hạn</label>
                            <div class="col-sm-10">
                                <input type="date" name="expired_date" v-model="currentPopup.expired_date" v-model="currentPopup.expired_date">
                            </div>
                        </div>
                        {{-- <div class="form-group">
                            <label class="control-label col-sm-2">Bật/tắt</label>
                            <div class="col-sm-10">
                                <input type='checkbox' class='ios8-switch ios8-switch-lg' id="checkbox" name="show" v-model="currentPopup.show"><label style="cursor: pointer;" for="checkbox">
                            </div>
                        </div> --}}
                    </form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="editPopup()" id="btn-action-model"></button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>

		</div>
	</div>

	<div id="deletModal" class="modal fade" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Xóa</h4>
				</div>
				<div class="modal-body">
					<p>Bạn có muốn xóa popup này không?</p>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="removePopup()">Xóa</button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>
		</div>
	</div>

</div>

<script>

function previewImage() {
    var preview = document.querySelector('#image_preview');
    var file    = document.querySelector('#image_file').files[0];
    var reader  = new FileReader();
    reader.onloadend = function () {
        preview.src = reader.result;
    }
    if (file) {
        reader.readAsDataURL(file);
    } else {
        preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
    }
}

$(document).ready(function() {
    $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });   
});

var user = new Vue({
	el: '#popup-manager',

	data: function () {
        return {
        	url			: window.location.origin + "/cdn/popup/small/",
        	popups		: {!! json_encode($popups) !!},
        	currentPopup: {id: -1, name: "", image_name: "", link:"", expired_date:"", show: false},
        	errors		: [],
        	currentId	: -1,
        	currentIndex: -1
		}
	},

	methods: {
		// mở popup sửa 1 popup
		startEditPopup(index, id) {
			var vm = this;
			vm.errors = [];

			// Bỏ tham chiếu
			const temp = Object.assign({}, vm.popups[index]);
			vm.currentPopup = temp;
			vm.currentId = id;
			vm.currentIndex = index;

			$("#title-model").text("Chỉnh sửa");
			$("#btn-action-model").text("Sửa");
		},

		// mở popup xóa 1 popup
		startRemovePopup(index, id) {
			var vm = this;
			vm.currentId = id;
			vm.currentIndex = index;
		},

		// Chuyển trạng thái TẮT <=> BẬT
		setStatus(index, id) {
			var vm = this;
			var data = {
	            'id'		: id,
	            'show'		: vm.popups[index].show
			};
			// console.log(data);
			$.ajax({
	            url: window.location.origin + "/backend/popup/active", type:"POST", data: data, async: true,
	            error: function() {
	            	console.log("Có lỗi xảy ra");
	            },
	            success: function(response) {
	            	// Nếu active một popup thì tất tất cả những popup khác
	            	if (vm.popups[index].show == false) return;
	            	for (var i = 0; i < vm.popups.length; i++) {
	            		if (i != index) {
	            			vm.popups[i].show = false;
	            		}
	            	}
	            }
	        });
		},

		// mở popup để thêm mới 1 popup
		startAddNewPopup() {
			var vm = this;
			vm.errors = [];
			vm.currentId = -1;
			vm.currentIndex = -1;
			$("#title-model").text("Thêm mới");
			$("#btn-action-model").text("Thêm");

			vm.currentPopup = {id: -1, name: "", image_name: "", link:"", expired_date:"", show: false};
		},

		// Sủa 1 popup
		editPopup() {
			var vm = this;
			var dataPopup = new FormData($("#popup_form")[0]);
			dataPopup.append('id', vm.currentId);
	        $.ajax({
                type: 'post',
                url: '/backend/popup/edit',
                processData: false,
                contentType: false,
                data : dataPopup,
                success: function(response) {
                    // console.log(response);
	            	if (response == "blank_data") {
	            		vm.errors = [];
	            		vm.errors.push("Dữ liệu còn trống");
	            	} else if (response.type == "new") {
	            		const newPopup = response.popup;
		            	vm.popups.push(newPopup);
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];

	            	} else if(response == 'imagetype'){
	            		vm.errors = [];
	            		vm.errors.push("Ảnh không đúng định dạng");
	            	} else if(response == 'imagesize'){
	            		vm.errors = [];
	            		vm.errors.push("Ảnh vượt quá kích thước 3MB");
	            	} else {
		            	// console.log("Thành công : " + response);
		            	vm.popups[vm.currentIndex].name = response.popup.name;
		            	vm.popups[vm.currentIndex].image_name = response.popup.image_name;
		            	vm.popups[vm.currentIndex].link = response.popup.link;
		            	vm.popups[vm.currentIndex].expired_date = response.popup.expired_date;
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
		            }
                }
            });
		},

		// xóa 1 popup
		removePopup() {
			var vm = this;
			var data = {
	            'id'		: vm.currentId
	        };
	        // console.log(data);
	        $.ajax({
	            url: window.location.origin + "/backend/popup/delete", type:"DELETE", data: data, async: true,
	            error: function() {
	            	vm.errors = [];
	            },
	            success: function(response) {
	            	if (response == "invalid_id") {
	            		vm.errors = [];
	            		vm.errors.push("Có lỗi xảy ra. Vui lòng thử lại");
	            	} else if (response == "success") {
		            	vm.popups.splice(vm.currentIndex, 1);
		            	$('#deletModal').modal('toggle');
		            }
	            }
	        });
		}
	},

	mounted() {
		var vm = this;
		
		$('#popup-table').DataTable({
			"pageLength": 10,
			"stateSave": true
		});
		
	}
});

</script>

@stop