@extends('backend._default.dashboard')

@section('title') Admin | Quản lý ảnh quảng cáo @stop
@section('description') Quản lý ảnh quảng cáo @stop
@section('keywords') advertisement @stop
@section('author') dungmori.com @stop

@section('assets')
    <link type="text/css"  rel="stylesheet" href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
@stop

@section('content')

<div id="advertis-manager">
	<div class="row bg-title">
		<h4 class="page-title pull-left">Quản lý ảnh quảng cáo</h4>
    	<button class="btn btn-success" style="float: right;" data-toggle="modal" data-target="#addOrEdit" v-on:click="addAdvertise()">Thêm mới</button>
	</div>
	<div class="all-data">
	    <table class="table" id="advertis-table">
			<thead>
				<tr>
					<th>Id</th>
					<th>Ảnh</th>
					<th>Ảnh mobile</th>
					<th>Tên</th>
					<th>link</th>
					<th>Trạng thái</th>
					<th>Campaign</th>
					<th>Thứ tự</th>
					<th>Hành động</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(advertis, index) in advertise">
					<td>@{{ advertis.id }}</td>
					<td>
						<img v-if="advertis.image_name != null && advertis.image_name != ''" :src="url + advertis.image_name" style="height: 100px; max-width: 170px;">
						<img v-if="advertis.image_name == null || advertis.image_name == ''" src="{{url('assets/img/icon_backend')}}/no_image.png" style="height: 100px; max-width: 170px;">
					</td>
					<td>
						<img v-if="advertis.mobile_img != null && advertis.mobile_img != ''" :src="url + advertis.mobile_img" style="height: 100px; max-width: 170px;">
						<img v-if="advertis.mobile_img == null || advertis.mobile_img == ''" src="{{url('assets/img/icon_backend')}}/no_image.png" style="height: 100px; max-width: 170px;">
					</td>
					<td>@{{ advertis.name }}</td>
					<td>@{{ advertis.link }}</td>
					<td>
						<input type='checkbox' class='ios8-switch ios8-switch-lg' :id="'advertis' + advertis.id" v-model="advertis.show"><label style="cursor: pointer;" :for="'advertis' + advertis.id" v-on:click="setStatus(index, advertis.id)"></label>
					</td>
					<td>
						<input type='checkbox' class='ios8-switch ios8-switch-lg' :id="'advertis_campaign' + advertis.id" v-model="advertis.is_campain"><label style="cursor: pointer;" :for="'advertis_campaign' + advertis.id" v-on:click="setCampaign(index, advertis.id)"></label>
					</td>
					<td>@{{ advertis.order }}</td>
					<td>
						<button class="btn btn-info" data-toggle="modal" data-target="#addOrEdit" v-on:click="openEditAdvertise(index, advertis.id)">Sửa</button>
		    			<button class="btn btn-danger" data-toggle="modal" data-target="#deletModal" v-on:click="openRemoveAdvertise(index, advertis.id)">Xóa</button>
		    		</td>
				</tr>
			</tbody>
	  	</table>
	</div>

	<div class="modal fade" id="addOrEdit" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title" id="title-model"></h4>
				</div>
				<div class="modal-body">
                    <form class="form-horizontal" role="form" id="advertise_form">
                    	<ul class="error-area">
                    		<li class="error-item" v-for="error in errors">@{{ error }}</li>
                    	</ul>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Tên</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="name" v-model="currentAdvertis.name">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Ảnh</label>
                            <div class="col-sm-10">
                            	<img v-if="currentAdvertis.image_name != null && currentAdvertis.image_name != ''" id="image_preview" :src="url + currentAdvertis.image_name" style="width: 150px; max-height: 150px;">
                            	<img v-if="currentAdvertis.image_name == null || currentAdvertis.image_name == ''" id="image_preview" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 150px; max-height: 150px;">
                                <input type='file' id="image_file" name="image_file" onchange="previewImage()"/>
                                <div class="col-sm-10" style="color: red;">
                                	Cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>gif,jpg,jpeg,png,svg</b>
                            	</div>
                            </div>
                        </div>
						<div class="form-group">
							<label class="control-label col-sm-2">Ảnh mobile</label>
							<div class="col-sm-10">
								<img v-if="currentAdvertis.mobile_img != null && currentAdvertis.mobile_img != ''" id="mobile_image_preview" :src="url + currentAdvertis.mobile_img" style="width: 150px; max-height: 150px;">
								<img v-if="currentAdvertis.mobile_img == null || currentAdvertis.mobile_img == ''" id="mobile_image_preview" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 150px; max-height: 150px;">
								<input type='file' id="mobile_image_file" name="mobile_image_file" onchange="previewImage('mobile_')"/>
								<div class="col-sm-10" style="color: red;">
									Cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>gif,jpg,jpeg,png,svg</b>
								</div>
							</div>
						</div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Link</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="link" v-model="currentAdvertis.link">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2">Thứ tự</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" name="order" v-model="currentAdvertis.order">
                            </div>
                        </div>
                    </form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="editAdvertise()" id="btn-action-model">Sửa</button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>

		</div>
	</div>

	<div id="deletModal" class="modal fade" role="dialog" tabindex="-1">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal">&times;</button>
					<h4 class="modal-title">Xóa</h4>
				</div>
				<div class="modal-body">
					<p>Bạn có muốn xóa ảnh quảng cáo này không?</p>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-success" v-on:click="removeAdvertise()">Xóa</button>
					<button type="button" class="btn btn-warning" data-dismiss="modal">Đóng</button>
				</div>
			</div>
		</div>
	</div>

</div>

<script>

//lựa chọn ảnh
function previewImage(device) {
	device = device ? device : "";
    var preview = document.querySelector('#' + device + 'image_preview');
    var file    = document.querySelector('#' + device + 'image_file').files[0];
    var reader  = new FileReader();
    reader.onloadend = function () {
        preview.src = reader.result;
    }
    if (file) {
        reader.readAsDataURL(file);
    } else {
        preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
    }
}

$(document).ready(function() {
    $.ajaxSetup({
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
});

var user = new Vue({
	el: '#advertis-manager',

	data: function () {
        return {
        	url			: window.location.origin + "/cdn/qc/small/",
        	advertise		: {!! json_encode($advertise) !!},
        	currentAdvertis: {id: -1, name: "", image_name: "", mobile_img: "", link:"", order:0, show: false},
        	errors		: [],
        	currentId	: -1,
        	currentIndex: -1
		}
	},

	methods: {
		// mở popup sửa 1 advertise
		openEditAdvertise(index, id) {
			var vm = this;
			vm.errors = [];

			// Bỏ tham chiếu
			const temp = Object.assign({}, vm.advertise[index]);
			vm.currentAdvertis = temp;
			vm.currentId = id;
			vm.currentIndex = index;

			$("#title-model").text("Chỉnh sửa");
			$("#btn-action-model").text("Sửa");
		},

		// mở popup xóa 1 advertise
		openRemoveAdvertise(index, id) {
			var vm = this;
			vm.currentId = id;
			vm.currentIndex = index;
		},

		// Chuyển trạng thái TẮT <=> BẬT
		setStatus(index, id) {
			var vm = this;
			var data = {
	            'id'		: id,
	            'show'		: vm.advertise[index].show
			};
			// console.log(data);
			$.ajax({
	            url: window.location.origin + "/backend/advertise/active", type:"POST", data: data, async: true,
	            error: function() {
	            	console.log("Có lỗi xảy ra");
	            },
	            success: function(response) {
	            	console.log(response);
	            }
	        });
		},
		setCampaign(index, id) {
			var vm = this;
			var data = {
					'id'		: id,
					'is_campain'		: vm.advertise[index].is_campain
			};
			// console.log(data);
			$.ajax({
					url: window.location.origin + "/backend/advertise/campaign", type:"POST", data: data, async: true,
					error: function() {
						console.log("Có lỗi xảy ra");
					},
					success: function(response) {
						console.log(response);
					}
			});
		},

		// mở popup để thêm mới 1 advertise
		addAdvertise() {
			var vm = this;
			vm.errors = [];
			vm.currentId = -1;
			vm.currentIndex = -1;
			$("#title-model").text("Thêm mới");
			$("#btn-action-model").text("Thêm");

			vm.currentAdvertis = {id: -1, name: "", image_name: "", mobile_img: "", link:"", order:0, show: false};
		},

		// Sủa 1 advertise
		editAdvertise() {
			var vm = this;
			var dataForm = new FormData($("#advertise_form")[0]);
			dataForm.append('id', vm.currentId);
	        $.ajax({
                type: 'post',
                url: '/backend/advertise/edit',
                processData: false,
                contentType: false,
                data : dataForm,
                success: function(response) {
                    // console.log(response);
	            	if (response == "blank_data") {
	            		vm.errors = [];
	            		vm.errors.push("Dữ liệu còn trống");
	            	} else if (response.type == "new") {
	            		const newAdvertise = response.advertise;
		            	vm.advertise.push(newAdvertise);
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];

	            	} else if(response == 'imagetype'){
	            		vm.errors = [];
	            		vm.errors.push("Ảnh không đúng định dạng");
	            	} else if(response == 'imagesize'){
	            		vm.errors = [];
	            		vm.errors.push("Ảnh vượt quá kích thước 3MB");
	            	} else {
		            	// console.log("Thành công : " + response);
		            	vm.advertise[vm.currentIndex].name = response.advertise.name;
		            	vm.advertise[vm.currentIndex].image_name = response.advertise.image_name;
		            	vm.advertise[vm.currentIndex].mobile_img = response.advertise.mobile_img;
		            	vm.advertise[vm.currentIndex].link = response.advertise.link;
		            	vm.advertise[vm.currentIndex].order = response.advertise.order;
		            	$('#addOrEdit').modal('toggle');
		            	vm.errors = [];
		            }
                }
            });
		},

		// xóa 1 advertise
		removeAdvertise() {
			var vm = this;
			var data = {
	            'id'		: vm.currentId
	        };
	        // console.log(data);
	        $.ajax({
	            url: window.location.origin + "/backend/advertise/delete", type:"DELETE", data: data, async: true,
	            error: function() {
	            	vm.errors = [];
	            },
	            success: function(response) {
	            	if (response == "invalid_id") {
	            		vm.errors = [];
	            		vm.errors.push("Có lỗi xảy ra. Vui lòng thử lại");
	            	} else if (response == "success") {
		            	vm.advertise.splice(vm.currentIndex, 1);
		            	$('#deletModal').modal('toggle');
		            }
	            }
	        });
		}
	},

	mounted() {
		var vm = this;

		$('#advertis-table').DataTable({
			"pageLength": 10,
			"stateSave": true
		});

	}
});

</script>

@stop
