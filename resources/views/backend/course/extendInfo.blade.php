@extends('backend._default.dashboard')

@section('description') Quản lý gia hạn @stop
@section('keywords') user @stop
@section('author') dungmori.com @stop
@section('title') Admin | <PERSON>ia hạn @stop

@section('content')


    <div id="extend-info">
        <div class="extend-info-table">
            <div style="margin-top: 20px">
                <button class="btn btn-success" v-on:click="showModal = true">Tạo mới</button>
            </div>
            <table class="table table-stripe">
                <thead>
                    <tr>
                        <th>Mã số</th>
                        <th>Tên khoá</th>
                        <th>Học viên</th>
                        <th>Thời hạn</th>
                        <th>Giá VN</th>
                        <th>Gi<PERSON> JP</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in extend">
                        <td>@{{item.id}}</td>
                        <td>@{{item.course.name}}</td>
                        <td>
                            <div v-if="item.is_new == 1">Học viên mới</div>
                            <div v-else>Học viên cũ</div>
                        </td>
                        <td>@{{ item.duration }} Tháng</td>
                        <th>@{{ item.price_vn }}</th>
                        <th>@{{ item.price_jp }}</th>
                        <td>
                            <span class="btn btn-success" v-on:click="editExtend(item)">edit</span>
                            <span class="btn btn-danger" v-on:click="destroyExtend(item.id)">xoá</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <backend-modal v-if="showModal" @close="closeModal">
            <div slot="body">

                <form @submit="submitFormData" enctype="multipart/form-data">
                    <div>
                        <label for="">Khoá</label>
                        <select v-model="formData.course_id" class="form-control">
                            <option value="null">Chọn khoá học</option>
                            <option v-for="course in courses" :value="course.id">@{{ course.name }}</option>
                        </select>
                    </div>

                    <div>
                        <label for="">Học viên</label>
                        <select v-model="formData.student" class="form-control">
                            <option value="0">Học viên cũ</option>
                            <option value="1">Học viên mới</option>
                        </select>
                    </div>
                    <div>
                        <label for="">Thời hạn</label>
                        <input type="text" v-model="formData.duration" class="form-control">
                    </div>
                    <div>
                        <label for="">giá VN</label>
                        <input type="text" v-model="formData.price_vn" class="form-control">
                    </div>
                    <div>
                        <label for="">giá JP</label>
                        <input type="text" v-model="formData.price_jp" class="form-control">
                    </div>

                    <button type="submit" class="btn btn-success">Lưu lại</button>
                </form>
            </div>
        </backend-modal>
    </div>
@stop

@section('footer')
    <!-- import lib, component, plugin -->
    <script src="{{ asset('/plugin/jquery/axios.min.js') }}"></script>
    <script src="{{ asset('/plugin/vuejs-paginate/vuejs-paginate.js') }}"></script>

    <!-- inport components -->
    <script src="{{ asset('/assets/backend/js/modal.js') }}"></script>

    <!-- process courses js -->
    <script>
        var courseList = {!! json_encode($course) !!};

        var extendInfo = new Vue({
            el: '#extend-info',
            data() {
                return {
                    extend: {},
                    courses: {},
                    formData:{
                        id: null,
                        course_id: null,
                        student: 0,
                        duration: 0,
                        price_vn: 0,
                        price_jp: 0,
                    },
                    showModal: false,
                }
            },

            methods: {

                closeModal: function () {
                    this.formData = {
                        id: null,
                        course_id: null,
                        student: null,
                        duration: 0,
                        price_vn: 0,
                        price_jp: 0,
                    }
                    this.showModal =false;
                },

                getExtend: function () {
                    var vm = this;

                    axios.get(window.location.origin + '/backend/extend-info/get-extend')
                        .then(function (response) {
                            vm.extend = response.data;
                        }).catch(function (error) {
                        console.log('lỗi');
                    });
                },

                submitFormData: function (e) {
                    e.preventDefault()
                    var vm = this;

                    var data = {
                        course_id: vm.formData.course_id,
                        is_new: vm.formData.student,
                        duration: vm.formData.duration,
                        price_vn: vm.formData.price_vn,
                        price_jp: vm.formData.price_jp
                    }

                    if (vm.formData.id) {
                        vm.updateExtend(data);
                    } else {
                        vm.saveExtend(data);
                    }

                },

                saveExtend: function (data) {
                    var vm = this;
                    var url = window.location.origin + '/backend/extend-info/store/';

                    axios.post(url, data)
                        .then(function (response) {
                            if (response.status == 200) {
                                vm.extend.unshift(response.data);
                            }
                            vm.closeModal();
                        }).catch(function (error) {
                        console.log('lỗi');
                    });
                },

                editExtend: function (extend) {
                    var vm = this;

                    this.formData = {
                        id: extend.id,
                        course_id: extend.course_id,
                        student: extend.is_new,
                        duration: extend.duration,
                        price_vn: extend.price_vn,
                        price_jp: extend.price_jp,
                    }

                    vm.showModal = true;
                },

                updateExtend: function (extend) {
                    var vm = this;
                    var url = window.location.origin + '/backend/extend-info/update/' + vm.formData.id;

                    axios.post(url, extend)
                        .then(function (response) {
                            vm.extend = vm.extend.map(function (item) {
                                if (item.id === response.data.id) {
                                    item = {...response.data}
                                }
                                return item;
                            })
                            vm.closeModal();
                        }).catch(function (error) {
                        console.log('lỗi');
                    });
                },

                destroyExtend: function(id){

                    var vm = this;

                    var conf = confirm('Xác nhận xoá');

                    if(conf) {
                        axios.post(window.location.origin + '/backend/extend-info/destroy', {id: id})
                            .then(function (response) {
                                if (response.status == 200) {
                                    vm.extend = vm.extend.filter(function (item) {
                                        return item.id !== id;
                                    })
                                }
                            }).catch(function (error) {
                            console.log('lỗi');
                        });
                    }
                },
            },
            mounted: function () {
                this.getExtend();
                this.courses = courseList;
            },
        });
    </script>
@endsection

