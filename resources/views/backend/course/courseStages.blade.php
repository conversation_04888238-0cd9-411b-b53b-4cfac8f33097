@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON><PERSON> lý chặng @stop
@section('keywords') group stage @stop
@section('author') dungmori.com @stop
@section('title') Admin | Chặng @stop

@section('assets')
    <link type="text/css" rel="stylesheet"
        href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript" src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}">
    </script>
    <script type="text/javascript"
        src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
    <link href="{{ asset('assets/backend/css/course-stage.css') }}?{{ filemtime('assets/backend/css/course-stage.css') }}"
        rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/css/bootstrap-select.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/js/bootstrap-select.min.js"></script>
    <style type="text/css">
        .row {
            font-family: Myriad Pro, sans-serif;
            font-weight: 500;
        }

        .btn {
            padding: 4px 6px 2px 6px;
            border-radius: 3px;
            margin: 0 0 0 3px;
            font-size: 12px;
        }

        .label {
            padding: 4px 6px 4px 8px;
            margin-right: 4px;
            cursor: pointer;
            border-radius: 3px;
        }

        .label-success {
            background: #10a31a;
        }

        .label-danger,
        .btn-danger {
            background: #e74c3c;
        }

        .table {
            background-color: #fff;
        }

        .text-left {
            text-align: left;
        }

        .dropdown-toggle {
            height: 40px;
        }
    </style>
@stop

@section('content')
    <div class="course-stages__screen" id="course-stages__screen">
        <div class="course-stages__panel course-stages__screen--panel">
            <div class="course-stages__panel--header">
                <span class="btn btn-sm btn-success" @click="addStage">Thêm</span>
                <span class="btn btn-sm btn-danger" @click="removeLastStage">Xóa chặng cuối</span>
            </div>
            <div class="mb-3">
                <label v-for="(course, index) in courses"
                    v-if="['N1','N2','N3','N4','N5', 'EJU - Toán', 'Sơ cấp N4', 'Sơ cấp N5', 'Luyện đề N4', 'Luyện đề N5','JLPT N1', 'JLPT N2', 'JLPT N3', 'Chữ Hán N5'].includes(course.name)" class="mr-5">
                    <input type="radio" :checked="course.id === selectedCourse.id"
                        @change="selectCourse($event, course)" />
                    @{{ course.name }}
                </label>
                {{--                <span class="btn btn-info" @click="applyFilter">Lọc</span> --}}
            </div>
            <div class="course-stages__panel--stage-list" v-if="!loading">
                <div class="course-stages__panel--stage-item" style="margin-bottom: 20px;"
                    :class="{ 'course-stages__panel--stage-item-selected': selectedStage === stage }"
                    v-for="stage in stages" @click.self="selectedStage = stage">
                    <div class="course-stages__panel--stage-item-info">
                        <span>Chặng @{{ stage }}
                        </span>
                        <div class="form-control mb-[20px]">
                            <input type="text" :id="`stage_${stage}`" :value="getStageName(stage)"
                                placeholder="Tên chặng">
                            <button class="btn btn-primary btn-change-stage-name"
                                @click="changeStageName(selectedCourse.id, stage)">Đổi tên
                                chặng</button>
                        </div>
                        <div class="form-control">
                            <input type="text" :id="`release_date_${stage}`" :value="getReleaseDate(stage)"
                                placeholder="Ngày ra mắt">
                            <button class="btn btn-primary btn-change-stage-name"
                                @click="changeReleaseDate(selectedCourse.id, stage)">
                                Đổi ngày ra mắt
                            </button>
                        </div>
                    </div>
                    <div class="course-stages__panel--stage-item-action">
                    </div>
                </div>
            </div>
            <div class="course-stages__screen--panel-loading" v-if="loading">
                <div class="fa-3x">
                    <i class="fa fa-spinner fa-spin"></i>
                </div>
            </div>
        </div>
        <lesson-category-panel :permission="permission" :course="selectedCourse" :stage="selectedStage"
            :stages="stages" v-on:update:category="categorySelected($event)"></lesson-category-panel>
        <lesson-group-panel :permission="permission" :category="selectedCategory"></lesson-group-panel>
    </div>
@stop
@section('footer')
    <script>
        var courses = {!! json_encode($courses) !!};
        var admin = {!! json_encode(Auth::guard('admin')->user()) !!};
    </script>
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.8.4/Sortable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Vue.Draggable/2.20.0/vuedraggable.umd.min.js"></script>
    <script src="{{ asset('/assets/backend/js/modal.js') }}?{{ filemtime('assets/backend/js/modal.js') }}"></script>
    <script src="{{ asset('assets/backend/js/course-stage/course-stage.js') }}"></script>
@stop
