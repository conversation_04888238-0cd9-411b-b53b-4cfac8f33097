@extends('backend._default.dashboard')

@section('description') Q<PERSON><PERSON><PERSON> lý share facebook @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Q<PERSON>ản lý share facebook @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/fb-share.css') }}?{{filemtime('assets/backend/css/fb-share.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
@stop

@section('content')
    <div class="filterable-list__screen" id="exam__results--screen">
        <div><h4>Quản lý share facebook</h4></div>
        <div class="filterable-list__filter">
            <div class="form-group">
                <label>ID</label>
                <input type="text" class="form-control" placeholder="Nhập ID" v-model="filter.id" @keyup.enter="applyFilter">
            </div>
            <div class="form-group">
                <label>Học viên</label>
                <input type="text" class="form-control" placeholder="Nhập ID, username, email, phone" v-model="filter.user" @keyup.enter="applyFilter">
            </div>
            <div class="form-group">
                <label>Admin duyệt</label>
                <input type="text" class="form-control" placeholder="Nhập ID, username, email" v-model="filter.admin" @keyup.enter="applyFilter">
            </div>
{{--            <div class="form-group">--}}
{{--                <label>Mã Voucher</label>--}}
{{--                <input type="text" class="form-control" placeholder="Voucher Key" v-model="filter.voucher_key" @keyup.enter="applyFilter">--}}
{{--            </div>--}}
{{--            <div class="form-group">--}}
{{--                <label>Dùng cho</label>--}}
{{--                <select class="form-control" v-model="filter.product_name" @change="applyFilter">--}}
{{--                    --}}{{--                <option value="">Tất cả</option>--}}
{{--                    <option value="course">Khoá học</option>--}}
{{--                    <option value="combo">Combo</option>--}}
{{--                </select>--}}
{{--            </div>--}}
{{--            <div class="form-group">--}}
{{--                <label>ID Sản phẩm</label>--}}
{{--                <input type="text" class="form-control" placeholder="Nhập ID khoá hoặc combo" v-model="filter.product_id" @keyup.enter="applyFilter">--}}
{{--            </div>--}}
            <div class="form-group">
                <label>Trạng thái</label>
                <select class="form-control" v-model="filter.status" @change="applyFilter">
                    <option value="">Tất cả</option>
                    <option value="0">Chờ duyệt</option>
                    <option value="1">Hợp lệ</option>
                    <option value="-1">Không hợp lệ</option>
                </select>
            </div>
            <div class="form-group">
                <label>Lọc theo t.gian</label>
                <select class="form-control" v-model="filter.time_type" @change="applyFilter">
                    <option value="created_at">T.gian tạo</option>
                    <option value="active_time">T.gian duyệt</option>
                </select>
            </div>
            <div class="form-group">
                <label>Từ ngày</label>
                <input type="text" class="form-control" name="time_from" id="time_from" placeholder="Chọn t.gian bắt đầu" v-model="filter.time_from" @change="onChangeDate($event)">
            </div>
            <div class="form-group">
                <label>Đến ngày</label>
                <input type="text" class="form-control" name="time_to" id="time_to" placeholder="Chọn t.gian kết thúc" v-model="filter.time_to" @change="onChangeDate($event)">
            </div>
            {{--            <input type="checkbox" class="form-control" name="is_printed" id="is_printed" :checked="filter.is_printed" @change="onChangeCheckbox('is_printed', $event)">--}}
            {{--            </select>--}}

        </div>
        <div class="bg-white p-3 mb-2" style="display: flex; align-items: center; justify-content: flex-end">
            <span style="margin-right: 10px; cursor: pointer" @click="resetFilter"><i class="fa fa-refresh"></i></span>
            <button class="btn btn-info" @click="applyFilter">Lọc</button>
{{--            <button class="btn btn-success" @click="exportExcel" style="margin-left: 10px;">Xuất Excel</button>--}}
        </div>
        <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
            <span>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</span>
            <div>

            </div>
        </div>
        <div class="filterable-list__list">
            <table>
                <thead>
                <th width="3%">ID</th>
                <th>Học viên</th>
                <th>Admin</th>
                <th>Link</th>
{{--                <th width="4%">Kiểu</th>--}}
                <th width="10%">Ghi chú</th>
                <th width="4%">Ảnh</th>
                <th width="5%">T.gian tạo</th>
                <th width="5%">T.gian duyệt</th>
                <th class="text-center">Thao tác</th>
                </thead>
                <tbody v-if="!loading">
                <tr v-if="results.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(result, index) in results" :key="result.id" v-if="results.length != 0">
                    <td>
                        <div>@{{ result.id }}</div>
                    </td>

                    <td>
                        <div>@{{ result.user ? result.user.username : '--'}}</div>
                        <span>@{{ result.user ? (result.user.email || result.user.phone || '--') : '--'  }}</span>
                        <span v-if="result.user">
                            <a v-if="result.user.conversation" :href="'{{url('/backend/chat#')}}' + result.user.conversation.id" target="_blank">
                                <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;" title="nhắn tin"></i>
                            </a>
                            <i v-else :class="'fa fa-comments fa-comments-' + result.user.id" style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                               @click="initConversation(result.user.id)"
                            ></i>
                        </span>

                    </td>

                    <td>
                        <div>@{{ result.admin ? result.admin.username : '--'  }}</div>
                    </td>

                    <td >
                        <div style="word-wrap: break-word;width: 30vw"><a :href="result.link" target="_blank">@{{ result.link || '--'  }}</a></div>
                        <div>
                            <span class="filterable-list__badge filterable-list__badge--orange" v-if="result.doppelganger.length > 1" @click="checkDuplicateLink(result)">trùng</span>
                            <span class="filterable-list__badge filterable-list__badge--red" v-if="!result.link.startsWith('https://www.facebook.com/') && !result.link.startsWith('https://m.facebook.com/')">sai link</span>
                        </div>
                    </td>
{{--                    <td>--}}
{{--                        <div>@{{ result.product_name || '--'  }}</div>--}}
{{--                        <div>@{{ result.product_id || '--'  }}</div>--}}
{{--                    </td>--}}
                    <td>
                        <div style="word-wrap: break-word;width: 10vw">@{{ result.comment || '--'  }} <span @click="editComment(result)" class="a-cursor-pointer"><i class="fa fa-edit text-info"></i></span></div>
                    </td>
                    <td class="text-center">
                        <span @click="editResultImage(result.id)" v-if="!result.previewImage && !result.image" class="filterable-list--bill-image-add text-info">Thêm</span>
                        <div class="filterable-list--bill-image" v-if="result.previewImage || result.image">
                            <img
                                    style="max-width: 70px; max-height: 70px"
                                    v-if="result.image || result.previewImage"
                                    :src="!result.previewImage ? '{{url('cdn/sharing_log/small')}}' + '/' + result.image : result.previewImage"
                                    data-toggle="modal"
                                    data-target="#resultImage"
                                    @click="currentResultImage = !result.previewImage ? '{{url('cdn/sharing_log/default')}}' + '/' + result.image : result.previewImage">
                            <span @click="editResultImage(result.id)" v-if="!result.previewImage" class="filterable-list--bill-image-edit">Sửa</span>
                            <div v-if="result.previewImage" class="filterable-list--bill-image-save">
                                <span class="text-success" @click="saveResultImage(result.id, result.previewImage)"><i class="fa fa-check"></i></span>
                                <span class="text-danger" @click="result.previewImage = null"><i class="fa fa-times"></i></span>
                            </div>
                        </div>

                        <form :id="'result-image-form-'+result.id">
                            <input type="text" name="inputResultId" v-model="result.id" style="display: none;"/>
                            <input type='file' :id="'inputResultImage'+result.id" name="inputResultImage" style="display: none;" @change="readURL(result.id, $event)" />
                        </form>
                    </td>
                    <td>
                        <div>@{{ result.created_at || '--'  }}</div>
                    </td>

                    <td>
                        <div>@{{ result.active_time || '--'  }}</div>
                    </td>

                    <td class="text-center">
                        <span class="text-bold" :class="{'text-success': result.status === 1, 'text-danger': result.status === -1}" v-if="result.status !== 0">@{{ result.status === -1 ? 'Không hợp lệ' : 'Hợp lệ' }}</span>
                        <span class="btn btn-sm btn-success" v-if="result.status === -1" @click="reConfirmLink(result,5)"><i class="fa fa-plus"></i>  5 </span>
                        <span class="btn btn-sm btn-success" v-if="result.status === -1" @click="reConfirmLink(result,15)"><i class="fa fa-plus"></i>  15 </span>
                        <span class="btn btn-sm btn-success" v-if="result.status === 0" @click="confirmLink(result,5)"><i class="fa fa-plus"></i>  5 </span>
                        <span class="btn btn-sm btn-success" v-if="result.status === 0" @click="confirmLink(result,15)"><i class="fa fa-plus"></i>  15 </span>
                        <span class="btn btn-sm btn-danger" v-if="result.status === 0" @click="rejectLink(result)"><i class="fa fa-ban"></i></span>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="loading">
                <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                    <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="modal fade" id="resultImage" tabindex="-1" role="dialog" aria-labelledby="resultImage" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <img style="max-width: 100%" :src="currentResultImage" class="filterable-list--bill-image-default">
                    </div>
                </div>
            </div>
        </div>
        <div class="filterable-list__paginate">
            <div>
                Hiển thị
                <select v-model="filter.per_page" @change="applyFilter">
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                </select>
                trong số @{{ loading ? '----' : total_result}} kết quả
            </div>
            <paginate
                    {{--                    v-model="filter.page"--}}
                    :page-count="filter.total_page"
                    :page-range="4"
                    :margin-pages="3"
                    :click-handler="changePage"
                    :prev-text="'&laquo;'"
                    :next-text="'&raquo;'"
                    :container-class="'pagination'"
                    :page-class="'page-item'"
                    :force-page="filter.page - 1"
            >
            </paginate>
        </div>
        <backend-modal v-if="showNoteModal" @close="closeModal">
            <h3 slot="header">Sửa ghi chú</h3>
            <div slot="body">
                <textarea class="form-control" rows="5" v-model="editingComment.comment"></textarea>
                <div class="text-center mt-5">
                    <span class="btn btn-success" @click="saveComment">Lưu</span>
                </div>
            </div>
        </backend-modal>
    </div>

    <script>
      jQuery.browser = {};
      (function () {
        jQuery.browser.msie = false;
        jQuery.browser.version = 0;
        if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
          jQuery.browser.msie = true;
          jQuery.browser.version = RegExp.$1;
        }
      })();
    </script>
    {{--    Local plugins--}}
    <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
    <script src="{{ asset('/plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
    <script type="text/javascript" src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}"></script>

    {{--     CDN--}}
    <script>
      $(document).on('show.bs.modal','#messageToAll', function () {
        filterable__list.clearMessageContent();
      });
    </script>
    <script>
      var adminId = {!! json_encode(Auth::guard('admin')->user()->id) !!};
    </script>
    <script src="https://unpkg.com/vuejs-paginate@0.9.0"></script>
    <script src="https://unpkg.com/vue-router/dist/vue-router.js"></script>
    <script src="{{asset('assets/backend/js/marketing/fb-share.js')}}?{{filemtime('assets/backend/js/marketing/fb-share.js')}}"></script>

@stop
