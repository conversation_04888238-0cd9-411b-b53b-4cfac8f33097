@extends('backend._default.dashboard')

@section('description') Khóa học combo @stop
@section('keywords') combo  @stop
@section('author') dungmori.com @stop
@section('title') Admin | Khóa học combo @stop

@section('assets')
    <link type="text/css" rel="stylesheet"
          href="{{ asset('/plugin/DataTables/DataTables-1.10.16/css/dataTables.bootstrap.min.css') }}">
    <script type="text/javascript"
            src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/jquery.dataTables.min.js') }}"></script>
    <script type="text/javascript"
            src="{{ asset('/plugin/DataTables/DataTables-1.10.16/js/dataTables.bootstrap.min.js') }}"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/css/bootstrap-select.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.12.4/js/bootstrap-select.min.js"></script>
    <style type="text/css">
        .form-horizontal .form-group {
            margin-left: 22.5px;
            margin-right: -151.5px;
            margin-bottom: 25px;
        }

        .row {
            font-family: Myriad Pro, sans-serif;
            font-weight: 500;
        }

        .btn {
            padding: 4px 6px 2px 6px;
            border-radius: 3px;
            margin: 0 0 0 3px;
            font-size: 12px;
        }

        .label {
            padding: 4px 6px 4px 8px;
            margin-right: 4px;
            cursor: pointer;
            border-radius: 3px;
        }

        .label-success {
            background: #10a31a;
        }

        .label-danger, .btn-danger {
            background: #e74c3c;
        }

        .table {
            background-color: #fff;
        }

        .text-left {
            text-align: left;
        }

        .dropdown-toggle {
            height: 40px;
        }
    </style>
    <script type="text/javascript" src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>

@stop

@section('content')
    <?php
    //    $comboTypes = ['jlpt'=>'Khóa Jlpt','eju'=>'Khóa Eju','kaiwa'=>'Khóa Kaiwa','book'=>'Sách'];
    $comboTypes = ['course' => 'Khóa học', 'book' => 'Sách'];
    ?>
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Khóa học combo
        </h4>
        @if(json_decode(Auth::guard('admin')->user()->matrix)->combo->add != null)
            <button class="add-modal btn btn-success"
                    style="right: 26px;position: absolute;width: 146px;">
                <span class="glyphicon glyphicon-plus"></span>Thêm mới
            </button>
        @endif
    </div>

    <div class="data-combo">
        @include('backend.combo.detailCombo')
    </div>
    <div id="pageModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog" role="document">
            <!-- Modal content-->
            <div class="modal-content" style="height: 80%">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <form class="form-horizontal" role="form" id="combo_form">
                    <li class="global_error text-left hidden"></li>
                    <div class="form-group" style="display: none;">
                        <label class="control-label col-sm-2" for="id">Mã số</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" name="id" id="fid">
                        </div>
                    </div>
                    <div class="form-group combo">
                        <label class="control-label col-sm-2" for="combo">Combo</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" name="combo" id="combo">
                        </div>
                    </div>
                    <div class="form-group combo">
                        <label class="control-label col-sm-2" for="combo">Tên viết tắt</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" name="alias_name" id="alias_name">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="img">Ảnh đại diện</label>
                        <div class="col-sm-6">
                            <div class="upload_complete" style="display: block;">
                                <div class="file_image_single">
                                    <div class="file_image_single_img">
                                        <img id="image_combo" src="{{url('assets/img/icon_backend')}}/no_image.png"
                                             style="width: 100px; max-height: 100px;">
                                    </div>
                                </div>
                            </div>
                            <div class="upload_action" style="margin-top:2px;">
                                <input type="file" name="img_combo" id="img_combo" onchange="previewCombo()">
                                <div class="f11 formNote" style="color: red;">
                                    CHÚ Ý - chỉ cho phép dung lượng tối đa: <b>3Mb</b> - và tệp:
                                    <b>gif,jpg,jpeg,png,svg</b>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group expire_from">
                        <label class="control-label col-sm-2" for="expire_from">Ngày bắt đầu</label>
                        <div class="col-sm-6">
                            <input type="date" class="form-control date_picker mask_datess" name="expire_from"
                                   id="expire_from">
                        </div>
                    </div>
                    <div class="form-group expire_to">
                        <label class="control-label col-sm-2" for="expire_to">Ngày kết thúc</label>
                        <div class="col-sm-6">
                            <input type="date" class="form-control date_picker mask_datess" name="expire_to"
                                   id="expire_to">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="expire_to">Loại combo</label>
                        <div class="col-sm-6">
                            <select name="type" id="type" class="form-control selectpicker" data-live-search="true"
                                    onchange="findItemType()">
                                @if(!empty($comboTypes))
                                    @foreach($comboTypes as $key => $item)
                                        <option value="{{$key}}">{{$item}}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>
                    <div class="form-group type-list" style="display: none;">
                        <label class="control-label col-sm-2">Khóa chi tiết </label>
                        <div class="col-sm-6">
                            <select class="form-control" name="typeList" id="typeList">
                                <option value="">Chọn khóa</option>
                                <option value="jlpt">Jlpt</option>
                                <option value="eju">Eju</option>
                                <option value="kaiwa">Kaiwa</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group course">
                        <label class="control-label col-sm-2" for="course">Áp dụng cho khóa học/sách</label>
                        <div class="col-sm-6">
                            <select class="selectpicker form-control" multiple data-live-search="true" name="course"
                                    id="course"></select>
                        </div>
                    </div>

                    <div class="form-group watch_expired">
                        <label class="control-label col-sm-2" for="watch_expired">Thời hạn xem(Ngày)</label>
                        <div class="col-sm-6">
                            <input type="number" class="form-control" name="watch_expired" id="watch_expired">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-sm-2" for="price">Giá(VND)</label>
                        <div class="col-sm-6">
                            <input type="number" class="form-control" name="price" id="price">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-sm-2" for="jpy">Giá(JPY)</label>
                        <div class="col-sm-6">
                            <input type="number" class="form-control" name="jpy" id="jpy">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="price_at_vn">Giá mua tại VN(VND)</label>
                        <div class="col-sm-6">
                            <input type="number" class="form-control" name="price_at_vn" id="price_at_vn">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-sm-2" for="jpy_price_at_vn">Giá mua tại VN(JPY)</label>
                        <div class="col-sm-6">
                            <input type="number" class="form-control" name="jpy_price_at_vn" id="jpy_price_at_vn">
                        </div>
                    </div>
                    <div class="description" style="display: block;">
                        {{--giá plus--}}
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="description">Mã Số</label>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" placeholder="Mã số 1" name="description"
                                       id="description">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row">
                                <label class="control-label col-md-9 text-center text-primary" for="extra_days">Khóa
                                    Plus</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="extra_days">Ngày cộng thêm</label>
                            <div class="col-sm-6">
                                <input type="number" class="form-control" name="extra_days" id="extra_days" min="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="extra_price">Giá(VND)</label>
                            <div class="col-sm-6">
                                <input type="number" class="form-control" name="extra_price" id="extra_price" min="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="extra_jp_price">Giá(JPY)</label>
                            <div class="col-sm-6">
                                <input type="number" class="form-control" name="extra_jp_price" id="extra_jp_price"
                                       min="0">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="extra_jp_price">Mô tả</label>
                        <div class="col-sm-6">
                            <textarea id="extra_desc" name="extra_desc"></textarea>
                        </div>
                    </div>
                    <div class="form-group public">
                        <label class="control-label col-sm-2" for="public">Trạng thái</label>
                        <div class="col-sm-6">
                            <label class="tcb-inline">
                                <input type="radio" class="tc" id="status_off" name="public" value="0">
                                <span class="labels">Tắt</span>
                            </label>
                            <label class="tcb-inline">
                                <input type="radio" class="tc" id="status_on" name="public" value="1">
                                <span class="labels">Bật</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group public">
                        <label class="control-label col-sm-2" for="public">Hiển thị cho đơn hàng</label>
                        <div class="col-sm-6">
                            <label class="tcb-inline">
                                <input type="radio" class="tc" id="invoice_off" name="is_invoice" value="0">
                                <span class="labels">Tắt</span>
                            </label>
                            <label class="tcb-inline">
                                <input type="radio" class="tc" id="invoice_on" name="is_invoice" value="1">
                                <span class="labels">Bật</span>
                            </label>
                        </div>
                    </div>
                </form>
                <div class="modal-body">
                    <div class="deleteContent">
                        Có muốn xóa combo "<span class="dname"></span>" này đi không? <span
                                class="hidden did"></span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        $(document).ready(function () {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        });
        $('select').selectpicker();
        var extra_desc = CKEDITOR.replace('extra_desc', {
            filebrowserBrowseUrl: '/backend/ckfinder/browser',
        });
        setDataTable();

        function setDataTable() {
            $('#table_combo').DataTable({
                "order": [[0, "desc"]],
                "stateSave": true
            });
        }

        function previewCombo() {
            var preview = document.querySelector('#image_combo');
            var file = document.querySelector('#img_combo').files[0];
            var reader = new FileReader();
            reader.onloadend = function () {
                preview.src = reader.result;
            }
            if (file) {
                reader.readAsDataURL(file);
            } else {
                preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
            }
        }

        //thao tac them, xoa, sua
        //created
        $(document).on('click', '.add-modal', function () {
            $('#footer_action_button').text(" Thêm");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            unlock();
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').addClass('add');
            $('.modal-title').text('Thêm');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            fillmodalData(null);
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '60%');
            $('#pageModal').css('left', '20%');
        });
        //edit
        $(document).on('click', '.edit-modal', function (e) {
            $('#footer_action_button').text(" Sửa");
            $('#footer_action_button').addClass('glyphicon-check');
            $('#footer_action_button').removeClass('glyphicon-trash');
            $('.actionBtn').addClass('btn-success');
            $('.actionBtn').removeClass('btn-danger');
            $('.actionBtn').removeClass('delete');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('edit');
            $('.modal-title').text('Sửa');
            $('.deleteContent').hide();
            $('.form-horizontal').show();
            const combo = $(this).data('info');

            if (combo.id === 3 || combo.id === 19) {
                lockCombo();
            } else {
                unlock();
            }
            fillmodalData(combo);
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '60%');
            $('#pageModal').css('left', '20%');
        });
        //delete
        $(document).on('click', '.delete-modal', function () {
            $('#footer_action_button').text(" Xóa");
            $('#footer_action_button').removeClass('glyphicon-check');
            $('#footer_action_button').addClass('glyphicon-trash');
            $('.actionBtn').removeClass('btn-success');
            $('.actionBtn').addClass('btn-danger');
            $('.actionBtn').removeClass('edit');
            $('.actionBtn').removeClass('add');
            $('.actionBtn').addClass('delete');
            $('.modal-title').text('Xóa');
            $('.deleteContent').show();
            $('.form-horizontal').hide();
            const combo = $(this).data('info');
            $('.did').text(combo.id);
            $('.dname').html(combo.name);
            $('#pageModal').modal('show');
            $('#pageModal').css('width', '50%');
            $('#pageModal').css('left', '27%');
        });

        function lockCombo() {
            $(".combo").hide();
            $(".expire_from").hide();
            $(".expire_to").hide();
            $(".course").hide();
            $(".watch_expired").hide();
            $('.public').hide();
        }

        function unlock() {
            $(".combo").show();
            $(".expire_from").show();
            $(".expire_to").show();
            $(".course").show();
            $(".watch_expired").show();
            $('.public').show();
        }

        function getComboType(type) {
            let result = 'course';
            if (type === 'book') {
                result = 'book'
            }
            return result;
        }

        function fillmodalData(combo) {
            if (combo != null) {
                const services = JSON.parse(combo.services);
                const type = getComboType(combo.type);
                $('#type').selectpicker('val', type);
                if (type === 'book') {
                    findItemType(services.books);
                } else {
                    const result = services.course_watch_expired_value;
                    findItemType(services.courses);
                    $("#watch_expired").val(result);
                }
                $('#fid').val(combo.id);
                $('#combo').val(combo.name);
                $('#alias_name').val(combo.alias_name);
                $('#expire_from').val(combo.expire_from_day);
                $('#expire_to').val(combo.expire_to_day);
                $('#price').val(combo.price);
                $('#description').val(combo.desc);
                (combo.public === 0) ? $('#status_off').prop('checked', true) : $('#status_on').prop('checked', true);
                (combo.is_invoice === 0) ? $('#invoice_off').prop('checked', true) : $('#invoice_on').prop('checked', true);
                $('#typeList').selectpicker('val', combo.type);
                $('#course').selectpicker('val', JSON.parse(combo.services).courses);
                $('#watch_expired').val(JSON.parse(combo.services).course_watch_expired_value);
                if (combo.image_name === null || combo.image_name === "") {
                    $('#image_combo').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
                } else {
                    $('#image_combo').attr('src', '{{url('cdn/combo/default')}}/' + combo.image_name);
                }
                $('#img_combo').val('');
                $('.global_error').addClass('hidden');
                $('#jpy').val(combo.jpy_price);
                $('#price_at_vn').val(combo.price_at_vn);
                $('#jpy_price_at_vn').val(combo.jpy_price_at_vn);
                $('#extra_price').val(combo.extra_price);
                $('#extra_jp_price').val(combo.extra_jp_price);
                $('#extra_days').val(combo.extra_days);
                extra_desc.setData(combo.extra_desc);
            } else {
                $('#combo').val('');
                $('#alias_name').val('');
                $('#expire_from').val(null);
                $('#expire_to').val(null);
                $('#type').selectpicker('val', null)
                $('#typeList').selectpicker('val', null);
                $('#price').val('');
                $('#description').val('');
                $('#status_off').prop('checked', true);
                $('#invoice_off').prop('checked', true);
                $('#course').selectpicker('val', '');
                $('#watch_expired').val('');
                $('#image_combo').attr('src', '{{url('assets/img/icon_backend')}}/no_image.png');
                $('#img_combo').val('');
                $('.global_error').addClass('hidden');
                $('#jpy').val('');
                $('#price_at_vn').val('');
                $('#jpy_price_at_vn').val('');
                $('#extra_price').val('');
                $('#extra_jp_price').val('');
                $('#extra_days').val('');
                extra_desc.setData('');
            }
        }

        function makeListOption(type, data) {
            const listName = type === 'book' ? 'Sách' : 'Khóa học';
            const listClass = type + '-list';
            let listGroup = '<optgroup label="' + listName + '" class="' + listClass + '">';
            for (let i = 0; i < data.length; i++) {
                listGroup += '<option value="' + data[i].id + '" id="item' + data[i].id + '">' + listName + ' ' + data[i].name + '</option>'
            }
            listGroup += '</optgroup>';
            return listGroup;
        }

        function makeSelectList(name, data) {
            const selectList = document.createElement("select");
            selectList.id = name;
            selectList.name = name;
            selectList.className = 'form-control';
            if (data.length > 0) {
                for (let i = 0; i < data.length; i++) {
                    const option = document.createElement("option");
                    option.value = data[i].id;
                    option.text = data[i].name;
                    selectList.appendChild(option);
                }
            }
            return selectList;
        }

        function findItemType(selected = false) {
            const input = $('#type').val();
            let selectList = '';
            if (input === 'course') {
                //hiện chọn khóa
                $('.type-list').show();
                //hiện input Thời hạn xem
                $('.watch_expired').show();
                $('#watch_expired')[0].disabled = false;
                //Hiển thị mã số
                $('.description').show();
                $('#description')[0].disabled = false;
            } else {
                //ẩn chọn khóa
                $('.type-list').hide();
                //ẩn input Thời hạn xem
                $('#watch_expired')[0].disabled = true;
                $('.watch_expired').hide();
                //ẩn thị mã số
                $('.description').hide();
                $('#description')[0].disabled = true;
            }
            $.ajax({
                type: 'post',
                url: '/backend/combo/find-item-type',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'type': input
                },
                success: function (data) {
                    const course = $('#course')
                    let list = '';
                    //remove old list
                    course.empty();
                    course.selectpicker("refresh");
                    const books = data.books;
                    const courses = data.courses;
                    //make new list
                    if (books.length) {
                        const bookList = makeListOption('book', books);
                        list += bookList;
                    }
                    if (courses.length) {
                        const courseList = makeListOption('course', courses)
                        list += courseList;
                    }
                  console.log(books);
                    //add new list
                    course.append(list)
                    if (selected) {
                        course.selectpicker('val', selected);
                    }
                    course.selectpicker("refresh");
                }
            });
        }

        $('.modal-footer').on('click', '.add', function () {
            var dataCombo = new FormData($("#combo_form")[0]);
            dataCombo.append('listCourse', $('#course').val());
            dataCombo.append('extra_desc', CKEDITOR.instances['extra_desc'].getData());
            $.ajax({
                type: 'post',
                url: '/backend/combo/create',
                processData: false,
                contentType: false,
                data: dataCombo,
                success: function (data) {
                    if (data.errors) {
                        if (Object.keys(data.errors).length > 0) {
                            $('#pageModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    } else {
                        if (data === 'imagetype') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh không đúng định dạng");
                        } else if (data === 'imagesize') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh vượt quá 3MB");
                        } else {
                            $('#pageModal').modal('hide');
                            $('.data-combo').html(data);
                            setDataTable();
                        }
                    }
                }
            });
        });

        $('.modal-footer').on('click', '.edit', function () {
            var dataCombo = new FormData($("#combo_form")[0]);
            dataCombo.append('listCourse', $('#course').val());
            dataCombo.append('extra_desc', CKEDITOR.instances['extra_desc'].getData());
            $.ajax({
                type: 'post',
                url: '/backend/combo/edit',
                processData: false,
                contentType: false,
                data: dataCombo,
                success: function (data) {
                    if (data.errors) {
                        if (Object.keys(data.errors).length > 0) {
                            $('#pageModal').modal('show');
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Dữ liệu còn trống !");
                        }
                    } else {
                        if (data === 'imagetype') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh không đúng định dạng");
                        } else if (data === 'imagesize') {
                            $('.global_error').removeClass('hidden');
                            $('.global_error').text("Ảnh vượt quá 3MB");
                        } else {
                            $('#pageModal').modal('hide');
                            $('.data-combo').html(data);
                            setDataTable();
                        }
                    }
                }
            });
        });

        $('.modal-footer').on('click', '.delete', function () {
            $.ajax({
                type: 'post',
                url: '/backend/combo/delete',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('.did').text()
                },
                success: function (data) {
                    $('#pageModal').modal('hide');
                    $('.item' + $('.did').text()).remove();
                    $('.data-combo').html(data);
                    setDataTable();
                }
            });
        });
    </script>
@stop
