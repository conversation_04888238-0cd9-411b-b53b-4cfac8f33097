<table class="table table-borderless" id="table_combo">
	<thead>
		<tr>
			<th class="text-center"><PERSON><PERSON></th>
			<th class="text-center">Ảnh đại diện</th>
			<th class="text-center">Combo</th>
			<th class="text-center">Gi<PERSON></th>
			<th class="text-center"><PERSON><PERSON><PERSON></th>
			<th class="text-center">Plus</th>
			<th class="text-center">Hạn</th>
			<th class="text-center">Loại</th>
			<th class="text-center">Trạng thái</th>
			<th class="text-center">Hà<PERSON> động</th>
		</tr>
	</thead>
	<tbody>
		@foreach($combo as $item)
		<tr class="item{{$item->id}}">
			<td class="text-center">{{$item->id}}</td>
			<td class="text-center">
				<img
				@if (is_null($item) || $item->image_name == null || $item->image_name == "")
					src="{{url('assets/img/icon_backend')}}/no_image.png"
				@else
					src="{{url('/cdn/combo/small/'.$item->image_name)}}"
				@endif
				width="200px"
				height="40px">
			</td>
			<td class="text-center">
				<a href="{{url('/khoa-hoc/combo/'.$item->id)}}" target="_blank">{{$item->name}}</a><br>
			</td>
			<td class="text-center">{{ number_format($item->price) }}</td>
			<td class="text-center">
				<input id="price_{{ $item->id }}" value="{{ intval($item->jpy_price) }}" class="border" onblur="savePrice({{ $item->id }})">
			</td>
			<td class="text-center">
				<p>{{number_format($item->extra_price)}}</p>
			</td>
			<td class="text-center">
				@if($item->expire_to_day >= date('Y-m-d'))
					<b>Còn hạn</b>
				@else
					<b style="color: red;">Hết hạn</b>
				@endif
					<br>{{$item->expire_from_day}}
					<br>{{$item->expire_to_day}}
			</td>
			<td class="text-center">
				<?php $comboTypes = ['jlpt'=>'Khóa Jlpt','eju'=>'Khóa Eju','kaiwa'=>'Khóa Kaiwa','book'=>'Sách','all'=>'Tổng hợp']; ?>
				{{isset($comboTypes[$item->type]) ? $comboTypes[$item->type] : '-'}}
			</td>
			<td class="text-center">
	            @if($item->public == 1)
	                <span class="label label-success">Bật</span>
	            @else
	                <span class="label label-danger">Tắt</span>
	            @endif
	        </td>
			<td class="text-center">
				@if(json_decode(Auth::guard('admin')->user()->matrix)->combo->edit != null)
					<button class="edit-modal btn btn-info"
						data-info="{{$item}}">
						<span class="glyphicon glyphicon-edit"></span> Sửa
					</button>
				@endif

				@if(json_decode(Auth::guard('admin')->user()->matrix)->combo->delete != null)
					<button class="delete-modal btn btn-danger"
						data-info="{{$item}}">
						<span class="glyphicon glyphicon-trash"></span> Xóa
					</button>
				@endif
			</td>
		</tr>
		@endforeach
	</tbody>
</table>

<script>
	function savePrice(id) {
		var input = document.getElementById("price_" + id);
		$.post(
			window.location.origin + '/backend/combo/save-jpy-price',
			{ id: id, jpy_price: input.value }, function (res) {
			}
		);
	}

	$(document).keyup(function (event) {
		if (event.key === "Enter") {
			if (document.activeElement) {
				if (document.activeElement.id.includes('price_')) {
					var id = document.activeElement.id.substr(6);
					savePrice(id);
				}
			}
		}
	});
</script>