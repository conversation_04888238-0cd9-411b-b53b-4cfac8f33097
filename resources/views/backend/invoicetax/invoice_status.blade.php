<table class="table table-borderless" style="background: #fff;">
    <thead>
        <tr>
            <th class="text-center"> <PERSON><PERSON> đơn </th>
            <th class="text-center" style="text-align: left;"><PERSON><PERSON><PERSON> phẩm</th>
            <th class="text-center"><PERSON><PERSON><PERSON><PERSON> hàng</th>
            <th class="text-center">Trạng thái</th>
            <th class="text-center">Số tiền</th>
            <th class="text-center">Th<PERSON>i gian</th>
            <th class="text-center">Hành động</th>
        </tr>
    </thead>
    <tbody>
        @foreach($invoice as $item)
            <tr class="item{{$item->id}}">
                <td class="text-center"><a href="{{ url('checkout') }}/{{$item->uuid}}" target="_blank" ><b>{{$item->uuid}}</b></a></td>
                <td class="text-center" style="text-align: left;">
                    <b>
                    @if($item->product_type == "course")
                        <PERSON><PERSON><PERSON><PERSON> h<PERSON><PERSON>
                    @else
                        Combo
                    @endif
                    </b>
                    {{$item->product_name}}<br/>

                    @if($item->payment_method_id == '5')
                        <img src="{{url('/assets/img/jp.gif')}}" style="border: 1px solid #CCC;">
                    @elseif($item->payment_method_id == '6')
                        <i class="fa fa-mobile" aria-hidden="true"></i>
                    @else
                        <img src="{{url('/assets/img/vn.gif')}}">
                    @endif

                    @if($item->payment_method_id == '1')
                        TT paypal <br>
                    @elseif($item->payment_method_id == '2')
                        CKNH Việt Nam <br>
                    @elseif($item->payment_method_id == '3')
                        Nộp tại VP <br>
                    @elseif($item->payment_method_id == '4')
                        Ship mã thẻ
                        @if($item->invoice_status == 'completed')
                            <i class="fa fa-credit-card" aria-hidden="true"></i> 
                            <br><span style="font-size: 12px;">{{ $item->voucher_key }}</span>
                        @endif
                    @elseif($item->payment_method_id == '5')
                    CKNH tại Nhật <br>
                    @elseif($item->payment_method_id == '6')
                    In app purchase <br>
                    @else
                       ---Chưa chọn--- <br>
                    @endif
                </td>
                <td class="text-center">
                    <b>{!! json_decode($item->info_contact)->name !!}</b> • {!! json_decode($item->info_contact)->phone !!} <br>
                    {!! json_decode($item->info_contact)->email !!} 
                </td>
                <td class="text-center">

                    @if($item->invoice_status == 'completed')
                        <span class="label new-label green-label" >Hoàn thành</span>
                    @elseif($item->invoice_status == 'new')
                        <span class="label new-label orange-label">Chờ xử lý...</span>
                    @else
                        <span class="label new-label red-label">Đã hủy</span>
                    @endif
                    
                </td>
                <td class="text-center">
                    {{ number_format($item->price) }}₫
                </td>
                <td class="text-center" style="font-size: 15px; line-height: 1.1;">
                    {{ $item->getFriendlyTime() }}
                            
                    {{-- @if($item->admin_active_name != null)
                        Active: {{friendlyTime($item->active_time)}} <br/>
                        <span style="font-size: 11px;">Admin: {{$item->admin_active_name}}</span>
                    @endif --}}
                    
                </td>
                <td class="text-center">
                    @if($item->invoice_status == 'new')
                    <a class="label new-label red-label" onclick="alert('Tài khoản của bạn không có quyền khích hoạt')">
                        Kích hoạt <i class="fa fa-caret-right" aria-hidden="true"></i>
                    </a>
                    <a class="label" style="color: red;">Hủy</a>
                    @else
                        ---
                    @endif

                    <a class="label" style="color: #666;" onclick="hInvoice({{$item->id}})">Ẩn</a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
{{ $invoice->render() }}

<script type="text/javascript">

    $(document).ready(function() {
        $.ajaxSetup({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });   
    });

    function activeInvoice(id){

        var r = confirm("Bạn có chắc chắn muốn kích hoạt đơn hàng này ???");
        if (r == true){
            $.ajax({
                type: 'post',
                url: '/backend/invoice/do-active',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': id
                },
                success: function(data) {
                    if(data == 'success'){
                        alert('Kích hoạt thành công');
                        $(".item"+ id).fadeOut();
                    }else alert('Kích hoạt lỗi');
                }
            });
        }
    }

    function cancelInvoice(id){

        var r = confirm("Bạn có chắc chắn muốn hủy đơn hàng này ?");
        if (r == true) {
            
            $.ajax({
                type: 'post',
                url: '/backend/invoice/do-cancel',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': id
                },
                success: function(data) {
                    if(data == 'success') {
                        $(".item"+ id).fadeOut();
                    }
                    else alert('Không hủy được');
                }
            });
        }
    }

</script>
