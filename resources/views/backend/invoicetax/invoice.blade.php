@extends('backend._default.dashboard')

@section('description') Quản lý đơn hàng @stop
@section('keywords') invoice @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý đơn hàng @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <style type="text/css">
        .form-control{padding: 7px 8px;}
    </style>
@stop

@section('content') 
	<div class="row bg-title">
        {{-- <h4 class="page-title pull-left">  <PERSON><PERSON><PERSON> hàng
            <a href="{{url('backend/thanhtoan/render')}}" target="_blank"><span style="color: #f06;">Render</span></a>
        </h4> --}}
        <form class="form-inline" style="float: left;">
            {{ csrf_field() }}
            <input type="text" class="check_url" name="url" style="display: none;">
            <input type="text" style="width: 180px;" class="form-control mb-2 mr-sm-2" name="email" placeholder="Lọc theo Email/Tên/Sđt"> &nbsp; &nbsp; &nbsp; 
            <input type="text" style="width: 130px;" class="form-control mb-2 mr-sm-2" name="date_from" id="date_from" placeholder="Từ ngày">
            <input type="text" style="width: 130px;" class="form-control mb-2 mr-sm-2" name="date_to" id="date_to" placeholder="Đến ngày"> &nbsp; &nbsp; &nbsp; 
            <select type="text" style="width: 180px;" class="form-control mb-2 mr-sm-2" name="method" id="method">
                <option value="created"> Ngày mới nhất </option>
                <option value="active_up"> Giá trị đơn tăng dần</option>
                <option value="active_down"> Giá trị đơn giảm dần</option>
            </select> 
            <a type="button" class="search btn btn-info mb-2">Tìm</a>
            @if(json_decode(Auth::guard('admin')->user()->matrix)->invoice->export != null)
                <button type="button" class="export btn btn-info mb-2">Xuất Excel</button>
            @endif
            <a href="{{url('backend/thanhtoan')}}"> &nbsp; <i class="fa fa-refresh"></i> &nbsp; &nbsp; &nbsp; </a>
        </form>
    </div>

	<div id="tabs" style="width: calc(100vw - 20px); margin-left: -20px;">
       <ul class="nav nav-tabs">
            <li>
                <a href="{{url('backend/thanhtoan/completed')}}" role="tab" data-toggle="tab">
                    <b>Đơn hàng hoàn thành</b>
                </a>
            </li>
            <li> 
                <a href="{{url('backend/thanhtoan/pending')}}" role="tab" data-toggle="tab">
                    <b>Đơn hàng đang chờ xử lý</b>
                </a>
            </li>
            <li> 
                <a href="{{url('backend/thanhtoan/canceled')}}" role="tab" data-toggle="tab">
                    <b>Đơn hàng đã hủy</b>
                </a>
            </li>
        </ul>
	</div>
   
	<script>

		$(document).ready(function() {
            $.ajaxSetup({
                headers: {
                  'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            }); 
            doSearch();  
        });

        //datetime picker
        $('#date_from').datetimepicker({
            language: 'vi'
        });
        $('#date_to').datetimepicker({
            language: 'vi'
        });


        function hInvoice(id){
            $.ajax({
                type: 'get',
                url: '/backend/thanhtoan/hide',
                data: {id, id},
                success: function(res) {
                    if(res == "success") $(".item"+id).fadeOut();
                }
            });
        }

        //tabs load url
		$( function() {
            var index, tab = ($('input[name=url]').val());
            if(tab == "canceled"){
                index = 2;
            }else if(tab == 'pending'){
                index = 1;
            }else{
                index = 0;
            }
		    $( "#tabs" ).tabs({
                active: index,
		      	beforeLoad: function( event, ui ) {
                    $( ".check_url" ).val((ui.ajaxSettings.url).split('/')[5]);
                    //if fail
			        ui.jqXHR.fail(function() {
			          ui.panel.html("Đang load ...");
		        	});
	      		}
	    	});
	  	});

        //tim kiem
        $(document).on('click', '.search', function() {doSearch(); });
        $(document).keypress(function(e) {
            if (e.which == 13) {
                doSearch();
                e.preventDefault();
            }
        });

        function doSearch(){
            if( $('input[name=email]').val() != '' || $('input[name=date_from]').val() != '' || $('input[name=date_to]').val() != ''){

                var data = {
                    '_token': $('input[name=_token]').val(),
                    'status': $('input[name=url]').val(),
                    'email': $('input[name=email]').val(),
                    'date_from': $('input[name=date_from]').val(),
                    'date_to': $('input[name=date_to]').val(),
                    'method': $('select[name=method]').val()
                };

                $.ajax({
                    type: 'get',
                    url: '/backend/thanhtoan/find',
                    data: data,
                    success: function(res) {

                        console.log(data);
                        $('#tabs').empty().html(res);
                        $(document).on('click', '#tabs .pagination a', function(e) {
                            var date_from = $('#date_from').val();
                            var date_to = $('#date_to').val();
                            var method = $('#order').val();
                            var status = $('input[name=url]').val();
                            e.preventDefault();
                            var page = $(this).attr('href').split('page=')[1];
                            resultPage(page,'/backend/thanhtoan/find/?page=', '#tabs', date_from, date_to, method, status);
                        });
                    }
                });
            }
        }

        function resultPage(page,link, div_id, date_from, date_to, method, status ){
            $.ajax({
                type: 'get',
                url: link + page,
                cache: false,
                data: {
                    date_from : date_from,
                    date_to : date_to,
                    method : method,
                    status : status
                }
            }).done(function(data){
                $(div_id).empty().html(data)
            })
        }


        //export
         $(document).on('click', '.export', function() {
            $.ajax({
                type: 'get',
                url: '/backend/thanhtoan/export',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'status': $('input[name=url]').val(),
                    'email': $('input[name=email]').val(),
                    'date_from': $('input[name=date_from]').val(),
                    'date_to': $('input[name=date_to]').val(),
                    'orderby': $('select[name=method]').val()
                },
                success: function() {
                    window.location = '/upload/excel/donhang.xls';
                }
            });
        });
         //pagination
	  	$(document).on('click', '#ui-id-2 .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page, '/backend/thanhtoan/completed?page=', '#ui-id-2');
	    });
	    $(document).on('click', '#ui-id-4 .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page, '/backend/thanhtoan/pending?page=',  '#ui-id-4');
	    });
	    $(document).on('click', '#ui-id-6 .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            readPage(page, '/backend/thanhtoan/canceled?page=', '#ui-id-6');
	    });
	    function readPage(page, link, div_id){
            $.ajax({
                url: link + page
           	}).done(function(data){
                $(div_id).html(data)
           	})
	    }
	</script>

@stop