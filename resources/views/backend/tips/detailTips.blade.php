@extends('backend._default.dashboard')

@section('description') <PERSON> tiết tin tức @stop
@section('keywords') blog @stop
@section('author') dungmori.com @stop
@section('title') Admin | <PERSON> tiết tin tức @stop

@section('assets')
	<script type="text/javascript"  src="{{ asset('/plugin/ckeditor/ckeditor.js') }}"></script>
	<script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.11.1/jquery.validate.min.js"></script>
@stop

@section('content')

	<script type="text/javascript">
		function hideScheduleTime(){
			console.log("Tắt lập lịch");
			$(".schedule-post").css('display', 'none');
		}
		function showScheduleTime(){
			console.log("Bật lập lịch");
			$(".schedule-post").css('display', 'block');
		}
	</script>

	<div class="row bg-title">
        <h4 class="page-title pull-left">
            Chỉnh sủa tin tức
        </h4>
        @if(json_decode(Auth::guard('admin')->user()->matrix)->blog->add != null)
	        <button class="add-modal btn btn-success"
					style="right: 26px;position: absolute;width: 146px;" onclick="location.href='{{url('backend/tips/add')}}'">
					<span class="glyphicon glyphicon-plus"></span>Thêm mới
			</button>
		@endif
    </div>

	<div>
		<form class="form-horizontal" action="{{url('/backend/tips/update')}}" method="POST" enctype="multipart/form-data" id="blog_form">
			 {{ csrf_field() }}
			<input type="text" name="id" style="display: none;" value="{{(is_null($tips)) ? "" : $tips[0]->id}}">
			<div class="form-group">
				<label class="control-label col-sm-2">
					Tiêu đề
				</label>
				<div class="col-sm-9">
					<input type="text" name="title" class="form-control" id="title" value="{{(is_null($tips)) ? "" : $tips[0]->title}}" placeholder="Nhập tiêu đề">
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">
					Series
					<span class="text-danger req">*</span>
				</label>
				<div class="col-sm-9">
					<select class="form-control" name="series" id="series">
						@if(is_null($tips))
							@foreach($listSeries as $series)
								<option value="{{$series->id}}">
									{{$series->name}}
								</option>
							@endforeach
						@else
							@foreach($listSeries as $series)
								@if($series->id == $tips[0]->series_id)
									<option value="{{$series->id}}" selected>
										{{$series->name}}
									</option>
								@else
									<option value="{{$series->id}}">
										{{$series->name}}
									</option>
								@endif
							@endforeach
						@endif
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-sm-2">Hình ảnh</label>
				<div class="col-sm-9">
					<div class="upload_complete" style="display: block;">
                        <div class="file_image_single">
                            <div class="file_image_single_img">
                            	@if (is_null($tips) || $tips[0]->image_name == null || $tips[0]->image_name == "")
                                	<img id ="image_blog" src="{{url('assets/img/icon_backend')}}/no_image.png" style="width: 100px; max-height: 100px;">
                            	@else
                            		<img id ="image_blog" src="{{url('cdn/blog/default')}}/{{$tips[0]->image_name}}" style="width: 100px; max-height: 100px;">
                            	@endif
                            </div>
                        </div>
                    </div>
                    <div class="upload_action" style="margin-top:2px;">
                        <input type="file" name="img_blog" id="img_blog" onchange="previewBlog()">
                        <div class="f11 formNote" style="color: red;">
                            CHÚ Ý - chỉ cho phép dung lượng tối đa: <b>3Mb</b> - và tệp: <b>gif, jpg, jpeg, png, svg</b>
                        </div>
                    </div>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">
					Hiển thị ảnh chi tiết
					<span class="req">*</span>
				</label>
				<div class="col-sm-9">
					@if(is_null($tips))
						<input type="checkbox" name="show_image" checked="true">
					@elseif($tips[0]->show_image == 1)
						<input type="checkbox" name="show_image" checked="true">
					@else
						<input type="checkbox" name="show_image">
					@endif
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">
					Nội dung thông báo
					<span class="text-danger req">*</span>
				</label>
				<div class="col-sm-9">
					<textarea  class="form-control" name="intro" row="3" required>{{ (is_null($tips)) ? "" : $tips[0]->intro}}</textarea>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">
					Nội dung
				</label>
				<div class="col-sm-9">
					<textarea  class="form-control" name="content" id="content">{{(is_null($tips)) ? "" : $tips[0]->content}}</textarea>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">
					Trạng thái
					<span class="req">*</span>
				</label>
				<div class="col-sm-9">

					{{-- nếu là tạo mới blog --}}
					@if(is_null($tips))
						<label class="tcb-inline">
							<input type="radio" class="tc" id="status_off" name="public" value="0" checked onclick="showScheduleTime()">
							<span class="labels">Tắt</span>
						</label>
						<label class="tcb-inline">
							<input type="radio" class="tc" id="status_on" name="public" value="1" onclick="hideScheduleTime()" >
							<span class="labels">Bật</span>
						</label>
					@elseif($tips[0]->public == 1)
						<label class="tcb-inline">
							<input type="radio" class="tc" id="status_off" name="public" value="0" onclick="showScheduleTime()">
							<span class="labels">Tắt</span>
						</label>
						<label class="tcb-inline">
							<input type="radio" class="tc" id="status_on" name="public" value="1" checked onclick="hideScheduleTime()">
							<span class="labels">Bật</span>
						</label>
						{{-- kiểm tra xem trạng thái public tắt hay bật để ẩn hiện trạng thái lập lịch --}}
						<script type="text/javascript">$(document).ready(function(){ hideScheduleTime(); });</script>
					@else
						<label class="tcb-inline">
							<input type="radio" class="tc" id="status_off" name="public" value="0" checked onclick="showScheduleTime()">
							<span class="labels">Tắt</span>
						</label>
						<label class="tcb-inline">
							<input type="radio" class="tc" id="status_on" name="public" value="1" onclick="hideScheduleTime()" >
							<span class="labels">Bật</span>
						</label>
					@endif

					<p class="schedule-post">Lập lịch đăng bài lúc
						<input type="time" name="schedule_time"
							@if(isset($tips) && $tips[0]->schedule_at != null) value="{{substr($tips[0]->schedule_at, 11, 5)}}" @endif
						/>
						<input type="date" name="schedule_date" min="{{$today->format('Y-m-d')}}"
							@if(isset($tips) && $tips[0]->schedule_at != null) value="{{substr($tips[0]->schedule_at, 0, 10)}}" @endif
						/>
					</p>

				</div>
			</div>
			<div class="form-group">
		     	<div class="col-sm-offset-2 col-sm-10">
			        <button type="submit" class="btn btn-success abc">Thực hiện</button>
			        <a href="{{url('backend/tips')}}" class="btn btn-danger">Hủy bỏ</a>
		      	</div>
	    	</div>
		</form>
		<span></span>
	</div>
	<script>
		CKEDITOR.replace( 'content', {
		   	filebrowserBrowseUrl: '/backend/ckfinder/browser',
		});
		function previewBlog() {
	        var preview = document.querySelector('#image_blog');
	        var file    = document.querySelector('#img_blog').files[0];
	        var reader  = new FileReader();
	        reader.onloadend = function () {
	            preview.src = reader.result;
	        }
	        if(file) {
	        	var nameFile = file.name;
            	var extension = nameFile.substring(nameFile.lastIndexOf('.') + 1).toLowerCase();
            	if(((file.size)/1000000) > 3){
            		alert('Ảnh vượt quá dung lượng');
            		preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
            		$('#img_blog').val('');
            	}else if(extension != 'gif' && extension != 'jpg' && extension != 'jpeg' && extension != 'png' && extension != 'svg'){
            		alert('Ảnh không đúng định dạng');
            		preview.src = "{{url('assets/img/icon_backend')}}/no_image.png";
            		$('#img_blog').val('');
            	}else{
	            	reader.readAsDataURL(file);
	        	}
	        }else {
	            preview.src = "{{url('upload')}}/teacher/no_image.png";
	        }
    	}
    	//
    	$(document).ready(function() {
	    	$("#blog_form").validate({
			    ignore: [],
			    rules: {
			      	content: {
			      		// required: function() {
	                    //  	CKEDITOR.instances.content.updateElement();
	                    // }
	                }
			      	},
			      	messages: {
	                    content: "Dữ liệu còn trống"
	                }
			});
		});
	</script>

@stop
