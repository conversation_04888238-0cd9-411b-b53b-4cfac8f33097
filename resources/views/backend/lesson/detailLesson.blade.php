<table class="table table-borderless" id="lesson_table">
    <thead>
        <tr>
            <th class="text-center"><PERSON><PERSON></th>
            <th class="text-left"><PERSON><PERSON><PERSON> học</th>
            <th class="text-left"><PERSON>h<PERSON>m khóa học</th>
            <th class="text-center">Gi<PERSON><PERSON> viên</th>
            <th width="140px" class="text-center">Loại</th>
            <th class="text-center">Trạng thái</th>
            <th class="text-center">Ẩn tiêu đề</th>
            <th class="text-center">Hành động</th>
        </tr>
    </thead>
    <tbody class="lessonCourse">
        @foreach($lesson as $item)
        <tr class="item{{$item->id}}">
            <td class="text-center">{{$item->id}}</td>
            <td class="text-left">
                @if(in_array($item->course_id, [39, 40, 30, 41, 44, 45, 46, 47]))
                    <a href="{{url('/khoa-hoc/'.$item->course_url.'/lesson/'.$item->id.'-'.$item->SEOurl)}}" target="_blank">{{$item->name}}</a>
                @else
                    <a href="{{url('/khoa-hoc/'.$item->course_url.'/'.$item->id.'-'.$item->SEOurl)}}" target="_blank">{{$item->name}}</a>
                @endif

                · {{$item->count_view}} views

                @if($item->type == 'checkpoint')
                  &nbsp; &nbsp; <b><a href="{{url('/checkpoint/'.$item->id)}}"> <i class="zmdi zmdi-link"></i> Xem bài kiểm tra đầu vào</a></b>
                @endif
                <!-- icon copy bai hoc hien tai sang khoa hoc khac -->
                &nbsp; &nbsp; <button class="btn btn-xs btn-primary copy-lesson" data-lesson="{{$item->id}}" data-info="{{json_encode($item)}}">
                    <i class="fa fa-copy"></i> Sao chép
                </button>

            </td>
            <td class="text-left">
                <span class="label label-default">{{$item->course}}</span> {{$item->group}}
            </td>
            <td class="text-center">{{$item->author}}</td>
            <td>
                <select class="form-control" name="type" id="lesson-type-{{$item->id}}" onchange="changeLessonType(this, {{$item->id}})">
                    @if (is_null($item->type) || $item->type == 'docs')
                        <option value="docs" selected>Tài liệu</option>
                    @else
                        <option value="docs">Tài liệu</option>
                    @endif
                    @foreach(['video' => 'Video', 'video_test' => 'Video + Test', 'test' => 'Bài tập', 'exam' => 'Bài thi', 'last_exam' => 'Bài thi cuối kỳ','flashcard' => 'Flashcard', 'checkpoint' => 'Bài test đầu vào', 'guide' => 'Hướng dẫn học', 'docs_test' => 'Tài liệu + Bài tập'] as $key => $value)
                        @if ($key !== 'docs')
                            @if ($item->type === $key)
                                <option value="{{$key}}" selected>{{$value}}</option>
                            @else
                                <option value="{{$key}}">{{$value}}</option>
                            @endif
                        @endif
                    @endforeach
                </select>
            </td>
            <td class="text-center">
                {{-- nếu trạng thái lập lịch bằng null --}}
                @if($item->schedule_at == null)
                    @if($item->show == 1)
                        <span class="label label-success">Bật</span>
                    @elseif ($item->show == 2)
                        <span class="label label-info">Testing</span>
                    @else
                        <span class="label label-danger">Tắt</span>
                    @endif
                 @else
                    <span><i class="fa fa-clock-o"></i> Hẹn giờ: {{ friendlyTime($item->schedule_at) }}</span>
                @endif
            </td>
            <td class="text-center">
                @if($item->is_secret == 0)
                    <span class="label label-danger toggleSecret" onclick="toggleSecret({{$item->id}})" id="secret-toggle-btn-{{$item->id}}" data-lesson="{{$item->id}}">Tắt</span>
                @elseif ($item->is_secret == 1)
                    <span class="label label-success toggleSecret" onclick="toggleSecret({{$item->id}})" id="secret-toggle-btn-{{$item->id}}" data-lesson="{{$item->id}}">Bật</span>
                @else
                    <span class="label label-success toggleSecret" onclick="toggleSecret({{$item->id}})" id="secret-toggle-btn-{{$item->id}}" data-lesson="{{$item->id}}">Bật</span>
                @endif
            </td>
            <td class="text-center">
                @if(json_decode(Auth::guard('admin')->user()->matrix)->lesson->edit != null)
                    <button class="edit-modal btn btn-info"
                        onclick="location.href='{{url('backend/lesson/'.$item->id.'/edit')}}'">
                        <span class="glyphicon glyphicon-edit"></span> Sửa
                    </button>
                @endif
                @if(json_decode(Auth::guard('admin')->user()->matrix)->lesson->delete != null)
                    <button class="delete-modal btn btn-danger"
                        data-info="{{json_encode($item)}}">
                        <span class="glyphicon glyphicon-trash"></span> Xóa
                    </button>
                @endif
                <!-- icon hanh dong di chuyen bai hoc hien tai sang khoa hoc khac  -->
                <button class="move-lesson btn btn-warning"
                    data-lesson="{{$item->id}}"
                    data-info="{{json_encode($item)}}">
                    <span class="glyphicon glyphicon-transfer"></span> Di chuyển
                </button>
            </td>
        </tr>
        @endforeach
    </tbody>
</table>
{{ $lesson->render() }}


