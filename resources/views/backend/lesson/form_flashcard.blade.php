<div id="body_flashcard" class="form-lesson-component" data-uri="{{ route('flashcard.add') }}"
    data-uri_edit="{{ route('flashcard.edit') }}">
    <div class="flashcard__field">
        <div id="flashcard__form">
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Tiếng Việt</label>
                <div class="col-sm-8">
                    <textarea name="vi_lang" class="form-control" placeholder="Vietnamese"></textarea>
                    <span class="error_vi_lang text-danger"></span>
                </div>
            </div>
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Tiếng Nhật</label>
                <div class="col-sm-7">
                    <textarea name="jp_lang" class="form-control" placeholder="Japanese"></textarea>

                    <span class="error_jp_lang text-danger"></span>
                </div>
                <div class="col-sm-1">
                    <input type="number" name="jpSz" class="form-control" value="20">
                </div>
            </div>
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Ví dụ</label>
                <div class="col-sm-8">
                    <textarea name="example" class="form-control" placeholder="Example..." id="fcExampleInput"></textarea>
                    <div style="margin-top: 5px;">
                        <span class="btn btn-xs btn-github" id="_udBtn"><i class="fa fa-undo"></i> undo</span>
                        <span class="btn btn-xs btn-youtube" id="_hlBtn"><i class="fa fa-asterisk"></i>
                            highlight</span>
                        <span class="btn btn-xs btn-info" id="_mp3Btn"><i class="fa fa-volume-down"></i> mp3</span>
                    </div>
                    <span class="error_example text-danger"></span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">Ảnh</label>
                <div class="col-sm-4">
                    <input type="file" accept="image/*" name="image_file" class="form-control"
                        onchange="previewCourse(this)">
                    <span class="error_image text-danger"></span>
                </div>

                <div class="col-sm-6">
                    <div class="flc_img_preview">

                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-2 control-label">File Mp3</label>
                <div class="col-sm-4">
                    <input type="file" accept="audio/mp3" name="audio_file" class="form-control">
                    <input type="file" accept="audio/mp3" id="audio_file_flashcard" style="display: none">
                    <span class="error_audio text-danger"></span>
                </div>
                <div class="col-sm-6">
                    <div id="mp3_file"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">Kiểu comment</label>
                <div class="col-sm-6">
                    <ul class="nav nav-pills comment-type">
                        <li role="presentation" data-flashcard_hint="1" class="active"><a href="#">Cách nhớ</a>
                        </li>
                        <li role="presentation" data-flashcard_hint="2"><a href="#">Đặt ví dụ</a></li>
                    </ul>
                    <input type="hidden" name="flashcard_hint" value="0">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">Trạng thái</label>
                <div class="col-sm-8">
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="flashcard_show" value="0">
                        <span class="labels"> Ẩn</span>
                    </label>
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="flashcard_show" value="1" checked>
                        <span class="labels"> Hiện</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="flashcard__preview">
            <div class="card-item" id="card-item">
                <div class="card-inner" id="card-inner">
                    <div class="card__face card__face--jp card__face--front">
                        <div class="card__word">
                            <div class="card__word--text">
                                <span class="word" id="fc_jp_word"></span>
                            </div>
                            <div class="card__word--example">
                                <p>Ví dụ</p>
                                <p class="example" id="fc_jp_ex"></p>
                            </div>
                        </div>
                        <div class="card__voice">
                            <span class="card__voice--button noFlip btn">
                                <i class="fa fa-volume-up noFlip"></i>
                            </span>
                        </div>
                    </div>
                    <div class="card__face card__face--vi card__face--back">
                        <div class="card_meaning">
                            <div id="fc_vi_img"></div>
                            <span v-html="card.value.vi" class="meaning" id="fc_vi_meaning">Hai</span>
                        </div>

                        <div class="card_comment--title">
                            <span id="card_comment--title-text">Cách nhớ</span>
                        </div>

                        <div class="card_comment--empty">
                            <i class="fa fa-comment noFlip a_cursor--pointer fa-lg"></i>
                            Chưa có bình luận
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="progress progress-lg m-b-5">
        <div class="progress-bar progress-bar-success progress-bar-striped active" id="progressBarUpload"
            role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0;">
        </div>
    </div>
</div>
