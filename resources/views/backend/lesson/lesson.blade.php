@extends('backend._default.dashboard')

@section('description') Quản lý bài học @stop
@section('keywords') b<PERSON>i học @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý bài học @stop

@section('assets')
<style type="text/css">
        .row{font-family: Myriad Pro,sans-serif; font-weight: 500;}
        .lessonCourse .btn{padding: 4px 6px 2px 6px; border-radius: 3px; margin: 0 0 0 3px; font-size: 12px;}
        .label {padding: 4px 6px 4px 8px; margin-right: 4px; cursor: pointer; border-radius: 3px; }
        .label-success{background: #10a31a; }
        .label-danger, .btn-danger {background: #e74c3c; }
        .table{background-color: #fff;}
        .text-left{text-align: left; font-family: Myriad Pro,sans-serif;}
        .dropdown-toggle{height: 40px;}
        .filter{width: 400px;}
        .filter > .form-control{float: left; width: 47%; margin-right: 10px;}

</style>
@stop

@section('content')
	<div class="row bg-title">
        <h4 class="page-title pull-left"> Quản lý bài học </h4>
        @if(json_decode(Auth::guard('admin')->user()->matrix)->lesson->add != null)
            <button class="add-modal btn btn-success" style="float: right; padding: 6px; margin: 2px;" onclick="addNewLesson()">
                <span class="glyphicon glyphicon-plus"></span>Thêm mới
            </button>
        @endif
        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <div class="filter pull-left">
                <select type="text" class="form-control mb-2 mr-sm-2" name="course" id="course" onchange="selectGroup()">
                    <option value=""> --Lọc theo khóa-- </option>
                    @foreach($course as $item)
                        <option value="{{$item->id}}">
                            {{$item->name}}
                        </option>
                    @endforeach
                </select>
                <select type="text" class="form-control mb-2 mr-sm-2" name="group" id="group">
                    <option value=""> --Chọn theo khóa học-- </option>
                </select>
                <input type="number" class="form-control mb-2 mr-sm-2" id="setGroup" style="display: none;">
            </div>
            <input type="text" class="form-control" name="lesson_id" placeholder="Mã bài học" style="width: 100px;">
            <a type="button" class="search btn btn-info">Tìm kiếm</a>
            <a href="{{url('backend/lesson')}}"> &nbsp; <i class="fa fa-refresh"></i> &nbsp; &nbsp; &nbsp; </a>
        </form>
    </div>
    <div class="data-lesson">
        @include('backend.lesson.detailLesson')
	</div>
    <div class="result-lesson"></div>
    <div id="lessModal" class="modal fade" role="dialog" tabindex="-1">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Xóa</h4>
                </div>
                <div class="modal-body">
                    <div class="deleteContent">
                        Có muốn xóa bài học "<span class="dname"></span>" này đi không? <span
                            class="hidden did"></span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn actionBtn" data-dismiss="modal">
                            <span id="footer_action_button" class='glyphicon'> </span>
                        </button>
                        <button type="button" class="btn btn-warning" data-dismiss="modal">
                            <span class='glyphicon glyphicon-remove'></span> Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Copy Lesson -->
    <div id="copyLessonModal" class="modal fade" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <h4 class="modal-title">Sao chép bài học</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Left column - Current lesson info -->
                        <div class="col-md-6">
                            <h4>Thông tin bài học hiện tại</h4>
                            <div class="form-group">
                                <label>Tên bài học:</label>
                                <div id="copy-current-name" class="form-control-static"></div>
                            </div>
                            <div class="form-group">
                                <label>Khóa học:</label>
                                <div id="copy-current-course" class="form-control-static"></div>
                            </div>
                            <div class="form-group">
                                <label>Nhóm bài học:</label>
                                <div id="copy-current-group" class="form-control-static"></div>
                            </div>
                        </div>
                        <!-- Right column - New lesson info -->
                        <div class="col-md-6">
                            <h4>Thông tin bài học mới</h4>
                            <form id="copyLessonForm">
                                <input type="hidden" id="copy-lesson-id" name="lesson_id">
                                <div class="form-group">
                                    <label for="copy-new-name">Tên bài học mới:</label>
                                    <input type="text" class="form-control" id="copy-new-name" name="new_name">
                                </div>
                                <div class="form-group">
                                    <label for="copy-new-course">Khóa học mới:</label>
                                    <select class="form-control" id="copy-new-course" name="new_course_id" onchange="selectCourse('copy-new-group', 'copy-new-course')">
                                        <option value="">-- Chọn khóa học --</option>
                                        @foreach($course as $item)
                                            <option value="{{$item->id}}">{{$item->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="copy-new-group">Nhóm bài học mới:</label>
                                    <select class="form-control" id="copy-new-group" name="new_group_id">
                                        <option value="">-- Chọn nhóm bài học --</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="copyLessonSubmit">Hoàn tất</button>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Hủy bỏ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Move Lesson -->
    <div id="moveLessonModal" class="modal fade" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <h4 class="modal-title">Di chuyển bài học</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Thông tin bài học hiện tại</h4>
                            <div class="form-group">
                                <label>Tên bài học:</label>
                                <div id="move-current-name" class="form-control-static"></div>
                            </div>
                            <div class="form-group">
                                <label>Khóa học:</label>
                                <div id="move-current-course" class="form-control-static"></div>
                            </div>
                            <div class="form-group">
                                <label>Nhóm bài học:</label>
                                <div id="move-current-group" class="form-control-static"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4>Thông tin bài học mới</h4>
                            <form id="moveLessonForm">
                                <input type="hidden" id="move-lesson-id" name="lesson_id">
                                <div class="form-group">
                                    <label for="move-new-name">Tên bài học mới:</label>
                                    <input type="text" class="form-control" id="move-new-name" name="new_name">
                                </div>
                                <div class="form-group">
                                    <label for="move-new-course">Khóa học mới:</label>
                                    <select class="form-control" id="move-new-course" name="new_course_id" onchange="selectCourse('move-new-group', 'move-new-course')">
                                        <option value="">-- Chọn khóa học --</option>
                                        @foreach($course as $item)
                                            <option value="{{$item->id}}">{{$item->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="move-new-group">Nhóm bài học mới:</label>
                                    <select class="form-control" id="move-new-group" name="new_group_id">
                                        <option value="">-- Chọn nhóm bài học --</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="moveLessonSubmit">Hoàn tất</button>
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Hủy bỏ</button>
                </div>
            </div>
        </div>
    </div>

	<script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                  'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            selectGroup();
            callBack($('input[name=lesson_id]').val(), $('select[name=course]').val(), parseInt($('#setGroup').val()));
        });

        $(document).on('click', '.data-lesson .pagination a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            pageLesson(page);
        });


        //thêm mới nhanh
        function addNewLesson(){
            $.ajax({
                url: '/backend/lesson/create',
                data: { '_token': $('input[name=_token]').val()}, type: 'post',
                success: function(response){
                    window.location.href = window.location.origin + "/backend/lesson/"+ response +'/edit';
                }
            });
        }

        function pageLesson(page){
            $.ajax({
                url: '/backend/lesson-page?page=' + page
            }).done(function(data){
                $('.result-lesson').empty();
                $('.data-lesson').html(data);
            })
        }

        $(document).on('click', '.delete-modal', function() {
            $('#footer_action_button').text(" Xóa");
            $('.actionBtn').addClass('btn-danger');
            $('.actionBtn').addClass('delete');
            $('#footer_action_button').addClass('glyphicon-trash');
            $('.modal-title').text('Xóa');
            $('.deleteContent').show();
            $('.form-horizontal').hide();
            var data = $(this).data('info');
            $('.did').text(data.id);
            $('.dname').html(data.name);
            $('#lessModal').modal('show');
        });

        $('.modal-footer').on('click', '.delete', function() {
            $.ajax({
                type: 'post',
                url: '/backend/lesson/delete',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'id': $('.did').text()
                },
                success: function(data) {
                    $('.item' + $('.did').text()).remove();
                }
            });
        });

        function toggleSecret(lessonId) {
            var data = {
                id: lessonId
            };
            $.post(window.location.origin + '/backend/lesson/toggle-secret', data, function (res) {
                if (res.code === 200) {
                    $('#secret-toggle-btn-' + lessonId).removeClass('label-danger');
                    $('#secret-toggle-btn-' + lessonId).removeClass('label-success');
                    $('#secret-toggle-btn-' + lessonId).addClass(res.data.is_secret == 1 ? 'label-success' : 'label-danger');
                    $('#secret-toggle-btn-' + lessonId).text(res.data.is_secret == 1 ? 'Bật' : 'Tắt');
                } else {
                    alert(res.msg)
                }
            })
        }

        function changeLessonType(target, lessonId) {
            var data = {
                id: lessonId,
                type: target.value
            };
            $.post(window.location.origin + '/backend/lesson/change-type', data, function (res) {
                if (res.code == 200) {
                    $('#lesson-type-' + lessonId).val(res.data.type);
                }
            })
        }

        //select group by course
        function selectGroup(){
            var course_id = $('#course').val();
            $('#group').empty();
            $.ajax({
                type: 'post',
                url: '/backend/group-by-course',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'course_id' : course_id,
                    'group_id' : $('#setGroup').val()
                },
                success: function(data) {
                    if(data.group.length != 0){
                        var option;
                        $.each(data.group, function(item, value) {
                            if(value.id == parseInt(data.index)){
                                option += '<option value="'+ value.id + '" selected>' + value.name + '</option>';
                            }
                            else{
                                option += '<option value="'+ value.id + '">' + value.name + '</option>';
                            }
                        });
                        $('#group').append(option);
                    }
                }
            });
        }

        $(document).keypress(function(e) {
            if (e.which == 13) {
                $('#setGroup').val($('#group').val());
                var id = $('input[name=lesson_id]').val(),
                    course = $('select[name=course]').val(),
                    group = $('select[name=group]').val();

                callBack(id, course, group);
                e.preventDefault();
            }
        });

        $(document).on('click', '.search', function() {
            $('#setGroup').val($('#group').val());
             var id = $('input[name=lesson_id]').val(),
                course = $('select[name=course]').val(),
                group = $('select[name=group]').val();
            callBack(id, course, group);
        });

        function callBack(id, course, group){
            if( id != '' || ( course != '' &&  group != '')){
                $.ajax({
                    type: 'get',
                    url: '/backend/lesson/find',
                    data: {
                        '_token': $('input[name=_token]').val(),
                        'lesson_id': id,
                        'course_id': course,
                        'group_id': group,
                    },
                    success: function(data) {
                        $('.result-lesson').empty().html(data);
                        $('.data-lesson').empty();
                        // Unbind previous event listeners to prevent multiple bindings
                        $(document).off('click', '.result-lesson .pagination a');
                        $(document).on('click', '.result-lesson .pagination a', function(e) {
                            var id = $('input[name=lesson_id]').val(),
                                course = $('select[name=course]').val(),
                                group = $('select[name=group]').val();
                            e.preventDefault();
                            var page = $(this).attr('href').split('page=')[1];
                            resultPage(page,'/backend/lesson/find/?page=', id, course, group);
                        });
                    }
                });
            }
        }

        function resultPage(page, link, id, course, group){
             $.ajax({
                type: 'get',
                url: link + page,
                cache: false,
                data: {
                    lesson_id : id,
                    course_id : course,
                    group_id : group
                }
            }).done(function(data){
                $('.data-lesson').empty();
                $('.result-lesson').empty().html(data);
            })
        }

        // Event handlers for copy and move lesson functionality
        // These are placed here to avoid multiple bindings when search results are loaded
        $(document).on('click', '.copy-lesson', function() {
            var lessonData = $(this).data('info');
            $('#copy-lesson-id').val(lessonData.id);
            $('#copy-current-name').text(lessonData.name);
            $('#copy-current-course').text(lessonData.course);
            $('#copy-current-group').text(lessonData.group);
            $('#copy-new-name').val(lessonData.name);
            $('#copyLessonModal').modal('show');
        });

        $(document).on('click', '.move-lesson', function() {
            var lessonData = $(this).data('info');
            $('#move-lesson-id').val(lessonData.id);
            $('#move-current-name').text(lessonData.name);
            $('#move-current-course').text(lessonData.course);
            $('#move-current-group').text(lessonData.group);
            $('#move-new-name').val(lessonData.name);
            $('#moveLessonModal').modal('show');
        });

        function selectCourse(selectId, elementCourseId) {
            var courseId = $('#' + elementCourseId).val();
            if (courseId) {
                appendGroupOptions(courseId, selectId);
            }
        }

        function appendGroupOptions(courseId, selectId) {
            var select = $('#' + selectId);
            $.post('/backend/group-by-course', {
                '_token': $('meta[name="csrf-token"]').attr('content'),
                'course_id': courseId
            }, function(data) {
                select.empty();
                select.append('<option value="">-- Chọn nhóm bài học --</option>');
                $.each(data.group, function(index, group) {
                    select.append('<option value="' + group.id + '">' + group.name + '</option>');
                });
            });
        }

        $(document).on('click', '#copyLessonSubmit', function() {
            // Prevent multiple clicks by checking if already processing
            if ($(this).prop('disabled')) {
                return;
            }

            // disable btn
            $('#copyLessonSubmit').prop('disabled', true);

            var lessonId = $('#copy-lesson-id').val();
            var newName = $('#copy-new-name').val();
            var newCourseId = $('#copy-new-course').val();
            var newGroupId = $('#copy-new-group').val();

            if (!newName || !newCourseId || !newGroupId) {
                alert('Vui lòng điền đầy đủ thông tin');
                $('#copyLessonSubmit').prop('disabled', false);
                return;
            }

            $.post('/backend/lesson/copy', {
                '_token': $('meta[name="csrf-token"]').attr('content'),
                'lesson_id': lessonId,
                'new_name': newName,
                'new_course_id': newCourseId,
                'new_group_id': newGroupId
            }, function(response) {
                if (response.code === 200) {
                    alert('Sao chép bài học thành công');
                    location.reload();
                } else {
                    alert('Có lỗi xảy ra: ' + response.message);
                    $('#copyLessonSubmit').prop('disabled', false);
                }
            }).fail(function() {
                alert('Có lỗi xảy ra khi sao chép bài học');
                $('#copyLessonSubmit').prop('disabled', false);
            });
        });

        $(document).on('click', '#moveLessonSubmit', function() {
            // Prevent multiple clicks by checking if already processing
            if ($(this).prop('disabled')) {
                return;
            }

            // disable btn
            $('#moveLessonSubmit').prop('disabled', true);

            var lessonId = $('#move-lesson-id').val();
            var newName = $('#move-new-name').val();
            var newCourseId = $('#move-new-course').val();
            var newGroupId = $('#move-new-group').val();

            if (!newName || !newCourseId || !newGroupId) {
                alert('Vui lòng điền đầy đủ thông tin');
                $('#moveLessonSubmit').prop('disabled', false);
                return;
            }

            $.post('/backend/lesson/move', {
                '_token': $('meta[name="csrf-token"]').attr('content'),
                'lesson_id': lessonId,
                'new_name': newName,
                'new_course_id': newCourseId,
                'new_group_id': newGroupId
            }, function(response) {
                if (response.code === 200) {
                    alert('Di chuyển bài học thành công');
                    location.reload();
                } else {
                    alert('Có lỗi xảy ra: ' + response.message);
                    $('#moveLessonSubmit').prop('disabled', false);
                }
            }).fail(function() {
                alert('Có lỗi xảy ra khi di chuyển bài học');
                $('#moveLessonSubmit').prop('disabled', false);
            });
        });
    </script>

@stop
