<div id="body_conjunction" class="form-lesson-component" data-uri="{{ route('conjunction.add') }}"
     data-uri_edit="{{ route('conjunction.edit') }}">
    <div class="conjunction__field">
        <div id="conjunction__form">
            <input hidden name="type_conjunction" value="{{ \App\Http\Models\LessonToTask::TYPE_FILL_IN_THE_BLANK }}">
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Phân loại câu hỏi</label>
                <div class="col-sm-7">
                    <select id="type_question_conjunction" name="type_question" class="form-control">
                        <option value="1">Từ vựng</option>
                        <option value="2">Chữ hán</option>
                        <option value="3">Ngữ pháp</option>
                        <option value="4">Đ<PERSON>c hiểu</option>
                        <option value="5"><PERSON><PERSON> hiể<PERSON></option>
                    </select>
                </div>
                {{--                <div class="col-sm-1">--}}
                {{--                    <input type="number" name="jpSz" class="form-control" value="20">--}}
                {{--                </div>--}}
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">Trạng thái</label>
                <div class="col-sm-8" style="padding-top: 7px">
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="conjunction_show" value="0">
                        <span class="labels"> Ẩn</span>
                    </label>
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="conjunction_show" value="1" checked>
                        <span class="labels"> Hiện</span>
                    </label>
                </div>
            </div>

{{--            <div class="form-group">--}}
{{--                <label for="" class="col-sm-2 control-label">Tiêu đề câu hỏi</label>--}}
{{--                <div class="col-sm-8">--}}
{{--                                <textarea id="title_question_conjunction" name="title_question" class="form-control"--}}
{{--                                          placeholder="Tiêu đề câu hỏi"></textarea>--}}
{{--                    <span class="error_vi_lang text-danger"></span>--}}
{{--                </div>--}}
{{--            </div>--}}

            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Điểm tối đa của câu hỏi</label>
                <div class="col-sm-8">
                    <input name="conjunction_grade" type="text" class="form-control" id="grade_question_conjunction"
                           placeholder="Nhập điểm tối đa của câu hỏi. VD: 1">
                    <span class="error_vi_lang text-danger"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Text tiếng Việt</label>
                <div class="col-sm-8">
                    <input name="lesson[text_vi]" type="text" class="form-control" id="text_vi_conjunction"
                           placeholder="Nhập Text tiếng Việt. VD: Tiếng Việt">
                    <span class="error_vi_lang text-danger"></span>
                </div>
            </div>

            @if(in_array($type, ['exam', 'last_exam']))
                <div class="form-group">
                    <label class="col-sm-2 control-label" style="text-align: right;">Phần</label>
                    <div class="col-sm-8">
                        <input value="{{ $type == 'exam' ? 1 : '' }}" type="text" name="conjunctionPart" id="conjunctionPart" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" style="text-align: right;">Mondai</label>
                    <div class="col-sm-8">
                        <input type="text" name="conjunctionMondaiName" id="conjunctionMondaiName" />
                    </div>
                </div>
            @endif

            <div style="border-bottom: 1px solid grey; margin: 2px"></div>

            <div class="form-group">
                <label for="" class="control-label">Lưu ý: Tổng điểm được chia đều cho số lượng đáp án</label>
            </div>

            <div class="list_question">
                <div class="question_item" data-number="0">
                    <div class="question_item_header">
                        <div class="question_item_header_number">Câu 1</div>
                    </div>

                    <div class="question_item_content grid grid-cols-2 gap-4 justify-items-stretch">

                    </div>
                    <div class="flex mt-2 mb-2">
                        <div class="question_item_header_add_content btn ml-15 btn btn-info btn-result-add"
                             id="question_item_header_add_content">
                            <i class="fa fa-plus-circle"></i>Thêm thành phần
                        </div>
                        <div class="question_item_header_del_content btn ml-15 btn btn-danger btn-result-del"
                             id="question_item_header_del_content">
                            <i class="fa fa-minus-circle"></i>Bớt thành phần
                        </div>
                    </div>
                    <div class="question_item_file">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Ảnh</label>
                            <div class="col-sm-6">
                                <input type="file" accept="image/*" name="lesson[image_conjunction]" class="form-control"
                                       onchange="previewCourse(this)">
                                <span class="error_image text-danger"></span>
                            </div>

                            <div class="col-sm-6">
                                <div class="flc_img_preview">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">File mp3 bài tập</label>
                            <div class="col-sm-6">
                                <input type="file" accept="audio/*" name="lesson[audio_conjunction]" class="form-control">
                                <input type="file" accept="audio/*" id="audio_file_conjunction" style="display: none">
                                <span class="error_audio text-danger"></span>
                            </div>
                            <div id="audio_preview_conjunction">
                                {{--                                <audio preload="none" controls controlslist="nodownload">--}}
                                {{--                                    <source id="source_audio" src="{{ asset('cdn/audio/1279_1729827756_8634.conjunction') }}"--}}
                                {{--                                            type="audio/conjunction">--}}
                                {{--                                </audio>--}}
                            </div>
                            <div class="col-sm-6">
                                <div id="conjunction_file"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Câu giải thích</label>
                            <div class="col-sm-6">
                                <textarea type="text" class="form-control" name="lesson[explain]"
                                          id="lesson_explain_conjunction"></textarea>
                                {{--                                <input id="lesson_explain_conjunction" type="text" name="lesson[explain]"--}}
                                {{--                                       class="form-control">--}}
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">File mp3 giải thích</label>
                            <div class="col-sm-6">
                                <div class="flex items-center">
                                    <input type="file" accept="audio/*" name="explain_mp3_conjunction" class="form-control" id="explain_mp3_conjunction">
                                    <input type="file" accept="audio/*" id="audio_explain_mp3_conjunction"
                                           style="display: none">
                                    <div class="btn-delete-audio" onclick="delete_audio_explain('explain_mp3_conjunction')">
                                        <i class="fa fa-trash"></i>
                                    </div>
                                </div>

                                <span class="error_audio text-danger"></span>
                            </div>
                            <div id="audio_preview_explain_mp3_conjunction">
                                {{--                                <audio preload="none" controls controlslist="nodownload">--}}
                                {{--                                    <source id="source_audio" src="{{ asset('cdn/audio/1279_1729827756_8634.conjunction') }}"--}}
                                {{--                                            type="audio/conjunction">--}}
                                {{--                                </audio>--}}
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Câu gợi ý</label>
                            <div class="col-sm-6">
                                <textarea type="text" class="form-control" name="lesson[suggest]"
                                          id="lesson_suggest_conjunction"></textarea>
                                {{--                                <input id="lesson_suggest_conjunction" type="text" name="lesson[suggest]"--}}
                                {{--                                       class="form-control">--}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {{--            <div class="question_item_add_question">--}}
            {{--                <div class="btn question_item_add_question_btn" id="question_item_add_question_btn">Thêm câu hỏi</div>--}}
            {{--            </div>--}}

            {{--            <div class="form-group">--}}
            {{--                <label for="" class="col-sm-2 control-label">Câu 1</label>--}}
            {{--                <div class="col-sm-8">--}}
            {{--                    <div class="col-sm-6 box-answer">--}}
            {{--                        <textarea name="example" class="form-control" placeholder="Example..."--}}
            {{--                                  id="fcExampleInput"></textarea>--}}
            {{--                        <div style="margin-top: 5px;">--}}
            {{--                            <span class="btn btn-xs btn-github" id="_udBtn"><i class="fa fa-undo"></i> undo</span>--}}
            {{--                            <span class="btn btn-xs btn-youtube" id="_hlBtn"><i class="fa fa-asterisk"></i>--}}
            {{--                            highlight</span>--}}
            {{--                            <span class="btn btn-xs btn-info" id="_conjunctionBtn"><i class="fa fa-volume-down"></i> conjunction</span>--}}
            {{--                        </div>--}}
            {{--                        <span class="error_example text-danger"></span>--}}
            {{--                    </div>--}}
            {{--                </div>--}}
            {{--            </div>--}}
            {{--            <div class="form-group">--}}
            {{--                <label class="col-sm-2 control-label">Ảnh</label>--}}
            {{--                <div class="col-sm-6">--}}
            {{--                    <input type="file" accept="image/*" name="image_file" class="form-control"--}}
            {{--                           onchange="previewCourse(this)">--}}
            {{--                    <span class="error_image text-danger"></span>--}}
            {{--                </div>--}}

            {{--                <div class="col-sm-6">--}}
            {{--                    <div class="flc_img_preview">--}}

            {{--                    </div>--}}
            {{--                </div>--}}
            {{--            </div>--}}
            {{--            <div class="form-group">--}}
            {{--                <label class="col-sm-2 control-label">File conjunction</label>--}}
            {{--                <div class="col-sm-6">--}}
            {{--                    <input type="file" accept="audio/conjunction" name="audio_file" class="form-control">--}}
            {{--                    <input type="file" accept="audio/conjunction" id="audio_file_conjunction" style="display: none">--}}
            {{--                    <span class="error_audio text-danger"></span>--}}
            {{--                </div>--}}
            {{--                <div class="col-sm-6">--}}
            {{--                    <div id="conjunction_file"></div>--}}
            {{--                </div>--}}
            {{--            </div>--}}
            {{--            <div class="form-group">--}}
            {{--                <label class="col-sm-2 control-label">Kiểu comment</label>--}}
            {{--                <div class="col-sm-6">--}}
            {{--                    <ul class="nav nav-pills comment-type">--}}
            {{--                        <li role="presentation" data-conjunction_hint="1" class="active"><a href="#">Cách nhớ</a>--}}
            {{--                        </li>--}}
            {{--                        <li role="presentation" data-conjunction_hint="2"><a href="#">Đặt ví dụ</a></li>--}}
            {{--                    </ul>--}}
            {{--                    <input type="hidden" name="conjunction_hint" value="0">--}}
            {{--                </div>--}}
            {{--            </div>--}}
        </div>
        <div class="progress progress-lg m-b-5">
            <div class="progress-bar progress-bar-success progress-bar-striped active" id="progressBarUpload"
                 role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0;">
            </div>
        </div>
    </div>
</div>
