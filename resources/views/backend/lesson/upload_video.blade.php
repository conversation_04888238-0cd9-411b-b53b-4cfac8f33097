@extends('backend._default.dashboard')

@section('description') B<PERSON>ng điều khiển @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | Upload video @stop

@section('assets')
    <link rel="stylesheet" href="{{ asset('/plugin/vue-toastr/vue-toastr.min.css') }}">
    <script src="{{ asset('/plugin/vue/vue.min.js') }}"></script>
    <script src="{{ asset('plugin/vue-upload-component.min.js') }}"></script>
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Upload video
        </h4>
    </div>
    <div id="app">
        <table class="table table-hover">
            <thead>
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Size</th>
                <th>Progress</th>
                <th>Upload status</th>
                <th>Render</th>
            </tr>
            </thead>
            <tbody>
            <tr v-if="!files.length">
                <td colspan="7">
                    <div class="text-center p-5">
                        <h4>Kéo thả videos vào đây để tải lên<br/>hoặc</h4>
                        <label :for="name" class="btn btn-lg btn-primary">Chọn videos</label>
                    </div>
                </td>
            </tr>
            <tr v-for="(file, index) in files">
                <td>@{{ index + 1 }}</td>
                <td contenteditable @blur="changeFileName($event, file)">@{{ file.name }}</td>
                <td>@{{ (file.size / (1024 * 1024)).toFixed(2) }} MB</td>
                <td>
                    <div v-if="file.active || file.progress !== '0.00'">
                        <div :class="{'progress-bar': true, 'progress-bar-striped': true, 'bg-danger': file.error, 'progress-bar-animated': file.active}" role="progressbar" :style="{width: file.progress + '%'}">@{{file.progress}}%</div>
                    </div>
                </td>
                <td>@{{ file.success }}</td>
                <td><input type="checkbox" v-model="file.data.isRender"></td>
            </tr>
            </tbody>
        </table>
        <file-upload
                ref="upload"
                v-show="false"
                v-model="files"
                :accept="'video/mp4'"
                name="uploadFile"
                :extensions="'mp4'"
                post-action="/backend/lesson/video/upload"
                :multiple="true"
                :drop="true"
                @input-file="inputFile"
                @input-filter="inputFilter"
                :headers="{'X-CSRF-TOKEN': '{{ csrf_token() }}'}"
        >
            Upload file
        </file-upload>
        <button v-if="$refs.upload" class="btn btn-success" :disabled="$refs.upload.active || files.length === 0" @click.prevent="uploadVideo" type="button">Start upload</button>
        <button v-if="$refs.upload" class="btn btn-warning" :disabled="$refs.upload.active || files.length === 0" @click.prevent="$refs.upload.clear()" type="button">Xoá hết file</button>
    </div>
    <script src="{{ asset('/plugin/vue-toastr/vue-toastr.cjs.min.js') }}"></script>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    files: [],
                    name: 'uploadFile'
                }
            },
            components: {
                FileUpload: VueUploadComponent
            },
            methods: {
                inputFile: (newFile, oldFile) => {
                    if (newFile && oldFile && !newFile.active && oldFile.active) {

                    }
                },

                inputFilter: function (newFile, oldFile, prevent) {
                    if (newFile && !oldFile) {
                        if (!/\.(mp4)$/i.test(newFile.name)) {
                            return prevent()
                        }
                        this.files.forEach(file => {
                            if (file.name === newFile.name) {
                                this.$toastr('warning', {
                                    msg: 'Bạn đã chọn file này rồi!',
                                    timeout: 4000,
                                    position: 'toast-bottom-left'
                                });
                                return prevent();
                            }
                        });
                    }
                },

                changeFileName(e, file) {
                    file.name = e.target.innerText;
                },

                uploadVideo() {
                    var countVideos = this.files.length;
                    for (var i = 0; i < countVideos; i++) {
                        if (!this.validateVideoName(this.files[i].name)) {
                            this.$toastr('warning', {
                                msg: 'Tên video chỉ cho phép kí tự la tinh và không có dấu cách',
                                timeout: 4000,
                                position: 'toast-bottom-left'
                            });
                            return false;
                        }
                    }
                    this.$refs.upload.active = true;
                },

                validateVideoName(name) {
                    if (name.match(/^[-\.\w]+.mp4$/) == null) {
                        return false;
                    }
                    return true;
                },
            }
        });
    </script>
@stop
