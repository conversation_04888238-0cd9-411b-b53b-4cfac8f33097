<div id="body_sentence_jumble" class="form-lesson-component" data-uri="{{ route('sentenceJumble.add') }}"
     data-uri_edit="{{ route('sentenceJumble.edit') }}">
    <div class="sentence_jumble__field">
        <div id="sentence_jumble__form">
            <input hidden name="type_sentence_jumble" value="{{ \App\Http\Models\LessonToTask::TYPE_SENTENCE_JUMBLE }}">
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Phân loại câu hỏi</label>
                <div class="col-sm-7">
                    <select id="type_question_sentence_jumble" name="type_question" class="form-control">
                        <option value="1">Từ vựng</option>
                        <option value="2">Chữ hán</option>
                        <option value="3">Ngữ pháp</option>
                        <option value="4"><PERSON><PERSON><PERSON> hiể<PERSON></option>
                        <option value="5"><PERSON><PERSON> hiểu</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">Trạng thái</label>
                <div class="col-sm-8" style="padding-top: 7px">
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="sentence_jumble_show" value="0">
                        <span class="labels"> Ẩn</span>
                    </label>
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="sentence_jumble_show" value="1" checked>
                        <span class="labels"> Hiện</span>
                    </label>
                </div>
            </div>
            @if(in_array($type, ['exam']))
                <div class="form-group">
                    <label class="col-sm-2 control-label" style="text-align: right;">Phần</label>
                    <div class="col-sm-8">
                        <input value="{{ $type == 'exam' ? 1 : '' }}" type="text" name="sentencePart" id="sentencePart" />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label" style="text-align: right;">Mondai</label>
                    <div class="col-sm-8">
                        <input type="text" name="sentenceMondaiName" id="sentenceMondaiName" />
                    </div>
                </div>
            @endif
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Điểm tối đa của câu hỏi</label>
                <div class="col-sm-8">
                    <input name="sentence_jumble_grade" type="text" class="form-control" id="grade_question_sentence_jumble"
                           placeholder="Nhập điểm tối đa của câu hỏi. VD: 1">
                    <span class="error_vi_lang text-danger"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Nội dung câu hỏi</label>
                <div class="col-sm-8">
                    <textarea type="text" class="form-control" name="lesson[title_question]"
                              id="lesson_question_sentence_jumble"></textarea>
                    <span class="error_vi_lang text-danger"></span>
                </div>
            </div>

            <div style="border-bottom: 1px solid grey; margin: 2px"></div>

            <div class="list_question">
                    <div class="question_item_file">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Ảnh</label>
                            <div class="col-sm-6">
                                <input type="file" accept="image/*" name="lesson[image]" class="form-control"
                                       onchange="previewCourse(this)">
                                <span class="error_image text-danger"></span>
                            </div>

                            <div class="col-sm-6">
                                <div class="flc_img_preview">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">File mp3 bài tập</label>
                            <div class="col-sm-6">
                                <input type="file" accept="audio/sentence_jumble" name="lesson[audio]" class="form-control">
                                <input type="file" accept="audio/sentence_jumble" id="audio_file_sentence_jumble" style="display: none">
                                <span class="error_audio text-danger"></span>
                            </div>
                            <div id="audio_preview_sentence_jumble">
                                {{--                                <audio preload="none" controls controlslist="nodownload">--}}
                                {{--                                    <source id="source_audio" src="{{ asset('cdn/audio/1279_1729827756_8634.sentence_jumble') }}"--}}
                                {{--                                            type="audio/sentence_jumble">--}}
                                {{--                                </audio>--}}
                            </div>
                            <div class="col-sm-6">
                                <div id="sentence_jumble_file"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Câu giải thích</label>
                            <div class="col-sm-6">
                                <textarea type="text" class="form-control" name="lesson[explain]"
                                          id="lesson_explain_sentence_jumble"></textarea>
                                {{--                                <input id="lesson_explain_sentence_jumble" type="text" name="lesson[explain]"--}}
                                {{--                                       class="form-control">--}}
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">File mp3 giải thích</label>
                            <div class="col-sm-6">
                                <div class="flex items-center">
                                    <input type="file" accept="audio/mp3" name="explain_mp3_sentence_jumble" class="form-control" id="explain_mp3_sentence_jumble">
                                    <input type="file" accept="audio/mp3" id="audio_explain_mp3_sentence_jumble"
                                           style="display: none">
                                    <div class="btn-delete-audio" onclick="delete_audio_explain('explain_mp3_sentence_jumble')">
                                        <i class="fa fa-trash"></i>
                                    </div>
                                </div>

                                <span class="error_audio text-danger"></span>

                            </div>
                            <div id="audio_preview_explain_mp3_sentence_jumble">
                                {{--                                <audio preload="none" controls controlslist="nodownload">--}}
                                {{--                                    <source id="source_audio" src="{{ asset('cdn/audio/1279_1729827756_8634.sentence_jumble') }}"--}}
                                {{--                                            type="audio/sentence_jumble">--}}
                                {{--                                </audio>--}}
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Câu gợi ý</label>
                            <div class="col-sm-6">
                                <textarea type="text" class="form-control" name="lesson[suggest]"
                                          id="lesson_suggest_sentence_jumble"></textarea>
                                {{--                                <input id="lesson_suggest_sentence_jumble" type="text" name="lesson[suggest]"--}}
                                {{--                                       class="form-control">--}}
                            </div>
                        </div>
                    </div>
                </div>


            <div class="question_item" data-number="0">
                <div class="question_item_header">
                    <div class="font-bold text-sm">Lưu ý: App sẽ tự động xáo trộn thứ tự của các thành phần trong câu</div>
                </div>
                <div class="sentence_jumble_item_content flex flex-wrap">
{{--                    <div class="item_component_sentence_jumble flex items-center" data-number="0">--}}
{{--                        <div class="form-control reloadCKeditor min-w-[100px]" id="lesson[value][0]" contenteditable="true" name="lesson[value][0]" placeholder="Thành phần 1"></div>--}}
{{--                    </div>--}}
{{--                    <div class="item_component_sentence_jumble flex items-center" data-number="1">--}}
{{--                        <p class="mx-2"> / </p>--}}
{{--                        <div class="form-control reloadCKeditor min-w-[100px]" id="lesson[value][1]" contenteditable="true" name="lesson[value][1]" placeholder="Thành phần 2"></div>--}}
{{--                    </div>--}}
                </div>

                <div class="flex flex-wrap mt-2 mb-2">
                    <div class="btn_sentence_jumble_item_add_content btn ml-15 btn btn-info btn-result-add"
                         id="question_item_header_add_content">
                        <i class="fa fa-plus-circle"></i>Thêm thành phần
                    </div>
                    <div class="btn_sentence_jumble_item_del_content btn ml-15 btn btn-danger btn-result-del"
                         id="question_item_header_del_content">
                        <i class="fa fa-minus-circle"></i>Bớt thành phần
                    </div>
                </div>

                <div style="border-bottom: 1px solid grey; margin: 2px"></div>

                <div>
                    <div>
                        Thành phần sai
                    </div>
                    <div class="sentence_jumble_item_content_false flex flex-wrap">

                    </div>

                    <div class="flex flex-wrap mt-2 mb-2">
                        <div class="btn_sentence_jumble_item_add_content_false btn ml-15 btn btn-info btn-result-add"
                             id="question_item_header_add_content_false">
                            <i class="fa fa-plus-circle"></i>Thêm thành phần sai
                        </div>
                        <div class="btn_sentence_jumble_item_del_content_false btn ml-15 btn btn-danger btn-result-del"
                             id="question_item_header_del_content_false">
                            <i class="fa fa-minus-circle"></i>Bớt thành phần sai
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="progress progress-lg m-b-5">
            <div class="progress-bar progress-bar-success progress-bar-striped active" id="progressBarUpload"
                 role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0;">
            </div>
        </div>
    </div>
</div>
