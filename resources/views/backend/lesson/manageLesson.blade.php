@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON><PERSON> l<PERSON> b<PERSON><PERSON> h<PERSON> @stop
@section('keywords') b<PERSON><PERSON> học @stop
@section('author') dungmori.com @stop
@section('title') Admin | Chi tiết bài học @stop

@section('assets')
    <script type="text/javascript" src="{{ url('/js/ckfinder/ckfinder.js') }}"></script>
    <script>
        CKFinder.config( { connectorPath: '/backend/ckfinder/connector' } );
    </script>
    <style>
        .task_content span span {
            font-size: 14px !important;
        }

        .loading-container {
            padding-left: 40%;
            padding-top: 20%;
        }

        .video-loader {
            border: 16px solid #f3f3f3;
            /* Light grey */
            border-top: 16px solid #3498db;
            /* Blue */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .panel-tab>.btn {
            font-weight: bold;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .disabled-video {
            pointer-events: none;
        }

        .list_question {
            /*margin: 15px;*/
            /*border-bottom: 1px solid gray;*/
            /*padding: 15px;*/
        }

        .question_item_header_number {
            font-weight: bold;
        }

        .question_item_add_question {
            display: flex;
            justify-content: center;
        }

        .question_item {
            margin: 15px;
            border-bottom: 1px solid gray;
            padding: 15px;
        }

        .question_item_header_add_content {
            /*margin-left: 15px;*/
        }

        .question_item_header {
            display: flex;
        }

        .question_content_item {
            display: flex;
            margin-right: 10px;
            align-items: center;
        }

        .question_content_item_input {
            width: 70%;
        }

        .question_content_item_select {
            width: 30%;
        }
    </style>
    <script type="text/javascript" src="{{ asset('/plugin/ckeditor4/ckeditor.js') }}"></script>
    <script type="text/javascript" src="{{ asset('/plugin/ckeditor4/adapters/jquery.js') }}"></script>
    <script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.11.1/jquery.validate.min.js"></script>

    {{--    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.js"></script> --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/jquery-ui.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-toast-plugin/1.3.2/jquery.toast.min.css" rel="stylesheet">
    <script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.11.1/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/4.4.0/bootbox.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-toast-plugin/1.3.2/jquery.toast.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
@stop

@section('foot-js')
    <script>
        var lesson = @json($lesson)
    </script>
    <script src="{{ asset('/plugin/jquery/axios.min.js') }}"></script>
    <script type="text/javascript"
        src="{{ url('assets/backend/js/upload_progress.js') }}?{{ filemtime('assets/backend/js/upload_progress.js') }}">
    </script>
    <script type="text/javascript"
        src="{{ asset('assets/backend/js/lesson-component.js') }}?v={{ filemtime('assets/backend/js/lesson-component.js') }}">
    </script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    <script type="text/javascript">
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        var tmp = new Vue({

            el: '#preview-exercise',
            data: function() {
                return {
                    tab: 1,
                    questions: [], //so luong client ket noi den
                    mp3: null, //lấy ra bài thi hiện tại
                    tt1: 0,
                    tt2: 0,
                    tt3: 0, //biến check tổng điểm 3 phần
                }
            },
            methods: {

                //lấy ra text từ html
                stripTags: function(text) {

                    if (text == null) return "";
                    return text.replace(/(<([^>]+)>)/ig, "");
                },

                loadPreview: function(val) {
                    vm = this;
                    $.post(window.location.origin + "/backend/lesson/preview-exercise", {
                        id: '{{ $lesson->id }}}'
                    }, function(res) {


                        console.log('res', res);

                        var lq1 = [];
                        var ttScore1 = 0;
                        var lq2 = [];
                        var ttScore2 = 0;
                        var lq3 = [];
                        var ttScore3 = 0;

                        if (['exam', 'last_exam'].includes(lesson?.type)) {
                            const uniqueExamPartIds = [...new Set(res.questions.map(item => item
                                .exam_part_id))];
                            console.log('uniqueExamPartIds', uniqueExamPartIds);
                            for (var i = 0; i < res.questions.length; i++) {
                                if (res.questions[i].exam_part_id == uniqueExamPartIds[0]) {
                                    lq1.push(res.questions[i]);
                                    ttScore1 += parseInt(res.questions[i].grade);
                                }
                                if (res.questions[i].exam_part_id == uniqueExamPartIds[1]) {
                                    lq2.push(res.questions[i]);
                                    ttScore2 += parseInt(res.questions[i].grade);
                                }
                                if (res.questions[i].exam_part_id == uniqueExamPartIds[2]) {
                                    lq3.push(res.questions[i]);
                                    ttScore3 += parseInt(res.questions[i].grade);
                                }

                                res.questions[i].value += "(" + res.questions[i].grade + "đ)";
                            }
                        } else {
                            for (var i = 0; i < res.questions.length; i++) {
                                if (res.questions[i].type_ld == 1) {
                                    lq1.push(res.questions[i]);
                                    ttScore1 += parseInt(res.questions[i].grade);
                                }
                                if (res.questions[i].type_ld == 2) {
                                    lq2.push(res.questions[i]);
                                    ttScore2 += parseInt(res.questions[i].grade);
                                }
                                if (res.questions[i].type_ld == 3) {
                                    lq3.push(res.questions[i]);
                                    ttScore3 += parseInt(res.questions[i].grade);
                                }

                                res.questions[i].value += "(" + res.questions[i].grade + "đ)";
                            }
                        }

                        if (val == 1) vm.questions = _.orderBy(lq1, ['sort'], ['asc']);
                        if (val == 2) vm.questions = _.orderBy(lq2, ['sort'], ['asc']);
                        if (val == 3) vm.questions = _.orderBy(lq3, ['sort'], ['asc']);

                        vm.tt1 = ttScore1;
                        vm.tt2 = ttScore2;
                        vm.tt3 = ttScore3;
                        console.log('questions', vm.questions);

                        if (vm.mp3 == null && res.mp3) {
                            vm.mp3 = "https://mp3-v2.dungmori.com/" + JSON.parse(res.mp3).link;
                            document.getElementById("audioSource").setAttribute('src', vm.mp3);
                        }

                    });
                },

                //load theo tab
                loadTab: function(val) {

                    vm = this;
                    if (vm.tab != val) {
                        vm.tab = val;
                        this.loadPreview(val);
                    }
                },
            },
            mounted: function() {
                this.loadPreview(1);
            }
        });
    </script>
@endsection
@section('content')
    <div class="row bg-title" style="width: 1000px; margin: 0 auto !important; border-bottom: none;">
        <h4 class="page-title pull-left">Sửa bài học: <a
                @if (in_array($lesson->course_id, [39, 40, 30, 41])) href="{{ url('/khoa-hoc/' . $lesson->course_url . '/lesson/' . $lesson->id . '-' . $lesson->SEOurl) }}"
                    @else
                        href="{{ url('/khoa-hoc/' . $lesson->course_url . '/' . $lesson->id . '-' . $lesson->SEOurl) }}" @endif
                target="_blank">{{ $lesson->name }}</a></h4>
        <div class="pull-right flex justify-between gap-3">
            <input type="file" id="fileImportInput" style="display: none;" />
            <a
                href="{{ url('assets/excel/trac-nghiem.xlsx') }}"
                style="box-shadow: -2px 2px 5px 0px rgba(0,0,0,0.75); border: 3px solid #fff; padding: 10px 10px; color: #fff; background: #4285F4; font-weight: bold; cursor: pointer"
            >Tải mẫu nhập</a>
            <button
                onclick="document.getElementById('fileImportInput').click()"
                style="box-shadow: -2px 2px 5px 0px rgba(0,0,0,0.75); border: 3px solid #fff; padding: 10px 10px; color: #fff; background: #4285F4; font-weight: bold; cursor: pointer"
            >Nhập trắc nghiệm</button>

            <a
                style="box-shadow: -2px 2px 5px 0px rgba(0,0,0,0.75); border: 3px solid #fff; padding: 10px 10px; color: #fff; background: #9f041b; font-weight: bold; cursor: pointer"
                href="{{ url('/backend/new-lesson/' . $lesson->id . '/edit') }}"
            >
                Giao diện mới</a>
        </div>

    </div>

    <div style="width: 1000px; margin: 0 auto;">
        <div class="navbar-inner">
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation"><a href="#overview" role="tab" data-toggle="tab">Thông tin</a></li>
                <li class="active" role="presentation"><a href="#content" id="content_tab" role="tab"
                        data-toggle="tab">Nội dung</a></li>
                <li><a href="#preview-exercise" role="tab" data-toggle="tab">Check bài test tổng hợp</a></li>
            </ul>
            <a href="{{ url('backend/lesson') }}" class="btn btn-danger" style="float: right; margin-top: -35px;">Trở
                về</a>
            <a class="btn btn-success render-vn" onclick="renderServer('vn')"
                style="float: right; margin: -33px 180px 0 0; padding: 2px 8px; border-radius: 4px;">
                <img src="{{ url('') }}/assets/img/vn.gif"> render vn
            </a>
            <a class="btn btn-success render-jp" onclick="renderServer('jp')"
                style="float: right; margin: -33px 80px 0 0; padding: 2px 8px; border-radius: 4px;">
                <img src="{{ url('') }}/assets/img/jp.gif"> render jp
            </a>
        </div>
        <div class="tab-content">
            <Style>
                .panel-tab .btn {
                    background: #fff;
                }
            </Style>
            @if (in_array($lesson->type, ['exam', 'last_exam']))
                <div id="exam-form" class="tab-pane" role="tabpanel">
                    <div>

                    </div>
                </div>
                <script type="application/javascript">
                    new Vue({
                        el: '#exam-form',
                        data() {
                            return {
                                questions: [],
                                question: {
                                    value: '',
                                    answers: [
                                        { value: '', grade: 0 },
                                        { value: '', grade: 0 },
                                        { value: '', grade: 0 },
                                        { value: '', grade: 0 },
                                    ]
                                }
                            }
                        },
                        mounted() {
                            console.log('mounted');
                        }
                    })
                </script>
            @endif

            <div id="preview-exercise" class="tab-pane" role="tabpanel" style="background: #fff; padding: 25px 15px;">
                <table class="table m-0" style="border: none; padding: 20px 0; margin-top: 36px !important;">
                    <div class="panel-tab">
                        <div class="btn" v-bind:style="(tab == 1) ? 'border-bottom:2px solid #777;':''"
                            v-on:click="loadTab('1')">Từ vựng ngữ pháp (@{{ tt1 }} đ)</div>
                        <div class="btn" v-bind:style="(tab == 2) ? 'border-bottom:2px solid #777;':''"
                            v-on:click="loadTab('2')">Đọc hiểu (@{{ tt2 }} đ)</div>
                        <div class="btn" v-bind:style="(tab == 3) ? 'border-bottom:2px solid #777;':''"
                            v-on:click="loadTab('3')">Nghe hiểu (@{{ tt3 }} đ)</div>
                        <audio controls="controls" id="audioSource"
                            style="float: right; height: 30px; margin-bottom: 15px; border: 1px solid #00c292;">
                            <source type="audio/mpeg">
                        </audio>
                    </div>
                    <tr class="question-item" v-for="(question, index) in questions">
                        <td style="border: none;">
                            <div v-html="question.value" style="font-weight: bold; font-size: 16px; width: 90%;"></div>
                            <div class="col-sm-12">
                                <template v-for="answer, index in question.answers">
                                    <div class="col-sm-6" style="line-height: 2.3"
                                        :class="{
                                            'del text-bold': _.filter(question.answers, function(item) {
                                                return _.replace(
                                                    item.value, /[0-9](\\.)/, '') === _.replace(answer
                                                    .value, /[0-9](\\.)/, '')
                                            }).length > 1
                                        }">
                                        <i v-if="answer.grade == 0" class="fa fa-square-o" aria-hidden="true"></i>
                                        <i v-if="answer.grade > 0" class="fa fa-check-square" aria-hidden="true"></i>
                                        <span>@{{ stripTags(answer.value) }}</span>
                                        <b v-if="answer.grade > 0">(@{{ answer.grade }}đ)</b>
                                    </div>
                                </template>
                            </div>
                        </td>
                        <td class="text-center"></td>
                    </tr>
                </table>
            </div>

            <!--Tab thông tin-->
            <div id="overview" class="tab-pane" role="tabpanel">
                <form class="form-horizontal" action="{{ url('backend/lesson/update') }}" method="POST"
                    enctype="multipart/form-data" id="lesson_info">
                    {{ csrf_field() }}
                    <div class="form-group" style="display: none;">
                        <label class="control-label col-sm-2" for="id">Mã số</label>
                        <div class="col-sm-3">
                            <input type="text" class="form-control" name="id"
                                value="{{ is_null($lesson) ? '' : $lesson->id }}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="lesson">Bài học</label>
                        <div class="col-sm-3">
                            <input type="text" class="form-control" name="lesson"
                                value="{{ is_null($lesson) ? '' : $lesson->name }}" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="name_html">Bài học (Furigana)</label>
                        <div class="col-sm-9">
                            {{-- <input type="text" class="form-control" name="name_html"
                                value="{{ is_null($lesson) ? '' : $lesson->name_html }}" required> --}}
                            <textarea id="name_html" name="name_html" class="form-control w-[200px]"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="name">Ảnh bài học</label>
                        <div class="col-sm-10">
                            <div class="upload_complete" style="display: block;">
                                <div class="file_image_single">
                                    @if (is_null($lesson) || $lesson->avatar_name == null || $lesson->avatar_name == '')
                                        <img id ="image_lesson" src="{{ url('assets/img/icon_backend') }}/no_image.png"
                                            style="width: 100px; max-height: 100px;">
                                    @else
                                        <img id ="image_lesson"
                                            src="{{ url('cdn/lesson/default') }}/{{ $lesson->avatar_name }}"
                                            style="width: 100px; max-height: 100px;">
                                    @endif
                                </div>
                            </div>
                            <div class="upload_action" style="margin-top:2px;">
                                <input type="file" name="img_lesson" id="img_lesson" onchange="previewLesson()"
                                    style="width: 80px; display: inline;">
                                <span class="f11 formNote" style="color: #bd6f6a; font-size: 9px;"> Maximum: <b>3MB</b>
                                    (jpg,jpeg,png) </span>
                            </div>
                        </div>
                    </div>
                    @if (!is_null($lesson))
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="course">Khóa học</label>
                            <div class="col-sm-3">
                                <select class="form-control" name="course" id="course"
                                    onchange="selectGroupByCourse()">
                                    <option value=""> --Chọn khóa học--</option>
                                    @foreach ($course as $item)
                                        @if ($item->id == $lesson->course_id)
                                            <option value="{{ $item->id }}" selected>
                                                {{ $item->name }}
                                            </option>
                                        @else
                                            <option value="{{ $item->id }}">
                                                {{ $item->name }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="course">Loại bài học</label>
                            <div class="col-sm-3">
                                <select class="form-control" name="type" id="type">
                                    @if (is_null($lesson->type) || $lesson->type == 'docs')
                                        <option value="docs" selected>Tài liệu</option>
                                    @else
                                        <option value="docs">Tài liệu</option>
                                    @endif
                                    @foreach (['video' => 'Video', 'video_test' => 'Video + Test', 'test' => 'Bài tập', 'exam' => 'Bài thi', 'last_exam' => 'Bài thi cuối kỳ', 'flashcard' => 'Flashcard', 'checkpoint' => 'Bài test đầu vào', 'guide' => 'Hướng dẫn học', 'docs_test' => 'Tài liệu + Bài tập'] as $key => $value)
                                        @if ($key !== 'docs')
                                            @if ($lesson->type === $key)
                                                <option value="{{ $key }}" selected>{{ $value }}</option>
                                            @else
                                                <option value="{{ $key }}">{{ $value }}</option>
                                            @endif
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                            @if ($lesson->type == 'checkpoint')
                                <b><a href="{{ url('/checkpoint/' . $lesson->id) }}"> <i class="zmdi zmdi-link"></i> Xem
                                        bài kiểm tra đầu vào</a></b>
                            @endif
                        </div>
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="group">Nhóm bài học</label>
                            <div class="col-sm-3">
                                <select name="group" class="form-control" id="group">
                                    <option> --Được chọn theo khóa học--</option>
                                    @foreach ($group as $item)
                                        @if ($item->id == $lesson->group_id)
                                            <option value="{{ $item->id }}" selected>
                                                {{ $item->name }}
                                            </option>
                                        @else
                                            <option value="{{ $item->id }}">
                                                {{ $item->name }}
                                            </option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    @if (is_null($lesson))
                        <div class="form-group" style="display: -webkit-box;">
                            <table id="multi-Course" class="table table-bordered"
                                style="width: 70%; margin-left: 210px;">
                                <thead>
                                    <tr>
                                        <th class="text-center">Khóa</th>
                                        <th class="text-center">Nhóm</th>
                                        <th class="text-center">Hành động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <button type="button" class="btn btn-success" id="addKey" style="height: 51px;">Thêm
                                khóa</button>
                            <input type="number" name="sumCourse" id="sumCourse" style="display: none;" />
                        </div>
                    @endif
                    {{-- xu lí cho them nhieu bai hoc --}}
                    <script type="text/javascript">
                        var index = 1,
                            removeId = 0;

                        //hien thi dialog chon khoa hoc
                        $(document).on('click', '#addKey', function() {
                            removeId = 0;
                            $('#myModal').modal('show');
                        });
                        //them dữ liệu sau khi chọn khóa học
                        $(document).on('click', '#multiCourseSubmit', function() {

                            $('.course' + removeId).remove();

                            var actions = '<button class="edit-course btn btn-info" data-info="' + $('#course').val() + '-' + $(
                                    '#group').val() + '-' + index + '" type="button">' +
                                '<span class="glyphicon glyphicon-edit"></span>' +
                                '</button>' +
                                '<button class="delete-course btn btn-danger" data-info="' + index + '" type="button">' +
                                '<span class="glyphicon glyphicon-trash"></span>' +
                                '</button>';

                            $('#multi-Course').append('<tr class="course' + index + '"><td>' +
                                $('#course :selected').text() +
                                '<input type="text"  name="course' + index + '" value="' + $('#course').val() +
                                '" style="display:none;"/>' +
                                '</td><td>' + $('#group :selected').text() +
                                '<input type="text"  name="group' + index + '" value="' + $('#group').val() +
                                '" style="display:none;"/>' +
                                '</td><td class="text-center">' + actions + '</td></tr>');
                            $('#sumCourse').val(index);
                            index++;
                        });

                        //Thao tac sửa xóa khóa học
                        $(document).on('click', '.edit-course', function() {
                            var ids = $(this).data('info').split('-');
                            $('#course').val(ids[0]);
                            selectGroupByCourse(ids[1]);
                            removeId = ids[2];
                            $('#myModal').modal('show');
                        });

                        //Xoa khoa hoc
                        $(document).on('click', '.delete-course', function() {
                            var ids = $(this).data('info');
                            $('.course' + ids).remove();
                        });

                        //hàm render server
                        function renderServer(location) {

                            $('.render-' + location).css('opacity', '0.1');

                            $.ajax({
                                type: 'post',
                                url: '/backend/lesson/render',
                                data: {
                                    '_token': $('input[name=_token]').val(),
                                    'location': location
                                },
                                success: function(response) {
                                    console.log(response);
                                }
                            });

                        }
                    </script>

                    <div class="form-group">
                        <label class="control-label col-sm-2" for="price_option">Tính phí</label>
                        <div class="col-sm-3">
                            <select name="price" class="form-control" id="price">
                                @if (!is_null($lesson))
                                    @if ($lesson->price_option == 0)
                                        <option value="0" selected>Miễn phí</option>
                                        <option value="2">Tính phí</option>
                                    @else
                                        <option value="0">Miễn phí</option>
                                        <option value="2" selected>Tính phí</option>
                                    @endif
                                @else
                                    <option value="0">Miễn phí</option>
                                    <option value="2">Tính phí</option>
                                @endif
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-sm-2" for="feature">Nổi bật</label>
                        <div class="col-sm-3">
                            <select name="feature" class="form-control" id="feature">
                                @if (!is_null($lesson))
                                    @if ($lesson->feature == 1)
                                        <option value="1" selected>Nổi bật</option>
                                        <option value="0">Ẩn</option>
                                    @else
                                        <option value="1">Nổi bật</option>
                                        <option value="0" selected>Ẩn</option>
                                    @endif
                                @else
                                    <option value="1">Nổi bật</option>
                                    <option value="0">Ẩn</option>
                                @endif
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2" for="teacher">Giáo viên</label>
                        <div class="col-sm-3">
                            <select class="form-control" name="teacher" id="teacher">
                                @if (is_null($lesson))
                                    @foreach ($author as $item)
                                        <option value="{{ $item->id }}">
                                            {{ $item->name }}
                                        </option>
                                    @endforeach
                                @else
                                    @foreach ($author as $item)
                                        @if ($item->id == $lesson->author_id)
                                            <option value="{{ $item->id }}" selected>
                                                {{ $item->name }}
                                            </option>
                                        @else
                                            <option value="{{ $item->id }}">
                                                {{ $item->name }}
                                            </option>
                                        @endif
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>

                    <script type="text/javascript">
                        function hideScheduleTime() {
                            console.log("Tắt lập lịch");
                            $(".schedule-post").css('display', 'none');
                        }

                        function showScheduleTime() {
                            console.log("Bật lập lịch");
                            $(".schedule-post").css('display', 'block');
                        }
                    </script>

                    <div class="form-group">
                        <div class="form-group">
                            <label class="control-label col-sm-2" for="status">Bài học bắt buộc</label>
                            <div class="col-sm-2 pt-1.5">
                                <input type="checkbox" id="require" name="require"
                                    @if (is_null($lesson) || $lesson->require) checked @endif>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-sm-2 !pt-0">Thời gian hoàn thành dự kiến (phút)</label>
                            <div class="col-sm-2">
                                <input type="number" class="form-control w-20" id="expect_time" name="expect_time"
                                    value="{{ is_null($lesson) ? 0 : $lesson->expect_time }}" maxlength="3">
                            </div>
                        </div>
                        <label class="control-label col-sm-2" for="status">Trạng thái</label>
                        <div class="col-sm-10">
                            @if (is_null($lesson))
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0"
                                        checked onclick="showScheduleTime()">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1"
                                        onclick="hideScheduleTime()">
                                    <span class="labels">Bật</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_testing" name="status"
                                        value="2" onclick="hideScheduleTime()">
                                    <span class="labels">Testing</span>
                                </label>
                            @elseif($lesson->show == 1)
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0"
                                        onclick="showScheduleTime()">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1"
                                        checked onclick="hideScheduleTime()">
                                    <span class="labels">Bật</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_testing" name="status"
                                        value="2" onclick="hideScheduleTime()">
                                    <span class="labels">Testing</span>
                                </label>
                                {{-- kiểm tra xem trạng thái public tắt hay bật để ẩn hiện trạng thái lập lịch --}}
                                <script type="text/javascript">
                                    $(document).ready(function() {
                                        hideScheduleTime();
                                    });
                                </script>
                            @elseif($lesson->show == 2)
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0"
                                        onclick="showScheduleTime()">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1"
                                        onclick="hideScheduleTime()">
                                    <span class="labels">Bật</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_testing" name="status"
                                        value="2" checked onclick="hideScheduleTime()">
                                    <span class="labels">Testing</span>
                                </label>
                                {{-- kiểm tra xem trạng thái public tắt hay bật để ẩn hiện trạng thái lập lịch --}}
                                <script type="text/javascript">
                                    $(document).ready(function() {
                                        hideScheduleTime();
                                    });
                                </script>
                            @else
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_off" name="status" value="0"
                                        checked onclick="showScheduleTime()">
                                    <span class="labels">Tắt</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_on" name="status" value="1"
                                        onclick="hideScheduleTime()">
                                    <span class="labels">Bật</span>
                                </label>
                                <label class="tcb-inline">
                                    <input type="radio" class="tc" id="status_testing" name="status"
                                        value="2" onclick="hideScheduleTime()">
                                    <span class="labels">Testing</span>
                                </label>
                            @endif
                            <p class="schedule-post">Lập lịch đăng bài lúc
                                <input type="time" name="schedule_time"
                                    @if (isset($lesson) && $lesson->schedule_at != null) value="{{ substr($lesson->schedule_at, 11, 5) }}" @endif />
                                <input type="date" name="schedule_date" min="{{ $today->format('Y-m-d') }}"
                                    @if (isset($lesson) && $lesson->schedule_at != null) value="{{ substr($lesson->schedule_at, 0, 10) }}" @endif />
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                            <button type="submit" class="btn btn-success">Lưu lại</button>
                        </div>
                    </div>
                </form>
                {{-- dialog cho phep chon nhieu khoa hoc --}}
                <div id="myModal" class="modal fade" role="dialog">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">×</span></button>
                                <h4 class="modal-title">Nội dung</h4>
                            </div>
                            <form class="form-horizontal" id="form_course" role="form">
                                <div class="modal-body">
                                    <div class="form-group">
                                        <label class="control-label col-sm-2" for="course">Khóa học</label>
                                        <div class="col-sm-10">
                                            <select class="form-control" name="course" id="course"
                                                onchange="selectGroupByCourse()">
                                                <option value=""> --Chọn khóa học--</option>
                                                @foreach ($course as $item)
                                                    <option value="{{ $item->id }}">{{ $item->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label col-sm-2" for="group">Nhóm</label>
                                        <div class="col-sm-10">
                                            <select name="group" class="form-control" id="group">
                                                <option> --Được chọn theo khóa học--</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <div class="text-center">
                                        <button type="button" class="btn btn-primary" id="multiCourseSubmit"
                                            data-dismiss="modal">Thực hiện</button>
                                        <button type="button" class="btn btn-danger" data-dismiss="modal">Hủy
                                            bỏ</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!--Tab nội dung-->
            <div id="content" class="tab-pane active" role="tabpanel">
                <form class="form-horizontal" action="{{ url('backend/lesson/update/content') }}" method="POST"
                    enctype="multipart/form-data">
                    {{ csrf_field() }}
                    <input type="hidden" name="lesson_id" id="lesson_id"
                        value="{{ is_null($lesson) ? '' : $lesson->id }}">
                    <input type="hidden" name="course_id" id="course_id"
                        value="{{ is_null($lesson) ? '' : $lesson->course_id }}">
                    <input type="hidden" name="group_id" id="group_id"
                        value="{{ is_null($lesson) ? '' : $lesson->group_id }}">
                    <div class="form-group">
                        <label class="control-label col-sm-2"></label>
                        <div class="col-sm-2">
                            @if (is_null($lesson))
                                <input type="checkbox" id="examination" name="is_examination">
                            @elseif($lesson->is_examination == 1)
                                <input type="checkbox" id="examination" name="is_examination" checked="checked">
                            @else
                                <input type="checkbox" id="examination" name="is_examination">
                            @endif
                            <label for="examination">Là bài kiểm tra</label>
                        </div>
                    </div>

                    <div class="form-group refer_total_marks" style="display: none;">
                        <label class="control-label col-sm-2">Điểm tổng</label>
                        <div class="col-sm-1">
                            <input type="number" class="form-control" id="total_marks" name="total_marks"
                                value="{{ is_null($lesson) ? 0 : $lesson->total_marks }}" maxlength="5"
                                style="width: 70px;">
                        </div>
                        <label class="control-label col-sm-1">Điểm đạt✓</label>
                        <div class="col-sm-1">
                            <input type="number" class="form-control" id="total_marks" name="pass_marks"
                                value="{{ is_null($lesson) ? 0 : $lesson->pass_marks }}" maxlength="5"
                                style="width: 70px;">
                        </div>
                        <label class="control-label col-sm-2">Thời gian làm (phút)</label>
                        <div class="col-sm-1">
                            <input type="number" class="form-control" id="max_duration" name="max_duration"
                                value="{{ is_null($lesson) ? 0 : $lesson->max_duration }}" maxlength="3"
                                style="width: 80px;">
                        </div>
                    </div>
                    @if (in_array($lesson->type, ['exam', 'last_exam']))
                        <input type="hidden" name="part_id" value="{{ $partId }}" />
                        @if (count($parts))
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><a
                                        href="/backend/lesson/{{ $lesson->id }}/edit?part={{ count($parts) ? $parts[0]->id : '' }}">Phần
                                        1</a></label>
                                <div class="col-sm-3 flex items-center gap-5">
                                    <input value="{{ $parts[0]->point }}" id="partPoint1" name="part_point_1"
                                        type="number" placeholder="Điểm" class="form-control w-[200px]" /> điểm
                                    <input value="{{ $parts[0]->duration }}" id="partDuration1" name="part_duration_1"
                                        type="number" placeholder="Thời gian làm bài" class="form-control w-[200px]" />
                                    phút
                                </div>
                            </div>
                        @endif
                    @endif
                    @if ($lesson->type == 'last_exam')
                        @if (count($parts) > 1)
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><a
                                        href="/backend/lesson/{{ $lesson->id }}/edit?part={{ count($parts) > 1 ? $parts[1]->id : '' }}">
                                        Phần 2</a></label>
                                <div class="col-sm-3 flex items-center gap-5">
                                    <input value="{{ $parts[1]->point }}" id="partPoint2" type="number"
                                        name="part_point_2" placeholder="Điểm" class="form-control w-[200px]" /> điểm
                                    <input value="{{ $parts[1]->duration }}" id="partDuration2" type="number"
                                        name="part_duration_2" placeholder="Thời gian làm bài"
                                        class="form-control w-[200px]" /> phút
                                </div>
                            </div>
                        @endif
                        @if (count($parts) > 2)
                            <div class="form-group">
                                <label class="col-sm-2 control-label"><a
                                        href="/backend/lesson/{{ $lesson->id }}/edit?part={{ count($parts) > 2 ? $parts[2]->id : '' }}">
                                        Phần 3</a></label>
                                <div class="col-sm-3 flex items-center gap-5">
                                    <input value="{{ $parts[2]->point }}" id="partPoint3" type="number"
                                        name="part_point_3" placeholder="Điểm" class="form-control w-[200px]" /> điểm
                                    <input value="{{ $parts[2]->duration }}" id="partDuration3" type="number"
                                        name="part_duration_3" placeholder="Thời gian làm bài"
                                        class="form-control w-[200px]" /> phút
                                </div>
                            </div>
                        @endif
                    @endif
                    <div class="form-group">
                        <label class="col-sm-2 control-label"> Tác vụ </label>
                        <div class="col-sm-3">
                            <select id="task-type" class="form-control"
                                value="{{ $lesson_task->count() ? $lesson_task[$lesson_task->count() - 1]->type : 1 }}">
                                <option value="2">Mp4/Youtube</option>
                                <option value="7">Kaiwa</option>
                                <option value="8">Tài liệu PDF</option>
                                <option value="5">Mp3</option>
                                <option value="1">Nội dung</option>
                                <option value="3">Trắc nghiệm</option>
                                <option value="6">Trả lời câu hỏi</option>
                                <option value="4">Kết quả</option>
                                <option value="9">Flash Card</option>
                                <option value="13">Điền vào chỗ trống</option>
                                <option value="14">Sắp xếp</option>
                                <option value="15">Nối từ</option>
                                <option value="16">Luyện nói</option>
                                <option value="17">Flash Card mới</option>
                                {{--                                <option value="10">Quiz trắc nghiệm</option> --}}
                                {{--                                <option value="11">Sắp xếp câu</option> --}}
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <a id="task-add" class="btn btn-info" onclick="resetData(this)" data-task="#task-type">
                                <i class="fa fa-plus-circle"></i>Thêm tác vụ</a>
                        </div>
                    </div>

                    {{-- bang hien thi cac tac vu da them --}}
                    <div id="task_area">
                        @include('backend.lesson.detailTask')
                    </div>
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                            <button type="submit" class="btn btn-success">Thực hiện</button>
                            <a href="{{ url('backend/lesson') }}" class="btn btn-danger">Trở về</a>
                        </div>
                    </div>
                </form>

                {{-- hien thi cac dialog theo tac vu --}}
                <div id="pageModal" class="modal fade" role="dialog">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">×</span></button>
                                <h4 class="modal-title">Nội dung</h4>
                            </div>
                            <form class="form-horizontal" id="form_task" role="form">
                                <input type="hidden" name="lesson_id" id="lesson_id"
                                    value="{{ is_null($lesson) ? '' : $lesson->id }}">
                                <input type="hidden" name="lesson_component_id" id="lesson_component_id"
                                    value="">
                                <select type="hidden" name="del_explain_mp3" id="del_explain_mp3">
                                    <option value="0">Không</option>
                                    <option value="1">Có</option>
                                </select>
                                <li class="global_error text-left hidden"></li>
                                <div class="modal-body">
                                    @include('backend.lesson.form_pdf')
                                    @include('backend.lesson.form_video')
                                    @include('backend.lesson.form_audio')
                                    @include('backend.lesson.form_mp3', [
                                        'type' => $lesson->type,
                                        'mondais' => $mondais,
                                        '$partId' => $partId,
                                    ])
                                    @include('backend.lesson.form_task_content', [
                                        'type' => $lesson->type,
                                        'mondais' => $mondais,
                                        '$partId' => $partId,
                                    ])
                                    @include('backend.lesson.form_multi_choice', [
                                        'type' => $lesson->type,
                                        'mondais' => $mondais,
                                        '$partId' => $partId,
                                    ])
                                    @include('backend.lesson.form_answer')
                                    @include('backend.lesson.form_result')
                                    @include('backend.lesson.form_flashcard')
                                    @include('backend.lesson.form_conjunction', [
                                        'type' => $lesson->type,
                                        'mondais' => $mondais,
                                        '$partId' => $partId,
                                    ])
                                    @include('backend.lesson.form_sentence_jumble', [
                                        'type' => $lesson->type,
                                        'mondais' => $mondais,
                                        '$partId' => $partId,
                                    ])
                                    @include('backend.lesson.form_word_pair_matching', [
                                        'type' => $lesson->type,
                                        'mondais' => $mondais,
                                        '$partId' => $partId,
                                    ])
                                    @include('backend.lesson.form_speaking', [
                                        'type' => $lesson->type,
                                        'mondais' => $mondais,
                                        '$partId' => $partId,
                                    ])
                                </div>
                                <div class="modal-footer " id="new-footer">
                                    <div class=" text-center">
                                        <button type="button" class="btn btn-primary actionBtn" id="task_submit"
                                            data-loading-text="Thực hiện <i class='fa fa-spinner fa-spin'></i>"
                                            data-task="#task-type" onclick="saveData(this)">Thực hiện</button>
                                        <button type="button" class="btn btn-danger" data-dismiss="modal">Hủy
                                            bỏ</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div id="lessModal" class="modal fade" role="dialog">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                <h4 class="modal-title">Xóa</h4>
                            </div>
                            <div class="modal-body">
                                <div id="deleteContent">
                                    Có muốn xóa tác vụ này đi không? <span class="hidden task_id"></span>
                                </div>
                                <div class="modal-footer" id="delete-footer">
                                    <button type="button" class="btn btn-danger deleteBtn" data-dismiss="modal">
                                        <span id="footer_action_button" class='glyphicon glyphicon-trash'>Xóa</span>
                                    </button>
                                    <button type="button" class="btn btn-warning" data-dismiss="modal">
                                        <span class='glyphicon glyphicon-remove'></span> Đóng
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        var fileBrowserUrl = '{{ asset('/plugin/ckfinder/ckfinder.html') }}';
        var noImageUrl = "{{ url('assets/img/icon_backend') }}/no_image.png";

        var lesson_task = {!! json_encode($lesson_task) !!};

        function highlightCell() {
            var taskId = window.location.hash.substr(1)
            $('#' + taskId).css('background-color', '#ffd57e')
        }
        highlightCell();
    </script>
@section('footer')
    <script src="{{ asset('assets/backend/js/manager-flashcard.js') }}"></script>
    {{--    <script src="https://cdn.ckeditor.com/4.16.2/standard/ckeditor.js"></script> --}}
    <script>
        const fileInput = document.getElementById('fileImportInput');

        fileInput.addEventListener('change', async () => {
            const file = fileInput.files[0];
            if (!file) {
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('_token', '{{ csrf_token() }}');
            formData.append('lesson_id', '{{ $lesson->id }}');
            try {
                
                // Replace with your API endpoint
                const response = await fetch('/backend/lesson/task/choice/import', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('API response:', result);
            } catch (error) {
                console.error('Error:', error);
            }
        });

        function delete_audio_explain(elementId = null) {
            $("#audio_file_explain").val("")
            $("#audio_preview").empty()
            $("#del_explain_mp3").val(1);
            if (elementId) {
                $(`#${elementId}`).val("");
                $(`#audio_preview_${elementId}`).empty();
            }
            console.log('vao day');
        }

        // tao su kien neu them hoac thay doi file audio_file_explain thi doi val del_explain_mp3 = 0
        $("#audio_file_explain").change(function() {
            if ($(this).val() != "") {
                $("#del_explain_mp3").val(0);
            }
        })

        $("#explain_mp3_conjunction").change(function() {
            if ($(this).val() != "") {
                $("#del_explain_mp3").val(0);
            }
        })

        $("#explain_mp3_sentence_jumble").change(function() {
            if ($(this).val() != "") {
                $("#del_explain_mp3").val(0);
            }
        })

        $("#explain_mp3_word_pair_matching").change(function() {
            if ($(this).val() != "") {
                $("#del_explain_mp3").val(0);
            }
        })
    </script>
@endsection()


@stop
