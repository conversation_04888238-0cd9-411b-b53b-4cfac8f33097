<div id="body_video" class="row form-lesson-component" style="width: 600px !important;">
    <div class="col-sm-6">
        <input type="hidden" id="video_id">
        <div class="form-group">
            <label class="control-label">Tên video</label>
            <div class="flex">
                <input id="link_video" name="link_video" class="form-control" placeholder="ví dụ: n4-bai15-tuvung.mp4">
                <button type="button" class="btn btn-success" id="btn-choose-video">Chọn video</button>
            </div>
        </div>
        <div class="form-group">
            <div>
                <input type="text" id="title_video" name="title_video" class="form-control"
                    placeholder="Tiêu đề video">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label">Trạng thái</label>
            <div>
                <label class="tcb-inline">
                    <input type="radio" class="tc" name="video_show" id="video_show_off" value="0">
                    <span class="labels"> Ẩn</span>
                </label>
                <label class="tcb-inline">
                    <input type="radio" class="tc" name="video_show" id="video_show_on" value="1">
                    <span class="labels"> Hiện</span>
                </label>
            </div>
        </div>
        <div class="form-group">
            <label>Video 1080(Full HD)</label>
            <input type="checkbox" name="video_full" class="video_full" id="video_full" v-model="videoFull">
        </div>
    </div>
</div>

<script>
    var originalData = {
        filters: {
            name: '',
            createdAt: ''
        },
        videoFull: 0,
    };

    var vueInstance = new Vue({
        el: '#body_video',

        data: Object.assign({}, originalData),

        watch: {},

        methods: {

        },
        mounted() {}
    });
</script>
