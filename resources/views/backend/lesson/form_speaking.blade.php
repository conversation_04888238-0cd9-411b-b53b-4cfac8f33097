<div id="body_speaking" class="form-lesson-component" data-uri="{{ route('speaking.add') }}"
     data-uri_edit="{{ route('speaking.edit') }}">
    <div class="speaking__field">
        <div id="speaking__form">
            <input hidden name="type_speaking" value="{{ \App\Http\Models\LessonToTask::TYPE_SPEAKING }}">
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Phân loại câu hỏi</label>
                <div class="col-sm-7">
                    <select id="type_question_speaking" name="type_question" class="form-control">
                        <option value="1">Từ vựng</option>
                        <option value="2"><PERSON><PERSON> hán</option>
                        <option value="3">Ngữ pháp</option>
                        <option value="4">Đ<PERSON><PERSON> hiểu</option>
                        <option value="5"><PERSON><PERSON> hiểu</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">Trạng thái</label>
                <div class="col-sm-8" style="padding-top: 7px">
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="speaking_show" value="0">
                        <span class="labels"> Ẩn</span>
                    </label>
                    <label class="tcb-inline">
                        <input type="radio" class="tc" name="speaking_show" value="1" checked>
                        <span class="labels"> Hiện</span>
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Điểm tối đa của câu hỏi</label>
                <div class="col-sm-8">
                    <input name="speaking_grade" type="text" class="form-control" id="grade_question_speaking"
                           placeholder="Nhập điểm tối đa của câu hỏi. VD: 1">
                    <span class="error_vi_lang text-danger"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="" class="col-sm-2 control-label">Nội dung câu hỏi</label>
                <div class="col-sm-8">
                    <textarea type="text" class="form-control" name="lesson[title_question]"
                              id="lesson_question_speaking"></textarea>
                    <span class="error_vi_lang text-danger"></span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">Ảnh</label>
                <div class="col-sm-4">
                    <input type="file" accept="image/*" name="lesson[image_speaking]" class="form-control" id="input_image_speaking"
                           onchange="previewCourse(this)">
                    <span class="error_image text-danger"></span>
                </div>

                <div class="col-sm-6">
                    <div class="flc_img_preview">
                    </div>
                </div>
            </div>

            <div style="border-bottom: 1px solid grey; margin: 2px"></div>

            <div class="list_question">
                <div class="question_item_file">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">File mp3 mẫu tốc độ chuẩn</label>
                        <div class="col-sm-4">
                            <input type="file" accept="audio/mp3" name="lesson[audio_speaking_speed_default]" id="input_audio_speaking_speed_default" class="form-control">
                            <span class="error_audio text-danger"></span>
                        </div>
                        <div id="audio_preview_speaking_speed_default">
                        </div>
                        <div class="col-sm-6">
                            <div id="speaking_file"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">File mp3 mẫu tốc độ chậm</label>
                        <div class="col-sm-4">
                            <input type="file" accept="audio/mp3" name="lesson[audio_speaking_speed_slow]" id="input_audio_speaking_speed_slow" class="form-control">
                            <span class="error_audio text-danger"></span>
                        </div>
                        <div id="audio_preview_speaking_speed_slow">
                            {{--                                <audio preload="none" controls controlslist="nodownload">--}}
                                {{--                                    <source id="source_audio" src="{{ asset('cdn/audio/1279_1729827756_8634.speaking') }}"--}}
                                                                                {{--                                            type="audio/speaking">--}}
                                {{--                                </audio>--}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
