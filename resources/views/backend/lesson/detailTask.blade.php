 <Style>
     .table>tbody>tr>td {
         padding: 8px 8px;
     }
 </Style>
 <table id="task_table" class="table table-bordered">
     <thead>
         <tr>
             <th class="text-center" width="2%">#</th>
             <th class="text-center" width="2%">ID</th>
             @if (in_array($lesson->type, ['exam', 'last_exam']))
                 <th class="text-center" width="2%">Phần</th>
                 <th class="text-center" width="2%">Mondai</th>
                 <th class="text-center" width="2%">K<PERSON> năng</th>
             @endif
             <th class="text-center" width="9%"><PERSON><PERSON><PERSON></th>
             <th class="text-center">Nội dung</th>
             <th class="text-center" style="width: 100px;"><PERSON><PERSON><PERSON><PERSON> (<span class="text-bold">{{ $gradeByTask }}</span>)
             </th>
             <th class="text-center" width="9%">Trạng thái</th>
             <th class="text-center">Action</th>
         </tr>
     </thead>
     <tbody class="body_task">
         @foreach ($lesson_task as $item)
             <tr class="item{{ $item->id }}" id="item{{ $item->id }}" style="max-width: 80vw !important;">
                 <td class="text-center">{{ $item->sort }}</td>
                 <td class="text-center">{{ $item->id }}</td>
                 @if (in_array($lesson->type, ['exam', 'last_exam']))
                     <th class="text-center" width="2%">{{ $item->part ? $item->part->type : '' }}</th>
                     <th class="text-center" width="2%">{{ $item->mondai ? $item->mondai->title : '' }}</th>
                     <th class="text-center">
                         {{ $item->skill ? ['Từ vựng', 'Chữ Hán', 'Ngữ pháp', 'Đọc hiểu', 'Nghe hiểu'][$item->skill - 1] : '' }}
                     </th>
                 @endif
                 <td class="text-center" style="font-size: 11px; font-weight: bold; vertical-align: middle;">
                     @switch($item->type)
                         @case(1)
                             Nội dung
                         @break

                         @case(2)
                             Video
                         @break

                         @case(3)
                             Trắc nghiệm
                         @break

                         @case(4)
                             Kết quả
                         @break

                         @case(5)
                             Mp3
                         @break

                         @case(7)
                             Kaiwa
                         @break

                         @case(8)
                             PDF
                         @break

                         @case(9)
                             Flash Card
                         @break

                         @case(10)
                             Quiz trắc nghiệm
                         @break

                         @case(11)
                             Sắp xếp câu
                         @break

                         @case(13)
                             Điền vào chỗ trống
                         @break

                         @case(14)
                             Sắp xếp câu
                         @break

                         @case(15)
                             Nối cặp từ
                         @break

                         @case(\App\Http\Models\LessonToTask::TYPE_SPEAKING)
                             Luyện nói
                         @break

                         @case(17)
                             Flash Card mới
                         @break

                         @default
                             Clip chữa đề
                     @endswitch
                 </td>
                 <td class="task_content text-left" style="max-width: 400px">
                     <span
                         style="overflow: hidden;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
                         @if ($item->type == 1)
                             <b>{{ strip_tags($item->value) }}</b>
                         @elseif($item->type == 3)
                             {{ strip_tags(html_entity_decode($item->value)) }}
                         @elseif($item->type == 4)
                             --------Kết quả-------
                         @elseif($item->type == 5)
                             https://mp3-v2.dungmori.com/{{ json_decode($item->value)->link }}
                         @elseif($item->type == 7)
                             {{-- chạy thử nghiệm mp3 --}}
                             <script type="text/javascript">
                                 var playMp3 = null;

                                 function listenMp3(audio) {

                                     if (playMp3 == null) playMp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
                                     else {
                                         playMp3.pause();
                                         playMp3.currentTime = 0;
                                         playMp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
                                     }
                                     playMp3.play();
                                 }

                                 //tắt mp3 khi đóng popup đáp án
                                 function turnOffMp3() {
                                     playMp3.pause();
                                     playMp3.currentTime = 0;
                                 }
                             </script>

                             {{ json_decode($item->value)->link }}

                             <a style="cursor: pointer; color: #f22b2b;"
                                 onclick="listenMp3('{{ json_decode($item->value)->link }} ')">
                                 &nbsp;<i class="glyphicon glyphicon-volume-up"></i>
                             </a>

                             {{ json_decode($item->value)->name }}
                         @elseif($item->type == 8)
                             {{ $item->value }}
                         @elseif($item->type == 6)
                             {!! json_decode($item->value)[0]->question !!}
                         @elseif($item->type == 9)
                             <p>Tiếng Nhật: <span style="font-weight: bold">{{ json_decode($item->value)->jp }}</span>
                             </p>
                             <p>Tiếng Việt: <span style="font-weight: bold">{{ json_decode($item->value)->vi }}</span>
                             </p>
                         @elseif($item->type == 13)
                             <p>{{ json_decode($item->value)->title_question }}</p>
                         @elseif($item->type == 14)
                             <p>{!! json_decode($item->value)->title_question !!}</p>
                         @elseif($item->type == 15)
                             <p>{{ json_decode($item->value)->title_question }}</p>
                         @elseif($item->type == \App\Http\Models\LessonToTask::TYPE_SPEAKING)
                             <p>{!! json_decode($item->value)->title_question !!}</p>
                         @elseif($item->type == 17)
                             <p>{!! json_decode($item->value)->word !!}</p>
                         @else
                             {{ strip_tags($item->video_name) }}
                         @endif
                     </span>
                 </td>
                 <td class="text-center font-bold" width="50">
                     @if ($item->grade == 0)
                         <span></span>
                     @elseif ($item->match_grade)
                         <span class="text-success">{{ $item->grade }}</span>
                     @else
                         <span class="text-danger">{{ $item->grade }}</span>
                     @endif

                 </td>
                 <td class="text-center status-{{ $item->id }}">
                     @if ($item->show == 1)
                         <span class="label label-success">Bật</span>
                     @else
                         <span class="label label-danger">Tắt</span>
                     @endif
                 </td>
                 <td id="task-action" class="text-center" style="width:1px;white-space:nowrap;">
                     @if (Auth::guard('admin')->user()->id == 69)
                         <button title="Clone sang bài khác" class="change-lesson btn btn-success text-dark"
                             type="button" style="padding: 0px 5px; font-size: 12px; border-radius: 3px;"
                             onclick="cloneToLesson({{ $item->id }})">
                             <span class="glyphicon glyphicon-transfer"></span>
                         </button>
                         <button title="Thay đổi bài học" class="change-lesson btn btn-warning text-dark" type="button"
                             style="padding: 0 5px; font-size: 12px; border-radius: 3px;"
                             onclick="changeLesson({{ $item->id }})">
                             <span class="glyphicon glyphicon-random"></span>
                         </button>
                     @endif

                     @if ($item->type == 17)
                         <a target="_blank" href="{{ route('backend.flashcard.edit', ['flashcard_id' => $item->id]) }}"
                             class="btn btn-info" style="padding: 0 5px; font-size: 12px; border-radius: 3px;">
                             <span class="glyphicon glyphicon-edit"></span>
                         </a>
                     @else
                         <button id="edit-task-button" class="edit-task btn btn-info" data-info="{{ $item->id }}"
                             data-type="#task-type" type="button"
                             style="padding: 0 5px; font-size: 12px; border-radius: 3px;" onclick="editData(this)"
                             data-uri="{{ route('flashcard.get') }}">
                             <span class="glyphicon glyphicon-edit"></span>
                         </button>
                     @endif
                     <button class="delete-task btn btn-danger" data-info="{{ $item->id }}" type="button"
                         style="padding: 0 5px; font-size: 12px; border-radius: 3px;">
                         <span class="glyphicon glyphicon-trash"></span>
                     </button>
                 </td>
             </tr>
         @endforeach
     </tbody>
 </table>
