@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON><PERSON> lý video tương tác @stop
@section('keywords') video @stop
@section('author') dungmori.com @stop
@section('title') Admin | <PERSON>u<PERSON><PERSON> lý video tương tác @stop

@section('assets')
    <link  href="{{asset('assets/backend/css/video-questions.css')}}?{{filemtime('assets/backend/css/video-questions.css')}}" rel="stylesheet" type="text/css">
    <link  href="{{asset('plugin/vue-draggable-resizable/dist/VueDraggableResizable.css')}}?{{filemtime('plugin/vue-draggable-resizable/dist/VueDraggableResizable.css')}}" rel="stylesheet" type="text/css">
@stop

@section('content')
    <div id="video__questions-screen" v-bind:class="{fullscreen: fullscreen}">
        <div class="video-screen-title text-white">
            <span class="text-bold text-white h4"><a :href="url + '/backend/new-lesson/' +  lessonId + '/edit'"><i class="fa fa-angle-left"></i></a>   Quản lý video tương tác</span>
            <span class="text-white ml-5 a-cursor-pointer" v-if="!fullscreen" @click="fullscreen = 1"><i class="fa fa-expand"></i></span>
            <span class="text-white ml-5 a-cursor-pointer" v-if="fullscreen" @click="fullscreen = 0"><i class="fa fa-compress"></i></span>
        </div>
        <div class="video__questions--content">
            <div class="video__questions--editor" >
                <div class="video__questions--editor-templates">
                    <div class="video__questions--editor-template">
                        <span class="text-center text-bold">Nửa màn hình</span>
                        <div class="flex justify-center items-center" style="height: 100%">
                            <span @click="addLayer(1)" class="btn"><i class="fa fa-plus fa-lg"></i></span>
                        </div>
                    </div>
                    <div class="video__questions--editor-template">
                        <span class="text-center text-bold">Full màn hình</span>
                        <div class="flex justify-center items-center" style="height: 100%">
                            <span @click="addLayer(2)" class="btn"><i class="fa fa-plus fa-lg"></i></span>
                        </div>
                    </div>
                </div>
                <div class="video__questions--editor-form" v-for="layer in layers" v-if="selectedLayer.uid && layer.uid === selectedLayer.uid">
                    <div class="video__questions--editor-form-timing">
                        <div class="mb-1">
                            <label class="video__questions--editor-form-label">Thời gian bắt đầu</label>
                            <input class="video__questions--editor-form-input" type=number v-model="layer.time_start" @change="layer.saved = false" @keyup="layer.time_end = parseFloat(layer.time_start) + parseFloat(layer.length); currentTime = layer.time_start; player.currentTime(layer.time_start)" placeholder="VD: 00:16:11.197">
                        </div>
                        <div class="mb-1">
                            <label class="video__questions--editor-form-label">Thời gian kết thúc</label>
                            <input class="video__questions--editor-form-input" type=number v-model="layer.time_end" @change="layer.saved = false" @keyup="layer.length = parseFloat(layer.time_end) - parseFloat(layer.time_start); currentTime = layer.time_end; player.currentTime(layer.time_end)" placeholder="VD: 00:26:11.197">
                        </div>
                        <div class="mb-1">
                            <label class="video__questions--editor-form-label">Thời lượng popup</label>
                            <input class="video__questions--editor-form-input" type=number v-model="layer.length" @change="layer.saved = false" @keyup="layer.time_end = parseFloat(layer.time_start) + parseFloat(layer.length); currentTime = layer.time_end; player.currentTime(layer.length)" placeholder="VD: 10">
                        </div>
{{--                        <div class="mb-1">--}}
{{--                            <label class="video__questions--editor-form-label">Bắt đầu đếm ngược</label>--}}
{{--                            <input class="video__questions--editor-form-input" type=number v-model="layer.q_time_start" @keyup="currentTime = layer.q_time_start; player.currentTime(layer.q_time_start)" @change="layer.saved = false" placeholder="VD: 2">--}}
{{--                        </div>--}}
                        <div class="mb-1">
                            <label class="video__questions--editor-form-label">Thời gian hiện câu trả lời</label>
                            <input class="video__questions--editor-form-input" type=number v-model="layer.a_length" @change="layer.saved = false" placeholder="VD: 2">
                        </div>
                        <div class="mb-1">
                            <label class="video__questions--editor-form-label">Loại</label>
                            <select class="video__questions--editor-form-input" v-model="layer.type" @change="layer.saved = false">
                                <option value="1" title="Chân ngắn thông minh mà. Chân ngắn đáng yêu mà">Nửa màn hình</option>
                                <option value="2" title="Trường túc bất chi lao">Full màn hình</option>
                            </select>
                        </div>
                    </div>
                    <div class="video__questions--editor-form-content">
{{--                        <ckeditor v-model="value" :config="editorConfig" class="mb-5"></ckeditor>--}}
                        <div>
                            <label class="video__questions--editor-form-label">Nội dung</label>
                            <ckeditor v-model="layer.content" :config="editorConfig" type="inline" @input="layer.saved = false" class="video__questions--editor-form-content-inline-ckeditor"></ckeditor>
                        </div>
                        <div class="video__questions--editor-form-content-answers mt-5">
                            <div v-for="(answer, index) in layer.answers" class="flex justify-start items-center">
                                <input class="video__questions--editor-form-input" v-model="answer.value" @change="layer.saved = false" :placeholder="'Câu trả lời ' + (index + 1)" >
                                <input type="radio" name="answerGrade" :checked="parseInt(answer.grade)" @change="selectAnswerLayer($event, answer, layer)" class="ml-3"/>
                            </div>
                        </div>
                        <div class="mt-3 flex justify-between">
                            <div>
                                <span class="btn btn-sm btn-danger text-bold" @click="saveDeleteLayer(layer)">Xoá</span>
                                <span class="ml-3 btn btn-sm btn-info text-bold" v-if="!layer.id" @click="saveAddLayer(layer)">Thêm</span>
                                <span class="ml-3 btn btn-sm btn-info text-bold" v-if="layer.id" @click="saveUpdateLayer(layer)">Lưu</span>
                            </div>
                            <span class="text-right text-white text-bold">@{{ layer.id ? '#' + layer.id : '' }}</span>
                        </div>
                    </div>


                </div>
            </div>
            <div class="video__questions--preview">
                <video id="myplayer_{{$randomId}}" class="myplayer video-js vjs-default-skin" controls></video>
            </div>
        </div>
        <div class="video__questions--timeline">
            <div class="video__questions--timeline-utilities">
                <div class="video__questions--timeline-tools">
                    <span class="btn btn-sm text-white text-bold" style="background: #f6ab6c"> Đang thao tác</span>
                    <span class="btn btn-sm text-white text-bold" style="background: #1dd3bd"> Đã lưu</span>
                    <span class="btn btn-sm text-white text-bold" style="background: #be5683"> Chưa lưu</span>
                </div>
                <div class="video__questions--timeline-statistics">
                    <span
                            :style="{color: (player && player.paused()) ? '#f0f0f0' : 'red'}"
                            class="video__questions--timeline-current mr-3"
                    >@{{ currentTime | timeInSeconds}}</span>
                    <span
                            :style="{color: (player && player.paused()) ? '#f0f0f0' : 'red'}"
                            class="video__questions--timeline-current"
                    >@{{ currentTime  | timeline }}</span>
                </div>
            </div>
            <div class="video__questions--timeline-layers" @keyup="console.log(event)">
                <div class="video__questions--timeline-ruler" style="display: grid" :style="{gridTemplateColumns: 'repeat(' + ruler.length + ', ' + (timelineWidth - (videoDuration%15)/videoTimelineRatio)/(ruler.length - 1) + 'px)'}">
                    <div v-for="point in ruler" style="display: flex; flex-flow: column; justify-content: space-between;">
                        <span style="font-size: 10px"><span v-if="point % 60 === 0">@{{point | timeruler}}</span></span>
                        <div style="width: 100%; height: 7px; border-left: 1px solid #b6b6b6; border-bottom: 1px solid #b6b6b6" :style="{height: point % 60 === 0 ? '14px' : '10px'}"></div>
                    </div>
                    <span style="display: inline-block; position: absolute; z-index: 999; left: 0; top: 0" :style="{left: currentTime/videoTimelineRatio +'px'}">
                        <span style="position: absolute; left: -5px;top: 20px"><i class="fa fa-caret-down fa-lg"></i></span>
                        <span style="width: 0; position: absolute; left: 0; top: 30px; border-left: 1px solid #FFF" :style="{height: (layers.length * 30 + 10) + 'px'}"></span>
                    </span>
                </div>
                <div style="height: 30px; background: #092532; border-top: 1px solid #423144; border-top: 1px solid #423144" :style="{width: videoDuration ? (videoDuration / videoTimelineRatio) + 'px' : '100%'}" v-for="layer in layers" id="videoLayer">
                    <vue-draggable-resizable
                            :w="layer.length / videoTimelineRatio"
                            :h="layer.height"
{{--                            :grid="[0.79,0.79]"--}}
                            :x="layer.time_start / videoTimelineRatio"
                            :grid="[1,1]"
                            :min-width="5"
                            @activated="selectLayer(layer)"
                            @dragging="onDrag"
                            @resizing="onResize"
                            :style="{background: layer.uid === selectedLayer.uid ? '#f6ab6c' : (layer.saved ? '#1dd3bd' : '#be5683') }"
                            :parent="true"
                            :handles="[layer.time_start == 0 && 'ml','mr']"
                            axis="x"
                            :z-index="1"
                            class="flex justify-center items-center a-cursor-pointer"
                    >
                    </vue-draggable-resizable>
                </div>
            </div>
        </div>
    </div>

@section('foot-js')
    <script>
        var taskId = {!! json_encode($taskId) !!}
        var lessonId = {!! json_encode($lessonId) !!}
        var playerId = '{{$randomId}}';
    </script>
    <script src="{{asset('assets/js/lib-hls.js')}}?{{filemtime('assets/js/lib-hls.js')}}"></script>
    <link  href="{{asset('plugin/videojs_hls/videojs.min.css')}}?{{filemtime('plugin/videojs_hls/videojs.min.css')}}" rel="stylesheet" type="text/css">
    <script src="{{asset('plugin/videojs-hotkeys/videojs.hotkeys.js')}}?{{filemtime('plugin/videojs-hotkeys/videojs.hotkeys.js')}}"></script>
    <script src="{{asset('assets/js/pie-timer.js')}}?{{filemtime('assets/js/pie-timer.js')}}"></script>
    <script src="{{asset('assets/js/video-modal.js')}}?{{filemtime('assets/js/video-modal.js')}}"></script>
    <link  href="{{asset('plugin/videojs-markers/dist/videojs.markers.css')}}?{{filemtime('plugin/videojs-markers/dist/videojs.markers.css')}}" rel="stylesheet" type="text/css">
    <script src="{{asset('plugin/videojs-markers/dist/videojs-markers.js')}}?{{filemtime('plugin/videojs-markers/dist/videojs-markers.js')}}"></script>
    <!-- CDNJS :: Sortable (https://cdnjs.com/) -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.8.4/Sortable.min.js"></script>
    <!-- CDNJS :: Vue.Draggable (https://cdnjs.com/) -->

    <script src="{{asset('plugin/ckeditor4/ckeditor.js')}}?{{filemtime('plugin/ckeditor4/ckeditor.js')}}"></script>
    <script src="{{asset('plugin/ckeditor4-vue/dist/ckeditor.js')}}?{{filemtime('plugin/ckeditor4-vue/dist/ckeditor.js')}}}"></script>
    <script src="{{asset('plugin/vue-draggable-resizable/dist/VueDraggableResizable.umd.js')}}?{{filemtime('plugin/vue-draggable-resizable/dist/VueDraggableResizable.umd.js')}}"></script>
    <script src="{{asset('assets/backend/js/lesson/video-questions.js')}}?{{filemtime('assets/backend/js/lesson/video-questions.js')}}"></script>
@endsection

@section('footer')

@endsection()

@stop
