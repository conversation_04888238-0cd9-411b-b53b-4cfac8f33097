@extends('backend._default.dashboard')

@section('description') Quản lý bài học @stop
@section('keywords') b<PERSON><PERSON> học @stop
@section('author') dungmori.com @stop
@section('title') Admin | Chi tiết bài học @stop

@section('assets')

@stop

@section('content')

    <div id="new-lesson-detail-screen">
        <div class="row bg-title">
            <h4 class="page-title pull-left">S<PERSON>a thông tin bài học</h4>
            <div>
                <a
                        class="pull-right ml-5"
                        style="border: 3px solid #fff; padding: 10px 10px; color: #fff; background: #9f041b; font-weight: bold; cursor: pointer"
                        href="{{url('/backend/lesson/' . $lesson->id. '/edit')}}"
                >Giao diện cũ</a>
                <span
                        class="pull-right"
                        style="border: 3px solid #fff; padding: 10px 10px; color: #fff; background: #00459f; font-weight: bold; cursor: pointer"
                        @click="convertToQuiz"
                >
                <PERSON><PERSON><PERSON>n thành bài quiz
            </span>
            </div>
        </div>
        <div class="lesson-detail__tabs">
            <div class="lesson-detail__tab" :class="tab.value == activeTab ? 'active' : ''" v-for="tab in tabs" @click="activeTab = tab.value">
                @{{ tab.name }}
            </div>
        </div>
        <div class="lesson-detail__content">
            <lesson-info v-if="activeTab == 'info'" :lesson="lesson" :groups="group" :courses="course" :authors="author"></lesson-info>
            <lesson-tasks v-if="activeTab == 'tasks'" :lesson="lesson" :grade-by-task="gradeByTask"></lesson-tasks>
        </div>
    </div>

@section('foot-js')
    <script>
        var author = {!! json_encode($author) !!}
        var lesson = {!! json_encode($lesson) !!}
        var group = {!! json_encode($group) !!}
        var course = {!! json_encode($course) !!}
        var gradeByTask = {!! json_encode($gradeByTask) !!}
    </script>
    <!-- CDNJS :: Sortable (https://cdnjs.com/) -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.8.4/Sortable.min.js"></script>
    <!-- CDNJS :: Vue.Draggable (https://cdnjs.com/) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Vue.Draggable/2.20.0/vuedraggable.umd.min.js"></script>
    <script src="{{asset('/plugin/ckeditor4/ckeditor.js')}}"></script>
    <script src="{{asset('/plugin/ckeditor4-vue/dist/ckeditor.js')}}"></script>
    <script type="text/javascript" src="{{asset('assets/backend/js/lesson/backend-lesson.js')}}?{{filemtime('assets/backend/js/lesson/backend-lesson.js')}}"></script>
@endsection

@section('footer')

@endsection()

@stop
