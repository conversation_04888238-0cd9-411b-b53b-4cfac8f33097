@extends('backend._default.dashboard')

@section('description')
    Quản lý lịch trình <PERSON> Kaiwa
@stop
@section('keywords')
    Quản lý lịch trình <PERSON> Kaiwa
@stop
@section('author')
    dungmori.com
@stop
@section('title')
    Admin | Quản lý lịch trình <PERSON> Kaiwa
@stop

@section('assets')
    <link media="all" type="text/css" rel="stylesheet"
          href="{{ asset('assets/backend/css/jlpt_results.css') }}?{{filemtime('assets/backend/css/jlpt_results.css')}}">
@stop

@section('content')
    <div id="app">
        <kaiwa-list-test :admin="{{ \Illuminate\Support\Facades\Auth::guard('admin')->user() }}"
                         :testing_time_frame="{{ json_encode($testing_time_frame) }}"
                         :list_teacher="{{ json_encode($list_teacher) }}"
        ></kaiwa-list-test>
    </div>
    <script>
        jQuery.browser = {};
        (function () {
            jQuery.browser.msie = false;
            jQuery.browser.version = 0;
            if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
                jQuery.browser.msie = true;
                jQuery.browser.version = RegExp.$1;
            }
        })();
    </script>
    <script type="text/javascript" src="{{asset('js/app.js')}}?{{filemtime('js/app.js')}}"></script>
@stop
