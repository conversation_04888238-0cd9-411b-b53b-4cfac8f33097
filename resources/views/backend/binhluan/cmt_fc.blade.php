@extends('backend._default.dashboard')

@section('description')
    Quản lý bình luận
@stop
@section('keywords')
    comment
@stop
@section('author')
    dungmori.com
@stop
@section('title')
    Admin | Quản lý bình luận
@stop
@section('assets')
    <link rel="stylesheet" href="{{ asset('plugin/bootstrap-daterangepicker/daterangepicker.css') }}">
@stop

@section('content')
    <div class="main-comment">
        <div class="main-comment-left">
            <li class="has_sub">
                <p class=" filter-item"><b>Lọc theo tình trạng</b></p>
                <div class="radio radio-success">
                    <input id="all_seen" type="radio" name="seen" v-model="filterSeen" value="all"
                           v-on:change="updateResults" checked="true"/>
                    <label for="all_seen">Tất c<PERSON></label>
                </div>
                <div class="radio radio-success">
                    <input id="unseen" type="radio" name="seen" v-model="filterSeen" value="0"
                           v-on:change="updateResults"/>
                    <label for="unseen">Chưa xem</label>
                </div>
                <div class="radio radio-success">
                    <input id="seen" type="radio" name="seen" v-model="filterSeen" value="1"
                           v-on:change="updateResults"/>
                    <label for="seen">Đã xem</label>
                </div>
            </li>
            <li class="has_sub">
                <p class=" filter-item"><b>Lọc theo mục</b></p>
                <div class="radio radio-success">
                    <input id="category_all" type="radio" name="category" v-model="filterCategory" value="all"
                           v-on:change="updateResults" checked="true">
                    <label for="category_all">Tất cả</label>
                </div>
                <div class="radio radio-success">
                    <input id="category_lesson" type="radio" name="category" v-model="filterCategory" value="lesson"
                           v-on:change="updateResults">
                    <label for="category_lesson">Khoá học</label>
                </div>
                <div class="child-container">
                    <div class="radio radio-success">
                        <input id="lesson_n5" type="radio" name="category" v-model="filterCategory" value="ln5"
                               v-on:change="updateResults">
                        <label for="lesson_n5">N5</label>
                    </div>
                    <div class="radio radio-success">
                        <input id="lesson_n4" type="radio" name="category" v-model="filterCategory" value="ln4"
                               v-on:change="updateResults">
                        <label for="lesson_n4">N4</label>
                    </div>
                    <div class="radio radio-success">
                        <input id="lesson_n3" type="radio" name="category" v-model="filterCategory" value="ln3"
                               v-on:change="updateResults">
                        <label for="lesson_n3">N3</label>
                    </div>
                    <div class="radio radio-success">
                        <input id="lesson_n2" type="radio" name="category" v-model="filterCategory" value="ln2"
                               v-on:change="updateResults">
                        <label for="lesson_n2">N2</label>
                    </div>
                    <div class="radio radio-success">
                        <input id="lesson_n1" type="radio" name="category" v-model="filterCategory" value="ln1"
                               v-on:change="updateResults">
                        <label for="lesson_n1">N1</label>
                    </div>
                    <div class="radio radio-success">
                        <input id="lesson_sc_n5" type="radio" name="category" v-model="filterCategory" value="sc-n5"
                               v-on:change="updateResults">
                        <label for="lesson_sc_n5">Sơ cấp N5</label>
                    </div>
                    <div class="radio radio-success">
                        <input id="lesson_sc_n4" type="radio" name="category" v-model="filterCategory" value="sc-n4"
                               v-on:change="updateResults">
                        <label for="lesson_sc_n4">Sơ cấp N4</label>
                    </div>
                </div>
            </li>

            <li class="has_sub">
                <p class=" filter-item"><b>Tìm kiếm</b> <i class="fa fa-search"></i></p>
                <div style="margin-top:10px;" class="input-group">
                    <input class="form-control" type="text" placeholder="Id hoặc từ khóa" v-model="filterKeywords"
                           v-on:change="updateResults"/>
                </div>
            </li>

        </div>

        <div class="main-comment-right">
            <div class="row" style="width: 90%">
                <div class="col-md-6">
                    <p>@{{ listComments.length }} / <strong>@{{ countResults }} </strong> kết quả</p>
                </div>
                <div class="col-md-6">
                    <div class="mb-3 flex flex-row-reverse">
                        <input type="button" value="Lọc theo ngày" v-on:click="filterRange" class="btn btn-info">
                        <input class="form-control input-daterange-datepicker" type="text" name="daterange"
                               style="width: 270px; margin-right: 15px"/>
                    </div>
                    <div class="mb-2  flex flex-row-reverse">
                        <input type="button" value="Lọc ID thẻ" v-on:click="filterCardByID" class="btn btn-info">
                        <input class="form-control" type="text" name="filterCard"
                               style="width: 270px; margin-right: 15px"/>
                    </div>
                </div>
            </div>
            <table class="table table-borderless" id="table_comment">
                <thead>
                <td>Id</td>
                <td>Bài học</td>
                <td>Flashcard</td>
                <td>Người bình luận</td>
                <td>Nội dung</td>
                <td>Hành động</td>
                </thead>
                <tbody>
                <tr :class="'item-'+ cmt.id" v-for="cmt in listComments">
                    <td style="vertical-align: middle;" class="text-left">@{{cmt.id}}</td>
                    <td style="vertical-align: middle;">
                        <a v-bind:href="window.location.origin + '/backend/lesson/' + cmt.flashcard_info?.lesson_id + '/edit#item' + cmt.table_id"
                           target="_blank">
                            <span title="Mở backend" style="font-weight: bold;color: darkblue">@{{ cmt.flashcard_info?.lesson_id }}</span>
                        </a>
                    </td>
                    <td style="vertical-align: middle; " class="text-left" width="300px">
                        <template v-if="cmt.flashcard_info?.type == 9">
                            <a v-bind:href="window.location.origin + '/khoa-hoc/khoa-hoc/' + cmt.flashcard_info?.lesson_id + '-bai-hoc'"
                                target="_blank">
                                <span title="Mở bài học" style="font-weight: bold;color: darkblue">@{{ cmt.table_info?.value?.jp }}</span>
                            </a>
                            <span style="font-weight: bold;color: darkred">@{{ cmt.flashcard_info?.value?.vi }}</span>
                        </template>
                        <template v-if="cmt.flashcard_info?.type == 17">
                            <a v-bind:href="window.location.origin + '/khoa-hoc/' + cmt.flashcard_info?.lesson.course.SEOurl + '/lesson/' + cmt.flashcard_info?.lesson_id + '-' + cmt.flashcard_info?.lesson.SEOurl + '?ref=notice&focus_fc=' + cmt.table_id"
                                target="_blank">
                                <span title="Mở bài học" style="font-weight: bold;color: darkblue" v-html="cmt.flashcard_info?.value?.word"></span>
                            </a>
                            <span style="font-weight: bold;color: darkred" v-html="cmt.flashcard_info?.value?.meaning"></span>
                        </template>

                        <span title="Lọc theo flashcard này" v-if="filterCard == ''"
                              v-on:click="filterByCard(cmt.table_id)" style="cursor:pointer;color: #0d95e8">
                            <i class="fa fa-filter"></i>
                        </span>
                        <span title="Bỏ lọc flashcard" v-if="filterCard != ''" v-on:click="filterByCard('')"
                              style="cursor:pointer;color: #9f041b"><i class="fa fa-window-close"></i></span>
                    </td>
                    <td style="vertical-align: middle; padding: 10px" class="text-left">
                        <div data-fancybox data-src="#popup-user-info" href="javascript:;"
                              data-options='{"touch" : false}'
                              v-on:click="cmt.user_info != null ? previewUser(cmt.user_info.id, cmt.user_info.name, cmt.user_info.email) : previewUser('--','--','--')">
                            @{{ cmt.user_info != null ? cmt.user_info.name : '--'}}
                        </div>
                        <div title="Lọc theo user này" v-if="cmt.user_info && filterCommenter == ''"
                              v-on:click="filterByUser(cmt.user_info.userId)" style="cursor:pointer;color: #0d95e8"><i
                                    class="fa fa-filter"></i></div>
                        <div title="Bỏ lọc user" v-if="cmt.user_info && filterCommenter != ''"
                              v-on:click="filterByUser('')" style="cursor:pointer;color: #9f041b"><i
                                    class="fa fa-window-close"></i></div>
                        <a v-if="cmt.cid != null" :href="'{{url('/backend/chat#')}}'+ cmt.cid" target="_blank">
                            <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;"
                               title="nhắn tin"></i>
                        </a>
                        <i v-if="cmt.cid == null" :class="'fa fa-comments fa-comments-'+ cmt.user_id"
                           style="cursor: pointer; font-size: 18px;" title="nhắn tin"
                           v-on:click="initConversation(cmt.user_id)"></i>
                    </td>
                    <td style="vertical-align: middle;" class="text-left" style="width: 50%;">
                        @{{ cmt.content }}

                        <p class="comment-action">
                            <a v-if="cmt.replies.length != 0" class="load-more-reply" role="button"
                               data-toggle="collapse" :id="'answer-reply-'+ cmt.id" :href="'#reply-'+ cmt.id"
                               aria-expanded="false" :aria-controls="'reply-'+ cmt.id">
                                <i class="fa fa-commenting"></i> @{{ cmt.replies.length }} phản hồi &nbsp;
                            </a>
                            <span v-if="cmt.replies.length == 0" class="answer" data-toggle="collapse"
                                  :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id">
				              Trả lời •
				            </span>
                            <span class="time">@{{ cmt.time_created }}</span>
                        </p>

                        <div class="reply-container">

                            <div class="collapse force-show" :id="'reply-'+ cmt.id">
                                <div class="child-comment-item" v-for="(childCmt, index) in cmt.replies"
                                     :id="'reply-item-'+ childCmt.id">
                                    <a v-if="childCmt.user_id == 0" class="pull-left avatar-container">
                                        <img class="avatar" :src="url + '/assets/img/oglogo.png'">
                                        <i class="zmdi zmdi-check-circle"></i>
                                    </a>
                                    <a v-if="childCmt.user_id != 0" class="pull-left avatar-container" data-fancybox
                                       data-src="#popup-user-info" href="javascript:;"
                                       data-options='{"touch" : false}'
                                       v-on:click="previewUser(childCmt.user_info.userId, childCmt.user_info.name, childCmt.user_info.email)">
                                        <img v-if="childCmt.user_info.avatar == null || childCmt.user_info.avatar == ''"
                                             class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
                                        <img v-if="childCmt.user_info.avatar != null && childCmt.user_info.avatar != ''"
                                             class="avatar"
                                             :src="url + '/cdn/avatar/small/'+ childCmt.user_info.avatar">
                                    </a>
                                    <div class="comment-content">
                                        <span class="delete-comment" v-on:click="delReply(childCmt.id)"><i
                                                    class="fa fa-trash"></i> xóa</span>
                                        <span class="admin-edit-pen" v-if="childCmt.user_id == 0" data-fancybox
                                              data-src="#edit-cmt-popup"
                                              href="javascript:;"
                                              v-on:click="editAdminComment(childCmt.id, childCmt.content)"><i
                                                    class="fa fa-pencil"></i> sửa</span>

                                        <p class="child-name">
                                            <b class="red" v-if="childCmt.user_id == 0">Dũng Mori
                                                @if(Auth::guard('admin')->user()->permission == 1)
                                                    ~@{{ viewAdminName(childCmt.admin_log) }}~
                                                @endif
                                            </b>
                                            <b class="red" v-if="childCmt.user_id != 0">
                                                @{{childCmt.user_info.name}}
                                            </b>
                                            <span :id="'child-comment-content-'+ childCmt.id"
                                                  v-html="printInfo(childCmt.content)"></span>

                                        </p>
                                        <div v-if="childCmt.img != null" class="preview-image">
                                            <a class="popup-preview-image"
                                               :href="url + '/cdn/comment/default/'+ childCmt.img" data-fancybox>
                                                <img class="preview-thumbnail"
                                                     :src="url + '/cdn/comment/small/'+ childCmt.img"/>
                                            </a>
                                        </div>
                                        <span class="child-comment-action">
					                    <span class="time">@{{childCmt.time_created}}</span>
					                    <span style="color: #f06;"
                                              v-if="childCmt.user_id == 0 && childCmt.updated_at != childCmt.created_at"> &nbsp; Đã sửa</span>
					                    @if(Auth::guard('admin')->user()->permission == 1)
                                                <span title="chỉnh sửa" style="cursor: pointer;"
                                                      v-if="childCmt.user_id == 0 && childCmt.updated_at != childCmt.created_at && childCmt.admin_log != null"
                                                      data-fancybox data-src="#show-cmt-log" href="javascript:;"
                                                      v-on:click="viewAdminLog(childCmt.admin_log)"> &nbsp; <i
                                                            class="fa fa-history"></i></span>
                                            @endif
					                </span>

                                    </div>


                                </div>

                                <div class="reply-form">
                                    <img class="me-avatar" :src="url + '/assets/img/oglogo.png'">
                                    <textarea data-emoji-picker="true" class="input-comment"
                                              :id="'reply-input-content-'+ cmt.id" rows="1"
                                              placeholder="comment..."></textarea>
                                    <span class="pick-image" data-toggle="tooltip" data-placement="bottom"
                                          title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif">
				                    <i class="fa fa-camera"></i>
				                    <form class="form-pick-image" :id="'cmt-attachment-form-'+ cmt.id">
				                        <input type='file' :id="'commentImagePicked'+ cmt.id" name="commentImagePicked"
                                               v-on:change="previewImageReply" accept=".png, .jpg, .jpeg, .gif"/>
				                    </form>
				                </span>
                                    <span class="post-comment-btn" v-on:click="postNewAnswer(cmt.id)">Trả lời</span>
                                    <div class="preview-image" :id="'preview-image-cmt-'+ cmt.id"></div>
                                </div>

                            </div>
                        </div>
                    </td>
                    <td class="text-right" width="150px">
                        <p style="margin-bottom: 2px;">
                            <span v-if="cmt.readed == 0" class="label new-label orange-label">Chưa xem</span>
                            <span v-if="cmt.readed != 0" class="label new-label green-label">Đã xem</span>
                        </p>
                        {{-- <span v-if="cmt.readed == 1" style="color: #000; cursor: pointer;" v-on:click="markAsRead(cmt.id)">chưa xem</span> --}}
                        <span v-if="cmt.readed == 0" style="color: #000; cursor: pointer;"
                              v-on:click="markAsRead(cmt.id)"><i class="fa fa-eye"></i> xem</span>
                        <span style="color: #000; cursor: pointer;" v-on:click="pinComment(cmt)"><i
                                    class="fa fa-thumb-tack" v-bind:class="{'text-danger': cmt.pin}"></i> @{{ cmt.pin ? 'bỏ ghim' : 'ghim'}}</span>
                        <span style="color: #000; cursor: pointer;" v-on:click="delComment(cmt.id)"><i
                                    class="fa fa-trash"></i> xóa</span>
                        {{--                            <span style="color: red; cursor: pointer; font-weight: bold" v-on:click="delComment(cmt.id)"> xóa</span>--}}
                    </td>
                </tr>

                </tbody>
            </table>

            <div class="popup-user-info" id="popup-user-info">
                <h4>Thông tin người dùng</h4>
                <p>ID: <b>@{{previewId || '--'}}</b></p>
                <p>Name: <b>@{{previewName || '--'}}</b></p>
                <p>Email: <b>@{{previewEmail || '--'}}</b></p>
            </div>

            <form id="edit-cmt-popup" class="edit-cmt-popup" action="" method="post">
                <h4>Sửa nội dung</h4>
                <input type="hidden" id="edit-comment-id"/>
                <p><textarea data-emoji-picker="true" class="edit-comment-area form-control" id='edit-comment-area'
                             placeholder="comment..."></textarea></p>
                <p class="mb-0 text-right">
                    <span class="edit-comment-btn-save" v-on:click="saveAdminComment()">Lưu</span>
                    <span class="edit-comment-btn-cancel" v-on:click="cancelAdminComment()">Hủy</span>
                </p>
            </form>

            <ul class="pagination"> {{ $comments->links('vendor.pagination.cmt-pagination') }} </ul>

        </div>
    </div>
    <style>
        .daterangepicker .ranges {
            display: none;
        }
    </style>
@stop
@section('footer')
    <script type="text/javascript">
        var comments = {!! json_encode($comments) !!};
        var totalResults = comments.total;
        console.log(comments);
    </script>
    <script src="{{ asset('/plugin/vuejs-paginate/vuejs-paginate.js') }}"></script>
    <script src="{{asset('plugin/vanilla-emoji-picker/emojiPicker.js')}}"></script>
    <script src="{{asset('plugin/autosize/autosize.js')}}"></script>
    <script src="{{asset('assets/backend/js/comment_fc.js')}}?{{filemtime('assets/backend/js/comment_fc.js')}}"></script>
    <script src="{{asset('plugin/fancybox/dist/jquery.fancybox.min.js')}}"></script>
    <link href="{{asset('plugin/fancybox/dist/jquery.fancybox.min.css')}}" rel="stylesheet" type="text/css">
    <script src="{{ asset('plugin/moment/moment.js') }}"></script>
    <script src="{{ asset('plugin/timepicker/bootstrap-timepicker.js') }}"></script>
    <script src="{{ asset('plugin/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ asset('plugin/bootstrap-daterangepicker/daterangepicker.js') }}"></script>
    <script>
        $('.input-daterange-datepicker').daterangepicker({
            format: 'DD/MM/YYYY',
            buttonClasses: ['btn', 'btn-sm'],
            applyClass: 'btn-default',
            cancelClass: 'btn-white',
        });
    </script>
@stop
