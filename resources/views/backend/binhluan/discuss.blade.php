@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON><PERSON> lý bình luận @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop
@section('title') Admin | Qu<PERSON>n lý bình luận @stop
@section('assets')
	<style type="text/css">

		.radio-success input[type=radio]:checked+label:before {border-color: #1d478a; }
		.radio-success input[type=radio]:checked+label:after {background-color: #1d478a; }

		.daterangepicker .ranges{display: none; }
		.fa-microphone{font-size: 16px; position: relative; cursor: pointer;}
		.preview-audio{width: 100%; float: left;}
		.controls { margin:0 0 0 20px; width: 48%; float: left; }
		ol{margin-top: 10px; padding-left: 0;}
		button {width: calc(50% - 10px); height: 34px; border: none; border-radius: 0.15rem; background: #ed341d; margin-right: 5px; align-items: center;
		color:#ffffff; box-shadow: inset 0 -0.15rem 0 rgba(0, 0, 0, 0.2); cursor: pointer; font-weight: bold; font-size: 1rem; }
		button:hover, button:focus {outline: none; background: #c72d1c; }
		button::-moz-focus-inner {border: 0; }
		button:active {box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.2); line-height: 3rem; }
		button:disabled {pointer-events: none; background: lightgray; }
		button:first-child {margin-left: 0; }
		audio {display: block; width: 97%; margin-top: 0.2rem; height: 30px; }
		li {list-style: none; }
		.activator-wrapper[data-v-7863e830] > input {
			background: transparent !important;
		}
	</style>
@stop

@section('content')
<div class="main-comment">
    <div class="main-comment-left">

    	<li class="has_sub">
            <p class=" filter-item"><b>Lọc theo tình trạng</b></p>
            <div class="radio radio-success">
				<input id="all_seen" type="radio" name="seen" v-model="filter.seen" :value="''" @change="applyFilter"/>
                <label for="all_seen"> Tất cả</label>
            </div>
            <div class="radio radio-success">
                <input id="unseen" type="radio" name="seen" v-model="filter.seen" :value="0" @change="applyFilter"/>
                <label for="unseen"> Chưa xem (返事しませんでした)</label>
            </div>
            <div class="radio radio-success">
				<input id="seen" type="radio" name="seen" v-model="filter.seen" :value="1" @change="applyFilter"/>
                <label for="seen"> Đã xem (返事しました)</label>
            </div>
        </li>


		<li class="has_sub">
			<p class=" filter-item"><b>Lọc theo admin trả lời</b></p>
			<div class="radio radio-success">
				<input id="all_admin" type="radio" name="admin" v-model="filter.admin" :value="''" @change="applyFilter"/>
				<label for="all_admin">Tất cả</label>
			</div>
			<div class="radio radio-success" v-for="admin in admins">
				<input :id="'admin-' + admin.id" type="radio" name="admin" v-model="filter.admin" :value="admin.id" @change="applyFilter"/>
				<label :for="'admin-' + admin.id">@{{admin.name}}</label>
			</div>
		</li>

		<li class="has_sub">
			<p class=" filter-item"><b>Lọc theo user</b></p>
			<div>
				<input type="text" placeholder="ID/SĐT/Email" v-model="filter.user" @keyup.enter="applyFilter"/>
			</div>
		</li>

		<li class="has_sub">
			<p class=" filter-item"><b>Lọc theo từ khoá</b></p>
			<div>
				<input type="text" placeholder="Nhập từ khoá" v-model="filter.keyword" @keyup.enter="applyFilter"/>
			</div>
		</li>
		<li class="has_sub">
			<p class=" filter-item"><b>Lọc theo ID comment</b></p>
			<div>
				<input type="text" placeholder="Nhập ID" v-model="filter.id" @keyup.enter="applyFilter"/>
			</div>
		</li>
    </div>

    <div class="main-comment-right">
		<div class="row" style="width: 90%;">
			<div class="col-md-6" style="display: flex; justify-content: flex-start; align-items: center">
				<p>@{{ listComments.length }} / <strong>@{{ total_result }} </strong> kết quả</p>

			</div>
			<div class="col-md-6" style="display: flex; justify-content: flex-end; align-items: center">
				<select class="form-control mr-5" v-model="filter.time_type" style="border: none; border-bottom: 1px solid #111; background: transparent; font-size: 18px;">
					<option :value="''">Lọc theo</option>
					<option :value="1">Thời gian HV gửi</option>
					<option :value="2">Thời gian GV trả lời</option>
				</select>
				<v-md-date-range-picker
						:start-date="filter.time_from"
						:end-date="filter.time_to"
						@change="onChangeDateRange"
						opens="right"
						show-year-select
				></v-md-date-range-picker>
				<span @click="applyFilter" class="btn btn-info"><i class="zmdi zmdi-search"></i> Lọc ngày</span>
				<span @click="resetFilter" class="btn btn-danger" style="margin-left: 10px"><i class="zmdi zmdi-close"></i></span>
			</div>

		</div>
		<div v-if="loading" style="height: 70vh; display: flex; justify-content: center; align-items: center">
			<i class="fa fa-spinner fa-pulse fa-2x"></i>
		</div>
    	<table class="table table-borderless" id="table_comment" v-if="!loading">
		    <tbody>
				<tr :class="'item-'+ cmt.id" v-for="cmt in listComments">
					<td class="text-left id-field">@{{cmt.id}}</td>
					<td class="text-left">
						<p>
							<b class="name" style="color:#333;" v-if="cmt.user_info">@{{ cmt.user_info.name }} </b> •
							<b  style="color:red;">@{{cmt.task ? cmt.task.lesson.name : '--'}}</b> # <b  style="color:green;">@{{cmt.vocab}}</b>
							<span title="Lọc theo từ này" v-if="filter.vocab == ''"  v-on:click="filterByVocab(cmt.vocab)" style="cursor:pointer;color: #0d95e8"><i class="fa fa-filter"></i></span>
							<span title="Bỏ lọc từ khoá" v-if="filter.vocab != ''" v-on:click="filterByVocab('')" style="cursor:pointer;color: #9f041b"><i class="fa fa-window-close"></i></span>
						</p>

                    	<span class="cmt-content" style="display: flex;">
                    		<span v-if="cmt.user_info" style="height: 40px;" class="avatar-field" data-fancybox data-src="#popup-user-info" href="javascript:;"
                    		data-options='{"touch" : false}' v-on:click="previewUser(cmt.user_info.id, cmt.user_info.name, cmt.user_info.email)">
								<img v-if="!cmt.user_info || cmt.user_info.avatar == null || cmt.user_info.avatar == ''" class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
		          				<img v-if="cmt.user_info &&cmt.user_info.avatar != null && cmt.user_info.avatar != ''" class="avatar" :src="url + '/cdn/avatar/small/'+ (cmt.user_info.avatar || '') ">
							</span>
                    		<audio controls style="width: 400px; height: 40px;" class="audio-hv">
                    			<source :src="url+ '/cdn/kaiwa/'+ cmt.audio " type="audio/mpeg">
                    		</audio>
							<span v-if="cmt.admin_info"
								  style="height: 40px; margin-left: 10px" class="avatar-field" data-fancybox data-src="#popup-user-info" href="javascript:;"
								  data-options='{"touch" : false}' v-on:click="previewUser(cmt.admin_info.id, cmt.admin_info.name, cmt.admin_info.email)">
								<img v-if="cmt.admin_info.avatar == null || cmt.admin_info.avatar == ''" class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
		          				<img v-if="cmt.admin_info.avatar != null && cmt.admin_info.avatar != ''" class="avatar" :src="url + '/cdn/avatar/small/'+ cmt.admin_info.avatar">
							</span>
                    	</span>

	                    <p class="comment-action" style="padding-left: 45px;">
				            <a v-if="cmt.replies.length != 0" class="load-more-reply" role="button" data-toggle="collapse" :id="'answer-reply-'+ cmt.id" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id" style="color:#1d478a;">
				              <i class="fa fa-commenting"></i> @{{ cmt.replies.length }} phản hồi &nbsp;
				            </a>
				            <span v-if="cmt.replies.length == 0" class="answer" data-toggle="collapse" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id">
				              Trả lời •
				            </span>
				            <span class="time">@{{ cmt.time_created }}</span>
				        </p>

				        <div class="reply-container" style="padding-left: 45px;">

				            <div class="collapse" :id="'reply-'+ cmt.id">
				              <div class="child-comment-item" v-for="(childCmt, index) in cmt.replies" :id="'reply-item-'+ childCmt.id">

				                {{-- nếu là dungmori rep --}}
				                <div class="comment-content" style="padding-left: 0; background: #f06;"  v-if="childCmt.user_id == 0">
				                	<span class="delete-comment" v-on:click="delReply(childCmt.id)"><i class="fa fa-trash"></i> xóa</span>
				                	<span class="admin-edit-pen" v-if="childCmt.user_id == 0" data-fancybox data-src="#edit-cmt-popup"
                					  href="javascript:;" v-on:click="editAdminComment(childCmt.id, childCmt.content)"><i class="fa fa-pencil"></i> sửa</span>

					                <div class="child-name">
					                	<span style="float: right; font-size: 14px; margin: 12px 0 0 10px;">@{{childCmt.time_created}}</span>

										<div style="display: flex; align-items: center" >
											<span v-if="childCmt.admin_info" style="height: 40px; float: left; margin-top: -5px;" class="pull-left avatar-container">
{{--											<img class="avatar" :src="url + '/assets/img/oglogo.png'">--}}
											<img v-if="childCmt.admin_info.avatar == null || childCmt.admin_info.avatar == ''" class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
		          							<img v-if="childCmt.admin_info.avatar != null && childCmt.admin_info.avatar != ''" class="avatar" :src="url + '/cdn/avatar/small/'+ childCmt.admin_info.avatar">
											<i class="zmdi zmdi-check-circle"></i>
										</span>

											<audio v-if="childCmt.audio != null" class="audio-hv" controls style="width: 360px; height: 40px; float: left; margin-right: 15px;">
												<source :src="url + '/cdn/kaiwa/'+ childCmt.audio" type="audio/mpeg">
											</audio>

											<div class="bubble" v-if="childCmt.content != null" style="float: left; margin: 5px 10px 0 0; max-width: 320px; text-align: left;">
												<div v-if="childCmt.user_id == 0" :id="'child-comment-content-'+ childCmt.id"
													 style="background: rgb(0, 153, 255); float: left; padding: 9px 15px; border-radius: 18px; color: #fff;"
													 v-html="printInfo(childCmt.content)"></div>
											</div>
											<div style="float: left;">
												<span style="color: #0ab22c" v-if="childCmt.is_correct == 1"><i class="fa fa-check"></i> Sửa phát âm</span>
												<input type="checkbox" v-on:change="setCorrectSpell(childCmt.id, $event)" :checked="childCmt.is_correct == 1" class="form-check-input" style="margin-top: 10px; margin-right: 15px"/>
											</div>
										</div>

					                </div>

				                </div>

				                {{-- nếu là user rep --}}
				                <div class="comment-content" style="padding-left: 0;"  v-if="childCmt.user_id != 0">
				                	<span class="delete-comment" v-on:click="delReply(childCmt.id)"><i class="fa fa-trash"></i> xóa</span>
				                	<span class="admin-edit-pen" v-if="childCmt.user_id == 0" data-fancybox data-src="#edit-cmt-popup"
                					  href="javascript:;" v-on:click="editAdminComment(childCmt.id, childCmt.content)"><i class="fa fa-pencil"></i> sửa</span>
					                <p class="child-name">
					                	<span style="height: 40px; float: left; margin-top: -5px;" class="avatar-field" v-if="cmt.user_info">
											<img v-if="cmt.user_info.avatar == null || cmt.user_info.avatar == ''" class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
					          				<img v-if="cmt.user_info.avatar != null && cmt.user_info.avatar != ''" class="avatar" :src="url + '/cdn/avatar/small/'+ cmt.user_info.avatar">
										</span>
					                	<audio v-if="childCmt.audio != null" class="audio-hv" controls style="width: 400px; height: 40px;  float: left;">
					                		<source :src="url + '/cdn/kaiwa/'+ childCmt.audio" type="audio/mpeg">
					                	</audio>
					                	<span style="float: right; font-size: 14px; margin: 12px 0 0 10px;">@{{childCmt.time_created}}</span>
					                </p>
				                </div>

				              </div>

				              <div class="reply-form">
								  @if(Auth::guard('admin')->user()->avatar == null)
									  <img src="{{ url('assets/img/default-avatar.jpg')}}" class="img-circle me-avatar" width="36">
								  @else
									  <img src="{{ url('cdn/avatar/small', Auth::guard('admin')->user()->avatar) }}" class="img-circle me-avatar" width="36">
								  @endif
				                <div class="controls">
								  	<button :id="'recordButton'+ cmt.id" v-on:click="recordButton(cmt.id)">Record</button>
								  	<button :id="'stopButton' + cmt.id" v-on:click="stopButton(cmt.id)" disabled>Stop</button>
									<label style="color: red; font-weight: bold">
										<input type="checkbox" :id="'is-correct-box-' + cmt.id"/>
										Check là sửa phát âm
									</label>

									<div class="preview-audio" :id="'preview-recorder-'+ cmt.id">
									  	<ol :id="'recordingsList'+ cmt.id"></ol>

									</div>
							    </div>
							    <textarea class="input-comment-x" style="width: 30% !important; float: left;margin: 0 15px; padding: 6px 10px; box-sizing: border-box; outline: none; border: 1px solid #DDD; border-radius: 2px;" :id="'reply-input-content-'+ cmt.id" rows="1" placeholder="chú thích..."></textarea>

							  	<span class="post-comment-btn" v-on:click="checkReplied(cmt.id)">Trả lời</span>

							  </div>

				         </div>
				        </div>
	                </td>
					<td class="text-right" width="130px">
						<p style="margin-bottom: 2px;">
	                    	<span v-if="cmt.readed == 0" class="label new-label orange-label">Chưa xem</span>
	                     	<span v-if="cmt.readed != 0" class="label new-label green-label">Đã xem</span>
	                    </p>
	                    {{-- <span v-if="cmt.readed == 1" style="color: #000; cursor: pointer;" v-on:click="markAsRead(cmt.id)">chưa xem</span> --}}
						<span v-if="cmt.readed == 0" style="color: #000; cursor: pointer;" v-on:click="markAsRead(cmt.id)"><i class="fa fa-eye"></i> xem</span> &nbsp;
                     	<span style="color: #000; cursor: pointer;" v-on:click="delComment(cmt.id)"><i class="fa fa-trash"></i> xóa</span>
	                </td>
				</tr>

		    </tbody>
		</table>

		<div class="popup-user-info" id="popup-user-info">
			<h4>Thông tin người dùng</h4>
			<p v-if="previewId">ID:  <b>@{{previewId}}</b></p>
			<p>Name:  <b>@{{previewName}}</b></p>
			<p>Email:  <b>@{{previewEmail}}</b></p>
		</div>
		<form id="edit-cmt-popup" class="edit-cmt-popup" action="" method="post">
	        <h4>Sửa nội dung</h4>
	        <input type="hidden" id="edit-comment-id"/>
	        <p><textarea data-emoji-picker="true" class="edit-comment-area form-control" id='edit-comment-area' placeholder="comment..."></textarea></p>
	        <p class="mb-0 text-right">
	            <span class="edit-comment-btn-save" v-on:click="saveAdminComment()">Lưu</span>
	            <span class="edit-comment-btn-cancel" v-on:click="cancelAdminComment()">Hủy</span>
	        </p>
	    </form>

{{--		<ul class="pagination"> {{ $comments->links('vendor.pagination.cmt-pagination') }} </ul>--}}
		<div class="kaiwa-component__pagination"
			 style="display:flex;justify-content:space-between;align-items:center;position:fixed;bottom:0;left:1vw;width:98vw;height:70px;background:#FFF;padding: 1vw;box-shadow: 0px 2px 11px -2px rgba(0,0,0,0.75);">
			<div>
				Hiển thị
				<select v-model="filter.per_page" @change="applyFilter">
					<option value="15">15</option>
					<option value="20">20</option>
					<option value="50">50</option>
					<option value="100">100</option>
					<option value="500">500</option>
					<option value="1000">1000</option>
					<option value="2000">2000</option>
				</select>
				trong số @{{ loading ? '----' : total_result}} kết quả
			</div>
			<paginate
					{{--                    v-model="filter.page"--}}
					:page-count="filter.total_page"
					:page-range="4"
					:margin-pages="3"
					:click-handler="changePage"
					:prev-text="'&laquo;'"
					:next-text="'&raquo;'"
					:container-class="'pagination'"
					:page-class="'page-item'"
					:force-page="filter.page - 1"
					style="width: auto !important;"
			>
			</paginate>
		</div>
    </div>
</div>
<style>
	.daterangepicker .ranges{
		display: none;
	}
</style>
@stop

@section('footer')
	<script type="text/javascript">
		{{--var comments = {!! json_encode($comments) !!};--}}
		var admins = {!! json_encode($admins) !!};
		// var totalResults = comments.total;
		var adminId = {{Auth::guard('admin')->user()->id}};

	</script>
    <script>
      jQuery.browser = {};
      (function () {
        jQuery.browser.msie = false;
        jQuery.browser.version = 0;
        if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
          jQuery.browser.msie = true;
          jQuery.browser.version = RegExp.$1;
        }
      })();
    </script>
    <script src="{{asset('plugin/autosize/autosize.js')}}"></script>
	<script src="{{asset('plugin/fancybox/dist/jquery.fancybox.min.js')}}"></script>
	<link href="{{asset('plugin/fancybox/dist/jquery.fancybox.min.css')}}" rel="stylesheet" type="text/css">
	<link href="{{asset('plugin/md-iconic/css/material-design-iconic-font.css')}}" rel="stylesheet" type="text/css">
	<script src="{{ asset('plugin/moment/moment-with-locales.js') }}"></script>
	<script type="text/javascript">
		moment.locale('vi');
	</script>
	<script src="{{ asset('plugin/vue-daterange/v-md-date-range-picker.min.js') }}"></script>
	<script src="{{ asset('/plugin/deparam/deparam.min.js') }}"></script>
	<script src="{{asset('plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
	<script src="{{asset('plugin/vue-router/vue-router.js')}}"></script>
	<script src="https://cdn.rawgit.com/mattdiamond/Recorderjs/08e7abd9/dist/recorder.js"></script>

	<script src="{{asset('assets/backend/js/discuss.js')}}?{{filemtime('assets/backend/js/discuss.js')}}"></script>
@stop
