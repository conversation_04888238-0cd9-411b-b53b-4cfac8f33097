@extends('backend._default.dashboard')

@section('description') Quản lý bình luận @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý bình luận facebook @stop
@section('content')
	<div class="main-comment" style="margin: 0 auto; float: none; width: 1320px">
		<div class="main-comment-left">
			<div class="list-group" id="list-filter">
				<h4>Lọc theo</h4>
				<a href="?param=all" class="list-group-item" data-link="all">
					Trang chủ
				</a>
				@foreach($list as $keyCourse => $itemCourse)
					@if($itemCourse['double'])
						<a href="?param={{$itemCourse['link'] . '&double=' . $itemCourse['double'] . '&link-double=' . $itemCourse['link_double'] }}" class="list-group-item" data-link="{{$itemCourse['link']}}" style="font-size: 13px">
							{{$itemCourse['name']}}
						</a>
					@else
						<a href="?param={{$itemCourse['link']}}" class="list-group-item" data-link="{{$itemCourse['link']}}" style="font-size: 13px">
							{{$itemCourse['name']}}
						</a>
					@endif
				@endforeach
			</div>
		</div>

		<div class="main-comment-right" id="facebook-comment-content" style="background: unset">
			<h4></h4>
			<div id="fb-root"></div>
			<script async defer crossorigin="anonymous" src="https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v5.0"></script>
			@if($double)
				<div class="row">
					<div class="col-md-6">
						<div class="fb-comments" data-href="http://dungmori.com/{{$param}}" data-width="450" data-numposts="5" data-order-by="reverse_time"></div>
					</div>
					<div class="col-md-6">
						<div class="fb-comments" data-href="http://dungmori.com/{{$linkDouble}}" data-width="450" data-numposts="5" data-order-by="reverse_time"></div>
					</div>
				</div>
			@else
				<div class="fb-comments" data-href="http://dungmori.com/{{$param}}" data-width="900" data-numposts="5" data-order-by="reverse_time"></div>
			@endif
		</div>
	</div>
@stop

@section('footer')
	<script src="{{ asset('/plugin/vuejs-paginate/vuejs-paginate.js') }}"></script>
    <script src="{{asset('plugin/vanilla-emoji-picker/emojiPicker.js')}}"></script>
    <script src="{{asset('plugin/autosize/autosize.js')}}"></script>
	<script src="{{asset('assets/backend/js/comment.js')}}?{{filemtime('assets/backend/js/comment.js')}}"></script>
	<script src="{{asset('plugin/fancybox/dist/jquery.fancybox.min.js')}}"></script>
	<link href="{{asset('plugin/fancybox/dist/jquery.fancybox.min.css')}}" rel="stylesheet" type="text/css">
	<script>
		$(document).ready(function () {
			var param = window.location.search;
			$("#list-filter a").each(function () {
				var link = $(this).data('link');
				var regex = new RegExp(link);
				var matches = param.match(regex);
				if (matches !== null) {
					$(this).addClass('active');
				}
			});

		})
	</script>
@stop
