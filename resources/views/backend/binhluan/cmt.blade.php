@extends('backend._default.dashboard')

@section('description') Q<PERSON>ản lý bình luận @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý bình luận @stop
@section('assets')
	<link rel="stylesheet" href="{{ asset('plugin/bootstrap-daterangepicker/daterangepicker.css') }}">
@stop

@section('content')
<div class="main-comment">
    <div class="main-comment-left">

    	<li class="has_sub">
            <p class=" filter-item"><b>Lọ<PERSON> theo tình tr<PERSON></b></p>
            <div class="radio radio-success">
                <input id="all_seen" type="radio" name="seen" v-model="filterSeen" value="all" v-on:change="updateResults" checked="true"/>
                <label for="all_seen">Tất c<PERSON></label>
            </div>
            <div class="radio radio-success">
                <input id="unseen" type="radio" name="seen" v-model="filterSeen" value="0" v-on:change="updateResults"/>
                <label for="unseen">Chưa xem</label>
            </div>
            <div class="radio radio-success">
                <input id="seen" type="radio" name="seen" v-model="filterSeen" value="1" v-on:change="updateResults"/>
                <label for="seen">Đã xem</label>
            </div>
            <div class="radio radio-success">
                <input id="admin_change" type="radio" name="seen" v-model="filterSeen" value="2" v-on:change="updateResults"/>
                <label for="admin_change">Admin đã sửa</label>
            </div>
            <p style="font-size: 12px; ">Lưu ý:<br/>Chọn theo "admin đã sửa" thì không lọc dc theo các điều kiện khác</p>
        </li>

    	<li class="has_sub">
            <p class=" filter-item"><b>Lọc theo mục</b></p>
            <div class="radio radio-success">
                <input id="category_all" type="radio" name="category"  v-model="filterCategory" value="all" v-on:change="updateResults" checked="true">
                <label for="category_all">Tất cả</label>
            </div>
            <div class="radio radio-success">
                <input id="category_lesson" type="radio" name="category"  v-model="filterCategory" value="lesson" v-on:change="updateResults">
                <label for="category_lesson">Bài học</label>
            </div>
            <div class="child-container">
							<label for=""><b>JLPT</b></label>
							<div>
								<div class="radio radio-success">
									<input id="lesson_n5_so_cap" type="radio" name="category"  v-model="filterCategory" value="ln5-so-cap" v-on:change="updateResults">
									<label for="lesson_n5_so_cap">N5 Sơ cấp</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_n4_so_cap" type="radio" name="category"  v-model="filterCategory" value="ln4-so-cap" v-on:change="updateResults">
									<label for="lesson_n4_so_cap">N4 Sơ cấp</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_jlpt_n3" type="radio" name="category"  v-model="filterCategory" value="jlpt_n3" v-on:change="updateResults">
									<label for="lesson_jlpt_n3">JLPT N3</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_jlpt_n2" type="radio" name="category"  v-model="filterCategory" value="jlpt_n2" v-on:change="updateResults">
									<label for="lesson_jlpt_n2">JLPT N2</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_jlpt_n1" type="radio" name="category"  v-model="filterCategory" value="jlpt_n1" v-on:change="updateResults">
									<label for="lesson_jlpt_n1">JLPT N1</label>
								</div>
								<div class="radio radio-success">
									<input id="kanji_n5" type="radio" name="category"  v-model="filterCategory" value="kanji_n5" v-on:change="updateResults">
									<label for="kanji_n5">Kanji N5</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_n5" type="radio" name="category"  v-model="filterCategory" value="ln5" v-on:change="updateResults">
									<label for="lesson_n5">N5</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_n4" type="radio" name="category"  v-model="filterCategory" value="ln4" v-on:change="updateResults">
									<label for="lesson_n4">N4</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_n3" type="radio" name="category"  v-model="filterCategory" value="ln3" v-on:change="updateResults">
									<label for="lesson_n3">N3</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_n2" type="radio" name="category"  v-model="filterCategory" value="ln2" v-on:change="updateResults">
									<label for="lesson_n2">N2</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_n1" type="radio" name="category"  v-model="filterCategory" value="ln1" v-on:change="updateResults">
									<label for="lesson_n1">N1</label>
								</div>
							</div>
							<label><b>EJU</b></label>
							<div>
								<div class="radio radio-success">
									<input id="lesson_eju" type="radio" name="category"  v-model="filterCategory" value="eju" v-on:change="updateResults">
									<label for="lesson_eju">EJU</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_ejut" type="radio" name="category"  v-model="filterCategory" value="ejut" v-on:change="updateResults">
									<label for="lesson_ejut">EJU Toán</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_ejuxh" type="radio" name="category"  v-model="filterCategory" value="ejuxh" v-on:change="updateResults">
									<label for="lesson_ejuxh">EJU XHTH</label>
								</div>
							</div>
							<label><b>Kaiwa</b></label>
							<div>
								<div class="radio radio-success">
									<input id="lesson_kaiwa_sc" type="radio" name="category"  v-model="filterCategory" value="kaiwasc" v-on:change="updateResults">
									<label for="lesson_kaiwa_sc">Sơ cấp</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_kaiwa_tc" type="radio" name="category"  v-model="filterCategory" value="kaiwatc" v-on:change="updateResults">
									<label for="lesson_kaiwa_tc">Trung cấp</label>
								</div>
								<div class="radio radio-success">
									<input id="lesson_kaiwa_nc" type="radio" name="category"  v-model="filterCategory" value="kaiwanc" v-on:change="updateResults">
									<label for="lesson_kaiwa_nc">Nâng cao</label>
								</div>
							</div>
            </div>
            <div class="radio radio-success">
                <input id="category_course" type="radio" name="category" v-model="filterCategory" value="course" v-on:change="updateResults">
                <label for="category_course">Khóa học</label>
            </div>
            <div class="radio radio-success">
                <input id="category_combo" type="radio" name="category" v-model="filterCategory" value="combo" v-on:change="updateResults"/>
                <label for="category_combo">Combo</label>
            </div>
            <div class="radio radio-success">
                <input id="category_teacher" type="radio" name="category" v-model="filterCategory" value="teacher" v-on:change="updateResults"/>
                <label for="category_teacher">Giáo viên</label>
            </div>
            <div class="radio radio-success">
                <input id="category_review" type="radio" name="category" v-model="filterCategory" value="feedback" v-on:change="updateResults"/>
                <label for="category_review">Reviews</label>
            </div>
        </li>

        <li class="has_sub">
            <p class=" filter-item"><b>Tìm kiếm</b> <i class="fa fa-search"></i></p>
            <div style="margin-top:10px;" class="input-group">
            	<input class="form-control" type="text" placeholder="Id hoặc từ khóa" v-model="filterKeywords" v-on:change="updateResults"/>
            </div>
        </li>

    </div>

    <div class="main-comment-right">
		<div class="row" style="width: 90%">
			<div class="col-md-6">
				<p>@{{ listComments.length }} / <strong>@{{ countResults }} </strong> kết quả</p>
			</div>
			<div class="col-md-6">
				<select name="product-select" id="product-select">
					<option value="all">---Tất cả---</option>
					<option value="course-17">N1</option>
					<option value="course-16">N2</option>
					<option value="course-3">N3</option>
					<option value="course-4">N4</option>
					<option value="course-5">N5</option>
					<option value="course-8">Eju</option>
					<option value="course-9">Eju toán</option>
					<option value="course-10">Eju XHTH</option>
					<option value="course-21">Kaiwa</option>

					<option value="combo-33">N2+N1</option>
					<option value="combo-20">N3+N2</option>
					<option value="combo-5">N4+N3</option>

					<option value="combo-34">N3+N2+N1</option>
					<option value="combo-13">N4+N3+N2</option>
					<option value="combo-35">N4+N3+N2+N1</option>

				</select>
				<input type="button" value="Lọc theo ngày" v-on:click="filterRange" class="btn btn-info" style="float: right; margin-left: 15px">
				<input class="form-control input-daterange-datepicker" type="text" name="daterange" style="width: 270px; float: right; margin-bottom: 5px"/>
			</div>
		</div>
    	<table class="table table-borderless" id="table_comment">
		    <tbody>

				<tr :class="'item-'+ cmt.id" v-for="cmt in listComments">
					<td class="text-left id-field">@{{cmt.id}}</td>
					<td class="text-left avatar-field" data-fancybox data-src="#popup-user-info" href="javascript:;" data-options='{"touch" : false}'
                					                   v-on:click="previewUser(cmt.user_info.userId, cmt.user_info.name, cmt.user_info.email)">
						<img v-if="cmt.user_info.avatar == null || cmt.user_info.avatar == ''" class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
          				<img v-if="cmt.user_info.avatar != null && cmt.user_info.avatar != ''" class="avatar" :src="url + '/cdn/avatar/small/'+ cmt.user_info.avatar">
					</td>
					<td class="text-left">

						<span>

						<b class="name" style="color:#588d3f;">@{{ cmt.user_info.name }} </b> &nbsp;

	                    <a v-if="cmt.cid != null" :href="'{{url('/backend/chat#')}}'+ cmt.cid" target="_blank">
	                        <i class="fa fa-comments" style="cursor: pointer; color: #00ab2e; font-size: 18px;" title="nhắn tin"></i>
	                    </a>
		                <i v-if="cmt.cid == null" :class="'fa fa-comments fa-comments-'+ cmt.user_id" style="cursor: pointer; font-size: 18px;" title="nhắn tin" v-on:click="initConversation(cmt.user_id)"></i>


						&nbsp; <i class="fa  fa-angle-right"></i> &nbsp;

						<a v-if="cmt.table_name == 'course'" :href="url+'/khoa-hoc/'+ cmt.table_info.SEOurl" target="_blank">@{{cmt.table_info.name}}</a>

						<b v-if="cmt.table_name == 'lesson'" style="text-transform: uppercase;">@{{cmt.table_info.course_url}}/</b>
						<a v-if="cmt.table_name == 'lesson' && (cmt.table_info.course_id == 39 || cmt.table_info.course_id == 40 || cmt.table_info.course_id == 44 || cmt.table_info.course_id == 45 || cmt.table_info.course_id == 46 || cmt.table_info.course_id == 47)" :href="url+'/khoa-hoc/'+ cmt.table_info.course_url +'/lesson/'+ cmt.table_info.id + '-' + cmt.table_info.SEOurl + '?ref=notice'" target="_blank">@{{cmt.table_info.name}}</a>
						<a v-if="cmt.table_name == 'lesson' && (cmt.table_info.course_id != 39 && cmt.table_info.course_id != 40 && cmt.table_info.course_id != 44 && cmt.table_info.course_id != 45 && cmt.table_info.course_id != 46 && cmt.table_info.course_id != 47)" :href="url+'/khoa-hoc/'+ cmt.table_info.course_url +'/'+ cmt.table_info.id + '-view'" target="_blank">@{{cmt.table_info.name}}</a>

						<a v-if="cmt.table_name == 'combo'" :href="url+'/khoa-hoc/combo/'+ cmt.table_info.id" target="_blank">@{{cmt.table_info.name}}</a>
						<a v-if="cmt.table_name == 'teacher'" :href="url+'/giao-vien/'+ cmt.table_info.SEOurl" target="_blank">@{{cmt.table_info.name}}</a>
						<a v-if="cmt.table_name == 'feedback'" :href="url+'/review'" target="_blank">Reviews ★★★☆☆</a>

						{{-- @if($item->table_name == 'lesson')
							<a href="{{url('/khoa-hoc/'.$item->table_info['course_url'].'/'.$item->table_info['id'].'-'.$item->table_info['SEOurl'])}}" target="_blank">{{$item->table_info['name']}}</a>
						@endif
						@if($item->table_name == 'teacher')
							<a href="{{url('/giao-vien/'.$item->table_info['SEOurl'])}}" target="_blank">{{$item->table_info['name']}}</a>
						@endif
						@if($item->table_name == 'combo')
							<a href="{{url('/khoa-hoc/combo/'.$item->table_info['id'])}}" target="_blank">{{$item->table_info['name']}}</a>
						@endif --}}

						</span>

	                    <br/>

                    		<span class="cmt-content" v-html="printInfo(cmt.content)"></span>
	                    	<p v-if="cmt.img != null" class="preview-image">
					            <a class="popup-preview-image" :href="url + '/cdn/comment/default/'+ cmt.img" data-fancybox>
					              <img class="preview-thumbnail" :src="url + '/cdn/comment/small/'+ cmt.img"/>
					            </a>
					        </p>


	                    <p class="comment-action">
				            <a v-if="cmt.replies.length != 0" class="load-more-reply" role="button" data-toggle="collapse" :id="'answer-reply-'+ cmt.id" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id">
				              <i class="fa fa-commenting"></i> @{{ cmt.replies.length }} phản hồi &nbsp;
				            </a>
				            <span v-if="cmt.replies.length == 0" class="answer" data-toggle="collapse" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id">
				              Trả lời •
				            </span>
				            <span class="time">@{{ cmt.time_created }}</span>
				        </p>

				        <div class="reply-container">

				            <div class="collapse force-show" :id="'reply-'+ cmt.id">
				              <div class="child-comment-item" v-for="(childCmt, index) in cmt.replies" :id="'reply-item-'+ childCmt.id">
				                <a v-if="childCmt.user_id == 0" class="pull-left avatar-container">
				                  <img class="avatar" :src="url + '/assets/img/oglogo.png'">
				                  <i class="zmdi zmdi-check-circle"></i>
				                </a>
				                <a v-if="childCmt.user_id != 0" class="pull-left avatar-container" data-fancybox data-src="#popup-user-info" href="javascript:;"
				                					data-options='{"touch" : false}' v-on:click="previewUser(childCmt.user_info.userId, childCmt.user_info.name, childCmt.user_info.email)">
				                  <img v-if="childCmt.user_info.avatar == null || childCmt.user_info.avatar == ''" class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
				                  <img v-if="childCmt.user_info.avatar != null && childCmt.user_info.avatar != ''" class="avatar" :src="url + '/cdn/avatar/small/'+ childCmt.user_info.avatar">
				                </a>
				                <div class="comment-content">
				                	<span class="delete-comment" v-on:click="delReply(childCmt.id)"><i class="fa fa-trash"></i> xóa</span>
				                	<span class="admin-edit-pen" v-if="childCmt.user_id == 0" data-fancybox data-src="#edit-cmt-popup"
                					  href="javascript:;" v-on:click="editAdminComment(childCmt.id, childCmt.content)"><i class="fa fa-pencil"></i> sửa</span>

					                <p class="child-name">
					                    <b class="red" v-if="childCmt.user_id == 0">Dũng Mori
					                    	@if(Auth::guard('admin')->user()->permission == 1)
					                    		~@{{ viewAdminName(childCmt.admin_log) }}~
					                    	@endif
					                    </b>
					                    <b class="red" v-if="childCmt.user_id != 0">
					                      @{{childCmt.user_info.name}}
					                    </b>
					                    <span :id="'child-comment-content-'+ childCmt.id" v-html="printInfo(childCmt.content)"></span>

					                </p>
				                  	<div v-if="childCmt.img != null" class="preview-image">
					                    <a class="popup-preview-image" :href="url + '/cdn/comment/default/'+ childCmt.img" data-fancybox>
					                      <img class="preview-thumbnail" :src="url + '/cdn/comment/small/'+ childCmt.img"/>
					                    </a>
				                  	</div>
				                    <span class="child-comment-action">
					                    <span class="time">@{{childCmt.time_created}}</span>
					                    <span style="color: #f06;" v-if="childCmt.user_id == 0 && childCmt.updated_at != childCmt.created_at"> &nbsp; Đã sửa</span>
					                    @if(Auth::guard('admin')->user()->permission == 1)
					                    <span title="chỉnh sửa" style="cursor: pointer;" v-if="childCmt.user_id == 0 && childCmt.updated_at != childCmt.created_at && childCmt.admin_log != null" data-fancybox data-src="#show-cmt-log" href="javascript:;" v-on:click="viewAdminLog(childCmt.admin_log)"> &nbsp; <i class="fa fa-history"></i></span>
					                    @endif
					                </span>

				                </div>


				              </div>

				              <div class="reply-form">
				                <img class="me-avatar" :src="url + '/assets/img/oglogo.png'">
				                <textarea data-emoji-picker="true" class="input-comment" :id="'reply-input-content-'+ cmt.id" rows="1" placeholder="comment..."></textarea>
				                <span class="pick-image" data-toggle="tooltip" data-placement="bottom" title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif">
				                    <i class="fa fa-camera"></i>
				                    <form class="form-pick-image" :id="'cmt-attachment-form-'+ cmt.id">
				                        <input type='file' :id="'commentImagePicked'+ cmt.id" name="commentImagePicked" v-on:change="previewImageReply" accept=".png, .jpg, .jpeg, .gif"/>
				                    </form>
				                </span>
				                <span class="post-comment-btn" v-on:click="postNewAnswer(cmt.id)">Trả lời</span>
				                <div class="preview-image" :id="'preview-image-cmt-'+ cmt.id"></div>
				              </div>

				         </div>
				        </div>
	                </td>
					<td class="text-right" width="200">
						<p style="margin-bottom: 2px;">
	                    	<span v-if="cmt.readed == 0" class="label new-label orange-label">Chưa xem</span>
	                     	<span v-if="cmt.readed != 0" class="label new-label green-label">Đã xem</span>
	                    </p>
	                    {{-- <span v-if="cmt.readed == 1" style="color: #000; cursor: pointer;" v-on:click="markAsRead(cmt.id)">chưa xem</span> --}}
                     	<span v-if="cmt.readed == 0" style="color: #000; cursor: pointer;" v-on:click="markAsRead(cmt.id)"><i class="fa fa-eye"></i> xem</span>
						<span style="color: #000; cursor: pointer;" v-on:click="pinComment(cmt)" ><i class="fa fa-thumb-tack" v-bind:class="{'text-danger': cmt.pin}"></i> @{{ cmt.pin ? 'bỏ ghim' : 'ghim'}}</span>
						<span style="color: #000; cursor: pointer;" v-on:click="delComment(cmt.id)"><i class="fa fa-trash"></i> xóa</span>
	                </td>
				</tr>

		    </tbody>
		</table>

		<div class="popup-user-info" id="popup-user-info">
			<h4>Thông tin người dùng</h4>
			<p>ID:  <b>@{{previewId}}</b></p>
			<p>Name:  <b>@{{previewName}}</b></p>
			<p>Email:  <b>@{{previewEmail}}</b></p>
		</div>

		<div class="popup-user-info" id="show-cmt-log">
			<h4>Admin đã sửa</h4>
			<p v-for="i in adminLog">@{{i.admin}} - @{{i.date}} - @{{i.content}}</b></p>
		</div>

		<form id="edit-cmt-popup" class="edit-cmt-popup" action="" method="post">
	        <h4>Sửa nội dung</h4>
	        <input type="hidden" id="edit-comment-id"/>
	        <p><textarea data-emoji-picker="true" class="edit-comment-area form-control" id='edit-comment-area' placeholder="comment..."></textarea></p>
	        <p class="mb-0 text-right">
	            <span class="edit-comment-btn-save" v-on:click="saveAdminComment()">Lưu</span>
	            <span class="edit-comment-btn-cancel" v-on:click="cancelAdminComment()">Hủy</span>
	        </p>
	    </form>

		<ul class="pagination"> {{ $comments->links('vendor.pagination.cmt-pagination') }} </ul>

    </div>
</div>
<style>
	.daterangepicker .ranges{
		display: none;
	}
	.force-show {
		visibility: unset;
	}
</style>
@stop

@section('footer')
	<script type="text/javascript">
		var comments = {!! json_encode($comments) !!};
		var totalResults = comments.total;
	</script>
	<script src="{{ asset('/plugin/vuejs-paginate/vuejs-paginate.js') }}"></script>
    <script src="{{asset('plugin/vanilla-emoji-picker/emojiPicker.js')}}"></script>
    <script src="{{asset('plugin/autosize/autosize.js')}}"></script>
	<script src="{{asset('assets/backend/js/comment.js')}}?{{filemtime('assets/backend/js/comment.js')}}"></script>
	<script src="{{asset('plugin/fancybox/dist/jquery.fancybox.min.js')}}"></script>
	<link href="{{asset('plugin/fancybox/dist/jquery.fancybox.min.css')}}" rel="stylesheet" type="text/css">
	<script src="{{ asset('plugin/moment/moment.js') }}"></script>
	<script src="{{ asset('plugin/timepicker/bootstrap-timepicker.js') }}"></script>
	<script src="{{ asset('plugin/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
	<script src="{{ asset('plugin/bootstrap-daterangepicker/daterangepicker.js') }}"></script>
	<script>
		$('.input-daterange-datepicker').daterangepicker({
			format: 'DD/MM/YYYY',
			buttonClasses: ['btn', 'btn-sm'],
			applyClass: 'btn-default',
			cancelClass: 'btn-white',
		});
	</script>
@stop
