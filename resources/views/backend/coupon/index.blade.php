@extends('backend._default.dashboard')

@section('description') Quản lý kết quả JLPT @stop
@section('keywords') jlpt result @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý kết quả JLPT @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
@stop

@section('content')
    <div class="filterable-list__screen" id="coupon__screen">
        <div class="filterable-list__filter">
            <div class="form-group">
                <label>Mã</label>
                <input type="text" class="form-control" placeholder="Tìm theo mã giảm giá" v-model="filter.code" @keyup.enter="applyFilter">
            </div>
        </div>
        <div style="display: flex; flex-flow: row; justify-content: space-between; align-items: center">
            <span>Tìm thấy <b>@{{  loading ? '----' : total_result }}</b> kết quả</span>
            <div>
                <!-- Nơi này để chứa các button thao tác -->
                <span style="margin-right: 10px; cursor: pointer" @click="resetFilter"><i class="fa fa-refresh"></i></span>
                <button class="btn btn-info" @click="applyFilter">Lọc</button>
                {{--                <button class="btn btn-success" @click="exportExcel" style="margin-left: 10px;">Xuất Excel</button>--}}
            </div>
        </div>
        <div class="filterable-list__list">
            <table>
                <thead>
                <th width="4%">CODE</th>
                <th>Email</th>
                <th>Loại</th>
                <th>Còn lại</th>
                <th>Kích hoạt bởi</th>
                <th>Hành động</th>
                </thead>
                <tbody v-if="!loading">
                <tr v-if="results.length == 0">
                    <td colspan="12" class="text-center"> Không có dữ liệu</td>
                </tr>
                <tr v-for="(coupon, index) in results" :key="coupon.id" v-if="results.length != 0">
                    <td>
                        <div>@{{ coupon.code }}</div>
                    </td>
                    <td>
                        <div>@{{ coupon.email }}</div>
                    </td>
                    <td>
                        <div>Giảm @{{ coupon.value }}@{{ coupon.type == 'percentage' ? '%' : 'đ' }}</div>
                    </td>
                    <td>
                        <div>@{{ coupon.use_left }} lượt</div>
                    </td>
                    <td>
                        <div>@{{ coupon.admin_id ? coupon.admin.name : coupon.user_id ? coupon.user.name : '--' }}</div>
                    </td>
                    <td>
                        <span v-if="coupon.use_left > 0" class="edit-modal label label-primary" @click="checkUsed(coupon)" style="padding: 4px 6px 4px 8px; margin: 0 4px 3px 0; float: left; cursor: pointer; border-radius: 3px;">
                            Đánh dấu đã dùng
                        </span>
                        <span v-else class="edit-modal label label-default" style="padding: 4px 6px 4px 8px; margin: 0 4px 3px 0; float: left; cursor: pointer; border-radius: 3px;">
                            <i class="fa fa-check" ></i> Đã dùng
                        </span>
                    </td>
                </tr>
                </tbody>
                <tbody v-if="loading">
                <tr style="height: 60vh; padding: 20px auto; text-align: center" >
                    <td colspan="12"><i class="fa fa-spinner fa-spin fa-3x"></i></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="filterable-list__paginate">
            <div>
                Hiển thị
                <select v-model="filter.per_page" @change="applyFilter">
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                    <option value="1000">1000</option>
                    <option value="2000">2000</option>
                </select>
                trong số @{{ loading ? '----' : total_result}} kết quả
            </div>
            <paginate
                    {{--                    v-model="filter.page"--}}
                    :page-count="filter.total_page"
                    :page-range="4"
                    :margin-pages="3"
                    :click-handler="changePage"
                    :prev-text="'&laquo;'"
                    :next-text="'&raquo;'"
                    :container-class="'pagination'"
                    :page-class="'page-item'"
                    :force-page="filter.page - 1"
            >
            </paginate>
        </div>
    </div>
    <script type="text/javascript">
      jQuery.browser = {};
      (function () {
        jQuery.browser.msie = false;
        jQuery.browser.version = 0;
        if (navigator.userAgent.match(/MSIE ([0-9]+)\./)) {
          jQuery.browser.msie = true;
          jQuery.browser.version = RegExp.$1;
        }
      })();
    </script>
    {{--    Local plugins--}}
    <script src="{{asset('plugin/vuejs-paginate/vuejs-paginate.js')}}"></script>
    <script src="{{asset('plugin/vue-router/vue-router.js')}}"></script>
    <script src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}"></script>
    <script src="{{asset('assets/backend/js/coupon.js')}}?{{filemtime('assets/backend/js/coupon.js')}}"></script>
@stop
