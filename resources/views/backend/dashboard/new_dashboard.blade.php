@extends('backend._default.dashboard')

@section('description') Bảng đi<PERSON><PERSON> k<PERSON>ển @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | Thống kê @stop

@section('assets')
    <script type="text/javascript" src="{{asset('assets/backend/js/jquery.canvasjs.min.js')}}"></script>
    <script src="{{asset('assets/backend/js/canvasjs.min.js')}}"></script>
    <link media="all" type="text/css" rel="stylesheet" href="{{ asset('assets/backend/css/new_dashboard.css') }}?{{filemtime('assets/backend/css/new_dashboard.css')}}">
    <style>
        .bg-title {
            border: none;
        }
    </style>
@stop
@section('content')
    <div class="grid grid-cols-12 gap-3" id="new-dashboard">
        <div class="col-span-12" data-toggle="modal" data-target="#genderByCourseModal">
            <h3 class="text-base font-semibold leading-6 text-gray-900">Giới tính</h3>
            <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                <div class="px-4 py-5 sm:p-6" v-for="gender in totalGender">
                    <dt class="text-base font-normal text-gray-900">@{{gender.label}}</dt>
                    <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                        <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                            @{{gender.y}}
                        </div>
                    </dd>
                </div>
            </dl>
        </div>
        <div class="col-span-6">
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Tỷ lệ phản hồi bình luận</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Bình luận học viên gửi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ receivedComments.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Bình luận phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ repliedComments.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tỷ lệ phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ Math.round(repliedComments.length * 100 / receivedComments.length) || '--' }}%
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Thời gian phản hồi bình luận</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tổng thời gian</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ totalReplyCommentTime.toFixed(1) }}p
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Trung bình / bình luận</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ repliedComments.length ? (totalReplyCommentTime / repliedComments.length).toFixed(2): 0 }}p
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Thời gian phản hồi lâu nhất</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ longestReplyCommentTime.toFixed(1) }}p
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        <div class="col-span-6">
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Tỷ lệ phản hồi hôm nay</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn nhận được</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ receivedMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn đã phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ repliedMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tỷ lệ phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ Math.round(repliedMessages.length * 100 / receivedMessages.length) || '--' }}%
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Tỷ lệ học viên phản hồi</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn đã gửi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ sentAdminMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn đã phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ repliedAdminMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tỷ lệ phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ Math.round(repliedAdminMessages.length * 100 / sentAdminMessages.length) || '--' }}%
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Thời gian phản hồi học viên</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tổng thời gian</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ totalReplyTime.toFixed(1) }}p
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Trung bình / tin nhắn</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ userAdminMessagePairs.length ? (totalReplyTime / userAdminMessagePairs.length).toFixed(2): 0 }}p
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Thời gian phản hồi lâu nhất</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ longestReplyTime.toFixed(1) }}p
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>

            <!-- frontend -->
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Tỷ lệ phản hồi hôm nay (ngoài web)</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn nhận được</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ frontendReceivedMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn đã phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ frontendRepliedMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tỷ lệ phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ Math.round(frontendRepliedMessages.length * 100 / frontendReceivedMessages.length) || '--' }}%
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Tỷ lệ học viên phản hồi (ngoài web)</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn đã gửi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ frontendSentAdminMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tin nhắn đã phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ frontendRepliedAdminMessages.length }}
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tỷ lệ phản hồi</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ Math.round(frontendRepliedAdminMessages.length * 100 / frontendSentAdminMessages.length) || '--' }}%
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
            <div>
                <h3 class="text-base font-semibold leading-6 text-gray-900">Thời gian phản hồi học viên (ngoài web)</h3>
                <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Tổng thời gian</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ frontendTotalReplyTime.toFixed(1) }}p
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Trung bình / tin nhắn</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ frontendUserAdminMessagePairs.length ? (frontendTotalReplyTime / frontendUserAdminMessagePairs.length).toFixed(2): 0 }}p
                            </div>
                        </dd>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <dt class="text-base font-normal text-gray-900">Thời gian phản hồi lâu nhất</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                @{{ frontendLongestReplyTime.toFixed(1) }}p
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Modal -->
        <div class="modal fade" id="genderByCourseModal" tabindex="-1" role="dialog" aria-labelledby="genderByCourseModalLabel" aria-hidden="true">
            <div class="modal-dialog" style="width: 95vmax;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="genderByCourseModalLabel">Thống kê giới tính theo khoá học</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" style="padding: 30px">
                        <div class="new__dashboard--gender">
                            <div class="bg-white" v-for="course in genderByCourse">
                                <h3 class="text-center">@{{ course.name }}</h3>
                                <div class="new__dashboard--gender-by-course" v-bind:style="{gridTemplateColumns: 'repeat('+ course.genders.length +', 1fr)'}">
                                    <div class="text-center" v-for="gender in course.genders">
                                        <div style="margin-bottom: 20px">
                                            <h2 class="m-0 counter font-600" v-bind:class="{'text-success': gender.name == 'Nam', 'text-danger': gender.name == 'Nữ'}">@{{gender.y}}</h2>
                                            <div class="text-muted m-t-5">@{{gender.name}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    <script src="{{asset('/plugin/jquery/lodash.min.js')}}"></script>
    <script src="{{asset('plugin/moment/moment.min.js')}}?{{filemtime('plugin/moment/moment.min.js')}}" type="text/javascript">
        moment.locale('vi')
    </script>
    <script src="{{asset('assets/backend/js/new_dashboard.js')}}?{{filemtime('assets/backend/js/new_dashboard.js')}}"></script>
@stop
