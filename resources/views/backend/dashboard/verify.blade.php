@extends('backend._default.dashboard')

@section('description') Bảng điều khiển @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | Xét duyệt @stop

@section('assets')
    <style>
        #verify-notification {
            position: absolute;
            top: 40%;
            width: 100%;
        }
    </style>
    <script type="text/javascript" src="{{asset('assets/backend/js/jquery.canvasjs.min.js')}}"></script>
    <script src="{{asset('assets/backend/js/canvasjs.min.js')}}"></script>
    <script src="{{asset('plugin/clientjs/client.min.js')}}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.js"></script>
@stop

@section('content')
    <div class="row h-100" id="verify-notification">
        <div class="alert alert-warning col-md-4 col-md-offset-4" role="alert" align="center" v-if="message === 'failed'">
         <p>Bạn đang đăng nhập trên một máy tính khác hoặc địa chỉ ip không đáng tin cậy.</p>
            <hr>
            <p class="mb-0">Bạn có thể liên hệ hoặc chờ người quản lý xét duyệt để đăng nhập trên thiết bị này.</p>
        </div>
    </div>

    <script>
        new Vue({
            el: '#verify-notification',

            data: {
                message: 'failed'
            },

            created() {
                this.checkVerify();
                setInterval(this.checkVerify, 5000);
            },

            methods: {
                checkVerify() {
                    var client = new ClientJS();
                    $.getJSON('https://jsonip.com/?callback=?', (response) => {
                        $.ajax({
                            url: window.location.origin + "/backend/verify/check",
                            type: "POST",
                            async: true,
                            data: {
                                "_token": "{{ csrf_token() }}",
                                fingerprint: client.getFingerprint(),
                                ip: response.ip
                            },
                            error: function (error) {
                                if (error.status == 500) {
                                    $.cookie('verifyDenied', 'Bạn đã bị từ chối đăng nhập bởi quản trị viên!');
                                    window.location.reload();
                                }
                                return false;
                            },
                            success: (response) => {
                                if (response.message === 'ok' || response.message === 'autologout') {
                                    window.location.reload();
                                }
                            }
                        });
                    });
                }
            }
        });
    </script>
@stop