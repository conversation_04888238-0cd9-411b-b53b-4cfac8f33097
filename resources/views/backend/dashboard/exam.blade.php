@extends('backend._default.dashboard')

@section('description') Bảng điều khiển @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | Thống kê @stop

@section('assets')
    <script type="text/javascript" src="{{asset('assets/backend/js/jquery.canvasjs.min.js')}}"></script>
    <script src="{{asset('assets/backend/js/canvasjs.min.js')}}"></script>
@stop

@section('content') 
    <div class="row bg-title">
        <h4 class="page-title pull-left">
           Thống kê lượng học viên thi trực tuyến
        </h4> 
    </div>

    {{-- biểu đồ thống kê thi thử --}}
    <div class="stackedChart">
        <div class="year" style="text-align: right; margin-bottom: 18px;">
            <select id="year-exam" onchange="changeYearExam()">
                @for($i=2017;$i<=2027;$i++)
                    <option value="{{$i}}" {{$i == date('Y') ? 'selected' : ''}}>Năm {{$i}}</option>
                @endfor
            </select>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6 col-sm-6 m-t-15">
            <div id="stackedColumnChartN5" style="height: 370px; width: 100%;"></div>
        </div>

        <div class="col-md-6 col-sm-6 m-t-15">
            <div id="stackedColumnChartN4" style="height: 370px; width: 100%;"></div>
        </div>

        <div class="col-md-6 col-sm-6 m-t-15">
            <div id="stackedColumnChartN3" style="height: 370px; width: 100%;"></div>
        </div>

        <div class="col-md-6 col-sm-6 m-t-15">
            <div id="stackedColumnChartN2" style="height: 370px; width: 100%;"></div>
        </div>

        <div class="col-md-6 col-sm-6 m-t-15">
            <div id="stackedColumnChartN1" style="height: 370px; width: 100%;"></div>
        </div>
    </div>
    <script type="text/javascript">

        //ve column chart thong ke so luong nguoi dang ki theo thang
        var userExamOnline = <?php echo $userExamOnline; ?>;
        //thống kê lượng người thi hàng tháng theo năm
        drawStackedChart('N5', userExamOnline.N5, 'stackedColumnChartN5');
        drawStackedChart('N4', userExamOnline.N4, 'stackedColumnChartN4');
        drawStackedChart('N3', userExamOnline.N3, 'stackedColumnChartN3');
        drawStackedChart('N2', userExamOnline.N2, 'stackedColumnChartN2');
        drawStackedChart('N1', userExamOnline.N1, 'stackedColumnChartN1');
        
        //vẽ biểu đồ thống kê số lượng học viên thi thử
        //Thay đổi theo năm thống kê lượng người thi hàng tháng theo năm
        function changeYearExam(){
            $.ajax({
                type: 'get',
                url: '/backend/dashboard/examOnline/' + $('#year-exam').val(),
                success: function(data) {
                    drawStackedChart('N5', data.N5, 'stackedColumnChartN5');
                    drawStackedChart('N4', data.N4, 'stackedColumnChartN4');
                    drawStackedChart('N3', data.N3, 'stackedColumnChartN3');
                    drawStackedChart('N2', data.N2, 'stackedColumnChartN2');
                    drawStackedChart('N1', data.N1, 'stackedColumnChartN1');
                }
            });
        }

        function drawStackedChart($course, $allData, $chartId){

            var chart = new CanvasJS.Chart($chartId, {
                animationEnabled: true,
                title:{
                    text: $course,
                    fontFamily: "arial black",
                    fontColor: "#695A42"
                },
                axisX: {
                    interval: 1,
                    intervalType: "month"
                },
                axisY:{
                    valueFormatString:"#0",
                    gridColor: "#B6B1A8",
                    tickColor: "#B6B1A8"
                },
                toolTip: {
                    shared: true,
                    content: toolTipContent
                },
                data: [{
                    type: "stackedColumn",
                    showInLegend: true,
                    color: "#024fcc",
                    name: "Pass",
                    dataPoints: $allData.pass
                    },
                    {        
                        type: "stackedColumn",
                        showInLegend: true,
                        name: "Not Pass",
                        color: "#c1016e",
                        dataPoints: $allData.notpass
                    }]
            });
            chart.render();
        }
        function toolTipContent(e) {
            var str = "";
            var total = 0;
            var str2, str3;
            for (var i = 0; i < e.entries.length; i++){
                var  str1 = "<span style= \"color:"+e.entries[i].dataSeries.color + "\"> "+e.entries[i].dataSeries.name+"</span>: <strong>"+e.entries[i].dataPoint.y+"</strong><br/>";
                total = e.entries[i].dataPoint.y + total;
                str = str.concat(str1);
            }
            str2 = "<span style = \"color:DodgerBlue;\"><strong>"+'Số liệu'+"</strong></span><br/>";
            total = Math.round(total * 100) / 100;
            str3 = "<span style = \"color:Tomato\">Total:</span><strong> "+total+"</strong><br/>";
            return (str2.concat(str)).concat(str3);
        }
    </script>
@stop