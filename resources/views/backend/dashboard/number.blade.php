<?php
$i = 0;
$color = ['#40af4c','#d43508','#0424ae','#b04d8a','#fe6570','#990033'];
?>
<div class="row">
    <div class="col-md-12">
        <div class="lineChart">
            <div class="title">
                <h3>Thống kê toàn thời gian</h3>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-sm-6 m-t-15">
        <div class="bg-white">
            <h3 class="text-center">Giới tính</h3>
            <div class="row">
                @if(!empty($data['gender']))
                    @foreach($data['gender'] as $key => $item)
                        <div class="col-md-4 col-sm-4  col-xs-4 text-center">
                            <div class="bg-white widget-panel">
                                <h2 class="m-0  counter font-600" style="color:{{$color[$key]}}">{{$item['y']}}</h2>
                                <div class="text-muted m-t-5">{{$item['label']}}</div>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
    <div class="col-md-6 col-sm-6 m-t-15">
        <div class="bg-white">
            <h3 class="text-center">Quốc gia</h3>
            <div class="row">
                @if(!empty($data['country']))
                    @foreach($data['country'] as $key => $item)
                        <div class="col-md-4 col-sm-4  col-xs-4 text-center">
                            <div class="bg-white widget-panel">
                                <h2 class="m-0 counter font-600" style="color:{{$color[$key]}}">{{$item['y']}}</h2>
                                <div class="text-muted m-t-5">{{$item['label']}}</div>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
    <div class="col-md-6 col-sm-6 m-t-15">
        <div class="bg-white">
            <h3 class="text-center">Trình độ</h3>

            <div class="row">

                @if(!empty($data['level']))
                    @foreach($data['level'] as $key => $item)
                        <div class="col-md-4 col-sm-4 col-xs-6 text-center">
                            <div class="bg-white widget-panel">
                                <h2 class="m-0 counter font-600" style="color:{{$color[$key]}}">{{$item['y']}}</h2>
                                <div class="text-muted m-t-5">{{$item['label']}}</div>
                            </div>
                        </div>
                        <?php
                            $i += $item['y'];
                            ?>
                    @endforeach
                        <div class="col-md-4 col-sm-4 col-xs-6 text-center">
                            <div class="bg-white widget-panel">
                                <h2 class="m-0 counter font-600" style="color:#006600">{{$user - $i}}</h2>
                                <div class="text-muted m-t-5">Không</div>
                            </div>
                        </div>
                @endif
            </div>
        </div>
    </div>
    <div class="col-md-6 col-sm-6 m-t-15">
        <div class="bg-white">
            <h3 class="text-center">Tuổi</h3>
            <div class="row">
                <?php
                    $j = 0;
                ?>
                @if(!empty($data['age']))
                    @foreach($data['age'] as $key => $item)
                        <div class="col-md-3 col-sm-4 col-xs-6 text-center">
                            <div class="bg-white widget-panel">
                                <h2 class="m-0  counter font-600" style="color:{{$color[$key]}}">{{$item['y']}}</h2>
                                <div class="text-muted m-t-5">{{$item['label']}}</div>
                            </div>
                        </div>
                        <?php $j += $item['y'] ?>
                    @endforeach
                        <div class="col-md-3 col-sm-4 col-xs-6 text-center">
                            <div class="bg-white widget-panel">
                                <h2 class="m-0 counter font-600" style="color:#006600" >{{$user - $j}}</h2>
                                <div class="text-muted m-t-5">Khác</div>
                            </div>
                        </div>
                @endif
            </div>
        </div>
    </div>
</div>