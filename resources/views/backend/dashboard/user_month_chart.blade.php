{{-- thống kê lượng người đăng kí theo tháng --}}

<div class="col-md-12">
    <div class="colChart">
        <div class="title">
            <h3>Thống kê lượng người dùng đăng kí theo tháng</h3>
        </div>
    </div>
    <div class="year" style="text-align: right; margin-bottom: 18px;">
        <select id="year" onchange="changeYear()">
            @for($i=2017;$i<=2027;$i++)
                <option value="{{$i}}" {{$i == date('Y') ? 'selected' : ''}}>Năm {{$i}}</option>
            @endfor
        </select>
    </div>
    <div id="columnChart" style="height: 370px; width: 100%;"></div>
</div>
<script>
    //thong ke theo năm
    var register = <?php echo $register; ?>;

    countUser(register);
    //hàm vẽ biểu đồ số lượng học viên đăng kí
    function countUser(data){
        var options = {
            title: {
                text: ""
            },
            data: [{
                type: "column",
                dataPoints: data,
            }]
        };

        $("#columnChart").CanvasJSChart(options);
    }

    //Thay đổi theo năm cho thống kê lượng người đăng kí
    function changeYear(){
        $.ajax({
            type: 'get',
            url: '/backend/dashboard/register/' + $('#year').val(),
            success: function(data) {
                countUser(data);
            }
        });
    }
</script>