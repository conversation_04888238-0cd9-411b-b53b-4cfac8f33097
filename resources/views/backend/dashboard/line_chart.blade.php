<div class="row">
   <div class="col-md-12">
       <div class="col-md-6">
           <div class="lineChart">
               <div class="title">
                   <h3>Th<PERSON>ng kê theo từng năm</h3>
               </div>
           </div>
       </div>
       <div class="col-md-6">
           <div class="year">
               <select id="year-line-chart" onchange="changeYearForLineChart()">
                   @for($i=2017;$i<=2027;$i++)
                       <option value="{{$i}}" {{$i == date('Y') ? 'selected' : ''}}>Năm {{$i}}</option>
                   @endfor
               </select>
           </div>
       </div>
   </div>
    <div class="col-md-6 m-t-15 ">
        <div class="bg-white">
            <h3 class="text-center p-10" id="gender-title">Giới tính</h3>
            <div class="row">
                <div class="col-md-12">

                </div>
                <div class="col-md-12">
                    <div id="lineChartSex" class="line-chart"></div>

                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 m-t-15">
        <div class="bg-white">
            <h3 class="text-center p-10" id="gender-title">Quốc gia</h3>
            <div class="row">
                <div class="col-md-12">

                </div>
                <div class="col-md-12">
                    <div id="lineChartCountry" class="line-chart"></div>

                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 m-t-15">
        <div class="bg-white">
            <h3 class="text-center p-10" id="gender-title">Trình độ</h3>
            <div class="row">
                <div class="col-md-12">

                </div>
                <div class="col-md-12">
                    <div id="lineChartLevel" class="line-chart"></div>

                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 m-t-15">
        <div class="bg-white">
            <h3 class="text-center p-10" id="gender-title">Tuổi</h3>
            <div class="row">
                <div class="col-md-12">

                </div>
                <div class="col-md-12">
                    <div id="lineChartAge" class="line-chart"></div>

                </div>
            </div>
        </div>
    </div>
    <script>
        // ve bieu do thong ke gioi tinh, quoc gia, level va tuoi
        setTimeout(function () {
            $.ajax({
                type: 'get',
                url: '/backend/dashboard/line-chart/' + $('#year-line-chart').val(),
                success: function(data) {
                    drawLineChart('lineChartSex','Giới tính', 'gender', data.gender);
                    drawLineChart('lineChartCountry','Quốc gia', 'country', data.country);
                    drawLineChart('lineChartLevel','Trình độ', 'japanese_level', data.level);
                    drawLineChart('lineChartAge','Tuổi', 'year', data.age);
                }
            });
        }, 2000);

        function changeYearForLineChart(){
            $.ajax({
                type: 'get',
                url: '/backend/dashboard/line-chart/' + $('#year-line-chart').val(),
                success: function(data) {
                    drawLineChart('lineChartSex','Giới tính', 'gender', data.gender);
                    drawLineChart('lineChartCountry','Quốc gia', 'country', data.country);
                    drawLineChart('lineChartLevel','Trình độ', 'japanese_level', data.level);
                    drawLineChart('lineChartAge','Tuổi', 'year', data.age);
                }
            });
        }
        //Thống kế giới tính, quốc gia, trình độ tiếng nhật và tuổi của học viên
        function drawLineChart(id, name, column, data){
            var chart = new CanvasJS.Chart(id, {
                animationEnabled: true,
                theme: "light2",
                title:{
                    text: ''
                },
                axisX:{
                    valueFormatString: "DD MMM",
                    crosshair: {
                        enabled: true,
                        snapToDataPoint: true
                    }
                },
                axisY: {
                    title: "",
                    crosshair: {
                        enabled: true
                    }
                },
                toolTip:{
                    shared:true
                },
                legend:{
                    cursor:"pointer",
                    verticalAlign: "top",
                    horizontalAlign: "left",
                    dockInsidePlotArea: false,
                    itemclick: toogleDataSeries
                },
                data: typeColumn(column, data)
            });
            chart.render();
        }
        function calculateData(data, listField){
            var listData = [];
            for(var i = 0; i < listField.length; i++){
                listData[i] = {
                    type: "line",
                    showInLegend: true,
                    name: listField[i],
                    markerType: "square",
                    dataPoints: data[i],
                }
            }
            return listData;
        }
        function typeColumn(column, data){
            switch(column){
                case 'gender':
                    return calculateData(data, ['Nam', 'Nữ']);
                    break;
                case 'year':
                    return calculateData(data, ['Từ 1 - 10', 'Từ 11 - 20', 'Từ 21 - 30', 'Từ 31 - 40', 'Từ 41 - 50', 'Từ 51 - 60']);
                    break;
                case 'japanese_level':
                    return calculateData(data, ['N1', 'N2', 'N3', 'N4', 'N5', 'Không']);
                    break;
                default:
                    return calculateData(data, ['Việt Nam', 'Nhật Bản', 'Khác']);
                    break;
            }
        }
        function toogleDataSeries(e){
            if (typeof(e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
                e.dataSeries.visible = false;
            } else{
                e.dataSeries.visible = true;
            }
            chart.render();
        }
    </script>
</div>