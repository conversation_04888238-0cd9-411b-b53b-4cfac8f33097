@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON> đ<PERSON>ển @stop
@section('keywords') dashboard @stop
@section('author') dungmori.com @stop
@section('title') Admin | <PERSON><PERSON><PERSON> duy<PERSON>t @stop

@section('assets')
    <script type="text/javascript" src="{{asset('assets/backend/js/jquery.canvasjs.min.js')}}"></script>
    <script src="{{asset('assets/backend/js/canvasjs.min.js')}}"></script>
    <script src="{{asset('plugin/clientjs/client.min.js')}}"></script>
@stop

@section('content')
    <div class="row bg-title">
        <h4 class="page-title pull-left">
            Kiểm duyệt tài khoản
        </h4>
    </div>
    <div id="normal-admin">
        <table v-if="normalAdmins.length" class="table table-bordered m-0">
            <thead>
            <tr>
                <th class="text-center"><PERSON><PERSON> số</th>
                <th class="text-center">Email</th>
                <th class="text-center">H<PERSON> tên</th>
                <th class="text-center">Hành động</th>
            </tr>
            </thead>
            <tbody>
                <tr v-for="(admin, index) in normalAdmins">
                    <th class="text-center" scope="row">@{{ index + 1 }}</th>
                    <td class="text-center">@{{ admin.email }}</td>
                    <td class="text-center">@{{ admin.name }}</td>
                    <td class="text-center">
                        <button @click="verifyAdmin(admin.id, 1)" class="btn btn-info" style="margin-right: 2%"><i class="fa fa-check"></i> Duyệt</button>
                        <button @click="verifyAdmin(admin.id, 0)" class="btn btn-danger"><i class="fa fa-remove"></i> Hủy</button>
                    </td>
                </tr>
            </tbody>
        </table>
        <div v-else class="alert alert-primary col-md-4 col-md-offset-4" role="alert" align="center">
            <p>Chưa có yêu cầu kiểm duyệt nào.</p>
        </div>
    </div>
    <script>
        new Vue({
            el: '#normal-admin',

            data: {
                normalAdmins: []
            },

            created() {
                this.getListNormalAdmins();
                setInterval(this.getListNormalAdmins, 4000);
            },

            methods: {
                verifyAdmin(id, isVerify) {
                    $.ajax({
                        url: window.location.origin + "/backend/dashboard/normal-admins/" + id + "/verify",
                        type: "POST",
                        async: true,
                        data: {
                            isVerify: isVerify,
                            "_token": "{{ csrf_token() }}",
                        },
                        error: function () {
                            return false;
                        },
                        success: function (response) {
                            this.message = response.message;
                            if (response.message == 'ok') {
                                window.location.reload();
                            }
                        }
                    });
                },

                getListNormalAdmins() {
                    $.ajax({
                        url: window.location.origin + "/backend/dashboard/normal-admins/list",
                        type: "GET",
                        async: true,
                        error: function () {
                            return false;
                        },
                        success: response => {
                            this.normalAdmins = response;
                        }
                    });
                }
            }
        });
    </script>
@stop