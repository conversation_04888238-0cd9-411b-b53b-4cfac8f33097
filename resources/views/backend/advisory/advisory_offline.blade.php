@extends('backend._default.dashboard')

@section('description') <PERSON><PERSON><PERSON> thô<PERSON> tin tư vấn của học viên @stop
@section('keywords') advisory @stop
@section('author') dungmori.com @stop
@section('title') Admin | Advisory offline @stop

@section('assets')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
@stop

@section('content') 
	<div class="row bg-title">
        <h4 class="page-title pull-left">
            Thông tin tư vấn khóa học của học viên
        </h4>
        <form class="form-inline" style="float: right;">
            {{ csrf_field() }}
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_from" id="date_from" placeholder="Từ ngày">
            <input type="text" class="form-control mb-2 mr-sm-2" name="date_to" id="date_to" placeholder="Đến ngày">
            <a type="button" class="search btn btn-info mb-2">Tìm kiếm</a>
            <button type="button" class="export btn btn-info mb-2">Xuất Excel</button>
        </form>
    </div>
    <div class="advisoryDetail">
        @include('backend.advisory.advisoryDetail')
    </div>

    <script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });	  
        });
        //datetime picker
        $('#date_from').datepicker({ 
            dateFormat: 'dd/mm/yy'
        });
        $('#date_to').datepicker({ 
            dateFormat: 'dd/mm/yy'
        });

            $(document).on('click', '.search', function() {
            $.ajax({
                type: 'get',
                url: '/backend/advisory/find',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'date_to': $('input[name=date_to]').val(),
                    'date_from': $('input[name=date_from]').val(),
                    'type': 'offline'
                },
                success: function(data) {
                    $('.advisoryDetail').empty().html(data);
                    $(document).on('click', '.advisoryDetail .pagination a', function(e) {
                        var date_from = $('#date_from').val();
                        var date_to = $('#date_to').val();
                        e.preventDefault();
                        var page = $(this).attr('href').split('page=')[1];
                        resultPage(page,'/backend/advisory/find/?page=', '.advisoryDetail', date_from, date_to);
                    });
                }
            });
        });

        function resultPage(page,link, div_id, date_from, date_to){
            $.ajax({
                type: 'get',
                url: link + page,
                cache: false,
                data: {
                    date_from : date_from,
                    date_to : date_to,
                }
            }).done(function(data){
                $(div_id).empty().html(data)
            })
        }

        $(document).on('click', '.export', function() {
            $.ajax({
                type: 'get',
                url: '/backend/advisory/export',
                data: {
                    '_token': $('input[name=_token]').val(),
                    'date_from': $('input[name=date_from]').val(),
                    'date_to': $('input[name=date_to]').val(),
                    'type': 'offline'
                },
                success: function(data) {
                    window.location = '/upload/excel/advisory.xls';
                }
            });
        });

    </script>
@stop