@extends('backend._default.dashboard')

@section('description')
    <PERSON><PERSON>ả<PERSON> lý lớp Offline
@stop
@section('keywords')
    Offline
@stop
@section('author')
    dungmori.com
@stop
@section('title')
    Admin | Quản lý lớp offline
@stop

@section('assets')
    <link media="all" type="text/css" rel="stylesheet"
          href="{{ asset('assets/backend/css/filterable_list.css') }}?{{filemtime('assets/backend/css/filterable_list.css')}}">
    <link media="all" type="text/css" rel="stylesheet"
          href="{{ asset('plugin/datepicker/vue2-datepicker.min.css') }}?{{filemtime('plugin/datepicker/vue2-datepicker.min.css')}}">
    <!-- import CSS -->
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script>
        ELEMENT.locale(ELEMENT.lang.vi)
    </script>
    <script src="{{asset('plugin/moment/moment.min.js')}}?{{filemtime('plugin/moment/moment.min.js')}}"
            type="text/javascript">
        moment.locale('vi')
    </script>
@stop

@section('content')
    <div id="offline-screen" class="offline-screen">
        <el-tabs type="border-card" v-model="tab">
            <el-tab-pane label="Danh sách lớp" name="list">
                <div>
                    <div class="bg-white w-full p-5 mt-5 rounded-md shadow">
                        <div class="grid grid-cols-8 gap-3 border-b pb-5">
                            <el-select v-model="filters.department" filterable
                                       :placeholder="loading.department ? 'Đang tải' :' Chọn cơ sở'"
                                       @change="getTeachers()"
                                       clearable>
                                <el-option
                                        v-for="item in departments"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-select v-model="filters.day_time" filterable
                                       :placeholder="loading.department ? 'Đang tải' : 'Chọn ca'" clearable>
                                <el-option
                                        v-for="item in times"
                                        :key="item.value"
                                        :label="`${item.period_code} ${item.period_detail}`"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                            <el-select v-model="filters.room" filterable
                                       :placeholder="loading.department ? 'Đang tải' : 'Chọn phòng'" clearable>
                                <el-option
                                        v-for="item in rooms"
                                        :key="item.id"
                                        :label="item.room_name"
                                        :value="item.id">
                                </el-option>
                            </el-select>
                            <el-select v-model="filters.teacher" filterable placeholder="Chọn giáo viên" clearable>
                                <el-option
                                        v-for="item in teachers"
                                        :key="item.id"
                                        :label="`${item.last_name} ${item.first_name}`"
                                        :value="item.id">
                                </el-option>
                            </el-select>
                            <el-select v-model="filters.level" filterable placeholder="Chọn trình độ" clearable>
                                <el-option
                                        v-for="item in levels"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                            <div class="col-span-2">
                                <el-date-picker
                                        v-model="filters.date_start"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        start-placeholder="Khai giảng từ"
                                        end-placeholder="Đến ngày"
                                        :picker-options="pickerOptions"
                                        style="width: 100%"
                                        format="dd/MM/yyyy"
                                        value-format="yyyy-MM-dd"
                                >
                                </el-date-picker>
                            </div>
                            <div class="col-span-2">
                                <el-date-picker
                                        v-model="filters.date_end"
                                        type="daterange"
                                        align="right"
                                        unlink-panels
                                        range-separator="-"
                                        start-placeholder="Kết thúc từ"
                                        end-placeholder="Đến ngày"
                                        :picker-options="pickerOptions"
                                        style="width: 100%"
                                        format="dd/MM/yyyy"
                                        value-format="yyyy-MM-dd"
                                >
                                </el-date-picker>
                            </div>
                        </div>
                        <div class="flex items-center justify-between py-3">
                            <button @click="getCoursesReset()" type="button"
                                    class="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                Lọc
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-12 mt-5 gap-3">
                        <div class="col-span-8 bg-white shadow rounded-md h-full p-5">
                            <div class="infinite-list-wrapper" style="overflow:auto">
                                <ul class="flex flex-col gap-3 relative" v-infinite-scroll="loadCourse"
                                    :infinite-scroll-disabled="loading_infinite"
                                    style="overflow:auto; max-height: 50vh;">
                                    <li class="px-3 pb-2 grid grid-cols-11 items-center justify-start gap-2 sticky top-0 bg-white shadow w-full">
                                        <div class="font-semibold col-span-1">ID</div>
                                        <div class="font-semibold col-span-1">Ca</div>
                                        <div class="font-semibold col-span-1">Phòng</div>
                                        <div class="font-semibold col-span-1">Giáo viên</div>
                                        <div class="font-semibold col-span-1">Sĩ số (@{{ totalStudentCount }})</div>
                                        <div class="font-semibold col-span-1">Sĩ số cuối (@{{ totalLastStudentCount }})
                                        </div>
                                        <div class="font-semibold col-span-1">Số buổi</div>
                                        <div class="font-semibold col-span-1 text-green-600">Bắt đầu</div>
                                        <div class="font-semibold col-span-1 text-rose-600">Kết thúc</div>
                                        <div class="font-semibold col-span-1">Trình độ</div>
                                        <div class="font-semibold col-span-1">Điểm danh</div>
                                    </li>
                                    <li
                                            v-for="(item, idx) in courses"
                                            :key="`course-${item.id}`"
                                            :id="`course-${item.id}`"
                                            class="p-3 grid grid-cols-11 items-center justify-start shadow gap-2 cursor-pointer hover:bg-gray-50"
                                            :class="[selectedCourse?.id === item.id ? 'bg-gray-200' : '']"
                                            @click="selectedCourse = item"
                                    >
                                        <div class="font-semibold col-span-1">@{{ item.id }}
                                        </div>
                                        <div class="font-semibold col-span-1">@{{ item.info?.periods[0]?.period_code }}
                                        </div>
                                        <div class="font-semibold col-span-1"> @{{ item.info?.room?.room_name }}</div>
                                        <div class="font-semibold col-span-1">@{{ item.teachers[0]?.first_name }}</div>
                                        <div class="font-semibold col-span-1">@{{ item.students?.length }}</div>
                                        <div class="font-semibold col-span-1">@{{ item.countedStudents }}</div>
                                        <div class="font-semibold col-span-1">@{{ item.course_times?.length }} / @{{
                                            item.total }}
                                        </div>
                                        <div class="font-semibold col-span-1 text-green-600">@{{ item.date_start |
                                            readableDate }}
                                        </div>
                                        <div class="font-semibold col-span-1 text-rose-600">@{{ item.date_end |
                                            readableDate
                                            }}
                                        </div>
                                        <div class="font-semibold col-span-1"> N@{{ 6 - item.level_of_n }}</div>
                                        <div class="font-semibold col-span-1 text-blue-600"
                                             @click.stop="openCourseTime(item)"> Xem
                                        </div>
                                    </li>
                                </ul>
                                <p v-if="loading.course" class="text-center font-semibold">Đang tải...</p>
                            </div>
                        </div>
                        <div class="col-span-4 bg-white shadow rounded-md h-full p-5">
                            <el-button
                                    type="primary"
                                    class="mb-3"
                                    @click="exportSelectedCourseStudents(selectedCourse)"
                                    :disabled="!selectedCourse">Xuất Excel</el-button>
                            <ul v-if="selectedCourse && selectedCourse.students?.length" class="flex flex-col gap-3">
                                <li class="p-3 grid grid-cols-8 items-center justify-start">
                                    <div class="font-semibold col-span-4">Học viên</div>
                                    <div class="font-semibold col-span-2">Trình độ</div>
                                    <div class="font-semibold col-span-2">Nợ học phí</div>
                                </li>
                                <li v-for="item in selectedCourse.students" :key="`student-${item.id}`"
                                    class="p-3 grid grid-cols-8 items-center justify-start shadow gap-2 cursor-pointer hover:bg-gray-50"
                                    :id="`student-${item.id}`" :style="item.is_debt == 1 ? 'background: #ffe5e5' : ''">
                                    <div class="font-semibold col-span-4 flex items-center gap-2">
                                        <el-avatar size="medium"
                                                   :src="`https://school.dungmori.com/cdn/${item.avatar}`"></el-avatar>
                                        <div>
                                            <div>
                                                @{{ item.last_name }} @{{ item.first_name }}
                                            </div>
                                            <div v-if="item.user_dmr != null && item.is_debt == 1" style="font-style: normal; font-weight: 100; font-size: small">
                                                @{{ item.user_dmr.id }} -  @{{ item.user_dmr.email }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="font-semibold col-span-2 flex items-center gap-2">
                                        @{{ item.course_owner || '--' }}
                                    </div>
                                    <div class="font-semibold col-span-2 flex items-center gap-2">
                                        <div>
                                            <div>
                                                @{{ item.is_debt == 1 ? 'Đang nợ' : '' }}
                                            </div>
                                            <div v-if="item.is_debt == 1" style="font-style: normal; font-weight: 100; font-size: small">
                                                @{{ item.debt_amount }}
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                            <div v-else class="flex justify-center text-center w-full">Không có học viên</div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="Công nợ" name="debt">
                <div>
                    <div class="p-2">
                        <button
                                type="button"
                                class="rounded bg-indigo-600 px-2 py-1 text-xs font-semibold text-white shadow-sm
                            hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2
                            focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                @click="exportDebt"
                        >
                            Xuất Excel
                        </button>
                    </div>
                    <el-table
                            :data="debts"
                            v-loading="loading.debt"
                            height="75vh"
                            style="width: 100%"
                    >
                        <el-table-column
                                type="index"
                                label="STT"
                                width="100"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="id"
                                label="Đơn hàng"
                                width="100"
                        >
                        </el-table-column>
                        <el-table-column
                                label="Thông tin"
                        >
                            <template slot-scope="scope">
                                <div>@{{ scope.row.info_contact?.name || '--' }}</div>
                                <div>
                                    <a class="text-blue-500"
                                       :href="`/backend/invoice?email=${scope.row.info_contact?.email}&status=completed&isVip=3&comboId=${scope.row.product_id}`"
                                       target="_blank">@{{ scope.row.info_contact?.email || '--' }}</a>
                                </div>
                                <div>@{{ scope.row.info_contact?.phone || '--' }}</div>
                                <div class="inline-flex items-center rounded-md bg-blue-100 px-1 py-0 text-xs font-medium text-blue-700 flex-wrap whitespace-wrap">
                                    @{{ scope.row.product_name || '--' }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                                label="Cơ sở"
                        >
                            <template slot-scope="scope">
                                <div>@{{ scope.row.department?.name || '--'}}</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                                label="Lớp tham gia"
                        >
                            <template slot-scope="scope">
                                <div v-for="group in scope.row.user?.offline_user?.courses" class="whitespace-wrap">
                                    <b v-for="teacher in group.teachers" :key="`teacher-${teacher.id}`">@{{
                                        teacher.last_name }} @{{ teacher.first_name }} | </b>
                                    <span><b>N@{{ 6 - group?.level_of_n }}</b> | </span>
                                    <span><b>@{{ group?.info?.room?.room_name }}</b> | </span>
                                    <span><b>@{{ group?.info?.periods?.map(o => o.period_code).join(',') }}</b> | </span>
                                    <span><b>@{{ group?.infos?.map(o => o.day_id + 2).join(',') }} | </b> </span>
                                    <span><b>@{{ group.date_start | readableDate }}</b> </span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                                label="Doanh thu dự kiến"
                        >
                            <template slot-scope="scope">
                                <b class="text-blue-600">
                                    @{{ new Intl.NumberFormat('vi-VN', { style: 'currency', currency:
                                    scope.row.currency.toUpperCase() }).format(scope.row.revenue) }}
                                </b>
                            </template>
                        </el-table-column>
                        <el-table-column
                                label="Chiết khấu"
                        >
                            <template slot-scope="scope">
                                <b class="text-black">
                                    @{{ new Intl.NumberFormat('vi-VN', { style: 'currency', currency:
                                    scope.row.currency.toUpperCase() }).format(scope.row.discount) }}
                                </b>
                            </template>
                        </el-table-column>
                        <el-table-column
                                label="Tổng thực thu"
                        >
                            <template slot-scope="scope">
                                <b class="text-green-600">
                                    @{{ new Intl.NumberFormat('vi-VN', { style: 'currency', currency:
                                    scope.row.currency.toUpperCase() }).format(scope.row.total_paid) }}
                                </b>
                            </template>
                        </el-table-column>
                        <el-table-column
                                label="Nợ"
                        >
                            <template slot-scope="scope">
                                <b class="text-red-600">
                                    @{{ new Intl.NumberFormat('vi-VN', { style: 'currency', currency:
                                    scope.row.currency.toUpperCase() }).format(scope.row.debt) }}
                                </b>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-tab-pane>
        </el-tabs>

        <el-drawer
                title="I am the title"
                :visible.sync="showCourseTime"
                :with-header="false">
            <div class="col-span-3 bg-white shadow rounded-md h-full p-5">
                <ul class="flex flex-col gap-3" v-if="showCourseTime">
                    <li class="p-3 grid grid-cols-5 items-center justify-start gap-2">
                        <div class="font-semibold col-span-1">Buổi</div>
                        <div class="font-semibold col-span-1">Ngày</div>
                        <div class="font-semibold col-span-1 text-green-600">Đi</div>
                        <div class="font-semibold col-span-1 text-rose-600">Nghỉ</div>
                        <div class="font-semibold col-span-1">Tỷ lệ (@{{ averagePresentPercent }}%)</div>
                    </li>
                    <li
                            v-for="item in current_course_times"
                            :key="`course_time-${item.id}`"
                            :id="`course_time-${item.id}`"
                            class="p-3 grid grid-cols-5 items-center justify-start shadow gap-2 cursor-pointer hover:bg-gray-50"
                            @click="openCourseTimeDetail(item)"
                    >
                        <div class="font-semibold col-span-1">@{{ item.index }}</div>
                        <div class="font-semibold col-span-1"> @{{ item.date_attendance | readableDate }}</div>
                        <div class="font-semibold col-span-1 text-green-500">@{{ item.present?.length }}</div>
                        <div class="font-semibold col-span-1 text-red-500">@{{ item.absence?.length }}</div>
                        <div class="font-semibold col-span-1">@{{ (item.present?.length * 100 / (item.present?.length +
                            item.absence?.length)).toFixed(1) }}%
                        </div>
                    </li>
                </ul>
                <el-drawer
                        :title="courseTimeDetail?.date_attendance"
                        :append-to-body="true"
                        :visible.sync="showCourseTimeDetail">
                    <ul class="flex flex-col gap-1" v-if="courseTimeDetail">
                        <li
                                v-for="item in courseTimeDetail.present"
                                :key="`course_time-${item.id}`"
                                :id="`course_time-${item.id}`"
                                class="p-5 flex items-center justify-between shadow gap-2 bg-green-50"
                        >
                            <div class="font-semibold col-span-3 flex items-center gap-2">
                                <el-avatar size="medium"
                                           :src="`https://school.dungmori.com/cdn/${item.student?.avatar}`"></el-avatar>
                                @{{ item.student?.last_name }} @{{ item.student?.first_name }}
                            </div>
                            <div class="font-semibold col-span-3 flex items-center gap-2 text-green-500">
                                Đi
                            </div>
                        </li>
                        <li
                                v-for="item in courseTimeDetail.absence"
                                :key="`course_time-${item.id}`"
                                :id="`course_time-${item.id}`"
                                class="p-5 flex items-center justify-between shadow gap-2 bg-red-50"
                        >
                            <div class="font-semibold col-span-3 flex items-center gap-2">
                                <el-avatar size="medium"
                                           :src="`https://school.dungmori.com/cdn/${item.student?.avatar}`"></el-avatar>
                                @{{ item.student?.last_name }} @{{ item.student?.first_name }}
                            </div>
                            <div class="font-semibold col-span-3 flex items-center gap-2 text-red-500">
                                Nghỉ
                            </div>
                        </li>
                    </ul>
                </el-drawer>
            </div>
        </el-drawer>
    </div>

@stop
@section('foot-js')
    <script src="{{asset('/plugin/jquery/axios.min.js')}}"></script>
    <script type="text/javascript">
        Vue.filter("readableDate", function (value) {
            return value ? moment(value).format("DD/MM/YYYY") : "--";
        });
        new Vue({
            el: '#offline-screen',
            data() {
                return {
                    tab: 'debt',
                    debts: [],
                    showCourseTime: false,
                    showCourseTimeDetail: false,
                    loading: {
                        department: false,
                        room: false,
                        time: false,
                        teacher: false,
                        level: false,
                        course: false,
                        courseTime: false,
                        debt: false,
                    },
                    count: 10,
                    departments: [],
                    rooms: [],
                    times: [],
                    teachers: [],
                    courses: [],
                    current_course_times: [],
                    drawerData: null,
                    courseTimeDetail: null,
                    levels: [
                        {value: 5, label: 'N1'},
                        {value: 4, label: 'N2'},
                        {value: 3, label: 'N3'},
                        {value: 2, label: 'N4'},
                        {value: 1, label: 'N5'},
                    ],
                    filters: {
                        department: '',
                        day_time: '',
                        room: '',
                        teacher: '',
                        level: '',
                        date_start: null,
                        date_end: null,
                        limit: 10,
                        page: 1
                    },
                    selectedCourse: null,
                    pickerOptions: {
                        shortcuts: [{
                            text: 'Tuần trước',
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: 'Tháng trước',
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                                picker.$emit('pick', [start, end]);
                            }
                        }, {
                            text: '3 tháng trước',
                            onClick(picker) {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                                picker.$emit('pick', [start, end]);
                            }
                        }]
                    },
                    loading_infinite: false,
                    total: 10
                }
            },
            computed: {
                totalStudentCount() {
                    return _.sumBy(this.courses, function (o) {
                        return o.students?.length || 0;
                    })
                },
                totalLastStudentCount() {
                    return _.sumBy(this.courses, function (o) {
                        return o.countedStudents || 0;
                    })
                },
                showedCourses() {
                    return this.courses.slice(0, this.count);
                },
                averagePresentPercent() {
                    const tmp = this.current_course_times.filter(o => o.present?.length + o.absence?.length > 0)
                    return (tmp.reduce((acc, o) => {
                        return acc + ((o.present?.length * 100 / (o.present?.length + o.absence?.length)) || 0)
                    }, 0) / tmp.length).toFixed(1)
                }
            },
            watch: {
                tab(value) {
                    this.debts = [];
                    if (value === 'debt') {
                        this.getDebts()
                    }
                    if (value === 'list') {
                        this.getDepartments()
                        this.getTimes()
                        this.getRooms()
                        this.getTeachers()
                    }
                }
            },
            methods: {
                loadCourse() {
                    if (this.loading_infinite) return;
                    this.loading_infinite = true;
                    this.getCourses();
                },
                async openCourseTime(course) {
                    this.loading.courseTime = true
                    this.drawerData = course
                    const res = await axios.get(window.location.origin + `/backend/offline/courses/${course.id}/course-time`)
                    this.current_course_times = res.data.data
                    this.showCourseTime = true
                    this.loading.courseTime = false
                },
                async openCourseTimeDetail(courseTime) {
                    this.courseTimeDetail = courseTime
                    this.showCourseTimeDetail = true
                },
                async getDepartments() {
                    this.loading.department = true
                    const res = await axios.get(window.location.origin + "/backend/offline/departments")
                    this.departments = res.data.data
                    this.filters.department = this.departments[0].id
                    this.loading.department = false
                    this.getCourses()
                },
                async getTimes() {
                    this.loading.time = true
                    const res = await axios.get(window.location.origin + "/backend/offline/times")
                    this.times = res.data.data
                    this.loading.time = false
                },
                async getRooms() {
                    this.loading.room = true
                    const res = await axios.get(window.location.origin + "/backend/offline/rooms")
                    this.rooms = res.data.data;
                    this.loading.room = false
                },
                async getTeachers() {
                    this.loading.teacher = true
                    const params = this.filters.department ? `?department_id=${this.filters.department}` : '';
                    const res = await axios.get(window.location.origin + "/backend/offline/teachers" + params)
                    this.teachers = res.data.data
                    this.loading.teacher = false
                },
                async getCourses() {
                    this.loading.course = true
                    const params = '?' + new URLSearchParams(_.omitBy(this.filters, _.isNil)).toString();
                    const res = await axios.get(window.location.origin + "/backend/offline/courses" + params)

                    this.total = res.data.data.data.total

                    let data = res.data.data.data.data.map(o => {
                        o.course_time_ids = o.course_times.map(t => t.id)
                        // console.log(o.course_time_ids)
                        o.students = o.students.map(s => {
                            s.course_time_student = res.data.data.course_time?.filter(o => o.sid === s.id) || [];
                            s.countedCourseTime = s.course_time_student.filter(c => o.course_time_ids.includes(c.ctd))
                            return s
                        })
                        // console.log(o.course_students)

                        o.countedStudents = o.students.filter(cs => (cs.countedCourseTime.length / o.course_time_ids.length) >= 0.7).length
                        // console.log(o.countedStudents)

                        return o
                    })
                    this.courses = [...this.courses, ...data]
                    if (this.courses.length) {
                        this.selectedCourse = this.courses[0]
                    }
                    this.filters.page += 1;
                    this.loading_infinite = false;
                    // Kiểm tra xem có còn dữ liệu để tải không
                    if (this.courses.length >= this.total) {
                        this.loading_infinite = true; // Ngừng Infinite Scroll nếu đã tải hết dữ liệu
                    }
                    this.loading.course = false
                },
                async getCoursesReset() {
                    this.total = 10
                    this.filters.page = 1
                    this.courses = []
                    this.getCourses()
                },
                async getDebts() {
                    this.loading.debt = true
                    const res = await axios.get(window.location.origin + "/backend/offline/debts")
                    this.debts = res.data.data.map(o => {
                        o.debt = o.revenue - o.discount - o.total_paid;
                        return o;
                    })
                    this.loading.debt = false
                },
                async exportDebt() {
                    const vm = this;
                    var url =
                        window.location.origin +
                        "/backend/offline/debts/export"
                    window.open(url);
                },
                async exportSelectedCourseStudents(course) {
                    const vm = this;
                    var url =
                        window.location.origin +
                        "/backend/offline/debts/export-course/" + course.id;
                    window.open(url);
                }
            },
            mounted() {
                if (this.tab === 'debt') {
                    this.getDebts()
                }
                if (this.tab === 'list') {
                    this.getDepartments()
                    this.getTimes()
                    this.getRooms()
                    this.getTeachers()
                }
            }
        })
    </script>
@stop
