<table>
    <thead>
    <tr>
        <td>ID</td>
        <td><PERSON><PERSON> tên</td>
        <td>Email</td>
        <td>SĐT</td>
        <td><PERSON><PERSON>n phẩm</td>
        <td>C<PERSON> sở</td>
        <td><PERSON>ớ<PERSON></td>
        <td>Doanh thu dự kiến</td>
        <td>Chiết khấu</td>
        <td>Tổng thực thu</td>
        <td>Nợ</td>
    </tr>
    </thead>
    <tbody>
    @foreach ($invoices as $invoice)
        <tr>
            <td>{{ $invoice['id'] ?? '' }}</td>
            <td>{{ $invoice['info_contact'] ? $invoice['info_contact']['name'] : $invoice['user']['name'] }}</td>
            <td>{{ $invoice['info_contact']['email'] ?? '' }}</td>
            <td>{{ $invoice['info_contact']['phone'] ?? '' }}</td>

            <td>
                {{ $invoice['product_name'] ?? '' }}
            </td>
            <td>
                @if (isset($invoice['department']['name']) && $invoice['department']['name'])
                    <span>{{ $invoice['department']['name'] ?? '' }}</span>
                @endif
            </td>
            <td>
                @if(isset($invoice['user']['offline_user']) && isset($invoice['user']['offline_user']['courses']) && count($invoice['user']['offline_user']['courses']))
                    @foreach($invoice['user']['offline_user']['courses'] as $course)
                        <div>
                            @foreach($course['teachers'] as $teacher)
                                <b>{{ $teacher['last_name'] . ' ' . $teacher['first_name'] }} | </b>
                            @endforeach
                            N{{ 6 - $course['level_of_n'] }} |
                            {{ $course['info']['room']['room_name'] ?? '' }} |
                            {{ implode(', ', array_map(function ($o) { return $o['day_id'] + 2; }, $course['infos'])) }} |
                            {{ $course['date_start'] }} - {{ $course['date_end'] }}
                            <br/>
                        </div>
                    @endforeach
                @endif
            </td>
            <td>
                {{ $invoice['revenue'] ?? '' }}
                {{ $invoice['currency'] === 'vnd' ? 'đ' :'¥' }}
            </td>
            <td>
                {{ $invoice['discount'] ?? '' }}
                {{ $invoice['currency'] === 'vnd' ? 'đ' :'¥' }}
            </td>
            <td>
                {{ $invoice['total_paid'] ?? '' }}
                {{ $invoice['currency'] === 'vnd' ? 'đ' :'¥' }}
            </td>
            <td>
                {{ (int) $invoice['revenue'] - (int) $invoice['discount'] - (int) $invoice['paid_money'] ?? '' }}
                {{ $invoice['currency'] === 'vnd' ? 'đ' :'¥' }}
            </td>
        </tr>
    @endforeach
    </tbody>
</table>
