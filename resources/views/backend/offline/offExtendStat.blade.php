@extends('backend._default.dashboard')

@section('description') Tỷ lệ gia hạn Offline @stop
@section('keywords') user @stop
@section('author') dungmori.com @stop
@section('title') Admin | Tỷ lệ gia hạn Offline @stop

@section('assets')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/3.1.3/js/bootstrap-datetimepicker.min.js"></script>
    <link href="{{ asset('plugin/select2-4.1.0/css/select2.min.css') }}" rel="stylesheet" />
@stop

@section('content')
    <div id="extend-wrapper" >
        <div class="flex items-center gap-4">
            <div class="form-group ml-5">
                <label>Từ ngày</label>
                <input v-model="from" type="text" class="form-control" name="from" id="from" placeholder="Chọn thời gian" @change="console.log(from)">
            </div>
            <div class="form-group ml-5">
                <label>Đến ngày</label>
                <input v-model="to" type="text" class="form-control" name="to" id="to" placeholder="Chọn thời gian" @change="console.log(from)">
            </div>
            <div class="form-group ml-5">
                <label>Cấp độ (HV kết thúc lớp)</label>
                <v-select
                    multiple
                    :options="vipLevel"
                    label="name"
                    :reduce="o => o.id"
                    v-model="level"
                >
                </v-select>
            </div>
            <div class="form-group ml-5">
                <label>Sản phẩm</label>
                <v-select
                    multiple
                    :options="offlineCombo"
                    label="name"
                    :reduce="o => o.id"
                    v-model="newProduct"
                >
                </v-select>
            </div>
            <div class="form-group ml-5">
                <label>Cấp độ (đơn hàng phát sinh)</label>
                <v-select
                    multiple
                    :options="vipLevel"
                    label="name"
                    :reduce="o => o.id"
                    v-model="newLevel"
                >
                </v-select>
            </div>
            <button class="btn btn-primary" @click="handleFilter()">Lọc</button>
        </div>
        <div class="px-2 text-lg"><b class="text-dmr-green">
            {{ $countInvoice }}</b> HV phát sinh đơn hàng / <b>{{ $studentCount }}</b> HV kết thúc khoá học
            <b>({{ number_format($invoices->filter(function ($value) { return $value->currency === 'vnd'; })->sum('price')) }} đ +
                {{ number_format($invoices->filter(function ($value) { return $value->currency === 'jpy'; })->sum('price')) }} ¥)</b>
        </div>

        <table class="table">
            <thead>
            <tr>
                <th scope="col">Mã đơn</th>
                <th scope="col">Học viên</th>
                <th scope="col">Combo</th>
                <th scope="col">Giá trị</th>
                <th scope="col" width="200">Ngày phát sinh</th>
            </tr>
            </thead>
            <tbody>
            @foreach($invoices as $invoice)
                <tr>
                    <th scope="row"><a href="{{ url('/backend/invoice?id=' . $invoice->id) . '&status=completed' }}" target="_blank">{{ $invoice->id }}</a></th>
                    <td>{{ $invoice->user?->name }} | {{ $invoice->user?->email }}</td>
                    <td>{{ $invoice->product_name }}</td>
                    <td>{{ number_format($invoice->price) }} {{ $invoice->currency === 'vnd' ? 'đ' : '¥' }}</td>
                    <td>{{ $invoice->created_at  }}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}">
    <script>
        Vue.component("v-select", VueSelect.VueSelect);
        Vue.filter("decimal", (number) => {
            return new Intl.NumberFormat("vi-VN").format(number);
        });
        new Vue({
            el: '#extend-wrapper',
            data() {
                return {
                    from: '',
                    to: '',
                    product: [],
                    level: [],
                    newProduct: [],
                    newLevel: [],
                    offlineCombo: [],
                    vipLevel: [
                        {id: 5, name: 'N1'},
                        {id: 4, name: 'N2'},
                        {id: 3, name: 'N3'},
                        {id: 2, name: 'N4'},
                        {id: 1, name: 'N5'},
                    ],
                }
            },
            mounted() {
                const vm = this
                $.get('/backend/vip/courses').then(res => {
                    vm.onlineProduct = res.data
                })
                $.get('/backend/offline/offline-combo').then(res => {
                    console.log('res', res);
                    vm.offlineCombo = res.data
                })

                $('#from').datetimepicker({
                    language: 'vi',
                    format: 'YYYY-MM-DD'
                }).on('dp.change', function (event) {
                    vm.from = event.target.value
                });
                $('#to').datetimepicker({
                    language: 'vi',
                    format: 'YYYY-MM-DD'
                }).on('dp.change', function (event) {
                    vm.to = event.target.value
                });

                const urlParams = new URLSearchParams(window.location.search);
                this.from = urlParams.get('from');
                this.to = urlParams.get('to');
                this.product = urlParams.get('product') ? urlParams.get('product').split(',') : [];
                this.level = urlParams.get('level') ? urlParams.get('level').split(',') : [];
                this.newProduct = urlParams.get('new_product') ? urlParams.get('new_product').split(',').map(o => Number(o)) : [];
                this.newLevel = urlParams.get('new_level') ? urlParams.get('new_level').split(',') : [];
            },
            methods: {
                handleFilter() {
                    if (!this.from || !this.to) return alert('Vui lòng chọn thời gian');
                    window.location.href = `{{ url('/backend/offline/ti-le-gia-han') }}?from=${this.from}&to=${this.to}&product=${this.product}&level=${this.level}&new_product=${this.newProduct}&new_level=${this.newLevel}`;
                }
            }
        })
    </script>
@stop
