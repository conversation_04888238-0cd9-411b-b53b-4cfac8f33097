<div class="step-1-container" v-if="steps == 1">
	<div class="customer-info-container">
		<div class="payment-heading">
			<span>Thông tin khách hàng</span>
			<a class="refresh" v-on:click="refresh">
				Làm mới <i class="zmdi zmdi-refresh-alt"></i>
			</a>
		</div>
		<div class="customer-info-table">
			<p style="font-size: 14px; margin-bottom: 25px;">(<span style="color: #e74c3c;">*</span>) Thông tin bắt buộc</p>
			<div v-if="customerError === true" class="alert alert-danger" role="alert">
                <i class="zmdi zmdi-alert-triangle"></i> Vui lòng chỉnh sửa điền đầy đủ thông tin sau đó làm mới
            </div>
		 	<table class="table">
		      <tbody>
		      	<tr>
					<td class="user-form-item important" width="200">Tên kh<PERSON><PERSON> hàng <span title="Thông tin bắt buộc">*</span></td>
					<td class="user-form-item" v-if="changeName == false">
						<b v-html="printInfo(customerName)"></b>
						<span class="action" v-on:click="showEditer('name')">
							<i class="zmdi zmdi-edit"></i> chỉnh sửa
						</span>
					</td>
					<td class="user-form-item" v-if="changeName == true">
						<span class="close" v-on:click="hideEditer('name')">
		            		<i class="zmdi zmdi-close-circle"></i> Đóng
		            	</span>
		            	<span class="error-info" v-if="errorName != ''">@{{ errorName }}</span>
		            	<input class="customer-input border" id="customer-name" type="text" :value="customerName" v-on:click="hideError" /><br/>
		            	<span class="change-btn" v-on:click="saveChange('name')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('name')">Hủy bỏ</span>
					</td>
				</tr>
				<tr>
					<td class="user-form-item important">Số điện thoại <span title="Thông tin bắt buộc">*</span></td>
					<td class="user-form-item" v-if="changePhone == false">
						<span v-html="printInfo(customerPhone)"></span>
						<span class="action" v-on:click="showEditer('phone')">
							<i class="zmdi zmdi-edit"></i> chỉnh sửa
						</span>
					</td>
					<td class="user-form-item" v-if="changePhone == true">
						<span class="close" v-on:click="hideEditer('phone')">
		            		<i class="zmdi zmdi-close-circle"></i> Đóng
		            	</span>
						<span class="error-info" v-if="errorPhone != ''">@{{ errorPhone }}</span>
						<input class="customer-input border" id="customer-phone" type="text" :value="customerPhone" v-on:click="hideError" /><br/>
		            	<span class="change-btn" v-on:click="saveChange('phone')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('phone')">Hủy bỏ</span>
					</td>
				</tr>
				<tr>
					<td class="user-form-item important">Email <span title="Thông tin bắt buộc">*</span></td>
					<td class="user-form-item">
						<span v-html="printInfo(customerEmail)"></span>
						@if(Auth::user()->activation == 0)
							<span class="label label-default"><i class="zmdi zmdi-alert-triangle"></i> chưa xác thực</span>
						@endif
						<span class="action protected"><i class="zmdi zmdi-lock" data-toggle="tooltip" data-placement="left" title="Thuộc tính bị khóa"></i></span>
					</td>
				</tr>
				@if(Auth::user()->username != null)
				<tr>
					<td class="user-form-item">Tên đăng nhập</td>
					<td class="user-form-item">{{ Auth::user()->username }}</td>
				</tr>
				@endif
				<tr>
					<td class="user-form-item">Ngày sinh</td>
					<td class="user-form-item">
						@if(Auth::user()->birth != null)
							{{ date('m/d/Y', strtotime(Auth::user()->birth)) }}
						@else
					    	<span class="empty-info">Chưa có thông tin</span>
					    @endif
					</td>
				</tr>
				<tr v-if="itemType == 'book'">
					<td class="user-form-item">Quốc gia</td>
					<td class="user-form-item" v-if="!changeShippingCountry">
						<span>@{{ customerShippingCountry | country }}</span>
						<span class="action" v-on:click="showEditer('shipping_country')">
							<i class="zmdi zmdi-edit"></i> chỉnh sửa
						</span>
					</td>
					<td class="user-form-item" v-if="changeShippingCountry">
						<select class="customer-input form-control" id="customer-shipping-country" :value="customerShippingCountry">
							<option v-for="country in shippingCountries" :key="'shipping-' + country" :value="country.value">@{{ country.label }}</option>
						</select>
						<div>
							<span class="change-btn" v-on:click="saveChange('shipping_country')">Lưu lại</span>
							<span class="cancel-btn" v-on:click="hideEditer('shipping_country')">Hủy bỏ</span>
						</div>
					</td>
				</tr>
				<tr>
					<td class="user-form-item">Địa chỉ</td>
					<td class="user-form-item" v-if="!changeAddress">
						<span v-html="printInfo(customerAddress)"></span>
						<span class="action" v-on:click="showEditer('address')">
							<i class="zmdi zmdi-edit"></i> chỉnh sửa
						</span>
					</td>
					<td class="user-form-item" v-if="changeAddress">
						<input class="customer-input border" id="customer-address" type="text" :value="customerAddress" v-on:click="hideError" /><br/>
						<span class="error-info" v-if="errorAddress != ''">@{{ errorAddress }}</span>
						<div>
							<span class="change-btn" v-on:click="saveChange('address')">Lưu lại</span>
							<span class="cancel-btn" v-on:click="hideEditer('address')">Hủy bỏ</span>
						</div>
					</td>
				</tr>
				<tr v-if="itemType == 'book' && customerShippingCountry == 'jp'">
					<td class="user-form-item important">Mã bưu điện <span title="Thông tin bắt buộc"> *</span></td>
					<td class="user-form-item" v-if="!changeZipCode">
						<span>@{{ customerZipCode }}</span>
						<span class="action" v-on:click="showEditer('zip_code')">
							<i class="zmdi zmdi-edit"></i> chỉnh sửa
						</span>
					</td>
					<td class="user-form-item" v-if="changeZipCode">
						<input class="customer-input" id="customer-zipcode" type="text" v-model="customerZipCode" v-on:click="hideError" /><br/>
						<div>
							<span class="change-btn" v-on:click="hideEditer('zip_code')">Lưu lại</span>
						</div>
					</td>
				</tr>
		      </tbody>
			</table>

		</div>
	</div>

	<div class="continue-container">
		<div class="continue">
	        <span class="dmr-btn" v-on:click="nextSteps">Tiếp Tục <i class="zmdi zmdi-arrow-right"></i></span>
	    </div>
	</div>
</div>
