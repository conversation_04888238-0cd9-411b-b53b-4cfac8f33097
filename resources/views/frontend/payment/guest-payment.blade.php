@extends('frontend._layouts.default')

@section('title') Dungmori - Trang thanh toán @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber dạy tiếng nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-js')
    <link rel="stylesheet" href="{{ asset('/plugin/vue-tel-input/vue-tel-input.css') }}" />
    <script src="https://www.gstatic.com/firebasejs/5.0.1/firebase.js"></script>
    <script>
      // Initialize Firebase
      // TODO: Replace with your project's customized code snippet
      var config = {
        apiKey: "AIzaSyBaSVeY1TcDmxH33tKjjlxlWrivvf2YmIk",
        authDomain: "dungmori-app.web.app",
      };
      firebase.initializeApp(config);
    </script>
    <script src="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.js"></script>
    <link type="text/css" rel="stylesheet" href="https://www.gstatic.com/firebasejs/ui/6.0.1/firebase-ui-auth.css" />
@stop
@section('content')

    <div class="main mb-5">
        <div class="main-center main-payment">
            <ul class="nav nav-wizard">
                <li :class="{active: steps >= 1}" @click="steps = steps == 3 ? 3 : 1"><a><span class="step">1</span> <span class="step-text">Thông tin khách hàng</span></a></li>
                <li :class="{active: steps >= 2}" @click="steps = steps == 3 ? 3 : validate() ? 2 : 1"><a><span class="step">2</span> <span class="step-text">Lựa chọn hình thức thanh toán</span></a></li>
                <li :class="{active: steps == 3}"><a><span class="step">3</span> <span class="step-text">Tạo thành công đơn hàng</span></a></li>
            </ul>
            <div class="steps-container">
                <div class="steps-container-left">
                    @include('frontend.payment.guest.step1')
                    @include('frontend.payment.guest.step2')
                    @include('frontend.payment.guest.step3')
                </div>
                @include('frontend.payment.right-container')
            </div>
        </div>
    </div>
@stop
@section('footer-js')
    <script type="text/javascript">
      var allMethods = {!! json_encode($methods) !!};
      var cbid = "{{ $item->id }}";
      var itemType = "{{ $type }}";
      @if($type == 'book' && Auth::user()->shipping_country == 'vn')
      var jpPrice = {{$item->jpy_price_at_vn}};
      @else
      var jpPrice = {{$item->jpy_price}};
      @endif
      var jpExtraPrice = {{$price['extra_jp_price']}};
      var product = '<?php echo json_encode($price, true) ?>';
      if (product){
        product = JSON.parse(product)
      }
    </script>
    <script src="{{ asset('/plugin/vue-tel-input/vue-tel-input.js') }}"></script>
    <script type="text/javascript">
      // Vue.use(window['vue-tel-input']);//ip của người dùng
    </script>
    <script src="{{asset('assets/js/guest-payment.js')}}?{{filemtime('assets/js/guest-payment.js')}}"></script>
@stop
