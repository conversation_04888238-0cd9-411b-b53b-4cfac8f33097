<div class="step-2-container" v-if="steps == 2">
    <div class="customer-info-container">
        <div class="payment-heading">
            <span>Ch<PERSON><PERSON> hình thức thanh toán</span>
        </div>
        <div class="customer-choose-payment-gates">
            <div class="col-md-4">
                <label class="tcb-inline choice_payment_method" v-for="method in filteredMethods">
                    <input type="radio" v-model="gates" :value="method.id" @change="updateDesc()">
                    <span class="labels">@{{ method.name }}</span>
                </label>
            </div>
            <div class="col-md-8">
                <div v-html="desc" style="text-align: justify;" class="payment-right-info-area"></div>
                <div v-if="gates == 4" class="delivery-info">
                    <div v-if="deliveryError === true" class="alert alert-danger" role="alert">
                        <i class="zmdi zmdi-alert-triangle"></i> <PERSON><PERSON> lòng điền đ<PERSON>y đủ thông tin người nhận thẻ
                    </div>
                    <div class="row">
                        <div class="col-sm-5 important">Tên người nhận <span title="Thông tin bắt buộc">*</span></div>
                        <div class="col-sm-7">
                            <input v-model="deliveryName" v-on:click="hideError" type="text" class="form-control" placeholder="Tên người nhận"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-5 important">Số điện thoại <span title="Thông tin bắt buộc">*</span></div>
                        <div class="col-sm-7">
                            <input v-model="deliveryPhone" v-on:click="hideError" type="text" class="form-control" placeholder="Số điện thoại"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-5 important">Địa chỉ người nhận hàng <span title="Thông tin bắt buộc">*</span> <br/>(vui lòng điền đầy đủ)</div>
                        <div class="col-sm-7">
                            <textarea v-model="deliveryAddress" v-on:click="hideError" class="form-control" rows="3"  placeholder="Địa chỉ giao hàng"/></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="continue-container">
        <div v-if="buyBtnState === false" class="buy-btn" onclick="ga('send', 'event', 'Pricing', 'click', 'Buy ' + '{{ $item->name }}')" v-on:click="clickBuy">
            <span v-if="itemType == 'book'"><i class="zmdi zmdi-shopping-cart"></i> Đặt mua sách</span>
            <span v-else><i class="zmdi zmdi-shopping-cart"></i> Đặt mua khóa học</span>
        </div>
        <div v-if="buyBtnState === true" class="buy-btn">
            <span><i class="fa fa-spinner fa-pulse fa-fw"></i> Đang xử lý...</span>
        </div>
        <span class="light-btn" v-on:click="prevStep">Trở lại</span>
    </div>
</div>
