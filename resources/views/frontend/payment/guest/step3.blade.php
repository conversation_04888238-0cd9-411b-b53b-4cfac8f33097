<div class="customer-info-container" v-if="steps == 3" v-cloak>
    <div class="payment-heading">
        <span>Tạo đơn hàng thành công</span>
    </div>
    <div class="customer-successfull-gates">
        <p class="success-text">
        <div class="alert alert-success" role="alert"  style="font-size: 16px; line-height: 1.2;">
            <i class="zmdi zmdi-check-circle"></i>
            Bạn đã đặt mua thành công <span v-if="itemType == 'book'">sách</span><span v-else>khóa học</span> trên dungmori.com, thông tin về đơn hàng
            đã được gửi về địa chỉ email <strong>@{{ guestForm.email }}</strong>
            vui lòng kiểm tra hòm thư để biết thêm chi tiết, xin cảm ơn !
        </div>
        </p>
        <div class="invoice-info">
            <p><b style="font-size: 16px;">Thông tin đơn hàng</b></p>
            <p>
                Tên khách hàng : @{{guestForm.name}}<br/>
                Số điện thoại : @{{guestForm.phone}}<br/>
                Email : @{{guestForm.email}}<br/>

                Mã đơn hàng : <strong>@{{ successInvoice.id }}</strong><br/>
                Ngày tạo : @{{ successInvoice.created_at }}<br/>
                Sản phẩm : @{{ printType(successInvoice.product_type) }} @{{ successInvoice.product_name }} <br />
                <span v-if="itemType == 'book'">Số lượng: @{{ successInvoice.quantity }}</span> <br/>
                Tổng tiền : @{{ formatNumber(successInvoice.price) }} đ
                {{--            <span v-if="itemType == 'book' && shippingCountry == 'jp'">(@{{ formatNumber(jpyPrice * quantity) ¥}})</span>--}}
                <span v-if="gates == 5 && successInvoice.extra_price == 0">(@{{ formatNumber(jpyPrice)}} ¥)</span>
                <span v-if="gates == 5 && successInvoice.extra_price > 0">(@{{ formatNumber(jpyPrice + jpExtraPrice)}} ¥)</span>

                <br/><br/>

                Hình thức thanh toán : @{{ successPaymentMedthodName }}<br/>

                <span v-if="gates == 4">
            Tên người nhận : @{{ deliveryName }}<br/>
            Số điện thoại người nhận : @{{ deliveryPhone }}<br/>
            Địa chỉ người nhận : @{{ deliveryAddress }}<br/>
            </span><br/>
                <span>Đường dẫn kiểm tra đơn hàng : </span>
                <a class="link-checkout" :href="url+ '/checkout/'+ successInvoice.uuid">@{{url}}/checkout/@{{successInvoice.uuid}}</a>
            </p>
        </div>
        <br/>
        <p>
            <span class="danger-text"><strong>* Lưu ý 1:</strong> Nếu không tìm thấy email đơn hàng trong hòm thư đến (inbox) xin vui lòng kiểm tra hộp thư Spam hoặc Junk Folder</span>
        </p>
        <p class="pm-notice-method">
            <span class="danger-text"><strong>* Lưu ý 2:</strong> Sau khi nhận được đơn hàng của bạn, trong trường hợp bạn lựa chọn hình thức:</span>
            <span v-if="itemType != 'book'"><strong>Chuyển khoản ngân hàng:</strong> chúng tôi sẽ liên lạc đến số điện thoại bạn đã đăng kí khi chuyển khoản để kích hoạt tài khoản khóa học cho bạn. Thường được xử lí trong vòng 1, 2 ngày, tùy vào ngân hàng chuyển khoản<br/></span>
            <span v-if="itemType == 'book'"><strong>Chuyển thẻ:</strong> Chúng tôi sẽ liên lạc đến người nhận sách theo số điện thoại đã đăng kí để xác nhận lần cuối ngay sau khi nhận được đơn hàng<br/></span>
            <span v-else><strong>Chuyển thẻ:</strong> Chúng tôi sẽ liên lạc đến người nhận thẻ theo số điện thoại đã đăng kí để xác nhận lần cuối ngay sau khi nhận được đơn hàng<br/></span>
            <span v-if="itemType == 'book'"><strong>Thanh toán trực tiếp:</strong> Nhận sách ngay sau khi bạn đến văn phòng và hoàn tất thanh toán</span>
            <span v-else><strong>Thanh toán trực tiếp:</strong> Tài khoản sẽ được được kích hoạt ngay sau khi bạn đến văn phòng và hoàn tất thủ tục mua khóa học</span>
        </p>
    </div>
</div>
