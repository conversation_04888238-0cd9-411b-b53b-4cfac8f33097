<div class="step-1-container" v-if="steps == 1" v-cloak>
    <div class="customer-info-container">
        <div class="payment-heading">
            <span>Thông tin khách hàng</span>
        </div>
        <div class="customer-info-table">
            <p style="font-size: 14px; margin-bottom: 25px;">(<span class="text-red">*</span>) Thông tin bắt buộc</p>
            <div class="form-group">
                <label for="nameInput">Tên khách hàng <span title="Thông tin bắt buộc" class="text-red">*</span></label>
                <input v-model="guestForm.name" @input="errorName = ''" type="text" name="name" class="form-control input-payment" id="nameInput" placeholder="Ví dụ: Nguyễn Văn A">
                <span class="text-red" v-if="errorName">@{{ errorName }}</span>
            </div>
            <div class="form-group">
                <label for="phoneInput">Số điện thoại <span title="Thông tin bắt buộc" class="text-red">*</span></label>
                <div class="flex justify-content-start items-center">
                    <input
                        v-if="!phoneVerifying && !phoneVerified"
                        v-model="guestForm.phone"
                        @input="errorPhone = ''"
                        class="form-control input-payment"
                        placeholder="Ví dụ: +84353230012"
                    >
                    <input v-else-if="phoneVerifying && !phoneVerified" @input="errorPhone = ''" v-model="phoneVerifyCode" type="text" name="code" class="form-control input-payment" id="codeInput" placeholder="Nhập mã xác minh nhận được qua tin nhắn SMS">
                    <span v-else>@{{ guestForm.phone }}</span>
                    <button class="btn ml-2" v-if="!phoneVerifying && !phoneVerified" @click="verify" style="width: 30%; color: #fff; margin-top: -2px; background: #96D962;">Gửi mã xác minh</button>
                    <button class="btn ml-2" v-if="phoneVerifying && !phoneVerified" @click="verifyCode" style="width: 30%; color: #fff; margin-top: -2px; background: #96D962;">Xác minh</button>
                </div>
                <span v-if="errorPhone == 'exist'" class="text-red">Số điện thoại đã tồn tại trong hệ thống. Tiếp tục bằng cách <span class="text-primary a-cursor-pointer">đăng nhập</span></span>
                <span v-else class="text-red">@{{ errorPhone }}</span>
            </div>
            <div class="form-group">
                <label for="emailInput">Email <span title="Thông tin bắt buộc" class="text-red">*</span></label>
                <input v-model="guestForm.email" @input="errorEmail= ''" type="email" name="email" class="form-control input-payment" id="emailInput" placeholder="Ví dụ: <EMAIL>">
                <span v-if="errorEmail == 'exist'" class="text-red">Email đã tồn tại trong hệ thống. Tiếp tục bằng cách <span class="text-primary a-cursor-pointer" @click="login">đăng nhập</span></span>
                <span v-else class="text-red">@{{ errorEmail }}</span>
            </div>
            <div class="continue-container">
                <div class="continue">
                    <span class="dmr-btn" @click="validate">Tiếp Tục <i class="zmdi zmdi-arrow-right"></i></span>
                </div>
            </div>
        </div>
        <div id="get-sign-in-code"></div>
    </div>
</div>
