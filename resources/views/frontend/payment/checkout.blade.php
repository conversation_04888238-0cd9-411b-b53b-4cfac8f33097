@extends('frontend._layouts.default')

@section('title') Dungmori - Trang thanh toán @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber dạy tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<div class="main">
   	<div class="main-center main-payment">
   		<div class="checkout-container">
	        <div class="invoice-info">
				<p><h3>Thông tin chi tiết đơn hàng <strong>{{ $invoice->uuid }}</strong></h3></p>
				<p>Trạng thái đơn hàng :
				<?php
				switch ($invoice->invoice_status){
					case 'completed': echo '<b style="color: #03a51e;"><PERSON><PERSON><PERSON> thành <i class="zmdi zmdi-check-circle"></i></b>'; break;
					case 'new': echo '<b style="color: #e02323;">Đang chờ xử lý...</b>'; break;
					default: echo '<b style="color: #e02323;">Đã hủy bỏ</b>'; break;
				}
				?>
				</p>
	          	<p>

				@if (is_null($invoice->user_id))
					Tên khách hàng : {{ json_decode($invoice->info_contact)->guestName }}<br/>
					Số điện thoại : {{ json_decode($invoice->info_contact)->guestPhone }}<br/>
					Email : {{ json_decode($invoice->info_contact)->guestEmail }}<br/>
				@else
					Tên khách hàng : {{ $customer['name'] }}<br/>
					Số điện thoại : {{ $customer['phone'] }}<br/>
					Email : {{ $customer['email'] }}<br/>
					Địa chỉ : {{ $customer['address'] }}<br/><br/>
				@endif

	            Mã đơn hàng : <strong>{{ $invoice->id }}</strong><br/>
	            Ngày tạo : {{ date('d/m/Y H:i', strtotime($invoice->created_at)) }}<br/>
	            Sản phẩm : <?php switch ($invoice->product_type) {
							   	case 'combo' :  echo 'Gói combo ' . $invoice->product_name; break;
							   	case 'course' :  echo 'Khóa học ' . $invoice->product_name; break;
								default: break;
							   }
							?> @if($invoice->extra_price > 0)<sup> <span class="label-hot">Plus</span></sup>@endif<br/>
	            Tổng tiền : <span style="color: #e02323;">{{ number_format($invoice->price) }}</span> đ

	            {{-- nếu là đơn hàng chuyển khoản bên nhật -> in thêm giá yên --}}
{{--	            @if($invoice->payment_method_id == 5)--}}
{{--	            	({{ number_format($invoice->getJpyPrice()) }} ¥)--}}
{{--	            @endif--}}

	            <br/><br/>

	            Hình thức thanh toán :
					@if ($invoice->payment_method_id == '1') {{ "Thanh toán qua paypal" }}
						@elseif ($invoice->payment_method_id == '2') {{ "Chuyển khoản ngân hàng Việt Nam" }}
						@elseif ($invoice->payment_method_id == '3') {{ "Nộp tại văn phòng" }}
						@elseif ($invoice->payment_method_id == '4') {{ "Chuyển phát nhanh mã thẻ" }}
						@elseif ($invoice->payment_method_id == '5') {{ "Chuyển khoản ngân hàng tại Nhật" }}
						@elseif ($invoice->payment_method_id == '7') {{ "Chuyển khoản ngân hàng Việt Nam" }}
						@elseif ($invoice->payment_method_id == '8') {{ "Chuyển khoản ngân hàng tại Nhật" }}
						@else {{ "---Chưa chọn---" }}
					@endif
					<br/>

	            @if($invoice->payment_method_id == 4 || ($invoice->product_type == 'combo' && $invoice->combo->type == 'book'))
		            Tên người nhận : {{ json_decode($invoice->info_contact)->name }}<br/>
		            Số điện thoại người nhận : {{ json_decode($invoice->info_contact)->phone }}<br/>
		            Địa chỉ người nhận : {{ json_decode($invoice->info_contact)->address }}<br/>
					@if (isset(json_decode($invoice->info_contact)->zipCode) && json_decode($invoice->info_contact)->zipCode != '')
						Mã bưu điện : {{ json_decode($invoice->info_contact)->zipCode }}<br/>
					@endif
		        @endif
	          	</p>
	        </div>
	        <br/>

	        @if($invoice->payment_method_id != 4)
		        <h3>Hướng dẫn thanh toán</h3>
		        <p>{!! html_entity_decode($paymentTutorial->description) !!}</p>
		    @endif

	    </div>
   </div>
</div>

@stop
