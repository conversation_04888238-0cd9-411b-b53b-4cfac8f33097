@extends('frontend._layouts.default')

@section('title') Dungmori - Trang thanh toán @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<div class="main">
   	<div class="main-center main-payment" style="opacity: 0;">

   		{{-- các đơn hàng tồn tại : {{$checkAllExistInvoices}} <br/>
		check khách hàng có thể mua : {{ $checkCustomerCanBuyNow }}<br/>
		đơn hàng tồn taijt rong 24h : {{ $existInvoice24h }} --}}

   		@if(!Auth::check())

   		<ul class="nav nav-wizard">
			<li class="active"><a><span class="step">1</span> <span class="step-text">Thông tin khách hàng</span></a></li>
			<li><a><span class="step">2</span> <span class="step-text">Lựa chọn hình thức thanh toán</span></a></li>
			<li><a><span class="step">3</span> <span class="step-text">Tạo thành công đơn hàng</span></a></li>
		</ul>

   		@else
		<ul v-if="steps == 1" class="nav nav-wizard">
			<li class="active"><a><span class="step">1</span> <span class="step-text">Thông tin khách hàng</span></a></li>
			<li v-on:click="nextSteps"><a><span class="step">2</span> <span class="step-text">Lựa chọn hình thức thanh toán</span></a></li>
			<li><a><span class="step">3</span> <span class="step-text">Tạo thành công đơn hàng</span></a></li>
		</ul>
		<ul v-if="steps == 2" class="nav nav-wizard">
			<li class="active" v-on:click="prevStep"><a><span class="step">1</span> <span class="step-text">Thông tin khách hàng</span></a></li>
			<li class="active"><a><span class="step">2</span> <span class="step-text">Lựa chọn hình thức thanh toán</span></a></li>
			<li><a><span class="step">3</span> <span class="step-text">Tạo thành công đơn hàng</span></a></li>
		</ul>
		<ul v-if="steps == 3" class="nav nav-wizard">
			<li class="active"><a><span class="step">1</span> <span class="step-text">Thông tin khách hàng</span></a></li>
			<li class="active"><a><span class="step">2</span> <span class="step-text">Lựa chọn hình thức thanh toán</span></a></li>
			<li class="active"><a><span class="step">3</span> <span class="step-text">Tạo thành công đơn hàng</span></a></li>
		</ul>
		@endif

		<h4 class="mb-stt" v-if="steps == 1"><a><span class="step-mb">1</span>&nbsp;&nbsp;Nhập thông tin khách hàng</a></h4>
        <h4 class="mb-stt" v-if="steps == 2"><a><span class="step-mb">2</span>&nbsp;&nbsp;Lựa chọn hình thức thanh toán</a></h4>
        <h4 class="mb-stt" v-if="steps == 3"><a><span class="step-mb">3</span>&nbsp;&nbsp;Tạo thành công đơn hàng</a></h4>

		<div class="steps-container">

			<div class="steps-container-left">

				{{-- Nếu người dùng chưa đăng nhập--}}
				@if(!Auth::check())

				<div class="guest-info-container">
				    <h3><i class="zmdi zmdi-lock"></i> Bạn cần đăng nhập hoặc tạo tài khoản để mua</h3>
				    <h3>
				        <a data-fancybox data-animation-duration="300" data-src="#auth-container">
				          <div class="btn-register" onclick="swichTab('login')">Đăng nhập</div>
				        </a>
				        <a data-fancybox data-animation-duration="300" data-src="#auth-container">
				          <div class="btn-login" onclick="swichTab('register')">Tạo tài khoản</div>
				        </a>
					</h3>
				</div>

				{{-- Nếu đã đăng nhập và khách hàng đã đặt hàng hoặc mua trước đó --}}
				@elseif(Auth::check() && $checkCustomerCanBuyNow != 0)

				<div class="guest-info-container">
					<h3>
						<i class="zmdi zmdi-card"></i>
						Đơn hàng cho @if($type == 'course') KHÓA HỌC @else COMBO @endif này <br/>
						đã tồn tại trong hệ thống</h3>
					<h3>
				        <a href="{{ url('account/billing') }}">
				          <div class="btn-register">Vào phần quản lý đơn hàng <i class="zmdi zmdi-arrow-right"></i></div>
				        </a>
					</h3>
					<p style="font-size: 14px; color: #f06; margin-top: 30px;">Bạn đã đặt hàng lúc {{ $existInvoice24h->getTimeDetail() }}<br/>
						Bạn có thể tạo lại đơn hàng mới sau 24h kể từ lúc đặt</p>
				</div>

				{{-- Nếu đã đăng nhập và khách hàng chưa order đơn hàng này bao h --}}
				@elseif(Auth::check() && $checkCustomerCanBuyNow == 0)

					{{-- Hiển thị thông báo đơn hàng cũ đã tồn tại trong hệ thống--}}
					@if( $checkAllExistInvoices != 0 )
					<div class="invoice-warning alert alert-warning" role="alert" v-if="steps != 3">
						<a href="#" class="close" data-dismiss="alert" aria-label="close" title="close">×</a>
						<a class="pull-right label label-default" href="{{ url('account/billing') }}">Vào xem ngay</a>
						<i class="zmdi zmdi-alert-circle"></i> <strong>Lưu ý ! </strong>
						Đơn hàng cho @if($type == 'course') KHÓA HỌC @else COMBO @endif này đã được tạo trước đây trong hệ thống
						<br/>
						&nbsp; &nbsp; Nhưng bạn vẫn có thể tạo đơn hàng mới

					</div>
					@endif
					@include('frontend.payment.step1-info')
					@include('frontend.payment.step2-form')
					@include('frontend.payment.step3-success')
				@endif
			</div>
			@include('frontend.payment.right-container')
		</div>
   </div>
</div>

@if(Auth::check())
	<script type="text/javascript">
		var allMethods = {!! json_encode($methods) !!};
		var userName = {!! json_encode(Auth::user()->name) !!};
		var userEmail = {!! json_encode(Auth::user()->email) !!};
		var userPhone = "{{ Auth::user()->phone }}";
		var userAddress = {!! json_encode(Auth::user()->address) !!};
		var userShippingCountry = {!! json_encode(Auth::user()->shipping_country) !!};
		var cbid = "{{ $item->id }}";
		var itemType = "{{ $type }}";
		@if($type == 'book' && Auth::user()->shipping_country == 'vn')
			var jpPrice = {{$item->jpy_price_at_vn}};
		@else
			var jpPrice = {{$item->jpy_price}};
		@endif
		var jpExtraPrice = {{$price['extra_jp_price']}};
		var product = '<?php echo json_encode($price,true) ?>';
		console.log("======================================================================================");
		console.log(product);
		console.log("===================================================================================");
		if (product){
			product = JSON.parse(product)
		}
	</script>
	@section('footer-js')
		<script src="{{asset('assets/js/payment.js')}}?{{filemtime('assets/js/payment.js')}}"></script>
		<script type="text/javascript"> $(".main-payment").css('opacity', 1); </script>

		{{-- nếu email rỗng do dùng login mạng xã hội thì bắt người dùng nhập vào để sử dụng đặt hàng các thứ --}}
		@if(Auth::check() && Auth::user()->email == null)

			<a class="btn-input-email" data-fancybox="" data-src="#inputEmailModal" data-modal="true" href="javascript:;" style="display: none;"></a>

			<div id="inputEmailModal" class="p-5 fancybox-content" style="display: none; max-width: 500px;">
		        <h3>Yêu cầu từ hệ thống</h3>
		        <p>Hiện tài khoản của bạn chưa có thông tin email</p>
		        <p>Vui lòng nhập email của bạn để mua các khóa học và cập nhật các thông báo quan trọng từ website dungmori.com</p><br/>
				<label class="control-label">Email của bạn</label>
				<input id="user-payment-email" class="form-control" type="email" v-on:click="hideError" placeholder="Email" autocomplete="off" required autofocus/><br/>
				<div class="alert-danger" style="display: none; padding: 5px 10px; margin-bottom: 20px;" v-show="error != null"><i class="zmdi zmdi-alert-octagon"></i> @{{error}}</div>
		        <p>
		            <button class="change-btn" style="padding: 5px 20px;" v-if="buttonState === true" v-on:click="saveEmail">Lưu lại</button>
		            <button class="change-btn" style="padding: 5px 20px;" v-if="buttonState === false"><i class="fa fa-spinner fa-pulse fa-fw"></i>  Đang kiểm tra...</button>
		            <b v-if="success === true" style="margin-top: 7px; color: #0ab22c;">Lưu thành công <i class="zmdi zmdi-check-circle"></i></b>
		        </p>
		    </div>
		    <script type="text/javascript">
		    	new Vue({
					el: '#inputEmailModal',
					data: {
						error: null,
						buttonState: true,
						success: false
					},
					methods: {
						saveEmail: function(){

							vm = this;
							vm.buttonState = false;
							setTimeout(function(){
								$.post(window.location.origin +"/account/change-email", {
						            email : $("#user-payment-email").val()
						        }, function(response){

						        	vm.buttonState= true;
						            if(response === "success"){
						            	vm.success = true;
						            	setTimeout(function(){location.reload(); }, 1000);
						            }else{
						                vm.error = response;
						            }
						        });
						    }, 1000);
						},
						hideError: function(){
							this.error = null;
						}
					},
					mounted() {
						$(".btn-input-email").click();
					}
				});
		    </script>

		@endif


	@stop
@else
<script type="text/javascript"> $(".main-payment").css('opacity', 1); </script>
@endif

@stop
