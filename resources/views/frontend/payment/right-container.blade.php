<div class="steps-container-right">
    <div class="payment-info-container">
        <div class="payment-heading" style="background: #96D962">
            <span>Thông tin đơn hàng</span>
        </div>
        <div class="combo-item">
            <div class="combo-name-container">
                @if($type == 'course')
                    <p>KHÓA</p>
                @elseif($type == 'book')
                    <p>SÁCH</p>
                @else
                    <p>COMBO</p>
                @endif
                <h1>{{ $item->name }}</h1>
            </div>
            @if($type == 'book')
                @if (Auth::user() && Auth::user()->shipping_country == 'vn')
                    <div class="combo-detail-container">
                        <div class="course-info">Đơn giá: <b style="color: #e74c3c;">{{ number_format($item->price_at_vn) }} ₫ </b>
{{--                            &nbsp; ({{ number_format($item->jpy_price_at_vn) }} ¥)--}}
                        </div>
                    </div>
                @else
                    <div class="combo-detail-container">
                        <div class="course-info">Đơn giá: <b style="color: #e74c3c;">{{ number_format($item->price) }} ₫ </b>
{{--                            &nbsp; ({{ number_format($item->jpy_price) }} ¥)--}}
                        </div>
                    </div>
                @endif
                <div>
                    Số lượng:
                    <span v-if="steps == 3">&nbsp;@{{ quantity }}</span>
                    <div v-else>
                        <input class="form-control" type="number" v-model="quantity"/>
                    </div>
                </div>
            @else
                <div class="combo-detail-container">
                    <div class="course-info">Học phí: <b style="color: #e74c3c;">{{ number_format($item->price) }} ₫ </b>
{{--                        &nbsp; ({{ number_format($item->jpy_price) }} ¥)--}}
                    </div>
                    <div class="course-info">Thời hạn : {!! round(json_decode($item->services)->course_watch_expired_value / 30) !!} tháng</div>
                    <div class="course-info">Mã số : {{$item->desc}}</div>
                </div>
                @if($item->extra_price > 0)
                    <div class="combo-plus">
                        <label class="chk-plus">
                            <input type="checkbox" v-model="isPlus" @change="setProductPlus" >
                            <i v-if="isPlus" class="fa fa-check-square-o"></i>
                            <i v-else class="fa fa-square-o"></i>
                            <span class="title">Mua kèm <strong>Gói {{$item->name}} Plus</strong></span>
                        </label>
                        <p>Học phí: <span class="vnd-plus">{{number_format($item->extra_price)}} ₫</span>
{{--                            ({{ number_format($item->extra_jp_price) }} ¥)--}}
                        </p>
                        <p>{!! $item->extra_desc  !!}</p>
                    </div>
                @endif
            @endif

            <div v-cloak class="mt-3">
                <input v-if="!useCoupon" v-model="coupon" placeholder="Mã giảm giá" style="width: 65%; padding: 7px 5px; border: 1px solid #ccc; border-radius: 2px;"/>
                <div v-else style="width: 65%; display: inline-block">Mã giảm giá: <span class="text-bold text-red">@{{ coupon }}</span></div>
                <button
                    v-if="[1,2].includes(steps)"
                    class="btn ml-2"
                    @click="toggleCoupon"
                    :disabled="loading"
                    style="width: 30%; color: #fff; margin-top: -2px;"
                    :style="{background: useCoupon ? '#e74c3c' : '#96D962'}"
                >@{{ useCoupon ? 'Gỡ' : 'Áp dụng' }}</button>
            </div>
        </div>
        <hr/>
        {{--					<h4 class="total-payment">Tổng tiền  <span>{{ number_format($item->price) }} ₫</span></h4>--}}
        <h4 class="total-payment">Tổng tiền  <span v-text="getPrice().subTotal"> </span></h4>
        <h4 class="total-payment sub" v-if="(coupon && useCoupon) || (product.price_discount_not_use_coupon > 0)">Giảm giá  <span v-text="getPrice().discount"> </span></h4>
        <h4 class="total-payment sub" v-if="coupon && useCoupon || product.price_discount_not_use_coupon > 0">Thực tổng <span v-text="getPrice().total"> </span></h4>
    </div>
</div>
