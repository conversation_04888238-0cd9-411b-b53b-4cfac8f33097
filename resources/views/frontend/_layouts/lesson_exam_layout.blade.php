<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>@yield('title')</title>
    <meta http-equiv="content-language" content="vi"/>
    <meta name="viewport"
          content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=5.0, user-scalable=1"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="format-detection" content="telephone=no">
    <meta name="google-site-verification" content="qXXUA_R1uoDBf-1WE-ghxdEO6pirYuP-yDVB5iqytRg"/>
    <meta name="facebook-domain-verification" content="4w08w6rk0e6sojaci5ix41vcdlh97t"/>

    <meta name="author" content="@yield('author')"/>
    <meta name="description" content="@yield('description')"/>
    <meta name="robots" content="index,follow"/>

    <link rel="icon" href="{{asset('assets/img/new_home/06-2024/dungmori-fav.png')}}"/>
    <link rel="alternate" href="{{url('')}}" hreflang="vi"/>

    <meta property="og:title" content="@yield('title')"/>
    <meta property="og:description" content="@yield('description')"/>
    <meta property="og:image" content="@yield('image')"/>
    <meta property="og:type" content="website"/>
    <meta property="og:site_name" content="DUNGMORI"/>
    <meta property="og:url" content="@yield('url')"/>
    <meta property="og:image:type" content=".png"/>
    <meta property="og:image:width" content="1200"/>
    <meta property="og:image:height" content="628"/>

    <meta property="fb:app_id" content="1768213996826394"/>
    <meta property="fb:admins" content="100004908811327"/>
    <meta property="fb:admins" content="1725907325"/>

    <!-- preconnect stuffs -->
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link rel="preconnect" href="https://www.youtube.com" crossorigin>
    <link rel="preconnect" href="https://i.ytimg.com" crossorigin>
    <link rel="preconnect" href="https://i9.ytimg.com" crossorigin>
    <link rel="preconnect" href="https://s.ytimg.com" crossorigin>

    <link rel="preload" href="https://fonts.googleapis.com/css?family=Noto+Sans+JP:700&display=swap" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Sans+JP:700&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap"
          as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet"
              href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap"
          as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet"
              href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700;800&display=swap"
          as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet"
              href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700;800&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap"
              media="screen">
    </noscript>

    <link rel="stylesheet"
          href="{{asset('plugin/driver/driver.min.css')}}?{{filemtime('plugin/driver/driver.min.css')}}"/>
    <link rel="stylesheet" href="{{asset('plugin/toastr/toastr.min.css')}}"/>

    <link rel="preload" href="{{asset('css/base.css')}}?{{filemtime('css/base.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="{{asset('css/base.css')}}?{{filemtime('css/base.css')}}" media="screen">
    </noscript>
    <link rel="preload" href="{{asset('css/plugins.css')}}?{{filemtime('css/plugins.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="{{asset('css/plugins.css')}}?{{filemtime('css/plugins.css')}}" media="screen">
    </noscript>
    <link rel="stylesheet" href="{{asset('assets/css/styles.css')}}?{{filemtime('assets/css/styles.css')}}"
          media="screen">
    <script src="{{asset('assets/js/headlibs.js')}}?{{filemtime('assets/js/headlibs.js')}}"></script>
    <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
    @yield('header-css')
    @yield('header-js')

    <?php

    //lưu admin ss vào cookie để giảm đi 1 truy vấn thường suyên
    $adminSession = false;
    if (isset($_COOKIE['admin_ss']) && $_COOKIE['admin_ss'] == 'be972bedb15a') {
        $adminSession = true;
    } else {
        if (Auth::guard('admin')->user()) {
            $adminSession = true;
            setcookie('admin_ss', 'be972bedb15a', time() + (86400 * 30), "/");
        }
    }
    //echo $adminSession;
//    ?>

</head>
<body>
<div class="w-screen relative h-screen overflow-hidden">
    <div class="fixed top-0 left-0 w-screen bg-[#FFFFFFCC] backdrop-blur-sm h-[68px] flex justify-between items-center px-14 shadow z-1" style="padding: 0 56px">
        <div class="flex items-center gap-5 justify-start">
            <svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_b_825_3490)">
                    <path d="M22.7701 0C10.189 0 0 10.189 0 22.7701C0 35.3512 10.189 45.5402 22.7701 45.5402C35.3512 45.5402 45.5402 35.3512 45.5402 22.7701C45.5402 10.189 35.3383 0 22.7701 0ZM17.3911 8.61152H17.4299C17.7661 8.52101 18.1282 8.5598 18.4256 8.74083C19.9643 9.68473 21.4124 10.745 22.7701 11.9087C24.1278 10.7321 25.5889 9.6718 27.1147 8.74083C27.4121 8.5598 27.7741 8.50808 28.1103 8.59859H28.1491C29.2352 8.89599 29.455 10.3571 28.4982 10.939C27.1535 11.7665 25.8734 12.6845 24.6708 13.706C24.0114 14.262 23.3778 14.8439 22.7701 15.4516C22.1624 14.8439 21.5288 14.262 20.8694 13.706C19.6669 12.6845 18.3868 11.7665 17.042 10.939C16.0852 10.3571 16.2921 8.88306 17.3911 8.61152ZM11.0683 11.5596C11.0683 11.5596 11.0812 11.5596 11.0941 11.5467C11.4562 11.2881 11.9475 11.2364 12.3613 11.4174C14.818 12.4906 17.0937 13.9129 19.1238 15.6197C19.7832 16.1757 20.4297 16.7705 21.0375 17.3782C21.6452 17.9989 22.227 18.6454 22.783 19.3177C23.339 18.6454 23.9209 17.9989 24.5286 17.3782C25.1363 16.7576 25.7699 16.1757 26.4423 15.6197C28.4723 13.9129 30.7481 12.4906 33.2048 11.4174C33.6186 11.2364 34.097 11.2751 34.472 11.5467C34.472 11.5467 34.4849 11.5467 34.4978 11.5596C35.3124 12.1415 35.1831 13.3828 34.278 13.7836C32.0152 14.7792 29.9205 16.0852 28.0586 17.6627C27.3862 18.2316 26.7526 18.8264 26.1449 19.46C25.5242 20.1065 24.9424 20.7789 24.3864 21.49C23.8175 22.227 23.2873 22.9899 22.796 23.7787C22.3046 22.9899 21.7745 22.2141 21.2056 21.49C20.6625 20.7789 20.0677 20.1065 19.447 19.46C18.8393 18.8264 18.2057 18.2316 17.5334 17.6627C15.6714 16.0852 13.5767 14.7663 11.3139 13.7836C10.3959 13.3828 10.2795 12.1415 11.0941 11.5596H11.0683ZM18.2963 36.9675H16.4731C16.2662 36.9675 16.0852 36.7994 16.0852 36.5796C16.0335 34.3814 15.4904 32.2997 14.5853 30.4377C14.0681 29.3774 13.4216 28.3818 12.6716 27.4767C10.7062 25.0975 8.02966 23.352 4.95227 22.5374C4.22818 22.3434 3.81441 21.5805 4.06009 20.8694V20.8435C4.2799 20.2229 4.97813 19.8608 5.61171 20.0289C8.70203 20.8435 11.4562 22.4727 13.6414 24.6838C14.3396 25.3949 14.9861 26.1578 15.555 26.9854C16.2403 27.981 16.8351 29.0413 17.3136 30.1662C18.154 32.1445 18.6324 34.3039 18.6842 36.5666C18.6842 36.7865 18.5161 36.9546 18.2963 36.9546V36.9675ZM24.2442 36.5925C24.2442 36.7994 24.0631 36.9804 23.8562 36.9804H21.671C21.4642 36.9804 21.2831 36.8123 21.2831 36.5925C21.2443 34.2909 20.8047 32.0799 20.0418 30.024C19.6151 28.8602 19.0721 27.7353 18.4385 26.688C17.9213 25.8087 17.3265 24.9812 16.68 24.2054C16.0852 23.4942 15.4516 22.8218 14.7663 22.1882C12.6458 20.2487 10.1244 18.7488 7.3185 17.8437C6.49097 17.5722 6.18064 16.5636 6.69785 15.8654V15.8395C7.04697 15.387 7.62883 15.2059 8.15897 15.374C11.0941 16.3309 13.7707 17.8696 16.0593 19.8479C16.7317 20.4297 17.3653 21.0633 17.973 21.7228C18.6066 22.4081 19.1884 23.1451 19.7315 23.908C20.3263 24.7484 20.8564 25.6148 21.3349 26.5328C21.9038 27.6189 22.3822 28.7568 22.7701 29.9464C23.158 28.7697 23.6364 27.6319 24.2054 26.5328C24.6838 25.6148 25.2139 24.7484 25.8087 23.908C26.3518 23.1451 26.9336 22.4081 27.5672 21.7228C28.162 21.0633 28.8085 20.4427 29.4809 19.8479C31.7566 17.8696 34.4332 16.3309 37.3812 15.374C37.9114 15.2059 38.4932 15.374 38.8294 15.8395V15.8654C39.3596 16.5636 39.0492 17.5722 38.2217 17.8437C35.4288 18.7488 32.8945 20.2487 30.7739 22.1882C30.0886 22.8089 29.455 23.4813 28.8602 24.2054C28.2137 24.9812 27.6319 25.8216 27.1017 26.688C26.4681 27.7482 25.938 28.8602 25.4984 30.024C24.7355 32.0799 24.3088 34.2909 24.2571 36.5925H24.2442ZM40.5621 22.5503C37.4976 23.352 34.8081 25.1105 32.8427 27.4896C32.0928 28.3948 31.4463 29.3904 30.9291 30.4507C30.011 32.3126 29.4809 34.3944 29.4292 36.5925C29.4292 36.7994 29.2481 36.9804 29.0413 36.9804H27.2181C26.9983 36.9804 26.8302 36.7994 26.8302 36.5925C26.8819 34.3297 27.3603 32.1704 28.2008 30.1921C28.6792 29.0671 29.274 28.0068 29.9593 27.0112C30.5282 26.1966 31.1747 25.4208 31.873 24.7096C34.0453 22.4986 36.8123 20.8694 39.9026 20.0548C40.5362 19.8867 41.2345 20.2487 41.4413 20.8694V20.8952C41.6999 21.6064 41.2862 22.3822 40.5621 22.5632V22.5503Z" fill="#07403F"/>
                </g>
                <defs>
                    <filter id="filter0_b_825_3490" x="-32" y="-32" width="109.54" height="109.54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feGaussianBlur in="BackgroundImageFix" stdDeviation="16"/>
                        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_825_3490"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_825_3490" result="shape"/>
                    </filter>
                </defs>
            </svg>
            <div>
                <div class="text-[#07403F] font-beanbag text-lg">{{ $course->name }}</div>
                <div class="flex items-start gap-1 font-beanbag text-xs uppercase text-[#07403F]">
                    {{ $thisLesson->name }}
                    @if($thisLesson->require)
                        <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg" class="mt-[2px]">
                            <path d="M3.04894 0.927051C3.3483 0.00574064 4.6517 0.0057404 4.95106 0.927051L5.12257 1.45491C5.25644 1.86694 5.6404 2.1459 6.07363 2.1459H6.62865C7.59738 2.1459 8.00015 3.38551 7.21644 3.95492L6.76741 4.28115C6.41693 4.5358 6.27027 4.98716 6.40414 5.39919L6.57565 5.92705C6.87501 6.84836 5.82053 7.61449 5.03681 7.04508L4.58779 6.71885C4.2373 6.4642 3.7627 6.4642 3.41221 6.71885L2.96319 7.04508C2.17947 7.61449 1.12499 6.84836 1.42435 5.92705L1.59586 5.39919C1.72973 4.98716 1.58307 4.5358 1.23259 4.28115L0.78356 3.95492C-0.000154018 3.38551 0.402622 2.1459 1.37135 2.1459H1.92637C2.3596 2.1459 2.74356 1.86694 2.87743 1.45491L3.04894 0.927051Z" fill="#EC6E23"/>
                        </svg>
                    @endif
                </div>
            </div>
        </div>
        <div class="flex justify-end items-center gap-3">
            @if (Auth::check())
                <div class="tooltip-i">
                    <svg onclick="ga('send', 'event', 'Menu', 'click', 'My courses'); myCourseClick()" width="17"
                         height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                         class="w-5 cursor-pointer stroke-current text-[#07403F]] hover:text-dmr-green-dark icon-color">
                        <path d="M6.90619 3.5C6.90619 5.08344 5.5237 6.43 3.7381 6.43C1.95249 6.43 0.57 5.08344 0.57 3.5C0.57 1.91656 1.95249 0.57 3.7381 0.57C5.5237 0.57 6.90619 1.91656 6.90619 3.5Z"
                              stroke-width="2"/>
                        <path d="M16.1625 3.5C16.1625 5.08344 14.7801 6.43 12.9944 6.43C11.2088 6.43 9.82635 5.08344 9.82635 3.5C9.82635 1.91656 11.2088 0.57 12.9944 0.57C14.7801 0.57 16.1625 1.91656 16.1625 3.5Z"
                              stroke-width="2"/>
                        <path d="M6.90619 12.1665C6.90619 13.7499 5.5237 15.0965 3.7381 15.0965C1.95249 15.0965 0.57 13.7499 0.57 12.1665C0.57 10.5831 1.95249 9.2365 3.7381 9.2365C5.5237 9.2365 6.90619 10.5831 6.90619 12.1665Z"
                              stroke-width="2"/>
                        <path d="M16.1625 12.1665C16.1625 13.7499 14.7801 15.0965 12.9944 15.0965C11.2088 15.0965 9.82635 13.7499 9.82635 12.1665C9.82635 10.5831 11.2088 9.2365 12.9944 9.2365C14.7801 9.2365 16.1625 10.5831 16.1625 12.1665Z"
                              stroke-width="2"/>
                    </svg>
                    <span class="tooltiptext">Khóa học của tôi</span>
                </div>
                <div id="my-course" style="display: none;">
                    <div class="my-course-main">
                        <div class="my-course-container">
                            <div class="title-box">
                                <span class="title">Khóa học của tôi</span><br>
                                <span class="description">Các khóa bạn đang sở hữu, học ngay!</span>
                                <i class="fa fa-window-close" aria-hidden="true" onclick="myCourseClick()"></i>
                            </div>
                            <hr>
                            <div id="my-course-content" class="my-course-content" style="display: none;">
                                <div v-if="myCourses.length === 0" class="empty-box">
                                    <img class="lazyload object-cover"
                                         data-src="{{url('assets/img/img_empty.png')}}">
                                    <span>Bạn không sở hữu khóa học nào</span>
                                    <a href="{{url('/bang-gia')}}">Mua ngay</a>
                                </div>
                                <a v-for="course in myCourses" :href="'{{url('/khoa-hoc')}}/' + course.SEOurl">
                                    <div class="my-course-item">
                                        <img :src="'{{asset('cdn/course/small')}}/' + course.avatar_name">
                                        <span>
                                                <i class="fa fa-circle" aria-hidden="true"></i> <b>@{{ course.name }}</b> <i
                                                class="fa fa-circle" aria-hidden="true"></i>
                                            </span>
                                        <span>
                                                <i class="fa fa-circle" aria-hidden="true"></i> Hết hạn:&nbsp;<b>@{{ course.expired_day }}</b> <i
                                                class="fa fa-circle" aria-hidden="true"></i>
                                            </span>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <script type="application/javascript">
                    function myCourseClick(onlyClose = false) {
                        var myCourseDiv = document.getElementById("my-course");
                        if (!onlyClose && myCourseDiv.style.display === 'none') {
                            $('#my-course').css('display', 'block');
                            myCourseDiv.classList.remove("my-course-hide");
                            myCourseDiv.classList.add("my-course-show");
                            if (myCoursesVue) {
                                myCoursesVue.getMyCourses();
                            }
                        } else {
                            myCourseDiv.classList.remove("my-course-show");
                            myCourseDiv.classList.add("my-course-hide");
                            setTimeout(function () {
                                $('#my-course').css('display', 'none');
                            }, 200);
                        }
                    }

                    var mycourse = document.getElementById('my-course');
                    mycourse.onclick = function (event) {
                        if (event.target == mycourse) {
                            myCourseClick();
                        }
                    }
                </script>
            @endif


            <a href="{{url('/khoa-hoc')}}"
               class="cursor-pointer text-[#07403F]] stroke-current fill-current hover:text-dmr-green-dark icon-color"
               onclick="ga('send', 'event', 'Menu', 'click', 'Store')">
                <div class="tooltip-i">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                         class="w-4">
                        <path d="M1.02191 1.28948H22.6139V20.2245C22.6139 21.6919 21.4243 22.8815 19.957 22.8815H3.67886C2.21146 22.8815 1.02191 21.6919 1.02191 20.2245V1.28948Z"
                              stroke-width="2.5"/>
                        <path d="M5.41602 7.4082C7.5498 11.2654 13.0484 16.6656 17.9725 7.4082" stroke-width="2.5"/>
                    </svg>
                    <span class="tooltiptext">Cửa hàng</span>
                </div>
            </a>
            <div id="maintain-container" style="display: none;background: none; text-align: center;">
                <img class="lazyload object-cover" data-src="{{ asset('assets/img/new_home/maintain-web.png') }}"
                     alt="" style="max-width: 50%; margin: auto;">
            </div>
            <style>
                #jlpt-gift-container {
                    display: none;
                    background: none;
                    text-align: center;
                }

                #jlpt-gift-container .jlpt-gift-content {
                    background: white;
                    border-radius: 15px;
                }

                #jlpt-gift-container .jlpt-gift-content > p {
                    font-family: 'Quicksand';
                    font-style: normal;
                    font-weight: 500;
                    font-size: 30px;
                    line-height: 1.61;
                    text-transform: uppercase;
                }

                #jlpt-gift-container .jlpt-gift-content > p:nth-child(2) {
                    font-weight: bold;
                    color: #96D962;
                }

                #jlpt-gift-container .jlpt-gift-content .quote {
                    width: 388px;
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 1.61;
                    margin-top: 20px;
                }

                .jlpt-gift-content .btn {
                    margin-top: 30px;
                    padding: 15px 34px;
                    font-family: 'Montserrat';
                    font-style: normal;
                    font-weight: 700;
                    font-size: 20px;
                    line-height: 1.2;
                    color: #FFFFFF;
                    border-radius: 15px;
                }

                .jlpt-gift-content .btn-danger {
                    background-color: #FB6D3A;
                    margin-right: 20px;
                }

                .jlpt-gift-content .btn-info {
                    background-color: #1093F1;
                }

                .jlpt-gift-policy {
                    margin-left: 24px;
                    max-width: 350px;
                    text-align: left;
                }

                .jlpt-gift-policy .uppercase {
                    text-transform: uppercase;
                }

                .jlpt-gift-policy .text-red {
                    color: #FB6D3A;
                }

                .jlpt-gift-policy .text-blue {
                    color: #0E65E5;
                }

                input::-webkit-outer-spin-button,
                input::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }

                .btn-upload {
                    font-family: Quicksand;
                    background: #F7FFF0;
                    border: 1px solid #96D962;
                    border-radius: 10px;
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: center;
                    padding: 8px 50px;
                    gap: 10px;
                    color: green !important;
                    font-size: 16px !important;
                    font-weight: 400 !important;
                    margin-top: 0 !important;
                }

                .point-input {
                    position: relative;
                }

                .point-label {
                    position: absolute;
                    top: -10px;
                    left: 9px;
                    background: white;
                    color: #BDBDBD !important;
                }

                .fancybox-slide:before {
                    height: 0 !important;
                }

                @media only screen and (max-width: 992px) {
                    .two-columns {
                        flex-flow: column;
                        align-items: center;
                    }

                    .jlpt-gift-content {
                        /*padding: 40px 5px;*/
                    }

                    .fancybox-slide > * {
                        padding: 0;
                    }
                }

                #point-input-1,
                #point-input-2,
                #point-input-3,
                #point-input-total {
                    font-weight: bold;
                    font-size: 13px;
                    border-bottom: 1px solid #000;
                    width: 5%;
                    text-align: right;
                    line-height: 1;
                }
            </style>
            @if (!Auth::check())
                @include('frontend._layouts.auth')
                <div class="flex items-center gap-1 bg-[#EC6E23] border-2 border-white rounded-[50px] py-2 px-5 text-white auth-button">
                    <div data-fancybox data-animation-duration="300" data-src="#auth-container"
                         class="cursor-pointer no-underline uppercase font-semibold">
                        <div class="text-white" id="text-login"
                             onclick="ga('send', 'event', 'Menu', 'click', 'Login'); swichTab('login')">Đăng nhập
                        </div>
                    </div>
                    <span>/</span>
                    <div data-fancybox data-animation-duration="300" data-src="#auth-container"
                         class="!text-white cursor-pointer no-underline uppercase font-semibold">
                        <div class="text-white" id="text-register"
                             onclick="ga('send', 'event', 'Menu', 'click', 'Register'); swichTab('register')">Đăng
                            ký
                        </div>
                    </div>
                </div>
            @else
                <div class="text-white hover:text-dmr-green-dark has-badge cursor-pointer icon-color"
                     onclick="ga('send', 'event', 'Menu', 'click', 'Chat'); showChatbox()">
                    <div id="auth-container" v-if="countUnreadMess > 0">
                        <span class="badge--red countMess" v-html="countUnreadMess"></span>
                    </div>
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                         class="w-5 stroke-current">
                        <path d="M8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H16C20 2 22 4 22 8V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19Z" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M7 8H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M7 13H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div id="announce" class="annouce">
                    <user-annouce></user-annouce>
                </div>
                <div class="header-avatar">
                    <div class="dropdown-toggle user-info-box" type="button" data-toggle="dropdown">
                        <img class="user-avatar-circle"
                             @if (Auth::user()->avatar == null)
                                 src="{{url('assets/img/default-avatar.jpg')}}"
                             @else
                                 src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                            @endif
                        />
                    </div>
                    <ul class="dropdown-menu user-menu">
                        <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>
                        <li><a href="{{url('/account')}}"
                               onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Profile')"><i
                                    class="zmdi zmdi-account-box"></i> Thông tin cá nhân</a></li>
                        <li><a href="{{url('/account/courses')}}"
                               onclick="ga('send', 'event', 'Avatar Menu', 'click', 'My courses from ava list')"><i
                                    class="zmdi zmdi-dns"></i> Khóa học của tôi</a></li>
                        <li><a href="{{url('/account/score')}}"
                               onclick="ga('send', 'event', 'Avatar Menu', 'click', 'My improvement')"><i
                                    class="fa fa-trophy trophy-icon"></i>Sự tiến bộ của tôi&nbsp;&nbsp;<div
                                    class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span
                                        class="tooltiptext">Theo dõi sự tiến bộ của bạn thông qua 2 bài thi giữa kỳ và cuối kỳ</span>
                                </div>
                            </a></li>
                        <li><a href="{{url('/account/achievement')}}"
                               onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Thành tích')"><i
                                    class="fa fa-star"></i>Thành tích&nbsp;&nbsp;<div class="tooltip-i"><i
                                        class="fa fa-info-circle" aria-hidden="true"></i><span
                                        class="tooltiptext">Phần thưởng cho sự chăm chỉ của bạn, duy trì và cải thiện thứ hạng trên bảng xếp hạng nhé!</span>
                                </div>
                            </a></li>
                        <li><a href="{{url('/account/billing')}}"
                               onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Payment history')"><i
                                    class="zmdi zmdi-card"></i> Lịch sử thanh toán</a></li>
                        <li><a href="{{url('/ho-tro/chinh-sach-gia-han-khoa-hoc-online')}}"
                               onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Chính sách gia hạn')"><i
                                    class="fa fa-shield"></i> Chính sách gia hạn</a></li>
                        <li><a href="{{url('/account?focus=changePass')}}"
                               onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Change passwrod')"><i
                                    class="zmdi zmdi-shield-security"></i> Thay đổi mật khẩu</a></li>
                        <div class="dropdown-divider"></div>
                        <li><a onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Logout'); logout()"><i
                                    class="zmdi zmdi-power"></i> Đăng xuất</a></li>
                    </ul>
                </div>
            @endif

            <script type="application/javascript">
                function showChatbox() {
                    if (typeof chatbox !== 'undefined' && chatbox) {
                        chatbox.initChat();
                        $("#chat-box").css('display', 'flex');
                        $("#mess-input").focus();
                        chatbox.goToBottom();
                    }
                }
                @if (Auth::check())
                var chatUser = {{ Auth::user()->id }};
                var at = "{{ session('_tokenApp') }}";
                var api = "{{ config('app.api_url') }}";
                @else
                var chatUser = localStorage.getItem('incognito_user');
                if (chatUser) {
                    chatUser = parseInt(chatUser);
                } else {
                    chatUser = (Math.floor(Math.random() * 1000000) + 1) * -1;
                    localStorage.setItem('incognito_user', chatUser);
                }
                @endif
                if (chatUser) {
                    var socketServer = "{{ config('app.socket_server') }}";

                    var socket = io.connect(socketServer, {
                        query: {
                            type: 'user',
                            from: 'web',
                            userId: chatUser
                        }
                    });

                    // Check receive new message
                    @if (Auth::check())
                    socket.on('send_new_message_enc', async function (message) {
                        if (typeof chatbox !== 'undefined') {
                            message = JSON.parse(dmsg(message));
                            chatbox.receiveNewMsg(message);
                            if (notiCount) {
                                notiCount.updateMessageCount(message);
                            }
                            if (message && parseInt(message.receiverId) == chatUser) {
                                if (!countAdminMess) {
                                    countAdminMess = 0;
                                }
                                countAdminMess = countAdminMess + 1;

                                if (message.senderType == 'admin' && $('#chat-box').css('display') === 'none') {
                                    showChatbox();
                                    chatbox.goToBottom();
                                }
                            }
                        }
                    });
                    @else
                    socket.on('send_new_message', async function (message) {
                        if (typeof chatbox !== 'undefined') {
                            chatbox.receiveNewMsg(message);
                        }
                        if (typeof notiCount !== 'undefined') {
                            notiCount.updateMessageCount(message);
                        }
                        if (message && parseInt(message.receiverId) == chatUser) {
                            if ($('#chat-box').css('display') === 'none') {
                                showChatbox();
                                chatbox.goToBottom();
                            }
                        }
                    });
                    @endif

                    socket.on('send_new_message_group_enc', async function (message) {
                        if (typeof chatbox !== 'undefined') {
                            message = JSON.parse(dmsg(message));
                            chatbox.receiveNewMsg(message);
                        }
                        if (typeof notiCount !== 'undefined') {
                            notiCount.updateMessageCount(message);
                        }
                    });
                }
            </script>
        </div>

    </div>
    {{-- cho phép admin xóa cache redis --}}
    @if($adminSession == true)
        <div class="refresh-cache" onclick="refreshCache()">
            <i class="fa fa-refresh" id="refresh-cache"></i>
            <i class="fa fa-check-circle" id="refresh-done" style="display: none; color: green;"></i> cache
        </div>
        <div class="refresh-cache" onclick="refreshMjtCache()" style="top: 230px;">
            <i class="fa fa-refresh" id="refresh-mjt-cache"></i>
            <i class="fa fa-check-circle" id="refresh-mjt-done" style="display: none; color: green;"></i> mjt
        </div>
    @endif

    {{-- load nội dung --}}
    @yield('content')

    {{-- hộp thoại chat --}}
    {{-- @if(Auth::check()) --}}
    @include('frontend._layouts.chatbox')
    {{-- @endif --}}
    <script type="application/javascript">
        document.addEventListener("DOMContentLoaded", function (event) {
            document.querySelectorAll('img').forEach(function (img) {
                img.onerror = function () {
                    this.style.display = 'none';
                };
            })
        });
    </script>


    {{--chống đăng nhập trên nhiều máy/chỉ gọi khi người dùng đang đăng nhập ko phải admin --}}
    <script type="application/javascript">
        var lastFingerprint = null;
        var embed = "";
    </script>
    <script type="application/javascript">
        function goTo(url) {
            window.location.href = url;
        }

        function goToWindow(url) {
            window.open(url, "_blank");
        }
    </script>
    @if(Auth::check() && $adminSession == false)
        <script type="application/javascript">
            var lastFingerprint = "{{Auth::user()->fingerprint}}";
            var lastBrowser = "{{Auth::user()->last_browser}}";
            var lastOS = "{{Auth::user()->last_os}}";
            var embed = "{{Auth::user()->embed}}";
        </script>
    @endif

    {{-- kích tài khoản đã bị khóa --}}
    @if(Auth::check() && Auth::user()->blocked == 1)
        <script type="text/javascript">logout()</script>
    @endif

    {{-- import default js contain setting  --}}
    <script src="{{asset('plugin/vue/vue.min.js')}}" type="application/javascript"></script>
    <script src="{{asset('plugin/lazysizes/lazysizes.min.js')}}" type="application/javascript"></script>
    <script src="{{asset('plugin/toastr/toastr.min.js')}}" type="application/javascript"></script>

    {{-- lấy ra thông tin Agent của trình duyệt/ check việc chống đăng nhập nhiều máy,
        tracking.js được gộp vào đây dể bảo mật --}}
    <script src="{{asset('assets/js/app.js')}}?{{filemtime('assets/js/app.js')}}" type="application/javascript"></script>
    <script type="application/javascript">
        var enableFIV = false;
        @if($adminSession == true) enableFIV = true; @endif
    </script>

    {{-- import các template của vue component để hỗ trợ các version cũ  --}}
    @include('frontend._layouts.template')

    {{-- import vanila emoji picker  --}}
    <script src="{{asset('plugin/vanilla-emoji-picker/emojiPicker.js')}}?{{filemtime('plugin/vanilla-emoji-picker/emojiPicker.js')}}"
            type="application/javascript"></script>
    <script src="{{asset('plugin/vanilla-emoji-picker/emojiContenteditable.js')}}?{{filemtime('plugin/vanilla-emoji-picker/emojiContenteditable.js')}}"
            type="application/javascript"></script>
    {{-- thư viện moment js --}}
    <script src="{{asset('plugin/moment/moment.min.js')}}?{{filemtime('plugin/moment/moment.min.js')}}"
            type="application/javascript">
        moment.locale('vi')
    </script>
    <script src="{{asset('plugin/crypto/crypto.js')}}?{{filemtime('plugin/crypto/crypto.js')}}" type="application/javascript"></script>
    @if(Auth::check())
        {{-- import vue my_course  --}}
        <script src="{{asset('assets/backend/js/my_course.js')}}?{{filemtime('assets/backend/js/my_course.js')}}"
                type="application/javascript"></script>
    @endif
    @if (Route::currentRouteName() != 'home')
        {{-- import all vue components  --}}
        <script src="{{asset('assets/js/components.js')}}?{{filemtime('assets/js/components.js')}}"
                type="application/javascript"></script>
    @endif
    <script src="{{asset('assets/js/crypto.js')}}?{{filemtime('assets/js/crypto.js')}}" type="application/javascript"></script>
    <script src="{{asset('plugin/gsap/gsap.min.js')}}?{{filemtime('plugin/gsap/gsap.min.js')}}"
            type="application/javascript"></script>
    <script src="{{asset('plugin/gsap/MotionPathPlugin.min.js')}}?{{filemtime('plugin/gsap/MotionPathPlugin.min.js')}}"
            type="application/javascript"></script>
    <script src="{{asset('plugin/gsap/ScrollTrigger.min.js')}}?{{filemtime('plugin/gsap/ScrollTrigger.min.js')}}"
            type="application/javascript"></script>
    {{-- nếu đã login -> import component thông báo /else/ import auth neu chua login  --}}
    @if(Auth::check())
        <script type="application/javascript">
            var myUserId = "{{Auth::user()->id}}";
            var name = "{{Auth::user()->name}}";
            var countMess = "{{ session('_countMess') }}";
            var countAdminMess = "{{ session('_countAdminMess') }}";
            countAdminMess = parseInt(countAdminMess);
            var countNoti = "{{ session('_countNoti') }}";
        </script>
        <script src="{{asset('assets/js/count_message.js')}}?{{filemtime('assets/js/count_message.js')}}" type="application/javascript"></script>
        <script src="{{ asset('js/announce.js') }}?{{filemtime('js/announce.js')}}" type="application/javascript"></script>
    @else
        <script src="{{asset('assets/js/GVuZ3RoKCk.js')}}?{{filemtime('assets/js/GVuZ3RoKCk.js')}}"
                type="application/javascript"></script>
    @endif
    <script src="{{ asset('assets/js/chatbox.js') }}?{{filemtime('assets/js/chatbox.js')}}"
            type="application/javascript"></script>
    <script src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}" type="application/javascript"></script>
    <script src="{{asset('plugin/jquery/axios.min.js')}}" type="application/javascript"></script>

    @yield('footer-js')
    @yield('bottom-js')
    @yield('lesson-bottom')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.perfect-scrollbar/1.5.6/perfect-scrollbar.min.js" integrity="sha512-gcLXgodlQJWRXhAyvb5ULNlBAcvjuufaOBRggyLCtCqez+9jW7MxP3Is/9serId1YmNZ0Lx1ewh9z2xBwwZeKg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    {{-- Global site tag (gtag.js) - Google Analytics  --}}
    {{-- <script src="https://www.googletagmanager.com/gtag/js?id=UA-131128751-4"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'UA-131128751-4');
    </script> --}}

    {{--        <script src="{{ asset('assets/js/counter.js') }}?{{filemtime('assets/js/counter.js')}}"></script>--}}


    {{-- đoạn theo dõi google analytics / chỉ bật trên server thật  --}}
    @if($_SERVER['SERVER_NAME'] == "dungmori.com")
        <script type="application/javascript">
            (function (i, s, o, g, r, a, m) {
                i['GoogleAnalyticsObject'] = r;
                i[r] = i[r] || function () {
                    (i[r].q = i[r].q || []).push(arguments)
                }, i[r].l = 1 * new Date();
                a = s.createElement(o),
                    m = s.getElementsByTagName(o)[0];
                a.async = 1;
                a.src = g;
                m.parentNode.insertBefore(a, m)
            })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
            ga('create', 'UA-98604763-1', 'auto');
            ga('send', 'pageview');
        </script>

        <!-- Facebook Pixel Code -->
        <script type="application/javascript">
            !function (f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function () {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '511629070106054');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none"
                 src="https://www.facebook.com/tr?id=511629070106054&ev=PageView&noscript=1"/>
        </noscript>
        <!-- End Facebook Pixel Code -->

        <!-- Hotjar Tracking Code for www.dungmori.com -->
        <script type="application/javascript">
            (function (h, o, t, j, a, r) {
                h.hj = h.hj || function () {
                    (h.hj.q = h.hj.q || []).push(arguments)
                };
                h._hjSettings = {hjid: 1928619, hjsv: 6};
                a = o.getElementsByTagName('head')[0];
                r = o.createElement('script');
                r.async = 1;
                r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
                a.appendChild(r);
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
        </script>
    @endif

    {{-- Global site tag (gtag.js) - Google Analytics  --}}
    <script src="https://www.googletagmanager.com/gtag/js?id=UA-131128751-4" type="application/javascript"></script>
    <script type="application/javascript">
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());
        gtag('config', 'UA-131128751-4');
    </script>

    <script type="application/javascript">
        (function () {
            var ta = document.createElement('script');
            ta.type = 'text/javascript';
            ta.async = true;
            ta.src = 'https://analytics.tiktok.com/i18n/pixel/sdk.js?sdkid=BPU67QVC2JVVVND7OQEG';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(ta, s);
        })();
    </script>

    <script type="application/javascript">
        var element = $('#app-btns');
        var bounce = gsap.to(element, {duration: 0.6, y: 10, ease: "circ.in", repeat: -1, yoyo: true, paused: true})

        function scrollToId(id) {
            if (document.getElementById(id)) {
                document.getElementById(id).scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            }
            bounce.play()
        }

        function stopBounce() {
            bounce.pause()
        }
    </script>
    @if(!Auth::check())
        <script type="application/javascript">
            var search = window.location.search;
            var qs = new URLSearchParams(search);
            if (qs.has("action")) {
                var action = qs.get("action");
                if (action == "login") {
                    var loginBtn = document.getElementById('text-login');
                    loginBtn.click();
                }
            }
        </script>
    @endif
</div>
</body>
</html>
