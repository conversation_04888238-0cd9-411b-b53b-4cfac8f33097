
<script type="text/x-template" id="test-ad-rank">
  <div class="test-online__rank">
    @if (auth()->check() && auth()->user()->is_tester)
      <div class="test-online__rank-header">
        <div @click="tab = 'day'" :class="{active: tab == 'day'}">TRONG NGÀY</div>
        <div @click="tab = 'all'" :class="{active: tab == 'all'}">TB CẢ KHOÁ</div>
      </div>
    @endif
    <div class="test-online__rank-body">
      <div class="test-online__rank-filter grid grid-cols-2">
        <template v-if="tab == 'day'">
          <el-date-picker
              v-model="date"
              type="date"
              placeholder="Chọn ngày"
              :picker-options="options"
              format="dd/MM/yyyy"
              value-format="yyyy-MM-dd"
          >
          </el-date-picker>
          <el-select filterable ref="examSelector" v-model="exam_id" placeholder="Bài kiểm tra">
            <el-option
                v-for="exam in exams"
                :key="'exam-' + exam.id"
                :label="exam.name"
                :value="exam.id"
            >
            </el-option>
          </el-select>
        </template>
        <template v-if="tab == 'all'">
          <el-select filterable ref="groupSelector" v-model="level" placeholder="Cấp độ" @change="getRanking">
            <el-option
                v-for="group in ['N1','N2','N3','N4','N5']"
                :key="'group-' + group"
                :label="group"
                :value="group"
            >
            </el-option>
          </el-select>
          <el-date-picker
              v-model="from"
              type="date"
              placeholder="Từ ngày"
              :picker-options="options"
              format="dd/MM/yyyy"
              value-format="yyyy-MM-dd"
              @change="getRanking"
          >
          </el-date-picker>
          <el-date-picker
              v-model="to"
              type="date"
              placeholder="Đến ngày"
              :picker-options="options"
              format="dd/MM/yyyy"
              value-format="yyyy-MM-dd"
              @change="getRanking"
          >
          </el-date-picker>
        </template>
      </div>
      <div style="width: 70%; margin: auto; margin-top: 40px; font-family: Quicksand, Arial, sans-serif">
        <div v-if="!userResult" class="text-center mb-3 mt-3">Bạn chưa làm bài thi</div>
        <div v-else-if="userResultIndex > 10" class="flex justify-between items-center text-md mt-3 mb-3 test-online__rank-student highlight" :style="rankBg(userResultIndex)">
          <div style="width: 100px; text-align: center" class="test-online__rank-order" :style="rankStyle(userResultIndex)">
            @{{ userResultIndex + 1 }}
          </div>
          <div class="flex-1 text-left flex items-center">
            <img class="test-online__rank-avatar mr-2"
                 :src="url + '/cdn/avatar/small/' + userResult?.user?.avatar"
            />
            <span>@{{ userResult.user?.name }}</span>
          </div>
          <div style="width: 100px; text-align: center" v-if="tab == 'day'">@{{ userResult.total_score }}</div>
          <div style="width: 100px; text-align: center" v-if="tab == 'all'" v-cloak>
            <p>@{{ userResult.average | point }}</p>
          </div>
        </div>
        <div class="flex justify-between items-center text-bold text-lg">
          <div style="width: 40px; text-align: right"></div>
          <div style="width: 70px; text-align: center">Top</div>
          <div class="flex-1 text-left">Học viên</div>
          <div style="width: 100px; text-align: center">Điểm</div>
        </div>
        <div v-for="(result, index) in ladder" class="flex justify-between items-center text-md mt-3 test-online__rank-student" :class="[index < 10 ? 'highlight' : '']" :style="rankBg(index)">
          <div style="width: 40px; text-align: right" :style="rankStyle(index)"><i class="fa fa-user mr-1" v-if="userResult && result.id == userResult.id"></i></div>
          <div style="width: 70px; text-align: center" class="test-online__rank-order" :style="rankStyle(index)">
            @{{ index + 1}}
          </div>
          <div class="flex-1 text-left flex items-center">
            <img class="test-online__rank-avatar mr-2"
                 :src="url + '/cdn/avatar/small/' + result.user?.avatar"
            />
            <span>@{{ result.user?.name }}</span>
          </div>
          <div style="width: 100px; text-align: center" v-if="tab == 'day'">@{{ result.total_score }}</div>
          <div style="width: 100px; text-align: center" v-if="tab == 'all'" v-cloak>
            <p>@{{ result.average | point }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</script>
