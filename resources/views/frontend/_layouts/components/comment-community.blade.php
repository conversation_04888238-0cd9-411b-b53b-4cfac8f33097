
{{-- template của comment community--}}
<script type="text/x-template" id="comment-community-template">
  <div class="list-comments" :style="'background-color:'+ background">

    <div class="input-comment-container" v-if="meid != null">
      <div class="form_action">
        <img v-if="avatar == null || avatar == ''" class="me-avatar" :src="url + '/assets/img/default-avatar.jpg'">
        <img v-if="avatar != null && avatar != ''" class="me-avatar" :src="url + '/cdn/avatar/small/'+ avatar">
        <textarea class="input-comment" :id="'comment-content-'+ postid" rows="1" placeholder="Bình luận..."></textarea>
        <span class="pick-image" data-toggle="tooltip" data-placement="bottom" title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif">
                <i class="zmdi zmdi-camera"></i>
                <form class="form-pick-image" id="cmt-attachment-form">
                    <input type='file' id="commentImagePicked" name="commentImagePicked" @change="previewImage" accept=".png, .jpg, .jpeg, .gif" />
                </form>
            </span>
        <span class="post-comment-btn" v-on:click="postNewComment">Trả lời</span>
        <div class="preview-image" id="preview-image-cmt"></div>
      </div>
    </div>

    <li v-if="showLoadingNewComment == true" class="comment-item" style='text-align: center;'>loading...</li>

    <li class="comment-item" v-for="cmt in listComments" :id="'cmt-item-'+ cmt.id">
      <a v-if="cmt.user_id == 0" class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
        <img class="avatar" :src="cdn + '/assets/img/oglogo.png'">
        <i class="zmdi zmdi-check-circle"></i>
      </a>
      <a v-if="cmt.user_id != 0" class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
        <img v-if="cmt.avatar == null || cmt.avatar == ''" class="avatar" :src="cdn + '/assets/img/default-avatar.jpg'">
        <img v-if="cmt.avatar != null && cmt.avatar != ''" class="avatar" :src="cdn + '/cdn/avatar/small/'+ cmt.avatar">
        <i v-show="cmt.is_tester == 1" class="zmdi zmdi-check-circle admin-tick"></i>
      </a>
      <div class="comment-content">
        <p class="name" style="width: 100%;">
          <b class="red" v-if="cmt.user_id == 0" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(0)">Dũng Mori</b>
          <b class="red" v-if="cmt.user_id != 0" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
            @{{cmt.name}}
          </b>
          <span :id="'cmt-content-div-'+ cmt.id" v-html="printInfo(cmt.content)"></span>
        </p>
        <p v-if="cmt.img != null" class="preview-image">
          <a class="popup-preview-image" :href="cdn + '/cdn/comment/default/'+ cmt.img" data-fancybox>
            <img class="preview-thumbnail" :src="cdn + '/cdn/comment/small/'+ cmt.img"/>
          </a>
        </p>
        <p class="comment-action">

                    <span class="love-btn" v-on:click="likeCmt(cmt.id)">
                        <span v-show="cmt.count_like == 0"><i class='bx bxs-like'></i></span>
                        <span v-show="cmt.count_like > 0"><i class="bx bxs-like" style="color: green;"></i></span>
                        <span v-show="cmt.count_like == 0">like</span>
                        <span v-show="cmt.count_like > 0" v-bind:style="(cmt.liked == true) ? 'color: green': ''">@{{cmt.count_like}} likes</span>
                    </span>

          &nbsp;<span style="opacity: 0.4;">•</span>&nbsp;

          <a v-if="cmt.reply.length != 0" class="load-more-reply" role="button" data-toggle="collapse" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id" style="color: green;">
            <i class="zmdi zmdi-comments"></i> @{{cmt.reply.length}} phản hồi
          </a>

          <span v-if="cmt.reply.length == 0" class="answer" data-toggle="collapse" :id="'answer-reply-'+ cmt.id" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id">phản hồi</span>


          &nbsp;<span style="opacity: 0.4;">•</span>&nbsp;

          <span class="time" style="font-weight: 400px; color: #333; font-size: 12px;"> @{{ prettyDate(cmt.created_at) }}</span>
        </p>
        <div class="reply-container">
          <div class="collapse" :id="'reply-'+ cmt.id">

            <div class="child-comment-item" v-for="(childCmt, index) in cmt.reply" :id="'reply-item-'+ childCmt.id">
              <a v-if="childCmt.user_id == 0" class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(0)">
                <img class="avatar" :src="cdn + '/assets/img/oglogo.png'">
                <i class="zmdi zmdi-check-circle"></i>
              </a>
              <a v-if="childCmt.user_id != 0" class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(childCmt.user_id)">
                <img v-if="childCmt.avatar == null || childCmt.avatar == ''" class="avatar" :src="cdn + '/assets/img/default-avatar.jpg'">
                <img v-if="childCmt.avatar != null && childCmt.avatar != ''" class="avatar" :src="cdn + '/cdn/avatar/small/'+ childCmt.avatar">
                <i v-show="childCmt.is_tester == 1" class="zmdi zmdi-check-circle admin-tick"></i>
              </a>
              <div class="comment-content">
                <p class="child-name">
                  <b class="red" v-if="childCmt.user_id == 0" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(0)">Dũng Mori </b>
                  <b class="red" v-if="childCmt.user_id != 0" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(childCmt.user_id)">
                    @{{childCmt.name}}
                  </b>
                  <span  :id="'cmt-content-div-'+ childCmt.id" v-html="printInfo(childCmt.content)"></span>
                </p>

                <p v-if="childCmt.img != null" class="preview-image">
                  <a class="popup-preview-image" :href="cdn + '/cdn/comment/default/'+ childCmt.img" data-fancybox>
                    <img class="preview-thumbnail" :src="cdn + '/cdn/comment/small/'+ childCmt.img"/>
                  </a>
                </p>

                <p class="child-comment-action">

                                    <span class="love-btn" v-on:click="likeReply(cmt.id, childCmt.id)">
                                        <span v-show="childCmt.count_like == 0"><i class='bx bxs-like'></i></span>
                                        <span v-show="childCmt.count_like > 0"><i class="bx bxs-like" style="color: green;"></i></span>
                                        <span v-show="childCmt.count_like == 0"> like</span>
                                        <span v-show="childCmt.count_like > 0" v-bind:style="(childCmt.liked == true) ? 'color: green': ''">@{{childCmt.count_like}} likes</span>
                                    </span>

                  &nbsp;<span style="opacity: 0.4;">•</span>&nbsp;
                  <span class="answer">trả lời</span>
                  &nbsp;<span style="opacity: 0.4;">•</span>&nbsp;

                  <span class="time">@{{ prettyDate(childCmt.created_at) }}</span>
                </p>
              </div>

              <div class="dropdown cmt-option">
                <div class="dropdown-toggle" type="button" :id="'dropdownMenuChildCmt' + childCmt.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                  <i class='bx bx-dots-horizontal-rounded'></i>
                </div>
                <ul class="dropdown-menu" :aria-labelledby="'dropdownMenuChildCmt' + childCmt.id">

                  @if(Auth::user())
                    <li v-if="childCmt.user_id == '{{Auth::user()->id}}'" v-on:click="showEditCmt(childCmt)"  data-fancybox data-animation-duration="300" data-src="#edit-cmt-popup"><a><i class='bx bxs-edit' ></i> Sửa</a></li>
                    <li v-if="childCmt.user_id == '{{Auth::user()->id}}'"v-on:click="delReply(childCmt.id, childCmt.parent_id)"><a><i class='bx bxs-trash'></i> Xóa</a></li>
                    <li v-if="childCmt.user_id != '{{Auth::user()->id}}'"v-on:click="reportCmt(childCmt.id)"><a><i class='bx bxs-flag-alt' ></i> Báo cáo</a></li>

                    @if(Auth::user()->is_tester == 1)
                      <li role="separator" class="divider"></li>
                      <li style="cursor: pointer;" v-on:click="delComment(childCmt.id)">
                        <a style="cursor: pointer; color: red;"><i class='bx bxs-user-detail' ></i> Xóa (admin)</a>
                      </li>
                    @endif
                  @endif
                </ul>
              </div>
            </div>

            <div class="reply-form" v-if="meid != null">
              <img v-if="avatar == null || avatar == ''" class="me-avatar" :src="url + '/assets/img/default-avatar.jpg'">
              <img v-if="avatar != null && avatar != ''" class="me-avatar" :src="url + '/cdn/avatar/small/'+ avatar">
              <textarea class="input-comment" :id="'reply-input-content-'+ cmt.id" rows="1" placeholder="Bình luận..."></textarea>

              <span class="pick-image" data-toggle="tooltip" data-placement="bottom" title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif">
                                <i class="zmdi zmdi-camera"></i>
                                <form class="form-pick-image" :id="'cmt-attachment-form-'+ cmt.id">
                                    <input type='file' :id="'commentImagePicked'+ cmt.id" name="commentImagePicked" @change="previewImageReply" accept=".png, .jpg, .jpeg, .gif"/>
                                </form>
                            </span>

              <span class="post-comment-btn" v-on:click="postNewAnswer(cmt.id)">Trả lời</span>
              <div class="preview-image" :id="'preview-image-cmt-'+ cmt.id"></div>
            </div>

          </div>
        </div>
      </div>

      <div class="dropdown cmt-option">
        <div class="dropdown-toggle" type="button" :id="'dropdownMenuCmt' + cmt.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
          <i class='bx bx-dots-horizontal-rounded'></i>
        </div>
        <ul class="dropdown-menu" :aria-labelledby="'dropdownMenuCmt' + cmt.id">
          @if(Auth::user())
            <li v-if="cmt.user_id == '{{Auth::user()->id}}'" v-on:click="showEditCmt(cmt)"  data-fancybox data-animation-duration="300" data-src="#edit-cmt-popup"><a><i class='bx bxs-edit' ></i> Sửa</a></li>
            <li v-if="cmt.user_id == '{{Auth::user()->id}}'"v-on:click="delComment(cmt.id)"><a><i class='bx bxs-trash'></i> Xóa</a></li>
            <li v-if="cmt.user_id != '{{Auth::user()->id}}'"v-on:click="reportCmt(cmt.id)"><a><i class='bx bxs-flag-alt' ></i> Báo cáo</a></li>

            @if(Auth::user()->is_tester == 1)
              <li role="separator" class="divider"></li>
              <li style="cursor: pointer;" v-on:click="delComment(cmt.id)">
                <a style="cursor: pointer; color: red;"><i class='bx bxs-user-detail' ></i> Xóa (admin)</a>
              </li>
            @endif
          @endif
        </ul>
      </div>

      {{-- <span v-if="meid == cmt.user_id" class="delete-comment" v-on:click="delComment(cmt.id)"><i class="zmdi zmdi-close-circle"></i> xxxx</span>  --}}
    </li>

    <!-- hiển thị loading -->
    <div v-show="theEnd == false" class="load-more-comment" v-on:click="fetchMoreComments" style="display: none;">
      <span v-show="theEnd == false">Tải thêm bình luận <i class="bx bx-caret-down"></i></span>
      <img class="loading-icon" v-show="showLoading == true" :src="cdn + '/assets/img/loading.gif'"/>
    </div>

    <!-- hiển thị profile -->
    <div style="display: none;" id="user-profile-popup" class="user-profile-popup">
      <div class="user-profile-container" v-if="showLoadingUser == false">
        <div class="loading-circle" style="margin-top: 150px;"></div>
      </div>
      <div class="user-profile-container" v-if="showLoadingUser == true">
        <div v-if="currentUser.id == 0" class="cover-container">
          <img class="user-avatar" :src="cdn + '/assets/img/oglogo.png'"/>
        </div>
        <div v-if="currentUser.id != 0" class="cover-container">
          <img v-if="currentUser.avatar == null || currentUser.avatar == ''" class="user-avatar" :src="cdn + '/assets/img/default-avatar.jpg'">
          <img v-if="currentUser.avatar != null && currentUser.avatar != ''" class="user-avatar" :src="cdn + '/cdn/avatar/default/'+ currentUser.avatar">
        </div>
        <table class="table" id="user-info-table">
          <tbody>
          <tr v-if="currentUser.name != null && currentUser.name != ''">
            <td class="user-form-item desc" style="width: 130px;">Họ và Tên</td>
            <td class="user-form-item">
              <b>@{{currentUser.name}}</b>
              <i v-if="currentUser.id == 0" style="color: #578fff;" class="zmdi zmdi-check-circle" data-toggle="tooltip" data-placement="left" title="Tài khoản đã xác thực"></i>
            </td>
          </tr>
          <tr v-if="currentUser.email != null && currentUser.email != ''">
            <td class="user-form-item desc">Email</td>
            <td class="user-form-item"><span class="info-item-email">@{{printPrivateEmail(currentUser.email)}}</span></td>
          </tr>
          <tr v-if="currentUser.username != null && currentUser.username != ''">
            <td class="user-form-item desc">Tên đăng nhập</td>
            <td class="user-form-item"><span>@{{currentUser.username}}</span></td>
          </tr>
          <tr v-if="currentUser.birthday != null && currentUser.birthday != ''">
            <td class="user-form-item desc">Ngày sinh</td>
            <td class="user-form-item">@{{currentUser.birthday}}</td>
          </tr>
          <tr v-if="currentUser.phone != null && currentUser.phone != ''">
            <td class="user-form-item desc">Số điện thoại</td>
            <td class="user-form-item"><span>@{{printPrivatePhone(currentUser.phone)}}</span></td>
          </tr>
          <tr v-if="currentUser.nihongo != null && currentUser.nihongo != ''">
            <td class="user-form-item desc">Trình tiếng Nhật</td>
            <td class="user-form-item">@{{currentUser.nihongo}}</td>
          </tr>
          <tr v-if="currentUser.address != null && currentUser.address != ''">
            <td class="user-form-item desc">Địa chỉ</td>
            <td class="user-form-item" style="padding-right:0;"><span>@{{currentUser.address}}</span></td>
          </tr>
          <tr v-if="currentUser.country != null && currentUser.country != ''">
            <td class="user-form-item desc">Quốc gia</td>
            <td class="user-form-item">@{{currentUser.country}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

  </div>
</script>

