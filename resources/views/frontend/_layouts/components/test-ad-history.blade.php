
<script type="text/x-template" id="test-ad-history">
  <div class="test-online__history">
    <div class="flex justify-center">
      <el-date-picker
          v-model="date"
          type="date"
          placeholder="Chọn ngày"
          :picker-options="options"
          format="dd/MM/yyyy"
          value-format="yyyy-MM-dd"
      >
      </el-date-picker>
    </div>
    <div class="test-online__group" v-for="exam in history" :key="'exam-' + exam.id">
      <div
        v-if="validateResult(exam)"
        :key="'exam-' + exam.id"
        class="test-online__history-item"
      >
        <div v-cloak>@{{ exam.name }}</div>
        <div v-cloak class="mr-5">
          Điểm: <span class="font-semibold" v-if="exam.result.length > 0">@{{ exam.result[0].total_score }}</span>
          <span class="ml-2" :style="{color: isPassed(exam) ? '#87D15F' : '#ff0033'}">
                          <i class="fa fa-check" v-if="isPassed(exam)"></i>
                          <i class="fa fa-times" v-else></i>
                      </span>
        </div>
        <div class="a-cursor-pointer" v-if="exam.result.length > 0">
          <a @click="show(exam)">Xem</a>
        </div>
      </div>
    </div>
    <modal v-if="showResult" @close="closeModal">
      <h3 slot="header" class="text-center">@{{ currentResult.name }}</h3>
      <div slot="body">
        <div class="test-online__exam-result">
          <div class="test-online__exam-body">
            <div class="mb-5 mt-5 flex">
              <div class="mr-5">
                <div>Kết quả: <span class="text-bold" :style="{color: isPassed(currentResult) ? '#87D15F' : '#ff0033'}">@{{ isPassed(currentResult) | passed }}</span></div>
                <div>Tổng điểm: <span class="text-bold">@{{ currentResult.result[0].total_score }}</span></div>
{{--                <div>Điểm đạt: <span class="text-bold">@{{ currentResult.passed_point }}</span></div>--}}
              </div>
            </div>
            <audio controls="controls" v-cloak style="width: 100%;" v-if="mp3">
              <source :src="'https://mp3-v2.dungmori.com/'+ mp3" type="audio/mpeg">
              Trình duyệt không hỗ trợ phát âm thanh
            </audio>
            <div v-for="(lesson, lIdx) in currentResult.lessons" :key="'lesson-' + lIdx">
              <div v-cloak class="test-online__exam-questions">
                <div v-for="(question, qIdx) in lesson.questions" :key="'question-' + qIdx" class="mt-3">
                  <div v-html="question.content" class="text-bold"></div>
                  <div style="display: flex; flex-wrap: wrap" class="test-online__exam-answers mt-3">
                    <div
                        v-for="(answer, aIdx) in question.answers"
                        :key="'answer-' + aIdx"
                        v-bind:style="answerLength(answer.content) >= 18 ? 'width:100%;' : 'width: 50%;'"
                        class="mt-2"
                    >
                      <label
                          class="test-online__exam-answer flex items-center"
                          :class="[
                                                    answers[question.id] == answer.id ? 'chose' : '',
                                                    answer.is_true ? 'correct' : ''
                                                ]"
                      >
                        <i class="fa fa-check" v-if="answer.is_true"></i>
                        <span class="ml-2" v-html="answer.content"></span>
                      </label>
                    </div>
                  </div>
                  <div v-if="question.explain && !question.is_content" class="m-t-3 m-b-30">
                    <div class="text-red m-b-2" style="color: #982deb;">★ Giải thích:</div>
                    <div v-html="question.explain"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </modal>
  </div>
</script>
