{{-- template c<PERSON>a nhóm bài học --}}
<script type="text/x-template" id="course-group-ld">
  <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true" ref="luyende">

    <div class="panel panel-default course-group-menu" v-for="group in listDeKyNang" v-if="tab === 1">
      <a :id="'group-ld-' + group.id" class="collapsed" role="button" data-toggle="collapse" data-parent="#accordion" :href="'#collapse-' + stateType + group.id" aria-expanded="false">
        <div class="panel-heading p-1 flex" role="tab" v-if="group.is_step != true">
          <img class="cg-icon" :src="url+ '/assets/img/cg-icon.png'" width="20px" height="20px" />
          <strong v-html="printContent(group.name)" style="color: #646464"></strong>
        </div>
      </a>
      <div :id="'collapse-'+ stateType + group.id" class="panel-collapse collapse" role="tabpanel" v-if="group.is_step != true">
        <div class="panel-body">
          <ul :class="'scroll-items scroll-items-'+ group.id">
            <li :class="'lesson-item-'+ lesson.id" v-for="lesson in group.lessons">
              <a :href="url +'/khoa-hoc/'+ courseurl.toLowerCase() +'/'+ lesson.id +'-'+ lesson.SEOurl">
                <span
                  style="width: 40%"
                  :class="lessonDetail.id == lesson.id ? 'font-bold' : ''"
                  :style="lessonDetail.id == lesson.id ? 'color: #8486F1' : ''"
                >
                  @{{ lesson.name }}
                </span>
                <span
                    class="score"
                    style="width: 30%; text-align: left;font-size: 14px; font-weight: 700; color: #474DDA;"
                    :style="{color: lesson.score ? '#474DDA' : '#C4C4C4'}"
                >@{{ lesson.score || 0 }}/@{{ lesson.total_marks }}</span>
                <div v-if="lesson.score == null" style="width: 30%; margin-left: 40px"></div>
                <div v-else-if="parseInt(lesson.score) >= parseInt(lesson.pass_marks)" style="width: 30%; margin-left: 40px"><strong class="passed">Đạt <img
                        width="20px" height="20px" src="{{ asset('assets/img/ld/passed.svg') }}" alt=""></strong></div>
                <div v-else style="width: 30%; margin-left: 40px"><strong class="not-passed">Trượt <img
                        width="20px" height="20px" src="{{ asset('assets/img/ld/not_passed.svg') }}" alt=""></strong></div>

              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="panel panel-default course-group-menu" v-for="item in listDeTongHop" v-if="tab === 2">
      <a :href="url +'/khoa-hoc/'+ courseurl.toLowerCase() +'/'+ item.id +'-'+ item.SEOurl">
        <div role="tab" style="width: 35%" class="flex items-center">
          <img class="cg-icon" :src="url+ '/assets/img/cg-icon.png'" />
          <span
            style="flex: 1"
            :class="lessonDetail.id == item.id ? 'font-bold' : ''"
            :style="lessonDetail.id == item.id ? 'color: #8486F1' : ''"
          >
            @{{ item.name }}
          </span>
        </div>
        <span
            class="score"
            style="width: 30%; text-align: left;font-size: 14px; font-weight: 700; color: #474DDA;"
            :style="{color: item.score ? '#474DDA' : '#C4C4C4'}"
            v-cloak
        >@{{ item.score || 0 }}/@{{ item.total_marks }}</span>
        <div v-if="item.score == null" style="width: 30%;"></div>
        <div v-else-if="parseInt(item.score) > parseInt(item.pass_marks)" style="width: 30%; color: #0FB78F;font-size: 14px;"><strong class="passed">Đạt <img
                width="20px" height="20px" src="{{ asset('assets/img/ld/passed.svg') }}" alt=""></strong></div>
        <div v-else style="width: 35%; color: #FF7D7E;font-size: 14px;"><strong class="not-passed">Không đạt <img
                width="20px" height="20px" src="{{ asset('assets/img/ld/not_passed.svg') }}" alt=""></strong></div>
      </a>
    </div>
  </div>
</script>
