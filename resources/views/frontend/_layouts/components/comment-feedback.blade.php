{{-- template của comment feedback--}}
<script type="text/x-template" id="comment-feedback-template">
  <div class="list-comments" :style="'background-color:'+ background">

    <div class="input-comment-container" v-if="meid != null">
      <div class="form_action">
        <img v-if="!avatar" class="me-avatar" :src="url + '/assets/img/default-avatar.jpg'" alt="" />
        <img v-else class="me-avatar" :src="url + '/cdn/avatar/small/'+ avatar" alt="" />
        <textarea class="input-comment" id="comment-content" rows="1" placeholder="Cảm nhận của tôi..."></textarea>
        <span class="pick-image" data-toggle="tooltip" data-placement="bottom" title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif">
                <i class="zmdi zmdi-camera"></i>
                <form class="form-pick-image" id="cmt-attachment-form">
                    <input type='file' id="commentImagePicked" name="commentImagePicked" @change="previewImage" accept=".png, .jpg, .jpeg, .gif" />
                </form>
            </span>
        <span class="post-comment-btn" v-on:click="postNewComment">Đăng</span>
        <div class="preview-image" id="preview-image-cmt"></div>
      </div>
    </div>

    <li v-if="showLoadingNewComment" class="comment-item" style='text-align: center;'>loading...</li>

    <li class="comment-item" v-for="cmt in listComments" :id="'cmt-item-'+ cmt.id">
      <a v-if="!cmt.user_id" class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
        <img class="avatar" :src="url + '/assets/img/oglogo.png'" alt="" />
        <i class="zmdi zmdi-check-circle"></i>
      </a>
      <a v-else class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
        <img v-if="!cmt.avatar" class="avatar" :src="url + '/assets/img/default-avatar.jpg'" alt="">
        <img v-else class="avatar" :src="url + '/cdn/avatar/small/'+ cmt.avatar" alt=""/>
      </a>
      <div class="comment-content">
        <p class="name" style="width: 100%;">
          <b class="red" v-if="!cmt.user_id" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(0)">Dũng Mori</b>
          <b class="red" v-else data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
            @{{cmt.name}} <span class="time" style="font-weight: 400; color: #888; margin-left: 15px;">@{{ prettyDate(cmt.created_at) }}</span>
          </b>
          <span style="margin: 10px 0; width: 100%; float: left;" v-html="printInfo(cmt.content)"></span>
        </p>
        <p v-if="cmt.img != null" class="preview-image">
          <a class="popup-preview-image" :href="url + '/cdn/comment/default/'+ cmt.img" data-fancybox>
            <img class="preview-thumbnail" :src="url + '/cdn/comment/small/'+ cmt.img" alt="" />
          </a>
        </p>
        <p class="comment-action">
          @if (auth()->check())
          <span class="love-btn" v-on:click="likeCmt(cmt.id)">
              <span v-show="!cmt.ulikes.length"><i class="fa fa-heart-o"></i></span>
              <span v-show="cmt.ulikes.length"><i class="fa fa-heart" style="color: #f06;"></i></span>
              <span v-show="!cmt.ulikes.length">like</span>
              <span v-show="cmt.ulikes.length == 1" v-bind:style="cmt.ulikes.includes(meid) ? 'color:#337ab7': ''">@{{cmt.ulikes.length}} like</span>
              <span v-show="cmt.ulikes.length > 1" v-bind:style="cmt.ulikes.includes(meid) ? 'color:#337ab7': ''">@{{cmt.ulikes.length}} likes</span>
          </span>
          @endif
          <a v-if="cmt.replies.length" class="load-more-reply" role="button" data-toggle="collapse" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id" style="color:#337ab7;">
            <i class="zmdi zmdi-comments"></i> @{{cmt.replies.length}} phản hồi
          </a>
          @if (auth()->check())
          <span v-if="cmt.replies.length == 0" class="answer" data-toggle="collapse" :id="'answer-reply-'+ cmt.id" :href="'#reply-'+ cmt.id" aria-expanded="false" :aria-controls="'reply-'+ cmt.id">Trả lời</span>
          @endif
        </p>
        <div class="reply-container">
          <div class="collapse" :id="'reply-'+ cmt.id">
            <div class="child-comment-item" v-for="(childCmt, index) in cmt.replies" :id="'reply-item-'+ childCmt.id">
              <a v-if="!childCmt.user_id" class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(0)">
                <img class="avatar" :src="url + '/assets/img/oglogo.png'" alt="" />
                <i class="zmdi zmdi-check-circle"></i>
              </a>
              <a v-else class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(childCmt.user_id)">
                <img v-if="!childCmt.avatar" class="avatar" :src="url + '/assets/img/default-avatar.jpg'" alt="" />
                <img v-else class="avatar" :src="url + '/cdn/avatar/small/'+ childCmt.avatar" alt="" />
              </a>
              <div class="comment-content">
                <p class="child-name">
                  <b class="red" v-if="!childCmt.user_id" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(0)">Dũng Mori </b>
                  <b class="red" v-else data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(childCmt.user_id)">
                    @{{childCmt.name}}
                  </b>
                  <span :id="'child-comment-content-'+ childCmt.id" v-html="printInfo(childCmt.content)"></span>
                </p>

                <p v-if="childCmt.img != null" class="preview-image">
                  <a class="popup-preview-image" :href="url + '/cdn/comment/default/'+ childCmt.img" data-fancybox>
                    <img class="preview-thumbnail" :src="url + '/cdn/comment/small/'+ childCmt.img"/>
                  </a>
                </p>

                <p class="child-comment-action">
                  <span class="time">@{{ prettyDate(childCmt.created_at) }}</span>
                  <span v-if="adminActive && childCmt.created_at != childCmt.updated_at"> &nbsp; Đã sửa</span>
                </p>
              </div>

              <!-- <span class="admin-edit-pen" v-if="adminActive == true && childCmt.user_id == 0" data-fancybox data-src="#edit-cmt-popup" href="javascript:;"
              v-on:click="editAdminComment(childCmt.id, childCmt.content)"
              >sửa</span> -->
              <span v-if="meid == childCmt.user_id" class="delete-comment" v-on:click="delReply(childCmt.id)"><i class="zmdi zmdi-close-circle"></i> xóa</span>
            </div>

            <div class="reply-form" v-if="meid != null">
              <img v-if="avatar" class="me-avatar" :src="url + '/assets/img/default-avatar.jpg'">
              <img v-else class="me-avatar" :src="url + '/cdn/avatar/small/'+ avatar">
              <textarea class="input-comment" :id="'reply-input-content-'+ cmt.id" rows="1" placeholder="comment..."></textarea>

              <span class="pick-image" data-toggle="tooltip" data-placement="bottom" title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif">
                  <i class="zmdi zmdi-camera"></i>
                  <form class="form-pick-image" :id="'cmt-attachment-form-'+ cmt.id">
                      <input type='file' :id="'commentImagePicked'+ cmt.id" name="commentImagePicked" @change="previewImageReply" accept=".png, .jpg, .jpeg, .gif"/>
                  </form>
              </span>

              <span class="post-comment-btn" v-on:click="postNewAnswer(cmt.id)">Đăng</span>
              <div class="preview-image" :id="'preview-image-cmt-'+ cmt.id"></div>
            </div>

          </div>
        </div>
      </div>
      <span v-if="meid == cmt.user_id" class="delete-comment" v-on:click="delComment(cmt.id)"><i class="zmdi zmdi-close-circle"></i> xóa</span>
    </li>

    <!-- hiển thị loading -->
    <div v-if="!theEnd" class="load-more-comment" v-on:click="fetchMoreComments">
      <span v-show="!showLoading">Tải thêm bình luận</span>
      <img class="loading-icon" v-show="showLoading" :src="url + '/assets/img/loading.gif'"/>
    </div>
    <div v-if="theEnd" class="end-of-list">Hết danh sách</div>

    <!-- hiển thị profile -->
    <div style="display: none;" id="user-profile-popup" class="user-profile-popup">
      <div class="user-profile-container" v-if="!showLoadingUser">
        <div class="loading-circle" style="margin-top: 150px;"></div>
      </div>
      <div class="user-profile-container" v-else>
        <div v-if="!currentUser.id" class="cover-container">
          <img class="user-avatar" :src="url + '/assets/img/oglogo.png'"/>
        </div>
        <div v-else class="cover-container">
          <img v-if="currentUser.avatar" class="user-avatar" :src="url + '/assets/img/default-avatar.jpg'">
          <img v-else class="user-avatar" :src="url + '/cdn/avatar/default/'+ currentUser.avatar">
        </div>
        <table class="table" id="user-info-table">
          <tbody>
          <tr v-if="currentUser.name">
            <td class="user-form-item desc" style="width: 130px;">Họ và Tên</td>
            <td class="user-form-item">
              <b>@{{currentUser.name}}</b>
              <i v-if="!currentUser.id" style="color: #578fff;" class="zmdi zmdi-check-circle" data-toggle="tooltip" data-placement="left" title="Tài khoản đã xác thực"></i>
            </td>
          </tr>
          <tr v-if="currentUser.email">
            <td class="user-form-item desc">Email</td>
            <td class="user-form-item"><span class="info-item-email">@{{printPrivateEmail(currentUser.email)}}</span></td>
          </tr>
          <tr v-if="currentUser.username">
            <td class="user-form-item desc">Tên đăng nhập</td>
            <td class="user-form-item"><span>@{{currentUser.username}}</span></td>
          </tr>
          <tr v-if="currentUser.birthday">
            <td class="user-form-item desc">Ngày sinh</td>
            <td class="user-form-item">@{{currentUser.birthday}}</td>
          </tr>
          <tr v-if="currentUser.phone">
            <td class="user-form-item desc">Số điện thoại</td>
            <td class="user-form-item"><span>@{{printPrivatePhone(currentUser.phone)}}</span></td>
          </tr>
          <tr v-if="currentUser.nihongo">
            <td class="user-form-item desc">Trình tiếng Nhật</td>
            <td class="user-form-item">@{{currentUser.nihongo}}</td>
          </tr>
          <tr v-if="currentUser.address">
            <td class="user-form-item desc">Địa chỉ</td>
            <td class="user-form-item" style="padding-right:0;"><span>@{{currentUser.address}}</span></td>
          </tr>
          <tr v-if="currentUser.country">
            <td class="user-form-item desc">Quốc gia</td>
            <td class="user-form-item">@{{currentUser.country}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <form id="edit-cmt-popup" action="" method="post">
      <h4>Sửa nội dung</h4>
      <input type="hidden" id="edit-comment-id"/>
      <p><textarea class="edit-comment-area form-control" id='edit-comment-area' placeholder="comment..."></textarea></p>
      <p class="mb-0 text-right">
        <span class="edit-comment-btn-save" v-on:click="saveAdminComment()">Lưu</span>
        <span class="edit-comment-btn-cancel" v-on:click="cancelAdminComment()">Hủy</span>
      </p>
    </form>

  </div>
</script>
