
<script type="text/x-template" id="test-online-rank">
  <div class="test-online__rank">
    <div class="test-online__rank-header">
      <div @click="tab = 'day'" :class="{active: tab == 'day'}">TRONG NGÀY</div>
      <div @click="tab = 'all'" :class="{active: tab == 'all'}">TB CẢ KHOÁ</div>
    </div>
    <div class="test-online__rank-body">
      <div class="test-online__rank-filter">
        <el-date-picker
            v-if="tab == 'day'"
            v-model="date"
            type="date"
            placeholder="Chọn ngày"
            :picker-options="options"
            format="dd/MM/yyyy"
            value-format="yyyy-MM-dd"
        >
        </el-date-picker>
        <el-select filterable ref="groupSelector" v-model="group_id" placeholder="Nhóm">
          <el-option
              v-for="group in groups"
              :key="'group-' + group.id"
              :label="group.name"
              :value="group.id"
          >
          </el-option>
        </el-select>
        <el-select v-if="tab == 'day'" filterable ref="examSelector" v-model="exam_id" placeholder="Bài kiểm tra">
          <el-option
              v-for="exam in exams"
              :key="'exam-' + exam.exam.id"
              :label="exam.exam.name"
              :value="exam.exam.id"
          >
          </el-option>
        </el-select>
      </div>
      <div style="width: 70%; margin: auto; margin-top: 40px; font-family: Quicksand, Arial, sans-serif">
        <div v-if="!userResult" class="text-center mb-3 mt-3">Bạn chưa làm bài thi</div>
        <div v-else-if="userResultIndex > 10" class="flex justify-between items-center text-md mt-3 mb-3 test-online__rank-student highlight" :style="rankBg(userResultIndex)">
          <div style="width: 100px; text-align: center" class="test-online__rank-order" :style="rankStyle(userResultIndex)">
            @{{ userResultIndex + 1 }}
          </div>
          <div class="flex-1 text-left flex items-center">
            <img class="test-online__rank-avatar mr-2"
                 :src="url + '/cdn/avatar/small/' + userResult.user.avatar"
            />
            <span>@{{ userResult.user.name }}</span>
          </div>
          <div style="width: 100px; text-align: center" v-if="tab == 'day'">@{{ userResult.total_score }}</div>
          <div style="width: 100px; text-align: center" v-if="tab == 'all'" v-cloak>
            <p>@{{ userResult.average | point }}</p>
            <p>(@{{ userResult.percentage }}%)</p>
          </div>
        </div>
        <div class="flex justify-between items-center text-bold text-lg">
          <div style="width: 40px; text-align: right"></div>
          <div style="width: 70px; text-align: center">Top</div>
          <div class="flex-1 text-left">Học viên</div>
          <div style="width: 100px; text-align: center">Điểm</div>
        </div>
        <div v-for="(result, index) in ladder" class="flex justify-between items-center text-md mt-3 test-online__rank-student" :class="[index < 10 ? 'highlight' : '']" :style="rankBg(index)">
          <div style="width: 40px; text-align: right" :style="rankStyle(index)"><i class="fa fa-user mr-1" v-if="userResult && result.id == userResult.id"></i></div>
          <div style="width: 70px; text-align: center" class="test-online__rank-order" :style="rankStyle(index)">
            @{{ index + 1}}
          </div>
          <div class="flex-1 text-left flex items-center">
            <img class="test-online__rank-avatar mr-2"
                 :src="url + '/cdn/avatar/small/' + result.user.avatar"
            />
            <span>@{{ result.user.name }}</span>
          </div>
          <div style="width: 100px; text-align: center" v-if="tab == 'day'">@{{ result.total_score }}</div>
          <div style="width: 100px; text-align: center" v-if="tab == 'all'" v-cloak>
            <p>@{{ result.average | point }}</p>
            <p>(@{{ result.percentage }}%)</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</script>
