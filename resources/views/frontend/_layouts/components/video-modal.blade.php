{{-- template của modal video--}}
<script type="text/x-template" id="video-modal-template">
  <div class="video__js--overlay-quiz-wrapper">
    <div class="video__js--overlay-quiz-timer" v-if="question.type === 1">
      <div class="video__js--overlay-skip-btn" @click="skip(question)">Bỏ qua</div>
    </div>
    <div class="video__js--overlay-quiz-content">
      <div class="video__js--overlay-quiz-qa" :class="{'longgg' : question.type === 2 && modalQuestion.longgg}">
        <div v-if="modalQuestion.content" class="video__js--overlay-question" v-html="modalQuestion.content"></div>
        <div class="video__js--overlay-answers" :class="{
                 'h-100': question.type === 1 && modalQuestion.answers.length === 4,
                 'h-50': question.type === 1 && modalQuestion.answers.length < 4,
                 'grid-3-1': question.type === 1 && modalQuestion.answers.length % 3 == 0,
                 'grid-2-1': question.type === 1 && modalQuestion.answers.length % 2 == 0,
                 'grid-1-1': question.type === 2 && modalQuestion.longgg
                 }">
          <div class="video__js--overlay-answer" v-for="(answer, index) in modalQuestion.answers" :key="answer.key" @click="selectAnswer(answer, modalQuestion)"
               :class="{
                            'video__js--overlay-answer-blink' : modalQuestion.time_up && answer.grade,
                            'longgg' : question.type === 2 && modalQuestion.longgg
                         }"
               :style="{background: !modalQuestion.time_up ? (answer.selected ? '#F3933D' : (question.type === 2 ? 'rgba(0, 0, 0, 0.7)' : '#7ECADE')) : (answer.grade ? '#93CA8C' : (answer.selected ? '#F3933D' : (question.type === 2 ? 'rgba(0, 0, 0, 0.7)' : '#7ECADE')))}">
            <div class="flex justify-center items-center">
              <span class="flex justify-center items-center" style="font-size: 1em; font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; width: 1.3em; height: 1.3em; border-radius: 50%; background: rgba(0,0,0,0.3)">@{{ index + 1 }}</span>
            </div>
            <span style="text-align: justify; padding: 5px"> @{{answer.value}}</span>
            {{--                    <span>--}}
            {{--                        <i v-if="modalQuestion.time_up && !answer.grade && answer.selected" class="fa fa-lg fa-times-circle" style="background: #FFF; line-height: 16px; border-radius: 50%; color: rgba(255, 0, 0, 0.7)"></i>--}}
            {{--                        <i v-if="!modalQuestion.time_up && answer.selected" class="fa fa-circle fa-lg" style="color: rgba(21, 255, 59, 0.6)"></i>--}}
            {{--                        <i v-if="(modalQuestion.time_up && !answer.selected & !answer.grade) || (!modalQuestion.time_up && !answer.selected)" class="fa fa-circle-o fa-lg"></i>--}}
            {{--                        <i v-if="modalQuestion.time_up && answer.grade " class="fa fa-check fa-lg" style="color: #FFF"></i>--}}
            {{--                    </span>--}}
          </div>
        </div>
      </div>
      <div class="video__js--overlay-navigation" v-if="modalQuestion.type === 2">
        <span class="video__js--overlay-navigation-button" @click="triggerCloseModal">@{{ question.time_up ? 'Tiếp tục' : 'Bỏ qua' }}</span>
      </div>
    </div>
  </div>
</script>
