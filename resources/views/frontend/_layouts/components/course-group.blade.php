{{-- template c<PERSON>a nhóm bài học --}}
<script type="text/x-template" id="course-group-template">
  <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
    <?php if(Auth::user()): ?>
    <div id="lesson__progress">
      <div class="lesson__progress--bar" id="lesson__progress--bar"></div>
    </div>
    <?php endif ?>
    <div class="panel panel-default" v-for="group in listGroups">
      <div class="panel-heading" role="tab" v-if="group.is_step == true">
        <a class="collapsed group-step-item" style="background: #EEE; color: #888; padding: 3px 10px; text-align: center !important; margin-bottom: 0px;">
          <span style="width: 95%;">@{{ group.name }}</span>
          <i class="zmdi zmdi-caret-down"></i>
        </a>
      </div>

      <div class="panel-heading" role="tab" v-if="group.is_step != true">
        <a class="collapsed" role="button" data-toggle="collapse" data-parent="#accordion" :href="'#collapse-' + stateType + group.id" aria-expanded="false">
          <strong v-html="printContent(group.name)"></strong>
          <span style="width: 5px" class="inBetweener">
                        <i v-if="ejulimit == 1 && group.eju_limited == 1" class="zmdi zmdi-lock" style="margin-left: -22px;" title="Dành cho học viên mua từ 2021"></i>
                    </span>
          <?php if(Auth::user()): ?>
          <div id="lesson__progress">
            <div class="lesson__progress--circle" v-bind:id="'lesson__progress--circle-' + group.id"></div>
          </div>
          <?php endif ?>
        </a>
      </div>

      <div :id="'collapse-'+ stateType + group.id" class="panel-collapse collapse" role="tabpanel" v-if="group.is_step != true">
        <div class="panel-body">
          <ul :class="'scroll-items scroll-items-'+ group.id">
            <li :class="'lesson-item-'+ lesson.id" v-for="lesson in group.lessons">
              <a :href="url +'/khoa-hoc/'+ courseurl.toLowerCase() +'/'+ lesson.id +'-'+ lesson.SEOurl">
                <div v-html="displayCheckMark(lesson.id)"></div>
                <span style="flex: 1">@{{ lesson.name }}</span>
                <div v-if="courseurl == 'khoa-n5' || courseurl == 'chuyen-nganh'" v-html="printN5Status(lesson.feature)"></div>
                <div v-if="courseurl != 'khoa-n5' && courseurl != 'chuyen-nganh'" v-html="printLessonStatus(lesson.price_option)"></div>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</script>

