
<script type="text/x-template" id="test-online-list">
    <div>
        <div class="test-online__date font-quicksand flex justify-center">
            <div class="a-cursor-pointer" @click="time = today" :class="[time == today ? 'text-success' : '']">
                •   @{{ today | slash_date }}   •
            </div>
            <div class="a-cursor-pointer ml-5" @click="time = tomorrow" :class="[time == tomorrow ? 'text-success' : '']">
                •   @{{ tomorrow | slash_date }}   •
            </div>
        </div>
        <div class="test-online__list">
            <div class="test-online__group" v-for="group in groups" :key="'group-' + group.id">
                <div class="test-online__group-name">
                    <svg width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="4.69834" cy="4.55161" r="4.27695" fill="#96D962"/>
                    </svg>
                    <div class="ml-5 font-quicksand">@{{ group.name }}</div>
                </div>
                <div v-if="group.exams.length == 0" class="text-center">
                    Không có bài kiểm tra trong nhóm ngày @{{ time | slash_date }}
                </div>
                <div v-for="exam in group.exams" :key="'exam-' + exam.id">
                    <template v-if="exam.type != 5">
                        <div v-if="!exam.exam?.time_split || exam.exam?.time_split?.length === 0" class="test-online__schedule">
                            <div>
                                <div class="font-semibold" v-cloak>@{{ exam.exam?.name }}</div>
                                <div v-if="!exam.available" class="font-quicksand">Bắt đầu lúc <span class="text-red font-semibold">@{{ exam.time_start | datetime }}</span></div>
                                <div class="font-quicksand">Hạn nộp bài <span class="text-red font-semibold">@{{ exam.time_end | datetime }}</span></div>
                            </div>
                            <div class="flex items-center">
                                <div class="test-online__join-btn" :class="{ disabled: !exam.available }" @click="goTest(exam)">@{{ exam.results && exam.results.data_1 ? 'Chi tiết' : 'Làm bài'}}</div>
                            </div>
                        </div>
                        <template v-else>
                            <div class="test-online__schedule">
                                <div>
                                    <div class="font-semibold" v-cloak>@{{ exam.exam?.name }}</div>
                                    <div v-if="!exam.available" class="font-quicksand">Bắt đầu lúc <span class="text-red font-semibold">@{{ exam.time_start | datetime }}</span></div>
                                    <div class="font-quicksand">Hạn nộp bài <span class="text-red font-semibold">@{{ exam.time_end | datetime }}</span></div>
                                </div>
                            </div>
                            <div
                                    v-for="(lesson, idx) in exam.exam?.lessons"
                                    class="flex justify-between items-center mb-0 p-4"
                                    :class="[exam.results && exam.results[`data_${idx+1}`] ? 'text-green-500' : '']"
                                    style="margin-left: 30px;border-bottom: 1px solid #C4C4C4;"
                            >
                                <div class="font-semibold" v-cloak>@{{ getLessonTitle(exam.exam, lesson) }}</div>
                                <div class="flex items-center">
                                    <div
                                            class="test-online__join-btn"
                                            :class="{ disabled: !exam.available }"
                                            @click="goTest(exam, lesson)"
                                    >@{{ exam.results && exam.results[`data_${idx+1}`] ? 'Chi tiết' : 'Làm bài'}}</div>
                                </div>
                            </div>
                        </template>
                    </template>
                </div>
            </div>
        </div>
    </div>
</script>
