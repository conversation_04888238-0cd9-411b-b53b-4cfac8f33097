{{-- template của nhóm bài học premium --}}
<script type="text/x-template" id="course-group-premium">
  <div v-cloak>
    <div class="progress-container" v-if="ul == true || (courseurl == 'khoa-n5' && auth ==true)" v-cloak>
      <div class="total-circlebar">
        <h4>Tiến độ cả khóa</h4>
        <div class="percentage">@{{  totalPrecent }}%</div>
      </div>
      <div class="stage-bar" id="stage-bar">
      </div>
    </div>
    <div class="search-group-container focus-highlight" v-if="(ul == true || courseurl == 'khoa-n5') && showSearch">
      <div class="search-input-container">
        <div id="search-submit">&nbsp;</div>
        <div class="header-search">
          <input type="text" v-on:keyup="searchLessons" v-model="key" class="search-input" placeholder="Tìm bài học..." autocomplete="off"/>
        </div>
        <span v-if="key.length > 0" v-on:click="clearSearch"><i class="zmdi zmdi-close-circle"></i></span>
      </div>
      <div class="search-result-dropdown focus-highlight" v-if="results.length > 0">
        <li class="result-item" v-for="lesson in results">
          <a :href="url +'/khoa-hoc/'+ courseurl.toLowerCase() +'/'+ lesson.id +'-'+ lesson.SEOurl">

            <span v-if="lesson.type == 'docs' || lesson.type == null">
                <img class="icon lazyload" :src="url + '/assets/img/premium/docb.png'"/>
                <img class="active lazyload" :src="url + '/assets/img/premium/docw.png'"/>
            </span>
            <span v-if="lesson.type == 'video'">
                <img class="icon lazyload" :src="url + '/assets/img/premium/videob.png'"/>
                <img class="active lazyload" :src="url + '/assets/img/premium/videow.png'"/>
            </span>
            <span v-if="lesson.type == 'test'">
                <img class="icon lazyload" :src="url + '/assets/img/premium/quizb.png'"/>
                <img class="active lazyload" :src="url + '/assets/img/premium/quizw.png'"/>
            </span>
            <span v-if="lesson.type == 'flashcard'">
                <img class="icon lazyload" :src="url + '/assets/img/premium/fcb.png'"/>
                <img class="active lazyload" :src="url + '/assets/img/premium/fcw.png'"/>
            </span>
            <span v-if="lesson.type == 'guide'">
                <img class="icon lazyload" :src="url + '/assets/img/premium/guideb.png'"/>
                <img class="active lazyload" :src="url + '/assets/img/premium/guidew.png'"/>
            </span>

            <div class="lesson-info">
              <span class="lesson-name" v-html="printNameLesson(lesson.is_secret, lesson.price_option, lesson.name)"></span>
              <span class="group-name">@{{lesson.group_name}}</span>
            </div>

          </a>
        </li>
      </div>
    </div>
    <div class="guide-search">
      <div class="guide guide-click" href="#guide-show" data-toggle="collapse">Lộ trình</div>
      <a class="guide guide-click" v-if="courseurl == 'khoa-n3'" :href="url + '/khoa-hoc/bo-tro-on-tap-kien-thuc-n4'" style="background-color: #FFBF00">
        Ôn tập N4
      </a>
      <a class="guide guide-click" v-if="courseurl == 'khoa-n2'" :href="url + '/khoa-hoc/bo-tro-on-tap-kien-thuc-n3'" style="background-color: #FFBF00">
        Ôn tập N3
      </a>
      <a class="guide guide-click" v-if="courseurl == 'khoa-n1'" :href="url + '/khoa-hoc/bo-tro-on-tap-kien-thuc-n2'" style="background-color: #FFBF00">
        Ôn tập N2
      </a>
      <div class="search" @click="showSearch = !showSearch" v-if="ul == true || courseurl == 'khoa-n5'">
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_4336_8741)">
            <path d="M12.6875 21.875C17.7616 21.875 21.875 17.7616 21.875 12.6875C21.875 7.61338 17.7616 3.5 12.6875 3.5C7.61338 3.5 3.5 7.61338 3.5 12.6875C3.5 17.7616 7.61338 21.875 12.6875 21.875Z" stroke="black" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19.1843 19.1843L24.5 24.5" stroke="black" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
          </g>
          <defs>
            <clipPath id="clip0_4336_8741">
              <rect width="28" height="28" fill="white"/>
            </clipPath>
          </defs>
        </svg>
      </div>
    </div>

    <div id="guide-show" class="collapse guide-colapse">
      <li :class="'lesson-item-'+ lesson.id" v-for="lesson in guideLessons">
        <a :href="url +'/khoa-hoc/'+ courseurl.toLowerCase() +'/'+ lesson.id +'-'+ lesson.SEOurl">
          <div class="lesson-name"><i class="fa fa-bookmark"></i> &nbsp; @{{ lesson.name }}</div>
          <span v-html="displayCheckMark(lesson.id)"></span>
        </a>
      </li>
    </div>
    <div v-if="courseurl != 'khoa-n4' && courseurl != 'khoa-n5'">
      <h3>HÀNH TRANG JLPT  <span>Chặng @{{currentStage}}</span></h3>

      <ul class="nav nav-tabs stage-container">
        <li v-on:click="changeStage(1)" v-bind:class="{ active: (currentStage == 1) }">
          <a >
            <div>Chặng</div>
            <div>1</div>
          </a>
        </li>
        <li v-on:click="changeStage(2)"  v-bind:class="{ active: (currentStage == 2) }">
          <a>
            <div>Chặng</div>
            <div>2</div>
          </a>
        </li>
        <li v-on:click="changeStage(3)"  v-bind:class="{ active: (currentStage == 3) }">
          <a>
            <div>Chặng</div>
            <div>3</div>
          </a>
        </li>
        <li v-on:click="changeStage(4)"  v-bind:class="{ active: (currentStage == 4) }">
            <a>
                <div>Chặng</div>
                <div>4</div>
            </a>
        </li>
      </ul>
    </div>

    <div class="category-container" v-bind:style="(listCategories.length == 2) ? 'justify-content: flex-start' : ''" :style="{'border-top': !['khoa-n1', 'khoa-n2', 'khoa-n3'].includes(courseurl) ? '2px dashed #96D962' : 'none', 'border-radius': !['khoa-n1', 'khoa-n2', 'khoa-n3'].includes(courseurl) ? '20px 20px 0 0' : '0px'}">
      <div v-if="currentStage == 1 && ct.type == '0'" v-for="(ct, index) in listCategories" :id="'ct-focus-'+ ct.id" v-on:click="focusCategory(index)" v-bind:class="[ct.type == '0' ? 'p-item' : 'p-item special']">
        <div class="cate-item" v-bind:style="'width: 100%; height: 40px; padding-top: 10px; background: url(\''+ url +'/cdn/lesson_category/' + ct.icon+ '\')'">
          <span v-show="ct.type != 2">@{{ct.title}}</span><br/>
        </div>
      </div>

      <div v-if="currentStage != 1 || (ct.type != '0' && index > 0)" v-for="(ct, index) in listCategories" :id="'ct-focus-'+ ct.id" v-on:click="focusCategory(index)" class="p-item" v-bind:class="{'active': ct.id == pickedCategory.id, 'special': ct.type != '1'}">
        <div class="cate-item" v-bind:style="(listCategories.length == 2) ? 'margin-right: 3px;' : ''">
          <span v-show="ct.type != 2">@{{ct.title}}</span><br/>
          <img class="lazyload" :src="url+ '/cdn/lesson_category/'+ ct.icon" /><br/>
          <span v-if="getCategoryVideoCount(ct) && ct.type != 2" class="text-dark mt-2">(@{{ getCategoryVideoCount(ct) }} video)</span>
        </div>
      </div>


      {{-- <div v-for="(ct, index) in listCategories" :id="'ct-focus-'+ ct.id" v-on:click="focusCategory(index)" v-bind:class="[ct.id == pickedCategory.id ? 'p-item active' : 'p-item']">
          <div v-if="currentStage == 1 && index == 0" class="guide">@{{ct.title}}</div>
          <div v-if="currentStage != 1 || index > 0" v-bind:class="[ct.type == '1' ? 'cate-item' : 'cate-item special']" v-bind:style="(listCategories.length == 2) ? 'width: 160px' : ''">
              <span v-show="ct.type != 2">@{{ct.title}}</span><br/>
               <img :src="url+ '/cdn/lesson_category/'+ ct.icon" />
              <img :src="'https://web-test.dungmori.com/cdn/lesson_category/'+ ct.icon" />
          </div>
      </div> --}}

    </div>

    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
      <div class="panel panel-default" v-for="group in listGroups">
        <div class="panel-heading" role="tab" v-if="group.is_step == true">
          <a class="collapsed group-step-item" style="background: #EEE; color: #888; padding: 3px 10px; text-align: center !important; margin-bottom: 0px;">
            <span style="width: 95%;">@{{ group.name }}</span>
            <i class="zmdi zmdi-caret-down"></i>
          </a>
        </div>
        <div class="panel-heading" role="tab" v-if="group.is_step != true">
          <a :id="'click-group-'+ group.id" class="collapsed" role="button" data-toggle="collapse" data-parent="#accordion" :href="'#collapse-' + group.id" aria-expanded="false">
            {{-- <strong v-html="printContent(group.name)"></strong> --}}
            <strong v-bind:style="(group.lesson_category_id == 42 || group.lesson_category_id == 46) ? 'text-align:left' : ''" v-html="printContent(group.name)"></strong>
            <span>
                <i class='fa fa-check' style='color: #41A336;' :style="{opacity: group.progress == 100 ? 1 : 0}"></i>
            </span>
          </a>
        </div>
        <div :id="'collapse-'+ group.id" class="panel-collapse collapse" role="tabpanel" v-if="group.is_step != true">
          <div class="panel-body">
            <ul :class="'scroll-items scroll-items-'+ group.id">
              <li :class="'lesson-item-'+ lesson.id" v-for="lesson in group.lessons">
                <a :href="url +'/khoa-hoc/'+ courseurl.toLowerCase() +'/'+ lesson.id +'-'+ lesson.SEOurl">

                  <div class="type-icon" v-if="lesson.type == 'docs' || lesson.type == null">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/docb.png'"/>
                    <img class="active lazyload" :src="url + '/assets/img/premium/docw.png'"/>
                  </div>
                  <div class="type-icon" v-if="lesson.type == 'video'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/videob.png'"/>
                    <img class="active lazyload" :src="url + '/assets/img/premium/videow.png'"/>
                  </div>
                  <div class="type-icon" v-if="lesson.type == 'test'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/quizb.png'"/>
                    <img class="active lazyload" :src="url + '/assets/img/premium/quizw.png'"/>
                  </div>
                  <div class="type-icon" v-if="lesson.type == 'flashcard'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/fcb.png'"/>
                    <img class="active lazyload" :src="url + '/assets/img/premium/fcw.png'"/>
                  </div>
                  <div class="type-icon" v-if="lesson.type == 'guide'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/guideb.png'"/>
                    <img class="active lazyload" :src="url + '/assets/img/premium/guidew.png'"/>
                  </div>

                  <div class="lesson-name" v-html="printNameLesson(lesson.is_secret, lesson.price_option, lesson.name)"></div>
                  <span class="hoc-thu" v-html="printLessonStatus(lesson.price_option)"></span>
                  <span v-html="displayCheckMark(lesson.id)"></span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</script>
