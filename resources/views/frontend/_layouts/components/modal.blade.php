<script type="text/x-template" id="modal-template">
  <transition name="v-modal">
    <div class="v-modal-mask">
      <div class="v-modal-wrapper" @mousedown="$emit('close')">
        <div class="v-modal-container" @mousedown.stop :style="{ width: width, backgroundColor: bgColor, shadow: shadow, padding: padding }">
          <div class="v-modal-header">
            <slot name="header">

            </slot>
          </div>

          <div class="v-modal-body">
            <slot name="body">

            </slot>
          </div>
        </div>
      </div>
    </div>
  </transition>
</script>
