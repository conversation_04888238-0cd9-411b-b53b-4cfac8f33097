{{-- template của test tổng hợp--}}
<script type="text/x-template" id="mixed-test-template">
  <div class="quiz__container" v-if="!loading">
    <div class="quiz__header">
      <div class="quiz__header--progress" v-bind:style="{gridTemplateColumns: 'repeat(' + questions.length + ', minmax(20px, 1fr))'}" v-if="state == 'testing'">
        @if($adminSession == true)
          <div
              class="quiz__header--progress-block"
              v-for="(question, index) in questions"
              v-bind:style="{color: question.answered ? (question.correct ? '#41A336' : '#d10000') : '#ccc', cursor: question.answered ? 'pointer' : 'default'}"
              @click="current = index"
          >
            <transition name="ltr" tag="div">
              <i class="fa fa-sort-down quiz__header--progress-flag" v-bind:style="{color: question.answered ? (question.correct ? '#41A336' : '#d10000') : '#1450db'}" v-if="current == index"></i>
              <span v-else></span>
            </transition>
            <span class="quiz__header--progress-number">@{{ index + 1 }}</span>
          </div>
        @else
          <div
              class="quiz__header--progress-block"
              v-for="(question, index) in questions"
              v-bind:style="{color: question.answered ? (question.correct ? '#41A336' : '#d10000') : '#ccc', cursor: question.answered ? 'pointer' : 'default'}"
              @click="question.answered ? (current = index) : null"
          >
            <transition name="ltr" tag="div">
              <i class="fa fa-sort-down quiz__header--progress-flag" v-bind:style="{color: question.answered ? (question.correct ? '#41A336' : '#d10000') : '#1450db'}" v-if="current == index"></i>
              <span v-else></span>
            </transition>
            <span class="quiz__header--progress-number">@{{ index + 1 }}</span>
          </div>
        @endif
      </div>
    </div>
    <div class="quiz__body" v-if="loading"></div>
    <div class="quiz__body" v-if="questions && !loading">
      <div class="quiz__body--title flex justify-between items-center" v-if="state == 'testing'">
        <span v-html="currentQuestion.section" class="quiz__body--title-content"></span>
        <div style="display: flex; align-items: center">
          <div class="mr-5">Autoplay</div>
          <label class="checkbox__switch">
            <input type="checkbox" v-model="autoplay" name="autoPlay" @change="setAutoplay(autoplay)"/>
            <span class="checkbox__slider checkbox__round"></span>
          </label>
        </div>
      </div>
      <div class="quiz__body--slider" v-if="state == 'testing'">
        <transition-group name="fade" tag="div" v-if="!loading">
          <div class="quiz__body--slide" v-for="(question, index) in questions" v-if="!loading && current == index" :key="question.id">
            <div class="quiz__body--content">
              <div class="quiz__body--content-question mb-5">
                <div>
                                    <span v-if="question.question_audio" class="quiz__circle-icon mr-5 a-cursor-pointer text-success" @click="playAudio(question.question_audio, 'playingQuestionAudio')">
                                    <i class="zmdi" :class="{'zmdi-volume-up': playingMp3 && playingQuestionAudio, 'zmdi-volume-off': !playingMp3 || !playingQuestionAudio}"></i>
                                </span>
                </div>
                <span v-if="question.type == 10" v-html="question.mask"></span>
                <span v-if="question.type == 11" v-html="question.blocks.join(' ')"></span>
              </div>
              <div class="quiz__body--content-answers" v-bind:style="{'gridTemplateColumns': question.longgggg ? '1fr' : '1fr 1fr'}">
                                <span
                                    v-if="question.type == 10"
                                    v-bind:style="{'backgroundColor': !question.answered ? (question.currentAnswer.id == answer.id ? '#e4fce7' : '#fff') : (question.currentAnswer.id == answer.id ? (answer.id == question.answer.id ? '#e4fce7' : '#FFEFEF') : (answer.id == question.answer.id ? '#e4fce7' : '#fff')), 'borderColor': (question.answered && (question.currentAnswer.id == answer.id) && answer.id != question.answer.id) ? 'red' : '#7fbd18'}"
                                    class="quiz__body--content-answer" v-for="(answer, index) in question.answers" @click="!question.answered && setAnswer(question, answer)">@{{ index + 1 }}. @{{ answer.value }}</span>
                <span
                    v-if="question.type == 11"
                    v-bind:style="{'backgroundColor': !question.answered ? ((question.currentAnswers.indexOf(answer.id) > -1) ? '#e4fce7' : '#fff') : (question.currentAnswer.id == answer.id ? (answer.id == question.answer.id ? '#e4fce7' : '#FFEFEF') : (answer.id == question.answer.id ? '#e4fce7' : '#fff')), 'borderColor': (question.answered && (question.currentAnswer.id == answer.id) && answer.id != question.answer.id) ? 'red' : '#7fbd18'}"
                    class="quiz__body--content-answer" v-for="(answer, index) in question.answers" @click="!question.answered && setAnswer(question, answer)">@{{ index + 1 }}. @{{ answer.value }}</span>
              </div>
            </div>
            <div class="quiz__body--navigation">
              <transition name="hint">
                <div
                    v-bind:style="{backgroundColor: question.correct ? '#e4fce7' : '#FFEFEF', color: question.correct ? '#41A336' : '#CC3333'}"
                    class="quiz__body--navigation-hint" v-if="question.answered"
                >
                  <div class="flex flex-column">
                    <span class="text-bold">Đáp án</span>
                    <span v-if="question.type == 10">@{{ question.answer.value }}</span>
                    <span v-if="question.type == 11" v-html="question.answerBlocks.join(' ')"></span>
                  </div>
                  <div>
                                        <span v-if="question.answer_audio" @click="playAudio(question.answer_audio, 'playingAnswerAudio')" class="quiz__circle-icon a-cursor-pointer">
                                            <i class="zmdi" :class="{'zmdi-volume-up': playingMp3 && playingAnswerAudio, 'zmdi-volume-off': !playingMp3 || !playingAnswerAudio}"></i>
                                        </span>
                  </div>
                </div>
              </transition>

              <div class="quiz__body--navigation-button">
                                <span ref="navBtn" v-if="!question.answered && question.type == 10" class="quiz__button"
                                      v-bind:style="{'backgroundColor': !question.currentAnswer.id ? 'gray': 'gray', 'cursor': question.currentAnswer.id ? 'pointer' : 'default'}">
                                    Câu tiếp theo &nbsp; <i class="fa fa-angle-right fa-lg"></i></span>
                <span ref="navBtn" v-if="!question.answered && question.type == 11" v-on:click="checkQuestion(question)" class="quiz__button"
                      v-bind:style="{'backgroundColor': !question.currentAnswers.includes(null) ? '#41A336': 'gray', 'cursor': question.currentAnswers.length == question.answers.length ? 'pointer' : 'default'}">
                                    Kiểm tra &nbsp; <i class="fa fa-angle-right fa-lg"></i></span>
                <span ref="navBtn" v-if="question.answered && current < questions.length - 1 && question.type == 10" v-on:click="nextQuestion" class="quiz__button"
                      v-bind:style="{'backgroundColor': question.answered && !question.correct ? '#CC3333' : '#41A336'}">
                                    Câu tiếp theo &nbsp; <i class="fa fa-angle-right fa-lg"></i></span>
                <span ref="navBtn" v-if="question.answered && current < questions.length - 1 && question.type == 11" v-on:click="nextQuestion" class="quiz__button"
                      v-bind:style="{'backgroundColor': question.answered && !question.correct ? '#CC3333' : '#41A336'}">
                                    Câu tiếp theo &nbsp; <i class="fa fa-angle-right fa-lg"></i></span>
                <span ref="navBtn" v-if="question.answered && current == questions.length - 1" v-on:click="showResult" class="quiz__button"
                      v-bind:style="{'backgroundColor': question.answered && !question.currentAnswer.grade ? '#CC3333' : '#41A336'}">
                                    Hoàn thành &nbsp; <i class="fa fa-angle-right fa-lg"></i></span>
              </div>
            </div>
          </div>
        </transition-group>
      </div>
      <div class="quiz__body--result" v-if="state == 'finished'">
        <div class="quiz__body--result-card" v-if="!viewHistory">
          <div class="quiz__body--result-card-left">
            {{--                        <div class="quiz__body--result-card-point">--}}
            {{--                            @{{ result.passed.length }}/@{{ questions.length }}--}}
            {{--                        </div>--}}
            <img width="200px" src="{{asset('assets/img/mori-quiz-result.png')}}?{{filemtime('assets/img/mori-quiz-result.png')}}"/>
          </div>
          <div class="quiz__body--result-card-right">
            <div style="font-size: 25px; font-weight: bold; text-align: center">CHÚC MỪNG</div>
            <div style="font-size: 16px; line-height: 27px; text-align: center">Bạn vừa làm xong bài quiz</div>
            <div style="font-size: 16px; line-height: 27px; text-align: center">Cùng xem lại kết quả nhé!</div>
          </div>
        </div>
        <div class="quiz__body--result-history" v-if="viewHistory">
          <div class="quiz__body--result-history-title">
            <i class="fa fa-history"></i> Lịch sử thi
          </div>
          <div class="quiz__body--result-history-table">
            <table class="table table-sm table-dark">
              <thead>
              <tr>
                <th scope="col">#</th>
                <th scope="col">Điểm thi</th>
                <th scope="col">Ngày thi</th>
                <th scope="col">Trạng thái</th>
                <th scope="col">Thao tác</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(result, index) in results">
                <td>@{{ index + 1 }}</td>
                <td>@{{ result.grade }}/@{{ result.total_grade }}</td>
                <td>@{{ result.created_at | dateTime }}</td>
                <td><span :class="{'text-success' : result.passed, 'text-danger': !result.passed}">@{{ result.passed | isPassed}}</span></td>
                <td>
                  <span class="text-success a-cursor-pointer" @click="showSelectedResult(result)">Xem bài làm</span>
                  <span>|</span>
                  <span class="text-danger a-cursor-pointer" @click="deleteResult(result)">Xoá</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
        <modal v-if="showOldResult" @close="closeOldResult">
          <div slot="header" class="flex justify-between"><h3>Chi tiết bài kiểm tra</h3><span class="a-cursor-pointer" @click="showOldResult = false"><i class="fa-lg fa fa-times"></i></span></div>
          <div slot="body">
            <div>
              <table class="table table-bordered table-hover tc-table">
                <tbody>
                <tr>
                  <th scope="row">Kết quả</th>
                  <td>@{{ selectedResult.passed | isPassed }}</td>
                </tr>
                <tr>
                  <th scope="row">Điểm của bạn</th>
                  <td>@{{ selectedResult.grade | mark}}</td>
                </tr>
                <tr>
                  <th scope="row">Điểm đạt yêu cầu</th>
                  <td>@{{ lesson.pass_marks | mark }}</td>
                </tr>
                <tr>
                  <th scope="row">Điểm tối đa</th>
                  <td>@{{ selectedResult.total_grade | mark}}</td>
                </tr>
                <tr>
                  <th scope="row">Thời gian thi</th>
                  <td>@{{ selectedResult.created_at | dateTime }}</td>
                </tr>
                </tbody>
              </table>
              <div class="quiz__body--result-questions">
                <div class="quiz__body--result-question" v-for="question in resultQuestions">
                  <div><span v-html="question.mask"></span><span class="a-cursor-pointer text-success" v-if="question.answer_audio" @click="playAudio(question.answer_audio, 'playingAnswerAudio')"><i class="zmdi zmdi-volume-up"></i></span></div>
                  <div class="quiz__body--result-answers" v-if="question.type == 10">
                    <div v-for="(answer, index) in question.answers" class="pl-5" :class="{'quiz__body--result-answer-correct': answer.grade > 0, 'quiz__body--result-answer-wrong': selectedResult.data[question.id] == answer.id && answer.grade == 0}">
                      <div style="display: inline-block" >
                        <input type="radio" :checked="selectedResult.data[question.id] == answer.id" onclick="this.checked = false"/>&nbsp; <span>@{{ index + 1 }}. </span><span v-html="answer.value"></span><span>@{{ answer.grade > 0 ? '(Đúng)' : (selectedResult.data[question.id] == answer.id ? '(Sai)' : '') }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="quiz__body--result-answers-gap" v-if="question.type == 11">
                    <div v-for="(answer, index) in question.answers" >
                                            <span class="quiz__body--result-answer-correct">
                                                <span>@{{ index + 1 }}.</span>&nbsp;<span v-html="answer.value"></span>
                                            </span>
                    </div>
                    <div v-for="(answer, index) in question.sortedAnswers" >
                                            <span :class="{'quiz__body--result-answer-correct': answer.id === question.answers[index].id, 'quiz__body--result-answer-wrong': answer.id !== question.answers[index].id}">
                                                <span><i class="fa" :class="{'fa-check': answer.id === question.answers[index].id, 'fa-times': answer.id !== question.answers[index].id}"></i></span>&nbsp;<span v-html="answer.value"></span>
                                            </span>
                    </div>
                  </div>
                </div>
                <div class="text-center"><span >-- Hết --</span></div>
              </div>
            </div>
          </div>
        </modal>
        <modal v-if="showExplain" @close="closeExplain">
          <div slot="header" class="flex justify-between"><h3>Lời giải chi tiết</h3><span class="a-cursor-pointer" @click="showExplain = false"><i class="fa-lg fa fa-times"></i></span></div>
          <div slot="body">
            <div v-if="explain" v-html="explain.content"></div>
            <div v-else>Chưa có lời giải chi tiết</div>
          </div>
        </modal>
        <div class="quiz__body--result-button">
          <span class="btn btn-md btn-success" @click="showExplain = true"><i class="fa fa-book-open"></i>  Lời giải chi tiết</span>
          <span class="btn btn-md btn-success" @click="reviewAnswer"><i class="fa fa-eye"></i>  Xem lại bài làm</span>
          <span class="btn btn-md btn-success" v-if="viewHistory" @click="viewHistory = 0"><i class="fa fa-calendar"></i>  Kết quả</span>
          <span class="btn btn-md btn-success" v-if="!viewHistory" @click="viewHistory = 1"><i class="fa fa-calendar"></i>  Lịch sử làm bài</span>
          <span class="btn btn-md btn-success" @click="testAgain"><i class="fa fa-undo"></i> Làm lại</span>
          {{--                    <a :href="url + '/' + nextLessonUrl" class="btn btn-md btn-success" @click="end"><i class="fa fa-step-forward"></i> Bài tiếp theo</a>--}}
        </div>
      </div>
    </div>
  </div>
  {{--    thêm dom này để kích hoạt autoplay --}}
  <iframe src="" allow="autoplay" id="audio" style="display: none"></iframe>
</script>
