
<script type="text/x-template" id="test-ad-list">
  <div>
    <div class="test-online__list">
        <div v-for="exam in groups" :key="'exam-' + exam.id">
          <div v-if="!exam.time_split || exam.time_split.length === 0" class="test-online__schedule">
            <div>
              <div
                  class="font-semibold"
                  :class="exam.result.length && exam.result[0].data_1 ? 'text-green-500' : 'text-gray-900'"
                  v-cloak
              >@{{ exam.name }}</div>
              <div v-if="!exam.available" class="font-quicksand">Bắt đầu lúc <span class="text-red font-semibold">@{{ exam.time_start | datetime }}</span></div>
              <div class="font-quicksand">Hạn nộp bài <span class="text-red font-semibold">@{{ exam.time_end | datetime }}</span></div>
            </div>
            <div class="flex items-center">
              <div class="test-online__join-btn" :class="{ disabled: !exam.available }" @click="goTest(exam)">@{{ exam.result.length && exam.result[0].data_1 ? 'Chi tiết' : 'Làm bài'}}</div>
            </div>
          </div>
        </div>
      </div>
  </div>
</script>
