@include('frontend._layouts.components.modal')
@if (Route::currentRouteName() == 'account')
  @include('frontend._layouts.components.ht')
@endif
@if (Route::currentRouteName() == 'home')
  @include('frontend._layouts.components.home-feedback')
@endif

@if (in_array(Route::currentRouteName(), ['frontend.course.detail', 'frontend.lesson.detail']))
  @if(isset($isLD) && $isLD)
    @include('frontend._layouts.components.course-group-ld')
  @elseif(isset($premium) && $premium)
    @include('frontend._layouts.components.course-group-premium')
  @else
    @include('frontend._layouts.components.course-group')
  @endif
@endif

@if (in_array(Route::currentRouteName(), ['frontend.lesson.detail', 'frontend.lesson.cdbh']))
  @if(isset($tasks) && in_array(10, $tasks->pluck('type')->toArray()))
    @include('frontend._layouts.components.mixed-test')
  @endif
  @if(isset($tasks) && in_array(12, $tasks->pluck('type')->toArray()))
    @include('frontend._layouts.components.video-modal')
    @include('frontend._layouts.components.pie-timer')
  @endif
@endif

@if (Route::currentRouteName() == 'review.index'))
  @include('frontend._layouts.components.comment-feedback')
@endif
@if (in_array(Route::currentRouteName(), ['frontend.course.detail', 'frontend.lesson.detail']))
  @if(isset($cdbh) && $cdbh)
    @include('frontend._layouts.components.comment-bh')
  @else
    @include('frontend._layouts.components.comment-eju')
    @include('frontend._layouts.components.comment')
  @endif
@endif

@if (str_starts_with(Route::currentRouteName(), 'community.'))
  @include('frontend._layouts.components.comment-community')
@endif

@if (in_array(Route::currentRouteName(), ['test-online.index', 'test-online.get-exam']))
  @include('frontend._layouts.components.test-online')
  @include('frontend._layouts.components.test-online-rank')
  @include('frontend._layouts.components.test-online-history')
@endif
@if (in_array(Route::currentRouteName(), ['ad-test-fe.index', 'ad-test-fe.get-exam']))
  @include('frontend._layouts.components.test-ad')
  @include('frontend._layouts.components.test-ad-rank')
  @include('frontend._layouts.components.test-ad-history')
@endif

