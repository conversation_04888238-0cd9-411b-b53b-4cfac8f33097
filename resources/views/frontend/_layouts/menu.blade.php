<div class="hidden lg:flex fixed w-full top-0 py-[15px] left-0 bg-dmr-green h-[76px] z-[100] pc-header">
    <div class="container">
        <div class="header-inner">
            <div class="header-logo">
                <a href="{{route('home')}}" class="logo1">
                    <img class="lazyload object-cover"
                         data-src="{{asset('assets/img/new_home/06-2024/dungmori-logo.svg')}}" alt="dungmori-logo.svg"/>
                </a>
                <a href="{{route('home')}}" class="logo2" style="display: none">
                    <img class="object-cover" src="{{asset('images/dungmori-logo2.png')}}" alt="dungmori-logo2.png"/>
                </a>
                <a href="{{route('home')}}" class="logo3" style="display: none">
                    <img class="object-cover" src="{{asset('images/dungmori-logo3.png')}}" alt="dungmori-logo3.png"/>
                </a>
            </div>
            <div class="header-menu font-beanbag">
                <div class="header-menu__item text-[0.9em] xlg:text-lg">
                    <a href="{{ url('/ve-dungmori') }}" class="!text-white hover:text-white active:text-white">Về
                        DUNGMORI</a>
                    {{--                    <img class="lazyload object-cover stroke-current text-white fill-current" src="{{ asset('/assets/img/hamburger.svg') }}" />--}}
                    <div class="header-menu__dropdown">
                        <div class="header-menu__dropdown-inner bg-dmr-green text-white">
                            <a href="{{ url('/ve-dungmori') }}" class="header-menu__dropdown-item">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">VỀ DUNGMORI</div>
                                    <div class="description">Lịch sử hình thành và phát triển</div>
                                </div>
                            </a>
                            <a href="{{ url('https://dungmori.co.jp') }}" class="header-menu__dropdown-item"
                               target="_blank">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">Dung Mori Japan</div>
                                    <div class="description">Homepage chính thức tại Nhật Bản</div>
                                </div>
                            </a>
                            <div onclick="goTo('{{ route('blog.index') }}')"
                                 class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'blog.index') ? 'active' : '' }}">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">Tin tức</div>
                                    <div class="description">Cập nhật tin tức và kiến thức tiếng Nhật</div>
                                </div>
                            </div>
                            {{-- <div onclick="goTo('{{ url('')}}/bai-viet/cm/cau-chuyen-lop-vip')" class="header-menu__dropdown-item {{ str_contains(Request::path(), 'cm/cau-chuyen-lop-vip') ? 'active' : '' }}">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">Câu chuyện lớp VIP</div>
                                    <div class="description">Chia sẻ những câu chuyện thú vị của lớp VIP Online</div>
                                </div>
                            </div> --}}
                            <div onclick="goTo('{{ route('teacher.index') }}')"
                                 class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'teacher') ? 'active' : '' }}">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">Giáo viên</div>
                                    <div class="description">Đội ngũ giáo viên của Dũng Mori</div>
                                </div>
                            </div>
                            <div onclick="goTo('{{ route('review.index') }}')"
                                 class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'review') ? 'active' : '' }}">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">Review dũng mori</div>
                                    <div class="description">Chia sẻ của học viên về Khóa học</div>
                                </div>
                            </div>
                            <div onclick="goTo('{{ route('recruitment') }}')"
                                 class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'recruitment') ? 'active' : '' }}">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">Tuyển dụng</div>
                                    <div class="description">Trang tuyển dụng Dũng Mori</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{--                <a href="{{url('/ve-dungmori')}}" class="header-menu__item text-[0.9em] xlg:text-lg">--}}
                {{--                    <span>Về DUNGMORI</span>--}}
                {{--                </a>--}}
                <a href="{{url('/groups')}}" class="header-menu__item text-[0.9em] xlg:text-lg">
                    <span>Cộng đồng</span>
                </a>
                <div class="header-menu__item text-[0.9em] xlg:text-lg">
                    <span>JLPT</span>
                    <div class="header-menu__dropdown mega">
                        <div class="header-menu__dropdown-inner flex">
                            <ul class="menu-column border-right w-0.3 flex-shrink-0" role="tablist">
                                <li class="header-menu__dropdown-item active">
                                    <a href="#jlpt" class="dropdown-parent header-menu__dropdown-item-inner"
                                       role="presentation" data-toggle="pill">
                                        <div class="flex">
                                            <div class="">
                                                <div class="title text-white">Học qua App/Web</div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                <li class="header-menu__dropdown-item">
                                    <a href="#kaiwa" class="dropdown-parent header-menu__dropdown-item-inner"
                                       role="presentation" data-toggle="pill">
                                        <div class="flex">
                                            <div class="">
                                                <div class="title text-white"
                                                     onclick="goToWindow('{{ url('https://onlinevip.dungmori.com/') }}')">
                                                    Học qua Zoom
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                                <li class="header-menu__dropdown-item">
                                    <a href="#offline" class="dropdown-parent header-menu__dropdown-item-inner"
                                       role="presentation" data-toggle="pill">
                                        <div class="flex">
                                            <div class="">
                                                <div class="title text-white"
                                                     onclick="ga('send', 'event', 'Menu', 'click', 'Offline'); goToWindow('{{ url('https://offline.dungmori.com/tructiep?utm_source=website&utm_medium=menu&utm_campaign=header') }}')">
                                                    Offline
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                            <div class="menu-column tab-content flex-grow-1" role="tablist">
                                <div class="tab-pane active in min-h-full" id="jlpt">
                                    <div class="flex">
                                        <ul>
                                            <a href="{{url('/khoa-hoc/so-cap-n5')}}"
                                               class="header-menu__dropdown-sub-item"
                                               onclick="ga('send', 'event', 'Menu', 'click', 'N5')">
                                                <div class="header-menu__dropdown-sub-item-inner">
                                                    <div class="title">Gungun N5</div>
                                                </div>
                                            </a>
                                            <a href="{{url('/khoa-hoc/so-cap-n4')}}"
                                               class="header-menu__dropdown-sub-item"
                                               onclick="ga('send', 'event', 'Menu', 'click', 'N4')">
                                                <div class="header-menu__dropdown-sub-item-inner">
                                                    <div class="title">Gungun N4</div>
                                                </div>
                                            </a>
                                            <a href="{{url('/khoa-hoc/jlpt-n3')}}"
                                               class="header-menu__dropdown-sub-item"
                                               onclick="ga('send', 'event', 'Menu', 'click', 'N3')">
                                                <div class="header-menu__dropdown-sub-item-inner">
                                                    <div class="title">Gungun N3 <span
                                                        style="font-size: 14px; background-color: #FF6531; padding: 5px 10px; border-radius: 15px; color: #fff; margin-left: 5px;">MỚI</span></div>
                                                </div>
                                            </a>
                                            <a href="{{url('/khoa-hoc/jlpt-n2')}}"
                                               class="header-menu__dropdown-sub-item"
                                               onclick="ga('send', 'event', 'Menu', 'click', 'N2')">
                                                <div class="header-menu__dropdown-sub-item-inner">
                                                    <div class="title">Gungun N2 <span
                                                        style="font-size: 14px; background-color: #FF6531; padding: 5px 10px; border-radius: 15px; color: #fff; margin-left: 5px;">MỚI</span></div>
                                                </div>
                                            </a>
                                            <a href="{{url('/khoa-hoc/jlpt-n1')}}"
                                               class="header-menu__dropdown-sub-item"
                                               onclick="ga('send', 'event', 'Menu', 'click', 'N1')">
                                                <div class="header-menu__dropdown-sub-item-inner">
                                                    <div class="title">Gungun N1 <span
                                                        style="font-size: 14px; background-color: #FF6531; padding: 5px 10px; border-radius: 15px; color: #fff; margin-left: 5px;">MỚI</span></div>
                                                </div>
                                            </a>
                                            <a href="{{url('/khoa-hoc/chu-han-n5')}}"
                                               class="header-menu__dropdown-sub-item"
                                               onclick="ga('send', 'event', 'Menu', 'click', 'Chữ Hán N5')">
                                                <div class="header-menu__dropdown-sub-item-inner">
                                                    <div class="title">Chữ Hán N5 <span
                                                        style="font-size: 14px; background-color: #FF6531; padding: 5px 10px; border-radius: 15px; color: #fff; margin-left: 5px;">MIỄN PHÍ</span></div>
                                                </div>
                                            </a>
                                            <a href="{{url('/khoa-hoc/chuyen-nganh')}}"
                                               class="header-menu__dropdown-sub-item"
                                               onclick="ga('send', 'event', 'Menu', 'click', 'Chuyên ngành')">
                                                <div class="header-menu__dropdown-sub-item-inner">
                                                    <div class="title">Chuyên ngành</div>
                                                </div>
                                            </a>
                                        </ul>
                                    </div>
                                </div>
                                <div class="tab-pane min-h-full" id="kaiwa">
                                    <div class="px-4">
                                        <img src="{{url('assets/img/new_home/06-2024/menu_2.png')}}"/>
                                    </div>
                                    {{--                                    <ul>--}}
                                    {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                    {{--                                            <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa sơ cấp')">--}}
                                    {{--                                                <div class="title">Kaiwa Sơ cấp</div>--}}
                                    {{--                                            </a>--}}
                                    {{--                                        </li>--}}
                                    {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                    {{--                                            <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa Trung cấp')">--}}
                                    {{--                                                <div class="title">Kaiwa Trung cấp</div>--}}
                                    {{--                                            </a>--}}
                                    {{--                                        </li>--}}
                                    {{--                                    </ul>--}}
                                </div>
                                <div class="tab-pane min-h-full" id="offline">
                                    <div class="px-4">
                                        <img src="{{url('assets/img/new_home/06-2024/menu_1.png')}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg mega mega">--}}
                {{--                    <span>JLPT</span>--}}
                {{--                    <div class="header-menu__dropdown">--}}
                {{--                        <div class="header-menu__dropdown-inner flex">--}}
                {{--                            <div class="menu-column tab-content flex-grow-1" role="tablist">--}}
                {{--                                <div class="tab-pane active" id="luyenThi">--}}
                {{--                                    <a href="{{url('/khoa-hoc/so-cap-n5')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N5')">--}}
                {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                {{--                                            <div class="title">Sơ cấp N5 <span style="font-size: 14px; background-color: #FF6531; padding: 5px 10px; border-radius: 15px; color: #fff; margin-left: 5px;">Học thử miễn phí</span></div>--}}
                {{--                                        </div>--}}
                {{--                                    </a>--}}
                {{--                                    <a href="{{url('/khoa-hoc/so-cap-n4')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N4')">--}}
                {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                {{--                                            <div class="title">Sơ cấp N4</div>--}}
                {{--                                        </div>--}}
                {{--                                    </a>--}}
                {{--                                    <a href="{{url('/khoa-hoc/jlpt-n3')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N3')">--}}
                {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                {{--                                            <div class="title">Khóa N3</div>--}}
                {{--                                        </div>--}}
                {{--                                    </a>--}}
                {{--                                    <a href="{{url('/khoa-hoc/jlpt-n2')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N2')">--}}
                {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                {{--                                            <div class="title">Khóa N2</div>--}}
                {{--                                        </div>--}}
                {{--                                    </a>--}}
                {{--                                    <a href="{{url('/khoa-hoc/jlpt-n1')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N1')">--}}
                {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                {{--                                            <div class="title">Khóa N1</div>--}}
                {{--                                        </div>--}}
                {{--                                    </a>--}}
                {{--                                </div>--}}
                {{--                            </div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                </div>--}}
                <div class="header-menu__item text-[0.9em] xlg:text-lg"
                     onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa'); goToWindow('{{ url('https://kaiwa.dungmori.com/') }}')">
                    <span>Giao tiếp</span>
                    {{--                    <div class="header-menu__dropdown">--}}
                    {{--                        <div class="header-menu__dropdown-inner">--}}
                    {{--                            <div class="tab-pane min-h-full" id="kaiwa">--}}
                    {{--                                <ul>--}}
                    {{--                                    <li class="header-menu__dropdown-sub-item">--}}
                    {{--                                        <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa sơ cấp')">--}}
                    {{--                                            <div class="title">Kaiwa Sơ cấp</div>--}}
                    {{--                                        </a>--}}
                    {{--                                    </li>--}}
                    {{--                                    <li class="header-menu__dropdown-sub-item">--}}
                    {{--                                        <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa Trung cấp')">--}}
                    {{--                                            <div class="title">Kaiwa Trung cấp</div>--}}
                    {{--                                        </a>--}}
                    {{--                                    </li>--}}
                    {{--                                </ul>--}}
                    {{--                            </div>--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}
                </div>

                {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg">--}}

                {{--                    <span class="text-red-500">MJT テスト--}}
                {{--                        <img src="{{url('assets/img/onl.gif')}}" />--}}
                {{--                    </span>--}}
                {{--                    <div class="header-menu__dropdown">--}}
                {{--                        <div class="header-menu__dropdown-inner">--}}
                {{--                            <div onclick="goToWindow('{{ route('thi-thu.index') }}')" class="header-menu__dropdown-item">--}}
                {{--                                <div class="header-menu__dropdown-item-inner">--}}
                {{--                                    <div class="title">MJT テスト--}}
                {{--                                        <img src="{{url('assets/img/onl.gif')}}" />--}}
                {{--                                    </div>--}}
                {{--                                    <div class="description">Thi thử JLPT trên Dũng Mori</div>--}}
                {{--                                </div>--}}
                {{--                            </div>--}}
                {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Test online'); goToWindow('{{ route('test-online.index') }}')" class="header-menu__dropdown-item">--}}
                {{--                                <div class="header-menu__dropdown-item-inner">--}}
                {{--                                    <div class="title">Test online</div>--}}
                {{--                                    <div class="description">Làm bài tập đầu giờ các nhóm zoom</div>--}}
                {{--                                </div>--}}
                {{--                            </div>--}}
                {{--                            --}}{{--                                    <div onclick="goToWindow('{{ route('ad-test-fe.index') }}')" class="header-menu__dropdown-item">--}}
                {{--                            --}}{{--                                        <div class="header-menu__dropdown-item-inner">--}}
                {{--                            --}}{{--                                            <div class="title">Đồng hồ báo thức mùa thi</div>--}}
                {{--                            --}}{{--                                            <div class="description"></div>--}}
                {{--                            --}}{{--                                        </div>--}}
                {{--                            --}}{{--                                    </div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                </div>--}}
                {{-- <div onclick="goTo('{{ route('thi-thu.index') }}')" class="header-menu__item text-[0.9em] xlg:text-lg featured {{ Route::currentRouteNamed('thi-thu') ? 'active' : '' }}">
                    <span>MJT テスト</span>
                </div> --}}
                {{-- <div onclick="goTo('{{ route('test-online.index') }}')" class="header-menu__item text-[0.9em] xlg:text-lg {{ Route::currentRouteNamed('test-online.index') ? 'active' : '' }}">
                    <span>Test Online</span>
                </div> --}}
                {{--                <a href="{{ url('/groups') }}" class="header-menu__item text-[0.9em] xlg:text-lg" onclick="ga('send', 'event', 'Menu', 'click', 'Community')">--}}
                {{--                    <span>Cộng đồng</span>--}}
                {{--                </a>--}}

                {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg">--}}
                {{--                    <span>Online Vip</span>--}}
                {{--                    <div class="header-menu__dropdown">--}}
                {{--                        <div class="header-menu__dropdown-inner">--}}
                {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Online VIP'); goToWindow('{{ url('https://onlinevip.dungmori.com') }}')" class="header-menu__dropdown-item">--}}
                {{--                                <div class="header-menu__dropdown-item-inner">--}}
                {{--                                    <div class="title">Online VIP</div>--}}
                {{--                                    <div class="description">Học qua phần mềm Zoom</div>--}}
                {{--                                </div>--}}
                {{--                            </div>--}}
                {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Online Plus'); goToWindow('{{ url('https://onlineplus.dungmori.com') }}')" class="header-menu__dropdown-item">--}}
                {{--                                <div class="header-menu__dropdown-item-inner">--}}
                {{--                                    <div class="title">Online Plus</div>--}}
                {{--                                    <div class="description">Học qua phần mềm Livestream</div>--}}
                {{--                                </div>--}}
                {{--                            </div>--}}
                {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa'); goToWindow('{{ url('https://kaiwa.dungmori.com/') }}')" class="header-menu__dropdown-item">--}}
                {{--                                <div class="header-menu__dropdown-item-inner">--}}
                {{--                                    <div class="title">Kaiwa</div>--}}
                {{--                                    <div class="description">Học qua phần mềm Zoom</div>--}}
                {{--                                </div>--}}
                {{--                            </div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                </div>--}}
                {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg" onclick="ga('send', 'event', 'Menu', 'click', 'Offline'); goToWindow('https://offline.dungmori.com/tructiep?utm_source=website&utm_medium=menu&utm_campaign=header')">--}}
                {{--                    <span>Offline</span>--}}
                {{--                </div>--}}
                <div class="header-menu__item text-[0.9em] xlg:text-lg"
                     onclick="ga('send', 'event', 'Menu', 'click', 'Book'); goToWindow('https://sach.dungmori.com')">
                    <span>Sách</span>
                </div>
                <div class="header-menu__item text-[0.9em] xlg:text-lg">
                    <div>B2B</div>
                    <div class="header-menu__dropdown">
                        <div class="header-menu__dropdown-inner">
                            <div onclick="ga('send', 'event', 'Menu', 'click', 'B2B'); goToWindow('https://online.dungmori.com/b2b')"
                                 class="header-menu__dropdown-item">
                                <div class="header-menu__dropdown-item-inner">
                                    <div class="title">Khóa doanh nghiệp</div>
                                    <div class="description">Hợp tác cùng với Dũng Mori</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
{{--                <a href="{{  url('/vocabulary') }}" class="header-menu__item text-[0.9em] xlg:text-lg">--}}
{{--                    <span>Rừng ngôn từ</span>--}}
{{--                </a>--}}
            </div>
            @if (auth()->check())
                <a href="{{ route('frontend.user.account.rewards') }}"
                   class="leaf border-2 border-white bg-white rounded-full py-2 px-4 text-dmr-green-dark hover:text-dmr-green-dark font-quicksand font-bold flex items-center gap-1 justify-center cursor-pointer hover:bg-[#F7FFF0]"
                   onclick="ga('send', 'event', 'Menu', 'click', 'Thành tích')">
                    <img src="{{ asset('assets/img/gamification/leaf.svg') }}" alt="" class="w-5 shrink-0">
                    {{ auth()->user()->getPoints() }}
                </a>
            @else
                <div class="leaf border border-white bg-white rounded-full py-2 px-4 text-dmr-green-dark font-quicksand font-bold flex items-center gap-1 justify-center">
                    <img src="{{ asset('assets/img/gamification/leaf.svg') }}" alt="" class="w-5 shrink-0">
                    0
                </div>
            @endif

            <div class="flex justify-end items-center gap-3">
                @if (Auth::check())
                    <div class="tooltip-i">
                        <svg onclick="ga('send', 'event', 'Menu', 'click', 'My courses'); myCourseClick()" width="17"
                            height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                            class="w-5 cursor-pointer stroke-current text-white hover:text-dmr-green-dark icon-color">
                            <path d="M6.90619 3.5C6.90619 5.08344 5.5237 6.43 3.7381 6.43C1.95249 6.43 0.57 5.08344 0.57 3.5C0.57 1.91656 1.95249 0.57 3.7381 0.57C5.5237 0.57 6.90619 1.91656 6.90619 3.5Z"
                                  stroke-width="2"/>
                            <path d="M16.1625 3.5C16.1625 5.08344 14.7801 6.43 12.9944 6.43C11.2088 6.43 9.82635 5.08344 9.82635 3.5C9.82635 1.91656 11.2088 0.57 12.9944 0.57C14.7801 0.57 16.1625 1.91656 16.1625 3.5Z"
                                  stroke-width="2"/>
                            <path d="M6.90619 12.1665C6.90619 13.7499 5.5237 15.0965 3.7381 15.0965C1.95249 15.0965 0.57 13.7499 0.57 12.1665C0.57 10.5831 1.95249 9.2365 3.7381 9.2365C5.5237 9.2365 6.90619 10.5831 6.90619 12.1665Z"
                                  stroke-width="2"/>
                            <path d="M16.1625 12.1665C16.1625 13.7499 14.7801 15.0965 12.9944 15.0965C11.2088 15.0965 9.82635 13.7499 9.82635 12.1665C9.82635 10.5831 11.2088 9.2365 12.9944 9.2365C14.7801 9.2365 16.1625 10.5831 16.1625 12.1665Z"
                                  stroke-width="2"/>
                        </svg>
                        <span class="tooltiptext">Khóa học của tôi</span>
                    </div>
                    <div id="my-course" style="display: none;">
                        <div class="my-course-main">
                            <div class="my-course-container">
                                <div class="title-box">
                                    <span class="title">Khóa học của tôi</span><br>
                                    <span class="description">Các khóa bạn đang sở hữu, học ngay!</span>
                                    <i class="fa fa-window-close" aria-hidden="true" onclick="myCourseClick()"></i>
                                </div>
                                <hr>
                                <div id="my-course-content" class="my-course-content" style="display: none;">
                                    <div v-if="myCourses.length === 0" class="empty-box">
                                        <img class="lazyload object-cover"
                                             data-src="{{url('assets/img/img_empty.png')}}">
                                        <span>Bạn không sở hữu khóa học nào</span>
                                        <a href="{{url('/bang-gia')}}">Mua ngay</a>
                                    </div>
                                    <a v-for="course in myCourses" :href="'{{url('/khoa-hoc')}}/' + course.SEOurl">
                                        <div class="my-course-item">
                                            <img :src="'{{asset('cdn/course/small')}}/' + course.avatar_name">
                                            <span>
                                                <i class="fa fa-circle" aria-hidden="true"></i> <b>@{{ course.name }}</b> <i
                                                    class="fa fa-circle" aria-hidden="true"></i>
                                            </span>
                                            <span>
                                                <i class="fa fa-circle" aria-hidden="true"></i> Hết hạn:&nbsp;<b>@{{ course.expired_day }}</b> <i
                                                    class="fa fa-circle" aria-hidden="true"></i>
                                            </span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script type="text/javascript">
                        function myCourseClick(onlyClose = false) {
                            var myCourseDiv = document.getElementById("my-course");
                            if (!onlyClose && myCourseDiv.style.display === 'none') {
                                $('#my-course').css('display', 'block');
                                myCourseDiv.classList.remove("my-course-hide");
                                myCourseDiv.classList.add("my-course-show");
                                if (myCoursesVue) {
                                    myCoursesVue.getMyCourses();
                                }
                            } else {
                                myCourseDiv.classList.remove("my-course-show");
                                myCourseDiv.classList.add("my-course-hide");
                                setTimeout(function () {
                                    $('#my-course').css('display', 'none');
                                }, 200);
                            }
                        }

                        var mycourse = document.getElementById('my-course');
                        mycourse.onclick = function (event) {
                            if (event.target == mycourse) {
                                myCourseClick();
                            }
                        }
                    </script>
                @endif


                <a href="{{url('/khoa-hoc')}}"
                   class="cursor-pointer text-white stroke-current fill-current hover:text-dmr-green-dark icon-color"
                   onclick="ga('send', 'event', 'Menu', 'click', 'Store')">
                   <div class="tooltip-i">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                            class="w-4">
                            <path d="M1.02191 1.28948H22.6139V20.2245C22.6139 21.6919 21.4243 22.8815 19.957 22.8815H3.67886C2.21146 22.8815 1.02191 21.6919 1.02191 20.2245V1.28948Z"
                                stroke-width="2.5"/>
                            <path d="M5.41602 7.4082C7.5498 11.2654 13.0484 16.6656 17.9725 7.4082" stroke-width="2.5"/>
                        </svg>
                        <span class="tooltiptext">Cửa hàng</span>
                    </div>
                </a>
                <div id="maintain-container" style="display: none;background: none; text-align: center;">
                    <img class="lazyload object-cover" data-src="{{ asset('assets/img/new_home/maintain-web.png') }}"
                         alt="" style="max-width: 50%; margin: auto;">
                </div>
                <style>
                    #jlpt-gift-container {
                        display: none;
                        background: none;
                        text-align: center;
                    }

                    #jlpt-gift-container .jlpt-gift-content {
                        background: white;
                        border-radius: 15px;
                    }

                    #jlpt-gift-container .jlpt-gift-content > p {
                        font-family: 'Quicksand';
                        font-style: normal;
                        font-weight: 500;
                        font-size: 30px;
                        line-height: 1.61;
                        text-transform: uppercase;
                    }

                    #jlpt-gift-container .jlpt-gift-content > p:nth-child(2) {
                        font-weight: bold;
                        color: #96D962;
                    }

                    #jlpt-gift-container .jlpt-gift-content .quote {
                        width: 388px;
                        font-weight: 600;
                        font-size: 16px;
                        line-height: 1.61;
                        margin-top: 20px;
                    }

                    .jlpt-gift-content .btn {
                        margin-top: 30px;
                        padding: 15px 34px;
                        font-family: 'Montserrat';
                        font-style: normal;
                        font-weight: 700;
                        font-size: 20px;
                        line-height: 1.2;
                        color: #FFFFFF;
                        border-radius: 15px;
                    }

                    .jlpt-gift-content .btn-danger {
                        background-color: #FB6D3A;
                        margin-right: 20px;
                    }

                    .jlpt-gift-content .btn-info {
                        background-color: #1093F1;
                    }

                    .jlpt-gift-policy {
                        margin-left: 24px;
                        max-width: 350px;
                        text-align: left;
                    }

                    .jlpt-gift-policy .uppercase {
                        text-transform: uppercase;
                    }

                    .jlpt-gift-policy .text-red {
                        color: #FB6D3A;
                    }

                    .jlpt-gift-policy .text-blue {
                        color: #0E65E5;
                    }

                    input::-webkit-outer-spin-button,
                    input::-webkit-inner-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }

                    .btn-upload {
                        font-family: Quicksand;
                        background: #F7FFF0;
                        border: 1px solid #96D962;
                        border-radius: 10px;
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                        padding: 8px 50px;
                        gap: 10px;
                        color: green !important;
                        font-size: 16px !important;
                        font-weight: 400 !important;
                        margin-top: 0 !important;
                    }

                    .point-input {
                        position: relative;
                    }

                    .point-label {
                        position: absolute;
                        top: -10px;
                        left: 9px;
                        background: white;
                        color: #BDBDBD !important;
                    }

                    .fancybox-slide:before {
                        height: 0 !important;
                    }

                    @media only screen and (max-width: 992px) {
                        .two-columns {
                            flex-flow: column;
                            align-items: center;
                        }

                        .jlpt-gift-content {
                            /*padding: 40px 5px;*/
                        }

                        .fancybox-slide > * {
                            padding: 0;
                        }
                    }

                    #point-input-1,
                    #point-input-2,
                    #point-input-3,
                    #point-input-total {
                        font-weight: bold;
                        font-size: 13px;
                        border-bottom: 1px solid #000;
                        width: 5%;
                        text-align: right;
                        line-height: 1;
                    }
                </style>
                <div id="jlpt-gift-container" class="bg-gray-50">
                    <div class="jlpt-gift-content px-10 py-10 sm:px-[100px] sm:py-[40px]">
                        <p>Báo thành tích JLPT</p>
                        <p>Rinh quà từ dũng mori</p>
                        <template v-if="isPassed == null">
                            <img src="{{ asset('assets/img/new_home/jlpt-gift.png') }}" alt=""
                                 style="margin-top: 60px; max-width: 180px; margin: auto;">
                            <p class="quote">Dù trượt hay đỗ JLPT thì Dũng mori đều có
                                món quà nhỏ gửi đến các bạn!</p>
                            <div class="flex justify-center items-center">
                                <button class="btn btn-danger" @click="isPassed = 'preFailed'">Trượt JLPT</button>
                                <button class="btn btn-info" @click="isPassed = 'prePassed'">Đỗ JLPT</button>
                            </div>
                        </template>
                        <template v-else-if="isPassed == 'prePassed'">
                            <div class="p-5 mt-2 border-2 border-[#96D962] rounded-xl w-[95%] sm:w-[60%] m-auto text-left bg-green-50 leading-10 max-w-[1024px]">
                                <div class="text-md font-semibold">HỌC VIÊN ĐỖ:</div>
                                <p class="text-md leading-6">
                                    - Đỗ → <b>129 điểm: Giảm 200.000 VND</b> khi đăng ký học khóa học bất kỳ của Dũng Mori (được cộng
                                    gộp ưu đãi học viên cũ, thời hạn áp dụng 1 tháng)
                                    <br>
                                    - <b>130 - 149 điểm: </b> Thưởng <b>200.000</b> VND tiền mặt hoặc Voucher giảm <b>400.000</b> VND học phí
                                    <br>
                                    - <b>150 - 175 điểm: </b> Thưởng <b>500.000 </b> VND tiền mặt hoặc Voucher giảm <b>700.000</b> VND học phí
                                    <br>
                                    - Trên <b>175 điểm: </b> Thưởng <b>1.000.000 </b> VND tiền mặt hoặc Voucher giảm <b>1.200.000</b> VND học phí
                                </p>
                                <br>
                                <div class="text-md font-semibold">
                                    Đặc biệt:
                                </div>
                                <p class="text-md leading-6">
                                    *Học viên trên 160 điểm sẽ được tham gia duet giao lưu trực tiếp với cô Thanh, thầy Dũng
                                    <br>
                                    *Học viên Offline trên 160 điểm sẽ được tặng thư tay do thầy cô viết và trao trực tiếp tại các cơ sở Dungmori
                                    <br>
                                    *Học viên báo điểm đỗ giới thiệu bạn bè đăng ký học tặng 200.000 VND tiền mặt/ 1 bạn bè đăng ký (có áp dụng cùng ưu đãi học viên cũ)
                                </p>
                                <div class="text-center">
                                    ---------------------------------------------------------------------------
                                </div>
                                <p class="text-md leading-6">
                                    <b>*Cách thức nhận thưởng cho học viên:</b>
                                    <br>
                                    - Bước 1: Báo điểm thông qua link: <a href="https://dungmori.com/nhan-qua-jlpt" target="_blank">https://dungmori.com/nhan-qua-jlpt</a>
                                    <br>
                                    - Bước 2: Đăng ảnh bằng khen lên FB/tiktok cá nhân kèm nội dung tự chọn và hashtag #mình_chọn_dũng_mori, đăng ở chế độ công khai
                                    <br>
                                    - Bước 3: Điền thông tin vào link <a href="https://forms.gle/mVGfNqBPwxP6wLmj7" target="_blank">form báo điểm</a>
                                    <br>
                                    <br>
                                    <b>*MỘT SỐ LƯU Ý TRONG NỘI DUNG BÁO ĐIỂM</b>
                                    <br>
                                    - Tất cả thông tin trong bảng “BÁO ĐIỂM THI” cần được điền đầy đủ và chính xác. Nếu thiếu thông tin nào thì tài khoản báo điểm mặc định không được xác minh quà tặng.
                                    <br>
                                    - Điểm thi hợp lệ chỉ được tính trong kỳ thi JLPT tháng 12/2024
                                    <br>
                                    - Thời gian báo điểm hợp lệ: tính từ ngày 31/01/2025 - 06/02/2025
                                    <br>
                                    - Thời gian nhận thưởng: 13 - 14/02/2025
                                    <br>
                                    - Thời hạn sử dụng Voucher: Tới hết 06/03/2025
                                    {{--                            <a href="https://bit.ly/baodiemJLPT" target="_blank">https://bit.ly/baodiemJLPT</a>--}}
                                    {{--                            <br>--}}
                                    {{--                            <b>- THỜI GIAN BÁO ĐIỂM HỢP LỆ: Tính từ ngày 26/08 đến hết ngày 01/09/2024 </b><br>--}}
                                    {{--                            <b>- THỜI GIAN NHẬN THƯỞNG Trong 2 ngày 09 và 10/09/2024</b> <br>--}}
                                    {{--                            <b>- THỜI GIAN SỬ DỤNG VOUCHER: Đến 03/10/2024</b> <br>--}}
                                </p>
                            </div>
                            <button class="btn bg-[#fe9c9c]" @click="isPassed = null">Trở lại</button>
                            <button class="btn bg-[#96D962]" @click="isPassed = true">Tiếp tục</button>
                        </template>
                        <template v-else-if="isPassed == 'preFailed'">
                            <div class="p-5 mt-2 border-2 border-[#96D962] rounded-xl w-[60%] m-auto text-left leading-5 bg-green-50">
                                <div class="text-md font-semibold">HỌC VIÊN TRƯỢT</div>
                                <p class="text-md leading-6">
                                    Dũng Mori sẽ tặng thêm 1 tháng khóa học online hoặc giảm 10% khi đăng ký lại cấp độ đã học
                                </p>
                                <div class="text-center">
                                    ---------------------------------------------------------------------------
                                </div>
                                <div class="text-md font-semibold leading-6">Lưu ý:</div>
                                <p class="text-md leading-6">
                                    <b>*Cách thức nhận thưởng cho học viên:</b>
                                    <br>
                                    - Bước 1: Báo điểm thông qua link: <a href="https://dungmori.com/nhan-qua-jlpt" target="_blank">https://dungmori.com/nhan-qua-jlpt</a>
                                    <br>
                                    - Bước 2: Đăng ảnh bằng khen lên FB/tiktok cá nhân kèm nội dung tự chọn và hashtag #mình_chọn_dũng_mori, đăng ở chế độ công khai
                                    <br>
                                    - Bước 3: Điền thông tin vào link <a href="https://forms.gle/mVGfNqBPwxP6wLmj7" target="_blank">form báo điểm</a>
                                    <br>
                                    <br>
                                    <b>*MỘT SỐ LƯU Ý TRONG NỘI DUNG BÁO ĐIỂM</b>
                                    <br>
                                    - Tất cả thông tin trong bảng “BÁO ĐIỂM THI” cần được điền đầy đủ và chính xác. Nếu thiếu thông tin nào thì tài khoản báo điểm mặc định không được xác minh quà tặng.
                                    <br>
                                    - Điểm thi hợp lệ chỉ được tính trong kỳ thi JLPT tháng 12/2024
                                    <br>
                                    - Thời gian báo điểm hợp lệ: tính từ ngày 31/01/2025 - 06/02/2025
                                    <br>
                                    - Thời gian nhận thưởng: 13 - 14/02/2025
                                    <br>
                                    - Thời hạn sử dụng Voucher: Tới hết 06/03/2025
                                    {{--                            <a href="https://bit.ly/baodiemJLPT" target="_blank">https://bit.ly/baodiemJLPT</a>--}}
                                    {{--                            <br>--}}
                                    {{--                            <b>- THỜI GIAN BÁO ĐIỂM HỢP LỆ: Tính từ ngày 26/08 đến hết ngày 01/09/2024 </b><br>--}}
                                    {{--                            <b>- THỜI GIAN NHẬN THƯỞNG Trong 2 ngày 09 và 10/09/2024</b> <br>--}}
                                    {{--                            <b>- THỜI GIAN SỬ DỤNG VOUCHER: Đến 03/10/2024</b> <br>--}}
                                </p>
                            </div>
                            <button class="btn bg-[#fe9c9c]" @click="isPassed = null">Trở lại</button>
                            <button class="btn bg-[#96D962]" @click="isPassed = false">Tiếp tục</button>
                        </template>
                        <template v-else-if="isPassed == true">
                            @if (!auth()->check() && !isset($achievement))
                                <div class="flex justify-center items-center" style="min-height: 500px;">
                                    <button class="btn btn-success" data-fancybox data-animation-duration="300"
                                            data-src="#auth-container" onclick="swichTab('login')">Đăng nhập
                                    </button>
                                </div>
                            @else
                                <div v-if="!success"
                                     class="w-[300px] flex flex-col justify-center items-center mt-5 mx-auto">
                                    <div class="w-full point-input mb-4">
                                        <div class="point-label">Họ và tên</div>
                                        <input v-model="name" type="text"
                                               style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                    </div>
                                    <div class="w-full point-input mb-4">
                                        <div class="point-label">Trình độ</div>
                                        <select name="" id="" v-model="level"
                                                style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                        >
                                            <option value="N1">N1</option>
                                            <option value="N2">N2</option>
                                            <option value="N3">N3</option>
                                            <option value="N4">N4</option>
                                            <option value="N5">N5</option>
                                        </select>
                                    </div>
                                    <div class="w-full point-input mb-4">
                                        <div class="point-label">Tổng điểm</div>
                                        <input v-model="point" type="text"
                                               style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                    </div>
                                    <div class="w-full point-input mb-4" v-if="show_gift_value">
                                        <div class="point-label">Chọn quà</div>
                                        <select name="" id="" v-model="gift_value"
                                                style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                        >
                                            <option value="3">Voucher giảm giá</option>
                                            <option value="2">Thưởng tiền mặt</option>
                                        </select>
                                    </div>
                                    <button v-if="!imageName" type="button"
                                            class="w-full rounded bg-green-50 px-2 py-1 font-semibold text-green-600 shadow-sm hover:bg-green-100 text-lg"
                                            @click="selectFile">
                                        <i class="fa fa-camera"></i> Tải ảnh
                                    </button>
                                    <div v-else class="w-full flex items-center">
                                        @{{ imageName }}
                                        <i class="fa fa-times ml-2 a-cursor-pointer" @click="removeImage"></i>
                                    </div>
                                    <input id="jlptGiftImage" type="file" style="display: none;" @change="uploadFile"
                                           accept="image/*"/>
{{--                                                                        <div class="w-full text-left mt-3 mb-4 font-quicksand font-semibold text-md">3 người bạn mà bạn muốn chia sẻ cùng</div>--}}
{{--                                                                        <div class="w-full point-input mb-4">--}}
{{--                                                                            <div class="point-label">Họ tên người bạn thứ nhất</div>--}}
{{--                                                                            <input v-model="friend1" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="w-full point-input mb-4">--}}
{{--                                                                            <div class="point-label">Họ tên người bạn thứ hai</div>--}}
{{--                                                                            <input v-model="friend2" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />--}}
{{--                                                                        </div>--}}
{{--                                                                        <div class="w-full point-input mb-4">--}}
{{--                                                                            <div class="point-label">Họ tên người bạn thứ ba</div>--}}
{{--                                                                            <input v-model="friend3" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />--}}
{{--                                                                        </div>--}}
                                </div>
                                <div v-if="success && id" class="flex gap-5 flex-col sm:flex-row relative">
                                    <img style="object-fit: contain" :src="url + '/nhan-qua-jlpt/gen-img/' + id" alt=""
                                         class="shadow-lg border border-t border-gray-900 rounded-md max-w-full md:max-w-[400px] order-2 md:order-1">
                                    <div class="max-w-[500px] font-quicksand text-left text-base mt-3 md:mt-0 leading-8 order-1 md:order-3">
                                        Yay! Bạn đã rất gần với phần quà của chúng mình rồi. <span
                                                class="text-[#96D962] font-semibold">Dũng Mori</span> cần một vài thông tin để
                                        thuận tiện cho việc gửi phần thưởng đến tận tay học viên. Bạn vui lòng
                                        làm theo các bước sau đây bạn nhé!
                                        <div class="mt-2">
                                            <b>Bước 1:</b> Đăng ảnh bằng khen lên FB/tiktok cá nhân kèm nội dung tự chọn và hashtag <span class="text-green-500">#mình_chọn_dũng_mori</span>, đăng ở chế độ công khai.
                                        </div>
                                        <div class="mt-2">
                                            <b>Bước 2:</b> Truy cập form sau đây: <a href="https://forms.gle/mVGfNqBPwxP6wLmj7"
                                                                                     target="_blank">https://bit.ly/baodiemJLPT</a>
                                            để điền thông tin nhận thưởng nhé!
                                        </div>
                                        <div class="flex items-center justify-center sm:justify-start mt-3 mb-3">
                                            <button style="background: #57d061"
                                                    class="justify-center mr-1 w-1/2 flex items-center text-center text-base font-semibold py-2.5 leading-0 rounded-md bg-blue-700 text-white"
                                                    @click="downloadImage()">
                                                <i class="fa fa-download mr-2"></i>
                                                Tải ảnh xuống
                                            </button>
                                            <button style="background: #0084ff"
                                                    class="justify-center mr-1 w-1/2 flex items-center text-center text-base font-semibold py-2.5 leading-0 rounded-md bg-blue-700 text-white"
                                                    @click="shareContent(url + '/assets/img/jlpt/img-user/' + user_id + '.png')"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"
                                                     width="16px"
                                                     height="16px">
                                                    <path d="M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z"
                                                          fill="#ffffff"/>
                                                </svg>
                                                Chia sẻ
                                            </button>
                                        </div>
                                        <button @click="openChatbox" type="button" style="background: rgb(87, 208, 97)"
                                                class="w-full py-2 px-4 flex justify-center items-center bg-[#0084ff] focus:ring-blue-500 focus:ring-offset-blue-200 text-white transition ease-in duration-200 text-center text-base font-semibold shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-lg">
                                            <svg width="42" height="38" viewBox="0 0 42 38" fill="none"
                                                 xmlns="http://www.w3.org/2000/svg" class="w-6 lg:w-[18px]">
                                                <path d="M21.0298 0.00783166C9.64407 0.00783166 0.380859 8.38874 0.380859 18.6888C0.380859 28.9889 9.64407 37.3698 21.0298 37.3698C24.9127 37.3698 28.6641 36.4014 31.9383 34.5527L38.9803 37.2466C39.2107 37.3346 39.441 37.3698 39.6549 37.3698C40.1979 37.3698 40.7408 37.1233 41.1357 36.6831C41.6951 36.0493 41.8432 35.1337 41.5306 34.3414L38.9968 27.9149C40.7737 25.0978 41.6951 21.9285 41.6951 18.6712C41.6951 8.37115 32.4319 -0.00976562 21.0462 -0.00976562L21.0298 0.00783166ZM34.7847 28.4959L35.9694 31.5067L32.4154 30.151C31.856 29.9397 31.2308 29.9925 30.7043 30.327C27.8579 32.1053 24.5014 33.0385 21.0133 33.0385C11.8653 33.0385 4.42838 26.6119 4.42838 18.6888C4.42838 10.7657 11.8653 4.33915 21.0133 4.33915C30.1613 4.33915 37.5982 10.7657 37.5982 18.6888C37.5982 21.3827 36.7098 24.0237 35.048 26.3126C34.5873 26.9289 34.4886 27.774 34.7847 28.4959ZM13.7409 17.157C14.1194 17.562 14.3333 18.1254 14.3333 18.6888C14.3333 18.8297 14.3168 18.9705 14.3004 19.1114C14.2675 19.2522 14.2346 19.3931 14.1852 19.5163C14.1358 19.6396 14.07 19.7629 13.9877 19.8861C13.9219 20.0093 13.8232 20.115 13.7409 20.2206C13.6422 20.3263 13.5435 20.4143 13.4283 20.4847C13.3132 20.5728 13.198 20.6256 13.0828 20.6784C12.9676 20.7312 12.836 20.7841 12.7044 20.8017C12.5728 20.8369 12.4411 20.8369 12.3095 20.8369C11.783 20.8369 11.2565 20.608 10.8781 20.203C10.7794 20.0974 10.6971 19.9918 10.6313 19.8685C10.5655 19.7453 10.4996 19.622 10.4503 19.4987C10.4009 19.3755 10.368 19.2346 10.3351 19.0938C10.3187 18.9529 10.3022 18.8121 10.3022 18.6712C10.3022 18.5304 10.3187 18.3895 10.3351 18.2486C10.368 18.1078 10.4009 17.9669 10.4503 17.8437C10.4996 17.7204 10.5655 17.5972 10.6313 17.474C10.7135 17.3507 10.7958 17.2451 10.8781 17.1394C11.3552 16.6288 12.0463 16.3999 12.7044 16.5408C12.836 16.5584 12.9512 16.6112 13.0828 16.664C13.198 16.7168 13.3296 16.7873 13.4283 16.8577C13.5435 16.9457 13.6422 17.0338 13.7409 17.1218V17.157ZM23.0042 18.2663C23.0206 18.4071 23.0371 18.548 23.0371 18.6888C23.0371 18.8297 23.0371 18.9705 23.0042 19.1114C22.9712 19.2522 22.9383 19.3931 22.889 19.5163C22.8396 19.6396 22.7738 19.7629 22.6915 19.8861C22.6257 20.0093 22.527 20.115 22.4447 20.2206C22.0663 20.6256 21.5398 20.8545 21.0133 20.8545C20.8817 20.8545 20.7501 20.8545 20.6184 20.8193C20.4868 20.8017 20.3716 20.7488 20.24 20.696C20.1248 20.6432 20.0097 20.5728 19.8945 20.5024C19.7793 20.4143 19.6806 20.3439 19.5819 20.2382C19.4832 20.1326 19.4009 20.0269 19.3351 19.9037C19.2693 19.7804 19.2034 19.6572 19.1541 19.534C19.1047 19.4107 19.0718 19.2698 19.0389 19.129C19.0225 18.9881 19.006 18.8473 19.006 18.7064C19.006 18.5656 19.0225 18.4247 19.0389 18.2839C19.0718 18.143 19.1047 18.0022 19.1541 17.8789C19.2034 17.7557 19.2693 17.6324 19.3351 17.5092C19.4173 17.3859 19.4996 17.2803 19.5819 17.1746C19.6806 17.069 19.7793 16.9809 19.8945 16.9105C20.0097 16.8225 20.1248 16.7697 20.24 16.7168C20.3716 16.664 20.4868 16.6112 20.6184 16.5936C21.2766 16.4528 21.9841 16.6816 22.4447 17.1922C22.5435 17.2979 22.6257 17.4035 22.6915 17.5268C22.7738 17.65 22.8232 17.7733 22.889 17.8965C22.9383 18.0198 22.9712 18.1606 23.0042 18.3015V18.2663ZM31.7244 18.2663C31.7409 18.4071 31.7573 18.548 31.7573 18.6888C31.7573 18.8297 31.7573 18.9705 31.7244 19.1114C31.6915 19.2522 31.6586 19.3931 31.6092 19.5163C31.5599 19.6396 31.4941 19.7629 31.4118 19.8861C31.346 20.0093 31.2473 20.115 31.165 20.2206C31.0663 20.3263 30.9676 20.4143 30.8524 20.4847C30.7372 20.5728 30.622 20.6256 30.5069 20.6784C30.3917 20.7312 30.2601 20.7841 30.1284 20.8017C29.9968 20.8369 29.8652 20.8369 29.7336 20.8369C29.2071 20.8369 28.6805 20.608 28.3021 20.203C28.2034 20.0974 28.1211 19.9918 28.0553 19.8685C27.9895 19.7453 27.9237 19.622 27.8743 19.4987C27.825 19.3755 27.7921 19.2346 27.7592 19.0938C27.7427 18.9529 27.7263 18.8121 27.7263 18.6712C27.7263 18.5304 27.7427 18.3895 27.7592 18.2486C27.7921 18.1078 27.825 17.9669 27.8743 17.8437C27.9237 17.7204 27.9895 17.5972 28.0553 17.474C28.1376 17.3507 28.2199 17.2451 28.3021 17.1394C28.7793 16.6288 29.4703 16.3999 30.1284 16.5408C30.2601 16.5584 30.3752 16.6112 30.5069 16.664C30.622 16.7168 30.7537 16.7873 30.8524 16.8577C30.9676 16.9457 31.0663 17.0338 31.165 17.1218C31.2637 17.2274 31.346 17.3331 31.4118 17.4563C31.4941 17.5796 31.5434 17.7028 31.6092 17.8261C31.6586 17.9493 31.6915 18.0902 31.7244 18.231V18.2663Z"
                                                      fill="white"></path>
                                            </svg>
                                            <span class="leading-0 ml-1"> Liên hệ nhận quà</span>
                                        </button>

                                    </div>
                                </div>
                                <button v-if="!success" class="btn bg-[#fe9c9c]" style="margin: 15px;" @click="isPassed = null">Trở lại</button>
                                <button v-if="!success" class="btn bg-[#96D962]" style="margin: 15px 80px;" :disabled="disabled"
                                        @click="upload">
                                    <i class="fa fa-spinner fa-pulse" v-if="loading"></i>
                                    Gửi thông tin
                                </button>
                            @endif
                        </template>
                        <template v-else>
                            @if (!auth()->check() && !isset($achievement))
                                <div class="flex justify-center items-center" style="min-height: 500px;">
                                    <button class="btn btn-success" data-fancybox data-animation-duration="300"
                                            data-src="#auth-container" onclick="swichTab('login')">Đăng nhập
                                    </button>
                                </div>
                            @else
                                <div>
                                    <div v-if="!success" class="flex flex-col justify-center items-center mt-5"
                                         style="margin: 7px;">
                                        <div class="point-input mb-4">
                                            <div class="point-label">Họ và tên</div>
                                            <input v-model="name" type="text"
                                                   style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                        </div>
                                        <div class="point-input mb-4">
                                            <div class="point-label">Trình độ</div>
                                            <select name="" id="" v-model="level"
                                                    style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                            >
                                                <option value="N1">N1</option>
                                                <option value="N2">N2</option>
                                                <option value="N3">N3</option>
                                                <option value="N4">N4</option>
                                                <option value="N5">N5</option>
                                            </select>
                                        </div>
                                        <div class="point-input mb-4">
                                            <div class="point-label">Tổng điểm</div>
                                            <input v-model="point" type="text"
                                                   style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                        </div>
                                        <div class="point-input mb-4">
                                            <div class="point-label">Chọn quà</div>
                                            <select name="" id="" v-model="gift_value"
                                                    style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                            >
                                                <option value="0">Giảm 10% khi đăng lại cấp độ đã học</option>
                                                <option value="1">Tặng thêm 1 tháng khóa học online</option>
                                            </select>
                                        </div>
{{--                                                                                            <div class="mb-4">--}}
{{--                                                                                                <div class="text-left mb-3 flex items-center w-[300px]">--}}
{{--                                                                                                    <input type="checkbox" v-model="showCaptionInput" id="post_to_community" class="m-0">--}}
{{--                                                                                                    <label for="post_to_community" class="font-normal m-0 ml-2"> Đăng bài lên cộng dồng Dungmori</label>--}}
{{--                                                                                                </div>--}}
{{--                                                                                                <div v-if="showCaptionInput" class="point-input">--}}
{{--                                                                                                    <div class="point-label">Caption đăng bài lên cộng đồng Dungmori</div>--}}
{{--                                                                                                    <textarea v-model="caption" style="width: 300px;height: 100px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;padding:12px;"></textarea>--}}
{{--                                                                                                </div>--}}
{{--                                                                                            </div>--}}
                                        <button v-if="!imageName" type="button"
                                                class="w-[300px] py-3 rounded bg-green-50 px-2 py-1 text-xs font-semibold text-green-600 shadow-sm hover:bg-green-100 text-lg"
                                                @click="selectFile">
                                            <i class="fa fa-camera"></i> Tải ảnh
                                        </button>
                                        <div v-else class="flex items-center">
                                            @{{ imageName }}
                                            <i class="fa fa-times ml-2 a-cursor-pointer" @click="removeImage"></i>
                                        </div>
                                        <input id="jlptGiftImage" type="file" accept="image/*" style="display: none;"
                                               @change="uploadFile"/>
                                    </div>
{{--                                                                        <div class="grid grid-cols-2 gap-5 mt-5">--}}
{{--                                                                            <div--}}
{{--                                                                                    v-for="gift in failedGift"--}}
{{--                                                                                    class="select-none px-4 py-12 duration-100 hover:shadow-md hover:-translate-y-1 rounded-lg border border-2 border-gray-200 cursor-pointer flex flex-col justify-start items-center"--}}
{{--                                                                                    :class="[--}}
{{--                                                                                                gift.value == choseGift ? 'bg-[#96D962] border-[#96D962]' : '',--}}
{{--                                                                                              ]"--}}
{{--                                                                                    @click="chooseGift(gift.value)"--}}
{{--                                                                            >--}}
{{--                                                                                <img :src="url + '/assets/img/new_home/12-2021/' + gift.img"  class="max-w-6xl max-h-24"/>--}}
{{--                                                                                <span class="mt-5 text-md font-semibold italic" :class="[gift.value == choseGift ? 'text-white' : '',]">@{{ gift.label }}</span>--}}
{{--                                                                            </div>--}}
{{--                                                                        </div>--}}
                                    <button v-if="!success" class="btn bg-[#fe9c9c]" style="margin: 15px;" @click="isPassed = null">Trở lại</button>
                                    <button v-if="!success" class="btn bg-[#96D962]" style="margin: 15px 80px;"
                                            :disabled="disabled" @click="upload">
                                        <i class="fa fa-spinner fa-pulse" v-if="loading"></i>
                                        Gửi thông tin
                                    </button>
                                </div>
                                <div v-if="!is_upload && success">
                                    Bạn đã gửi thông tin trước đó
                                </div>
                            @endif
                        </template>
                    </div>
                </div>
                <div class="stage">
                    <el-dialog title="Chọn lộ trình" :visible.sync="true">
                        <span slot="footer" class="dialog-footer">

                        </span>
                    </el-dialog>
                </div>
                @if (!Auth::check())
                    @include('frontend._layouts.auth')
                    <div class="flex items-center gap-1 bg-[#EC6E23] border-2 border-white rounded-[50px] py-2 px-5 text-white auth-button">
                        <div data-fancybox data-animation-duration="300" data-src="#auth-container"
                             class="cursor-pointer no-underline uppercase font-semibold">
                            <div class="text-white" id="text-login"
                                 onclick="ga('send', 'event', 'Menu', 'click', 'Login'); swichTab('login')">Đăng nhập
                            </div>
                        </div>
                        <span>/</span>
                        <div data-fancybox data-animation-duration="300" data-src="#auth-container"
                             class="!text-white cursor-pointer no-underline uppercase font-semibold">
                            <div class="text-white" id="text-register"
                                 onclick="ga('send', 'event', 'Menu', 'click', 'Register'); swichTab('register')">Đăng
                                ký
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-white hover:text-dmr-green-dark has-badge cursor-pointer icon-color"
                         onclick="ga('send', 'event', 'Menu', 'click', 'Chat'); showChatbox()">
                        <div id="auth-container" v-if="countUnreadMess > 0">
                            <span class="badge--red countMess" style="background: #EF6D13" v-html="countUnreadMess"></span>
                        </div>
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                             class="w-5 stroke-current">
                             <path d="M8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H16C20 2 22 4 22 8V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19Z" stroke="currentColor" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                             <path d="M7 8H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                             <path d="M7 13H13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div id="announce" class="annouce">
                        <user-annouce></user-annouce>
                    </div>
                    <div class="header-avatar">
                        <div class="dropdown-toggle user-info-box" type="button" data-toggle="dropdown">
                            <img class="user-avatar-circle"
                                 @if (Auth::user()->avatar == null)
                                     src="{{url('assets/img/default-avatar.jpg')}}"
                                 @else
                                     src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                                    @endif
                            />
                        </div>
                        <ul class="dropdown-menu user-menu">
                            <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>
                            <li><a href="{{url('/account')}}"
                                   onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Profile')"><i
                                            class="zmdi zmdi-account-box"></i> Thông tin cá nhân</a></li>
                            <li><a href="{{url('/account/courses')}}"
                                   onclick="ga('send', 'event', 'Avatar Menu', 'click', 'My courses from ava list')"><i
                                            class="zmdi zmdi-dns"></i> Khóa học của tôi</a></li>
                            <li><a href="{{url('/account/score')}}"
                                   onclick="ga('send', 'event', 'Avatar Menu', 'click', 'My improvement')"><i
                                            class="fa fa-trophy trophy-icon"></i>Sự tiến bộ của tôi&nbsp;&nbsp;<div
                                            class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span
                                                class="tooltiptext">Theo dõi sự tiến bộ của bạn thông qua 2 bài thi giữa kỳ và cuối kỳ</span>
                                    </div>
                                </a></li>
                            <li><a href="{{url('/account/achievement')}}"
                                   onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Thành tích')"><i
                                            class="fa fa-star"></i>Thành tích&nbsp;&nbsp;<div class="tooltip-i"><i
                                                class="fa fa-info-circle" aria-hidden="true"></i><span
                                                class="tooltiptext">Phần thưởng cho sự chăm chỉ của bạn, duy trì và cải thiện thứ hạng trên bảng xếp hạng nhé!</span>
                                    </div>
                                </a></li>
                            <li><a href="{{url('/account/billing')}}"
                                   onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Payment history')"><i
                                            class="zmdi zmdi-card"></i> Lịch sử thanh toán</a></li>
                            <li><a href="{{url('/ho-tro/chinh-sach-gia-han-khoa-hoc-online')}}"
                                   onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Chính sách gia hạn')"><i
                                            class="fa fa-shield"></i> Chính sách gia hạn</a></li>
                            <li><a href="{{url('/account?focus=changePass')}}"
                                   onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Change passwrod')"><i
                                            class="zmdi zmdi-shield-security"></i> Thay đổi mật khẩu</a></li>
                            <div class="dropdown-divider"></div>
                            <li><a onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Logout'); logout()"><i
                                            class="zmdi zmdi-power"></i> Đăng xuất</a></li>
                        </ul>
                        <div class="btn-profile"  style="display: none">
                            <div class="w-[40px] h-[40px] rounded-full bg-cover bg-center bg-no-repeat" style="background-image: url({{ Auth::user()->avatar ? url('cdn/avatar/small/'. Auth::user()->avatar) : asset('images/icons/default-user.png') }})">
                            </div>
                            <div class="menu hidden fixed right-0 top-[70px]">
                                <div class="bg-white rounded-3xl p-4 mr-4" style="box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;">
                                    <div class="absolute -top-[5px] right-4 w-3 h-3 bg-white rotate-45 right-[30px]" style="box-shadow: -10px -10px 10px rgba(0, 0, 0, 0.1) "></div>
                                    <ul class="space-y-3 font-averta-regular">
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/profile.png') }}" class="flex-none w-[16px]" alt="menu-1">
                                        <a href="/account"><span class="text-md font-medium text-gray-800">Thông tin cá nhân</span></a>
                                      </li>
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/menu.png') }}" class="flex-none w-[16px]" alt="menu-2">
                                        <a href="/account/courses"><span class="text-sm font-medium text-gray-800">Khóa học của tôi</span></a>
                                      </li>
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/status-up.png') }}" class="flex-none w-[16px]" alt="menu-3">
                                        <a href="/account/score"><span class="text-sm font-medium text-gray-800">Sự tiến bộ của tôi</span></a>
                                      </li>
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/cup.png') }}" class="flex-none w-[16px]" alt="menu-4">
                                        <a href="/account/achievement"><span class="text-sm font-medium text-gray-800">Thành tích</span></a>
                                      </li>
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/receipt-item.png') }}" class="flex-none w-[16px]" alt="menu-5">
                                        <a href="/account/billing"><span class="text-sm font-medium text-gray-800">Lịch sử thanh toán</span></a>
                                      </li>
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/shield-tick.png') }}" class="flex-none w-[16px]" alt="menu-6">
                                        <a href="/ho-tro/chinh-sach-gia-han-khoa-hoc-online"><span class="text-sm font-medium text-gray-800">Chính sách gia hạn</span></a>
                                      </li>
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/shield-security.png') }}" class="flex-none w-[16px]" alt="menu-7">
                                        <a href="/account?focus=changePass"><span class="text-sm font-medium text-gray-800">Thay đổi mật khẩu</span></a>
                                      </li>
                                      <li class="flex items-center space-x-3">
                                        <img src="{{ asset('images/icons/menu/logout.png') }}" class="flex-none w-[16px]" alt="menu-8">
                                        <a href="javascript:void(0);" onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Logout'); logout()"><span class="text-sm font-medium text-gray-800">Đăng xuất</span></a>
                                      </li>
                                    </ul>
                                  </div>
                            </div>
                        </div>
                    </div>

                @endif

                <script type="text/javascript">
                    function showChatbox() {
                        if (typeof chatbox !== 'undefined' && chatbox) {
                            chatbox.initChat();
                            $("#chat-box").css('display', 'flex');
                            $("#mess-input").focus();
                            chatbox.goToBottom();
                        }
                    }
                    @if (Auth::check())
                    var chatUser = {{ Auth::user()->id }};
                    var at = "{{ session('_tokenApp') }}";
                    var api = "{{ config('app.api_url') }}";
                    @else
                    var chatUser = localStorage.getItem('incognito_user');
                    if (chatUser) {
                        chatUser = parseInt(chatUser);
                    } else {
                        chatUser = (Math.floor(Math.random() * 1000000) + 1) * -1;
                        localStorage.setItem('incognito_user', chatUser);
                    }
                    @endif
                    if (chatUser) {
                        var socketServer = "{{ config('app.socket_server') }}";

                        var socket = io.connect(socketServer, {
                            query: {
                                type: 'user',
                                from: 'web',
                                userId: chatUser
                            }
                        });

                        // Check receive new message
                        @if (Auth::check())
                        socket.on('send_new_message_enc', async function (message) {
                            if (typeof chatbox !== 'undefined') {
                                message = JSON.parse(dmsg(message));
                                chatbox.receiveNewMsg(message);
                                if (notiCount) {
                                    notiCount.updateMessageCount(message);
                                }
                                if (message && parseInt(message.receiverId) == chatUser) {
                                    if (!countAdminMess) {
                                        countAdminMess = 0;
                                    }
                                    countAdminMess = countAdminMess + 1;

                                    if (message.senderType == 'admin' && $('#chat-box').css('display') === 'none') {
                                        showChatbox();
                                        chatbox.goToBottom();
                                    }
                                }
                            }
                        });
                        @else
                        socket.on('send_new_message', async function (message) {
                            if (typeof chatbox !== 'undefined') {
                                chatbox.receiveNewMsg(message);
                            }
                            if (typeof notiCount !== 'undefined') {
                                notiCount.updateMessageCount(message);
                            }
                            if (message && parseInt(message.receiverId) == chatUser) {
                                if ($('#chat-box').css('display') === 'none') {
                                    showChatbox();
                                    chatbox.goToBottom();
                                }
                            }
                        });
                        @endif

                        socket.on('send_new_message_group_enc', async function (message) {
                            if (typeof chatbox !== 'undefined') {
                                message = JSON.parse(dmsg(message));
                                chatbox.receiveNewMsg(message);
                            }
                            if (typeof notiCount !== 'undefined') {
                                notiCount.updateMessageCount(message);
                            }
                        });
                    }
                </script>
            </div>
        </div>
    </div>
</div>
