<div id="add_people" class="modal fade show-people" role="dialog" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><PERSON><PERSON><PERSON><PERSON> thành viên</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="search_member_modal_body" class="modal-body">
                <input v-model="searchMemberKey" type="text" class="search-con" placeholder="Tìm kiếm">
                <div
                    v-for="(member, index) in searchMembers"
                    :key="member.id"
                    class="flex align-items-center member-item"
                    v-bind:style="{ 'border-top-width': index == 0 ? 0 : 1 + 'px', opacity: people.find((p) => p.id == member.user_id) ? 0.4 : 1 }"
                >
                    <img
                        v-if="member.avatar"
                        class="member-avatar"
                        :src="'{{ url('cdn/avatar/small') }}' + '/' + member.avatar"
                    />
                    <img
                        v-else
                        class="member-avatar"
                        src="{{ url('assets/img/default-avatar.jpg') }}"
                    />
                    <div class="flex-1 member-name"><b>@{{ member.name }}</b></div>
                    @if (Auth::check())
                        <i v-if="{{ Auth::user()->id }} != member.user_id" class="fa fa-commenting-o" aria-hidden="true" v-on:click="goToChatUser(member)"></i>
                    @endif
                    <div style="width: 29px">
                        <i v-if="!people.find((p) => p.id == member.user_id)" class="fa fa-user-plus a-cursor-pointer py-3 pl-3 pr-1" aria-hidden="true" v-on:click="addMember(member)"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>