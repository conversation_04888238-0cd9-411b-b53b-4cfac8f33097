<div id="search_msg" class="modal fade show-people" role="dialog" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">T<PERSON><PERSON> tin nhắn</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="search_msg_modal_body" class="modal-body">
                <input v-model="searchMsgKey" class="search-con" type="text" placeholder="Tìm tin nhắn">
                <p class="search-msg-result-txt">Kết quả tìm kiếm</p>
                <div
                    v-for="(msg, index) in listSearchMsg"
                    :key="'search-msg' + msg.id"
                    class="flex align-items-center member-item a-cursor-pointer"
                    v-bind:style="index == 0 ? 'border: 0' : ''"
                    v-on:click="focusMessage(msg.id)"
                >
                    <img
                        v-if="!msg.member"
                        class="member-avatar"
                        src="{{url('/assets/img/admin-ava.png')}}"
                    />
                    <img
                        v-else-if="msg.member.avatar"
                        class="member-avatar"
                        :src="'{{ url('cdn/avatar/small') }}' + '/' + msg.member.avatar"
                    />
                    <img
                        v-else
                        class="member-avatar"
                        src="{{ url('assets/img/default-avatar.jpg') }}"
                    />
                    <div class="flex-1 member-name">
                        <div v-if="msg.member"><b>@{{ msg.member.name }}</b></div>
                        <div v-else><b>DũngMori</b></div>
                        <div v-html="renderContent(msg)" class="search-msg-content" style="-webkit-box-orient: vertical;"></div>
                    </div>
                    <div class="search-msg-time">@{{ prettyDate(msg.created_at) }}</div>
                </div>
                <div v-if="searchMsgKey && !endOfSearchMsg" class="text-center">
                    <img style="width: 15px;" src="{{url('/assets/img/fbload.gif')}}"/>
                </div>
            </div>
        </div>
    </div>
</div>