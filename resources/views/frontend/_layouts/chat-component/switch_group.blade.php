<div id="switch_group" class="modal fade" role="dialog" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><PERSON><PERSON><PERSON><PERSON> đế<PERSON> nh<PERSON> chat khác</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div id="search_member_modal_body" class="modal-body">
                <select v-model="switchConvId">
                    <option v-for="(cg, index) in conversationGroups" :value="cg.id">@{{ cg.title }} (@{{ cg.count }} người)</option>
                </select><br><br>
                <button class="btn btn-primary" v-on:click="switchConvGroup">Save</button>
            </div>
        </div>
    </div>
</div>