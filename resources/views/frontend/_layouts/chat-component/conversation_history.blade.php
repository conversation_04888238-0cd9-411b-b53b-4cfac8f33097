<div id="show_history" class="modal fade show-people" role="dialog" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><PERSON><PERSON>ch sử chuyển nhóm</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div v-if="histories.length == 0" class="text-center">Trống</div>
                <div
                    v-else
                    v-for="(history, index) in histories"
                    :key="history.id"
                    class="flex align-items-center member-item"
                    v-bind:style="index == 0 ? 'border: 0' : ''"
                >
                    <img
                        v-if="history.user.avatar"
                        class="member-avatar"
                        :src="'{{ url('cdn/avatar/small') }}' + '/' + history.user.avatar"
                    />
                    <img
                        v-else
                        class="member-avatar"
                        src="{{ url('assets/img/default-avatar.jpg') }}"
                    />
                    <div class="flex-1 member-name">
                        <b>@{{ history.user.name }}</b><br>
                        <span>@{{ history.user.email }}</span>
                    </div>
                    <div v-if="history.conversation_id == curConv.id" class="text-center">
                        <div>Thêm vào</div>
                        <div>@{{ history.created_at }}</div>
                    </div>
                    <div v-else class="text-center">
                        <div>Xóa đi</div>
                        <div>@{{ history.created_at }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>