<div id="admin_select" class="modal fade show-people" role="dialog" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"><PERSON><PERSON><PERSON></h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div id="search_member_modal_body" class="modal-body">
        <input v-model="adminSearchKey" type="text" class="search-con" placeholder="Tìm kiếm">
        <div
          v-for="(admin, index) in adminFilter"
          :key="admin.id"
          class="flex align-items-center member-item"
          v-bind:style="{ 'border-top-width': index == 0 ? 0 : 1 + 'px' }"
        >
          <img
            v-if="admin.avatar"
            class="member-avatar"
            :src="'{{ url('cdn/avatar/small') }}' + '/' + admin.avatar"
          />
          <img
            v-else
            class="member-avatar"
            src="{{ url('assets/img/default-avatar.jpg') }}"
          />
          <div class="flex-1 member-name"><b>@{{ admin.name }}</b></div>
          <button class="btn btn-success" @click="login(admin.id)">Login</button>
        </div>
      </div>
    </div>
  </div>
</div>