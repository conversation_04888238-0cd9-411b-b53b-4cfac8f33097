<div id="show_people" class="modal fade show-people" role="dialog" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Th<PERSON><PERSON> viên</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div><b>Quản trị</b></div>
                <div
                    v-for="(member, index) in testers"
                    :key="member.id"
                    class="flex items-center member-item"
                    v-bind:style="index == 0 ? 'border: 0' : ''"
                >
                    <img
                        v-if="member.avatar"
                        class="member-avatar"
                        :src="'{{ url('cdn/avatar/small') }}' + '/' + member.avatar"
                    />
                    <img
                        v-else
                        class="member-avatar"
                        src="{{ url('assets/img/default-avatar.jpg') }}"
                    />
                    <div class="flex-1 member-name"><b>@{{ member.name }}</b></div>
                    @if (Auth::check())
                        <i v-if="{{ Auth::user()->id }} != member.id" class="fa fa-commenting-o" aria-hidden="true" v-on:click="goToChatUser(member)"></i>
                        @if (Auth::user()->is_tester)
                            <i class="fa fa-times a-cursor-pointer py-3 pl-3 pr-1" aria-hidden="true" v-on:click="removeMember(member)"></i>
                        @endif
                    @endif
                </div>
                <div class="mt-3"><b>Thành viên</b></div>
                <div
                    v-for="(member, index) in members"
                    :key="member.id"
                    class="flex items-center member-item"
                    v-bind:style="index == 0 ? 'border: 0' : ''"
                >
                    <img
                        v-if="member.avatar"
                        class="member-avatar"
                        :src="'{{ url('cdn/avatar/small') }}' + '/' + member.avatar"
                    />
                    <img
                        v-else
                        class="member-avatar"
                        src="{{ url('assets/img/default-avatar.jpg') }}"
                    />
                    <div class="flex-1 member-name">
                        <b>@{{ member.name }}</b><br>
                        <span>@{{ member.email }}</span>
                    </div>
                    @if (Auth::check() && Auth::user()->is_tester)
                        <i class="fa fa-share a-cursor-pointer" aria-hidden="true" v-on:click="openSwitchGroupModal(member)"></i>
                        <i class="fa fa-commenting-o pl-3 a-cursor-pointer" aria-hidden="true" v-on:click="goToChatUser(member)"></i>
                        <i class="fa fa-times a-cursor-pointer py-3 pl-3 pr-1" aria-hidden="true" v-on:click="removeMember(member)"></i>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>