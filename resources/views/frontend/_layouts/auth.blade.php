{{-- login / register --}}
<div class="login-container" id="auth-container" style="display: none;">
  <div class="flex flex-row p-4 font-averta">
    <div class="flex-1 flex justify-center items-center xl:pt-0 pt-5">
      <div id="login-content" class="auth-content xl:w-[310px] w-full xl:px-0 px-3">
        <div class="font-zuume-semibold text-[#073A3B] text-4xl leading-9">HỌC BÀI MỚI CÙNG<br>DUNGMORI NÀO!</div>
        <div class="text-black mt-2">Tiếng Nhật khó, đã có Dungmori!</div>
        <div class="text-black mt-0.5">Hệ sinh thái Nhật ngữ số 1 dành cho người Việt!</div>
        <form id="login-form" accept-charset="UTF-8" class="form-horizontal">
          {{ csrf_field() }}
          <div class="">
            <label class="control-label"></label>
            <div class="">
              <div class="error-container">
                <div class="alert-danger rounded py-0.5 px-2" style="display: none;" v-show="error != null"><i class="zmdi zmdi-alert-octagon"></i> @{{ error }}</div>
              </div>
            </div>
          </div>
          <div class="auth-item">
            <label class="control-label text-[#073A3B] m-0">Email</label>
            <div class="mt-0.5">
              <input id="email" class="form-control rounded-lg" type="email" name="email" v-on:click="hideError" placeholder="Email hoặc tên đăng nhập" autocomplete="off" required autofocus onkeypress="return enterToLogin(event, this)">
            </div>
          </div>
          <div class="auth-item xl:mt-1 mt-2.5">
            <label class="control-label text-[#073A3B] m-0">Mật khẩu</label>
            <div class="mt-0.5">
              <input id="password" type="password" class="form-control rounded-lg" name="password" v-on:click="hideError" placeholder="Mật khẩu" autocomplete="off" required onkeypress="return enterToLogin(event, this)">
            </div>
          </div>
          <div class="hidden">
            <div class="h-7.5">
              <div class="checkbox">
                <label for="login-remember" class="agree-policy block">
                  <input id="login-remember" type="checkbox" name="remember" checked>
                  <span class="save-login-info text-[#2D3748] flex items-center">Ghi nhớ đăng nhập</span>
                </label>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <button type="button" class="btn-login w-full bg-[#57D061] py-2 text-white rounded-full" id="login-submit" v-on:click="login">
              <span v-show="buttonState == true">Đăng nhập</span>
              <span v-show="buttonState == false"><i class="fa fa-spinner fa-pulse fa-fw"></i> Đang xác thực...</span>
            </button>
            <p class="mt-3 text-center"><a href="{{ url('/password/reset') }}" class="text-[#1E4AE9] cursor-pointer">Quên mật khẩu?</a></p>
          </div>
        </form>
        <div class="flex flex-row items-center mt-6">
          <div class="h-[1px] flex-1 bg-[#CFDFE2]"></div>
          <div class="text-[#294957] mx-2.5">Hoặc đăng nhập với</div>
          <div class="h-[1px] flex-1 bg-[#CFDFE2]"></div>
        </div>
        <div class="mt-6 flex flex-row">
          <a href="{{ url('oauth/facebook') }}" onclick="authBySocicalAccount()" title="Facebook" class="flex-1 text-black">
            <div class="flex flex-row bg-[#F3F9FA] items-center justify-center py-1.5 rounded-lg cursor-pointer mr-2">
              <img class="w-7 h-7 mr-1.5" src="{{ url('assets/img/new_home/06-2024/facebook.png') }}" />
              Facebook
            </div>
          </a>
          <a href="{{ url('oauth/google') }}"  onclick="authBySocicalAccount()" title="Google" class="flex-1 text-black">
            <div class="flex flex-row bg-[#F3F9FA] items-center justify-center py-1.5 rounded-lg cursor-pointer ml-2">
              <img class="w-7 h-7 mr-1.5" src="{{ url('assets/img/new_home/06-2024/google.png') }}" />
              Google
            </div>
          </a>
        </div>
        <div class="text-[#122B31] mt-6 text-center">
          Bạn chưa có tài khoản? <span class="text-[#1E4AE9] cursor-pointer" onclick="swichTab('register')">Đăng ký ngay</span>
        </div>
      </div>

      <div id="register-content" class="auth-content xl:w-[310px] hidden w-full xl:px-0 px-3">
        <div class="font-zuume-semibold text-[#073A3B] text-4xl leading-9">TẠO TÀI KHOẢN DUNGMORI</div>
        <div class="text-black mt-2">Gia nhập hệ sinh thái Dungmori để cùng cập nhật những bài học mới ngay nhé!</div>
        <form id="register-form" accept-charset="UTF-8" class="form-horizontal">
          {{ csrf_field() }}
          <div class="">
            <label class="control-label"></label>
            <div class="">
              <div class="error-container">
                <div class="alert-danger rounded py-0.5 px-2" style="display: none;" v-show="error != null"><i class="zmdi zmdi-alert-octagon"></i> @{{ error }}</div>
              </div>
            </div>
          </div>
          <div class="auth-item">
            <label class="control-label text-[#073A3B] m-0">Họ và tên</label>
            <div class="mt-0.5">
              <input type="text" class="form-control rounded-lg" name="name" id="register-name" v-on:click="hideError" placeholder="Họ và tên" autocomplete="off" required>
            </div>
          </div>
          <div class="auth-item xl:mt-0 mt-2.5">
            <label class="control-label text-[#073A3B] m-0">Email</label>
            <div class="mt-0.5">
              <input type="email" class="form-control" name="email" id="register-email" v-on:click="hideError" placeholder="Email" autocomplete="off" required>
            </div>
          </div>
          <div class="auth-item xl:mt-0 mt-2.5">
            <label class="control-label text-[#073A3B] m-0">Mật khẩu</label>
            <div class="mt-0.5">
              <input type="password" class="form-control" name="password" id="register-password" v-on:click="hideError" placeholder="Mật khẩu" autocomplete="off" required>
            </div>
          </div>
          <div class="auth-item xl:mt-0 mt-2.5">
            <label class="control-label text-[#073A3B] m-0">Nhập lại mật khẩu</label>
            <div class="mt-0.5">
              <input type="password" class="form-control" name="password_confirm" id="register-password-confirm" v-on:click="hideError" placeholder="Nhập lại mật khẩu" autocomplete="off" required>
            </div>
          </div>
          <div class="hidden">
            <div class="h-7.5">
              <div class="checkbox">
                <label for="register-remember" class="agree-policy block">
                  <input id="register-remember" type="checkbox" name="remember" checked>
                  <span class="save-login-info text-[#2D3748] flex items-center">Ghi nhớ đăng nhập</span>
                </label>
              </div>
            </div>
          </div>
          <div class="mt-2">
            <div class="text-[#2D3748]">
               <div class="checkbox" style="margin-top: 0; padding-top: 0;">
                 <label for="agree-checkbox" class="agree-policy text-xs">
                    <input id="agree-checkbox" type="checkbox" name="agreement" checked>
                    <span class="agree-policy-info">Tôi đồng ý với mọi <a href="{{url('trang/dieu-khoan-su-dung')}}" class="text-[#1E4AE9]" target="_blank">điều khoản</a> và <a href="{{url('trang/chinh-sach-bao-mat')}}" class="text-[#1E4AE9]" target="_blank">chính sách</a></span>
                 </label>
             </div>
            </div>
         </div>
          <div class="mt-4">
            <button type="button" class="btn-register w-full bg-[#57D061] py-2 text-white rounded-full" id="register-submit" v-on:click="register" style="border: none;">
              <span v-show="buttonState == true">Đăng ký</span>
              <span v-show="buttonState == false"><i class="fa fa-spinner fa-pulse fa-fw"></i> Đang xác thực...</span>
            </button>
          </div>
        </form>
        <div class="flex flex-row items-center mt-6">
          <div class="h-[1px] flex-1 bg-[#CFDFE2]"></div>
          <div class="text-[#294957] mx-2.5">Hoặc đăng nhập với</div>
          <div class="h-[1px] flex-1 bg-[#CFDFE2]"></div>
        </div>
        <div class="mt-6 flex flex-row text-black">
          <a href="{{ url('oauth/facebook') }}" onclick="authBySocicalAccount()" title="Facebook" class="flex-1 text-black">
            <div class="flex flex-row bg-[#F3F9FA] items-center justify-center py-1.5 rounded-lg cursor-pointer mr-2">
              <img class="w-7 h-7 mr-1.5" src="{{ url('assets/img/new_home/06-2024/facebook.png') }}" />
              Facebook
            </div>
          </a>
          <a href="{{ url('oauth/google') }}"  onclick="authBySocicalAccount()" title="Google" class="flex-1 text-black">
            <div class="flex flex-row bg-[#F3F9FA] items-center justify-center py-1.5 rounded-lg cursor-pointer ml-2">
              <img class="w-7 h-7 mr-1.5" src="{{ url('assets/img/new_home/06-2024/google.png') }}" />
              Google
            </div>
          </a>
        </div>
        <div class="text-[#122B31] mt-6 text-center">
          Bạn đã có tài khoản? <span class="text-[#1E4AE9] cursor-pointer" onclick="swichTab('login')">Đăng nhập ngay</span>
        </div>
      </div>
    </div>
    <img class="flex-1 auth-image xl:block hidden" src="{{ url('assets/img/new_home/06-2024/auth_co_thanh.png') }}" />
  </div>
</div>
