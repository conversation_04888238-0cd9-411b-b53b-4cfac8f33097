<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>@yield('title')</title>
    <meta http-equiv="content-language" content="vi"/>
    <meta name="viewport"
          content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=5.0, user-scalable=1"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="format-detection" content="telephone=no">
    <meta name="google-site-verification" content="qXXUA_R1uoDBf-1WE-ghxdEO6pirYuP-yDVB5iqytRg"/>
    <meta name="facebook-domain-verification" content="4w08w6rk0e6sojaci5ix41vcdlh97t"/>

    <meta name="author" content="@yield('author')"/>
    <meta name="description" content="@yield('description')"/>
    <meta name="robots" content="index,follow"/>

    <link rel="icon" href="{{asset('assets/img/new_home/06-2024/dungmori-fav.png')}}"/>
    <link rel="alternate" href="{{url('')}}" hreflang="vi"/>

    <meta property="og:title" content="@yield('title')"/>
    <meta property="og:description" content="@yield('description')"/>
    <meta property="og:image" content="@yield('image')"/>
    <meta property="og:type" content="website"/>
    <meta property="og:site_name" content="DUNGMORI"/>
    <meta property="og:url" content="@yield('url')"/>
    <meta property="og:image:type" content=".png"/>
    <meta property="og:image:width" content="1200"/>
    <meta property="og:image:height" content="628"/>

    <meta property="fb:app_id" content="1768213996826394"/>
    <meta property="fb:admins" content="100004908811327"/>
    <meta property="fb:admins" content="1725907325"/>

    <!-- preconnect stuffs -->
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link rel="preconnect" href="https://www.youtube.com" crossorigin>
    <link rel="preconnect" href="https://i.ytimg.com" crossorigin>
    <link rel="preconnect" href="https://i9.ytimg.com" crossorigin>
    <link rel="preconnect" href="https://s.ytimg.com" crossorigin>

    <link rel="preload" href="https://fonts.googleapis.com/css?family=Noto+Sans+JP:700&display=swap" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Sans+JP:700&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap"
          as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet"
              href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap"
          as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet"
              href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700;800&display=swap"
          as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet"
              href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700;800&display=swap"
              media="screen">
    </noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap"
              media="screen">
    </noscript>

    <link rel="stylesheet"
          href="{{asset('plugin/driver/driver.min.css')}}?{{filemtime('plugin/driver/driver.min.css')}}"/>
    <link rel="stylesheet" href="{{asset('plugin/toastr/toastr.min.css')}}"/>

    <link rel="preload" href="{{asset('css/base.css')}}?{{filemtime('css/base.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="{{asset('css/base.css')}}?{{filemtime('css/base.css')}}" media="screen">
    </noscript>
    <link rel="preload" href="{{asset('css/plugins.css')}}?{{filemtime('css/plugins.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <noscript>
        <link rel="stylesheet" href="{{asset('css/plugins.css')}}?{{filemtime('css/plugins.css')}}" media="screen">
    </noscript>
    <link rel="stylesheet" href="{{asset('assets/css/styles.css')}}?{{filemtime('assets/css/styles.css')}}"
          media="screen">
    <link rel="stylesheet" href="{{ asset('css/form-survey-new-course.css') }}">

    <script src="{{asset('assets/js/headlibs.js')}}?{{filemtime('assets/js/headlibs.js')}}"></script>
    <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
    <script type="text/javascript">
        var userCurrent = @json(\Illuminate\Support\Facades\Auth::user());
        @if(isset($lastWish))
        var latestWish = @json($lastWish);
        @else
        var latestWish = true;
        @endif
    </script>
    @yield('header-css')
    @yield('header-js')

    <?php

    //lưu admin ss vào cookie để giảm đi 1 truy vấn thường suyên
    $adminSession = false;
    if (isset($_COOKIE['admin_ss']) && $_COOKIE['admin_ss'] == 'be972bedb15a') {
        $adminSession = true;
    } else {
        if (Auth::guard('admin')->user()) {
            $adminSession = true;
            setcookie('admin_ss', 'be972bedb15a', time() + (86400 * 30), "/");
        }
    }
    //echo $adminSession;
//    ?>

</head>
<body>
<div id="application" class="w-screen pb-20 lg:pb-0">

    {{-- {{Auth::user()}} --}}
    {{-- nav header trên pc --}}
    @if(!isset($cdbh) || !$cdbh)
        @include('frontend._layouts.menu')
    @endif
    {{-- cho phép admin xóa cache redis --}}
    @if($adminSession == true)
        <div class="refresh-cache" onclick="refreshCache()">
            <i class="fa fa-refresh" id="refresh-cache"></i>
            <i class="fa fa-check-circle" id="refresh-done" style="display: none; color: green;"></i> cache
        </div>
        <div class="refresh-cache" onclick="refreshMjtCache()" style="top: 230px;">
            <i class="fa fa-refresh" id="refresh-mjt-cache"></i>
            <i class="fa fa-check-circle" id="refresh-mjt-done" style="display: none; color: green;"></i> mjt
        </div>
    @endif

    {{-- end of pc --}}

    {{-- mobile --}}
    <div class="mobile-header !bg-dmr-green lg:hidden">
        <div class="flex items-center justify-between px-4 w-full h-full">
            <a href="{{url('/')}}" class="h-full flex items-center" id="logo">
                <img class="hidden xs:block lg:hidden lazyload object-cover"
                     data-src="{{url('assets/img/new_home/06-2024/dungmori-logo.svg')}}"
                     alt="Dũng Mori - Website tiếng Nhật hàng đầu Việt Nam"/>
                <a href="{{route('home')}}" class="logo3" style="display: none"
                   data-category="{{ isset($currentLesson) && $currentLesson ? $currentLesson['category_id'] : 0 }}">
                    <img class="object-cover" src="{{asset('images/dungmori-logo3.png')}}" alt="dungmori-logo3.png"/>
                </a>
                <div class="flex items-center justify-end">
                    <div class="account-container w-full" id="account-container">
                        <a href="{{url('/khoa-hoc')}}"
                           class="cursor-pointer text-white stroke-current fill-current hover:text-dmr-green-dark icon-color btn-store"
                           style="display: none"
                           onclick="ga('send', 'event', 'Menu', 'click', 'Store')">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg"
                                 class="w-4">
                                <path d="M1.02191 1.28948H22.6139V20.2245C22.6139 21.6919 21.4243 22.8815 19.957 22.8815H3.67886C2.21146 22.8815 1.02191 21.6919 1.02191 20.2245V1.28948Z"
                                      stroke-width="2.5"/>
                                <path d="M5.41602 7.4082C7.5498 11.2654 13.0484 16.6656 17.9725 7.4082"
                                      stroke-width="2.5"/>
                            </svg>
                        </a>
                        @if(!Auth::check())
                            <div class="flex items-center justify-end flex-grow-1" id="auth-buttons">
                                <a data-fancybox data-animation-duration="300" onclick="swichTab('login')"
                                   data-src="#auth-container"
                                   class="login-button flex-1 max-w-1/2 w-[110px] mr-1.5 inline-flex justify-center text-register rounded-[40px] py-2 border-2 border-white text-white font-beanbag text-sm bg-[#EC6E23] uppercase leading-none text-center font-beanbag">
                                    Đăng nhập
                                </a>
                                <a data-fancybox data-animation-duration="300" onclick="swichTab('register')"
                                   data-src="#auth-container"
                                   class="register-button flex-1 max-w-1/2 w-[110px] inline-flex justify-center text-register rounded-[40px] py-2 border-2 border-white text-white font-beanbag text-sm bg-dmr-green uppercase leading-none text-center font-beanbag">
                                    Đăng ký
                                </a>
                            </div>
                        @else
                            <div class="dropdown auth-container" id="auth-container">
                                <script type="text/javascript">
                                    function showChatbox() {
                                        if (typeof chatbox !== 'undefined' && chatbox) {
                                            chatbox.initChat();
                                            $("#chat-box").css('display', 'flex');
                                            $("#mess-input").focus();
                                            chatbox.goToBottom();
                                        }
                                    }
                                </script>
                                <span class="messenger-icon" onclick="showChatbox()">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none"
                                     xmlns="http://www.w3.org/2000/svg"
                                     class="w-5 stroke-current">
                                <path d="M8.5 19H8C4 19 2 18 2 13V8C2 4 4 2 8 2H16C20 2 22 4 22 8V13C22 17 20 19 16 19H15.5C15.19 19 14.89 19.15 14.7 19.4L13.2 21.4C12.54 22.28 11.46 22.28 10.8 21.4L9.3 19.4C9.14 19.18 8.77 19 8.5 19Z"
                                      stroke="currentColor" stroke-width="2" stroke-miterlimit="10"
                                      stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M7 8H17" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round"/>
                                <path d="M7 13H13" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round"/>
                           </svg>
                            <span class="mess-counts" v-show="countUnreadMess != 0" style="display: none;">@{{ countUnreadMess }}</span>
                        </span>

                                <a href="{{url('/account/notifications')}}">
                            <span class="svgIcon svgIcon--bell svgIcon--25px">
                                <span class="noti-counts" v-show="countNotification != 0" style="display: none;">@{{ countNotification }}</span>
                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.33811 12.65C3.44133 12.4959 3.54684 12.3234 3.65174 12.1311C4.26774 11.0017 4.85 9.2134 4.85 6.5C4.85 5.46566 5.26089 4.47368 5.99228 3.74228C6.72368 3.01089 7.71566 2.6 8.75 2.6C9.78435 2.6 10.7763 3.01089 11.5077 3.74228C12.2391 4.47368 12.65 5.46566 12.65 6.5C12.65 9.2134 13.2323 11.0017 13.8483 12.1311C13.9532 12.3234 14.0587 12.4959 14.1619 12.65H3.33811Z"
                                          stroke="white" stroke-width="1.2" stroke-linecap="round"
                                          stroke-linejoin="round"/>
                                    <path d="M10.2981 15.75C10.1663 15.9773 9.97701 16.166 9.74929 16.2971C9.52158 16.4283 9.26341 16.4973 9.00062 16.4973C8.73784 16.4973 8.47967 16.4283 8.25196 16.2971C8.02424 16.166 7.83498 15.9773 7.70312 15.75"
                                          stroke="white" stroke-width="1.2" stroke-linecap="round"
                                          stroke-linejoin="round"/>
                                </svg>
                            </span>
                                </a>
                                <div class="btn-profile" style="display: none">
                                    <div class="w-[40px] h-[40px] rounded-full bg-cover bg-center bg-no-repeat"
                                         style="background-image: url({{ Auth::user()->avatar ? url('cdn/avatar/small/'. Auth::user()->avatar) : asset('images/icons/default-user.png') }})">
                                    </div>
                                    <div class="menu hidden fixed right-0 top-[70px]">
                                        <div class="bg-white rounded-3xl p-4 mr-4"
                                             style="box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;">
                                            <div class="absolute -top-[5px] right-4 w-3 h-3 bg-white rotate-45 right-[30px]"
                                                 style="box-shadow: -10px -10px 10px rgba(0, 0, 0, 0.1) "></div>
                                            <ul class="space-y-3 font-averta-regular">
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/profile.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-1">
                                                    <a href="/account"><span class="text-md font-medium text-gray-800">Thông tin cá nhân</span></a>
                                                </li>
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/menu.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-2">
                                                    <a href="/account/courses"><span
                                                                class="text-sm font-medium text-gray-800">Khóa học của tôi</span></a>
                                                </li>
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/status-up.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-3">
                                                    <a href="/account/score"><span
                                                                class="text-sm font-medium text-gray-800">Sự tiến bộ của tôi</span></a>
                                                </li>
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/cup.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-4">
                                                    <a href="/account/achievement"><span
                                                                class="text-sm font-medium text-gray-800">Thành tích</span></a>
                                                </li>
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/receipt-item.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-5">
                                                    <a href="/account/billing"><span
                                                                class="text-sm font-medium text-gray-800">Lịch sử thanh toán</span></a>
                                                </li>
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/shield-tick.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-6">
                                                    <a href="/ho-tro/chinh-sach-gia-han-khoa-hoc-online"><span
                                                                class="text-sm font-medium text-gray-800">Chính sách gia hạn</span></a>
                                                </li>
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/shield-security.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-7">
                                                    <a href="/account?focus=changePass"><span
                                                                class="text-sm font-medium text-gray-800">Thay đổi mật khẩu</span></a>
                                                </li>
                                                <li class="flex items-center space-x-3">
                                                    <img src="{{ asset('images/icons/menu/logout.png') }}"
                                                         class="flex-none w-[16px]" alt="menu-8">
                                                    <a href="javascript:void(0);"
                                                       onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Logout'); logout()"><span
                                                                class="text-sm font-medium text-gray-800">Đăng xuất</span></a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        @endif
                    </div>
                    <div id="nav-icon">
                        <span class="nav-span"></span>
                        <span class="nav-span"></span>
                        <span class="nav-span"></span>
                        <ul class="dropdown-menu overflow-y-scroll z-[999] !bg-dmr-green-dark text-white">
                            @if(Auth::user())
                                <div class="col">
                                    <div class="tabs">
                                        <div class="tab">
                                            <input type="checkbox" id="chck0">
                                            <label class="tab-label label-account" for="chck0">
                                                <div class="user-info-box">
                                                    <img class="user-avatar-circle"
                                                         @if(Auth::user()->avatar == null)
                                                             src="{{url('assets/img/default-avatar.jpg')}}"
                                                         @else
                                                             src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                                                            @endif
                                                    />
                                                    <div style="width: 70%;">
                                                        <p class="user-name">{{ Auth::user()->name }}</p>
                                                        <i class="zmdi zmdi-settings"></i><span class="user-name ml-2">Xem trang cá nhân</span>
                                                    </div>
                                                </div>
                                            </label>
                                            <div class="tab-content account-expand">
                                                <li>
                                                    <a href="{{url('/account/notifications')}}">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M9 21H15" stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M5.27051 9.74994C5.26926 8.86045 5.44409 7.97951 5.7849 7.1579C6.12571 6.33628 6.62576 5.59025 7.25624 4.9628C7.88672 4.33535 8.63515 3.83889 9.45839 3.50204C10.2816 3.16519 11.1634 2.99461 12.0529 3.00013C15.7644 3.02772 18.7332 6.11276 18.7332 9.83468V10.4999C18.7332 13.8577 19.4357 15.8061 20.0544 16.871C20.1211 16.9848 20.1565 17.1142 20.1572 17.246C20.1579 17.3779 20.1238 17.5076 20.0584 17.6221C19.993 17.7366 19.8985 17.8318 19.7845 17.8982C19.6706 17.9645 19.5411 17.9996 19.4092 17.9999H4.59369C4.4618 17.9996 4.33234 17.9645 4.21835 17.8981C4.10437 17.8318 4.0099 17.7366 3.94448 17.622C3.87905 17.5075 3.84499 17.3778 3.84571 17.2459C3.84644 17.114 3.88194 16.9846 3.94863 16.8709C4.56768 15.8059 5.2705 13.8575 5.2705 10.4999L5.27051 9.74994Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                        Xem thông báo
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{{url('/account')}}">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11.998 15.75C13.6549 15.75 14.998 14.4069 14.998 12.75C14.998 11.0931 13.6549 9.75 11.998 9.75C10.3412 9.75 8.99805 11.0931 8.99805 12.75C8.99805 14.4069 10.3412 15.75 11.998 15.75Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M7.49805 17.9998C8.02208 17.3013 8.70153 16.7343 9.4826 16.3438C10.2637 15.9533 11.1249 15.75 11.9982 15.75C12.8714 15.75 13.7327 15.9532 14.5138 16.3437C15.2949 16.7342 15.9744 17.3011 16.4984 17.9996"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M19.498 20.25V3.75C19.498 3.33579 19.1623 3 18.748 3L5.24805 3C4.83383 3 4.49805 3.33579 4.49805 3.75L4.49805 20.25C4.49805 20.6642 4.83383 21 5.24805 21H18.748C19.1623 21 19.498 20.6642 19.498 20.25Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M8.99805 6H14.998" stroke="white"
                                                                  stroke-width="1.52202" stroke-linecap="round"
                                                                  stroke-linejoin="round"/>
                                                        </svg>
                                                        Thông tin cá nhân
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="{{url('/account/courses')}}">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M7.125 10.5C8.98896 10.5 10.5 8.98896 10.5 7.125C10.5 5.26104 8.98896 3.75 7.125 3.75C5.26104 3.75 3.75 5.26104 3.75 7.125C3.75 8.98896 5.26104 10.5 7.125 10.5Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M16.875 10.5C18.739 10.5 20.25 8.98896 20.25 7.125C20.25 5.26104 18.739 3.75 16.875 3.75C15.011 3.75 13.5 5.26104 13.5 7.125C13.5 8.98896 15.011 10.5 16.875 10.5Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M16.875 20.249C18.739 20.249 20.25 18.738 20.25 16.874C20.25 15.0101 18.739 13.499 16.875 13.499C15.011 13.499 13.5 15.0101 13.5 16.874C13.5 18.738 15.011 20.249 16.875 20.249Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M7.125 20.249C8.98896 20.249 10.5 18.738 10.5 16.874C10.5 15.0101 8.98896 13.499 7.125 13.499C5.26104 13.499 3.75 15.0101 3.75 16.874C3.75 18.738 5.26104 20.249 7.125 20.249Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                        Khóa học của tôi</a></li>
                                                <li>
                                                    <a href="{{url('/account/courses')}}">
                                                        <i class="fa fa-star-o text-black !text-[21px] w-6 !mr-[15px] text-center"></i>
                                                        Thành tích</a></li>
                                                <li>
                                                    <a href="{{url('/account/courses')}}">
                                                        <i class="fa fa-trophy text-black !text-[21px] w-6 !mr-[15px] text-center"
                                                           aria-hidden="true"></i>
                                                        Sự tiến bộ của tôi</a></li>
                                                <li><a href="{{url('/account/billing')}}">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M21.752 6H2.25195C1.83774 6 1.50195 6.33579 1.50195 6.75V17.25C1.50195 17.6642 1.83774 18 2.25195 18H21.752C22.1662 18 22.502 17.6642 22.502 17.25V6.75C22.502 6.33579 22.1662 6 21.752 6Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M12.002 15C13.6588 15 15.002 13.6569 15.002 12C15.002 10.3431 13.6588 9 12.002 9C10.3451 9 9.00195 10.3431 9.00195 12C9.00195 13.6569 10.3451 15 12.002 15Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M16.502 6L22.502 11.25" stroke="white"
                                                                  stroke-width="1.52202" stroke-linecap="round"
                                                                  stroke-linejoin="round"/>
                                                            <path d="M16.502 18L22.502 12.75" stroke="white"
                                                                  stroke-width="1.52202" stroke-linecap="round"
                                                                  stroke-linejoin="round"/>
                                                            <path d="M7.50195 6L1.50195 11.25" stroke="white"
                                                                  stroke-width="1.52202" stroke-linecap="round"
                                                                  stroke-linejoin="round"/>
                                                            <path d="M7.50195 18L1.50195 12.75" stroke="white"
                                                                  stroke-width="1.52202" stroke-linecap="round"
                                                                  stroke-linejoin="round"/>
                                                        </svg>
                                                        Lịch sử thanh toán</a></li>
                                                <li><a href="{{url('/ho-tro/chinh-sach-gia-han-khoa-hoc-online')}}">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M7.125 10.5C8.98896 10.5 10.5 8.98896 10.5 7.125C10.5 5.26104 8.98896 3.75 7.125 3.75C5.26104 3.75 3.75 5.26104 3.75 7.125C3.75 8.98896 5.26104 10.5 7.125 10.5Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M16.875 10.5C18.739 10.5 20.25 8.98896 20.25 7.125C20.25 5.26104 18.739 3.75 16.875 3.75C15.011 3.75 13.5 5.26104 13.5 7.125C13.5 8.98896 15.011 10.5 16.875 10.5Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M7.125 20.25C8.98896 20.25 10.5 18.739 10.5 16.875C10.5 15.011 8.98896 13.5 7.125 13.5C5.26104 13.5 3.75 15.011 3.75 16.875C3.75 18.739 5.26104 20.25 7.125 20.25Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M16.875 14.251V19.501" stroke="white"
                                                                  stroke-width="1.52202" stroke-linecap="round"
                                                                  stroke-linejoin="round"/>
                                                            <path d="M19.5 16.875H14.25" stroke="white"
                                                                  stroke-width="1.52202" stroke-linecap="round"
                                                                  stroke-linejoin="round"/>
                                                        </svg>
                                                        Chính sách gia hạn</a></li>
                                                <li><a href="{{url('/account?focus=changePass')}}">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M3.75 10.75V5.25C3.75 5.05109 3.82902 4.86032 3.96967 4.71967C4.11032 4.57902 4.30109 4.5 4.5 4.5H19.5C19.6989 4.5 19.8897 4.57902 20.0303 4.71967C20.171 4.86032 20.25 5.05109 20.25 5.25V10.75C20.25 18.6264 13.5651 21.236 12.2303 21.6785C12.0811 21.7298 11.9189 21.7298 11.7697 21.6785C10.4349 21.236 3.75 18.6264 3.75 10.75Z"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                        Thay đổi mật khẩu</a></li>
                                                <div class="dropdown-divider"></div>
                                                <li><a onclick="logout()">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M16.3145 8.0625L20.2509 12L16.3145 15.9375"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M9.75 12H20.2472" stroke="white"
                                                                  stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                            <path d="M9.75 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V4.5C3.75 4.30109 3.82902 4.11032 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75H9.75"
                                                                  stroke="white" stroke-width="1.52202"
                                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                                        </svg>
                                                        Đăng xuất</a></li>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <div class="col">
                                <div class="tabs">
                                    <div class="tab">
                                        <input type="checkbox" id="chck1">
                                        <label class="tab-label" for="chck1">
                                            <p class="text-white font-beanbag">Về DUNGMORI</p>
                                        </label>
                                        <div class="tab-content">
                                            <a href="{{url('/ve-dungmori')}}" class="child-label">
                                                <div class="content-label">Về DUNGMORI</div>
                                            </a>
                                            <a href="{{url('/bai-viet')}}" class="child-label">
                                                <div class="content-label">Tin tức</div>
                                            </a>
                                            <a href="{{url('/giao-vien')}}" class="child-label">
                                                <div class="content-label">Giáo viên</div>
                                            </a>
                                            <a href="{{url('/review')}}" class="child-label">
                                                <div class="content-label">Cảm nhận học viên</div>
                                            </a>
                                            <a href="{{url('/tuyen-dung')}}" class="child-label">
                                                <div class="content-label">Tuyển dụng</div>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="tab">
                                        <input type="checkbox" id="chck2">
                                        <label class="tab-label" for="chck2">
                                            <p class="text-white font-beanbag">JLPT </p>
                                        </label>
                                        <div class="tab-content">
                                            <div class="tabs">
                                                <div class="tab">
                                                    <input type="checkbox" id="chck1_1">
                                                    <label class="tab-label child-label" for="chck1_1"><p>Học qua
                                                            App/Web</p></label>
                                                    <div class="tab-content">
                                                        <a href="{{url('khoa-hoc/chuyen-nganh')}}" class="child-label">
                                                            <div class="content-label">Chuyên ngành
                                                                <div class="free">Miễn phí</div>
                                                            </div>
                                                        </a>
                                                        <a href="{{url('khoa-hoc/so-cap-n5')}}" class="child-label">
                                                            <div class="content-label">N5
                                                                <div class="free">Học thử miễn phí</div>
                                                            </div>
                                                        </a>
                                                        <a href="{{url('khoa-hoc/so-cap-n4')}}" class="child-label">
                                                            <div class="content-label">N4</div>
                                                        </a>
                                                        <a href="{{url('khoa-hoc/jlpt-n3')}}" class="child-label">
                                                            <div class="content-label">N3</div>
                                                        </a>
                                                        <a href="{{url('khoa-hoc/jlpt-n2')}}" class="child-label">
                                                            <div class="content-label">N2</div>
                                                        </a>
                                                        <a href="{{url('khoa-hoc/jlpt-n1')}}" class="child-label">
                                                            <div class="content-label">N1</div>
                                                        </a>
                                                    </div>
                                                </div>
                                                {{-- <div class="tab">
                                                    <input type="checkbox" id="chck1_2">
                                                    <label class="tab-label child-label" for="chck1_2"><p>Online Basic luyện
                                                            đề</p></label>
                                                    <div class="tab-content">
                                                        <a href="{{url('/khoa-hoc/luyen-de-n1?from_lesson=1')}}"
                                                           class="child-label">
                                                            <div class="content-label">Luyện đề N1</div>
                                                        </a>
                                                        <a href="{{url('/khoa-hoc/luyen-de-n2?from_lesson=1')}}"
                                                           class="child-label">
                                                            <div class="content-label">Luyện đề N2</div>
                                                        </a>
                                                        <a href="{{url('/khoa-hoc/luyen-de-n3?from_lesson=1')}}"
                                                           class="child-label">
                                                            <div class="content-label">Luyện đề N3</div>
                                                        </a>
                                                        <a href="{{url('https://luyende.dungmori.com')}}" target="_blank"
                                                           class="child-label">
                                                            <div class="content-label">Luyện đề VIP500</div>
                                                        </a>
                                                    </div>
                                                </div> --}}
                                                <div class="tab">
                                                    <input type="checkbox" id="chck1_3">
                                                    <label class="tab-label child-label" for="chck1_5">
                                                        <a href="https://onlinevip.dungmori.com/?utm_source=website&utm_medium=menu&utm_campaign=header"
                                                           target="_blank" class="!p-0">
                                                            Học qua ZOOM</a></label>
                                                </div>
                                                {{-- <div class="tab">
                                                    <input type="checkbox" id="chck1_4">
                                                    <label class="tab-label child-label" for="chck1_5">
                                                        <a href="https://onlineplus.dungmori.com/?utm_source=website&utm_medium=menu&utm_campaign=header"
                                                           target="_blank" class="!p-0">
                                                            Online Plus</a></label>
                                                </div> --}}
                                                <div class="tab">
                                                    <input type="checkbox" id="chck1_5">
                                                    <label class="tab-label child-label" for="chck1_5">
                                                        <a href="https://offline.dungmori.com/tructiep?utm_source=website&utm_medium=menu&utm_campaign=header"
                                                           target="_blank" class="!p-0">
                                                            Offline</a></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab">
                                        <input type="checkbox" id="chck4">
                                        <label class="tab-label" for="chck4">
                                            <p class="text-white font-beanbag">KAIWA</p>
                                        </label>
                                        <div class="tab-content">
                                            <a href="{{url('khoa-hoc/kaiwa-so-cap')}}" class="child-label">
                                                <div class="content-label">Kaiwa sơ cấp</div>
                                            </a>
                                            <a href="{{url('khoa-hoc/kaiwa-trung-cap-1')}}" class="child-label">
                                                <div class="content-label">Kaiwa trung cấp</div>
                                            </a>
                                            <a href="https://kaiwa.dungmori.com/" class="child-label">
                                                <div class="content-label">Kaiwa VIP</div>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="tab">
                                        <input type="checkbox" id="chck6">
                                        <label class="tab-label" for="chck6">
                                            <a href="https://online.dungmori.com/b2b?utm_source=website&utm_medium=menu&utm_campaign=header"
                                               target="_blank"><p class="text-white font-beanbag">
                                                    B2B</p></a></label>
                                    </div>
                                    <div class="tab">
                                        <input type="checkbox" id="chck7">
                                        <label class="tab-label" for="chck7">
                                            <a href="https://sach.dungmori.com?utm_source=website&utm_medium=menu&utm_campaign=header"
                                               target="_blank"><p class="text-white font-beanbag">
                                                    SÁCH</p></a></label>
                                    </div>
                                    <div class="tab">
                                        <input type="checkbox" id="chck8">
                                        <label class="tab-label" for="chck8">
                                            <a href="{{ url('/bang-gia') }}" target="_blank"><p
                                                        class="text-white font-beanbag">
                                                    GIỎ HÀNG</p></a></label>
                                    </div>
                                    <div class="tab">
                                        <input type="checkbox" id="chck10">
                                        <label class="tab-label" for="chck10">
                                            <a href="{{url('khoa-hoc/khoa-n5')}}" target="_blank"><p
                                                        class="text-white font-beanbag">
                                                    HỌC THỬ MIỄN PHÍ</p></a></label>
                                    </div>

                                </div>
                            </div>

                        </ul>
                    </div>
                </div>
            </a>
        </div>


    </div>
    {{-- end of mobile --}}
    {{-- end header --}}

    {{-- load nội dung --}}
    <div id="default_page">
        @yield('content')
    </div>

    {{-- hộp thoại chat --}}
    {{-- @if(Auth::check()) --}}
    @include('frontend._layouts.chatbox')
    {{-- @endif --}}

    <div class="{{ isset($hideFooter) ? 'hidden' : '' }} bg-[#07403F] text-white text-[14px]
    @if (isset($courseOwner) && $courseOwner && $courseOwner->code_active_at != null && !($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->lessThan($courseOwner->watch_expired_day))) {
     pt-15 pb-[130px]
    @else
     py-15
    @endif
     relative font-averta-regular float-left w-full px-2 lg:px-0">
        <div class="container mx-auto">
            @if(\Illuminate\Support\Facades\Auth::check())
                <img class="lazyload object-cover w-[85px] h-[85px] absolute -top-[42px]" style="left: calc(50% - 42px)"
                     data-src="{{asset('assets/img/logo_footer.png')}}"/>
            @endif

            <div class="flex flex-col border-b pb-4.5 md:flex-row">
                <div class="flex-1 md:mr-4 mr-[0px] md:mt-0 mt-[20px]">
                    <div class="text-base font-bold border-b pb-2.5 font-averta-bold">Về DUNGMORI</div>
                    <div class="flex flex-row justify-between">
                        <div class="flex flex-col mt-2">
                            <a class="text-white py-1.5" class="text-white" href="{{ url('/ve-dungmori') }}">Về chúng
                                tôi</a>
                            <a class="text-white py-1.5" href="{{ url('/trang/chinh-sach-doi-tra') }}">Chính sách đổi
                                trả </a>
                            <a class="text-white py-1.5" href="{{ url('/ho-tro/chinh-sach-gia-han-khoa-hoc-online') }}">Chính
                                sách gia hạn</a>
                        </div>
                        <div class="flex flex-col mt-1.5">
                            <a class="text-white py-1.5" href="{{ url('/trang/dieu-khoan-su-dung') }}">Điều khoản sử
                                dụng</a>
                            <a class="text-white py-1.5" href="{{ url('/trang/chinh-sach-bao-mat') }}">Chính sách bảo
                                mật</a>
                            <a class="text-white py-1.5" href="{{ url('/tuyen-dung') }}">Tuyển dụng</a>
                        </div>
                    </div>
                </div>
                <div class="flex-1 md:mx-4 mx-[0px] md:mt-0 mt-[20px]">
                    <div class="text-base font-bold border-b pb-2.5 font-averta-bold">Fanpage</div>
                    <div class="flex flex-row justify-between">
                        <div class="flex flex-col mt-1.5">
                            <a class="text-white py-1.5" href="https://www.facebook.com/dungmori" target="_blank">DUNGMORI</a>
                            <a class="text-white py-1.5" href="https://www.facebook.com/Nhatngudungmori"
                               target="_blank">Nhật ngữ DUNGMORI</a>
                        </div>
                        <div class="flex flex-col mt-1.5">
                            <a class="text-white py-1.5" href="https://www.facebook.com/dungmoritiengnhat"
                               target="_blank">Tiếng Nhật giao tiếp</a>
                            <a class="text-white py-1.5" href="https://www.facebook.com/dungmoriluyenthiJLPT"
                               target="_blank">Luyện thi JLPT</a>
                        </div>
                    </div>
                </div>
                <div class="flex-1 md:ml-4 ml-[0px] md:mt-0 mt-[20px]">
                    <div class="text-base font-bold border-b pb-2.5 font-averta-bold">Social</div>
                    <div class="mt-4">
                        <a class="mr-1.25" href="https://www.facebook.com/dungmori" target="_blank">
                            <img class="lazyload object-cover w-[36px] h-[36px]"
                                 data-src="{{asset('assets/img/fb_footer.png')}}"/>
                        </a>
                        <a class="mx-1.25" href="https://www.youtube.com/user/moridung" target="_blank">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/yt_footer.png')}}"/>
                        </a>
                        <a class="mx-1.25" href="https://www.instagram.com/dung.mori" target="_blank">
                            <img class="lazyload object-cover w-[36px] h-[36px]"
                                 data-src="{{asset('assets/img/ins.png')}}"/>
                        </a>
                        <a class="mx-1.25" href="https://zalo.me/3424692655094534236" target="_blank">
                            <img class="lazyload object-cover w-[36px] h-[36px]"
                                 data-src="{{asset('assets/img/zalo.png')}}"/>
                        </a>
                        <a class="mx-1.25" href="https://line.me/ti/p/qBzuwAs9rq" target="_blank">
                            <img class="lazyload object-cover w-[36px] h-[36px]"
                                 data-src="{{asset('assets/img/line.png')}}"/>
                        </a>
                        <a class="mx-1.25" class="social_link" href="https://www.tiktok.com/@dungmoriofficial"
                           target="_blank">
                            <img class="lazyload object-cover w-[36px] h-[36px]"
                                 data-src="{{asset('assets/img/tiktok_thay_dung.png')}}"/>
                        </a>
                        <a class="ml-1.25" class="social_link" href="https://www.tiktok.com/@phuongthanh_dungmori"
                           target="_blank">
                            <img class="lazyload object-cover w-[36px] h-[36px]"
                                 data-src="{{asset('assets/img/tiktok_co_thanh.png')}}"/>
                        </a>
                    </div>
                    <div class="mt-4" id="app-btns" onmouseover="stopBounce()">
                        <div class="hidden border-amber-500"></div>
                        <a href="https://apps.apple.com/us/app/id1486123836" target="_blank" class="mobile-app-button">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/dungmori_ios.png')}}"
                                 alt=""/>
                        </a>
                        <a class="ml-2.5 mobile-app-button"
                           href=" https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp"
                           target="_blank">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/dungmori_android.png')}}"
                                 alt=""/>
                        </a>
                    </div>
                </div>
            </div>
            <div class="mt-4.5 flex md:flex-row flex-col pb-4.5 border-b">
                <div class="flex-1 md:mr-3 mr-[0px]">
                    <a class="text-white block md-h-[75px] h-auto"
                       href="https://www.google.com/maps/place/Nh%E1%BA%ADt+ng%E1%BB%AF+D%C5%A9ng+Mori/@20.9840508,105.7745928,18.57z/data=!4m6!3m5!1s0x3134530686edd6fb:0xf77a293c314835cd!8m2!3d20.9841005!4d105.7752573!16s%2Fg%2F11w4bq37rp?entry=ttu&g_ep=EgoyMDI0MTAwNy4xIKXMDSoASAFQAw%3D%3D"
                       target="_blank"><span class="font-averta-bold font-bold">Trụ sở:</span> Số 49 Galaxy 3, Vạn Phúc,<br>
                        Hà Đông, Hà Nội.</a><br>
                    <a class="text-white mt-3 font-bold font-averta-bold" href="tel:+84862056363"><i
                                class="zmdi zmdi-phone-in-talk"></i>&nbsp;&nbsp;&nbsp;(+84) 336 283 595</a>
                </div>
                <div class="flex-1 md:mx-3 mx-[0px] md:mt-0 mt-[16px]">
                    <a class="text-white block md-h-[75px] h-auto"
                       href="https://www.google.com/maps/place/Nh%E1%BA%ADt+Ng%E1%BB%AF+Nihongonomori+Hanoi/@21.0016597,105.8200671,17z/data=!4m5!3m4!1s0x3135ac84ffd5e36f:0x127912f9639dc8d9!8m2!3d21.0016547!4d105.8200617"
                       target="_blank"><span class="font-averta-bold font-bold">Cơ sở 2:</span> Nhà liền kề số 03 VNT
                        TOWER, Số 19 Nguyễn Trãi,<br>Thanh Xuân, Hà Nội</a>
                    <a class="text-white mt-3 font-bold font-averta-bold" href="tel:+84969856116"><i
                                class="zmdi zmdi-phone-in-talk"></i>&nbsp;&nbsp;&nbsp;(+84) 969 856 116</a>
                </div>
                <div class="flex-1 md:mx-3 mx-[0px] md:mt-0 mt-[16px]">
                    <a class="text-white block md-h-[75px] h-auto"
                       href="https://www.google.com/maps/place/Trung+t%C3%A2m+ti%E1%BA%BFng+Nh%E1%BA%ADt+D%C5%A9ng+Mori/@21.0458207,105.7828938,17z/data=!3m1!4b1!4m6!3m5!1s0x3135ab74ed3e31b1:0x18998e5174af7527!8m2!3d21.0458207!4d105.7850825!16s%2Fg%2F11s4bw01p_"
                       target="_blank"><span class="font-averta-bold font-bold">Cơ sở 3:</span> Số 457 Hoàng Quốc
                        Việt,<br>Cổ Nhuế, Bắc Từ Liêm, Hà Nội</a><br>
                    <a class="text-white mt-3 font-bold font-averta-bold" href="tel:+84833532039"><i
                                class="zmdi zmdi-phone-in-talk"></i>&nbsp;&nbsp;&nbsp;(+84) 833 532 039</a>
                </div>
                {{-- <div class="flex-1 md:ml-3 ml-[0px] md:mt-0 mt-[16px]">
                    <a class="text-white block md-h-[75px] h-auto"
                       href="https://www.google.com/maps/place/Nh%E1%BA%ADt+Ng%E1%BB%AF+D%C5%A9ng+Mori/@10.7928556,106.6876573,17z/data=!4m16!1m9!3m8!1s0x317528cda3e9107f:0x2572d228c75fef!2zMTcgxJAuIFRy4bqnbiBOaOG6rXQgRHXhuq10LCBUw6JuIMSQ4buLbmgsIFF14bqtbiAxLCBUaMOgbmggcGjhu5EgSOG7kyBDaMOtIE1pbmgsIFZp4buHdCBOYW0!3b1!8m2!3d10.7928556!4d106.689846!10e3!16s%2Fg%2F11fsjxt25d!3m5!1s0x3175299e5651cb75:0xfe4b7d166b821a9b!8m2!3d10.7928556!4d106.689846!16s%2Fg%2F11kbyzhct7"
                       target="_blank"><span class="font-averta-bold font-bold">Cơ sở 4:</span> Số 17 Trần Nhật Duật,<br>P.Tân Định, Q1, TP.Hồ Chí Minh</a><br>
                    <a class="text-white mt-3 font-bold font-averta-bold" href="tel:+84862426363"><i
                                class="zmdi zmdi-phone-in-talk"></i>&nbsp;&nbsp;&nbsp;(+84) 862 426 363</a>
                </div>
                <div class="flex-1 md:ml-3 ml-[0px] md:mt-0 mt-[16px]">
                    <a class="text-white block md-h-[75px] h-auto"
                       href="https://maps.app.goo.gl/thGLzeoNrpzYtAPn9"
                       target="_blank"><span class="font-averta-bold font-bold">Cơ sở 5:</span> Số 32 Đường số 2, P.10, Q.Gò Vấp, TP.Hồ Chí Minh</a><br>
                    <a class="text-white mt-3 font-bold font-averta-bold" href="tel:+84862426363"><i
                                class="zmdi zmdi-phone-in-talk"></i>&nbsp;&nbsp;&nbsp;(+84) 988 938 364</a>
                </div> --}}
            </div>
            <div class="mt-4.5 flex flex-row pb-4.5 border-b font-averta-bold font-bold">
                <div class="flex-1 md:flex block flex-row">
                    <div class="flex-1 md:mr-3 mx-[0px]">Thứ 2-6: 7h00 - 21h00</div>
                    <div class="flex-1 md:mx-3 mx-[0px] md:mt-0 mt-[8px]">Thứ 7: 8h00 - 15h00</div>
                </div>
                <div class="flex-1 md:flex block flex-row">
                    <div class="flex-1 md:mx-3 mx-[0px]">Email: <EMAIL></div>
                    <div class="flex-1 md:ml-3 mx-[0px] md:mt-0 mt-[8px]">Online: +84.969.868.485</div>
                </div>
            </div>
            <div class="mt-4.5 flex md:flex-row flex-col md:items-center items-start">
                <div class="flex-1">
                    <div>Copyright © 2020 DUNGMORI</div>
                    <div>Mã số thuế 0106799375, do Sở Kế hoạch và Đầu tư TP. Hà Nội cấp ngày 24/03/2015</div>
                    <div>Quyết định thành lập Trung tâm Nhật ngữ DUNGMORI, số 3920 do Sở Giáo dục và Đào tạo Hà Nội cấp
                        ngày 9/9/2019
                    </div>
                </div>
                <a href="http://online.gov.vn/HomePage/CustomWebsiteDisplay.aspx?DocId=59464"
                   class="bocongthuong md:mt-0 mt-[15px]" target="_blank">
                    <img class="lazyload object-cover h-[65px]"
                         data-src="{{asset('assets/img/new_home/bocongthuong.png')}}" alt="dungmori logo"/>
                </a>
            </div>
        </div>
    </div>

    <div class="{{ isset($hideBottomMenu) ? 'hidden' :'' }} flex lg:hidden lg:scale-[65%] rounded-t-xl overflow-hidden origin-bottom bg-dmr-green fixed-panel-wrapper h-[120px] lg:h-auto w-screen lg:w-auto fixed right-0 lg:-right-[20px] bottom-0 z-[99] flex-row lg:flex-col gap-0 lg:gap-4 items-center justify-center">
        <a href="{{ url('/test-online') }}"
           class="w-[80px] h-full bg-dmr-green p-2 pt-4 lg:p-4 flex flex-col items-center justify-start gap-2">
            <div class="w-[60px] h-[60px] bg-white aspect-square rounded-lg flex justify-center items-center p-2 flex-shrink-0">
                <img src="{{ asset('assets/img/new_home/06-2024/fixed-panel-test.svg') }}" alt="" class="h-[50%]">
            </div>
            <div class="text-white font-averta-bold">Test</div>
        </a>
        <a href="{{ url('/bang-gia') }}"
           class="w-[80px] h-full bg-dmr-green p-2 pt-4 lg:p-4 flex flex-col items-center justify-start gap-2">
            <div class="w-[60px] h-[60px] bg-white aspect-square rounded-lg flex justify-center items-center p-2 flex-shrink-0">
                <svg width="29" height="35" viewBox="0 0 29 35" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="h-[50%]">
                    <mask id="mask0_691_2593" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                          width="29" height="35">
                        <path d="M28.6512 0.294434H0.308594V34.3056H28.6512V0.294434Z" fill="white"/>
                    </mask>
                    <g mask="url(#mask0_691_2593)">
                        <path d="M9.64087 12.7339C9.36209 12.7339 9.07003 12.6277 8.85763 12.402L6.62739 10.1717C6.18931 9.73367 6.18931 9.03008 6.62739 8.59199C7.06547 8.15391 7.76906 8.15391 8.20714 8.59199L9.64087 10.0257L13.3048 6.36177C13.7429 5.92368 14.4465 5.92368 14.8846 6.36177C15.3227 6.79985 15.3227 7.50344 14.8846 7.94152L10.4241 12.402C10.2117 12.6144 9.91965 12.7339 9.64087 12.7339Z"
                              fill="#57D061"/>
                        <path d="M6.58666 24.6816H21.9726C23.0347 24.6816 23.0479 23.0222 21.9726 23.0222H6.58666C5.52464 23.0222 5.51136 24.6816 6.58666 24.6816Z"
                              fill="#57D061"/>
                        <path d="M6.58666 27.4426H14.4721C15.5342 27.4426 15.5474 25.7832 14.4721 25.7832H6.58666C5.52464 25.7832 5.51136 27.4426 6.58666 27.4426Z"
                              fill="#57D061"/>
                        <path d="M1.71549 34.0409C1.07828 34.0409 0.560547 33.5232 0.560547 32.886V1.71575C0.560547 1.07854 1.07828 0.560791 1.71549 0.560791H18.7211C19.0264 0.560791 19.3184 0.680268 19.5441 0.892672L28.0403 9.38883C28.2527 9.60123 28.3722 9.89329 28.3722 10.1986V32.8727C28.3722 33.5099 27.8544 34.0276 27.2172 34.0276H1.71549V34.0409ZM2.87044 31.731H26.0755V11.3668H18.7211C18.0838 11.3668 17.5661 10.8491 17.5661 10.2119V2.85742H2.87044V31.731ZM19.876 9.05695H24.4427L19.876 4.49027V9.05695Z"
                              fill="#18393A"/>
                        <path d="M9.64099 12.4681C9.41531 12.4681 9.20291 12.3751 9.0436 12.2158L6.81336 9.98559C6.48148 9.65371 6.48148 9.12271 6.81336 8.79083C6.97267 8.63153 7.18507 8.5386 7.41075 8.5386C7.63643 8.5386 7.84883 8.63153 8.00813 8.79083L9.64099 10.4237L13.5041 6.56058C13.6634 6.40128 13.8758 6.30835 14.1015 6.30835C14.3271 6.30835 14.5395 6.40128 14.6988 6.56058C15.0307 6.89246 15.0307 7.43676 14.6988 7.75536L10.2384 12.2158C10.0791 12.3751 9.86667 12.4681 9.64099 12.4681Z"
                              fill="#57D061"/>
                        <path d="M6.58765 24.416C6.16284 24.416 6.05664 24.0576 6.05664 23.8584C6.05664 23.6593 6.16284 23.2876 6.58765 23.2876H21.9736C22.3984 23.2876 22.5046 23.646 22.5046 23.8452C22.5046 24.0443 22.3984 24.416 21.9736 24.416H6.58765Z"
                              fill="#57D061"/>
                        <path d="M6.58765 27.1767C6.16284 27.1767 6.05664 26.8183 6.05664 26.6192C6.05664 26.42 6.16284 26.0483 6.58765 26.0483H14.4731C14.8979 26.0483 15.0041 26.4068 15.0041 26.6059C15.0041 26.805 14.8979 27.1767 14.4731 27.1767H6.58765Z"
                              fill="#57D061"/>
                    </g>
                    <path d="M7.30433 20.5918C6.77332 20.5918 6.30869 20.4325 5.92371 20.1404C5.75113 20.0077 5.6582 19.8218 5.6582 19.5962C5.6582 19.4103 5.72458 19.2377 5.85733 19.1182C5.99008 18.9855 6.14939 18.9191 6.33524 18.9191C6.48127 18.9191 6.62729 18.9722 6.76005 19.0651C6.9459 19.1979 7.13175 19.2775 7.29106 19.2775C7.53001 19.2775 7.76896 19.1979 7.76896 18.6536V16.0384C7.76896 15.8393 7.83534 15.68 7.96809 15.5339C8.11412 15.4012 8.2867 15.3215 8.47255 15.3215C8.6584 15.3215 8.83098 15.3879 8.97701 15.5207C9.10976 15.6667 9.18941 15.8393 9.18941 16.0251V18.6934C9.18941 19.2908 9.01684 19.7687 8.68495 20.1006C8.35307 20.4325 7.88844 20.6051 7.30433 20.6051V20.5918Z"
                          fill="#18393A"/>
                    <path d="M10.6231 20.5126C10.424 20.5126 10.2647 20.4462 10.1187 20.3134C9.9859 20.1674 9.90625 19.9948 9.90625 19.809V16.0123C9.90625 15.8132 9.97263 15.6538 10.1054 15.5078C10.2514 15.3751 10.424 15.2954 10.6098 15.2954C10.7957 15.2954 10.9683 15.3618 11.1143 15.4945C11.247 15.6406 11.3267 15.8131 11.3267 15.999V19.1851H13.1056C13.2914 19.1851 13.4375 19.2514 13.5702 19.3709C13.703 19.5037 13.7693 19.663 13.7693 19.8488C13.7693 20.0347 13.703 20.1807 13.5835 20.3134C13.4507 20.4462 13.2914 20.5126 13.1056 20.5126H10.6098H10.6231Z"
                          fill="#18393A"/>
                    <path d="M14.8868 20.566C14.7009 20.566 14.5284 20.4997 14.3823 20.3669C14.2363 20.2209 14.1699 20.0483 14.1699 19.8624V16.0657C14.1699 15.8799 14.2363 15.7073 14.369 15.5613C14.5151 15.4152 14.6877 15.3489 14.8735 15.3489H16.3072C16.9179 15.3489 17.4091 15.5214 17.7808 15.8533C18.1392 16.1852 18.3251 16.6366 18.3251 17.1941C18.3251 17.8048 18.1127 18.2694 17.6879 18.6146C17.3029 18.9199 16.7984 19.0792 16.201 19.0792H15.5771V19.8624C15.5771 20.0616 15.5107 20.2209 15.378 20.3669C15.2319 20.5129 15.0594 20.5793 14.8735 20.5793L14.8868 20.566ZM16.2541 17.7782C16.4665 17.7782 16.6258 17.7251 16.7586 17.6057C16.8648 17.4995 16.9179 17.38 16.9179 17.2207C16.9179 16.9286 16.8382 16.6498 16.2409 16.6498H15.6036V17.765H16.2674L16.2541 17.7782Z"
                          fill="#18393A"/>
                    <path d="M20.6076 20.5657C20.4217 20.5657 20.2492 20.4993 20.1031 20.3666C19.9704 20.2206 19.8907 20.048 19.8907 19.8621V16.6893H19.0544C18.8685 16.6893 18.7225 16.623 18.5898 16.4902C18.457 16.3574 18.3906 16.1982 18.3906 16.0256C18.3906 15.853 18.457 15.6804 18.5898 15.5609C18.7225 15.4282 18.8818 15.3618 19.0544 15.3618H22.1475C22.3334 15.3618 22.4794 15.4282 22.6121 15.5609C22.7449 15.6937 22.8113 15.853 22.8113 16.0256C22.8113 16.1982 22.7449 16.3574 22.6121 16.4902C22.4794 16.623 22.3201 16.6893 22.1475 16.6893H21.3112V19.8621C21.3112 20.0612 21.2448 20.2206 21.112 20.3666C20.966 20.5126 20.7934 20.579 20.6076 20.579V20.5657Z"
                          fill="#18393A"/>
                </svg>

            </div>
            <div class="text-white font-averta-bold text-xs">Cửa hàng</div>
        </a>
        <a href="{{ url('/thi-thu') }}"
           class="w-[80px] h-full bg-dmr-green p-2 pt-4 lg:p-4 flex flex-col items-center justify-start gap-2">
            <div class="w-[60px] h-[60px] bg-white aspect-square rounded-lg flex justify-center items-center p-2 flex-shrink-0">
                <svg width="29" height="35" viewBox="0 0 29 35" fill="none" xmlns="http://www.w3.org/2000/svg"
                     class="h-[50%]">
                    <mask id="mask0_691_2593" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                          width="29" height="35">
                        <path d="M28.6512 0.294434H0.308594V34.3056H28.6512V0.294434Z" fill="white"/>
                    </mask>
                    <g mask="url(#mask0_691_2593)">
                        <path d="M9.64087 12.7339C9.36209 12.7339 9.07003 12.6277 8.85763 12.402L6.62739 10.1717C6.18931 9.73367 6.18931 9.03008 6.62739 8.59199C7.06547 8.15391 7.76906 8.15391 8.20714 8.59199L9.64087 10.0257L13.3048 6.36177C13.7429 5.92368 14.4465 5.92368 14.8846 6.36177C15.3227 6.79985 15.3227 7.50344 14.8846 7.94152L10.4241 12.402C10.2117 12.6144 9.91965 12.7339 9.64087 12.7339Z"
                              fill="#57D061"/>
                        <path d="M6.58666 24.6816H21.9726C23.0347 24.6816 23.0479 23.0222 21.9726 23.0222H6.58666C5.52464 23.0222 5.51136 24.6816 6.58666 24.6816Z"
                              fill="#57D061"/>
                        <path d="M6.58666 27.4426H14.4721C15.5342 27.4426 15.5474 25.7832 14.4721 25.7832H6.58666C5.52464 25.7832 5.51136 27.4426 6.58666 27.4426Z"
                              fill="#57D061"/>
                        <path d="M1.71549 34.0409C1.07828 34.0409 0.560547 33.5232 0.560547 32.886V1.71575C0.560547 1.07854 1.07828 0.560791 1.71549 0.560791H18.7211C19.0264 0.560791 19.3184 0.680268 19.5441 0.892672L28.0403 9.38883C28.2527 9.60123 28.3722 9.89329 28.3722 10.1986V32.8727C28.3722 33.5099 27.8544 34.0276 27.2172 34.0276H1.71549V34.0409ZM2.87044 31.731H26.0755V11.3668H18.7211C18.0838 11.3668 17.5661 10.8491 17.5661 10.2119V2.85742H2.87044V31.731ZM19.876 9.05695H24.4427L19.876 4.49027V9.05695Z"
                              fill="#18393A"/>
                        <path d="M9.64099 12.4681C9.41531 12.4681 9.20291 12.3751 9.0436 12.2158L6.81336 9.98559C6.48148 9.65371 6.48148 9.12271 6.81336 8.79083C6.97267 8.63153 7.18507 8.5386 7.41075 8.5386C7.63643 8.5386 7.84883 8.63153 8.00813 8.79083L9.64099 10.4237L13.5041 6.56058C13.6634 6.40128 13.8758 6.30835 14.1015 6.30835C14.3271 6.30835 14.5395 6.40128 14.6988 6.56058C15.0307 6.89246 15.0307 7.43676 14.6988 7.75536L10.2384 12.2158C10.0791 12.3751 9.86667 12.4681 9.64099 12.4681Z"
                              fill="#57D061"/>
                        <path d="M6.58765 24.416C6.16284 24.416 6.05664 24.0576 6.05664 23.8584C6.05664 23.6593 6.16284 23.2876 6.58765 23.2876H21.9736C22.3984 23.2876 22.5046 23.646 22.5046 23.8452C22.5046 24.0443 22.3984 24.416 21.9736 24.416H6.58765Z"
                              fill="#57D061"/>
                        <path d="M6.58765 27.1767C6.16284 27.1767 6.05664 26.8183 6.05664 26.6192C6.05664 26.42 6.16284 26.0483 6.58765 26.0483H14.4731C14.8979 26.0483 15.0041 26.4068 15.0041 26.6059C15.0041 26.805 14.8979 27.1767 14.4731 27.1767H6.58765Z"
                              fill="#57D061"/>
                    </g>
                    <path d="M7.30433 20.5918C6.77332 20.5918 6.30869 20.4325 5.92371 20.1404C5.75113 20.0077 5.6582 19.8218 5.6582 19.5962C5.6582 19.4103 5.72458 19.2377 5.85733 19.1182C5.99008 18.9855 6.14939 18.9191 6.33524 18.9191C6.48127 18.9191 6.62729 18.9722 6.76005 19.0651C6.9459 19.1979 7.13175 19.2775 7.29106 19.2775C7.53001 19.2775 7.76896 19.1979 7.76896 18.6536V16.0384C7.76896 15.8393 7.83534 15.68 7.96809 15.5339C8.11412 15.4012 8.2867 15.3215 8.47255 15.3215C8.6584 15.3215 8.83098 15.3879 8.97701 15.5207C9.10976 15.6667 9.18941 15.8393 9.18941 16.0251V18.6934C9.18941 19.2908 9.01684 19.7687 8.68495 20.1006C8.35307 20.4325 7.88844 20.6051 7.30433 20.6051V20.5918Z"
                          fill="#18393A"/>
                    <path d="M10.6231 20.5126C10.424 20.5126 10.2647 20.4462 10.1187 20.3134C9.9859 20.1674 9.90625 19.9948 9.90625 19.809V16.0123C9.90625 15.8132 9.97263 15.6538 10.1054 15.5078C10.2514 15.3751 10.424 15.2954 10.6098 15.2954C10.7957 15.2954 10.9683 15.3618 11.1143 15.4945C11.247 15.6406 11.3267 15.8131 11.3267 15.999V19.1851H13.1056C13.2914 19.1851 13.4375 19.2514 13.5702 19.3709C13.703 19.5037 13.7693 19.663 13.7693 19.8488C13.7693 20.0347 13.703 20.1807 13.5835 20.3134C13.4507 20.4462 13.2914 20.5126 13.1056 20.5126H10.6098H10.6231Z"
                          fill="#18393A"/>
                    <path d="M14.8868 20.566C14.7009 20.566 14.5284 20.4997 14.3823 20.3669C14.2363 20.2209 14.1699 20.0483 14.1699 19.8624V16.0657C14.1699 15.8799 14.2363 15.7073 14.369 15.5613C14.5151 15.4152 14.6877 15.3489 14.8735 15.3489H16.3072C16.9179 15.3489 17.4091 15.5214 17.7808 15.8533C18.1392 16.1852 18.3251 16.6366 18.3251 17.1941C18.3251 17.8048 18.1127 18.2694 17.6879 18.6146C17.3029 18.9199 16.7984 19.0792 16.201 19.0792H15.5771V19.8624C15.5771 20.0616 15.5107 20.2209 15.378 20.3669C15.2319 20.5129 15.0594 20.5793 14.8735 20.5793L14.8868 20.566ZM16.2541 17.7782C16.4665 17.7782 16.6258 17.7251 16.7586 17.6057C16.8648 17.4995 16.9179 17.38 16.9179 17.2207C16.9179 16.9286 16.8382 16.6498 16.2409 16.6498H15.6036V17.765H16.2674L16.2541 17.7782Z"
                          fill="#18393A"/>
                    <path d="M20.6076 20.5657C20.4217 20.5657 20.2492 20.4993 20.1031 20.3666C19.9704 20.2206 19.8907 20.048 19.8907 19.8621V16.6893H19.0544C18.8685 16.6893 18.7225 16.623 18.5898 16.4902C18.457 16.3574 18.3906 16.1982 18.3906 16.0256C18.3906 15.853 18.457 15.6804 18.5898 15.5609C18.7225 15.4282 18.8818 15.3618 19.0544 15.3618H22.1475C22.3334 15.3618 22.4794 15.4282 22.6121 15.5609C22.7449 15.6937 22.8113 15.853 22.8113 16.0256C22.8113 16.1982 22.7449 16.3574 22.6121 16.4902C22.4794 16.623 22.3201 16.6893 22.1475 16.6893H21.3112V19.8621C21.3112 20.0612 21.2448 20.2206 21.112 20.3666C20.966 20.5126 20.7934 20.579 20.6076 20.579V20.5657Z"
                          fill="#18393A"/>
                </svg>

            </div>
            <div class="text-white font-averta-bold text-xs">Thi thử mobile</div>
        </a>

        <div onclick="ga('send', 'event', 'Right menu', 'click', 'Chat right side'); showChatbox()"
             class="w-[80px] h-full bg-dmr-green p-2 pt-4 lg:p-4 flex flex-col items-center justify-start gap-2">
            <div class="w-[60px] h-[60px] bg-white aspect-square rounded-lg flex justify-center items-center p-2 flex-shrink-0">
                <img src="{{ asset('assets/img/new_home/06-2024/fixed-panel-discussion.svg') }}" alt="" class="h-[50%]">
            </div>
            <div class="text-white font-averta-bold">@if (Auth::check())
                    Hỗ trợ
                @else
                    Tư vấn
                @endif</div>
        </div>
        <div class="w-[80px] h-full bg-dmr-green p-2 pt-4 lg:p-4 flex flex-col items-center justify-start gap-2"
             onclick="scrollToId('app-btns')">
            <div class="w-[60px] h-[60px] bg-white aspect-square rounded-lg flex justify-center items-center p-2 flex-shrink-0">
                <img src="{{ asset('assets/img/new_home/06-2024/fixed-panel-app.svg') }}" alt="" class="h-[50%]">
            </div>
            <div class="text-white font-averta-bold">Ứng dụng</div>
        </div>

    </div>
    <div class="{{ isset($hideSupportMenu) ? 'lg:hidden' :'' }} hidden lg:flex lg:scale-[65%] rounded-t-xl origin-bottom fixed-panel-wrapper h-[60px] lg:h-auto w-screen lg:w-auto fixed right-0 lg:-right-[20px] bottom-0 z-[999] flex-row lg:flex-col gap-0 lg:gap-4 items-center">

        {{--        {{ dd(\Illuminate\Support\Facades\Auth::user()) }}--}}
        @if(isset($config['web_icon_quick_jlpt_exam']) && $config['web_icon_quick_jlpt_exam'] == 1 && \Illuminate\Support\Facades\Auth::user())
            <!-- <a href="{{ url('/thi-thu') }}" onclick="ga('send', 'event', 'Right menu', 'click', 'Store right side')"
           class="">
            <svg class="" width="105" height="105" viewBox="0 0 94 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                <ellipse cx="46.5853" cy="47.7081" rx="47.7081" ry="46.5856" transform="rotate(90 46.5853 47.7081)"
                         fill="#EC6E23"/>
                <path d="M19.6396 70.1255H17.5676C17.297 70.1255 17.0636 70.0275 16.8676 69.8315C16.6716 69.6355 16.5736 69.4022 16.5736 69.1315C16.5736 68.8608 16.6716 68.6275 16.8676 68.4315C17.0636 68.2355 17.297 68.1375 17.5676 68.1375H23.8676C24.1383 68.1375 24.3716 68.2355 24.5676 68.4315C24.7636 68.6275 24.8616 68.8608 24.8616 69.1315C24.8616 69.4022 24.7636 69.6355 24.5676 69.8315C24.3716 70.0275 24.1383 70.1255 23.8676 70.1255H21.7956V76.9435C21.7956 77.2422 21.693 77.4988 21.4876 77.7135C21.273 77.9188 21.0163 78.0215 20.7176 78.0215C20.419 78.0215 20.1623 77.9188 19.9476 77.7135C19.7423 77.4988 19.6396 77.2422 19.6396 76.9435V70.1255ZM26.4358 69.1315C26.4358 68.8328 26.5384 68.5762 26.7438 68.3615C26.9584 68.1562 27.2151 68.0535 27.5138 68.0535C27.8124 68.0535 28.0691 68.1562 28.2838 68.3615C28.4891 68.5762 28.5918 68.8328 28.5918 69.1315V72.0155H32.5678V69.1315C32.5678 68.8328 32.6704 68.5762 32.8758 68.3615C33.0904 68.1562 33.3471 68.0535 33.6458 68.0535C33.9444 68.0535 34.2011 68.1562 34.4158 68.3615C34.6211 68.5762 34.7238 68.8328 34.7238 69.1315V76.9435C34.7238 77.2422 34.6211 77.4988 34.4158 77.7135C34.2011 77.9188 33.9444 78.0215 33.6458 78.0215C33.3471 78.0215 33.0904 77.9188 32.8758 77.7135C32.6704 77.4988 32.5678 77.2422 32.5678 76.9435V74.0035H28.5918V76.9435C28.5918 77.2422 28.4891 77.4988 28.2838 77.7135C28.0691 77.9188 27.8124 78.0215 27.5138 78.0215C27.2151 78.0215 26.9584 77.9188 26.7438 77.7135C26.5384 77.4988 26.4358 77.2422 26.4358 76.9435V69.1315ZM37.1705 69.1315C37.1705 68.8328 37.2732 68.5762 37.4785 68.3615C37.6932 68.1562 37.9498 68.0535 38.2485 68.0535C38.5472 68.0535 38.8038 68.1562 39.0185 68.3615C39.2238 68.5762 39.3265 68.8328 39.3265 69.1315V76.9435C39.3265 77.2422 39.2238 77.4988 39.0185 77.7135C38.8038 77.9188 38.5472 78.0215 38.2485 78.0215C37.9498 78.0215 37.6932 77.9188 37.4785 77.7135C37.2732 77.4988 37.1705 77.2422 37.1705 76.9435V69.1315ZM48.2549 70.1255H46.1829C45.9122 70.1255 45.6789 70.0275 45.4829 69.8315C45.2869 69.6355 45.1889 69.4022 45.1889 69.1315C45.1889 68.8608 45.2869 68.6275 45.4829 68.4315C45.6789 68.2355 45.9122 68.1375 46.1829 68.1375H52.4829C52.7535 68.1375 52.9869 68.2355 53.1829 68.4315C53.3789 68.6275 53.4769 68.8608 53.4769 69.1315C53.4769 69.4022 53.3789 69.6355 53.1829 69.8315C52.9869 70.0275 52.7535 70.1255 52.4829 70.1255H50.4109V76.9435C50.4109 77.2422 50.3082 77.4988 50.1029 77.7135C49.8882 77.9188 49.6315 78.0215 49.3329 78.0215C49.0342 78.0215 48.7775 77.9188 48.5629 77.7135C48.3575 77.4988 48.2549 77.2422 48.2549 76.9435V70.1255ZM55.051 69.1315C55.051 68.8328 55.1537 68.5762 55.359 68.3615C55.5737 68.1562 55.8303 68.0535 56.129 68.0535C56.4277 68.0535 56.6843 68.1562 56.899 68.3615C57.1043 68.5762 57.207 68.8328 57.207 69.1315V72.0155H61.183V69.1315C61.183 68.8328 61.2857 68.5762 61.491 68.3615C61.7057 68.1562 61.9623 68.0535 62.261 68.0535C62.5597 68.0535 62.8163 68.1562 63.031 68.3615C63.2363 68.5762 63.339 68.8328 63.339 69.1315V76.9435C63.339 77.2422 63.2363 77.4988 63.031 77.7135C62.8163 77.9188 62.5597 78.0215 62.261 78.0215C61.9623 78.0215 61.7057 77.9188 61.491 77.7135C61.2857 77.4988 61.183 77.2422 61.183 76.9435V74.0035H57.207V76.9435C57.207 77.2422 57.1043 77.4988 56.899 77.7135C56.6843 77.9188 56.4277 78.0215 56.129 78.0215C55.8303 78.0215 55.5737 77.9188 55.359 77.7135C55.1537 77.4988 55.051 77.2422 55.051 76.9435V69.1315ZM74.0737 69.6075V73.6675C74.0737 75.1235 73.6864 76.2295 72.9117 76.9855C72.1651 77.7228 71.1197 78.0915 69.7757 78.0915C68.4411 78.0915 67.4051 77.7275 66.6677 76.9995C65.9117 76.2435 65.5337 75.1562 65.5337 73.7375V69.1315C65.5337 68.8328 65.6364 68.5762 65.8417 68.3615C66.0564 68.1562 66.3131 68.0535 66.6117 68.0535C66.9104 68.0535 67.1671 68.1562 67.3817 68.3615C67.5871 68.5762 67.6897 68.8328 67.6897 69.1315V73.6815C67.6897 75.2962 68.3944 76.1035 69.8037 76.1035C71.2131 76.1035 71.9177 75.3195 71.9177 73.7515V69.1315C71.9177 68.8328 72.0204 68.5762 72.2257 68.3615C72.4404 68.1562 72.6971 68.0535 72.9957 68.0535H73.0937C73.5324 67.9695 73.7751 67.6335 73.8217 67.0455C74.0177 66.8215 74.2604 66.7095 74.5497 66.7095C74.8204 66.7095 75.0491 66.8028 75.2357 66.9895C75.4224 67.1762 75.5157 67.4048 75.5157 67.6755C75.5157 67.9088 75.4411 68.1748 75.2917 68.4735C75.0491 69.0148 74.6431 69.3928 74.0737 69.6075ZM69.9157 67.2975C69.6264 67.2975 69.4584 67.1482 69.4117 66.8495L69.3557 66.4575C69.2997 66.1215 69.4444 65.9302 69.7897 65.8835C70.1444 65.8462 70.3217 65.7528 70.3217 65.6035C70.3217 65.4728 70.2191 65.4075 70.0137 65.4075C69.8271 65.4075 69.6544 65.4682 69.4957 65.5895C69.3744 65.6828 69.2484 65.7295 69.1177 65.7295C68.9497 65.7295 68.8051 65.6688 68.6837 65.5475C68.5717 65.4262 68.5157 65.2862 68.5157 65.1275C68.5157 64.9408 68.5904 64.7822 68.7397 64.6515C69.0944 64.3528 69.5237 64.2035 70.0277 64.2035C70.4944 64.2035 70.8677 64.3202 71.1477 64.5535C71.4464 64.8055 71.5957 65.1462 71.5957 65.5755V65.5895C71.5957 66.3082 71.2177 66.7422 70.4617 66.8915L70.4757 66.8495C70.4291 67.1482 70.2611 67.2975 69.9717 67.2975H69.9157Z"
                      fill="white"/>
                <path d="M19.4841 60C18.5634 60 18.103 59.5396 18.103 58.6189V24.1971C18.103 23.2764 18.5634 22.816 19.4841 22.816H27.399C28.3198 22.816 28.7979 23.2587 28.8333 24.144L31.1705 48.951V54.2099H32.1798V48.951L34.5702 24.144C34.6056 23.2587 35.066 22.816 35.9513 22.816H43.6537C44.5745 22.816 45.0349 23.2764 45.0349 24.1971V58.6189C45.0349 59.5396 44.5745 60 43.6537 60H40.5728C39.652 60 39.1917 59.5396 39.1917 58.6189V32.2182L39.2448 29.2435H38.2886L38.1824 32.2182L35.5264 58.672C35.491 59.5573 35.0129 60 34.0921 60H29.0457C28.125 60 27.6469 59.5573 27.6115 58.672L24.9555 32.2182L24.8493 29.2435H23.8931L23.9462 32.2182V58.6189C23.9462 59.5396 23.4859 60 22.5651 60H19.4841ZM48.5462 60C47.6254 60 47.1651 59.5396 47.1651 58.6189V56.0691C47.1651 55.1484 47.6254 54.688 48.5462 54.688H49.7679C50.5116 54.688 51.0959 54.4755 51.5209 54.0506C51.9813 53.5902 52.2115 52.9882 52.2115 52.2445V24.1971C52.2115 23.2764 52.6718 22.816 53.5926 22.816H56.6735C57.5943 22.816 58.0547 23.2764 58.0547 24.1971V52.6163C58.0547 54.8474 57.3287 56.6357 55.8767 57.9814C54.4248 59.3271 52.5125 60 50.1398 60H48.5462ZM73.2026 22.816C74.1233 22.816 74.5837 23.2764 74.5837 24.1971V26.7469C74.5837 27.6676 74.1233 28.128 73.2026 28.128H70.4403V58.6189C70.4403 59.5396 69.98 60 69.0592 60H65.9783C65.0575 60 64.5971 59.5396 64.5971 58.6189V28.128H61.8349C60.9141 28.128 60.4538 27.6676 60.4538 26.7469V24.1971C60.4538 23.2764 60.9141 22.816 61.8349 22.816H73.2026Z"
                      fill="white"/>
            </svg>
        </a> -->
        @endif

        {{--            @yield('fixed-panel')--}}
        {{-- <a href="{{ url('/thi-thu') }}"
           class="transition-all hover:scale-105 h-full border-r border-white lg:border-r-0 lg:h-auto w-1/4 p-2 lg:p-0 cursor-pointer text-white hover:text-white lg:w-[100px] aspect-auto lg:aspect-square rounded-none lg:rounded-full bg-[#118C60] hover:bg-[#EC6E23] flex flex-col items-center justify-center gap-0">
            <svg class="" width="105" height="105" viewBox="0 0 94 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                <ellipse cx="46.5853" cy="47.7081" rx="47.7081" ry="46.5856" transform="rotate(90 46.5853 47.7081)"
                         fill="#EC6E23"/>
                <path d="M19.6396 70.1255H17.5676C17.297 70.1255 17.0636 70.0275 16.8676 69.8315C16.6716 69.6355 16.5736 69.4022 16.5736 69.1315C16.5736 68.8608 16.6716 68.6275 16.8676 68.4315C17.0636 68.2355 17.297 68.1375 17.5676 68.1375H23.8676C24.1383 68.1375 24.3716 68.2355 24.5676 68.4315C24.7636 68.6275 24.8616 68.8608 24.8616 69.1315C24.8616 69.4022 24.7636 69.6355 24.5676 69.8315C24.3716 70.0275 24.1383 70.1255 23.8676 70.1255H21.7956V76.9435C21.7956 77.2422 21.693 77.4988 21.4876 77.7135C21.273 77.9188 21.0163 78.0215 20.7176 78.0215C20.419 78.0215 20.1623 77.9188 19.9476 77.7135C19.7423 77.4988 19.6396 77.2422 19.6396 76.9435V70.1255ZM26.4358 69.1315C26.4358 68.8328 26.5384 68.5762 26.7438 68.3615C26.9584 68.1562 27.2151 68.0535 27.5138 68.0535C27.8124 68.0535 28.0691 68.1562 28.2838 68.3615C28.4891 68.5762 28.5918 68.8328 28.5918 69.1315V72.0155H32.5678V69.1315C32.5678 68.8328 32.6704 68.5762 32.8758 68.3615C33.0904 68.1562 33.3471 68.0535 33.6458 68.0535C33.9444 68.0535 34.2011 68.1562 34.4158 68.3615C34.6211 68.5762 34.7238 68.8328 34.7238 69.1315V76.9435C34.7238 77.2422 34.6211 77.4988 34.4158 77.7135C34.2011 77.9188 33.9444 78.0215 33.6458 78.0215C33.3471 78.0215 33.0904 77.9188 32.8758 77.7135C32.6704 77.4988 32.5678 77.2422 32.5678 76.9435V74.0035H28.5918V76.9435C28.5918 77.2422 28.4891 77.4988 28.2838 77.7135C28.0691 77.9188 27.8124 78.0215 27.5138 78.0215C27.2151 78.0215 26.9584 77.9188 26.7438 77.7135C26.5384 77.4988 26.4358 77.2422 26.4358 76.9435V69.1315ZM37.1705 69.1315C37.1705 68.8328 37.2732 68.5762 37.4785 68.3615C37.6932 68.1562 37.9498 68.0535 38.2485 68.0535C38.5472 68.0535 38.8038 68.1562 39.0185 68.3615C39.2238 68.5762 39.3265 68.8328 39.3265 69.1315V76.9435C39.3265 77.2422 39.2238 77.4988 39.0185 77.7135C38.8038 77.9188 38.5472 78.0215 38.2485 78.0215C37.9498 78.0215 37.6932 77.9188 37.4785 77.7135C37.2732 77.4988 37.1705 77.2422 37.1705 76.9435V69.1315ZM48.2549 70.1255H46.1829C45.9122 70.1255 45.6789 70.0275 45.4829 69.8315C45.2869 69.6355 45.1889 69.4022 45.1889 69.1315C45.1889 68.8608 45.2869 68.6275 45.4829 68.4315C45.6789 68.2355 45.9122 68.1375 46.1829 68.1375H52.4829C52.7535 68.1375 52.9869 68.2355 53.1829 68.4315C53.3789 68.6275 53.4769 68.8608 53.4769 69.1315C53.4769 69.4022 53.3789 69.6355 53.1829 69.8315C52.9869 70.0275 52.7535 70.1255 52.4829 70.1255H50.4109V76.9435C50.4109 77.2422 50.3082 77.4988 50.1029 77.7135C49.8882 77.9188 49.6315 78.0215 49.3329 78.0215C49.0342 78.0215 48.7775 77.9188 48.5629 77.7135C48.3575 77.4988 48.2549 77.2422 48.2549 76.9435V70.1255ZM55.051 69.1315C55.051 68.8328 55.1537 68.5762 55.359 68.3615C55.5737 68.1562 55.8303 68.0535 56.129 68.0535C56.4277 68.0535 56.6843 68.1562 56.899 68.3615C57.1043 68.5762 57.207 68.8328 57.207 69.1315V72.0155H61.183V69.1315C61.183 68.8328 61.2857 68.5762 61.491 68.3615C61.7057 68.1562 61.9623 68.0535 62.261 68.0535C62.5597 68.0535 62.8163 68.1562 63.031 68.3615C63.2363 68.5762 63.339 68.8328 63.339 69.1315V76.9435C63.339 77.2422 63.2363 77.4988 63.031 77.7135C62.8163 77.9188 62.5597 78.0215 62.261 78.0215C61.9623 78.0215 61.7057 77.9188 61.491 77.7135C61.2857 77.4988 61.183 77.2422 61.183 76.9435V74.0035H57.207V76.9435C57.207 77.2422 57.1043 77.4988 56.899 77.7135C56.6843 77.9188 56.4277 78.0215 56.129 78.0215C55.8303 78.0215 55.5737 77.9188 55.359 77.7135C55.1537 77.4988 55.051 77.2422 55.051 76.9435V69.1315ZM74.0737 69.6075V73.6675C74.0737 75.1235 73.6864 76.2295 72.9117 76.9855C72.1651 77.7228 71.1197 78.0915 69.7757 78.0915C68.4411 78.0915 67.4051 77.7275 66.6677 76.9995C65.9117 76.2435 65.5337 75.1562 65.5337 73.7375V69.1315C65.5337 68.8328 65.6364 68.5762 65.8417 68.3615C66.0564 68.1562 66.3131 68.0535 66.6117 68.0535C66.9104 68.0535 67.1671 68.1562 67.3817 68.3615C67.5871 68.5762 67.6897 68.8328 67.6897 69.1315V73.6815C67.6897 75.2962 68.3944 76.1035 69.8037 76.1035C71.2131 76.1035 71.9177 75.3195 71.9177 73.7515V69.1315C71.9177 68.8328 72.0204 68.5762 72.2257 68.3615C72.4404 68.1562 72.6971 68.0535 72.9957 68.0535H73.0937C73.5324 67.9695 73.7751 67.6335 73.8217 67.0455C74.0177 66.8215 74.2604 66.7095 74.5497 66.7095C74.8204 66.7095 75.0491 66.8028 75.2357 66.9895C75.4224 67.1762 75.5157 67.4048 75.5157 67.6755C75.5157 67.9088 75.4411 68.1748 75.2917 68.4735C75.0491 69.0148 74.6431 69.3928 74.0737 69.6075ZM69.9157 67.2975C69.6264 67.2975 69.4584 67.1482 69.4117 66.8495L69.3557 66.4575C69.2997 66.1215 69.4444 65.9302 69.7897 65.8835C70.1444 65.8462 70.3217 65.7528 70.3217 65.6035C70.3217 65.4728 70.2191 65.4075 70.0137 65.4075C69.8271 65.4075 69.6544 65.4682 69.4957 65.5895C69.3744 65.6828 69.2484 65.7295 69.1177 65.7295C68.9497 65.7295 68.8051 65.6688 68.6837 65.5475C68.5717 65.4262 68.5157 65.2862 68.5157 65.1275C68.5157 64.9408 68.5904 64.7822 68.7397 64.6515C69.0944 64.3528 69.5237 64.2035 70.0277 64.2035C70.4944 64.2035 70.8677 64.3202 71.1477 64.5535C71.4464 64.8055 71.5957 65.1462 71.5957 65.5755V65.5895C71.5957 66.3082 71.2177 66.7422 70.4617 66.8915L70.4757 66.8495C70.4291 67.1482 70.2611 67.2975 69.9717 67.2975H69.9157Z"
                      fill="white"/>
                <path d="M19.4841 60C18.5634 60 18.103 59.5396 18.103 58.6189V24.1971C18.103 23.2764 18.5634 22.816 19.4841 22.816H27.399C28.3198 22.816 28.7979 23.2587 28.8333 24.144L31.1705 48.951V54.2099H32.1798V48.951L34.5702 24.144C34.6056 23.2587 35.066 22.816 35.9513 22.816H43.6537C44.5745 22.816 45.0349 23.2764 45.0349 24.1971V58.6189C45.0349 59.5396 44.5745 60 43.6537 60H40.5728C39.652 60 39.1917 59.5396 39.1917 58.6189V32.2182L39.2448 29.2435H38.2886L38.1824 32.2182L35.5264 58.672C35.491 59.5573 35.0129 60 34.0921 60H29.0457C28.125 60 27.6469 59.5573 27.6115 58.672L24.9555 32.2182L24.8493 29.2435H23.8931L23.9462 32.2182V58.6189C23.9462 59.5396 23.4859 60 22.5651 60H19.4841ZM48.5462 60C47.6254 60 47.1651 59.5396 47.1651 58.6189V56.0691C47.1651 55.1484 47.6254 54.688 48.5462 54.688H49.7679C50.5116 54.688 51.0959 54.4755 51.5209 54.0506C51.9813 53.5902 52.2115 52.9882 52.2115 52.2445V24.1971C52.2115 23.2764 52.6718 22.816 53.5926 22.816H56.6735C57.5943 22.816 58.0547 23.2764 58.0547 24.1971V52.6163C58.0547 54.8474 57.3287 56.6357 55.8767 57.9814C54.4248 59.3271 52.5125 60 50.1398 60H48.5462ZM73.2026 22.816C74.1233 22.816 74.5837 23.2764 74.5837 24.1971V26.7469C74.5837 27.6676 74.1233 28.128 73.2026 28.128H70.4403V58.6189C70.4403 59.5396 69.98 60 69.0592 60H65.9783C65.0575 60 64.5971 59.5396 64.5971 58.6189V28.128H61.8349C60.9141 28.128 60.4538 27.6676 60.4538 26.7469V24.1971C60.4538 23.2764 60.9141 22.816 61.8349 22.816H73.2026Z"
                      fill="white"/>
            </svg>
        </a> --}}
        <a href="{{ url('/test-online') }}"
            class="transition-all hover:scale-105 h-full border-r border-white lg:border-r-0 lg:h-auto w-1/4 p-2 lg:p-0 cursor-pointer text-white hover:text-white lg:w-[100px] aspect-auto lg:aspect-square rounded-none lg:rounded-full bg-[#EC6E23] hover:bg-[#EC6E23] flex flex-col items-center justify-center gap-0">
            <svg width="39" height="40" viewBox="0 0 39 40" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="w-6 lg:w-[40px]">
                <path d="M17.937 39.1565C17.6546 39.1565 17.3735 39.0853 17.1255 38.9491C16.2949 38.4961 15.9641 37.5238 16.3394 36.6381L16.3445 36.6266C16.3699 36.5681 16.4068 36.479 16.4691 36.3798C16.6383 36.1125 16.6472 36.0298 16.6472 36.0158C16.6472 36.0158 16.637 35.9814 16.5836 35.914C16.244 35.4877 16.2224 34.9557 16.2058 34.5676V34.5269C16.1257 32.7224 16.0532 30.9573 16.0201 29.2113C16.0099 28.687 16.1333 28.1207 16.3572 27.6562C19.9506 20.209 23.7119 12.4437 27.8547 3.91742C28.7375 2.10144 30.6188 0.972656 32.7659 0.972656C33.2594 0.972656 33.7542 1.03374 34.2376 1.15464C36.7511 1.78456 38.5064 3.84742 38.7087 6.41168C38.7748 7.24395 38.5929 8.10167 38.1528 9.0332C37.9684 9.42516 37.7788 9.81457 37.5855 10.2142C37.499 10.3936 37.4112 10.5743 37.3222 10.7576L36.5463 12.3648L36.4801 12.4921C36.442 12.5646 36.4064 12.6333 36.372 12.7033L34.9754 15.5794C32.2342 21.2258 29.4931 26.871 26.757 32.52C26.4568 33.1398 26.0523 33.6272 25.5194 34.0102C24.0973 35.0359 22.6854 36.0769 21.4922 36.9613C20.9491 37.3647 20.3792 37.604 19.7585 37.6905C19.7343 37.7376 19.7102 37.7898 19.686 37.842C19.6262 37.9692 19.5588 38.1117 19.471 38.2581C19.1365 38.8206 18.5628 39.1565 17.9383 39.1565H17.937ZM19.6542 32.59C19.6707 33.0265 19.686 33.4541 19.7013 33.8804C20.0409 33.6297 20.3831 33.379 20.7239 33.1296C20.3563 32.9641 20.0015 32.7847 19.6542 32.5887V32.59ZM19.887 28.4261C21.0458 29.4569 22.3521 30.0932 23.8658 30.3681C25.5652 26.8456 27.2989 23.2734 28.9766 19.8171L30.3428 17.0021C30.5628 16.5491 30.7816 16.0948 31.0055 15.6341L27.0318 13.7112L25.7318 16.3913C23.7856 20.4037 21.8382 24.4162 19.887 28.4261ZM32.5586 12.4412C32.6769 12.1994 32.7952 11.9576 32.9135 11.7145C33.6003 10.3083 34.3101 8.85377 34.9843 7.41575C35.0898 7.1905 35.1522 6.89017 35.1496 6.61529C35.142 5.76775 34.5073 4.98257 33.5711 4.66315C33.2887 4.56643 33.0025 4.51808 32.7227 4.51808C31.9646 4.51808 31.3514 4.86549 31.0424 5.47251C30.3593 6.81 29.722 8.1373 29.0479 9.54224C28.8927 9.86547 28.7362 10.19 28.5798 10.5158L32.5598 12.4412H32.5586Z"
                      fill="white"/>
                <path d="M12.4021 39.3343C12.2508 39.3343 12.1007 39.3343 11.9493 39.3343H11.5677C10.3657 39.3343 9.08222 39.3013 7.79751 38.9233C6.18844 38.4499 5.18102 37.5947 4.71802 36.3094C4.34151 35.2621 4.51068 34.2695 4.69894 33.4219C5.02457 31.9559 5.6542 30.6375 6.22787 29.5405C6.70487 28.6281 7.1984 27.7067 7.6754 26.8159C8.24525 25.7495 8.83546 24.6474 9.40022 23.5581C9.97135 22.4561 10.3186 21.5105 10.4941 20.5815C10.7765 19.0875 10.241 17.9524 8.90033 17.2092C7.62834 16.5029 6.20116 16.186 4.84776 15.9481C4.53103 15.8921 4.21431 15.8374 3.89631 15.7826C3.22469 15.6656 2.55181 15.5485 1.88147 15.4263C0.916027 15.2507 0.266038 14.4133 0.370342 13.4793C0.468285 12.5999 1.2124 11.9102 2.06337 11.9102C2.15749 11.9102 2.25162 11.9191 2.34448 11.9343C2.83801 12.0221 3.34553 12.1023 3.83652 12.1799C5.83991 12.4968 7.91072 12.8239 9.86832 13.7274C11.7153 14.5788 12.9427 15.7585 13.6232 17.3326C14.2147 18.7007 14.2707 20.182 13.8026 21.9967C13.4095 23.5174 12.7265 24.8918 12.0014 26.2814C11.5677 27.1137 11.1289 27.9434 10.6888 28.7732C10.0718 29.9401 9.43329 31.1465 8.82019 32.3389C8.42206 33.1127 8.16257 33.9424 8.04937 34.8027C8.00103 35.1679 8.03283 35.2264 8.36228 35.3702C9.19162 35.7342 10.1303 35.7647 11.1238 35.7966H11.1607C11.3451 35.8042 11.5473 35.8067 11.7954 35.8067C11.948 35.8067 12.1019 35.8067 12.2546 35.8042C12.411 35.8042 12.5688 35.8016 12.7252 35.8016H12.9147C13.9947 35.8093 14.7846 36.5474 14.7897 37.5591C14.796 38.5708 14.0099 39.3153 12.9224 39.3318C12.7481 39.3343 12.5751 39.3356 12.4009 39.3356L12.4021 39.3343Z"
                      fill="white"/>
            </svg>

            <span class="hidden lg:flex font-beanbag uppercase">Test</span>
        </a>

        <a href="{{ url('/bang-gia') }}" onclick="ga('send', 'event', 'Right menu', 'click', 'Store right side')"
           class="transition-all hover:scale-105 h-full border-r border-white lg:border-r-0 lg:h-auto w-1/4 p-2 lg:p-0 cursor-pointer text-white hover:text-white lg:w-[100px] aspect-auto lg:aspect-square rounded-none lg:rounded-full bg-[#118C60] hover:bg-[#EC6E23] flex flex-col items-center justify-center gap-0">
            <svg width="48" height="42" viewBox="0 0 48 42" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="w-6 lg:w-[40px]">
                <path d="M41.646 36.0479H14.1906C12.0255 36.0479 10.2684 34.2907 10.2684 32.1257V30.1646C10.2684 29.7763 10.3821 29.3997 10.5979 29.0781L13.916 24.1008L6.34619 7.44717L5.08324 4.67417H2.42399C1.34146 4.67025 0.462891 3.79168 0.462891 2.70915C0.462891 1.62662 1.34146 0.748047 2.42399 0.748047H6.34619C7.11495 0.748047 7.8131 1.1991 8.1308 1.89725L9.39375 4.67025H43.6071C45.7722 4.67025 47.5293 6.4274 47.5293 8.59245C47.5293 8.89838 47.4587 9.19647 47.3214 9.47102L39.477 25.1598C39.1437 25.8227 38.4651 26.2424 37.7238 26.2424H17.2029L14.1906 30.7568V32.1257H41.646C42.7285 32.1257 43.6071 33.0042 43.6071 34.0868C43.6071 35.1693 42.7285 36.0479 41.646 36.0479ZM17.4146 22.3202H36.5119L43.3718 8.59637H11.1744L17.4146 22.3202Z"
                      fill="white"/>
                <path d="M37.7269 41.5925C39.8931 41.5925 41.6491 39.8364 41.6491 37.6702C41.6491 35.5041 39.8931 33.748 37.7269 33.748C35.5607 33.748 33.8047 35.5041 33.8047 37.6702C33.8047 39.8364 35.5607 41.5925 37.7269 41.5925Z"
                      fill="white"/>
                <path d="M14.1917 41.5925C16.3579 41.5925 18.1139 39.8364 18.1139 37.6702C18.1139 35.5041 16.3579 33.748 14.1917 33.748C12.0256 33.748 10.2695 35.5041 10.2695 37.6702C10.2695 39.8364 12.0256 41.5925 14.1917 41.5925Z"
                      fill="white"/>
            </svg>

            <span class="hidden lg:flex font-beanbag uppercase text-xs">Cửa hàng</span>
        </a>

        {{-- chatbox --}}
        <div class="transition-all hover:scale-105 h-full border-r border-white lg:border-r-0 lg:h-auto group cursor-pointer text-white w-1/4 lg:w-[100px] aspect-auto lg:aspect-square rounded-none lg:rounded-full bg-[#118C60] hover:bg-[#EC6E23] flex flex-col items-center justify-center gap-2"
             onclick="ga('send', 'event', 'Right menu', 'click', 'Chat right side'); showChatbox()">
            <svg width="42" height="38" viewBox="0 0 42 38" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="w-6 lg:w-[40px]">
                <path d="M21.0298 0.00783166C9.64407 0.00783166 0.380859 8.38874 0.380859 18.6888C0.380859 28.9889 9.64407 37.3698 21.0298 37.3698C24.9127 37.3698 28.6641 36.4014 31.9383 34.5527L38.9803 37.2466C39.2107 37.3346 39.441 37.3698 39.6549 37.3698C40.1979 37.3698 40.7408 37.1233 41.1357 36.6831C41.6951 36.0493 41.8432 35.1337 41.5306 34.3414L38.9968 27.9149C40.7737 25.0978 41.6951 21.9285 41.6951 18.6712C41.6951 8.37115 32.4319 -0.00976562 21.0462 -0.00976562L21.0298 0.00783166ZM34.7847 28.4959L35.9694 31.5067L32.4154 30.151C31.856 29.9397 31.2308 29.9925 30.7043 30.327C27.8579 32.1053 24.5014 33.0385 21.0133 33.0385C11.8653 33.0385 4.42838 26.6119 4.42838 18.6888C4.42838 10.7657 11.8653 4.33915 21.0133 4.33915C30.1613 4.33915 37.5982 10.7657 37.5982 18.6888C37.5982 21.3827 36.7098 24.0237 35.048 26.3126C34.5873 26.9289 34.4886 27.774 34.7847 28.4959ZM13.7409 17.157C14.1194 17.562 14.3333 18.1254 14.3333 18.6888C14.3333 18.8297 14.3168 18.9705 14.3004 19.1114C14.2675 19.2522 14.2346 19.3931 14.1852 19.5163C14.1358 19.6396 14.07 19.7629 13.9877 19.8861C13.9219 20.0093 13.8232 20.115 13.7409 20.2206C13.6422 20.3263 13.5435 20.4143 13.4283 20.4847C13.3132 20.5728 13.198 20.6256 13.0828 20.6784C12.9676 20.7312 12.836 20.7841 12.7044 20.8017C12.5728 20.8369 12.4411 20.8369 12.3095 20.8369C11.783 20.8369 11.2565 20.608 10.8781 20.203C10.7794 20.0974 10.6971 19.9918 10.6313 19.8685C10.5655 19.7453 10.4996 19.622 10.4503 19.4987C10.4009 19.3755 10.368 19.2346 10.3351 19.0938C10.3187 18.9529 10.3022 18.8121 10.3022 18.6712C10.3022 18.5304 10.3187 18.3895 10.3351 18.2486C10.368 18.1078 10.4009 17.9669 10.4503 17.8437C10.4996 17.7204 10.5655 17.5972 10.6313 17.474C10.7135 17.3507 10.7958 17.2451 10.8781 17.1394C11.3552 16.6288 12.0463 16.3999 12.7044 16.5408C12.836 16.5584 12.9512 16.6112 13.0828 16.664C13.198 16.7168 13.3296 16.7873 13.4283 16.8577C13.5435 16.9457 13.6422 17.0338 13.7409 17.1218V17.157ZM23.0042 18.2663C23.0206 18.4071 23.0371 18.548 23.0371 18.6888C23.0371 18.8297 23.0371 18.9705 23.0042 19.1114C22.9712 19.2522 22.9383 19.3931 22.889 19.5163C22.8396 19.6396 22.7738 19.7629 22.6915 19.8861C22.6257 20.0093 22.527 20.115 22.4447 20.2206C22.0663 20.6256 21.5398 20.8545 21.0133 20.8545C20.8817 20.8545 20.7501 20.8545 20.6184 20.8193C20.4868 20.8017 20.3716 20.7488 20.24 20.696C20.1248 20.6432 20.0097 20.5728 19.8945 20.5024C19.7793 20.4143 19.6806 20.3439 19.5819 20.2382C19.4832 20.1326 19.4009 20.0269 19.3351 19.9037C19.2693 19.7804 19.2034 19.6572 19.1541 19.534C19.1047 19.4107 19.0718 19.2698 19.0389 19.129C19.0225 18.9881 19.006 18.8473 19.006 18.7064C19.006 18.5656 19.0225 18.4247 19.0389 18.2839C19.0718 18.143 19.1047 18.0022 19.1541 17.8789C19.2034 17.7557 19.2693 17.6324 19.3351 17.5092C19.4173 17.3859 19.4996 17.2803 19.5819 17.1746C19.6806 17.069 19.7793 16.9809 19.8945 16.9105C20.0097 16.8225 20.1248 16.7697 20.24 16.7168C20.3716 16.664 20.4868 16.6112 20.6184 16.5936C21.2766 16.4528 21.9841 16.6816 22.4447 17.1922C22.5435 17.2979 22.6257 17.4035 22.6915 17.5268C22.7738 17.65 22.8232 17.7733 22.889 17.8965C22.9383 18.0198 22.9712 18.1606 23.0042 18.3015V18.2663ZM31.7244 18.2663C31.7409 18.4071 31.7573 18.548 31.7573 18.6888C31.7573 18.8297 31.7573 18.9705 31.7244 19.1114C31.6915 19.2522 31.6586 19.3931 31.6092 19.5163C31.5599 19.6396 31.4941 19.7629 31.4118 19.8861C31.346 20.0093 31.2473 20.115 31.165 20.2206C31.0663 20.3263 30.9676 20.4143 30.8524 20.4847C30.7372 20.5728 30.622 20.6256 30.5069 20.6784C30.3917 20.7312 30.2601 20.7841 30.1284 20.8017C29.9968 20.8369 29.8652 20.8369 29.7336 20.8369C29.2071 20.8369 28.6805 20.608 28.3021 20.203C28.2034 20.0974 28.1211 19.9918 28.0553 19.8685C27.9895 19.7453 27.9237 19.622 27.8743 19.4987C27.825 19.3755 27.7921 19.2346 27.7592 19.0938C27.7427 18.9529 27.7263 18.8121 27.7263 18.6712C27.7263 18.5304 27.7427 18.3895 27.7592 18.2486C27.7921 18.1078 27.825 17.9669 27.8743 17.8437C27.9237 17.7204 27.9895 17.5972 28.0553 17.474C28.1376 17.3507 28.2199 17.2451 28.3021 17.1394C28.7793 16.6288 29.4703 16.3999 30.1284 16.5408C30.2601 16.5584 30.3752 16.6112 30.5069 16.664C30.622 16.7168 30.7537 16.7873 30.8524 16.8577C30.9676 16.9457 31.0663 17.0338 31.165 17.1218C31.2637 17.2274 31.346 17.3331 31.4118 17.4563C31.4941 17.5796 31.5434 17.7028 31.6092 17.8261C31.6586 17.9493 31.6915 18.0902 31.7244 18.231V18.2663Z"
                      fill="white"/>
            </svg>
            <span class="hidden lg:flex font-beanbag uppercase">@if (Auth::check())
                    Hỗ trợ
                @else
                    Tư vấn
                @endif</span>
        </div>
        <div class="transition-all hover:scale-105 h-full lg:h-auto group w-1/4 lg:w-full flex flex-col items-center bg-[#118C60] text-white px-4 py-3 rounded-none lg:rounded-full relative cursor-pointer hover:bg-[#EC6E23]"
             onclick="scrollToId('app-btns')">
            <svg width="60" height="25" viewBox="0 0 60 25" fill="none" xmlns="http://www.w3.org/2000/svg"
                 class="w-6 lg:w-[40px]">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M1.64282 21.0466C-0.63225 18.3781 -0.313328 14.3705 2.35516 12.0955L13.9513 2.20894C16.6198 -0.066139 20.6273 0.252786 22.9024 2.92127L40.4784 23.5366C42.7535 26.2051 42.4346 30.2126 39.7661 32.4877L28.17 42.3742C25.5015 44.6493 21.4939 44.3304 19.2189 41.6619L1.64282 21.0466ZM5.10141 15.3166C4.21192 16.075 4.10561 17.4108 4.86397 18.3003L22.44 38.9156C23.1984 39.8051 24.5342 39.9114 25.4237 39.1531L37.0198 29.2666C37.9093 28.5082 38.0156 27.1724 37.2573 26.2829L19.6812 5.66753C18.9229 4.77803 17.587 4.67172 16.6975 5.43008L5.10141 15.3166Z"
                      fill="white"/>
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M9.73446 17.4885C8.9761 16.599 9.08241 15.2632 9.9719 14.5048L15.1257 10.1108C16.0152 9.35243 17.3511 9.45874 18.1094 10.3482C18.8678 11.2377 18.7615 12.5736 17.872 13.3319L12.7182 17.7259C11.8287 18.4843 10.4928 18.378 9.73446 17.4885Z"
                      fill="white"/>
                <path d="M42.7754 13.3431L45.2346 6.94922M47.6938 19.2451L57.5305 10.8839" stroke="white"
                      stroke-width="4" stroke-linecap="round"/>
            </svg>
            <div class="hidden lg:flex absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-0 h-0 border-l-[10px] border-l-transparent border-t-[20px] border-t-[#118C60] border-r-[10px] border-r-transparent group-hover:border-t-[#EC6E23]"></div>
            <span class="hidden lg:flex font-beanbag">TẢI ỨNG DỤNG</span>
        </div>
        {{-- @if (Auth::check() && Auth::user()->giftCondition()) --}}
        {{-- @if(Auth::check() && (\Carbon\Carbon::now()->isAfter(\Carbon\Carbon::parse('2025-01-31')) || Auth::user()->id === 349661))
           <a class="cursor-pointer" data-fancybox data-animation-duration="300" data-src="#jlpt-gift-container" id="link-jlpt-gift-container"
              data-options='{"touch" : false}'>
               <div class="call-btn">
                   <img class="lazyload object-cover" data-src="{{ asset('/assets/img/mori-gift-icon.gif') }}"/>
                   <p style="text-align: center; font-size: 21px; font-weight: bold; font-family: Beanbag_Dungmori_Rounded,arial">
                       Quà JLPT</p>
               </div>
           </a>
       @endif --}}

        <img data-src="{{ asset('/assets/img/new_home/06-2024/mori-kun.svg') }}"
             class="hidden lg:block fixed-panel-icon pc lazyload object-cover relative z-[2]"/>
        @if (Auth::check())
            <div class="fixed-panel-board" id="fixed-panel-board" style="display: block;">
                <i class="fa fa-times" id="hide-fixed-panel-board"></i>
                <img src="{{ asset('assets/img/new_home/jlpt-gift-2.png') }}"/>
                <div style="width: 200px">
                    <p style="color: #FB6D3A; font-size: 16px; font-weight: 700;">Báo điểm nhận quà</p>
                    <p class="mt-2">Dù trượt hay đỗ JLPT thì DUNGMORI đều có quà gửi các bạn!</p>
                    <a data-fancybox data-animation-duration="300" data-src="#jlpt-gift-container" id="jlpt-gift-toggle"
                       class="button"
                       style="display:block;cursor:pointer;text-align:center;width:100%;border:none;margin-top:10px;background:#96D962;padding:7px 18px;border-radius:30px;font-family:Quicksand;color:#fff;"
                    >Gửi điểm ngay</a>
                </div>
            </div>
        @endif
    </div>

    @include('frontend.home.survey')

    {{-- tắt truy cập bằng mobile --}}
    <div class="download-store" id="download-store">
        <h2>DUNGMORI hiện đã có <br/>trên kho ứng dụng</h2>
        <p>Vui lòng tải app và sử dụng<br/>Để có được trải nghiệm tốt nhất</p>
        <div class="mobile-app">
            <div class="hidden border-amber-500"></div>
            <a href="https://apps.apple.com/us/app/id1486123836" target="_blank" class="mobile-app-button">
                <img class="lazyload object-cover" data-src="{{asset('assets/img/dungmori_ios.png')}}" alt=""/>
            </a>
            <a href=" https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp" target="_blank"
               class="mobile-app-button">
                <img class="lazyload object-cover" data-src="{{asset('assets/img/dungmori_android.png')}}" alt=""/>
            </a>
        </div>
    </div>

    {{--    <div class="modal fade" id="chat_modal" tabindex="-1" aria-labelledby="chat_modal_label" aria-hidden="true">--}}
    {{--        <div class="modal-dialog modal-xl modal-dialog-scrollable">--}}
    {{--            <div class="modal-content" style="position: relative">--}}
    {{--                <div style="position: absolute; opacity: 1; top: -15px; right: -15px; cursor: pointer" type="button"--}}
    {{--                     class="close" data-dismiss="modal" aria-label="Close">--}}
    {{--                    <svg width="36px" height="36px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
    {{--                        <g id="style=bulk">--}}
    {{--                            <g id="close-circle">--}}
    {{--                                <path id="vector (Stroke)" fill-rule="evenodd" clip-rule="evenodd"--}}
    {{--                                      d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12Z"--}}
    {{--                                      fill="#BFBFBF"/>--}}
    {{--                                <path id="vector (Stroke)_2" fill-rule="evenodd" clip-rule="evenodd"--}}
    {{--                                      d="M8.46967 8.46967C8.76257 8.17678 9.23744 8.17678 9.53033 8.46967L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7625 15.8232 14.4696 15.5303L8.46967 9.53033C8.17678 9.23743 8.17678 8.76256 8.46967 8.46967Z"--}}
    {{--                                      fill="#000000"/>--}}
    {{--                                <path id="vector (Stroke)_3" fill-rule="evenodd" clip-rule="evenodd"--}}
    {{--                                      d="M15.5303 8.46967C15.8232 8.76257 15.8232 9.23744 15.5303 9.53033L9.53033 15.5303C9.23743 15.8232 8.76256 15.8232 8.46967 15.5303C8.17678 15.2374 8.17678 14.7625 8.46967 14.4696L14.4697 8.46967C14.7626 8.17678 15.2374 8.17678 15.5303 8.46967Z"--}}
    {{--                                      fill="#000000"/>--}}
    {{--                            </g>--}}
    {{--                        </g>--}}
    {{--                    </svg>--}}
    {{--                </div>--}}
    {{--                <div class="modal-body" id="chat_modal_body">--}}
    {{--                    <img style="cursor: pointer" onclick="openChatBox()" class="w-full"--}}
    {{--                         src="/images/stop_responding_to_messages.png">--}}
    {{--                </div>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}



    <!-- Popup Modal Promotion-->
{{--    <div id="modalPromotion" class="modal fade" tabindex="-1" role="dialog" style="padding-right: 0">--}}
{{--        <div class="modal-dialog" role="document">--}}
{{--            <div class="modal-content"--}}
{{--                 style='background-image: url("{{ asset('/images/lessons/bg-popup-promotion-new.png') }}"); box-shadow: none'>--}}
{{--                <div class="modal-body">--}}
{{--                    <div class="">--}}
{{--                        <div class="flex justify-end m-[5px]">--}}
{{--                            <svg class="cursor-pointer" data-dismiss="modal" aria-label="Close" width="21"--}}
{{--                                 height="21" viewBox="0 0 21 21" fill="white"--}}
{{--                                 xmlns="http://www.w3.org/2000/svg">--}}
{{--                                <path d="M8.1337 7L13.7922 1.34154C13.9305 1.18886 14.0048 0.988831 13.9998 0.782863C13.9947 0.576896 13.9106 0.380767 13.7649 0.235082C13.6192 0.0893969 13.4231 0.00531458 13.2171 0.00024342C13.0112 -0.00482774 12.8111 0.0695006 12.6585 0.207839L7 5.8663L1.34154 0.207839C1.18886 0.0695006 0.988831 -0.00482774 0.782863 0.00024342C0.576896 0.00531458 0.380767 0.0893969 0.235082 0.235082C0.0893969 0.380767 0.00531458 0.576896 0.00024342 0.782863C-0.00482774 0.988831 0.0695006 1.18886 0.207839 1.34154L5.8663 7L0.207839 12.6585C0.0695006 12.8111 -0.00482774 13.0112 0.00024342 13.2171C0.00531458 13.4231 0.0893969 13.6192 0.235082 13.7649C0.380767 13.9106 0.576896 13.9947 0.782863 13.9998C0.988831 14.0048 1.18886 13.9305 1.34154 13.7922L7 8.1337L12.6585 13.7922C12.8111 13.9305 13.0112 14.0048 13.2171 13.9998C13.4231 13.9947 13.6192 13.9106 13.7649 13.7649C13.9106 13.6192 13.9947 13.4231 13.9998 13.2171C14.0048 13.0112 13.9305 12.8111 13.7922 12.6585L8.1337 7Z"--}}
{{--                                      fill="#ffffff"/>--}}
{{--                            </svg>--}}
{{--                        </div>--}}
{{--                        <div class="img-main mb-[14px] sm:mb-[41px] text-center">--}}
{{--                            <img class="sm:h-full" src="/images/lessons/svg/lesson-promotion-popup.svg">--}}
{{--                        </div>--}}
{{--                        <div class="md:flex mb-[17px] text-center items-baseline justify-center">--}}
{{--                            <div class="text-[#EF6D13] font-beanbag text-[30px] md:text-[40px] leading-none">Ưu đãi tới--}}
{{--                                30%--}}
{{--                            </div>--}}
{{--                            <div class="px-[8px] hidden md:flex">--}}
{{--                                <svg width="26" height="16" viewBox="0 0 26 16" fill="none"--}}
{{--                                     xmlns="http://www.w3.org/2000/svg">--}}
{{--                                    <path d="M1.5 7C0.947715 7 0.5 7.44772 0.5 8C0.5 8.55228 0.947715 9 1.5 9V7ZM25.7071 8.70711C26.0976 8.31658 26.0976 7.68342 25.7071 7.29289L19.3431 0.928932C18.9526 0.538408 18.3195 0.538408 17.9289 0.928932C17.5384 1.31946 17.5384 1.95262 17.9289 2.34315L23.5858 8L17.9289 13.6569C17.5384 14.0474 17.5384 14.6805 17.9289 15.0711C18.3195 15.4616 18.9526 15.4616 19.3431 15.0711L25.7071 8.70711ZM1.5 9H25V7H1.5V9Z"--}}
{{--                                          fill="#07403F"/>--}}
{{--                                </svg>--}}
{{--                            </div>--}}
{{--                            <div class="text-[#EF6D13] font-beanbag text-[20px] md:text-[23px]  leading-none">--}}
{{--                                TRONG HÔM NAY--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="mb-[22px] max-w-[600px] mx-auto bg-[#F4F5FA] rounded-[24px] border-[1px] border-[#D0D3DA] grid-cols-1 divide-y divide-[#D0D3DA]">--}}
{{--                            <div class="flex pl-[20px] pt-[24px] pb-[16px] pr-[12px] items-center">--}}
{{--                                <div class="w-[28px]">--}}
{{--                                    <svg class="w-[28px]" width="28" height="28" viewBox="0 0 28 28" fill="none"--}}
{{--                                         xmlns="http://www.w3.org/2000/svg">--}}
{{--                                        <circle cx="14" cy="14" r="14" fill="#57D061"/>--}}
{{--                                        <path d="M19.3333 10L12 17.3333L8.66663 14" stroke="white"--}}
{{--                                              stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                    </svg>--}}
{{--                                </div>--}}
{{--                                <div class="text-[14px] text-[#07403F] ml-[12px]">--}}
{{--                                    <span class="font-averta-regular">--}}
{{--                                        Mở khóa toàn bộ--}}
{{--                                    </span>--}}
{{--                                    <span class="font-averta-bold font-bold">350+ bài giảng</span>--}}
{{--                                    <span class="font-averta-regular">--}}
{{--                                        “độc quyền” từ DUNGMORI với giáo trình giúp bạn--}}
{{--                                    </span>--}}
{{--                                    <span class="font-averta-bold font-bold">VỮNG KIẾN THỨC - CHUẨN PHÁT ÂM!</span>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            <div class="flex pl-[20px] py-[16px]  items-center">--}}
{{--                                <div class="w-[28px]">--}}
{{--                                    <svg class="w-[28px]" width="28" height="28" viewBox="0 0 28 28" fill="none"--}}
{{--                                         xmlns="http://www.w3.org/2000/svg">--}}
{{--                                        <circle cx="14" cy="14" r="14" fill="#57D061"/>--}}
{{--                                        <path d="M19.3333 10L12 17.3333L8.66663 14" stroke="white"--}}
{{--                                              stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                    </svg>--}}
{{--                                </div>--}}
{{--                                <div class="text-[14px] text-[#07403F] ml-[12px]">--}}
{{--                                    <span class="font-averta-regular">--}}
{{--                                        Truy cập--}}
{{--                                    </span>--}}
{{--                                    <span class="font-averta-bold font-bold">không giới hạn</span>--}}
{{--                                    <span class="font-averta-regular">--}}
{{--                                        trong 6 tháng!--}}
{{--                                    </span>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            <div class="flex  pl-[20px] pt-[16px] pb-[24px]  pr-[12px] items-center">--}}
{{--                                <div class="w-[28px]">--}}
{{--                                    <svg class="w-[28px]" width="28" height="28" viewBox="0 0 28 28" fill="none"--}}
{{--                                         xmlns="http://www.w3.org/2000/svg">--}}
{{--                                        <circle cx="14" cy="14" r="14" fill="#57D061"/>--}}
{{--                                        <path d="M19.3333 10L12 17.3333L8.66663 14" stroke="white"--}}
{{--                                              stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                    </svg>--}}
{{--                                </div>--}}
{{--                                <div class="text-[14px] text-[#07403F] ml-[12px]">--}}
{{--                                    <span class="font-averta-regular">--}}
{{--                                        8 dạng bài tập “mới toanh” khắc phục mọi điểm yếu trong--}}
{{--                                    </span>--}}
{{--                                    <span class="font-averta-bold font-bold">Nghe - Nói - Đọc - Viết</span>--}}
{{--                                    <span class="font-averta-regular">--}}
{{--                                        tiếng Nhật!--}}
{{--                                    </span>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="text-center mb-[45px]">--}}
{{--                            <a id="link_promotion" href="https://google.com"--}}
{{--                               class="shadow-orange-500 shadow-lg rounded-full bg-[#EF6D13] py-[12px] sp:px-[50px] md:px-[100px] font-beanbag text-white sp:text-[16px] md:text-[20px] hover:text-white">--}}
{{--                                Nhận ưu đãi--}}
{{--                            </a>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}

    <!-- Popup Modal Achievement-->
    <div id="modalAchievement" class="modal fade" tabindex="-1" role="dialog" style="padding-right: 0">
        <div class="modal-dialog" role="document">
            <div class="modal-content rounded-[24px]" style="border-radius: 24px">
                <div class="modal-body">
                    <div class="text-center">
                        <div class="stamp">
                            <img id="img-achievement-stamp" src="{{ asset('images/lessons/svg/stamp.svg') }}" alt="">
                        </div>
                        <div class="title">
                            <div class="text-[#57D061] font-beanbag text-[20px] leading-1 mb-[21px]">
                                Chúc mừng bạn đã
                            </div>
                            <div id="text-achievement-popup"
                                 class="text-[#07403F] font-zuume-semibold text-[48px] uppercase leading-[51px] mb-[26px]">

                            </div>
                        </div>
                        <div class="border-b-[1px] border-[#D0D3DA] max-w-[176px] mx-auto"></div>
                        <div class="content mt-[24px]">
                            <div id="text-top-detail-popup"
                                 class="text max-w-[241px] text-[14px] text-[#1E1E1E] font-averta-regular mx-auto leading-[17px] mb-[14px]">
                                Tiếp tục chinh phục tiếng Nhật cùng DUNGMORI thôi nào!
                            </div>
                            <div class="flex justify-center">
                                <img id="img-achievement-popup"
                                     src="{{ asset('images/lessons/svg/achievement/first_exercise.svg') }}">
                            </div>
                            <div id="text-bottom-detail-popup"
                                 class="text max-w-[241px] text-[14px] text-[#1E1E1E] font-averta-regular mx-auto leading-[17px] my-[14px]">
                                Một bước gần hơn tới ước mơ!
                            </div>
                        </div>
{{--                        @if(session()->get('show_btn_next_lesson'))--}}
{{--                            <div class="bottom mb-[36px] flex justify-around">--}}
{{--                                @if (isset($nextLessonCurrentGroup))--}}
{{--                                    <a--}}
{{--                                            href="/khoa-hoc/{{ $course->SEOurl }}/lesson/{{ $nextLessonCurrentGroup->id }}-{{ $nextLessonCurrentGroup->SEOurl }}"--}}
{{--                                            class="font-beanbag text-[#FFFFFF] hover:text-[#FFFFFF] text-[20px] bg-[#57D061] py-[12px] px-[80px] rounded-full shadow-2xl">--}}
{{--                                        Đi tiếp--}}
{{--                                    </a>--}}
{{--                                @endif--}}
{{--                                @if(isset($courseOwner) && $courseOwner && $courseOwner->code_active_at && \Carbon\Carbon::now()->lessThanOrEqualTo(\Carbon\Carbon::parse($courseOwner->watch_expired_day)->addDay()) && \Carbon\Carbon::parse($courseOwner->watch_expired_day)->lessThanOrEqualTo(\Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()))--}}
{{--                                    <a--}}
{{--                                            href="https://google.com"--}}
{{--                                            class="font-beanbag text-[#FFFFFF] hover:text-[#FFFFFF] text-[20px] bg-[#EF6D13] py-[12px] px-[80px] rounded-full shadow-2xl">--}}
{{--                                        Nhận ưu đãi--}}
{{--                                    </a>--}}
{{--                                @endif--}}
{{--                            </div>--}}
{{--                        @endif--}}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="modalEvent" class="modal fade" tabindex="-1" role="dialog" style="padding-right: 0">
        <div class="modal-dialog" style="display: flex; align-items: center; justify-content: center; height: 90%;" role="document">
            <div class="">
                <div class="modal-body" style="padding: 0">
                    <div class="text-center relative">
                        <a href="https://m.me/1595926847401625?ref=2441582">
                            <img style="max-height: 70vh" src="{{ asset('/images/event/popup-new-course.jpg') }}" alt="">
                        </a>
                        <img src="{{ asset('assets/img/birthday/thoat-pop-up-chuc-mung-sinh-nhat-dungmori.png') }}"
                             alt="" class="absolute top-[-23px] right-[-23px] cursor-pointer w-[46px] h-[46px]"
                             data-dismiss="modal" aria-label="Close">
                    </div>
                </div>
            </div>
        </div>
    </div>


    {{--    kiểm tra route hiện tại có phải dạng */khoa-hoc/* không --}}
    @if (!\Illuminate\Support\Facades\Request::is('khoa-hoc/*'))
        <div class="footer-promotion fixed bottom-[115px] md:bottom-[0] w-full z-[100]">
            <div class="max-w-[1170px] md:p-[13px] mx-auto text-center">
                <div
                        class="flex flex-wrap items-center md:gap-4 pb-[5px] pt-[5px] md:pt-[13px] px-[12px] md:p-[11px] md:rounded-[10.87px] shadow-[0px_0px_14px_0px_rgba(236,110,35,0.5)] relative"
                        style="background: url('{{ asset('/images/course/bg-banned-promotion.svg') }}'), linear-gradient(90deg,rgba(239, 109, 19, 1) 0%, rgba(226, 35, 35, 1) 50%);
                        background-position: 30%;
                        background-repeat: no-repeat;
                        background-attachment: fixed;"
                >
                    <div class="flex-1 md:flex-1 md:h-full h-[30px]">
                        <img src="{{ asset('/images/course/trail-free-course.svg') }}" alt="trail-free-course"
                             class="w-full h-full">
                    </div>
                    <div class="list-promotion md:flex-[0_0_50%] w-full md:w-auto order-2 md:order-1">
                        <div class="flex flex-wrap gap-4 items-center justify-center h-full">
                            <a
                                    @if(!\Illuminate\Support\Facades\Auth::check())
                                        data-fancybox data-animation-duration="300" data-src="#auth-container"
                                    onclick="swichTab('login', '/khoa-hoc/chu-han-n5')"
                                    @else
                                        href="/khoa-hoc/chu-han-n5"
                                        onclick="eventClickBanner('n5')"
                                    @endif
                                    class="cursor-pointer promotion-course-item bg-white rounded-[24px] px-[10px] py-[5px] font-zuume-semibold text-[#07403F] text-[20px] md:text-[30px] lg:text-[36px] shadow-[0px_0px_10px_0px_rgba(0,0,0,0.1)] hover:bg-[#FFE53A] hover:text-[#07403F]">
                                Kanji N5
                            </a>
                            <a
                                    @if(!\Illuminate\Support\Facades\Auth::check())
                                        data-fancybox data-animation-duration="300" data-src="#auth-container"
                                    onclick="swichTab('login', '/khoa-hoc/jlpt-n3')"
                                    @else
                                        onclick="eventClickBanner('n3')"
                                        href="/khoa-hoc/jlpt-n3"
                                    @endif
                                    class="cursor-pointer promotion-course-item bg-white rounded-[24px] px-[10px] py-[5px] font-zuume-semibold text-[#07403F] text-[20px] md:text-[30px] lg:text-[36px] shadow-[0px_0px_10px_0px_rgba(0,0,0,0.1)] hover:bg-[#FFE53A] hover:text-[#07403F]">
                                JLPT N3
                            </a>
                            <a
                                    @if(!\Illuminate\Support\Facades\Auth::check())
                                        data-fancybox data-animation-duration="300" data-src="#auth-container"
                                    onclick="swichTab('login', '/khoa-hoc/jlpt-n2')"
                                    @else
                                        onclick="eventClickBanner('n2')"
                                        href="/khoa-hoc/jlpt-n2"
                                    @endif
                                    class="cursor-pointer promotion-course-item bg-white rounded-[24px] px-[10px] py-[5px] font-zuume-semibold text-[#07403F] text-[20px] md:text-[30px] lg:text-[36px] shadow-[0px_0px_10px_0px_rgba(0,0,0,0.1)] hover:bg-[#FFE53A] hover:text-[#07403F]">
                                JLPT N2
                            </a>
                            <a
                                    @if(!\Illuminate\Support\Facades\Auth::check())
                                        data-fancybox data-animation-duration="300" data-src="#auth-container"
                                    onclick="swichTab('login', '/khoa-hoc/jlpt-n1')"
                                    @else
                                        onclick="eventClickBanner('n1')"
                                        href="/khoa-hoc/jlpt-n1"
                                    @endif
                                    class="cursor-pointer promotion-course-item bg-white rounded-[24px] px-[10px] py-[5px] font-zuume-semibold text-[#07403F] text-[20px] md:text-[30px] lg:text-[36px] shadow-[0px_0px_10px_0px_rgba(0,0,0,0.1)] hover:bg-[#FFE53A] hover:text-[#07403F]">
                                JLPT N1
                            </a>
                        </div>
                    </div>
                    <div class="flex-1 md:flex-1 order-1 md:order-0">
                        <!-- <div class=""> -->
                        <div class="group-promotion-time leading-1 mr-[35px] justify-items-center">
                            <div class="time-active flex md:flex-col">
                                <div class="text-[8px] leading-[26px] text-bold flex justify-end text-white md:text-[20px] md:leading-[20px] lg:text-[26px] lg:leading-[26px]">
                                    Ưu đãi còn
                                </div>
                                <div class="flex time-active-group sp:items-center sp:justify-center">
                                    <svg class="w-[14px] h-[14px] md:w-[32px] md:h-[32px] lg:w-[44px] lg:h-[44px]"
                                         width="24" height="24"
                                         viewBox="0 0 24 24" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20.75 13.2568C20.75 18.0868 16.83 22.0068 12 22.0068C7.17 22.0068 3.25 18.0868 3.25 13.2568C3.25 8.42684 7.17 4.50684 12 4.50684C16.83 4.50684 20.75 8.42684 20.75 13.2568Z"
                                              stroke="#FFE53A" stroke-width="1.81666" stroke-linecap="round"
                                              stroke-linejoin="round"/>
                                        <path d="M12 8.00391V13.0039" stroke="#FFE53A" stroke-width="1.81666"
                                              stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8.99951 1.99902H14.9995" stroke="#FFE53A" stroke-width="1.81666"
                                              stroke-miterlimit="10" stroke-linecap="round"
                                              stroke-linejoin="round"/>
                                    </svg>
                                    <div id="countdown-timer"
                                         class="ml-[8px] text-[14px] leading-1 md:text-[32px] md:leading-[32px] lg:text-[44px] lg:leading-[44px] text-bold text-[#FFE53A]">
                                        00:00:00
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- </div> -->
                    </div>
                    <div
                            class="absolute top-[-13px] right-[13px] md:top-[10px] btn-close-promotion w-[27px] h-[27px] rounded-full bg-[#B3B3B3] flex items-center justify-center cursor-pointer ml-3"
                            onclick="closePromotion()"
                    >
                        <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.43359 1.43945L11.968 11.9739" stroke="white" stroke-width="1.6875"
                                  stroke-linecap="round"/>
                            <path d="M11.9727 1.43945L1.43823 11.9739" stroke="white" stroke-width="1.6875"
                                  stroke-linecap="round"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{--    {{dd(Auth::user()->toArray()) }}--}}
    <!-- Popup Modal Form survey new course-->
    @if(\Illuminate\Support\Facades\Auth::check() && \Illuminate\Support\Facades\Auth::user()->created_at > '2025-07-01 00:00:00'))
        <div id="modalFormSurveyNewCourse" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static"
             data-keyboard="false"
        >
            <div style="margin: 0 auto;"
                 class="modal-dialog modal-dialog-centered modal-dialog-scrollable flex justify-center items-center w-full h-full"
                 role="document">
                <div class="modal-content rounded-[32px]"
                     style="background-color: #ffffff00; border: none; box-shadow: none; border-radius: none;">
                    <div class="flex justify-between items-center w-[90%] mx-auto">
                        <img src="{{ asset('/images/course/img-form-survey-new-course.svg') }}" alt="img"
                             class="cursor-pointer">
                        <div
                                class="union-form-survey-course min-h-[126px] flex flex-col md:block items-center justify-center font-beanbag-medium text-[12px] sm:text-[14px] md:text-base text-[#07403F] text-center w-2/3 md:p-[30px] sm:px-[125px] pr-[32px] pl-[36px]"
                                style="background-size: contain; background-position: center; background-repeat: no-repeat; background-attachment: fixed;"
                        >
                            <div class="text-center mt-[10px]">
                                Chào bạn thân iu!
                            </div>
                            <div class="text-center mb-[10px]">
                                Sắp tới hãy cùng nhau học tập thật tốt nào!!!
                            </div>
                        </div>
                    </div>
                    <div class="modal-body bg-white rounded-[32px] max-w-[600px]">
                        <form id="form-survey-new-course" action="{{ route('user.update-info') }}" method="POST">
                            @csrf
                            <div class="form-survey-new-course">
                                <div class="form-survey-new-course__title font-beanbag-medium text-xl text-[#07403F]">
                                    <p class="break-words">
                                        Để hỗ trợ bạn được tốt
                                        nhất, {{ \Illuminate\Support\Facades\Auth::user()->name }}
                                        vui lòng cho chúng mình biết thêm một vài
                                        thông tin nha! <span><3</span>
                                    </p>
                                </div>
                                <div class="form-survey-new-course__form">
                                    <div class="form-survey-new-course__form__item">
                                        <select id="address" name="address" class="w-full">
                                            <option value="" disabled selected></option>
                                            <option value="230">Việt Nam</option>
                                            <option value="107">Nhật Bản</option>
                                            <option value="1">Khác</option>
                                        </select>
                                        <label for="address" class="font-beanbag-medium">Địa chỉ</label>
                                    </div>
                                    <div class="md:flex md:gap-4">
                                        <div class="form-survey-new-course__form__item">
                                            <input type="number" id="age" name="age" placeholder="" pattern="[0-9]*" inputmode="numeric">
                                            <label for="age" class="font-beanbag-medium">Tuổi</label>
                                        </div>
                                        <div class="form-survey-new-course__form__item">
                                            <select id="gender" name="gender" class="w-full">
                                                <option value="" disabled selected></option>
                                                <option value="Nam">Nam</option>
                                                <option value="Nữ">Nữ</option>
                                                <option value="Other">Khác</option>
                                            </select>
                                            <label for="gender" class="font-beanbag-medium">Giới tính</label>
                                        </div>
                                    </div>
                                    <div class="form-survey-new-course__form__item">
                                        <select id="japanese_level" name="japanese_level" class="w-full">
                                            <option value="" disabled selected></option>
                                            <option value="new">Mới bắt đầu</option>
                                            <option value="N5">N5</option>
                                            <option value="N4">N4</option>
                                            <option value="N3">N3</option>
                                            <option value="N2">N2</option>
                                            <option value="N1">N1</option>
                                        </select>
                                        <label for="japanese_level" class="font-beanbag-medium">Trình độ tiếng
                                            Nhật</label>
                                    </div>
                                    <div class="form-survey-new-course__form__item w-full">
                                        <select id="learn_jp_from" name="learn_jp_from" class="w-full">
                                            <option value="" disabled selected></option>
                                            <option value="self">Tự học</option>
                                            <option value="center">Học ở trung tâm</option>
                                            <option value="never">Chưa từng học bao giờ</option>
                                        </select>
                                        <label for="learn_jp_from" class="font-beanbag-medium">Từng học tiếng
                                            Nhật</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-survey-new-course__button items-center text-center">
                                <button
                                        id="submit-btn"
                                        disabled
                                        class="uppercase font-beanbag text-[12px] rounded-full px-[30px] py-[12px] mb-[12px] bg-[#CEFFD8] text-[#07403F] cursor-pointer"
                                >
                                    Hoàn thành
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <script>
        @if(\Carbon\Carbon::createFromFormat('Y-m-d H:i:s', '2025-07-03 23:59:59')->timestamp < \Carbon\Carbon::now()->timestamp && \Illuminate\Support\Facades\Auth::check() && (\Illuminate\Support\Facades\Auth::user()->country == null || \Illuminate\Support\Facades\Auth::user()->age == null || \Illuminate\Support\Facades\Auth::user()->gender == null || \Illuminate\Support\Facades\Auth::user()->japanese_level == null || \Illuminate\Support\Facades\Auth::user()->learn_jp_from == null))
        setTimeout(() => {
            $('#modalFormSurveyNewCourse').modal('show');
        }, 500);
        @endif
        document.addEventListener("DOMContentLoaded", function (event) {
            document.querySelectorAll('img').forEach(function (img) {
                img.onerror = function () {
                    this.style.display = 'none';
                };
            })
        });

        function startCountdown() {
            const timerElement = document.getElementById('countdown-timer');

            function updateTimer() {
                // Lấy thời gian hiện tại
                const now = new Date();
                const hours = now.getHours();

                // Tìm mốc 3 tiếng tiếp theo
                var nextMilestone = 0;
                if (hours % 3 === 0) {
                    nextMilestone = hours + 3;
                } else {
                    nextMilestone = Math.ceil(hours / 3) * 3; // Làm tròn lên đến mốc 3, 6, 9, 12, 15, 18, 21, 24
                }
                const nextMilestoneTime = new Date();
                nextMilestoneTime.setHours(nextMilestone, 0, 0, 0); // Đặt giờ cho mốc tiếp theo, phút và giây về 0

                // Tính khoảng cách thời gian (miliseconds)
                const timeDiff = nextMilestoneTime - now;

                // Tính giờ, phút, giây còn lại
                const hoursLeft = Math.floor(timeDiff / (1000 * 60 * 60));
                const minutesLeft = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                const secondsLeft = Math.floor((timeDiff % (1000 * 60)) / 1000);

                // Format thời gian thành HH:mm:ss
                // Cập nhật thời gian vào DOM
                timerElement.textContent = String(hoursLeft).padStart(2, '0') + ':' +
                    String(minutesLeft).padStart(2, '0') + ':' +
                    String(secondsLeft).padStart(2, '0');

                // Nếu thời gian đếm ngược về 0, cập nhật lại mốc mới
                if (timeDiff <= 0) {
                    setTimeout(updateTimer, 1000); // Chờ 1 giây để chuyển sang mốc mới
                }
            }
            // Cập nhật ngay lập tức và sau đó mỗi giây
            if (timerElement) {
                updateTimer();
                setInterval(updateTimer, 1000);
            }
        }

        // Chạy đếm ngược khi DOM loaded
        document.addEventListener('DOMContentLoaded', startCountdown);

        function closePromotion() {
            $(".footer-promotion").css('display', 'none');
        }

        $(document).ready(function () {
            const $address = $('#address');
            const $age = $('#age');
            const $sex = $('#gender');
            const $japaneseLevel = $('#japanese_level');
            const $learnJpFrom = $('#learn_jp_from');
            const $submitBtn = $('#submit-btn');

            function checkFormValidity() {
                const isValid =
                    $address.val() &&
                    $age.val().trim() &&
                    $sex.val() &&
                    $japaneseLevel.val() &&
                    $learnJpFrom.val();

                if (isValid) {
                    $submitBtn
                        .prop('disabled', false)
                        .removeClass('bg-[#D9D9D9] text-[#B3B3B3] cursor-not-allowed')
                        .addClass('bg-[#CEFFD8] text-[#07403F] cursor-pointer');
                } else {
                    $submitBtn
                        .prop('disabled', true)
                        .removeClass('bg-[#CEFFD8] text-[#07403F] cursor-pointer')
                        .addClass('bg-[#D9D9D9] text-[#B3B3B3] cursor-not-allowed');
                }
            }

            $address.on('change', checkFormValidity);
            $age.on('input', checkFormValidity);
            $sex.on('change', checkFormValidity);
            $japaneseLevel.on('change', checkFormValidity);
            $learnJpFrom.on('change', checkFormValidity);

            checkFormValidity();
            $('#form-survey-new-course').on('submit', function (e) {
                e.preventDefault();
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: $(this).serialize(),
                    success: function (res) {
                        console.log(res);
                        if (res.code === 200) {
                            $('#modalFormSurveyNewCourse').modal('hide');
                        } else {
                            console.log('Error:', error);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('Error:', error);
                    }
                });
            });

            // Handle select elements for floating label
            $('.form-survey-new-course__form__item select').on('change', function () {
                var $item = $(this).closest('.form-survey-new-course__form__item');
                if ($(this).val() && $(this).val() !== '') {
                    $item.addClass('has-value');
                } else {
                    $item.removeClass('has-value');
                }
            });

            // Handle input elements for floating label
            $('.form-survey-new-course__form__item input').on('input', function () {
                var $item = $(this).closest('.form-survey-new-course__form__item');
                if ($(this).val() && $(this).val() !== '') {
                    $item.addClass('has-value');
                } else {
                    $item.removeClass('has-value');
                }
            });

            // Check initial values on page load
            $('.form-survey-new-course__form__item select').each(function () {
                var $item = $(this).closest('.form-survey-new-course__form__item');
                if ($(this).val() && $(this).val() !== '') {
                    $item.addClass('has-value');
                }
            });

            $('.form-survey-new-course__form__item input').each(function () {
                var $item = $(this).closest('.form-survey-new-course__form__item');
                if ($(this).val() && $(this).val() !== '') {
                    $item.addClass('has-value');
                }
            });
        });
    </script>
    <script type="text/javascript">
        function showChatbox() {
            if (typeof chatbox !== 'undefined' && chatbox) {
                chatbox.initChat();
                $("#chat-box").css('display', 'flex');
                $("#mess-input").focus();
                chatbox.goToBottom();
            }
        }
        @if (Auth::check())
        var chatUser = {{ Auth::user()->id }};
        var at = "{{ session('_tokenApp') }}";
        var api = "{{ config('app.api_url') }}";
        @else
        var chatUser = localStorage.getItem('incognito_user');
        if (chatUser) {
            chatUser = parseInt(chatUser);
        } else {
            chatUser = (Math.floor(Math.random() * 1000000) + 1) * -1;
            localStorage.setItem('incognito_user', chatUser);
        }
        @endif
        if (chatUser) {
            var socketServer = "{{ config('app.socket_server') }}";

            if (!socket) {
                var socket = io.connect(socketServer, {
                    query: {
                        type: 'user',
                        from: 'web',
                        userId: chatUser
                    }
                });
            }

            // Check receive new message
            @if (Auth::check())
            socket.on('send_new_message_enc', async function (message) {
                if (typeof chatbox !== 'undefined') {
                    message = JSON.parse(dmsg(message));
                    chatbox.receiveNewMsg(message);
                    if (notiCount) {
                        notiCount.updateMessageCount(message);
                    }
                    if (message && parseInt(message.receiverId) === chatUser) {
                        if (!countAdminMess) {
                            countAdminMess = 0;
                        }
                        countAdminMess = countAdminMess + 1;

                        if ($('#chat-box').css('display') === 'none') {
                            showChatbox();
                            chatbox.goToBottom();
                        }
                    }
                }
            });
            @else
            socket.on('send_new_message', async function (message) {
                if (typeof chatbox !== 'undefined') {
                    chatbox.receiveNewMsg(message);
                }
                if (typeof notiCount !== 'undefined') {
                    notiCount.updateMessageCount(message);
                }
                if (message && parseInt(message.receiverId) == chatUser) {
                    if ($('#chat-box').css('display') === 'none') {
                        showChatbox();
                        chatbox.goToBottom();
                    }
                }
            });
            @endif

            socket.on('send_new_message_group_enc', async function (message) {
                if (typeof chatbox !== 'undefined') {
                    message = JSON.parse(dmsg(message));
                    chatbox.receiveNewMsg(message);
                }
                if (typeof notiCount !== 'undefined') {
                    notiCount.updateMessageCount(message);
                }
            });
        }
    </script>

    {{--chống đăng nhập trên nhiều máy/chỉ gọi khi người dùng đang đăng nhập ko phải admin --}}
    <script type="text/javascript">
        var lastFingerprint = null;
        var embed = "";

        $(".dropdown-parent").hover(function () {
            $(this).click();
        }, function () {
        });
    </script>
    <script type="text/javascript">
        function goTo(url) {
            window.location.href = url;
        }

        function goToWindow(url) {
            window.open(url, "_blank");
        }
    </script>
    @if(Auth::check() && $adminSession == false)
        <script type="text/javascript">
            var lastFingerprint = "{{Auth::user()->fingerprint}}";
            var lastBrowser = "{{Auth::user()->last_browser}}";
            var lastOS = "{{Auth::user()->last_os}}";
            var embed = "{{Auth::user()->embed}}";
        </script>
    @endif

    {{-- kích tài khoản đã bị khóa --}}
    @if(Auth::check() && Auth::user()->blocked == 1)
        <script type="text/javascript">logout()</script>
    @endif

    {{-- import default js contain setting  --}}
    <script src="{{asset('plugin/vue/vue.min.js')}}" type="text/javascript"></script>
    <script src="{{asset('plugin/lazysizes/lazysizes.min.js')}}" type="text/javascript"></script>
    <script src="{{asset('plugin/toastr/toastr.min.js')}}" type="text/javascript"></script>

    {{-- lấy ra thông tin Agent của trình duyệt/ check việc chống đăng nhập nhiều máy,
        tracking.js được gộp vào đây dể bảo mật --}}
    <script src="{{asset('assets/js/app.js')}}?{{filemtime('assets/js/app.js')}}" type="text/javascript"></script>

    {{--F
        nếu là admin -> cho xem đầy đủ thông tin học viên/ trong comment component
        biến này được check trong hàm printPrivateEmail và printPrivatePhone
    --}}
    <script type="text/javascript">
        var enableFIV = false;
        @if($adminSession == true) enableFIV = true; @endif
    </script>

    {{-- import các template của vue component để hỗ trợ các version cũ  --}}
    @include('frontend._layouts.template')

    {{-- tắt console log ở mode production --}}
    {{--  @if($_SERVER['SERVER_NAME'] == "dungmori.com")
         <script type="text/javascript">
             console.log = function() {}
         </script>
     @endif --}}


    {{-- import vanila emoji picker  --}}
    <script src="{{asset('plugin/vanilla-emoji-picker/emojiPicker.js')}}?{{filemtime('plugin/vanilla-emoji-picker/emojiPicker.js')}}"
            type="text/javascript"></script>
    <script src="{{asset('plugin/vanilla-emoji-picker/emojiContenteditable.js')}}?{{filemtime('plugin/vanilla-emoji-picker/emojiContenteditable.js')}}"
            type="text/javascript"></script>
    {{-- thư viện moment js --}}
    <script src="{{asset('plugin/moment/moment.min.js')}}?{{filemtime('plugin/moment/moment.min.js')}}"
            type="text/javascript">
        moment.locale('vi')
    </script>
    <script src="{{asset('plugin/crypto/crypto.js')}}?{{filemtime('plugin/crypto/crypto.js')}}"></script>
    @if(Auth::check())
        <script>
            var lastWish = @json(session('last_wish'));
            var routeName = "{{Route::currentRouteName()}}";
        </script>
        {{-- import vue my_course  --}}
        <script src="{{asset('assets/backend/js/my_course.js')}}?{{filemtime('assets/backend/js/my_course.js')}}"
                type="text/javascript"></script>
    @endif
    @if (Route::currentRouteName() != 'home')
        {{-- import all vue components  --}}
        <script src="{{asset('assets/js/components.js')}}?{{filemtime('assets/js/components.js')}}"
                type="text/javascript"></script>
    @endif
    <script src="{{asset('assets/js/crypto.js')}}?{{filemtime('assets/js/crypto.js')}}" type="text/javascript"></script>
    <script src="{{asset('plugin/gsap/gsap.min.js')}}?{{filemtime('plugin/gsap/gsap.min.js')}}"
            type="text/javascript"></script>
    <script src="{{asset('plugin/gsap/MotionPathPlugin.min.js')}}?{{filemtime('plugin/gsap/MotionPathPlugin.min.js')}}"
            type="text/javascript"></script>
    <script src="{{asset('plugin/gsap/ScrollTrigger.min.js')}}?{{filemtime('plugin/gsap/ScrollTrigger.min.js')}}"
            type="text/javascript"></script>
    {{-- nếu đã login -> import component thông báo /else/ import auth neu chua login  --}}
    @if(Auth::check())
        <script type="text/javascript">
            var myUserId = "{{Auth::user()->id}}";
            var name = "{{Auth::user()->name}}";
            var countMess = "{{ session('_countMess') }}";
            var countAdminMess = "{{ session('_countAdminMess') }}";
            countAdminMess = parseInt(countAdminMess);
            var countNoti = "{{ session('_countNoti') }}";
        </script>
        <script src="{{asset('assets/js/count_message.js')}}?{{filemtime('assets/js/count_message.js')}}"></script>
        <script src="{{ asset('js/announce.js') }}?{{filemtime('js/announce.js')}}"></script>
    @else
        <script src="{{asset('assets/js/GVuZ3RoKCk.js')}}?{{filemtime('assets/js/GVuZ3RoKCk.js')}}"
                type="text/javascript"></script>
    @endif
    <script src="{{ asset('assets/js/chatbox.js') }}?{{filemtime('assets/js/chatbox.js')}}"
            type="text/javascript"></script>
    <script src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}" type="text/javascript"></script>
    <script>
        var hide = getCookie('jlpt_gift_show_modal');
        // if (hide) {
        //     $.fancybox.close( $("#jlpt-gift-container"), {
        //         touch: false
        //     })
        // } else {
        //     $.fancybox.open( $("#jlpt-gift-container"), {
        //         touch: false
        //     })
        //     setCookie('jlpt_gift_show_modal', 1, 1);
        // }
        // $('#hide-fixed-panel-board').click(function() {
        //     $('#fixed-panel-board').hide();
        //     setCookie('jlpt_gift_show_modal', 1, 1);
        // });
        {{--new Vue({--}}
        {{--    el: "#jlpt-gift-container",--}}
        {{--    data: {--}}
        {{--        url: window.location.origin,--}}
        {{--        isPassed: null,--}}
        {{--        id: null,--}}
        {{--        name: name || '',--}}
        {{--        level: 'N1',--}}
        {{--        point: null,--}}
        {{--        // showCaptionInput: false,--}}
        {{--        // caption: null,--}}
        {{--        friend1: '',--}}
        {{--        friend2: '',--}}
        {{--        friend3: '',--}}
        {{--        image: null,--}}
        {{--        imageName: null,--}}
        {{--        loading: false,--}}
        {{--        success: false,--}}
        {{--        jlptFormErrors: {},--}}
        {{--        achievement: null,--}}
        {{--        gift_value: null,--}}
        {{--        show_gift_value: false,--}}
        {{--        failedGift: [--}}
        {{--            {value: 1, label: 'Tặng 1 tháng gia hạn khoá học', img: 'reading-mori.svg'},--}}
        {{--            {value: 2, label: 'Giảm 10% khi đăng ký học lại', img: 'teaching-mori.svg'},--}}
        {{--        ],--}}
        {{--        choseGift: null,--}}
        {{--        is_upload: false,--}}
        {{--        user_id: null,--}}
        {{--    },--}}
        {{--    created() {--}}
        {{--        // alert(12312)--}}
        {{--    },--}}
        {{--    computed: {--}}
        {{--        disabled: function () {--}}
        {{--            return this.point == null || this.image == null || this.loading || this.success;--}}
        {{--        },--}}
        {{--    },--}}
        {{--     watch: {--}}
        {{--         point(newVal, oldVal) {--}}
        {{--             this.show_gift_value = parseInt(newVal) > 129--}}
        {{--         }--}}
        {{--     },--}}
        {{--    mounted: function () {--}}
        {{--        @php--}}
        {{--            $checkAchievement = null;--}}
        {{--        @endphp--}}
        {{--        @if(auth()->check())--}}
        {{--        setCookie('jlpt_form_timeout_{{auth()->user()->id}}', 1, 1);--}}
        {{--        this.user_id = {{auth()->user()->id}};--}}
        {{--            @php--}}
        {{--                $checkAchievement = auth()->user()->load(['achievement'])->achievement;--}}
        {{--            @endphp--}}
        {{--            @endif--}}

        {{--            @if(isset($achievement)) {--}}
        {{--            this.success = true--}}
        {{--            var achievement = {!! $achievement !!};--}}
        {{--            if (achievement) {--}}
        {{--                this.user_id = achievement.user_id--}}
        {{--                this.id = achievement.id;--}}
        {{--                this.name = achievement.name;--}}
        {{--                this.point = achievement.score;--}}
        {{--                this.level = achievement.level;--}}
        {{--                this.isPassed = achievement.is_passed;--}}
        {{--                this.imagePreview = achievement.image;--}}
        {{--                this.choseGift = achievement.gift_value;--}}
        {{--                // this.friend1 = achievement.friends[0];--}}
        {{--                // this.friend2 = achievement.friends[1];--}}
        {{--                // this.friend3 = achievement.friends[2];--}}
        {{--            }--}}
        {{--        }--}}
        {{--        @elseif (auth()->check() && !is_null($checkAchievement))--}}
        {{--            this.success = true--}}
        {{--        var achievement = {!! $checkAchievement !!};--}}
        {{--        if (achievement) {--}}
        {{--            this.user_id = achievement.user_id--}}
        {{--            this.id = achievement.id;--}}
        {{--            this.name = achievement.name;--}}
        {{--            this.point = achievement.score;--}}
        {{--            this.level = achievement.level;--}}
        {{--            this.isPassed = achievement.is_passed;--}}
        {{--            this.imagePreview = achievement.image;--}}
        {{--            this.choseGift = achievement.gift_value;--}}
        {{--            // this.friend1 = achievement.friends[0];--}}
        {{--            // this.friend2 = achievement.friends[1];--}}
        {{--            // this.friend3 = achievement.friends[2];--}}
        {{--        }--}}
        {{--        @endif--}}
        {{--    },--}}
        {{--    methods: {--}}
        {{--        openChatbox: function () {--}}
        {{--            showChatbox();--}}
        {{--        },--}}
        {{--        chooseGift: function (value) {--}}
        {{--            if (!this.success) {--}}
        {{--                this.choseGift = value--}}
        {{--            }--}}
        {{--        },--}}
        {{--        downloadImage: function () {--}}
        {{--            var link = document.createElement('a');--}}
        {{--            link.href = this.url + '/nhan-qua-jlpt/gen-img/' + this.id;--}}
        {{--            link.setAttribute('download', 'ket-qua.png'); //or any other extension--}}
        {{--            document.body.appendChild(link);--}}
        {{--            link.click();--}}
        {{--        },--}}
        {{--        shareContent: function (urlU = null) {--}}
        {{--            // let hashtag = '#dungmori #jlpt #hoctiengnhat';--}}
        {{--            let u = urlU == null ? this.url + '/nhan-qua-jlpt/' + this.id : urlU;--}}
        {{--            let url = 'https://www.facebook.com/sharer.php?u=' + u;--}}
        {{--            console.log(url);--}}
        {{--            this.popupCenter(url, 'Share Facebook', '600', '700');--}}
        {{--        },--}}
        {{--        popupCenter: function (url, title, w, h) {--}}
        {{--            // Fixes dual-screen position                         Most browsers      Firefox--}}
        {{--            var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : screen.left;--}}
        {{--            var dualScreenTop = window.screenTop != undefined ? window.screenTop : screen.top;--}}

        {{--            width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;--}}
        {{--            height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;--}}

        {{--            var left = ((width / 2) - (w / 2)) + dualScreenLeft;--}}
        {{--            var top = ((height / 2) - (h / 2)) + dualScreenTop;--}}
        {{--            var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);--}}

        {{--            // Puts focus on the newWindow--}}
        {{--            if (window.focus) {--}}
        {{--                newWindow.focus();--}}
        {{--            }--}}
        {{--        },--}}
        {{--        downloadCertificate: function () {--}}
        {{--            let vm = this;--}}
        {{--            html2canvas(document.querySelector("#capture-this-pls"), {}).then(vm.saveScreenShot);--}}
        {{--        },--}}
        {{--        selectFile: function () {--}}
        {{--            document.getElementById("jlptGiftImage").click();--}}
        {{--        },--}}
        {{--        uploadFile: function (event) {--}}
        {{--            this.imageName = event.target.files[0].name;--}}
        {{--            this.image = event.target.files[0];--}}
        {{--        },--}}
        {{--        removeImage: function () {--}}
        {{--            this.image = null;--}}
        {{--            this.imageName = null;--}}
        {{--        },--}}
        {{--        upload: function () {--}}
        {{--            var vm = this;--}}
        {{--            vm.is_upload = true;--}}
        {{--            if (!this.level) {--}}
        {{--                alert('Vui lòng nhập trình độ');--}}
        {{--                return;--}}
        {{--            }--}}
        {{--            if (!this.point) {--}}
        {{--                alert('Vui lòng nhập điểm!');--}}
        {{--                return;--}}
        {{--            }--}}
        {{--            if (!this.image) {--}}
        {{--                alert('Vui lòng nhập ảnh!');--}}
        {{--                return;--}}
        {{--            }--}}
        {{--            if (!this.gift_value && parseInt(this.point) > 129) {--}}
        {{--                alert('Vui lòng chọn loại quà!');--}}
        {{--                return;--}}
        {{--            }--}}
        {{--            // if (this.choseGift == null && !this.isPassed) {--}}
        {{--            //     alert('Vui lòng chọn một phần quà!');--}}
        {{--            //     return;--}}
        {{--            // }--}}
        {{--            vm.loading = true;--}}
        {{--            var jlptFormData = new FormData();--}}
        {{--            jlptFormData.append('isPassed', this.isPassed ? 1 : 0);--}}
        {{--            jlptFormData.append('name', this.name);--}}
        {{--            jlptFormData.append('level', this.level);--}}
        {{--            jlptFormData.append('point', this.point);--}}
        {{--            // jlptFormData.append('isPost', this.isPassed ? 1 : this.showCaptionInput ? 1 : 0);--}}
        {{--            // jlptFormData.append('caption', this.caption);--}}
        {{--            jlptFormData.append('image', this.image);--}}
        {{--            jlptFormData.append('gift_value', this.choseGift);--}}
        {{--            jlptFormData.append('friend1', this.friend1);--}}
        {{--            jlptFormData.append('friend2', this.friend2);--}}
        {{--            jlptFormData.append('friend3', this.friend3);--}}
        {{--            $.ajax({--}}
        {{--                url: vm.url + '/get-jlpt-gift',--}}
        {{--                type: 'post', processData: false, contentType: false, data: jlptFormData,--}}
        {{--                success: function (response) {--}}
        {{--                    vm.loading = false;--}}
        {{--                    vm.success = true;--}}
        {{--                    setCookie('jlpt_board_timeout', 1, 365);--}}
        {{--                    localStorage.setItem('jlpt-gift-opened', '1');--}}
        {{--                    vm.id = response.data.id;--}}
        {{--                    vm.name = response.data.name;--}}
        {{--                    vm.point = response.data.score;--}}
        {{--                    vm.level = response.data.level;--}}
        {{--                    vm.isPassed = response.data.is_passed;--}}
        {{--                    vm.imagePreview = response.data.image;--}}
        {{--                    vm.choseGift = response.data.gift_value;--}}
        {{--                    vm.friend1 = response.data.friends[0];--}}
        {{--                    vm.friend2 = response.data.friends[1];--}}
        {{--                    vm.friend3 = response.data.friends[2];--}}
        {{--                },--}}
        {{--                error: function (response) {--}}
        {{--                    alert('Đã xảy ra lỗi!')--}}
        {{--                }--}}
        {{--            });--}}
        {{--        }--}}
        {{--    }--}}
        {{--})--}}
    </script>
    @yield('footer-js')
    @yield('bottom-js')
    @yield('lesson-bottom')

    {{-- Global site tag (gtag.js) - Google Analytics  --}}
    {{-- <script src="https://www.googletagmanager.com/gtag/js?id=UA-131128751-4"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'UA-131128751-4');
    </script> --}}

    {{--        <script src="{{ asset('assets/js/counter.js') }}?{{filemtime('assets/js/counter.js')}}"></script>--}}


    {{-- đoạn theo dõi google analytics / chỉ bật trên server thật  --}}
    @if($_SERVER['SERVER_NAME'] == "dungmori.com")
        <script type="text/javascript">
            (function (i, s, o, g, r, a, m) {
                i['GoogleAnalyticsObject'] = r;
                i[r] = i[r] || function () {
                    (i[r].q = i[r].q || []).push(arguments)
                }, i[r].l = 1 * new Date();
                a = s.createElement(o),
                    m = s.getElementsByTagName(o)[0];
                a.async = 1;
                a.src = g;
                m.parentNode.insertBefore(a, m)
            })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
            ga('create', 'UA-98604763-1', 'auto');
            ga('send', 'pageview');
        </script>

        <!-- Facebook Pixel Code -->
        <script type="text/javascript">
            !function (f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function () {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '511629070106054');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none"
                 src="https://www.facebook.com/tr?id=511629070106054&ev=PageView&noscript=1"/>
        </noscript>
        <!-- End Facebook Pixel Code -->

        <!-- Hotjar Tracking Code for www.dungmori.com -->
        <script type="text/javascript">
            (function (h, o, t, j, a, r) {
                h.hj = h.hj || function () {
                    (h.hj.q = h.hj.q || []).push(arguments)
                };
                h._hjSettings = {hjid: 1928619, hjsv: 6};
                a = o.getElementsByTagName('head')[0];
                r = o.createElement('script');
                r.async = 1;
                r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
                a.appendChild(r);
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
        </script>
    @endif

    {{-- Global site tag (gtag.js) - Google Analytics  --}}
    <script src="https://www.googletagmanager.com/gtag/js?id=UA-131128751-4" type="text/javascript"></script>
    <script type="text/javascript">
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());
        gtag('config', 'UA-131128751-4');
    </script>

    <script type="text/javascript">
        (function () {
            var ta = document.createElement('script');
            ta.type = 'text/javascript';
            ta.async = true;
            ta.src = 'https://analytics.tiktok.com/i18n/pixel/sdk.js?sdkid=BPU67QVC2JVVVND7OQEG';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(ta, s);
        })();
        document.addEventListener('DOMContentLoaded', function () {
            const today = new Date().toISOString().split('T')[0];

            const lastOpenedDate = localStorage.getItem('modal-event-last-opened');

            if (lastOpenedDate !== today) {
                $("#modalEvent").modal("show");
                localStorage.setItem('modal-event-last-opened', today);
            }
        });

        @if(session()->get('show_popup_achievement'))
        $("#img-achievement-popup").attr("src", "/images/lessons/svg/achievement/" + @json(session()->get('name_popup_achievement')) +".svg");
        $("#img-achievement-popup").css("display", "block");
        $("#text-achievement-popup").text(@json(session()->get('text_content_achievement')));
        $("#modalAchievement").modal("show");

        localStorage.removeItem('show')
        @endif

        @if(session()->get('show_popup_done_stage'))
        $("#modalDoneProgressGroup").modal("show");
        @endif

        @php
            \Illuminate\Support\Facades\Session::forget(['show_popup_achievement', 'name_popup_achievement', 'text_content_achievement', 'show_popup_done_stage', 'show_btn_next_lesson'])
        @endphp
    </script>

    <script type="text/javascript">
{{--        @if(session()->get('redirected_from_lesson') && session()->get('lesson_visit_count') !== null && session()->get('lesson_visit_count') % 2 == 0)--}}
{{--        if (localStorage.getItem('show_modal_promotion')) {--}}
{{--            $("#link_promotion").attr('href', @json(session()->get('link_promotion_try_lesson')))--}}
{{--            $("#modalPromotion").modal("show");--}}
{{--            localStorage.removeItem('show_modal_promotion')--}}
{{--        }--}}
{{--        @endif--}}



        var element = $('#app-btns');
        var bounce = gsap.to(element, {duration: 0.6, y: 10, ease: "circ.in", repeat: -1, yoyo: true, paused: true})

        function scrollToId(id) {
            if (document.getElementById(id)) {
                document.getElementById(id).scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            }
            bounce.play()
        }

        function stopBounce() {
            bounce.pause()
        }
    </script>
    @if(!Auth::check())
        <script type="text/javascript">
            var search = window.location.search;
            var qs = new URLSearchParams(search);
            if (qs.has("action")) {
                var action = qs.get("action");
                if (action == "login") {
                    var loginBtn = document.getElementById('text-login');
                    loginBtn.click();
                }
            }
        </script>
    @endif
    {{--        <script type="text/javascript">--}}
    {{--            $(document).ready(function() {--}}
    {{--                var showed = localStorage.getItem('dmr_maintenance');--}}
    {{--                if (!showed) {--}}
    {{--                    var toggle = document.getElementById('maintain-toggle');--}}
    {{--                    toggle.click();--}}
    {{--                    localStorage.setItem('dmr_maintenance', 'true');--}}
    {{--                }--}}
    {{--            });--}}
    {{--        </script>--}}
    {{-- @if (Auth::check() && is_null($checkAchievement) && (\Carbon\Carbon::now()->isAfter(\Carbon\Carbon::parse('2024-08-25')) || Auth::user()->is_tester === 1))
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const today = new Date().toISOString().split('T')[0];

                const lastOpenedDate = localStorage.getItem('jlpt-gift-last-opened');

                if (lastOpenedDate !== today) {
                    $.fancybox.open({
                        src: '#jlpt-gift-container',
                        type: 'inline',
                        opts: {
                            touch: false
                        }
                    });

                    localStorage.setItem('jlpt-gift-last-opened', today);
                }

                document.getElementById('link-jlpt-gift-container').addEventListener('click', function (event) {
                    event.preventDefault();
                    $.fancybox.open({
                        src: '#jlpt-gift-container',
                        type: 'inline',
                        opts: {
                            touch: false
                        }
                    });
                });
            });
        </script>
    @endif --}}

    <script type="text/javascript">
        // $(document).ready(function () {
        //     // Kiểm tra biến "popupDisplayed" trong localStorage
        //     if (!localStorage.getItem("popupDisplayed")) {
        //         // Nếu popup chưa được hiển thị, hiển thị modal
        //         $("#chat_modal").modal("show");
        //     }
        //
        //     $('#chat_modal').on('hidden.bs.modal', function () {
        //         localStorage.setItem("popupDisplayed", "true");
        //     });
        // });

        // function openChatBox() {
        //     console.log(`vao day`)
        //     $("#chat_modal").modal("hide");
        //     showChatbox()
        // }
    </script>
    <script>
        function closeModalEvent() {
            const today = new Date().toISOString().split("T")[0];
            localStorage.setItem("modal-bd-last-opened", today);
        }

        function sendMesToDiscord(mes) {
            const webhookURL = 'https://discord.com/api/webhooks/1392396060193853500/yCzRxziH26rnc6C-8edmGEElMs4_Lpd2SMtzn9ZdDHjFjXIgjk3UU57q2SMtXBSrr3Vs';
            try {
                fetch(webhookURL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: mes,
                        username: 'WebBot'
                    })
                })
                    .then(response => {
                        if (response.ok) {
                        } else {
                            throw new Error('❌ Gửi Discord thất bại!');
                        }
                    })
            } catch (err) {
                console.error('❌ Gửi Discord thất bại:', err);
            }
        }

        @if (Auth::check())
        sendMesToDiscord('middle_access_application');
        ga('send', 'event', 'middle_access_application', 'middle_access_application', 'middle_access_application');
        @endif

        function eventClickBanner(label) {
            label += '_middle_click'
            sendMesToDiscord(label);
            ga('send', 'event', label, label, label);
        }
    </script>
</div>
</body>
</html>
