<div class="chat-box" id="chat-box">
    <div v-show="showConversationList" class="conversation-list">
        <div class="chat-top">
            <div class="admin-item chat-title-box flex flex-row justify-between">
                @if (Auth::check() && Auth::user()->vendor == 'trogiang')
                    <button class="btn btn-success font-normal text-sm" style="padding-top: 2px; padding-bottom: 2px" @click="showAdmin">Login</button>
                @endif
                <div class="font-normal text-sm cursor-pointer" @click="readAll">
                    <i class="fa fa-check" aria-hidden="true"></i> Đ<PERSON> đọc
                </div>
                @if (Auth::check() && Auth::user()->is_tester)
                    <div class="flex align-items-center">
                        <select class="text-sm font-normal" v-model="typeChatFilter">
                            <option value="all" selected="selected"><PERSON><PERSON><PERSON> c<PERSON></option>
                            <option value="personal"><PERSON>á nhân</option>
                            <option value="group">Nhóm</option>
                        </select>
                    </div>
                    <div class="flex align-items-center">
                        <input type="checkbox" class="m-0" v-model="notReadFilter">
                        <span class="text-sm font-normal ml-1" v-on:click="notReadFilter = !notReadFilter">Chưa đọc</span>
                    </div>
                    @if (Auth::user()->is_assistant)
                        <div class="a-cursor-pointer p-1 mr-3" v-on:click="openAddChatModal">
                            <i class="fa fa-plus-circle ml-1" aria-hidden="true" style="font-size: 17px"></i>
                        </div>
                    @endif
                @endif
                <div v-if="!showConversationList" class="close-icon" v-on:click="hideChatbox"><svg class="svgIcon" height="16px" width="16px" version="1.1" viewBox="0 0 16 16" x="0px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" y="0px"><g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><g transform="translate(-712.000000, -1096.000000)"><g stroke="#bec2c9" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" transform="translate(709.000000, 320.000000)"><path d="M5.833,778.833 L16.167,789.167"></path><path d="M16.167,778.833 L5.833,789.167"></path></g></g></g></svg></div>
            </div>
        </div>
        <div>
            <input v-model="searchKey" class="search-con" type="text" placeholder="Tìm kiếm">
            <i v-if="searchKey" class="fa fa-times-circle a-cursor-pointer clear-search" v-on:click="clearSearchConversation" v-cloak></i>
            <div id="con-container" class="chat-container con-container" v-cloak>
                <div
                    v-for="(conversation, index) in listConversation"
                    :key="conversation.id"
                    class="flex con-item"
                    :class="curConv && curConv.id == conversation.id ? 'con-item-border' : ''"
                    :style="{ 'background-color': (conversation.is_group && conversation.last_message && conversation.last_message.id != conversation.last_message_id) || (!conversation.is_group && conversation.last_message && @if (Auth::check()) conversation.last_message.sender_id != {{ Auth::user()->id }} @else false @endif && !conversation.last_message.status) ? '#eaeaea' : '#fff' }"
                    v-on:click="selectConversation(conversation)"
                >
                    <img v-if="conversation.fr_id && conversation.fr_avatar" class="user-avatar" :src="url + '/cdn/avatar/small/'+ conversation.fr_avatar" />
                    <img v-else-if="conversation.fr_id" class="user-avatar" src="{{ url('assets/img/default-avatar.jpg') }}" />
                    <img v-else-if="conversation.avatar" class="user-avatar" :src="url + '/cdn/community/small/'+ conversation.avatar" />
                    <img v-else class="user-avatar" src="{{url('/assets/img/admin-ava.png')}}" />
                    <div class="flex flex-col con-info">
                        <div class="flex">
                            <span v-if="conversation.fr_name" class="user-name">@{{ conversation.fr_name }}</span>
                            <span v-else-if="conversation.fr_id" class="user-name">Noname</span>
                            <span
                                v-else-if="conversation.is_group"
                                class="user-name"
                                style="-webkit-box-orient: vertical;"
                                :title="conversation.title"
                            >
                                @{{ conversation.title ? conversation.title : "Chat group" }}
                            </span>
                            <span v-else class="user-name">DũngMori - CSKH</span>
                            <span v-if="conversation.last_message" class="flex items-center time-last-msg">
                                @{{ conversation.last_message.created_at }}
                            </span>
                        </div>
                        <span v-if="conversation.last_message && conversation.last_message.type == 'text'" class="last-msg" style="-webkit-box-orient: vertical;">
                            @{{ conversation.last_message.content }}
                        </span>
                        <span v-else-if="conversation.last_message && conversation.last_message.type == 'image'" class="last-msg">
                            [Hình ảnh]
                        </span>
                        <span v-else-if="conversation.last_message && conversation.last_message.type == 'file'" class="last-msg">
                            [File]
                        </span>
                    </div>
                </div>
                <div v-if="!endOfConversation" class="text-center">
                    <img style="width: 15px;" src="{{url('/assets/img/fbload.gif')}}"/>
                </div>
            </div>
        </div>
    </div>
    <div class="top-container" v-if="curConv != null" v-cloak>
        <div class="chat-top">
            <div class="admin-item">
                <i v-if="!showConversationList && (listConversation.length > 1 || searchKey != '' || notReadFilter)" class="fa fa-arrow-left back-icon" aria-hidden="true" v-on:click="backChat()"></i>
                <div>
                    <img v-if="curConv.avatar && curConv.is_group" class="admin-img" :src="url + '/cdn/community/small/'+ curConv.avatar" />
                    <img v-else-if="curConv.fr_id && curConv.fr_avatar" class="admin-img" :src="url + '/cdn/avatar/small/'+ curConv.fr_avatar" />
                    <img v-else-if="curConv.fr_id" class="admin-img" src="{{ url('assets/img/default-avatar.jpg') }}" />
                    <img v-else class="admin-img" src="{{url('/assets/img/admin-ava.png')}}"/>
                    <span v-if="!curConv.fr_id && !curConv.is_group" class="admin-active"></span>
                </div>
                <div v-if="curConv.is_group" class="group-name-box flex">
                    <input v-if="isEditGroupName" id="edit_group_name" v-model="groupName" v-on:keyup.enter="saveGroupName" class="edit-group-name" type="text" placeholder="Tên nhóm">
                    <span v-else class="admin-name group-name" style="-webkit-box-orient: vertical;">@{{ curConv.title }}</span>
                    @if (Auth::check() && Auth::user()->is_assistant)
                        <select class="font-normal text-xs" v-model="testerSenderId">
                            <option v-for="(tester, index) in testers" :value="tester.id">@{{ tester.name }}</option>
                        </select>
                    @endif
                </div>
                <div v-else-if="curConv.fr_name" class="flex-1 flex flex-col pt-1">
                    <span class="admin-name leading-[15px]"> &nbsp;@{{ curConv.fr_name }}</span>
                    <span v-if="curConv.fr_email" class="text-[13px] font-normal pl-[3px]"> &nbsp;@{{ curConv.fr_email }}</span>
                </div>
                <div v-else-if="curConv.fr_id" class="flex-1 flex flex-col pt-1">
                    <span class="admin-name leading-[15px]"> &nbsp;Noname</span>
                    <span v-if="curConv.fr_email" class="text-[13px] font-normal pl-[3px]"> &nbsp;@{{ curConv.fr_email }}</span>
                </div>
                <span v-else class="admin-name"> &nbsp;DũngMori - CSKH <i class="zmdi zmdi-check-circle" style="color: #578fff; font-size: 13px;"></i></span>
                @if (Auth::check() && Auth::user()->is_tester)
                    <span class="mark-as-unread" data-toggle="tooltip" data-placement="bottom" data-original-title="Đánh dấu chưa đọc" v-on:click="markAsUnread()">
                        <i class="fa fa-envelope-o p-2 mr-2" aria-hidden="true"></i>
                    </span>
                @endif
                <i
                    v-if="curConv.is_group"
                    class="fa fa-ellipsis-v group-chat-manager a-cursor-pointer"
                    aria-hidden="true"
                    v-on:click="settingOpen = !settingOpen"
                ></i>
                <div class="close-icon" v-on:click="hideChatbox"><svg class="svgIcon" height="16px" width="16px" version="1.1" viewBox="0 0 16 16" x="0px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" y="0px"><g fill="none" fill-rule="evenodd" stroke="none" stroke-width="1"><g transform="translate(-712.000000, -1096.000000)"><g stroke="#bec2c9" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" transform="translate(709.000000, 320.000000)"><path d="M5.833,778.833 L16.167,789.167"></path><path d="M16.167,778.833 L5.833,789.167"></path></g></g></g></svg></div>
            </div>
        </div>
        <div v-if="settingOpen" class="group-chat-setting">
            <div class="flex items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="copyLink">
                <i class="fa fa-link" aria-hidden="true"></i>
                <div>Copy link</div>
            </div>
            <div class="flex items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="openSearchMsgModal">
                <i class="fa fa-search" aria-hidden="true"></i>
                <div>Tìm tin nhắn</div>
            </div>
            @if (Auth::check() && Auth::user()->is_assistant)
                <div class="flex items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="editGroupName">
                    <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                    <div>Đổi tên</div>
                </div>
                <div class="flex items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="changeGroupAvatar">
                    <form
                        id="form_group_avatar"
                        class="flex items-center"
                        data-toggle="tooltip"
                        data-placement="bottom"
                        title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif"
                    >
                        <i class="fa fa-file-image-o" aria-hidden="true"></i>
                        <div>Đổi avatar</div>
                        <input
                            id="input_group_avatar"
                            style="display: none"
                            type='file'
                            name="input_group_avatar" @change="saveGroupAvatar"
                            accept=".png, .jpg, .jpeg, .gif">
                    </form>
                </div>
            @endif
            <div class="flex items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="showPeople">
                <i class="fa fa-user-circle-o" aria-hidden="true"></i>
                <div>Thành viên nhóm</div>
            </div>
            @if (Auth::check() && Auth::user()->is_assistant)
                <div class="flex align-items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="showHistory">
                    <i class="fa fa-history" aria-hidden="true"></i>
                    <div>Lịch sử chuyển nhóm</div>
                </div>
            @endif
            @if (Auth::check() && Auth::user()->is_assistant)
                <div class="flex align-items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="toggleJoinable">
                    <i class="fa fa-wheelchair-alt" aria-hidden="true"></i>
                    <div>@{{ curConv.is_joinable ? 'Bật' : 'Tắt' }} tự động thêm thành viên</div>
                </div>
            @endif
            @if (Auth::check() && Auth::user()->is_assistant)
                <div class="flex align-items-center a-cursor-pointer setting-item hover:bg-gray-100" v-on:click="openAddPeopleModal">
                    <i class="fa fa-user-plus" aria-hidden="true"></i>
                    <div>Thêm thành viên</div>
                </div>
            @endif
        </div>
        <div v-if="curConv && curConv.is_group && pinMessages.length > 0" id="pin_container" class="pin-container">
            <div v-if="pinMessages.length > 1" class="show-full-icon">
                <i v-if="showFullPin" class="fa fa-angle-up a-cursor-pointer" aria-hidden="true" v-on:click="changeFullPinState"></i>
                <i v-else class="fa fa-angle-down a-cursor-pointer" aria-hidden="true" v-on:click="changeFullPinState"></i>
            </div>
            <div v-if="showFullPin">
                <div
                    v-for="(pinMsg, index) in pinMessages"
                    :key="'pin' + pinMsg.id"
                    class="pin-item"
                    v-bind:style="index == 0 ? 'border: 0' : ''"
                >
                    <div v-if="pinMsg.showFull">
                        @if (Auth::check() && Auth::user()->is_tester)
                            <i
                                class="fa fa-thumb-tack pin-icon a-cursor-pointer"
                                aria-hidden="true"
                                v-on:click="unPinMsg(pinMsg)"
                            ></i>
                        @endif
                        <div
                            class="a-cursor-pointer display-content"
                            v-html="renderContent(pinMsg)"
                            v-on:click="changePinState(index)"></div>
                    </div>
                    <div v-else class="flex items-center">
                        @if (Auth::check() && Auth::user()->is_tester)
                            <i
                                style="margin-top: -1px"
                                class="fa fa-thumb-tack pin-icon a-cursor-pointer"
                                aria-hidden="true"
                                v-on:click="unPinMsg(pinMsg)"
                            ></i>
                        @endif
                        <div
                            v-if="pinMsg.type == 'text'"
                            class="pin-item-content a-cursor-pointer"
                            style="-webkit-box-orient: vertical;"
                            v-html="renderContent(pinMsg)"
                            v-on:click="changePinState(index)">
                        </div>
                        <div v-if="pinMsg.type == 'image'" class=" flex-1 flex items-center pin-image">
                            <div class="flex-1 flex">Hình ảnh</div>
                            <a
                                v-for="(img, index) in pinMsg.content"
                                :key="'pin_img' + img"
                                data-fancybox="images"
                                :href="'{{ url('cdn/conversation/default') }}' + '/' + img"
                            >
                                <img :src="'{{ url('cdn/conversation/small') }}' + '/' + img">
                            </a>
                        </div>
                        <div v-if="pinMsg.type == 'file'" class=" flex-1 flex flex-col justify-content-center pin-image pin-file">
                            <a
                                v-for="(file, index) in pinMsg.content"
                                :key="'pin_file' + index"
                                :href="'{{ url('upload/chat_file/') }}' + '/' + file"
                                class="a-cursor-pointer"
                                style="-webkit-box-orient: vertical;"
                                download
                            >@{{ file }}</a>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class="pin-first">
                <div v-if="pinMessages[0].showFull">
                    @if (Auth::check() && Auth::user()->is_tester)
                        <i
                            class="fa fa-thumb-tack pin-icon a-cursor-pointer"
                            aria-hidden="true"
                            v-on:click="unPinMsg(pinMessages[0])"
                        ></i>
                    @endif
                    <div
                        class="a-cursor-pointer display-content"
                        v-html="renderContent(pinMessages[0])"
                        v-on:click="changePinState(0)"
                    ></div>
                </div>
                <div v-else class="flex items-center">
                    @if (Auth::check() && Auth::user()->is_tester)
                        <i
                            class="fa fa-thumb-tack pin-icon a-cursor-pointer"
                            aria-hidden="true"
                            v-on:click="unPinMsg(pinMessages[0])"
                        ></i>
                    @endif
                    <div
                        v-if="pinMessages[0].type == 'text'"
                        class="pin-item-content a-cursor-pointer"
                        v-html="renderContent(pinMessages[0])"
                        style="-webkit-box-orient: vertical;"
                        v-on:click="changePinState(0)"
                    ></div>
                    <div v-if="pinMessages[0].type == 'image'" class=" flex-1 flex items-center pin-image">
                        <div class="flex-1">Hình ảnh</div>
                        <a
                            v-for="(img, index) in pinMessages[0].content"
                            :key="'pin_full_img' + img"
                            data-fancybox="images"
                            :href="'{{ url('cdn/conversation/default') }}' + '/' + img"
                        >
                            <img :src="'{{ url('cdn/conversation/small') }}' + '/' + img">
                        </a>
                    </div>
                    <div v-if="pinMessages[0].type == 'file'" class=" flex-1 flex flex-col justify-content-center pin-image pin-file">
                        <a
                            v-for="(file, index) in pinMessages[0].content"
                            :key="'pin_file' + index"
                            :href="'{{ url('upload/chat_file/') }}' + '/' + file"
                            class="a-cursor-pointer"
                            style="-webkit-box-orient: vertical;"
                            download
                        >@{{ file }}</a>
                    </div>
                </div>
            </div>
        </div>
        <div id="chat-screen" class="chat-content chat-container">
            <div
                v-if="loadMoreState == true"
                class="load-more"
                :style="{ 'margin-top': curConv && curConv.is_group && pinMessages.length > 0 ? '30px' : '5px' }"
                v-on:click="loadMoreMsg()"
            >
                <span v-show="loading == false">Cũ hơn</span>
                <img v-show="loading == true" style="width: 15px;" src="{{url('/assets/img/fbload.gif')}}"/>
            </div>
            <div
                v-for="(item, index) in listMessages"
                :key="'msg' + item.id"
                :id="'item_container_' + item.id"
                class="item-container"
                v-bind:style="index == 0 && loadMoreState == false ? 'margin-top: 32px':''"
            >
                <div v-if="item.rootMessage" class="root-msg flex" :class="item.sender_id == curConv.user_id ? 'root-msg-right' : 'root-msg-left'">
                    <p
                        class="text-root-msg"
                        v-if="item.rootMessage.type == 'text'"
                        v-html="renderContent(item.rootMessage)"
                        v-on:click="goToRootMsg(item.rootMessage.id)"
                    ></p>
                    <p v-if="item.rootMessage.type == 'image'" class="content ct-img img-root-msg">
                        <a
                            v-for="img in item.rootMessage.content"
                            :key="'root' + img"
                            data-fancybox="images"
                            :href="'{{ url('cdn/conversation/default') }}' + '/' + img"
                        >
                            <img :src="'{{ url('cdn/conversation/default') }}' + '/' + img"/>
                        </a>
                    </p>
                    <p v-if="item.rootMessage.type == 'file'" class="text-root-msg">
                        <a
                            v-for="(file, index) in item.rootMessage.content"
                            :key="'root_file' + index"
                            :href="'{{ url('upload/chat_file/') }}' + '/' + file"
                            class="a-cursor-pointer"
                            style="display: inline-block; -webkit-box-orient: vertical; color: #fff"
                            download
                        >@{{ file }}</a>
                    </p>
                </div>
                <div v-if="item.sender_id == curConv.user_id">
                    <div :id="'chat-item-' + item.id" class="chat-item user-chat" v-bind:style="((index+1) < listMessages.length && listMessages[index+1].sender_type == 'admin') ? 'margin-bottom:10px' : ''">
                        <span class="mess-time">@{{ prettyDate(item.created_at) }}</span>
                        <i class="fa fa-reply reply-icon a-cursor-pointer" aria-hidden="true" v-on:click="startReply(item)"></i>
                        @if (Auth::check())
                            <div v-if="curConv.is_group || curConv.friend_id " class="menu-option">
                                <i class="fa fa-ellipsis-v option dropdown-toggle" aria-hidden="true" type="button" data-toggle="dropdown"></i>
                                <ul class="dropdown-menu">
                                    <li v-if="userId === parseInt(item.sender_id)" v-on:click="editMsg(item)"><i class="fa fa-pencil-square-o" aria-hidden="true" style="font-size: 13px"></i> Sửa</li>
                                    @if (Auth::check() && Auth::user()->is_tester)
                                        <li v-if="curConv.is_group && !item.pinned_at" v-on:click="pinMsg(item)"><i class="fa fa-thumb-tack" aria-hidden="true"></i> Ghim</li>
                                    @endif
                                    <li v-on:click="unsendMsg(item)"><i class="fa fa-undo" aria-hidden="true" style="font-size: 12px"></i> Hủy gửi</li>
                                </ul>
                            </div>
                        @endif
                        <div v-if="item.type == 'text'" :id="'content_' + item.id" class="content">
                            <span v-html="renderContent(item)"></span>
                            <div v-if="curConv.is_group || curConv.friend_id" class="flex justify-content-center align-items-center like-box" :class="item.sender_id == curConv.user_id ? 'left-1' : 'right-1'">
                                <div v-if="item.reaction_data && (JSON.parse(item.reaction_data)).length > 0" class="group relative inline-block">
                                    <span class="like-number">@{{ (JSON.parse(item.reaction_data)).length }}</span>
                                    <div
                                      class="like-tooltip absolute left-1/2 z-20 -translate-x-1/2 whitespace-nowrap bg-gray-800 py-[3px] px-2 text-xs text-white rounded-md hidden"
                                      :class="index === listMessages.length - 1 ? 'bottom-full mb-3' : 'top-full mt-3'"
                                      v-html="showListLiked(item)"
                                    >
                                    </div>
                                </div>
                                <i class="fa like-icon" :class="item.liked ? 'fa-heart like-icon-red' : 'fa-heart-o'" aria-hidden="true" v-on:click="likeMessage(item)"></i>
                            </div>
                        </div>
                        <div v-if="item.type == 'image'" class="content ct-img">
                            <a data-fancybox="images" v-for="img in item.content" :key="'img' + img" :href="url + '/cdn/conversation/default/'+ img">
                                <img :src="url + '/cdn/conversation/small/' + img"/>
                            </a>
                            <div v-if="curConv.is_group || curConv.friend_id" class="flex justify-content-center align-items-center like-box" :class="item.sender_id == curConv.user_id ? 'left-1' : 'right-1'">
                                <div v-if="item.reaction_data && (JSON.parse(item.reaction_data)).length > 0" class="group relative inline-block">
                                    <span class="like-number">@{{ (JSON.parse(item.reaction_data)).length }}</span>
                                    <div
                                      class="like-tooltip absolute left-1/2 z-20 -translate-x-1/2 whitespace-nowrap bg-gray-800 py-[3px] px-2 text-xs text-white rounded-md hidden"
                                      :class="index === listMessages.length - 1 ? 'bottom-full mb-3' : 'top-full mt-3'"
                                      v-html="showListLiked(item)"
                                    >
                                    </div>
                                </div>
                                <i class="fa like-icon" :class="item.liked ? 'fa-heart like-icon-red' : 'fa-heart-o'" aria-hidden="true" v-on:click="likeMessage(item)"></i>
                            </div>
                        </div>
                        <p v-else-if="item.type == 'file'" class="flex flex-col content ct-img ct-file">
                            <a
                                v-for="file in item.content"
                                :href="'{{ url('upload/chat_file/') }}' + '/' + file"
                                class="a-cursor-pointer"
                                download
                            >@{{ file }}</a>
                        </p>
                    </div>
                    <div v-if="item.seenUsers && item.seenUsers.length > 0" class="seen-user-box seen-user-box-left">
                        <div
                            v-for="(seenUser, index) in item.seenUsers"
                            :key="'seen_user' + seenUser"
                            :style="{ 'margin-left': index != 0 ? '3px' : '0' }"
                        >
                            <img
                                v-if="people.find((p) => p.id == seenUser) && (people.find((p) => p.id == seenUser)).avatar"
                                class="seen-avatar"
                                :src="'{{ url('cdn/avatar/small') }}' + '/' + (people.find((p) => p.id == seenUser)).avatar"
                                :title="(people.find((p) => p.id == seenUser)).name"
                            />
                            <img
                                v-else
                                class="seen-avatar"
                                src="{{ url('assets/img/default-avatar.jpg') }}"
                                :title="people.find((p) => p.id == seenUser) && (people.find((p) => p.id == seenUser)).name ? (people.find((p) => p.id == seenUser)).name : ''"
                            />
                        </div>
                    </div>
                </div>

                <div v-else>
                    <div class="chat-item admin-chat" :id="'chat-item-' + item.id" v-bind:style="((index+1) < listMessages.length && listMessages[index+1].sender_id != item.sender_id) ? 'margin-bottom:10px' : ''">
                        <div class="avatar-flex">
                            <div class="avatar" v-if="(index+1) < listMessages.length">
                                <img
                                    v-if="item.sender_type == 'admin'"
                                    class="ad-avatar w-[30px] h-[30px] object-cover"
                                    v-bind:style="listMessages[index+1].sender_id != item.sender_id ? '' : 'opacity: 0'"
                                    src="{{url('/assets/img/admin-ava.png')}}"
                                    title="Dũng Mori"
                                />
                                <img
                                    v-else-if="item.user_avatar"
                                    class="ad-avatar w-[30px] h-[30px] object-cover"
                                    v-bind:style="listMessages[index+1].sender_id != item.sender_id ? '' : 'opacity: 0'"
                                    :src="'{{ url('cdn/avatar/small') }}' + '/' + item.user_avatar"
                                    :title="item.user_name"
                                    v-on:click="goToChatUser(item)"
                                />
                                <img
                                    v-else
                                    class="ad-avatar w-[30px] h-[30px] object-cover"
                                    v-bind:style="listMessages[index+1].sender_id != item.sender_id ? '' : 'opacity: 0'"
                                    src="{{ url('assets/img/default-avatar.jpg') }}"
                                    :title="item.user_name"
                                />
                                <span v-if="item.is_tester" class="admin-active" v-bind:style="listMessages[index+1].sender_id != item.sender_id ? '' : 'opacity: 0'"></span>
                            </div>
                            <div class="avatar" v-if="(index+1) == listMessages.length">
                                <img
                                    v-if="item.sender_type == 'admin'"
                                    class="ad-avatar w-[30px] h-[30px] object-cover"
                                    src="{{url('/assets/img/admin-ava.png')}}"
                                    title="Dũng Mori"
                                />
                                <img
                                    v-else-if="item.user_avatar"
                                    class="ad-avatar w-[30px] h-[30px] object-cover"
                                    :src="'{{ url('cdn/avatar/small') }}' + '/' + item.user_avatar"
                                    :title="item.user_name"
                                />
                                <img
                                    v-else
                                    class="ad-avatar w-[30px] h-[30px] object-cover"
                                    src="{{ url('assets/img/default-avatar.jpg') }}"
                                    :title="item.user_name"
                                />
                                <span v-if="item.is_tester" class="admin-active"></span>
                            </div>
                        </div>
                        <div v-if="item.type == 'text'" :id="'content_' + item.id" class="content">
                            <span v-html="renderContent(item)"></span>
                            <div v-if="curConv.is_group || curConv.friend_id" class="flex justify-content-center align-items-center like-box" :class="item.sender_id == curConv.user_id ? 'left-1' : 'right-1'">
                                <div v-if="item.reaction_data && (JSON.parse(item.reaction_data)).length > 0" class="group relative inline-block">
                                    <span class="like-number">@{{ (JSON.parse(item.reaction_data)).length }}</span>
                                    <div
                                      class="like-tooltip absolute left-1/2 z-20 -translate-x-1/2 whitespace-nowrap bg-gray-800 py-[3px] px-2 text-xs text-white rounded-md hidden"
                                      :class="index === listMessages.length - 1 ? 'bottom-full mb-3' : 'top-full mt-3'"
                                      v-html="showListLiked(item)"
                                    >
                                    </div>
                                </div>
                                <i class="fa like-icon" :class="item.liked ? 'fa-heart like-icon-red' : 'fa-heart-o'" aria-hidden="true" v-on:click="likeMessage(item)"></i>
                            </div>
                        </div>
                        <div v-if="item.type == 'image'" class="content ct-img">
                            <a data-fancybox="images" v-for="img in item.content" :key="'main2' + img" :href="'{{ url('cdn/conversation/default') }}' + '/' + img">
                                <img :src="'{{ url('cdn/conversation/small') }}' + '/' + img"/>
                            </a>
                            <div v-if="curConv.is_group || curConv.friend_id" class="flex justify-content-center align-items-center like-box" :class="item.sender_id == curConv.user_id ? 'left-1' : 'right-1'">
                                <div v-if="item.reaction_data && (JSON.parse(item.reaction_data)).length > 0" class="group relative inline-block">
                                    <span class="like-number">@{{ (JSON.parse(item.reaction_data)).length }}</span>
                                    <div
                                      class="like-tooltip absolute left-1/2 z-20 -translate-x-1/2 whitespace-nowrap bg-gray-800 py-[3px] px-2 text-xs text-white rounded-md hidden"
                                      :class="index === listMessages.length - 1 ? 'bottom-full mb-3' : 'top-full mt-3'"
                                      v-html="showListLiked(item)"
                                    >
                                    </div>
                                </div>
                                <i class="fa like-icon" :class="item.liked ? 'fa-heart like-icon-red' : 'fa-heart-o'" aria-hidden="true" v-on:click="likeMessage(item)"></i>
                            </div>
                        </div>
                        <div v-if="item.type == 'file'" class="flex flex-col content ct-img ct-file">
                            <a
                                v-for="file in item.content"
                                :href="'{{ url('upload/chat_file/') }}' + '/' + file"
                                class="a-cursor-pointer"
                                download
                            >@{{ file }}</a>
                        </div>
                        @if (Auth::check() && Auth::user()->is_tester)
                            <div class="menu-option">
                                <i v-if="curConv.is_group && !item.pinned_at" class="fa fa-ellipsis-v option dropdown-toggle" aria-hidden="true" type="button" data-toggle="dropdown"></i>
                                <ul v-if="curConv.is_group && !item.pinned_at" class="dropdown-menu">
                                    <li v-on:click="pinMsg(item)"><i class="fa fa-thumb-tack" aria-hidden="true"></i> Ghim</li>
                                </ul>
                            </div>
                        @endif
                        <i class="fa fa-reply reply-icon a-cursor-pointer" aria-hidden="true" v-on:click="startReply(item)"></i>
                        <span class="mess-time">@{{ prettyDate(item.created_at) }}</span>
                    </div>
                    <div v-if="item.seenUsers && item.seenUsers.length > 0" class="seen-user-box">
                        <div
                            v-for="(seenUser, index) in item.seenUsers"
                            :key="'seen_user' + seenUser"
                            :style="{ 'margin-left': index != 0 ? '3px' : '0' }"
                        >
                            <img
                                v-if="people.find((p) => p.id == seenUser) && (people.find((p) => p.id == seenUser)).avatar"
                                class="seen-avatar"
                                :src="'{{ url('cdn/avatar/small') }}' + '/' + (people.find((p) => p.id == seenUser)).avatar"
                                :title="(people.find((p) => p.id == seenUser)).name"
                            />
                            <img
                                v-else
                                class="seen-avatar"
                                src="{{ url('assets/img/default-avatar.jpg') }}"
                                :title="people.find((p) => p.id == seenUser) && (people.find((p) => p.id == seenUser)).name ? (people.find((p) => p.id == seenUser)).name : ''"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="notSeenMsgId" v-on:click="goToNotSeenMsg" class="a-cursor-pointer notSeenTextBox">
                <i class="fa fa-angle-double-up" aria-hidden="true"></i>Tin chưa đọc
            </div>
            <div v-if="tagMsgId" v-on:click="goToTagMsg" class="a-cursor-pointer notSeenTextBox tagTextBox">
                <i class="fa fa-angle-double-up" aria-hidden="true"></i>Nhắc đến bạn
            </div>
        </div>
        <div class="chat-bottom">
            <div class="preview-image" id="preview-image" :style="{ 'margin-top': rootMessage ? '-91px' : '-50px' }">
                <div id="image-list"></div>
                <i class="fa fa-arrow-circle-right send-btn" title="Gửi đi" onclick="sendImgMess()"></i>
            </div>
            <div class="reply-info-box" id="reply-info-container" v-if="rootMessage != null">
                <div class="reply-title">Đang trả lời <b>@{{ rootMessage.senderName }}</b></div>
                <div v-if="rootMessage.type == 'text'" class="root-cm-content" style="-webkit-box-orient: vertical;">@{{ rootMessage.content }}</div>
                <div v-else-if="rootMessage.type == 'image'" class="root-cm-content">[Hình ảnh]</div>
                <div v-else-if="rootMessage.type == 'file'" class="root-cm-content">[File]</div>
                <i class="fa fa-times-circle a-cursor-pointer" aria-hidden="true" v-on:click="clearRootMessage"></i>
            </div>
            <div v-if="isTag && tagMembers.length > 0" class="member-tag-container" id="member-tag-container" :style="{ 'bottom': rootMessage ? '90px' : '49px' }">
                <div
                    v-for="(member, index) in tagMembers"
                    class="member-tag-item a-cursor-pointer"
                    :style="{ 'background-color': index == tagSelectedIndex ? '#dbdbdb' : '#f0f0f0' }"
                    v-on:click="applyTagMemberName(member)"
                >
                    <img
                        v-if="member.avatar"
                        class="tag-avatar"
                        :src="'{{ url('cdn/avatar/small') }}' + '/' + member.avatar"
                        :title="member.name"
                    />
                    <img
                        v-else
                        class="tag-avatar"
                        src="{{ url('assets/img/default-avatar.jpg') }}"
                        :title="member.name"
                    />
                    @{{ member.name }}
                </div>
            </div>
            <div class="form-chat">
                <div
                    v-model="mess"
                    contenteditable="true"
                    v-on:keyup="keyTyping"
                    @keydown.enter.exact.prevent="sendMess"
                    class="mess"
                    id="mess-input"
                    placeholder="Nhập tin nhắn..."
                    autocomplete="off"
                    autofocus
                    @keydown="onKeyDown($event)"
                    @input="changeTextInput"
                    data-emoji-contenteditable="true"
                    :class="@if (Auth::check() && Auth::user()->is_tester) true @else false @endif ? '' : 'mess-incognito'"
                ></div>
                <form id="send_image_form" data-toggle="tooltip" data-placement="bottom" title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif">
                    <i class="fa fa-camera"></i>
                    <input id="input_image_mess" type='file' name="input_image_mess[]" onchange="sendImgMess()" accept=".png, .jpg, .jpeg, .gif" multiple>
                </form>
                <form
                    v-if="@if (Auth::check() && Auth::user()->is_tester) true @else false @endif"
                    id="send_file_form"
                    data-toggle="tooltip"
                    data-placement="bottom"
                    title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: pdf, doc, docx, xlsx, xlsm, xlsb, xltx"
                >
                    <i class="fa fa-paperclip" aria-hidden="true"></i>
                    <input id="input_file_mess" type='file' name="input_file_mess[]" onchange="sendFileMess()" accept=".pdf, .doc, .docx, .xlsx, .xlsm, .xlsb, .xltx" multiple>
                </form>
            </div>
        </div>
    </div>

    @include('frontend._layouts.chat-component.people_list')
    @include('frontend._layouts.chat-component.conversation_history')
    @include('frontend._layouts.chat-component.add_people')
    @include('frontend._layouts.chat-component.add_chat')
    @include('frontend._layouts.chat-component.switch_group')
    @include('frontend._layouts.chat-component.search_message')
    @include('frontend._layouts.chat-component.select_admin')
</div>
