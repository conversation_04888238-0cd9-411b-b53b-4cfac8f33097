<script type="text/x-template" id="exam_container">
  <div class="w-screen">
    <div v-if="stage < 4" class="w-full h-[68px] fixed -top-[360px] left-0 -z-1">
      <div class="w-[1000px] h-[300px]  mx-auto rounded-[50%] bg-[#57D06180] blur-3xl"></div>
    </div>
    <div v-if="stage > 0 && stage < 4" class="fixed top-0 left-0 w-screen flex justify-center items-center h-[68px] z-2" v-cloak>
      <div v-if="!isWaiting" class="flex items-center gap-3">
        <div class="font-averta-bold text-[40px] text-[#757575]">@{{ timerDisplay.minute }}:@{{ timerDisplay.second }}</div>
        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path opacity="0.4" d="M19.9998 36.6666C27.9803 36.6666 34.4498 30.1971 34.4498 22.2166C34.4498 14.2361 27.9803 7.7666 19.9998 7.7666C12.0193 7.7666 5.5498 14.2361 5.5498 22.2166C5.5498 30.1971 12.0193 36.6666 19.9998 36.6666Z" fill="#5A5A5A"/>
          <path d="M20 22.9163C19.3167 22.9163 18.75 22.3497 18.75 21.6663V13.333C18.75 12.6497 19.3167 12.083 20 12.083C20.6833 12.083 21.25 12.6497 21.25 13.333V21.6663C21.25 22.3497 20.6833 22.9163 20 22.9163Z" fill="#5A5A5A"/>
          <path d="M24.8167 5.74967H15.1834C14.5167 5.74967 13.9834 5.21634 13.9834 4.54967C13.9834 3.88301 14.5167 3.33301 15.1834 3.33301H24.8167C25.4834 3.33301 26.0167 3.86634 26.0167 4.53301C26.0167 5.19967 25.4834 5.74967 24.8167 5.74967Z" fill="#5A5A5A"/>
        </svg>
      </div>
    </div>
    <div v-if="stage === 0" class="w-screen h-screen overflow-y-scroll mt-[68px] pb-[100px] bg-[#F4F5FA]">
      <div class="mt-[85px] w-[1320px] rounded-[30px] mx-auto shadow-[0px_-22px_31.5px_-19px_rgba(33,33,33,0.11)] py-[20px] flex flex-col justify-center items-center z-2 bg-gradient-to-b from-white from-10% via-transparent via-30% to-transparent to-90% to-transparent">
        <img :src="`{{ asset('assets/img/exam-overview.svg') }}`" />
        <div class="flex items-start gap-1 mt-[28px]">
          <div class="font-zuume-semibold uppercase text-[38px] text-[#07403F]">@{{ lesson.name }}</div>

          <svg v-if="lesson.require" width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="mt-2">
            <path d="M6.09789 1.8541C6.69659 0.0114813 9.30341 0.0114808 9.90211 1.8541L10.2451 2.90983C10.5129 3.73387 11.2808 4.2918 12.1473 4.2918H13.2573C15.1948 4.2918 16.0003 6.77103 14.4329 7.90983L13.5348 8.56231C12.8339 9.07159 12.5405 9.97433 12.8083 10.7984L13.1513 11.8541C13.75 13.6967 11.6411 15.229 10.0736 14.0902L9.17557 13.4377C8.4746 12.9284 7.5254 12.9284 6.82443 13.4377L5.92638 14.0902C4.35895 15.229 2.24999 13.6967 2.84869 11.8541L3.19172 10.7984C3.45947 9.97433 3.16615 9.07159 2.46517 8.56231L1.56712 7.90983C-0.000308037 6.77103 0.805244 4.2918 2.74269 4.2918H3.85275C4.7192 4.2918 5.48711 3.73387 5.75486 2.90983L6.09789 1.8541Z" fill="#EC6E23"/>
          </svg>
        </div>
        <div v-else class="font-averta-regular text-[14px] text-[#57D061]" v-cloak>Mô phỏng đề thi JLPT thực tế</div>
        <i v-if="loading" class="fa fa-spinner fa-spin fa-3x mt-[50px]"></i>
        <div v-else-if="lesson.type == 'exam'" class="flex gap-8 font-averta-regular mt-[50px] text-[#1E1E1E]" v-cloak>
          <div class="flex items-center gap-2">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.8332 9.33333C13.8332 12.5533 11.2198 15.1667 7.99984 15.1667C4.77984 15.1667 2.1665 12.5533 2.1665 9.33333C2.1665 6.11333 4.77984 3.5 7.99984 3.5C11.2198 3.5 13.8332 6.11333 13.8332 9.33333Z" stroke="#57D061" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M8 5.83301V9.16634" stroke="#57D061" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6 1.83301H10" stroke="#57D061" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <div>@{{ lesson.exam_parts[0].duration }} phút</div>
          </div>
          <div class="flex items-center gap-2">
            <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="1.3335" y="1.83301" width="13.3333" height="13.3333" rx="4" stroke="#57D061" stroke-width="1.5"/>
              <path d="M11.7466 6.91309H8.24658C7.97325 6.91309 7.74658 6.68642 7.74658 6.41309C7.74658 6.13975 7.97325 5.91309 8.24658 5.91309H11.7466C12.0199 5.91309 12.2466 6.13975 12.2466 6.41309C12.2466 6.68642 12.0266 6.91309 11.7466 6.91309Z" fill="#57D061"/>
              <path d="M4.74687 7.42021C4.6202 7.42021 4.49354 7.37354 4.39354 7.27354L3.89354 6.77354C3.7002 6.58021 3.7002 6.26021 3.89354 6.06688C4.08687 5.87354 4.40687 5.87354 4.6002 6.06688L4.74687 6.21354L5.89353 5.06688C6.08687 4.87354 6.40687 4.87354 6.6002 5.06688C6.79353 5.26021 6.79353 5.58021 6.6002 5.77354L5.1002 7.27354C5.00687 7.36687 4.8802 7.42021 4.74687 7.42021Z" fill="#57D061"/>
              <path d="M11.7466 11.5801H8.24658C7.97325 11.5801 7.74658 11.3534 7.74658 11.0801C7.74658 10.8067 7.97325 10.5801 8.24658 10.5801H11.7466C12.0199 10.5801 12.2466 10.8067 12.2466 11.0801C12.2466 11.3534 12.0266 11.5801 11.7466 11.5801Z" fill="#57D061"/>
              <path d="M4.74687 12.0862C4.6202 12.0862 4.49354 12.0396 4.39354 11.9396L3.89354 11.4396C3.7002 11.2462 3.7002 10.9262 3.89354 10.7329C4.08687 10.5396 4.40687 10.5396 4.6002 10.7329L4.74687 10.8796L5.89353 9.73289C6.08687 9.53956 6.40687 9.53956 6.6002 9.73289C6.79353 9.92622 6.79353 10.2462 6.6002 10.4396L5.1002 11.9396C5.00687 12.0329 4.8802 12.0862 4.74687 12.0862Z" fill="#57D061"/>
            </svg>
            <div>
              @{{ tasks.filter(task => [3,13].includes(task.type)).length }} câu</div>
          </div>
        </div>
        <div v-else class="grid grid-cols-3 gap-[50px] mt-[50px]" v-cloak>
          <div v-for="part in examLevels[level]">
            <div class="font-beanbag text-[#07403F] text-[14px] uppercase">@{{ part.label }}</div>
            <div class="flex gap-8 font-averta-regular mt-3">
              <div class="flex items-center gap-2">
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13.8332 9.33333C13.8332 12.5533 11.2198 15.1667 7.99984 15.1667C4.77984 15.1667 2.1665 12.5533 2.1665 9.33333C2.1665 6.11333 4.77984 3.5 7.99984 3.5C11.2198 3.5 13.8332 6.11333 13.8332 9.33333Z" stroke="#57D061" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8 5.83301V9.16634" stroke="#57D061" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M6 1.83301H10" stroke="#57D061" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <div>@{{ part.duration }} phút</div>
              </div>
              <div class="flex items-center gap-2">
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="1.3335" y="1.83301" width="13.3333" height="13.3333" rx="4" stroke="#57D061" stroke-width="1.5"/>
                  <path d="M11.7466 6.91309H8.24658C7.97325 6.91309 7.74658 6.68642 7.74658 6.41309C7.74658 6.13975 7.97325 5.91309 8.24658 5.91309H11.7466C12.0199 5.91309 12.2466 6.13975 12.2466 6.41309C12.2466 6.68642 12.0266 6.91309 11.7466 6.91309Z" fill="#57D061"/>
                  <path d="M4.74687 7.42021C4.6202 7.42021 4.49354 7.37354 4.39354 7.27354L3.89354 6.77354C3.7002 6.58021 3.7002 6.26021 3.89354 6.06688C4.08687 5.87354 4.40687 5.87354 4.6002 6.06688L4.74687 6.21354L5.89353 5.06688C6.08687 4.87354 6.40687 4.87354 6.6002 5.06688C6.79353 5.26021 6.79353 5.58021 6.6002 5.77354L5.1002 7.27354C5.00687 7.36687 4.8802 7.42021 4.74687 7.42021Z" fill="#57D061"/>
                  <path d="M11.7466 11.5801H8.24658C7.97325 11.5801 7.74658 11.3534 7.74658 11.0801C7.74658 10.8067 7.97325 10.5801 8.24658 10.5801H11.7466C12.0199 10.5801 12.2466 10.8067 12.2466 11.0801C12.2466 11.3534 12.0266 11.5801 11.7466 11.5801Z" fill="#57D061"/>
                  <path d="M4.74687 12.0862C4.6202 12.0862 4.49354 12.0396 4.39354 11.9396L3.89354 11.4396C3.7002 11.2462 3.7002 10.9262 3.89354 10.7329C4.08687 10.5396 4.40687 10.5396 4.6002 10.7329L4.74687 10.8796L5.89353 9.73289C6.08687 9.53956 6.40687 9.53956 6.6002 9.73289C6.79353 9.92622 6.79353 10.2462 6.6002 10.4396L5.1002 11.9396C5.00687 12.0329 4.8802 12.0862 4.74687 12.0862Z" fill="#57D061"/>
                </svg>
                <div>
                  @{{ part.question }} câu</div>
              </div>
            </div>
            <div class="flex items-center gap 2 text-[#EF6D13] font-averta-regular mt-2">
              @{{ part.type === 3 ? '— Kết thúc bài thi —' : '— Nghỉ 10 phút giữa giờ —' }}
            </div>
          </div>
        </div>
          <div v-if="lesson.type == 'exam'" class="font-averta-regular text-[#757575] mt-[90px]">Điểm đạt: ≥85% (@{{ Math.ceil(tasks.filter(task => [3,13].includes(task.type)).length * 0.85) }} / @{{ tasks.filter(task => [3,13].includes(task.type)).length }} câu)</div>
          <div v-else class="font-averta-regular text-[#757575] mt-[90px]">- Điểm đạt: @{{ lesson.pass_marks }} / @{{ lesson.total_marks }} -</div>
        <div
            v-if="!result"
            class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center
          bg-[#57D061] text-[#07403F]  rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
            v-cloak
            @click="startExam()"
        >
          <i v-if="start_loading" class="fa fa-spinner fa-spin"></i>
          <span v-else>Bắt đầu</span>
        </div>
        <div v-else-if="result && !result?.submit_at" class="flex items-center gap-8" v-cloak>
          <div
              class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center
          bg-white text-[#07403F]  rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
              @click="startExam()"
          >Bắt đầu lại</div>
          <div
              class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center
          bg-[#57D061] text-[#07403F]  rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
              @click="continueExam()"
          >Tiếp tục</div>
        </div>
        <div v-else v-cloak class="w-[700px]">
          <div class="rounded-[24px] bg-[#FFF9F9] w-[700px] flex flex-col items-center pt-[18px] mt-[68px] shadow-lg">
            <div class="w-[281px]">
              <div class="flex justify-between items-center">
                <div class="font-averta-semibold text-[16px]">Kết quả</div>
                <div class="font-averta text-[16px] px-[32px] py-1 bg-[#C1EACA] rounded-full" v-cloak>@{{ result && result?.is_passed ? 'Đạt' : 'Không đạt' }}</div>
              </div>
              <div class="flex justify-between items-center">
                <div class="font-averta-semibold text-[16px]">Điểm của bạn</div>
                <div v-if="lesson.type == 'exam'" class="font-averta text-[16px] py-1" v-cloak>@{{ resultStatistics?.correctQuestions.length }}/@{{ resultStatistics?.questions?.length }}</div>
                <div v-else class="font-averta text-[16px] py-1" v-cloak>@{{ result?.total_score || 0 }} / @{{ lesson.total_marks || 0 }}</div>
              </div>
              <div class="flex justify-between items-center">
                <div class="font-averta-semibold text-[16px]">Điểm đạt</div>
                <div v-if="lesson.type == 'exam'" class="font-averta-semibold text-[16px]"v-cloak>@{{ Math.ceil(tasks.filter(task => [3,13].includes(task.type)).length * 0.85) }} / @{{ tasks.filter(task => [3,13].includes(task.type)).length }}</div>
                <div v-else class="font-averta-semibold text-[16px]"v-cloak>@{{ result?.total_score || 0 }} / @{{ lesson.total_marks || 0 }}</div>
              </div>
            </div>
            <div
                class="w-full rounded-full flex justify-center items-center py-[14px] border-[#57D061] border-[2px] mt-[14px] font-beanbag text-[#57D061] uppercase cursor-pointer bg-[#F4F5FA]"
                @click="stage = 4"
            >Xem bài làm gần đây nhất</div>
          </div>
          <div class="flex items-center gap-8">
            <div
                class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center
          bg-white text-[#07403F]  rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
                @click="startExam()"
            >Làm lại</div>
            <div
                class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center
          bg-[#57D061] text-[#07403F]  rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase"
                @click="nextLesson()"
            >Học tiếp</div>
          </div>
        </div>
        <a v-if="!result || !result?.submit_at" href="" class="mt-[28px]"><u class="font-beanbag text-[#07403F] cursor-pointer uppercase">Bài tiếp theo ></u></a>
      </div>
    </div>

    <div v-if="stage > 0 && stage < 4 && !isWaiting" id="examWrapper" v-cloak class="bg-[#F4F5FA] w-screen h-screen overflow-y-scroll mt-[68px] mb-[88px]">
      <div class="h-[88px] w-screen fixed bottom-0 left-0 bg-white shadow-md">
        <div class="w-[1200px] h-[88px] mx-auto flex items-center justify-between">
          <div
              class="w-[100px] rounded-full border-2 border-[#D9D9D9] text-[#D9D9D9] cursor-pointer inline-flex justify-center items-center p-3 hover:border-[#57D061] hover:text-[#57D061]"
              @click="prevMondai()"
          >
            <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-current">
              <path d="M7.86918 14.0398L1.92627 7.96177L7.86918 1.88379" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="font-beanbag text-[20px]">もんだい @{{ currentMondai + 1 }}：@{{ currentMondai*5 + 1 }}→@{{ currentMondai*5+5 }}</div>
          <div
              class="w-[100px] rounded-full border-2 border-[#D9D9D9] text-[#D9D9D9] cursor-pointer rotate-180 inline-flex justify-center items-center p-3 hover:border-[#57D061] hover:text-[#57D061]"
              @click="nextMondai()"
          >
            <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-current">
              <path d="M7.86918 14.0398L1.92627 7.96177L7.86918 1.88379" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
      <div class="pt-[75px] pb-[200px] w-[1200px] mx-auto">
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2 py-2 px-3 rounded-full border border-[#757575] font-beanbag text-[14px] flex-shrink-0">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14.6831 8.0166H10.3081C9.96644 8.0166 9.68311 7.73327 9.68311 7.3916C9.68311 7.04993 9.96644 6.7666 10.3081 6.7666H14.6831C15.0248 6.7666 15.3081 7.04993 15.3081 7.3916C15.3081 7.73327 15.0331 8.0166 14.6831 8.0166Z" fill="#757575"/>
              <path d="M5.93346 8.65026C5.77513 8.65026 5.6168 8.59193 5.4918 8.46693L4.8668 7.84193C4.62513 7.60026 4.62513 7.20026 4.8668 6.95859C5.10846 6.71693 5.50846 6.71693 5.75013 6.95859L5.93346 7.14193L7.3668 5.70859C7.60846 5.46693 8.00846 5.46693 8.25013 5.70859C8.4918 5.95026 8.4918 6.35026 8.25013 6.59193L6.37513 8.46693C6.25846 8.58359 6.10013 8.65026 5.93346 8.65026Z" fill="#757575"/>
              <path d="M14.6836 13.8496H10.3086C9.96693 13.8496 9.68359 13.5663 9.68359 13.2246C9.68359 12.8829 9.96693 12.5996 10.3086 12.5996H14.6836C15.0253 12.5996 15.3086 12.8829 15.3086 13.2246C15.3086 13.5663 15.0336 13.8496 14.6836 13.8496Z" fill="#757575"/>
              <path d="M5.93346 14.4833C5.77513 14.4833 5.6168 14.4249 5.4918 14.2999L4.8668 13.6749C4.62513 13.4333 4.62513 13.0333 4.8668 12.7916C5.10846 12.5499 5.50846 12.5499 5.75013 12.7916L5.93346 12.9749L7.3668 11.5416C7.60846 11.2999 8.00846 11.2999 8.25013 11.5416C8.4918 11.7833 8.4918 12.1833 8.25013 12.4249L6.37513 14.2999C6.25846 14.4166 6.10013 14.4833 5.93346 14.4833Z" fill="#757575"/>
            </svg>
            <div>@{{ currentMondai*5 + 1 }}-@{{ currentMondai*5+5 }}/@{{ examLevels[level][stage-1].question }}</div>
          </div>
          <div class="w-full bg-[#D9D9D9] rounded-full h-2.5 flex-grow-1">
            <div class="bg-[#2CD868] h-2.5 rounded-full" :style="{width: `${Object.keys(currentStageAnswers).length * 100 / currentQuestions.length}%`}"></div>
          </div>
          <div
              class="w-[100px] p-3 font-beanbag text-[14px] text-white uppercase bg-[#57D061] text-center rounded-full flex-shrink-0 cursor-pointer hover:shadow-md"
              @click="submitExam()"
          >Nộp bài</div>
        </div>
        <div class="mt-[30px]">
          <div class="grid grid-cols-4">
            <div class="col-span-1 flex flex-col items-start justify-start gap-10">
              <div class="bg-[#C1EACA] text-[14px] font-medium text-black rounded-[10px] px-4 py-1 inline-flex items-center justify-center font-averta-regular">
                Đã làm <b class="text-[#EF6D13] ml-1">@{{ Object.keys(currentStageAnswers).length }}</b>/<b class="text-[#009951] mr-1"> @{{ currentQuestions.length }}</b> câu
              </div>
              <div v-for="(mondai, idx) in currentMondais">
                <div class="px-2 py-1 flex items-center gap-1 font-gen-jyuu-gothic-medium" :class="[idx === currentMondai ? 'border-l-2 border-[#EF6D13]' : 'border-l border-black']">もんだい <div class="bg-black w-4 h-4 rounded-full inline-flex justify-center items-center text-white font-beanbag text-xs">@{{ idx + 1 }}</div></div>
                <div class="mt-3 flex flex-wrap gap-2 w-[175px]">
                  <div
                      v-for="(question, qIdx) in mondai.questions"
                      :key="question.id"
                      class="rounded-full w-[28px] h-[28px] inline-flex justify-center items-center text-[#07403F] font-beanbag text-[16px] cursor-pointer"
                      :class="[currentStageAnswers[question.id] ? 'bg-[#B2EEFA]' : 'bg-white']"
                      @click="jumpToQuestion(idx, question.id)"
                  >@{{ qIdx + 1 }}</div>
                </div>
              </div>
            </div>
            <div class="col-span-2">
              <div v-if="stageData && stageData.mp3">
                <div class="text-[#C00F0C]">*Lưu ý: File nghe sẽ phát 1 lần duy nhất</div>
<svg class="hidden">
  <symbol
      id="backward"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
  >
    <path
        d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"
    ></path>
    <path d="M5 15V19"></path>
    <path
        d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523
            10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"
    ></path>
  </symbol>

  <symbol id="play" viewBox="0 0 24 24">
    <path d="M0.166748 9.99996V5.84662C0.166748 0.689958 3.81842 -1.42171 8.28675 1.15663L11.8917 3.23329L15.4967 5.30996C19.9651 7.88829 19.9651 12.1116 15.4967 14.69L11.8917 16.7666L8.28675 18.8433C3.81842 21.4216 0.166748 19.31 0.166748 14.1533V9.99996Z" fill="#4E87FF"/>

  </symbol>

  <symbol id="pause" viewBox="0 0 24 24">
    <path
        fill-rule="evenodd"
        d="M6.75 5.25a.75.75 0 01.75-.75H9a.75.75 0 01.75.75v13.5a.75.75 0
          01-.75.75H7.5a.75.75 0 01-.75-.75V5.25zm7.5 0A.75.75 0 0115 4.5h1.5a.75.75 0 01.75.75v13.5a.75.75 0
          01-.75.75H15a.75.75 0 01-.75-.75V5.25z"
        clip-rule="evenodd"
    />
  </symbol>

  <symbol id="forward" viewBox="0 0 24 24">
    <path
        d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
    ></path>
    <path
        d="M13 15V19"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
    ></path>
    <path
        d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18
                          19H17C16.4477 19 16 18.5523 16 18Z"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
    ></path>
  </symbol>

  <symbol id="high" viewBox="0 0 24 24">
    <path d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z" fill="#4E87FF"/>
    <path d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z" fill="#4E87FF"/>
    <path opacity="0.4" d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z" fill="#4E87FF"/>
    <path d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z" fill="#4E87FF"/>

  </symbol>

  <symbol id="off" viewBox="0 0 24 24">
    <path d="M21.5003 19.5407C21.3137 19.5407 21.1387 19.4823 20.9753 19.3657C20.5903 19.074 20.5087 18.5257 20.8003 18.1407C22.632 15.7023 22.632 12.2957 20.8003 9.85735C20.5087 9.47235 20.5903 8.92402 20.9753 8.63235C21.3603 8.34068 21.9087 8.42235 22.2003 8.80735C24.487 11.864 24.487 16.134 22.2003 19.1907C22.0253 19.424 21.7687 19.5407 21.5003 19.5407Z" fill="#4E87FF"/>
    <path d="M23.6353 22.458C23.4487 22.458 23.2737 22.3997 23.1103 22.283C22.7253 21.9913 22.6437 21.443 22.9353 21.058C26.0503 16.9047 26.0503 11.0947 22.9353 6.94133C22.6437 6.55633 22.7253 6.008 23.1103 5.71633C23.4953 5.42467 24.0437 5.50633 24.3353 5.89133C27.917 10.663 27.917 17.3363 24.3353 22.108C24.172 22.3413 23.9037 22.458 23.6353 22.458Z" fill="#4E87FF"/>
    <path opacity="0.4" d="M18.8751 8.645V19.355C18.8751 21.3617 18.1517 22.8667 16.8567 23.59C16.3317 23.8817 15.7484 24.0217 15.1417 24.0217C14.2084 24.0217 13.2051 23.7067 12.1784 23.065L8.77175 20.93C8.53841 20.79 8.27008 20.7083 8.00175 20.7083H6.91675V7.29167H8.00175C8.27008 7.29167 8.53841 7.21 8.77175 7.07L12.1784 4.935C13.8817 3.87334 15.5501 3.68667 16.8567 4.41C18.1517 5.13334 18.8751 6.63834 18.8751 8.645Z" fill="#4E87FF"/>
    <path d="M6.91683 7.29102V20.7077H6.3335C3.51016 20.7077 1.9585 19.156 1.9585 16.3327V11.666C1.9585 8.84268 3.51016 7.29102 6.3335 7.29102H6.91683Z" fill="#4E87FF"/>

  </symbol>
</svg>

<media-controller
    audio
    style="
                      --media-background-color: transparent;
                      --media-control-background: transparent;
                      --media-control-hover-background: transparent;
                      --media-control-width: 100%;
                    "
    class="@container block w-full mt-5 mb-[30px]"
>
  <audio
      slot="media"
      controls="" class="w-full">
    <source
        :src="`https://mp3-v2.dungmori.com/${stageData.mp3}`"
        type="audio/mpeg">
    Your browser does not support the audio element.
  </audio>
  <media-control-bar
      class="px-5 py-[10px] bg-[#E1EBFF] rounded-full items-center shadow-xl shadow-black/5 w-full"
  >
    <media-play-button class="h-10 w-10 p-2 mx-3 rounded-full bg-slate-700">
      <svg slot="play" aria-hidden="true" class="relative left-px">
        <use href="#play" />
      </svg>
      <svg slot="pause" aria-hidden="true">
        <use href="#pause" />
      </svg>
    </media-play-button>
    <media-time-display class="text-[#4E87FF] text-[14px] font-averta"></media-time-display>
    <media-time-range
        disabled
        class="block h-2 min-h-0 p-0 m-2 rounded-md bg-slate-50"
        style="
                          --media-range-track-background: white;
                          --media-time-range-buffered-color: rgb(0 0 0 / 0.02);
                          --media-range-bar-color: rgb(79 70 229);
                          --media-range-track-border-radius: 4px;
                          --media-range-track-height: 0.5rem;
                          --media-range-thumb-background: rgb(79 70 229);
                          --media-range-thumb-box-shadow: 0 0 0 2px rgb(255 255 255 / 0.9);
                          --media-range-thumb-width: 0.25rem;
                          --media-range-thumb-height: 1rem;
                          --media-preview-time-text-shadow: transparent;
                        "
    >
      <media-preview-time-display
          slot="preview"
          class="text-[#4E87FF] text-[14px] font-averta"
      ></media-preview-time-display>
    </media-time-range>
    <media-duration-display
        class="text-[#4E87FF] text-[14px] font-averta"
    ></media-duration-display>
    <media-mute-button>
      <svg slot="high" aria-hidden="true" class="h-5 w-5 fill-slate-500">
        <use href="#high" />
      </svg>
      <svg slot="medium" aria-hidden="true" class="h-5 w-5 fill-slate-500">
        <use href="#high" />
      </svg>
      <svg slot="low" aria-hidden="true" class="h-5 w-5 fill-slate-500">
        <use href="#high" />
      </svg>
      <svg slot="off" aria-hidden="true" class="h-5 w-5 fill-slate-500">
        <use href="#off" />
      </svg>
    </media-mute-button>
  </media-control-bar>
</media-controller>
</div>
<div v-if="currentMondais.length">
  <div v-for="task in currentMondais[currentMondai].tasks">
    <div v-if="task.type == 1" class="font-gen-jyuu-gothic text-[16px] mb-[26px] text-[#212121]" v-html="task.value"></div>
    {{--                  <div v-if="task.type == 5" class="multimedia-item w-full mb-[40px]">--}}
    {{--                    <label>@{{ task.name }}</label>--}}
    {{--                    <audio controls="" class="w-full">--}}
    {{--                      <source--}}
    {{--                          :src="`https://mp3-v2.dungmori.com/${task.link}`"--}}
    {{--                          type="audio/mpeg">--}}
    {{--                      Your browser does not support the audio element.--}}
    {{--                    </audio>--}}
    {{--                  </div>--}}
    <template v-if="task.type == 3">
      <div :id="`question_${task.id}`" class="py-5 px-4 bg-white border border-[#D9D9D9] rounded-[16px] font-gen-jyuu-gothic-medium mt-5 text-[16px] text-[#000000]" v-html="task.value"></div>
      <div class="mt-[68px] grid grid-cols-2 gap-7 mb-[102px]">
        <div
            v-for="(answer, aIdx) in task.answers"
            class="flex items-center gap-2 group hover:bg-[#B2EEFA] cursor-pointer py-1 px-0 rounded-full"
            :class="[Number(currentStageAnswers[task.id]) === Number(answer.id) ? 'bg-[#B1EEFA]' : '' ]"
            @click="selectAnswer(task, answer)"
        >
          <div
              class="group-hover:border-[#B1EEFA] w-10 h-10 border-[3px] rounded-full bg-white text-[#757575] font-beanbag text-[16px] flex justify-center items-center shadow-[0px 0px 18px 0px #21212114] font-bold flex-shrink-0"
              :class="[Number(currentStageAnswers[task.id]) === Number(answer.id) ? 'border-[#B1EEFA]' : 'border-[#D9D9D9]' ]"
          >@{{ aIdx + 1 }}</div>
          <div v-html="answer.value" class="font-gen-jyuu-gothic text-[16px] text-wrap text-[#212121]"></div>
        </div>
      </div>
    </template>
    <template v-if="task.type === 13">
      <div class="border border-[#D9D9D9] rounded-[24px] px-[24px] py-[40px] my-5 bg-white shadow">
        <media-controller
            v-if="JSON.parse(task.value).mp3"
            audio
            style="
                      --media-background-color: transparent;
                      --media-control-background: transparent;
                      --media-control-hover-background: transparent;
                      --media-control-width: 100%;
                    "
            class="@container block w-full mt-5 mb-[30px]"
        >
          <audio
              slot="media"
              controls="" class="w-full">
            <source
                :src="`https://mp3-v2.dungmori.com/${JSON.parse(task.value).mp3}`"
                type="audio/mpeg">
            Your browser does not support the audio element.
          </audio>
          <media-control-bar
              class="px-5 py-[10px] bg-[#E1EBFF] rounded-full items-center shadow-xl shadow-black/5 w-full"
          >
            <media-play-button class="h-10 w-10 p-2 mx-3 rounded-full bg-slate-700">
              <svg slot="play" aria-hidden="true" class="relative left-px">
                <use href="#play" />
              </svg>
              <svg slot="pause" aria-hidden="true">
                <use href="#pause" />
              </svg>
            </media-play-button>
            <media-time-display class="text-[#4E87FF] text-[14px] font-averta"></media-time-display>
            <media-time-range
                disabled
                class="block h-2 min-h-0 p-0 m-2 rounded-md bg-slate-50"
                style="
                          --media-range-track-background: white;
                          --media-time-range-buffered-color: rgb(0 0 0 / 0.02);
                          --media-range-bar-color: rgb(79 70 229);
                          --media-range-track-border-radius: 4px;
                          --media-range-track-height: 0.5rem;
                          --media-range-thumb-background: rgb(79 70 229);
                          --media-range-thumb-box-shadow: 0 0 0 2px rgb(255 255 255 / 0.9);
                          --media-range-thumb-width: 0.25rem;
                          --media-range-thumb-height: 1rem;
                          --media-preview-time-text-shadow: transparent;
                        "
            >
              <media-preview-time-display
                  slot="preview"
                  class="text-[#4E87FF] text-[14px] font-averta"
              ></media-preview-time-display>
            </media-time-range>
            <media-duration-display
                class="text-[#4E87FF] text-[14px] font-averta"
            ></media-duration-display>
            <media-mute-button>
              <svg slot="high" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                <use href="#high" />
              </svg>
              <svg slot="medium" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                <use href="#high" />
              </svg>
              <svg slot="low" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                <use href="#high" />
              </svg>
              <svg slot="off" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                <use href="#off" />
              </svg>
            </media-mute-button>
          </media-control-bar>
        </media-controller>
        <img :src="`{{ asset('cdn/lesson/default/${JSON.parse(task.value)?.img}') }}`" class="mt-5 mx-auto" />
        <div class="font-beanbag mt-5 text-[16px]">@{{ task.text_vi }}</div>
        <div class="flex items-end flex-wrap gap-2 mt-4">
          <template v-for="(block, bIdx) in JSON.parse(task.value).question">
            <div v-if="block.type == 'default' " v-html="block.value"  class="font-gen-jyuu-gothic-medium text-[20px] translate-y-[-10px]"></div>
            <input
                v-model="task.blockAnswers[bIdx]"
                v-if="block.type == 'question'"
                class="bg-[#D9D9D9] font-beanbag text-[20px] p-3 rounded-[12px] text-center focus:outline-[3px] focus:outline-[#4E87FF]"
                :style="{width: `${block.result.length * 20 + 24}px`}"
                @blur="submitBlocks(task, bIdx)"
            />
          </template>
        </div>
      </div>
      <div v-if="JSON.parse(task.value).suggest" class="w-full px-4 py-3 rounded-full bg-[#9EE1A6] font-gen-jyuu-gothic text-[14px]" v-html="JSON.parse(task.value).suggest">
      </div>
    </template>
  </div>
</div>
</div>
</div>
</div>
</div>
</div>
<div v-if="stage > 0 && stage < 3 && isWaiting" v-cloak class="bg-[#F4F5FA] w-screen h-screen overflow-hidden mt-[68px] flex justify-center items-center flex-col">
  <div class="font-beanbag text-[20px] text-[#07403F]">Phần thi tiếp theo</div>
  <div class="font-beanbag text-[40px] text-[#57D061]">@{{ ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'][stage] }}</div>
  <img :src="`{{ asset('assets/img/waiting-banner-${1}.png') }}`" class="my-[97px]" />
  <div class="select-none w-[335px] font-beanbag text-[16px] py-[14px] flex justify-center items-center
          bg-[#57D061] text-[#07403F]  rounded-full mt-[16px] shadow-lg cursor-pointer hover:shadow-md transition-all uppercase" @click="nextStage()">Tiếp tục làm bài</div>
</div>
<div
    v-if="stage > 3"
    v-cloak
    class="bg-gradient-to-br  w-screen h-screen overflow-y-scroll mt-[68px] flex justify-start items-center flex-col relative pb-[168px]"
    :class="[
          result?.is_passed ? 'from-[#E6FFE9] via-[#F3F5F9] to-[#E5FFE9]' : 'from-[#FFFDF)] via-[#FFFFFF] to-[#FFFDF)]'
        ]"
>
  <template v-if="result && lesson.type == 'exam' ">
    <div class="font-beanbag text-[#07403F] text-[24px] mt-[160px] mb-[80px]">
          <span
              class="inline-flex px-2 py-0 rounded-full bg-[#CEFFD8]"
              :class="result.is_passed ? 'bg-[#CEFFD8]' : 'bg-[#FFE8A3]'"
          >@{{ resultStatistics?.correctQuestions?.length }}/@{{ resultStatistics.questions?.length }}</span> câu đúng! <br>
      <span v-if="result.is_passed" v-cloak>Bạn làm tốt lắm!</span>
      <span v-else v-cloak>Cố gắng lên nhé!</span>
    </div>
    <img v-if="result.is_passed" :src="`{{ asset('assets/img/exam_passed.svg') }}`" v-cloak />
    <img v-else :src="`{{ asset('assets/img/exam_failed.svg') }}`" v-cloak />
  </template>
  <template v-else>
    <img v-if="result?.total_score == lesson.total_marks" src="{{ asset('assets/img/perfect_flower.svg') }}" class="select-none w-screen h-screen object-cover absolute top-0 left-0 -z-1" />
    <img v-else-if="result?.is_passed" src="{{ asset('assets/img/passed_flower.svg') }}" class="select-none w-screen h-screen object-cover absolute top-0 left-0 -z-1" />
    <img v-else="result?.is_passed" src="{{ asset('assets/img/failed_flower.svg') }}" class="select-none absolute bottom-0 right-0 -z-1" />
    <div class="font-beanbag text-[36px] text-[#07403F] uppercase flex items-center justify-center gap-1 my-10">
      @{{ lesson.name }}
        <svg v-if="lesson.require" width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6.09789 1.8541C6.69659 0.0114813 9.30341 0.0114808 9.90211 1.8541L10.2451 2.90983C10.5129 3.73387 11.2808 4.2918 12.1473 4.2918H13.2573C15.1948 4.2918 16.0003 6.77103 14.4329 7.90983L13.5348 8.56231C12.8339 9.07159 12.5405 9.97433 12.8083 10.7984L13.1513 11.8541C13.75 13.6967 11.6411 15.229 10.0736 14.0902L9.17557 13.4377C8.4746 12.9284 7.5254 12.9284 6.82443 13.4377L5.92638 14.0902C4.35895 15.229 2.24999 13.6967 2.84869 11.8541L3.19172 10.7984C3.45947 9.97433 3.16615 9.07159 2.46517 8.56231L1.56712 7.90983C-0.000308037 6.77103 0.805244 4.2918 2.74269 4.2918H3.85275C4.7192 4.2918 5.48711 3.73387 5.75486 2.90983L6.09789 1.8541Z" fill="#EC6E23"/>
        </svg>
    </div>
    <div class="w-[950px] bg-white shadow-[0_0_1.35px_0_#4C5D7052] shadow-[0_21.68px_32.52px_0_#617C9A52] rounded-[22px] relative px-[25px] py-[10px]">
      <img src="{{ asset('assets/img/logo-bang.svg') }}" class="absolute bottom-0 left-0 h-[80%]" />
      <div class="px-4 bg-white absolute bottom-2 left-2 text-[#07403F]">22-12-2024</div>

      <div class="flex items-center justify-between border-b-2 border-[#C1EACA] pb-2">
        <img src="{{ asset('assets/img/logo-bang-nho.svg') }}" class="" />
        <div class="flex flex-col items-end">
          <div>
            <ruby class="font-gen-jyuu-gothic text-[15px] text-[#07403F]"><rb>試験</rb><rp>(</rp><rt>しけん</rt><rp>)</rp></ruby>
            <ruby class="font-gen-jyuu-gothic text-[15px] text-[#07403F]"><rb>結果</rb><rp>(</rp><rt>けっか</rt><rp>)</rp></ruby>
            <ruby class="font-gen-jyuu-gothic text-[15px] text-[#07403F]"><rb>発表</rb><rp>(</rp><rt>はっぴょう</rt><rp>)</rp></ruby>
          </div>
          <div class="font-averta-regular text-[18px] text-[#07403F]">Kết quả bài thi</div>
        </div>
      </div>
      <div class="py-4 border-b-2 border-[#C1EACA] flex flex-col gap-1 select-none">
        <div class="flex items-center justify-between">
          <div class="flex flex-col items-start">
            <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]"><rb>氏名</rb><rp>(</rp><rt>しめい</rt><rp>)</rp></ruby>
            <div class="font-averta-regular text-[16px] text-[#07403F]">Họ tên</div>
          </div>
          <div class="w-[750px] min-h-[54px] flex items-center justify-center border-2 border-[#07403F] font-beanbag text-[20px] uppercase rounded-[22px] py-3 z-10 bg-white">
            @{{ result?.user?.name || '' }}</div>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex flex-col items-start">
            <div class="font-gen-jyuu-gothic text-[20px] text-[#07403F]">レベル</div>
            <div class="font-averta-regular text-[16px] text-[#07403F]">Cấp độ</div>
          </div>
          <div class="w-[750px] min-h-[54px] flex items-center justify-center border-2 border-[#07403F] font-beanbag text-[20px] uppercase rounded-[22px] py-3 z-10 bg-white">
            @{{ result?.course }}</div>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex flex-col items-start">
            <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]"><rb>試験</rb><rp>(</rp><rt>しけん</rt><rp>)</rp></ruby>
            <div class="font-averta-regular text-[16px] text-[#07403F]">Kết quả thi</div>
          </div>
          <div
              class="w-[750px] min-h-[54px] flex items-center justify-center border-2 border-[#07403F] rounded-[22px] py-0 gap-5 z-10"
              :class="[result?.is_passed ? 'bg-[#CEFFD8]' : 'bg-[#FFF1C2]']"
          >
            <div class="font-gen-jyuu-gothic text-[20px] text-[#07403F]">
              <ruby v-if="!result?.is_passed"><rb>不</rb><rp>(</rp><rt>ふ</rt><rp>)</rp></ruby>
              <ruby><rb>合格</rb><rp>(</rp><rt>ごうかく</rt><rp>)</rp></ruby>
            </div>
            <div class="font-beanbag text-[20px]">@{{ result?.is_passed ? 'Đỗ' : 'Không Đỗ' }}</div>
            <img src="{{ asset('assets/img/khong-do.svg') }}" />
          </div>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex flex-col items-start gap-16">
            <div class="flex flex-col items-start">
              <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]"><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby>
              <div class="font-averta-regular text-[16px] text-[#07403F]">Điểm</div>
            </div>
            <div class="flex flex-col items-start">
              <div>
                <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]"><rb>参考</rb><rp>(</rp><rt>さんこう</rt><rp>)</rp></ruby>
                <ruby class="font-gen-jyuu-gothic text-[20px] text-[#07403F]"><rb>情報</rb><rp>(</rp><rt>じょうほう</rt><rp>)</rp></ruby>
              </div>

              <div class="font-averta-regular text-[16px] text-[#07403F]">Thông tin <br> tham khảo</div>
            </div>
          </div>
          <div class="w-[750px] min-h-[54px] h-[316px] items-center justify-center border-2 border-[#07403F] rounded-[22px] py-0 bg-white z-10 grid grid-cols-4 grid-rows-5 gap-0">
            <div class="col-span-3 row-span-2 flex flex-col justify-center items-center py-6 border-b border-r border-[#A6CCCB] h-full w-full">
              <div>
                <ruby><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby><ruby><rb>区分別</rb><rp>(</rp><rt>くぶんべつ</rt><rp>)</rp></ruby><ruby><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby>
              </div>
              <div class="font-averta-regular text-[16px] text-[#07403F]">Điểm đạt được từng phần</div>
            </div>
            <div class="row-span-2 col-start-4 py-6 flex flex-col justify-center items-center border-b border-l border-[#A6CCCB] h-full w-full">
              <div><ruby><rb>総合</rb><rp>(</rp><rt>そうごう</rt><rp>)</rp></ruby><ruby><rb>得点</rb><rp>(</rp><rt>とくてん</rt><rp>)</rp></ruby></div>
              <div class="font-averta-regular text-[16px] text-[#07403F]">Tổng điểm</div>

            </div>
            <div
                class="row-span-3 col-start-4 row-start-3 flex flex-col justify-center items-center border-t border-l border-[#A6CCCB] py-6 h-full w-full rounded-br-[22px]"
                :class="[result?.is_passed ? 'bg-[#CEFFD8]' : 'bg-[#FFF1C2]']"
            >
              <div class="font-beanbag text-[#07403F] text-[20px]">@{{ result?.total_score }}/180</div>
              <div class="font-averta-regular text-[16px] mt-[35px]">Điểm đỗ @{{ lesson.pass_marks }}/180</div>
            </div>
            <div class="col-span-2 row-span-2 col-start-1 row-start-3 flex flex-col justify-center items-center border-t border-b border-r border-[#A6CCCB] py-6 h-full w-full">
              <div><ruby><rb>言語</rb><rp>(</rp><rt>げんご</rt><rp>)</rp></ruby><ruby><rb>知識</rb><rp>(</rp><rt>ちしき</rt><rp>)</rp></ruby>(<ruby><rb>文字</rb><rp>(</rp><rt>もじ</rt><rp>)</rp></ruby><font _mstmutation="1">・</font><ruby><rb>語彙</rb><rp>(</rp><rt>ごい</rt><rp>)</rp></ruby><font _mstmutation="1">・</font><ruby><rb>文法</rb><rp>(</rp><rt>ぶんぽう</rt><rp>)</rp></ruby><font _mstmutation="1">)・</font><ruby><rb>読解</rb><rp>(</rp><rt>どっかい</rt><rp>)</rp></ruby></div>
              <div class="font-averta-regular text-[16px] text-[#07403F] text-center">
                Kiến thức ngôn ngữ <br />
                (Từ vựng/Ngữ pháp) & Đọc hiểu
              </div>
            </div>
            <div class="col-span-2 col-start-1 row-start-5 flex justify-center items-center border-t border-r border-[#A6CCCB] py-6 gap-1 h-full w-full">
              <div><ruby><rb>読解</rb><rp>(</rp><rt>どっかい</rt><rp>)</rp></ruby></div>
              <div class="font-averta-regular text-[16px] text-[#07403F]">Nghe hiểu</div>
            </div>
            <div class="row-span-2 col-start-3 row-start-3 flex justify-center items-center border-t border-b border-l border-r border-[#A6CCCB] py-6 gap-1 h-full w-full">
              <div class="font-beanbag text-[#07403F] text-[20px]">@{{ result.score_1 }}/120</div>
            </div>
            <div class="col-start-3 row-start-5 flex justify-center items-center border-l border-t border-r border-[#A6CCCB] py-6 gap-1 h-full w-full">
              <div class="font-beanbag text-[#07403F] text-[20px]">@{{ result.score_3 }}/60</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  <div class="w-[514px] py-[14px] font-beanbag uppercase text-[14px] text-[#57D061] border-2 border-[#57D061] rounded-full mt-[56px] text-center cursor-pointer" @click="openResultModal">Xem chi tiết kết quả</div>
  <div class="w-[514px] py-[14px] font-beanbag uppercase text-[14px] text-[#57D061]  flex items-center gap-3">
    <div class="w-full py-[14px] text-[#07403F] bg-[#FFF193] rounded-full text-center cursor-pointer" @click="stage = 0">Làm lại</div>
    <div class="w-full py-[14px] text-[#07403F] bg-[#57D061] rounded-full text-center cursor-pointer">Học tiếp</div>
  </div>
  <modal v-if="showModal" @close="showModal = false" v-cloak>
    <h3 slot="header" class="mt-[50px]">Chi tiết bài làm</h3>
    <div slot="body" class="flex gap-[30px] mt-[24px]">
      <div class="w-[400px] pr-[40px] border-r-[3px] border-[#D0D3DA]">
        <div class="p-5 bg-white shadow-md rounded-[20px]">
          <div class="flex items-center justify-between">
            <div class="font-averta-semibold text-[16px]">Kết quả</div>
            <div
                class="p-[10px] rounded-full font-averta-regular text-[16px] leading-none"
                :class="[result?.is_passed ? 'bg-[#57D061]' : 'bg-[#E8B931]']"
            >@{{ result?.is_passed ? 'Đạt' : 'Chưa đạt' }}</div>
          </div>
          <div class="flex items-center justify-between mt-5">
            <div class="font-averta-semibold text-[16px]">Điểm của bạn</div>
            <div
                class="font-averta-regular text-[16px] leading-none mr-[20px]"
                :class="[result?.is_passed ? 'text-[#CEFFD8]' : 'text-[#C00F0C]']"
            >
              <template v-if="lesson.type == 'last_exam'">@{{ result.total_score }}/180</template>
              <template>@{{ resultStatistics?.correctQuestions.length }}/@{{ resultStatistics?.questions?.length }}</template>
            </div>
          </div>
          <div class="flex items-center gap-2 mt-5">
            <div class="font-averta-regular text-[14px]">Điểm đạt: > 85% (@{{ Math.ceil(resultStatistics?.questions?.length * 0.85) }}/@{{ resultStatistics?.questions?.length }} câu) </div>
          </div>
        </div>
        <div v-if="examData">
          <div v-for="part in examData.exam_parts" class="mt-[30px] pb-[32px] border-b border-[#D9D9D9]">
            <div class="font-beanbag text-[16px] text-[#07403F]">@{{ ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'][part.type - 1] }}</div>
            <div class="bg-[#C1EACA] text-[14px] font-medium text-black rounded-[10px] px-4 py-1 inline-flex items-center justify-center font-averta-regular">
              Đúng <b class="text-[#EF6D13] ml-1">@{{ resultStatistics.partStats[part.type - 1].correct }}</b>/<b class="text-[#009951] mr-1"> @{{ part.questions.length }}</b> câu
            </div>
            <div v-for="(mondai, idx) in part.mondais">
              <div class="mt-8 px-2 py-1 flex items-center gap-1 font-gen-jyuu-gothic-medium border-l-2 border-black">もんだい <div class="bg-black w-4 h-4 rounded-full inline-flex justify-center items-center text-white font-beanbag text-xs">@{{ idx + 1 }}</div></div>
              <div class="mt-3 flex flex-wrap gap-2">
                <div
                    v-for="(question, qIdx) in mondai.questions"
                    :key="question.id"
                    class="rounded-full w-[28px] h-[28px] inline-flex justify-center items-center text-[#07403F] font-beanbag text-[16px] cursor-pointer border-[3px]"
                    :class="[!questionState(question) ? 'bg-white border-[#D9D9D9]' : (questionState(question) === 'correct' ? 'bg-[#95FF99] border-[#57D061]' : 'bg-[#FDD3D0] border-[#FF7C79]')]"
                >
                  @{{ qIdx + 1 }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-grow-1 w-full">
        <template v-if="lesson.type == 'last_exam'">
          <div class="p-[35px] bg-white rounded-[30px] shadow-md">
            <div class="text-center relative">
              <img v-if="result && result.is_passed" :src="`{{ asset('assets/img/result-passed.svg') }}`" class="mx-auto" />
              <img v-else :src="`{{ asset('assets/img/result-failed.svg') }}`" class="mx-auto" />
              <div class="absolute top-0 left-0 h-full w-full flex justify-center items-center font-averta-semibold text-[20px] text-[#1E1E1E] ml-[5%] mb-[5%]">
                <span v-if="result && result.is_passed">Bạn đã sẵn sàng thi JLPT N5!</span>
                <span v-else>Cố gắng thêm bạn nha!</span>
              </div>
            </div>
            <div class="flex justify-center items-center">
              <div class="w-[600px] h-[600px] relative selectr-none">
                <canvas id="resultChart" class="max-w-full max-h-full"></canvas>
                <img :src="`{{ asset('assets/img/radar-chart.svg') }}`" class="mx-auto absolute top-[14.6%] left-[13.8%] w-[75%]" />
                <img :src="`{{ asset('assets/img/radar-axis.svg') }}`" class="mx-auto absolute top-[14%] left-[49%] h-[36%]" />
              </div>
            </div>
            <div class="mt-[80px] flex gap-[28px] items-start">
              <img :src="`{{ asset('assets/img/giao-vien.svg') }}`" />
              <div class="flex flex-col gap-3">
                <div v-if="resultStatistics.vocabularyCorrect < resultStatistics.vocabulary.length" class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]">
                  Trong bài thi, có một số câu phần Từ vựng bạn đã đánh rơi điểm 1 cách đáng tiếc. Mình hãy tranh thủ ôn tập lại và cố gắng đọc kĩ đề khi làm bài nhé
                  <br>
                  Mẹo nhỏ: Ghi nhớ ví dụ đi kèm từ vựng sẽ giúp bạn hiểu đúng cách sử dụng của từ!
                </div>
                <div v-if="resultStatistics.kanjiCorrect < resultStatistics.kanji.length" class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]">
                  Trong bài thi, có một số câu phần Chữ Hán bạn đã đánh rơi điểm 1 cách đáng tiếc. Mình hãy tranh thủ ôn tập lại và cố gắng đọc kĩ đề khi làm bài nhé
                  <br>
                  Mẹo nhỏ: Ghi nhớ ví dụ đi kèm chữ Hán sẽ giúp bạn hiểu đúng cách sử dụng của từ!
                </div>
                <div v-if="resultStatistics.grammarCorrect < resultStatistics.grammar.length" class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]">
                  Trong bài thi, có một số câu phần Ngữ pháp bạn đã đánh rơi điểm 1 cách đáng tiếc. Mình hãy tranh thủ ôn tập lại và cố gắng đọc kĩ đề khi làm bài nhé
                  <br>
                  Mẹo nhỏ: Ghi nhớ ví dụ đi kèm từ vựng sẽ giúp bạn hiểu đúng cách sử dụng của từ!
                </div>
                <div v-if="resultStatistics.readingCorrect < resultStatistics.reading.length" class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]">
                  Trong bài thi, có một số câu phần Đọc hiểu bạn đã đánh rơi điểm 1 cách đáng tiếc. Mình hãy tranh thủ ôn tập lại và cố gắng đọc kĩ đề khi làm bài nhé
                  <br>
                  Mẹo nhỏ: Ghi nhớ ví dụ đi kèm từ vựng sẽ giúp bạn hiểu đúng cách sử dụng của từ!
                </div>
                <div v-if="resultStatistics.listeningCorrect < resultStatistics.listening.length" class="p-4 rounded-[16px] bg-[#CEFFD8] font-averta-regular text-[14px] text-[#1E1E1E]">
                  Trong bài thi, có một số câu phần Nghe hiểu bạn đã đánh rơi điểm 1 cách đáng tiếc. Mình hãy tranh thủ ôn tập lại và cố gắng đọc kĩ đề khi làm bài nhé
                  <br>
                  Mẹo nhỏ: Ghi nhớ ví dụ đi kèm từ vựng sẽ giúp bạn hiểu đúng cách sử dụng của từ!
                </div>
              </div>
            </div>
          </div>
          <div v-if="examData" class="mt-[40px] grid grid-cols-3 gap-5">
            <div v-for="part in examData.exam_parts" class="p-5 bg-white border border-[#D9D9D9] rounded-[20px] ">
              <div class="font-beanbag text-[#07403F] text-[14px] uppercase">@{{ ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'][part.type - 1] }}</div>
              <div class="font-averta-regular">Đúng: @{{ resultStatistics.partStats[part.type - 1].correct }}/@{{ part.questions.length }}</div>
              <div class="mt-5 flex flex-col gap-1 w-full">
                <div v-for="(mondai, mIdx) in part.mondais" class="flex justify-between items-center w-full">
                  <div
                      class="bg-[#E6E6E6] p-1 w-[90%] rounded-full font-beanbag uppercase text-[10px] relative"
                      :class="[resultStatistics.partStats[part.type - 1].mondais[mIdx].correct === mondai.questions.length ? 'text-[#57D061]' : (resultStatistics.partStats[part.type - 1].mondais[mIdx].correct > 0 ? 'text-[#E8B931]' : 'text-[#B3B3B3]')]"
                  >
                    <div
                        class="absolute top-0 left-0 h-full w-full rounded-full"
                        :class="[resultStatistics.partStats[part.type - 1].mondais[mIdx].correct === mondai.questions.length ? 'bg-[#CEFFD8]' : (resultStatistics.partStats[part.type - 1].mondais[mIdx].correct > 0 ? 'bg-[#FFF1BB]' : 'bg-transparent')]"
                        :style="{width: `${resultStatistics.partStats[part.type - 1].mondais[mIdx].correct * 100 / mondai.questions.length}%`}"
                    ></div>
                    <div class="opacity-0">@{{ partMondais[part.type - 1][mIdx] }}</div>
                    <div class="absolute top-0 left-0 z-10 p-1">@{{ partMondais[part.type - 1][mIdx] }}</div>
                  </div>
                  <div class="font-beanbag text-[#1E1E1E] text-[10px]">@{{ resultStatistics.partStats[part.type - 1].mondais[mIdx].correct }}/@{{ mondai.questions.length }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div :class="[lesson.type == 'exam' ? '' : 'mt-[44px]']">
          <div v-if="lesson.type == 'last_exam'" class="font-averta-regular text-[20px]">Chi tiết đáp án</div>
          <div v-for="part in examData.exam_parts" class="mt-[22px]">
            <div v-if="lesson.type == 'last_exam'" class="bg-[#F4F5FA] py-2 px-[14px] font-beanbag text-[14px] text-[#07403F] mb-5">@{{ ['Từ vựng - Chữ Hán', 'Ngữ pháp - Đọc hiểu', 'Nghe hiểu'][part.type - 1] }}</div>
            <div v-for="mondai in part.mondais">
              <template v-for="task in mondai.tasks">
                <div v-if="task.type == 1" class="font-gen-jyuu-gothic text-[14px] mb-[26px]" v-html="task.value"></div>
                <div v-if="task.type == 5" class="multimedia-item w-full mb-[40px]">
                  <label>@{{ task.name }}</label>
                  <media-controller
                      audio
                      style="
                      --media-background-color: transparent;
                      --media-control-background: transparent;
                      --media-control-hover-background: transparent;
                      --media-control-width: 100%;
                    "
                      class="@container block w-full mt-5 mb-[30px]"
                  >
                    <audio
                        slot="media"
                        controls="" class="w-full">
                      <source
                          :src="`https://mp3-v2.dungmori.com/${part.mp3}`"
                          type="audio/mpeg">
                      Your browser does not support the audio element.
                    </audio>
                    <media-control-bar
                        class="px-5 py-[10px] bg-[#E1EBFF] rounded-full items-center shadow-xl shadow-black/5 w-full"
                    >
                      <media-play-button class="h-10 w-10 p-2 mx-3 rounded-full bg-slate-700">
                        <svg slot="play" aria-hidden="true" class="relative left-px">
                          <use href="#play" />
                        </svg>
                        <svg slot="pause" aria-hidden="true">
                          <use href="#pause" />
                        </svg>
                      </media-play-button>
                      <media-time-display class="text-[#4E87FF] text-[14px] font-averta"></media-time-display>
                      <media-time-range
                          disabled
                          class="block h-2 min-h-0 p-0 m-2 rounded-md bg-slate-50"
                          style="
                          --media-range-track-background: white;
                          --media-time-range-buffered-color: rgb(0 0 0 / 0.02);
                          --media-range-bar-color: rgb(79 70 229);
                          --media-range-track-border-radius: 4px;
                          --media-range-track-height: 0.5rem;
                          --media-range-thumb-background: rgb(79 70 229);
                          --media-range-thumb-box-shadow: 0 0 0 2px rgb(255 255 255 / 0.9);
                          --media-range-thumb-width: 0.25rem;
                          --media-range-thumb-height: 1rem;
                          --media-preview-time-text-shadow: transparent;
                        "
                      >
                        <media-preview-time-display
                            slot="preview"
                            class="text-[#4E87FF] text-[14px] font-averta"
                        ></media-preview-time-display>
                      </media-time-range>
                      <media-duration-display
                          class="text-[#4E87FF] text-[14px] font-averta"
                      ></media-duration-display>
                      <media-mute-button>
                        <svg slot="high" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                          <use href="#high" />
                        </svg>
                        <svg slot="medium" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                          <use href="#high" />
                        </svg>
                        <svg slot="low" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                          <use href="#high" />
                        </svg>
                        <svg slot="off" aria-hidden="true" class="h-5 w-5 fill-slate-500">
                          <use href="#off" />
                        </svg>
                      </media-mute-button>
                    </media-control-bar>
                  </media-controller>
                </div>
                <template v-if="task.type == 3">
                  <div :id="`question_${task.id}`" class="py-5 px-4 bg-white border border-[#D9D9D9] rounded-[10px] font-gen-jyuu-gothic-medium mt-5" v-html="task.value"></div>
                  <div class="mt-[68px] mb-[25px] grid grid-cols-2 gap-7">
                    <div
                        v-for="(answer, aIdx) in task.answers"
                        class="flex items-center gap-2 cursor-pointer py-1 px-0 rounded-full"
                    >
                      <div
                          class="w-10 h-10 border-[3px] rounded-full bg-white text-[#757575] font-beanbag text-[16px] flex justify-center items-center shadow-[0px 0px 18px 0px #21212114] font-bold flex-shrink-0"
                          :class="[answer.grade > 0 ? 'bg-[#95FF99] border-[#57D061]' : (Object.values(userAnswers).includes(answer.id) ? 'bg-[#FDD3D0] border-[#FF7C79]' : 'bg-white border-[#D9D9D9]')]"
                      >@{{ aIdx + 1 }}</div>
                      <div v-html="answer.value" class="font-gen-jyuu-gothic text-[14px] text-wrap"></div>
                    </div>
                  </div>
                </template>
                <template v-if="task.type === 13">
                  <div class="border border-[#D9D9D9] rounded-[8px] px-[24px] py-[60px] bg-white shadow mt-5">
                    <img :src="`{{ asset('cdn/lesson/default/${JSON.parse(task.value)?.img}') }}`" class="mt-5 mx-auto" />
                    <div class="font-beanbag mt-5 text-[14px]">@{{ task.text_vi }}</div>
                    <div class="flex items-start flex-wrap gap-2 mt-5">
                      <template v-for="block in JSON.parse(task.value).question">
                        <div v-if="block.type == 'default' " v-html="block.value"  class="font-gen-jyuu-gothic-medium text-[20px]"></div>
                        <div v-if="block.type == 'question'" class="bg-[#D9D9D9] font-beanbag text-[20px] p-3 rounded-[12px] text-center" :style="{width: `${block.result.length * 20 + 24}px`}">
                          <div v-html="block.value"></div>
                        </div>
                      </template>
                    </div>
                  </div>
                </template>
                <div v-if="task.explain" class="bg-[#F0FFF1] p-3 rounded-[20px] mb-[30px] mt-5">
                  <div class="font-beanbag uppercase font-[12px]">* Giải thích</div>
                  <div class="mt-[20px]" v-html="task.explain"></div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </modal>
</div>
</div>
</script>


