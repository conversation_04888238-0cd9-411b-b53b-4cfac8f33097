@extends('frontend._layouts.default')

@section('title') Khóa {{ $thisLesson->getGroupOfLesson->getCourseOfGroup->name }} | {{ $thisLesson->name }} @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON>y tiếng N<PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image')
    @if (!is_null($thisLesson) && $thisLesson->avatar_name != null && $thisLesson->avatar_name != "")
      {{url('cdn/lesson/default')}}/{{ $thisLesson->avatar_name }}
    @else
      {{url('assets/img/oglogo.png')}}
    @endif
@stop
@section('author') DUNGMORI @stop

@section('content')
<link href="https://vjs.zencdn.net/7.11.4/video-js.css" rel="stylesheet" />

<style type="text/css">
    #lesson__progress{display: none;}
    .comment-tab { border-bottom: 1px solid #fff !important; margin-bottom: 0; }
    .comment-tab .active > a {background-color: #F0F6FF !important;}
    .nav-pills > li.active > a{color: #124896; font-size: 14px; text-align: center;}
    .comment-tab .active > .active{ color: #124896; border: 1px solid #124896 !important; }
    .list-comments .comment-item .comment-content .name b {color: #105B89 !important; }
    .comment-action > a{color: #105B89 !important;}
    .child-comment-item .comment-content .child-name b {color: #105B89 !important; }
    /*.list-comments .input-comment-container .post-comment-btn {background: #474DDA !important;}*/
    .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {background: #474DDA !important;}
    .list-comments .load-more-comment {background: #E5E6FF !important; color: #474DDA; font-weight: bold;}
    .comment-tab li{width: 50%;}
    .comment-tab li a{ font-size: 14px; text-align: center; color: #124896; text-decoration: underline; }
    .nav-pills > li.active > a:hover{color: #666;}
    .list-comments .comment-item .comment-content .name b { width: 100%; }

    a:hover {
        color: white;
    }
    .course-cta__fb {
        background-color: #8486F1;
        color: white;
        padding: 8px 20px;
    }
    .course-cta__fb.active {
        background-color: white;
        color: #8486F1;
        border: solid 2px #8486F1;
    }
    .submit-exam-btn {
        background: #8486F1;
        box-shadow: 0px 0px 35.7718px rgba(0, 0, 0, 0.1);
        border-radius: 10.7315px;
        padding: 15px;
        font-size: 16px;
        font-weight: 700;
    }
    .time-block-wrapper {
        position: sticky;
        top: 190px;
    }
    .time-block {
        border: 1px solid #8486F1;
        border-top-width: 4px;
        border-radius: 5px;
        padding: 16px 23px;
    }
    .list-video-area-parent {
        /*margin-top: -20px;*/
        padding: 0;
        border: 2px dashed #474DDA;
        border-radius: 10px;

    }
    .list-video-area-parent .panel-heading {
        background-color: #D0D1FB;
        padding: 22px 35px;
        border-radius: 5px 5px 0 0;
    }
    .list-video-area-parent .panel-body {
        background-color: #F1F1FF;
        padding: 10px 22px;
        border-radius: 5px 5px 0 0;
    }
    .video-item {
        font-family: Quicksand, Arial, sans-serif;
        font-weight: 400;
        font-size: 16px;
        padding: 12px 10px;
        border-bottom: 1px solid #8486F1;
        cursor: pointer;
    }
    .video-item .video-title {
        color: black;
    }
    .video-item:last-child {
        border-bottom: none;
    }
    .reading-block {
        position: fixed;
        top: 210px;
        margin-left: -10px;
        background: white;
        z-index: 2;
        width: 660px !important;
        height: 40%;
        overflow-y: scroll;
        border: 2px solid #000;
        padding: 20px;
    }
</style>

<?php $tab = 1; if(isset($_GET['tab'])) $tab = $_GET['tab']; ?>

<div class="main" id="main-lesson-ld">
    @include('frontend.course.course-cta-ld', ['course' => $course, 'unlock' => $unlock, 'dark' => true, 'color' => '#8486F1'])
    {{-- luyện đề tổng hợp --}}

    <div class="main-center main-course flex">

        <div class="popup-result" v-show="showResult == true" style="display: none;">
            <i class="zmdi zmdi-close" v-on:click="closePopupResult"></i>
            <h2>Kết quả của bạn</h2>
            <div v-if="result != null && ldth == false">
                <h1>
                    <i style="color: green" class="zmdi zmdi-check-circle" v-if="result.grade >= {{ $thisLesson->pass_marks }}" ></i>
                    <i style="color: red" class="zmdi zmdi-close-circle" v-if="result.grade < {{ $thisLesson->pass_marks }}"></i> &nbsp;@{{result.grade}}/@{{result.total_grade}}
                </h1>
                <p style="text-align: center;">Điểm đạt yêu cầu: {{ $thisLesson->pass_marks }}/{{ $thisLesson->total_marks }}</p>
            </div>
            <div v-if="result != null && ldth == true">
                <img class="img-bg" src="{{url('/assets/img/ld/kq.png')}}"/>
                <div class="rinfo">
                    <span class="ru_name">@{{ result.username }}</span>
                    <span class="ru_level">{{ $course->name }}</span>
                    <span class="ru_pass">
                        <b v-if="result.passed == 1">Passed ✓</b>
                        <b v-if="result.passed == 0">Not Pass ✘</b>
                    </span>
                    <span class="ru_score1">@{{result.score_data.s1}}</span>
                    <span class="ru_score2">@{{result.score_data.s2}}</span>
                    <span class="ru_score3">@{{result.score_data.s3}}</span>
                    <span class="ru_total">@{{result.grade}}</span>
                </div>
            </div>
            <div class="btn-do-again" v-on:click="closePopupResult"><i class="zmdi zmdi-refresh"></i> Làm lại</div>
        </div>
        <div class="main-left main-left-ld" style="float: unset" :style="ldth == true && tab == 1 ? 'margin-top: 0' : 'margin-top: 10px'">
            <div class="flex items-center justify-between course-cta__right mt-2.5 mb-7.5">
                @if(Route::currentRouteName() == 'frontend.lesson.detail' && $unlock)
                  <div class="course-cta__fb mr-3 flex items-center" :class="[tab == 1 ? 'active' : '']" v-on:click="switchTab(1)">
                    <img width="25px" v-show="tab == 1" src="{{url('/assets/img/ld/tab1a.png')}}" style="display: none;" />
                    <img width="25px" v-show="tab != 1" src="{{url('/assets/img/ld/tab1.png')}}"/>
                    <span class="ml-2">Làm đề ngay</span>
                  </div>
                  <div class="course-cta__fb mr-3 flex items-center" :class="[tab == 2 ? 'active' : '']" v-on:click="switchTab(2)">
                    <img width="25px" v-show="tab == 2" src="{{url('/assets/img/ld/tab2a.png')}}" style="display: none;"/>
                    <img width="25px" v-show="tab != 2" src="{{url('/assets/img/ld/tab2.png')}}"/>
                    <span class="ml-2">Video chữa đề</span>
                  </div>
                  <div class="course-cta__fb mr-3 flex items-center" :class="[tab == 3 ? 'active' : '']" v-on:click="switchTab(3)">
                    <img width="25px" v-show="tab == 3" src="{{url('/assets/img/ld/tab3a.png')}}" style="display: none;"/>
                    <img width="25px" v-show="tab != 3" src="{{url('/assets/img/ld/tab3.png')}}"/>
                    <span class="ml-2">Xem lại bài làm</span>
                  </div>
                @else
                  <a class="course-cta__fb mr-3" href="https://luyende.dungmori.com/online" style="background-color: #8486F1">
                    <span>Giới thiệu khoá học</span>
                  </a>
                @endif
            </div>
            <div v-if="ldth == true && tab == 1" class="heading-bar" style="float: unset; margin: 0;width: 100%;position: sticky; top: 76px;z-index: 3;">
                <div class="panel-tab">
                    <div class="btn" v-bind:style="(part == 1) ? 'background: #eeeeff;':''" v-on:click="loadPart('1')">Từ vựng chữ Hán</div>
                    <div class="btn" v-bind:style="(part == 2) ? 'background: #eeeeff;':''" v-on:click="loadPart('2')">Ngữ pháp - Đọc hiểu</div>
                    <div class="btn" v-bind:style="(part == 3) ? 'background: #eeeeff;':''" v-on:click="loadPart('3')">Nghe hiểu</div>
                </div>
            </div>
            <div class="exercise-container" v-show="tab == 1" style="float: unset" :style="ldth == true && tab == 1 ? 'margin-top: -20px' : ''" v-cloak>
                {{-- luyện đề kỹ năng --}}
                <div v-if="ldth == false">
                    <div class="heading-bar"> <h4 style="font-weight: bold; text-align: center; padding: 5px 0 4px 0;float: unset;">{{$thisLesson->name}} ~ {{$thisLesson->getGroupOfLesson['name']}}</h4> </div>
                    <div class="mp3-container" v-show="mp3 != null">
                        <audio controls="controls" id="audioSource">
                            <source type="audio/mpeg" :src="'https://mp3-v2.dungmori.com/'+ mp3">
                        </audio>
                    </div>
                    <div v-for="(question, index) in questions" class="questions-container">
                        <b class="question" v-html="question.value"></b>
                        <div class="answer-container">
                            <div class="answer-item" v-for="(answer, index2) in question.answers" v-bind:style="answer.value.length >= 18 ? 'width:100%;' : 'width: 50%;'">
                                <input :name="question.id" class="answers-radio" :id="answer.id" type="radio" :value="answer.id" v-model="userAnswers[question.id]"/>
                                {{-- <label class="answers-label" :for="answer.id" v-if="answer.grade > 0" style="color: green;">@{{index2 + 1}}. @{{answer.value}}</label>
                                <label class="answers-label" :for="answer.id" v-if="answer.grade == 0">@{{index2 + 1}}. @{{answer.value}}</label> --}}
                                <label class="answers-label" :for="answer.id">@{{index2 + 1}}. @{{answer.value}}</label>

                            </div>
                        </div>
                    </div>
                </div>
                <div
                    v-if="currentQBlock && currentQBlock.value"
                    class="question reading-block"
                >
                    <b v-html="currentQBlock.value"></b>
                    <div @click="currentQBlock = null" style="position: fixed;width: 30px;height: 30px;display: flex;justify-content: center;align-items: center;background: red;top: calc(50% + 90px);left: calc(50% + 116px);cursor: pointer">
                        <i class="fa fa-times" style="position: absolute; color: white;"></i>
                    </div>
                </div>
                {{-- luyện đề tổng hợp --}}
                <div v-if="ldth == true">
                    <div class="mp3-container" v-show="part == 3" v-cloak>
                        <audio controls="controls" id="audioSource">
                            <source type="audio/mpeg" :src="'https://mp3-v2.dungmori.com/'+ mp3">
                        </audio>
                    </div>
                    <div v-for="(question, index) in questions" :key="'question-block-' + question.id" :class="'questions-container type_ld_'+ question.type_ld" :id="'type-ld-'+ question.id">
                        <template>
                            <span v-if="isLongQuestion(question) && (!currentQBlock || question.id != currentQBlock.id)" @click="pinQuestion(question)" class="a-cursor-pointer">
                                <i class="fa fa-thumb-tack" style="color: red;"></i>
                            </span>
                            <b
                                class="question"
                                :id="'question-block-' + question.id"
                                ref="questionBlock"
                                :data-id="question.id"
                                :data-value="question.value"
                                v-html="question.value"
                                v-if="!currentQBlock || currentQBlock.id != question.id"
                            ></b>
                            <div class="answer-container">
                                <div class="answer-item" v-for="(answer, index2) in question.answers" v-bind:style="answer.value.length >= 18 ? 'width:100%;' : 'width: 50%;'">
                                    <input :name="question.id" class="answers-radio" :id="answer.id" type="radio" :value="answer.id" v-model="userAnswers[question.id]"/>
                                    {{-- <label class="answers-label" :for="answer.id" v-if="answer.grade > 0" style="color: green;">@{{index2 + 1}}. @{{answer.value}}</label>
                                    <label class="answers-label" :for="answer.id" v-if="answer.grade == 0">@{{index2 + 1}}. @{{answer.value}}</label> --}}
                                    <label class="answers-label" :for="answer.id">@{{index2 + 1}}. @{{answer.value.replace(/\d\. */, '')}}</label>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

            </div>

            <div class="guides-container" v-show="tab == 2" style=" box-shadow: none;" v-cloak>
                <div class="cover-container">
                    @if($video == null)
                        <script>
                            var mdaId = "{{base64_encode(base64_encode(base64_encode("empty")))}}";
                            var listServers = {!! json_encode($servers) !!};
                            var playerId = '{{$random_id}}';
                            lessonInfo        = {!! json_encode($lessonInfo) !!};
                            lessonDetail      = {!! json_encode($thisLesson) !!};
                            lesson_otherVideo = {!! json_encode($otherVideo) !!};
                            courseUrl         = "{{ $course->SEOurl }}";
                        </script>
                    @elseif($video != null && $video->server != 'youtube')
                        @if($video->type == 2)
                            @include('frontend.course.components.mp4-ld')
                        @endif
                    @endif
                    <div v-if="currentResult" v-html="currentResult" style="width: 100%; padding: 10px 1px;"></div>
                    <div class="app-pdf" style="width: 100%; float: left;" v-if="pdf != null">
                        <div role="toolbar" class="toolbar" >
                            <div class="pager" style="margin-top: 0;">
                                <p style="color: #555"><i class="fa fa-file-pdf-o" style="color: red;"></i> Tài liệu PDF</p>
                            </div>
                        </div>
                        <div id="pdf-embed"></div>
                    </div>

                    <div class="empty-box" v-if="pdf == null && currentMediaName == 'empty' && currentResult"> 😂 &nbsp; Chưa có nội dung chữa đề </div>

                </div>

                <template>
                    <div class="comment-container mt-5" id="comment-container">
                        <ul class="nav nav-pills comment-tab">
                            <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a>
                            </li>
                            <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng
                                    facebook</a></li>
                        </ul>
                        <div class="tab-content">
                            <div id="user-comment-content" class="tab-pane fade in active">
    
                                @if(Auth::check())
                                    <comments meid="{{ Auth::user()->id }}"
                                              avatar="{{ Auth::user()->avatar }}"
                                              tbid="{{ $thisLesson->id }}"
                                              tbname="lesson"
                                              num-posts="15"
                                              background="#fff"
                                              ref="comment">
                                    </comments>
                                @else
                                    <comments tbid="{{ $thisLesson->id }}"
                                              tbname="lesson"
                                              num-posts="15"
                                              background="#fff"
                                              ref="comment">
                                    </comments>
                                @endif
    
                            </div>
                            <div id="facebook-comment-content" class="tab-pane fade">
                                <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <div class="exercise-container history-container" v-if="tab == 3" v-cloak>
                <h4>Đáp án đề</h4>
                <div v-for="(question, index) in compareQuestions" class="questions-container-history" v-if="history.length > 0">
                    <template v-if="!showOnlyWrong || isCorrect(question)">
                        <b class="question" v-html="question.value"></b>
                        <div class="answer-container">
                            <div class="answer-item" v-for="(answer, index2) in question.answers">
                                {{-- <input class="answers-radio" :id="answer.id" type="radio" :value="answer.id"/> --}}
                                <label class="answers-label a-l-n" :id="'c-q-'+ answer.id" v-if="answer.grade > 0" style="color:green; font-weight: bold;">
                                    @{{index2 + 1}}. @{{answer.value}} <i class="fa fa-check"></i>
                                </label>
                                <label class="answers-label a-l-n" :id="'c-q-'+ answer.id" v-if="answer.grade == 0">@{{index2 + 1}}. @{{answer.value}}</label>
                            </div>
                        </div>
                    </template>

                </div>

                <div class="empty-box" v-show="history.length == 0" style="display: none; border: none; font-weight: bold; font-size: 16px;">Bạn chưa có dữ liệu bài làm</div>

            </div>

        </div>
        {{-- kết thúc block mainleft --}}

        <div class="main-right main-right-ld" style="float: unset" :style="tab == 1 ? 'position: sticky; top: 190px;' : ''">

            @if($numberOfDay <= 30 && $numberOfDay != 0)
                <div style="border: 1px dashed green; border-radius: 5px;padding: 10px 5px;margin-bottom: 10px">
                    <span style="color: red; font-weight: bold; text-transform: uppercase;">Hết hạn: Còn {{$numberOfDay}} ngày</span>
                    <p>Lưu ý: Nếu hết thời hạn khóa học, bạn sẽ không thể gia hạn khóa học này được nữa.</p>
                </div>
            @endif

            <div v-if="tab == 1" v-cloak class="time-block-wrapper">
                <div class="time-block">
                    <div class="flex align-items-flex-start">
                        <img class="cg-icon mr-3" :src="url+ '/assets/img/cg-icon.png'" />
                        <div>
                            <div style="font-size: 16px; color: #474DDA;font-weight: 700;margin-bottom: 4px;">{{$thisLesson->getGroupOfLesson['name']}}</div>
                            <div class="btinfo" v-cloak style="padding: 7px 11px; background: rgba(132, 134, 241, 0.2);color: #474DDA;border-radius: 7px;display: inline-block">
                                <span style="width: 10px; height: 10px; display: inline-block; border-radius: 50%;background: #474DDA; margin-right: 7px;"></span>
                                Đã làm <b v-cloak>@{{Object.keys(userAnswers).length}}/@{{numOfQuestions}}</b> câu</div>
                        </div>
                    </div>

                    <div class="btn submit-exam-btn mt-3" v-on:click="submitDataExercise">Nộp bài</div>
                    <img src="{{ asset('assets/img/ld/dash.svg') }}" alt="" class="mt-5 mb-4">
                    <div class="btinfo" v-cloak style="font-size: 21px; font-weight: 700; color: #8486F1;text-align: center">
                        <img src="{{ asset('assets/img/ld/clock.svg') }}">
                        <b>@{{countDownTime}}</b>
                    </div>
                </div>
                <div class="course-list-ld mt-4">
                    @if(Auth::check() && $unlock == 1)
                        <course-group-ld :lessontab="ldth ? 2 : 1" :price="{{$course->price}}" :lessonprogress="{{$lessonProgress}}" :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :auth="1" :ul="{{$unlock}}" :type="'pc'" ref="luyende"></course-group>
                    @else
                        <course-group-ld :lessontab="ldth ? 2 : 1" :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :ul="{{$unlock}}" :type="'pc'" ref="luyende"></course-group>
                    @endif
                </div>
            </div>

            @if (count($otherVideo) > 0)
                <div class="list-video-area-parent" v-if="tab == 2" v-cloak>
                    <div>
                        <div class="panel-heading clickable">
                            <h3 class="panel-title"><strong>Danh sách chữa đề</strong></h3>
                        </div>
                        <div class="panel-body">
                            @foreach($otherVideo as $key => $v)
                                @if ($v->id)
                                    @if ($v->type == 2)
                                        <div class="video-item" >
                                            <a class="video-title"
                                               v-on:click="loadMp4('{{$v->video_name}}')"
                                               v-bind:style="(currentMediaName == '{{$v->video_name}}') ? 'color: #474DDA; font-weight: 600;' : ''"
                                               title="{{ $v->video_title }}">
                                                <img v-cloak style="filter: hue-rotate(140deg);" v-if="currentMediaName == '{{$v->video_name}}'" src="{{ asset('assets/img/premium/videow.png') }}" class="mr-3"/>
                                                <img v-cloak v-else src="{{ asset('assets/img/premium/videob.png') }}" class="mr-3"/>{{ $v->video_title }}
                                            </a>
                                        </div>
                                    @elseif ($v->type == 4)
                                        <div class="video-item" >
                                            <a class="video-title"
                                               v-on:click="loadResult({{ json_encode(json_decode($v->value)[0]->content) }})"
                                               title="{{ json_decode($v->value)[0]->name }}">
                                                <img src="{{ asset('assets/img/premium/videob.png') }}" class="mr-3"/>{{ json_decode($v->value)[0]->name }}
                                            </a>
                                        </div>
                                    @endif
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
            {{-- lịch sử làm bài --}}
            <div class="course-list-ld result-list" v-if="tab == 3" v-cloak>
                <p class="ld-name" style="width: 100%; text-align: center; margin-bottom: 15px;">
                    {{ $course->name }} &nbsp; <i class="zmdi zmdi-chevron-right"></i>  &nbsp; <b>Kết quả {{ $thisLesson->name }}</b>
                </p>
                <div class="panel panel-default course-group-menu" v-for="(item, index) in history" v-on:click="compareResult(index)">
                    <div class="panel-heading mn-tab-2" role="tab">
                        <i class="zmdi zmdi-assignment"></i> &nbsp;
                        <span style="flex: 1"><b>Lần @{{ (history.length - index) }}</b> (@{{ item.created_at }})</span>
                        <span class="score">@{{ item.grade }}/@{{ item.total_grade }}</span>
                        <span style="margin-left: 10px">
                            <strong class="passed" v-if="item.grade  >= {{ $thisLesson->pass_marks }}" title="Đạt"><i class="zmdi zmdi-check-circle"></i></strong>
                            <strong class="passed" v-if="item.grade < {{ $thisLesson->pass_marks }}" title="Không đạt"><i class="zmdi zmdi-close-circle" style="color: red;"></i></strong>
                        </span>
                    </div>
                    <label class="flex items-center m-3" v-if="currentHistoryIndex != null && currentHistoryIndex == index"><input type="checkbox" class="mr-2 mt-0" @change="toggleShowOnlyWrong" name="showOnlyWrongCheckbox">Chỉ hiển thị những câu sai</label>
                </div>
            </div>

        </div>
        {{-- kết thúc block main-right --}}
    </div>
</div>
@section('footer-js')
    <script type="text/javascript">
        @if(Auth::check())
        var authUserId = "{{Auth::user()->id}}";
        @endif
        var lesson_tasks  = {!! json_encode($tasks) !!};
        var lgmenu = "{{ $thisLesson->getTypeLD() }}";
        var ldResults = {!! json_encode($ldResults) !!};
        var lessonInfo = {!! json_encode($lessonInfo) !!};
        var lesson_otherVideo     = {!! json_encode($otherVideo) !!};
        var lessonDetail   = {!! json_encode($thisLesson) !!};
        var courseUrl             = "{{ $course->SEOurl }}";
        var mdaId = "{{base64_encode(base64_encode(base64_encode($video->video_name)))}}";

        {{--lấy ra danh sách server mà bài học có--}}
        var listServers = {!! json_encode($servers) !!};

        var playerId = '{{$random_id}}';

        var taskId = '{{$video->id}}';

        var playbackSpeed = {{ $thisLesson->default_speed }};
        new Vue({ el: '#comment-container' });
    </script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>

    {{-- libs video mp4 --}}
    <script src="{{asset('assets/js/lib-hls.js')}}?{{filemtime('assets/js/lib-hls.js')}}"></script>
    <link  href="{{asset('plugin/videojs_hls/videojs.min.css')}}" rel="stylesheet" type="text/css">
    <link  href="{{asset('plugin/videojs-markers/dist/videojs.markers.css')}}" rel="stylesheet" type="text/css">
    <script src="{{asset('plugin/videojs-hotkeys/videojs.hotkeys.js')}}"></script>
    <script src="{{asset('plugin/videojs-markers/dist/videojs-markers.js')}}"></script>
    <script src="{{asset('plugin/videojs-ie8/videojs-ie8.min.js')}}"></script>
    <script src="{{asset('plugin/pdf-object/pdfobject.min.js')}}"></script>
    <script src="{{asset('assets/js/lesson-ld.js')}}?{{filemtime('assets/js/lesson-ld.js')}}"></script>

    <script>PDFObject.embed("https://dungmori.com/cdn/pdf/"+ mainLuyenDe.pdf, "#pdf-embed", {background: "#fff"});</script>

    <script type="text/javascript">
        $(document).ready(function(){
            $('.panel-collapse').on('show.bs.collapse', function () {
                $(this).siblings('.panel-heading').addClass('active');
                // console.log("Mở");
            });

            $('.panel-collapse').on('hide.bs.collapse', function () {
                $(this).siblings('.panel-heading').removeClass('active');
            });

            if (lessonDetail && lessonDetail.group_id) {
                $("#group-ld-" + lessonDetail.group_id).click();
            }
        });
    </script>
@stop
@stop

