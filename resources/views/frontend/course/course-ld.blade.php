@extends('frontend._layouts.default')

@section('title')
    <PERSON><PERSON><PERSON><PERSON> h<PERSON> {{ $course->name }}  - Dung<PERSON>i
@stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('cdn/course/default')}}/{{ $course->avatar_name }} @stop
@section('author') DUNGMORI @stop

@section('header-js')
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
    <style type="text/css">
        #lesson__progress{display: none;}
        .course-ldp__comment {
            width: 1100px;
            margin: auto;
        }
        .comment-container {
            width: 670px;
        }
        .comment-tab { border-bottom: 1px solid #fff !important; margin-bottom: 0; }
        .comment-tab .active > a {background-color: #F0F6FF !important;}
        .nav-pills > li.active > a{color: #124896; font-size: 14px; text-align: center;}
        .comment-tab .active > .active{ color: #124896; border: 1px solid #124896 !important; }
        .list-comments .comment-item .comment-content .name b {color: #105B89 !important; }
        .comment-action > a{color: #105B89 !important;}
        .child-comment-item .comment-content .child-name b {color: #105B89 !important; }
        .list-comments .input-comment-container .post-comment-btn {background: #474DDA !important;}
        .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {background: #474DDA !important;}
        .list-comments .load-more-comment {background: #E5E6FF !important; color: #474DDA; font-weight: bold;}
        .comment-tab li{width: 50%;}
        .comment-tab li a{ font-size: 14px; text-align: center; color: #124896; text-decoration: underline; }
        .nav-pills > li.active > a:hover{color: #666;}
        .list-comments .comment-item .comment-content .name b { width: 100%; float: left; }
        a:hover {
            color: white;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
@stop

@section('content')

<div id="fb-root"></div>

<div class="main">
    @include('frontend.course.course-cta-ld', ['course' => $course, 'unlock' => $unlock, 'dark' => true, 'firstLesson' => $firstLesson, 'color' => '#8486F1'])

    <div class="main-center main-course" id="main-course-ld" style="margin-top: 20px;">
    <div class="ld-top-menu mb-10">
        <div class="flex items-center text-base">
            <img src="{{ asset('assets/img/new_course/file.svg') }}" class="mr-2"/> Hãy chọn dạng bạn muốn ôn luyện
        </div>
        <div class="flex items-center justify-end">
            <label class="tab-item m-0 mr-3 a-cursor-pointer" v-on:click="switchTab(1)">
                <input type="radio" name="ld-tab" :checked="tab == 1" class="m-0 !mr-1 scale-125 !mt-0" width="30px">
                <span v-cloak class="text-lg">Luyện đề kỹ năng (@{{ getStackVideoCount.ldkn }} video)</span>
            </label>
            <label class="tab-item m-0 a-cursor-pointer" v-on:click="switchTab(2)">
                <input type="radio" name="ld-tab" :checked="tab == 2" class="m-0 !mr-1 scale-125 !mt-0" width="30px">
                <span v-cloak class="text-lg">Luyện đề tổng hợp (@{{ getStackVideoCount.ldth }} video)</span>
            </label>
        </div>
    </div>

    {{-- <a href="#">
        <img class="banner-img" src="{{ asset('assets/img/banner-ld.png') }}" />
    </a> --}}

    {{-- luyện đề kỹ năng --}}
    <div class="main-left main-left-ld">
        <div class="mb-2.5">
            <b style="text-transform: uppercase; font-size: 20px; font-weight: 700">Biểu đồ điểm số</b>
        </div>
        {{-- nếu chưa đăng nhập hoặc chưa sở hữu --}}
        @if($unlock == 0)
            <div id="cover_container" class="cover-container relative">
                <img src="{{ asset('assets/img/ldbn.png') }}"/>
                <br>
                @include('frontend.course.play-button')
            </div>

            <?php preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $course->link, $match); ?>
            <iframe id="iframe-youtube" class="mb-3.75" style="display: none;" width="100%" height="395" frameborder="0" gesture="media" allow="encrypted-media" allowfullscreen
            src="https://www.youtube.com/embed/{{ $match[1] }}?rel=0"></iframe>
        @else
            <div class="cover-container cover-container-mb">
                <div class="chart-container" v-show="tab == 1" v-cloak>
                    <span style="margin: 20px 0 0 25px; float: left;">Điểm</span>
                    <div class="canvas-container" v-for="(item, index) in ldknGroups" v-show="ldknTabId == item.id" style="position: relative">
                        <div class="canvas" :id="'chartScoreLDKN'+item.id"></div>
                        <img v-if="checkEmptyChart(item)" src="{{ asset('assets/img/ld/empty-chart.svg') }}" alt="" style="position: absolute;width: 200px;height: 200px;top: calc(50% - 100px);left: calc(50% - 100px);">
                    </div>
                    <span style="margin: -26px 20px 0 0; float: right;">Đề</span>
                    <div class="tab-container">
                        <span class="tab-item c-tab-press" v-for="(item, index) in ldknGroups" v-show="item.type_ld != 'ldth'"
                        v-bind:style="(ldknTabId == item.id) ? 'background: #FFBA3B; color: #fff;': ''" v-on:click="switchChatKN(item.id)">
                        <i class="zmdi zmdi-circle" style="font-size: 10px;" :style="{color: '#' + Math.floor(Math.random()*16777215).toString(16)}"></i> &nbsp;@{{item.name}}</span>
                    </div>
                </div>
                <div class="chart-container" v-show="tab == 2" style="padding-top: 40px; position: relative;" v-cloak>
                    <div id="chartScoreLDTH"></div>
                    <img v-if="checkEmptyTotalChart()" src="{{ asset('assets/img/ld/empty-chart.svg') }}" alt="" style="position: absolute;width: 200px;height: 200px;top: calc(50% - 100px);left: calc(50% - 100px);">
                </div>
            </div>
        @endif

        @if (!$unlock)
        @includeIf('frontend.course.components.fb_btn_message')
        <h4 style="color: #474DDA;">Giới thiệu khóa học</h4>
        <div class="course-price-container">
            <div class="info"><span>Học phí:</span> <b>
              @if(in_array($course->id, array(8, 9, 10)))
                <strike style="opacity:0.6;">2,090,000 ₫</strike> &nbsp;
              @endif
              {{ number_format($course->price) }}
            </b> ₫ ( {{ number_format($courseJpPrice) }} ¥ ) </div>
            <div class="info"><span>Thời gian:</span> @if ($course->name == "N5") Miễn phí vô thời hạn @else {!! json_decode($course->stats_data)->time !!}
            tháng kể từ ngày kích hoạt @endif</div>
            <div class="info"><span>Khóa học bao gồm</span> <b> {!! json_decode($course->stats_data)->lesson !!}</b> bài học với
                <b>{!! json_decode($course->stats_data)->video !!}</b> videos bài giảng</div>
            <div class="info"><span>Giảng viên:</span> {{ $course->getAuthorName() }}</div>
            <div class="info"><span>Mô tả khóa học:</span> {!! $course->brief !!}</div>
        </div>
        @endif
    </div>
    <div class="main-right main-right-ld">
        <div class="mb-2.5">
            <div v-if="tab == 1" v-cloak class="flex items-center">
                <b v-cloak style="text-transform: uppercase; font-size: 20px; font-weight: 700">Luyện đề kỹ năng</b>
                <div class="tooltip-i" v-cloak><i class="fa fa-info-circle text-lg ml-3" aria-hidden="true"></i><span class="tooltiptext" style="margin-left: -80px;">Tập trung ôn luyện từng mondai trong phần Từ vựng - Chữ Hán - Ngữ pháp của đề thi JLPT</span></div>
            </div>
            <div v-if="tab == 2 " v-cloak class="flex items-center">
                <b v-cloak style="text-transform: uppercase; font-size: 20px; font-weight: 700">Luyện đề tổng hợp</b>
                <div class="tooltip-i" v-cloak><i class="fa fa-info-circle text-lg ml-3" aria-hidden="true"></i><span class="tooltiptext" style="width: 200px; margin-left: -95px;">Tập trung ôn luyện đề JLPT</span></div>
            </div>
        </div>
        <div class="course-list-ld">
            @if(Auth::check() && $unlock == 1)
                <course-group-ld :price="{{$course->price}}" :lessonprogress="{{$lessonProgress}}" :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :auth="1" :ul="{{$unlock}}" :type="'pc'" ref="luyende"></course-group>
            @else
                <course-group-ld :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :ul="{{$unlock}}" :type="'pc'" ref="luyende"></course-group>
            @endif
        </div>
    </div>


</div>{{--  end main-course --}}

<div class="course-ldp__comment">
    <div class="comment-container" id="comment-container">
        <ul class="nav nav-pills comment-tab">
            <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a>
            </li>
            <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng
                    facebook</a></li>
        </ul>
        <div class="tab-content">
            <div id="user-comment-content" class="tab-pane fade in active">

                @if(Auth::check())
                    <comments meid="{{ Auth::user()->id }}"
                              avatar="{{ Auth::user()->avatar }}"
                              tbid="{{ $course->id }}"
                              tbname="course"
                              num-posts="15"
                              background="#fff"
                              ref="comment">
                    </comments>
                @else
                    <comments tbid="{{ $course->id }}"
                              tbname="course"
                              num-posts="15"
                              background="#fff"
                              ref="comment">
                    </comments>
                @endif

            </div>
            <div id="facebook-comment-content" class="tab-pane fade">
                <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
            </div>
        </div>
    </div>
</div>
</div>

@section('fixed-panel')


@stop

@section('footer-js')

    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>

    <script type="text/javascript"> new Vue({ el: '#comment-container' }); </script>

    <script type="text/javascript">
        var ldknGroups = {!! json_encode($groups) !!};
        var ldLessons = {!! json_encode($lessons) !!};
        var ldResults = {!! json_encode($ldResults) !!};

        var unlock = 0; @if($unlock == 1) unlock = 1; @endif

        // console.log("ldknGroups", ldknGroups);

        var ldkn = ldknGroups.filter((g) => g.type_ld != 'ldth');

        var tmp = new Vue({

            el: '#main-course-ld',
            data: function () {
                return {
                    tab: 1,

                    ldknGroups: ldknGroups,
                    ldLessons: ldLessons,
                    ldknTabId: ldkn[0].id,
                    countLoad: 0,

                }
            },
            computed: {
                getStackVideoCount: function() {
                    var vm = this;

                    var ldknStack = vm.ldknGroups.filter(function(o) {
                        return o.type_ld == 'ldkn'
                    })
                    var ldthStack = vm.ldknGroups.filter(function(o) {
                        return o.type_ld == 'ldth'
                    })
                    var stats = {
                        ldkn: 0,
                        ldth: 0
                    }
                    stats.ldkn = _.sumBy(ldknStack, function(g) {
                        return _.sumBy(vm.ldLessons.filter(function(l) {
                            return l.group_id == g.id
                        }), function(o) {
                            return o.videos ? o.videos.length : 0
                        })
                    })
                    stats.ldth = _.sumBy(ldthStack, function(g) {
                        return _.sumBy(vm.ldLessons.filter(function(l) {
                            return l.group_id == g.id
                        }), function(o) {
                            return o.videos ? o.videos.length : 0
                        })
                    })
                    return stats
                },
            },
            methods: {
                //lấy ra text từ html
                switchTab: function(tab){

                    setCookie("LD_COURSE_TAB_{{ $course->id }}", tab, 10);

                    // console.log("switchTab", tab);
                    this.tab = tab;
                    // vm.$refs.luyende.switchTab(tab);
                    // ds.$emit('switchTab', tab);
                    this.$root.$refs.luyende.changeTab(tab);

                },
                checkEmptyChart: function(group) {
                    var vm = this;
                    var scoresData = []; //danh sách điểm cao nhất từng bài cho chart

                    vm.ldLessons.forEach(function(lesson) {
                        if(lesson.group_id == group.id){
                            var ldknResult = _.find(ldResults, { lesson_id: lesson.id });
                            var scoreMax = ldknResult ? ldknResult.grade : 0;

                            scoresData.push(scoreMax);
                        }

                    });
                    return  _.every(scoresData, function (o) {return o == 0});
                },
                checkEmptyTotalChart: function() {
                    var vm = this;
                    //lấy ra nhóm luyện đề tổng hợp
                    var groupLDTH = _.find(vm.ldknGroups, {type_ld: 'ldth'});

                    // console.log("groupLDTH", groupLDTH);

                    // vm.ldknGroups.forEach(function(group) {

                    var score_1 = [];
                    var score_2 = [];
                    var score_3 = [];

                    vm.ldLessons.forEach(function(lesson) {

                        if(lesson.group_id == groupLDTH.id){

                            var result = _.find(ldResults, {lesson_id: lesson.id});

                            if(result){
                                if(result.score_data){
                                    score_1.push(parseInt(JSON.parse(result.score_data).s1));
                                    score_2.push(parseInt(JSON.parse(result.score_data).s2));
                                    score_3.push(parseInt(JSON.parse(result.score_data).s3));
                                }

                            } else{
                                score_1.push(0); score_2.push(0); score_3.push(0);
                            }
                        }

                    });
                    // console.log('score 1 ', score_1)
                    return _.every(score_1, function (o) {return o == 0}) && _.every(score_2, function (o) {return o == 0}) && _.every(score_3, function (o) {return o == 0});
                },
                initChartKNData: function(){

                    var vm = this;

                    // console.log(vm.ldknGroups)
                    vm.ldknGroups.forEach(function(group) {

                        var lnamesData = []; //danh sách tên bài học cho chart
                        var scoresData = []; //danh sách điểm cao nhất từng bài cho chart

                        vm.ldLessons.forEach(function(lesson) {
                            if(lesson.group_id == group.id){
                                lnamesData.push(lesson.name);

                                var ldknResult = _.find(ldResults, { lesson_id: lesson.id });
                                var scoreMax = ldknResult ? ldknResult.grade : 0;

                                scoresData.push(scoreMax);
                            }

                        });

                        group.lnamesData = lnamesData;
                        group.scoresData = scoresData;
                        //vẽ chart kỹ năng
                        if(group.type_ld == "ldkn"){

                            var options = {
                                chart: {type: 'bar'},
                                series: [{
                                    name: 'Điểm',
                                    data: _.every(scoresData, function (o) {return o == 0}) ? [] : scoresData,
                                    fontSize: '16px'
                                }],
                                dataLabels: {
                                    enabled: true,
                                    style: {
                                        fontSize: "14px",
                                        fontFamily: "Helvetica, Arial, sans-serif",
                                        fontWeight: "bold"
                                    }
                                },
                                xaxis: {
                                    categories: lnamesData,
                                    labels: {
                                        style: {
                                            fontSize: '16px'
                                        }
                                    }
                                },
                                yaxis: {
                                    labels: {
                                        style: {
                                            fontSize: '16px'
                                        }
                                    }
                                },
                            };

                            var chart = new ApexCharts(document.querySelector("#chartScoreLDKN"+group.id), options);
                            if (chart) chart.render();
                        }
                    });

                    //lấy ra nhóm luyện đề tổng hợp
                    var groupLDTH = _.find(vm.ldknGroups, {type_ld: 'ldth'});

                    // console.log("groupLDTH", groupLDTH);

                    // vm.ldknGroups.forEach(function(group) {

                    var score_1 = [];
                    var score_2 = [];
                    var score_3 = [];

                    vm.ldLessons.forEach(function(lesson) {

                        if(lesson.group_id == groupLDTH.id){

                            var result = _.find(ldResults, {lesson_id: lesson.id});

                            if(result){
                                if(result.score_data){
                                    score_1.push(parseInt(JSON.parse(result.score_data).s1));
                                    score_2.push(parseInt(JSON.parse(result.score_data).s2));
                                    score_3.push(parseInt(JSON.parse(result.score_data).s3));
                                }

                            }else{
                                score_1.push(0); score_2.push(0); score_3.push(0);
                            }
                        }

                    });


                    // console.log("NamesTh", groupLDTH.lnamesData);
                    // console.log("score_1", score_1);
                    // console.log("score_2", score_2);
                    // console.log("score_3", score_3);

                    var options2 = {
                        series: [{
                            name: 'Từ vựng - chữ hán',
                            data: _.every(score_1, function (o) {return o == 0}) ? [] : score_1
                        }, {
                            name: 'Ngữ pháp - Đọc hiểu',
                            data: _.every(score_2, function (o) {return o == 0}) ? [] : score_2
                        }, {
                            name: 'Nghe hiểu',
                            data: _.every(score_3, function (o) {return o == 0}) ? [] : score_3
                        }],
                        chart: {
                            type: 'bar',
                            height: 350,
                            stacked: true,
                            toolbar: {
                                show: true
                            },
                            zoom: {
                                enabled: true
                            }
                        },
                        responsive: [{
                            breakpoint: 480,
                            options: {
                                legend: {
                                    position: 'bottom',
                                    offsetX: -10,
                                    offsetY: 0
                                }
                            }
                        }],
                        plotOptions: {
                            bar: {
                                borderRadius: 8,
                                horizontal: false,
                            },
                        },
                        xaxis: {
                            type: 'text',
                            categories: groupLDTH.lnamesData,
                            labels: {
                                style: {
                                    fontSize: '16px'
                                }
                            }
                        },
                        yaxis: {
                            labels: {
                                style: {
                                    fontSize: '16px'
                                }
                            }
                        },
                        legend: {
                            position: 'top',
                            // offsetY: 40
                        },
                        fill: {
                            opacity: 1
                        }
                    };

                    var chart2 = new ApexCharts(document.querySelector("#chartScoreLDTH"), options2);
                    if (chart2) chart2.render();



                },

                //vẽ chart dựa theo dữ liệu switch
                switchChatKN: function(gid){

                    var vm = this;
                    vm.ldknTabId = gid;

                },

            },
            mounted: function(){

                if(unlock == 1)
                    this.initChartKNData();

                // this.switchChatKN(this.ldknTabId);

                // setTimeout(function(){
                //     $("c-tab-press").first().click();
                // }, 500);
            }
        });


        $(document).ready(function(){
            $('.panel-collapse').on('show.bs.collapse', function () {
                $(this).siblings('.panel-heading').addClass('active');
                // console.log("Mở");
            });

            $('.panel-collapse').on('hide.bs.collapse', function () {
                $(this).siblings('.panel-heading').removeClass('active');
            });

            $('#cover_container').on('click', function(ev) {
                $("#cover_container").css("display", "none");
                $("#iframe-youtube").css("display", "block");
                ev.preventDefault();
            });
        });
    </script>


    <script src='https://cdn.jsdelivr.net/npm/apexcharts'></script>

</body>

@stop
@stop
