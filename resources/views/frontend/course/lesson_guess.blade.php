@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON><PERSON><PERSON> họ<PERSON> | {{ $thisLesson->name }} @stop
@section('description')Dungmori YouTuber d<PERSON>y tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image')
    @if (!is_null($thisLesson) && $thisLesson->avatar_name != null && $thisLesson->avatar_name != "")
      {{url('cdn/lesson/default')}}/{{ $thisLesson->avatar_name }}
    @else
      {{url('assets/img/oglogo.png')}}
    @endif
@stop
@section('author') DUNGMORI @stop

@section('header-js')
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
    <script type="text/javascript">
    var meid = null;
    var myAvatar = null;
    @if(Auth::check())
        meid = '{{ Auth::user()->id }}';
        myAvatar = '{{ Auth::user()->avatar }}';
    @endif
    </script>
@stop

@section('content')







{{-- ##############################nếu là trang guess của khóa thường --}}
@if($isld == false)

<div class="main">
  <div class="main-top">
    @if($course->price != 0)
          @include('frontend.course.course-cta', ['course' => $course])
    @endif
  </div>
  <div class="main-center main-course">

    <div class="main-left @if($premium == true) main-left-premium @endif">

        <h2 class="lesson-detail-title mb-3">
            @if ($thisLesson->is_secret == 1)
                <b>Nội dung đã bị ẩn</b>
            @else
                <b>{{ $thisLesson->name }}</b>
            @endif
        </h2>
{{--        <p style="width: 100%; float: left; margin: 5px 0 0px;">--}}
{{--          <i class="zmdi zmdi-time-countdown"></i> <b>{{ $thisLesson->count_view }}</b> Lượt xem--}}
{{--        </p>--}}

{{--        @if(!in_array($course->id, array(27, 28, 29)))--}}
{{--        <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">--}}
{{--            <div class="premium-expried buy-this-course buy-in-lesson">Mua khóa học này <i class="fa fa-cart-plus"></i></div>--}}
{{--        </a>--}}
{{--        @endif--}}

        {{-- nếu là 1 trong 3 khóa zoom --}}
        @if(in_array($course->id, array(27, 28, 29)))

            <style type="text/css">
                .server-localtion{background-color: #EDBA70 !important;}
                .main .main-course .main-left .course-detail-container .course-price-container {background: #fff1e0 !important;}
                .course-tab{background: #FFF !important;}
                .course-tab > .active > a{ background-color: #D68E24 !important; }
                .course-tab > .li-tab > a{ color: #fff !important; }
                .course-list-container > .block-title{background: #D68E24;}
                .course-list-container .panel-default > .panel-heading a {background: #FFE8CD !important; color: #D68E24;}
                .course-list-container .panel-default > .panel-heading > .group-step-item{background: #FFE8CD !important;}
                .course-list-container .panel-default .panel-body li a {background: #f7f2eb !important;}
                .course-list-container .panel-default .panel-body li a:hover {background: #f7f2eb !important; color: #222;}
                .lesson__progress--circle .progressbar-text {color: #222 !important;}
                .comment-tab { border-bottom: 1px solid #D68E24 !important; }
                .comment-tab .active > a {background-color: #D68E24 !important;}
                .list-comments .comment-item .comment-content .name b {color: #D68E24 !important; }
                .comment-action > a{color: #D68E24 !important;}
                .child-comment-item .comment-content .child-name b {color: #D68E24 !important; }
                .list-comments .input-comment-container .post-comment-btn {background: #D68E24 !important;}
                .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {background: #D68E24 !important;}
                .list-comments .load-more-comment {background: #D68E24 !important;}
                .main-right{margin-top: 35px !important;}
                .main .main-course .main-right .course-info-container {margin-top: 35px;}
                .course-list-container .panel-default .panel-body li.active a {color: #111;}
            </style>

        @endif

        {{-- nếu là 1 trong 3 khóa eju --}}
        @if(in_array($course->id, array(8, 9, 10)))
            <style type="text/css">
                .lesson-detail-title { margin-top: 21px !important; }
                .lesson-detail-title > a{color: #105B89 !important;}

                .server-localtion{background-color: #F0F6FF !important; color: #0D4890 !important; }
                .course-tab{background: #77C2EF !important;}
                .course-tab > .active > a{ background-color: #105B89 !important; }
                .course-tab > .li-tab > a{ color: #fff !important; }
                .course-list-container > .block-title{background: #124896; text-align: center;}
                .course-list-container .panel-default > .panel-heading a {background: #F0F6FF !important; color: #124896;  text-align: center;}
                .course-list-container .panel-default > .panel-heading a strong{width: 100%; text-align: center;}
                .scroll-items .pull-right{color: #124896;}

                .course-list-container .panel-default .panel-body li a .free{background: #124896;}
                /*.course-list-container .panel-default > .panel-heading > .group-step-item{background: rgb(238, 238, 238) !important;}*/
                .course-list-container .panel-default .panel-body li a {background: #FBFCFF !important; font-size: 13px; font-weight: 300; font-family: Roboto;}
                .course-list-container .panel-default .panel-body li a:hover {color: #124896; font-weight: bold;}

                .comment-tab { border-bottom: 1px solid #fff !important; margin-bottom: 0; }
                .comment-tab .active > a {background-color: #F0F6FF !important;}
                .nav-pills > li.active > a{color: #124896; font-size: 14px; text-align: center;}
                .comment-tab .active > .active{ color: #124896; border: 1px solid #124896 !important; }
                .list-comments .comment-item .comment-content .name b {color: #105B89 !important; }
                .comment-action > a{color: #105B89 !important;}
                .child-comment-item .comment-content .child-name b {color: #105B89 !important; }
                .list-comments .input-comment-container .post-comment-btn {background: #124896 !important;}
                .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {background: #124896 !important;}
                .list-comments .load-more-comment {background: #F0F6FF !important; color: #0D4890; font-weight: bold;}
                .main-right{margin-top: 55px !important;}
                .main .main-course .main-right .course-info-container {margin-top: 35px; margin-left: -270px;}
                .main .main-course .main-left .course-detail-container .course-price-container{
                    border: 2px dashed #4F92E4 !important; background: #fff !important; font-size: 12px !important; }
                .comment-tab li{width: 50%;}
                .comment-tab li a{ font-size: 14px; text-align: center; color: #124896; text-decoration: underline; }
                .nav-pills > li.active > a:hover{color: #666;}
                .list-comments .comment-item .comment-content .name b { width: 100%; float: left; }
                .numberOfDay{width: 100%; float: left; margin-left: -270px; margin-top: -13px; height: 120px; font-size: 12px !important; text-align: left !important;}
                .progressbar-text{color: #fff !important;}
                .course-list-container .panel-default > .panel-heading .group-step-item {background: #124896 !important; text-align: center !important; text-transform: uppercase; color: #fff !important; font-size: 18px; padding: 10px 0 8px 0 !important; }
                .course-list-container .panel-default > .panel-heading + .panel-collapse > .panel-body .scroll-items a{color: #124896 ;}
                @media only screen and (max-width: 768px) {
                    .main-right{margin-top: 0 !important;}
                }
            </style>
        @endif

        {{-- nếu người dùng chưa đăng nhập --}}
        @if ($type == 'auth')

            <div class="cover-container guest-cover-container">
              <h3><i class="zmdi zmdi-lock"></i> Bạn cần đăng nhập để học hoặc bình luận</h3>
                <div class="flex justify-center items-center">
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container" style="margin-right: 10px;">
                        <div class="btn-register" onclick="swichTab('login')">Đăng nhập</div>
                    </a>
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                        <div class="btn-login" onclick="swichTab('register')">Tạo tài khoản</div>
                    </a>
                </div>
            </div>

        {{-- nếu người dùng đăng nhập rồi mà hết hạn hoặc chưa mua --}}
        @elseif($type == 'expried')
            <div class="cover-container guest-cover-container">
                @if(!in_array($course->id, array(27, 28, 29)))
                    <h3>Mua khóa học để học ngay</h3>
                    <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}" class="guest-buy-btn">
                        <div class="premium-expried buy-this-course buy-in-lesson">Mua ngay <i class="fa fa-cart-plus"></i></div>
                    </a>
                @else
                    <h3>Nội dung chỉ dành cho <br/>học viên Zoom hoặc kích hoạt N1|N2|N3 <br/>từ ngày 23/01/2021</h3>
                @endif
            </div>
        @endif

        {{-- container comment học viên --}}
        <div class="comment-container" id="comment-container">
            <ul class="nav nav-pills comment-tab">
                <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a></li>
                <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng facebook</a></li>
            </ul>

            <div class="tab-content" style="padding-top: 0;">
                <div id="user-comment-content" class="tab-pane fade in active">
                    <comments tbid="{{ $thisLesson->id }}"
                        tbname="lesson"
                        num-posts="15"
                        background="#fff"
                        ref="comment">
                    </comments>
                </div>

                <div id="facebook-comment-content" class="tab-pane fade">
                    <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
                </div>
            </div>
        </div>

    </div>
    {{-- kết thúc block mainleft --}}

    {{-- khóa n1, n2, n3 tiến trình kiểu premium / cho guess / không progress --}}
    @if($premium == true)
        <div class="main-right-premium">
            <a href="{{url('/bang-gia')}}">
                <div class="see-more">Xem thêm các khóa học khác <i class="zmdi zmdi-long-arrow-right"></i></div>
            </a>
            <div class="course-list-premium">
                <course-group-premium :categories="{{$categories}}" :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :focus="{{json_encode($thisLesson->getFocusIds())}}" :ul="0"></course-group-premium>
            </div>
        </div>

    {{-- nếu là các khóa thường --}}
    @else

        <div class="main-right">

            {{-- tiến trình học thường --}}
            <div class="course-list-container lesson-course-list" id="course-list-pc">
                @if(!in_array($course->id, array(27, 28, 29)))
                <div class="buy-item">
                    <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                      <div class="buy-btn">Mua khóa học này</div>
                    </a>
                </div>
                @endif
                <div class="block-title" id="lesson-list-detail"> Tiến trình học </div>
                <course-group :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :ul="0" :type="'pc'"></course-group>
            </div>
        </div>
    @endif
    {{-- kết thúc block mainright --}}

</div>
</div>

    @section('fixed-panel')

    @stop

@stop


@section('footer-js')

    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    {{-- nếu là 1 trong 3 khóa eju --}}
    @if(in_array($course->id, array(8, 9, 10)))
      <script type="text/javascript">$(".eju").addClass("active")</script>
    @endif
    {{-- bật vue cho component comment --}}
    <script type="text/javascript">
        new Vue({ el: '#comment-container' });
    </script>
    @if($premium == true)
        <script type="text/javascript"> new Vue({ el: '.course-list-premium' }); </script>
    @else
        <script type="text/javascript">
            new Vue({ el: '#course-list-pc' });
            $(".khoa-hoc").addClass("active");
            $("#collapse-pc{{ $thisLesson->lesson_group->id }}").collapse({toggle: true });
            $(".lesson-item-{{$thisLesson->id}}").addClass("active");

            var container = null;
            var scrollTo = null;
            container = $(".scroll-items-{{ $thisLesson->lesson_group->id }}");
            scrollTo  = $(".lesson-item-{{$thisLesson->id}}");
            if (container && scrollTo) {
              container.animate({ scrollTop: scrollTo.offset().top - container.offset().top - 32 + container.scrollTop()});
            }

        </script>
    @endif

    <script>(function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) return;
    js = d.createElement(s); js.id = id;
    js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
    fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>

{{-- ##############################nếu là trang guess của luyện đề --}}
@else

<style type="text/css">
        #lesson__progress{display: none;}

        .comment-tab { border-bottom: 1px solid #fff !important; margin-bottom: 0; }
        .comment-tab .active > a {background-color: #F0F6FF !important;}
        .nav-pills > li.active > a{color: #124896; font-size: 14px; text-align: center;}
        .comment-tab .active > .active{ color: #124896; border: 1px solid #124896 !important; }
        .list-comments .comment-item .comment-content .name b {color: #105B89 !important; }
        .comment-action > a{color: #105B89 !important;}
        .child-comment-item .comment-content .child-name b {color: #105B89 !important; }
        .list-comments .input-comment-container .post-comment-btn {background: #474DDA !important;}
        .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {background: #474DDA !important;}
        .list-comments .load-more-comment {background: #E5E6FF !important; color: #474DDA; font-weight: bold;}
        .comment-tab li{width: 50%;}
        .comment-tab li a{ font-size: 14px; text-align: center; color: #124896; text-decoration: underline; }
        .nav-pills > li.active > a:hover{color: #666;}
        .list-comments .comment-item .comment-content .name b { width: 100%; float: left; }

    </style>

<div class="main">
  @include('frontend.course.course-cta-ld', ['course' => $course, 'unlock' => false, 'dark' => true, 'color' => '#8486F1'])

    <div class="main-center main-course" id="main-course-ld">
        <h2 class="ld-detail-title">
            <a href="{{url('/khoa-hoc')}}/{{ $course->SEOurl }}" style="color: #474DDA;">{{ $course->name }} </a>
                &nbsp; <i class="zmdi zmdi-chevron-right"></i>  &nbsp; <b>{{ $thisLesson->name }}</b>
        </h2>
        <div class="buy-box">
            <div class="buy-item">
                <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                  <span class="buy-btn" style="padding-top: 3px;">Mua khóa học này</span>
                </a>
            </div>
        </div>
        <div class="main-left main-left-ld">

            <div class="cover-container guest-cover-container" style="background: #EEE;">
                @if(!Auth::user())
                    <h3><i class="zmdi zmdi-lock"></i> Bạn cần đăng nhập để học hoặc bình luận</h3>
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                        <div class="btn-register" onclick="swichTab('login')">Đăng nhập</div>
                    </a>
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                        <div class="btn-login" onclick="swichTab('register')">Tạo tài khoản</div>
                    </a>
                @elseif($type = "expire")
                    <h3>Mua khóa học để học ngay</h3>
                @endif
            </div>

        </div>
        <div class="main-right main-right-ld" style="height: 470px;">
            <div class="course-list-ld" id="course-list-ld">
                <course-group-ld :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :ul="0" :type="'pc'" ref="luyende"></course-group-ld>
            </div>
        </div>
    </div>

    <div class="main-comment">
        <h3 style="width: 100%; float: left; color: #474DDA; margin-bottom: 20px; font-weight: bold;">❤️ ❤️ Bình luận của học viên</h3>
        <div class="comment-container" id="comment-container" style="margin-bottom: 30px;">
            <comments tbid="{{ $course->id }}"
                    tbname="course"
                    num-posts="15"
                    background="#fff"
                    ref="comment">
            </comments>
        </div>
    </div>
</div>
    @section('footer-js')

    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    <script type="text/javascript">
        new Vue({ el: '#comment-container' });
        new Vue({ el: '#course-list-ld' });
    </script>
    @stop

@endif
{{-- ##############################nếu là trang guess của luyện đề --}}



@stop
