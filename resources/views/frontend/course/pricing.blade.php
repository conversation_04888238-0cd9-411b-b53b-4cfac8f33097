@extends('frontend._layouts.default')

@section('title') <PERSON><PERSON><PERSON><PERSON> học tiếng Nhật online - Dungmori @stop
@section('description')<PERSON><PERSON><PERSON><PERSON> học tiếng Nh<PERSON>t online - Dungmori @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON> với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-js')
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
@stop

@section('content')

<div class="main pricing__screen">
    <div class="main-center">
        <div class="all-courses-container">
            <div class="pricing__title">Danh mục sản phẩm</div>
            <ul class="course-tabs">
                <li onclick="viewFreeCourseDot()">
                    <a data-toggle="tab" href="#home">
                        <img src="{{url('assets/img/new_course/free.png')}}"/><br/>
                        <span>Miễn phí </span>
                        <span class="relative size-3 w-3 h-3 ml-2 free-course-dot hidden">
                          <span class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#EF6D13] opacity-75"></span>
                          <span class="relative inline-flex size-3 w-3 h-3 rounded-full bg-[#EF6D13]"></span>
                        </span>
                    </a>
                </li>
                <li class="active">
                    <a data-toggle="tab" href="#menu1">
                        <img src="{{url('assets/img/new_course/jlpt.png')}}"/><br/>
                        <span>JLPT</span>
                    </a>
                </li>
                <li>
                    <a data-toggle="tab" href="#menu2">
                        <img src="{{url('assets/img/new_course/kaiwa.png')}}"/><br/>
                        <span>Kaiwa</span>
                    </a>
                </li>
                {{-- <li>
                    <a href="{{route('frontend.book')}}">
                        <img src="{{url('assets/img/new_course/book.svg')}}"/><br/>
                        <span>Sách</span>
                    </a>
                </li> --}}
                <li>
                    <a href="https://sach.dungmori.com/" target="_blank">
                        <img src="{{url('assets/img/new_course/book.svg')}}"/><br/>
                        <span>Sách</span>
                    </a>
                </li>
            </ul>
            <div class="tab-content">
                <div id="home" class="tab-pane fade">

                    <div class="title-container">
                        <h2 class="tab-title"> Khóa học miễn phí</h2>
                    </div>

                    <div class="list-product-container lp1">
                    @foreach($listProducts as $product) @if(in_array($product->id, [9, 292]))
                      <div class="course-item" style="margin-right: 40px;">
                          
                         <div class="course-card relative" onclick="viewCourseBookmark({{ $product->id }})">
                            @if($product->id == 292)
                              <img src="{{ asset('assets/img/bookmark.svg') }}" alt="bookmark" class="absolute top-[2px] right-[25px] course-bookmark-{{ $product->id }} hidden">
                            @endif
                            <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png" class="course-icon">
                            <h1 class="course-name"><span>Khóa {{$product->name}}</span></h1>
                            <div class="course-info" style="color: #8366F4; padding: 20px 0 30px 0; font-size: 18px;">
                              @if($product->id == 292)
                               <s class="" style="color: #c9c9c9;">590.000đ</s> ->
                              @endif
                              Miễn phí
                            </div>
                            <div class="course-info">Vô thời hạn</div>
{{--                            <div class="course-info">{{$product->desc}}</div>--}}
                            <div class="course-info buy-info">
                              @if($product->id == 292)
                               <a href="{{url('/khoa-hoc/chu-han-n5')}}"><div class="buy-btn" style="background: #8366F4">Xem thêm</div></a>
                              @else
                               <a href="{{url('/khoa-hoc/chuyen-nganh')}}"><div class="buy-btn" style="background: #8366F4">Xem thêm</div></a>
                              @endif
                            </div>
                         </div>
                      </div>
                    @endif @endforeach
                    </div>
                </div>
                <div id="menu1" class="tab-pane fade in active">
                    <div class="title-container" style="margin-top: 30px;">
                        <h2 class="tab-title">Khóa lẻ luyện thi</h2>
                    </div>
                <div class="scroll-horizontal v2">
                    <div class="list-product-container lp2">
                        @foreach($listProducts as $product)
                            @if(in_array($product->name, ['JLPT N1', 'JLPT N2', 'JLPT N3', 'Sơ cấp N4', 'Sơ cấp N5']))
                                <div class="combo-it">
                                    <div class="course-card combo relative" onclick="viewCourseBookmark({{ $product->id }})">
                                        @if(in_array($product->name, ['JLPT N1', 'JLPT N2', 'JLPT N3']))
                                          <img src="{{ asset('assets/img/bookmark.svg') }}" alt="bookmark" class="absolute top-[2px] right-[25px] course-bookmark-{{ $product->id }} hidden">
                                        @endif
                                        <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png"
                                             class="course-icon">
                                        <h1 class="course-name flex items-center gap-3">
                                          <span>Khóa {{$product->name}}</span> 
                                          @if(in_array($product->name, ['JLPT N1', 'JLPT N2', 'JLPT N3']))
                                            <span class="px-[6px] py-1 font-beanbag-bold bg-[#FF2E2B] text-white rounded-full text-[14px]">HOT</span>
                                          @endif
                                        </h1>
                                        <div class="course-info ci2"
                                             style="font-weight: 500; font-size: 14px; margin: 8px 0 0 0;">
                                            Thời
                                            hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!}
                                            tháng &nbsp; &nbsp; &nbsp;
                                            <br/>Mã số <strong class="text-code">{{$product->desc}}</strong>
                                        </div>
                                        <div class="course-info text-center">
                                            <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
{{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                                        </div>
                                        <div class="px-2 text-center">
                                          <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                                        </div>
                                        <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($product->getCourseId()) }}" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">
                                            <div class="buy-btn">Mua ngay</div>
                                        </a>
                                        <a href="{{url('/khoa-hoc')}}/{{ ($product->services && json_decode($product->services) && json_decode($product->services)->courses) ? json_decode($product->services)->courses[0] : 0 }}">
                                            <div style="font-size: 12px; margin-right: 10px;  color: #333;  opacity: .7; margin-top: 10px;text-align: center">
                                                >> Xem chi tiết
                                            </div>
                                        </a>
                                        @includeIf('frontend.course.components.plus-popup',['$product' => $product])
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                <div class="title-container" style="margin-top: 30px;">
                        <h2 class="tab-title">Khóa luyện đề</h2>
                    </div>
                <div class="scroll-horizontal">
                    <div class="list-product-container lp2">
                    @foreach($listProducts as $product)
                      {{-- @if($product->id == 57 || $product->id == 58  || $product->id == 59) --}}
                      @if(in_array($product->id, [58, 59, 186, 274, 57]))
                      <div class="combo-it mb-7.5">
                              <div class="course-card combo">
                            <img src="{{url('assets/img/ld')}}/ld{{$product->id}}.png" class="course-icon">
                            <h1 class="course-name"><span>Khóa {{$product->name}}</span></h1>
                                <div class="course-info text-center">
                                  <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
                                  {{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                                </div>
                                <div class="px-2 text-center">
                                  <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                                </div>
                            <div class="course-info ci2" style="font-weight: 500; font-size: 14px; margin: 8px 0 0 0;">
                                Thời hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!} tháng
                                &nbsp; &nbsp; &nbsp; <br/>
                                Mã số <strong class="text-code" style="color: #474DDA">{{$product->desc}}</strong>
                            </div>
                            <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($product->getCourseId()) }}" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">
                                <div class="buy-btn" style="background: #474DDA;">Mua ngay</div>
                            </a>
                                  <a href="{{url('/khoa-hoc/combo')}}/{{$product->id}}">
                                      <div style="font-size: 12px;margin-right: 10px; color: #293142; opacity: 1; margin-top: 5px;text-align: center">Xem chi tiết và học thử</div>
                                  </a>
                                  @includeIf('frontend.course.components.plus-popup',['$product' => $product])
                         </div>
                      </div>
                    @endif @endforeach
                    </div>
                </div>

                    <div class="title-container" style="margin-top: 40px;">
                        <h2 class="tab-title">Khóa combo</h2>
                    </div>
                    <div style="width: 100%; display: flex; flex-wrap: wrap;">
                        @foreach($listProducts as $product)
                          @if(sizeof(json_decode($product->services)->courses) == 2 && $product->type == 'jlpt')
                          <div class="combo-it">
                              <div class="course-card combo">
                                <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png" class="course-icon">
                                <h1 class="course-name"><span>Combo</span> {{$product->name}}</h1>
                                <div class="course-info text-center">
                                  <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
                                  {{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                                </div>
                                <div class="px-2 text-center">
                                  <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                                </div>
                                <div class="course-info ci2" style="font-weight: 500; font-size: 14px;">Thời hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!} tháng &nbsp; &nbsp; &nbsp;
                                    <br/>Mã số <strong class="text-code">{{$product->desc}}</strong>
                                </div>

                                <a href="{{url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($product->id)}}"><div class="buy-btn" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">Mua ngay</div></a>
                                <a href="{{url('/khoa-hoc/combo')}}/{{$product->id}}">
                                    <div style="font-size: 12px;margin-right: 10px; color: #293142; opacity: 1; margin-top: 5px;text-align: center">Xem chi tiết và học thử</div>
                                </a>
                                  @includeIf('frontend.course.components.plus-popup',['$product' => $product,'type' => 'combo'])
                             </div>
                          </div>
                          @endif
                        @endforeach
                    </div>


                    <div class="title-container" style="margin-top: 20px;">
                        <h2 class="tab-title">Khóa combo lớn</h2>
                    </div>
                    <div style="width: 100%;display: flex; flex-wrap: wrap;">
                        @foreach($listProducts as $product)
                          @if(sizeof(json_decode($product->services)->courses) >= 3 && $product->type == 'jlpt')
                          <div class="combo-it mb-7.5">
                              <div class="course-card combo">
                                <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png" class="course-icon">
                                @if (sizeof(json_decode($product->services)->courses) === 4)
                                    <h1 class="course-name" style="height: 53px; font-size: 18px;"><span>Combo</span> {{$product->name}}</h1>
                                @else
                                    <h1 class="course-name"><span>Combo</span> {{$product->name}}</h1>
                                @endif
                                <div class="course-info text-center">
                                  <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
                                  {{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                                </div>
                                <div class="px-2 text-center">
                                  <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                                </div>
                                <div class="course-info ci2" style="font-weight: 500; font-size: 14px;">Thời hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!} tháng &nbsp; &nbsp; &nbsp;
                                    <br/>Mã số <strong class="text-code">{{$product->desc}}</strong>
                                </div>
                                <a href="{{url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($product->id)}}"><div class="buy-btn" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">Mua ngay</div></a>
                                <a href="{{url('/khoa-hoc/combo')}}/{{$product->id}}">
                                    <div style="font-size: 12px;margin-right: 10px; color: #293142; opacity: 1; margin-top: 5px;text-align: center">Xem chi tiết và học thử</div>
                                </a>
                                  @includeIf('frontend.course.components.plus-popup',['$product' => $product,'type' => 'combo'])
                             </div>
                          </div>
                          @endif
                        @endforeach
                    </div>

                </div>
                <div id="menu2" class="tab-pane fade">
                    <div class="title-container" style="margin-top: 20px;">
                        <h2 class="tab-title">Khóa giao tiếp Kaiwa</h2>
                    </div>
                    <div class="kaiwa-container" style="width: 100%; display: flex; flex-wrap: wrap;">
                        @foreach($listProducts as $product)
                          @if(in_array($product->id, [55,64,65,74]))
                          <div class="combo-it">
                              <div class="course-card combo">
                                <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png?" class="course-icon">
                                <h1 class="course-name"><span>Khóa học </span> {{$product->name}}</h1>
                                <div class="course-info text-center">
                                  <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
                                  {{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                                </div>
                                <div class="px-2 text-center">
                                  <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                                </div>
                                <div class="course-info ci2" style="font-weight: 500; font-size: 14px;">Thời hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!} tháng &nbsp; &nbsp; &nbsp;
                                    <br/>Mã số {{$product->desc}}</div>

                                {{-- <a href="{{url('/khoa-hoc/combo')}}/{{$product->id}}"> --}}
                                @if($product->id != 74)
                                  <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($product->getCourseId()) }}" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">
                                      <div class="buy-btn" style="background: #D6902E;">Mua ngay</div>
                                  </a>
                                @else

                                  <a href="{{url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($product->id)}}" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">
                                    <div class="buy-btn" style="background: #D6902E;">Mua ngay</div>
                                  </a>
                                @endif
                                <a href="{{url('/khoa-hoc')}}/{{$product->getSEOurl() }}">
                                    <div style="font-size: 12px;margin-right: 10px; color: #293142; opacity: 1; margin-top: 5px;text-align: center">Xem chi tiết và học thử</div>
                                </a>
                                  @includeIf('frontend.course.components.plus-popup',['$product' => $product])
                             </div>
                          </div>
                          @endif
                        @endforeach
                    </div>
                </div>
                <div id="eju1" class="tab-pane fade">
                  <div class="title-container" style="margin-top: 30px;">
                      <h2 class="tab-title">Khóa lẻ luyện thi</h2>
                  </div>
                  <div class="kaiwa-container" style="width: 100%; display: flex; flex-wrap: wrap;">
                    @foreach($listProducts as $product)
                      @if(in_array($product->id, [7,48,49,89,90,91]))
                        <div class="combo-it mt-4">
                          <div class="course-card combo">
                            <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png?" class="course-icon">
                            <h1 class="course-name"><span>Khóa học </span> {{$product->name}}</h1>
                            <div class="course-info text-center">
                              <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
                              {{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                            </div>
                            <div class="px-2 text-center">
                              <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                            </div>
                            <div class="course-info ci2" style="font-weight: 500; font-size: 14px;">Thời hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!} tháng &nbsp; &nbsp; &nbsp;
                              <br/>Mã số {{$product->desc}}</div>

                            <a href="{{url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($product->id)}}" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">
                              <div class="buy-btn" style="background: #124896;">Mua ngay</div>
                            </a>
                            <a href="{{url('/khoa-hoc')}}/{{$product->getSEOurl() }}">
                              <div style="font-size: 12px;margin-right: 10px; color: #293142; opacity: 1; margin-top: 5px;text-align: center">Xem chi tiết và học thử</div>
                            </a>
                            @includeIf('frontend.course.components.plus-popup',['$product' => $product])
                          </div>
                        </div>
                      @endif
                    @endforeach
                  </div>
                  <div class="title-container" style="margin-top: 30px;">
                    <h2 class="tab-title">Khóa Combo</h2>
                  </div>
                  <div class="kaiwa-container" style="width: 100%; display: flex; flex-wrap: wrap;margin-top: 20px;">
                    @foreach($listProducts as $product)
                      @if(in_array($product->id, [50,51,52,92,93,94,96,97,98]))
                        <div class="combo-it mt-4">
                          <div class="course-card combo">
                            <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png?" class="course-icon">
                            <h1 class="course-name"><span>Khóa học </span> {{$product->name}}</h1>
                            <div class="course-info text-center">
                              <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
                              {{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                            </div>
                            <div class="px-2 text-center">
                              <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                            </div>
                            <div class="course-info ci2" style="font-weight: 500; font-size: 14px;">Thời hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!} tháng &nbsp; &nbsp; &nbsp;
                              <br/>Mã số {{$product->desc}}</div>

                            <a href="{{url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($product->id)}}" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">
                              <div class="buy-btn" style="background: #124896;">Mua ngay</div>
                            </a>
                            <a href="{{url('/khoa-hoc')}}/{{$product->getSEOurl() }}">
                              <div style="font-size: 12px;margin-right: 10px; color: #293142; opacity: 1; margin-top: 5px;text-align: center">Xem chi tiết và học thử</div>
                            </a>
                            @includeIf('frontend.course.components.plus-popup',['$product' => $product])
                          </div>
                        </div>
                      @endif
                    @endforeach
                  </div>
                  <div class="title-container" style="margin-top: 30px;">
                    <h2 class="tab-title">Khóa Combo lớn</h2>
                  </div>
                  <div class="kaiwa-container" style="width: 100%; display: flex; flex-wrap: wrap;margin-top: 20px;">
                    @foreach($listProducts as $product)
                      @if(in_array($product->id, [53,95,99,100,101,102]))
                        <div class="combo-it mt-4">
                          <div class="course-card combo">
                            <img src="{{url('assets/img/new_course')}}/{{$product->id}}.png?" class="course-icon">
                            <h1 class="course-name"><span>Khóa học </span> {{$product->name}}</h1>
                            <div class="course-info text-center">
                              <sup>VND</sup> <b class="text-code">{{ number_format($product->price) }}</b>
                              {{--                                            <sup>JPY </sup><b class="text-off">{{ number_format($product->jpy_price) }} ¥</b>--}}
                            </div>
                            <div class="px-2 text-center">
                              <div class="text-red-500 text-xs mb-2">Giá Nhật cần liên hệ trung tâm <br> để nhận tư vấn</div>
                            </div>
                            <div class="course-info ci2" style="font-weight: 500; font-size: 14px;">Thời hạn {!! round(json_decode($product->services)->course_watch_expired_value / 30) !!} tháng &nbsp; &nbsp; &nbsp;
                              <br/>Mã số {{$product->desc}}</div>

                            <a href="{{url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($product->id)}}" onclick="ga('send', 'event', 'Pricing', 'click', 'Start buy ' + '{{ $product->name }}')">
                              <div class="buy-btn" style="background: #124896;">Mua ngay</div>
                            </a>
                            <a href="{{url('/khoa-hoc')}}/{{$product->getSEOurl() }}">
                              <div style="font-size: 12px;margin-right: 10px; color: #293142; opacity: 1; margin-top: 5px;text-align: center">Xem chi tiết và học thử</div>
                            </a>
                            @includeIf('frontend.course.components.plus-popup',['$product' => $product])
                          </div>
                        </div>
                      @endif
                    @endforeach
                  </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="fb-root"></div>
@stop

@section('footer-js')

<script>
    $(".khoa-hoc").addClass("active");
    var url = document.location.toString();
    if (url.match('#eju')) {
        $('.course-tabs a[href="#eju1"]').tab('show');
    }
    if (!localStorage.getItem('pricing_free_viewed')) {
      $('.free-course-dot').removeClass('hidden').addClass('flex'); 
    } else {
      $('.free-course-dot').removeClass('flex').addClass('hidden'); 
    }

    [289,290,291,292].forEach(function(id) {
      if (localStorage.getItem('new_course_bookmark_' + id)) {
        $('.course-bookmark-' + id).removeClass('flex').addClass('hidden'); 
      } else {
        $('.course-bookmark-' + id).removeClass('hidden').addClass('flex'); 
      }
    });
    
    function viewFreeCourseDot() {
      $('.free-course-dot').removeClass('flex').addClass('hidden'); 
      localStorage.setItem('pricing_free_viewed', true);
    }
    function viewCourseBookmark(id) {
      $('.course-bookmark-' + id).removeClass('flex').addClass('hidden'); 
      localStorage.setItem('new_course_bookmark_' + id, true);
    }
</script>

<script>(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) return;
  js = d.createElement(s); js.id = id;
  js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));</script>
@stop
