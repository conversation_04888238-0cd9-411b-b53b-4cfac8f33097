@extends('frontend._layouts.default')

@section('title') Dungmori - {{ $thisLesson->name }} @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON>y tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image')
    @if (!is_null($thisLesson) && $thisLesson->avatar_name != null && $thisLesson->avatar_name != "")
        {{url('cdn/lesson/default')}}/{{ $thisLesson->avatar_name }}
    @else
        {{url('assets/img/oglogo.png')}}
    @endif
@stop
@section('author') DUNGMORI @stop

@section('header-js')

@stop
@section('header-css')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">

    <style>
        table, thead, tbody, tr, td {
            border-style: solid;
            border-width: 1px;
            border-color: #000;
        }

    </style>
@stop
@section('content')

    <div id="fb-root"></div>

    <div class="main">
        <div class="main-center main-course" id="lesson-checkpoint">
            <div class="main-block">
                <div class="main-left main-left-premium m-l-checkpoint">

                    <div class="time-remaining-zone">
                        <div v-if="checkHastime" style="display: none" class="time-remaining-mobile">
                            <div class="time-remaining-title" v-cloak>
                                <div class="time-remaining-center">
                                    Thời gian còn lại:
                                    <span class="font-semibold">@{{ timeText.hour | two_digits }}:@{{ timeText.minute | two_digits }}:@{{ timeText.second | two_digits }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h1 class="lesson-title"><b>{{ $thisLesson->name }}</b></h1>

                    <div class="cover-container" v-cloak>
                        <div class="lesson-content-detail" style="max-height: max-content;">
                            <div class="mp3-container" v-show="mp3 != null">
                                <audio controls="controls" id="audioSource">
                                    <source type="audio/mpeg" :src="'https://mp3-v2.dungmori.com/'+ mp3">
                                </audio>
                            </div>
                            <div v-for="(question, index) in questions" class="questions-container">
                                <div class="question" v-html="question.value"></div>
                                <div class="answer-container">
                                    <div class="answer-item" v-for="(answer, index2) in question.answers"
                                         v-bind:style="answer.value.length >= 18 ? 'width:100%;' : 'width: 50%;'">
                                        <input :name="question.id" class="answers-radio" :id="answer.id" type="radio"
                                               :value="answer.id" v-model="userAnswers[question.id]"/>
                                        <label class="answers-label" :for="answer.id" v-html="answer.value"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="main-right border-amber-50" v-cloak>
                    <div class="timer-sm">
                        <div v-if="checkHastime">
                            <div class="time-remaining">Thời gian còn lại:
                                <span class="font-semibold">@{{ timeText.hour | two_digits }}:@{{ timeText.minute | two_digits }}:@{{ timeText.second | two_digits }}</span>
                            </div>
                            <div class="btn btn-cham-diem" v-on:click="submitDataExercise" v-if="timerValue > 0">
                                Nộp bài
                            </div>

                            <div class="time-end" v-else>
                                <div>HẾT GIỜ</div>
                                <div class="btn btn-retest" v-on:click="reTest">Làm lại</div>
                            </div>
                        </div>
                        <div v-if="checkHastime == false">
                            <div>
                                <div class="btn btn-cham-diem" v-on:click="submitDataExercise">Nộp bài</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="popup-result" v-show="showPopup == true" style="display: none;">

                <i class="zmdi zmdi-close" v-on:click="closePopupResult"></i>

                <div>
                    <div class="popup-header">
                        <h2 class="popup-title-mb" style="margin-bottom: 30px;">Thông tin</h2>
                        <span class="popup-warning">Vui lòng nhập thông tin để xem hết quả.</span>
                    </div>
                    <div class="main-popup">
                        <form class="form-horizontal" accept-charset="UTF-8" autocomplete="off" @keydown="clearField($event.target.name)">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <input type="text" class="form-control" v-model="name" name="name" placeholder="Họ và tên" autocomplete="off" required>
                                    <span class="help is-danger" v-if="errors.hasOwnProperty('name')" v-text="errors.name.join()"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <input type="email" class="form-control" v-model="email" name="email" placeholder="Email" autocomplete="off" required v-on="focus">
                                    <span class="help is-danger" v-if="errors.hasOwnProperty('email')" v-text="errors.email.join()"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <input type="number" class="form-control" v-model="phone" name="phone" placeholder="Số điện thoại" autocomplete="off" required>
                                    <span class="help is-danger" v-if="errors.hasOwnProperty('phone')" v-text="errors.phone.join()"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <input type="number" class="form-control" v-model="age" name="age" placeholder="Tuổi" autocomplete="off" required>
                                    <span class="help is-danger" v-if="errors.hasOwnProperty('age')" v-text="errors.age.join()"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <select class="form-control" name="career" id="career-select" v-model="career">
                                        <option value="">Chọn nghề nghiệp</option>
                                        <option value="student">Học sinh, sinh viên</option>
                                        <option value="staff">Nhân viên văn phòng</option>
                                        <option value="it">Nhân viên IT và kỹ thuật</option>
                                        <option value="teacher">Giáo viên hoặc nghiên cứu viên</option>
                                        <option value="business">Tự kinh doanh hoặc làm tự do</option>
                                        <option value="export">Xuất khẩu lao động / du học sinh</option>
                                        <option value="study">Chưa đi làm, tập trung học tiếng</option>
                                        <option value="other">Khác</option>
                                    </select>
                                    <span class="help is-danger" v-if="errors.hasOwnProperty('career')" v-text="errors.career.join()"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <select class="form-control" name="career" id="place-select" v-model="place">
                                        <option value="">Chọn nơi ở</option>
                                        <option value="vn">Việt Nam</option>
                                        <option value="jp">Nhật Bản</option>
                                        <option value="other">Khác</option>
                                    </select>
                                    <span class="help is-danger" v-if="errors.hasOwnProperty('place')" v-text="errors.place.join()"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <select class="form-control" name="career" id="purpose-select" v-model="purpose">
                                        <option value="">Mục tiêu tiếng Nhật</option>
                                        <option value="certificate">Thi chứng chỉ (JLPT, NAT-TEST,...)</option>
                                        <option value="communication">Học để giao tiếp hàng ngày</option>
                                        <option value="hobby">Học để phát triển bản thân hoặc vì sở thích</option>
                                        <option value="teaching">Học để giảng dạy hoặc nghiên cứu</option>
                                        <option value="other">Khác</option>
                                    </select>
                                    <span class="help is-danger" v-if="errors.hasOwnProperty('purpose')" v-text="errors.purpose.join()"></span>
                                </div>
                            </div>
                            <div>
                                <div class="col-md-12 checkpoint-submit">
                                    <span v-on:click="saveUserInfo"> Xem kết quả</span>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="triangle-bottomright">
                    <svg width="60" height="70" viewBox="0 0 60 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 70L60 0V55C60 63.2843 53.2843 70 45 70H0Z" fill="#9BE25B"/>
                    </svg>
                </div>
            </div>

            <!-- popup modal show test results -->
            <modal v-if="showAnswers" @close="closePopup" @mousedown="closePopup">
                <div slot="header">
                    <div v-cloak>
                        <div class="results-title">Thành tích</div>
                    </div>
                </div>
                <div slot="body">
                    <div class="main-results" style="margin-bottom: 20px">
                        <div class="main-popup-results" v-cloak>
                            <p>
                                @{{name}}
                            </p>
                            <span class="total-point">
                                Tổng điểm của bạn: <b style="color: #ff0000">@{{result.grade}}/@{{result.total_grade}}</b>
                            </span>
                            <br >
                            <span style="text-align: center; color: #7E7E7E; font-weight: 400">
                                Điểm đạt yêu cầu: {{ $thisLesson->pass_marks }}/{{ $thisLesson->total_marks }}
                            </span>
                            <div class="result-text">
                                <span class="result-text-pass" v-if="result.grade >= {{ $thisLesson->pass_marks }}" style="color: #96D962">bạn đã đỗ</span>
                                <span class="result-text-faill" v-if="result.grade < {{ $thisLesson->pass_marks }}">chưa đạt</span>
                            </div>
                            <div class="result-icone" style="margin: 10px 0">
                                <img src="{{asset('assets/img/checkpoint/checkpoint-pass.png')}}" alt="checkpoint-pass"  v-if="result.grade >= {{ $thisLesson->pass_marks }}">
                                <img src="{{asset('assets/img/checkpoint/checkpoint-notpass.png')}}" alt="checkpoint-notpass" v-else>
                            </div>
                            <div class="checkpoint-submit" v-on:click="reTest">
                                <span>Làm Lại</span>
                            </div>
                        </div>
                    </div>
                    <div class="answer-result" v-if="result.grade >= {{ $thisLesson->pass_marks }}" v-cloak>
                        <h3>Đáp án</h3>
                        <div v-for="(question, index) in showResults" class="questions-container">
                            <p class="question-content" v-html="question.value"></p>
                            <div class="answer-container">
                                <div class="answer-item" v-for="(answer, index2) in question.answers"
                                     v-bind:style="answer.value.length >= 18 ? 'width:100%;' : 'width: 50%;'">
                                    <input type="radio"
                                           :name="'ans-'+answer.id"
                                           :checked="answer.user_chose"
                                           disabled>
                                    <label class="label-answer" :id="'id-'+answer.id"
                                           :class="[answer.is_true == 0 ? 'answer-false' : '' , answer.grade > 0 ? 'answer-true': '' ]">
                                        <i class="fa fa-check icon-true" v-if="answer.grade > 0"></i>
                                        <i class="fa fa-times icon-fasle" v-if="answer.is_true == 0"></i>
                                        <span v-html="answer.value"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </modal>
        </div>
    </div>
@stop

@section('footer-js')

    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    <script src="{{ asset('/plugin/jquery/axios.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script type="text/javascript">
        var lesson_tasks = {!! json_encode($tasks) !!};
        var lessonDetail = {!! json_encode($thisLesson) !!};
    </script>

    <script src="{{asset('assets/js/lesson-checkpoint.js')}}?{{filemtime('assets/js/lesson-checkpoint.js')}}"></script>

@stop
