@extends('frontend._layouts.default')
@section('title')
    <PERSON><PERSON><PERSON><PERSON><PERSON> {{ $course->name }} - Dungmori
@stop
@section('description')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất
@stop
@section('keywords')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất
@stop
@section('image')
    {{ url('cdn/course/default') }}/{{ $course->avatar_name }}
@stop
@section('author')
    DUNGMORI
@stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link href="https://vjs.zencdn.net/8.16.1/video-js.css" rel="stylesheet"/>
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <link rel="stylesheet" href="//unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css">
@stop

@section('content')
    <div id="course_basic_vue" v-cloak v-show="isMounted">
        @php
            $assetVersion = '1.0.1';
            $isLocked = !$isUnlock;
            $firstStage = $stages->first();
            $firstStageCategoryIds = $firstStage->pluck('id');
            $totalGroup = $groups->whereIn('lesson_category_id', $firstStageCategoryIds)->count();
            $groupIds = $groups->whereIn('lesson_category_id', $firstStageCategoryIds)->pluck('id');
            $totalPercent = $groups->whereIn('lesson_category_id', $firstStageCategoryIds)->sum('progress');
            $totalPercentStage = $totalGroup > 0 ? round($totalPercent / $totalGroup) : 0;

            $completedIntroduction = $totalPercentStage >= 85;

            $completedStages = 0;
            $completedRequireStages = 0;
            $requireStages = 0;
            foreach ($stages as $stageName => $stage) {
                $stageCategories = $categories->whereIn('stage', $stage->pluck('stage')->toArray());
                $completedRequireCategories = 0;
                $completedNonRequireCategories = 0;
                $requiredCategories = 0;
                foreach ($stageCategories as $category) {
                    $categoryGroups = $groups->where('lesson_category_id', $category->id);
                    $completedRequireGroups = 0;
                    $completedNonRequireGroups = 0;
                    $requiredGroups = 0;

                    foreach ($categoryGroups as $group) {
                        $groupIsRequired = $group->lessons->where('require', 1)->count();
                        if ($groupIsRequired) {
                            $requiredGroups++;
                        }

                        if ($groupIsRequired && $group->is_pass) {
                            $completedRequireGroups++;
                        }

                        if (!$groupIsRequired && $group->is_pass) {
                            $completedNonRequireGroups++;
                        }
                    }
                    if ($requiredGroups > 0) {
                        $requiredCategories++;
                        if ($completedRequireGroups == $requiredGroups) {
                            $completedRequireCategories += 1;
                        }
                    } else {
                        if ($completedNonRequireGroups == $categoryGroups->count()) {
                            $completedNonRequireCategories += 1;
                        }
                    }
                }
                if ($requiredCategories > 0) {
                    $requireStages++;
                    $stagePercent = round($completedRequireCategories * 100 / $requiredCategories);
                    if ($stagePercent == 100) {
                        $completedRequireStages += $stagePercent;
                    }
                } else {
                    $stagePercent = $stageCategories->count() > 0 ? round($completedNonRequireCategories * 100 / $stageCategories->count()) : 0;
                }
            }
            $courseCompletedPercent = $requireStages ? round($completedRequireStages / $requireStages) : 0;

            if($courseExpireDay) {
                $date = \Carbon\Carbon::parse($courseExpireDay)->format('d/m/Y');
                $colorCourseExpire =
                    \Carbon\Carbon::parse($courseExpireDay)->addDays(-7) > \Carbon\Carbon::parse(date('Y-m-d H:i:s'))
                        ? 'bg-[#C1EACA]'
                        : 'text-[#975102] bg-[#FFF1C2]';

                $text = 'Khóa học còn hạn đến ';
                if ((\Carbon\Carbon::parse($courseExpireDay)->addDays(-7) > \Carbon\Carbon::now() && $courseOwner->watch_expired_day == null) || ($courseOwner->watch_expired_day != null && \Carbon\Carbon::parse($courseOwner->code_active_at)->addDay()->equalTo($courseOwner->watch_expired_day))) {
                    $text = 'Thời hạn học thử còn ';
                }
            }
        @endphp
        @include('frontend.course.components.modal-teacher-info')
        <div class="course-basic">
            <div id="course-basic-tutorial">
                @include('frontend.course.components.course-tutorial')
            </div>
            <div class="pb-20 bg-[#F4F5FA] pt-7">
                <div class="bg-cover bg-no-repeat sp:!bg-none"
                     style="{{ $completedIntroduction ? "background-image: url('/images/lessons/banner-paid.png')" : '' }}">
                    <div class="container max-w-[1100px] mx-auto">
                        <div class="grid grid-cols-3 sp:grid-cols-1">
                            <div class="col-span-2 left-col {{ $completedIntroduction ? 'invisible sp:hidden' : '' }}">
                                @if($course->SEOurl == 'luyen-de-n4' || $course->SEOurl == 'luyen-de-n5')
                                    <div class="img">
                                        <img src="{{ asset("/images/course/LD.png") }}">
                                    </div>
                                @else
                                    <div class="video">
                                        <video id="my-video-intro" class="video-js h-[413px] sp:h-[250px]" controls
                                               preload="auto"
                                               poster="
                                               @if ($course->id === 47) /images/course/chuhann5thumb.jpg
                                               @elseif ($course->id === 44) /images/course/poster-intro-n3.jpg
                                               @elseif ($course->id === 45) /images/course/poster-intro-n2.jpg
                                               @elseif ($course->id === 46) /images/course/poster-intro-n1.jpg
                                               @else /images/course/poster-intro-n4-n5.jpg
                                               @endif
                                               "
                                               data-setup="{}">
                                            <source
                                                    @if($course->id == 40)
                                                        src="https://vn.dungmori.com/720p/GioithieuN4.mp4/index.m3u8"
                                                    @elseif ($course->id == 39)
                                                        src="https://vn.dungmori.com/720p/GIOI-THIEU-KHOA-HOC.mp4/index.m3u8"
                                                    @elseif ($course->id == 44)
                                                        src="https://vn.dungmori.com/720p/Gioi-thieu-khoa-hoc-N3.mp4/index.m3u8"
                                                    @elseif ($course->id == 45)
                                                        src="https://vn.dungmori.com/720p/Gioi-thieu-khoa-hoc-N2.mp4/index.m3u8"
                                                    @elseif ($course->id == 46)
                                                        src="https://vn.dungmori.com/720p/Gioi-thieu-khoa-hoc-N1.mp4/index.m3u8"
                                                    @elseif ($course->id == 47)
                                                        src="https://vn.dungmori.com/720p/GIOI-THIEU-KHOA-HOC.mp4/index.m3u8"
                                                    @endif
                                                    type="application/x-mpegURL"/>
                                        </video>
                                    </div>
                                @endif
                            </div>
                            {{-- COURSE INFO --}}
                            <div class="right-col text-[#212121] sp:px-6 z-50 md:mt-0 mt-10">
                                <div class="desktop:text-right sp:text-left relative">
                                    @if ($course->price_option === 0)
                                        <div class="bg-[#07403F] px-3 py-1 text-white rounded-full font-beanbag-bold absolute desktop:top-1 top-4 desktop:left-8 desktop:right-auto left-auto right-0">
                                            MIỄN PHÍ
                                        </div>
                                    @endif
                                    <div class="text-2xl font-averta-semibold text-[#07403F] sp:hidden">
                                        Tiếng Nhật GUNGUN
                                    </div>
                                    <div class="flex items-center justify-end sp:justify-between whitespace-nowrap mb-[7px]">
                                        <div class="relative">
                                            <span
                                                    class="@if ($course->id === 47) text-[89px] @else text-[98px] @endif sp:text-[48px] text-[#07403F] font-zuume-semibold relative">{{ $course->name }}
                                            </span>
                                            <span
                                                    class="absolute bg-[#EF6D13] text-white text-[6px] px-2 py-[1px] rounded-full top-0 right-[-30px] desktop:hidden">
                                                NEW
                                            </span>
                                        </div>
                                    </div>
                                    <div class="font-averta-regular text-base relative top-[-25px] sp:top-[-10px]">
                                        ぐんぐん上達！- Tiến bộ nhanh chóng cùng DUNGMORI!
                                        {{--                                        Nhập môn tiếng Nhật cùng DUNGMORI!--}}
                                    </div>
                                    @if(in_array($course->id, [30, 41]))
                                        <div class="font-averta-regular text-base relative top-[-25px] sp:top-[-10px]">
                                            Với kho đề thi phong phú, sát thực tế, khóa "Luyện đề" là trợ thủ đắc lực số
                                            1 giúp bạn tổng ôn kiến thức và rèn luyện kĩ năng làm bài trước mỗi kì thi!
                                        </div>
                                    @endif
                                    @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                        <div
                                                class="flex items-center justify-end sp:justify-start relative top-[-20px] sp:top-[-8px]">
                                            <div class="flex items-center">
                                                @for ($i = 5; $i > 0; $i--)
                                                    <img class="mr-[3px] w-4"
                                                         src="{{ asset('/images/icons/star.png') }}?v={{ $assetVersion }}"
                                                         alt="star">
                                                @endfor
                                                <span class="ml-2 text-[15px] text-[#EF6D13]">5/5</span>
                                            </div>
                                            @if ($course->price_option === 1)
                                                <div class="text-[#6F727D] text-[15px] ml-3 underline">
                                                    (@{{ course.id === 44 ? 3628 : course.id == 45 ? 5968 : course.id === 46 ? 2324 : 5999 }} đánh giá)
                                                </div>
                                            @endif
                                        </div>
                                    @endif

                                    <div class="font-averta-regular sp:mt-5">
                                        <div class="text-base font-averta-semibold">
                                            Giáo viên phụ trách
                                        </div>
                                        @if(in_array($course->id, [30, 31, 32, 33, 39, 40, 41, 44, 45, 46, 47]))
                                            <div class="flex items-center justify-end sp:justify-start mt-4 gap-2 flex-wrap">
                                                @if (strpos($course->author_id, '"47"'))
                                                    <div class="bg-white rounded-full p-2 flex items-center justify-center mr-2 cursor-pointer btn-teacher-info"
                                                         data-id="#teacher-1">
                                                        <img class="flex-none rounded-full w-[32px] h-[32px]"
                                                             src="{{ asset('/images/lessons/thay-dung.png') }}?v=4"
                                                             alt="thầy Dũng">
                                                        <span class="ml-1 text-md whitespace-nowrap">Thầy Dũng Mori</span>
                                                    </div>
                                                @endif
                                                @if (strpos($course->author_id, '"48"'))
                                                    <div class="bg-white rounded-full p-2 flex items-center justify-center cursor-pointer btn-teacher-info"
                                                         data-id="#teacher-2">
                                                        <img class="flex-none rounded-full w-[32px] h-[32px]"
                                                             src="{{ asset('/images/lessons/co-thanh.png') }}?v=4"
                                                             alt="cô Thanh">
                                                        <span class="ml-1 text-md whitespace-nowrap">Cô Phương Thanh</span>
                                                    </div>
                                                @endif
                                                @if (strpos($course->author_id, '"49"'))
                                                    <div class="bg-white rounded-full p-2 flex items-center justify-center cursor-pointer btn-teacher-info"
                                                         data-id="#teacher-3">
                                                        <img class="flex-none rounded-full w-[32px] h-[32px] object-cover"
                                                             src="{{ asset('/images/lessons/co-phan-ha.png') }}?v=4"
                                                             alt="cô Thanh">
                                                        <span class="ml-1 text-md whitespace-nowrap">Cô Phan Hà</span>
                                                    </div>
                                                @endif
                                                @if (strpos($course->author_id, '"50"'))
                                                    <div class="bg-white rounded-full p-2 flex items-center justify-center mr-2 cursor-pointer btn-teacher-info"
                                                         data-id="#teacher-4">
                                                        <img class="flex-none rounded-full w-[32px] h-[32px] object-cover"
                                                             src="{{ asset('/images/lessons/thay-truong.png') }}?v=4"
                                                             alt="thầy Dũng">
                                                        <span class="ml-1 text-md whitespace-nowrap">Thầy Minh Trường</span>
                                                    </div>
                                                @endif
                                                @if (strpos($course->author_id, '"51"'))
                                                    <div class="bg-white rounded-full p-2 flex items-center justify-center cursor-pointer btn-teacher-info"
                                                         data-id="#teacher-5">
                                                        <img class="flex-none rounded-full w-[32px] h-[32px] object-cover"
                                                             src="{{ asset('/images/lessons/co-ninh.png') }}?v=4"
                                                             alt="cô Thanh">
                                                        <span class="ml-1 text-md whitespace-nowrap">Cô Nguyễn Ninh</span>
                                                    </div>
                                                @endif
                                                @if (strpos($course->author_id, '"52"'))
                                                    <div class="bg-white rounded-full p-2 flex items-center justify-center cursor-pointer btn-teacher-info"
                                                         data-id="#teacher-6">
                                                        <img class="flex-none rounded-full w-[32px] h-[32px] object-cover"
                                                             src="{{ asset('/images/lessons/dinh-hien.png') }}?v=4"
                                                             alt="cô Thanh">
                                                        <span class="ml-1 text-md whitespace-nowrap">Cô Đinh Hiền</span>
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                    </div>

                                    @if (!$isLocked)
                                        <div v-if="courseExpireDay !== null"
                                             class="inline-flex mt-[15px] select-none border {{ $courseCompletedPercent < 85 ? 'border-[#BF6A02]' : ($courseCompletedPercent == 100 ? 'border-transparent' : '') }} {{ $courseCompletedPercent < 85 ? 'text-[#BF6A02]' : ($courseCompletedPercent == 100 ? 'bg-[#57D061] text-[#07403F]' : 'text-[#009951]') }} rounded-[13px] px-[7px] font-averta-regular text-[14px]">
                                            Bạn đã hoàn thành {{ $courseCompletedPercent }}%
                                        </div>
                                    @endif

                                    <div v-if="courseExpireDay !== null && course.id !== 47"
                                         class="flex items-center justify-end sp:justify-start font-averta-regular mt-4 whitespace-nowrap sp:hidden">
                                        <div
                                                :class="`${(new Date(courseExpireDay.replace(' ', 'T')).getTime() / 1000) > (new Date().getTime() / 1000) + 60 * 60 * 24 * 7 ? 'bg-[#C1EACA]' : 'bg-[#FFF1C2]'}`"
                                                class="px-4 py-1 rounded-full text-[15px]">
                                            Khóa học còn hạn đến
                                            <b class="font-averta-semibold">
                                                @{{ new Date(courseExpireDay.replace(' ',
                                                'T')).toLocaleDateString('vi-VN', { year: 'numeric', month: '2-digit',
                                                day: '2-digit' }) }}
                                            </b>
                                        </div>

                                        <a v-if="courseExpireDay !== null" onclick="showChatbox()"
                                           class="mobile:hidden ml-2 text-base text-[#07403F] !underline cursor-pointer">
                                            Gia hạn
                                        </a>
                                    </div>


                                    <div v-if="showBoxPrice" class="wrapper-price">
                                        <div v-if="!priceDiscount"
                                             class="container-price bg-white rounded-3xl shadow-lg lg:w-[310px] ml-auto mb-4 text-center p-2 mt-3">
                                            <div class="flex items-start text-[#07403F] text-3xl justify-center text-bold font-beanbag">
                                                <div class="price-pay">
                                                    @{{ number_format(course.id === 47 ? courseN5.price : course.price)
                                                    }}
                                                </div>
                                                <div class="text-base">
                                                    VNĐ
                                                </div>
                                            </div>
                                            <div class="text-xl font-beanbag-regular text-[#07403F]">
                                                Thời gian học <span class="text-bold font-beanbag"> @{{ [39, 40, 44, 45, 46, 47].includes(course.id) ? 8 : 2  }} tháng</span>
                                            </div>
                                        </div>

                                        <div v-if="priceDiscount"
                                             class="container-price-trial bg-white rounded-3xl shadow-lg lg:w-[310px] ml-auto mb-4 p-5 mt-3">
                                            <div class="flex justify-between items-start">
                                                <div class="flex items-start text-[#07403F] text-3xl justify-center text-bold font-beanbag">
                                                    <div>
                                                        @{{ number_format(course.id === 47 ? courseN5.price - 100000 :
                                                        course.id === 39 ? course.price - 100000 : course.id === 46 ?
                                                        course.price - 200000 : course.price - 150000) }}
                                                    </div>
                                                    <div class="text-base">
                                                        đ
                                                    </div>
                                                </div>

                                                <div class="line-through flex items-start text-[#757575] text-[14px] justify-center text-bold font-beanbag mt-[5px]">
                                                    <div>
                                                        @{{ number_format(course.id === 47 ? courseN5.price :
                                                        course.price) }}
                                                    </div>
                                                    <div class="text-[8px]">
                                                        đ
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center text-[#009951] text-[14px] text-bold font-beanbag">
                                                    <div class="mr-1">Giảm</div>
                                                    <div class="flex items-start justify-center text-bold">
                                                        <div>
                                                            @{{ number_format(course.id === 47 || course.id === 39 ?
                                                            100000 : course.id === 46 ? 200000 : 150000) }}
                                                        </div>
                                                        <div class="text-[8px]">
                                                            đ
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="flex items-center">
                                                    <svg width="16" height="16" viewBox="0 0 16 16"
                                                         fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M13.4202 8.77262C13.4202 11.765 10.9916 14.1937 7.99918 14.1937C5.00676 14.1937 2.57812 11.765 2.57812 8.77262C2.57812 5.7802 5.00676 3.35156 7.99918 3.35156C10.9916 3.35156 13.4202 5.7802 13.4202 8.77262Z"
                                                              stroke="#C00F0C" stroke-width="0.929325"
                                                              stroke-linecap="round"
                                                              stroke-linejoin="round"/>
                                                        <path d="M8 5.51953V8.61728" stroke="#C00F0C"
                                                              stroke-width="0.929325" stroke-linecap="round"
                                                              stroke-linejoin="round"/>
                                                        <path d="M6.14062 1.80469H9.85792" stroke="#C00F0C"
                                                              stroke-width="0.929325" stroke-miterlimit="10"
                                                              stroke-linecap="round"
                                                              stroke-linejoin="round"/>
                                                    </svg>
                                                    <div class="countdown-timer-trial flex items-center text-[#C00F0C] font-beanbag">
                                                        <div class="ml-2 mr-1">Còn</div>
                                                        <div class=" text-[14px] leading-[14px] text-bold text-[#C00F0C]">
                                                            @{{ currentCourseTimeLeft }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-xl font-beanbag-regular text-[#07403F] text-left mt-2">
                                                Thời gian học <span class="text-bold font-beanbag"> @{{ [39, 40, 44, 45, 46, 47].includes(course.id) ? 8 : 2  }} tháng</span>
                                            </div>
                                        </div>


                                        <div class="container-button-buy" v-if="showButtonBuy">
                                            <a v-if="course.id === 47" href="{{url('/khoa-hoc/so-cap-n5')}}">
                                                <div class="font-beanbag-bold text-white py-1.5 text-center mt-4 text-lg desktop:ml-8 rounded-full bg-[#EF6D13]">
                                                    NÂNG CẤP LÊN KHÓA N5
                                                </div>
                                            </a>
                                            <button v-else
                                                    class="bg-[#EF6D13] shadow-md ml-auto flex items-center justify-center rounded-full text-white w-full lg:w-[310px] py-3 text-base font-beanbag uppercase"
                                                    @click="buyNow({{ $course->id }})">
                                                <div class="text relative">
                                                    @{{ priceDiscount ? 'Nhận ưu đãi' : 'Mua ngay' }}
                                                    <div v-if="[44, 45, 46, 47, 39].includes(course.id) && priceDiscount"
                                                         class="absolute tag-discount w-[57px] h-[21px] -rotate-6 top-[-3px] right-[-60px]"
                                                         style="background: url({{ asset('/images/course/tag-discount.svg') }}) no-repeat; background-size: contain;">
                                                        <div class="price-discount text-[#07403F] text-base">
                                                            @{{ course.id === 47 || course.id === 39 ? '-100k' :
                                                            course.id === 46 ? '-200k' : '-150k' }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{-- END COURSE INFO --}}
                        </div>
                    </div>
                </div>

                {{-- COURSE LIST --}}
                <div class="container max-w-[1100px] mx-auto pt-4 mt-7 sp:mt-3 font-averta-regular sp:px-6">
                    <div class="sp:grid-cols-1 grid grid-cols-3">
                        <div class="col-span-2 left-col">
                            <div id="step-content-2">
                                @include('frontend.course.components.first-lesson')
                            </div>
                            {{-- END FIRST LESSON --}}

                            @if ($course->price_option === 1)
                                <div class="desktop:hidden mt-5">
                                    @include('frontend.course.components.course-new-purpose', ['courseId' => $course->id])
                                </div>
                            @endif

                            <div class="mt-7" id="step-content-3">
                                @include('frontend.course.components.lesson-list')
                            </div>
                        </div>

                        {{-- PURPOSE AND FEEDBACK --}}
                        @if ($course->price_option === 1)
                            <div class="right-col text-[#212121] pl-14 sp:pl-0">
                                <div class="sp:hidden">
                                    @include('frontend.course.components.course-new-purpose', ['courseId' => $course->id])
                                </div>
                                @if(in_array($course->id, [39, 40, 44, 45, 46, 47]))
                                    <div class="mt-5 sp:hidden">
                                        <div class="font-beanbag text-xl text-[#1E1E1E]">
                                            Đánh giá của học viên
                                        </div>

                                        <div v-if="[ID_COURSE_N3, ID_COURSE_N2, ID_COURSE_N1].includes(course.id)"
                                             class="grid grid-cols-1 gap-1">
                                            <div v-for="(cmt, i) in dataCmtCourse.find(item => item.course_id === course.id).cmt"
                                                 :key="cmt.id">
                                                <div class="flex items-center my-2">
                                                    <div class="flex-none">
                                                        <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                            <div class="bg-[#DAFBDE] rounded-full">
                                                                <img class="rounded-full"
                                                                     :src="`/images/course/img-user-cmt/${course.id}-${i + 1}.png`"
                                                                     alt="user-default-logo.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="font-averta-regular ml-2">
                                                        <div class="font-semibold">@{{ cmt.name }}</div>
                                                        <div class="flex items-center">
                                                            <img v-for="i in 5" class="mr-[1px] w-4"
                                                                 src="{{ asset('/images/icons/star.png') }}"
                                                                 alt="star">
                                                            <span
                                                                    class="text-[#737680] ml-4 text-[10px] relative top-[2px]">@{{ cmt.date }}</span>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="font-averta-regular my-2 text-justify">@{{ cmt.comment }}
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="grid grid-cols-1 gap-1">
                                            @if($course->id == 39)
                                                @foreach ($feedbacks->take(5) as $feedback)
                                                    <div>
                                                        <div class="flex items-center my-2">
                                                            <div class="flex-none">
                                                                <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                                    <div class="bg-[#DAFBDE] rounded-full">
                                                                        <img class="rounded-full"
                                                                             src="{{ $feedback->users->avatar !== null ? asset('/cdn/avatar/small/' . $feedback->users->avatar) : asset('/images/icons/user-default-logo.png') }}"
                                                                             alt="user-default-logo.png">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="font-averta-regular ml-2">
                                                                <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                                <div class="flex items-center">
                                                                    @for ($i = 5; $i > 0; $i--)
                                                                        <img class="mr-[1px] w-4"
                                                                             src="{{ asset('/images/icons/star.png') }}"
                                                                             alt="star">
                                                                    @endfor
                                                                    <span
                                                                            class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                                    </div>
                                                @endforeach
                                            @else
                                                @foreach ($feedbacks->reverse()->take(4) as $feedback)
                                                    <div>
                                                        <div class="flex items-center my-2">
                                                            <div class="flex-none">
                                                                <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                                    <div class="bg-[#DAFBDE] rounded-full">
                                                                        <img class="rounded-full"
                                                                             src="{{ $feedback->users->avatar !== null ? asset('/cdn/avatar/small/' . $feedback->users->avatar) : asset('/images/icons/user-default-logo.png') }}"
                                                                             alt="user-default-logo.png">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="font-averta-regular ml-2">
                                                                <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                                <div class="flex items-center">
                                                                    @for ($i = 5; $i > 0; $i--)
                                                                        <img class="mr-[1px] w-4"
                                                                             src="{{ asset('/images/icons/star.png') }}"
                                                                             alt="star">
                                                                    @endfor
                                                                    <span
                                                                            class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endif
                        {{-- END PURPOSE AND FEEDBACK --}}
                    </div>

                    @if(in_array($course->id, [39, 40, 44, 45, 46, 47]) && $course->price_option === 1)
                        {{-- FEEDBACK MOBILE --}}
                        <div class="mt-5 desktop:hidden">
                            <div class="font-beanbag text-xl text-[#1E1E1E]">
                                Đánh giá của học viên
                            </div>

                            <div class="grid grid-cols-1 gap-1">
                                @if(in_array($course->id, [44, 45, 46, 47]))
                                    <div v-for="(cmt, i) in dataCmtCourse.find(item => item.course_id === course.id).cmt.slice(0, 2)"
                                         :key="cmt.id">
                                        <div class="flex items-center my-2">
                                            <div class="flex-none">
                                                <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                    <div class="bg-[#DAFBDE] rounded-full">
                                                        <img class="rounded-full"
                                                             :src="`/images/course/img-user-cmt/${course.id}-${i + 1}.png`"
                                                             alt="user-default-logo.png">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="font-averta-regular ml-2">
                                                <div class="font-semibold">@{{ cmt.name }}</div>
                                                <div class="flex items-center">
                                                    <img v-for="i in 5" class="mr-[1px] w-4"
                                                         src="{{ asset('/images/icons/star.png') }}"
                                                         alt="star">
                                                    <span
                                                            class="text-[#737680] ml-4 text-[10px] relative top-[2px]">@{{ cmt.date }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="font-averta-regular my-2 text-justify">@{{ cmt.comment }}</div>
                                    </div>
                                    <div v-for="(cmt, i) in dataCmtCourse.find(item => item.course_id === course.id).cmt.slice(2)"
                                         :key="cmt.id" class="hidden feedback-more-item">
                                        <div class="flex items-center my-2">
                                            <div class="flex-none">
                                                <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                    <div class="bg-[#DAFBDE] rounded-full">
                                                        <img class="rounded-full"
                                                             :src="`/images/course/img-user-cmt/${course.id}-${i + 3}.png`"
                                                             alt="user-default-logo.png">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="font-averta-regular ml-2">
                                                <div class="font-semibold">@{{ cmt.name }}</div>
                                                <div class="flex items-center">
                                                    <img v-for="i in 5" class="mr-[1px] w-4"
                                                         src="{{ asset('/images/icons/star.png') }}"
                                                         alt="star">
                                                    <span
                                                            class="text-[#737680] ml-4 text-[10px] relative top-[2px]">@{{ cmt.date }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="font-averta-regular my-2 text-justify">@{{ cmt.comment }}</div>
                                    </div>
                                @else
                                    @foreach ($feedbacks->take(2) as $feedback)
                                        <div>
                                            <div class="flex items-center my-2">
                                                <div class="flex-none">
                                                    <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                        <div class="bg-[#DAFBDE] rounded-full">
                                                            <img class="rounded-full"
                                                                 src="{{ asset('/images/icons/user-default-logo.png') }}"
                                                                 alt="user-default-logo.png">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="font-averta-regular ml-2">
                                                    <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                    <div class="flex items-center">
                                                        @for ($i = 5; $i > 0; $i--)
                                                            <img class="mr-[1px] w-4"
                                                                 src="{{ asset('/images/icons/star.png') }}"
                                                                 alt="star">
                                                        @endfor
                                                        <span
                                                                class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                        </div>
                                    @endforeach
                                    @foreach ($feedbacks->slice(2) as $feedback)
                                        <div class="hidden feedback-more-item">
                                            <div class="flex items-center my-2">
                                                <div class="flex-none">
                                                    <div class="bg-white rounded-full p-1 w-[40px] h-[40px]">
                                                        <div class="bg-[#DAFBDE] rounded-full">
                                                            <img class="rounded-full"
                                                                 src="{{ asset('/images/icons/user-default-logo.png') }}"
                                                                 alt="user-default-logo.png">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="font-averta-regular ml-2">
                                                    <div class="font-semibold">{{ $feedback->users->name }}</div>
                                                    <div class="flex items-center">
                                                        @for ($i = 5; $i > 0; $i--)
                                                            <img class="mr-[1px] w-4"
                                                                 src="{{ asset('/images/icons/star.png') }}"
                                                                 alt="star">
                                                        @endfor
                                                        <span
                                                                class="text-[#737680] ml-4 text-[10px] relative top-[2px]">{{ $feedback->date }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="font-averta-regular my-2 text-justify">{{ $feedback->content }}</div>
                                        </div>
                                    @endforeach
                                @endif

                            </div>

                            <div class="underline text-[#EF6D13] text-right text-base cursor-pointer btn-more-feedback">
                                Xem thêm >>
                            </div>
                        </div>
                        {{-- END FEEDBACK MOBILE --}}
                    @endif
                </div>

                {{-- APP INTRO --}}
                <div class="bg-[#F0FFF1] py-40 relative mt-[160px] mb-[50px] sp:mt-10 sp:mb-10 sp:py-10">
                    <div class="container max-w-[1100px] mx-auto sp:px-6">
                        <div class="grid grid-cols-3 sp:grid-cols-1">
                            <div class="col-span-2 flex items-center justify-end sp:block">
                                <div
                                        class="text-[48px] text-[#07403F] flex-none font-beanbag w-auto sp:text-center sp:leading-[50px]">
                                    Học mọi lúc, <br>
                                    mọi nơi
                                </div>
                                <div class="flex-none hyphen bg-[#57D061] h-[90%] w-[8px] rounded-full mx-6 sp:hidden"></div>
                                <div>
                                    <div class="font-averta-semibold text-[18px] ml-3 sp:text-justify sp:mt-5">
                                        Kho tàng kiến thức nằm ngay trong túi của bạn!
                                        Một chạm mở <b class="font-averta-bold">App DUNGMORI</b>,
                                        bạn có thể bắt đầu giờ học ngay dù đang ở bất kì đâu!
                                    </div>
                                    <div class="flex justify-center items-center mt-5 sp:grid sp:grid-cols-1 sp:gap-2">
                                        <a class="w-1/2 sp:w-full sp:text-center"
                                           href="https://apps.apple.com/us/app/dungmori/id1486123836"
                                           target="_blank">
                                            <img class="sp:w-[170px]"
                                                 src="{{ asset('/images/icons/apple-store.png') }}?v={{ $assetVersion }}"
                                                 alt="apple-store.png">
                                        </a>
                                        <a class="w-1/2 desktop:ml-3 sp:w-full sp:text-center"
                                           href="https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp&pli=1"
                                           target="_blank">
                                            <img class="sp:w-[170px]"
                                                 src="{{ asset('/images/icons/google-play.png') }}?v={{ $assetVersion }}"
                                                 alt="google-play.png">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-1">
                                <img class="absolute sp:relative top-1/2 transform -translate-y-1/2 ml-4 sp:ml-0 w-[360px] mt-[10px]"
                                     src="{{ asset('/images/lessons/phone.png') }}?v={{ $assetVersion }}"
                                     alt="phone.png">
                            </div>
                        </div>
                    </div>
                </div>
                {{-- END APP INTRO --}}
            </div>
        </div>

        <!-- Popup Modal Coupon Code-->
        <div v-if="showModalAddCode" id="modalEnterCouponCode" class="modal fade" tabindex="-1" role="dialog"
             data-backdrop="static"
             ref="modalEnterCouponCode">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="grid grid-cols-2 gap-4 justify-center items-center">
                            <div>
                                <img class="rounded-[24px]"
                                     src="/images/lessons/img-popup-enter-coupon-code.png"
                                     alt="">
                            </div>
                            <div class="">
                                <div class="flex justify-end">
                                    <svg @click="skipModal" class="cursor-pointer"
                                         width="14"
                                         height="14" viewBox="0 0 14 14" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8.1337 7L13.7922 1.34154C13.9305 1.18886 14.0048 0.988831 13.9998 0.782863C13.9947 0.576896 13.9106 0.380767 13.7649 0.235082C13.6192 0.0893969 13.4231 0.00531458 13.2171 0.00024342C13.0112 -0.00482774 12.8111 0.0695006 12.6585 0.207839L7 5.8663L1.34154 0.207839C1.18886 0.0695006 0.988831 -0.00482774 0.782863 0.00024342C0.576896 0.00531458 0.380767 0.0893969 0.235082 0.235082C0.0893969 0.380767 0.00531458 0.576896 0.00024342 0.782863C-0.00482774 0.988831 0.0695006 1.18886 0.207839 1.34154L5.8663 7L0.207839 12.6585C0.0695006 12.8111 -0.00482774 13.0112 0.00024342 13.2171C0.00531458 13.4231 0.0893969 13.6192 0.235082 13.7649C0.380767 13.9106 0.576896 13.9947 0.782863 13.9998C0.988831 14.0048 1.18886 13.9305 1.34154 13.7922L7 8.1337L12.6585 13.7922C12.8111 13.9305 13.0112 14.0048 13.2171 13.9998C13.4231 13.9947 13.6192 13.9106 13.7649 13.7649C13.9106 13.6192 13.9947 13.4231 13.9998 13.2171C14.0048 13.0112 13.9305 12.8111 13.7922 12.6585L8.1337 7Z"
                                              fill="#212121"/>
                                    </svg>
                                </div>
                                <div class="mb-[42px]">
                                    <div class="font-zuume-semibold uppercase text-[38px] text-[#57D061]">
                                        Có mã giới thiệu?
                                    </div>
                                    <div class="text-[14px] text-[#1E1E1E] font-averta-regular">
                                        Mở toàn bộ khóa học
                                    </div>
                                    <div class="text-[14px] text-[#1E1E1E]">
                                    <span class="text-bold">GUNGUN
                                        @switch($course->id)
                                            @case(\App\Http\ModelsFrontend\Course::ID_COURSE_N3_NEW)
                                                N3
                                                @break
                                            @case(\App\Http\ModelsFrontend\Course::ID_COURSE_N2_NEW)
                                                N2
                                                @break
                                            @case(\App\Http\ModelsFrontend\Course::ID_COURSE_N1_NEW)
                                                N1
                                                @break
                                            @default
                                                Chữ Hán N5
                                                @break
                                        @endswitch
                                        mới</span> trong
                                        @if($course->id === 47)
                                            3 tháng
                                        @else
                                            24 giờ
                                        @endif
                                    </div>
                                </div>

                                <div class="form__group field mb-[42px]">
                                    <input
                                            autocomplete="off"
                                            :disabled="loading_btn_add_code"
                                            type="text" class="form__field" placeholder="Nhập mã"
                                            v-model="add_code_to_user.code_input" name="name" id='name'
                                            required/>
                                    <p
                                            class="text-[14px]"
                                            :class="`${status_add_code ? 'text-[#C00F0C]' : 'text-[#FFFFFF]'}`">
                                        *Mã không hợp lệ</p>
                                </div>

                                <div>
                                    <button
                                            @click="addCodeToUser()"
                                            v-loading="loading_btn_add_code"
                                            :disabled="!add_code_to_user.code_input"
                                            :class="`uppercase font-beanbag text-[12px] w-full rounded-full py-[12px] mb-[12px] ${add_code_to_user.code_input ? 'bg-[#CEFFD8] text-[#07403F] cursor-pointer' : 'bg-[#D9D9D9] text-[#B3B3B3]'}`"
                                    >
                                        áp dụng mã
                                    </button>

                                    <button @click="skipModal"
                                            class="uppercase font-beanbag text-[12px] text-[#07403F] w-full rounded-full py-[12px] border border-[#D9D9D9]">
                                        Bỏ qua
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showModalAddCode"
             class="fixed z-100 sm:top-[80px] sm:left-[15px] z-[100] bottom-[30px] right-[30px] cursor-pointer w-[180px] h-[152px]"
             data-toggle="modal" data-target="#modalEnterCouponCode">
            <div class="relative">
                <img class="block"
                     src="{{ asset('images/course/' . ($course->id === 47  ? 'img-add-3month-pc.png' : 'img-add-24h-pc.png')) }}"
                     alt="">
                <div class="time-countdown absolute bottom-[15px] left-0 right-0 flex items-center justify-center">
                    <svg width="26" height="26" viewBox="0 0 26 26" fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M21.8457 14.3223C21.8457 19.4078 17.7184 23.5352 12.6328 23.5352C7.5473 23.5352 3.41992 19.4078 3.41992 14.3223C3.41992 9.23676 7.5473 5.10938 12.6328 5.10938C17.7184 5.10938 21.8457 9.23676 21.8457 14.3223Z"
                              stroke="#07403F" stroke-width="1.91277" stroke-linecap="round"
                              stroke-linejoin="round"/>
                        <path d="M12.6348 8.79297V14.0575" stroke="#07403F" stroke-width="1.91277"
                              stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9.47461 2.4707H15.792" stroke="#07403F" stroke-width="1.91277"
                              stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>

                    <div class="ml-[8px] leading-1 text-[24px] leading-[24px] text-bold text-[#07403F] font-averta-bold">
                        @{{ formatTime(currentTimeCodeLeft) }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Popup noti active trail course -->
        <div class="relative z-[100]"
             id="noti-active-trail-course">
            <div id="noti-active-trail-course-wrap"
                 :class="`${is_active_trail_course ? 'translate-y-0' : 'translate-y-full'}`"
                 class="fixed bottom-0 inset-x-0 rounded-t-lg transform transition-transform duration-300 px-[20px] py-[170px] md:p-[60px] text-center"
            >
                <div v-if="is_active_trail_course"
                     class="px-[44px] py-[24px] bg-[#F0FFF1] max-w-[1096px] items-center justify-between mx-auto rounded-[24px] mb-[10px] flex"
                     style="box-shadow: 0 0 24px 0 rgba(7, 64, 63, 0.4);"
                >
                    <div class="svg-icon">
                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="20" fill="#57D061"/>
                            <path d="M27.6192 14.2861L17.143 24.7623L12.3811 20.0004" stroke="#EBFFEE"
                                  stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="text-content">
                        <div class="text-[#57D061] text-[20px] font-beanbag leading-[16px] mb-[14px]">
                            <svg width="30" height="24" viewBox="0 0 30 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path d="M16.7694 1.18927C17.6354 3.32006 18.4398 5.46186 19.1828 7.61466C21.9426 7.75782 24.6968 8.03311 27.4398 8.43504C29.4789 8.74337 29.8979 10.9622 28.1605 11.6395C25.8141 12.581 23.5571 13.6216 21.3951 14.7503C21.9929 16.9196 22.5292 19.0945 23.0041 21.2748C23.3281 22.877 21.3896 23.9507 20.1326 22.899C18.4007 21.4565 16.5627 20.08 14.6242 18.7751C12.6856 20.08 10.8477 21.4565 9.11581 22.899C7.85883 23.9507 5.92029 22.877 6.24431 21.2748C6.71358 19.0945 7.24989 16.9196 7.85324 14.7503C5.69123 13.6216 3.43425 12.581 1.08788 11.6395C-0.649548 10.9622 -0.230554 8.74337 1.80855 8.43504C4.54598 8.03311 7.30017 7.75782 10.0655 7.61466C10.803 5.46186 11.6074 3.32006 12.4789 1.18927C13.1102 -0.396425 16.1437 -0.396425 16.775 1.18927H16.7694Z"
                                      fill="url(#paint0_radial_7193_11252)"/>
                                <defs>
                                    <radialGradient id="paint0_radial_7193_11252" cx="0" cy="0" r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(14.6186 11.6615) scale(16.4134 16.1763)">
                                        <stop offset="0.12" stop-color="#E9FF6B"/>
                                        <stop offset="0.92" stop-color="#FF9D00"/>
                                    </radialGradient>
                                </defs>
                            </svg>
                            Kích hoạt thành công
                            <svg width="30" height="24" viewBox="0 0 30 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path d="M16.7694 1.18927C17.6354 3.32006 18.4398 5.46186 19.1828 7.61466C21.9426 7.75782 24.6968 8.03311 27.4398 8.43504C29.4789 8.74337 29.8979 10.9622 28.1605 11.6395C25.8141 12.581 23.5571 13.6216 21.3951 14.7503C21.9929 16.9196 22.5292 19.0945 23.0041 21.2748C23.3281 22.877 21.3896 23.9507 20.1326 22.899C18.4007 21.4565 16.5627 20.08 14.6242 18.7751C12.6856 20.08 10.8477 21.4565 9.11581 22.899C7.85883 23.9507 5.92029 22.877 6.24431 21.2748C6.71358 19.0945 7.24989 16.9196 7.85324 14.7503C5.69123 13.6216 3.43425 12.581 1.08788 11.6395C-0.649548 10.9622 -0.230554 8.74337 1.80855 8.43504C4.54598 8.03311 7.30017 7.75782 10.0655 7.61466C10.803 5.46186 11.6074 3.32006 12.4789 1.18927C13.1102 -0.396425 16.1437 -0.396425 16.775 1.18927H16.7694Z"
                                      fill="url(#paint0_radial_7193_11252)"/>
                                <defs>
                                    <radialGradient id="paint0_radial_7193_11252" cx="0" cy="0" r="1"
                                                    gradientUnits="userSpaceOnUse"
                                                    gradientTransform="translate(14.6186 11.6615) scale(16.4134 16.1763)">
                                        <stop offset="0.12" stop-color="#E9FF6B"/>
                                        <stop offset="0.92" stop-color="#FF9D00"/>
                                    </radialGradient>
                                </defs>
                            </svg>
                        </div>
                        <div class="text-[#07403F] text-[20px] font-averta-regular font-[600] leading-[16px]">
                            Bạn có @{{ is_active_trail_course.type_code }} giờ trải nghiệm toàn bộ khóa @{{
                            is_active_trail_course.course_id === 47 ? 'Chữ Hán N5' : is_active_trail_course.course_id
                            === 46 ? 'N1' : is_active_trail_course.course_id === 45 ? 'N2' : 'N3' }} mới
                        </div>
                    </div>
                    <p>
                    </p>
                </div>
            </div>
        </div>
    </div>

@stop

@section('footer-js')
    <script src="{{ asset('assets/js/course/basic.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script src="https://vjs.zencdn.net/8.16.1/video.min.js"></script>
    <script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>

    <script type="text/javascript">
        @if(Auth::check() && session('show_modal_add_code'))
        $(document).ready(function () {
            $('#modalEnterCouponCode').modal('show');
        });
        @endif

        let course = @json($course);
        let courseOwner = @json($courseOwner);
        let key_stage_open = @json($stageOpen);
        let courseOwnerKanjiN5 = @json($courseOwnerKanjiN5);
        let courseOwnerN5 = @json($courseOwnerN5);
        let courseN5 = @json($courseN5);
        let courseKanjiN5 = @json($courseKanjiN5);
        let courseExpireDay = null;
        @if(isset($courseExpireDay))
            courseExpireDay = @json($courseExpireDay);
        @endif
        let is_active_trail_course = @json(session('is_active_trail_course'));
        let serverTime = @json(now()->timestamp);
        console.log(`serverTime: `, serverTime);

        var checkFocusTab = true;
        $(document).ready(function () {
            window.onfocus = function () {
                checkFocusTab = true;
            };
            window.onblur = function () {
                checkFocusTab = false;
            };
        });
        var auth = @json(Auth::user());

        if (courseOwner) {
            if (auth && !auth.is_tester && !auth.is_assistant) {
                setInterval(() => {
                    if (checkFocusTab) {
                        switch (courseOwner.course_id) {
                            case 39:
                                ga('send', 'event', courseOwner.code, 'hoc_n5', 'thoi_gian_hoc');
                                break;
                            case 40:
                                ga('send', 'event', courseOwner.code, 'hoc_n4', 'thoi_gian_hoc');
                                break;
                            case 3:
                                ga('send', 'event', courseOwner.code, 'hoc_n3', 'thoi_gian_hoc');
                                break;
                            case 16:
                                ga('send', 'event', courseOwner.code, 'hoc_n2', 'thoi_gian_hoc');
                                break;
                            case 17:
                                ga('send', 'event', courseOwner.code, 'hoc_n1', 'thoi_gian_hoc');
                                break;
                        }
                    }
                }, 60000); // 60s
            }
        }

        const ID_CODE_3MONTH = 90;
        const ID_CODE_24H = 91;
        const ID_CODE_1H = 92;

        const ID_COURSE_N3 = 44;
        const ID_COURSE_N2 = 45;
        const ID_COURSE_N1 = 46;
        const ID_COURSE_KANJI_N5 = 47;

        // lấy data từ file js
        let dataCmtCourse = @json(file_get_contents(public_path('data-cmt-course.json')));

        new Vue({
            el: "#course_basic_vue",
            data: {
                totalTime: 24 * 60 * 60, // 24 giờ tính bằng giây
                timeElapsed: 0, // Thời gian đã trôi qua (tính bằng giây)
                activeStep: 0, // Bắt đầu từ mốc 0
                timer: null, // Để lưu setInterval
                courseOwner: courseOwner,
                linkPromotion: {
                    1: "https://m.me/102856559258931?ref=IAP30", // 30%
                    2: "https://m.me/102856559258931?ref=IAP20", // 20%
                    3: "https://m.me/102856559258931?ref=IAP10", // 10%
                },
                key_stage_open: key_stage_open,
                status_add_code: 0,
                loading_btn_add_code: false,
                add_code_to_user: {
                    code_input: null,
                    course_id: course.id
                },
                timeLeft: 0,
                isMounted: false,
                courseOwnerKanjiN5: courseOwnerKanjiN5,
                courseOwnerN5: courseOwnerN5,
                courseN5: courseN5,
                courseKanjiN5: courseKanjiN5,
                courseExpireDay: courseExpireDay,
                priceDiscount: false,
                showModalAddCode: false,
                auth: auth,
                showBoxPrice: false,
                showButtonBuy: true,
                is_active_trail_course: null,
                dataCmtCourse: JSON.parse(dataCmtCourse),
                ID_COURSE_N3: ID_COURSE_N3,
                ID_COURSE_N2: ID_COURSE_N2,
                ID_COURSE_N1: ID_COURSE_N1,
                ID_COURSE_KANJI_N5: ID_COURSE_KANJI_N5,
                serverTime: serverTime,
            },
            methods: {
                skipModal() {
                    if ((this.courseOwner !== null && this.courseOwner.code_active_at === null) || (this.courseOwner === null && this.auth)) {
                        this.add_code_to_user.code_input = 'free1gio';
                        this.addCodeToUser();
                        return;
                    }
                    // Reset data
                    this.add_code_to_user.code_input = '';
                    this.status_add_code = false;
                    $('#modalEnterCouponCode').modal('hide');
                },
                async addCodeToUser() {
                    this.loading_btn_add_code = true
                    await axios.post('/khoa-hoc/add-code-lesson-to-user', this.add_code_to_user).then(res => {
                        if (res.data.error_code) {
                            this.status_add_code = 1
                            return;
                        }
                        location.reload();
                    }).catch(err => {
                        this.loading_btn_add_code = false
                    })
                    this.loading_btn_add_code = false
                },
                handleClickPromotionFooter(event, type = 'btn') {
                    const clickedElement = event.currentTarget;
                    var text = "buy_n" + (this.courseOwner.course_id == 39 ? "5" : "4") + "_" + (this.activeStep == 1 ? "30" : (this.activeStep == 2 ? "20" : (this.activeStep == 3 ? "10" : "")));
                    if (type === 'link' || clickedElement.classList.contains('promotion-active')) {
                        ga('send', 'event', 'hoc_thu_cate', text, 'buy_label');
                        window.open(this.linkPromotion[this.activeStep], '_blank');
                    }
                },

                buyNow(courseId) {
                    var text = "buy_n" + (courseId == 39 ? "5" : "4") + "_0";
                    ga('send', 'event', 'hoc_thu_cate', text, 'buy_label');
                    let link = `/payment?buy=${btoa('course')}&item=${btoa(course.id)}`
                    window.open(link, '_blank');
                },

                startTimer() {
                    this.timer = setInterval(() => {
                        this.timeElapsed--;
                        if (this.timeElapsed <= 0) {
                            this.stopTimer();
                            this.priceDiscount = false;
                            this.courseExpireDay = null;
                        }
                    }, 1000);
                },
                stopTimer() {
                    clearInterval(this.timer);
                },

                formatTime(seconds) {
                    const hours = Math.floor(seconds / 3600);
                    const minutes = Math.floor((seconds % 3600) / 60);
                    const secs = seconds % 60;

                    return `${hours.toString().padStart(2, '0')}:${minutes
                        .toString()
                        .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                initializePromotion(startTime = null) {
                    if (startTime) {
                        // Nếu có thời gian kích hoạt mã từ trước
                        this.startTime = startTime;
                        this.timeElapsed = startTime - this.serverTime;

                    } else {
                        // Nếu không có, thiết lập thời gian kích hoạt mới
                        this.startTime = Math.floor(Date.now() / 1000); // Lưu timestamp hiện tại
                        localStorage.setItem("promotionStartTime", this.startTime); // Lưu thời gian kích hoạt vào localStorage
                    }
                    this.startTimer(); // Bắt đầu đếm ngược
                },
                scrollToLastLesson() {
                    if (this.key_stage_open) {
                        this.$nextTick(() => {
                            const lessonElement = $("#step-content-3").find(`#content-stage-${this.key_stage_open}`);
                            if (lessonElement) {
                                window.scrollTo({
                                    top: lessonElement.offset().top,
                                    behavior: 'smooth'
                                });
                            }
                        });
                    }
                },

                updateTimer() {
                    const now = new Date();
                    const hours = now.getHours();
                    // Tìm mốc 3 tiếng tiếp theo
                    var nextMilestone = 0;
                    if (hours % 3 === 0) {
                        nextMilestone = hours + 3;
                    } else {
                        nextMilestone = Math.ceil(hours / 3) * 3; // Làm tròn lên đến mốc 3, 6, 9, 12, 15, 18, 21, 24
                    }
                    const nextMilestoneTime = new Date();
                    nextMilestoneTime.setHours(nextMilestone, 0, 0, 0);
                    // Tính thời gian còn lại đơn vị giây
                    this.timeLeft = Math.floor((nextMilestoneTime - now) / 1000);
                    // Nếu thời gian hết, chờ 1 giây để chuyển mốc mới
                    if (this.timeLeft <= 0) {
                        setTimeout(() => this.updateTimer(), 1000);
                    }
                },

                number_format(number) {
                    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                }
            },
            beforeCreate() {
            },
            created() {
                const nowTimestamp = this.serverTime;
                if (course.id === 47) {
                    if (this.courseN5 !== null && this.auth && this.courseOwner.code_id && ((this.courseOwner.code_id == ID_CODE_1H && (new Date(this.courseOwner.watch_expired_day.replace(" ", "T")).getTime() / 1000) - this.serverTime < 60 * 60) || (this.courseOwner.code_id == ID_CODE_3MONTH && (new Date(this.courseOwner.watch_expired_day.replace(" ", "T")).getTime() / 1000) - this.serverTime < 60 * 60 * 24))) {
                        this.priceDiscount = true;
                    }

                    // nếu có khóa sơ cấp N5 thì không hiện nút nâng cấp
                    // chưa có khóa sơ cấp N5 và chưa nhập code thì hiển thị giá tiền của khóa sơ cấp N5 và nút nâng cấp
                    // chưa có khóa sơ cấp N5 và đã nhập code 1h hoặc 24h và thời gian active code nằm trong khoảng thời gian học của code now - thời gian kích hoạt code < thời gian học của code thì hiển thị box price discount
                    // chưa có khóa sơ cấp N5 và đã nhập code 1h hoặc 24h và thời gian active code nằm trong khoảng thời gian học của code now - thời gian kích hoạt code > thời gian học của code thì không hiển thị box price discount hiển thị mỗi nút nâng cấp
                    if (this.courseOwnerN5) {
                        this.showButtonBuy = false;
                    } else if (!this.courseOwnerN5) {
                        this.showButtonBuy = true;
                        this.showBoxPrice = true;
                        if (this.courseOwner.code_id) {
                            const codeActiveAtTimestamp = new Date(this.courseOwner.code_active_at.replace(" ", "T")).getTime() / 1000;
                            if ((this.courseOwner.code_id === ID_CODE_1H &&
                                (nowTimestamp - codeActiveAtTimestamp < 60 * 60)
                                ||
                                (this.courseOwner.code_id === ID_CODE_3MONTH &&
                                    (nowTimestamp - codeActiveAtTimestamp < 60 * 60 * 25)))) {
                                this.priceDiscount = true;
                            }
                        }
                        if (!this.courseOwner.code_id || (this.courseOwner && this.courseOwner.code_id === ID_CODE_1H)) {
                            this.showModalAddCode = true;
                        }
                    }
                } else {
                    if (course.id === 39 && this.courseOwnerKanjiN5 && this.courseOwnerKanjiN5.code_id) {
                        const codeActiveAtTimestamp = new Date(this.courseOwnerKanjiN5.code_active_at.replace(" ", "T")).getTime() / 1000;
                        if (this.courseOwnerKanjiN5 !== null && this.auth && this.courseOwnerKanjiN5.code_id && ((this.courseOwnerKanjiN5.code_id == ID_CODE_1H && (nowTimestamp - codeActiveAtTimestamp < 60 * 60) || (this.courseOwnerKanjiN5.code_id == ID_CODE_3MONTH && (nowTimestamp - codeActiveAtTimestamp < 60 * 60 * 25))))) {
                            this.priceDiscount = true;
                        }
                    }

                    // nếu khóa còn hạn và không có code_id thì không hiển thị box price
                    // nếu khóa còn hạn và có code_id và thời gian active code nằm trong khoảng thời gian học của code và thời gian hết hạn của khóa học = thời gian kích hoạt code + thời gian học của code thì hiển thị box price
                    // nếu khóa hết hạn hoặc không có khóa thì hiện box price
                    if (this.courseOwner && this.courseExpireDay && this.courseOwner.code_id) {
                        const codeActiveAtTimestamp = new Date(this.courseOwner.code_active_at.replace(" ", "T")).getTime() / 1000;
                        if ((nowTimestamp - codeActiveAtTimestamp < 60 * 60 && this.courseOwner.code_id === ID_CODE_1H) || (nowTimestamp - codeActiveAtTimestamp < 60 * 60 * 25 && this.courseOwner.code_id === ID_CODE_3MONTH) || (nowTimestamp - codeActiveAtTimestamp < 60 * 60 * 25 && this.courseOwner.code_id === ID_CODE_24H)) {
                            this.priceDiscount = true;
                            const serverTimestamp = new Date(this.courseOwner.watch_expired_day.replace(" ", "T")).getTime() / 1000; // Chuyển thành timestamp
                            if ((serverTimestamp - (codeActiveAtTimestamp + 60 * 60) < 180 && this.courseOwner.code_id === ID_CODE_1H) || (serverTimestamp - (codeActiveAtTimestamp + 60 * 60 * 25) < 180 && this.courseOwner.code_id === ID_CODE_3MONTH) || (serverTimestamp - (codeActiveAtTimestamp + 60 * 60 * 25) < 180 && this.courseOwner.code_id === ID_CODE_24H)) {
                                this.showBoxPrice = true;
                            }
                        }
                    } else {
                        this.showBoxPrice = !(this.courseOwner && this.courseExpireDay && !this.courseOwner.code_id);
                    }

                    //showModalAddCode
                    // nếu chưa đăng nhập hoặc không có khóa học thì hiển thị modal
                    // nếu có khóa học đã hết hạn và chưa nhập code thì hiển thị modal
                    // nếu có khóa học còn hạn và không có code_id thì không hiển thị modal
                    // nếu có khóa học còn hạn và có code_id là code 3 tháng hoặc 24h thì không hiển thị modal
                    // nếu có khóa học còn hạn và có code_id là code 1h và thời gian active code nằm trong khoảng thời gian học của code và thời gian hết hạn của khóa học = thời gian kích hoạt code + thời gian học của code thì hiển thị modal
                    if ([44, 45, 46, 47].includes(course.id)) {
                        if (!this.auth || !this.courseOwner) {
                            this.showModalAddCode = true;
                        } else if (this.courseOwner && this.courseExpireDay && this.courseOwner.code_id) {
                            const codeActiveAtTimestamp = new Date(this.courseOwner.code_active_at.replace(" ", "T")).getTime() / 1000;
                            const nowTimestamp = this.serverTime;
                            const serverTimestamp = new Date(this.courseOwner.watch_expired_day.replace(" ", "T")).getTime() / 1000;
                            if (nowTimestamp - codeActiveAtTimestamp < 60 * 60 && this.courseOwner.code_id === ID_CODE_1H && serverTimestamp - (codeActiveAtTimestamp + 60 * 60) < 180) {
                                this.showModalAddCode = true;
                            }
                        } else if (this.courseOwner && !this.courseExpireDay && !this.courseOwner.code_id) {
                            this.showModalAddCode = true;
                        } else if (this.courseOwner && !this.courseExpireDay && this.courseOwner.code_id === ID_CODE_1H) {
                            this.showModalAddCode = true;
                        }
                    }
                }
            },
            watch: {
                'add_code_to_user.code_input': {
                    handler: function (val, oldVal) {
                        this.status_add_code = 0;
                    },
                    deep: true
                }
            },
            mounted() {
                if (courseOwnerKanjiN5 && courseOwnerKanjiN5.code_id) {
                    const codeActiveAtTimestamp = new Date(courseOwnerKanjiN5.code_active_at.replace(" ", "T")).getTime() / 1000;
                    const nowTimestamp = this.serverTime;
                    if (!courseExpireDay && (((nowTimestamp - codeActiveAtTimestamp) < 60 * 60 && courseOwnerKanjiN5.code_id === ID_CODE_1H) || ((nowTimestamp - codeActiveAtTimestamp) < 60 * 60 * 24 && courseOwnerKanjiN5.code_id === ID_CODE_3MONTH))) {
                        this.initializePromotion(Number(new Date(courseOwnerKanjiN5.watch_expired_day.replace(" ", "T")).getTime() / 1000));
                    }
                }

                if (this.courseOwner !== null && this.courseOwner.code_active_at !== null) {
                    const serverTimestamp = new Date(this.courseOwner.watch_expired_day.replace(" ", "T")).getTime() / 1000; // Chuyển thành timestamp
                    this.initializePromotion(Number(serverTimestamp));
                }

                const now = new Date();
                const nowFormat = now.toISOString().slice(0, 19).replace('T', ' ');

                this.updateTimer();
                this.timer = setInterval(() => {
                    this.updateTimer();
                }, 1000);
                this.isMounted = true;
                this.is_active_trail_course = is_active_trail_course;
                setTimeout(() => {
                    this.is_active_trail_course = null;
                }, 5000);

                // this.scrollToLastLesson();
            },
            computed: {
                currentCourseTimeLeft() {
                    return this.formatTime(this.timeElapsed);
                },
                currentTimeCodeLeft() {
                    if (this.timeLeft) {
                        return this.timeLeft;
                    } else {
                        return 0;
                    }
                }
            },
            beforeDestroy() {
                this.stopTimer(); // Dừng đếm ngược khi component bị hủy
                clearInterval(this.timer);
                clearInterval(this.timeLeft);
            },
        })
    </script>
@stop
