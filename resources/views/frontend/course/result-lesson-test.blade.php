@extends('frontend._layouts.default')
@section('title')
    {{--    <PERSON><PERSON><PERSON><PERSON> họ<PERSON> {{ $course->name }} - Dungmori--}}
@stop
@section('description')
    Dungmori YouTuber dạy tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất
@stop
@section('keywords')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất
@stop
{{--@section('image'){{ url('cdn/course/default') }}/{{ $course->avatar_name }} @stop--}}
@section('author')
    DUNGMORI
@stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link href="https://vjs.zencdn.net/8.16.1/video-js.css" rel="stylesheet"/>
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
@stop
@section('content')

    <div id="container-result-lesson-test">
        <div class="my-[100px]">
            <el-button type="text" @click="dialogTableVisible = true">open a Table nested Dialog</el-button>

            <el-dialog title="Chi tiết bài làm" :visible.sync="dialogTableVisible" width="1097px" class="custom-dialog-user-result" :close-on-click-modal="false">
                <!-- Slot tùy chỉnh tiêu đề -->
                <template #title>
                    <div class="custom-title pt-[57px] pl-[86px] bg-[#F4F5FA]">
                        <span class="text-[#07403F] font-beanbag text-[20px] ">Tiêu đề tùy chỉnh</span>
                    </div>
                </template>

                <!-- Slot header -->
                <template #header>
                    <div class="custom-header">
                        <span class="custom-title">Tiêu đề Tùy Chỉnh 123123</span>
                        <button class="custom-close-button" @click="dialogVisible = false">
                            ✖ zxc
                        </button>
                    </div>
                </template>


                <div class="dialog-wrapper pl-[43px] pt-[24px] pr-[38px] mx-[43px] custom-scrollbar bg-[#F4F5FA]">
                    <div class="mb-[50px] px-[23px] py-[32px] rounded-[24px] border-[1px] border-[#e1dfdf] text-sm w-full translate-x-0 scale-100 transition-all duration-300 ease-in-out text-[#07403F] bg-[#CEFFD8] drop-shadow-2xl">
                        <table style="border: none">
                            <tbody style="border: none">
                            <tr style="border: none">
                                <td style="border: none" class="min-w-[140px]">
                                    <h5 class="font-averta-bold">Kết quả</h5>
                                </td>
                                <td style="border: none" class="text-[16px] font-averta-regular">Đạt</td>
                            </tr>
                            <tr style="border: none">
                                <td style="border: none" class="min-w-[150px]">
                                    <h5 class="font-averta-bold">Điểm của bạn</h5>
                                </td>
                                <td style="border: none" class="text-[16px] font-averta-regular">13/15 (>85%)</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="list-result-question">
                        <div class="result-question-wrapper">
                            <div v-for="(question, key) in dataLesson.component" class="result-question-item"
                                 :key="key + 1">
                                <div class="result-question-wrapper p-2 mb-[36px]">
                                    <div class="result-question-item-divider flex items-center">
                                        <h1 class="font-zuume-semibold text-[#57D061] font-bold mr-2">@{{ key + 1
                                            }}</h1>
                                        <div class="flex-1 border-t border-gray-300"></div>
                                    </div>
                                    <div class="result-question-item-content">
                                        <template v-if="question.type === 3">
                                            <!-- Question -->
                                            <div :class="`mt-6 mb-15 text-center ${ checkAnswerAudio(question) != 1 ? 'max-w-[695px]' : ''} mx-auto flex justify-content-center align-items-center font-gen-jyuu-gothic text-black`">
                                                <template v-if="checkAnswerAudio(question) == 1">
                                                    <div class="bg-white shadow-md rounded-3xl px-[18px] py-[21px] w-full">
                                                        <!-- Icon âm thanh -->
                                                        <div class="flex">
                                                            <svg @click="toggleAudio(question)" class="cursor-pointer"
                                                                 width="48" height="48" viewBox="0 0 48 48" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                      fill="#4E87FF"/>
                                                                <path d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                      fill="#4E87FF"/>
                                                                <path opacity="0.4"
                                                                      d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                      fill="#4E87FF"/>
                                                                <path d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                      fill="#4E87FF"/>
                                                            </svg>
                                                        </div>

                                                        <!-- Văn bản Furigana -->
                                                        <div class="flex-1 text-center text-gray-800 mt-2"
                                                             v-html="getTagImg(question)">
                                                        </div>
                                                    </div>
                                                </template>
                                                <template v-else-if="checkAnswerAudio(question) == 2">
                                                    <button :data-audioButton="`audioButton-${question.id}`"
                                                            :ref="`audioButton-${question.id}`"
                                                            @click="toggleAudio(question)"
                                                            :class="['flex items-center justify-center w-[100px] h-[100px] rounded-full bg-white text-blue-500 hover:bg-blue-100 flex-col', isPlaying ? 'animate-blink' : 'shadow-[0_0_15px_4px] shadow-[#4E87FF80]']"
                                                    >
                                                        <!-- Icon loa -->
                                                        <div class="w-[50px] h-[50px] bg-blue-500 rounded-full flex items-center justify-center">
                                                            <svg width="100px" height="100px" viewBox="0 0 45 44"
                                                                 fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M33.7505 30.7083C33.4505 30.7083 33.1693 30.6166 32.9068 30.4333C32.288 29.975 32.1568 29.1133 32.6255 28.5083C35.5693 24.6766 35.5693 19.3233 32.6255 15.4916C32.1568 14.8866 32.288 14.025 32.9068 13.5666C33.5255 13.1083 34.4068 13.2366 34.8755 13.8416C38.5505 18.645 38.5505 25.355 34.8755 30.1583C34.5943 30.525 34.1818 30.7083 33.7505 30.7083Z"
                                                                      fill="#4E87FF" fill-opacity="0.5"
                                                                      :class="{ 'animate-opacity': isPlaying }"/>
                                                                <path d="M37.1822 35.2917C36.8822 35.2917 36.6009 35.2001 36.3384 35.0167C35.7197 34.5584 35.5884 33.6967 36.0572 33.0917C41.0634 26.5651 41.0634 17.4351 36.0572 10.9084C35.5884 10.3034 35.7197 9.44172 36.3384 8.98339C36.9572 8.52505 37.8384 8.65339 38.3072 9.25839C44.0634 16.7567 44.0634 27.2434 38.3072 34.7417C38.0447 35.1084 37.6134 35.2917 37.1822 35.2917Z"
                                                                      fill="#4E87FF" fill-opacity="0.5"
                                                                      :class="{ 'animate-opacity-second': isPlaying }"/>
                                                                <path opacity="0.4"
                                                                      d="M29.5312 13.585V30.415C29.5312 33.5683 28.3688 35.9333 26.2875 37.07C25.4438 37.5283 24.5063 37.7483 23.5312 37.7483C22.0312 37.7483 20.4188 37.2533 18.7688 36.245L13.2938 32.89C12.9188 32.67 12.4875 32.5417 12.0562 32.5417H10.3125V11.4583H12.0562C12.4875 11.4583 12.9188 11.33 13.2938 11.11L18.7688 7.75499C21.5063 6.08666 24.1875 5.79333 26.2875 6.92999C28.3688 8.06666 29.5312 10.4317 29.5312 13.585Z"
                                                                      fill="#4E87FF"/>
                                                                <path d="M10.3125 11.4583V32.5417H9.375C4.8375 32.5417 2.34375 30.1033 2.34375 25.6667V18.3333C2.34375 13.8967 4.8375 11.4583 9.375 11.4583H10.3125Z"
                                                                      fill="#4E87FF"/>
                                                            </svg>
                                                        </div>
                                                        <!-- Thời gian -->
                                                        <span class="text-[#4E87FF] text-sm font-averta-bold text-blue-500">@{{ formattedTime }}</span>
                                                    </button>
                                                </template>
                                                <template v-else class="">
                                                    <div v-html="question.value"></div>
                                                </template>

                                                {{--                            @if(false)--}}
                                                {{--                                <p class="text-lg font-semibold">このことばは　ひらがなで　どう　かきますか。</p>--}}
                                                {{--                                <p class="text-sm text-gray-500">--}}
                                                {{--                                    1・2・3・4から　いちばん　いいものを　ひとつえらんで　ください。</p>--}}
                                                {{--                            @elseif(true)--}}
                                                {{--                                <div class="video">--}}
                                                {{--                                    <video id="my-video" class="video-js w-[50%] h-[352px]" controls preload="auto"--}}
                                                {{--                                           poster="https://web-test.dungmori.com/cdn/lesson/default/1729757832_118153995_79687.jpeg"--}}
                                                {{--                                           data-setup="{}">--}}
                                                {{--                                        <source--}}
                                                {{--                                                src="https://tokyo-v2.dungmori.com/720p/XHTH-2022-01-3.mp4/index.m3u8"--}}
                                                {{--                                                type="application/x-mpegURL"/>--}}
                                                {{--                                    </video>--}}
                                                {{--                                </div>--}}
                                                {{--                            @endif--}}
                                            </div>

                                            <!-- Options -->
                                            <div id="options"
                                                 class="grid grid-cols-2 gap-4 mb-15 max-w-[695px] mx-auto">
                                                {{--                                                @foreach($component->answers as $keyAnswer => $answer)--}}
                                                <template v-for="(answer, keyAnswer) in question.answers">
                                                    <template v-if="checkAnswerAudio(answer) == 3">
                                                        <label class="block">
                                                            <input :id="answer.id" type="radio"
                                                                   :data-result="answer.grade"
                                                                   name="answer" :value="answer.value"
                                                                   class="hidden">
                                                            <span :id="`span-option-${answer.id}`"
                                                                  :data-question="question.id"
                                                                  :data-answer="answer.id"
                                                                  :data-result="answer.grade"
                                                                  :class="`text-bold span-option p-[12px] ${renderStatusOptionResult(answer)} border-[4px] rounded-full cursor-pointer flex align-items-center text-[#07403F]`">
                                                                <span>
{{--                                                                    {{ chr(65 + $keyAnswer) }}.--}}
                                                                </span>
                                                                <span class="ml-2 text-black font-gen-jyuu-gothic w-full text-center">
                                                                    <svg class="cursor-pointer"
                                                                         width="32" height="32" viewBox="0 0 48 48"
                                                                         fill="none"
                                                                         xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                              fill="#4E87FF"/>
                                                                        <path d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                              fill="#4E87FF"/>
                                                                        <path opacity="0.4"
                                                                              d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                              fill="#4E87FF"/>
                                                                        <path d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                              fill="#4E87FF"/>
                                                                    </svg>
                                                                </span>
                                                            </span>
                                                        </label>
                                                    </template> <!-- Đáp án là audio -->
                                                    <template v-else>
                                                        <label class="block">
                                                            <input :id="answer.id" type="radio"
                                                                   :data-result="answer.grade"
                                                                   name="answer" :value="answer.value"
                                                                   class="hidden">
                                                            <span :id="`span-option-${answer.id}`"
                                                                  :data-question="question.id"
                                                                  :data-answer="answer.id"
                                                                  :data-result="answer.grade"
                                                                  :class="`max-h-[161px] span-option p-4 ${renderStatusOptionResult(answer)} border-[4px] rounded-${checkAnswerAudio(answer) === 4 ? '2xl' : 'full'} cursor-pointer flex align-items-flex-${checkAnswerAudio(answer) === 4 ? 'start' : 'end'} text-[#07403F]`">
                                                                    <span class="text-bold">
{{--                                                                        {{ chr(65 + $keyAnswer) }}.--}}
                                                                    </span>
                                                                    <span class="ml-2 text-black font-averta-semibold font-normal"
                                                                          v-html="answer.value">
                                                                    </span>
                                                                </span>
                                                        </label>
                                                    </template>
                                                </template>
                                                {{--                                                @endforeach--}}
                                            </div>

                                        </template>
                                        <template v-else-if="question.type === 13">
                                            <!-- Question box -->
                                            <div class="grid align-items-center w-full max-w-[80%] min-h-[300px] bg-white rounded-3xl shadow-lg p-6 text-center mx-auto">
                                                <div class="text-2xl font-bold flex justify-content-center flex-wrap items-baseline">
                                                    <template
                                                            v-for="(element, keyElement) in question.value['question']">
                                                        <template v-if="element.type === 'default'">
                                                            <div class="my-2 ml-[6px] missing-word-wrap text-[20px]"
                                                                 v-html="element.value"></div>
                                                        </template>
                                                        <template v-else>
                                                            <span class="ml-[6px] my-2">
                                                            <!-- Input field -->
                                                            <input type="text"
                                                                   :value="element.user_result"
                                                                   :id="`missingWord-${keyElement}-${question.id}`"
                                                                   :data-id="question.id"
                                                                   :data-result="element.result"
                                                                   ref="inputFillInBlank"
                                                                   :class="`missingWord missingWord-${question.id} outline-none min-w-[73px] rounded-[12px] ${renderStatusFillInBlankResult(element, keyElement, question.id)} text-center h-12 font-bold text-[20px]`"
                                                                   @input="checkInput">
                                                            </span>
                                                        </template>
                                                    </template>
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else></template>
                                        <div class="result-question-item-explain mt-[28px]"
                                             v-if="(question.type === 3 && question.explain !== null && question.explain !== '') || (question.type === 13 && question.value['explain'] !== null && question.value['explain'] !== '')">
                                            <div class="px-[12px] py-[9px] rounded-[16px] text-sm w-full text-[#1E1E1E] bg-[#F0FFF1]">
                                                <p class="uppercase font-beanbag">*Giải thích</p>
                                                <div v-html="question.type === 3 ? question.explain : question.value['explain']"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>

            <el-button type="text" @click="dialogVisible = true">open a Table nested Dialog</el-button>

            <el-dialog
                    :visible.sync="dialogVisible"
                    width="50%"
                    class="custom-dialog-user-result"
            >
                <div slot="title">Tiêu đề Dialog</div>

                <div class="custom-scrollbar">
                    <!-- Nội dung dài để test thanh cuộn -->
                    <div v-for="i in 100" :key="i">Dòng nội dung @{{ i }}</div>
                </div>

                <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">Hủy</el-button>
      <el-button type="primary" @click="dialogVisible = false">Xác nhận</el-button>
    </span>
            </el-dialog>
        </div>


    </div>

@stop

@section('footer-js')
    <script src="https://vjs.zencdn.net/8.16.1/video.min.js"></script>
    <script src="{{ asset('assets/js/course/basic.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script>
        let dataLesson = @json($contentLesson);
        let result = @json($result);
        let userResult = []
        let apiLessonExercise = axios.create({
            baseURL: "/khoa-hoc",
            headers: {
                "Content-Type": "application/json",
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            withCredentials: true,
        });

        new Vue({
            el: "#container-result-lesson-test",
            data: {
                dialogTableVisible: false,
                result: result,
                dataLesson: dataLesson,
                duration: 0, // Tổng thời gian của file âm thanh (giây)
                timeRemaining: 0, // Thời gian còn lại (giây)
                isPlaying: false,
                intervalId: null,
                audio: null,
                isCheckAnswer: false,
                statusAnswer: 'doing', // doing, test, next
                isShowExplainButton: false,
                isAnswerSuccess: true,
                isShowExplainIconAudio: true,
                userResult: {
                    lesson_id: dataLesson.id,
                    total_grade: dataLesson.total_marks,
                    grade_pass: dataLesson.pass_marks,
                    grade: 0,
                    data: {},
                    course_id: dataLesson.course_id,
                    correct_answer: 0,
                    total_answer: dataLesson.component.length
                },
                dialogVisible: false
            },
            created() {
                console.log(`this.dataLesson: `, this.dataLesson)
                console.log(`this.result: `, this.result)
            },
            methods: {
                getList() {
                    console.log(`vao day`)
                },
                toggleAudio(currentAnswer) {
                    console.log('currentAnswer: ', currentAnswer)
                    let linkMp3 = this.getLinkMp3(currentAnswer)
                    if (!this.audio) {
                        console.log(`1`)
                        // Khởi tạo Audio object khi lần đầu phát
                        this.audio = new Audio(`https://mp3-vn.dungmori.com/${linkMp3}`);
                        // Lấy thời gian khi âm thanh đã load xong
                        this.audio.addEventListener("loadedmetadata", () => {
                            // console.log(this.audio.duration)
                            this.duration = Math.floor(this.audio.duration)
                            this.timeRemaining = this.duration;
                        });
                        this.audio.addEventListener("ended", this.resetAudio);
                    } else {
                        if (this.audio.src != `https://mp3-vn.dungmori.com/${linkMp3}`) {
                            this.audio = new Audio(`https://mp3-vn.dungmori.com/${linkMp3}`);
                            // Lấy thời gian khi âm thanh đã load xong
                            this.audio.addEventListener("loadedmetadata", () => {
                                // console.log(this.audio.duration)
                                this.duration = Math.floor(this.audio.duration)
                                this.timeRemaining = this.duration;
                            });
                            this.audio.addEventListener("ended", this.resetAudio);
                        }
                    }
                    if (this.isPlaying) {
                        this.pauseAudio();
                    } else {
                        this.playAudio();
                    }
                },
                playAudio() {
                    this.audio.play();
                    this.isPlaying = true;
                    // this.isPlaying = !this.isPlaying;
                    // Bắt đầu đếm ngược thời gian còn lại
                    this.intervalId = setInterval(() => {
                        if (this.timeRemaining > 0) {
                            this.timeRemaining -= 1;
                        } else {
                            this.resetAudio();
                        }
                    }, 1000);
                },
                pauseAudio() {
                    this.audio.pause();
                    this.isPlaying = false;
                    clearInterval(this.intervalId);
                },
                resetAudio() {
                    this.pauseAudio();
                    this.timeRemaining = this.duration;
                    this.isPlaying = false;
                },
                checkAnswerAudio(currentAnswer) {
                    const str = currentAnswer.value;

                    if (typeof currentAnswer.value !== 'string') return 0;

                    const regexMp3 = /{! (.+?\.mp3) !}/;
                    const regexImg = /<p><img[^>]*src="([^"]+)"[^>]*><\/p>/;
                    const regexMp3Option = /([A-Za-z0-9/-]+\.mp3)/;

                    const matchMp3 = str.match(regexMp3);
                    const imgTagMatch = str.match(regexImg);
                    const matchMp3Option = str.match(regexMp3Option);

                    const imgTag = imgTagMatch ? imgTagMatch[0] : null;

                    if (matchMp3 && imgTag != null) {
                        return 1; // có cả ảnh và audio
                    } else if (matchMp3 && imgTag == null) {
                        return 2; // có audio nhưng không có ảnh
                    } else if (matchMp3Option) {
                        return 3; // audio đáp án
                    } else if (!matchMp3 && imgTag) {
                        return 4; // có ảnh nhưng không có audio
                    } else {
                        return 0; // không có audio
                    }
                },
                getTagImg(currentAnswer) {
                    const str = currentAnswer.value;

                    const regexImg = /<p><img[^>]*src="([^"]+)"[^>]*><\/p>/;

                    // Kiểm tra và lấy toàn bộ thẻ <p><img ... /></p>
                    const imgTagMatch = str.match(regexImg);
                    return imgTagMatch ? imgTagMatch[0] : null;
                },
                getLinkMp3(currentAnswer) {
                    const str = currentAnswer.value;

                    const regexMp3 = /{! (.+?\.mp3) !}/;
                    const regexMp3Option = /([A-Za-z0-9/-]+\.mp3)/;

                    const match = str.match(regexMp3);
                    const matchOption = str.match(regexMp3Option)

                    let mp3Path = null;
                    if (match != null) mp3Path = match[1];
                    if (matchOption != null) mp3Path = matchOption[1];

                    return mp3Path;
                },
                selectOption(answer) {
                    if (this.statusAnswer === 'next') return;
                    console.log(`answer: `, answer);
                    if (this.checkAnswerAudio(answer) == 3) {
                        this.toggleAudio(answer);
                    } else {
                        if (this.isCheckAnswer) return;
                    }

                    let id = answer.id
                    let allSpanOption = $('.span-option')
                    let optionSelected = $(`#span-option-${id}`);

                    allSpanOption.removeClass('border-[#B2EEFA]')
                    optionSelected.addClass('border-[#B2EEFA]')

                    let checkButton = document.getElementById("checkButton");
                    checkButton.disabled = false;
                    this.statusAnswer = 'test'
                },
                enableCheckButton(selectedOption) {
                    console.log(`vao day 123123123`)
                    if (this.isCheckAnswer) return;
                    console.log(`thay doi 123`)

                    let id = selectedOption.id
                    let allSpanOption = $('.span-option')
                    let optionSelected = $(`#span-option-${id}`);

                    allSpanOption.removeClass('border-[#B2EEFA]')
                    optionSelected.addClass('border-[#B2EEFA]')

                    let checkButton = document.getElementById("checkButton");
                    checkButton.disabled = false;
                    checkButton.classList.remove("bg-gray-300");
                    checkButton.classList.remove('text-[#B3B3B3]')
                    checkButton.classList.add("drop-shadow-2xl");
                    checkButton.classList.add('text-[#07403F]')
                    $("#checkButton").css('background-color', '#57D061');
                },
                setupButtonSuggest() {
                    let button_suggest = $("#button_suggest");
                    let type_question = $("#type_question");

                    type_question.empty();
                    if (currentAnswer.type === 13) {
                        type_question.text("Điền từ vào chỗ trống")
                    } else if (currentAnswer.type === 3) {
                        type_question.text("Chọn đáp án đúng")
                    }

                    if ((currentAnswer.type === 13 && currentAnswer.value.suggest != null) || (currentAnswer.type === 3 && currentAnswer.suggest != null)) {
                        button_suggest.removeClass('hidden')
                        $("#content_suggest").html(currentAnswer.type === 13 ? currentAnswer.value.suggest : currentAnswer.suggest)
                    } else {
                        button_suggest.addClass('hidden')
                    }
                },
                showExplanationPopup() {
                    this.setupStyleExplanationPopup();
                    // Hiển thị overlay và popup
                    document.getElementById("popupOverlay").classList.remove("hidden");
                    document.getElementById("explanationPopup").classList.remove("hidden");
                    // Xóa translate-y-full để trượt popup lên
                    setTimeout(() => {
                        document.getElementById("explanationPopup").classList.remove("translate-y-full");
                    }, 10);
                },
                checkAnswer() {
                    let currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
                    if (currentIndex >= 0 && currentIndex < this.dataLesson.component.length - 1) {
                        this.statusAnswer = 'next'
                    } else {
                        this.statusAnswer = 'end'
                    }
                    let currentAnswer = this.dataLesson.component.find(t => t.id === this.idCurrentAnswer);
                    console.log("currentAnswer: ", currentAnswer)
                    if (currentAnswer.type === 3) {
                        console.log(33333333)
                        this.checkAnswerMultiChoice(currentAnswer);
                    } else if (currentAnswer.type === 13) {
                        console.log(131313131313)
                        this.checkAnswerFillIn();
                    }
                    this.showExplainButton(currentAnswer);
                },
                showExplainButton(currentAnswer) {
                    console.log(`currentAnswer showExplainButton: `, currentAnswer)
                    console.log((currentAnswer.type === 13 && currentAnswer.value.explain != null))
                    console.log((currentAnswer.type === 3 && currentAnswer.explain != null))
                    if ((currentAnswer.type === 13 && currentAnswer.value.explain != null) || (currentAnswer.type === 3 && currentAnswer.explain != null)) {
                        this.isShowExplainButton = true;
                    } else {
                        this.isShowExplainButton = false
                    }
                    console.log('this.isShowExplainButton: ', this.isShowExplainButton)
                },
                nextQuestion() {
                    console.log(`vao day`);
                    // Tìm index của câu hỏi hiện tại
                    let currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
                    $("#progress_bar").css('width', `${(currentIndex + 1) * 100 / this.dataLesson.component.length}%`);
                    $("#progress_text").text(`${currentIndex + 1}/${this.dataLesson.component.length}`)

                    // Kiểm tra xem có phần tử tiếp theo không
                    if (currentIndex >= 0 && currentIndex < this.dataLesson.component.length - 1) {
                        $(`#wrap-question-${this.idCurrentAnswer}`).addClass('hidden')
                        this.idCurrentAnswer = this.dataLesson.component[currentIndex + 1].id;
                        this.currentAnswer = this.dataLesson.component[currentIndex + 1];
                        $(`#wrap-question-${this.idCurrentAnswer}`).removeClass('hidden')
                        this.resetCheckButton();
                        this.setupButtonSuggest();
                        if (this.checkAnswerAudio(this.currentAnswer) === 2 || this.checkAnswerAudio(this.currentAnswer) === 1) {
                            this.toggleAudio(this.currentAnswer)
                        }
                    } else {
                        console.log("Không có câu hỏi tiếp theo.", save_result);
                    }
                    this.stausAnswer = 'doing';
                },
                saveResultExerciseUser() {
                    let currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
                    $("#progress_bar").css('width', `${(currentIndex + 1) * 100 / this.dataLesson.component.length}%`);
                    $("#progress_text").text(`${currentIndex + 1}/${this.dataLesson.component.length}`)

                    let save_result = apiLessonExercise.post('/list-course/save-result-exercise-user', this.userResult).then(res => {
                        console.log(`res: `, res);
                        if (res.data.error_code === 0) {
                            this.$message.success(res.data.msg);
                            this.statusAnswer = 'result';
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    })
                    console.log("Không có câu hỏi tiếp theo.", save_result);
                },
                actionCheckButton() {
                    console.log(`this.statusAnswer actionCheckButton`, this.statusAnswer)
                    if (this.statusAnswer === 'test') {
                        console.log('vao kiemr tra')
                        this.checkAnswer();
                    } else if (this.statusAnswer === 'next') {
                        console.log('-------------------------------')
                        this.nextQuestion();
                    } else if (this.statusAnswer === 'end') {
                        console.log("Không có câu hỏi tiếp theo.");
                        this.saveResultExerciseUser();
                    } else {
                        return;
                    }

                    console.log('statusAnswer isShowExplainButton', this.statusAnswer, this.isShowExplainButton)
                },
                checkInput(event) {
                    const dataId = event.target.getAttribute('data-id');
                    const checkButton = document.getElementById("checkButton");

                    $(`#${event.target.id}`).css('width', `${Math.max(73, event.target.value.length * 10)}px`) // 10 là tỉ lệ có thể thay đổi theo font-size

                    let valueMissingWordArray = $(`input[data-id="${dataId}"]`).map(function () {
                        return this.value;
                    }).get();

                    // Kiểm tra nếu có giá trị trong ô điền từ
                    if (!valueMissingWordArray.includes("")) {
                        this.statusAnswer = 'test'
                        checkButton.disabled = false;
                    } else {
                        this.statusAnswer = 'doing'
                        checkButton.disabled = true;
                    }
                },
                resetCheckButton() {
                    const checkButton = document.getElementById("checkButton");
                    this.statusAnswer = 'doing'
                    checkButton.disabled = true;
                    this.isCheckAnswer = false;
                    this.isAnswerSuccess = true;
                },
                setupStyleExplanationPopup() {
                    $("#content_explain").html(this.currentAnswer.type === 13 ? this.currentAnswer.value.explain : this.currentAnswer.explain);
                    console.log('this.currentAnswer setupStyleExplanationPopup:', this.currentAnswer)
                    if (this.currentAnswer.value.audio != null && this.currentAnswer.value.audio !== "") {
                        this.isShowExplainIconAudio = true
                        $("#audio").attr('src', `/cdn/audio/${this.currentAnswer.value.audio}`)
                    } else {
                        this.isShowExplainIconAudio = false
                    }
                },
                hideExplanationPopup() {
                    document.getElementById("explanationPopup").classList.add("translate-y-full");
                    setTimeout(() => {
                        document.getElementById("popupOverlay").classList.add("hidden");
                        document.getElementById("explanationPopup").classList.add("hidden");
                    }, 300);
                },
                checkAnswerMultiChoice(currentAnswer) {
                    let options = $(`.span-option[data-question="${currentAnswer.id}"]`)
                    console.log(`options: `, options)
                    let userResult = this.userResult.data
                    let isAnswerSuccess = true
                    options.each(function () {
                        if ($(this).prev().is(':checked')) {
                            userResult = {...userResult, [currentAnswer.id]: $(this).data('answer')}
                        }

                        if (parseInt($(this).data('result')) !== 0) {
                            $(this).removeClass('border-[#B2EEFA]')
                            $(this).removeClass('border-[#F4F5FA]')
                            $(this).addClass("border-[#57D061] text-black bg-[#95FF99]")
                        } else if ($(this).prev().is(':checked')) {
                            isAnswerSuccess = false;
                            $(this).removeClass('border-[#F4F5FA]')
                            $(this).addClass("border-[#FF7C79] text-black bg-[#FDD3D0]")
                        }
                    })
                    this.isAnswerSuccess = isAnswerSuccess;
                    if (this.isAnswerSuccess) {
                        this.userResult.grade += parseInt(currentAnswer.grade)
                        this.userResult.correct_answer += 1;
                    }
                    this.userResult.data = userResult
                    console.log('this.userResult checkAnswerMultiChoice:', this.userResult)
                },
                checkAnswerFillIn() {
                    let isAnswerSuccess = true
                    let inputMissingWord = $(`.missingWord[data-id="${this.idCurrentAnswer}"]`);
                    let dataResult = []
                    inputMissingWord.each(function () {
                        $(this).removeClass('bg-[#D9D9D9]')
                        dataResult.push($(this).val().trim())
                        if ($(this).data('result').toLowerCase() === $(this).val().trim().toLowerCase()) {
                            // this.isAnswerSuccess = true
                            $(this).css({
                                "border": "3px solid #57D061",
                                "background": "#95FF99"
                            })
                        } else {
                            console.log(`co vao day khong`)
                            isAnswerSuccess = false
                            $(this).css({
                                "border": "3px solid #FF7C79",
                                "background": "#FDD3D0"
                            });
                        }
                    });
                    this.isAnswerSuccess = isAnswerSuccess;
                    if (this.isAnswerSuccess && parseInt(this.currentAnswer.grade) > 0) {
                        this.userResult.grade += parseInt(this.currentAnswer.grade)
                        this.userResult.correct_answer += 1;
                    }
                    this.userResult.data = {...this.userResult.data, [this.idCurrentAnswer]: dataResult}
                    console.log('this.isAnswerSuccess: checkAnswerFillIn', this.isAnswerSuccess)
                    console.log('this.userResult checkAnswerFillIn:', this.userResult)
                },
                handleClose(done) {
                    this.$confirm('Are you sure to close this dialog?')
                        .then(_ => {
                            done();
                        })
                        .catch(_ => {
                        });
                },
                renderStatusOptionResult(answer) {
                    if (answer.user_choice === 0 && answer.grade === '0') {
                        return 'bg-white border-[F4F5FA]'
                    } else if (answer.grade !== '0') {
                        return 'bg-[#95FF99] border-[#57D061]'
                    } else if (answer.user_choice === 1 && answer.grade === '0') {
                        return 'bg-[#FDD3D0] border-[#FF7C79]'
                    }
                },
                renderStatusFillInBlankResult(element, keyElement, questionId) {
                    if (element.type === 'question' && element.user_result !== undefined) {
                        this.$nextTick(() => {
                            console.log(`#missingWord-${keyElement}-${questionId}`);
                            const input = $(`#missingWord-${keyElement}-${questionId}`);
                            console.log(input.val());
                            input.css('width', `${Math.max(73, element.user_result.length * 13)}px`);
                        });

                        if (element.result.toLowerCase() === element.user_result.toLowerCase()) {
                            return `border-[3px] border-[#57D061] bg-[#95FF99]`;
                        } else {
                            return `border-[3px] border-[#FF7C79] bg-[#FDD3D0]`;
                        }
                    }
                    return '';
                }
            },
            computed: {
                formattedTime() {
                    const minutes = Math.floor(this.timeRemaining / 60);
                    const seconds = this.timeRemaining % 60;
                    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                }
            },
            mounted() {
            },
            beforeDestroy() {
                // Clear interval khi component bị huỷ
                if (this.intervalId) {
                    clearInterval(this.intervalId);
                }
                if (this.audio) {
                    this.audio.removeEventListener("ended", this.resetAudio);
                }
            },
        })

        function playAudio() {
            const audio = document.getElementById("audio");
            audio.play();
        }
    </script>
@stop

<style>
    .custom-dialog-user-result .el-dialog__body .custom-scrollbar {
        max-height: 75vh;
        overflow-y: auto;
    }

    /* Style thanh cuộn */
    .custom-dialog-user-result .custom-scrollbar {
        /*scrollbar-width: thin;*/
        /*scrollbar-color: #57D061 #F4F5FA;*/
    }

    .custom-dialog-user-result .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
        background-color: #D9D9D9;
        border-radius: 10px;
    }

    .custom-dialog-user-result .custom-scrollbar::-webkit-scrollbar-track {
        border-radius: 6px;
        background-color: #D9D9D9;
    }

    .custom-dialog-user-result .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #57D061;
        border-radius: 10px;
    }

    .custom-dialog-user-result .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #45B052;
    }

    /* Header tùy chỉnh */
    .custom-dialog-user-result .custom-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 18px;
        padding: 10px;
        border-bottom: 1px solid #eaeaea;
    }

    .custom-dialog-user-result .custom-title {
        font-weight: bold;
    }



    .custom-dialog-user-result .el-dialog__header, .custom-dialog-user-result .el-dialog__body {
        padding: 0;
        background: #F4F5FA;
    }
</style>
