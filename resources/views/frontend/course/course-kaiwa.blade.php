@extends('frontend._layouts.default')

@section('title')
  @if($course->name == 'N5')
    Tiếng Nh<PERSON>t cho người mới bắt đầu - <PERSON>h<PERSON>a học N5 - Dungmori
  @else
    Họ<PERSON> tiếng Nh<PERSON>t online - <PERSON><PERSON><PERSON><PERSON> họ<PERSON> {{ $course->name }}  - Dungmori
  @endif
@stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber dạy tiếng Nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('cdn/course/default')}}/{{ $course->avatar_name }} @stop
@section('author') DUNGMORI @stop

@section('header-js')
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
@stop

@section('content')

<script>
    @if($course->name == 'N5')
        $(".cn5").addClass("active");
    @elseif($course->name == 'N4')
        $(".cn4").addClass("active");
    @elseif($course->name == 'N3')
        $(".cn3").addClass("active");
    @elseif($course->name == 'N2')
        $(".cn2").addClass("active");
    @elseif($course->name == 'N1')
        $(".cn1").addClass("active");
    @elseif($course->name == 'EJU')
        $(".eju").addClass("active");
    @elseif($course->name == 'Kaiwa')
        $(".kaiwa").addClass("active");
    @endif
</script>

<div id="fb-root"></div>

<div class="main">

<div class="main-center main-course">
    <div class="main-left">
        <h2 class="course-detail-title">Khóa học <a><b>giao tiếp DũngMori KAIWA</b></a></h2>
        {{-- nếu là 1 trong 2 khóa kaiwa --}}
        @if(in_array($course->id, array(21, 25, 26, 34, 35)))
            <script type="text/javascript">$(".kaiwa").addClass("active")</script>
            <div class="kaiwa-top-menu" style="margin-top: 20px;">
                <a href="{{url('khoa-hoc/kaiwa-so-cap')}}" class="kaiwa-course-item @if($course->id == 25) active @endif">
                    @if($course->id == 25) <img src="{{url('assets/img/new_course/kaiwa-so-cap-active.png')}}??"/> @else <img src="{{url('assets/img/new_course/kaiwa-so-cap-inactive.png')}}??"/> @endif
                    <span>Kaiwa Sơ Cấp</span>
                </a>
                <a href="{{url('khoa-hoc/kaiwa-trung-cap-1')}}" class="kaiwa-course-item @if($course->id == 34) active @endif">
                    @if($course->id == 34) <img src="{{url('assets/img/new_course/tc1.png')}}??"/> @else <img src="{{url('assets/img/new_course/tc11.png')}}??"/> @endif
                    <span>Kaiwa Trung Cấp</span>
                </a>
            </div>
            <style type="text/css">
                .kaiwa-course-item > img{height: 50px;}
                .server-localtion{background-color: #cd882e !important;}
                .course-tab{background: #ffc16b !important;}
                .course-tab > .active > a{ background-color: #cd882d !important; }
                .course-tab > .li-tab > a{ color: #fff !important; }
                .course-list-container > .block-title{background: linear-gradient(180deg, #d6902e 0%, #dc972d 48.44%, #d6902e 100%);
                    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);}
                .course-list-container .panel-default > .panel-heading a {background: #cd882d !important;}
                .course-list-container .panel-default > .panel-heading > .group-step-item{background: rgb(238, 238, 238) !important;}
                .course-list-container .panel-default .panel-body li a {background: #fdb82d !important;}
                .course-detail-title a {color: #d6902e}
                .comment-tab { border-bottom: 1px solid #cd882d !important; }
                .comment-tab .active > a {background-color: #CD882D !important;}
                .list-comments .comment-item .comment-content .name b {color: #111 !important; }
                .comment-action > a{color: #CD882D !important;}
                .child-comment-item .comment-content .child-name b {color: #111 !important; }
                .list-comments .input-comment-container .post-comment-btn {background: #CD882D !important;}
                .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {background: #CD882D !important;}
                .list-comments .load-more-comment {background: #CD882D !important;}
                .main-right{margin-top: 117px !important;}
                .main .main-course .main-right .course-info-container {margin-top: 35px;}
            </style>
        @endif
        <div class="btn-list-lesson-box">
            <button class="btn btn-list-lesson" id="btn-list-lesson" onclick="goToLessonListNow();">
                Bắt đầu học&nbsp;&nbsp;<i class="zmdi zmdi-format-line-spacing"></i>
            </button>
        </div>

        <div class="cover-container">
            <div class="movie-play">
                <img src="{{url('cdn/course/default')}}/{{ $course->avatar_name }}"/>
                <br>
                @include('frontend.course.play-button')
            </div>
            <?php preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $course->link, $match); ?>
            <iframe id="iframe-youtube" style="display: none;" width="100%" height="395" frameborder="0" gesture="media" allow="encrypted-media" allowfullscreen
            src="https://www.youtube.com/embed/{{ $match[1] }}?rel=0"></iframe>

            <div class="server-localtion-container">
                {{-- <span class="server-localtion active"># Máy chủ youtube</span> --}}
            </div>

            <div class="course-info-container course-info-status-mobile">

                @if($unlock == 0 && $course->price != 0)
                  <div class="buy-item">
                    <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                      <div class="buy-btn">Mua khóa học này</div>
                    </a>
                  </div>
                @elseif($unlock == 0 && $course->price == 0)
                  <div class="buy-item">
                    <div class="buy-btn bought">Miễn phí</div>
                  </div>
                @else
                  <div class="buy-item">
                    <div class="buy-btn bought">Bạn đã mua <i class="zmdi zmdi-check-circle"></i></div>
                  </div>
                @endif

            </div>
            <div class="msg-fb-box">
                @includeIf('frontend.course.components.fb_btn_message')
            </div>
          <script type="text/javascript">

            {{-- sự kiện click vào chạy video youtube --}}
            $('.movie-play').on('click', function(ev) {
                $(".movie-play").css("display", "none");
                $("#iframe-youtube").css("display", "block");
                $("#iframe-youtube")[0].src += "&autoplay=1";
                ev.preventDefault();
            });

          </script>
        </div>

        <ul class="nav nav-pills course-tab">
            <li class="li-tab intro-tab active"><a data-toggle="pill" href="#intro-content">Giới thiệu khóa học</a></li>
        </ul>

        <div class="tab-content">
            <div id="intro-content" class="tab-pane fade in active">
                <div class="course-detail-container">
                    <div class="course-price-container">
                        <div class="info">Học phí: <b>{{ number_format($course->price) }} </b> ₫ ( {{ number_format($courseJpPrice) }} ¥ ) </div>
                        <div class="info">Thời gian: @if ($course->name == "N5") Miễn phí vô thời hạn @else {!! json_decode($course->stats_data)->time !!}
                        tháng kể từ ngày kích hoạt @endif</div>
                        <div class="info">Khóa học bao gồm <b> {!! json_decode($course->stats_data)->lesson !!}</b> bài học với
                            <b>{!! json_decode($course->stats_data)->video !!}</b> videos bài giảng</div>
                        <div class="info">Giảng viên: {{ $course->getAuthorName() }}</div>
                        <div class="info">Mô tả: {!! $course->brief !!}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="comment-container" id="comment-container">
            <ul class="nav nav-pills comment-tab">
                <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a></li>
                <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng facebook</a></li>
            </ul>
            <div class="tab-content">
                <div id="user-comment-content" class="tab-pane fade in active">

                    @if(Auth::check())
                    <comments meid="{{ Auth::user()->id }}"
                            avatar="{{ Auth::user()->avatar }}"
                            tbid="{{ $course->id }}"
                            tbname="course"
                            num-posts="15"
                            background="#fff"
                            ref="comment">
                    </comments>
                    @else
                    <comments tbid="{{ $course->id }}"
                            tbname="course"
                            num-posts="15"
                            background="#fff"
                            ref="comment">
                    </comments>
                    @endif

                </div>
                <div id="facebook-comment-content" class="tab-pane fade">
                    <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
                </div>
            </div>
        </div>

    </div>
    <div class="main-right">
        <div class="course-info-container course-info-status-pc">

            @if($unlock == 0 && $course->price != 0)
              <div class="buy-item">
                <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                  <div class="buy-btn">Mua khóa học này</div>
                </a>
              </div>
            @elseif($unlock == 0 && $course->price == 0)
              <div class="buy-item">
                <div class="buy-btn bought">Miễn phí</div>
              </div>
            @else
              <div class="buy-item">
                <div class="buy-btn bought">Bạn đã mua <i class="zmdi zmdi-check-circle"></i></div>
              </div>
            @endif
        </div>

        {{-- Hien thi so ngay het han --}}
        <div class="numberOfDay">
            @if($numberOfDay == -1)
              <div>Khóa học đã hết hạn</div>
            @elseif($numberOfDay == 0)
              <div>Khóa học chỉ còn hạn trong hôm nay</div>
            @elseif($numberOfDay > 0)
              <div style="text-transform: uppercase;">Hết hạn: còn {{$numberOfDay}} ngày</div>
            @endif
        </div>

        <a href="{{url('/bang-gia')}}">
            <div class="see-more">Xem thêm các khóa học khác <i class="zmdi zmdi-long-arrow-right"></i></div>
        </a>

        <div class="course-list-container" id="course-list-pc">
          <div class="block-title" id="lesson-list-detail"> Tiến trình học </div>
          <div class="store-block" style="text-align: center; padding: 10px 0 50px 0; width: 100%; float: left; background: #EEE;">
                <h2 style="font-size: 16px;">Vui lòng tải ứng dụng để học</h2>
                <a href="https://itunes.apple.com/us/app/id1486123836">
                    <img src="{{url('/assets/img/dungmori_ios.png')}}" style="width: 180px; margin: 20px 0 15px 0" >
                </a>
                <a href="https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp">
                    <img src="{{url('/assets/img/dungmori_android.png')}}" style="width: 180px;">
                </a>
          </div>
        </div>
    </div>
</div>
</div>

<script type="text/javascript">
    $(".fancybox").fancybox().trigger('click');
</script>

@section('fixed-panel')



@stop

@section('footer-js')

<script type="text/javascript"> new Vue({ el: '#course-list-pc' }); </script>
<script type="text/javascript"> new Vue({ el: '#comment-container' }); </script>

<script>
  function goToLessonListNow(id){
    $('html, body').animate({
      scrollTop: $("#lesson-list-detail").offset().top-50
    }, 1100);
  }
  $( document ).ready(function() {
    function changeColorBtnListCourse() {
      var btn = $('#btn-list-lesson');
      if (btn.css('borderColor') == "rgb(255, 0, 0)") {
        btn.css('color', 'rgb(0, 0, 255)');
        btn.css('border-color', 'rgb(0, 0, 255)');
      } else if (btn.css('borderColor') == "rgb(0, 0, 255)") {
        btn.css('color', 'rgb(94, 152, 0)');
        btn.css('border-color', 'rgb(94, 152, 0)');
      } else if (btn.css('borderColor') == "rgb(94, 152, 0)") {
        btn.css('color', 'rgb(231, 117, 0)');
        btn.css('border-color', 'rgb(231, 117, 0)');
      } else if (btn.css('borderColor') == "rgb(231, 117, 0)") {
        btn.css('color', 'rgb(64, 0, 111)');
        btn.css('border-color', 'rgb(64, 0, 111)');
      } else {
        btn.css('color', 'rgb(255, 0, 0)');
        btn.css('border-color', 'rgb(255, 0, 0)');
      }
    }
    setInterval(changeColorBtnListCourse, 500);
  });
</script>

<script>(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) return;
  js = d.createElement(s); js.id = id;
  js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1768213996826394';
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));</script>
@stop
@stop
