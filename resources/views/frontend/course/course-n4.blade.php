@extends('frontend._layouts.default')

@section('title')
  <PERSON><PERSON><PERSON><PERSON> h<PERSON> {{ $course->name }}  - Dungmori
@stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('cdn/course/default')}}/{{ $course->avatar_name }} @stop
@section('author') DUNGMORI @stop

@section('header-js')
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
  <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
@stop
@section('content')
  <div id="fb-root"></div>
@stop
@section('fixed-panel')

  <a href="{{url('/bang-gia')}}">

  </a>
@stop
@section('footer-js')

@stop
