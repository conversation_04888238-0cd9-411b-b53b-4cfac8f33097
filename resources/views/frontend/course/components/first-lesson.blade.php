<div class="next-lesson first-lesson border border-[#F5F5F5] {{ $learningLesson->percent < 85 ? 'bg-[#FFFBEB]' : 'bg-[#F2FFF4]' }} flex items-center p-4 sp:p-2 rounded-[24px]"
     style="--data-first-lesson-color:{{ $learningLesson->percent < 85 ? '#e8b93199' : '#57D06199' }}">
    <a class="flex items-center "
            href="{{ !empty($isTutorial) ? 'javascript:void(0);' : route('frontend.lesson.lesson_basic_new', ['courseSlug' => $course->SEOurl, 'lessonId' => $learningLesson->id, 'lessonSlug' => $learningLesson->SEOurl]) }}">
        <div class="flex-none relative h-[120px] w-[192px] sp:h-[70px] sp:w-[102px] rounded-[24px] sp:rounded-[16px] overflow-hidden bg-cover bg-center"
             style="background-image: url('{{ $learningLesson->avatar_name ? url('cdn/lesson/default/' . $learningLesson->avatar_name) : asset('/images/lessons/lesson-default.png') }}')">
            <div class="h-full w-full rounded-2xl bg-black opacity-50 absolute top-0 flex justify-center items-center">
            </div>

            <div class="h-full w-full absolute top-0 flex justify-center items-center">
                <div class="cursor-pointer">
                    <div class="text-center">
                        <img class="w-[20px]" src="{{ asset('/images/icons/play-button.png') }}" alt="play button">
                    </div>
                    <div class="text-white text-base sp:hidden"> {{ $learningLesson->percent > 0 ? 'Học ngay' : 'Bắt đầu học' }}</div>
                    <div class="text-white text-xs desktop:hidden">Học ngay</div>
                </div>
            </div>
        </div>
        <div class="ml-10 sp:ml-3">
            <div class="text-[#212121]  text-base sp:text-[10px]">
                {{ $learningLesson->stage_name }}
            </div>
            <div class="flex font-averta-semibold text-xl mt-3 sp:mt-2 sp:text-[12px] text-[#073A3B]">
                @if($learningLesson->name_html)
                    {!! $learningLesson->name_html !!}
                @else
                    {{ $learningLesson->name }}
                @endif
                @if($learningLesson['require'])
                    <img
                            class="ml-[5px] max-h-[12px] relative min-w-[12px]"
                            src="{{ asset('/images/icons/require.png') }}"
                            alt="require.png"
                    />
                @endif
            </div>
        </div>
    </a>

    @if (!in_array($learningLesson->type, ['exam', 'last_exam']) && $learningLesson->percent < 85)
        <div class="relative ml-auto transform scale-x-[-1]">
            <svg class="{{ $wrapperClass ?? '' }} w-[70px] h-[70px]">
                <circle class="text-[#F5F5F5]" stroke-width="5" stroke="currentColor" fill="transparent" r="30" cx="35"
                        cy="35" />
                <circle class="{{ $learningLesson->percent < 85 ? 'text-[#E8B931]' : 'text-[#57D061]' }}" stroke-width="5"
                        stroke-dasharray="188.4" stroke-dashoffset="{{ ((100 - ($learningLesson->percent < 85 ? $learningLesson->percent : 100)) * 188.4) / 100 }}" stroke-linecap="round"
                        stroke="currentColor" fill="transparent" transform="rotate(-90 35 35)" r="30" cx="35"
                        cy="35" />
            </svg>
            <div class="absolute inset-0 flex items-center justify-center transform scale-x-[-1]">
                <span class="text-[#414348] text-base font-averta-semibold">{{ $learningLesson->percent < 85 ? $learningLesson->percent : 100 }}%</span>
            </div>
        </div>
    @elseif($learningLesson->percent >= 85)
        <div class="relative ml-auto transform scale-x-[-1]">
            <svg class="{{ $wrapperClass ?? '' }} w-[70px] h-[70px]">
                <circle class="text-[#F5F5F5]" stroke-width="5" stroke="currentColor" fill="transparent" r="30" cx="35"
                        cy="35" />
                <circle class="{{ $learningLesson->percent < 85 ? 'text-[#E8B931]' : 'text-[#57D061]' }}" stroke-width="5"
                        stroke-dasharray="188.4" stroke-dashoffset="{{ ((100 - ($learningLesson->percent < 85 ? $learningLesson->percent : 100)) * 188.4) / 100 }}" stroke-linecap="round"
                        stroke="currentColor" fill="transparent" transform="rotate(-90 35 35)" r="30" cx="35"
                        cy="35" />
            </svg>
            <div class="absolute inset-0 flex items-center justify-center transform scale-x-[-1]">
                <span class="text-[#414348] text-base font-averta-semibold">{{ $learningLesson->percent < 85 ? $learningLesson->percent : 100 }}%</span>
            </div>
        </div>
    @endif

</div>
