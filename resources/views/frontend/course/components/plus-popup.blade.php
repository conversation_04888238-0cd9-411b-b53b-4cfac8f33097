@if($product->extra_price > 0)
    <div class="plus-intro" data-fancybox data-animation-duration="300" data-src="#plus-info{{$product->id}}">
        <h4><strong>+</strong> <PERSON><PERSON> kèm <strong><PERSON><PERSON><PERSON> {{$product->name}} PLUS</strong> để nhận thêm nhiều ưu đãi.</h4>
        <div class="dropdown plus-info" id="plus-info{{$product->id}}">
            <div class="title">
                <h4><strong>+</strong> <PERSON><PERSON> kèm <strong>Gói <span>{{$product->name}} PLUS</span></strong> để nhận thêm nhiều ưu đãi.</h4>
            </div>
            <div class="content">
                <p>+ Học phí: <strong id="plus-vnd" style="color: #F2994A;">{{ number_format($product->extra_price) }} đ</strong>
{{--                    (<span id="plus-jp"> {{ number_format($product->extra_jp_price) }} </span> ¥)--}}
                </p>
                <div id="plus-des">
                    {!! $product->extra_desc !!}
                </div>
            </div>
            <div class="end">
                @if(isset($type) && $type == 'combo')
                    <a href="{{url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($product->id)}}&plus=1"><div class="btn plus-buy">Mua ngay</div></a>
                @else
                    <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($product->getCourseId()) }}&plus=1" class="btn plus-buy">Mua ngay</a>
                @endif
            </div>
        </div>
    </div>
@endif
