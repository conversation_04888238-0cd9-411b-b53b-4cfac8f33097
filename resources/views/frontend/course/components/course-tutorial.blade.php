<script>
    $(document).ready(function() {
        var checkTutCourse = localStorage.getItem('SHOW_NEW_BASIC_COURSE_TUT');
        if (checkTutCourse != 1 && checkTutCourse != '1') {
            setTimeout(() => {
                localStorage.setItem('SHOW_NEW_BASIC_COURSE_TUT', 1);
                $(".tutorial").show();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                disableScroll();
            }, 1000);
        }
    })
</script>
<div class="tutorial hidden fixed w-screen h-full top-0 left-0 z-[9999]">
    <div class="course-step step-1 active" data-step="1">
        <div class="w-full h-full absolute top-[85px] sp:top-[75px] left-0 z-10">
            <div class="container max-w-[1150px] mx-auto font-averta-regular">
                <div class="grid grid-cols-3 sp:grid-cols-1">
                    <div class="col-span-2 left-col">
{{--                        {{ dd($learningLesson) }}--}}
{{--                        @if (!empty($learningLesson->video))--}}
                            <div class="bg-white desktop:p-[25px] rounded-[24px]">
                                <div class="bg-cover bg-center bg-no-repeat w-full h-[413px] sp:h-[250px] flex items-center justify-center"
                                     style="background-image: url('{{ url('images/course/poster-intro-n4-n5.jpg') }}')">
                                    <img class="w-[120px] sp:w-[80px]"
                                        src="{{ asset('images/icons/play-button2.png') }}" alt="play-button">
                                </div>
                            </div>
{{--                        @endif--}}
                        <div class="text-white text-base flex items-center justify-center mt-5 sp:hidden">
                            <span class="font-averta-regular">Click để tiếp tục </span>
                            <img class="w-[40x] h-[40px] ml-3" src="{{ asset('images/icons/click.png') }}"
                                alt="click.png">
                        </div>
                        <div class="flex justify-center sp:mt-3">
                            <div
                                class="relative bg-contain bg-center bg-no-repeat h-[83px] sp:h-[63px] w-[250px] cursor-pointer bg-[url('/images/icons/union.png')] sp:bg-[url('/images/icons/union-sp.png')]">
                                <span
                                    class="text-xl sp:text-sm text-white font-beanbag whitespace-nowrap z-10 absolute left-1/2 -translate-x-1/2 top-[calc(50%-5px)] sp:top-[calc(50%-7px)] -tranlsate-y-1/2">
                                    Hướng dẫn học bài
                                </span>
                            </div>
                        </div>

                        <div class="text-white text-base text-center mt-10 desktop:hidden">
                            <img class="w-[80px]" src="{{ asset('images/icons/tap.png') }}" alt="click.png">
                            <div class="font-averta-regular">Chạm để tiếp tục</div>
                        </div>
                    </div>
                    <div class="right-col pl-6">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="course-step step-2" data-step="2">
        <div class="w-full h-full absolute top-[370px] sp:top-[200px] left-0 z-10">
            <div class="container max-w-[1150px] mx-auto font-averta-regular">
                <div class="grid grid-cols-3 sp:grid-cols-1">
                    <div class="col-span-2 left-col">
                        <div class="text-white text-base flex items-center justify-center mt-5 sp:hidden">
                            <span class="font-averta-regular">Click để tiếp tục </span>
                            <img class="w-[40x] h-[40px] ml-3" src="{{ asset('images/icons/click.png') }}"
                                alt="click.png">
                        </div>
                        <div class="text-white text-base text-center mt-10 desktop:hidden">
                            <img class="w-[80px]" src="{{ asset('images/icons/tap.png') }}" alt="click.png">
                            <div class="font-averta-regular">Chạm để tiếp tục</div>
                        </div>
                        <div
                            class="relative bg-contain bg-center bg-no-repeat mt-3 h-[83px] text-center cursor-pointer bg-[url('/images/icons/union2.png')] sp:bg-[url('/images/icons/union-sp2.png')]">
                            <span
                                class="text-xl sp:text-sm text-white font-beanbag z-10 absolute
                                left-1/2 -translate-x-1/2 top-[20%] -tranlsate-y-1/2 sp:whitespace-pre-line">{{ "Lối tắt truy cập nhanh \n vào bài học" }}</span>
                        </div>
                        <div class=" desktop:bg-white sp:px-5 desktop:p-[25px] rounded-[24px] mt-3">
                            @include('frontend.course.components.first-lesson', ['isTutorial' => true])
                        </div>
                    </div>
                    <div class="right-col pl-6">
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="course-step step-3" data-step="3">
        <div class="w-full h-full absolute top-22 left-0 z-10">
            <div class="container max-w-[1150px] mx-auto font-averta-regular">
                <div class="grid grid-cols-3 sp:grid-cols-1">
                    <div class="col-span-2 left-col sp:px-5">
                        <div class="text-white text-base flex items-center justify-center mt-5 sp:hidden">
                            <span class="font-averta-regular">Click để tiếp tục </span>
                            <img class="w-[40x] h-[40px] ml-3" src="{{ asset('images/icons/click.png') }}"
                                alt="click.png">
                        </div>
                        <div class="text-white text-base text-center mt-10 desktop:hidden">
                            <img class="w-[80px]" src="{{ asset('images/icons/tap.png') }}" alt="click.png">
                            <div class="font-averta-regular">Chạm để tiếp tục</div>
                        </div>
                        <div class="relative bg-contain bg-center bg-no-repeat mt-3 h-[76px] cursor-pointer"
                            style="background-image: url({{ asset('images/icons/union3.png') }})">
                            <span
                                class="text-xl sp:text-sm text-white font-beanbag z-10 absolute left-1/2 -translate-x-1/2 top-[20px] -tranlsate-y-1/2">
                                Danh sách bài học
                            </span>
                        </div>
                        <div class="bg-white p-[25px] rounded-[24px] mt-3">
                            @include('frontend.course.components.lesson-list', ['isTutorial' => true])
                        </div>
                    </div>
                    <div class="right-col pl-6">
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if ($isLocked)
        <div class="course-step step-4" data-step="4">
            <div class="w-full h-full absolute top-[400px] left-0 z-10">
                <div class="container max-w-[1150px] mx-auto font-averta-regular">
                    <div class="grid grid-cols-3 sp:grid-cols-1">
                        <div class="col-span-2 left-col flex justify-end">
                            <div class="w-fit relative top-[-24px]">
                                <div class="text-white text-base flex items-center justify-center mt-5">
                                    <span class="font-averta-regular">Click để tiếp tục </span>
                                    <img class="w-[40x] h-[40px] ml-3" src="{{ asset('images/icons/click.png') }}"
                                        alt="click.png">
                                </div>
                                <div class="relative bg-contain bg-center bg-no-repeat mt-3 h-[63px] w-[521px] cursor-pointer"
                                    style="background-image: url({{ asset('images/icons/union4.png') }})">
                                    <span
                                        class="text-xl text-white font-beanbag whitespace-nowrap z-10 absolute left-1/2 -translate-x-1/2 top-[calc(50%-13px)] -tranlsate-y-1/2">
                                        ƯU ĐÃI LỚN dành riêng cho bạn!!!
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="right-col flex justify-center items-end">
                            <div class="bg-white px-4 py-8 rounded-[24px]">
                                <button
                                    class="bg-[#EF6D13] shadow-md flex items-center justify-center rounded-full text-white max-w-[100%] w-[310px] py-3 text-base font-beanbag uppercase">Nhận
                                    ưu đãi
                                    <img class="ml-2 relative top-[-2px] w-14"
                                        src="{{ asset('/images/icons/sale-off.png') }}?v={{ $assetVersion }}"
                                        alt="sale-off">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class="bg-black opacity-80 w-full h-full absolute top-0 left-0">
    </div>
</div>
