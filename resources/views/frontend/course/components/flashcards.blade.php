<div class="flashcards">
    <div class="stats" style="opacity: 0;" v-show="currentTempCard != 'end' && !focusFC">
        <span class="stats__not_learned">@{{ cardStats.current_unknown }}</span>
        <span>←</span>
        <span class="stats__total">@{{ cardStats.current_total }}</span>
        <span>→</span>
        <span class="stats__learned">@{{ cardStats.current_known }}</span>
    </div>
    <div class="settings" id="settings" v-if="!focusFC">
        <span data-toggle="modal" data-target="#settingModal" class="a_cursor--pointer">
            <i class="fa fa-cog"></i> <span id="settingTitle">Tuỳ chỉnh</span>
        </span>
    </div>

    <div class="content" v-show="currentTempCard != 'end' || focusFC">
        <div id="stacked-cards-block" class="stackedcards stackedcards--animatable init a_cursor--pointer">
            <div class="stackedcards-container" v-if="flashcards">
                <div class="card-item" v-for="(card, index) in flashcards" :key="card.id">
                    <div class="card-inner">
                        <div class="card__face card__face--jp" v-if="index < currentCard + 3" v-bind:class="[userSettings.frontLang == 'jp' ? 'card__face--front' : 'card__face--back']">
                            <div class="card__word" v-bind:style="[card.value.ex ? {'padding-top': '70', 'justify-content': 'flex-start'} : {'padding-top': '0', 'justify-content': 'center'}]">
                                <div class="card__word--text" v-bind:style="[(card.value.jpSz && card.value.jpSz != '') ? {'font-size': card.value.jpSz + 'px'} : card.value.ex ? {'font-size': '3em'} : {'font-size': '100px'}]">
                                    <p class="word" v-html="card.value.jp"></p>
                                </div>
                                <div class="card__word--example" v-if="card.value.ex">
                                    <p>Ví dụ</p>
                                    <p class="example" v-html="card.value.ex"></p>
                                </div>
                            </div>
                            <div class="card__voice">
                                <span v-if="card.value.audio" class="card__voice--button noFlip btn" v-on:click="playAudio(card.value.audio)">
                                    <i class="fa fa-volume-up noFlip"></i>
                                </span>
                            </div>
                        </div>
                        <div class="card__face card__face--vi" v-if="index < currentCard + 3" v-bind:class="[userSettings.frontLang == 'vi' ? 'card__face--front' : 'card__face--back']">
                            <div class="card_meaning" v-bind:style="[card.value.img ? {'justify-content': 'space-between', 'font-size': '20px', 'line-height': '26px'} : {'justify-content': 'center', 'font-size': '45px', 'line-height': '58px'}]">
                                <img :src="url +'/cdn/flashcard/default/' + card.value.img" v-if="card.value.img"/>
                                <span v-html="card.value.vi" class="meaning" v-bind:style="[card.value.img ? {'font-size': '20px', 'line-height': '1.3'} : {'font-size': '34px', 'line-height': '1.3'}]"></span>
                            </div>

                            <div class="card-bottom">
                                <div class="card_comment--title" v-bind:style="[ card.value.ex ? {'border-top': 'none'} : {'border-top': '1px solid #e6e6e6'}]">
                                    <span v-if="!card.value.ex">Cách nhớ</span>
                                </div>

                                <div v-if="listComments.length > 0" class="card_comment--main">
                                    <div class="card_comment--avatar mr-4">
                                        <div>
                                            <img v-if="!listComments[0] || !listComments[0].avatar" class="me-avatar" :src="url + '/assets/img/default-avatar.jpg'">
                                            <img v-else :src="url + '/cdn/avatar/small/'+ listComments[0].avatar">
                                        </div>
                                    </div>
                                    <div class="card_comment--content mr-4">
                                        <span class="author">@{{listComments[0].name}}</span>
                                        <span class="time text-secondary">@{{listComments[0].time_created}}</span>
                                        <span class="comment" v-text="printFirstComment(listComments[0].content)"></span>
                                    </div>
                                    <div class="card_comment--action">
                                    <span v-show="listComments[0].pin == 1" v-bind:style="listComments[0].pin == 1 ? 'color: red': ''" >
                                        <i class="fa fa-thumb-tack"></i>
                                    </span>
                                        <span v-on:click="!likeLoading ? likeComment(listComments[0].id) : undefined">
                                        <i class="fa fa-thumbs-up noFlip a_cursor--pointer" v-bind:style="[listComments[0].liked ? {'color': '#69aa00'} : {'color': 'gray'}]"></i> @{{listComments[0].count_like || 0}}
                                    </span>
                                    </div>
                                </div>
                                <div v-if="listComments.length == 0" class="card_comment--empty">
                                    <i class="fa fa-comment noFlip a_cursor--pointer fa-lg"></i>
                                    Chưa có bình luận
                                </div>
                                <div v-show="listComments.length > 0" class="card_comment--toggle" id="card_comment--toggle" >
                                    <i class="fa fa-comment noFlip a_cursor--pointer" v-on:click="scrollToComment()"></i>
                                </div>
                            </div>

{{--                            <div v-if="listComments.length > 1" class="card_comment--main" style="margin-top: 20px;">--}}
{{--                                <div class="card_comment--avatar mr-4">--}}
{{--                                    <div>--}}
{{--                                        <img v-if="!listComments[1] || !listComments[1].avatar" class="me-avatar" :src="url + '/assets/img/default-avatar.jpg'">--}}
{{--                                        <img v-else class="me-avatar" :src="url + '/cdn/avatar/small/'+ listComments[1].avatar">--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                                <div class="card_comment--content mr-4">--}}
{{--                                    <span class="author">@{{listComments[1].name}}</span>--}}
{{--                                    <span class="time text-secondary">@{{listComments[1].time_created}}</span>--}}
{{--                                    <span class="comment" v-text="printFirstComment(listComments[1].content)"></span>--}}
{{--                                </div>--}}
{{--                                <div class="card_comment--action">--}}
{{--                                    <span v-show="listComments[1].pin == 1" v-bind:style="listComments[1].pin == 1 ? 'color: red': ''" >--}}
{{--                                        <i class="fa fa-thumb-tack"></i>--}}
{{--                                    </span>--}}
{{--                                    <span v-on:click="!likeLoading ? likeComment(listComments[1].id) : undefined">--}}
{{--                                        <i class="fa fa-thumbs-up noFlip a_cursor--pointer" v-bind:style="[listComments[0].liked ? {'color': '#69aa00'} : {'color': 'gray'}]"></i> @{{listComments[1].count_like || 0}}--}}
{{--                                    </span>--}}
{{--                                </div>--}}
{{--                            </div>--}}

                        </div>
                    </div>
                </div>
            </div>
            <div class="stackedcards--animatable stackedcards-overlay left">Chưa thuộc</div>
            <div class="stackedcards--animatable stackedcards-overlay right">Đã thuộc</div>

        </div>
        <div class="global-actions" v-if="!focusFC">
            <span class="left-action flex justify-center items-center a-cursor-pointer" v-bind:style="[swipeLoading ? {'background-color': 'gray'} : {'background-color': '#5cb85c'}]" :disabled="swipeLoading" v-on:click="swipeCard('unknown')">Chưa thuộc </span>
            <span class="right-action flex justify-center items-center a-cursor-pointer" v-bind:style="[swipeLoading ? {'background-color': 'gray'} : {'background-color': '#5cb85c'}]" :disabled="swipeLoading" v-on:click="swipeCard('known')">Đã thuộc</span>
        </div>
        <div class="undo-action" v-if="!focusFC">
            <span class="undo-action flex justify-center items-center a-cursor-pointer" v-on:click="undo()">
                <i class="fa fa-undo" aria-hidden="true"></i>
            </span>
        </div>
    </div>
    <div class="final-state" v-if="currentTempCard == 'end' && !focusFC">
        <div v-if="flashcards.length==0" class="text-center">
            <p>Không có thẻ cho mục này</p>
        </div>
        <div class="btn btn-success loadCards" v-on:click="cardStats.unknown > 0 ? fetchCards('unknown', true) : undefined" v-if="showComment"
             :class="{'disabled': cardStats.unknown == 0}"
        >Từ vựng chưa thuộc <span class="badge badge-pill badge-light">@{{ cardStats.unknown }}</span></div>
        <div class="btn btn-success loadCards" v-on:click="cardStats.known > 0 ? fetchCards('known', true) : undefined" v-if="showComment"
             :class="{'disabled': cardStats.known == 0}"
        >Từ vựng đã thuộc <span class="badge badge-pill badge-light">@{{ cardStats.known }}</span></div>
        <div class="btn btn-success loadCards" v-on:click="cardStats.total > 0 ? fetchCards('all', true) : undefined"
             :class="{'disabled': cardStats.total == 0}"
        >@{{ showComment ? 'Tất cả từ vựng' : 'Học lại' }} <span class="badge badge-pill badge-light">@{{ cardStats.total }}</span></div>
    </div>
    <div class="modal fade" id="settingModal" role="dialog">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title text-center fl-setting-header-title">TÙY CHỈNH</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="setting">
                        <span>Xáo trộn thẻ</span>
                        <label class="switch">
                            <input type="checkbox" name="isShuffle" v-model="userSettings.isShuffle" v-on:change="onChangeCheckboxSetting($event)">
                            <span class="slider round"></span>
                        </label>
                    </div>
                    <div class="setting">
                        <span>Mặt trước thẻ</span>
                        <div class="form-group">
                            <select class="form-control" name="frontLang" v-model="userSettings.frontLang" v-on:change="onChangeSelectSetting($event)">
                                <option value="vi">Tiếng Việt</option>
                                <option value="jp">Tiếng Nhật</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- comments cho riêng flashcards --}}
    <div class="list-comments" id="list-comments" v-if="showComment">

        <div class="input-comment-container" v-if="meid != null">
            <p><b>Góp ý cách học</b></p>
            <div class="form_action">
                <img v-if="avatar == null || avatar == ''" class="me-avatar" :src="url + '/assets/img/default-avatar.jpg'">
                <img v-if="avatar != null && avatar != ''" class="me-avatar" :src="url + '/cdn/avatar/small/'+ avatar">
                <textarea class="input-comment" id="comment-content" rows="1" placeholder="Đề xuất cách nhớ của bạn..."></textarea>
                <i class="zmdi zmdi-mail-send post-comment-btn" v-on:click="postNewComment" style="margin-top: 13px;"></i>
            </div>
        </div>

        <li v-if="showLoadingNewComment == true" class="comment-item" style='text-align: center;'>loading...</li>

        <li class="comment-item" v-for="cmt in listComments" :id="'cmt-item-'+ cmt.id">
            <a v-if="cmt.user_id != 0" class="pull-left avatar-container" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
                <img v-if="!cmt || !cmt.avatar" class="avatar" :src="url + '/assets/img/default-avatar.jpg'">
                <img v-else class="avatar" :src="url + '/cdn/avatar/small/'+ cmt.avatar">
            </a>
            <div class="comment-content">
                <p class="name">
                    <b class="red" v-if="cmt.user_id == 0" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(0)">Dũng Mori</b>
                    <b class="red" v-if="cmt.user_id != 0" data-fancybox data-src="#user-profile-popup" href="javascript:;" v-on:click="fetchUserInfo(cmt.user_id)">
                  @{{cmt.name}}
                </b>
                    <span v-html="printInfo(cmt.content)"></span>
                </p>
                <p class="comment-action">
                    <span
                            v-on:click="!likeLoading ? likeComment(cmt.id) : undefined"
                            v-bind:style="[cmt.liked ? {'color': '#69aa00'} : {'color': 'gray'}]"
                            class="answer"
                            :id="'answer-reply-'+ cmt.id"
                            :href="'#reply-'+ cmt.id"
                            aria-expanded="false"
                            :aria-controls="'reply-'+ cmt.id"
                    >@{{cmt.count_like || ''}} <i class="fa fa-thumbs-up"></i> Like</span> •
                    <span class="time">@{{ prettyDate(cmt.created_at) }}</span>
                </p>
            </div>

            @if(Auth::guard('admin')->user() && auth()->user()->is_tester)
                <span style=" cursor: pointer; margin-right: 5px;" v-on:click="pinComment(cmt)" v-bind:style="cmt.pin == 1 ? 'color: red': ''" ><i class="fa fa-thumb-tack"
                    ></i></span>
                <span class="delete-comment" v-on:click="delComment(cmt.id)"><i class="zmdi zmdi-close-circle"></i> xóa</span>
            @else
                <span v-if="meid == cmt.user_id" class="delete-comment" v-on:click="delComment(cmt.id)"><i class="zmdi zmdi-close-circle"></i> xóa</span>
            @endif
        </li>

        <!-- hiển thị loading -->
        <div v-if="theEnd == false" class="load-more-comment" v-on:click="fetchMoreComments">
            <span v-show="showLoading == false">Tải thêm bình luận</span>
            <img class="loading-icon" v-show="showLoading == true" :src="url + '/assets/img/loading.gif'" />
        </div>
        <div v-if="theEnd == true" class="end-of-list">Hết danh sách</div>

    </div>
</div>

<script type="text/javascript">
    var defaultUserSettings = {
        autoPlay: true,
        isShuffle: false,
        frontLang: 'jp'
    };
    var defaultCardStats = {
        unknown: 0,
        current_unknown: 0,
        known: 0,
        current_known: 0,
        current_total: 0,
        total: 0
    };

    var userSettings = {};
    var userLoggedIn = {!! json_encode(Auth::user()) !!};

    @if(Auth::user())
        try {
        userSettings = JSON.parse({!! json_encode(Auth::user()->setting) !!});
    } catch (e) {
    }
    @else
        userSettings = defaultUserSettings;
    @endif
</script>
<script src="{{asset('assets/js/flashcards.js')}}?{{filemtime('assets/js/flashcards.js')}}"></script>
