<div class="rm-container">
    @if($unlock == 1)
    <div class="course-list-roadmap" id="course-list-roadmap" v-if="paths.length > 0" v-cloak>
        <div id="stage-small-container" class="fc-popup-container stage-small-container">
            <img class="path-bg lazyload" src="{{url('assets/img/roadmap/rm_small.png')}}"/>
            <div class="stage-small">
                <span v-cloak>&bull;&nbsp;@{{currentStage.title}}&nbsp;&bull;</span>
            </div>
            <div class="search-stage">
                {{-- <i class="fa fa-search-plus" aria-hidden="true"></i> --}}
                <svg width="46" height="47" viewBox="0 0 46 47" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_583_6085)">
                    <path d="M21.0575 36.3149C29.2694 36.3149 35.9264 29.6578 35.9264 21.4459C35.9264 13.234 29.2694 6.5769 21.0575 6.5769C12.8455 6.5769 6.18848 13.234 6.18848 21.4459C6.18848 29.6578 12.8455 36.3149 21.0575 36.3149Z" fill="#F7F7F7"/>
                    <path d="M15.3926 21.4458H26.7213" stroke="black" stroke-width="2.83219" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M21.0576 27.1104V15.7816" stroke="black" stroke-width="2.83219" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M21.0575 36.3149C29.2694 36.3149 35.9264 29.6578 35.9264 21.4459C35.9264 13.234 29.2694 6.5769 21.0575 6.5769C12.8455 6.5769 6.18848 13.234 6.18848 21.4459C6.18848 29.6578 12.8455 36.3149 21.0575 36.3149Z" stroke="black" stroke-width="2.83219" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M31.5713 31.9607L40.1741 40.5635" stroke="black" stroke-width="2.83219" stroke-linecap="round" stroke-linejoin="round"/>
                    </g>
                    <defs>
                    <clipPath id="clip0_583_6085">
                    <rect width="45.315" height="45.315" fill="white" transform="translate(0.524414 0.912598)"/>
                    </clipPath>
                    </defs>
                </svg>
            </div>
        </div>

        <div id="stage-container" class="stage-container fc-popup-container">
            <i class="fa fa-expand expand-btn" v-on:click="zoom = true"></i>
            <img class="path-bg lazyload" src="{{url('assets/img/roadmap/rm.png')}}"/>
            <img class="lazyload" v-for="path in paths" :class="'st-step st'+ path.sort_no" :src="url +'/assets/img/roadmap/st'+ path.sort_no +'.png'" v-on:click="currentStage = path"/>
        </div>

        <div class="roadmap-category">
            <div class="rmboxmn right-0 bottom-[190px] lg:bottom-[70px] lg:right-[110px] z-[1]">
                <a v-if="courseId == 3" :href="url + '/khoa-hoc/bo-tro-on-tap-kien-thuc-n4'">
                    <span class="uppercase flex items-center justify-center w-[50px] h-[50px] p-1 rounded-[15px] bg-[#ffe64c] font-[600] text-[6px] text-center text-black mb-3">Ôn tập N4</span>
                </a>
                <a v-if="courseId == 16" :href="url + '/khoa-hoc/bo-tro-on-tap-kien-thuc-n3'">
                    <span class="uppercase flex items-center justify-center w-[50px] h-[50px] p-1 rounded-[15px] bg-[#ffe64c] font-[600] text-[6px] text-center text-black mb-3">Ôn tập N3</span>
                </a>
                <a v-if="courseId == 17" :href="url + '/khoa-hoc/bo-tro-on-tap-kien-thuc-n2'">
                    <span class="uppercase flex items-center justify-center w-[50px] h-[50px] p-1 rounded-[15px] bg-[#ffe64c] font-[600] text-[6px] text-center text-black mb-3">Ôn tập N2</span>
                </a>
                <span v-on:click="fc = true" class="rm_mn-item"><img class="path-bg lazyload" src="{{url('assets/img/roadmap/fc.png')}}"/></span>
                <span v-on:click="sc = true" class="rm_mn-item"><img class="path-bg lazyload" src="{{url('assets/img/roadmap/sc.png')}}"/></span>
                <span v-on:click="nt = true" class="rm_mn-item"><img class="path-bg lazyload" src="{{url('assets/img/roadmap/nt.png')}}"/></span>
            </div>
        </div>

        {{-- expand chặng 1/2/3 --}}
        <div class="stage-container expand-stage" v-show="zoom == true">
            <i class="fa fa-compress hide-bg-btn" v-on:click="zoom = false"></i>
            <img class="path-bg lazyload" src="{{url('assets/img/roadmap/rm.png')}}"/>
            <img class="lazyload" v-for="path in paths" :class="'st-step st'+ path.sort_no" :src="url +'/assets/img/roadmap/st'+ path.sort_no +'.png'" v-on:click="currentStage = path; zoom = false; zoomStage = true;"/>
        </div>

        <div class="illustrator" v-if="illustrator.title != null && illustrator.detail != null"
        style="position: fixed; top: 200px; left: calc(100vw/2 - 200px); z-index: 999999; width: 400px;
        height: 200px; background: #F5FFED; color: #222; padding: 15px; border: solid 1px #96D962; border-radius: 8px; ">
            <i class='fa fa-window-close' style="float: right; color: red; font-size: 22px; cursor: pointer;" v-on:click="illustrator = {title: null, detail: null}"></i>
            <h4 style="border-bottom: 1px solid #96D962; padding-bottom: 10px;" v-html="illustrator.title"></h4>
            <p v-html="illustrator.detail"></p>
        </div>

        {{-- expand lộ trình --}}
        <div class="stage-container expand-stage rm-expand" v-show="zoomStage == true">
            <div class="bg-left">
                <img class="lazyload" v-for="(info, index) in currentStage.illustrator" v-if="index%2 != 0" class="bg-img" :src="url + '/cdn/adventure/default/'+ info.i" v-on:click="showPopover(info.n, info.d)" />
            </div>
            <div class="bg-center">


                <div class="stage-lesson-head">
                    <h2 class="st-title" v-cloak>@{{currentStage.title}}</h2>
                </div>
                <div class="mnbg">

                    <div v-bind:class="(index + 1) % 5 == 0 ? 'dropdown stg-item item5': 'dropdown stg-item'" v-for="(point, index) in currentPoints" v-cloak>
                        <button v-if="point.count_done == 0 && point.inprogress == 0" class="g-item dropdown-toggle" :id="'dropdownMenu' + point.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" >@{{ point.key }}</button>
                        <button v-if="point.count_done == point.count && point.inprogress == 0"  class="g-item done dropdown-toggle" :id="'dropdownMenu' + point.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" >@{{ point.key }}</button>
                        <button v-if="(point.count_done >0 && point.count_done < point.count) || point.inprogress == 1" class="g-item in-progress dropdown-toggle" :id="'dropdownMenu' + point.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true" >@{{ point.key }}</button>



                        <ul class="dropdown-menu stage-drop-menu" aria-labelledby="'dropdownMenu' + point.id">
                            <img class="caret-up lazyload" src="{{url('assets/img/caret-up.png')}}"/>
                            <li v-for="lesson in currentLessons" v-if="lesson.key == point.key">

                                <a :href="url +'/khoa-hoc/{{$course->SEOurl}}/'+ lesson.id +'-'+ lesson.SEOurl">
                                    <span v-if="lesson.type == 'docs' || lesson.type == null">
                                        <img class="icon lazyload" :src="url + '/assets/img/premium/docb.png'"/>
                                    </span>
                                    <span v-if="lesson.type == 'video'">
                                        <img class="icon lazyload" :src="url + '/assets/img/premium/videob.png'"/>
                                    </span>
                                    <span v-if="lesson.type == 'test'">
                                        <img class="icon lazyload" :src="url + '/assets/img/premium/quizb.png'"/>
                                    </span>
                                    <span v-if="lesson.type == 'flashcard'">
                                        <img class="icon lazyload" :src="url + '/assets/img/premium/fcb.png'"/>
                                    </span>
                                    <span v-if="lesson.type == 'guide'">
                                        <img class="icon" :src="url + '/assets/img/premium/guideb.png'"/>
                                    </span>
                                    <b v-if="lesson.cate">@{{ lesson.cate.title }}</b> • @{{ lesson.name }}
                                    <i v-show="lesson.done == 1">✔️</i>
                                    <i v-show="lesson.done == 2" class="fa fa-refresh"></i>
                                </a>
                            </li>
                        </ul>

                        <svg v-if="(index%5 == 0 || index%5 == 1 || index%5 == 2) && (index+1) < currentPoints.length" width="87" height="6" viewBox="0 0 87 6" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 31px 0 0 0; position: absolute; z-index: 11;">
                            <path d="M129 5.5C130.381 5.5 131.5 4.38071 131.5 3C131.5 1.61929 130.381 0.5 129 0.5V5.5ZM0 5.5H129V0.5H0V5.5Z" :fill="point.color"/>
                        </svg>

                        <svg v-if="index%5 == 4 && (index+1) < currentPoints.length" width="459" height="142" xmlns="http://www.w3.org/2000/svg" fill="none" style="margin: -80px 0px 0px -147px; position: absolute; z-index: 11; transform: scale(0.6);">
                            <path :stroke="point.color" id="svg_1" stroke-width="7.99" d="m342,4l46,0c37.003,0 67,29.9969 67,67l0,0c0,37.003 -29.997,67 -67,67l-388,2"/>
                        </svg>

                        <svg v-if="index%5 == 4 && (index+1) < currentPoints.length" width="459" height="142" viewBox="0 0 760 142" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 0px 0px 0px -306px; position: absolute; z-index: 11; ">
                            <path :stroke="point.color" id="svg_1" stroke-width="7.99" d="m116.06224,137l-45.61826,0c-36.69602,0 -66.44398,-29.997 -66.44398,-67l0,0c0,-37.0031 29.74796,-67 66.44398,-67l388.55602,-1"/>
                        </svg>

                    </div>
                </div>
            </div>
            <div class="bg-right">
                <i class="fa fa-compress hide-bg-btn" v-on:click="zoomStage = false"></i>
                <img v-for="(info, index) in currentStage.illustrator" v-if="index%2 == 0" class="bg-img lazyload" :src="url + '/cdn/adventure/default/'+ info.i" v-on:click="showPopover(info.n, info.d)"/>
            </div>
        </div>

        {{-- flashcards --}}
        <div class="stage-container expand-stage fc-popup" v-show="fc == true">
            <i class="fa fa-compress hide-bg-btn" v-on:click="fc = false"></i>
            <h3 style="padding-left: 20px;">Flashcard cả khoá</h3>
            <h5 style="padding-left: 20px; margin-bottom: 25px;">Tổng hợp chữ Hán, từ vựng,  ngữ pháp</h5>
            <div class="scroll-box-flashcard" v-if="currentGfc">
                <div class="gfc-left" style="width: 300px; float: left;">
                    <div class="g-fc fc-mn-item" v-for="gfc in groupFlashcard"  v-on:click="currentGfc = gfc" v-bind:style="currentGfc == gfc ? 'background: rgb(186 213 182)': ''"
                    style="width: 100%; background: #DDD; font-weight: bold; border: none; border-radius: 0; cursor: pointer; float: left; ">
                        @{{gfc.title}}
                    </div>
                </div>
                <div class="gfc-right" style="width: calc(100% - 300px); padding-left: 30px; float: left;">
                    <a :href="url +'/khoa-hoc/{{$course->SEOurl}}/'+ fcitem.lesson_id +'-'+ fcitem.SEOurl" v-for="fcitem in currentGfc.lessons">
                        <div class= "fc-mn-item" style="width: 45%;">
                            <img class="fc-icon lazyload" src="{{url('assets/img/roadmap/cards-duotone.png')}}"/> @{{fcitem.lesson_name}}
                        </div>
                    </a>
                </div>
            </div>
        </div>

        {{-- schedule --}}
        <div class="stage-container expand-stage sc-popup" v-show="sc == true">
            <i class="fa fa-compress hide-bg-btn" v-on:click="sc = false"></i>
            <h3 style="padding-left: 20px;">Thông tin khóa</h3>
            <h5 style="padding-left: 20px;">Chương trình, hướng dẫn học chi tiết từng ngày</h5>
            <a :href="url +'/khoa-hoc/{{$course->SEOurl}}/'+ lesson.id +'-'+ lesson.SEOurl" class="guide-item" v-for="lesson in guideLessons">
                <span v-if="lesson.type == 'docs' || lesson.type == null">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/docb.png'"/>
                </span>
                <span v-if="lesson.type == 'video'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/videob.png'"/>
                </span>
                <span v-if="lesson.type == 'test'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/quizb.png'"/>
                </span>
                <span v-if="lesson.type == 'flashcard'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/fcb.png'"/>
                </span>
                <span v-if="lesson.type == 'flashcard'">
                    <img class="icon lazyload" :src="url + '/assets/img/premium/guideb.png'"/>
                </span>
                @{{ lesson.name }}
                <i v-show="lesson.done == 1">✔️</i>
                <i v-show="lesson.done == 2" class="fa fa-refresh"></i>
            </a>
        </div>

        {{-- notes --}}
        <div class="stage-container expand-stage nt-popup" v-show="nt == true">
            <i class="fa fa-compress hide-bg-btn" v-on:click="nt = false"></i>
            <h3 style="padding-left: 20px;">Lịch sử học</h3>
            <h5 style="padding-left: 20px;">Thống kê bài học mỗi ngày.</h5>
            <div class="date-picker">
                <v-date-picker v-model='selectedDate' :attributes='attributes'/>
            </div>
            <div class="date-history">
                <h3><i class="fa fa-calendar-o"></i> &nbsp; @{{ prettyDate(selectedDate) }}</h3>
                <hr style="border-bottom: 1px solid #96D962" />
                <div class="history-container">
                    <a class="history-item" v-for="item in historyItems" v-if="item.date_at == dateFilter">
                        {{-- @{{item.id}} <span v-if="item.lesson">@{{item.lesson.name}}</span> --}}
                        <div v-if="item.lesson">
                            <span v-if="item.lesson.type == 'docs' || item.lesson.type == null">
                                <img class="icon lazyload" :src="url + '/assets/img/premium/docb.png'"/>
                            </span>
                            <span v-if="item.lesson.type == 'video'">
                                <img class="icon lazyload" :src="url + '/assets/img/premium/videob.png'"/>
                            </span>
                            <span v-if="item.lesson.type == 'test'">
                                <img class="icon lazyload" :src="url + '/assets/img/premium/quizb.png'"/>
                            </span>
                            <span v-if="item.lesson.type == 'flashcard'">
                                <img class="icon lazyload" :src="url + '/assets/img/premium/fcb.png'"/>
                            </span>
                            @{{item.lesson.name}}
                            <i v-show="item.lesson.done == 1">✔️</i>
                            <i v-show="item.lesson.done == 2" class="fa fa-refresh"></i>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <div class="stage-lesson">
            <div class="stage-lesson-head">
                <img v-if="currentStage.id !== paths[0].id" src="{{ asset('assets/img/roadmap/prev-map.svg') }}" class="lesson-path-prev lazyload" v-on:click="prevStage" v-cloak />
                <img v-if="currentStage.id !== paths[paths.length - 1].id" src="{{ asset('assets/img/roadmap/next-map.svg') }}" class="lesson-path-next lazyload" v-on:click="nextStage" v-cloak />
                <h2 class="st-title" v-cloak>@{{currentStage.title}}</h2>
                <i class="fa fa-expand expand-btn" style="margin: -65px 10px 0 0;" v-on:click="zoomStage = true"></i>
            </div>
            <div class="mnbg">

                <div v-bind:class="(index + 1) % 3 == 0 ? 'dropdown stg-item item3': 'dropdown stg-item'" v-for="(point, index) in currentPoints">
                    <button v-if="point.count_done == 0 && point.inprogress == 0" class="g-item dropdown-toggle" :id="'dropdownMenu' + point.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                        @{{ point.key }}
                    </button>
                    <button v-if="point.count_done == point.count && point.inprogress == 0"  class="g-item done dropdown-toggle" :id="'dropdownMenu' + point.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                        @{{ point.key }}
                    </button>
                    <button v-if="(point.count_done >0 && point.count_done < point.count) || point.inprogress == 1" class="g-item in-progress dropdown-toggle" :id="'dropdownMenu' + point.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                        @{{ point.key }}
                    </button>

                    <ul :id="'ul-dropdownMenu' + point.id" class="dropdown-menu stage-drop-menu" aria-labelledby="'dropdownMenu' + point.id">
                        <img class="caret-up lazyload" src="{{url('assets/img/caret-up.png')}}"/>
                        <li
                            v-for="lesson in currentLessons"
                            :key="'lesson-' + lesson.id"
                            v-if="lesson.key == point.key"
                            :style="{ 'background-color': lesson.id === this_l_id ? '#96d962' : '#F5FFED' }"
                        >
                            <a
                                :href="url +'/khoa-hoc/{{$course->SEOurl}}/'+ lesson.id +'-'+ lesson.SEOurl"
                                :style="{ 'font-weight': lesson.id === this_l_id ? 'bold' : 'normal' }"

                            >
                                <span v-if="lesson.type == 'docs' || lesson.type == null">
                                    <img class="icon lazyload" :src="url + '/assets/img/premium/docb.png'"/>
                                </span>
                                <span v-if="lesson.type == 'video'">
                                    <img class="icon lazyload" :src="url + '/assets/img/premium/videob.png'"/>
                                </span>
                                <span v-if="lesson.type == 'test'">
                                    <img class="icon lazyload" :src="url + '/assets/img/premium/quizb.png'"/>
                                </span>
                                <span v-if="lesson.type == 'flashcard'">
                                    <img class="icon lazyload" :src="url + '/assets/img/premium/fcb.png'"/>
                                </span>
                                <b v-if="lesson.cate">@{{ lesson.cate.title }}</b> • @{{ lesson.name }}
                                <i v-show="lesson.done == 1">✔️</i>
                                <i v-show="lesson.done == 2" class="fa fa-refresh"></i>
                            </a>
                        </li>
                    </ul>

                    <svg v-if="index%3 == 0 && (index+1) < currentPoints.length" width="70" height="6" viewBox="0 0 70 6" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 31px 0 0 0; position: absolute; z-index: 11;">
                        <path d="M129 5.5C130.381 5.5 131.5 4.38071 131.5 3C131.5 1.61929 130.381 0.5 129 0.5V5.5ZM0 5.5H129V0.5H0V5.5Z" :fill="point.color"/>
                    </svg>

                    <svg v-if="index%3 == 1 && (index+1) < currentPoints.length" width="116" height="89" viewBox="0 0 116 89" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 31px 0 0 -64px; position: absolute; z-index: 11;">
                        <path d="m2.5,82.32491c-1.38071,0 -2.5,1.24779 -2.5,2.78699c0,1.5392 1.11929,2.78699 2.5,2.78699l0,-5.57399zm60,-76.25092l13.8005,0l0,-5.57399l-13.8005,0l0,5.57399zm13.8005,76.25092l-73.8005,0l0,5.57399l73.8005,0l0,-5.57399zm34.1995,-38.12541c0,21.05607 -15.3116,38.12541 -34.1995,38.12541l0,5.57399c21.6493,0 39.1995,-19.56492 39.1995,-43.69939l-5,0zm-34.1995,-38.12552c18.8879,0 34.1995,17.06933 34.1995,38.12552l5,0c0,-24.13459 -17.5502,-43.69951 -39.1995,-43.69951l0,5.57399z" :fill="point.color"/>
                    </svg>

                    <svg v-if="index%3 == 2 && (index+1) < currentPoints.length" width="116" height="95" viewBox="0 0 116 95" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 30px 0px 0px -168px; position: absolute; z-index: 11;">
                        <path d="m107.62987,6.39288c1.30926,0 2.37013,-1.31917 2.37013,-2.94644c0,-1.62727 -1.06087,-2.94644 -2.37013,-2.94644l0,5.89287zm-56.88312,80.61315l-13.08359,0l0,5.89287l13.08359,0l0,-5.89287zm-13.08359,-80.61315l69.96671,0l0,-5.89287l-69.96671,0l0,5.89287zm-32.4229,40.30663c0,-22.26079 14.51619,-40.30663 32.4229,-40.30663l0,-5.89287c-20.52466,0 -37.16316,20.6842 -37.16316,46.1995l4.74026,0zm32.4229,40.30652c-17.90671,0 -32.4229,-18.04585 -32.4229,-40.30652l-4.74026,0c0,25.51518 16.6385,46.19939 37.16316,46.19939l0,-5.89287z" :fill="point.color"/>
                    </svg>

                </div>
            </div>
        </div>
    </div>
    @endif
</div>
