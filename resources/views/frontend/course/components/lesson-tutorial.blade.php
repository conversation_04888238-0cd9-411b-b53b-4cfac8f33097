@php
    $hasExercise = $lesson->components->whereIn('type', [3, 13])->isNotEmpty() ? '1' : '0';
@endphp
<script>
    $(document).ready(function() {
        var checkTutLesson = localStorage.getItem('SHOW_NEW_BASIC_LESSON_TUT');
        if (checkTutLesson != 1 && checkTutLesson != '1') {
            setTimeout(() => {
                localStorage.setItem('SHOW_NEW_BASIC_LESSON_TUT', 1);
                $(".lesson-tutorial").show();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                disableScroll();
                setupTutorial();
                $(window).resize(function() {
                    setupTutorial();
                });
                if (window.innerWidth >= 1024) {
                    // Is desktop
                    $(".lesson-step").removeClass("active");
                    $(".lesson-step.step-1").addClass("active");
                } else {
                    // Is mobile
                    $(".lesson-step").removeClass("active");
                    if ("{{ $hasExercise }}" == "1") {
                        $(".lesson-step.step-2").addClass("active");
                    } else {
                        $(".lesson-step.step-3").addClass("active");
                    }
                }
            }, 1000);
        }
    });
</script>

<div class="lesson-tutorial hidden fixed w-screen h-full top-0 left-0 z-[9999]">
    <div class="lesson-step step-1" data-step="1">
        <div class="sp:hidden">
            <div
                class="mx-auto max-w-[90%] sp:max-w-full font-averta-regular z-50 relative {{ $lesson->components->whereIn('type', [3, 13])->isNotEmpty() ? 'mt-[196px]' : 'mt-[136px]' }}">
                <div id="tut1-video"
                    class="absolute top-0 left-0 z-50 rounded-3xl overflow-hidden bg-white p-[20px] scale-[1.05] w-2/3 max-w-[calc(100%-420px)]">
                    <div class="w-full aspect-video min-h-[500px] bg-cover bg-center bg-no-repeat"
                        style="background-image: url({{ $lesson->avatar_name ? url('cdn/lesson/default/' . $lesson->avatar_name) : asset('images/lessons/lesson-default.png') }})">
                    </div>
                    <div class="mt-5">
                        <div class="mb-3 font-averta-semibold text-2xl text-[#1E1E1E]">
                            <span>{{ $lesson->name }}</span>
                            @if ($lesson->require)
                                <span class="relative">
                                    <img class="w-[12px]"
                                        src="{{ asset('images/icons/' . ($isLocked ? 'locked/' : '') . 'require.png') }}"
                                        alt="require.png">
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <div id="tut1-lesson" class="absolute w-[425px] right-[30px] z-50 text-center">
                <img class="w-[425px]" src="{{ asset('images/lessons/tut1.png') }}" alt="tut1.png">
                <div class="rounded-2xl p-[10px] bg-white mt-8">
                    <div id="tut1-content-2" class="tut-content"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="lesson-step step-2" data-step="2">
        <div class="w-full h-full absolute top-[74px] left-0 z-10 sp:hidden">
            <div id="tut2" class="absolute left-1/3 -translate-x-1/2 z-50 text-center">
                <div class="rounded-2xl p-[10px] bg-white mt-8">
                    <div class="tut-content flex items-center justify-center my-auto">
                        <div class="my-[12px] flex justify-center lesson-main-container w-2/3 sp:w-full duration-500" id="tabList">
                            <div class="tab-content tabs @if(\Illuminate\Support\Facades\Auth::check()) bg-[#C1EACA] @else bg-[#E6E6E6] @endif p-1 rounded-full text-base flex w-[250px]">
                                <div :class="`font-averta-regular  tab py-2 text-center ${tabActive === 'tabLesson' ? 'active-tab bg-white text-black rounded-full disabled' : 'text-[#757575]'} w-1/2 cursor-pointer nav-item`"
                                     data-tab="tabLesson">
                                    Bài học
                                </div>
                                <div :class="`font-averta-regular tab py-2 text-center  w-1/2 cursor-pointer nav-item ${tabActive === 'tabExercise' ? 'active-tab bg-white text-black rounded-full disabled' : 'text-[#757575]'}`"
                                     data-tab="tabExercise">
                                    Bài tập
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <img class="w-[400px] mt-4" src="{{ asset('images/lessons/tut2.png') }}" alt="tut2.png">
            </div>
        </div>
        <div class="desktop:hidden">
            <div class="w-full h-full absolute top-[96px] left-0 z-10">
                <div class="tablist-clone"></div>
                <div class="mt-3 text-center">
                    <img class="w-[250px]" src="{{ asset('images/lessons/tut1m.png') }}" alt="tut1m.png">
                </div>
                <div class="text-white text-base text-center mt-10">
                    <img class="w-[80px]" src="{{ asset('images/icons/tap.png') }}" alt="click.png">
                    <div class="font-averta-regular">Chạm để tiếp tục</div>
                </div>
            </div>
        </div>
    </div>
    <div class="lesson-step step-3" data-step="3">
        <div class="w-full h-full absolute top-[74px] left-0 z-10">
            <div id="tut3" class="absolute bottom-[42px] right-[393px] z-50">
                <img class="w-[455px] mt-4" src="{{ asset('images/lessons/tut3.png') }}" alt="tut3.png">
            </div>
            <div class="absolute bottom-[80px] right-[353px] z-50">
                <div class="bg-white p-2 rounded-full">
                    <div class="rounded-full w-[44px] h-[44px] flex-none flex items-center justify-center bg-[#C1EACA]">
                        <img src="/images/icons/comment.png" alt="comment" class="w-[22px] h-[22px]">
                    </div>
                </div>
            </div>
        </div>
        <div class="desktop:hidden">
            <div class="w-full h-full absolute top-[75px] left-0 z-10">
                <div class="flex justify-end">
                    <div class="w-fit py-1 pl-10 bg-white rounded-l-full">
                        <button
                            class="btn-show-menu top-20 sp:top-[72px] w-[46px] h-[44px] border-none rounded-l-full hover:w-[75px] bg-white text-left z-50 right-0"
                            style="box-shadow: rgba(76, 93, 112, 0.24) 0px 2px 4px 0px;">
                            <img src="/images/icons/hide.png" alt="hide.png"
                                class="h-[14px] rotate-180 relative left-[15px]">
                        </button>
                    </div>
                </div>
                <div class="mt-3 text-right">
                    <img class="w-[250px] mr-3" src="{{ asset('images/lessons/tut2m.png') }}" alt="tut2m.png">
                </div>
                <div class="text-white text-base text-center mt-10">
                    <img class="w-[80px]" src="{{ asset('images/icons/tap.png') }}" alt="click.png">
                    <div class="font-averta-regular">Chạm để tiếp tục</div>
                </div>
            </div>
        </div>
    </div>
    <div class="lesson-step step-4" data-step="4">
        <div class="w-full h-full absolute top-[74px] left-0 z-10">
            <div id="tut4" class="absolute bottom-[46px] right-[438px] z-50">
                <img class="w-[268px] mt-4" src="{{ asset('images/lessons/tut4.png') }}" alt="tut4.png">
            </div>
            <div class="absolute bottom-[80px] right-[396px] z-50">
                <div class="bg-white p-2 rounded-full">
                    <div class="rounded-full w-[44px] h-[44px] flex-none flex items-center justify-center bg-[#C1EACA]">
                        <img src="/images/icons/lesson-list.png" alt="lesson list" class="w-[22px] h-[22px]">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bg-black opacity-80 w-full h-full absolute top-0 left-0 overlay-tutorial">
    </div>
</div>
