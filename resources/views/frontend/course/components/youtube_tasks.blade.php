<div class="dmrx-player">
  <div class="movie-play" id="movie-play-{{ $task->id }}" onclick='return playVideo({{ $task->id }})'>
      <img
      @if (!is_null($thisLesson) && $thisLesson->avatar_name != null && $thisLesson->avatar_name != "")
      src="{{url('cdn/lesson/default')}}/{{ $thisLesson->avatar_name }}"
      @else
      {{-- src="https://i.ytimg.com/vi/{{$video->video_name}}/hqdefault.jpg" --}}
      src="https://i.ytimg.com/vi_webp/{{$task->video_name}}/sddefault.webp"
      @endif
      width="700" height="395"
      />
      <br>
      @include('frontend.course.play-button')
  </div>

  <?php

  $youtube_id = $task->video_name;

                        //nếu là dạng link gốc youtube
  if (strpos($task->video_name, 'youtu') !== false) {
    preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $task->video_name, $match);
    $youtube_id = $match[1];
}

?>

<iframe class="ytplayer" id="ytplayer-{{ $task->id }}" src="https://www.youtube.com/embed/{{$youtube_id}}" frameborder="0" allowfullscreen></iframe>

<div class="server-localtion-container">
  <span class="server-localtion active"># Máy chủ youtube</span>
</div>

<script type="text/javascript">

    // sự kiện click vào chạy video
    function playVideo(taskId) {
      $("#movie-play-" + taskId).css("display", "none");
      $("#ytplayer-" + taskId).css("display", "block");
      $("#ytplayer-" + taskId)[0].src += "?autoplay=1";
    }
</script>
</div>
