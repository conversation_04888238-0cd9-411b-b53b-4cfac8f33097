<div class="dmrx-player" v-show="tab == 2" v-show="!currentResult">
    <div class="fast-area fast-left" v-on:click="fastRewind()">

        <img src="{{url('assets/img/ic_back10s.png')}}" alt="icon-back" class="right-fast-icon">
        {{--    <i class="zmdi zmdi-fast-rewind left-fast-icon"></i>--}}
    </div>
    <div class="fast-area fast-right" v-on:click="fastForward()">
        <img src="{{url('assets/img/ic_next10s.png')}}" alt="icon-next" class="right-fast-icon">
        {{--    <i class="zmdi zmdi-fast-forward right-fast-icon"></i>--}}
    </div>
    <a class="movie-play" v-on:click="clickPlayVideo">
        <img
        @if (!is_null($thisLesson) && $thisLesson->avatar_name != null && $thisLesson->avatar_name != "")
        src="{{url('cdn/lesson/default')}}/{{ $thisLesson->avatar_name }}"
        @else
        src="{{url('assets/img/gray.jpg')}}"
        @endif
        width="100%" height="395"/>
        <br>
        @include('frontend.course.play-button')
    </a>
    <video id="myplayer_{{$random_id}}" class="myplayer video-js vjs-default-skin" controls></video>
    <div class="server-localtion-container">

      <a class="server-item" v-for="server in listServers" style="display: none;" v-show="listServers != null">
        <span v-if="server.id == currentServerId" class="server-localtion active">@{{server.name}}</span>
        <span v-if="server.id != currentServerId" class="server-localtion" v-on:click="changeServerLocaltion(server.id)">@{{server.name}}</span>
      </a>

      <a class="quality-item">
        <span class="qitem" v-bind:class="{ active: (currentQuality == '360p')}" v-on:click="changeQuality('360p')">360p</span>
        <span class="qitem" v-bind:class="{ active: (currentQuality == '480p')}" v-on:click="changeQuality('480p')">480p</span>
        <span class="qitem" v-bind:class="{ active: (currentQuality == '720p')}" v-on:click="changeQuality('720p')">720p<sup>HD</sup></span>
        <span class="qitem" v-bind:class="{ active: (currentQuality == '1080p')}" v-on:click="changeQuality('1080p')">1080p<sup>FHD</sup></span>
      </a>

    </div>
</div>


{{-- nếu có nhiều video trong một bài học
@if (count($otherVideo) > 1)
<div class="list-video-area-parent" style="margin-bottom: 0; padding-bottom: 0;">
    <div class="list-video-area-child">
      @foreach($otherVideo as $key => $v)
        <div class="video-item-area" style="margin-right: 14px; cursor: pointer;">
          <a class="image-area" v-on:click="loadMp4('{{$v->video_name}}')" title="{{ $v->video_title }}">
            <img class="image-video" style="width: 210px;" src="{{url('assets/img/video-thumbnail-default.jpg')}}"
            v-bind:style="(currentMediaName == '{{$v->video_name}}') ? 'border-bottom: 4px solid #41A336;' : ''">
            <span class="play-icon-btn-sm"><i class="zmdi zmdi-play"></i></span>
        </a>
        <a class="video-title" style="-webkit-box-orient: vertical;" v-on:click="loadMp4('{{$v->video_name}}')" title="{{ $v->video_title }}">{{ $v->video_title }}</a>
      </div>

  @endforeach
</div>
</div>
@endif --}}

<script type="text/javascript">
  lessonInfo            = {!! json_encode($lessonInfo) !!};
  lesson_otherVideo     = {!! json_encode($otherVideo) !!};
  lessonDetail         = {!! json_encode($thisLesson) !!};
  courseUrl             = "{{ $course->SEOurl }}";

  {{--mã hóa tên video--}}
  var mdaId = "{{base64_encode(base64_encode(base64_encode($video->video_name)))}}";

  {{--lấy ra danh sách server mà bài học có--}}
  var listServers = {!! json_encode($servers) !!};

  var playerId = '{{$random_id}}';

  $(document).on('click', '.clickable', function(e){
        var $this = $(this);
        if(!$this.hasClass('panel-collapsed')) {
            $this.parents('.panel').find('.panel-body').slideUp();
            $this.addClass('panel-collapsed');
            $this.find('i').removeClass('glyphicon-chevron-down').addClass('glyphicon-chevron-up');

        } else {
            $this.parents('.panel').find('.panel-body').slideDown();
            $this.removeClass('panel-collapsed');
            $this.find('i').removeClass('glyphicon-chevron-up').addClass('glyphicon-chevron-down');

        }
    })

  {{-- chuyển script xuống footer --}}
</script>

