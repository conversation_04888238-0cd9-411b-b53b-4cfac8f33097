{{-- nút nộp bài --}}
@foreach($tasks as $task)
    @if (($task->type == 3 || $task->type == 6))
        @if (Auth::check())
            <button class="btn btn-primary trac-nghiem" v-on:click="sendTestResult('auth')">Nộp bài</button>
        @else
            <button class="btn btn-primary trac-nghiem" v-on:click="sendTestResult('no-auth')">Nộp bài</button>
        @endif
        @break
    @endif
@endforeach

  {{-- kết quả nộp bài kiểm tra --}}
  <hr style="border: 0; border-bottom: 1px solid #ddd;">
  <div class="alert mt20" id="result" style="display: none;">
    <div v-if="results.length > 0">
      <h4>Kết quả các bài kiểm tra đã làm</h4>
      <hr>
      <div class="alert" v-for="(result, index) in results" v-bind:class="[result.grade < lesson.pass_marks ? 'bg-warning' : 'bg-success']" style="font-size: 14px">
        {{-- <p>Thời gian thực hiện lúc <b>@{{ printTime(result.created) }}</b> ngày <b>@{{ printDate(result.created) }}</b></p> --}}
        <p>Tổng điểm: <b>@{{ result.grade }} / @{{ result.total_grade }}</b>
          __
          @{{ convertTime(result.created_at, "time") }} ngày <b> @{{ convertTime(result.created_at, "date") }}</b>
        </p>
        <p>Kết quả: <b v-if="result.grade < lesson.pass_marks">Không đạt yêu cầu</b>
          <b v-if="result.grade >= lesson.pass_marks">Đã qua</b></p>
        <button class="btn btn-info review-result" data-toggle="modal" data-target="#myModal" v-on:click="reviewTestResult(index)">Xem bài làm</button>
        <button class="btn btn-warning remove-result" data-toggle="modal" data-target="#removeResult" v-on:click="removeTestResult(index)">Xóa</button>
      </div>
    </div>
  </div>

  {{-- hiển thị modal lưu ý điên đầy đủ câu trả lời --}}
  <div class="modal fade" id="empty_answers" role="dialog" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" style="float: right;" data-dismiss="modal">&times;</button>
          <h4 style="width: 100%; float: right;" class="modal-title">Lưu ý</h4>
        </div>
        <div class="modal-body">
          <div class="alert alert-danger">
            <i class="fa fa-tags"></i>&nbspBạn chưa nhập câu trả lời. Vui lòng trả lời rồi mới nộp bài thi.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-info" data-dismiss="modal">Đóng</button>
        </div>
      </div>

    </div>
  </div>

  {{-- hiển thị modal xóa bài kiểm tra --}}
  <div class="modal fade" id="removeResult" role="dialog" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title">Xóa</h4>
        </div>
        <div class="modal-body">
          <p>Bạn có muốn xóa bài kiểm tra này không?</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-warning" v-on:click="confirmRemove()">Xóa</button>
          <button type="button" class="btn btn-info" data-dismiss="modal">Đóng</button>
        </div>
      </div>

    </div>
  </div>

  {{-- modal in thông báo điểm --}}
  <div class="modal fade" id="myMessage" role="dialog" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title">Thông báo</h4>
        </div>
        <div class="modal-body">
          <div class="alert alert-info">
            <i class="fa fa-tags"></i>&nbsp;Bạn được @{{ userScore }} / @{{ lesson.total_marks }} điểm.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" data-dismiss="modal" class="btn btn-primary">&nbsp;&nbsp;OK&nbsp;&nbsp;</button>
        </div>
      </div>

    </div>
  </div>

  {{-- chi tiết kết quả bài kiểm tra --}}
  <div class="modal fade" id="myModal" role="dialog" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" onclick="turnOffMp3()">&times;</button>
          <h4 class="modal-title">Chi tiết bài kiểm tra</h4>
        </div>
        <div class="modal-body">
          <table class="table table-bordered bg-warning">
            <tbody>
            <tr>
              <td class="row_label">Kết quả</td>
              <td class="row_item">
                <b v-if="currentResult.grade < lesson.pass_marks"><span class="text-danger">Không đạt yêu cầu</span></b>
                <b v-if="currentResult.grade >= lesson.pass_marks"><span class="text-success">Đã qua</span></b></td>
            </tr>
            <tr>
              <td class="row_label">Điểm đạt được</td>
              <td class="row_item">@{{ currentResult.grade }} <span style="font-size: 12px">(Điểm đạt yêu cầu: {{ $thisLesson->pass_marks }})</span></td>
            </tr>
            <tr>
              <td class="row_label">Tổng điểm bài kiểm tra</td>
              <td class="row_item">@{{ currentResult.total_grade }}</td>
            </tr>
            <tr>
              <td class="row_label">Thời gian thi</td>
              <td class="row_item"><b> @{{ convertTime(currentResult.created_at, "time") }} </b> ngày <b> @{{ convertTime(currentResult.created_at, "date") }} </b></td>
            </tr>
            </tbody>
          </table>

          @foreach($tasks as $key => $task)
            @if ($task->type == 4 && $task->is_quiz != 1)
              <div id="see-guides">
                <button type="button" class="btn btn-info btn-display-answer"
                        data-toggle="collapse" data-target="#collapse-modal{{ $task->id }}">
                  <i class="zmdi zmdi-sun"></i> Xem giải thích chi tiết
                </button>
              </div>
              <div id="collapse-modal{{ $task->id }}" class="collapse">
                <div class="answer-box answer-box-area" v-html="JSON.parse(tasks[{{$key}}].value)[0].content"></div>
              </div>
            @endif
          @endforeach

          <table class="table table-bordered table-striped table-hover tc-table">
            <tbody>
            <tr>
              <td colspan="2" class="content-test-detail">
                <div v-for="(task, index) in tasks">

                  {{-- in ra nội dung --}}
                  <div v-if="task.type == 1" v-html="printContentAsAswer(task.value)">

                  </div>
                  <div v-if="task.type == 3 || task.type == 5">
                    <p style="margin: 10px 0; font-size: 16px;" v-html="printContentAsAswer(task.value)" v-if="task.type == 3"></p>
                    <div v-if="task.type == 5" style="margin: 5px 0;" class="audio-task-area">
                      <label>@{{ JSON.parse(task.value).name }}</label>
                      <audio controls="">
                        {{--  <source :src="'http://cdn.dungmori.com:8081/mp3/' + JSON.parse(task.value).link.split(';')[1] + '?wmsAuthSign=' + tokenMp3" type="audio/mpeg"> --}}
                        {{-- <source src="http://cdn.dungmori.com:8081/mp3/supido-nghehieu-N5CD2/39 Track 39.mp3?wmsAuthSign=c2VydmVyX3RpbWU9My8yMi8yMDE4IDM6MTM6MzQgQU0maGFzaF92YWx1ZT1RZDFmd1ZyWlRhblNNSzFyRHgyeTh3PT0mdmFsaWRtaW51dGVzPTE4MDAw" type="audio/mpeg"> --}}
                        <source :src="'https://mp3-v2.dungmori.com/' + JSON.parse(task.value).link" type="audio/mpeg">
                        Your browser does not support the audio element.
                      </audio>
                    </div>
                    <div class="col-sm-6 answer-area" style="margin-bottom: 10px;" v-for="(answer, index) in answers[task.id]">
                      <div class="question-answer">

                        <div class="labels question-answer-content" v-bind:class="[answer.grade == 0 ? '' : 'label-true', (answer.grade == 0 && answer.checked) ? 'label-false' : '']">
                          <input type="radio" v-if="answer.checked == true" checked onclick="this.checked = true">
                          <input type="radio" v-if="answer.checked != true" onclick="this.checked = false">&nbsp;&nbsp;@{{ answer.value }}<span v-if="answer.grade != 0">&nbsp;(Đúng)</span>
                        </div>
                      </div>
                    </div>
                    {{-- <div style="position: relative;" v-if="task.type == 3" v-bind:class="[answers[task.id].length > 4 ? 'three-line-answer' : 'two-line-answer']"><hr style="position: absolute; width: 100%; bottom: 0; border-color: #ddd;"></div> --}}
                    <div v-if="task.type == 3" class="col-md-11" style="display: inline-block; width: 100%; height: 10px;"></div>
                    <hr v-if="task.type == 3" style="width: 100%; margin-top: 20px; margin-bottom: 30px; bottom: 0; border-color: #ddd;">
                  </div>
                  <div v-if="task.type == 6" style="padding: 0; font-size: 13px;">
                    <table class="table table-hover">
                      <thead>
                      <tr>
                        <th style="width: 1%">Stt</th>
                        <th style="width: 30%">Câu hỏi</th>
                        <th style="width: 25%">Câu trả lời</th>
                        <th style="width: 25%">Đáp án</th>
                        <th class="text-center">Điểm</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="(item, index) in JSON.parse(task.value)" v-bind:class="[(resultData[task.id] == undefined || resultData[task.id][item.id] != item.answer) ? 'danger' : 'success']">
                        <td>@{{ index + 1 }}</td>
                        <td v-html="item.question"></td>
                        <td v-if="resultData[task.id] != undefined">@{{ resultData[task.id][item.id] }}</td>
                        <td v-else></td>
                        <td>@{{ item.answer }}</td>
                        <td class="text-center">@{{ item.grade }}</td>
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <div class="modal-footer">
          <button type="button" data-dismiss="modal" class="btn btn-primary"> Đóng </button>
        </div>
      </div>
    </div>
  </div>

