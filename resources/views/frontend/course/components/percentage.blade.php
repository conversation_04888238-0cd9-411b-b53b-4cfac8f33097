<div class="relative ml-auto transform scale-x-[-1]">
    <svg class="{{ $wrapperClass ?? '' }} w-[70px] h-[70px]">
        <circle class="text-[#F5F5F5]" stroke-width="5" stroke="currentColor" fill="transparent" r="30" cx="35"
            cy="35" />
        <circle class="{{ $percent < 85 ? 'text-[#E8B931]' : 'text-[#57D061]' }}" stroke-width="5"
            stroke-dasharray="188.4" stroke-dashoffset="{{ ((100 - $percent) * 188.4) / 100 }}" stroke-linecap="round"
            stroke="currentColor" fill="transparent" transform="rotate(-90 35 35)" r="30" cx="35"
            cy="35" />
    </svg>
    <div class="absolute inset-0 flex items-center justify-center transform scale-x-[-1]">
        <span class="text-[#414348] text-base font-averta-semibold">{{ $percent }}%</span>
    </div>
</div>
