@extends('frontend._layouts.default')

@section('title') Dungmori - Gói combo {{ $combo->name }} @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-js')
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
@stop

@section('content')

<div id="fb-root"></div>

<div class="main">

  <div class="main-center main-course" id="main-course">
      <div class="main-left">

        <div class="hidden-pc">
          <div class="course-info-container">
              <div class="course-heading">
                <span>Thông tin khóa học</span>
              </div>
              <div class="price" style="margin-top: 25px;"> {{ number_format($combo->price) }} ₫ </div>
{{--              <div class="price-yen"> ( {{ number_format($combo->jpy_price) }} ¥ ) </div>--}}

                <div class="buy-item">
                  <a href="{{url('/payment')}}?buy={{ base64_encode("combo") }}&item={{ base64_encode($combo->id) }}">
                    <span class="buy-btn">Mua combo</span>
                  </a>
                </div>

            </div>
            <a href="{{url('/bang-gia')}}">
              <div class="see-more">Xem thêm các khóa học khác</div>
            </a>
        </div>

        <h2 class="course-detail-title">Gói combo <a><b>{{ $combo->name }}</b></a></h2>
        <div class="combo-list-container">
          @foreach($listCourses as $course)
            <div class="combo-item @if(in_array($course->id, array(8, 9, 10))) combo-eju @endif">
               <div class="combo-name-container">
                  <p>KHÓA</p>
                  <h1>{{ $course->name }}</h1>
               </div>
               <div class="combo-detail-container">
                  <div class="course-info">Thời gian <span>{!! round(json_decode($combo->services)->course_watch_expired_value / 30) !!} tháng</span></div>
                  <div class="course-info">Số videos  <span>{!! json_decode($course->stats_data)->video !!} videos</span></div>
                  <div class="course-info">Số bài học <span>{!! json_decode($course->stats_data)->lesson !!}</span></div>
                  <a href="{{url('khoa-hoc')}}/{{ $course->SEOurl }}" target="_blank">
                    <div class="dmr-btn">Xem chi tiết</div>
                  </a>
               </div>
            </div>
{{--            @if (in_array($course->id, array(3, 16, 17)))--}}
{{--              <?php--}}
{{--                if ($course->id == 3) $ldCourse = $listLDCourses[0];--}}
{{--                if ($course->id == 16) $ldCourse = $listLDCourses[1];--}}
{{--                if ($course->id == 17) $ldCourse = $listLDCourses[2];--}}
{{--              ?>--}}
{{--              <div class="combo-item">--}}
{{--                <div class="combo-name-container">--}}
{{--                  <p>KHÓA</p>--}}
{{--                  <h1>{{ $ldCourse->name }}</h1>--}}
{{--                </div>--}}
{{--                <div class="combo-detail-container">--}}
{{--                  <div class="course-info">Thời gian <span>{!! round(json_decode($combo->services)->course_watch_expired_value / 30) !!} tháng</span></div>--}}
{{--                  <div class="course-info">Số videos  <span>{!! json_decode($ldCourse->stats_data)->video !!} videos</span></div>--}}
{{--                  <div class="course-info">Số bài học <span>{!! json_decode($ldCourse->stats_data)->lesson !!}</span></div>--}}
{{--                  <a href="{{url('khoa-hoc')}}/{{ $ldCourse->SEOurl }}" target="_blank">--}}
{{--                    <div class="dmr-btn">Xem chi tiết</div>--}}
{{--                  </a>--}}
{{--                </div>--}}
{{--              </div>--}}
{{--            @endif--}}
          @endforeach
        </div>

        <div class="comment-container" id="comment-container">


          <ul class="nav nav-pills comment-tab">
            <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a></li>
            <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng facebook</a></li>
          </ul>

          <div class="tab-content">
            <div id="user-comment-content" class="tab-pane fade in active">

              @if(Auth::check())
              <comments meid="{{ Auth::user()->id }}"
                        avatar="{{ Auth::user()->avatar }}"
                        tbid="{{ $combo->id }}"
                        tbname="combo"
                        num-posts="15"
                        background="#fff"
                        ref="comment">
              </comments>
              @else
              <comments tbid="{{ $combo->id }}"
                        tbname="combo"
                        num-posts="15"
                        background="#fff"
                        ref="comment">
              </comments>
              @endif

            </div>
            <div id="facebook-comment-content" class="tab-pane fade">
                <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
            </div>

          </div>
        </div>
      </div>
      <div class="main-right">
        <div class="course-info-container hidden-mobile">
          <div class="course-heading">
            <span>Thông tin khóa học</span>
          </div>
            @if($combo->extra_price > 0)
                <div class="combo-plus">
                    <label class="chk-plus">
                        <input type="checkbox" v-model="isPlus" @change="setProductPlus()" >
                        <i v-if="isPlus" class="fa fa-check-square-o"></i>
                        <i v-else class="fa fa-square-o"></i>
                        <span class="title">Mua kèm <strong>Gói {{$combo->name}} Plus</strong></span>
                    </label>
                    <p>Học phí: <span class="vnd-plus">{{number_format($combo->extra_price)}} ₫</span>
{{--                      ({{ number_format($combo->extra_jp_price) }} ¥)--}}
                    </p>
                    <p>{!! $combo->extra_desc  !!}</p>
                </div>
            @endif
          <div class="price mb-3" style="margin-top: 25px;"> <span v-text="totalPrice"></span> ₫</div>
{{--          <div class="price-yen"> ( <span v-text="totalJpPrice"></span>  ¥) </div>--}}
            <div class="buy-item">

              <a :href="url">
                <span class="buy-btn">Mua combo</span>
              </a>
            </div>
        </div>
        <a href="{{url('/bang-gia')}}" class="hidden-mobile">
          <div class="see-more">Xem thêm các khóa học khác</div>
        </a>

      </div>
  </div>
</div>

@section('fixed-panel')



@stop

@stop


@section('footer-js')
  <script> $(".khoa-hoc").addClass("active"); </script>
  <script type="text/javascript">
    new Vue({
        el: '#main-course',
        data:{
            isPlus:{{$isPlus != null ? $isPlus : '0'}},
            price: {{$combo->price}},
            jpPrice:{{$combo->jpy_price}},
            extraPrice:{{$combo->extra_price}},
            extraJpPrice:{{$combo->extra_jp_price}},
            totalPrice: '',
            totalJpPrice: '',
            url:''
        },
        mounted() {
            this.setProductPlus()
        },
        methods:{
            //hiển thị giá khi chọn gói plus
            setProductPlus(){
                const vm = this;
                let url = '{!! url('/payment').'?buy='.base64_encode("combo").'&item='.base64_encode($combo->id) !!}';
                vm.totalPrice = vm.formatNumber(vm.price);
                vm.totalJpPrice = vm.formatNumber(vm.jpPrice);
                if (vm.isPlus){
                    url += '&plus=1';
                    vm.totalPrice = vm.formatNumber(vm.price + vm.extraPrice);
                    vm.totalJpPrice = vm.formatNumber(vm.jpPrice + vm.extraJpPrice);
                }
               vm.url = url;
            },
            //hàm in ra tiền tệ
            formatNumber: function (num) {
                return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
            },
        }
    });
  </script>
  <script>(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) return;
  js = d.createElement(s); js.id = id;
  js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));</script>
@stop
