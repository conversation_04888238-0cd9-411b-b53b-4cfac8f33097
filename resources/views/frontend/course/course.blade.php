@extends('frontend._layouts.default')

@section('title')
  @if($course->name == 'N5')
    Tiếng Nh<PERSON>t cho người mới bắt đầu - <PERSON>h<PERSON>a học N5 - Dungmori
  @else
    Họ<PERSON> tiếng Nh<PERSON>t online - <PERSON><PERSON><PERSON><PERSON> họ<PERSON> {{ $course->name }}  - Dungmori
  @endif
@stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber dạy tiếng Nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('cdn/course/default')}}/{{ $course->avatar_name }} @stop
@section('author') DUNGMORI @stop

@section('header-js')
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
@stop

@section('content')

  <script>
    @if($course->name == 'N5')
    $(".cn5").addClass("active");
    @elseif($course->name == 'N4')
    $(".cn4").addClass("active");
    @elseif($course->name == 'N3')
    $(".cn3").addClass("active");
    @elseif($course->name == 'N2')
    $(".cn2").addClass("active");
    @elseif($course->name == 'N1')
    $(".cn1").addClass("active");
    @elseif($course->name == 'Kaiwa')
    $(".kaiwa").addClass("active");
    @elseif($course->name == 'Chuyên Ngành')
    $(".chuyen-nganh").addClass("active");
    @endif
  </script>

  <div id="fb-root"></div>

  <div class="main">
    <div class="main-top">
      @include('frontend.course.course-cta', ['course' => $course, 'unlock' => $unlock, 'firstLesson' => $firstLesson])
    </div>
    <div class="main-center main-course">

    <div class="main-left @if($premium == true) main-left-premium @endif">

        @if(!in_array($course->id, array(8, 9, 10)))
        <h2 class="course-detail-title">JLPT | <b>Khóa {{ $course->name }}
            @if (in_array($course->name, ["Chuyên Ngành"])) miễn phí @endif</b>
        </h2>
        @else
            <h2 class="course-detail-title" style="height: 0"> &nbsp; <b style="color: #105B89;"> &nbsp;</b></h2>

        @endif

        {{-- Hien thi so ngay het han --}}
        @if($premium == true)
            @if($unlock == 0 && $course->price_option != 0)
                <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}" style="margin: 35px 0 0 0;float: right;font-size: 24px;color: #41A336;">
                    <button name="buyCourseButton" id="buyCourseBtn" class="premium-expried buy-this-course">Mua khóa học này <i class="fa fa-cart-plus"></i></button>
                </a>
            @elseif($unlock == 0 && $course->price_option == 0)
            @else
              <div class="premium-expried">
                @if($numberOfDay == -1)
                  <div>Khóa học đã hết hạn</div>
                @elseif($numberOfDay == 0)
                  <div>Khóa học chỉ còn hạn trong hôm nay</div>
                @elseif($numberOfDay > 0)
                  <div>Khóa học còn {{$numberOfDay}} ngày</div>
                @endif
              </div>
            @endif
          @endif


        {{-- nếu là 1 trong 3 khóa zoom --}}
        @if(in_array($course->id, array(27, 28, 29)))

          <style type="text/css">
            .server-localtion {
              background-color: #EDBA70 !important;
            }

            .main .main-course .main-left .course-detail-container .course-price-container {
              background: #fff1e0 !important;
            }

            .course-tab {
              background: #EEE !important;
            }

            .course-tab > .active > a {
              background-color: #D68E24 !important;
            }

            .course-tab > .li-tab > a {
              color: #444 !important;
            }

            .course-list-container > .block-title {
              background: #D68E24;
            }

            .course-list-container .panel-default > .panel-heading a {
              background: #FFE8CD !important;
              color: #D68E24;
            }

            .course-list-container .panel-default > .panel-heading > .group-step-item {
              background: #FFE8CD !important;
            }

            .course-list-container .panel-default .panel-body li a {
              background: #f7f2eb !important;
            }

            .course-list-container .panel-default .panel-body li a:hover {
              background: #f7f2eb !important;
              color: #222;
            }

            .lesson__progress--circle .progressbar-text {
              color: #222 !important;
            }

            .comment-tab {
              border-bottom: 1px solid #D68E24 !important;
            }

            .comment-tab .active > a {
              background-color: #D68E24 !important;
            }

            .list-comments .comment-item .comment-content .name b {
              color: #D68E24 !important;
            }

            .comment-action > a {
              color: #D68E24 !important;
            }

            .child-comment-item .comment-content .child-name b {
              color: #D68E24 !important;
            }

            .list-comments .input-comment-container .post-comment-btn {
              background: #D68E24 !important;
            }

            .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
              background: #D68E24 !important;
            }

            .list-comments .load-more-comment {
              background: #D68E24 !important;
            }

            .main-right {
              margin-top: 20px !important;
            }

            .main .main-course .main-right .course-info-container {
              margin-top: 35px;
            }
          </style>

        @endif

        {{-- nếu là 1 trong 3 khóa eju --}}
        @if(in_array($course->id, array(8, 9, 10)))
          <script type="text/javascript">$(".eju").addClass("active")</script>
          <div class="eju-top-menu" style="margin-top: 20px;">
            <a href="{{url('khoa-hoc/eju')}}" class="eju-course-item @if($course->id == 8) active @endif">
              @if($course->id == 8)
                <img src="{{url('assets/img/e1-active.svg')}}"/>
              @else
                <img src="{{url('assets/img/e1.svg')}}"/>
              @endif
              <span>EJU Tiếng Nhật Luyện Đề</span>
            </a>
            <a href="{{url('khoa-hoc/eju-xhth')}}" class="eju-course-item @if($course->id == 10) active @endif">
              @if($course->id == 10)
                <img src="{{url('assets/img/e2-active.svg')}}"/>
              @else
                <img src="{{url('assets/img/e2.svg')}}"/>
              @endif
              <span>EJU Xã hội TH Luyện Đề</span>
            </a>
            <a href="{{url('khoa-hoc/eju-toan')}}" class="eju-course-item @if($course->id == 9) active @endif">
              @if($course->id == 9)
                <img src="{{url('assets/img/e3-active.svg')}}"/>
              @else
                <img src="{{url('assets/img/e3.svg')}}"/>
              @endif
              <span>EJU Toán Luyện Đề</span>
            </a>
          </div>
          <style type="text/css">
            .server-localtion {
              background-color: #105B89 !important;
            }

            .course-tab {
              background: #77C2EF !important;
            }

            .course-tab > .active > a {
              background-color: #105B89 !important;
            }

            .course-tab > .li-tab > a {
              color: #fff !important;
            }

            .course-list-container > .block-title {
              background: linear-gradient(180deg, #167EBD 0%, #1F94CF 48.44%, #167EBD 100%);
              box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
            }

            .course-list-container .panel-default > .panel-heading a {
              background: #105B89 !important;
            }

            .course-list-container .panel-default > .panel-heading > .group-step-item {
              background: rgb(238, 238, 238) !important;
            }

            .course-list-container .panel-default .panel-body li a {
              background: #77C2EF !important;
            }

            .comment-tab {
              border-bottom: 1px solid #105B89 !important;
            }

            .comment-tab .active > a {
              background-color: #105B89 !important;
            }

            .list-comments .comment-item .comment-content .name b {
              color: #105B89 !important;
            }

            .comment-action > a {
              color: #105B89 !important;
            }

            .child-comment-item .comment-content .child-name b {
              color: #105B89 !important;
            }

            .list-comments .input-comment-container .post-comment-btn {
              background: #105B89 !important;
            }

            .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
              background: #105B89 !important;
            }

            .list-comments .load-more-comment {
              background: #105B89 !important;
            }

            .main-right {
              margin-top: 117px !important;
            }

            .main .main-course .main-right .course-info-container {
              margin-top: 35px;
            }
          </style>
        @endif

        @if (!Auth::check() && (in_array($course->name, ["Chuyên Ngành"])))
            <div class="free-course-box">
                <div class="icon-container">
                    <i class="zmdi zmdi-info"></i>
                </div>
                <div class="content-container">
                    <span>@if($course->name == "Chuyên Ngành") Khóa Chuyên ngành @endif hoàn toàn miễn phí tại Dungmori.com, các bạn có
                        thể học ngay sau khi đăng nhập, hãy
                        <a data-fancybox data-animation-duration="300" data-src="#auth-container"
                           onclick="swichTab('register')" style="cursor: pointer;">Tạo tài khoản</a> hoặc
                        <a data-fancybox data-animation-duration="300" data-src="#auth-container"
                           onclick="swichTab('login')" style="cursor: pointer;">Đăng nhập</a> để học ngay
                    </span>
            </div>
          </div>
        @endif

        <div class="btn-list-lesson-box">
          <button class="btn btn-list-lesson" id="btn-list-lesson" onclick="goToLessonListNow();">
            Bắt đầu học&nbsp;&nbsp;<i class="zmdi zmdi-format-line-spacing"></i>
          </button>
        </div>

        {{-- nếu là 1 trong 3 khóa eju  --}}
        @if(in_array($course->id, array(8, 9, 10)))
          <img style="margin-top: 20px; border: 1px solid #035096;" src="{{url('assets/img/eju.png')}}"/>
        @endif

        @if(in_array($course->id, array(27, 28, 29)))
          <div class="free-course-box" style="margin-top: 0;">
            <div class="content-container">
              <i class="zmdi zmdi-info" style="font-size: 15px; padding: 0 0 0 15px;"></i> <span>Khóa học chỉ dành cho học viên Zoom hoặc kích hoạt N1|N2|N3 từ ngày 23/01/2021</span>
            </div>
          </div>
        @endif

        <div class="cover-container">

          @if($course->name == 'N3')
            <a href="{{url('khoa-hoc/bo-tro-on-tap-kien-thuc-n4')}}"> <img style="margin-bottom: 5px;"
                                                                           src="{{url('assets/img/on-tap-n4.png')}}">
            </a>
          @elseif($course->name == 'N2')
            <a href="{{url('khoa-hoc/bo-tro-on-tap-kien-thuc-n3')}}"> <img style="margin-bottom: 5px;"
                                                                           src="{{url('assets/img/on-tap-n3.png')}}">
            </a>
          @elseif($course->name == 'N1')
            <a href="{{url('khoa-hoc/bo-tro-on-tap-kien-thuc-n2')}}"> <img style="margin-bottom: 5px;"
                                                                           src="{{url('assets/img/on-tap-n2.png')}}">
            </a>
          @endif
          <div class="movie-play">
            <img src="{{url('cdn/course/default')}}/{{ $course->avatar_name }}"/>
            <br>
            @include('frontend.course.play-button')
          </div>
          <?php preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $course->link, $match); ?>
          <iframe id="iframe-youtube" style="display: none;" width="100%" height="395" frameborder="0" gesture="media"
                  allow="encrypted-media" allowfullscreen
                  src="https://www.youtube.com/embed/{{ $match[1] }}?rel=0"></iframe>

          <div class="course-info-container course-info-status-mobile">

            @if($unlock == 0 && $course->price != 0)
              <div class="buy-item">
                <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                  <div class="buy-btn">Mua khóa học này</div>
                </a>
              </div>
            @elseif($unlock == 0 && $course->price == 0)
              <div class="buy-item">
                <div class="buy-btn bought">Miễn phí</div>
              </div>
            @else
              <div class="buy-item">
                <div class="buy-btn bought">Bạn đã mua <i class="zmdi zmdi-check-circle"></i></div>
              </div>
            @endif

          </div>

          <script type="text/javascript">

            {{-- sự kiện click vào chạy video youtube --}}
            $('.movie-play').on('click', function (ev) {
              $(".movie-play").css("display", "none");
              $("#iframe-youtube").css("display", "block");
              $("#iframe-youtube")[0].src += "&autoplay=1";
              ev.preventDefault();
            });

          </script>
          <div class="msg-fb-box">
            @includeIf('frontend.course.components.fb_btn_message')
          </div>
        </div>
        <ul class="nav nav-pills course-tab">
          <li class="li-tab intro-tab active"><a data-toggle="pill" href="#intro-content">Giới thiệu khóa học</a></li>
          <li class="li-tab preview-tab"><a data-toggle="pill" href="#preview-content">Xem thử các bài giảng</a></li>
        </ul>

        <div class="tab-content">
            <div id="intro-content" class="tab-pane fade in active">
                <div class="course-detail-container">
                    <div class="course-price-container">
                        <div class="info">Học phí: <b>
                          @if(in_array($course->id, array(8, 9, 10)))
                            <strike style="opacity:0.6;">2,090,000 ₫</strike> &nbsp;
                          @endif
                          {{ number_format($course->price) }}
                        </b> ₫ ( {{ number_format($courseJpPrice) }} ¥ ) </div>
                        <div class="info">Thời gian: {!! json_decode($course->stats_data)->time !!}
                        tháng kể từ ngày kích hoạt</div>
                        <div class="info">Khóa học bao gồm <b> {!! json_decode($course->stats_data)->lesson !!}</b> bài học với
                            <b>{!! json_decode($course->stats_data)->video !!}</b> videos bài giảng</div>
                        <div class="info">Giảng viên: {{ $course->getAuthorName() }}</div>
                        <div class="info">Mô tả: {!! $course->brief !!}</div>
                    </div>
                </div>
            </div>
            <div id="preview-content" class="tab-pane fade">
                <div class="preview-course-container">
                    @foreach($previewLessons as $item)
                    <div class="course-item">
                      <div class="images">
                          <a href="{{url('khoa-hoc')}}/{{ $course->SEOurl }}/{{ $item->id }}-{{ $item->SEOurl }}">
                            <img src="{{url('cdn/lesson/small')}}/{{$item->avatar_name}}">
                          </a>
                      </div>
                      <div class="info">
                          <div class="title">
                              <a href="{{url('khoa-hoc')}}/{{ $course->SEOurl }}/{{ $item->id }}-{{ $item->SEOurl }}">{{$item->name}}</a>
                          </div>
                      </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="comment-container" id="comment-container">
          <ul class="nav nav-pills comment-tab">
            <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a>
            </li>
            <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng
                facebook</a></li>
          </ul>
          <div class="tab-content">
            <div id="user-comment-content" class="tab-pane fade in active">

              @if(Auth::check())
                <comments meid="{{ Auth::user()->id }}"
                          avatar="{{ Auth::user()->avatar }}"
                          tbid="{{ $course->id }}"
                          tbname="course"
                          num-posts="15"
                          background="#fff"
                          ref="comment">
                </comments>
              @else
                <comments tbid="{{ $course->id }}"
                          tbname="course"
                          num-posts="15"
                          background="#fff"
                          ref="comment">
                </comments>
              @endif

            </div>
            <div id="facebook-comment-content" class="tab-pane fade">
              <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
            </div>
          </div>
        </div>
      </div>
      {{-- end of main left --}}

      {{-- khóa n1, n2, n3 tiến trình kiểu premium --}}
      @if($premium == true)
        <div class="main-right-premium">
            @if($unlock == 1 && in_array($course->id, array(3,4,5,16,17)))
              <div class="adventure-toggle">
                <h4>Thiết lập lộ trình học</h4>
                {{-- chỉ bật cho n3 n2 n1 --}}
                <a class="gear-option" data-fancybox data-animation-duration="300"
                   data-src="#roadmap-container">
                  <img src="{{url('assets/img/gear.png')}}"/>
                </a>
              </div>
            @endif
            {{--            @if($unlock == 1 && in_array($course->id, array(3,16,17)))--}}
            {{--              --}}{{-- chỉ bật cho n3 n2 n1 --}}
            {{--              <a class="gear-option" data-fancybox data-animation-duration="300" data-src="#roadmap-container">--}}
            {{--                <img src="{{url('assets/img/gear.png')}}"/>--}}
            {{--              </a>--}}
            {{--            @endif--}}
            @includeIf('frontend.course.components.roadmap')
          <div class="course-list-premium">
            @if(Auth::check())
              <course-group-premium
                  :price="{{$course->price}}"
                  :categories="{{$categories}}"
                  :groups="{{$groups}}"
                  :lprogress="{{$lessonProgress}}"
                  :lessons="{{$lessons}}"
                  :courseurl="'{{$course->SEOurl}}'"
                  :auth="1" :ul="{{$unlock}}">
              </course-group-premium>
            @else
              <course-group-premium
                  :categories="{{$categories}}"
                  :groups="{{$groups}}"
                  :lessons="{{$lessons}}"
                  :courseurl="'{{$course->SEOurl}}'"
                  :ul="{{$unlock}}">
              </course-group-premium>
            @endif
          </div>
        </div>

        {{-- nếu là các khóa thường --}}
      @else
        <div class="main-right">

          <div class="course-info-container course-info-status-pc">

            {{-- //nếu ko nằm trong khóa zoom --}}
            @if(!in_array($course->id, array(27, 28, 29)))
              @if($unlock == 0 && $course->price != 0)
                <div class="buy-item">
                  <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                    <div class="buy-btn">Mua khóa học này</div>
                  </a>
                </div>
              @elseif($unlock == 0 && $course->price == 0)
                <div class="buy-item">
                  <div class="buy-btn bought">Miễn phí</div>
                </div>
              @else
                <div class="buy-item">
                  <div class="buy-btn bought">Bạn đã mua <i class="zmdi zmdi-check-circle"></i></div>
                </div>
              @endif
            @endif
          </div>

          {{-- Hien thi so ngay het han --}}
          <div class="numberOfDay">
            @if($numberOfDay == -1)
              <div>Khóa học đã hết hạn</div>
            @elseif($numberOfDay == 0)
              <div>Khóa học chỉ còn hạn trong hôm nay</div>
            @elseif($numberOfDay > 0)
              <div>Khóa học còn {{$numberOfDay}} ngày ✔</div>
            @endif
          </div>

          @if(!in_array($course->id, array(27, 28, 29)))
            <a href="{{url('/bang-gia')}}">
              <div class="see-more">Xem thêm các khóa học khác <i class="zmdi zmdi-long-arrow-right"></i></div>
            </a>
          @endif


          <script type="text/javascript">


            function cancel() {
              $.fancybox.close();
            }

            // xem tiếp bài học dở
            function openLastLesson(url) {
              window.location.href = url;
            }

            //đóng popup xem tiếp : đóng popup và xóa cookie
            function closeLastLessonPopup() {
              $.fancybox.close();
              setCookie('last_lesson', '', 0);
            }

          </script>

          <div class="course-list-container" id="course-list-pc">
            <div class="block-title" id="lesson-list-detail"> Tiến trình học</div>

            @if(Auth::check())
              <course-group :price="{{$course->price}}" :lessonprogress="{{$lessonProgress}}" :groups="{{$groups}}"
                            :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :auth="1" :ul="{{$unlock}}"
                            :type="'pc'"></course-group>
            @else
              <course-group :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'"
                            :ul="{{$unlock}}" :type="'pc'"></course-group>
            @endif

          </div>
        </div>
        {{-- end of main right --}}
      @endif

    </div>
  </div>

  <script type="text/javascript"> $(".fancybox").fancybox().trigger('click'); </script>

  @section('fixed-panel')

    <a href="{{url('/bang-gia')}}">

    </a>

  @stop

  @section('footer-js')
    <script type="text/javascript"> new Vue({el: '#comment-container'}); </script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>

    <script src="{{asset('plugin/progressbar/dist/progressbar.js')}}"></script>

    @if($premium == true)
      <script type="text/javascript"> new Vue({el: '.course-list-premium'}); </script>
      <link href="{{asset('plugin/loading-bar/loading-bar.min.css')}}" rel="stylesheet" type="text/css">
      <script src="{{asset('plugin/loading-bar/loading-bar.min.js')}}"></script>
    @else
      <script src="{{asset('plugin/progressbar/dist/progressbar.js')}}"></script>
      <script type="text/javascript"> new Vue({el: '#course-list-pc'}); </script>
    @endif

    @if($checkOwner && $checkOwner->period_id == null)
      <script type="text/javascript">
        var first_time_show_popup = getCookie("first_time_show_popup");
        if (first_time_show_popup == null || first_time_show_popup == 0) {

          setTimeout(function () {
            $(".gear-option").click();
            setCookie("first_time_show_popup", 1, 60);
          }, 2000);
        }
      </script>
    @endif

    {{-- <script>
      function goToLessonListNow(id){
        $('html, body').animate({
          scrollTop: $("#lesson-list-detail").offset().top-50
        }, 1100);
      }
      $( document ).ready(function() {
        function changeColorBtnListCourse() {
          var btn = $('#btn-list-lesson');
          if (btn.css('borderColor') == "rgb(255, 0, 0)") {
            btn.css('color', 'rgb(0, 0, 255)');
            btn.css('border-color', 'rgb(0, 0, 255)');
          } else if (btn.css('borderColor') == "rgb(0, 0, 255)") {
            btn.css('color', 'rgb(94, 152, 0)');
            btn.css('border-color', 'rgb(94, 152, 0)');
          } else if (btn.css('borderColor') == "rgb(94, 152, 0)") {
            btn.css('color', 'rgb(231, 117, 0)');
            btn.css('border-color', 'rgb(231, 117, 0)');
          } else if (btn.css('borderColor') == "rgb(231, 117, 0)") {
            btn.css('color', 'rgb(64, 0, 111)');
            btn.css('border-color', 'rgb(64, 0, 111)');
          } else {
            btn.css('color', 'rgb(255, 0, 0)');
            btn.css('border-color', 'rgb(255, 0, 0)');
          }
        }
        setInterval(changeColorBtnListCourse, 500);
      });
    </script> --}}

    <script>(function (d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s);
        js.id = id;
        js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1768213996826394';
        fjs.parentNode.insertBefore(js, fjs);
      }(document, 'script', 'facebook-jssdk'));</script>
  @stop
@stop
