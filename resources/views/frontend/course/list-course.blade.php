@extends('frontend._layouts.default')
@section('title')
    {{--    <PERSON><PERSON><PERSON><PERSON> họ<PERSON> {{ $course->name }} - Dungmori--}}
@stop
@section('description')
    Dungmori YouTuber d<PERSON>y tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất
@stop
@section('keywords')
    Dungmori YouTuber d<PERSON><PERSON> tiếng Nhật với phong cách gần gũi dễ hiểu nhất
@stop
{{--@section('image'){{ url('cdn/course/default') }}/{{ $course->avatar_name }} @stop--}}
@section('author')
    DUNGMORI
@stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link href="https://vjs.zencdn.net/8.16.1/video-js.css" rel="stylesheet"/>
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
@stop
@section('content')

    <div id="container-lesson">
        <template v-if="statusAnswer !== 'result'">
            <div class="relative font-beanbag">
                <!-- Overlay -->
                <div id="popupOverlay" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden z-[1]"></div>

                <!-- Popup Giải thích -->
                <div id="explanationPopup"
                     :class="`${isAnswerSuccess ? 'bg-[#F0FFF1] ' : 'bg-[#FEE9E7]'}  z-[2] fixed bottom-0 max-w-5xl left-0 right-0 bg-[#F0FFF1] rounded-t-lg shadow-lg transform translate-y-full transition-transform duration-300 ease-in-out p-6 mx-auto hidden`">
                    <div class="grid grid-cols-6 gap-4">
                        <!-- Icon -->
                        <div class="col-start-2 col-span-4">
                            <div class="flex justify-content-space-between">
                                <div class="text-green-500 flex align-items-center">
                                    <div id="iconResultExplanationPopup">
                                        <svg v-if="isAnswerSuccess" width="28" height="28" viewBox="0 0 28 28"
                                             fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="14" cy="14" r="14" fill="#57D061"/>
                                            <path d="M19.3337 10L12.0003 17.3333L8.66699 14" stroke="#EBFFEE"
                                                  stroke-width="3"
                                                  stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <svg v-else width="28" height="28" viewBox="0 0 28 28" fill="none"
                                             xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="14" cy="14" r="14" fill="#FF7C79"/>
                                            <g clip-path="url(#clip0_87_1918)">
                                                <path d="M8 20L20 8" stroke="white" stroke-width="3"
                                                      stroke-linecap="round"
                                                      stroke-linejoin="round"/>
                                                <path d="M20 20L8 8" stroke="white" stroke-width="3"
                                                      stroke-linecap="round"
                                                      stroke-linejoin="round"/>
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_87_1918">
                                                    <rect width="16" height="16" fill="white"
                                                          transform="translate(6 6)"/>
                                                </clipPath>
                                            </defs>
                                        </svg>
                                    </div>

                                    <p id="textResultExplanationPopup"
                                       :class="` ${isAnswerSuccess ? 'text-[#57D061] ' : 'text-[#FF7C79] '} ml-2 text-[#57D061] font-semibold`">
                                        @{{ isAnswerSuccess ? 'Chính xác' : 'Không chính xác' }}
                                    </p>
                                </div>

                                <button v-if="isShowExplainIconAudio" class="text-blue-600 focus:outline-none"
                                        id="buttonAudio" onclick="playAudio()">
                                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <rect x="1" y="1" width="38" height="38" rx="19" stroke="#4E87FF"
                                              stroke-width="2"/>
                                        <path d="M26.0003 24.75C25.8403 24.75 25.6903 24.7 25.5503 24.6C25.2203 24.35 25.1503 23.88 25.4003 23.55C26.9703 21.46 26.9703 18.54 25.4003 16.45C25.1503 16.12 25.2203 15.65 25.5503 15.4C25.8803 15.15 26.3503 15.22 26.6003 15.55C28.5603 18.17 28.5603 21.83 26.6003 24.45C26.4503 24.65 26.2303 24.75 26.0003 24.75Z"
                                              fill="#4E87FF"/>
                                        <path d="M27.8304 27.25C27.6704 27.25 27.5204 27.2 27.3804 27.1C27.0504 26.85 26.9804 26.38 27.2304 26.05C29.9004 22.49 29.9004 17.51 27.2304 13.95C26.9804 13.62 27.0504 13.15 27.3804 12.9C27.7104 12.65 28.1804 12.72 28.4304 13.05C31.5004 17.14 31.5004 22.86 28.4304 26.95C28.2904 27.15 28.0604 27.25 27.8304 27.25Z"
                                              fill="#4E87FF"/>
                                        <path opacity="0.4"
                                              d="M23.75 15.41V24.59C23.75 26.31 23.13 27.6 22.02 28.22C21.57 28.47 21.07 28.59 20.55 28.59C19.75 28.59 18.89 28.32 18.01 27.77L15.09 25.94C14.89 25.82 14.66 25.75 14.43 25.75H13.5V14.25H14.43C14.66 14.25 14.89 14.18 15.09 14.06L18.01 12.23C19.47 11.32 20.9 11.16 22.02 11.78C23.13 12.4 23.75 13.69 23.75 15.41Z"
                                              fill="#4E87FF"/>
                                        <path d="M13.5 14.25V25.75H13C10.58 25.75 9.25 24.42 9.25 22V18C9.25 15.58 10.58 14.25 13 14.25H13.5Z"
                                              fill="#4E87FF"/>
                                    </svg>
                                </button>
                                <audio id="audio" src=""></audio>
                            </div>
                        </div>

                        <div class="col-start-2 col-span-4">
                            <div id="content_explain" class="mt-2 text-lg font-medium text-gray-800">
                                リンさんは　ハノイ（B)だいがく　の　学生です。
                            </div>
                        </div>
                    </div>
                    <button @click="hideExplanationPopup()"
                            class="bg-white text-gray-500 hover:text-gray-700 focus:outline-none absolute top-[14px] right-[15px] rounded-full">
                        <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <g id="Menu / Close_SM">
                                <path id="Vector" d="M16 16L12 12M12 12L8 8M12 12L16 8M12 12L8 16" stroke="#000000"
                                      stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </g>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Main Content Section -->
            <!-- Progress Bar -->
            <div class="bg-white shadow-sm py-2 mt-4 font-beanbag">
                <div class="container mx-auto flex items-center justify-between">
                    <div class="text-sm text-gray-500" id="progress_text">
                        0/{{ $contentLesson->component->count() }}</div>
                    <div class="w-full mx-4 bg-gray-300 rounded-full border ml-5 mr-5" style="height: 6px">
                        <div class="bg-green-500 rounded-full border-green-500" id="progress_bar"
                             style="width: 0%; height: 5px; background-color: #2CD868"></div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 22V14M5 14V4M5 14L7.47067 13.5059C9.1212 13.1758 10.8321 13.3328 12.3949 13.958C14.0885 14.6354 15.9524 14.7619 17.722 14.3195L17.8221 14.2945C18.4082 14.148 18.6861 13.4769 18.3753 12.9589L16.8147 10.3578C16.4732 9.78863 16.3024 9.50405 16.2619 9.19451C16.2451 9.06539 16.2451 8.93461 16.2619 8.80549C16.3024 8.49595 16.4732 8.21137 16.8147 7.64221L18.0932 5.51132C18.4278 4.9536 17.9211 4.26972 17.2901 4.42746C15.8013 4.79967 14.2331 4.69323 12.8082 4.12329L12.3949 3.95797C10.8321 3.33284 9.1212 3.17576 7.47067 3.50587L5 4M5 4V2"
                                  stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main class="container mx-auto mt-8 flex-1 font-beanbag">
                <div class="bg-[#F4F5FA] shadow-md rounded-lg p-6">
                    <!-- Lesson Title -->
                    <div class="flex justify-content-space-between align-items-center">
                        <div class="mb-2 font-averta-semibold">
                            <h1 class="text-[18px] font-bold text-black">{{ $contentLesson->name }}</h1>
                            <p id="type_question" class="text-sm mt-2 text-black font-averta-regular">
                                @{{ currentAnswer.type === 13 ? 'Điền từ vào chỗ trống' : 'Chọn đáp án đúng' }}
                            </p>
                        </div>
                        <div class="relative group">
                            <button id="button_suggest"
                                    v-if="(currentAnswer.type === 13 && currentAnswer.value.suggest != null) || (currentAnswer.type === 3 && currentAnswer.suggest != null)"
                                    class="bg-[#F4F5FA] w-auto text-[14px] text-[#57D061] border-[2px] border-[#57D061] text-bold px-4 py-1 rounded-full mr-2.5 transform -translate-x-10 transition duration-500 ease-in-out">
                                <svg width="16" height="14" viewBox="0 0 16 14" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <path opacity="0.4"
                                          d="M3.73935 9.33317C3.81101 9.04734 3.68071 8.639 3.45268 8.43484L1.8695 7.01734C1.37435 6.574 1.17889 6.1015 1.32222 5.69317C1.47207 5.28484 1.93465 5.00484 2.62526 4.89984L4.65798 4.5965C4.95116 4.54984 5.3095 4.3165 5.44632 4.07734L6.56692 2.06484C6.89268 1.48734 7.33571 1.1665 7.81783 1.1665C8.29995 1.1665 8.74298 1.48734 9.06874 2.06484L10.1893 4.07734C10.274 4.229 10.45 4.37484 10.6389 4.474L3.62207 10.7565C3.53086 10.8382 3.3745 10.7623 3.40056 10.6457L3.73935 9.33317Z"
                                          fill="#57D061"/>
                                    <path d="M12.1832 8.4351C11.9487 8.6451 11.8184 9.0476 11.8966 9.33344L12.3461 11.0893C12.535 11.8184 12.4178 12.3668 12.0138 12.6293C11.8509 12.7343 11.6555 12.7868 11.4275 12.7868C11.0952 12.7868 10.7043 12.6759 10.2743 12.4484L8.36534 11.4334C8.06564 11.2759 7.57049 11.2759 7.2708 11.4334L5.36186 12.4484C4.63867 12.8276 4.01974 12.8918 3.62231 12.6293C3.47246 12.5301 3.36171 12.3959 3.29004 12.2209L11.2125 5.1276C11.5122 4.85927 11.9356 4.73677 12.3461 4.80094L13.0041 4.9001C13.6947 5.0051 14.1573 5.2851 14.3072 5.69344C14.4505 6.10177 14.255 6.57427 13.7599 7.0176L12.1832 8.4351Z"
                                          fill="#57D061"/>
                                </svg>
                                Gợi ý
                            </button>
                            <!-- Tooltip -->
                            <div :class="`min-w-[200px] max-w-[700px] absolute right-[150%] top-1/2 transform -translate-y-1/2 rounded-full text-black text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 overflow-hidden`">
                                <div class="flex items-center rounded-full">
                                    <div
                                            :class="`px-4 py-2 bg-[#57D0618C] bg-green-300 rounded-full`"
                                            :style="{width: renderWidthTooltipSuggest(currentAnswer) }"
                                            v-html="currentAnswer.type === 13 ? currentAnswer.value.suggest : currentAnswer.suggest">
                                    </div>
                                    <div class="top right-[-9px] top-[50%]">
                                        <svg fill="#57D0618C" width="12" height="12" viewBox="8 -2.56 37.12 37.12"
                                             version="1.1"
                                             xmlns="http://www.w3.org/2000/svg" stroke="#57D0618C"
                                             transform="matrix(1, 0, 0, 1, 0, 0)rotate(0)" stroke-width="0.00032">
                                            <g id="SVGRepo_iconCarrier"><title>play</title>
                                                <path d="M5.92 24.096q0 1.088 0.928 1.728 0.512 0.288 1.088 0.288 0.448 0 0.896-0.224l16.16-8.064q0.48-0.256 0.8-0.736t0.288-1.088-0.288-1.056-0.8-0.736l-16.16-8.064q-0.448-0.224-0.896-0.224-0.544 0-1.088 0.288-0.928 0.608-0.928 1.728v16.16z"/>
                                            </g>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @foreach($contentLesson->component as $key => $component)
                        <div id="wrap-question-{{ $component->id }}"
                             class="wrap-question {{ !$loop->first ? 'hidden' : '' }}"
                             data-id="{{ $component->id }}">
                            @if($component->type === 3)
                                <!-- Question -->
                                <div :class="`mt-6 mb-15 text-center ${ checkAnswerAudio({{ $component }}) != 1 ? 'max-w-[695px]' : ''} mx-auto flex justify-content-center align-items-center font-gen-jyuu-gothic text-black`">
                                    <template v-if="checkAnswerAudio({{ $component }}) == 1">
                                        <div class="bg-white shadow-md rounded-3xl px-[18px] py-[21px] w-full">
                                            <!-- Icon âm thanh -->
                                            <div class="flex">
                                                <svg @click="toggleAudio({{ $component }})" class="cursor-pointer"
                                                     width="48" height="48" viewBox="0 0 48 48" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                          fill="#4E87FF"/>
                                                    <path d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                          fill="#4E87FF"/>
                                                    <path opacity="0.4"
                                                          d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                          fill="#4E87FF"/>
                                                    <path d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                          fill="#4E87FF"/>
                                                </svg>
                                            </div>

                                            <!-- Văn bản Furigana -->
                                            <div class="flex-1 text-center text-gray-800 mt-2"
                                                 v-html="getTagImg({{ $component }})">
                                            </div>
                                        </div>
                                    </template>
                                    <template v-else-if="checkAnswerAudio({{ $component }}) == 2">
                                        <button data-audioButton="audioButton-{{ $component->id }}"
                                                ref="audioButton-{{ $component->id }}"
                                                @click="toggleAudio({{ $component }})"
                                                :class="['flex items-center justify-center w-[100px] h-[100px] rounded-full bg-white text-blue-500 hover:bg-blue-100 flex-col', isPlaying ? 'animate-blink' : 'shadow-[0_0_15px_4px] shadow-[#4E87FF80]']"
                                        >
                                            <!-- Icon loa -->
                                            <div class="w-[50px] h-[50px] bg-blue-500 rounded-full flex items-center justify-center">
                                                <svg width="100px" height="100px" viewBox="0 0 45 44" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M33.7505 30.7083C33.4505 30.7083 33.1693 30.6166 32.9068 30.4333C32.288 29.975 32.1568 29.1133 32.6255 28.5083C35.5693 24.6766 35.5693 19.3233 32.6255 15.4916C32.1568 14.8866 32.288 14.025 32.9068 13.5666C33.5255 13.1083 34.4068 13.2366 34.8755 13.8416C38.5505 18.645 38.5505 25.355 34.8755 30.1583C34.5943 30.525 34.1818 30.7083 33.7505 30.7083Z"
                                                          fill="#4E87FF" fill-opacity="0.5"
                                                          :class="{ 'animate-opacity': isPlaying }"/>
                                                    <path d="M37.1822 35.2917C36.8822 35.2917 36.6009 35.2001 36.3384 35.0167C35.7197 34.5584 35.5884 33.6967 36.0572 33.0917C41.0634 26.5651 41.0634 17.4351 36.0572 10.9084C35.5884 10.3034 35.7197 9.44172 36.3384 8.98339C36.9572 8.52505 37.8384 8.65339 38.3072 9.25839C44.0634 16.7567 44.0634 27.2434 38.3072 34.7417C38.0447 35.1084 37.6134 35.2917 37.1822 35.2917Z"
                                                          fill="#4E87FF" fill-opacity="0.5"
                                                          :class="{ 'animate-opacity-second': isPlaying }"/>
                                                    <path opacity="0.4"
                                                          d="M29.5312 13.585V30.415C29.5312 33.5683 28.3688 35.9333 26.2875 37.07C25.4438 37.5283 24.5063 37.7483 23.5312 37.7483C22.0312 37.7483 20.4188 37.2533 18.7688 36.245L13.2938 32.89C12.9188 32.67 12.4875 32.5417 12.0562 32.5417H10.3125V11.4583H12.0562C12.4875 11.4583 12.9188 11.33 13.2938 11.11L18.7688 7.75499C21.5063 6.08666 24.1875 5.79333 26.2875 6.92999C28.3688 8.06666 29.5312 10.4317 29.5312 13.585Z"
                                                          fill="#4E87FF"/>
                                                    <path d="M10.3125 11.4583V32.5417H9.375C4.8375 32.5417 2.34375 30.1033 2.34375 25.6667V18.3333C2.34375 13.8967 4.8375 11.4583 9.375 11.4583H10.3125Z"
                                                          fill="#4E87FF"/>
                                                </svg>
                                            </div>
                                            <!-- Thời gian -->
                                            <span class="text-[#4E87FF] text-sm font-averta-bold text-blue-500">@{{ formattedTime }}</span>
                                        </button>
                                    </template>
                                    <template v-else class="">
                                        {!! $component->value !!}
                                    </template>

                                    {{--                            @if(false)--}}
                                    {{--                                <p class="text-lg font-semibold">このことばは　ひらがなで　どう　かきますか。</p>--}}
                                    {{--                                <p class="text-sm text-gray-500">--}}
                                    {{--                                    1・2・3・4から　いちばん　いいものを　ひとつえらんで　ください。</p>--}}
                                    {{--                            @elseif(true)--}}
                                    {{--                                <div class="video">--}}
                                    {{--                                    <video id="my-video" class="video-js w-[50%] h-[352px]" controls preload="auto"--}}
                                    {{--                                           poster="https://web-test.dungmori.com/cdn/lesson/default/1729757832_118153995_79687.jpeg"--}}
                                    {{--                                           data-setup="{}">--}}
                                    {{--                                        <source--}}
                                    {{--                                                src="https://tokyo-v2.dungmori.com/720p/XHTH-2022-01-3.mp4/index.m3u8"--}}
                                    {{--                                                type="application/x-mpegURL"/>--}}
                                    {{--                                    </video>--}}
                                    {{--                                </div>--}}
                                    {{--                            @endif--}}
                                </div>

                                <!-- Options -->
                                <div id="options" class="grid grid-cols-2 gap-4 mb-15 max-w-[695px] mx-auto">
                                    @foreach($component->answers as $keyAnswer => $answer)
                                        <template v-if="checkAnswerAudio({{ $answer }}) == 3">
                                            <label class="block">
                                                <input id="{{ $answer->id }}" type="radio"
                                                       data-result="{{ $answer->grade }}"
                                                       name="answer" value="{{ $answer->value }}" class="hidden"
                                                       {{--                                                   onclick="enableCheckButton(this)"--}}
                                                       @click="selectOption({{ $answer }})">
                                                <span id="span-option-{{ $answer->id }}"
                                                      data-question="{{ $component->id }}"
                                                      data-answer="{{ $answer->id }}"
                                                      data-result="{{ $answer->grade }}"
                                                      class="text-bold span-option p-[12px] bg-white border-[#F4F5FA] border-[4px] rounded-full cursor-pointer flex align-items-center text-[#07403F]">
                                                <span>
                                                    {{ chr(65 + $keyAnswer) }}.
                                                </span>
                                                <span class="ml-2 text-black font-gen-jyuu-gothic w-full text-center">
                                                    <svg class="cursor-pointer"
                                                         width="32" height="32" viewBox="0 0 48 48" fill="none"
                                                         xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                              fill="#4E87FF"/>
                                                        <path d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                              fill="#4E87FF"/>
                                                        <path opacity="0.4"
                                                              d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                              fill="#4E87FF"/>
                                                        <path d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                              fill="#4E87FF"/>
                                                    </svg>
                                                </span>
                                            </span>
                                            </label>
                                        </template> <!-- Đáp án là audio -->
                                        <template v-else>
                                            <label class="block">
                                                <input id="{{ $answer->id }}" type="radio"
                                                       data-result="{{ $answer->grade }}"
                                                       name="answer" value="{{ $answer->value }}" class="hidden"
                                                       @click="selectOption({{ $answer }})">
                                                <span id="span-option-{{ $answer->id }}"
                                                      data-question="{{ $component->id }}"
                                                      data-answer="{{ $answer->id }}"
                                                      data-result="{{ $answer->grade }}"
                                                      :class="`max-h-[161px] span-option p-4 bg-white border-[#F4F5FA] border-[4px] rounded-${checkAnswerAudio({{ $answer }}) === 4 ? '2xl' : 'full'} cursor-pointer flex align-items-flex-${checkAnswerAudio({{ $answer }}) === 4 ? 'start' : 'end'} text-[#07403F]`">
                                                <span class="text-bold">
                                                    {{ chr(65 + $keyAnswer) }}.
                                                </span>
                                                <span class="ml-2 text-black font-averta-semibold font-normal">
                                                    {!! $answer->value !!}
                                                </span>
                                            </span>
                                            </label>
                                        </template>

                                    @endforeach
                                </div>
                            @elseif($component->type === \App\Http\Models\LessonToTask::TYPE_FILL_IN_THE_BLANK)
                                <!-- Question box -->
                                <div class="grid align-items-center w-full max-w-[80%] min-h-[300px] bg-white rounded-3xl shadow-lg p-6 text-center mx-auto">
                                    <div class="text-2xl font-bold flex justify-content-center flex-wrap items-baseline">
                                        @foreach($component->value['question'] as $keyQuestion => $question)
                                            @if($question['type'] === "default")
                                                <div class="my-2 ml-[6px] missing-word-wrap">
                                                    {!! $question['value'] !!}
                                                </div>
                                            @else
                                                <span class="ml-[6px] my-2">
                                            <!-- Input field -->
                                                <input type="text"
                                                       id="missingWord-{{ $keyQuestion }}-{{ $component->id }}"
                                                       data-id="{{ $component->id }}"
                                                       data-index="{{ $keyQuestion }}"
                                                       data-result="{{ $question['result'] }}"
                                                       class="missingWord missingWord-{{ $component->id }} outline-none min-w-[73px] rounded-[12px] bg-[#D9D9D9] text-center h-12 font-bold text-[16px]"
                                                       @input="checkInput">
                                            </span>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endforeach


                    <!-- Submit Button -->
                    <div class="mt-8 text-center mb-15 flex max-w-[695px] mx-auto">
                        <div id="explanationButton" v-if="statusAnswer == 'next' && isShowExplainButton">
                            <Transition name="slide-fade">
                                {{--                            <button v-if="" id="explanation" @click="showExplanationPopup()"--}}
                                {{--                                    class="text-[12px] text-[#57D061] border-[2px] border-[#57D061] bg-white text-bold px-6 py-2 rounded-full mr-2.5 w-[151px] opacity-0 transform -translate-x-10 transition duration-500 ease-in-out">--}}
                                {{--                                GIẢI THÍCH--}}
                                {{--                            </button>--}}
                                <button id="explanation" @click="showExplanationPopup()"
                                        class="text-[12px] text-[#57D061] border-[2px] border-[#57D061] bg-white text-bold px-6 py-2 rounded-full mr-2.5 w-[151px]">
                                    GIẢI THÍCH
                                </button>
                            </Transition>
                        </div>

                        <button id="checkButton" @click="actionCheckButton()" disabled
                                :class="`px-6 py-2 rounded-full text-sm w-full translate-x-0 scale-100 transition-all duration-300 ease-in-out ${statusAnswer !== 'doing' ? ' drop-shadow-2xl text-[#07403F] bg-[#57D061]' : ' text-[#B3B3B3] bg-[#D9D9D9]'}`">
                            @{{ statusAnswer === 'end' ? 'NỘP BÀI' : (statusAnswer !== 'next' ? 'KIỂM TRA' : 'LÀM TIẾP')
                            }}
                        </button>
                    </div>
                </div>
            </main>
            {{--    {{ dd($contentLesson->toArray()) }}--}}
        </template>
        <template v-else>
            <main class="container mx-auto pt-8 flex-1 font-beanbag bg-[#F4F5FA]">
                <div class="rounded-lg p-6">
                    <!-- Lesson Title -->
                    <div class="flex justify-content-space-between align-items-center">
                        {{--                                {{ dd($contentLesson) }}--}}
                        <div class="mb-2 font-averta-semibold">
                            <h1 class="text-[18px] font-bold text-[#212121]">{{ $contentLesson->name }}</h1>
                        </div>
                    </div>
                </div>
                <div class="text-center font-beanbag  text-[#07403F] text-[24px] uppercase">
                    <div class="text-center mb-[12px]">
                        <h3>
                            @{{ userResult.correct_answer }}/@{{ userResult.total_answer }} CÂU ĐÚNG!
                        </h3>
                    </div>
                    <div class="text-center">
                        <h3>
                            @{{ (userResult.correct_answer/userResult.total_answer) > 0.85 ? 'Bạn là cao thủ từ vựng!' :
                            'Cố gắng thêm nhé!' }}
                        </h3>
                    </div>
                    <div>
                        <template v-if="(userResult.correct_answer/userResult.total_answer) > 0.85">
                            <svg width="538" height="248" viewBox="0 0 538 248" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_1448_6581)">
                                    <path d="M163.728 101.915C164.759 98.052 162.929 92.3096 158.723 87.3273C157.797 86.2506 156.725 85.3428 155.568 84.625C156.935 84.5194 158.302 84.2239 159.606 83.6749C165.621 81.2049 169.89 76.9403 170.753 73.0557C168.25 72.1691 166.588 71.1979 166.588 71.1979C166.588 71.1979 167.114 69.3401 168.271 66.9545C164.927 64.78 158.912 64.7588 152.897 67.2289C151.593 67.7778 150.415 68.5167 149.364 69.4034C149.679 68.0734 149.826 66.68 149.721 65.2444C149.237 58.742 146.524 53.3585 143.096 51.3318C141.477 53.443 140.047 54.7308 140.047 54.7308C140.047 54.7308 138.448 53.6752 136.535 51.8174C133.443 54.3297 131.55 60.072 132.034 66.5744C132.139 67.9889 132.497 69.3401 133.001 70.6279C131.824 69.9101 130.562 69.3401 129.174 69.0023C122.864 67.4611 116.912 68.3901 113.947 71.029C115.44 73.2246 116.239 74.9769 116.239 74.9769C116.239 74.9769 114.746 76.1592 112.391 77.4259C113.821 81.1415 118.679 84.7305 124.967 86.2717C126.355 86.6095 127.743 86.6939 129.111 86.6095C128.08 87.5173 127.134 88.5518 126.397 89.7551C122.99 95.3075 122.023 101.261 123.621 104.934C126.166 104.174 128.08 103.984 128.08 103.984C128.08 103.984 128.753 105.779 129.216 108.418C133.191 108.207 138.07 104.681 141.498 99.1287C142.234 97.9254 142.76 96.6164 143.096 95.2864C143.622 96.5531 144.316 97.7565 145.242 98.8543C149.448 103.837 154.79 106.581 158.744 106.201C158.828 103.541 159.206 101.662 159.206 101.662C159.206 101.662 161.12 101.578 163.749 101.937L163.728 101.915Z"
                                          fill="#FFDFDF"/>
                                    <path d="M411.845 187.828C412.455 185.527 411.361 182.086 408.858 179.109C408.312 178.454 407.66 177.927 406.966 177.483C407.786 177.42 408.606 177.23 409.384 176.913C412.981 175.435 415.525 172.881 416.051 170.559C414.558 170.031 413.569 169.461 413.569 169.461C413.569 169.461 413.885 168.363 414.579 166.927C412.581 165.619 408.985 165.619 405.388 167.096C404.61 167.413 403.895 167.877 403.264 168.405C403.453 167.603 403.537 166.78 403.474 165.914C403.18 162.03 401.561 158.799 399.52 157.596C398.553 158.863 397.712 159.623 397.712 159.623C397.712 159.623 396.765 158.989 395.609 157.871C393.758 159.369 392.622 162.811 392.917 166.695C392.98 167.54 393.19 168.363 393.485 169.123C392.79 168.701 392.012 168.363 391.192 168.152C387.427 167.223 383.873 167.772 382.086 169.355C382.99 170.664 383.453 171.72 383.453 171.72C383.453 171.72 382.548 172.438 381.16 173.177C382.022 175.414 384.925 177.547 388.689 178.476C389.51 178.687 390.351 178.729 391.171 178.666C390.561 179.193 389.993 179.827 389.552 180.545C387.512 183.859 386.944 187.427 387.89 189.623C389.425 189.179 390.561 189.053 390.561 189.053C390.561 189.053 390.961 190.129 391.234 191.691C393.611 191.565 396.534 189.454 398.574 186.139C399.016 185.421 399.331 184.64 399.52 183.838C399.836 184.598 400.257 185.316 400.803 185.97C403.306 188.947 406.524 190.594 408.879 190.361C408.921 188.757 409.153 187.638 409.153 187.638C409.153 187.638 410.289 187.596 411.866 187.807L411.845 187.828Z"
                                          fill="#FFDFDF"/>
                                    <path d="M201.905 222.246C202.515 219.924 201.422 216.461 198.898 213.464C198.351 212.809 197.699 212.281 197.005 211.838C197.825 211.775 198.645 211.585 199.445 211.268C203.062 209.79 205.628 207.215 206.154 204.871C204.639 204.322 203.651 203.752 203.651 203.752C203.651 203.752 203.966 202.633 204.66 201.198C202.662 199.889 199.024 199.868 195.407 201.367C194.607 201.683 193.913 202.148 193.282 202.676C193.472 201.873 193.556 201.029 193.493 200.184C193.198 196.279 191.558 193.027 189.497 191.803C188.529 193.07 187.667 193.851 187.667 193.851C187.667 193.851 186.7 193.217 185.564 192.099C183.713 193.619 182.577 197.06 182.851 200.965C182.914 201.81 183.124 202.633 183.44 203.393C182.746 202.971 181.968 202.612 181.147 202.422C177.341 201.493 173.765 202.042 171.978 203.647C172.882 204.977 173.345 206.032 173.345 206.032C173.345 206.032 172.44 206.75 171.031 207.51C171.894 209.748 174.817 211.901 178.603 212.83C179.423 213.041 180.264 213.084 181.084 213.02C180.453 213.569 179.906 214.181 179.444 214.92C177.383 218.256 176.815 221.845 177.761 224.041C179.297 223.597 180.432 223.471 180.432 223.471C180.432 223.471 180.832 224.547 181.105 226.131C183.503 226.004 186.426 223.872 188.487 220.536C188.929 219.797 189.244 219.016 189.455 218.214C189.77 218.974 190.191 219.713 190.738 220.367C193.261 223.365 196.479 225.012 198.877 224.779C198.919 223.175 199.15 222.035 199.15 222.035C199.15 222.035 200.307 221.993 201.884 222.204L201.905 222.246Z"
                                          fill="#FFDFDF"/>
                                    <path d="M526.331 113.87C527.741 104.474 521.866 91.4114 510.559 80.7821C508.077 78.4895 505.29 76.6351 502.355 75.2519C505.569 74.6352 508.731 73.5701 511.676 71.9239C525.277 64.4767 534.259 53.2631 535.264 43.8624C529.093 42.4423 524.893 40.5968 524.893 40.5968C524.893 40.5968 525.643 36.0703 527.747 30.1284C519.236 25.8948 504.968 27.462 491.367 34.9093C488.422 36.5554 485.827 38.6161 483.571 40.9917C483.963 37.7676 483.939 34.4391 483.306 31.0789C480.419 15.8611 472.545 3.88354 463.874 0.0213726C460.599 5.43981 457.553 8.86396 457.553 8.86396C457.553 8.86396 453.48 6.80212 448.445 2.93151C441.787 9.69251 438.835 23.7554 441.722 38.9732C442.35 42.2836 443.56 45.3767 445.101 48.2807C442.116 46.903 438.972 45.8968 435.59 45.4727C420.217 43.5312 406.353 47.3239 400.028 54.3499C404.156 59.1309 406.52 63.052 406.52 63.052C406.52 63.052 403.296 66.244 398.049 69.8671C402.435 78.2528 414.915 85.418 430.238 87.3651C433.62 87.7893 436.933 87.6154 440.152 87.0486C437.952 89.4684 435.984 92.1645 434.561 95.2028C427.968 109.224 427.268 123.537 432.041 131.777C437.871 129.299 442.358 128.336 442.358 128.336C442.358 128.336 444.434 132.391 446.238 138.496C455.606 136.929 466.232 127.295 472.875 113.268C474.298 110.23 475.195 106.999 475.637 103.769C477.222 106.618 479.19 109.272 481.678 111.614C492.985 122.243 506.386 127.285 515.66 125.325C515.147 119.024 515.542 114.487 515.542 114.487C515.542 114.487 520.057 113.773 526.387 113.914L526.331 113.87Z"
                                          fill="#FFDFDF"/>
                                    <path d="M105.855 241.584C108.696 233.939 106.064 221.93 98.4398 211.006C96.7588 208.642 94.7452 206.593 92.5236 204.913C95.3317 204.962 98.1762 204.625 100.941 203.761C113.68 199.904 123.204 192.068 125.706 184.348C120.772 182.066 117.569 179.775 117.569 179.775C117.569 179.775 118.997 176.105 121.813 171.484C115.41 166.426 103.147 165.227 90.4073 169.084C87.643 169.948 85.0997 171.221 82.7854 172.819C83.6834 170.179 84.2504 167.379 84.3108 164.445C84.5694 151.151 80.0673 139.7 73.4642 134.926C69.7573 138.901 66.5942 141.24 66.5942 141.24C66.5942 141.24 63.5366 138.79 59.9893 134.65C53.2033 139.156 48.2436 150.449 47.9851 163.742C47.9287 166.634 48.3992 169.446 49.182 172.157C46.9175 170.474 44.4532 169.074 41.6871 168.121C29.115 163.779 16.7991 164.52 10.2462 169.307C12.871 174.051 14.1654 177.762 14.1654 177.762C14.1654 177.762 10.8937 179.875 5.84745 181.993C8.05257 189.812 17.2731 198.032 29.8024 202.37C32.5685 203.323 35.3831 203.761 38.1871 203.853C35.9117 205.498 33.7835 207.416 32.0519 209.717C24.0405 220.333 20.9276 232.234 23.4839 239.998C28.8192 238.945 32.7587 238.927 32.7587 238.927C32.7587 238.927 33.7876 242.7 34.2259 248.146C42.3726 248.482 52.9984 242.263 61.0527 231.651C62.7842 229.349 64.1071 226.793 65.0481 224.158C65.8778 226.831 67.0628 229.407 68.7398 231.814C76.3639 242.737 86.7326 249.337 94.8689 249.326C95.5497 243.942 96.6815 240.2 96.6815 240.2C96.6815 240.2 100.601 240.397 105.893 241.631L105.855 241.584Z"
                                          fill="#FFDFDF"/>
                                    <g clip-path="url(#clip1_1448_6581)">
                                        <path d="M264.975 203.943C264.975 203.943 262.885 205.583 263.354 206.989C263.823 208.395 267.085 218.666 270.641 221.38C274.197 224.095 278.202 224.192 280.82 223.157C283.438 222.122 285.782 218.393 284.375 213.335C282.969 208.278 281.093 204.177 280.351 203.513C279.608 202.849 268.804 200.779 264.936 203.923L264.975 203.943Z"
                                              fill="#CEBEAA"/>
                                        <path d="M277.461 224.739C275.175 224.739 272.557 224.036 270.076 222.161C267.067 219.857 264.293 212.925 262.515 207.516L262.437 207.301C261.773 205.329 264.058 203.416 264.371 203.181C268.552 199.784 279.922 201.814 281.036 202.791C282.11 203.728 284.045 208.336 285.354 213.081C286.877 218.568 284.357 222.845 281.212 224.075C280.157 224.485 278.867 224.758 277.461 224.758V224.739ZM272.029 203.24C269.47 203.24 266.95 203.591 265.602 204.704C264.957 205.212 264.156 206.208 264.312 206.677L264.391 206.891C266.754 214.058 269.275 219.057 271.287 220.599C274.647 223.157 278.262 223.118 280.528 222.239C282.892 221.322 284.728 217.983 283.517 213.589C282.091 208.473 280.313 204.743 279.786 204.236C279.141 203.884 275.585 203.22 272.068 203.22L272.029 203.24Z"
                                              fill="#07403F"/>
                                        <path d="M264.98 203.943C264.98 203.943 269.512 215.522 271.818 218.197C274.123 220.873 285.689 225.032 284.829 215.854C284.732 214.858 283.501 206.13 279.535 203.181C275.569 200.233 264.57 199.823 264.98 203.923V203.943Z"
                                              fill="white"/>
                                        <path d="M280.16 222.493C276.585 222.493 272.462 220.463 271.095 218.861C268.711 216.089 264.276 204.802 264.081 204.314C264.042 204.236 264.022 204.138 264.022 204.06C263.886 202.791 264.511 201.717 265.761 200.994C269.043 199.12 276.682 199.881 280.121 202.42C284.751 205.837 285.806 215.678 285.806 215.776C286.177 219.721 284.419 221.341 282.875 222.005C282.055 222.357 281.137 222.513 280.16 222.513V222.493ZM265.957 203.728C267.246 207.009 270.782 215.503 272.56 217.573C273.908 219.135 279.3 221.4 282.114 220.189C283.501 219.603 284.067 218.217 283.852 215.952C283.676 214.058 282.309 206.462 278.949 203.982C276.174 201.932 269.317 201.209 266.718 202.674C266.074 203.045 265.937 203.416 265.937 203.728H265.957Z"
                                              fill="#07403F"/>
                                        <path d="M280.649 215.405C280.649 215.405 281.098 211.637 284.126 212.496C284.126 212.496 285.045 212.671 284.849 211.109C284.654 209.547 283.286 209.137 283.286 209.137C283.286 209.137 280.961 209.645 279.438 213.355L280.649 215.405Z"
                                              fill="#CEBEAA"/>
                                        <path d="M280.627 216.382C280.295 216.382 279.962 216.206 279.787 215.893L278.575 213.843C278.419 213.589 278.4 213.257 278.517 212.984C280.197 208.844 282.932 208.2 283.049 208.18C283.206 208.141 283.381 208.161 283.538 208.18C284.28 208.395 285.57 209.235 285.804 210.973C285.921 211.91 285.745 212.593 285.276 213.042C284.847 213.433 284.319 213.511 283.948 213.453C283.342 213.296 282.932 213.316 282.619 213.531C281.955 213.98 281.662 215.112 281.623 215.522C281.565 215.932 281.252 216.264 280.842 216.362C280.783 216.362 280.705 216.382 280.646 216.382H280.627ZM283.225 210.192C282.795 210.387 281.897 210.934 281.057 212.3C281.193 212.164 281.35 212.027 281.525 211.91C282.014 211.578 282.795 211.266 283.889 211.441C283.889 211.383 283.889 211.305 283.889 211.226C283.811 210.641 283.479 210.328 283.245 210.192H283.225Z"
                                              fill="#07403F"/>
                                        <path d="M267.678 212.222C266.76 210.582 269.671 208.415 273.656 209.352C277.642 210.289 281.862 214.331 280.65 215.405C279.185 216.713 277.407 214.839 276.958 214.214C276.509 213.589 272.308 210.133 270.628 212.515C270.628 212.515 268.792 214.214 267.678 212.222Z"
                                              fill="#CEBEAA"/>
                                        <path d="M279.515 216.831C279.417 216.831 279.3 216.831 279.202 216.831C277.483 216.635 276.213 214.878 276.154 214.8C275.822 214.37 273.673 212.691 272.267 212.691H272.228C271.856 212.691 271.622 212.828 271.407 213.101C271.368 213.16 271.329 213.199 271.27 213.257C271.153 213.374 270.039 214.37 268.672 214.155C268.125 214.077 267.363 213.745 266.796 212.71C266.327 211.871 266.425 210.875 267.07 210.055C268.066 208.727 270.626 207.653 273.849 208.415C277.209 209.196 281.195 212.222 281.742 214.39C281.918 215.073 281.742 215.718 281.273 216.147C280.746 216.616 280.14 216.85 279.495 216.85L279.515 216.831ZM272.267 210.719C274.611 210.719 277.327 213.042 277.756 213.648C278.069 214.097 278.831 214.8 279.417 214.878C279.534 214.878 279.671 214.878 279.847 214.78C279.554 213.784 276.565 211.051 273.419 210.309C270.88 209.703 269.141 210.563 268.633 211.226C268.398 211.539 268.477 211.695 268.516 211.754C268.672 212.027 268.828 212.183 268.984 212.222C269.297 212.261 269.707 212.007 269.883 211.871C270.45 211.129 271.27 210.738 272.247 210.738L272.267 210.719Z"
                                              fill="#07403F"/>
                                        <path d="M261.73 181.24C261.73 181.24 258.443 181.293 257.208 182.396C257.208 182.396 249.673 196.799 256.483 201.512C263.294 206.225 267.569 197.882 268.193 195.862C268.816 193.842 265.398 180.769 261.748 181.219L261.73 181.24Z"
                                              fill="#CEBEAA"/>
                                        <path d="M260.92 203.924C259.282 204.038 257.601 203.509 255.913 202.335C248.503 197.174 256.006 182.578 256.335 181.968C256.387 181.866 256.46 181.783 256.552 181.698C257.971 180.425 261.114 180.284 261.682 180.284C262.732 180.172 263.708 180.691 264.648 181.819C267.425 185.13 269.753 194.089 269.114 196.17C268.749 197.389 266.557 202.2 262.618 203.571C262.064 203.766 261.504 203.883 260.92 203.924ZM257.976 183.028C257.167 184.631 251.375 196.798 257.014 200.712C258.763 201.921 260.392 202.258 261.961 201.718C264.867 200.713 266.833 196.876 267.233 195.596C267.567 194.476 265.751 186.127 263.108 183.042C262.637 182.468 262.187 182.187 261.855 182.21C261.816 182.212 261.797 182.214 261.777 182.215C261.777 182.215 261.738 182.218 261.719 182.219C260.565 182.241 258.723 182.506 257.956 183.029L257.976 183.028Z"
                                              fill="#07403F"/>
                                        <path d="M261.128 182.124C261.128 182.124 252.936 196.377 259.537 200.889C266.138 205.401 269.749 193.424 269.749 193.424"
                                              fill="white"/>
                                        <path d="M262.771 202.876C261.524 202.962 260.242 202.562 258.987 201.691C251.729 196.735 259.944 182.245 260.293 181.634C260.554 181.166 261.169 181.005 261.619 181.268C262.087 181.529 262.246 182.124 261.985 182.593C259.837 186.344 255.296 196.78 260.088 200.068C261.3 200.883 262.394 201.121 263.464 200.733C266.255 199.755 268.268 194.917 268.81 193.138C268.97 192.618 269.517 192.325 270.038 192.484C270.558 192.644 270.851 193.191 270.69 193.692C270.593 193.992 268.421 201.053 264.12 202.566C263.68 202.714 263.237 202.823 262.77 202.856L262.771 202.876Z"
                                              fill="#07403F"/>
                                        <path d="M261.674 196.747C261.674 196.747 263.901 194.419 268.885 195.677L267.411 198.422C267.411 198.422 265.292 196.397 263.65 198.684L261.694 196.745L261.674 196.747Z"
                                              fill="#CEBEAA"/>
                                        <path d="M267.462 199.397C267.189 199.416 266.908 199.318 266.719 199.136C266.532 198.973 266.008 198.481 265.388 198.582C265.057 198.606 264.741 198.843 264.436 199.256C264.276 199.482 264.013 199.637 263.72 199.658C263.45 199.716 263.147 199.58 262.957 199.378L261 197.44C260.621 197.055 260.618 196.448 260.983 196.071C261.093 195.965 263.672 193.339 269.134 194.72C269.433 194.797 269.662 194.996 269.78 195.282C269.898 195.567 269.881 195.882 269.743 196.146L268.269 198.891C268.131 199.155 267.87 199.349 267.56 199.39C267.54 199.392 267.501 199.394 267.482 199.396L267.462 199.397ZM263.194 196.876L263.613 197.277C264.187 196.807 264.784 196.648 265.212 196.618C265.853 196.534 266.554 196.759 267.041 196.999L267.388 196.368C265.351 196.08 263.989 196.488 263.194 196.876Z"
                                              fill="#07403F"/>
                                        <path d="M257.209 190.03C257.209 190.03 262.283 192.574 261.634 198.159C261.634 198.159 263.648 199.525 264.226 197.978C264.803 196.431 263.969 189.794 258.167 188.026C258.167 188.026 256.126 187.972 257.211 190.05L257.209 190.03Z"
                                              fill="#CEBEAA"/>
                                        <path d="M263.366 199.682C262.314 199.755 261.31 199.121 261.083 198.96C260.775 198.747 260.614 198.406 260.666 198.03C261.232 193.215 256.944 190.988 256.761 190.884C256.579 190.798 256.431 190.652 256.34 190.463C255.82 189.461 255.761 188.624 256.166 187.969C256.765 187.008 258.039 187.017 258.176 187.027C258.254 187.021 258.353 187.034 258.434 187.067C261.615 188.04 263.3 190.291 264.143 191.994C265.327 194.378 265.573 197.062 265.11 198.288C264.814 199.092 264.249 199.425 263.847 199.551C263.674 199.602 263.519 199.632 263.344 199.644L263.366 199.682ZM262.653 197.598C262.916 197.717 263.154 197.759 263.27 197.731C263.484 197.149 263.474 195.036 262.403 192.879C261.741 191.535 260.444 189.785 258.02 188.995C257.942 189 257.864 189.006 257.828 189.048C257.829 189.067 257.875 189.162 257.945 189.333C259.088 189.997 262.895 192.629 262.653 197.598Z"
                                              fill="#07403F"/>
                                        <path d="M254.603 147.471C254.603 147.471 253.861 166.178 254.603 175.863C255.346 185.548 259.585 197.421 263.59 204.255C263.59 204.255 271.815 208.219 277.813 208.219C283.811 208.219 285.55 203.826 286.546 201.404C287.542 198.983 288.539 197.226 288.539 197.226C288.539 197.226 298.21 195.019 300.867 183.147C303.524 171.274 294.791 145.304 287.308 144.19C279.825 143.077 254.642 147.491 254.642 147.491L254.603 147.471Z"
                                              fill="#57D061"/>
                                        <path d="M277.794 209.196C271.659 209.196 263.493 205.31 263.161 205.134C262.985 205.056 262.829 204.919 262.751 204.743C258.804 198.007 254.408 185.959 253.646 175.941C252.904 166.314 253.646 147.627 253.646 147.432C253.646 146.963 253.998 146.592 254.447 146.514C255.483 146.338 279.865 142.101 287.426 143.214C289.399 143.507 291.431 145.147 293.443 148.076C298.991 156.121 303.954 173.657 301.805 183.342C299.343 194.355 291.216 197.44 289.184 198.046C288.852 198.671 288.148 200.057 287.426 201.775C286.41 204.236 284.358 209.196 277.794 209.196ZM264.274 203.494C265.72 204.158 272.695 207.243 277.794 207.243C282.893 207.243 284.573 203.552 285.589 201.111C286.624 198.573 287.621 196.815 287.679 196.737C287.816 196.503 288.051 196.327 288.305 196.269C288.676 196.191 297.428 194.023 299.89 182.932C301.883 173.969 297.116 156.883 291.821 149.189C290.141 146.748 288.52 145.362 287.132 145.147C280.627 144.19 259.84 147.588 255.541 148.311C255.405 151.962 254.916 167.408 255.561 175.785C256.303 185.373 260.465 196.894 264.255 203.494H264.274Z"
                                              fill="#07403F"/>
                                        <path d="M249.996 164.03C249.996 164.03 251.852 175.101 253.317 181.526C253.552 182.541 254.353 183.303 255.369 183.498C260.917 184.552 279.907 187.325 303.117 181.858L301.339 160.905C301.339 160.905 267.736 171.098 250.016 164.049L249.996 164.03Z"
                                              fill="#CCFFCE"/>
                                        <path d="M274.712 186.095C265.12 186.095 258.086 185.002 255.175 184.435C253.769 184.162 252.655 183.108 252.362 181.721C250.916 175.336 249.06 164.284 249.041 164.166C248.982 163.815 249.119 163.463 249.392 163.249C249.666 163.034 250.037 162.975 250.369 163.092C267.581 169.946 300.716 160.027 301.048 159.929C301.341 159.851 301.634 159.89 301.888 160.046C302.122 160.202 302.298 160.476 302.318 160.769L304.095 181.721C304.135 182.209 303.822 182.639 303.353 182.756C292.647 185.295 282.8 186.056 274.732 186.056L274.712 186.095ZM251.248 165.514C251.815 168.833 253.163 176.449 254.277 181.311C254.413 181.936 254.902 182.405 255.566 182.522C260.782 183.498 279.362 186.271 302.083 181.077L300.481 162.155C294.503 163.854 267.679 170.903 251.268 165.514H251.248Z"
                                              fill="#07403F"/>
                                        <path d="M275.724 136.536C275.724 136.536 291.998 141.456 295.749 143.897C299.5 146.338 304.795 156.18 301.766 161.823C301.766 161.823 307.393 164.108 311.945 166.646C318.685 170.395 323.296 173.031 320.307 178.753C318.451 182.287 317.435 185.236 308.858 185.138C300.282 185.041 276.31 163.854 274.532 150.439C272.754 137.024 275.724 136.536 275.724 136.536Z"
                                              fill="#57D061"/>
                                        <path d="M309.27 186.115C309.133 186.115 308.996 186.115 308.859 186.115C299.99 186.017 275.432 164.557 273.576 150.576C271.857 137.61 274.396 135.774 275.569 135.579C275.725 135.559 275.862 135.579 275.998 135.618C276.663 135.813 292.448 140.617 296.278 143.097C300.224 145.674 305.343 155.125 303.076 161.335C304.874 162.097 308.976 163.893 312.434 165.807C318.979 169.458 324.645 172.602 321.187 179.222L320.894 179.788C319.214 183.049 317.631 186.134 309.27 186.134V186.115ZM275.901 137.61C275.451 138.196 274.24 140.754 275.51 150.302C277.229 163.268 300.712 184.064 308.879 184.162C309.016 184.162 309.152 184.162 309.289 184.162C316.44 184.162 317.573 181.936 319.155 178.87L319.448 178.284C321.91 173.559 318.589 171.45 311.477 167.486C307.023 165.006 301.455 162.741 301.396 162.721C301.142 162.624 300.947 162.409 300.849 162.155C300.751 161.901 300.771 161.608 300.908 161.354C303.623 156.297 298.7 146.963 295.223 144.698C291.979 142.589 278.558 138.41 275.901 137.59V137.61Z"
                                              fill="#07403F"/>
                                        <path d="M275.723 136.536C264.744 138.274 253.784 141.203 252.279 148.682C250.775 156.16 245.031 171.43 267.987 182.405C290.943 193.379 296.218 191.914 298.972 190.684C301.727 189.454 312.98 185.763 315.872 174.184C317.61 167.213 283.929 159.734 280.823 158.484C277.716 157.234 275.723 136.555 275.723 136.555V136.536Z"
                                              fill="#57D061"/>
                                        <path d="M294.813 192.481C290.222 192.481 282.524 190.411 267.579 183.283C245.815 172.875 249.136 158.289 250.914 150.459C251.07 149.756 251.227 149.092 251.344 148.486C253.004 140.187 265.176 137.219 275.589 135.56C275.843 135.52 276.136 135.579 276.331 135.755C276.546 135.911 276.683 136.165 276.703 136.438C277.738 147.237 279.809 156.922 281.196 157.566C281.723 157.781 283.443 158.25 285.631 158.855C305.343 164.245 318.199 168.951 316.831 174.418C314.076 185.412 304.054 189.61 300.283 191.172C299.912 191.328 299.619 191.446 299.365 191.563C298.193 192.09 296.786 192.481 294.794 192.481H294.813ZM274.866 137.668C265.313 139.25 254.626 142.003 253.258 148.877C253.141 149.502 252.985 150.166 252.809 150.888C251.031 158.699 248.062 171.782 268.419 181.526C291.961 192.793 296.591 190.684 298.564 189.786C298.818 189.669 299.15 189.532 299.541 189.376C303.39 187.775 312.416 183.986 314.936 173.949C315.718 170.806 305.695 166.353 285.123 160.749C282.7 160.085 281.098 159.656 280.473 159.402C277.347 158.152 275.569 144.542 274.866 137.688V137.668Z"
                                              fill="#07403F"/>
                                        <path d="M273.63 151.858C306.754 147.211 330.02 117.912 325.597 86.4176C321.175 54.9231 290.737 33.1589 257.613 37.8059C224.489 42.4529 201.222 71.7514 205.645 103.246C210.068 134.74 240.506 156.505 273.63 151.858Z"
                                              fill="white"/>
                                        <path d="M264.744 153.446C234.774 153.446 208.771 132.474 204.668 103.379C200.174 71.4137 223.873 41.5571 257.476 36.8315C291.099 32.1256 322.065 54.2886 326.558 86.2737C331.052 118.259 307.373 148.096 273.77 152.821C270.741 153.251 267.733 153.446 264.744 153.446ZM266.482 38.1594C263.591 38.1594 260.68 38.3546 257.73 38.7647C225.201 43.334 202.265 72.1948 206.602 103.106C210.939 134.017 240.928 155.438 273.477 150.888C306.005 146.319 328.942 117.458 324.604 86.5471C320.658 58.4283 295.475 38.1594 266.463 38.1594H266.482Z"
                                              fill="#07403F"/>
                                        <path d="M278.192 35.1327C278.192 35.1327 273.054 93.3619 325.686 90.14C325.686 90.14 335.904 65.7118 316.425 48.0595C299.252 32.477 278.192 35.1327 278.192 35.1327Z"
                                              fill="#57D061"/>
                                        <path d="M321.58 91.253C308.647 91.253 298.292 87.3477 290.77 79.615C274.926 63.3491 277.114 36.1871 277.231 35.0546C277.27 34.6055 277.622 34.2345 278.091 34.1759C278.306 34.1563 299.777 31.6374 317.106 47.337C336.877 65.2627 326.718 90.2767 326.62 90.5305C326.484 90.882 326.152 91.0968 325.78 91.1359C324.354 91.214 322.967 91.2726 321.599 91.2726L321.58 91.253ZM279.126 36.0309C278.892 40.6783 278.443 64.1692 292.177 78.2481C299.972 86.2346 311.03 89.9252 324.999 89.1832C326.327 85.4731 332.892 64.2864 315.777 48.7625C300.988 35.3865 283.092 35.7576 279.126 36.0309Z"
                                              fill="#07403F"/>
                                        <path d="M226.67 47.1611C226.67 47.1611 257.089 97.0914 208.461 117.497C208.461 117.497 187.655 99.7081 199.729 72.3704C208.618 52.2381 226.689 47.1611 226.689 47.1611H226.67Z"
                                              fill="#57D061"/>
                                        <path d="M208.46 118.473C208.226 118.473 208.011 118.395 207.816 118.239C207.601 118.063 186.599 99.6495 198.809 71.9603C207.777 51.6523 225.653 46.4191 226.395 46.2238C226.805 46.1067 227.274 46.2824 227.489 46.6534C228.095 47.6298 242.083 71.0035 235.089 92.6003C231.416 103.945 222.566 112.615 208.812 118.395C208.695 118.454 208.558 118.473 208.441 118.473H208.46ZM226.219 48.3327C222.82 49.5239 208.187 55.5577 200.607 72.7609C189.822 97.2086 205.667 113.572 208.656 116.365C221.53 110.819 229.814 102.637 233.253 92.0145C239.348 73.2296 228.446 52.3162 226.239 48.3327H226.219Z"
                                              fill="#07403F"/>
                                        <path d="M259.996 103.672C259.996 103.672 266.189 98.7707 274.688 100.294C274.688 100.294 279.943 124.41 273.437 126.46C266.932 128.51 259.996 103.672 259.996 103.672Z"
                                              fill="#FF5727"/>
                                        <path d="M272.659 127.553C272.014 127.553 271.35 127.378 270.685 127.007C264.648 123.726 259.276 104.726 259.061 103.926C258.963 103.555 259.08 103.145 259.393 102.891C259.667 102.676 266.016 97.7358 274.866 99.3175C275.257 99.3956 275.569 99.6885 275.648 100.079C276.566 104.258 280.747 125.191 273.733 127.378C273.381 127.495 273.03 127.534 272.659 127.534V127.553ZM261.132 104.082C262.792 109.803 267.384 123.004 271.623 125.308C272.17 125.601 272.678 125.679 273.147 125.542C277.172 124.273 275.765 110.194 273.889 101.173C267.618 100.294 262.695 103.067 261.132 104.102V104.082Z"
                                              fill="#07403F"/>
                                        <path d="M249.993 99.6105C252.216 99.6105 254.018 97.8096 254.018 95.588C254.018 93.3664 252.216 91.5654 249.993 91.5654C247.771 91.5654 245.969 93.3664 245.969 95.588C245.969 97.8096 247.771 99.6105 249.993 99.6105Z"
                                              fill="#07403F"/>
                                        <path d="M249.977 100.587C248.902 100.587 247.847 100.235 246.968 99.5714C245.894 98.7708 245.21 97.5992 245.034 96.2713C244.858 94.9435 245.19 93.6352 245.991 92.5612C246.792 91.4873 247.965 90.8038 249.293 90.6281C250.602 90.4328 251.931 90.7843 253.005 91.5849C254.08 92.3855 254.763 93.5571 254.939 94.8849C255.115 96.2128 254.783 97.5211 253.982 98.5951C253.181 99.669 252.009 100.352 250.68 100.528C250.446 100.567 250.211 100.587 249.977 100.587ZM249.996 92.5417C249.86 92.5417 249.703 92.5417 249.567 92.5808C248.766 92.6979 248.043 93.108 247.554 93.7719C247.066 94.4163 246.851 95.2169 246.968 96.037C247.085 96.8376 247.496 97.5601 248.16 98.0483C248.805 98.5365 249.606 98.7513 250.426 98.6341C251.227 98.5169 251.95 98.1069 252.439 97.443C252.927 96.7986 253.142 95.998 253.025 95.1778C252.907 94.3772 252.497 93.6547 251.833 93.1666C251.305 92.7565 250.661 92.5612 250.016 92.5612L249.996 92.5417Z"
                                              fill="#07403F"/>
                                        <path d="M252.594 93.6352C252.926 95.0997 251.715 95.7441 250.113 96.1151C248.511 96.4861 247.123 96.4471 246.772 95.0021C246.42 93.5571 247.456 92.0535 249.058 91.6825C250.66 91.3115 252.242 92.1902 252.594 93.6352Z"
                                              fill="white"/>
                                        <path d="M248.708 98.3802C248.493 97.4624 249.274 97.0524 250.29 96.818C251.306 96.5837 252.185 96.6033 252.4 97.521C252.615 98.4388 251.97 99.3761 250.954 99.6104C249.938 99.8447 248.942 99.298 248.727 98.3802H248.708Z"
                                              fill="white"/>
                                        <path d="M277.521 92.776C279.743 92.776 281.545 90.9751 281.545 88.7535C281.545 86.5319 279.743 84.731 277.521 84.731C275.298 84.731 273.496 86.5319 273.496 88.7535C273.496 90.9751 275.298 92.776 277.521 92.776Z"
                                              fill="#07403F"/>
                                        <path d="M277.521 93.7525C276.446 93.7525 275.391 93.401 274.512 92.7371C273.438 91.9365 272.754 90.7649 272.578 89.437C272.187 86.7033 274.102 84.1843 276.837 83.7938C279.572 83.4032 282.092 85.3169 282.483 88.0506C282.659 89.3784 282.327 90.6868 281.526 91.7607C280.725 92.8347 279.553 93.5182 278.224 93.6939C277.99 93.733 277.755 93.7525 277.521 93.7525ZM277.54 85.7074C277.404 85.7074 277.267 85.7074 277.11 85.7464C275.45 85.9808 274.278 87.5234 274.512 89.1832C274.629 89.9838 275.04 90.7063 275.704 91.1945C276.349 91.6826 277.15 91.8974 277.97 91.7803C278.771 91.6631 279.494 91.253 279.982 90.5891C280.471 89.9447 280.686 89.1441 280.569 88.324C280.354 86.8009 279.045 85.7074 277.56 85.7074H277.54Z"
                                              fill="#07403F"/>
                                        <path d="M280.258 86.7227C280.59 88.1873 279.379 88.8316 277.777 89.2027C276.175 89.5737 274.787 89.5346 274.436 88.0896C274.084 86.6446 275.12 85.1411 276.722 84.7701C278.324 84.399 279.906 85.2777 280.258 86.7227Z"
                                              fill="white"/>
                                        <path d="M276.348 91.4678C276.133 90.5501 276.915 90.14 277.931 89.9057C278.947 89.6714 279.826 89.6909 280.041 90.6087C280.256 91.5264 279.611 92.4637 278.595 92.698C277.579 92.9324 276.583 92.3856 276.368 91.4678H276.348Z"
                                              fill="white"/>
                                        <path d="M241.148 76.1C241.031 76.1 240.914 76.0805 240.816 76.0414C240.308 75.8462 240.054 75.2994 240.249 74.7917C240.308 74.6355 241.754 70.8473 244.919 69.8123C246.677 69.2461 248.553 69.6171 250.506 70.9449C250.956 71.2378 251.073 71.8627 250.78 72.2923C250.467 72.7414 249.862 72.8585 249.432 72.5656C248.006 71.6088 246.697 71.2964 245.544 71.6674C243.278 72.3899 242.105 75.4361 242.105 75.4556C241.949 75.8462 241.578 76.0805 241.187 76.0805L241.148 76.1Z"
                                              fill="#07403F"/>
                                        <path d="M277.542 68.9727C277.249 68.9727 276.956 68.836 276.761 68.5822C276.761 68.5626 274.788 65.946 272.404 65.8874C271.232 65.8679 270.021 66.4928 268.907 67.8206C268.555 68.2307 267.93 68.2893 267.52 67.9378C267.11 67.5863 267.051 66.981 267.403 66.5709C268.927 64.7744 270.587 63.8957 272.463 63.9543C275.804 64.0714 278.226 67.3129 278.324 67.4496C278.636 67.8792 278.558 68.4845 278.109 68.8165C277.933 68.9532 277.738 69.0118 277.523 69.0118L277.542 68.9727Z"
                                              fill="#07403F"/>
                                        <path d="M287.709 98.8318C290.941 97.7459 293.079 95.432 292.484 93.6636C291.889 91.8951 288.787 91.3417 285.555 92.4276C282.323 93.5134 280.185 95.8273 280.78 97.5958C281.375 99.3642 284.477 99.9176 287.709 98.8318Z"
                                              fill="#FF7B79"/>
                                        <path d="M247.045 110.061C250.422 109.588 252.949 107.706 252.689 105.858C252.43 104.011 249.482 102.897 246.106 103.371C242.729 103.844 240.203 105.726 240.462 107.574C240.721 109.421 243.669 110.535 247.045 110.061Z"
                                              fill="#FF7B79"/>
                                        <path d="M287.523 136.145C287.523 136.145 288.149 133.822 289.36 134.251C290.571 134.681 290.669 138.606 289.653 140.851"
                                              fill="white"/>
                                        <path d="M289.655 141.828C289.519 141.828 289.382 141.808 289.245 141.73C288.757 141.496 288.542 140.929 288.757 140.441C289.636 138.547 289.441 135.579 288.972 135.13C288.933 135.247 288.62 135.852 288.464 136.399C288.327 136.926 287.799 137.219 287.272 137.083C286.744 136.946 286.451 136.399 286.588 135.891C286.666 135.579 287.155 133.997 288.229 133.431C288.698 133.197 289.206 133.158 289.695 133.333C290.222 133.529 290.632 133.958 290.906 134.603C291.609 136.263 291.433 139.309 290.535 141.242C290.378 141.593 290.007 141.808 289.655 141.808V141.828Z"
                                              fill="#07403F"/>
                                        <path d="M278.932 130.033C283.777 127.612 287.724 135.169 291.084 144.64L292.197 149.209C292.197 149.209 293.643 154.403 289.814 158.211L282.703 156.18C280.417 147.08 272.426 133.275 278.912 130.033H278.932Z"
                                              fill="white"/>
                                        <path d="M289.81 159.187C289.81 159.187 289.635 159.187 289.537 159.148L282.426 157.117C282.093 157.02 281.839 156.746 281.742 156.414C281.136 153.973 280.12 151.24 279.046 148.33C276.057 140.266 272.97 131.928 278.479 129.174C279.827 128.491 281.195 128.432 282.523 128.979C285.766 130.287 288.677 135.013 291.999 144.327L293.151 148.975C293.21 149.17 294.695 154.735 290.514 158.914C290.318 159.089 290.084 159.207 289.83 159.207L289.81 159.187ZM283.52 155.399L289.517 157.117C292.409 153.817 291.315 149.658 291.276 149.482L290.143 144.893C289.068 141.867 285.669 132.357 281.82 130.795C280.999 130.463 280.198 130.522 279.378 130.932C275.451 132.904 278.225 140.402 280.921 147.666C281.898 150.302 282.894 152.997 283.539 155.418L283.52 155.399Z"
                                              fill="#07403F"/>
                                        <path d="M268.888 151.025C268.888 151.025 265.195 149.248 265.195 146.514C265.195 143.78 267.442 144.64 267.442 144.64C267.442 144.64 265.195 134.329 269.806 132.806C276.058 130.736 279.066 140.031 280.844 150.342C281.802 155.829 279.907 164.01 276.038 164.87C276.038 164.87 269.884 159.871 267.208 154.052C266.426 152.353 267.012 151.025 268.888 151.025Z"
                                              fill="white"/>
                                        <path d="M276.039 165.846C275.824 165.846 275.589 165.768 275.413 165.631C275.159 165.416 269.064 160.417 266.309 154.462C265.743 153.251 265.743 152.118 266.29 151.259C266.407 151.084 266.544 150.908 266.7 150.771C265.528 149.853 264.199 148.389 264.199 146.495C264.199 145.284 264.57 144.425 265.293 143.917C265.606 143.702 265.918 143.605 266.231 143.546C265.743 140.48 265.098 133.294 269.494 131.85C271.232 131.283 272.854 131.42 274.339 132.318C277.68 134.329 280.063 139.992 281.821 150.166C282.779 155.692 281.04 164.733 276.273 165.826C276.195 165.826 276.136 165.846 276.058 165.846H276.039ZM266.68 145.479C266.583 145.479 266.485 145.479 266.407 145.538C266.29 145.616 266.172 145.909 266.172 146.495C266.172 148.233 268.439 149.717 269.318 150.127C269.728 150.322 269.943 150.771 269.845 151.22C269.748 151.669 269.357 151.982 268.888 151.982C268.419 151.982 268.068 152.099 267.931 152.294C267.774 152.548 267.833 153.056 268.087 153.622C270.256 158.328 274.866 162.546 276.253 163.756C279.086 162.487 280.727 155.379 279.868 150.478C278.227 141.047 276.097 135.638 273.303 133.978C272.327 133.392 271.272 133.294 270.08 133.705C266.993 134.72 267.814 141.964 268.361 144.405C268.439 144.757 268.321 145.128 268.048 145.362C267.774 145.596 267.403 145.655 267.052 145.538C266.973 145.499 266.798 145.479 266.641 145.479H266.68Z"
                                              fill="#07403F"/>
                                        <path d="M311.4 174.828H311.381C281.685 174.828 272.757 163.288 272.385 162.8C272.073 162.37 272.151 161.765 272.581 161.433C273.011 161.12 273.616 161.198 273.948 161.628C274.027 161.745 282.31 172.231 308.997 172.836C306.868 172.016 303.801 171.02 301.026 170.181C300.518 170.025 300.206 169.478 300.382 168.97C300.538 168.443 301.085 168.169 301.593 168.306C313.706 171.938 313.569 172.895 313.452 173.735C313.296 174.809 311.987 174.828 311.4 174.828Z"
                                              fill="#07403F"/>
                                        <path d="M267.812 148.154C267.343 148.154 266.913 147.803 266.854 147.315L266.464 144.776C266.385 144.249 266.757 143.741 267.284 143.663C267.812 143.605 268.32 143.956 268.398 144.483L268.788 147.022C268.867 147.549 268.495 148.057 267.968 148.135C267.909 148.135 267.87 148.135 267.812 148.135V148.154Z"
                                              fill="#07403F"/>
                                        <path d="M270.313 143.995C270.313 143.995 268.34 138.313 268.32 133.802L270.313 143.995Z"
                                              fill="white"/>
                                        <path d="M270.313 144.972C269.903 144.972 269.532 144.718 269.395 144.308C269.317 144.073 267.363 138.411 267.363 133.802C267.363 133.255 267.793 132.826 268.34 132.826C268.887 132.826 269.317 133.255 269.317 133.802C269.317 138.098 271.232 143.624 271.251 143.683C271.427 144.19 271.153 144.757 270.645 144.932C270.548 144.972 270.431 144.991 270.333 144.991L270.313 144.972Z"
                                              fill="#07403F"/>
                                        <path d="M273.163 142.765C273.163 142.765 270.897 135.286 271.033 132.533L273.163 142.765Z"
                                              fill="white"/>
                                        <path d="M273.165 143.741C272.755 143.741 272.364 143.468 272.227 143.058C272.13 142.745 269.902 135.384 270.059 132.513C270.078 131.986 270.547 131.537 271.075 131.596C271.622 131.615 272.032 132.084 271.993 132.611C271.895 134.72 273.458 140.461 274.083 142.492C274.24 142.999 273.946 143.546 273.439 143.702C273.341 143.722 273.243 143.741 273.165 143.741Z"
                                              fill="#07403F"/>
                                        <path d="M274.238 133.451C274.238 133.451 274.707 138.469 276.075 141.945L274.238 133.451Z"
                                              fill="white"/>
                                        <path d="M276.077 142.941C275.686 142.941 275.315 142.706 275.158 142.316C273.752 138.742 273.283 133.763 273.263 133.548C273.205 133.021 273.615 132.533 274.143 132.494C274.67 132.435 275.158 132.845 275.198 133.372C275.198 133.431 275.666 138.293 276.975 141.613C277.171 142.121 276.917 142.687 276.428 142.882C276.311 142.921 276.194 142.941 276.077 142.941Z"
                                              fill="#07403F"/>
                                        <path d="M278.441 130.326C278.441 130.326 280.239 137.2 281.763 140.012L278.441 130.326Z"
                                              fill="white"/>
                                        <path d="M281.764 140.988C281.412 140.988 281.08 140.812 280.904 140.48C279.341 137.61 277.563 130.873 277.505 130.58C277.368 130.053 277.681 129.526 278.208 129.389C278.736 129.252 279.263 129.565 279.4 130.092C279.4 130.151 281.178 136.887 282.643 139.563C282.897 140.031 282.721 140.637 282.252 140.89C282.096 140.968 281.94 141.008 281.783 141.008L281.764 140.988Z"
                                              fill="#07403F"/>
                                        <path d="M280.844 129.604C280.844 129.604 283.11 136.868 284.595 138.918L280.844 129.604Z"
                                              fill="white"/>
                                        <path d="M284.598 139.875C284.305 139.875 283.992 139.738 283.817 139.484C282.273 137.376 280.163 130.639 279.909 129.877C279.753 129.37 280.046 128.823 280.554 128.647C281.081 128.491 281.609 128.784 281.785 129.291C282.39 131.264 284.246 136.751 285.399 138.313C285.712 138.742 285.634 139.367 285.184 139.68C285.008 139.816 284.813 139.875 284.598 139.875Z"
                                              fill="#07403F"/>
                                        <path d="M301.79 162.799C301.79 162.799 301.614 162.799 301.516 162.76L282.448 157.117C281.921 156.961 281.628 156.414 281.784 155.906C281.94 155.399 282.487 155.086 282.995 155.243L302.063 160.886C302.591 161.042 302.884 161.589 302.728 162.097C302.61 162.526 302.22 162.799 301.79 162.799Z"
                                              fill="#07403F"/>
                                        <path d="M254.516 160.82C254.047 160.82 253.657 160.488 253.559 160.039C253.52 159.863 252.875 155.938 258.697 153.498C261.413 152.365 266.453 152.15 266.668 152.131C267.235 152.092 267.665 152.521 267.684 153.068C267.704 153.615 267.293 154.064 266.746 154.083C266.688 154.083 261.823 154.298 259.44 155.294C255.142 157.071 255.454 159.59 255.474 159.688C255.552 160.215 255.2 160.722 254.673 160.801C254.614 160.801 254.555 160.801 254.516 160.801V160.82Z"
                                              fill="#07403F"/>
                                        <path d="M225.204 161.433C219.694 163.034 218.737 164.342 217.545 171.723C216.353 164.342 215.377 163.034 209.887 161.433C215.396 159.831 216.353 158.523 217.545 151.142C218.737 158.523 219.714 159.831 225.204 161.433Z"
                                              fill="#FFE747"/>
                                        <path d="M217.526 172.7C217.037 172.7 216.647 172.348 216.569 171.88C215.435 164.83 214.713 163.854 209.594 162.37C209.184 162.253 208.891 161.862 208.891 161.433C208.891 161.003 209.184 160.613 209.594 160.495C214.713 159.011 215.435 158.015 216.569 150.986C216.647 150.517 217.057 150.166 217.526 150.166C217.995 150.166 218.405 150.517 218.483 150.986C219.616 158.035 220.339 159.011 225.458 160.495C225.868 160.613 226.161 161.003 226.161 161.433C226.161 161.862 225.868 162.253 225.458 162.37C220.339 163.854 219.616 164.83 218.483 171.88C218.405 172.348 217.995 172.7 217.526 172.7ZM212.837 161.433C215.377 162.526 216.647 163.971 217.526 166.978C218.405 163.971 219.675 162.526 222.215 161.433C219.675 160.339 218.405 158.894 217.526 155.887C216.647 158.894 215.377 160.32 212.837 161.433Z"
                                              fill="#07403F"/>
                                        <path d="M340.867 141.3C331.899 143.878 330.317 145.967 328.363 157.84C326.409 145.967 324.846 143.878 315.859 141.3C324.827 138.723 326.409 136.633 328.363 124.761C330.317 136.633 331.88 138.723 340.867 141.3Z"
                                              fill="#FFE747"/>
                                        <path d="M328.379 158.797C327.89 158.797 327.5 158.445 327.422 157.976C325.526 146.456 324.217 144.698 315.621 142.218C315.211 142.101 314.918 141.71 314.918 141.281C314.918 140.851 315.211 140.461 315.621 140.344C324.217 137.883 325.526 136.106 327.422 124.585C327.5 124.117 327.91 123.765 328.379 123.765C328.848 123.765 329.258 124.117 329.336 124.585C331.231 136.106 332.54 137.864 341.136 140.344C341.547 140.461 341.84 140.851 341.84 141.281C341.84 141.71 341.547 142.101 341.136 142.218C332.54 144.679 331.231 146.456 329.336 157.976C329.258 158.445 328.848 158.797 328.379 158.797ZM319.099 141.3C324.706 143.312 326.816 145.792 328.359 152.587C329.922 145.772 332.032 143.292 337.62 141.3C332.013 139.289 329.903 136.809 328.359 130.014C326.796 136.829 324.686 139.309 319.099 141.3Z"
                                              fill="#07403F"/>
                                        <path d="M243.566 180.628C243.41 180.628 243.253 180.589 243.097 180.51C233.251 175.141 233.837 165.24 233.856 164.811C233.895 164.264 234.423 163.893 234.892 163.913C235.439 163.952 235.829 164.42 235.81 164.948C235.79 165.319 235.322 174.028 244.035 178.792C244.504 179.046 244.68 179.651 244.426 180.12C244.25 180.452 243.918 180.628 243.566 180.628Z"
                                              fill="#07403F"/>
                                        <path d="M247.34 175.713C247.125 175.713 246.891 175.635 246.715 175.498C241.303 171.027 240.561 165.383 240.522 165.149C240.463 164.622 240.834 164.134 241.362 164.055C241.87 163.997 242.378 164.368 242.456 164.895C242.456 164.954 243.159 170.011 247.946 173.975C248.356 174.327 248.415 174.932 248.082 175.342C247.887 175.576 247.614 175.693 247.321 175.693L247.34 175.713Z"
                                              fill="#07403F"/>
                                    </g>
                                </g>
                                <defs>
                                    <clipPath id="clip0_1448_6581">
                                        <rect width="537.537" height="248" fill="white"/>
                                    </clipPath>
                                    <clipPath id="clip1_1448_6581">
                                        <rect width="146.839" height="190.739" fill="white"
                                              transform="translate(195 34)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </template>
                        <template v-else>
                            <svg width="538" height="248" viewBox="0 0 538 248" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path d="M123.147 105.839C122.712 105.839 122.289 105.579 122.068 105.128C121.743 104.465 121.964 103.638 122.559 103.276L128.428 99.7226L122.338 93.7908C121.829 93.2919 121.774 92.4309 122.221 91.8637C122.669 91.2965 123.442 91.235 123.951 91.7338L131.366 98.9572C131.666 99.251 131.82 99.6952 131.777 100.139C131.734 100.584 131.495 100.973 131.145 101.192L123.73 105.682C123.546 105.791 123.344 105.846 123.147 105.846V105.839Z"
                                      fill="#FF5A5A"/>
                                <path d="M135.805 97.7175C135.359 97.7175 134.946 97.4911 134.716 97.1136L130.176 89.6974C129.813 89.0998 130.004 88.3324 130.609 87.9675C131.214 87.609 131.997 87.7977 132.36 88.389L135.531 93.5659L137.391 88.6029C137.639 87.955 138.365 87.6216 139.027 87.8606C139.69 88.1059 140.021 88.8293 139.779 89.4772L137.009 96.8872C136.837 97.3401 136.423 97.6609 135.933 97.7049C135.894 97.7049 135.85 97.7049 135.812 97.7049L135.805 97.7175Z"
                                      fill="#FF5A5A"/>
                                <path d="M141.322 114.877C140.886 114.877 140.463 114.627 140.242 114.187L136.429 106.595L132.065 111.041C131.574 111.54 130.794 111.514 130.327 110.989C129.861 110.463 129.885 109.629 130.376 109.13L135.895 103.501C136.172 103.219 136.558 103.087 136.939 103.153C137.32 103.219 137.645 103.468 137.829 103.829L142.403 112.939C142.722 113.576 142.501 114.378 141.905 114.719C141.721 114.824 141.519 114.877 141.328 114.877H141.322Z"
                                      fill="#FF5A5A"/>
                                <path d="M146.589 106.749C146.247 106.749 145.905 106.63 145.644 106.399L140.247 101.631C139.751 101.193 139.724 100.488 140.18 100.02L143.498 96.638C143.498 96.638 143.545 96.5847 143.565 96.561L145.577 94.5116C146.059 94.02 146.911 93.9667 147.467 94.3932C148.024 94.8196 148.084 95.5719 147.601 96.0635L145.657 98.0418C145.657 98.0418 145.61 98.0951 145.59 98.1188L143.022 100.737L147.534 104.723C148.057 105.185 148.057 105.937 147.534 106.399C147.273 106.63 146.931 106.749 146.589 106.749Z"
                                      fill="#FF5A5A"/>
                                <path d="M108.012 160.826C108.012 160.481 108.22 160.146 108.581 159.971C109.111 159.714 109.772 159.888 110.062 160.359L112.905 165.006L117.65 160.185C118.049 159.782 118.738 159.738 119.192 160.092C119.646 160.447 119.695 161.059 119.296 161.462L113.517 167.332C113.282 167.57 112.927 167.691 112.571 167.657C112.216 167.623 111.904 167.434 111.73 167.157L108.138 161.287C108.05 161.141 108.006 160.981 108.006 160.826L108.012 160.826Z"
                                      fill="#FF5A5A"/>
                                <path d="M114.513 170.845C114.513 170.492 114.695 170.164 114.997 169.982L120.93 166.388C121.408 166.101 122.022 166.252 122.313 166.731C122.6 167.21 122.449 167.83 121.976 168.117L117.835 170.628L121.805 172.1C122.324 172.296 122.59 172.871 122.399 173.395C122.203 173.92 121.624 174.182 121.106 173.99L115.178 171.797C114.815 171.661 114.559 171.334 114.524 170.945C114.524 170.915 114.524 170.88 114.524 170.85L114.513 170.845Z"
                                      fill="#FF5A5A"/>
                                <path d="M100.787 175.212C100.787 174.867 100.987 174.532 101.339 174.357L107.413 171.339L103.856 167.883C103.456 167.495 103.477 166.877 103.898 166.508C104.318 166.139 104.986 166.158 105.385 166.547L109.888 170.916C110.114 171.135 110.219 171.441 110.166 171.742C110.114 172.044 109.914 172.301 109.625 172.447L102.337 176.068C101.828 176.32 101.187 176.145 100.913 175.674C100.829 175.528 100.787 175.368 100.787 175.217L100.787 175.212Z"
                                      fill="#FF5A5A"/>
                                <path d="M107.287 179.384C107.287 179.113 107.382 178.842 107.567 178.635L111.381 174.363C111.732 173.97 112.296 173.949 112.67 174.31L115.376 176.937C115.376 176.937 115.419 176.974 115.438 176.99L117.077 178.582C117.47 178.964 117.513 179.639 117.172 180.079C116.831 180.52 116.229 180.567 115.836 180.185L114.253 178.646C114.253 178.646 114.21 178.609 114.191 178.593L112.097 176.56L108.908 180.132C108.538 180.546 107.937 180.546 107.567 180.132C107.382 179.925 107.287 179.654 107.287 179.384Z"
                                      fill="#FF5A5A"/>
                                <path d="M382.43 165.053C382.053 165.572 381.379 165.853 380.654 165.728C379.588 165.545 378.801 164.57 378.888 163.55L379.768 153.501L367.475 155.653C366.444 155.831 365.377 155.156 365.094 154.134C364.811 153.113 365.407 152.14 366.438 151.962L381.407 149.343C382.015 149.238 382.673 149.437 383.162 149.87C383.65 150.304 383.904 150.924 383.86 151.528L382.748 164.224C382.718 164.537 382.608 164.825 382.438 165.059L382.43 165.053Z"
                                      fill="#FF5A5A"/>
                                <path d="M383.781 142.99C383.395 143.521 382.768 143.82 382.123 143.768L369.416 142.797C368.394 142.716 367.652 141.828 367.744 140.793C367.844 139.764 368.746 138.993 369.76 139.07L378.632 139.745L374.371 133.26C373.819 132.407 374.054 131.256 374.91 130.672C375.774 130.094 376.917 130.322 377.474 131.168L383.841 140.842C384.228 141.437 384.249 142.206 383.876 142.828C383.843 142.873 383.804 142.926 383.771 142.972L383.781 142.99Z"
                                      fill="#FF5A5A"/>
                                <path d="M408.861 151.179C408.483 151.698 407.821 151.988 407.109 151.873L394.824 149.883L396.304 158.908C396.469 159.923 395.762 160.829 394.736 160.933C393.711 161.036 392.745 160.289 392.58 159.275L390.701 147.858C390.606 147.286 390.786 146.712 391.193 146.315C391.601 145.918 392.178 145.746 392.765 145.837L407.504 148.225C408.535 148.393 409.291 149.346 409.179 150.349C409.144 150.659 409.031 150.945 408.866 151.172L408.861 151.179Z"
                                      fill="#FF5A5A"/>
                                <path d="M403.807 137.909C403.511 138.317 403.075 138.622 402.575 138.735L392.259 141.063C391.31 141.277 390.453 140.702 390.295 139.757L389.168 132.894C389.168 132.894 389.145 132.792 389.135 132.748L388.452 128.588C388.289 127.591 388.963 126.53 389.95 126.234C390.937 125.938 391.879 126.514 392.042 127.511L392.699 131.529C392.699 131.529 392.721 131.631 392.732 131.676L393.605 136.987L402.229 135.041C403.229 134.815 404.119 135.462 404.212 136.483C404.259 136.993 404.103 137.502 403.807 137.909Z"
                                      fill="#FF5A5A"/>
                                <g clip-path="url(#clip0_1448_6788)">
                                    <path d="M284.599 19.7911C284.599 19.7911 266.078 84.8931 327.182 92.5465C327.182 92.5465 344.208 66.9654 325.668 42.6946C309.313 21.2914 284.599 19.7911 284.599 19.7911Z"
                                          fill="#57D061"/>
                                    <path d="M327.164 93.4961C327.164 93.4961 327.087 93.4961 327.048 93.4961C309.812 91.3501 297.196 84.4373 289.527 72.9856C274.917 51.1457 283.583 19.8671 283.66 19.5443C283.775 19.1075 284.197 18.8416 284.638 18.8606C284.887 18.8606 310.004 20.6268 326.416 42.1248C345.129 66.6425 328.122 92.8124 327.95 93.0593C327.777 93.3252 327.47 93.4961 327.144 93.4961H327.164ZM285.309 20.8167C284.101 25.6974 278.253 52.703 291.118 71.9411C298.366 82.7661 310.33 89.356 326.665 91.521C328.851 87.8747 341.371 64.8763 324.882 43.2643C310.636 24.5959 289.623 21.3104 285.309 20.8167Z"
                                          fill="#07403F"/>
                                    <path d="M330.156 83.6016C328.68 114.5 306.266 133.89 267.69 132.2C234.905 130.757 209.941 108.955 211.418 78.0562C212.894 47.1574 240.676 23.3424 273.461 24.8807C306.247 26.419 331.632 52.7029 330.175 83.6016H330.156Z"
                                          fill="white"/>
                                    <path d="M272.386 133.245C270.833 133.245 269.261 133.207 267.67 133.131C249.666 132.333 233.925 125.591 223.342 114.101C214.273 104.245 209.825 91.7678 210.477 77.9991C211.205 62.7872 218.165 48.7906 230.071 38.5733C241.978 28.375 257.374 23.1714 273.498 23.9121C306.763 25.4694 332.608 52.247 331.113 83.6205C329.617 114.918 307.779 133.226 272.367 133.226L272.386 133.245ZM270.45 25.7542C255.917 25.7542 242.131 30.7679 231.318 40.0167C219.795 49.8921 213.084 63.4139 212.375 78.0941C211.742 91.35 216.018 103.371 224.741 112.829C234.961 123.92 250.241 130.472 267.727 131.232C304.673 132.865 327.681 115.032 329.196 83.5446C330.634 53.2156 305.613 27.3305 273.402 25.8302C272.405 25.7922 271.428 25.7542 270.431 25.7542H270.45Z"
                                          fill="#07403F"/>
                                    <path d="M252.619 19.373C252.619 19.373 278.598 87.7225 213.583 101.51C213.583 101.51 191.956 74.8464 213.314 44.0616C229.036 21.4241 252.619 19.373 252.619 19.373Z"
                                          fill="#57D061"/>
                                    <path d="M213.583 102.46C213.296 102.46 213.027 102.327 212.835 102.099C212.605 101.833 191.017 74.5237 212.529 43.5301C228.308 20.7786 252.293 18.4426 252.524 18.4236C252.945 18.3857 253.348 18.6325 253.501 19.0314C253.636 19.3542 265.849 52.1901 252.178 76.9926C245.008 90.0016 232.085 98.5667 213.775 102.441C213.717 102.441 213.641 102.46 213.583 102.46ZM251.968 20.4177C247.903 21.0064 227.829 24.8427 214.101 44.6126C194.928 72.2448 211.321 96.8005 213.967 100.466C231.395 96.6485 243.666 88.4443 250.491 76.1C262.647 54.0512 253.616 25.2605 251.968 20.4177Z"
                                          fill="#07403F"/>
                                    <path d="M302.888 109.791C301.891 109.791 300.914 109.278 300.396 108.347C300.358 108.271 298.69 105.669 294.682 105.517C294.587 105.517 294.472 105.517 294.376 105.517C290.599 105.517 288.259 108.062 288.164 108.176C287.109 109.335 285.307 109.468 284.118 108.423C282.929 107.379 282.795 105.612 283.831 104.435C284.003 104.245 288.183 99.5922 294.912 99.8201C302.198 100.086 305.266 105.29 305.4 105.498C306.186 106.866 305.707 108.613 304.326 109.392C303.885 109.639 303.387 109.772 302.908 109.772L302.888 109.791Z"
                                          fill="#D3D3D3"/>
                                    <path d="M294.449 91.54C294.027 91.54 293.644 91.2551 293.529 90.8373L289.349 75.8532C289.215 75.3405 289.503 74.8277 290.02 74.6948C290.538 74.5618 291.056 74.8467 291.209 75.3595L295.389 90.3435C295.523 90.8563 295.235 91.3691 294.718 91.502C294.641 91.521 294.545 91.54 294.468 91.54H294.449Z"
                                          fill="#07403F"/>
                                    <path d="M300.872 91.54C300.757 91.54 300.642 91.5211 300.527 91.4831C300.028 91.2932 299.779 90.7424 299.971 90.2486L305.358 76.48C305.55 75.9862 306.106 75.7583 306.605 75.9292C307.103 76.1192 307.352 76.6699 307.161 77.1637L301.773 90.9323C301.62 91.3121 301.255 91.54 300.872 91.54Z"
                                          fill="#07403F"/>
                                    <path d="M272.975 95.87L275.928 90.5905L290.269 97.8261L287.566 101.776L277.078 99.1935L271.844 97.9021L272.975 95.87Z"
                                          fill="#FFE747"/>
                                    <path d="M287.566 102.726C287.566 102.726 287.413 102.726 287.336 102.707L271.614 98.8327C271.327 98.7567 271.077 98.5668 270.962 98.2819C270.847 98.016 270.866 97.6932 271.001 97.4463L275.084 90.1347C275.334 89.6789 275.909 89.527 276.35 89.7548L290.691 96.9905C290.94 97.1045 291.113 97.3323 291.19 97.5982C291.266 97.8641 291.209 98.149 291.056 98.3579L288.371 102.308C288.199 102.574 287.892 102.726 287.566 102.726ZM273.282 97.2944L287.144 100.713L288.851 98.187L276.312 91.8629L273.8 96.3448L273.263 97.2944H273.282Z"
                                          fill="#07403F"/>
                                    <path d="M273.454 108.157C273.071 108.157 272.706 107.929 272.553 107.55C272.361 107.056 272.61 106.505 273.109 106.315L287.239 100.884C287.738 100.694 288.294 100.941 288.486 101.434C288.677 101.928 288.428 102.479 287.93 102.669L273.799 108.1C273.684 108.138 273.569 108.157 273.454 108.157Z"
                                          fill="#07403F"/>
                                    <path d="M319.105 98.4719L315.212 91.5021L307.045 95.6042L300.871 98.7187L303.555 102.688L308.042 101.586L318.28 99.0606L319.296 98.8137L319.105 98.4719Z"
                                          fill="#FFE747"/>
                                    <path d="M303.555 103.637C303.249 103.637 302.942 103.486 302.75 103.22L300.066 99.2695C299.912 99.0416 299.874 98.7567 299.932 98.5098C300.008 98.2439 300.181 98.016 300.43 97.9021L314.772 90.6664C315.232 90.4385 315.788 90.6095 316.037 91.0463L319.929 98.016L320.121 98.3579C320.255 98.6238 320.274 98.9276 320.159 99.1935C320.044 99.4594 319.795 99.6683 319.507 99.7443L303.785 103.618C303.785 103.618 303.632 103.637 303.555 103.637ZM302.271 99.0796L303.977 101.605L317.839 98.187L314.81 92.7555L302.271 99.0796Z"
                                          fill="#07403F"/>
                                    <path d="M318.668 109.183C318.553 109.183 318.438 109.183 318.342 109.126L303.234 103.561C302.735 103.372 302.486 102.84 302.678 102.346C302.87 101.852 303.426 101.605 303.905 101.795L319.013 107.36C319.512 107.55 319.761 108.081 319.569 108.575C319.416 108.955 319.052 109.202 318.668 109.202V109.183Z"
                                          fill="#07403F"/>
                                    <path d="M298.516 116.343C298.171 116.343 297.826 116.153 297.653 115.811L295.084 110.683L288.795 116.096C288.393 116.438 287.779 116.4 287.434 116.001C287.089 115.602 287.127 115.013 287.53 114.652L294.758 108.423C294.988 108.233 295.295 108.157 295.582 108.214C295.87 108.271 296.119 108.461 296.254 108.727L299.36 114.956C299.59 115.431 299.398 116.001 298.919 116.229C298.784 116.305 298.631 116.324 298.497 116.324L298.516 116.343Z"
                                          fill="#07403F"/>
                                    <path d="M280.512 98.7187C281.073 98.7187 281.528 97.7154 281.528 96.4778C281.528 95.2401 281.073 94.2368 280.512 94.2368C279.951 94.2368 279.496 95.2401 279.496 96.4778C279.496 97.7154 279.951 98.7187 280.512 98.7187Z"
                                          fill="#07403F"/>
                                    <path d="M280.514 99.6683C279.344 99.6683 278.539 98.3579 278.539 96.4778C278.539 94.5976 279.344 93.2872 280.514 93.2872C281.683 93.2872 282.489 94.5976 282.489 96.4778C282.489 98.3579 281.683 99.6683 280.514 99.6683ZM280.514 95.8321C280.476 96.022 280.456 96.2309 280.456 96.4778C280.456 96.7246 280.476 96.9335 280.514 97.1235C280.552 96.9335 280.571 96.7246 280.571 96.4778C280.571 96.2309 280.552 96.022 280.514 95.8321Z"
                                          fill="#07403F"/>
                                    <path d="M307.317 96.1549C307.413 96.0979 307.528 96.0599 307.643 96.0599C308.256 96.0599 308.755 97.0665 308.755 98.3009C308.755 99.4594 308.314 100.409 307.739 100.523C307.7 100.523 307.662 100.523 307.624 100.523C307.01 100.523 306.512 99.5163 306.512 98.2819C306.512 97.3134 306.857 96.4398 307.298 96.1549H307.317Z"
                                          fill="#07403F"/>
                                    <path d="M307.679 101.472C307.679 101.472 307.583 101.472 307.545 101.472C306.471 101.51 305.57 100.143 305.57 98.2819C305.57 96.9715 306.05 95.832 306.797 95.3572C307.047 95.2053 307.334 95.1104 307.641 95.1104C308.849 95.1104 309.712 96.4587 309.712 98.3009C309.712 99.9721 309.002 101.245 307.929 101.453C307.871 101.453 307.756 101.453 307.66 101.453L307.679 101.472ZM307.66 97.2943C307.583 97.5412 307.507 97.8641 307.507 98.2819C307.507 98.7187 307.583 99.0605 307.66 99.2884C307.737 99.0605 307.814 98.7377 307.814 98.3009C307.814 97.8641 307.737 97.5412 307.66 97.3133V97.2943Z"
                                          fill="#07403F"/>
                                    <path d="M263.78 190.351C263.78 190.351 263.416 193.599 263.569 195.365C263.722 197.131 275.744 198.764 281.975 197.397C281.975 197.397 283.892 195.422 283.95 193.523L263.78 190.351Z"
                                          fill="#CEBEAA"/>
                                    <path d="M276.184 198.859C270.873 198.859 265.122 197.891 263.396 196.656C262.917 196.315 262.667 195.916 262.629 195.46C262.476 193.637 262.821 190.389 262.84 190.256C262.878 189.99 262.993 189.763 263.204 189.611C263.415 189.459 263.684 189.383 263.933 189.421L284.103 192.592C284.582 192.668 284.927 193.086 284.908 193.561C284.831 195.745 282.876 197.815 282.665 198.043C282.531 198.176 282.358 198.271 282.185 198.309C280.479 198.688 278.351 198.84 276.165 198.84L276.184 198.859ZM264.527 195.118C265.85 196.258 275.705 197.682 281.495 196.523C281.898 196.049 282.55 195.175 282.857 194.301L264.661 191.453C264.565 192.478 264.47 194.055 264.527 195.118Z"
                                          fill="#07403F"/>
                                    <path d="M265.39 184.255C265.39 184.255 262.591 190.218 265.122 191.623C267.653 193.029 280.134 195.384 282.838 193.371C285.541 191.358 280.498 188.11 279.195 187.464C277.891 186.819 275.705 184.635 275.705 184.635L265.39 184.255Z"
                                          fill="white"/>
                                    <path d="M278.674 195.061C273.555 195.061 266.423 193.409 264.659 192.421C261.438 190.636 264.199 184.521 264.525 183.837C264.678 183.495 264.985 183.248 265.426 183.286L275.741 183.666C275.99 183.666 276.22 183.78 276.393 183.951C276.968 184.54 278.674 186.135 279.614 186.591C279.652 186.61 284.292 188.984 284.56 191.719C284.618 192.364 284.484 193.295 283.41 194.092C282.49 194.776 280.745 195.061 278.674 195.061ZM266.001 185.224C265.138 187.313 264.486 190.18 265.579 190.788C268.072 192.174 280.074 194.225 282.241 192.592C282.682 192.269 282.643 192.022 282.643 191.908C282.528 190.731 280.016 188.908 278.77 188.3C277.601 187.73 275.952 186.192 275.3 185.546L266.001 185.205V185.224Z"
                                          fill="#07403F"/>
                                    <path d="M270.968 192.497C270.968 192.497 281.398 185.66 282.912 190.351C282.912 190.351 277.046 190.94 273.786 193.751C273.786 193.751 270.623 194.169 270.968 192.497Z"
                                          fill="#CEBEAA"/>
                                    <path d="M272.963 194.757C272.158 194.757 271.084 194.605 270.47 193.96C270.068 193.523 269.914 192.953 270.029 192.307C270.087 192.06 270.221 191.833 270.432 191.7C271.218 191.187 278.178 186.724 281.821 187.996C282.799 188.338 283.489 189.06 283.815 190.066C283.911 190.332 283.872 190.636 283.7 190.883C283.546 191.13 283.278 191.282 282.99 191.301C282.933 191.301 277.373 191.889 274.401 194.472C274.267 194.586 274.094 194.681 273.902 194.7C273.711 194.719 273.365 194.757 272.944 194.757H272.963ZM272.253 192.82C272.56 192.858 272.982 192.877 273.404 192.839C275.666 191.016 278.887 190.123 280.92 189.724C278.926 189.326 274.88 191.225 272.253 192.82Z"
                                          fill="#07403F"/>
                                    <path d="M255.745 192.003C255.745 192.003 255.38 195.251 255.534 197.017C255.687 198.783 267.709 200.417 273.94 199.049C273.94 199.049 275.857 197.074 275.915 195.175L255.745 192.003Z"
                                          fill="#CEBEAA"/>
                                    <path d="M268.264 200.493C262.934 200.493 257.028 199.524 255.341 198.309C254.862 197.967 254.613 197.568 254.574 197.112C254.421 195.289 254.766 192.06 254.785 191.909C254.824 191.643 254.939 191.415 255.15 191.263C255.36 191.111 255.629 191.035 255.878 191.073L276.048 194.244C276.527 194.32 276.872 194.738 276.853 195.213C276.777 197.397 274.821 199.467 274.61 199.695C274.476 199.828 274.303 199.923 274.131 199.961C272.482 200.322 270.411 200.474 268.245 200.474L268.264 200.493ZM256.472 196.77C257.795 197.91 267.65 199.334 273.441 198.176C273.843 197.701 274.495 196.827 274.802 195.954L256.607 193.105C256.511 194.131 256.415 195.707 256.472 196.77Z"
                                          fill="#07403F"/>
                                    <path d="M257.339 185.888C257.339 185.888 254.54 191.852 257.071 193.257C259.602 194.662 272.083 197.017 274.787 195.004C277.49 192.991 272.448 189.744 271.144 189.098C269.84 188.452 267.654 186.268 267.654 186.268L257.339 185.888Z"
                                          fill="white"/>
                                    <path d="M270.624 196.713C265.504 196.713 258.372 195.061 256.608 194.074C253.387 192.288 256.148 186.173 256.474 185.49C256.627 185.148 256.896 184.901 257.375 184.939L267.69 185.319C267.939 185.319 268.169 185.433 268.342 185.603C268.917 186.192 270.624 187.787 271.563 188.243C271.601 188.262 276.241 190.636 276.51 193.371C276.567 194.017 276.433 194.947 275.359 195.745C274.439 196.447 272.694 196.713 270.624 196.713ZM257.969 186.857C257.107 188.946 256.455 191.814 257.548 192.421C260.04 193.808 272.062 195.859 274.228 194.226C274.669 193.903 274.631 193.656 274.631 193.542C274.516 192.364 272.004 190.541 270.739 189.933C269.569 189.364 267.939 187.825 267.268 187.18L257.969 186.838V186.857Z"
                                          fill="#07403F"/>
                                    <path d="M262.933 194.149C262.933 194.149 273.363 187.313 274.877 192.003C274.877 192.003 269.01 192.592 265.751 195.403C265.751 195.403 262.587 195.821 262.933 194.149Z"
                                          fill="#CEBEAA"/>
                                    <path d="M264.928 196.39C264.122 196.39 263.049 196.239 262.435 195.593C262.033 195.156 261.879 194.586 261.994 193.941C262.052 193.694 262.186 193.466 262.397 193.333C263.183 192.82 270.162 188.357 273.786 189.63C274.763 189.971 275.454 190.693 275.78 191.7C275.875 191.966 275.837 192.269 275.665 192.516C275.511 192.763 275.243 192.915 274.955 192.934C274.898 192.934 269.337 193.523 266.366 196.106C266.231 196.22 266.059 196.314 265.867 196.333C265.675 196.352 265.33 196.39 264.908 196.39H264.928ZM264.218 194.453C264.525 194.491 264.947 194.51 265.369 194.472C267.631 192.649 270.852 191.757 272.884 191.358C270.871 190.959 266.845 192.858 264.218 194.453Z"
                                          fill="#07403F"/>
                                    <path d="M277.636 132.637C271.712 129.351 253.593 116.722 248.378 136.112C243.163 155.502 250.698 179.906 253.363 184.198C253.363 184.198 269.123 189.307 284.999 185.338C284.999 185.338 293.454 163.175 284.615 132.922L277.655 132.656L277.636 132.637Z"
                                          fill="#57D061"/>
                                    <path d="M271.981 187.787C261.589 187.787 253.518 185.224 253.057 185.072C252.847 184.996 252.655 184.863 252.54 184.673C249.587 179.906 242.206 155.35 247.44 135.846C248.897 130.415 251.504 126.902 255.186 125.42C262.261 122.553 271.464 127.927 276.391 130.814C276.947 131.137 277.445 131.441 277.886 131.687L284.616 131.953C285.038 131.953 285.383 132.238 285.498 132.637C294.337 162.852 285.958 185.433 285.862 185.66C285.747 185.945 285.498 186.173 285.191 186.249C280.628 187.389 276.103 187.806 271.943 187.806L271.981 187.787ZM253.997 183.382C256.336 184.065 270.294 187.844 284.252 184.521C285.364 181.217 291.538 160.706 283.868 133.814L277.599 133.568C277.445 133.568 277.292 133.53 277.177 133.454C276.679 133.169 276.084 132.827 275.432 132.447C270.773 129.731 262.126 124.661 255.933 127.167C252.847 128.421 250.603 131.497 249.319 136.321C244.315 154.895 251.351 178.52 254.016 183.363L253.997 183.382Z"
                                          fill="#07403F"/>
                                    <path d="M246.333 144.81C246.333 144.81 245.393 146.501 244.876 147.602C244.358 148.704 244.396 158.883 245.796 159.737C245.796 159.737 264.202 167.372 288.782 158.807C288.782 158.807 290.488 158.275 290.431 156.927C290.373 155.578 289.05 141.943 288.207 141.43C287.363 140.917 286.711 142.095 286.711 142.095C286.711 142.095 257.913 149.748 246.333 144.791V144.81Z"
                                          fill="#CCFFCE"/>
                                    <path d="M265.158 163.858C253.424 163.858 245.889 160.82 245.391 160.611C243.186 159.244 243.358 148.476 243.991 147.184C244.528 146.064 245.486 144.354 245.486 144.354C245.717 143.918 246.253 143.747 246.714 143.937C257.182 148.419 282.855 142.113 286.114 141.278C286.363 140.955 286.766 140.556 287.341 140.404C287.801 140.29 288.281 140.347 288.702 140.613C290.255 141.563 291.367 156.737 291.387 156.889C291.444 158.522 289.949 159.433 289.067 159.699C280.075 162.833 271.926 163.839 265.177 163.839L265.158 163.858ZM246.311 158.94C248.209 159.661 265.752 165.834 288.453 157.914C288.491 157.914 289.488 157.553 289.469 156.965C289.412 155.388 288.319 144.924 287.629 142.455C287.609 142.493 287.571 142.531 287.552 142.569C287.418 142.797 287.207 142.949 286.958 143.025C285.788 143.329 258.946 150.375 246.771 146.007C246.464 146.576 246.023 147.393 245.736 148.02C245.314 149.159 245.486 157.61 246.311 158.94Z"
                                          fill="#07403F"/>
                                    <path d="M278.849 147.203C278.849 147.203 280.862 145.57 281.553 145.304C282.243 145.038 283.911 146.235 283.067 147.355C282.224 148.476 281.303 148.874 281.476 149.178C281.648 149.482 284.754 149.786 285.253 149.862C285.751 149.938 286.787 151.609 285.157 152.521C285.157 152.521 281.246 152.559 281.284 152.806C281.322 153.053 284.544 154.021 284.639 154.933C284.735 155.844 283.738 157.572 280.498 156.053C280.498 156.053 283.01 157.06 282.128 158.104C281.246 159.149 277.277 158.009 276.568 156.433C275.858 154.857 277.469 147.317 278.887 147.203H278.849Z"
                                          fill="white"/>
                                    <path d="M280.861 159.472C280.631 159.472 280.382 159.472 280.132 159.415C278.407 159.187 276.298 158.218 275.665 156.813C274.86 155.047 276.087 149.406 277.237 147.45C277.372 147.203 277.755 146.558 278.407 146.33C279.04 145.817 280.497 144.696 281.187 144.412C282.088 144.032 283.181 144.62 283.756 145.361C284.389 146.159 284.408 147.146 283.833 147.925C283.622 148.21 283.411 148.438 283.219 148.647C283.814 148.742 284.446 148.817 284.753 148.836C285.06 148.874 285.29 148.893 285.405 148.912C286.21 149.045 286.805 149.957 286.939 150.755C287.131 151.799 286.632 152.768 285.616 153.337C285.328 153.508 284.983 153.451 284.638 153.47C285.175 153.85 285.539 154.287 285.578 154.838C285.635 155.483 285.386 156.452 284.504 157.079C284.197 157.288 283.795 157.477 283.258 157.553C283.277 157.952 283.143 158.37 282.855 158.712C282.433 159.206 281.762 159.472 280.861 159.472ZM279.097 148.115C278.177 148.969 276.892 154.914 277.41 156.034C277.602 156.452 278.445 157.06 279.672 157.383C280.382 157.572 280.899 157.572 281.187 157.534C280.995 157.383 280.708 157.193 280.382 157.041C280.286 157.003 280.171 156.946 280.075 156.908C279.596 156.68 279.404 156.148 279.596 155.673C279.807 155.199 280.363 154.99 280.823 155.18C280.919 155.218 281.034 155.275 281.187 155.331C282.165 155.73 282.97 155.806 283.373 155.54C283.622 155.369 283.66 155.104 283.641 155.028C283.469 154.914 282.222 154.344 281.743 154.135C280.842 153.736 280.42 153.565 280.305 152.996C280.248 152.711 280.324 152.407 280.497 152.198C280.784 151.856 280.957 151.628 284.811 151.59C285.002 151.438 285.022 151.305 285.022 151.267C285.022 151.077 284.945 150.888 284.868 150.793C284.772 150.793 284.638 150.755 284.485 150.755C281.494 150.394 280.919 150.242 280.593 149.672C280.132 148.836 280.861 148.21 281.264 147.868C281.551 147.602 281.916 147.298 282.261 146.842C282.184 146.463 281.858 146.273 281.743 146.254C281.551 146.368 280.343 147.241 279.423 147.982C279.308 148.058 279.193 148.134 279.059 148.172L279.097 148.115Z"
                                          fill="#07403F"/>
                                    <path d="M248.379 136.093C248.379 136.093 247.075 148.627 253.574 155.749C253.574 155.749 243.183 172.348 245.637 175.215C248.091 178.083 258.981 180.685 262.758 181.349C266.037 181.938 274.55 161.352 277.483 151.039C278.097 148.893 277.1 146.633 275.106 145.589C272.115 144.013 268.299 141.544 269.239 139.512"
                                          fill="#57D061"/>
                                    <path d="M262.833 182.337C262.833 182.337 262.661 182.337 262.584 182.318C262.45 182.299 248.109 179.602 244.907 175.861C242.318 172.841 249.527 160.478 252.384 155.863C246.211 148.457 247.38 136.53 247.418 136.017C247.476 135.505 247.974 135.106 248.473 135.163C249.01 135.22 249.374 135.676 249.336 136.207C249.336 136.321 248.147 148.4 254.282 155.142C254.57 155.464 254.608 155.92 254.378 156.281C249.547 164.01 245.156 173.24 246.364 174.646C248.415 177.038 258.174 179.621 262.929 180.476C265.173 180.191 273.091 163.023 276.542 150.85C277.021 149.14 276.235 147.355 274.625 146.501C269.467 143.785 267.339 141.335 268.336 139.189C268.566 138.714 269.122 138.505 269.602 138.714C270.081 138.942 270.292 139.493 270.081 139.968C269.717 140.765 271.25 142.569 275.526 144.81C277.942 146.083 279.111 148.76 278.383 151.343C276.791 156.927 267.818 182.356 262.814 182.356L262.833 182.337Z"
                                          fill="#07403F"/>
                                    <path d="M256.086 155.844C256.086 155.844 264.637 159.054 269.219 155.028L256.086 155.844Z"
                                          fill="#57D061"/>
                                    <path d="M262.855 158.009C259.212 158.009 255.972 156.813 255.742 156.737C255.243 156.547 254.994 155.996 255.186 155.521C255.377 155.047 255.914 154.781 256.413 154.971C256.49 155.009 264.465 157.914 268.568 154.325C268.952 153.983 269.565 154.021 269.93 154.401C270.275 154.8 270.237 155.388 269.853 155.749C267.878 157.496 265.271 158.009 262.855 158.009Z"
                                          fill="#07403F"/>
                                    <path d="M285.537 137.328C285.288 137.328 285.038 137.233 284.847 137.024L281.242 133.264C280.878 132.884 280.897 132.276 281.28 131.915C281.664 131.554 282.277 131.573 282.642 131.953L286.246 135.713C286.611 136.093 286.591 136.701 286.208 137.062C286.016 137.233 285.786 137.328 285.556 137.328H285.537Z"
                                          fill="#07403F"/>
                                    <path d="M283.795 143.785C283.795 143.785 283.68 143.785 283.623 143.785C283.105 143.69 282.76 143.196 282.856 142.683L284.236 135.239C284.332 134.726 284.831 134.365 285.348 134.479C285.866 134.574 286.211 135.068 286.115 135.581L284.735 143.025C284.658 143.481 284.255 143.804 283.795 143.804V143.785Z"
                                          fill="#07403F"/>
                                    <path d="M272.522 187.787C272.522 187.787 272.369 187.787 272.292 187.768C271.774 187.654 271.468 187.142 271.583 186.629L277.756 161.352C277.891 160.839 278.389 160.535 278.907 160.649C279.424 160.763 279.731 161.276 279.616 161.788L273.442 187.066C273.327 187.502 272.944 187.787 272.503 187.787H272.522Z"
                                          fill="#07403F"/>
                                    <path d="M264.88 69.6575C264.732 69.8606 264.468 69.9705 264.185 69.9216C263.767 69.85 263.459 69.4685 263.493 69.0691L263.838 65.1349L259.025 65.9776C258.621 66.047 258.204 65.7827 258.093 65.3829C257.982 64.9831 258.216 64.602 258.619 64.5325L264.48 63.5071C264.718 63.4659 264.975 63.544 265.167 63.7136C265.358 63.8832 265.457 64.1259 265.44 64.3626L265.005 69.3329C264.993 69.4555 264.95 69.5683 264.883 69.6599L264.88 69.6575Z"
                                          fill="#FF5A5A"/>
                                    <path d="M265.412 61.0202C265.261 61.2281 265.016 61.3449 264.763 61.3247L259.789 60.9446C259.389 60.9127 259.098 60.5651 259.134 60.1601C259.173 59.7572 259.526 59.4554 259.923 59.4853L263.397 59.7497L261.729 57.211C261.513 56.877 261.604 56.4261 261.94 56.1978C262.278 55.9715 262.725 56.0607 262.943 56.3917L265.436 60.1792C265.587 60.412 265.596 60.713 265.45 60.9566C265.437 60.9744 265.422 60.9952 265.409 61.013L265.412 61.0202Z"
                                          fill="#FF5A5A"/>
                                    <path d="M275.229 64.226C275.081 64.4293 274.822 64.5428 274.543 64.4977L269.734 63.7186L270.313 67.2518C270.378 67.649 270.101 68.0037 269.7 68.0444C269.298 68.085 268.92 67.7926 268.856 67.3955L268.12 62.9259C268.083 62.7019 268.153 62.4773 268.313 62.3219C268.472 62.1665 268.698 62.0988 268.928 62.1346L274.698 63.0696C275.102 63.1353 275.398 63.5082 275.354 63.9009C275.34 64.0222 275.296 64.1344 275.231 64.2232L275.229 64.226Z"
                                          fill="#FF5A5A"/>
                                    <path d="M273.253 59.0311C273.137 59.1906 272.966 59.3102 272.771 59.3543L268.732 60.2656C268.361 60.3493 268.025 60.1244 267.963 59.7542L267.522 57.0675C267.522 57.0675 267.513 57.0276 267.509 57.0103L267.242 55.382C267.178 54.9913 267.442 54.5763 267.828 54.4604C268.214 54.3445 268.583 54.5697 268.647 54.9604L268.904 56.5334C268.904 56.5334 268.913 56.5733 268.917 56.5906L269.259 58.6699L272.635 57.908C273.027 57.8197 273.375 58.0731 273.412 58.4726C273.43 58.6723 273.369 58.8717 273.253 59.0311Z"
                                          fill="#FF5A5A"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_1448_6788">
                                        <rect width="132.217" height="182.069" fill="white"
                                              transform="translate(203 18.4236)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </template>
                    </div>
                    <div class="flex items-center justify-items-center justify-content-center min-h-[50px]">
                        <h4 class="mx-[5px] my-0 text-[#07403F] text-[16px] leading-relaxed">NHẬN ĐƯỢC <span
                                    class="text-[#41A336]">@{{ userResult.correct_answer * 10 }}</span>
                        </h4>

                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                             class="mr-[10px]">
                            <path d="M6.83765 18.4944C2.27709 10.8935 8.35783 3.29255 21.2794 4.05264C22.0395 16.9742 14.4386 23.0549 6.83765 18.4944Z"
                                  fill="#96D962" stroke="#41A336" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round"/>
                            <path d="M16 9.33337L4 21.3334" stroke="#41A336" stroke-width="2" stroke-linecap="round"
                                  stroke-linejoin="round"/>
                        </svg>
                        <el-tooltip placement="right" class="icon-wrapper" effect="customized">
                            <div slot="content">Có thể dùng để đổi<br/>voucher giảm học phí!</div>
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="11" stroke="#D9D9D9" stroke-width="2"/>
                                <path d="M10.5 7.065C10.5 6.745 10.625 6.485 10.875 6.285C11.105 6.095 11.405 6 11.775 6C12.145 6 12.45 6.095 12.69 6.285C12.93 6.485 13.05 6.745 13.05 7.065V7.095C13.05 7.415 12.93 7.675 12.69 7.875C12.45 8.075 12.145 8.175 11.775 8.175C11.405 8.175 11.105 8.075 10.875 7.875C10.625 7.675 10.5 7.415 10.5 7.095V7.065ZM10.635 10.035C10.635 9.715 10.745 9.445 10.965 9.225C11.195 9.005 11.465 8.895 11.775 8.895C12.095 8.895 12.365 9.005 12.585 9.225C12.805 9.445 12.915 9.715 12.915 10.035V15.975C12.915 16.285 12.805 16.555 12.585 16.785C12.365 17.005 12.095 17.115 11.775 17.115C11.465 17.115 11.195 17.005 10.965 16.785C10.745 16.555 10.635 16.285 10.635 15.975V10.035Z"
                                      fill="#D9D9D9"/>
                            </svg>
                        </el-tooltip>
                    </div>
                    <div class="max-w-[695px] mx-auto">
                        <el-divider></el-divider>
                    </div>
                </div>
                <!-- Submit Button -->
                <div class="my-[100px] text-center max-w-[695px] mx-auto grid grid-cols-3 gap-3 items-center">
                    <button @click="dialogUserResultVisible = true"
                            class="link-review-result-lesson text-sm text-[#57D061] uppercase hover-underline hover-text-[#57D061]">
                        xem lại bài làm
                    </button>
                    <button id="checkButton" @click="reload()"
                            :class="`px-6 py-2 rounded-full text-sm w-full translate-x-0 scale-100 transition-all duration-300 ease-in-out text-[#07403F] bg-white drop-shadow-2xl`">
                        Làm lại
                    </button>
                    <button id="checkButton" @click="actionCheckButton()"
                            :class="`px-6 py-2 rounded-full text-sm w-full translate-x-0 scale-100 transition-all duration-300 ease-in-out text-[#07403F] bg-[#57D061] drop-shadow-2xl`">
                        Tiếp theo
                    </button>
                </div>
            </main>

            <el-dialog :visible.sync="dialogUserResultVisible" max-width="1097px" class="dialog-user-result-lesson">
                <!-- Slot tùy chỉnh tiêu đề -->
                <template #title>
                    <div class="custom-title pt-[57px] pl-[86px] bg-[#F4F5FA]">
                        <span class="text-[#07403F] font-beanbag text-[20px] ">Chi tiết bài làm</span>
                    </div>
                </template>


                <div class="dialog-wrapper pl-[43px] py-[12px] pr-[38px] mx-[43px] my-[12px] custom-scrollbar bg-[#F4F5FA]">
                    <div :class="`mb-[50px] px-[23px] py-[32px] rounded-[24px] border-[1px] border-[#e1dfdf] text-sm w-full text-[#07403F] bg-[${(userResult.correct_answer/userResult.total_answer) > 0.85 ? '#CEFFD8' : '#FDD3D0'}] drop-shadow-2xl`">
                        <table style="border: none">
                            <tbody style="border: none">
                            <tr style="border: none">
                                <td style="border: none" class="min-w-[140px]">
                                    <h5 class="font-averta-bold">Kết quả</h5>
                                </td>
                                <td style="border: none" class="text-[16px] font-averta-regular">@{{
                                    (userResult.correct_answer/userResult.total_answer) > 0.85 ? 'Đạt' : 'Không đạt' }}
                                </td>
                            </tr>
                            <tr style="border: none">
                                <td style="border: none" class="min-w-[150px]">
                                    <h5 class="font-averta-bold">Điểm của bạn</h5>
                                </td>
                                <td style="border: none" class="text-[16px] font-averta-regular">@{{
                                    userResult.correct_answer }}/@{{ userResult.total_answer }}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="list-result-question">
                        <div class="result-question-wrapper overscroll-auto">
                            <div v-for="(question, key) in dataLesson.component" class="result-question-item"
                                 :key="key + 1">
                                <div class="result-question-wrapper p-2 mb-[36px]">
                                    <div class="result-question-item-divider flex items-center">
                                        <h1 class="font-zuume-semibold text-[#57D061] font-bold mr-2">@{{ key + 1
                                            }}</h1>
                                        <div class="flex-1 border-t border-gray-300"></div>
                                    </div>
                                    <div class="result-question-item-content">
                                        <template v-if="question.type === 3">
                                            <!-- Question -->
                                            <div :class="`mt-6 mb-15 text-center ${ checkAnswerAudio(question) != 1 ? 'max-w-[695px]' : ''} mx-auto flex justify-content-center align-items-center font-gen-jyuu-gothic text-black`">
                                                <template v-if="checkAnswerAudio(question) == 1">
                                                    <div class="bg-white shadow-md rounded-3xl px-[18px] py-[21px] w-full">
                                                        <!-- Icon âm thanh -->
                                                        <div class="flex">
                                                            <svg @click="toggleAudio(question)" class="cursor-pointer"
                                                                 width="48" height="48" viewBox="0 0 48 48" fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                      fill="#4E87FF"/>
                                                                <path d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                      fill="#4E87FF"/>
                                                                <path opacity="0.4"
                                                                      d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                      fill="#4E87FF"/>
                                                                <path d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                      fill="#4E87FF"/>
                                                            </svg>
                                                        </div>

                                                        <!-- Văn bản Furigana -->
                                                        <div class="flex-1 text-center text-gray-800 mt-2"
                                                             v-html="getTagImg(question)">
                                                        </div>
                                                    </div>
                                                </template>
                                                <template v-else-if="checkAnswerAudio(question) == 2">
                                                    <button :data-audioButton="`audioButton-${question.id}`"
                                                            :ref="`audioButton-${question.id}`"
                                                            @click="toggleAudio(question)"
                                                            :class="['flex items-center justify-center w-[100px] h-[100px] rounded-full bg-white text-blue-500 hover:bg-blue-100 flex-col', isPlaying ? 'animate-blink' : 'shadow-[0_0_44px_0px] shadow-[#4E87FF80]']"
                                                    >
                                                        <!-- Icon loa -->
                                                        <div class="w-[50px] h-[50px] bg-blue-500 rounded-full flex items-center justify-center">
                                                            <svg width="100px" height="100px" viewBox="0 0 45 44"
                                                                 fill="none"
                                                                 xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M33.7505 30.7083C33.4505 30.7083 33.1693 30.6166 32.9068 30.4333C32.288 29.975 32.1568 29.1133 32.6255 28.5083C35.5693 24.6766 35.5693 19.3233 32.6255 15.4916C32.1568 14.8866 32.288 14.025 32.9068 13.5666C33.5255 13.1083 34.4068 13.2366 34.8755 13.8416C38.5505 18.645 38.5505 25.355 34.8755 30.1583C34.5943 30.525 34.1818 30.7083 33.7505 30.7083Z"
                                                                      fill="#4E87FF" fill-opacity="0.5"
                                                                      :class="{ 'animate-opacity': isPlaying }"/>
                                                                <path d="M37.1822 35.2917C36.8822 35.2917 36.6009 35.2001 36.3384 35.0167C35.7197 34.5584 35.5884 33.6967 36.0572 33.0917C41.0634 26.5651 41.0634 17.4351 36.0572 10.9084C35.5884 10.3034 35.7197 9.44172 36.3384 8.98339C36.9572 8.52505 37.8384 8.65339 38.3072 9.25839C44.0634 16.7567 44.0634 27.2434 38.3072 34.7417C38.0447 35.1084 37.6134 35.2917 37.1822 35.2917Z"
                                                                      fill="#4E87FF" fill-opacity="0.5"
                                                                      :class="{ 'animate-opacity-second': isPlaying }"/>
                                                                <path opacity="0.4"
                                                                      d="M29.5312 13.585V30.415C29.5312 33.5683 28.3688 35.9333 26.2875 37.07C25.4438 37.5283 24.5063 37.7483 23.5312 37.7483C22.0312 37.7483 20.4188 37.2533 18.7688 36.245L13.2938 32.89C12.9188 32.67 12.4875 32.5417 12.0562 32.5417H10.3125V11.4583H12.0562C12.4875 11.4583 12.9188 11.33 13.2938 11.11L18.7688 7.75499C21.5063 6.08666 24.1875 5.79333 26.2875 6.92999C28.3688 8.06666 29.5312 10.4317 29.5312 13.585Z"
                                                                      fill="#4E87FF"/>
                                                                <path d="M10.3125 11.4583V32.5417H9.375C4.8375 32.5417 2.34375 30.1033 2.34375 25.6667V18.3333C2.34375 13.8967 4.8375 11.4583 9.375 11.4583H10.3125Z"
                                                                      fill="#4E87FF"/>
                                                            </svg>
                                                        </div>
                                                        <!-- Thời gian -->
                                                        <span class="text-[#4E87FF] text-sm font-averta-bold text-blue-500">@{{ formattedTime }}</span>
                                                    </button>
                                                </template>
                                                <template v-else class="">
                                                    <div v-html="question.value"></div>
                                                </template>

                                                {{--                            @if(false)--}}
                                                {{--                                <p class="text-lg font-semibold">このことばは　ひらがなで　どう　かきますか。</p>--}}
                                                {{--                                <p class="text-sm text-gray-500">--}}
                                                {{--                                    1・2・3・4から　いちばん　いいものを　ひとつえらんで　ください。</p>--}}
                                                {{--                            @elseif(true)--}}
                                                {{--                                <div class="video">--}}
                                                {{--                                    <video id="my-video" class="video-js w-[50%] h-[352px]" controls preload="auto"--}}
                                                {{--                                           poster="https://web-test.dungmori.com/cdn/lesson/default/1729757832_118153995_79687.jpeg"--}}
                                                {{--                                           data-setup="{}">--}}
                                                {{--                                        <source--}}
                                                {{--                                                src="https://tokyo-v2.dungmori.com/720p/XHTH-2022-01-3.mp4/index.m3u8"--}}
                                                {{--                                                type="application/x-mpegURL"/>--}}
                                                {{--                                    </video>--}}
                                                {{--                                </div>--}}
                                                {{--                            @endif--}}
                                            </div>

                                            <!-- Options -->
                                            <div id="options"
                                                 class="grid grid-cols-2 gap-4 mb-15 max-w-[695px] mx-auto">
                                                {{--                                                @foreach($component->answers as $keyAnswer => $answer)--}}
                                                <template v-for="(answer, keyAnswer) in question.answers">
                                                    <template v-if="checkAnswerAudio(answer) == 3">
                                                        <label class="block">
                                                            <input :id="answer.id" type="radio"
                                                                   :data-result="answer.grade"
                                                                   name="answer" :value="answer.value"
                                                                   class="hidden">
                                                            <span :id="`span-option-${answer.id}`"
                                                                  :data-question="question.id"
                                                                  :data-answer="answer.id"
                                                                  :data-result="answer.grade"
                                                                  :class="`text-bold span-option p-[12px] ${renderStatusOptionResult(answer)} border-[4px] rounded-full cursor-pointer flex align-items-center text-[#07403F]`">
                                                                <span>
{{--                                                                    {{ chr(65 + $keyAnswer) }}.--}}
                                                                </span>
                                                                <span class="ml-2 text-black font-gen-jyuu-gothic w-full text-center">
                                                                    <svg class="cursor-pointer"
                                                                         width="32" height="32" viewBox="0 0 48 48"
                                                                         fill="none"
                                                                         xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M35.9996 33.5001C35.6796 33.5001 35.3796 33.4001 35.0996 33.2001C34.4396 32.7001 34.2996 31.7601 34.7996 31.1001C37.9396 26.9201 37.9396 21.0801 34.7996 16.9001C34.2996 16.2401 34.4396 15.3001 35.0996 14.8001C35.7596 14.3001 36.6996 14.4401 37.1996 15.1001C41.1196 20.3401 41.1196 27.6601 37.1996 32.9001C36.8996 33.3001 36.4596 33.5001 35.9996 33.5001Z"
                                                                              fill="#4E87FF"/>
                                                                        <path d="M39.6597 38.5001C39.3397 38.5001 39.0397 38.4001 38.7597 38.2001C38.0997 37.7001 37.9597 36.7601 38.4597 36.1001C43.7997 28.9801 43.7997 19.0201 38.4597 11.9001C37.9597 11.2401 38.0997 10.3001 38.7597 9.80006C39.4197 9.30006 40.3597 9.44006 40.8597 10.1001C46.9997 18.2801 46.9997 29.7201 40.8597 37.9001C40.5797 38.3001 40.1197 38.5001 39.6597 38.5001Z"
                                                                              fill="#4E87FF"/>
                                                                        <path opacity="0.4"
                                                                              d="M31.5 14.8199V33.1799C31.5 36.6199 30.26 39.1999 28.04 40.4399C27.14 40.9399 26.14 41.1799 25.1 41.1799C23.5 41.1799 21.78 40.6399 20.02 39.5399L14.18 35.8799C13.78 35.6399 13.32 35.4999 12.86 35.4999H11V12.4999H12.86C13.32 12.4999 13.78 12.3599 14.18 12.1199L20.02 8.45994C22.94 6.63994 25.8 6.31994 28.04 7.55994C30.26 8.79994 31.5 11.3799 31.5 14.8199Z"
                                                                              fill="#4E87FF"/>
                                                                        <path d="M11 12.5V35.5H10C5.16 35.5 2.5 32.84 2.5 28V20C2.5 15.16 5.16 12.5 10 12.5H11Z"
                                                                              fill="#4E87FF"/>
                                                                    </svg>
                                                                </span>
                                                            </span>
                                                        </label>
                                                    </template> <!-- Đáp án là audio -->
                                                    <template v-else>
                                                        <label class="block">
                                                            <input :id="answer.id" type="radio"
                                                                   :data-result="answer.grade"
                                                                   name="answer" :value="answer.value"
                                                                   class="hidden">
                                                            <span :id="`span-option-${answer.id}`"
                                                                  :data-question="question.id"
                                                                  :data-answer="answer.id"
                                                                  :data-result="answer.grade"
                                                                  :class="`max-h-[161px] span-option p-4 ${renderStatusOptionResult(answer)} border-[4px] rounded-${checkAnswerAudio(answer) === 4 ? '2xl' : 'full'} cursor-pointer flex align-items-flex-${checkAnswerAudio(answer) === 4 ? 'start' : 'end'} text-[#07403F]`">
                                                                    <span class="text-bold">
{{--                                                                        {{ chr(65 + $keyAnswer) }}.--}}
                                                                    </span>
                                                                    <span class="ml-2 text-black font-averta-semibold font-normal"
                                                                          v-html="answer.value">
                                                                    </span>
                                                                </span>
                                                        </label>
                                                    </template>
                                                </template>
                                                {{--                                                @endforeach--}}
                                            </div>

                                        </template>
                                        <template v-else-if="question.type === 13">
                                            <!-- Question box -->
                                            <div class="grid align-items-center w-full max-w-[80%] min-h-[300px] bg-white rounded-3xl shadow-lg p-6 text-center mx-auto">
                                                <div class="text-2xl font-bold flex justify-content-center flex-wrap items-baseline">
                                                    <template
                                                            v-for="(element, keyElement) in question.value['question']">
                                                        <template v-if="element.type === 'default'">
                                                            <div class="my-2 ml-[6px] missing-word-wrap text-[20px]"
                                                                 v-html="element.value"></div>
                                                        </template>
                                                        <template v-else>
                                                            <span class="ml-[6px] my-2">
                                                            <!-- Input field -->
                                                            <input type="text"
                                                                   :value="element.user_result"
                                                                   :id="`missingWord-${keyElement}-${question.id}`"
                                                                   :data-id="question.id"
                                                                   :data-result="element.result"
                                                                   ref="inputFillInBlank"
                                                                   :class="`missingWord missingWord-${question.id} outline-none min-w-[73px] rounded-[12px] ${renderStatusFillInBlankResult(element, keyElement, question.id)} text-center h-12 font-bold text-[16px]`"
                                                                   @input="checkInput">
                                                            </span>
                                                        </template>
                                                    </template>
                                                </div>
                                            </div>
                                        </template>
                                        <template v-else></template>
                                        <div class="result-question-item-explain mt-[28px]"
                                             v-if="(question.type === 3 && question.explain !== null && question.explain !== '') || (question.type === 13 && question.value['explain'] !== null && question.value['explain'] !== '')">
                                            <div class="px-[12px] py-[9px] rounded-[16px] text-sm w-full text-[#1E1E1E] bg-[#F0FFF1]">
                                                <p class="uppercase font-beanbag">*Giải thích</p>
                                                <div v-html="question.type === 3 ? question.explain : question.value['explain']"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </template>
    </div>

@stop

@section('footer-js')
    <script src="https://vjs.zencdn.net/8.16.1/video.min.js"></script>
    <script src="{{ asset('assets/js/course/basic.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script>
        let dataLesson = @json($contentLesson);
        let currentAnswer = dataLesson.component[0];
        let idCurrentAnswer = dataLesson.component[0].id;
        let userResult = []
        let apiLessonExercise = axios.create({
            baseURL: "/khoa-hoc",
            headers: {
                "Content-Type": "application/json",
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            withCredentials: true,
        });

        new Vue({
            el: "#container-lesson",
            data: {
                dataLesson: dataLesson,
                currentAnswer: currentAnswer,
                idCurrentAnswer: idCurrentAnswer,
                duration: 0, // Tổng thời gian của file âm thanh (giây)
                timeRemaining: 0, // Thời gian còn lại (giây)
                isPlaying: false,
                intervalId: null,
                audio: null,
                isCheckAnswer: false,
                statusAnswer: 'doing', // doing, test, next
                isShowExplainButton: false,
                isAnswerSuccess: true,
                isShowExplainIconAudio: true,
                userResult: {
                    lesson_id: dataLesson.id,
                    total_grade: dataLesson.total_marks,
                    grade_pass: dataLesson.pass_marks,
                    grade: 0,
                    data: {},
                    course_id: dataLesson.course_id,
                    correct_answer: 0,
                    total_answer: dataLesson.component.length
                },
                dialogUserResultVisible: false,
            },
            created() {
                console.log(`dataLesson vue:`, dataLesson)
                console.log(`idCurrentAnswer vue:`, idCurrentAnswer)
            },
            methods: {
                toggleAudio(currentAnswer) {
                    console.log('currentAnswer: ', currentAnswer)
                    let linkMp3 = this.getLinkMp3(currentAnswer)
                    if (!this.audio) {
                        console.log(`1`)
                        // Khởi tạo Audio object khi lần đầu phát
                        this.audio = new Audio(`https://mp3-vn.dungmori.com/${linkMp3}`);
                        // Lấy thời gian khi âm thanh đã load xong
                        this.audio.addEventListener("loadedmetadata", () => {
                            // console.log(this.audio.duration)
                            this.duration = Math.floor(this.audio.duration)
                            this.timeRemaining = this.duration;
                        });
                        this.audio.addEventListener("ended", this.resetAudio);
                    } else {
                        if (this.audio.src != `https://mp3-vn.dungmori.com/${linkMp3}`) {
                            this.audio = new Audio(`https://mp3-vn.dungmori.com/${linkMp3}`);
                            // Lấy thời gian khi âm thanh đã load xong
                            this.audio.addEventListener("loadedmetadata", () => {
                                // console.log(this.audio.duration)
                                this.duration = Math.floor(this.audio.duration)
                                this.timeRemaining = this.duration;
                            });
                            this.audio.addEventListener("ended", this.resetAudio);
                        }
                    }
                    if (this.isPlaying) {
                        this.pauseAudio();
                    } else {
                        this.playAudio();
                    }
                },
                playAudio() {
                    this.audio.play();
                    this.isPlaying = true;
                    // this.isPlaying = !this.isPlaying;
                    // Bắt đầu đếm ngược thời gian còn lại
                    this.intervalId = setInterval(() => {
                        if (this.timeRemaining > 0) {
                            this.timeRemaining -= 1;
                        } else {
                            this.resetAudio();
                        }
                    }, 1000);
                },
                pauseAudio() {
                    this.audio.pause();
                    this.isPlaying = false;
                    clearInterval(this.intervalId);
                },
                resetAudio() {
                    this.pauseAudio();
                    this.timeRemaining = this.duration;
                    this.isPlaying = false;
                },
                checkAnswerAudio(currentAnswer) {
                    const str = currentAnswer.value;

                    if (typeof currentAnswer.value !== 'string') return 0;

                    const regexMp3 = /{! (.+?\.mp3) !}/;
                    const regexImg = /<p><img[^>]*src="([^"]+)"[^>]*><\/p>/;
                    const regexMp3Option = /([A-Za-z0-9/-]+\.mp3)/;

                    const matchMp3 = str.match(regexMp3);
                    const imgTagMatch = str.match(regexImg);
                    const matchMp3Option = str.match(regexMp3Option);

                    const imgTag = imgTagMatch ? imgTagMatch[0] : null;

                    if (matchMp3 && imgTag != null) {
                        return 1; // có cả ảnh và audio
                    } else if (matchMp3 && imgTag == null) {
                        return 2; // có audio nhưng không có ảnh
                    } else if (matchMp3Option) {
                        return 3; // audio đáp án
                    } else if (!matchMp3 && imgTag) {
                        return 4; // có ảnh nhưng không có audio
                    } else {
                        return 0; // không có audio
                    }
                },
                getTagImg(currentAnswer) {
                    const str = currentAnswer.value;

                    const regexImg = /<p><img[^>]*src="([^"]+)"[^>]*><\/p>/;

                    // Kiểm tra và lấy toàn bộ thẻ <p><img ... /></p>
                    const imgTagMatch = str.match(regexImg);
                    return imgTagMatch ? imgTagMatch[0] : null;
                },
                getLinkMp3(currentAnswer) {
                    const str = currentAnswer.value;

                    const regexMp3 = /{! (.+?\.mp3) !}/;
                    const regexMp3Option = /([A-Za-z0-9/-]+\.mp3)/;

                    const match = str.match(regexMp3);
                    const matchOption = str.match(regexMp3Option)

                    let mp3Path = null;
                    if (match != null) mp3Path = match[1];
                    if (matchOption != null) mp3Path = matchOption[1];

                    return mp3Path;
                },
                selectOption(answer) {
                    if (this.statusAnswer === 'next') return;
                    console.log(`answer: `, answer);
                    if (this.checkAnswerAudio(answer) == 3) {
                        this.toggleAudio(answer);
                    } else {
                        if (this.isCheckAnswer) return;
                    }

                    let id = answer.id
                    let allSpanOption = $('.span-option')
                    let optionSelected = $(`#span-option-${id}`);

                    allSpanOption.removeClass('border-[#B2EEFA]')
                    optionSelected.addClass('border-[#B2EEFA]')

                    let checkButton = document.getElementById("checkButton");
                    checkButton.disabled = false;
                    this.statusAnswer = 'test'
                },
                enableCheckButton(selectedOption) {
                    console.log(`vao day 123123123`)
                    if (this.isCheckAnswer) return;
                    console.log(`thay doi 123`)

                    let id = selectedOption.id
                    let allSpanOption = $('.span-option')
                    let optionSelected = $(`#span-option-${id}`);

                    allSpanOption.removeClass('border-[#B2EEFA]')
                    optionSelected.addClass('border-[#B2EEFA]')

                    let checkButton = document.getElementById("checkButton");
                    checkButton.disabled = false;
                    checkButton.classList.remove("bg-gray-300");
                    checkButton.classList.remove('text-[#B3B3B3]')
                    checkButton.classList.add("drop-shadow-2xl");
                    checkButton.classList.add('text-[#07403F]')
                    $("#checkButton").css('background-color', '#57D061');
                },
                showExplanationPopup() {
                    this.setupStyleExplanationPopup();
                    // Hiển thị overlay và popup
                    document.getElementById("popupOverlay").classList.remove("hidden");
                    document.getElementById("explanationPopup").classList.remove("hidden");
                    // Xóa translate-y-full để trượt popup lên
                    setTimeout(() => {
                        document.getElementById("explanationPopup").classList.remove("translate-y-full");
                    }, 10);
                },
                checkAnswer() {
                    let currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
                    if (currentIndex >= 0 && currentIndex < this.dataLesson.component.length - 1) {
                        this.statusAnswer = 'next'
                    } else {
                        this.statusAnswer = 'end'
                    }
                    let currentAnswer = this.dataLesson.component.find(t => t.id === this.idCurrentAnswer);
                    console.log("currentAnswer: ", currentAnswer)
                    if (currentAnswer.type === 3) {
                        console.log(33333333)
                        this.checkAnswerMultiChoice(currentAnswer);
                    } else if (currentAnswer.type === 13) {
                        console.log(131313131313)
                        this.checkAnswerFillIn();
                    }
                    this.showExplainButton(currentAnswer);
                },
                showExplainButton(currentAnswer) {
                    console.log(`currentAnswer showExplainButton: `, currentAnswer)
                    console.log((currentAnswer.type === 13 && currentAnswer.value.explain != null))
                    console.log((currentAnswer.type === 3 && currentAnswer.explain != null))
                    if ((currentAnswer.type === 13 && currentAnswer.value.explain != null) || (currentAnswer.type === 3 && currentAnswer.explain != null)) {
                        this.isShowExplainButton = true;
                    } else {
                        this.isShowExplainButton = false
                    }
                    console.log('this.isShowExplainButton: ', this.isShowExplainButton)
                },
                nextQuestion() {
                    console.log(`vao day`);
                    // Tìm index của câu hỏi hiện tại
                    let currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
                    $("#progress_bar").css('width', `${(currentIndex + 1) * 100 / this.dataLesson.component.length}%`);
                    $("#progress_text").text(`${currentIndex + 1}/${this.dataLesson.component.length}`)

                    // Kiểm tra xem có phần tử tiếp theo không
                    if (currentIndex >= 0 && currentIndex < this.dataLesson.component.length - 1) {
                        $(`#wrap-question-${this.idCurrentAnswer}`).addClass('hidden')
                        this.idCurrentAnswer = this.dataLesson.component[currentIndex + 1].id;
                        this.currentAnswer = this.dataLesson.component[currentIndex + 1];
                        $(`#wrap-question-${this.idCurrentAnswer}`).removeClass('hidden')
                        this.resetCheckButton();
                        if (this.checkAnswerAudio(this.currentAnswer) === 2 || this.checkAnswerAudio(this.currentAnswer) === 1) {
                            this.toggleAudio(this.currentAnswer)
                        }
                    } else {
                        console.log("Không có câu hỏi tiếp theo.", save_result);
                    }
                    this.stausAnswer = 'doing';
                },
                saveResultExerciseUser() {
                    let currentIndex = this.dataLesson.component.findIndex(q => q.id === this.idCurrentAnswer);
                    $("#progress_bar").css('width', `${(currentIndex + 1) * 100 / this.dataLesson.component.length}%`);
                    $("#progress_text").text(`${currentIndex + 1}/${this.dataLesson.component.length}`)

                    let save_result = apiLessonExercise.post('/list-course/save-result-exercise-user', this.userResult).then(res => {
                        console.log(`res: `, res);
                        if (res.data.error_code === 0) {
                            this.$message.success(res.data.msg);
                            this.statusAnswer = 'result';
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    })
                    console.log("Không có câu hỏi tiếp theo.", save_result);
                },
                actionCheckButton() {
                    console.log(`this.statusAnswer actionCheckButton`, this.statusAnswer)
                    if (this.statusAnswer === 'test') {
                        console.log('vao kiemr tra')
                        this.checkAnswer();
                    } else if (this.statusAnswer === 'next') {
                        console.log('-------------------------------')
                        this.nextQuestion();
                    } else if (this.statusAnswer === 'end') {
                        console.log("Không có câu hỏi tiếp theo.");
                        this.saveResultExerciseUser();
                    } else {
                        return;
                    }

                    console.log('statusAnswer isShowExplainButton', this.statusAnswer, this.isShowExplainButton)
                },
                checkInput(event) {
                    const dataId = event.target.getAttribute('data-id');
                    const checkButton = document.getElementById("checkButton");

                    $(`#${event.target.id}`).css('width', `${Math.max(73, event.target.value.length * 10)}px`) // 10 là tỉ lệ có thể thay đổi theo font-size

                    let valueMissingWordArray = $(`input[data-id="${dataId}"]`).map(function () {
                        return this.value;
                    }).get();

                    // Kiểm tra nếu có giá trị trong ô điền từ
                    if (!valueMissingWordArray.includes("")) {
                        this.statusAnswer = 'test'
                        checkButton.disabled = false;
                    } else {
                        this.statusAnswer = 'doing'
                        checkButton.disabled = true;
                    }
                },
                resetCheckButton() {
                    const checkButton = document.getElementById("checkButton");
                    this.statusAnswer = 'doing'
                    checkButton.disabled = true;
                    this.isCheckAnswer = false;
                    this.isAnswerSuccess = true;
                },
                setupStyleExplanationPopup() {
                    $("#content_explain").html(this.currentAnswer.type === 13 ? this.currentAnswer.value.explain : this.currentAnswer.explain);
                    console.log('this.currentAnswer setupStyleExplanationPopup:', this.currentAnswer)
                    if (this.currentAnswer.value.audio != null && this.currentAnswer.value.audio !== "") {
                        this.isShowExplainIconAudio = true
                        $("#audio").attr('src', `/cdn/audio/${this.currentAnswer.value.audio}`)
                    } else {
                        this.isShowExplainIconAudio = false
                    }
                },
                hideExplanationPopup() {
                    document.getElementById("explanationPopup").classList.add("translate-y-full");
                    setTimeout(() => {
                        document.getElementById("popupOverlay").classList.add("hidden");
                        document.getElementById("explanationPopup").classList.add("hidden");
                    }, 300);
                },
                checkAnswerMultiChoice(currentAnswer) {
                    let options = $(`.span-option[data-question="${currentAnswer.id}"]`)
                    console.log(`options: `, options)
                    let userResult = this.userResult.data
                    let isAnswerSuccess = true

                    let question = this.dataLesson.component.find(item => item.id === currentAnswer.id)

                    options.each(function () {
                        if ($(this).prev().is(':checked')) {
                            userResult = {...userResult, [currentAnswer.id]: $(this).data('answer')}
                            question.answers.map(item => {
                                item.user_choice = $(this).data('answer') === item.id ? 1 : 0;
                            })
                        }

                        if (parseInt($(this).data('result')) !== 0) {
                            $(this).removeClass('border-[#B2EEFA]')
                            $(this).removeClass('border-[#F4F5FA]')
                            $(this).addClass("border-[#57D061] text-black bg-[#95FF99]")
                        } else if ($(this).prev().is(':checked')) {
                            isAnswerSuccess = false;
                            $(this).removeClass('border-[#F4F5FA]')
                            $(this).addClass("border-[#FF7C79] text-black bg-[#FDD3D0]")
                        }
                    })
                    this.isAnswerSuccess = isAnswerSuccess;
                    if (this.isAnswerSuccess) {
                        this.userResult.grade += parseInt(currentAnswer.grade)
                        this.userResult.correct_answer += 1;
                    }
                    this.userResult.data = userResult
                    console.log('this.userResult checkAnswerMultiChoice:', this.userResult)
                    console.log('this.dataLesson checkAnswerMultiChoice:', this.dataLesson)
                },
                checkAnswerFillIn() {
                    let isAnswerSuccess = true
                    let inputMissingWord = $(`.missingWord[data-id="${this.idCurrentAnswer}"]`);
                    let dataResult = []
                    let question = this.dataLesson.component.find(item => item.id === this.idCurrentAnswer)

                    inputMissingWord.each(function () {
                        $(this).removeClass('bg-[#D9D9D9]')
                        question.value.question[$(this).data('index')].user_result = $(this).val().trim();
                        dataResult.push($(this).val().trim())
                        if ($(this).data('result').toLowerCase() === $(this).val().trim().toLowerCase()) {
                            // this.isAnswerSuccess = true
                            $(this).css({
                                "border": "3px solid #57D061",
                                "background": "#95FF99"
                            })
                        } else {
                            console.log(`co vao day khong`)
                            isAnswerSuccess = false
                            $(this).css({
                                "border": "3px solid #FF7C79",
                                "background": "#FDD3D0"
                            });
                        }
                    });
                    this.isAnswerSuccess = isAnswerSuccess;
                    if (this.isAnswerSuccess && parseInt(this.currentAnswer.grade) > 0) {
                        this.userResult.grade += parseInt(this.currentAnswer.grade)
                        this.userResult.correct_answer += 1;
                    }
                    this.userResult.data = {...this.userResult.data, [this.idCurrentAnswer]: dataResult}
                    console.log('this.isAnswerSuccess: checkAnswerFillIn', this.isAnswerSuccess)
                    console.log('this.userResult checkAnswerFillIn:', this.userResult)
                    console.log('this.dataLesson checkAnswerFillIn:', this.dataLesson)
                },
                handleClose(done) {
                    this.$confirm('Are you sure to close this dialog?')
                        .then(_ => {
                            done();
                        })
                        .catch(_ => {
                        });
                },
                renderStatusOptionResult(answer) {
                    if (answer.user_choice === 0 && answer.grade === '0') {
                        return 'bg-white border-[F4F5FA]'
                    } else if (answer.grade !== '0') {
                        return 'bg-[#95FF99] border-[#57D061]'
                    } else if (answer.user_choice === 1 && answer.grade === '0') {
                        return 'bg-[#FDD3D0] border-[#FF7C79]'
                    }
                },
                renderStatusFillInBlankResult(element, keyElement, questionId) {
                    if (element.type === 'question' && element.user_result !== undefined) {
                        this.$nextTick(() => {
                            console.log(`#missingWord-${keyElement}-${questionId}`);
                            const input = $(`#missingWord-${keyElement}-${questionId}`);
                            console.log(input.val());
                            input.css('width', `${Math.max(73, element.user_result.length * 13)}px`);
                        });

                        if (element.result.toLowerCase() === element.user_result.toLowerCase()) {
                            return `border-[3px] border-[#57D061] bg-[#95FF99]`;
                        } else {
                            return `border-[3px] border-[#FF7C79] bg-[#FDD3D0]`;
                        }
                    }
                    return '';
                },
                reload() {
                    location.reload();
                },
                renderWidthTooltipSuggest(currentAnswer) {
                    let suggest = currentAnswer.type === 13 ? currentAnswer.value.suggest : currentAnswer.suggest
                    if (suggest === null || suggest === '') {
                        return '700px';
                    }
                    return `${Math.min(700, suggest.length * 10)}px`
                }
            },
            computed: {
                // Chuyển đổi giây thành định dạng mm:ss
                formattedTime() {
                    const minutes = Math.floor(this.timeRemaining / 60);
                    const seconds = this.timeRemaining % 60;
                    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                }
            },
            mounted() {
                $(document).on("click", ".reloadCKeditor", function () {
                    $(this).ckeditor(function () {
                    }, {
                        on: {
                            blur: function () {
                                this.destroy();
                            },
                        },
                        extraPlugins: "furigana,colorbutton",
                    });
                });
            },
            beforeDestroy() {
                // Clear interval khi component bị huỷ
                if (this.intervalId) {
                    clearInterval(this.intervalId);
                }
                if (this.audio) {
                    this.audio.removeEventListener("ended", this.resetAudio);
                }
            },
        })

        function playAudio() {
            const audio = document.getElementById("audio");
            audio.play();
        }
    </script>
@stop

<style>
    /* Định nghĩa animation nhấp nháy */
    @keyframes blink {
        0%, 100% {
            box-shadow: 0 0 44px 25px rgba(78, 135, 255, 0.5); /* shadow-[0_0_44px_4px] */
            -webkit-box-shadow: 0 0 44px 25px rgba(78, 135, 255, 0.51);
            -moz-box-shadow: 0 0 44px 25px rgba(78, 135, 255, 0.51);
        }
        50% {
            box-shadow: 0 0 44px 44px rgba(78, 135, 255, 0.5); /* shadow-[0_0_44px_25px] */
            -webkit-box-shadow: 0 0 44px 44px rgba(78, 135, 255, 0.51);
            -moz-box-shadow: 0 0 44px 44px rgba(78, 135, 255, 0.51);
        }
    }

    .animate-blink {
        animation: blink 1s infinite; /* Thời gian 1s và lặp vô hạn */
    }

    @keyframes blinkOpacity {
        0%, 100% {
            fill-opacity: 0.5;
        }
        50% {
            fill-opacity: 1;
        }
    }

    @keyframes blinkOpacityFirst {
        0% {
            fill-opacity: 1;
        }
        /* Hiện lên tại giây đầu tiên */
        25% {
            fill-opacity: 0.5;
        }
        /* Tắt đi tại giây thứ hai */
        50% {
            fill-opacity: 0.5;
        }
        /* Giữ nguyên tắt cho đến giữa chu kỳ */
        100% {
            fill-opacity: 1;
        }
        /* Hiện lên lại ở giây thứ tư */
    }

    .animate-opacity {
        animation: blinkOpacityFirst 2s infinite; /* Chu kỳ 4s lặp lại vô hạn */
        /*animation-delay: 1s; !* Chờ 1s trước khi bắt đầu *!*/
    }

    .animate-opacity-second {
        animation: blinkOpacity 1s infinite; /* Thời gian 1s và lặp vô hạn */
    }

    .span-option img {
        max-height: 121px;
        object-fit: contain;
        min-width: 250px;
    }

    .missing-word-wrap p {
        font-size: 16px;
    }

    .missingWord:focus {
        border: 3px solid #4E87FF;
    }

    .missingWord {
        width: 73px;
        /*-webkit-transition: 0.5s;*/
    }

    /*
  Enter and leave animations can use different
  durations and timing functions.
*/
    .slide-fade-enter-active {
        transition: all 2s ease-out;
    }

    .slide-fade-leave-active {
        transition: all 2s cubic-bezier(1, 0.5, 0.8, 1);
    }

    .slide-fade-enter-from, .slide-fade-leave-to {
        transform: translateX(-20px);
        opacity: 0;
    }

    /* tooltip body */
    .el-tooltip__popper.is-customized {
        background: #FFBE97B8;
        color: #07403F;
        font-size: 14px;
        border-radius: 15px;
        font-family: "Averta-Semibold", arial;
        font-weight: 400;
    }

    /* tooltip arrow body */
    .el-tooltip__popper.is-customized .popper__arrow {
        border-right-color: #FFBE97B8;
    }

    /* tooltip arrow border */
    .el-tooltip__popper.is-customized .popper__arrow::after {
        border-right-color: #FFBE97B8;
    }

    .link-review-result-lesson:hover {
        color: #57D061 !important;
        text-decoration: underline !important;
    }

    .dialog-user-result-lesson .el-dialog__body .custom-scrollbar {
        max-height: 75vh;
        overflow-y: auto;
    }

    /* Style thanh cuộn */
    .dialog-user-result-lesson .custom-scrollbar {
        /*scrollbar-width: thin;*/
        /*scrollbar-color: #57D061 #F4F5FA;*/
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
        background-color: #D9D9D9;
        border-radius: 10px;
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar-track {
        border-radius: 6px;
        background-color: #D9D9D9;
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #57D061;
        border-radius: 10px;
    }

    .dialog-user-result-lesson .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #45B052;
    }

    .dialog-user-result-lesson .el-dialog__header, .dialog-user-result-lesson .el-dialog__body {
        padding: 0;
    }

    .dialog-user-result-lesson .el-dialog {
        background: #F4F5FA;
    }
</style>
