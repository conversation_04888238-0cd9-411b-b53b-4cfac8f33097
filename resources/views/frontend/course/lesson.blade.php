@extends('frontend._layouts.default')

@section('title')
  Dungmori - <PERSON><PERSON><PERSON><PERSON> học {{ $thisLesson->lesson_group ? $thisLesson->lesson_group->getCourseOfGroup->name : '' }} | {{ $thisLesson->name }}
@stop
@section('description')
  Dungmori YouTuber d<PERSON>y tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất
@stop
@section('keywords')
  Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất
@stop
@section('image')
  @if (!is_null($thisLesson) && $thisLesson->avatar_name != null && $thisLesson->avatar_name != "")
    {{url('cdn/lesson/default')}}/{{ $thisLesson->avatar_name }}
  @else
    {{url('assets/img/oglogo.png')}}
  @endif
@stop
@section('author')
  DUNGMORI
@stop
@section('header-js')
  <?php

  //lưu admin ss vào cookie để giảm đi 1 truy vấn thường suyên
  $adminSession = false;
  if (isset($_COOKIE['admin_ss']) && $_COOKIE['admin_ss'] == 'be972bedb15a') {
    $adminSession = true;
  } else {
    if (Auth::guard('admin')->user()) {
      $adminSession = true;
      setcookie('admin_ss', 'be972bedb15a', time() + (86400 * 30), "/");
    }
  }
  //echo $adminSession;
  ?>
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
  </script>
  <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
  </script>
  <script type="text/javascript">
    var meid = null;
    var myAvatar = null;
    @if(Auth::check())
        meid = '{{ Auth::user()->id }}';
        myAvatar = '{{ Auth::user()->avatar }}';
    @endif
  </script>

  <script>
    $(document).ready(function() {
      window.top.postMessage({ height: document.body.scrollHeight }, "*");
    });
  </script>
  <style>
    table, thead, tbody, tr, td {
      border-style: solid;
      border-width: 1px;
      border-color: #000;
    }
  </style>
  @if($cdbh == true)
    <script>
      $(document).ready(function() {
        window.top.postMessage({ height: document.body.scrollHeight }, "*");
      });
    </script>
    <style type="text/css">
      .header--pc, .site-header, .footer, .main-right-premium, .main-right {
        display: none;
      }

      #application {
        padding-top: 0;
      }

      .main .main-center {
        width: 100%;
        float: left;
      }

      .main .main-course .main-left {
        float: none;
        margin: 0 auto 0 12%;
      }

      .fixed-panel {
        display: none;
      }

      #buyCourseBtn {
        display: none;
      }

      .dmr {
        display: none;
      }

      .bhschool {
        display: block;
      }

      .comment-tab .active > a {
        background-color: #3B5998 !important;
      }

      .comment-tab {
        border-bottom-color: #3B5998;
      }

      #bh-comment-content .list-comments .input-comment-container .post-comment-btn {
        background-color: #3B5998 !important;
      }

      #bh-comment-content .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
        background-color: #3B5998 !important;
      }

      #bh-comment-content .list-comments .comment-item .comment-content .name b {
        color: #3B5998 !important;
      }

      #bh-comment-content .list-comments .comment-item .comment-content .load-more-reply {
        color: #3B5998 !important;
      }

      #bh-comment-content .list-comments .comment-item .comment-content .reply-container .child-comment-item .comment-content .child-name b {
        color: #3B5998 !important;
      }

      #bh-comment-content .list-comments .load-more-comment {
        background-color: #3B5998 !important;
      }

      .main .tab-content {
        padding-top: 0;
      }

      .main .main-course .main-left .cover-container .lesson-content-detail {
        overflow-y: inherit;
        overflow-x: inherit;
        padding-right: 0;
        max-height: none;
      }

      @media only screen and (max-width: 768px) {
        .mobile-header {
          display: none;
        }

        .main .main-course .main-left {
          padding: 0px 15px;
          margin: 0;
        }

        .main .main-course .main-left .lesson-detail-title {
          font-size: 15px;
        }
      }

      /* Hide scrollbar for Chrome, Safari and Opera */
      body::-webkit-scrollbar {
        display: none;
      }

      /* Hide scrollbar for IE, Edge and Firefox */
      body {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
      }
    </style>
  @endif

  {{-- nếu là 1 trong 3 khóa zoom --}}
  @if(in_array($course->id, array(27, 28, 29)))

    <style type="text/css">
      .server-localtion {
        background-color: #EDBA70 !important;
      }

      .main .main-course .main-left .course-detail-container .course-price-container {
        background: #fff1e0 !important;
      }

      .lesson-detail-title a {
        color: #9f5f03;
      }

      .course-tab {
        background: #FFF !important;
      }

      .course-tab > .active > a {
        background-color: #D68E24 !important;
      }

      .course-tab > .li-tab > a {
        color: #fff !important;
      }

      .course-list-container > .block-title {
        background: #D68E24;
      }

      .course-list-container .panel-default > .panel-heading a {
        background: #FFE8CD !important;
        color: #D68E24;
      }

      .course-list-container .panel-default > .panel-heading > .group-step-item {
        background: #FFE8CD !important;
      }

      .course-list-container .panel-default .panel-body li a {
        background: #f7f2eb !important;
      }

      .course-list-container .panel-default .panel-body li a:hover {
        background: #f7f2eb !important;
        color: #222;
      }

      .lesson__progress--circle .progressbar-text {
        color: #222 !important;
      }

      .comment-tab {
        border-bottom: 1px solid #D68E24 !important;
      }

      .comment-tab .active > a {
        background-color: #D68E24 !important;
      }

      .list-comments .comment-item .comment-content .name b {
        color: #D68E24 !important;
      }

      .comment-action > a {
        color: #D68E24 !important;
      }

      .child-comment-item .comment-content .child-name b {
        color: #D68E24 !important;
      }

      .list-comments .input-comment-container .post-comment-btn {
        background: #D68E24 !important;
      }

      .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
        background: #D68E24 !important;
      }

      .list-comments .load-more-comment {
        background: #D68E24 !important;
      }

      .main-right {
        margin-top: 20px !important;
      }

      .main .main-course .main-right .course-info-container {
        margin-top: 35px;
      }

      .course-list-container .panel-default .panel-body li.active a {
        color: #111;
      }
    </style>

  @endif

  {{-- nếu là 1 trong 3 khóa eju --}}
  @if(in_array($course->id, array(8, 9, 10, 36, 37, 38)))
    <style type="text/css">

      .server-localtion {
        background-color: #F0F6FF !important;
        color: #0D4890 !important;
      }

      .course-tab {
        background: #77C2EF !important;
      }

      .course-tab > .active > a {
        background-color: #105B89 !important;
      }

      .course-tab > .li-tab > a {
        color: #fff !important;
      }

      .course-list-container > .block-title {
        background: #124896;
        text-align: center;
      }

      .course-list-container .panel-default > .panel-heading a {
        background: #F0F6FF !important;
        color: #124896;
        text-align: center;
      }

      .course-list-container .panel-default > .panel-heading a strong {
        width: 100%;
        text-align: center;
      }

      .scroll-items .pull-right {
        color: #124896;
      }

      .course-list-container .panel-default .panel-body li a .free {
        background: #124896;
      }

      /*.course-list-container .panel-default > .panel-heading > .group-step-item{background: rgb(238, 238, 238) !important;}*/
      .course-list-container .panel-default .panel-body li a {
        background: #FBFCFF !important;
        font-size: 13px;
        font-weight: 300;
        font-family: Roboto;
      }

      .course-list-container .panel-default .panel-body li a:hover {
        color: #124896;
        font-weight: bold;
      }


      .comment-tab {
        border-bottom: 1px solid #fff !important;
        margin-bottom: 0;
      }

      .comment-tab .active > a {
        background-color: #F0F6FF !important;
      }

      .nav-pills > li.active > a {
        color: #124896;
        font-size: 14px;
        text-align: center;
      }

      .comment-tab .active > .active {
        color: #124896;
        border: 1px solid #124896 !important;
      }

      .list-comments .comment-item .comment-content .name b {
        color: #105B89 !important;
      }

      .comment-action > a {
        color: #105B89 !important;
      }

      .child-comment-item .comment-content .child-name b {
        color: #105B89 !important;
      }

      .list-comments .input-comment-container .post-comment-btn {
        background: #124896 !important;
      }

      .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
        background: #124896 !important;
      }

      .list-comments .load-more-comment {
        background: #F0F6FF !important;
        color: #0D4890;
        font-weight: bold;
      }

      .main-right {
        margin-top: 55px !important;
      }

      .main .main-course .main-right .course-info-container {
        margin-top: 35px;
        margin-left: -270px;
      }

      .main .main-course .main-left .course-detail-container .course-price-container {
        border: 2px dashed #4F92E4 !important;
        background: #fff !important;
        font-size: 12px !important;
      }

      .comment-tab li {
        width: 50%;
      }

      .comment-tab li a {
        font-size: 14px;
        text-align: center;
        color: #124896;
        text-decoration: underline;
      }

      .nav-pills > li.active > a:hover {
        color: #666;
      }

      .list-comments .comment-item .comment-content .name b {
        width: 100%;
        float: left;
      }

      .numberOfDay {
        width: 100%;
        float: left;
        margin-left: -270px;
        margin-top: -13px;
        height: 120px;
        font-size: 12px !important;
        text-align: left !important;
      }

      .progressbar-text {
        color: #111 !important;
      }

      .course-list-container .panel-default > .panel-heading .group-step-item {
        background: #124896 !important;
        text-align: center !important;
        text-transform: uppercase;
        color: #fff !important;
        font-size: 18px;
        padding: 10px 0 8px 0 !important;
      }

      .course-list-container .panel-default > .panel-heading + .panel-collapse > .panel-body .scroll-items a {
        color: #124896;
      }

      .main .main-course .main-left .cover-container .myplayer {
        height: 394px;
      }

      .main .main-course .main-left .cover-container .server-localtion-container .active {
        background-color: #0D4890 !important;
        color: #fff !important;
      }

      @media only screen and (max-width: 768px) {
        .main-right {
          margin-top: 0 !important;
        }
      }
    </style>
  @endif
  <style>
    .tab-header {
      margin-top: 40px;
      display: flex;
      align-items: center;
      font-family: Quicksand, Arial, sans-serif;
      font-size: 16px;
      font-weight: 500;
      width: 100%;
      border-bottom: 1px solid #E1E1E1;
    }
    .tab-header > li > a {
      cursor: pointer;
      padding: 10px 30px;
      text-decoration: none;
      color: #A5A5A5;
      transition: background-color 0.15s;
    }
    .tab-header > li.active > a,
    .tab-header > li.active > a:hover,
    .tab-header > li.active > a:focus,
    .tab-header > li:hover > a {
      color: black !important;
      background-color: #F7FFF0 !important;
      border-bottom: 3px solid #E1E1E1;
      border-radius: 0;
    }
  </style>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@500&display=swap" rel="stylesheet">
  @if($video != null && $video->server == 'youtube' && $nbOfYoutubeVideo == 1)
    <style>
      .plyr--video {
        overflow: visible !important;
      }
      .plyr__poster {
        display: none;
      }
    </style>
    <link rel="stylesheet"
          href="{{asset('plugin/plyr-video-js/dist/plyr.css')}}?{{filemtime('plugin/plyr-video-js/dist/plyr.css')}}"/>
  @endif
@stop

@section('content')
  <div id="fb-root"></div>

  <div class="main tw-flex">
    <div class="main-top">
      @if (!$cdbh)
        @include('frontend.course.course-cta', ['course' => $course, 'unlock' => $unlock])
      @endif
    </div>
    <div class="main-center main-course">
      <div class="main-left @if($premium == true) main-left-premium @endif">
        <h2 class="lesson-detail-title">
          @if (!$unlock && $thisLesson->is_secret == 1 && $thisLesson->price_option > 0)
            <b>Nội dung đã bị ẩn</b>
          @else
            <b>{{ $thisLesson->name }}</b>
          @endif
          @if (count($otherVideo) > 1 && !is_null($video) && $video->video_title != null && $video->video_title != "")
            <br><br><b>{{ $video->video_title }}</b>
          @endif
        </h2>
{{--        @if($adminSession == true)--}}
{{--          <p style="width: 100%; float: left; margin: 5px 0 0px;">--}}
{{--            <i class="zmdi zmdi-time-countdown"></i> <b>{{ $thisLesson->count_view }}</b> Lượt xem--}}

{{--            --}}{{-- @if($cdbh == true)--}}
{{--            <span style="float: right;">--}}
{{--                @if(Auth::check())--}}
{{--                    {{Auth::user()->id}}:{{Auth::user()->name}}--}}
{{--                @else--}}
{{--                    Chưa đăng nhập--}}
{{--                @endif--}}
{{--            </span>--}}
{{--            @endif --}}
{{--          </p>--}}
{{--        @endif--}}
        {{-- Hien thi so ngay het han --}}
{{--        @if(!in_array($course->id, array(27, 28, 29)))--}}
{{--          @if($premium == true)--}}
{{--            @if($unlock == 0 && $course->price != 0)--}}
{{--              <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">--}}
{{--                <button name="buyCourseButton" id="buyCourseBtn"--}}
{{--                        class="premium-expried buy-this-course buy-in-lesson">Mua khóa học này <i--}}
{{--                      class="fa fa-cart-plus"></i></button>--}}
{{--              </a>--}}
{{--            @endif--}}
{{--          @endif--}}
{{--        @endif--}}

        {{-- nếu người dùng chưa đăng nhập --}}
        @if ($thisLesson->course_name != "N5" && !Auth::check() && $thisLesson->price_option != 0)
          <div class="cover-container guest-cover-container">
            <h3><i class="zmdi zmdi-lock"></i> Bạn cần đăng nhập để học hoặc bình luận</h3>
            <a data-fancybox data-animation-duration="300" data-src="#auth-container">
              <div class="btn-register" onclick="swichTab('login')">Đăng nhập</div>
            </a>
            <a data-fancybox data-animation-duration="300" data-src="#auth-container">
              <div class="btn-login" onclick="swichTab('register')">Tạo tài khoản</div>
            </a>
          </div>

          {{-- nếu là bài giảng yêu cầu đăng nhập của n5 --}}
        @elseif (!Auth::check() && $thisLesson->feature == 0 && $thisLesson->course_id == 5)
          <div class="cover-container guest-cover-container">
            <div class="free-course-info">
              <h3><i class="zmdi zmdi-info-outline"></i> Đây là bài giảng miễn phí. Bạn có thể tham gia
                học ngay sau khi đăng nhập</h3>
            </div>
            <a data-fancybox data-animation-duration="300" data-src="#auth-container">
              <div class="btn-register" onclick="swichTab('login')">Đăng nhập</div>
            </a>
            <a data-fancybox data-animation-duration="300" data-src="#auth-container">
              <div class="btn-login" onclick="swichTab('register')">Tạo tài khoản</div>
            </a>
          </div>

          {{-- nếu người dùng đã đăng nhập và đã mua --}}
        @else

          <div class="cover-container">

            {{-- nếu bài học có video mp4 --}}
            @if($video != null && $video->server != 'youtube')
              @if ($video->type == 2)
                @include('frontend.course.components.mp4')
              @elseif ($video->type == 12)
                @include('frontend.course.components.interactive_video')
              @endif
              {{-- nếu video lưu trữ trên youtube --}}
            @elseif($video != null && $video->server == 'youtube' && $nbOfYoutubeVideo == 1)
              @include('frontend.course.components.youtube')
            @endif
            <ul class="tab-header nav nav-pills">
              <li id="doc-tab" class="active"><a data-toggle="pill" href="#lesson-content-detail">Tài liệu - Kiểm tra</a></li>
              <li id="comment-tab"><a data-toggle="pill" href="#comment-container">Bình luận của học viên</a></li>
            </ul>
            <div class="tab-content">
              {{-- dành cho các task khác --}}
              @if(sizeof($tasks) > 0)
                <div class="lesson-content-detail tab-pane fade in active" id="lesson-content-detail">
                  @if (!$cdbh)
                    <div class="roadmap-container" id="roadmap-container" style="display: none;">
                      <div class="roadmap-bg">
                        <div class="form">
                          <h3>
                            Chào mừng đến với khóa học <br/>Dũng Mori<br/>
                            Hãy để Mori chan giúp bạn lên<br/> kế hoạch học tập nhé!
                          </h3>
                        </div>
                        <div class="form-group">
                          <h4><img class="lazyload object-cover" style="width: 18px; margin-left: -18px;" src="{{url('assets/img/qarm.png')}}"/> Bạn sẽ học khoá này trong bao lâu ?</h4>
                          @foreach($period as $pritem)
                            <p>
                              <input type="radio" id="period-id-{{ $pritem->id }}" v-model="currPeriod" name="roadmap" value="{{ $pritem->id }}"/>
                              <label for="period-id-{{ $pritem->id }}">{{ $pritem->name }}</label><br/>
                              <small>{{ $pritem->description }}</small>
                            </p>
                          @endforeach
                          <p><input type="radio" name="roadmap" id="freepr" v-model="currPeriod" :value="0">
                            <label for="freepr" style="color: #333;">Tôi muốn học tự do</label><br/>
                            <small>Học viên tự học theo thời gian biểu của mình.</small>
                          </p>
                          <button class="roadmap-btn-submit" v-on:click="chooseRoadmap()" v-if="submiting == false">Đăng ký</button>
                          <button class="roadmap-btn-submit" v-if="submiting == true">Đang lưu...</button>
                        </div>
                      </div>

                    </div>
                  @endif

                  <?php $isBKT = false; ?>
                  <?php $isQuiz = false; ?>

                  @if (sizeof($tasks) === 1 && $tasks[0]->type === 2)
                    <p class="text-center text-base">Bài học không đính kèm tài liệu</p>
                  @endif

                  @foreach($tasks as $key => $task)
                    {{-- nếu là nội dung --}}
                    @if($task->type == 1 && count($otherVideo) <= 1 && $task->is_quiz != 1)
                      <hr style="width: 100%;">
                      <div v-html="printContentWithAudio(`{{$task->value}}`)"></div>
                      {{-- nếu có nhiều video youtube khác --}}
                    @elseif ($task->type == 2 && $nbOfYoutubeVideo > 1)
                      @include('frontend.course.components.youtube_tasks')
                      {{-- nếu là câu trả lời --}}
                    @elseif ($task->type == 3 && $task->is_quiz != 1)
                      <?php $isBKT = true; ?>
                      <div style="margin-top: 25px; display: inline-block;"
                           v-html="printContentWithAudio(`{{$task->value}}`)"></div>
                      <div
                          style="width: 100%; flex-wrap: wrap; display: flex; -webkit-box-pack: justify;">
                        @foreach ($taskToAnswer[$task->id] as $key => $answer)
                          <div style="width: 48%; margin-top: 10px;">
                            <label for="answer<?php echo htmlspecialchars($answer->id); ?>"
                                   class="col-md-11 answers-input flex items-center gap-1"
                                   style="font-weight: normal; font-size: 13px; color: gray">
                              <span style="display: none;">{{ $answer->id }}</span>
                              <input type="radio"
                                     class="custom-control-input col-md-1 answers-input !mt-0"
                                     id="answer<?php echo htmlspecialchars($answer->id); ?>"
                                     name="task<?php echo htmlspecialchars($task->id); ?>">
                              <span style="display: none;">{{ $answer->grade }}</span>&nbsp;&nbsp;{!! $answer->value !!}
                            </label>
                          </div>
                        @endforeach
                      </div>

                      {{-- nếu là dạng xem đáp án --}}
                    @elseif ($task->type == 4 && $task->is_quiz != 1)

                      {{-- nếu là xem lời giải ở bài test / yêu cầu bấm nộp bài xong mới hiện nút --}}
                      @if($isBKT == true)
                        <div id="see-correct-answer">
                          <button type="button"
                                  class="see-correct-answer-btn btn btn-info btn-display-answer"
                                  disabled data-toggle="collapse"
                                  data-target="#collapse{{ $task->id }}">
                            <i class="zmdi zmdi-sun"></i> Lời giải chi tiết <br/>
                            (xem được sau khi nộp bài)
                          </button>
                        </div>
                        {{-- nếu là xem lời giải ở video -> luôn luôn hiện --}}
                      @else
                        <div id="see-guides">
                          <button type="button" class="btn btn-info btn-display-answer"
                                  data-toggle="collapse" data-target="#collapse{{ $task->id }}">
                            <i class="zmdi zmdi-sun"></i> Xem giải thích chi tiết
                          </button>
                        </div>
                      @endif
                      <div id="collapse{{ $task->id }}" class="collapse">
                        <div class="answer-box answer-box-area"
                             v-html="JSON.parse(tasks[{{$key}}].value)[0].content"></div>
                      </div>


                      {{-- nếu là audio mp3 --}}
                    @elseif ($task->type == 5)

                      <div class="multimedia-item">
                        <label>{{ json_decode($task->value)->name }}</label>
                        <audio controls="">
                          <source
                              src="https://mp3-v2.dungmori.com/{{ json_decode($task->value)->link }}"
                              type="audio/mpeg">
                          Your browser does not support the audio element.
                        </audio>
                      </div>

                      {{-- nếu là dạng câu hỏi bài test --}}
                    @elseif ($task->type == 6 && $task->is_quiz != 1)

                      <table class="table table-hover">
                        <thead>
                        <tr>
                          <th>Stt</th>
                          <th>Câu hỏi</th>
                          <th>Trả lời</th>
                          <th class="text-center">Điểm</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($writeQuestions[$task->id] as $key => $question)
                          <tr>
                            <td style="width: 1%">{{ $key + 1 }}</td>
                            <td style="width: 40%">{!! $question->question !!}</td>
                            <td style="width: 40%"><input class="form-control" type="text"
                                                          id="write-question{{ $task->id }}-{{ $question->id }}"
                                                          name="write-question{{ $question->id }}">
                            </td>
                            <td class="text-center">{{ $question->grade }}</td>
                          </tr>
                        @endforeach
                        </tbody>
                      </table>
                      <hr style="border: 0; border-bottom: 1px solid #ddd;">
                      <!-- tài liệu pdf -->
                    @elseif ($task->type == 8)
                      <div class="app-pdf">
                        <div role="toolbar" class="toolbar">
                          <div class="pager">
                            <p style="color: #555"><i class="fa fa-file-pdf-o"
                                                      style="color: red;"></i> Tài liệu PDF</p>
                          </div>
                          <div class="page-mode">
                            <input type="hidden" value="1" min="1"/>
                          </div>
                        </div>

                        <div class="viewport-container viewport">
                          <div role="main" id="viewport-{{$task->id}}"></div>
                        </div>

                      </div>
                    @elseif ($task->type == 9)
                      <?php $isFlashcards = true; ?>
                      @include('frontend.course.components.flashcards')
                      @break;
                    @elseif($task->is_quiz == 1)
                      <?php $isQuiz = true; ?>
                      @include('frontend.course.components.mixed-test')
                      @break;
                    @endif
                  @endforeach
                  {{-- import riêng cho flashcards --}}
                  @if (isset($isQuiz) && !$isQuiz)
                    {{-- nút nộp bài và thông báo kết quả kiểm tra --}}
                    @include('frontend.course.components.exercises')
                  @endif
                  <modal v-if="suggestionShowed" v-cloak :bg-color="'background-color: transparent;'"
                         :shadow="'box-shadow: none;'" @close="suggestionShowed = false">
                    <div slot="body">
                      <div style="position: relative; width: 600px; margin: auto">
                        <img
                            class="lazyload object-cover"
                            :src="suggestToShow.url"
                            alt="Gợi ý học tập"
                            style="display: block;max-width: 600px;cursor: pointer;user-select: none"
                            @click="sendSuggestMessage"
                        >
                        <span @click="suggestionShowed = false" class="text-red"
                              style="font-size: 20px;position: absolute;top: 5px;right: 15px;cursor: pointer;"><i
                              class="fa fa-times fa-lg"></i></span>
                        <div
                            style="
                                position: absolute;bottom: 20px;width: 100%;text-align: center;
                                color: #fff;text-shadow: 1px 1px 1px #000;font-family: Montserrat;font-size: 30px;"
                        >@{{ suggestionCountdown }}
                        </div>
                      </div>
                    </div>
                  </modal>
                </div>
              @endif
              @if(!isset ($isFlashcards))

                {{-- container comment học viên --}}
                <div class="comment-container tab-pane fade in" id="comment-container">

                  {{-- comment của dũng mori --}}
                  @if($cdbh == false)
                    <ul class="nav nav-pills comment-tab dmr">
                      <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý
                          kiến học viên</a></li>
                      <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình
                          luận bằng facebook</a></li>
                    </ul>

                    <div class="tab-content dmr">
                      <div id="user-comment-content" class="tab-pane fade in active">
                        @if( Auth::check() && ( $unlock == 1 || $thisLesson->price_option == 0))
                          <comments meid="{{ Auth::user()->id }}"
                                    avatar="{{ Auth::user()->avatar }}"
                                    tbid="{{ $thisLesson->id }}"
                                    tbname="lesson"
                                    num-posts="15"
                                    background="#fff"
                                    ref="comment">
                          </comments>
                        @else
                          <comments tbid="{{ $thisLesson->id }}"
                                    tbname="lesson"
                                    num-posts="15"
                                    background="#fff"
                                    ref="comment">
                          </comments>
                        @endif
                      </div>

                      <div id="facebook-comment-content" class="tab-pane fade">
                        <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
                      </div>
                    </div>
                  @endif
                  {{-- end comment của dũng mori --}}

                  {{-- comment của bắc hà --}}
                  @if($cdbh == true)
                    <ul class="nav nav-pills comment-tab bhschool" style="margin-bottom: 20px;">
                      <li class="li-tab bh-tab active"><a data-toggle="pill" href="#bh-comment-content">Bình
                          luận</a></li>
                      <li class="li-tab dmr-tab"><a data-toggle="pill" href="#dmr-comment-content">Xem bình
                          luận Dũngmori</a></li>
                    </ul>
                    <div class="tab-content bhschool">
                      <div id="bh-comment-content" class="tab-pane fade in active">
                        <comments-bh meid="{{ Auth::user()->id }}"
                                     avatar="{{ Auth::user()->avatar }}"
                                     tbid="{{ $thisLesson->id }}"
                                     tbname="lesson"
                                     num-posts="15"
                                     background="#fff"
                                     ref="comment">
                        </comments-bh>
                      </div>
                      <div id="dmr-comment-content" class="tab-pane fade">
                        <comments tbid="{{ $thisLesson->id }}"
                                  tbname="lesson"
                                  num-posts="15"
                                  background="#fff"
                                  ref="comment">
                        </comments>
                      </div>
                    </div>
                  @endif
                  {{-- end comment của bắc hà --}}
                </div>
              @endif
            </div>
          </div>
      @endif
    </div>
      {{-- kết thúc block mainleft --}}

      {{-- khóa n1, n2, n3 tiến trình kiểu premium --}}
      @if($premium == true)
        <div class="main-right-premium">
          {{--          <div class="see-more" style="text-align: right; color: red;">--}}
          {{--            <a href="{{url('/bang-gia')}}"><i class="fa fa-sticky-note" aria-hidden="true"></i> Các khóa--}}
          {{--              học khác</a>--}}
          {{--            @if($unlock == 1 && $thisLesson->price_option > 0 && in_array($course->id, array(3,16,17)))--}}
          {{--              --}}{{-- chỉ bật cho n3 n2 n1 --}}
          {{--              đổi lộ trình học <i class="zmdi zmdi-long-arrow-right"></i>--}}
          {{--            @endif--}}
          {{--          </div>--}}
          @if($numberOfDay <= 30 && $numberOfDay != 0)
          <div style="border: 1px dashed green; border-radius: 5px;padding: 10px 5px;margin-bottom: 10px">
            <span style="color: red; font-weight: bold; text-transform: uppercase;">Hết hạn: Còn {{$numberOfDay}} ngày</span>
            <p>Lưu ý: Nếu hết thời hạn khóa học, bạn sẽ không thể gia hạn khóa học này được nữa.</p>
          </div>
          @endif

          @if(!$cdbh && $unlock == 1 && in_array($course->id, array(3,4,5,16,17)))
            <div class="adventure-toggle">
              <h4>Thiết lập lộ trình học</h4>
              {{-- chỉ bật cho n3 n2 n1 --}}
              <a class="gear-option" data-fancybox data-animation-duration="300"
                 data-src="#roadmap-container">
                <img class="lazyload object-cover" src="{{url('assets/img/gear.png')}}"/>
              </a>
            </div>
          @endif
          @if($checkOwner && $checkOwner->period_id != null)
            @include('frontend.course.components.roadmap')
          @else
            <div class="course-list-premium" v-cloak>
              @if(Auth::check())
                <course-group-premium :price="{{$course->price}}" :categories="{{$categories}}"
                                      :groups="{{$groups}}" :lprogress="{{$lessonProgress}}"
                                      :lessons="{{$lessons}}"
                                      :focus="{{json_encode($thisLesson->getFocusIds())}}"
                                      :courseurl="'{{$course->SEOurl}}'" :auth="1"
                                      :ul="{{$unlock}}"></course-group-premium>
              @else
                <course-group-premium :categories="{{$categories}}" :groups="{{$groups}}"
                                      :lessons="{{$lessons}}"
                                      :focus="{{json_encode($thisLesson->getFocusIds())}}" :courseurl="'{{$course->SEOurl}}'"
                                      :ul="{{$unlock}}"></course-group-premium>
              @endif
            </div>
          @endif
        </div>
        {{-- nếu là các khóa thường --}}
      @else

        <div class="main-right">

          {{-- tiến trình học --}}
          <div class="course-list-container lesson-course-list" id="course-list-pc">
            @if($numberOfDay <= 30 && $numberOfDay != 0)
              <div style="border: 1px dashed green; border-radius: 5px;padding: 10px 5px;margin-bottom: 10px">
                <span style="color: red; font-weight: bold; text-transform: uppercase;">Hết hạn: Còn {{$numberOfDay}} ngày</span>
              </div>
            @endif

            @if(!in_array($course->id, array(27, 28, 29)))
              @if($unlock == 0 && $course->price != 0)
                <div class="buy-item">
                  <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                    <div class="buy-btn">Mua khóa học này</div>
                  </a>
                </div>
              @endif
            @endif

            <div class="block-title" id="lesson-list-detail"> Tiến trình học</div>


            @if(Auth::check())
              <course-group :ejulimit="{{$ejuLimit}}" :price="{{$course->price}}"
                            :lessonProgress="{{$lessonProgress}}" :groups="{{$groups}}"
                            :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :auth="1"
                            :ul="{{$unlock}}" :type="'pc'"></course-group>
            @else
              <course-group :ejulimit="{{$ejuLimit}}" :groups="{{$groups}}" :lessons="{{$lessons}}"
                            :courseurl="'{{$course->SEOurl}}'" :ul="{{$unlock}}"
                            :type="'pc'"></course-group>
            @endif
          </div>

        </div>
      @endif{{-- kết thúc block mainright --}}

    </div>
  </div>

  @section('fixed-panel')

  @stop
@stop
@section('footer-js')
  @if(sizeof($tasks) > 0)
      <script type="text/javascript">
          var playMp3 = null;
          var isFlashcards = false;
          var isQuiz = false;
          <?php $isQuiz = false; ?>
          <?php $isBKT = false; ?>

          function listenMp3(audio) {

              audio = audio.replace(/(<([^>]+)>)/gi, "");
              audio = audio.replace(/\s+/gi, '');
              /* console.log("chuỗi sau xóa dấu cách-"+ audio + "-"); */

              if (playMp3 == null) playMp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
              else {
                  playMp3.pause();
                  playMp3.currentTime = 0;
                  playMp3 = new Audio('https://mp3-v2.dungmori.com/' + audio);
              }

              playMp3.play();
          }

          //tắt mp3 khi đóng popup đáp án
          function turnOffMp3() {
              playMp3.pause();
              playMp3.currentTime = 0;
          }
          @foreach($tasks as $task)
              @if($task->type == 1 && count($otherVideo) <= 1 && $task->is_quiz != 1)
              @elseif ($task->type == 2 && $nbOfYoutubeVideo > 1)
              @elseif ($task->type == 3 && $task->is_quiz != 1)
              <?php $isBKT = true; ?>
              @elseif ($task->type == 4 && $task->is_quiz != 1)
              @elseif ($task->type == 5)
              @elseif ($task->type == 6 && $task->is_quiz != 1)
              @elseif ($task->type == 8)
              @elseif($task->type == 9)
              isFlashcards = true;
            <?php $isFlashcards = true; ?>
              @elseif ($task->is_quiz == 1)
              isQuiz = true;
          <?php $isQuiz = true; ?>
        @endif
        @endforeach

        if (isFlashcards == false) {
          var params = (new URL(document.location)).searchParams;
          var ref = params.get("ref");
          if (ref === 'notice') {
            $('#comment-tab').addClass('active');
            $('#doc-tab').removeClass('active');
            $('#comment-container').addClass('active');
            $('#lesson-content-detail').removeClass('active');
          }
        }
      </script>
    @endif
  <script type="text/javascript">
      lesson_tasks = {!! json_encode($tasks) !!};
      lesson_answers = {!! json_encode($taskToAnswer) !!};
      lesson_lesson = {!! json_encode($thisLesson) !!};
      lesson_results = {!! json_encode($lessonResults) !!};
      lesson_writeQuestions = {!! json_encode($writeQuestions) !!};
      var currentProgress = {!! json_encode($currentLessonProgress) !!};
      var currentLessonPoints = {!! json_encode($currentLessonPoints) !!};
      var currPeriod = '';
      @if($checkOwner && $checkOwner->period_id !== null)
          currPeriod = {{$checkOwner->period_id}};
      @endif
      var cdbh = {!! json_encode($cdbh) !!};
  </script>
  <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
  {{-- nếu là 1 trong 3 khóa eju --}}
  @if(in_array($course->id, array(8, 9, 10)))
    <script type="text/javascript">$(".eju").addClass("active")</script>
  @endif
  {{-- bê nguyên điều kiện từ view xuống cho khỏi lẫn - phần chạy video --}}
  {{-- nếu người dùng chưa đăng nhập --}}
  @if(!Auth::check() && $thisLesson->price_option != 0)
    {{-- nếu người dùng đã đăng nhập và chưa mua --}}
  @elseif(Auth::check() && $unlock == 0 && $thisLesson->price_option != 0)

  @else
    {{-- nếu bài học có video --}}
    @if($video != null && $video->server != 'youtube')

      <script type="text/javascript">
          lessonInfo            = {!! json_encode($lessonInfo) !!};
          lesson_otherVideo     = {!! json_encode($otherVideo) !!};
          lesson_lessonDetail   = {!! json_encode($thisLesson) !!};
          courseUrl             = "{{ $course->SEOurl }}";
          var userId = null;

          @if(Auth::user())
            userId = {!! Auth::user()->id !!};
          @endif
              {{--mã hóa tên video--}}
          var mdaId = "{{base64_encode(base64_encode(base64_encode($video->video_name)))}}";

          {{--lấy ra danh sách server mà bài học có--}}
          var listServers = {!! json_encode($servers) !!};

          var playerId = '{{$random_id}}';

          var taskId = '{{$video->id}}';

          var playbackSpeed = {{ $thisLesson->default_speed }};

          var videoFull = {{ $video->video_full }};

        var courseObject = @json($course);

        var checkFocusTab = true;
        $(document).ready(function(){
            window.onfocus = function() { checkFocusTab = true; };
            window.onblur = function() { checkFocusTab = false; };
        });

        if (courseObject) {
          var auth = @json(Auth::user());
          if (auth && !auth.is_tester && !auth.is_assistant) {
            setInterval(() => {
              if (checkFocusTab) {
                switch (courseObject.id) {
                  case 5:
                    ga('send', 'event', 'thoi_gian_hoc', 'hoc_n5_cu', 'thoi_gian_hoc');
                    break;
                  case 4:
                    ga('send', 'event', 'thoi_gian_hoc', 'hoc_n4_cu', 'thoi_gian_hoc');
                    break;
                  case 3:
                    ga('send', 'event', 'thoi_gian_hoc', 'hoc_n3', 'thoi_gian_hoc');
                    break;
                  case 16:
                    ga('send', 'event', 'thoi_gian_hoc', 'hoc_n2', 'thoi_gian_hoc');
                    break;
                  case 17:
                    ga('send', 'event', 'thoi_gian_hoc', 'hoc_n1', 'thoi_gian_hoc');
                    break;
                }
              }
            }, 60000); // 60s
          }
        }
      </script>

      <script src="{{asset('assets/js/lib-hls.js')}}?{{filemtime('assets/js/lib-hls.js')}}"></script>
      <link href="{{asset('plugin/videojs_hls/videojs.min.css')}}?{{filemtime('plugin/videojs_hls/videojs.min.css')}}"
            rel="stylesheet" type="text/css">
      <link
          href="{{asset('plugin/videojs-markers/dist/videojs.markers.css')}}?{{filemtime('plugin/videojs-markers/dist/videojs.markers.css')}}"
          rel="stylesheet" type="text/css">
      <script
          src="{{asset('plugin/videojs-hotkeys/videojs.hotkeys.js')}}?{{filemtime('plugin/videojs-hotkeys/videojs.hotkeys.js')}}"></script>
      <script
          src="{{asset('plugin/videojs-markers/dist/videojs-markers.js')}}?{{filemtime('plugin/videojs-markers/dist/videojs-markers.js')}}"></script>
{{--      <script src="https://vjs.zencdn.net/ie8/1.1.2/videojs-ie8.min.js"></script>--}}
{{--      <script--}}
{{--          src="{{asset('plugin/videojs-ie8/videojs-ie8.min.js')}}?{{filemtime('plugin/videojs-ie8/videojs-ie8.min.js')}}"></script>--}}
      @if ($video->type == 2)
        {{-- import js file for lesson  --}}
        <script src="{{asset('assets/js/mp4-player.js')}}?{{filemtime('assets/js/mp4-player.js')}}"></script>
      @elseif ($video->type == 12)
        <script src="{{asset('assets/js/pie-timer.js')}}?{{filemtime('assets/js/pie-timer.js')}}"></script>
        <script src="{{asset('assets/js/video-modal.js')}}?{{filemtime('assets/js/video-modal.js')}}"></script>
        <script src="{{asset('assets/js/interactive_video.js')}}?{{filemtime('assets/js/interactive_video.js')}}"></script>
      @endif
    @elseif ($video != null && $video->server == 'youtube' && $nbOfYoutubeVideo == 1)
      <script
          src="{{asset('plugin/plyr-video-js/dist/plyr.min.js')}}?{{filemtime('plugin/plyr-video-js/dist/plyr.js')}}"></script>
      <script>
          var youtubePlayer = new Plyr('#ytplayer', {
              controls: false,
              youtube: { controls: 1, disablekb: 0, enablejsapi: 1, fs: 1, modestbranding: 0 },
              keyboard: { global: true },
          });
          youtubePlayer.on('statechange', function(event) {
              if(event.detail.code == 1) youtubePlayer.play();
          });
      </script>
      <script type="text/javascript">

          // sự kiện click vào chạy video
          $(".movie-play").click(function(){
              $(".movie-play").css("display", "none");
              $("#ytplayer").css("position", "static");
              youtubePlayer.play();
              // $(".ytplayer")[0].src += "?autoplay=1";
          });
      </script>
    @endif

    {{-- phần làm bài kiểm tra --}}
    @if(sizeof($tasks) > 0)
      <script src="{{asset('assets/js/constants.js')}}?{{filemtime('assets/js/constants.js')}}"></script>
      <script src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}"></script>
      <script src="{{asset('assets/js/detail_lesson.js')}}?{{filemtime('assets/js/detail_lesson.js')}}&cache_js"
              nonce="r@nd0m"></script>
    @endif
  @endif

  <script src="{{ asset('/plugin/sweetalert2/sweetalert2.min.js') }}"></script>
  <script
      src="{{asset('plugin/progressbar/dist/progressbar.js')}}?{{filemtime('plugin/progressbar/dist/progressbar.js')}}"></script>

  <script type="text/javascript">
    new Vue({el: '#comment-container'});

    {{-- lưu vết bài học dở vào cookie trong 30 ngày, không lưu đối với bài kiểm tra online --}}
    var lessonInfo = {
      c: "{{ $thisLesson->lesson_group ? $thisLesson->lesson_group->getCourseOfGroup->name : '' }}",
      title: "{{ $thisLesson->name }}",
      url: window.location.href,
      date: "{{date('Y-m-d')}}"
    }
    setCookie("last_lesson", JSON.stringify(lessonInfo), 30);


  </script>
  @if($premium == true)
    <script type="text/javascript"> new Vue({el: '.course-list-premium'}); </script>
    <link href="{{asset('plugin/loading-bar/loading-bar.min.css')}}" rel="stylesheet" type="text/css">
    <script src="{{asset('plugin/loading-bar/loading-bar.min.js')}}"></script>
  @else
    <script type="text/javascript">
      new Vue({el: '#course-list-pc'});
      $(".khoa-hoc").addClass("active");
      $("#collapse-pc{{ $thisLesson->lesson_group->id }}").collapse({toggle: true});
      $(".lesson-item-{{$thisLesson->id}}").addClass("active");

      {{-- cuộn tới item dc focus trong trường hợp danh sách quá dài --}}
      var container = null;
      var scrollTo = null;
      container = $(".scroll-items-{{ $thisLesson->lesson_group->id }}");
      scrollTo = $(".lesson-item-{{$thisLesson->id}}");
      if (container && scrollTo) {
        container.animate({scrollTop: scrollTo.offset().top - container.offset().top - 32 + container.scrollTop()});
      }

    </script>
  @endif

  @if($checkOwner && $checkOwner->period_id == null)
    <script type="text/javascript">
      var first_time_show_popup = getCookie("first_time_show_popup");
      if (first_time_show_popup == null || first_time_show_popup == 0) {

        setTimeout(function () {
          $(".gear-option").click();
          setCookie("first_time_show_popup", 1, 60);
        }, 2000);
      }
    </script>
  @endif

  <script>(function (d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s);
      js.id = id;
      js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>

  <?php $isPdf = true; ?>
  @foreach($tasks as $key => $task)
    @if($task->type == 8)
      @if($isPdf)
        <script src="{{ asset('plugin/pdfjs/pdf.min.js') }}"></script>
          <?php $isPdf = false; ?>
      @endif
      <script
          src="{{asset('assets/js/custom_pdf_view.js')}}?{{filemtime('assets/js/custom_pdf_view.js')}}"></script>
      <script>
        var pdfFile = '{{$task->value}}';
        initPDFViewer("/cdn/pdf/" + pdfFile, "#viewport-{{$task->id}}");
        $(document).ready(function () {
          var widthCanvas = $("#viewport-{{$task->id}}").width();
          $("#viewport-{{$task->id}}").height(widthCanvas * 1.3);
        })
      </script>
    @endif
  @endforeach
@stop

@section('lesson-bottom')
  @if($unlock == 1 && $checkOwner)
    <script type="text/javascript">
        var this_course_id = {{$course->id}};

        var this_l_id = null;
        @if(isset($thisLesson))
            this_l_id = {{$thisLesson->id}};
        @endif
        var paths = {!! json_encode($paths) !!};
        var groupFlashcards = {!! json_encode($groupFlashcards) !!};
        var group_and_flashcards = {!! json_encode($group_and_flashcards) !!};
        var checkpoints = {!! json_encode($checkpoints) !!};
        var periodOptions = {!! json_encode($period) !!};
        var listLessons = {!! json_encode($lessons) !!};
        var myPrg = {!! json_encode($lessonProgress) !!};
        var dailyLogs = {!! json_encode($dailyLogs, JSON_NUMERIC_CHECK) !!};
        var oldGroups = {!! json_encode($groups) !!};
        var oldCategories = {!! json_encode($categories) !!};
    </script>

    <script src="{{asset('plugin/v-calendar/v-calendar.min.js')}}?{{filemtime('plugin/v-calendar/v-calendar.min.js')}}"></script>
    <script src="{{asset('plugin/sweetalert/sweetalert.min.js')}}?{{filemtime('plugin/sweetalert/sweetalert.min.js')}}"></script>
    <script src="{{asset('assets/js/roadmap.js')}}?{{filemtime('assets/js/roadmap.js')}}&cache_js"></script>

  @endif
@stop
