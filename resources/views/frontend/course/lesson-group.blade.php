@extends('frontend._layouts.default')
@section('title')
    {{ $lessonGroup->name }} - Dungmori
@stop
@section('description')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất
@stop
@section('keywords')
    Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất
@stop
@section('image')
    {{ $course->avatar_name ? url('cdn/course/default/' . $course->avatar_name) : url('images/lessons/lesson-default.png') }}
@stop
@section('author')
    DUNGMORI
@stop

@section('header-css')
    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
@stop
@section('footer-js')
    <script src="{{ asset('assets/js/course/basic.js') }}"></script>
@stop
@section('content')
    @php
        $assetVersion = '1.0.0';
        $isLocked = !$isUnlock;
    @endphp
    <div class="bg-[#F4F5FA] lesson-group pb-[440px] min-h-screen">
        <div class="container mx-auto pt-10 font-averta-regular sp:px-4">
            <div class="flex items-center">
                <a href="{{ route('frontend.course.detail', ['url' => $course->SEOurl]) }}">
                    <div class="bg-white rounded-full w-[40px] h-[40px] flex items-center justify-center"
                         style="box-shadow: 0px 2px 4px 0px #4C5D703D;">
                        <img class="rotate-180 w-[14px]" src="{{ asset('images/icons/arrow-right2.png') }}"
                             alt="arrow-right">
                    </div>
                </a>
                <div class="text-[#1E1E1E] text-2xl font-gen-jyuu-gothic-medium ml-3">{{ $lessonGroup->category->title }}
                </div>
            </div>
            <div class="flex items-center my-5">
                <img class="w-[24px] h-[24px]" src="{{ asset('images/icons/information.png') }}" alt="information.png">
                <div class="ml-3 text-xl text-[#07403F] font-averta-semibold">
                    {{ $lessonGroup->name }}
                </div>
            </div>
            <div class="text-base">{{ $lessonGroup->description }}</div>
            <div class="grid grid-cols-4 sp:grid-cols-1 gap-y-4 gap-x-4 gap-y-8 mt-5 justify-stretch">
                @foreach ($lessonGroup->lessons as $key => $lesson)
                    <div
                            style="--data-first-lesson-color:{{ $learningLesson->percent < 85 ? '#e8b93199' : '#57D06199' }};grid-template-columns: repeat(auto-fill, minmax(200px, 1fr))"
                            class="list-lesson grid grid-cols-1 items-start justify-stretch rounded-[24px] bg-white overflow-hidden {{ $lesson->id == $learningLesson->id ? 'first-lesson' : '' }}"
                            onclick="window.location.href='{{ route('frontend.lesson.lesson_basic_new', ['courseSlug' => $course->SEOurl, 'lessonId' => $lesson->id, 'lessonSlug' => $lesson->SEOurl]) }}'">
                        @php
                            $imageUrl = $lesson->avatar_name
                                ? url('cdn/lesson/default/' . $lesson->avatar_name)
                                : url('images/lessons/lesson-default.png');
//                            $isTestOrFlashcard = $lesson->type == 'test' || $lesson->type == 'flashcard';
//                            if ($isTestOrFlashcard) {
//                                $imageUrl = url('images/lessons/' . $lesson->type . '.png');
//                            }
                            $percent = $lessonProgress->where('lesson_id', $lesson->id)->first()->total_progress ?? 0;

                            $iconFolder =
                                $isLocked && ($lesson->price_option == 2 || !auth()->check()) ? 'locked/' : '';
                            $textColor =
                                ($isLocked && $lesson->percent < 85) || $lesson->percent >= 85 ? 'text-[#757575]' : 'text-[#1E1E1E]';

                            if ($lesson->price_option == 0 && (!auth()->check() || $isLocked)) {
                                $iconFolder = '';
                                $textColor = 'text-[#1E1E1E]';
                            }
                        @endphp
                        <div class="image h-[123px] bg-center bg-cover cursor-pointer relative"
                             style="background-image: url('{{ $imageUrl }}')">
                            <div class="flex justify-center items-center h-full">
                                <img class="{{ $lesson->components->where('type', 2)->first() ? '' : 'hidden' }} w-[48px] h-[48px]"
                                     src="{{ asset('images/icons/play-button2.png') }}" alt="play-button2.png">
                                @if ($isLocked && $lesson->price_option == 0)
                                    <span
                                            class="absolute left-[15px] top-[12px] text-[8px] text-white bg-[#57D061] py-0.5 px-2 rounded-full">Học
                                        thử</span>
                                @endif
                            </div>
                        </div>
                        <div class="cursor-pointer p-4 flex items-center justify-stretch">
                            <div class="max-w-[calc(100%-75px)] w-full">
                                <div class="relative">
                                    <div class="truncate flex">
                                        @if ($lesson->name_html)
                                            <div
                                                    class="name-lesson w-full flex font-averta-semibold text-base {{ $textColor }}">{!! $lesson->name_html !!}
                                                @if ($lesson->require)
                                                    <img class="w-[12px] h-[12px]  min-w-[12px]"
                                                         src="{{ asset('images/icons/' . $iconFolder . 'require.png') }}"
                                                         alt="require.png">
                                                @endif
                                            </div>
                                        @else
                                            <div
                                                    class="relative name-lesson truncate font-averta-semibold text-base pr-[15px] {{ $textColor }}">

                                                {{ $lesson->name }}
                                                @if ($lesson->require)
                                                    <img class="w-[12px] min-w-[12px] absolute right-0"
                                                         src="{{ asset('images/icons/' . $iconFolder . 'require.png') }}"
                                                         alt="require.png">
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                @if (in_array($lesson->type, ['last_exam']))
                                  @if ($lesson->passed_exam_result)
                                    <div class="mt-1 font-averta-semibold text-[14px] text-[#07403F] bg-[#86F082] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Đạt</div>
                                  @elseif($lesson->last_exam_result && !$lesson->last_exam_result->submit_at)
                                    <div class="mt-1 font-averta-semibold text-[14px] text-[#414348] bg-[#F5F5F5] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Chưa nộp</div>
                                  @elseif($lesson->exam_result && !$lesson->passed_exam_result)
                                    <div class="mt-1 font-averta-semibold text-[14px] text-[#682D03] bg-[#FFF1C2] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Chưa đạt</div>
                                  @endif
                                @endif
                                <div class="flex flex-wrap gap-y-1 gap-x-4 mt-2">
                                    @if ($lesson->components->where('type', 2)->count() > 0)
                                        <div class="flex items-center">
                                            <img class="w-[12px] flex-none"
                                                 src="{{ asset('images/icons/' . $iconFolder . 'play2.png') }}"
                                                 alt="play2.png">
                                            <span class="ml-[4px] text-sm {{ $textColor }}">
                                                Video
                                            </span>
                                        </div>
                                    @endif
                                    @if ($lesson->component->whereIn('type', \App\Http\Models\LessonToTask::COMPONENT_TYPES_EXERCISE)->count() > 0)
                                        <div class="flex items-center">
                                            @if($lesson->type == 'flashcard')
                                                <img class="w-[12px] flex-none"
                                                     src="{{ asset('images/icons/tag-flashcard.svg') }}"
                                                     alt="file.png">
                                                <span class="ml-[4px] text-sm {{ $textColor }}">
                                                    {{ $lesson->component->where('type', \App\Http\Models\LessonToTask::TYPE_FLASHCARD)->count() }} Từ vựng
                                                </span>
                                            @else
                                                <img class="w-[12px] flex-none"
                                                     src="{{ asset('images/icons/' . $iconFolder . 'file.png') }}"
                                                     alt="file.png">
                                                    <span class="ml-[4px] text-sm {{ $textColor }}">
                                                    Bài tập
                                                </span>
                                            @endif
                                        </div>
                                    @endif

                                    @if ($lesson->components->whereIn('type', [1, 8])->count() > 0)
                                        <div class="flex items-center">
                                            <img class="w-[12px] flex-none"
                                                 src="{{ asset('images/icons/' . $iconFolder . 'docs.png') }}"
                                                 alt="docs.png">
                                            <span class="ml-[4px] text-sm {{ $textColor }}">
                                                Tài liệu
                                            </span>
                                        </div>
                                    @endif

                                    @if ($lesson->components->where('type', 5)->count() > 0)
                                        <div class="flex items-center">
                                            <img class="w-[12px] flex-none"
                                                 src="{{ asset('images/icons/' . $iconFolder . 'speaker.png') }}"
                                                 alt="speaker.png">
                                            <span class="ml-[4px] text-sm {{ $textColor }}">
                                                File âm thanh
                                            </span>
                                        </div>
                                    @endif

                                    @if ($lesson->expect_time)
                                        <div class="flex items-center">
                                            <img class="w-[12px] flex-none"
                                                 src="{{ asset('images/icons/' . $iconFolder . 'clock.png') }}"
                                                 alt="clock.png">
                                            <span class="ml-[4px] text-sm {{ $textColor }}">
                                                @php
                                                    $time = convertMinutesToHours($lesson->expect_time);
                                                @endphp
                                                {{ $time[0] . 'h' . $time[1] . "'" }}
                                            </span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="flex items-center pl-2 ml-auto">
                                @if ($isLocked && $lesson->price_option == 2)
                                    <img class="w-[20px]" src="{{ asset('/images/icons/lock.png') }}" alt="lock">
                                @elseif($lesson->percent >= 85)
                                    <img class="w-[24px] h-[24px]" src="{{ asset('images/icons/done.png') }}"
                                     alt="done.png">
                                @elseif (!in_array($lesson->type, ['exam', 'last_exam']) && $lesson->percent < 85)
                                    @include('frontend.course.components.percentage', [
                                        'percent' => $lesson->percent,
                                    ])
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            <div
                    class="fixed bottom-0 left-0 w-full desktop:backdrop-blur-sm bg-white/50 sp:bg-[#F4F5FA] py-3 sp:py-4 sp:px-5">
                <div class="container">
                    <div class="flex items-center justify-between">
                        @if ($previousGroup)
                            <a
                                    href="{{ route('frontend.lesson.group', ['courseSlug' => $course->SEOurl, 'groupId' => $previousGroup->id]) }}">
                                <div
                                        class="tooltip-custom__wrapper rounded-full border-2 border-[#57D061] py-3 sp:py-1 px-13 cursor-pointer">
                                    <img class="w-[10px] rotate-180 relative bottom-0.5"
                                         src="{{ asset('images/icons/arrow-right2.png') }}" alt="right-arrow2">
                                    @if ($previousGroup)
                                        <span
                                                class="tooltip-custom font-averta-semibold absolute left-0 top-[-45px] rounded">{{ $previousGroup->name }}</span>
                                    @endif
                                </div>
                            </a>
                        @endif

                        @if ($nextGroup)
                            <a class="float-right ml-auto"
                               href="{{ route('frontend.lesson.group', ['courseSlug' => $course->SEOurl, 'groupId' => $nextGroup->id]) }}">
                                <div
                                        class="tooltip-custom__wrapper rounded-full border-2 border-[#57D061] py-3 sp:py-1 px-13 cursor-pointer">
                                    <img class="w-[10px] relative bottom-0.5"
                                         src="{{ asset('images/icons/arrow-right2.png') }}" alt="right-arrow2">
                                    @if ($nextGroup)
                                        <span
                                                class="tooltip-custom font-averta-semibold absolute right-0 top-[-45px] rounded">{{ $nextGroup->name }}</span>
                                    @endif
                                </div>
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{--{{ dd([--}}
    {{--                'course' => $course->toArray(),--}}
    {{--                'lessonGroup' => $lessonGroup->toArray(),--}}
    {{--                'lessonGroupIndex' => $lessonGroupIndex,--}}
    {{--                'lessonProgress' => $lessonProgress->toArray(),--}}
    {{--                'totalGroup' => $totalGroup,--}}
    {{--                'nextGroup' => $nextGroup->toArray(),--}}
    {{--                'previousGroup' => $previousGroup->toArray(),--}}
    {{--                'learningLesson' => $learningLesson->toArray(),--}}
    {{--                'isUnlock' => $isUnlock,--}}
    {{--                'hideBottomMenu' => true,--}}
    {{--                'hideSupportMenu' => true,--}}
    {{--                'hideFooter' => true,--}}
    {{--            ]) }}--}}
    {{-- Learning lesson --}}
    <div class="fixed bottom-[85px] right-0 sp:left-0 desktop:bottom-[150px] desktop:right-[30px] sp:px-5">
        <a
                href="{{ route('frontend.lesson.lesson_basic_new', ['courseSlug' => $course->SEOurl, 'lessonId' => $learningLesson->id, 'lessonSlug' => $learningLesson->SEOurl]) }}">
            <div class="rounded-[24px] next-lesson first-lesson {{ $learningLesson->percent < 85 ? 'bg-[#FFFBEB]' : 'bg-[#F2FFF4]' }} overflow-hidden w-[400px] sp:w-full sp:grid sp:items-center sp:p-2"
                 style="--data-first-lesson-color:{{ $learningLesson->percent < 85 ? '#e8b93199' : '#57D06199' }};grid-template-columns: auto 1fr auto;">
                @php
                    $imageUrl = $learningLesson->avatar_name
                        ? url('cdn/lesson/default/' . $learningLesson->avatar_name)
                        : url('images/lessons/lesson-default.png');

//                    $isTestOrFlashcard = $learningLesson->type == 'test' || $learningLesson->type == 'flashcard';
//                    if ($isTestOrFlashcard) {
//                        $imageUrl = url('images/lessons/' . $learningLesson->type . '.png');
//                    }
                @endphp
                <div class="image h-[123px] bg-center bg-cover cursor-pointer sp:flex-none sp:w-[100px] sp:h-[70px] sp:rounded-[16px]"
                     style="background-image: url('{{ $imageUrl }}')">
                    <div class="flex justify-center items-center h-full">
                        <div>
                            <img class="{{ $learningLesson->components->where('type', 2)->first() ? '' : 'hidden' }} w-[48px] h-[48px] sp:w-[35px] sp:h-[35px]"
                                 src="{{ asset('images/icons/play-button2.png') }}" alt="play-button2.png">
                            <div class="text-white text-xs desktop:hidden">Học tiếp</div>
                        </div>
                    </div>
                </div>
                <div class="cursor-pointer p-4 sp:py-0 sp:px-2 flex overflow-hidden">
                    <div class="max-w-[calc(100%-75px)] desktop:max-w-[calc(100%-75px)] w-full">
                        <div class="relative">
                            <div class="truncate flex">
                                <div
                                        class="relative font-averta-semibold truncate text-base sp:text-xs text-[#1E1E1E] pr-[15px]">
                                    @if ($learningLesson->name_html)
                                        {!! $learningLesson->name_html !!}
                                    @else
                                        {{ $learningLesson->name }}
                                    @endif
                                    @if ($learningLesson->require)
                                        <img class="w-[12px] min-w-[12px] absolute right-0"
                                             src="{{ asset('images/icons/require.png') }}"
                                             alt="require.png">
                                    @endif
                                </div>
{{--                                    <span--}}
{{--                                            class="font-averta-semibold text-base sp:text-xs text-[#1E1E1E]">--}}
{{--                                        @if ($learningLesson->require)--}}
{{--                                            <img class="w-[12px] require-icon"--}}
{{--                                                 src="{{ asset('images/icons/require.png') }}"--}}
{{--                                                 alt="require.png">--}}
{{--                                        @endif--}}
{{--                                    </span>--}}
{{--                                @endif--}}
                            </div>
                        </div>
                        @if (in_array($learningLesson->type, ['last_exam']))
                          @if ($learningLesson->passed_exam_result)
                            <div class="mt-1 font-averta-semibold text-[14px] text-[#07403F] bg-[#86F082] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Đạt</div>
                          @elseif($learningLesson->last_exam_result && !$learningLesson->last_exam_result->submit_at)
                            <div class="mt-1 font-averta-semibold text-[14px] text-[#414348] bg-[#F5F5F5] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Chưa nộp</div>
                          @elseif($learningLesson->exam_result && !$learningLesson->passed_exam_result)
                            <div class="mt-1 font-averta-semibold text-[14px] text-[#682D03] bg-[#FFF1C2] min-w-[100px] px-1 rounded-full inline-flex text-center justify-center">Chưa đạt</div>
                          @endif
                        @endif
                        <div class="flex flex-wrap gap-y-1 gap-x-2 mt-2 sp:gap-1 sp:mt-1">
                            @if ($learningLesson->components->where('type', 2)->count() > 0)
                                <div class="flex items-center">
                                    <img class="w-[12px] flex-none" src="{{ asset('images/icons/play2.png') }}"
                                         alt="play2.png">
                                    <span class="ml-[4px] text-sm text-[#1E1E1E]">
                                        Video
                                    </span>
                                </div>
                            @endif
                            @if ($learningLesson->component->whereIn('type', \App\Http\Models\LessonToTask::COMPONENT_TYPES_EXERCISE)->count() > 0)
                                <div class="flex items-center">
                                    @if($learningLesson->type == 'flashcard')
                                        <img class="w-[12px] flex-none"
                                             src="{{ asset('images/icons/tag-flashcard.svg') }}"
                                             alt="file.png">
                                        <span class="ml-[4px] text-sm {{ $textColor }}">
                                                {{ $learningLesson->component->where('type', \App\Http\Models\LessonToTask::TYPE_FLASHCARD)->count() }} Từ vựng
                                            </span>
                                    @else
                                        <img class="w-[12px] flex-none" src="{{ asset('images/icons/file.png') }}"
                                             alt="file.png">
                                        <span class="ml-[4px] text-sm text-[#1E1E1E]">
                                            Bài tập
                                        </span>
                                    @endif
                                </div>
                            @endif
{{--                            @if ($learningLesson->components->whereIn('type', \App\Http\Models\LessonToTask::COMPONENT_TYPES_EXERCISE)->count() > 0)--}}
{{--                                <div class="flex items-center">--}}
{{--                                    <img class="w-[12px] flex-none" src="{{ asset('images/icons/file.png') }}"--}}
{{--                                         alt="file.png">--}}
{{--                                    <span class="ml-[4px] text-sm text-[#1E1E1E]">--}}
{{--                                        Bài tập--}}
{{--                                    </span>--}}
{{--                                </div>--}}
{{--                            @endif--}}

                            @if ($learningLesson->components->whereIn('type', [1, 8])->count() > 0)
                                <div class="flex items-center">
                                    <img class="w-[12px] flex-none" src="{{ asset('images/icons/docs.png') }}"
                                         alt="docs.png">
                                    <span class="ml-[4px] text-sm text-[#1E1E1E]">
                                        Tài liệu
                                    </span>
                                </div>
                            @endif

                            @if ($learningLesson->components->where('type', 5)->count() > 0)
                                <div class="flex items-center">
                                    <img class="w-[12px] flex-none" src="{{ asset('images/icons/speaker.png') }}"
                                         alt="speaker.png">
                                    <span class="ml-[4px] text-sm text-[#1E1E1E]">
                                        File âm thanh
                                    </span>
                                </div>
                            @endif

                            @if ($learningLesson->expect_time)
                                <div class="flex items-center">
                                    <img class="w-[12px] flex-none" src="{{ asset('images/icons/clock.png') }}"
                                         alt="clock.png">
                                    <span class="ml-[4px] text-sm text-[#1E1E1E]">
                                        @php
                                            $time = convertMinutesToHours($learningLesson->expect_time);
                                        @endphp
                                        {{ $time[0] . 'h' . $time[1] . "'" }}
                                    </span>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="flex-none flex items-center ml-auto sp:hidden">
                        @if ($learningLesson->percent >= 85)
                          <img class="w-[24px] h-[24px]" src="{{ asset('images/icons/done.png') }}" alt="done.png">
                        @elseif (!in_array($learningLesson->type, ['exam', 'last_exam']) && $learningLesson->percent < 85)
                            @include('frontend.course.components.percentage', [
                                'percent' => $learningLesson->percent ?? 0  ,
                            ])
                        @endif
                    </div>
                </div>
                <div class="flex-none flex items-center desktop:hidden">
                    @if (!$learningLesson->require)
                    @elseif (!in_array($learningLesson->type, ['exam', 'last_exam']) && $learningLesson->percent < 85)
                        @include('frontend.course.components.percentage', [
                            'percent' => $learningLesson->percent,
                        ])
                    @elseif($learningLesson->percent >= 85)
                        <img class="w-[24px] h-[24px]" src="{{ asset('images/icons/done.png') }}" alt="done.png">
                    @endif
                </div>
            </div>
        </a>
    </div>
@stop

@section('footer-js')

@stop
