@extends('frontend._layouts.default')

@section('title')
  @if($course->name == 'N5')
    Tiếng Nh<PERSON>t cho người mới bắt đầu - <PERSON>h<PERSON>a học N5 - Dungmori
  @else
    Họ<PERSON> tiếng Nh<PERSON>t online - <PERSON><PERSON><PERSON><PERSON> họ<PERSON> {{ $course->name }}  - Dungmori
  @endif
@stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber dạy tiếng Nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('cdn/course/default')}}/{{ $course->avatar_name }} @stop
@section('author') DUNGMORI @stop

@section('header-js')
    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
@stop

@section('content')

<div id="fb-root"></div>

<div class="main">

@if($unlock == 0 && $course->price != 0)
    <div class="main-eju-feature">
        {{-- <div class="eju-center anhdao-block"> --}}
            <img class="topf" src="{{url('assets/img/eju/top-feature.png')}}"/>
            <img class="topf-mb" src="{{url('assets/img/eju/topf-mb.png')}}"/>
            {{-- <div class="ej-block-right">
                <h5>Examination for Japanese University Admission for International Students</h5>
                <span>Khóa YOSHI EJU chất lượng mở ra con đường ngắn nhất đến các trường đại học Nhật Bản.</span>
                <div class="btn-container">
                    <div class="btn-eju">Học thử ngay</div>
                    <div class="btn-eju btn-tran">Xem thêm</div>
                </div>
            </div> --}}
        {{-- </div> --}}
    </div>

    <div class="main-eju-feature">
        <div class="eju-center why">
            <div class="eju-why-item">
                <img src="{{url('assets/img/eju/ic1.png')}}">
                <div>
                    <b>Chương trình bám sát đề thi</b>
                    <span>Bài giảng siêu dễ hiểu, bám sát xu hướng ra đề. Ngoài ra còn có những mẹo làm bài giúp học viên đạt điểm cao trong kì thi</span>
                </div>
                {{-- <p class="link">Feedback của học viên</p> --}}
            </div>

            <div class="eju-why-item">
                <img src="{{url('assets/img/eju/ic2.png')}}">
                <div>
                    <b>Đội ngũ giáo viên hàng đầu</b>
                    <span>Yoshi Eju luôn tự hào vì đang có đội ngũ giảng viên không những giàu kinh nghiệm thực tiễn mà còn còn là những chuyên gia rất tâm huyết với việc chia sẻ những kĩ năng, kiến thức liên quan đến kì thi EJU</span>
                </div>
                {{-- <p class="link">Xem thêm</p> --}}
            </div>
            <div class="eju-why-item">
                <img src="{{url('assets/img/eju/ic3.png')}}">
                <div>
                    <b>Tối ưu chi phí học tập</b>
                    <span>Học phí của Yoshi EJU tự tin có giá rẻ cạnh tranh trên thị trường giúp các bạn học sinh yên tâm hơn để tập trung vào việc học! Tuy vậy, yếu tố đảm bảo chất lượng vẫn là mục tiêu hàng đầu.</span>
                </div>
                {{-- <p class="link">Bảng giá các khoá học</p> --}}
            </div>
        </div>
    </div>

    <div class="main-eju-feature">
        <div class="eju-center intro">
            <img src="{{url('assets/img/eju/hat.png')}}">
            <h3>YOSHIEJU dành cho ai ?</h3>
            <p>Các bạn học sinh, sinh viên có nguyện vọng theo học tại các trường Đại học Nhật Bản<br/> với mong muốn được tiếp cận với các bài giảng, đề thi sát với kì thi nhất.</p>

            <a href="https://dungmori.com/bai-viet/479-eju-la-gi-" style="color: #888;">
                <p class="intro-more">Xem thêm các thông tin về <a>kì thi EJU</a></p>
            </a>

            <p style="margin-top: 30px;">
                <a href="{{url('khoa-hoc#eju')}}"><span class="register-now">Đăng ký học ngay</span></a>
            </p>
        </div>
    </div>

    <div class="main-eju-feature feature-teacher-eju">

        <div class="parrent-scroll">
        <span class="mobile-arrow-left">
            <i class="zmdi zmdi-long-arrow-right"></i>
        </span>
        <div class="eju-teacher-scroll-container">
        <div class="eju-center list-teacher" style="width: 804px;">
            {{-- <div class="eju-teacher-item">
                <p>Cố vấn chuyên môn</p>
                <img src="{{url('assets/img/eju/teacher-park.png')}}">
                <div>
                    <b>Thầy Park</b><br/><br/>
                    <span>Cử nhân Đại học Hitosubashi Sáng lập Morning EDU - trung tâm dạy EJU hàng đầu Hàn Quốc</span>
                </div>
            </div> --}}

            {{-- <div class="eju-teacher-item seperate">
            </div> --}}

            <div class="eju-teacher-item">
                <img src="{{url('assets/img/eju/teacher-thanh.png')}}">
                <div>
                    <b>Cô Thanh</b><br/><br/>
                    <span>NCS Thạc sĩ Đại học Ngoại Ngữ Tokyo</span>
                </div>
            </div>

            <div class="eju-teacher-item">
                <p class="p-center">Đội ngũ giáo viên</p>
                <img src="{{url('assets/img/eju/teacher-dzung.png')}}">
                <div>
                    <b>Thầy Dũng</b><br/><br/>
                    <span>Cử nhân khoa Giáo dục Đại học Nhật Bản</span>
                </div>
            </div>
            <div class="eju-teacher-item">
                <img src="{{url('assets/img/eju/teacher-nghia.png')}}">
                <div>
                    <b>Thầy Nghĩa</b><br/><br/>
                    <span>Học bổng Thạc sĩ Đại học Quốc Gia Nagoya</span>
                </div>
            </div>
            <div class="eju-teacher-item">
                <img src="{{url('assets/img/eju/teacher-han.png')}}">
                <div>
                    <b>Cô Hân</b><br/><br/>
                    <span>Cử nhân khoa Giáo dục Đại học Quốc Gia</span>
                </div>
            </div>
        </div>
        </div>
        </div>
    </div>

    <div class="main-eju-feature hv-share">
        <h4 style="margin: 50px 0 20px 0;">Tối ưu chi phí học tập</h4>
        <div class="eju-center why hv-fee" style="text-align: center;">
            <img style="display: inline;" src="{{url('assets/img/eju/pricing.png')}}?">
        </div>
    </div>

    <div class="main-eju-feature hv-share">
        <h4>Chia sẻ từ học viên các khoá EJU</h4>

        <div class="parrent-scroll">
        <span class="mobile-arrow-left">
            <i class="zmdi zmdi-long-arrow-right"></i>
        </span>
        <div class="eju-why-scroll-container">
        <div class="eju-center why">
            <div class="eju-why-item">
                <img src="{{url('assets/img/eju/sharing-1.png?')}}">
                <div class="hv-info">
                    <b>Hân Đinh Đỗ</b>
                    <span>Sinh viên ĐH Quốc Gia HN</span>
                </div>
            </div>

            <div class="eju-why-item">
                <img src="{{url('assets/img/eju/sharing-2.jpg?')}}">
                <div class="hv-info">
                    <b>Nguyễn Thị Hân</b>
                    <span>Đại Học Giáo Dục</span>
                </div>
            </div>
            <div class="eju-why-item">
                <img src="{{url('assets/img/eju/sharing-3.png?')}}">
                <div class="hv-info">
                    <b>Nguyễn Phương Anh</b>
                    <span>Đại Học Hà Nội</span>
                </div>
            </div>
        </div>
        </div>
        </div>
    </div>

    <div class="main-eju-feature" style="border-bottom: 0.5px solid #4F92E4; color: #124896; height: 43px; font-weight: bold;">
        <div class="eju-center introx" style="width: 250px; background: #fff; ">
            <img src="{{url('assets/img/eju/hat.png')}}">
            <h4 style="font-weight: 600;">Hệ thống các khoá EJU</h4>
        </div>
    </div>

@endif

<div class="main-center main-course">

    <div class="main-left @if($premium == true) main-left-premium @endif">


        {{-- nếu là 1 trong 3 khóa eju --}}
        @if($isEju)
            <div class="eju-top-menu" style="margin-top: 65px;">
                <div class="eju-course-item @if($course->id == 8) active @endif">
                    <a href="{{url('khoa-hoc/eju')}}">
                        @if($course->id == 8) <img src="{{url('assets/img/e1-active.png')}}"/> @else <img src="{{url('assets/img/e1.png')}}"/> @endif <br/>
                        <span>EJU Tiếng Nhật</span>
                    </a>
                    <br/>
                    @if($daysEju == -1)
                        <a href="{{url('payment?buy=Y291cnNl&item=OA==')}}">
                            <div class="buy-eju-btn"><i class="fa fa-cart-plus"></i> &nbsp; Mua khóa học này</div>
                        </a>
                    @else
                        <div class="buy-eju-btn" style="border-color: green; color: green;">
                            <i class="fa fa-check-square"></i>&nbsp; còn {{$daysEju}} ngày
                        </div>
                    @endif
                </div>

                <div class="eju-course-item @if($course->id == 10) active @endif">
                    <a href="{{url('khoa-hoc/eju-xhth')}}">
                        @if($course->id == 10) <img src="{{url('assets/img/e2-active.png')}}"/> @else <img src="{{url('assets/img/e2.png')}}"/> @endif <br/>
                        <span>EJU Xã Hội Tổng Hợp</span>
                    </a>
                    <br/>
                    @if($daysEjuXH == -1)
                        <a href="{{url('payment?buy=Y291cnNl&item=MTA=')}}">
                            <div class="buy-eju-btn"><i class="fa fa-cart-plus"></i> &nbsp; Mua khóa học này</div>
                        </a>
                    @else
                        <div class="buy-eju-btn" style="border-color: green; color: green;">
                            <i class="fa fa-check-square"></i>&nbsp; còn {{$daysEjuXH}} ngày
                        </div>
                    @endif
                </div>
                <div class="eju-course-item @if($course->id == 9) active @endif">
                    <a href="{{url('khoa-hoc/eju-toan')}}">
                        @if($course->id == 9) <img src="{{url('assets/img/e3-active.png')}}"/> @else <img src="{{url('assets/img/e3.png')}}"/> @endif <br/>
                        <span>EJU Toán</span>
                    </a>
                    <br/>
                    @if($daysEjuT == -1)
                        <a href="{{url('payment?buy=Y291cnNl&item=OQ==')}}">
                            <div class="buy-eju-btn"><i class="fa fa-cart-plus"></i> &nbsp; Mua khóa học này</div>
                        </a>
                    @else
                        <div class="buy-eju-btn" style="border-color: green; color: green;">
                            <i class="fa fa-check-square"></i>&nbsp; còn {{$daysEjuT}} ngày
                        </div>
                    @endif
                </div>
            </div>
            <div class="eju-top-menu">
                <div class="eju-course-item @if($course->id == 38) active @endif">
                    <a href="{{url('khoa-hoc/eju-toan-ii')}}">
                        @if($course->id == 38) <img src="{{url('assets/img/e4-active.png')}}"/> @else <img src="{{url('assets/img/e4.png')}}"/> @endif <br/>
                        <span>EJU Toán 2</span>
                    </a>
                    <br/>
                    @if($daysEju == -1)
                        <a href="{{url('payment?buy=Y29tYm8=&item=ODk=')}}">
                            <div class="buy-eju-btn"><i class="fa fa-cart-plus"></i> &nbsp; Mua khóa học này</div>
                        </a>
                    @else
                        <div class="buy-eju-btn" style="border-color: green; color: green;">
                            <i class="fa fa-check-square"></i>&nbsp; còn {{$daysEju}} ngày
                        </div>
                    @endif
                </div>
                <div class="eju-course-item @if($course->id == 36) active @endif">
                    <a href="{{url('khoa-hoc/eju-ly')}}">
                        @if($course->id == 36) <img src="{{url('assets/img/e5-active.png')}}"/> @else <img src="{{url('assets/img/e5.png')}}"/> @endif <br/>
                        <span>EJU Vật Lý</span>
                    </a>
                    <br/>
                    @if($daysEju == -1)
                        <a href="{{url('payment?buy=Y29tYm8=&item=OTE=')}}">
                            <div class="buy-eju-btn"><i class="fa fa-cart-plus"></i> &nbsp; Mua khóa học này</div>
                        </a>
                    @else
                        <div class="buy-eju-btn" style="border-color: green; color: green;">
                            <i class="fa fa-check-square"></i>&nbsp; còn {{$daysEju}} ngày
                        </div>
                    @endif
                </div>
                <div class="eju-course-item @if($course->id == 37) active @endif">
                    <a href="{{url('khoa-hoc/eju-hoa')}}">
                        @if($course->id == 37) <img src="{{url('assets/img/e6-active.png')}}"/> @else <img src="{{url('assets/img/e6.png')}}"/> @endif <br/>
                        <span>EJU Hoá Học</span>
                    </a>
                    <br/>
                    @if($daysEju == -1)
                        <a href="{{url('payment?buy=Y29tYm8=&item=OTA=')}}">
                            <div class="buy-eju-btn"><i class="fa fa-cart-plus"></i> &nbsp; Mua khóa học này</div>
                        </a>
                    @else
                        <div class="buy-eju-btn" style="border-color: green; color: green;">
                            <i class="fa fa-check-square"></i>&nbsp; còn {{$daysEju}} ngày
                        </div>
                    @endif
                </div>
            </div>
            <style type="text/css">
                .server-localtion{background-color: #F0F6FF !important; color: #0D4890 !important; }
                .course-tab{background: #77C2EF !important;}
                .course-tab > .active > a{ background-color: #105B89 !important; }
                .course-tab > .li-tab > a{ color: #fff !important; }
                .course-list-container > .block-title{background: #124896; text-align: center;}
                .course-list-container .panel-default > .panel-heading a {background: #F0F6FF !important; color: #124896;  text-align: center;}
                .course-list-container .panel-default > .panel-heading a strong{width: 100%; text-align: center;}
                .scroll-items .pull-right{color: #124896;}

                .course-list-container .panel-default .panel-body li a .free{background: #124896;}
                .course-list-container .panel-default > .panel-heading > .group-step-item{background: rgb(238, 238, 238) !important;}
                .course-list-container .panel-default .panel-body li a {background: #FBFCFF !important; font-size: 13px; font-weight: 300; font-family: Roboto;}
                .course-list-container .panel-default .panel-body li a:hover {color: #124896; font-weight: bold;}

                .comment-tab { border-bottom: 1px solid #fff !important; margin-bottom: 0; }
                .comment-tab .active > a {background-color: #F0F6FF !important;}
                .nav-pills > li.active > a{color: #124896; font-size: 14px; text-align: center;}
                .comment-tab .active > .active{ color: #124896; border: 1px solid #124896 !important; }
                .list-comments .comment-item .comment-content .name b {color: #105B89 !important; }
                .comment-action > a{color: #105B89 !important;}
                .child-comment-item .comment-content .child-name b {color: #105B89 !important; }
                .list-comments .input-comment-container .post-comment-btn {background: #124896 !important;}
                .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {background: #124896 !important;}
                .list-comments .load-more-comment {background: #F0F6FF !important; color: #0D4890; font-weight: bold;}
                .main-right{margin-top: 415px !important;}
                .main .main-course .main-right .course-info-container {margin-top: 35px; margin-left: -270px;}
                .main .main-course .main-left .course-detail-container .course-price-container{
                    border: 2px dashed #4F92E4 !important; background: #fff !important; font-size: 12px !important; }
                .comment-tab li{width: 50%;}
                .comment-tab li a{ font-size: 14px; text-align: center; color: #124896; text-decoration: underline; }
                .nav-pills > li.active > a:hover{color: #666;}
                .list-comments .comment-item .comment-content .name b { width: 100%; float: left; }
                .numberOfDay{width: 100%; float: left; margin-left: -270px; margin-top: -13px; height: 120px; font-size: 12px !important; text-align: left !important;}
                .lesson__progress--circle .progressbar-text {color: #222 !important;}
                .progressbar-text{color: #fff !important;}
                @media only screen and (max-width: 768px) {
                    .main-right{margin-top: 0 !important;}
                }
                .main .main-course .main-right-premium{margin-top: 186px;}
                .main .main-course .main-left-premium .premium-expried {position: absolute; margin-top: 226px; margin-left: 438px; }
                .fixed-panel{display: none;}
                .buy-eju-btn{padding: 5px 20px; width: 190px; margin-top: 20px; display: inline-block; border: 1px solid #E74C3C; border-radius: 14px; color: #E74C3C; font-size: 12px; font-weight: bold; font-family: Roboto; }
                .main .main-course .m-r-eju .course-list-container { margin-top: 0px; }
                .eju-course-item a{color: #666;}
            </style>
        @endif

        @if (!Auth::check() && (in_array($course->name, ["N5", "Chuyên Ngành"])))
            <div class="free-course-box">
                <div class="icon-container">
                    <i class="zmdi zmdi-info"></i>
                </div>
                <div class="content-container">
                    <span>@if($course->name == "N5") Khóa N5 @elseif($course->name == "Chuyên Ngành") Khóa Chuyên ngành @endif @if($course->name == "N5") giờ đây đã @endif hoàn toàn miễn phí tại Dungmori.com, các bạn có
                        thể học ngay sau khi đăng nhập, hãy
                        <a data-fancybox data-animation-duration="300" data-src="#auth-container"
                        onclick="swichTab('register')" style="cursor: pointer;">Tạo tài khoản</a> hoặc
                        <a data-fancybox data-animation-duration="300" data-src="#auth-container"
                        onclick="swichTab('login')" style="cursor: pointer;">Đăng nhập</a> để học ngay
                    </span>
                </div>
            </div>
        @endif

        <div class="btn-list-lesson-box">
            <button class="btn btn-list-lesson" id="btn-list-lesson" onclick="goToLessonListNow();">
                Bắt đầu học&nbsp;&nbsp;<i class="zmdi zmdi-format-line-spacing"></i>
            </button>
        </div>

        <div class="cover-container">
            <div class="movie-play" >
                <img style="box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.25); height: 380px;" src="{{url('cdn/course/default')}}/{{ $course->avatar_name }}"/>
                <br>
                @include('frontend.course.play-button')
            </div>
            <?php preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $course->link, $match); ?>
            <iframe id="iframe-youtube" style="display: none;" width="100%" height="395" frameborder="0" gesture="media" allow="encrypted-media" allowfullscreen
            src="https://www.youtube.com/embed/{{ $match[1] }}?rel=0"></iframe>

            <div class="server-localtion-container">
                <span class="server-localtion active"># Máy chủ youtube</span>
            </div>

            <div class="course-info-container course-info-status-mobile">

                @if($unlock == 0 && $course->price != 0)
                  <div class="buy-item">
                    <a href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}">
                      <div class="buy-btn">Mua khóa học này</div>
                    </a>
                  </div>
                @elseif($unlock == 0 && $course->price == 0)
                  <div class="buy-item">
                    <div class="buy-btn bought">Miễn phí</div>
                  </div>
                @else
                  <div class="buy-item">
                    <div class="buy-btn bought">Bạn đã mua <i class="zmdi zmdi-check-circle"></i></div>
                  </div>
                @endif

            </div>
            @includeIf('frontend.course.components.fb_btn_message')
        </div>


            <p class="eju-tab-title">Xem thử các bài giảng</p>

            <div class="parrent-scroll ez-preview">
                <span class="mobile-arrow-left">
                    <i class="zmdi zmdi-long-arrow-right"></i>
                </span>
                <div class="eju-why-scroll-container">
                    <div id="preview-content" class="tab-pane fade in active">
                        <div class="preview-course-container">
                            @foreach($previewLessons as $item)
                            <div class="course-item" style="width: 220px; box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.25);">
                              <div class="images">
                                  <a href="{{url('khoa-hoc')}}/{{ $course->SEOurl }}/{{ $item->id }}-{{ $item->SEOurl }}">
                                    <img src="{{url('cdn/lesson/small')}}/{{$item->avatar_name}}">
                                  </a>
                              </div>
                              {{-- <div class="info">
                                  <div class="title">
                                      <a href="{{url('khoa-hoc')}}/{{ $course->SEOurl }}/{{ $item->id }}-{{ $item->SEOurl }}">{{$item->name}}</a>
                                  </div>
                              </div> --}}
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <p class="eju-tab-title">Giới thiệu khóa học</p>
            <div id="intro-content" class="tab-pane fade in active">
                <div class="course-detail-container">
                    <div class="course-price-container">
                        <div class="info">Học phí: <b>
                          @if(in_array($course->id, array(8, 9, 10)))
                            <strike style="opacity:0.6;">6,000,000 ₫</strike> &nbsp;
                          @endif
                          {{ number_format($course->price) }}
                        </b> ₫
{{--                            ( {{ number_format($courseJpPrice) }} ¥ ) --}}
                        </div>
                        <div class="info">Thời gian: @if ($course->name == "N5") Miễn phí vô thời hạn @else {!! json_decode($course->stats_data)->time !!}
                        tháng kể từ ngày kích hoạt @endif</div>
                        <div class="info">Khóa học bao gồm <b> {!! json_decode($course->stats_data)->lesson !!}</b> bài học với
                            <b>{!! json_decode($course->stats_data)->video !!}</b> videos bài giảng</div>
                        <div class="info">Giảng viên: {{ $course->getAuthorName() }}</div>
                        <div class="info">Mô tả: {!! $course->brief !!}</div>
                    </div>
                </div>
            </div>

        <h4 style="width: 100%; float: left; text-align: center; margin:0 0 45px 0; font-weight: bold;">
            <span style="border-bottom: 2px solid #124896; padding-bottom: 10px;">Bình luận</span>
        </h4>

        <div class="comment-container" id="comment-container">
            <ul class="nav nav-pills comment-tab">
                <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a></li>
                <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng facebook</a></li>
            </ul>
            <div class="tab-content">
                <div id="user-comment-content" class="tab-pane fade in active">

                    @if(Auth::check())
                    <comments-eju meid="{{ Auth::user()->id }}"
                            avatar="{{ Auth::user()->avatar }}"
                            tbid="{{ $course->id }}"
                            tbname="course"
                            num-posts="15"
                            background="#fff"
                            ref="comment">
                    </comments-eju>
                    @else
                    <comments-eju tbid="{{ $course->id }}"
                            tbname="course"
                            num-posts="15"
                            background="#fff"
                            ref="comment">
                    </comments-eju>
                    @endif

                </div>
                <div id="facebook-comment-content" class="tab-pane fade">
                    <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
                </div>
            </div>
        </div>
    </div>
    {{-- end of main left --}}

    {{-- khóa n1, n2, n3 tiến trình kiểu premium --}}
    @if($premium == true)
    <div class="main-right-premium">

        <div class="course-list-premium">
            @if(Auth::check())
                <course-group-premium :price="{{$course->price}}" :categories="{{$categories}}" :groups="{{$groups}}" :lprogress="{{$lessonProgress}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :auth="1" :ul="{{$unlock}}"></course-group-premium>
            @else
                <course-group-premium :categories="{{$categories}}" :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :ul="{{$unlock}}"></course-group-premium>
            @endif
        </div>

    </div>

    {{-- nếu là các khóa thường --}}
    @else
    <div class="main-right m-r-eju">

        <div class="course-list-container" id="course-list-pc">
          <div class="block-title" id="lesson-list-detail"> Tiến trình học </div>

            @if(Auth::check())
                <course-group :ejulimit="{{$ejuLimit}}" :price="{{$course->price}}" :lessonprogress="{{$lessonProgress}}" :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :auth="1" :ul="{{$unlock}}" :type="'pc'"></course-group>
            @else
                <course-group :ejulimit="{{$ejuLimit}}" :groups="{{$groups}}" :lessons="{{$lessons}}" :courseurl="'{{$course->SEOurl}}'" :ul="{{$unlock}}" :type="'pc'"></course-group>
            @endif

        </div>
    </div>
    {{-- end of main right --}}
    @endif

</div>
</div>


{{-- @section('fixed-panel')

<a href="{{url('/bang-gia')}}">
    <div class="muahang-btn">
        <img src="{{asset('assets/img/fixed-muahang.png')}}" alt="img"/>
        <p>MUA HÀNG</p>
    </div>
</a>

@stop --}}


@section('footer-js')
    <script type="text/javascript">$(".eju").addClass("active")</script>
    <script type="text/javascript">

        {{-- sự kiện click vào chạy video youtube --}}
        $('.movie-play').on('click', function(ev) {
            $(".movie-play").css("display", "none");
            $("#iframe-youtube").css("display", "block");
            $("#iframe-youtube")[0].src += "&autoplay=1";
            ev.preventDefault();
        });
    </script>
    <script type="text/javascript"> $(".fancybox").fancybox().trigger('click'); </script>

    <script type="text/javascript"> new Vue({ el: '#comment-container' }); </script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>

    @if($premium == true)
        <script type="text/javascript"> new Vue({ el: '.course-list-premium' }); </script>
        <link href="{{asset('plugin/loading-bar/loading-bar.min.css')}}" rel="stylesheet" type="text/css">
        <script src="{{asset('plugin/loading-bar/loading-bar.min.js')}}"></script>
    @else
        <script src="{{asset('plugin/progressbar/dist/progressbar.js')}}"></script>
        <script type="text/javascript"> new Vue({ el: '#course-list-pc' }); </script>
    @endif


<!-- Load Facebook SDK for JavaScript -->
      <div id="fb-root"></div>
      <script>
        window.fbAsyncInit = function() {
          FB.init({
            xfbml            : true,
            version          : 'v9.0'
          });
        };

        (function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = 'https://connect.facebook.net/vi_VN/sdk/xfbml.customerchat.js';
        fjs.parentNode.insertBefore(js, fjs);
      }(document, 'script', 'facebook-jssdk'));</script>

      <!-- Your Chat Plugin code -->
      <div class="fb-customerchat"
        attribution=setup_tool
        page_id="102715574487110"
  theme_color="#1b62c4"
  logged_in_greeting="Nhận tư vấn và nhận lộ trình miễn phí từ thầy Nghĩa Yoshi nhé !"
  logged_out_greeting="Nhận tư vấn và nhận lộ trình miễn phí từ thầy Nghĩa Yoshi nhé !">
      </div>

<script>(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) return;
  js = d.createElement(s); js.id = id;
  js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1768213996826394';
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));</script>



@stop
@stop
