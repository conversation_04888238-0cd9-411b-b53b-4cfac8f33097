@extends('frontend._layouts.default')

@section('title')
  <PERSON><PERSON><PERSON><PERSON> học {{ $course->name }}  - Du<PERSON><PERSON>i
@stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('cdn/course/default')}}/{{ $course->avatar_name }} @stop
@section('author') DUNGMORI @stop

@section('header-css')
  <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
  <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
  {{-- nếu là 1 trong 3 khóa zoom --}}
  @if(in_array($course->id, array(27, 28, 29)))

    <style type="text/css">
      .server-localtion {
        background-color: #EDBA70 !important;
      }

      .main .main-course .main-left .course-detail-container .course-price-container {
        background: #fff1e0 !important;
      }

      .course-tab {
        background: #EEE !important;
      }

      .course-tab > .active > a {
        background-color: #D68E24 !important;
      }

      .course-tab > .li-tab > a {
        color: #444 !important;
      }

      .course-list-container > .block-title {
        background: #D68E24;
      }

      .course-list-container .panel-default > .panel-heading a {
        background: #FFE8CD !important;
        color: #D68E24;
      }

      .course-list-container .panel-default > .panel-heading > .group-step-item {
        background: #FFE8CD !important;
      }

      .course-list-container .panel-default .panel-body li a {
        background: #f7f2eb !important;
      }

      .course-list-container .panel-default .panel-body li a:hover {
        background: #f7f2eb !important;
        color: #222;
      }

      .lesson__progress--circle .progressbar-text {
        color: #222 !important;
      }

      .comment-tab {
        border-bottom: 1px solid #D68E24 !important;
      }

      .comment-tab .active > a {
        background-color: #D68E24 !important;
      }

      .list-comments .comment-item .comment-content .name b {
        color: #D68E24 !important;
      }
      .list-comments .comment-item .comment-content .name b {
        color: #D68E24 !important;
      }

      .comment-action > a {
        color: #D68E24 !important;
      }

      .child-comment-item .comment-content .child-name b {
        color: #D68E24 !important;
      }

      .list-comments .input-comment-container .post-comment-btn {
        background: #D68E24 !important;
      }

      .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
        background: #D68E24 !important;
      }

      .list-comments .load-more-comment {
        background: #D68E24 !important;
      }

      .main-right {
        margin-top: 20px !important;
      }

      .main .main-course .main-right .course-info-container {
        margin-top: 35px;
      }
    </style>

  @endif
  {{-- nếu là 1 trong 3 khóa eju --}}
  @if(in_array($course->id, array(8, 9, 10)))
    <style type="text/css">
      .server-localtion {
        background-color: #105B89 !important;
      }

      .course-tab {
        background: #77C2EF !important;
      }

      .course-tab > .active > a {
        background-color: #105B89 !important;
      }

      .course-tab > .li-tab > a {
        color: #fff !important;
      }

      .course-list-container > .block-title {
        background: linear-gradient(180deg, #167EBD 0%, #1F94CF 48.44%, #167EBD 100%);
        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
      }

      .course-list-container .panel-default > .panel-heading a {
        background: #105B89 !important;
      }

      .course-list-container .panel-default > .panel-heading > .group-step-item {
        background: rgb(238, 238, 238) !important;
      }

      .course-list-container .panel-default .panel-body li a {
        background: #77C2EF !important;
      }

      .comment-tab {
        border-bottom: 1px solid #105B89 !important;
      }

      .comment-tab .active > a {
        background-color: #105B89 !important;
      }

      .list-comments .comment-item .comment-content .name b {
        color: #105B89 !important;
      }

      .comment-action > a {
        color: #105B89 !important;
      }

      .child-comment-item .comment-content .child-name b {
        color: #105B89 !important;
      }

      .list-comments .input-comment-container .post-comment-btn {
        background: #105B89 !important;
      }

      .list-comments .comment-item .comment-content .reply-container .reply-form .post-comment-btn {
        background: #105B89 !important;
      }

      .list-comments .load-more-comment {
        background: #105B89 !important;
      }

      .main-right {
        margin-top: 117px !important;
      }

      .main .main-course .main-right .course-info-container {
        margin-top: 35px;
      }
    </style>
  @endif
@stop

@section('header-js')
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
  <script type="application/ld+json">
       {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "DUNGMORI",
      "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
      "url": "https://dungmori.com"
    }
    </script>
@stop
@section('content')
  <div id="fb-root"></div>
  @include('frontend.course.course-cta', ['course' => $course, 'unlock' => $unlock, 'dark' => true, 'firstLesson' => $firstLesson])
  <div class="course-ldp">
    <div class="course-ldp__header">
      <div class="course-ldp__header-inner">
        <img src="{{ asset('assets/img/new_course/dung-thanh-mori.png') }}" alt="Thầy Dũng Mori và cô Thanh" class="course-ldp__header-image">
        <div class="course-ldp__header-board">
          <div class="board-title">Khóa học JLPT</div>
          <div class="board-name"><span class="small">ONLINE</span> {{ $course->name }}</div>
          <div class="separate">
            <svg width="458" height="2" viewBox="0 0 458 2" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0 1H458" stroke="black" stroke-width="2" stroke-dasharray="14 14"/>
            </svg>
          </div>
          <div class="board-expired"><b>Thời gian học</b> {!! json_decode($course->stats_data)->time !!} tháng</div>
          <div class="board-price">
{{--            <div class="yen">{{ number_format($courseJpPrice) }}¥</div>--}}
{{--            /--}}
            <div class="vnd">{{ number_format($course->price) }}</div>
          </div>
          <div class="cta">
            <a class="cta-button" href="https://m.me/dungmori" target="_blank" style="background-color: #2D6DB5;">Nhận tư vấn</a>
            <a class="cta-button" href="{{url('/payment')}}?buy={{ base64_encode("course") }}&item={{ base64_encode($course->id) }}" target="_blank" style="background-color: #E9493D;">
              <svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.9375 35.875C19.3527 35.875 20.5 34.7277 20.5 33.3125C20.5 31.8973 19.3527 30.75 17.9375 30.75C16.5223 30.75 15.375 31.8973 15.375 33.3125C15.375 34.7277 16.5223 35.875 17.9375 35.875Z" fill="white"/>
                <path d="M29.8958 35.875C31.3111 35.875 32.4583 34.7277 32.4583 33.3125C32.4583 31.8973 31.3111 30.75 29.8958 30.75C28.4806 30.75 27.3333 31.8973 27.3333 33.3125C27.3333 34.7277 28.4806 35.875 29.8958 35.875Z" fill="white"/>
                <path d="M22.2083 22.2083H25.625V17.1004H30.7329V13.6837H25.625V8.5929H22.2083V13.6837H17.1004V17.1004H22.2083V22.2083Z" fill="white"/>
                <path d="M17.0833 29.0417H30.75C31.0941 29.0407 31.4299 28.9357 31.7134 28.7406C31.9969 28.5456 32.2149 28.2694 32.3388 27.9483L37.1733 15.375H33.5175L29.5713 25.625H18.2279L10.5575 7.22626C10.2974 6.60282 9.8584 6.07047 9.29588 5.69646C8.73336 5.32246 8.07259 5.1236 7.39709 5.12501H3.41667V8.54167H7.39709L15.5117 27.9825C15.6398 28.2945 15.8575 28.5615 16.1372 28.75C16.4168 28.9385 16.7461 29.04 17.0833 29.0417Z" fill="white"/>
              </svg>
              Mua ngay
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="course-ldp__introduce">
      <div class="course-ldp__introduce-inner">
        <style>
          .movie-play,
          #iframe-youtube {
            width: 27.3vw;
            height: 13.9vw;
            position: absolute;
            top: 8.3vw;
            left: 35.6vw;
            border: 1px solid black;
            border-radius: 20px;
          }
          .movie-play {
            position: relative;
          }
          .movie-play:hover {
            cursor: pointer;
          }
          .movie-play .play-icon-btn {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .movie-play img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        </style>
        <div class="movie-play">
          <img src="{{url('cdn/course/default')}}/{{ $course->avatar_name }}" alt="Giới thiệu khoá học"/>
          <br>
          @include('frontend.course.play-button')
        </div>
        <?php preg_match('%(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})%i', $course->link, $match); ?>
        <iframe id="iframe-youtube" style="display: none;" width="100%" height="395" frameborder="0" gesture="media"
                allow="encrypted-media" allowfullscreen
                src="https://www.youtube.com/embed/{{ $match[1] }}?rel=0">
        </iframe>
        <div class="course-ldp__introduce-name">
          {{ $course->name }} ONLINE
        </div>
      </div>
    </div>
    <div class="course-ldp__adventure">
      @if ($course->name == 'N5')
        @include('frontend.home.components.section_title',['title' => '勉強ルート', 'description' => 'Lộ trình N5 cơ bản'])
      @elseif ($course->name == 'N4')
        @include('frontend.home.components.section_title',['title' => '勉強ルート', 'description' => 'Lộ trình N4 JLPT'])
      @else
        @include('frontend.home.components.section_title',['title' => '勉強ルート', 'description' => 'Lộ trình 4 chặng bài bản'])
      @endif
      <div class="course-ldp__adventure-inner">
        <img src="{{asset('assets/img/new_course/message-bubble.svg')}}" alt="" style="position: absolute; top: -60px; right: -130px">
        <img src="{{asset('assets/img/new_course/xx.svg')}}" alt="" style="position: absolute; top: calc(50% + 100px); left: -220px">
        <img src="{{asset('assets/img/new_home/12-2021/running-mori.svg')}}" alt="" style="transform: scaleX(-1); position: absolute; top: calc(50% - 100px); left: -60px">
        <img src="{{asset('assets/img/new_course/books.svg')}}" alt="" style="position: absolute; bottom: -100px; right: -180px">
        @if ($course->name == 'N5')
          <div class="course-ldp__adventure-content bg-green-25">
            <div>
              <div class="title" style="color: #41A336">Nội dung</div>
              <ul class="description">
                <li>Bảng chữ cái</li>
                <li>Buổi học kiến thức theo giáo trình minano nihongo <br />(bao gồm Từ vựng + ngữ pháp + chữ Hán + đọc + nghe + hội thoại+ kiểm tra).</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #41A336">Giáo trình</div>
              <ul class="description">
                <li>Giáo trình trung tâm biên soạn dựa trên nền tảng giáo trình Minnanihongo</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #41A336">Điểm nổi bật</div>
              <ul class="description">
                <li>Học từ kiến thức căn bản.</li>
                <li>Trang bị kỹ năng học từ mới.</li>
                <li>Hướng dẫn chuẩn bị bài, học và ôn bài hiệu quả.</li>
              </ul>
            </div>
          </div>
        @elseif ($course->name == 'N4')
          <div class="course-ldp__adventure-content bg-green-25">
            <div>
              <div class="title" style="color: #41A336">Nội dung</div>
              <ul class="description">
                <li>Ôn tập kiến thức N5</li>
                <li>Buổi học kiến thức theo giáo trình minano nihongo <br />(bao gồm Từ vựng + ngữ pháp + chữ Hán + đọc + nghe + hội thoại+ kiểm tra).</li>
                <li>Luyện đề N4</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #41A336">Giáo trình</div>
              <ul class="description">
                <li>Giáo trình trung tâm biên soạn dựa trên nền tảng giáo trình Minnanihongo</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #41A336">Điểm nổi bật</div>
              <ul class="description">
                <li>Trang bị kỹ năng học từ mới.</li>
                <li>Hướng dẫn chuẩn bị bài, học và ôn bài hiệu quả.</li>
              </ul>
            </div>
          </div>
        @else
          <div class="home-ribbon" style="transform: rotate(3deg);margin: auto">
            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg" style="position: absolute; left: -9px;">
              <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
              <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#F27712" stroke="black"/>
            </svg>
            <div class="bookmark-ribbon bookmark-ribbon--green" style="position: static;">
              Chặng 1: Chữa mất gốc căn bản
            </div>
          </div>
          <div class="course-ldp__adventure-content bg-green-25">
            <div>
              <div class="title" style="color: #41A336">Nội dung</div>
              <ul class="description">
                <li>Tổng ôn Ngữ pháp N{{ (int) $course->name[1] + 1 }}</li>
                <li>Trang bị kỹ năng dịch câu</li>
                <li>Hướng dẫn phương pháp học hiệu quả.</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #41A336">Giáo trình</div>
              <ul class="description">
                <li>Chữa mất gốc căn bản Dũng Mori</li>
                <li>3 video + test + giáo trình</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #41A336">Điểm nổi bật</div>
              <ul class="description">
                <li>Củng cố lại kiến thức căn bản của N{{ (int) $course->name[1] + 1 }}</li>
                <li>Trang bị kỹ năng dịch câu cơ bản</li>
                <li>Hướng dẫn chuẩn bị bài, học và ôn bài hiệu quả.</li>
              </ul>
            </div>
          </div>
          <div class="home-ribbon" style="transform: rotate(3deg);margin: auto">
            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg" style="position: absolute; left: -9px;">
              <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
              <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#F27712" stroke="black"/>
            </svg>
            <div class="bookmark-ribbon bookmark-ribbon--yellow" style="position: static;">
              Chặng 2: Kiến thức nền tảng {{ $course->name }}
            </div>
          </div>
          <div class="course-ldp__adventure-content bg-orange-25">
            <div>
              <div class="title" style="color: #FB6D3A">Nội dung</div>
              <ul class="description">
                <li>Chữ Hán</li>
                <li>Từ Vựng</li>
                <li>Ngữ pháp</li>
                <li>Kỹ năng đọc + dịch câu ngắn</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #FB6D3A">Giáo trình</div>
              <ul class="description">
                <li>Bộ giáo trình Chữ hán, Từ vựng, Ngữ pháp độc quyền Dũng Mori</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #FB6D3A">Điểm nổi bật</div>
              <ul class="description">
                <li>Cách học Chữ Hán “Học 1 nhớ 3” dễ nhớ</li>
                <li>Test từ vựng liên tục theo lộ trình học</li>
                <li>Luyện kỹ năng đặt câu, đọc, dịch câu ngắn theo ngữ pháp</li>
              </ul>
            </div>
          </div>
          <div class="home-ribbon" style="transform: rotate(3deg);margin: auto">
            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg" style="position: absolute; left: -9px;top: 8px;">
              <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
              <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#F27712" stroke="black"/>
            </svg>
            <div class="bookmark-ribbon bookmark-ribbon--blue" style="position: static;">
              Chặng 3: Đọc hiểu - nghe hiểu - củng cố kiến thức {{ $course->name }}
            </div>
          </div>
          <div class="course-ldp__adventure-content bg-blue-25">
            <div>
              <div class="title" style="color: #0E65E5">Nội dung</div>
              <ul class="description">
                <li>Chuyên sâu Đọc Hiểu</li>
                <li>Chuyên sâu Nghe Hiểu</li>
                <li>Củng cố kiến thức Chữ hán -Từ vựng - Ngữ pháp</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #0E65E5">Giáo trình</div>
              <ul class="description">
                <li>Đọc hiểu + Nghe hiểu Shinkanzen</li>
                <li>Sách luyện đề Pawa Doriru + 20 ngày </li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #0E65E5">Điểm nổi bật</div>
              <ul class="description">
                <li>Trang bị phần lý thuyết kỹ càng trước khi luyện đề</li>
                <li>Luyện kỹ năng làm từng dạng đề chữ hán, từ vựng, ngữ pháp, đọc, nghe</li>
                <li>Luyện đề trực tuyến trên web/app Dũng mori, xem ngay kết quả, xem lại lịch sử thi và bảng xếp hạng thành tích</li>
                <li>Củng cố lại kiến thức ở chặng 2</li>
              </ul>
            </div>
          </div>
          <div class="home-ribbon" style="transform: rotate(3deg);margin: auto">
            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg" style="position: absolute; left: -9px;top: 8px;">
              <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
              <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#F27712" stroke="black"/>
            </svg>
            <div class="bookmark-ribbon bookmark-ribbon--violet" style="position: static;">
              Chặng 4: Luyện kĩ năng làm đề - Tổng ôn tập
            </div>
          </div>
          <div class="course-ldp__adventure-content bg-violet-25">
            <div>
              <div class="title" style="color: #9582E1">Nội dung</div>
              <ul class="description">
                <li>Kỹ năng làm đề tổng hợp</li>
                <li>Luyện đề thi JLPT quá khứ </li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #9582E1">Giáo trình</div>
              <ul class="description">
                <li>Bộ đề thi thật các năm bản đẹp</li>
              </ul>
            </div>
            <div>
              <div class="title" style="color: #9582E1">Điểm nổi bật</div>
              <ul class="description">
                <li>Luyện kỹ năng làm đề, phân bổ thời gian hợp lý cho từng phần</li>
                <li>Làm quen với đề thi thật</li>
                <li>Thi đua thành tích trên bảng xếp hạng</li>
                <li>Hướng dẫn mẹo làm bài nhanh cùng chữa đề chi tiết trên livestream trong nhóm kín</li>
                <li>Làm đề, thi thử liên tục</li>
                <li>Giải đáp thắc mắc 24/7</li>
              </ul>
            </div>
          </div>
        @endif

      </div>
    </div>
    <div class="course-ldp__feature">
      @include('frontend.home.components.section_title',['title' => '顕著な特徴', 'description' => 'Tính năng nổi bật'])
      <img src="{{ asset('assets/img/new_course/mori-teach.svg') }}" alt="" class="course-ldp__feature-icon">
      <div class="three-item">
        <div class="course-ldp__feature-item">
          <video autoplay loop muted playsinline>
            <source src="{{ asset('assets/img/new_course/feature-1.mp4') }}" type="video/mp4">
          </video>
        </div>
        <div class="course-ldp__feature-item">
          <video autoplay loop muted playsinline>
            <source src="{{ asset('assets/img/new_course/feature-2.mp4') }}" type="video/mp4">
          </video>
        </div>
        <div class="course-ldp__feature-item">
          <video autoplay loop muted playsinline>
            <source src="{{ asset('assets/img/new_course/feature-3.mp4') }}" type="video/mp4">
          </video>
        </div>
      </div>
      <div class="two-item">
        <div class="flex items-center">
          <div class="course-ldp__feature-item">
            <video autoplay loop muted playsinline>
              <source src="{{ asset('assets/img/new_course/feature-4.mp4') }}" type="video/mp4">
            </video>
          </div>
          <style>
            .fold-paper li {
              list-style: disc;
              line-height: 1.30;
            }
          </style>
          <div class="fold-paper">
            <div class="fold-paper__title">Khám phá Nhật Bản qua 3 chặng</div>
            <div class="fold-paper__desc">
              <ul>
                <li>Chặng 1: Học chữ Hán, Từ vựng, Ngữ pháp</li>
                <li>Chặng 2: Luyện kĩ năng Đọc hiểu, Nghe hiểu</li>
                <li>Chặng 3: Chữa đề - Luyện đề JLPT</li>
              </ul>
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <div class="course-ldp__feature-item">
            <video autoplay loop muted playsinline>
              <source src="{{ asset('assets/img/new_course/feature-5.mp4') }}" type="video/mp4">
            </video>
          </div>
          @include('frontend.home.components.fold_paper',['title' => 'Cá nhân hóa lộ trình học với bản thân', 'description' => 'Lượng bài học được chia cụ thể theo ngày Flashcard từ vựng kèm cách nhớ và audio phát âm'])
        </div>
      </div>
    </div>
    <div class="course-ldp__trial">
      @include('frontend.home.components.section_title',['title' => '体験動画', 'description' => 'BÀI GIẢNG XEM THỬ'])
      <div class="course-ldp__trial-inner">
        @foreach($previewLessons as $lesson)
          <div class="course-ldp__trial-item">
            <a href="{{url('khoa-hoc')}}/{{ $course->SEOurl }}/{{ $lesson->id }}-{{ $lesson->SEOurl }}" target="_blank" style="background-image: url('{{url('cdn/lesson/default')}}/{{$lesson->avatar_name}}')">
              <img src="{{ asset('assets/img/new_course/play-btn.svg') }}" alt="" class="play-btn">
            </a>
          </div>
        @endforeach
      </div>
    </div>
    <div class="course-ldp__benefit">
      <?php $benefitTitle = "Khóa $course->name đem đến cho bạn"?>
      <div class="course-ldp__benefit-inner">
        @include('frontend.home.components.section_title',['title' => '取り柄', 'description' => $benefitTitle])
        <div class="three-item">
          <div class="course-ldp__benefit-item yellow">
            <img src="{{ asset('assets/img/new_course/ic_report.svg') }}" alt="">
            <div>
              <p><b>300,000 + sinh viên, người đi làm</b> đã trải nghiệm và thành công với khóa học <b>Online</b></p>
            </div>
          </div>
          <div class="course-ldp__benefit-item green">
            <img src="{{ asset('assets/img/new_course/ic_target.svg') }}" alt="">
            <div>
              <p><b>Cung cấp đầy đủ kiến thức và kĩ năng làm đề thi JLPT {{ $course->name }} chỉ trong duy nhất 1 khóa học</b></p>
            </div>
          </div>
          <div class="course-ldp__benefit-item blue">
            <img src="{{ asset('assets/img/new_course/ic_gift.svg') }}" alt="">
            <div>
              <p><b>Lộ trình học được cá nhân hóa đầu tiên tại Việt Nam, hỗ trợ đắc lực cho người tự học</b></p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="course-ldp__extension" id="extend_info">
      <div class="home-title">
        <div class="home-title__content">
          <div class="home-title__name">更新ポリシー</div>
          <div class="home-title__desc" style="text-align: center">cần thêm thời gian học? Gia hạn ngay</div>
        </div>
      </div>
      <div class="course_extension">
        <div class="extension_policy_data">
         <table>

           <thead>
             <tr>
               <th width="30%">chính sách</th>
               <th width="25%" style="text-align: center">thời hạn</th>
               <th width="45%" style="text-align: center">chi phí</th>
             </tr>
           </thead>

           <tbody>
             <tr v-for="exInfo in extend" v-cloak>
               <td>
                 <div style="display: inline" v-if="exInfo.is_new == 0">
                   <svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 16 15" fill="none">
                     <path d="M7.11555 0.676337C7.49076 -0.0348099 8.50924 -0.0348078 8.88445 0.676339L10.5214 3.77891C10.6661 4.05316 10.9298 4.24477 11.2353 4.29763L14.6919 4.89571C15.4842 5.03279 15.7989 6.00143 15.2385 6.57802L12.7936 9.09359C12.5775 9.31595 12.4768 9.62598 12.5209 9.9329L13.0203 13.4051C13.1347 14.201 12.3108 14.7996 11.5892 14.4448L8.44125 12.897C8.16299 12.7601 7.83701 12.7601 7.55875 12.897L4.4108 14.4448C3.68925 14.7996 2.86527 14.201 2.97973 13.4051L3.47906 9.9329C3.5232 9.62598 3.42247 9.31595 3.20636 9.09359L0.761477 6.57803C0.201081 6.00143 0.515809 5.03279 1.30809 4.89571L4.76466 4.29763C5.07019 4.24477 5.33392 4.05316 5.47861 3.77891L7.11555 0.676337Z"
                           fill="#FF6531"/>
                   </svg>
                   Học viên cũ
                 </div>
                 <div v-else>Học viên mới</div>
               </td>
               <td>@{{exInfo.duration}} Tháng</td>
               <td><span>@{{exInfo.price_vn | decimal}} VNĐ
{{--                   / @{{exInfo.price_jp | decimal}} ¥--}}
                 </span></td>
             </tr>
           </tbody>
         </table>
        </div>
        <div class="extension_more">
          <h4 style="margin-bottom: 40px">Chính sách gia hạn:</h4>

          <ul>
            <li>• Thông báo gia hạn khi khóa học còn thời hạn.</li>
            <li>• Thời hạn thông báo và thanh toán học phí gia hạn muộn nhất là ngày cuối cùng kết thúc khóa học.</li>
            <li>• Những khoá học đã hết hạn không thể gia hạn được.</li>
          </ul>

          <div class="conntact-now">
            <a href="#" onclick="showChatbox()">Liên hệ ngay</a>
          </div>

          <div class="pencil-image">
            <img src="{{asset('/assets/img/extend_info.png')}}" alt="extend_info">
          </div>
        </div>
      </div>
    </div>

    <div class="course-ldp__teacher">
      <h1>Giảng viên nhiều năm kinh nghiệm<br/>
        và được kiểm chứng bởi <span class="green">300,000+ học viên</span></h1>
      <div class="course-ldp__teacher-inner">
        <div class="course-ldp__teacher-item">
          <img src="{{ asset('assets/img/new_course/giao-vien-1.png') }}" alt="">
          <ul>
            <li>• Cử nhân Luật Đại học Aomori Chuo.</li>
            <li>• Học bổng toàn phần của Tập đoàn Tokyo Marine cho hệ thạc sĩ KHTT Đại học Quốc gia Nagoya.</li>
            <li>• Phó giám đốc CTCP Dũng Mori.</li>
          </ul>
        </div>
        <div class="course-ldp__teacher-item">
          <img src="{{ asset('assets/img/new_course/giao-vien-2.png') }}" alt="">
          <ul>
            <li>• Cử nhân khoa Tiếng Nhật, Đại học Hà Nội.</li>
            <li>• Thạc sĩ chuyên ngành Giáo dục tiếng Nhật,
              Đại học Ngoại ngữ Tokyo (MEXT).</li>
            <li>• Phó giám đốc CTCP Dũng Mori.
            </li>
          </ul>
        </div>
        <div class="course-ldp__teacher-item">
          <img src="{{ asset('assets/img/new_course/giao-vien-3.png') }}" alt="">
          <ul>
            <li>• Cử nhân khoa Giáo dục, Đại học Nhật Bản <br/>
              (日本大学).</li>
            <li>• Giám đốc CTCP Dũng Mori.</li>
          </ul>
        </div>
        <div class="course-ldp__teacher-item">
          <img src="{{ asset('assets/img/new_course/giao-vien-4.png') }}" alt="">
          <ul>
            <li>• Thành thạo tiếng Nhật và tiếng Trung.</li>
            <li>• Thực tập sinh kỹ năng tại Đài Loan và Nhật Bản.</li>
            <li>• Quản lí chất lượng giáo viên tại Dũng Mori online.</li>
          </ul>
        </div>
      </div>
    </div>
    <div class="course-ldp__promo">
      <div class="title">Ưu đãi 20% khi đăng ký combo</div>
      <div class="course-ldp__promo-inner">
        @foreach ($validCombos as $combo)
          <div class="course-ldp__promo-item">
            <img src="{{ asset('assets/img/new_course/' . $combo->id . '.png') }}" alt="">
            <div class="combo-info">
              <div class="board-price text-center">
{{--                <div class="yen">{{ number_format($combo->jpy_price) }}¥</div>--}}
{{--                /--}}
                <div class="vnd">{{ number_format($combo->price) }}</div>
              </div>
              <div class="sold">Đã bán: {{ $combo->invoices_count < 100 ? rand(1001, 2002) : $combo->invoices_count }}</div>
            </div>
          </div>
        @endforeach
      </div>
      <div class="course-ldp__promo-cta">
        <a class="cta-button" href="https://m.me/dungmori" target="_blank" style="background-color: #FFF;color: #000;border: 2px solid #000;">Nhận tư vấn</a>
        <a class="cta-button" href="{{ route('bang_gia') }}" target="_blank" style="background-color: #96D962;">
          Cửa hàng
        </a>
      </div>
    </div>
    <div class="course-ldp__comment">
      <div class="comment-container" id="comment-container">
        <ul class="nav nav-pills comment-tab">
          <li class="li-tab user-tab active"><a data-toggle="pill" href="#user-comment-content">Ý kiến học viên</a>
          </li>
          <li class="li-tab facebook-tab"><a data-toggle="pill" href="#facebook-comment-content">Bình luận bằng
              facebook</a></li>
        </ul>
        <div class="tab-content">
          <div id="user-comment-content" class="tab-pane fade in active">

            @if(Auth::check())
              <comments meid="{{ Auth::user()->id }}"
                        avatar="{{ Auth::user()->avatar }}"
                        tbid="{{ $course->id }}"
                        tbname="course"
                        num-posts="15"
                        background="#fff"
                        ref="comment">
              </comments>
            @else
              <comments tbid="{{ $course->id }}"
                        tbname="course"
                        num-posts="15"
                        background="#fff"
                        ref="comment">
              </comments>
            @endif

          </div>
          <div id="facebook-comment-content" class="tab-pane fade">
            <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
          </div>
        </div>
      </div>
    </div>
  </div>
@stop
@section('fixed-panel')
  <a href="{{url('/bang-gia')}}">

  </a>
@stop
@section('footer-js')
  <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
  <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
  <script type="text/javascript"> new Vue({el: '#comment-container'}); </script>
  <script type="text/javascript">
    {{--Student feedback slick--}}
    $('#student-feedback-slider').slick({
      slidesToShow: 5,
      slidesToScroll: 3,
      loop: true,
      autoplay: true,
      autoplaySpeed: 2000,
      centerMode: false,
      responsive: [
        {
          breakpoint: 1366,
          settings: {
            slidesToShow: 2,
            slidesToScroll: 2,
            autoplay: true,
            centerMode: true,
            centerPadding: '15%',
          }
        },
        {
          breakpoint: 1024,
          settings: {
            slidesToShow: 2,
            slidesToScroll: 2,
            autoplay: true,
            centerMode: true,
            centerPadding: '70px',
          }
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            centerMode: true,
            centerPadding: '120px',
          }
        },
        {
          breakpoint: 600,
          settings: {
            slidesToShow: 1,
            centerMode: true,
            centerPadding: '13%',
          }
        },
      ]
    });
    $('#home-header-slider').slick({
      slidesToShow: 2,
      slidesToScroll: 1,
      autoplay: true,
      autoplaySpeed: 2000,
      arrows: false,
      loop: true,
      centerMode: false,
      variableWidth: true,
      responsive: [
        {
          breakpoint: 1024,
          settings: {
            slidesToShow: 3,
            slidesToScroll: 2,
            autoplay: true,
            centerMode: true,
            centerPadding: '70px',
          }
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 2,
            slidesToScroll: 1,
            centerMode: true,
            centerPadding: '120px',
          }
        },
        {
          breakpoint: 600,
          settings: {
            slidesToShow: 1,
            centerMode: true,
            centerPadding: '13%',
          }
        },
      ]
    });
    $("#nxt_sld").on("click", function(e) {
      e.preventDefault();
      $("#home-header-slider").slick("slickNext")
    });
    $("#prv_sld").on("click", function(e) {
      e.preventDefault();
      $("#home-header-slider").slick("slickPrev")
    });
  </script>
  <script type="text/javascript">
    {{-- sự kiện click vào chạy video youtube --}}
    $('.movie-play').on('click', function (ev) {
      $(".movie-play").css("display", "none");
      $("#iframe-youtube").css("display", "block");
      $("#iframe-youtube")[0].src += "&autoplay=1";
      ev.preventDefault();
    });

  </script>

  <script src="{{asset('/plugin/jquery/axios.min.js')}}"></script>
  <script>

    var courseId = {!!json_encode($course->id)!!};

    Vue.filter('decimal', (number) => {
      return new Intl.NumberFormat('vi-VN').format(number);
    });

    var extend = new Vue({
      el: '#extend_info',
      data() {
        return {
          extend: {},
        }
      },
      methods:{
        initData: function () {

          var extendObj = this;

          var url = window.location.origin + '/course/extend-info' + '?course_id=' + courseId;

          axios.get(url).then(function (res){
            extendObj.extend = res.data;
          })
        }
      },
      mounted: function () {
        this.initData();
      }
    });
  </script>
@stop
