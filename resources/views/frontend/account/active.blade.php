@extends('frontend._layouts.default')

@section('title') Dungmori - Tài <PERSON>n cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

@if(Auth::check())
<input type="hidden" id="csrf_token" value="{{ csrf_token() }}">
@endif

<div class="main">
   <div class="main-center">
    <div class="main-user-left">
      {{-- import left menu --}}
      @include('frontend.account._left')
    </div>

    <div class="main-user-right" id="fill_active_code">
        <h2 class="main-user-title">Nhập mã kích hoạt</h2>
        <table class="table">
            <ul class="error-list" id="error-list" style="display: none;">
                <li v-for="error in errors">@{{ error }}</li>
            </ul>
          <tbody>
            <tr>
                <td class="user-form-item password-label">Nhập mã kích hoạt</td>
                <td class="user-form-item"><input type="text" class="form-control" id="pin" placeholder="Mã kích hoạt" autocomplete="off"></td>
             </tr>
             <tr>
                <td class="user-form-item password-label">Mã chống spam</td>
                <td class="user-form-item lock">
                    <input type="text" class="form-control captcha-input" style="width: 200px;" id="captcha" placeholder="Nhập mã bên cạnh" autocomplete="off">
                    <img class="captcha-container captcha-active-course" id="captcha-change" src="{{ route('get-captcha-img') }}"></img>
                    <i class="glyphicon glyphicon-repeat change-captcha-icon cursor-pointer" v-on:click="changeCaptcha()"></i>
                </td>
             </tr>
             <tr>
              <td class="user-form-item password-label"></td>
                <td class="user-form-item lock"><button type="button" class="btn btn-success" v-on:click="activeCourse()">Kích hoạt</button></td>
             </tr>
          </tbody>
       </table>

        <div id="successModal" class="modal fade" role="dialog" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Thành công</h4>
                    </div>
                    <div class="modal-body">
                        <p>Bạn đã kích hoạt khóa học thành công. Bây giờ bạn đã có thể tham gia khóa học online này của Dungmori.com. Chúc bạn học tốt!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-info" data-dismiss="modal" v-on:click="gotoCourseUrl()">Đóng</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
   </div>
</div>
<script>
  $(".item-user").removeClass("current");
  $(".active").addClass("current");
</script>
@stop

@section('footer-js')
  @if(Auth::check())
    <script src="{{asset('assets/js/fill_active_code.js')}}?{{filemtime('assets/js/fill_active_code.js')}}"></script>
  @endif
@stop
