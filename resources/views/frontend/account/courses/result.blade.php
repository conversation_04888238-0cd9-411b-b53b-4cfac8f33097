@extends('frontend._layouts.default')

@section('title') Dungmori - Tài <PERSON>n cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')


<div class="main">
   <div class="main-center">
    <div class="main-user-left">

      {{-- import left menu --}}
      @include('frontend.account._left')

    </div>

    <div class="main-user-right">

       <div class="dm-tab">
    <a href="{{url('/account/courses')}}" ><div class="tab-item"><i class="zmdi zmdi-layers"></i> K<PERSON><PERSON><PERSON> học đã mua</div></a>
    {{-- <a href="{{url('/account/courses')}}/journey" ><div class="tab-item"><i class="zmdi zmdi-calendar-note"></i> Quá trình học tập</div></a> --}}
    <div class="tab-item active"><i class="zmdi zmdi-check-square"></i> Kết quả kiểm tra</div>

</div>
<div class="tab-content-container course-test" id="result-test-profile" style="display: none;">

  <div class="panel panel-default" v-for="lesson in listLessons" v-if="listResults[lesson.id].length != 0">
    <div class="panel-heading" role="tab">
      <h4 class="panel-title">
        <a role="button" data-toggle="collapse" data-parent="#accordion" :href="'#collapse-' + lesson.id" aria-expanded="false" :aria-controls="'#collapse-' + lesson.id" class="collapsed">
          @{{ lesson.lesson_name }} - @{{ lesson.group_name }}
        </a>
      </h4>
    </div>
    <div :id="'collapse-' + lesson.id" class="panel-collapse collapse" role="tabpanel" :aria-labelledby="'heading-' + lesson.id" aria-expanded="false">
      <div class="panel-body">
        <div class="alert" v-for="(result, index) in listResults[lesson.id]" v-bind:class="[result.grade < lesson.pass_marks ? 'bg-warning' : 'bg-success']">
          <p>Thời gian thực hiện lúc <b> @{{ convertTime(result.created_at, "time") }} </b> ngày <b> @{{ convertTime(result.created_at, "date") }} </b></p>
          <p>Tổng điểm: <b>@{{ result.grade }}</b> / <b>@{{ result.total_grade }}</b></p>
          <p>Kết quả: <b v-if="result.grade < lesson.pass_marks">Không đạt yêu cầu</b>
            <b v-if="result.grade >= lesson.pass_marks">Đã qua</b></p>
          <button class="btn btn-info review-test-result" data-toggle="modal" data-target="#reviewTestResult" v-on:click="reviewTestResult(lesson.id, index)">Xem bài làm</button>
          <button class="btn btn-warning remove-test-result" data-toggle="modal" data-target="#removeTestResult" v-on:click="removeTestResult(lesson.id, index)">Xóa</button>
        </div>
      </div>
    </div>

    <div id="removeTestResult" class="modal fade" role="dialog">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title">Xóa</h4>
        </div>
        <div class="modal-body">
          <p>Bạn có muốn xóa bài kiểm tra này không?</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-warning" v-on:click="confirmRemoveResult()">Xóa</button>
          <button type="button" class="btn btn-info" data-dismiss="modal">Đóng</button>
        </div>
      </div>

      </div>
    </div>

    <div class="modal fade" id="reviewTestResult" role="dialog" tabindex="-1">
      <div class="modal-dialog test-modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">Chi tiết bài kiểm tra</h4>
            <button type="button" class="close" data-dismiss="modal">&times;</button>
          </div>
          <div class="modal-body">
            <table class="table table-bordered table-striped table-hover tc-table">
              <tbody>
                <tr>
                  <td class="row_label">Kết quả</td>
                  <td class="row_item">
                    <b v-if="currentResult.grade < lesson.pass_marks">Không đạt yêu cầu</b>
                    <b v-if="currentResult.grade >= lesson.pass_marks">Đã qua</b></td>
                </tr>
                <tr>
                  <td class="row_label">Điểm đạt được</td>
                  <td class="row_item">@{{ currentResult.grade }} <span style="font-size: 12px">(Điểm đạt yêu cầu: @{{ lesson.pass_marks }})</span></td>
                </tr>
                <tr>
                  <td class="row_label">Tổng điểm bài kiểm tra</td>
                  <td class="row_item">@{{ currentResult.total_grade }}</td>
                </tr>
                <tr>
                  <td class="row_label">Thời gian thi</td>
                  <td class="row_item">@{{ convertTime(currentResult.created_at, "time") }} - @{{ convertTime(currentResult.created_at, "date") }}</td>
                </tr>
                <tr>
                  <td colspan="2" class="test-detail-result">
                    <div v-for="(task, index) in tasks" class="question-box">
                      <div v-if="task.type == 1" v-html="task.value">
                      </div>
                      <div v-if="task.type == 3 || task.type == 5 || task.type == 10" class="answer-box">
                        <p style="margin: 10px 0; font-size: 16px;" v-if="task.type == 3 || task.type == 10" >
                            <span v-html="task.mask"></span>
                            <i v-if="task.answer_audio" style="cursor: pointer" class="zmdi zmdi-volume-up" @click="playAudio(task.answer_audio)"></i>
                        </p>
                        <div v-if="task.type == 5" style="margin: 5px 0;">
                          <label>@{{ JSON.parse(task.value).name }}</label>
                          <audio controls="">
                            <source :src="'http://mp3-v2.dungmori.com/' + JSON.parse(task.value).link" type="audio/mpeg">
                            {{-- <source src="http://cdn.dungmori.com:8081/mp3/supido-nghehieu-N5CD2/39 Track 39.mp3?wmsAuthSign=c2VydmVyX3RpbWU9My8yMi8yMDE4IDM6MTM6MzQgQU0maGFzaF92YWx1ZT1RZDFmd1ZyWlRhblNNSzFyRHgyeTh3PT0mdmFsaWRtaW51dGVzPTE4MDAw" type="audio/mpeg"> --}}
                            Your browser does not support the audio element.
                          </audio>
                        </div>
                        <div class="col-sm-6 result-answer-area" style="margin-bottom: 10px;" v-for="(answer, index) in answers[task.id]">
                          <div class="question-answer">
                            <div class="labels" v-bind:class="[answer.grade == 0 ? '' : 'label-true', (answer.grade == 0 && answer.checked) ? 'label-false' : '']"
                              style="display: flex; align-items: center;"
                            >
                                <input type="radio" v-if="answer.checked == true" checked onclick="this.checked = true">
                                <input type="radio" v-if="answer.checked != true" onclick="this.checked = false">&nbsp;&nbsp;<span v-html="answer.value"></span><span v-if="answer.grade != 0">&nbsp(Đúng)</span></div>
                          </div>
                        </div>
                        {{-- <div style="position: relative;" v-if="task.type == 3" v-bind:class="[answers[task.id].length > 4 ? 'three-line-answer' : 'two-line-answer']"><hr style="position: absolute; width: 100%; bottom: 0; border-color: #ddd;"></div> --}}
                        <div v-if="task.type == 3" class="col-md-11" style="display: inline-block; width: 100%; height: 10px;"></div>
                        <hr v-if="task.type == 3" style="width: 100%; margin-top: 20px; margin-bottom: 30px; bottom: 0; border-color: #ddd;">
                      </div>
                      <div v-if="task.type == 6" style="padding: 0; font-size: 13px;">
                        <table class="table table-hover">
                          <thead>
                            <tr>
                              <th style="width: 1%">Stt</th>
                              <th style="width: 30%">Câu hỏi</th>
                              <th style="width: 25%">Câu trả lời</th>
                              <th style="width: 25%">Đáp án</th>
                              <th class="text-center">Điểm</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="(item, index) in JSON.parse(task.value)" v-bind:class="[(resultData[task.id] == undefined || resultData[task.id][item.id] != item.answer) ? 'danger' : 'success']">
                              <td>@{{ index + 1 }}</td>
                              <td v-html="item.question"></td>
                              <td v-if="resultData[task.id] != undefined">@{{ resultData[task.id][item.id] }}</td>
                              <td v-else></td>
                              <td>@{{ item.answer }}</td>
                              <td class="text-center">5</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="modal-footer">
            <button type="button" data-dismiss="modal" class="btn btn-primary"> Đóng </button>
          </div>
        </div>

      </div>
    </div>

  </div>

  <div class="paginate-container">{{ $listLessons->links() }} </div>

  {{-- Nếu danh sách trống --}}
  @if(sizeof($listTests) == 0 )
  <div class="notification-empty-container">
    <i class="fa fa-mortar-board"></i> Bạn chưa làm một bài kiểm tra nào
  </div>
  @endif
</div>

    </div>

   </div>
</div>

<script>
    result_ListResults = {!! json_encode($listTests) !!};
    result_ListLessons = JSON.parse({!! json_encode($listLessonsJson) !!}).data;
    result_tokenMp3    = "";

</script>

@section('footer-js')
  <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
<script src="{{asset('assets/js/result.js')}}?{{filemtime('assets/js/result.js')}}"></script>
@stop

<script>
  $(".item-user").removeClass("current");
  $(".courses").addClass("current");
</script>
@stop
