@extends('frontend._layouts.default')

@section('title') Dungmori - Tài <PERSON>n cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')


<div class="main">
   <div class="main-center">
    <div class="main-user-left">

      {{-- import left menu --}}
      @include('frontend.account._left')

    </div>

    <div class="main-user-right">

       <div class="dm-tab">
    <a href="{{url('/account/courses')}}" ><div class="tab-item"><i class="zmdi zmdi-layers"></i> K<PERSON><PERSON><PERSON> học đã mua</div></a>
    <div class="tab-item active"><i class="zmdi zmdi-calendar-note"></i> Quá trình học tập</div>
    <a href="{{url('/account/courses')}}/test" ><div class="tab-item"><i class="zmdi zmdi-check-square"></i> Kết quả kiểm tra</div></a>
</div>
<div class="tab-content-container course-journey">

    @foreach($listCourses as $course)
        <div class="journey-item row">
            <div class="col-sm-2"><span style="font-size: 40px;">{{ $course->title }}</span></div>
            <div class="col-sm-8">
                <div class="progress" style="height: 40px; margin-top: 10px; margin-bottom: 0;">
                  <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                  aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width:40%;font-size: 16px; padding-top: 10px;">Hoàn thành</div>
                </div>
            </div>
            <div class="col-sm-2">
                <p style="text-align: center; font-size: 25px; margin-top: 15px;">50%</p>
            </div>
        </div>
    @endforeach

    {{-- Nếu danh sách trống --}}
    @if(sizeof($listCourses) == 0 )
    <div class="notification-empty-container">
      <i class="fa fa-mortar-board"></i> Bạn chưa mua khóa học nào
    </div>
    @endif

</div>

    </div>

   </div>
</div>
<script>
  $(".item-user").removeClass("current");
  $(".courses").addClass("current");
</script>
@stop
