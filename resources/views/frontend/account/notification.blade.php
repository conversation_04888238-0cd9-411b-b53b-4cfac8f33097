@extends('frontend._layouts.default')

@section('title') Dungmori - Tài khoản cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng Nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')


<div class="main">
   <div class="main-center">
    <div class="main-user-left">
      {{-- import left menu --}}
          @include('frontend.account._left')
    </div>

    <div class="main-user-right">
      <h2 class="main-user-title noti-title">Thông báo của bạn</h2>
      <span class="mark-as-readed" onclick="markAsReadedAll()"><PERSON><PERSON>h d<PERSON>u tất cả là đã đọc</span>

      {{-- N<PERSON><PERSON> danh sách trống --}}
      @if(sizeof($listNotifications) == 0 )

      <div class="notification-empty-container">
        <i class="zmdi zmdi-notifications-off"></i> Bạn không có thông báo
      </div>

      {{-- Nếu có thông báo--}}
      @else
      <table class="table">
        <tbody>
          @foreach($listNotifications as $item)

              <tr class="notification-item @if($item->readed == 0) unread @endif" id="noti-{{ $item->id }}">
                <td class="user-form-item">
                  <a href="{{url('')}}/{{ $item->getLink() }}" target="_blank" @if($item->readed == 0) onclick="markAsReaded({{ $item->id }})"  @endif>
                    <p>

                        {{-- <i class="zmdi @if($item->readed == 0) zmdi-notifications-active  @endif"></i> --}}
                        @if($item->sender_id != 0)
                            <img style="width: 35px; height: 35px; border-radius: 50%; margin-right: 10px;" src="{{url('cdn/avatar/small')}}/{{ $item->avatar}}"/>
                        @else
                            <i class="zmdi zmdi-notifications"></i>
                        @endif

                        @if($item->table_name == 'community_posts')

                            @if ($item->type != 'community_create_post')
                                <b>{{$item->userName}}</b>
                            @endif

                            @if($item->type == 'community_like_owner')
                                 đã thích bài viết của bạn
                            @elseif ($item->type == 'community_comment')
                                đã bình luận trên bài đăng của bạn
                            @elseif ($item->type == 'community_like_comment')
                                đã thích bình luận của bạn trong một bài viết
                            @elseif ($item->type == 'community_reply')
                                trả lời bình luận của bạn trong một bài viết
                            @elseif ($item->type == 'community_create_post')
                                {{ $item->title }}
                            @endif
                        @elseif($item->table_name == 'invoice')
                            @if($item->getInvoiceBook())
                                Bạn đã đăng ký <b>mua sách</b> với mã đơn hàng
                                <a style="color: green;" href="{{url('checkout')}}/{{$item->getInvoiceBook()->uuid}}">{{$item->getInvoiceBook()->uuid}}</a>
                            @else
                                {{ $item->title }}
                            @endif

                        @else
                            {{ $item->title }}
                        @endif
                    </p>
                  </a>
                </td>
                <td class="user-form-item notifi-info"> {{ $item->getFriendlyTime()}}</td>
              </tr>
            {{-- @else
              <tr class="notification-item">
                <td class="user-form-item">
                  <a href="{{url('')}}/{{ $item->getLink() }}" target="_blank">
                    <p><i class="zmdi zmdi-notifications"></i> {{ $item->title }}</p>
                  </a>
                </td>
                <td class="user-form-item notifi-info"> {{ $item->getFriendlyTime()}}</td>
              </tr>
            @endif --}}
          @endforeach
        </tbody>
     </table>
     <div class="paginate-container">{{ $listNotifications->links() }} </div>
     @endif

    </div>
   </div>
</div>

<script>
  $(".item-user").removeClass("current");
  $(".notifications").addClass("current");
</script>

@stop
