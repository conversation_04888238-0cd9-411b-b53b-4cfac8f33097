
<div class="user-avatar-container">
	<span class="clear-preview-upload"><i class="zmdi zmdi-close-circle"></i></span>
	<img class="user-avatar" id="user-avatar-preview"
	@if(Auth::user()->avatar == null)
	src="{{url('assets/img/default-avatar.jpg')}}"
	@else
	src="{{url('cdn/avatar/default')}}/{{ Auth::user()->avatar}}"
	@endif
	/>
</div>

<button type="button" class="btn change-avatar">
	<i class="zmdi zmdi-camera-add"></i><span class="change-avatar-btn-desktop">&nbspThay ảnh đại diện</span><span class="change-avatar-btn-mobile">&nbspThay ảnh</span>
</button>
<button type="button" class="btn btn-success save-avatar" style="display: none;">
	<i class="zmdi zmdi-cloud-upload"></i> <PERSON><PERSON><PERSON>
</button>

<form id="avatar-form">
	<input type='file' id="inputAvatar" name="inputAvatar" style="display: none;" onchange="readURL(this);" />
</form>

<div class="account-left-menu">
	<a href="{{url('/account')}}" class="item-user current"><i class="zmdi zmdi-account-box"></i><span>Thông tin cá nhân</span></a>
	<a href="{{url('/account/notifications')}}" class="item-user notifications"><i class="zmdi zmdi-notifications"></i><span>Thông báo</span></a>
	<a href="{{url('/account/courses')}}" class="item-user courses"><i class="zmdi zmdi-dns"></i><span>Khóa học của tôi</span></a>
	<a href="{{url('/account/score')}}" class="item-user score"><i class="fa fa-trophy"></i><span>Sự tiến bộ của tôi&nbsp;&nbsp;<div class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span class="tooltiptext">Theo dõi sự tiến bộ của bạn thông qua 2 bài thi giữa kỳ và cuối kỳ</span></div></span></a>
	<a href="{{url('/account/achievement')}}" class="item-user achievement"><i class="fa fa-star"></i><span>Thành tích&nbsp;&nbsp;<div class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span class="tooltiptext">Phần thưởng cho sự chăm chỉ của bạn, duy trì và cải thiện thứ hạng trên bảng xếp hạng nhé!</span></div></span></a>
	<a href="{{url('/account/billing')}}" class="item-user billing"><i class="zmdi zmdi-card"></i><span>Lịch sử thanh toán</span></a>
	<a href="{{url('/account/active')}}" class="item-user active"><i class="zmdi zmdi-shopping-cart"></i><span>Nhập mã kích hoạt</span></a>
	<a style="cursor: pointer;" onclick="logout()" class="item-user danger logout-tab"><i class="zmdi zmdi-power"></i></i><span>Đăng xuất</span></a>
</div>
<script type="text/javascript">
	var userAvatar = "";
	@if(Auth::user()->avatar == null)
		userAvatar ="{{url('assets/img/default-avatar.jpg')}}";
	@else
		userAvatar ="{{url('cdn/avatar/default')}}/{{ Auth::user()->avatar}}";
	@endif
</script>

@section('bottom-js')
<script src="{{asset('assets/js/avatar.js')}}?{{filemtime('assets/js/avatar.js')}}"></script>
@stop
