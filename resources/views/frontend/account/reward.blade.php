@extends('frontend._layouts.default')

@section('title') Dungmori - T<PERSON><PERSON> c<PERSON> nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/frontend/img/logo.png')}} @stop
@section('author') DUNGMORI @stop
@section('content')

<div class="main">
   <div class="main-center">
    <div class="main-user-left">
      {{-- import left menu --}}
      @include('frontend.account._left')
    </div>
    <div class="main-user-right">
      <div class="" id="reward-screen">
        <div style="display: none">
          <div id="reward-container" style="border-radius: 20px;">
            <div v-if="currentReward">
              <div class="w-[400px] p-8 flex flex-col items-center gap-5">
                <div class="w-32 group col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center cursor-pointer hover:bg-gradient-to-r hover:from-green-500 hover:to-lime-500">
                  <div class="bg-white rounded-[10px] w-[82%] aspect-square flex justify-center items-center shadow shadow-sm overflow-hidden">
                    <img
                        class="w-full object-fit mix-blend-multiply"
                        :src="currentReward.image_name"
                        alt=""
                    >
                  </div>
                </div>
                <div class="text-lg text-[#96D962] font-semibold font-quicksand">@{{ currentReward.name }}</div>
                <div class="text-base font-quicksand">@{{ currentReward.description }}</div>
                <div class="text-lg font-semibold font-quicksand">Đổi <span class="text-[#96D962]">@{{ currentReward.price }}</span> điểm để lấy item này?</div>
                <div class="w-full rounded-xl p-3 bg-[#96D962] text-lg font-semibold font-montserrat text-center cursor-pointer hover:bg-green-400" @click="confirmBuyReward(currentReward)">
                  <template>Đổi thưởng</template>
                </div>
                <div class="w-full rounded-xl p-3 text-gray-400 text-lg font-semibold font-montserrat text-center hover:bg-gray-200 cursor-pointer" @click="closeModal">Huỷ</div>
              </div>
            </div>
            <div v-if="currentActiveReward" id="active-reward-container" style="border-radius: 20px;">
              <div class="w-[400px] p-8 flex flex-col items-center gap-5">
                <div class="w-32 group col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center cursor-pointer hover:bg-gradient-to-r hover:from-green-500 hover:to-lime-500">
                  <div class="bg-white rounded-[10px] w-[82%] aspect-square flex justify-center items-center shadow shadow-sm overflow-hidden">
                    <img
                        class="w-full object-fit mix-blend-multiply"
                        :src="currentActiveReward.image_name"
                        alt=""
                    >
                  </div>
                </div>
                <div class="text-lg text-[#96D962] font-semibold font-quicksand">Đã nhận phần thưởng này</div>
                <div class="text-base font-quicksand">
                  Phần thưởng đã được chuyển vào mục
                  <b>Phần thưởng đã nhận</b>.
                  Để kích hoạt phần thưởng ngay bây giờ, ấn nút bên dưới <span v-if="['discount_percent', 'discount_money'].includes(currentActiveReward.type)">liên hệ với bộ phận CSKH của Dũng Mori.</span></div>
                <div class="w-full rounded-xl p-3 bg-[#96D962] text-lg font-semibold font-montserrat text-center cursor-pointer hover:bg-green-400" @click="confirmActiveReward(currentActiveReward)">
                  <template v-if="['discount_percent', 'discount_money'].includes(currentActiveReward.type)">Liên hệ</template>
                  <template v-else>Sử dụng ngay</template>
                </div>
                <div class="w-full rounded-xl p-3 text-gray-400 text-lg font-semibold font-montserrat text-center hover:bg-gray-200 cursor-pointer" @click="closeModal">Để sau</div>
              </div>
            </div>
          </div>
        </div>
        <div class="font-montserrat font-semibold text-[24px] flex items-center v-cloak">
          <b><span class="text-[#96D962]" v-cloak>@{{ userPoints }}</span></b>
          <img src="{{ asset('assets/img/gamification/leaf.svg') }}" alt="" class="w-5 shrink-0">
        </div>
        <div class="font-montserrat font-semibold text-[24px]"><b>Chưa sử dụng</b></div>
        <div class="grid grid-cols-4 gap-10 px-10 py-5" v-cloak>
          <div
              v-for="(group, idx) in unUsedUserRewardGroups"
              :key="'used-user-reward-group' + idx"
              @click="activeReward(group.rewards[0], rewardIsUsing(group))"
              class="relative"
              :class="[rewardIsUsing(group) ? 'grayscale' : '']"
          >
            <div v-if="group.rewards.length > 1" class="py-1 px-3 bg-red-500 text-white rounded-full absolute -top-3 -right-3 z-10">x@{{ group.rewards.length }}</div>
            <div
                class="group col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center cursor-pointer hover:bg-gradient-to-r hover:from-green-500 hover:to-lime-500"
            >
              <div class="bg-white rounded-[10px] w-[82%] aspect-square flex justify-center items-center shadow shadow-sm overflow-hidden">
                <img
                    :src="group.rewards[0].image_name"
                    class="w-15 transition-transform object-fit mix-blend-multiply"
                    alt=""
                >
              </div>
            </div>
            <div class="text-center font-bold mt-2 font-quicksand text-base">
              @{{ rewardIsUsing(group) ? 'Đang được sử dụng' : 'Dùng ngay' }}
            </div>
          </div>
        </div>
        <div class="font-montserrat font-semibold text-[24px]"><b>Đang sử dụng</b></div>
        <div class="grid grid-cols-4 gap-10 px-10 py-5" v-cloak>
          <div
              v-for="(group, idx) in usingUserRewardGroups"
              :key="'used-user-reward-group' + idx"
          >
            <div
                class="group col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center cursor-pointer hover:bg-gradient-to-r hover:from-green-500 hover:to-lime-500"
            >
              <div class="bg-white rounded-[10px] w-[82%] aspect-square flex justify-center items-center shadow shadow-sm overflow-hidden">
                <img
                    :src="group.rewards[0].image_name"
                    class="w-15 transition-transform object-fit mix-blend-multiply"
                    alt=""
                >
              </div>
            </div>
            <div class="text-center font-bold mt-2 font-quicksand text-base">
              <div class="text-red-500">Còn @{{ getMinutesLeft(group.rewards[0].pivot.time_left) ?? '-- giờ -- phút' }}</div>
            </div>
          </div>
        </div>

        <div class="font-montserrat font-semibold text-[24px]"><b>Đã sử dụng</b></div>
        <div class="grid grid-cols-4 gap-10 px-10 py-5" v-cloak>
          <div
              v-for="(group, idx) in usedUserRewardGroups"
              :key="'used-user-reward-group' + idx"
              class="relative"
          >
            <div v-if="group.rewards.length > 1" class="py-1 px-3 bg-red-500 text-white rounded-full absolute -top-3 -right-3 z-10">x@{{ group.rewards.length }}</div>
            <div
                class="group col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center grayscale"
            >
              <div class="bg-white rounded-[10px] w-[82%] aspect-square flex justify-center items-center shadow shadow-sm overflow-hidden">
                <img
                    :src="group.rewards[0].image_name"
                    class="w-15 transition-transform object-fit mix-blend-multiply"
                    alt=""
                >
              </div>
            </div>
            <div class="text-center font-bold mt-2 font-quicksand text-base">
              <template v-if="group.rewards[0].claim_status == 3">Đã sử dụng</template>
              <div v-else>
                Hết hạn
              </div>
            </div>
          </div>
        </div>
        <div class="mt-5 font-montserrat font-semibold text-[24px]"><b>Phần thưởng &nbsp;&nbsp;<div class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span class="tooltiptext once">Sử dụng điểm kiếm được để đổi lấy những phần quà giá trị của Dũng Mori bạn nhé!</span></div></b></div>
        <div class="grid grid-cols-2 gap-5 px-10 py-5" v-cloak>
          <div
              v-for="reward in rewards"
              class="group flex gap-3 items-start hover:shadow-md cursor-pointer p-2 rounded-lg"
              :class="[
                reward.price <= userPoints ? 'cursor-pointer' : 'cursor-not-allowed grayscale'
              ]"
              @click="buyReward(reward)"
          >
            <div
                class="w-22 col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center"
                :class="[
                  reward.price <= userPoints ? 'group-hover:bg-gradient-to-r group-hover:from-green-500 group-hover:to-lime-500' : ''
                ]"
            >
              <div class="bg-white rounded-[10px] w-[82%] aspect-square flex justify-center items-center shadow shadow-sm overflow-hidden">
                <img
                    :src="reward.image_name"
                    class="w-full transition-transform object-fit mix-blend-multiply"
                    alt=""
                >
              </div>
            </div>
            <div class="flex-1">
              <div class="w-full font-bold font-quicksand text-base flex justify-between items-start">
                <span>@{{ reward.name }}</span>
                <div class="flex items-center gap-1">
                  <span class="text-[#96D962] flex-1">@{{ reward.price }}</span>
                  <img src="{{ asset('assets/img/gamification/leaf.svg') }}" alt="" class="w-4 shrink-0">
                </div>
              </div>
              <p class="font-quicksand text-sm text-gray-400">@{{ reward.description }}</p>
            </div>
          </div>
        </div>
        @if(auth()->guard('admin')->check())
          <div class="mt-5 font-montserrat font-semibold text-[24px]"><b>Lịch sử</b></div>
          <div class="flow-root mt-3" v-cloak>
            <ul role="list" class="-mb-8">
              <li v-for="(record, index) in pointHistories" :key="`record-${record}`">
                <div class="relative pb-8">
                  <span v-if="index < pointHistories.length - 1" class="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                  <div class="relative flex space-x-3 items-center gap-2 flex-start">
                    <div>
                    <span
                        class="h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white text-white"
                        :class="[
                          record.type == 'remove' || record.points < 0 ? 'bg-red-500' : 'bg-green-500'
                        ]"
                    >
                      @{{ record.type == 'remove' || record.points < 0 ? '-' : '+' }}
                    </span>
                    </div>
                    <div class="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                      <div>
                        <p class="text-sm text-gray-500"><b :class="[record.type == 'remove' || record.points < 0 ? 'text-red-500' : 'text-green-500']">@{{ record.points }} điểm</b> @{{ record.reason }} <span v-if="record.lesson_id">Bài @{{ record.lesson_id }} </span>(@{{ record.reason_table }} @{{ record.reason_id }})</p>
                      </div>
                      <div class="whitespace-nowrap text-right text-sm text-gray-500">
                        <time datetime="2020-09-20">@{{ record.created_at }}</time>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        @endif

      </div>
    </div>
   </div>
</div>
@stop
@section('footer-js')
  <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
  <script type="text/javascript">
    var tmp = localStorage.getItem('hide_reward_tooltip');
    if (!tmp) {
        $(".tooltiptext.once").css("visibility", "visible");
        localStorage.setItem("hide_reward_tooltip", "1")
    }
    new Vue({
        el: '#reward-screen',
        data: function() {
            return {
                url: window.location.origin,
                userRewards: @json($userRewards),
                rewards: @json($rewards),
                pointHistories: @json($pointHistories),
                userPoints: '{{ $point }}',
                currentReward: null,
                currentActiveReward: null,
                now: '',
                auditType: {
                  add: 'Cộng',
                  remove: 'Trừ',
                  reset: 'Đặt lại',
                  level_up: 'Lên level'
                },
            }
        },
        computed: {
            unUsedUserRewardGroups: function() {
                return _.chain(this.userRewards)
                    .filter(function (o) { return o.claim_status == 1 })
                    // Group the elements of Array based on `color` property
                    .groupBy("id")
                    // `key` is group's name (color), `value` is the array of objects
                    .map((value, key) => ({ id: parseInt(key), rewards: value }))
                    .value()
            },
            usingUserRewardGroups: function() {
                return _.chain(this.userRewards)
                    .filter(function (o) { return o.claim_status == 2 })
                    // Group the elements of Array based on `color` property
                    .groupBy("id")
                    // `key` is group's name (color), `value` is the array of objects
                    .map((value, key) => ({ id: parseInt(key), rewards: value }))
                    .value()
            },
            usedUserRewardGroups: function() {
                return _.chain(this.userRewards)
                    .filter(function (o) { return [3,4].includes(o.claim_status) })
                    // Group the elements of Array based on `color` property
                    .groupBy("id")
                    // `key` is group's name (color), `value` is the array of objects
                    .map((value, key) => ({ id: parseInt(key), rewards: value }))
                    .value()
            },
        },
        mounted: function() {
            var vm = this;
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            setInterval(() => { vm.now = moment.utc() }, 1000);
        },
        methods: {
            rewardIsUsing: function(reward) {
                var cond = _.findIndex(this.usingUserRewardGroups, function(item) {
                    return item.id == reward.id
                })
                return cond !== -1
            },
            getMinutesLeft: function(time) {
                var duration = moment.duration(moment(time).diff(this.now))
                var hours = parseInt(duration.asHours())
                var minutes = parseInt(duration.asMinutes()) % 60
                if (!hours && !minutes) return null
                return hours + ' giờ ' + minutes + ' phút'
            },
            getAuditType: function(type) {
                return this.auditType[type];
            },
            closeModal: function() {
                $.fancybox.close()
            },
            buyReward: function(reward) {
                if (reward.price > this.userPoints) return;
                $.fancybox.open( $("#reward-container"), {
                    touch: false
                })
                this.currentActiveReward = null;
                this.currentReward = reward;
            },
            activeReward: function(reward, isUsing) {
                if (isUsing) return;
                if ([2, 3, 4].includes(reward.claim_status)) return;
                $.fancybox.open( $("#reward-container"), {
                    touch: false
                } )
                this.currentReward = null;
                this.currentActiveReward = reward;
            },
            confirmBuyReward: function(reward) {
                var vm = this;
                var data = {
                    id: reward.id
                }
                $.post(vm.url + '/account/buy-reward', data, function (res) {
                    if (res.code == 200) {
                        vm.userRewards.unshift(res.data.userReward)
                        vm.userPoints = res.data.userPoints
                        vm.currentReward = null;
                        var idx = _.findIndex(vm.usingUserRewardGroups, function (o) {
                            return o.id == res.data.userReward.id
                        })
                        if (idx != -1) {
                            vm.closeModal()
                        } else {
                            vm.currentActiveReward = res.data.userReward;
                        }
                    } else {
                        alert(res.message)
                    }
                }).fail(function(e) {
                    alert( e.responseJSON.message );
                });
            },
            confirmActiveReward: function(reward) {
                var vm = this;
                if (['discount_money', 'discount_percent'].includes(reward.type)) {
                    this.openChatbox()
                    $.fancybox.close()
                    return;
                }
                var data = {
                    id: reward.pivot.id
                }
                $.post(vm.url + '/account/active-reward', data, function (res) {
                    if (res.code == 200) {
                        vm.userRewards = res.data
                        $.fancybox.close()
                    } else {
                        alert(res.message)
                    }
                }).fail(function(e) {
                    alert( e.responseJSON.message );
                });
            },
            openChatbox: function() {
                showChatbox()
            },
        }
    })
  </script>
@stop
