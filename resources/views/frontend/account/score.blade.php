@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON><PERSON>n cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')


  <div class="main">
    <div class="main-center">
      <div class="main-user-left">
        {{-- import left menu --}}
        @include('frontend.account._left')
      </div>

      <div class="main-user-right">
        @if (count($groups) === 0)
          <div class="text-lg">Bạn chưa tham gia lớp ONLINE VIP nào. Đăng ký ngay <a href="https://onlinevip.dungmori.com/" target="_blank">tại đ<PERSON></a></div>
        @else
          <h2 class="main-user-title">Danh sách lớp tham gia</h2>
          <div class="table-box">
            @foreach ($groups as $group)
              <a href="{{ url('account/score/detail') }}/{{ $group->id }}">
                <div class="flex align-items-center a-cursor-pointer mb-3">
                  <img data-src="{{ url('cdn/community/small') }}/{{ $group->avatar}}" class="lazyload score-group-image" alt="" />
                  <div class="font-semibold">{{ $group->name }}</div>
                </div>
              </a>
            @endforeach
          </div>
        @endif
      </div>
    </div>
  </div>
  <script>
      $(".item-user").removeClass("current");
      $(".score").addClass("current");
  </script>
@stop
