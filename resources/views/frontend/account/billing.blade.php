@extends('frontend._layouts.default')

@section('title') Dungmori - Tài <PERSON>n cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON>y tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')


<div class="main">
   <div class="main-center">
    <div class="main-user-left">
      {{-- import left menu --}}
      @include('frontend.account._left')
    </div>

    <div class="main-user-right">
      <h2 class="main-user-title">Lịch sử thanh toán của bạn</h2>
      <div class="table-box">
        <table class="table">
          <thead> <tr> <th><PERSON><PERSON>ố</th> <th><PERSON><PERSON><PERSON></th> <th><PERSON><PERSON> tả</th>
          <th><PERSON><PERSON><PERSON> hàng</th><th>Trạng thái</th><th>Số tiền</th><th>Ngày tạo</th><th style="text-align: right;">Xem</th></tr> </thead>
          <tbody>
            @foreach($listBilling as $item)
            <tr class="notification-item">
                <td class="user-form-item">{{$item->id}}</td>
                <td class="user-form-item" style="min-width: 120px;">{{$item->getOrderName()}}</td>
                <td class="user-form-item" style="max-width: 200px;">
                    {{$item->product_name}}
                    @if($item->extra_price > 0)<sup> <span class="label-hot">Plus</span></sup>@endif
                </td>
                <td class="user-form-item">{!! $item->getOrderStatus() !!}</td>
                <td class="user-form-item" style="min-width: 110px; font-size: 13px;">{{$item->getPaymentStatus()}}</td>
                <td class="user-form-item">{{number_format($item->price)}}đ</td>
                <td class="user-form-item" style="text-align: right;min-width: 140px;">{{$item->getFriendlyTime()}}</td>
                <td class="user-form-item" style="text-align: right;min-width: 30px;">
                  <a href="{{url('checkout')}}/{{$item->uuid}}" target="_blank"><i class="zmdi zmdi-more"></i></a>
                </td>
            </tr>
             @endforeach
          </tbody>
        </table>
      </div>

        {{-- Nếu danh sách trống --}}
        @if(sizeof($listBilling) == 0 )

        <div class="notification-empty-container">
          <i class="zmdi zmdi-card"></i> Bạn chưa thực hiện bất kỳ giao dịch nào
        </div>
        @endif

       <div class="paginate-container">{{ $listBilling->links() }} </div>
    </div>

   </div>
</div>
<script>
  $(".item-user").removeClass("current");
  $(".billing").addClass("current");
</script>
@stop
