@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON><PERSON> cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{asset('assets/frontend/img/logo.png')}} @stop
@section('author') DUNGMORI @stop
@section('content')

<div class="main">
   <div class="main-center">
    <div class="main-user-left">
      {{-- import left menu --}}
      @include('frontend.account._left')
    </div>

    <div class="main-user-right">
      <div class="" id="achievement-screen">
        <div style="display: none">
          <div id="login-streak-modal" style="border-radius: 20px;">
            <div>
              <div class="w-[400px] p-8 flex flex-col items-center gap-5 text-center">
                <div class="relative">
                  <img src="{{ asset('assets/img/score/login-streak-sun.png') }}" alt="" class="w-32">
                  <div class="w-9 h-9 bg-red-400 text-white font-quicksand font-semibold flex items-center justify-center border-2 border-white rounded-full absolute bottom-0 left-[43px]">@{{ currentStreak }}</div>
                </div>
                <div class="text-lg text-[#96D962] font-semibold font-quicksand">Bạn đã bắt đầu chuỗi kỉ lục</div>
                <div class="text-base font-quicksand">Hãy vào học hàng ngày để duy trì động lực học tập, tạo chuỗi kỉ lục.</div>
                <div class="w-full p-4 bg-gray-50 rounded-xl">
                  <div class="flex items-center justify-center gap-3 pb-3 mb-3 border-b border-gray-300">
                    <div v-for="date in weekDays" class="flex flex-col items-center gap-3">
                      <img v-if="streakData.includes(date.format('YYYY-MM-DD'))" src="{{ asset('assets/img/score/streak-check.svg') }}" alt="" class="w-8 h-8">
                      <div v-else class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center"></div>
                      <div class="text-gray-500">
                        @{{ daysOfWeek[date.day()] }}
                      </div>
                    </div>
                  </div>
                  <div class="w-full flex items-center justify-center gap-2 font-quicksand">
                    <img src="{{ asset('assets/img/score/sun.png') }}" alt="" class="w-10 h-10 shrink-0">
                    <span>Kỉ lục học tập: </span>
                    <span class="text-[#41A336] font-bold">@{{ currentStreakRecord }} ngày liên tiếp</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="display: none">
          <div id="achievement-modal" style="border-radius: 20px;">
            <div v-if="unclaimedAchievements.length">
              <div class="w-[400px] p-8 flex flex-col items-center gap-1 text-center">
                <img :src="unclaimedAchievements[0].image_name" alt="">
                <div class="mt-3 text-lg font-semibold font-quicksand">
                  Chúc mừng bạn đã nhận <span v-if="unclaimedAchievements.length > 1">@{{ unclaimedAchievements.length }}</span> danh hiệu</div>
                <div v-for="achievement in unclaimedAchievements" class="text-lg font-semibold font-quicksand text-[#96D962]">“@{{ achievement.name }}"</div>
                <div class="mt-3 text-base font-quicksand">Hãy tích cực học tập để nhận nhận được nhiều danh hiệu cùng những ưu đãi nhé!</div>
{{--                <div class="mt-3 w-full rounded-xl p-3 bg-[#96D962] text-lg font-semibold font-montserrat text-center cursor-pointer hover:bg-green-400" @click="shareAchievement()">Share lên Cộng đồng</div>--}}
{{--                <div class="mt-3 w-full rounded-xl p-3 text-gray-400 text-lg font-semibold font-montserrat text-center hover:bg-gray-200 cursor-pointer" @click="closeModal">Để sau</div>--}}
              </div>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-3 gap-5">
          <a href="{{ route('frontend.user.account.rewards') }}" class="col-span-1 flex justify-center items-center gap-4 border border-gray-200 rounded-lg p-4 font-quicksand text-[16px] shadow-md cursor-pointer hover:shadow-lg">
            <img src="{{ asset('assets/img/score/leaf.png') }}" class="w-10 h-10 object-fit">
            <div class="text-center text-gray-900">
              <p>Điểm nhận được</p>
              <p><b>{{ $point }}</b></p>
            </div>
          </a>
          <div class="col-span-1 flex justify-center items-center gap-4 border border-gray-200 rounded-lg p-4 font-quicksand text-[16px] shadow-md cursor-pointer hover:shadow-lg" @click="openStreakModal">
            <img src="{{ asset('assets/img/score/sun.png') }}" class="w-10 h-10 object-fit">
            <div class="text-center">
              <p>Chuỗi ngày học</p>
              <p><b>{{ $loginStreak }}</b></p>
            </div>
          </div>
          <div class="col-span-1 flex justify-center items-center gap-4 border border-gray-200 rounded-lg p-4 font-quicksand text-[16px] shadow-md cursor-pointer hover:shadow-lg">
            <img src="{{ $userAchievements[0]->image_name ?? '--' }}" class="w-10 h-10 object-fit">
            <div class="text-center">
              <p>{{ isset($userAchievements[0]) ? $userAchievements[0]->name : '--' }}</p>
            </div>
          </div>
        </div>
        <div class="mt-5 flex items-center justify-center gap-3 font-quicksand text-[20px] pb-3 border-b-2 border-gray-400 border-dashed">
          <img src="{{ asset('assets/img/score/sun-shining.png') }}" class="w-12 object-fit">
          <p>Kỉ lục hoàn thành mục tiêu: <b>{{ max($loginStreak ?? 0, $highestLoginStreak ?? 0) }} ngày liên tiếp</b></p>
        </div>
        <div class="mt-5 font-montserrat font-semibold text-[24px]"><b>Danh hiệu <div class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span class="tooltiptext once">Vinh danh cho những cố gắng vừa qua của bạn</span></div></b></div>
        <div class="grid grid-cols-4 gap-10 px-10 py-5">
          @foreach($achievements as $achievement)
            <div>
              <div
                  @class([
                    'group col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center ',
                    'col-span-1 bg-gray-200 rounded-[10px] aspect-square flex justify-center items-center relative overflow-hidden grayscale' => is_null($userAchievements->find($achievement->id)),
                    'cursor-pointer hover:bg-gradient-to-r hover:from-green-500 hover:to-lime-500' => !is_null($userAchievements->find($achievement->id))
                  ])
              >
                <div class="bg-white rounded-[10px] w-[82%] aspect-square flex justify-center items-center shadow shadow-sm">
                  <img
                      src="{{ $achievement->image_name }}"
                      class=""
                      @class([
                        'w-15 transition-transform object-fit mix-blend-multiply',
                        'group-hover:-translate-y-1.25' => !is_null($userAchievements->find($achievement->id))
                      ])
                   alt="">
                </div>
                @if (is_null($userAchievements->find($achievement->id)))
                  <div class="w-full h-full bg-black/50 absolute top-0 left-0 flex justify-center items-center">
                    <img src="{{ asset('assets/img/score/lock.svg') }}" class="w-10 object-fit">
                  </div>
                @endif
              </div>
              <p class="text-center font-bold mt-2 font-quicksand text-base">{{ $achievement->name }}</p>
            </div>
          @endforeach
        </div>
        <div class="mt-5 flex items-center">
          <b class="font-montserrat font-semibold text-[24px]">Xếp hạng tuần <div class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span class="tooltiptext once">Số điểm kiếm được trong tuần sẽ quyết định thứ hạng của bạn. Cùng kiếm thật nhiều điểm và nhận danh hiệu Top 1 nào!</span></div></b>
          <p class="font-quicksand ml-auto text-base">Kết thúc: <b><span id="cd-days">--</span> ngày <span id="cd-hours">--</span> giờ <span id="cd-minutes">--</span> phút <span id="cd-seconds">--</span> giây</b></p>
        </div>
        <div class="my-4 flex items-center gap-3 text-[#96D962]">
          <img src="{{ asset('assets/img/score/trophy-duotone.svg') }}" class="w-5 object-fit">
          <p>Xếp hạng của tôi: <b>#{{ $userLeaderboardRank !== false ? $userLeaderboardRank + 1 : '--' }}</b></p>
        </div>
        <ul role="list" class="rounded-lg border border-gray-100 py-4 mt-2">
          @foreach($leaderboard as $key => $user)
            <li class="flex justify-between gap-x-6 py-3 px-4 hover:bg-[#E8FFD4] cursor-pointer" id="{{ auth()->guard('admin')->check() ? $user->user?->id : '' }}">
              <div class="flex items-center min-w-0 gap-x-4">
                <p class="font-black font-quicksand text-xl {{ $key < 3 ? 'text-amber-500' : 'text-gray-500' }}">{{ $key + 1 }}</p>
                <img class="h-10 w-10 flex-none rounded-full bg-gray-50"
                     @if(!$user->user?->avatar)
                       src="{{asset('assets/img/default-avatar.jpg')}}"
                     @else
                       src="{{asset('cdn/avatar/small')}}/{{ $user->user->avatar ?? '' }}"
                    @endif
                />
                <div class="min-w-0 flex-auto">
                  <p class="text-lg font-quicksand font-base leading-6 text-gray-900">
                    <span href="#" class="hover:underline">{{ $user->user?->name ?? '' }}</span>
                  </p>
                </div>
              </div>
              <div class="flex shrink-0 items-center gap-x-6">
                <div class="relative flex-none font-quicksand text-base flex items-center gap-1">
                  <img src="{{ asset('assets/img/score/leaf.svg') }}" class="w-5 object-fit">
                  <p>{{ $user->total_point ?? 0 }}</p>
                </div>
              </div>
            </li>
          @endforeach
        </ul>
      </div>
    </div>
   </div>
</div>
<script>
  var tmp = localStorage.getItem('hide_achievement_tooltip');
  if (!tmp) {
      $(".tooltiptext.once").css("visibility", "visible");
      localStorage.setItem("hide_achievement_tooltip", "1")
  }
  $(".item-user").removeClass("current");
  $(".achievement").addClass("current");
  var leaderboardTimeEnd = '{{ $leaderboardEndTime }}'
  let timer = function (date) {
      let timer = Math.round(date - Math.round(new Date().getTime()/1000))
      let days, hours, minutes, seconds;
      setInterval(function () {
          if (--timer < 0) {
              timer = 0;
          }
          days = parseInt(timer / 60 / 60 / 24, 10);
          hours = parseInt((timer / 60 / 60) % 24, 10);
          minutes = parseInt((timer / 60) % 60, 10);
          seconds = parseInt(timer % 60, 10);

          days = days < 10 ? "0" + days : days;
          hours = hours < 10 ? "0" + hours : hours;
          minutes = minutes < 10 ? "0" + minutes : minutes;
          seconds = seconds < 10 ? "0" + seconds : seconds;

          document.getElementById('cd-days').innerHTML = days;
          document.getElementById('cd-hours').innerHTML = hours;
          document.getElementById('cd-minutes').innerHTML = minutes;
          document.getElementById('cd-seconds').innerHTML = seconds;
      }, 1000);
  }

  //using the function
  const today = new Date()
  timer(leaderboardTimeEnd);
</script>
@stop
@section('footer-js')
  <script type="text/javascript">
      new Vue({
          el: '#achievement-screen',
          data: function() {
              return {
                  url: window.location.origin,
                  achievements: @json($achievements),
                  userAchievements: @json($userAchievements),
                  currentStreak: 0,
                  currentStreakRecord: 0,
                  streakData: [],
                  daysOfWeek: [
                      'CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'
                  ],
                  loading: false,
              }
          },
          computed: {
            weekDays: function() {
                moment.updateLocale('en', { week: { dow: 1 } });

                var startOfWeek = moment().startOf('isoWeek');
                var endOfWeek = moment().endOf('isoWeek');

                var days = [];
                var day = startOfWeek;

                for (var i = 0; i <= endOfWeek.diff(startOfWeek, 'days'); i++) {
                    days.push(day);
                    day = day.clone().add(1, 'd');
                }

                return days;
            },
            unclaimedAchievements: function() {
                return this.userAchievements.filter(function (item) {
                    return !item.pivot.claim_at
                })
            }
          },
          mounted: function() {
              $.ajaxSetup({
                  headers: {
                      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                  }
              });
              this.openAchievementModal()
          },
          methods: {
              closeModal: function() {
                  $.fancybox.close()
              },
              openAchievementModal: function() {
                  if (this.unclaimedAchievements.length) {
                      $.fancybox.open( $("#achievement-modal"), {
                          touch: false
                      })
                      $.post(this.url + '/account/claim-achievements', function (res) {})
                  }
              },
              openStreakModal: function() {
                  const vm = this;
                  this.loading = true
                  $.fancybox.open( $("#login-streak-modal"), {
                      touch: false
                  })

                  $.get(vm.url + '/account/current-streak', function (res) {
                      if (res.code === 200) {
                          vm.currentStreakRecord = res.data.currentStreakRecord
                          vm.currentStreak = res.data.currentStreak
                          vm.streakData = res.data.streakData
                      } else {
                          alert(res.message)
                      }
                  }).fail(function(e) {
                      alert( e.responseJSON.message );
                  });
              },
              shareAchievement: function() {
                  // console.log()
              }
          }
      })
  </script>
@stop
