@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON>i <PERSON> cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<div class="main">
   <div class="main-center">
    <div class="main-user-left">
      {{-- import left menu --}}
      @include('frontend.account._left')
    </div>

    <div class="main-user-right improve-score-container">
      <h2 class="main-user-title">Sự tiến bộ của bạn</h2>
      <div class="table-box improve-score-content">
        @if (count($results) >= 1)
          <div>
            <canvas id="myChart"></canvas>
          </div>
          <table class="table statistic-table">
            <tr>
              <th class="text-center"></th>
              <th class="text-center">Từ vựng</th>
              <th class="text-center">Chữ Hán</th>
              <th class="text-center">Ngữ pháp</th>
              <th class="text-center">Đọc hiểu</th>
              <th class="text-center">Nghe hiểu</th>
              <th class="text-center">Tổng điểm</th>
            </tr>
            <tr>
              <td class="text-center">Giữa kỳ</td>
              <td class="text-center">{{ $results[0]->tuvung }}</td>
              <td class="text-center">{{ $results[0]->chuhan }}</td>
              <td class="text-center">{{ $results[0]->nguphap }}</td>
              <td class="text-center">{{ $results[0]->dochieu }}</td>
              <td class="text-center">{{ $results[0]->score_3 }}</td>
              <td class="text-center">{{ $results[0]->total_score }}</td>
            </tr>
            @if (count($results) >= 2)
              <tr>
                <td class="text-center">Cuối kỳ</td>
                <td class="text-center">{{ $results[1]->tuvung }}</td>
                <td class="text-center">{{ $results[1]->chuhan }}</td>
                <td class="text-center">{{ $results[1]->nguphap }}</td>
                <td class="text-center">{{ $results[1]->dochieu }}</td>
                <td class="text-center">{{ $results[1]->score_3 }}</td>
                <td class="text-center">{{ $results[1]->total_score }}</td>
              </tr>
            @endif
          </table>
          @if ($groupUser && $groupUser->teacher_review)
            <div class="comment-box">
              <img class="teacher-comment-img" src="{{ asset('assets/img/teacher_comment.png') }}" />
              <img class="mori-icon" src="{{ asset('assets/img/new_home/12-2021/teaching-mori.svg') }}" />
              <div class="teacher-comment">
                <span><b>Đánh giá giữa kỳ:</b></span>
                @if($groupUser->teacher_first_review)
                  <p> {{ $groupUser->teacher_first_review }}</p>              
                @else
                  <p>Không có</p>
                @endif
              </div>
              <div class="teacher-comment" style="margin-top: 30px">
                <span><b>Đánh giá cuối kỳ:</b></span>
                @if($groupUser->teacher_review)
                  <p>{{ $groupUser->teacher_review }}</p>              
                @else
                  <p>Không có</p>
                @endif
              </div>
            </div>
          @endif
        @else
          <div class="error-score">
            Bạn chưa hoàn thành bài thi giữa kỳ
          </div>
        @endif
      </div>
    </div>
   </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  @if (count($results) >= 1)
    const ctx = document.getElementById('myChart');
    var results = <?php echo $results; ?>;

    var statistic = [{
      label: 'Giữa kỳ',
      data: [
        results[0].tuvung,
        results[0].chuhan,
        results[0].nguphap,
        results[0].dochieu,
        results[0].score_3
      ],
      fill: true,
      backgroundColor: 'rgba(255, 99, 132, 0.6)',
      borderColor: 'rgb(255, 99, 132)',
      pointBackgroundColor: 'rgb(255, 99, 132)',
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: 'rgb(255, 99, 132)'
    }];

    if (results.length >= 2) {
      statistic.push({
        label: 'Cuối kỳ',
        data: [
          results[1].tuvung,
          results[1].chuhan,
          results[1].nguphap,
          results[1].dochieu,
          results[1].score_3
        ],
        fill: true,
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgb(54, 162, 235)',
        pointBackgroundColor: 'rgb(54, 162, 235)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(54, 162, 235)'
      });
    }

    const data = {
      labels: [
        'Từ vựng',
        'Chữ Hán',
        'Ngữ pháp',
        'Đọc hiểu',
        'Nghe hiểu',
      ],
      datasets: statistic
    };

    new Chart(ctx, {
      type: 'bar',
      data: data,
    });
  @endif

  $(".item-user").removeClass("current");
  $(".score").addClass("current");
</script>
@stop
