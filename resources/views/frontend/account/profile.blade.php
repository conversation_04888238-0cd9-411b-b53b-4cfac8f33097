@extends('frontend._layouts.default')

@section('title') Dungmori - Tài <PERSON>n cá nhân @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/logo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

@if(Auth::check())
<input type="hidden" id="csrf_token" value="{{ csrf_token() }}">
@endif

<div class="main">
   <div class="main-center">
		<div class="main-user-left">

			{{-- import left menu --}}
         	@include('frontend.account._left')

		</div>

		<div class="main-user-right" id="user-info">
			<h2 class="main-user-title">Thông tin cá nhân</h2>
		    <table class="table" id="user-info-table" style="display: none;">
		      	<tbody>
		      	<tr>
		            <td class="user-form-item desc">Họ và Tên</td>

		            <td class="user-form-item" v-if="changeName == false" v-html="printInfo(user.name)"></td>
		            <td class="user-form-item action" v-if="changeName == false" v-on:click="showEditer('name')"><i class="zmdi zmdi-edit"></i> chỉnh sửa</td>

		            <td class="user-form-item" v-if="changeName == true">
						<ul class="error-list" v-show="errors.length > 0 && currentField == 'name'">
							<li v-for="error in errors">@{{ error }}</li>
						</ul>
		            	<input class="user-form-input" type="text" id="account-name" :value="user.name" /><br/>
		            	<p style="opacity: 0.6;"><i>Ví dụ: Trần Văn A</i></p>
		            	<span class="change-btn" v-on:click="saveChange('name')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('name')">Hủy bỏ</span>
		            </td>
		            <td class="user-form-item action" v-if="changeName == true" v-on:click="hideEditer('name')"><i class="zmdi zmdi-close-circle"></i> Đóng</td>

		         </tr>
		         <tr>
		            <td class="user-form-item desc">Email</td>
		            <td class="user-form-item lock form-text-email" title="Không sửa được" @if(Auth::user()->email != null && Auth::user()->activation == 0) v-on:click="mouseOver" @endif>
		            	<p style="word-wrap: break-word;">
		            		@if(Auth::user()->provider == 'facebook')
		            			<i class="fa fa-facebook-square" aria-hidden="true" title="Đăng nhập sử dụng facebook"></i>
		            		@elseif(Auth::user()->provider == 'google')
		            			<i class="fa fa-google-plus-square" aria-hidden="true" title="Đăng nhập sử dụng google"></i>
		            		@endif

		            		@if(Auth::user()->email != null)
		            			{{ Auth::user()->email }}
		            			@if(Auth::user()->activation == 0)
									<span class="label label-default"><i class="zmdi zmdi-alert-triangle"></i> chưa xác thực</span>
                                    <button class="btn btn-success send-mail-verify-email" v-show="displaySendMailBtn" v-on:click="sendMailVerify">Gửi mail xác nhận</button>
								@endif
		            		@else
		            			Đăng nhập sử dụng tài khoản facebook
		            		@endif
		            	</p>
		            	</td>
		            <td class="user-form-item protected">
		            	<i class="zmdi zmdi-lock" data-toggle="tooltip" data-placement="left" title="Thuộc tính không sửa được"></i>
		            </td>
		         </tr>

		         {{-- nếu là các người dùng từ hệ thống cũ có username thì in ra --}}
		         @if(Auth::user()->username != null)
		         <tr>
		            <td class="user-form-item desc">Tên đăng nhập</td>
		            <td class="user-form-item lock" title="Không sửa được">{{ Auth::user()->username }}</td>
		            <td class="user-form-item protected">
		            	<i class="zmdi zmdi-lock" data-toggle="tooltip" data-placement="left" title="Thuộc tính bị khóa"></i>
		            </td>
		         </tr>
		         @endif

		         {{-- chỉ cho sửa mật khẩu nếu password khác null - xử lý lỗi với các dữ liệu cũ --}}
		         @if(Auth::user()->password != null)
		         <tr class="password-container">
		            <td class="user-form-item desc">Mật khẩu</td>
		            <td class="user-form-item" v-if="changePass == false">******</td>

		            <td class="user-form-item change-pass action" v-if="changePass == false" v-on:click="showEditer('password')"><i class="zmdi zmdi-edit"></i> chỉnh sửa</td>

		            <td class="user-form-item" v-if="changePass == true">
		            	<ul class="error-list" v-show="errors.length > 0 && currentField == 'password'">
							<li v-for="error in errors">@{{ error }}</li>
						</ul>
		            	<input type="password" class="form-control" id="old-password" style="margin-bottom: 10px;" placeholder="Mật khẩu cũ" autocomplete="off"/>
		            	<input type="password" class="form-control" id="new-password" placeholder="Mật khẩu mới"/>
		            	<input type="password" class="form-control" id="retype-password" style="margin-bottom: 10px; border-top: none;" placeholder="Mật khẩu mới"/>
		            	<input type="text" class="form-control" style="width: 200px;" id="captcha-input" placeholder="Nhập mã bên cạnh" autocomplete="off">
									<img class="captcha-container captcha-active-course" id="captcha-change" src="{{ route('get-captcha-img') }}"></img>
									<i style="margin-top: 12px; margin-left: 10px;" class="glyphicon glyphicon-repeat cursor-pointer" v-on:click="changeCaptcha()"></i>

		            	<div class="change-btn-container">
		            		<span class="change-btn" v-on:click="saveChange('password')">Lưu lại</span>
		            		<span class="cancel-btn" v-on:click="hideEditer('password')">Hủy bỏ</span>
		            	</div>
		            </td>
		            <td class="user-form-item action" v-if="changePass == true" v-on:click="hideEditer('password')"><i class="zmdi zmdi-close-circle"></i> Đóng</td>

		         </tr>
		         @endif

		         <tr>
		            <td class="user-form-item desc">Ngày sinh</td>
		            <td class="user-form-item" v-if="changeBirthday == false" v-html="printInfo(user.birthday)"></td>
		            <td class="user-form-item action" v-if="changeBirthday == false" v-on:click="showEditer('birthday')"><i class="zmdi zmdi-edit"></i> chỉnh sửa</td>
		            <td class="user-form-item" v-if="changeBirthday == true">
                        <ul class="error-list" v-show="errors.length > 0 && currentField == 'birthday'">
                            <li v-for="error in errors">@{{ error }}</li>
                        </ul>
		            	<select class="user-form-input" v-model="birthday.month" style="min-width: 120px; margin-right: 10px; ">
							<option value="">-Tháng-</option>
		            		<option value="01">Tháng 1</option>
		            		<option value="02">Tháng 2</option>
		            		<option value="03">Tháng 3</option>
		            		<option value="04">Tháng 4</option>
		            		<option value="05">Tháng 5</option>
		            		<option value="06">Tháng 6</option>
		            		<option value="07">Tháng 7</option>
		            		<option value="08">Tháng 8</option>
		            		<option value="09">Tháng 9</option>
		            		<option value="10">Tháng 10</option>
		            		<option value="11">Tháng 11</option>
		            		<option value="12">Tháng 12</option>
		            	</select>
		            	<input class="user-form-day" type="number" min="0" max="31" v-model="birthday.day" placeholder="ngày"/>
		            	<input class="user-form-year" type="number" min="1010" v-model="birthday.year" placeholder="năm"/><br/>
		            	<span class="change-btn" v-on:click="saveChange('birthday')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('birthday')">Hủy bỏ</span>
		            </td>
		            <td class="user-form-item action" v-if="changeBirthday == true" v-on:click="hideEditer('birthday')"><i class="zmdi zmdi-close-circle"></i> Đóng</td>
		         </tr>
		         <tr>
		            <td class="user-form-item desc">Số điện thoại</td>
		            <td class="user-form-item" v-if="changePhone == false" v-html="printInfo(user.phone)"></td>
		            <td class="user-form-item action" v-if="changePhone == false" v-on:click="showEditer('phone')"><i class="zmdi zmdi-edit"></i> chỉnh sửa</td>
		            <td class="user-form-item" v-if="changePhone == true">
		            	<ul class="error-list" v-show="errors.length > 0 && currentField == 'phone'">
							<li v-for="error in errors">@{{ error }}</li>
						</ul>
		            	<input class="user-form-input" type="text" id="account-phone" :value="user.phone"/><br/>
		            	<span class="change-btn" v-on:click="saveChange('phone')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('phone')">Hủy bỏ</span>
		            </td>
		            <td class="user-form-item action" v-if="changePhone == true" v-on:click="hideEditer('phone')"><i class="zmdi zmdi-close-circle"></i> Đóng</td>
		         </tr>
		         <tr>
		            <td class="user-form-item desc">Trình độ tiếng Nhật</td>
		            <td class="user-form-item" v-if="changeNihongo == false" v-html="printInfo(user.nihongo)"></td>
		            <td class="user-form-item action" v-if="changeNihongo == false" v-on:click="showEditer('nihongo')"><i class="zmdi zmdi-edit"></i> chỉnh sửa</td>

		            <td class="user-form-item" v-if="changeNihongo == true">
		            	<ul class="error-list" v-show="errors.length > 0 && currentField == 'nihongo'">
                            <li v-for="error in errors">@{{ error }}</li>
                        </ul>
                        <ht-select :init_selected="user.nihongo" @change="updateJapaneseLevel" init_other_value="none" init_options='[{"value":"none","text":"Chọn trình độ"},{"value":"N1","text":"N1"},{"value":"N2","text":"N2"},{"value":"N3","text":"N3"},{"value":"N4","text":"N4"},{"value":"N5","text":"N5"},{"value":"Không","text":"Không"}]'></ht-select>
		            	</ht-select>
		            	<span class="change-btn" v-on:click="saveChange('nihongo')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('nihongo')">Hủy bỏ</span>
		            </td>
		            <td class="user-form-item action" v-if="changeNihongo == true" v-on:click="hideEditer('nihongo')"><i class="zmdi zmdi-close-circle"></i> Đóng</td>
		         </tr>
		         <tr>
		            <td class="user-form-item desc">Địa chỉ</td>
		            <td class="user-form-item" v-if="changeAddress == false" v-html="printInfo(user.address)"></td>
		            <td class="user-form-item action" v-if="changeAddress == false" v-on:click="showEditer('address')"><i class="zmdi zmdi-edit"></i> chỉnh sửa</td>

		            <td class="user-form-item" v-if="changeAddress == true">
		            	<ul class="error-list" v-show="errors.length > 0 && currentField == 'address'">
							<li v-for="error in errors">@{{ error }}</li>
						</ul>
		            	<textarea class="user-form-text-area" type="text" id="account-address" :value="user.address"></textarea><br/>
		            	<p style="opacity: 0.6;"><i>Ví dụ: Số 05, Khu đô thị Mộ Lao, Quận Hà Đông, Hà Nội</i></p>
		            	<span class="change-btn" v-on:click="saveChange('address')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('address')">Hủy bỏ</span>
		            </td>
		            <td class="user-form-item action" v-if="changeAddress == true" v-on:click="hideEditer('address')"><i class="zmdi zmdi-close-circle"></i> Đóng</td>
		         </tr>
		         <tr>
		            <td class="user-form-item desc">Quốc gia</td>
		            <td class="user-form-item" v-if="changeCountry == false" v-html="printCountry(user.country)"></td>
		            <td class="user-form-item action" v-if="changeCountry == false" v-on:click="showEditer('country')"><i class="zmdi zmdi-edit"></i> chỉnh sửa</td>

		            <td class="user-form-item" v-if="changeCountry == true">
                        <ul class="error-list" v-show="errors.length > 0 && currentField == 'country'">
                            <li v-for="error in errors">@{{ error }}</li>
                        </ul>
		            	<ht-select :init_selected="user.country" @change="updateCountry" init_other_value="none" init_options='[{"value":"none","text":"Chọn quốc gia"},{"value":"230","text":"Việt Nam"},{"value":"107","text":"Nhật Bản"},{"value":"1","text":"Khác"}]'></ht-select>
		            	</ht-select>
		            	<span class="change-btn" v-on:click="saveChange('country')">Lưu lại</span>
		            	<span class="cancel-btn" v-on:click="hideEditer('country')">Hủy bỏ</span>
		            </td>
		            <td class="user-form-item action" v-if="changeCountry == true" v-on:click="hideEditer('country')"><i class="zmdi zmdi-close-circle"></i> Đóng</td>

		         </tr>
		      </tbody>
		   </table>
		</div>

@if(Auth::check())
<script type="text/javascript">
user_name =     "{{ Auth::user()->name }}";
user_birthday = "{{ Auth::user()->birth }}";
user_phone =    "{{ Auth::user()->phone }}";
user_nihongo =  "{{ Auth::user()->japanese_level }}";
user_address =  {!! json_encode(Auth::user()->address) !!};
user_country =  "{{ Auth::user()->country }}";
</script>
@endif

@section('footer-js')
	@isset($_GET['focus'])
		@if($_GET['focus'] == 'changePass')
			<script type="text/javascript">
				$(document).ready(function () {
					$('.change-pass').trigger('click');
					$(".password-container").css('background', '#f2f2f2');
				});
			</script>
		@endif
	@endisset
	<script src="{{asset('assets/js/profile.js')}}?{{filemtime('assets/js/profile.js')}}"></script>
@stop
   </div>
</div>

@stop
