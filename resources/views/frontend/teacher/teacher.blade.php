@extends('frontend._layouts.default')

@section('title') Dungmori <PERSON> <PERSON><PERSON> sách gi<PERSON>o viên @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
@stop

@section('header-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
@stop


@section('content')

<script> $(".giao-vien").addClass("active");  </script>

<div class="main">
   <div class="main-center">

    <script type="text/javascript">
      function cuonTrang(id){
        $('html, body').animate({
            scrollTop: $("#scroll-"+id).offset().top-120
        }, 500);
      }
    </script>

    <h2 class="main-title">Đội ngũ <span class="green-text">giáo viên</span></h2>
    <div class="teacher-slider">

      @foreach($authors as $author)
        <div class="teacher-slider-item" onclick="cuonTrang('{{$author->id}}')">
          <img src="{{url('cdn/teacher/default')}}/{{$author->avatar_name}}"  />
       </div>
      @endforeach

    </div>
    <script type="text/javascript">
       $('.teacher-slider').slick({
         slidesToShow: 4,
         slidesToScroll: 1,
         autoplay: true,
         autoplaySpeed: 3000,
       });
    </script>


    <h3 class="teacher-text-intro">Đội ngũ giáo viên của dungmori.com là những người có năng lực, <br/>trách nhiệm cao, luôn sẵn sàng
để giải đáp tất cả các thắc mắc cũng như hỗ trợ các bạn trong suốt quá trình học tập.</h3>

   </div>
</div>

@foreach($authors as $index => $author)
<div class="full-container block-teacher @if($index % 2 == 0) gv1 @else gv2 @endif" id="scroll-{{$author->id}}">
    <div class="center-container">
        <div class="teacher-image">
        </div>
        <div class="teacher-image-content">
            <img src="{{url('cdn/teacher/default')}}/{{$author->avatar_name}}"/>
        </div>
        <div class="teacher-info">
            <h2><span class="green-text">{{$author->name}}</span></h2>
            <h3 style="-webkit-box-orient: vertical;">{!! $author->information !!}</h3>
           <a href="{{url('giao-vien')}}/{{ $author->SEOurl }}"><div class="read-more">Xem chi tiết</div></a>
        </div>
    </div>
</div>
@endforeach

@stop
