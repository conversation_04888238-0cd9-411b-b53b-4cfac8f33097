@extends('frontend._layouts.default')

@section('title') Dungmori - Đ<PERSON>i ngũ giáo viên @stop
@section('description') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop
@section('header-css')

@stop
@section('content')
    <div class="main">
        <div class="teacher__screen--wrapper" id="teacher__screen--wrapper">
            <div class="home-philosophy">
                <div class="home-philosophy__icon container">
                    <img src="{{asset('assets/img/new_home/12-2021/running-mori.svg')}}" style="transform: scale(-1, 1)" />
                </div>
                <div class="container">
                    <div class="flex home-philosophy__content">
                        <div class="home-philosophy__text">
                            <div class="text-ghost">
                                Bắt đầu bằng việc <strong>học tiếng Nhật</strong>, tại Dũng Mori <br />
                                chúng tôi cùng học trò của mình thắp lên và trao giữ <strong>lửa học tập </strong><br />
                                hướng tới xây dựng một nền <br />
                                <strong class="text-ghost">"Giáo dục thay đổi cuộc đời"</strong>
                            </div>
                            <img src="{{asset('assets/img/new_home/12-2021/triet-ly-giao-duc.png')}}" />
                        </div>
                        <div class="home-philosophy__image ml-5 pc">
                            <div class="mr-5">
                                <div class="home-teacher flex flex-col bg-orange-75">
                                    <div class="home-teacher__inside">
                                        <img src="{{ asset('assets/img/new_home/12-2021/co-thanh.png') }}" alt="Cô Thanh" style="width: 110%; left: 0; bottom: -17px;">
                                    </div>
                                    <div class="mt-4">
                                        Thạc sĩ đại học Ngoại ngữ Tokyo, chuyên ngành Giáo dục tiếng Nhật
                                        Học bổng toàn phần Chính phủ
                                        Nhật Bản MEXT
                                    </div>
                                    <div class="home-teacher-name" id="thanh">
                                        <div class="home-ribbon">
                                            <svg width="17" height="60" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                                <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#F27712" stroke="black"/>
                                            </svg>
                                            <div class="bookmark-ribbon bookmark-ribbon--yellow">
                                                <strong>Cô Thanh</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="home-teacher__arrow-left home-teacher__arrow--orange"></div>
                                </div>
                            </div>
                            <div>
                                <div class="home-teacher flex flex-col bg-green-25" style="width: 220px">
                                    <div class="home-teacher__inside">
                                        <img src="{{ asset('assets/img/new_home/12-2021/thay-dung.png') }}" alt="Cô Thanh" style="width: 90%; left: 9px; bottom: 0">
                                    </div>
                                    <div class="mt-4">
                                        Cử nhân đại học Nihon, khoa Giáo dục
                                        Học bổng Mitsubishi
                                    </div>
                                    <div class="home-teacher-name" id="dung">
                                        <div class="home-ribbon">
                                            <svg width="17" height="60" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                                <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#96D962" stroke="black"/>
                                            </svg>
                                            <div class="bookmark-ribbon bookmark-ribbon--green">
                                                <strong>Thầy Dũng</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="home-teacher__arrow-left home-teacher__arrow--green"></div>
                                </div>
                                <div class="home-teacher flex flex-col bg-blue-25 mt-5" style="width: 220px">
                                    <div class="home-teacher__inside">
                                        <img src="{{ asset('assets/img/new_home/12-2021/thay-nghia.png') }}" alt="Cô Thanh" style="width: 90%; left: 0; bottom: 0">
                                    </div>
                                    <div class="mt-4">
                                        Cử nhân đại học Aomori Chuo
                                        Học bổng toàn phần của Tập đoàn Tokyo Marine, hệ thạc sĩ KHTT Đại học Quốc gia Nagoya
                                    </div>
                                    <div class="home-teacher-name" id="nghia">
                                        <div class="home-ribbon">
                                            <svg width="17" height="60" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                                <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#9097AC" stroke="black"/>
                                            </svg>
                                            <div class="bookmark-ribbon bookmark-ribbon--blue">
                                                <strong>Thầy Nghĩa</strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="home-teacher__arrow-left home-teacher__arrow--blue"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="teacher__text--block text-center teacher__container">
                <div style="margin-top: 100px;">
                    @include('frontend.home.components.section_title',['title' => '教師チーム', 'description' => 'ĐỘI NGŨ GIÁO VIÊN dũng mori'])
                </div>
                <span class="teacher__text--content">
                    <br/>
                    <p><i>“Nếu bạn không thể giải thích vấn đề một cách đơn giản, thì bạn không hiểu điều đó đủ kỹ càng” </i></p>
                    <p style="font-weight: 700">- Albert Einstein - </p>
                    <br/>
                    <p>Đó cũng chính là <span style="color: #41A336; font-weight: 400">CHÂM NGÔN GIẢNG DẠY</span> của đội ngũ giảng viên/giáo viên tại <span style="color: #41A336; font-weight: 400">Dũng Mori</span>.</p>
                    <p>Hãy cùng chúng tôi tiếp cận tiếng Nhật theo 1 cách nhìn <span style="color: #41A336; font-weight: 400">Thú vị - Khoa học - Đơn giản</span> nhất nhé!</p>
                </span>
            </div>
            <div class="teacher__cards teacher__container">
                <div class="teacher__cards--normal">
                    @foreach($teachers as $teacher)
                        <div class="teacher__card">
                            <div class="teacher__card--info">
                                <div class="teacher-image-wrapper">
                                    <div class="teacher-image">
                                        <div class="image-inner">
                                            <img src="{{asset('cdn/teacher/default/') .'/'. $teacher->avatar_name}}"/>
                                        </div>
                                        <div class="circle"></div>
                                    </div>
                                </div>
                                <div class="my-3">
                                    <img src="{{ asset('assets/img/teacher/scholarship.svg') }}">
                                </div>
                                <div class="teacher__card--full-name" style="height: 12%; font-size: 25px">
                                    <span class="-mx-4">{{$teacher->name}}</span>
                                    <div class="blur-background"></div>
                                </div>
                                <div class="teacher__card--desc">
                                    <div class="teacher__card--desc-short teacher__card--desc-short-sm">
                                        {!! $teacher->information !!}
                                    </div>
                                </div>
                                <div class="teacher__card--button flex flex-column justify-end">
                                    <a href="{{url('/giao-vien/' . $teacher->SEOurl)}}" target="_blank" class="teacher__card--button-detail mb-5">
                                        <svg width="30" height="30" viewBox="0 0 46 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M23.3644 32.8097L32.4079 23.7661L23.3644 14.7226" stroke="#96D962" stroke-width="1.65385" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M14.3208 23.7659H32.4079" stroke="#96D962" stroke-width="1.65385" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M23.018 46.375C35.0861 46.375 44.8693 36.2527 44.8693 23.7661C44.8693 11.2796 35.0861 1.15723 23.018 1.15723C10.9498 1.15723 1.16663 11.2796 1.16663 23.7661C1.16663 36.2527 10.9498 46.375 23.018 46.375Z" stroke="#96D962" stroke-width="1.65385" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <span class="ml-3">Xem chi tiết</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <modal v-if="playingVideo" @close="closeVideo">
                <div slot="body">
                    <iframe width="100%" height="700px" :src="'https://www.youtube.com/embed/' + playingUrl + '?autoplay=1'" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
            </modal>
        </div>
    </div>
@stop

@section('footer-js')
    <link rel="stylesheet" href="{{asset('plugin/owlcarousel2/dist/assets/owl.carousel.min.css')}}"/>
    <script src="{{asset('plugin/owlcarousel2/dist/owl.carousel.min.js')}}"></script>
    <script> $(".giao-vien").addClass("active");  </script>
    <script>
        $(document).ready(function(){
            var owl;
            owl = $(".owl-carousel").owlCarousel({
                items: 1,
                loop: true,
                dotsContainer: '#carousel-custom-dots',
                dotsData: true,
                autoplay: true,
                autoplayTimeout: 3000000,
                slideSpeed : 300000,
            });
            $('.owl-dot').on('click', function () {
                owl.trigger('to.owl.carousel', [$(this).index(), 300]);
            });
        });
    </script>
    <script src="{{asset('assets/backend/js/modal.js')}}?{{filemtime('assets/backend/js/modal.js')}}"></script>
    <script type="text/javascript">
        var teachers = new Vue({
            el: '#teacher__screen--wrapper',
            data: function () {
                return {
                    playingVideo: false,
                    playingUrl: ''
                }
            },
            methods: {
                playVideo: function (url) {
                    var vm = this;
                    vm.playingVideo = true;
                    vm.playingUrl = url;
                },
                closeVideo: function () {
                    var vm = this;
                    vm.playingVideo = false;
                    vm.playingUrl = '';
                }
            },
        });
    </script>
@stop
