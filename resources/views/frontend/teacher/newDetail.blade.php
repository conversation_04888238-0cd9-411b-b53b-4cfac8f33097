@extends('frontend._layouts.default')

@section('title') Dungmori - Gi<PERSON><PERSON> viên {{$author->name}} @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop
@section('header-css')

@stop
@section('content')
    <div class="main">
        <div class="teacher-detail__screen--wrapper" id="teacher-detail__screen--wrapper">
            <div class="teacher-detail__screen--slider">
                <div class="teacher-detail__screen--slider-inner">
                    <div class="teacher-detail__info container">
                        <div class="teacher-detail__avatar">
                            <svg width="94" height="81" viewBox="0 0 94 81" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="path-1-inside-1_1726_2647" fill="white">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.69487 25.4553C2.10196 26.4052 0.770049 29.2773 1.71995 31.8702L13.1998 63.2061C14.1497 65.799 17.0217 67.1309 19.6146 66.181L42.4947 57.7989L50.7737 64.7163C51.3645 65.2099 52.268 64.8789 52.4001 64.1205L54.2512 53.492L78.5858 44.577C81.1787 43.6271 82.5106 40.7551 81.5607 38.1622L70.0809 6.8263C69.131 4.2334 66.259 2.90149 63.6661 3.85139L4.69487 25.4553Z"/>
                                </mask>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.69487 25.4553C2.10196 26.4052 0.770049 29.2773 1.71995 31.8702L13.1998 63.2061C14.1497 65.799 17.0217 67.1309 19.6146 66.181L42.4947 57.7989L50.7737 64.7163C51.3645 65.2099 52.268 64.8789 52.4001 64.1205L54.2512 53.492L78.5858 44.577C81.1787 43.6271 82.5106 40.7551 81.5607 38.1622L70.0809 6.8263C69.131 4.2334 66.259 2.90149 63.6661 3.85139L4.69487 25.4553Z" fill="#96D962"/>
                                <path d="M1.71995 31.8702L2.98757 31.4058L1.71995 31.8702ZM4.69487 25.4553L4.23048 24.1877L4.69487 25.4553ZM13.1998 63.2061L11.9322 63.6705L13.1998 63.2061ZM42.4947 57.7989L43.3603 56.7629L42.7622 56.2632L42.0303 56.5313L42.4947 57.7989ZM50.7737 64.7163L51.6393 63.6803L50.7737 64.7163ZM52.4001 64.1205L51.0701 63.8888L52.4001 64.1205ZM54.2512 53.492L53.7868 52.2244L53.0549 52.4925L52.9212 53.2603L54.2512 53.492ZM63.6661 3.85139L64.1305 5.119L63.6661 3.85139ZM2.98757 31.4058C2.29414 29.513 3.26643 27.4164 5.15925 26.723L4.23048 24.1877C0.937493 25.3941 -0.754039 29.0416 0.452338 32.3345L2.98757 31.4058ZM14.4674 62.7417L2.98757 31.4058L0.452338 32.3345L11.9322 63.6705L14.4674 62.7417ZM19.1502 64.9134C17.2574 65.6068 15.1608 64.6345 14.4674 62.7417L11.9322 63.6705C13.1386 66.9635 16.786 68.655 20.079 67.4486L19.1502 64.9134ZM42.0303 56.5313L19.1502 64.9134L20.079 67.4486L42.9591 59.0665L42.0303 56.5313ZM41.6291 58.8349L49.9081 65.7523L51.6393 63.6803L43.3603 56.7629L41.6291 58.8349ZM49.9081 65.7523C51.2964 66.9122 53.4197 66.1343 53.7301 64.3521L51.0701 63.8888C51.1163 63.6234 51.4326 63.5076 51.6393 63.6803L49.9081 65.7523ZM53.7301 64.3521L55.5812 53.7236L52.9212 53.2603L51.0701 63.8888L53.7301 64.3521ZM78.1214 43.3094L53.7868 52.2244L54.7156 54.7596L79.0502 45.8447L78.1214 43.3094ZM80.2931 38.6266C80.9865 40.5194 80.0142 42.616 78.1214 43.3094L79.0502 45.8447C82.3432 44.6383 84.0347 40.9908 82.8283 37.6978L80.2931 38.6266ZM68.8133 7.29069L80.2931 38.6266L82.8283 37.6978L71.3485 6.36192L68.8133 7.29069ZM64.1305 5.119C66.0233 4.42557 68.1198 5.39787 68.8133 7.29069L71.3485 6.36192C70.1421 3.06893 66.4947 1.3774 63.2017 2.58378L64.1305 5.119ZM5.15925 26.723L64.1305 5.119L63.2017 2.58378L4.23048 24.1877L5.15925 26.723Z" fill="#96D962" mask="url(#path-1-inside-1_1726_2647)"/>
                                <path d="M45.176 46.9185C39.3054 49.0692 23.1929 35.4897 27.7085 29.7061C31.9687 24.2441 38.6855 29.2018 38.6855 29.2018C38.6855 29.2018 40.5995 21.0823 47.3905 22.4957C54.5775 24.0041 51.0466 44.7679 45.176 46.9185Z" fill="#F6F6F6" stroke="#F6F6F6" stroke-width="1.3547" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                                <mask id="path-4-inside-2_1726_2647" fill="white">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.5784 26.2329C8.98551 27.1828 7.65359 30.0548 8.6035 32.6477L20.0833 63.9837C21.0332 66.5766 23.9053 67.9085 26.4982 66.9586L49.3782 58.5765L57.6574 65.494C58.2481 65.9876 59.1516 65.6566 59.2837 64.8982L61.1348 54.2695L85.4694 45.3546C88.0623 44.4047 89.3942 41.5327 88.4443 38.9398L76.9644 7.60389C76.0145 5.01099 73.1425 3.67907 70.5496 4.62898L11.5784 26.2329Z"/>
                                </mask>
                                <path d="M49.3782 58.5765L50.6606 57.0417L49.7745 56.3014L48.6903 56.6986L49.3782 58.5765ZM57.6574 65.494L58.9397 63.9592L58.9397 63.9592L57.6574 65.494ZM59.2837 64.8982L57.3134 64.555L57.3134 64.555L59.2837 64.8982ZM61.1348 54.2695L60.4469 52.3916L59.3626 52.7888L59.1645 53.9264L61.1348 54.2695ZM88.4443 38.9398L86.5663 39.6278L88.4443 38.9398ZM76.9644 7.60389L78.8424 6.91591L76.9644 7.60389ZM10.4814 31.9598C9.9115 30.404 10.7106 28.6808 12.2664 28.1109L10.8904 24.355C7.26037 25.6848 5.39569 29.7057 6.72555 33.3357L10.4814 31.9598ZM21.9613 63.2957L10.4814 31.9598L6.72555 33.3357L18.2054 64.6717L21.9613 63.2957ZM25.8102 65.0806C24.2544 65.6506 22.5312 64.8514 21.9613 63.2957L18.2054 64.6717C19.5353 68.3017 23.5561 70.1664 27.1861 68.8365L25.8102 65.0806ZM48.6903 56.6986L25.8102 65.0806L27.1861 68.8365L50.0662 60.4545L48.6903 56.6986ZM48.0959 60.1113L56.375 67.0288L58.9397 63.9592L50.6606 57.0417L48.0959 60.1113ZM56.375 67.0288C58.1472 68.5095 60.8578 67.5165 61.254 65.2413L57.3134 64.555C57.4454 63.7966 58.349 63.4656 58.9397 63.9592L56.375 67.0288ZM61.254 65.2413L63.1052 54.6127L59.1645 53.9264L57.3134 64.555L61.254 65.2413ZM84.7814 43.4767L60.4469 52.3916L61.8228 56.1475L86.1573 47.2326L84.7814 43.4767ZM86.5663 39.6278C87.1363 41.1835 86.3371 42.9067 84.7814 43.4767L86.1573 47.2326C89.7874 45.9027 91.6521 41.8819 90.3222 38.2518L86.5663 39.6278ZM75.0865 8.29187L86.5663 39.6278L90.3222 38.2518L78.8424 6.91591L75.0865 8.29187ZM71.2376 6.50692C72.7933 5.93698 74.5165 6.73613 75.0865 8.29187L78.8424 6.91591C77.5125 3.28584 73.4917 1.42117 69.8616 2.75103L71.2376 6.50692ZM12.2664 28.1109L71.2376 6.50692L69.8616 2.75103L10.8904 24.355L12.2664 28.1109Z" fill="black" mask="url(#path-4-inside-2_1726_2647)"/>
                                <path d="M52.0595 47.6961C46.1889 49.8468 30.0764 36.2673 34.5921 30.4837C38.8522 25.0217 45.5691 29.9794 45.5691 29.9794C45.5691 29.9794 47.483 21.8599 54.274 23.2732C61.461 24.7816 57.9302 45.5454 52.0595 47.6961Z" stroke="black" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>

                            <div class="teacher-detail__avatar-small">
                                <img class="teacher-detail__avatar-image" src="{{asset('cdn/teacher_detail/default/') .'/'. $author->avartar_detail}}" />
                            </div>
                        </div>
                        <div class="teacher-detail__overview">
                            <div class="home-ribbon">
                                <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                    <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#96D962" stroke="black"/>
                                </svg>
                                <div class="bookmark-ribbon bookmark-ribbon--green">
                                    <strong>{{ $author->name }}</strong>
                                </div>
                            </div>
                            <div class="mt-5 mr-35">
                                {!! $author->introduction !!}
                            </div>
                            <svg class="teacher-detail__overview-icon pc" width="142" height="200" viewBox="0 0 142 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M125.424 109.679C124.749 109.268 124.104 108.843 123.474 108.388C117.356 118.52 107.874 126.18 96.6834 130.032C96.6834 130.032 107.285 144.695 109.03 156.895C111.171 172.175 105.379 176.662 100.598 177.864V186.868C99.603 187.09 98.594 187.247 97.5778 187.337V197.601H57.2825L62.3853 194.082L61.3149 186.75C56.432 186.398 53.1472 186.105 53.1472 186.105C52.358 182.174 51.8678 178.189 51.6809 174.184C47.4193 171.622 43.4426 168.614 39.8181 165.21C34.6419 160.063 31.4453 136.249 31.4453 136.249C53.8219 148.2 56.4759 131.307 56.6665 129.694V129.562C37.7946 122.671 24.4067 106.79 22.1926 87.2289C21.9757 85.2373 21.8778 83.2347 21.8994 81.2315C21.8977 78.8321 22.0691 76.4357 22.4125 74.061C22.4125 73.6993 22.4712 73.3474 22.5885 73.0052C21.6764 70.207 21.4304 67.2345 21.87 64.3244C22.0778 62.8184 22.4617 61.3419 23.0138 59.9254C23.3804 29.9385 44.2172 17.8851 44.2172 17.8851C45.286 18.3862 46.2744 19.0434 47.1499 19.8353C50.7566 23.0996 53.8031 26.9338 56.168 31.1848C57.6344 30.5836 59.2034 30.0557 60.7724 29.5718L60.1418 14.3218L48.9682 11.2278L70.9635 2.42969L93.6041 9.39486L82.7824 13.4127L83.3981 27.8709L83.8528 27.9442C86.5229 28.311 89.1605 28.8846 91.7418 29.6599C91.7418 24.6303 91.8884 21.5803 91.8884 21.5803C173.198 54.1626 125.424 109.679 125.424 109.679Z" fill="white"/>
                                <path d="M62.4009 194.052H97.5787V197.572H59.9814L62.4009 194.052Z" fill="#96D962"/>
                                <path d="M53.5739 136.894C53.5739 136.894 78.0766 145.384 100.6 136.498C103.452 141.118 105.792 146.035 107.58 151.161L98.4736 155.326C98.4736 155.326 61.8149 160.869 53.2807 153.654L47.2832 139.973L53.5739 136.894Z" fill="#96D962"/>
                                <path d="M23.1467 57.1681C23.1467 57.1681 23.3813 29.7767 44.3648 17.8259C44.3648 17.8259 53.1629 24.0872 55.9197 31.287C55.8464 31.243 23.9092 51.772 23.1467 57.1681Z" fill="#96D962"/>
                                <path d="M23.4099 69.1194C23.0073 69.1177 22.6195 68.9669 22.3216 68.696C22.0237 68.4252 21.8366 68.0536 21.7968 67.653C21.5477 65.0507 21.44 62.4367 21.4742 59.8227C21.8408 29.2785 42.6043 16.9319 43.4695 16.4186C43.6779 16.3028 43.9085 16.2324 44.1461 16.2121C44.3837 16.1919 44.623 16.2223 44.848 16.3013C46.0846 16.8664 47.2269 17.6181 48.2351 18.5302C51.9899 21.8951 55.1612 25.8591 57.6198 30.261C57.7288 30.4429 57.8008 30.6446 57.8318 30.8544C57.8628 31.0642 57.8522 31.278 57.8004 31.4837C57.7487 31.6893 57.6568 31.8827 57.5302 32.0529C57.4036 32.223 57.2447 32.3665 57.0626 32.4752C56.6942 32.6914 56.2557 32.7542 55.8415 32.6499C55.4273 32.5457 55.0705 32.2828 54.8484 31.918C52.5817 27.8153 49.6479 24.1185 46.1675 20.979C45.5995 20.491 44.9967 20.045 44.3639 19.6446C40.7567 22.1081 24.9935 34.3081 24.6856 59.8373C24.6462 62.334 24.749 64.8308 24.9935 67.3157C25.0136 67.5277 24.9916 67.7416 24.9287 67.9451C24.8658 68.1485 24.7632 68.3374 24.6269 68.501C24.4906 68.6646 24.3232 68.7996 24.1345 68.8981C23.9457 68.9967 23.7393 69.0569 23.5271 69.0753L23.4099 69.1194Z" fill="#282828"/>
                                <path d="M77.6366 134.84C74.6931 134.842 71.7531 134.636 68.8385 134.225C64.4979 133.628 60.2351 132.561 56.1251 131.043C36.432 123.843 22.8243 107.112 20.6101 87.3746C20.3766 85.3206 20.2738 83.2537 20.3022 81.1866C20.3087 78.7334 20.4851 76.2836 20.8301 73.8548C20.8301 73.5029 20.9327 73.1363 21.006 72.7697C21.3333 70.6629 21.7836 68.5771 22.355 66.523C24.0009 60.6445 26.6017 55.0764 30.0533 50.0413L30.2001 49.8653C33.0171 45.7831 36.3709 42.0988 40.1712 38.9116C44.779 35.021 49.9891 31.9058 55.5972 29.6883C57.1516 29.0724 58.7499 28.5005 60.3189 28.0313C67.8704 25.7884 75.8111 25.1687 83.6192 26.213L84.1031 26.2863C86.8529 26.6658 89.5691 27.259 92.2267 28.0606C105.046 31.9285 116.063 40.2459 123.294 51.5151C130.526 62.7844 133.497 76.2651 131.672 89.5301C130.714 96.5008 128.376 103.211 124.794 109.267C118.473 119.706 108.7 127.606 97.1683 131.6C90.8875 133.775 84.2835 134.871 77.6366 134.84ZM32.6488 51.9915C29.456 56.6832 27.049 61.8637 25.5223 67.3295C24.9875 69.2592 24.5616 71.2174 24.2466 73.1949C24.2466 73.5762 24.1293 73.8695 24.0853 74.1775C23.7557 76.4944 23.5892 78.8316 23.5868 81.1719C23.5609 83.1309 23.6538 85.0896 23.8653 87.0373C25.9476 105.543 38.7489 121.262 57.2983 128.095C61.1952 129.499 65.231 130.482 69.337 131.028C78.3496 132.354 87.5493 131.489 96.1566 128.506C106.974 124.759 116.143 117.353 122.082 107.566C125.455 101.878 127.649 95.5702 128.534 89.0169C130.253 76.5154 127.453 63.8107 120.639 53.1897C113.824 42.5686 103.442 34.7288 91.3616 31.0813C88.8502 30.3348 86.2869 29.7761 83.6926 29.4097H83.238C75.8672 28.426 68.3718 29.0106 61.2427 31.1253C59.7763 31.5799 58.31 32.0931 56.8436 32.6796C51.5565 34.7698 46.6476 37.7121 42.312 41.3897C38.6706 44.4281 35.468 47.9565 32.7954 51.8742L32.6488 51.9915Z" fill="#282828"/>
                                <path d="M125.425 109.679C124.75 109.268 124.105 108.843 123.475 108.388C95.4233 89.3257 91.9626 47.2267 91.8159 29.5425C91.8159 24.5129 91.9627 21.4629 91.9627 21.4629C173.199 54.1626 125.425 109.679 125.425 109.679Z" fill="#96D962"/>
                                <path d="M125.426 111.291C125.131 111.288 124.843 111.207 124.59 111.057C123.886 110.632 123.211 110.177 122.537 109.708C93.7963 90.2346 90.2771 47.1678 90.2771 29.5569C90.2771 24.5273 90.4237 21.4773 90.4237 21.3453C90.4437 21.0922 90.5232 20.8474 90.6557 20.6308C90.7882 20.4143 90.97 20.232 91.1862 20.0989C91.4007 19.9574 91.6469 19.8713 91.9028 19.8483C92.1587 19.8253 92.4163 19.866 92.6526 19.9669C119.648 30.8326 135.529 45.3935 139.884 63.2684C145.91 88.0791 127.522 109.81 126.687 110.734C126.532 110.916 126.338 111.061 126.12 111.157C125.901 111.254 125.664 111.3 125.426 111.291ZM93.5324 23.8234C93.5324 25.1872 93.5324 27.1374 93.5324 29.5129C93.679 46.6252 96.9783 88.431 124.472 107.054L125.176 107.538C128.725 102.904 141.687 84.1932 136.76 64.0015C132.669 47.681 118.152 34.1759 93.5324 23.8234Z" fill="#282828"/>
                                <path d="M83.6475 32.0346C76.3796 34.708 68.4566 35.0362 60.9924 32.973L60.8457 29.5124L60.2152 14.2624L60.054 10.3472L82.6797 9.37939L82.8556 13.2946L83.4716 27.7528L83.6475 32.0346Z" fill="#96D962"/>
                                <path d="M70.8477 35.9357C67.3757 35.9129 63.9228 35.4196 60.5832 34.4694C60.2553 34.3827 59.9633 34.1938 59.7498 33.9302C59.5363 33.6666 59.4121 33.3419 59.3954 33.003L58.4569 10.3772C58.4475 10.1655 58.4802 9.95417 58.5532 9.7553C58.6262 9.55643 58.7381 9.37407 58.8822 9.2188C59.0221 9.05944 59.1932 8.93038 59.3848 8.83959C59.5765 8.74881 59.7847 8.6982 59.9966 8.69089L82.6226 7.72309C82.8353 7.7092 83.0487 7.73842 83.2499 7.80901C83.4512 7.8796 83.6359 7.99011 83.7934 8.13389C83.9509 8.27767 84.0777 8.45175 84.1662 8.64574C84.2548 8.83972 84.3032 9.04962 84.3087 9.26279L85.2766 31.9179C85.2936 32.2602 85.2001 32.5989 85.01 32.8841C84.8199 33.1692 84.5433 33.3857 84.2208 33.5016C79.9405 35.0915 75.4137 35.9155 70.8477 35.9357ZM62.5481 31.7126C68.9759 33.2816 75.7162 33.002 81.9919 30.9061L81.1415 11.0664L61.727 11.8875L62.5481 31.7126Z" fill="#282828"/>
                                <path d="M93.6924 9.27743L82.8561 13.2952L71.6825 17.4157L60.2303 14.263L49.0566 11.169L71.0373 2.31226L93.6924 9.27743Z" fill="#96D962"/>
                                <path d="M71.6833 19.0283C71.5375 19.0504 71.3893 19.0504 71.2435 19.0283L48.6175 12.7816C48.2934 12.6902 48.006 12.4997 47.7956 12.2367C47.5852 11.9737 47.4624 11.6515 47.4444 11.3152C47.4231 10.98 47.5079 10.6465 47.6869 10.3622C47.8659 10.0779 48.13 9.85722 48.4416 9.73154L70.4369 0.933419C70.7879 0.809303 71.1709 0.809303 71.522 0.933419L94.1771 7.8986C94.4941 7.99569 94.7733 8.18901 94.9756 8.45174C95.1779 8.71447 95.2933 9.03361 95.3062 9.36495C95.3169 9.70232 95.2205 10.0345 95.0309 10.3137C94.8412 10.5929 94.568 10.8049 94.2505 10.9193L72.2551 19.0576C72.0644 19.0833 71.8704 19.0734 71.6833 19.0283ZM54.087 10.8753L71.6833 15.729L88.7369 9.42361L71.1407 4.01277L54.087 10.8753Z" fill="#282828"/>
                                <path d="M48.8379 11.6226L46.785 21.887L42.8992 28.5149L47.3569 29.4094L46.785 21.887" fill="#96D962"/>
                                <path d="M47.3552 31.023H47.0327L42.6336 30.1285C42.3807 30.0783 42.1435 29.9683 41.942 29.8075C41.7404 29.6468 41.5803 29.4401 41.4751 29.2047C41.3727 28.9708 41.3272 28.7159 41.3425 28.461C41.3578 28.2061 41.4335 27.9584 41.5632 27.7384L45.317 21.3451L47.3112 11.3299C47.3996 10.9145 47.6469 10.55 48.0003 10.3144C48.3538 10.0788 48.7853 9.99065 49.2028 10.0688C49.6241 10.1538 49.9945 10.4019 50.2335 10.759C50.4725 11.1162 50.5606 11.5533 50.4786 11.9751L48.4697 22.0196L49.0269 29.3514C49.0449 29.5983 49.0052 29.8461 48.9111 30.0751C48.8169 30.3042 48.6708 30.5082 48.4843 30.6711C48.1668 30.9284 47.7627 31.0544 47.3552 31.023ZM45.4342 27.3865H45.581V27.1225L45.4342 27.3865Z" fill="#282828"/>
                                <path d="M79.3658 72.435C80.6525 63.3256 76.0141 55.1384 69.0056 54.1485C61.9971 53.1585 55.2726 59.7406 53.9858 68.85C52.6991 77.9594 57.3375 86.1465 64.3459 87.1365C71.3544 88.1264 78.0791 81.5444 79.3658 72.435Z" fill="white"/>
                                <path d="M65.8608 88.8125C65.3209 88.8167 64.7815 88.7774 64.2479 88.6951C62.243 88.3641 60.3432 87.5683 58.7009 86.3716C57.0586 85.1749 55.7192 83.6104 54.7898 81.8033C52.6131 77.7699 51.8199 73.1337 52.5317 68.6061C53.1113 64.0492 55.1721 59.8091 58.3972 56.538C59.7923 55.0567 61.5151 53.923 63.4275 53.2278C65.34 52.5326 67.3888 52.2953 69.4095 52.5349C71.4152 52.8638 73.3156 53.6587 74.9582 54.8556C76.6009 56.0526 77.9399 57.6182 78.8674 59.4267C81.045 63.4598 81.8433 68.0947 81.1403 72.6239C79.7766 81.9206 73.134 88.8125 65.8608 88.8125ZM67.7378 55.6436C65.0339 55.7284 62.4728 56.8772 60.6114 58.8402C57.8986 61.6329 56.1778 65.2391 55.7136 69.1047C55.1057 72.9342 55.7647 76.8576 57.5906 80.2783C58.2838 81.6521 59.2873 82.8457 60.5217 83.7645C61.7561 84.6833 63.1874 85.302 64.7024 85.5718C70.8171 86.4076 76.7411 80.4543 77.8995 72.2427C78.5196 68.4179 77.86 64.4952 76.0228 61.0837C75.3333 59.7071 74.3308 58.5113 73.0958 57.592C71.8607 56.6727 70.4273 56.0556 68.9108 55.7902C68.524 55.7121 68.132 55.6631 67.7378 55.6436Z" fill="#282828"/>
                                <path d="M45.9487 67.667C44.7316 76.2745 38.3675 82.5065 31.7543 81.5827C27.4872 80.9815 24.1586 77.5942 22.663 72.9752C21.7509 70.177 21.5049 67.2044 21.9445 64.2943C22.1523 62.7883 22.536 61.3118 23.0881 59.8953C23.7639 57.8933 24.8591 56.0586 26.3004 54.5136C27.7417 52.9686 29.4962 51.7489 31.4464 50.9359C32.939 50.359 34.5552 50.1772 36.1387 50.408C36.8863 50.5224 37.6191 50.7191 38.3235 50.9945C43.6757 53.0914 47.0338 60.0566 45.9487 67.667Z" fill="white"/>
                                <path d="M33.0287 83.2988C32.5378 83.2935 32.0479 83.2543 31.5624 83.1814C26.8114 82.5069 22.9256 78.885 21.166 73.4742C20.186 70.4378 19.92 67.2162 20.3888 64.0602C20.6153 62.425 21.0285 60.821 21.6204 59.2799C22.377 57.0593 23.6011 55.0268 25.2099 53.3194C26.8187 51.6119 28.7747 50.2693 30.9464 49.382H31.0785C32.7627 48.7742 34.57 48.588 36.3427 48.8395C37.2166 48.9639 38.0729 49.1904 38.8941 49.514C45.0381 51.9628 48.66 59.7785 47.5016 67.9314C46.2845 76.7002 39.9499 83.2988 33.0287 83.2988ZM31.8702 52.5054C30.1607 53.2522 28.6258 54.3474 27.3634 55.7209C26.101 57.0944 25.1389 58.7161 24.5385 60.4823C24.0225 61.7882 23.668 63.1523 23.4827 64.5442C23.0798 67.2125 23.3007 69.9378 24.128 72.5064C25.5064 76.7295 28.4097 79.545 31.9143 80.0435C37.6477 80.8354 43.2053 75.2045 44.2904 67.4769C45.2435 60.8196 42.3988 54.3823 37.6918 52.5054C37.107 52.2725 36.4967 52.11 35.8735 52.0214C34.5572 51.8299 33.2136 51.9814 31.973 52.4613L31.8702 52.5054Z" fill="#282828"/>
                                <path d="M45.949 67.6671C45.949 67.6671 49.3948 65.5115 54.1165 68.8255L45.949 67.6671Z" fill="white"/>
                                <path d="M54.1164 70.4381C53.7853 70.4403 53.4619 70.3377 53.1926 70.1449C49.4681 67.5348 46.8873 68.9865 46.7847 69.0451C46.4203 69.2619 45.9852 69.3265 45.5736 69.225C45.1619 69.1234 44.8069 68.8639 44.5851 68.5025C44.3657 68.1417 44.2966 67.7092 44.3925 67.2979C44.4885 66.8867 44.7419 66.5294 45.0983 66.303C45.2743 66.2003 49.4974 63.6196 55.0402 67.5054C55.2149 67.6256 55.364 67.7794 55.4786 67.9577C55.5933 68.1361 55.6712 68.3355 55.7081 68.5443C55.7449 68.7531 55.7399 68.9671 55.6932 69.1739C55.6465 69.3808 55.5591 69.5762 55.4362 69.749C55.2896 69.9632 55.0925 70.1381 54.8623 70.2583C54.6322 70.3785 54.376 70.4402 54.1164 70.4381Z" fill="#282828"/>
                                <path d="M48.9097 89.1782L56.4614 89.0461C56.4614 89.0461 59.9074 108.109 55.0978 109.575C50.2882 111.041 48.9097 89.1782 48.9097 89.1782Z" fill="white"/>
                                <path d="M54.6287 111.306C53.9727 111.299 53.334 111.095 52.7957 110.72C48.7633 107.963 47.5755 93.622 47.2969 89.2816C47.2843 89.0652 47.3155 88.8485 47.3885 88.6444C47.4616 88.4403 47.5752 88.2531 47.7222 88.0939C47.8704 87.9332 48.0492 87.8037 48.2481 87.7131C48.447 87.6224 48.6622 87.5724 48.8807 87.5659L56.4323 87.434C56.817 87.4211 57.1936 87.5475 57.4926 87.7899C57.7917 88.0324 57.9932 88.3746 58.0601 88.7537C59.1598 94.8977 61.1393 109.385 55.5818 111.16C55.272 111.25 54.9514 111.299 54.6287 111.306ZM50.6402 90.7772C51.3441 99.4874 53.177 107.523 54.6727 108.11C56.3297 107.552 56.535 99.4287 55.1127 90.6893L50.6402 90.7772Z" fill="#282828"/>
                                <path d="M72.7522 45.9945C72.7522 45.9945 79.4975 45.188 80.8759 49.3085L72.7522 45.9945Z" fill="white"/>
                                <path d="M80.8773 50.9212C80.5496 50.9104 80.2332 50.7991 79.9709 50.6024C79.7086 50.4056 79.5131 50.133 79.411 49.8214C78.4872 47.0647 73.0617 47.5779 73.003 47.5926C72.7891 47.6149 72.5729 47.5945 72.3669 47.5326C72.1609 47.4708 71.969 47.3688 71.8027 47.2325C71.6363 47.0961 71.4988 46.9281 71.3978 46.7383C71.2967 46.5484 71.2343 46.3404 71.2141 46.1263C71.1898 45.912 71.2088 45.695 71.2698 45.4881C71.3308 45.2813 71.4325 45.0887 71.5693 44.922C71.706 44.7552 71.8748 44.6175 72.0656 44.5171C72.2565 44.4166 72.4656 44.3555 72.6805 44.3373C73.0177 44.3373 80.7454 43.4281 82.5196 48.7363C82.6547 49.1408 82.624 49.5823 82.4344 49.9642C82.2448 50.3462 81.9118 50.6376 81.5079 50.7746C81.3096 50.8648 81.0952 50.9146 80.8773 50.9212Z" fill="#282828"/>
                                <path d="M49.717 40.9797C49.717 40.9797 43.7196 37.8857 41.2561 40.0999L49.717 40.9797Z" fill="white"/>
                                <path d="M49.7171 42.5924C49.4623 42.5919 49.2112 42.5317 48.9839 42.4164C46.8724 41.3313 43.3971 40.3489 42.3413 41.302C42.0215 41.5878 41.6019 41.7358 41.1735 41.7139C40.7452 41.6919 40.3428 41.5017 40.0539 41.1847C39.7699 40.863 39.6245 40.4422 39.6492 40.0138C39.6739 39.5854 39.8667 39.1841 40.1858 38.8972C43.3092 36.0965 49.2919 38.8972 50.4503 39.5424C50.8287 39.7397 51.1145 40.0776 51.2461 40.4835C51.3778 40.8894 51.3447 41.3308 51.1541 41.7126C51.0177 41.9764 50.8117 42.1978 50.5584 42.3529C50.3051 42.508 50.0141 42.5908 49.7171 42.5924Z" fill="#282828"/>
                                <path d="M62.2979 72.1837C63.5127 72.1837 64.4974 71.199 64.4974 69.9842C64.4974 68.7694 63.5127 67.7847 62.2979 67.7847C61.0832 67.7847 60.0984 68.7694 60.0984 69.9842C60.0984 71.199 61.0832 72.1837 62.2979 72.1837Z" fill="#282828"/>
                                <path d="M31.109 67.184C32.3237 67.184 33.3085 66.1992 33.3085 64.9844C33.3085 63.7697 32.3237 62.7849 31.109 62.7849C29.8942 62.7849 28.9094 63.7697 28.9094 64.9844C28.9094 66.1992 29.8942 67.184 31.109 67.184Z" fill="#282828"/>
                                <path d="M85.9917 90.0708C86.2217 88.443 83.8924 86.768 80.7891 86.3296C77.6858 85.8913 74.9837 86.8556 74.7537 88.4834C74.5238 90.1113 76.8531 91.7863 79.9564 92.2246C83.0597 92.663 85.7618 91.6987 85.9917 90.0708Z" fill="#F9D373"/>
                                <path d="M29.716 85.2053C29.4961 86.7743 26.9446 87.7421 23.9972 87.3901C23.7734 85.3992 23.6756 83.3961 23.704 81.3928C23.9533 81.3928 24.232 81.3928 24.5106 81.3928C27.546 81.906 29.9507 83.5777 29.716 85.2053Z" fill="#F9D373"/>
                                <path d="M33.0743 147.73C32.6742 147.733 32.2875 147.586 31.9893 147.32C29.0565 144.68 24.2469 139.885 24.3349 137.319C24.3414 136.99 24.421 136.667 24.5682 136.373C24.7153 136.079 24.9261 135.821 25.1854 135.618C27.7808 133.595 31.8866 136.381 32.6931 136.953C33.0343 137.204 33.2654 137.577 33.3382 137.994L34.6726 145.853C34.7264 146.185 34.6764 146.525 34.5294 146.827C34.3824 147.129 34.1456 147.378 33.8515 147.54C33.6128 147.668 33.3454 147.734 33.0743 147.73ZM27.9421 138.17C28.7553 139.341 29.6625 140.445 30.6548 141.469L30.2736 139.211C29.5533 138.748 28.7673 138.397 27.9421 138.17Z" fill="#282828"/>
                                <path d="M51.7249 175.753C51.4366 175.753 51.1535 175.677 50.9039 175.533C46.5286 172.914 42.4481 169.831 38.7331 166.339C33.3222 160.942 30.2282 138.932 29.9349 136.425C29.9071 136.143 29.9549 135.858 30.0735 135.6C30.192 135.343 30.377 135.121 30.6094 134.959C30.8542 134.789 31.1408 134.689 31.4382 134.671C31.7355 134.653 32.0322 134.717 32.2957 134.856C39.9501 138.947 45.8888 139.959 49.892 137.789C54.291 135.486 55.0536 130.076 55.127 129.46C55.1489 129.245 55.2129 129.037 55.3155 128.847C55.418 128.657 55.5571 128.489 55.7246 128.353C55.8922 128.216 56.085 128.115 56.2919 128.053C56.4988 127.991 56.7158 127.971 56.9306 127.993C57.1454 128.012 57.3544 128.073 57.5452 128.173C57.7361 128.274 57.9049 128.411 58.0416 128.578C58.1783 128.745 58.2802 128.937 58.3412 129.144C58.4023 129.351 58.4212 129.568 58.3969 129.782C58.2356 131.058 57.1798 137.598 51.4464 140.589C46.9594 142.921 40.9766 142.378 33.6155 138.962C34.8619 146.763 37.6626 160.561 41.0792 163.978C44.6051 167.29 48.4793 170.21 52.6341 172.688C52.9989 172.91 53.2617 173.267 53.366 173.681C53.4702 174.095 53.4075 174.534 53.1913 174.902C53.0512 175.167 52.8395 175.387 52.5802 175.538C52.3209 175.688 52.0245 175.763 51.7249 175.753Z" fill="#282828"/>
                                <path d="M97.5789 199.184H63.4275C63.0599 199.184 62.7033 199.059 62.4158 198.83C62.1282 198.601 61.9267 198.281 61.8439 197.923C61.7681 197.563 61.8151 197.189 61.9772 196.859C62.1394 196.53 62.4074 196.264 62.7383 196.105L65.583 194.785L64.1166 187.321C64.0342 186.902 64.1203 186.466 64.3563 186.109C64.5923 185.753 64.9593 185.503 65.3778 185.415C65.5849 185.372 65.7984 185.371 66.0059 185.412C66.2135 185.452 66.4109 185.533 66.5868 185.651C66.7627 185.768 66.9137 185.919 67.0309 186.095C67.148 186.271 67.229 186.468 67.2693 186.676L69.0584 195.371C69.0949 195.565 69.0949 195.764 69.0584 195.958H96.0098V187.805C96.0098 187.377 96.1799 186.967 96.4824 186.665C96.7849 186.362 97.1951 186.192 97.6229 186.192C97.8341 186.192 98.0431 186.234 98.238 186.315C98.4328 186.396 98.6097 186.515 98.7583 186.665C98.9069 186.815 99.0243 186.993 99.1038 187.189C99.1832 187.385 99.2231 187.594 99.2212 187.805V197.571C99.2233 197.786 99.182 197.999 99.0998 198.198C99.0177 198.396 98.8962 198.576 98.7429 198.727C98.5896 198.877 98.4075 198.995 98.2075 199.074C98.0075 199.152 97.7937 199.19 97.5789 199.184Z" fill="#282828"/>
                                <path d="M57.283 197.571L62.4005 194.052L61.3301 186.72L63.427 197.571H57.283Z" fill="white"/>
                                <path d="M63.4268 199.185H57.2829C56.9516 199.172 56.6324 199.057 56.3696 198.854C56.1069 198.652 55.9136 198.373 55.8165 198.056C55.7119 197.729 55.7153 197.377 55.8262 197.052C55.9371 196.727 56.1496 196.447 56.4323 196.252L60.7288 193.32L59.7903 186.985C59.7348 186.568 59.8428 186.146 60.0916 185.807C60.3403 185.468 60.7105 185.239 61.1247 185.167C61.5386 185.094 61.9644 185.187 62.3102 185.425C62.656 185.664 62.8939 186.03 62.9723 186.442L65.0838 197.293C65.1255 197.527 65.1171 197.767 65.0589 197.997C65.0008 198.227 64.8943 198.442 64.7466 198.628C64.5837 198.815 64.3801 198.963 64.1514 199.059C63.9227 199.156 63.6747 199.199 63.4268 199.185Z" fill="#282828"/>
                                <path d="M93.1808 164.593C93.1808 164.593 89.8522 164.872 89.6615 166.587C89.4709 168.303 94.0606 170.195 94.0606 170.195" fill="white"/>
                                <path d="M94.0896 171.808C93.8775 171.806 93.6678 171.761 93.4737 171.676C91.3328 170.796 87.7697 168.948 88.0629 166.412C88.3709 163.479 92.2714 163.054 93.0486 162.98C93.4743 162.944 93.8972 163.078 94.2244 163.352C94.5516 163.627 94.7562 164.021 94.7934 164.447C94.826 164.87 94.6903 165.289 94.4158 165.613C94.1412 165.937 93.75 166.14 93.3271 166.177C92.6589 166.238 92.0077 166.422 91.4063 166.719C92.3906 167.551 93.5053 168.215 94.7056 168.684C95.1005 168.85 95.4142 169.164 95.5789 169.56C95.7436 169.955 95.7459 170.399 95.5853 170.796C95.4621 171.092 95.2547 171.346 94.9889 171.526C94.7231 171.706 94.4105 171.804 94.0896 171.808Z" fill="#282828"/>
                                <path d="M87.0645 189.404C80.0847 189.404 72.0785 189.023 65.5386 188.612L62.6059 188.422L61.2714 188.334C56.3591 187.982 53.0599 187.674 53.0599 187.674C52.708 187.649 52.3741 187.509 52.1092 187.276C51.8444 187.043 51.6632 186.729 51.5935 186.383C50.4205 180.401 49.438 165.752 53.2652 148.801C53.2965 148.652 53.3509 148.508 53.4265 148.376C53.5625 148.105 53.7724 147.879 54.032 147.723C54.2916 147.567 54.5902 147.489 54.8928 147.496C55.3181 147.5 55.7246 147.671 56.0239 147.973C56.3233 148.275 56.4911 148.684 56.4911 149.109C56.493 149.293 56.4634 149.477 56.4032 149.651C53.7217 161.093 53.1057 172.922 54.5848 184.58C55.9631 184.697 58.4121 184.888 61.4914 185.108L62.8259 185.196L65.7586 185.386C76.023 186.046 89.748 186.618 97.4171 185.738C97.9743 185.738 98.5021 185.621 99.0153 185.533V177.879C99.0153 177.451 99.1852 177.041 99.4877 176.738C99.7902 176.436 100.2 176.266 100.628 176.266C101.056 176.266 101.466 176.436 101.769 176.738C102.071 177.041 102.241 177.451 102.241 177.879V186.882C102.243 187.249 102.118 187.604 101.889 187.89C101.659 188.176 101.338 188.374 100.98 188.451C99.9178 188.695 98.84 188.867 97.7543 188.964C94.2018 189.306 90.6331 189.453 87.0645 189.404Z" fill="#282828"/>
                                <path d="M70.2453 151.323H70.0987L59.8342 150.37C59.622 150.352 59.4156 150.291 59.2268 150.193C59.0381 150.094 58.8707 149.959 58.7344 149.796C58.5981 149.632 58.4956 149.443 58.4326 149.24C58.3697 149.036 58.3477 148.822 58.3678 148.61C58.4088 148.183 58.6165 147.79 58.946 147.515C59.2755 147.241 59.7001 147.107 60.1274 147.144L70.3919 148.097C70.8188 148.138 71.2124 148.346 71.487 148.675C71.7615 149.005 71.8949 149.429 71.8582 149.857C71.8183 150.257 71.6315 150.629 71.3336 150.9C71.0357 151.171 70.6479 151.321 70.2453 151.323Z" fill="#282828"/>
                                <path d="M70.2451 151.323C69.8415 151.325 69.452 151.175 69.1533 150.904C68.8547 150.632 68.6688 150.259 68.6321 149.857C68.6115 149.441 68.7526 149.033 69.0259 148.718C69.2992 148.404 69.6836 148.208 70.0985 148.17L87.6947 147.217C87.9032 147.199 88.1131 147.223 88.3122 147.287C88.5114 147.352 88.6956 147.455 88.8541 147.592C89.0126 147.729 89.1422 147.896 89.2352 148.083C89.3282 148.271 89.3828 148.475 89.3957 148.684C89.4163 149.1 89.2751 149.507 89.0017 149.822C88.7284 150.136 88.3442 150.332 87.9294 150.37L70.3331 151.323H70.2451Z" fill="#282828"/>
                                <path d="M65.2889 153.625C65.2889 153.625 63.1919 165.502 63.2213 170.194L65.2889 153.625Z" fill="white"/>
                                <path d="M63.2223 171.808C62.7982 171.804 62.3923 171.635 62.0911 171.337C61.7898 171.038 61.617 170.634 61.6094 170.21C61.6094 165.429 63.6184 153.831 63.7063 153.347C63.7815 152.927 64.0194 152.555 64.3683 152.31C64.7172 152.066 65.1488 151.969 65.5686 152.042C65.7772 152.077 65.9766 152.153 66.1554 152.266C66.3342 152.379 66.4886 152.527 66.61 152.7C66.7314 152.873 66.8172 153.069 66.8624 153.276C66.9077 153.482 66.9114 153.696 66.8735 153.904C66.8735 154.021 64.8061 165.635 64.8355 170.18C64.8374 170.392 64.7975 170.602 64.7182 170.799C64.6389 170.995 64.5217 171.174 64.3733 171.325C64.2249 171.476 64.0483 171.597 63.8533 171.68C63.6583 171.763 63.4488 171.806 63.237 171.808H63.2223Z" fill="#282828"/>
                                <path d="M73.634 153.625C73.634 153.625 77.2266 167.13 77.124 175.62L73.634 153.625Z" fill="white"/>
                                <path d="M77.123 177.175C76.9118 177.173 76.703 177.129 76.5088 177.046C76.3147 176.963 76.1389 176.842 75.9917 176.691C75.8444 176.54 75.7287 176.36 75.6511 176.164C75.5734 175.968 75.5353 175.758 75.5392 175.547C75.6418 167.423 72.1373 154.167 72.108 154.035C72.0528 153.831 72.0385 153.618 72.0663 153.409C72.094 153.199 72.1631 152.997 72.2694 152.814C72.3758 152.631 72.5175 152.472 72.6861 152.344C72.8547 152.216 73.047 152.123 73.2518 152.071C73.4559 152.015 73.669 152.001 73.8786 152.029C74.0883 152.057 74.2903 152.126 74.4731 152.232C74.6558 152.338 74.8157 152.48 74.9433 152.649C75.071 152.817 75.1638 153.009 75.2166 153.214C75.3633 153.771 78.8679 167.027 78.7506 175.576C78.7506 175.789 78.7083 175.999 78.6262 176.195C78.544 176.391 78.4237 176.568 78.2721 176.717C78.1206 176.866 77.9409 176.983 77.7436 177.061C77.5462 177.14 77.3353 177.178 77.123 177.175Z" fill="#282828"/>
                                <path d="M26.1814 138.507C25.9133 138.506 25.6496 138.439 25.414 138.311C25.1785 138.183 24.9787 137.998 24.8325 137.773L0.271051 100.601C0.153216 100.426 0.0714179 100.228 0.0304774 100.021C-0.010463 99.813 -0.00984041 99.5993 0.0324464 99.392C0.0747332 99.1846 0.157806 98.9877 0.276779 98.8127C0.395752 98.6377 0.548273 98.4882 0.725527 98.3726C0.901294 98.2547 1.09876 98.1729 1.30638 98.132C1.51399 98.0911 1.72768 98.0918 1.93502 98.1341C2.14236 98.1763 2.33926 98.2593 2.51426 98.3783C2.68925 98.4973 2.83883 98.6499 2.95441 98.8271L27.5305 135.999C27.7616 136.359 27.8434 136.794 27.7585 137.213C27.6737 137.632 27.4288 138.001 27.076 138.243C26.8089 138.414 26.4986 138.505 26.1814 138.507Z" fill="#282828"/>
                                <path d="M97.3281 179.858C96.4579 179.868 95.5888 179.794 94.7326 179.638C94.3884 179.568 94.077 179.386 93.8464 179.121C93.6158 178.856 93.4788 178.522 93.4569 178.172L91.9905 162.042C91.9569 161.688 92.0426 161.332 92.2338 161.032C92.4251 160.732 92.7111 160.504 93.0465 160.385C95.29 159.564 96.8882 154.974 97.3575 152.276C97.4327 151.856 97.6701 151.482 98.0185 151.235C98.3668 150.988 98.7983 150.888 99.2197 150.956C99.4289 150.993 99.6287 151.071 99.8076 151.186C99.9865 151.3 100.141 151.449 100.262 151.624C100.383 151.798 100.469 151.995 100.514 152.202C100.559 152.41 100.563 152.624 100.525 152.833C100.393 153.654 99.0584 160.385 95.2898 162.848L96.507 176.618C97.7382 176.694 98.9739 176.585 100.173 176.295C106.434 174.726 108.971 168.083 107.387 157.115C105.716 145.384 95.4512 131.117 95.3339 130.97C95.0864 130.623 94.9861 130.193 95.0548 129.773C95.1235 129.352 95.3556 128.976 95.7005 128.726C95.8705 128.601 96.0637 128.511 96.2688 128.461C96.4739 128.411 96.687 128.402 96.8956 128.435C97.1042 128.467 97.3042 128.541 97.4842 128.652C97.6641 128.762 97.8205 128.907 97.944 129.078C98.3986 129.694 108.795 144.138 110.569 156.66C112.974 173.743 105.584 178.26 100.95 179.433C99.7631 179.713 98.5477 179.856 97.3281 179.858Z" fill="#282828"/>
                            </svg>

                        </div>
                    </div>
                </div>
            </div>
            <div class="teacher-detail__screen--info">
                <div class="teacher-detail__screen--info-content">
{{--                    <div class="teacher-detail__screen--info-fullname">--}}
{{--                        <div class="teacher-detail__screen--info-biography">--}}
{{--                            <div class="teacher-name" style="font-size: 2vw; line-height: 3.4vw; text-transform: uppercase; text-align: center; border-bottom: 2px solid #A1C93D">--}}
{{--                                {{ $author->name }}--}}
{{--                            </div>--}}
{{--                            <div class="teacher-detail__screen--info-biography-content">--}}
{{--                                {!! $author->introduction !!}--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="teacher-detail__screen--info-avatar">--}}
{{--                            <img src="{{asset('cdn/teacher_detail/default/') .'/'. $author->avartar_detail}}" />--}}
{{--                        </div>--}}
{{--                    </div>--}}
                    <div class="teacher-detail__screen--info-description">
                        {!! $author->description !!}
                    </div>
                </div>
            </div>
            {{--        <div class="teacher-detail__screen--video">--}}
            {{--            <div class="teacher-detail__screen--video-header">--}}
            {{--                <div class="teacher-detail__screen--video-title"><span>Lời chào giảng viên</span></div>--}}
            {{--                <div class="teacher-detail__screen--separate"></div>--}}
            {{--            </div>--}}

            {{--            <div class="teacher-detail__screen--video-content">--}}
            {{--                <div class="teacher-detail__screen--video-image" id="teacher-detail__screen--video-image" style="position: relative">--}}
            {{--                    <img src="https://img.youtube.com/vi/{{ explode("https://www.youtube.com/watch?v=",$author->youtube_intro)[1] }}/hqdefault.jpg"/>--}}
            {{--                    <div--}}
            {{--                            @click="playVideo = true"--}}
            {{--                            class="flex justify-center items-center teacher-detail__screen--video-image-mask"--}}
            {{--                    >--}}
            {{--                        <span class="play-icon-btn"> <i class="zmdi zmdi-play zmdi-hc-5x"></i> </span>--}}
            {{--                    </div>--}}
            {{--                </div>--}}
            {{--                <modal v-if="playVideo" @close="closeVideo">--}}
            {{--                    --}}{{--            <h3 slot="header">Giới thiệu</h3>--}}
            {{--                    <div slot="body">--}}
            {{--                        <iframe width="100%" height="700px" src="https://www.youtube.com/embed/{{ explode("https://www.youtube.com/watch?v=",$author->youtube_intro)[1] }}?autoplay=1" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>--}}
            {{--                    </div>--}}
            {{--                </modal>--}}
            {{--            </div>--}}
            {{--        </div>--}}
            @if ($author->type == 3)
                <div class="teacher-detail__screen--story">
                    <div class="teacher-detail__screen--story-header">
                        <span>Hoạt động tiêu biểu</span>
                        <span class="teacher-detail__screen--separate"></span>
                    </div>

                    <div class="teacher-detail__screen--timeline-content">
                        {!! $author->story_description !!}
                    </div>
                </div>
            @endif
        </div>
    </div>
@stop

@section('footer-js')
    <link rel="stylesheet" href="{{asset('plugin/owlcarousel2/dist/assets/owl.carousel.min.css')}}"/>
    <link rel="stylesheet" href="{{asset('plugin/owlcarousel2/dist/assets/owl.theme.default.min.css')}}"/>
    <script src="{{asset('plugin/owlcarousel2/dist/owl.carousel.min.js')}}"></script>
    <script src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}"></script>
    <script> $(".giao-vien").addClass("active");  </script>

    <script type="text/javascript">
        var teacherComponent = new Vue({
            el: '#teacher-detail__screen--wrapper',
            data: function () {
                return {
                    playVideo: false
                }
            },
            methods: {
                closeVideo: function () {
                    var vm = this;
                    vm.playVideo = false;
                }
            },
            mounted: function () {

            }
        });
    </script>
@stop
