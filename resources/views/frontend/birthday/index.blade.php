@extends('frontend._layouts.default')

@section('title')
    DUNGMORI - <PERSON><PERSON><PERSON> mừng sinh nhật nền tảng học tiếng Nhật Online Số 1 tại Việt Nam
@stop
@section('description')
    Hơn 300,000 học viên đã tin tưởng theo học khóa tiếng Nhật online và offline của DUNGMORI, chuyên sâu về luyện thi JLPT, Kaiwa, EJU và tiếng Nhật doanh nghiệp.
@stop
@section('keywords')
    DUNGMORI, dạy, tiếng nhật, học, online, d<PERSON> hi<PERSON>, nihongo, Japanese, miễn phí, n1, n2, n3, n4, n5
@stop
@section('image')
    {{url('assets/img/oglogo.png')}}
@stop
@section('author')
    DUNGMORI
@stop
@section('header-css')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
@stop
@section('header-js')

    <script type="application/ld+json">
        {
          "@context": "http://schema.org",
          "@type": "Organization",
          "name": "DUNGMORI",
          "url": "https://dungmori.com",
          "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
        {
           "@context": "http://schema.org",
           "@type": "WebSite",
           "name": "DUNGMORI",
           "alternateName": "DUNGMORI - Website học tiếng Nhật online số 1 tại Việt Nam",
           "url": "https://dungmori.com"
         }
    </script>
    
@stop

@section('content')
         <div class="w-screen overflow-x-hidden relative pb-[190px] lg:pb-[770px] bg-[#F4F5FA]" id="birthday">
            <img src="{{ asset('assets/img/birthday/gateux-sinh-nhat-dungmori.png') }}" alt="" class="hidden xl:block w-[30vw] absolute top-[50%] right-[30px] z-[1]">
            <img src="{{ asset('assets/img/birthday/trai-tim-sinh-nhat-dungmori.png') }}" alt="" class="hidden xl:block w-[30vw] absolute top-[60%] left-[-75px] z-[1]">
            <img src="{{ asset('assets/img/birthday/thu-sinh-nhat-dungmori.png') }}" alt="" class="hidden xl:block w-[30vw] absolute top-[70%] right-[-10vw] z-[1]">
            <img @click="window.scrollTo({ top: 0, behavior: 'smooth' })" src="{{ asset('assets/img/birthday/ve-dau-trang-sinh-nhat-dungmori.png') }}" alt="" class="hidden lg:block fixed bottom-[20px] right-[120px] z-[99] cursor-pointer">
            <!-- <div v-if="currentWish?.id" v-cloak class="flex flex-col mb-[30px] lg:mb-[270px] w-full max-w-[870px] px-6 lg:px-2 mx-auto pt-8 lg:pt-[48px] font-averta-semibold text-[14px] lg:text-[20px] text-[#0C403F] text-left lg:text-justify">
                <div>Cảm ơn lời chúc chứa đầy tình yêu bạn dành cho Dungmori! Chúng mình cần một vài thông tin để thuận tiện cho việc gửi phần quà tới tay bạn. Bạn vui lòng làm theo các bước dưới đây nha:</div>
                <ul>
                    <li class="list-disc list-inside ml-5">Bước 1: <span class="font-averta-regular">Tải ảnh lời chúc bạn gửi tới Dũng Mori bên dưới, Share lên Facebook cá nhân để chế độ công khai kèm hashtag </span><span class="text-[#4E87FF]">#dungmori11tuoi</span> <span class="font-averta-regular">và tag Fanpage Dũng Mori</span></li>
                    <li class="list-disc list-inside ml-5">Bước 2: <span class="font-averta-regular">Truy cập form sau đây: <a href="https://forms.gle/PaaantzStBtZvYHYA" target="_blank" class="font-averta-semibold inline-flex px-1 rounded-md bg-[#CEFFD8] underline cursor-pointer">Form thông tin nhận quà </a> điền đầy đủ thông tin để nhận quà nha!</span></li>
                </ul>
                <div class="mt-3">
                    Bài viết chỉ được tính hợp lệ khi:
                    <ul>
                        <li class="list-disc list-inside ml-5">Gắn hashtag <span class="text-[#4E87FF]">#dungmori11tuoi</span></li>
                        <li class="list-disc list-inside ml-5">Tag kênh chính thức của Dungmori trên nền tảng tương ứng</li>
                        <li class="list-disc list-inside ml-5">Điền đầy đủ Google Form thông tin nhận quà</li>
                        <li class="list-disc list-inside ml-5">Quyết định cuối cùng về việc trao giải thuộc về Ban Tổ Chức.</li>
                    </ul>
                </div>

                <div class="text-[#EF6D13] mt-3">*Thời hạn tham dự: 1/6/2025 - 11/6/2025</div>
                <div class="w-full lg:w-[610px] mx-auto ">
                    <div id="bdImage" class="mt-5 lg:mt-[70px] rounded-[32px] shadow-[0px_0px_0.47px_0px_#4C5D7052,_0px_7.53px_11.29px_0px_#617C9A52] lg:shadow-[0px_0px_1px_0px_#4C5D7052,_0px_16px_24px_0px_#617C9A52] w-full md:w-[610px] mx-auto overflow-hidden relative">
                        <img src="https://dungmori.com/assets/img/birthday/bai-share-sinh-nhat-dungmori.png" alt="" v-cloak class="w-full object-cover"/>
                        <div class="absolute w-[73%] h-[45%] top-[29%] left-[13%] overflow-hidden rotate-[-2deg] text-[#07403F] text-[3vw] md:text-[20px] text-wrap text-justify font-gen-jyuu-gothic-bold leading-[1.5] text-ellipsis">@{{ currentWish.wish }}</div>
                        <div class="absolute bottom-[16%] right-[12.8%] rotate-[-2deg] text-[#07403F] text-[3vw] md:text-[20px] text-wrap text-right font-gen-jyuu-gothic-bold leading-[1.5] text-ellipsis">@{{ currentWish.name }}</div>
                    </div>
                    <div class="w-full mt-8 lg:mt-10 mb-5 flex justify-between gap-4">
                        <div @click="downloadDivAsImage()" class="w-1/2 py-[14px] lg:py-[18px] text-[16px] lg:text-[20px] leading-none cursor-pointer hover:brightness-110 rounded-full flex justify-center items-center font-beanbag-bold text-[#07403F] bg-[#CEFFD8]">Tải xuống</div>
                        <div @click=shareToFacebook() class="w-1/2 py-[14px] lg:py-[18px] text-[16px] lg:text-[20px] leading-none cursor-pointer hover:brightness-110 rounded-full flex justify-center items-center font-beanbag-bold text-white bg-[#4E87FF] gap-3">
                            <svg width="11" height="16" viewBox="0 0 11 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M1.20579 6.80547C0.484112 6.80547 0.333496 6.94297 0.333496 7.60177V8.79622C0.333496 9.45502 0.484112 9.59251 1.20579 9.59251H2.95038V14.3703C2.95038 15.0291 3.101 15.1666 3.82267 15.1666H5.56726C6.28894 15.1666 6.43956 15.0291 6.43956 14.3703V9.59251H8.39847C8.9458 9.59251 9.08684 9.4954 9.2372 9.01497L9.61104 7.82053C9.86862 6.99756 9.7099 6.80547 8.77231 6.80547H6.43956V4.81473C6.43956 4.37495 6.8301 4.01844 7.31185 4.01844H9.79454C10.5162 4.01844 10.6668 3.88094 10.6668 3.22214V1.62955C10.6668 0.970746 10.5162 0.833252 9.79454 0.833252H7.31185C4.90308 0.833252 2.95038 2.61582 2.95038 4.81473V6.80547H1.20579Z" fill="#FFF8F8"/>
                            </svg>
                            Chia sẻ
                        </div>
                    </div>
                    <div @click="currentWish = null" class="py-[18px] w-full rounded-full border border-[#57D061] text-[16px] lg:text-[20px] cursor-pointer hover:brightness-110 font-beanbag-bold text-[#07403F] bg-white leading-none flex justify-center items-center">Gửi thêm lời chúc khác</div>
                </div>
                
            </div> -->
            <div v-if="!currentWish?.id" class="w-full h-auto lg:h-[800px] relative overflow-visible lg:overflow-hidden pt-[50px]" v-cloak>
                <img src="{{ asset('assets/img/birthday/hinh-nen-sinh-nhat-dungmori.png') }}" alt="" class="w-full mx-auto absolute top-0 left-0 z-[1] lg:static">
                <div class="w-full static lg:absolute top-[40px] lg:top-[150px]">
                    <div class="w-full lg:w-[1100px] mx-auto flex flex-col items-center px-[28px]">
                        <img src="{{ asset('assets/img/birthday/giai-thuong-sinh-nhat-dungmori.png') }}" alt="" class="w-[85%] mx-auto z-[2]">
                        {{-- <div class="w-full bg-white shadow-[0px_0px_1px_0px_#4C5D7052,_0px_16px_24px_0px_#617C9A52] rounded-[20px] lg:rounded-[32px] pt-[34px] pb-[16px] lg:py-8 px-[21px] lg:px-9 mt-[-13px] z-[1]">
                            <h6 class="hidden xl:block font-beanbag-medium text-[#07403F] italic text-[16px] leading-[1.4]">
                                Đừng bỏ lỡ cơ hội nhận quà tặng ý nghĩa từ chương trình Triệu Lời Tri Ân – Tặng Quà Gắn Kết! <br>
                                Gửi lời chúc sinh nhật dành tặng Dungmori nhân dịp kỷ niệm 11 năm ngay tại đây
                            </h6>
                            <div class="block xl:hidden w-full font-pacifico text-[#EF6D13] text-[17px]">
                                Triệu Lời Tri Ân - Tặng Quà Gắn Kết 
                            </div>
                            <h6 class="block xl:hidden font-beanbag-medium text-[#07403F] italic text-[13px] leading-[1.4]">
                                Nhân dịp kỷ niệm Dungmori 11 năm, gửi lời chúc mừng sinh nhật - Nhận quà tặng ý nghĩa tại đây!
                            </h6>
                            <div class="space-y-5">
                                <div class="bg-gray-50 p-4 rounded-[20px]">
                                    <input
                                        type="text"
                                        placeholder="Tên của mình là"
                                        class="text-[14px] lg:text-[20px] font-beanbag-medium text-[#07403F] w-full bg-transparent outline-none placeholder-[#B3B3B3]"
                                        v-model="name"
                                        @click="checkAuth()"
                                    />
                                </div>

                                <div class="bg-gray-50 p-4 relative rounded-[20px]">
                                    <textarea
                                        placeholder="Mình chúc Dũng Mori..."
                                        :maxLength="maxCharacters"
                                        rows="4"
                                        class="text-[14px] lg:text-[20px] font-beanbag-medium text-[#07403F] w-full bg-transparent outline-none resize-none placeholder-[#B3B3B3]"
                                        v-model="message"
                                        @click="checkAuth()"
                                    ></textarea>
                                    <div v-cloak class="text-[#9A9A9A] text-right mt-2 font-averta-regular text-[14px] lg:text-[20px]">
                                        @{{ message.length }}/@{{ maxCharacters }} ký tự
                                    </div>
                                </div>
                                <div
                                    class="font-beanbag-bold text-[16px] lg:text-[20px] text-center px-0 lg:px-[80px] py-[12px] lg:py-[20px] rounded-full w-full lg:w-[430px] max-w-screen mx-auto mt-6 cursor-pointer leading-none"
                                    :class="[!validated ? 'cursor-not-allowed text-[#757575] bg-[#E3E3E3]' : 'cursor-pointer text-[#07403F] bg-[#CEFFD8] shadow-[0px_0px_1px_0px_#4C5D704D,_0px_4px_8px_0px_#4C5D704D]']"
                                    @click="sendBirthdayMessage()"
                                >
                                    Gửi lời chúc tới Dũng Mori
                                </div>
                            </div>
                        </div> --}}
                    </div>
                </div>
            </div>

            <div class="w-full text-center font-zuume-semibold text-[32px] lg:text-[64px] text-[#176867] mt-5 lg:mt-[50px]">Lời chúc từ trái tim tới trái tim</div>
            <div v-if="wishes.length" v-cloak class="relative px-5 xl:px-0 w-full lg:w-[1320px] mx-auto columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6 mt-6 lg:mt-[69px] z-[3]">
                <div v-for="wish in wishes" :key="`wish-${wish.id}`" class="transition-all break-inside-avoid overflow-hidden rounded-[32px] lg:rounded-[40px] p-[30px] lg:p-10 bg-white shadow-[0px_0px_1px_0px_#4C5D704D,_0px_2px_4px_0px_#4C5D703D] hover:bg-[#CDFFD7] hover:shadow-[0px_0px_1px_0px_#4C5D7052,_0px_16px_24px_0px_#617C9A52] z-[3]">
                    <div class="flex items-center gap-3 mb-8 lg:mb-4 z-[3]">
                        <div v-cloak class="w-[40px] h-[40px] lg:w-[50px] lg:h-[50px] bg-gray-300 rounded-full overflow-hidden object-cover flex-shrink-0">
                            <img v-if="!wish.user?.avatar" class="user-avatar" :src="url + '/assets/img/default-avatar.jpg'">
                            <img v-else class="user-avatar" :src="url + '/cdn/avatar/default/'+ wish.user?.avatar">
                        </div>
                        <div>
                        <h3 v-cloak class="font-medium font-beanbag-medium text-[#176867] text-[16px] lg:text-[20px] leading-none">@{{ wish.name }}</h3>
                        <p v-cloak class="font-averta-regular text-[#757575] text-[10px] lg:text-[14px] leading-none mt-2">@{{ moment(wish.created_at).format('DD/MM/YYYY HH:mm') }}</p>
                        </div>
                    </div>
                    <div v-cloak class="font-bold leading-[1.5] !font-gen-jyuu-gothic-bold text-[#176867] text-[16px] lg:text-[20px] lg:leading-[2] whitespace-pre-wrap">@{{ wish.wish }}</div>
                </div>
            </div>

            <div class="absolute bottom-0 w-full">
                <img src="{{ asset('assets/img/birthday/banh-sinh-nhat-dungmori.png') }}" alt="" class="w-full mx-auto">
            </div>

         </div>
    <script>
        window.fbAsyncInit = function () {
            FB.init({
                appId: '1768213996826394',
                xfbml: true,
                version: 'v16.0'
            });
            FB.AppEvents.logPageView();
        };

        var isMobile = screen.width < 768;

        if (isMobile) {
            $('.yt-social-item').css({display: 'none'});
        }

        (function (d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) {
                return;
            }
            js = d.createElement(s);
            js.id = id;
            js.src = "https://connect.facebook.net/en_US/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));
    </script>
@stop
@section('fixed-panel')

@stop

@section('footer-js')
    <script>
        var userWishes = @json($userWishes);
        var otherWishes = @json($otherWishes);
        var latestWish = @json($latestWish);
    </script>
   <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js"></script>
   <script src="{{asset('plugin/jquery/axios.min.js')}}"></script>
   <script src="{{asset('plugin/moment/moment.min.js')}}?{{filemtime('plugin/moment/moment.min.js')}}" type="text/javascript">
        moment.locale('vi')
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
   <script>
        var duration = 50 * 1000;
        var animationEnd = Date.now() + duration;
        var defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

        function randomInRange(min, max) {
            return Math.random() * (max - min) + min;
        }

        var interval = setInterval(function() {
            var timeLeft = animationEnd - Date.now();

            if (timeLeft <= 0) {
                return clearInterval(interval);
            }

            var particleCount = 50 * (timeLeft / duration);
            // since particles fall down, start a bit higher than random
            confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 } });
            confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 } });
            confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 } });
            confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 } });
        }, 1500);
   </script>
   <script>
        new Vue({
            el: '#birthday',
            data: {
                name: `{{ auth()->check() ? auth()->user()->name : '' }}`,
                message: '',
                maxCharacters: 280,
                token: '{{ csrf_token() }}',
                wishes: [],
                url: window.location.origin,
                moment: moment,
                currentWish: latestWish,
                auth: `{{ auth()->check() ? 'true' : 'false' }}`
            },
            computed: {
                validated() {
                    return this.name.length > 0 && this.message.length > 0 && this.message.length <= this.maxCharacters;
                }
            },
            mounted() {
                this.wishes = [...userWishes, ...otherWishes];
                // console.log(this.currentWish);
                this.name = this.currentWish?.name ? this.currentWish.name : this.name;
            },
            methods: {
                checkAuth() {
                    if (this.auth === 'false') {
                        $('#text-login').click();
                    }
                },
                sendBirthdayMessage() {
                    var that = this;
                    // Handle sending the birthday message
                    that.checkAuth();
                    if (this.validated) {
                        axios.post('/chuc-mung-sinh-nhat', {
                            name: this.name,
                            wish: this.message,
                            _token: this.token
                        })
                        .then(response => {
                            console.log(response);
                            if (response.data.code === 200) {
                                // that.name = '';
                                that.message = '';
                                that.wishes.unshift(response.data.data);
                                that.currentWish = response.data.data;

                                const today = new Date().toISOString().split("T")[0];
                                localStorage.setItem("modal-bd-last-opened", today);
                            } else {
                                alert('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                            }
                        })
                        .catch(error => {
                            if (error.response && error.response.status === 403) {
                                alert(error.response.data.message || 'Bạn cần đăng nhập để gửi lời chúc.');
                            } else {
                                console.error(error);
                            }
                            alert('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                        });
                    } else {
                        alert('Vui lòng điền đầy đủ thông tin trước khi gửi.');
                    }
                },
                downloadDivAsImage() {
                    const element = document.getElementById('bdImage');
    
                    html2canvas(element, {
                        scale: 2, // Higher quality
                        useCORS: true, // Handle cross-origin images
                        allowTaint: false
                    }).then(canvas => {
                        const imgData = canvas.toDataURL('image/png');
                        const link = document.createElement('a');
                        link.href = imgData;
                        link.download = 'chuc-mung-sinh-nhat-dungmori.png';
                        link.click();
                    }).catch(error => {
                        console.error('Error generating image:', error);
                    });
                },
                async downloadImageAsJpeg(url, filename = 'image.jpg') {
                    try {
                        // Fetch the image
                        const response = await fetch(url);
                        if (!response.ok) throw new Error('Failed to fetch image');
                        const blob = await response.blob();

                        // Create a canvas to convert the image to JPEG
                        const img = new Image();
                        img.crossOrigin = 'Anonymous'; // Needed for cross-origin images, if applicable
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Wait for the image to load
                        await new Promise((resolve, reject) => {
                            img.onload = resolve;
                            img.onerror = reject;
                            img.src = URL.createObjectURL(blob);
                        });

                        // Set canvas size to match image
                        canvas.width = img.width;
                        canvas.height = img.height;

                        // Fill with white background (since JPEG doesn't support transparency)
                        ctx.fillStyle = '#FFFFFF'; // White background
                        ctx.fillRect(0, 0, canvas.width, canvas.height);

                        // Draw the image on the canvas
                        ctx.drawImage(img, 0, 0);

                        // Convert to JPEG data URL
                        const jpegDataUrl = canvas.toDataURL('image/jpeg', 0.95); // 0.95 is quality (0 to 1)

                        // Create a temporary link to trigger download
                        const link = document.createElement('a');
                        link.href = jpegDataUrl;
                        link.download = filename;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // Clean up
                        URL.revokeObjectURL(img.src);
                    } catch (error) {
                        console.error('Error downloading image:', error);
                    }
                },
                shareToFacebook() {
                    let vm = this;
                    const hashtag = 'dungmori11tuoi';
                    const u = window.location.origin + '/chuc-mung-sinh-nhat/loi-chuc/' + this.currentWish.id;
                    const url = 'https://www.facebook.com/sharer.php?hashtag=%23' + hashtag;
                    vm.PopupCenter(url,'Share Facebook','610','610');
                },
                PopupCenter: function (url, title, w, h) {
                    // Fixes dual-screen position                         Most browsers      Firefox
                    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : screen.left;
                    var dualScreenTop = window.screenTop != undefined ? window.screenTop : screen.top;

                    width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
                    height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

                    var left = ((width / 2) - (w / 2)) + dualScreenLeft;
                    var top = ((height / 2) - (h / 2)) + dualScreenTop;
                    var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

                    // Puts focus on the newWindow
                    if (window.focus) {
                        newWindow.focus();
                    }
                },
            }
        });
   </script>
@stop
