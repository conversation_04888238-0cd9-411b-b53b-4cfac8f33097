@extends('frontend.jlpt._app')

@section('title') Bảng xếp hạng thi thử JLPT Online trên Dungmori @stop
@section('description')Bảng xếp hạng thi thử JLPT Online trên Dungmori @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <style>
        .main,
        .exam-container-menu {
            /*background: #96D962 !important;*/
        }
        .lang-btn,
        .dropdown {
            background: white;
        }
        .title-exam-container {
            background: url("/assets/img/jlpt/caro.png") #96D962;
            background-repeat: repeat;
            padding: 30px 5px;
        }
        .title-text {
            color: white;
        }
    </style>
@stop

@section('header-js')
@stop


@section('content')
<div class="main">
    <div class="main-center">
        @include('frontend.jlpt.common-title')
        <div class="text-center font-quicksand font-semibold text-lg -mt-3"><PERSON>hi thử JLPT online miễn phí</div>
        <div class="main-ranking-menu">
            <div class="exam-container-menu">
                <a href="{{url('/thi-thu/ranking')}}" class="item-exam ranking active">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_75_3009)"><path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2327 23.0904V11.5452C41.2327 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" fill="#96D962"></path> <path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2328 23.0904V11.5452C41.2328 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7917 46.1807H32.9862" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M26.3889 37.9341V46.1806" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M40.8616 26.3889H42.882C44.6317 26.3889 46.3097 25.6939 47.5469 24.4566C48.7842 23.2194 49.4792 21.5414 49.4792 19.7917V16.4931C49.4792 16.0556 49.3055 15.6361 48.9961 15.3268C48.6868 15.0175 48.2673 14.8438 47.8299 14.8438H41.2327" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M11.9576 26.3889H9.87532C8.12562 26.3889 6.44759 25.6939 5.21036 24.4566C3.97314 23.2194 3.27808 21.5414 3.27808 19.7917V16.4931C3.27808 16.0556 3.45184 15.6361 3.76115 15.3268C4.07045 15.0175 4.48996 14.8438 4.92739 14.8438H11.5246" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_75_3009"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.ranking')</span>
                </a>
                <a href="{{url('/thi-thu')}}" class="item-exam room">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_77_3339)"><path d="M41.3369 45.5834H11.6493C11.2119 45.5834 10.7924 45.4097 10.4831 45.1004C10.1738 44.7911 10 44.3716 10 43.9341V7.64931C10 7.21189 10.1738 6.79238 10.4831 6.48307C10.7924 6.17377 11.2119 6 11.6493 6H31.441L42.9862 17.5452V43.9341C42.9862 44.3716 42.8124 44.7911 42.5031 45.1004C42.1938 45.4097 41.7743 45.5834 41.3369 45.5834Z" fill="#F6F6F6"></path> <path d="M31.3369 6.59717V18.1423H42.8821L31.3369 6.59717Z" fill="white"></path> <path d="M41.2329 46.1806H11.5453C11.1079 46.1806 10.6884 46.0068 10.3791 45.6975C10.0698 45.3882 9.896 44.9687 9.896 44.5313V8.24648C9.896 7.80905 10.0698 7.38955 10.3791 7.08024C10.6884 6.77093 11.1079 6.59717 11.5453 6.59717H31.337L42.8822 18.1423V44.5313C42.8822 44.9687 42.7084 45.3882 42.3991 45.6975C42.0898 46.0068 41.6703 46.1806 41.2329 46.1806Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M31.3369 6.59717V18.1423H42.8821" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7915 28.0382H32.986" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7915 34.6355H32.986" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_77_3339"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.jlpt')</span>
                </a>
                <a href="{{url('/thi-thu/history')}}" class="item-exam history">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_75_3017)"><path d="M26.3888 44.5313C36.4085 44.5313 44.5312 36.4087 44.5312 26.3889C44.5312 16.3691 36.4085 8.24646 26.3888 8.24646C16.369 8.24646 8.24634 16.3691 8.24634 26.3889C8.24634 36.4087 16.369 44.5313 26.3888 44.5313Z" fill="#F2F2F2"></path> <path d="M26.3889 16.493V26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M34.9653 31.3368L26.3889 26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M14.8027 20.5545H6.55615V12.308" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M13.5657 39.2123C16.1024 41.751 19.335 43.4804 22.8547 44.1816C26.3744 44.8828 30.023 44.5244 33.339 43.1517C36.6549 41.779 39.4892 39.4536 41.4834 36.4698C43.4776 33.486 44.542 29.9777 44.542 26.3889C44.542 22.8 43.4776 19.2918 41.4834 16.308C39.4892 13.3241 36.6549 10.9988 33.339 9.62606C30.023 8.25334 26.3744 7.89492 22.8547 8.59615C19.335 9.29738 16.1024 11.0268 13.5657 13.5655L6.55615 20.5544" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_75_3017"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.my_score')</span>
                </a>
            </div>
            @include('frontend.jlpt._lang')
        </div>
        <a href="https://m.me/1595926847401625?ref=meo-thi-2025" target="_blank">
         <img style="width: 1150px; margin: 20px 0 0 -50px; max-width: 1150px;" src="{{url('assets/img/jlpt/thi_thu_left_11_2024.jpg')}}"/>
        </a>
        <div class="main-ranking-right">
            <div id="main-ranking-right" v-cloak>
                <div class="title-exam-container">
                    <div class="title-exam-cover">
                        <div class="title-text" style="width: 340px;">
                            @if(Auth::check())

                                <div v-if="myRank > 0">
                                    <i class="fa fa-trophy"></i> @lang('jlpt.ranking.me') <span>#@{{ myRank }}</span>
                                </div>
                                <div v-if="myRank == 0 && ranking != null">
                                    <span><i class="fa fa-trophy"></i> @lang('jlpt.ranking.notMe')</span>
                                </div>
                            @else
                                <i class="fa fa-trophy"></i> Bảng xếp hạng
                            @endif
                        </div>

                        <div v-if="isExamNull == false" style="border-bottom: 0px;">
                            <div class="filter-container" style="float: left;">
                                <div class="dropdown show" style="font-size: 14px; border: none;">
                                    <a class="btn btn-secondary dropdown-toggle drop-name" href="#" role="button"
                                       id="levelDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-file-text-o" aria-hidden="true"></i> @{{exams}}
                                        <span class="caret"></span>
                                    </a>
                                    <div class="dropdown-menu user-menu" aria-labelledby="levelDropdown" v-if="examInMonth != null">
                                        <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>
                                        <li v-for="exam in examInMonth" v-on:click="changeExam(exam.exams.id, exam.exams.name)"><a>@{{exam.exams.name}}</a></li>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-container">

                            <div class="dropdown show">
                                <a class="btn btn-secondary dropdown-toggle drop-name" href="#" role="button"
                                   id="yearDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    @lang('jlpt.ranking.year', ['year' => '@{{year}} '])  <span class="caret"></span>
                                </a>
                                <div class="dropdown-menu user-menu" aria-labelledby="yearDropdown">
                                    <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>
                                    <li v-on:click="changeCondition('year', '2018')"><a>@lang('jlpt.ranking.year', ['year' => 2018])</a></li>
                                    <li v-on:click="changeCondition('year', '2019')"><a>@lang('jlpt.ranking.year', ['year' => 2019])</a></li>
                                    <li v-on:click="changeCondition('year', '2020')"><a>@lang('jlpt.ranking.year', ['year' => 2020])</a></li>
                                </div>
                            </div>
                            @php
                                $monthArray = [
                                    'vi' => [1,2,3,4,5,6,7,8,9,10,11,12],
                                    'en' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
                                ]
                            @endphp
                            <div class="dropdown show">
                                <a class="btn btn-secondary dropdown-toggle drop-name" href="#" role="button"
                                   id="monthDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span id="monthSelect">@lang('jlpt.ranking.month', ['month' => $monthArray[Lang::locale()][$lastItem->created_at->month - 1]])</span> <span class="caret"></span>
                                </a>
                                <div class="dropdown-menu user-menu" aria-labelledby="monthDropdown">
                                    <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>
                                    @foreach($monthArray[Lang::locale()] as $key => $value)
                                        <li  v-on:click="changeCondition('month', '{{$key+1}}', $event.target)"><a>@lang('jlpt.ranking.month', ['month' => $value])</a></li>
                                    @endforeach
                                </div>
                            </div>
                            <div class="dropdown show">
                                <a class="btn btn-secondary dropdown-toggle drop-name" href="#" role="button"
                                   id="levelDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    @lang('jlpt.ranking.exam', ['exam' => '@{{level}}'])  <span class="caret"></span>
                                </a>
                                <div class="dropdown-menu user-menu" aria-labelledby="levelDropdown">
                                    <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>
                                    <li v-on:click="changeCondition('level', 'N5')"><a>@lang('jlpt.ranking.exam', ['exam' => 'N5'])</a></li>
                                    <li v-on:click="changeCondition('level', 'N4')"><a>@lang('jlpt.ranking.exam', ['exam' => 'N4'])</a></li>
                                    <li v-on:click="changeCondition('level', 'N3')"><a>@lang('jlpt.ranking.exam', ['exam' => 'N3'])</a></li>
                                    <li v-on:click="changeCondition('level', 'N2')"><a>@lang('jlpt.ranking.exam', ['exam' => 'N2'])</a></li>
                                    <li v-on:click="changeCondition('level', 'N1')"><a>@lang('jlpt.ranking.exam', ['exam' => 'N1'])</a></li>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-center items-center pt-[40px] gap-5">
                        <div class="relative">
                            <img src="{{ asset('assets/img/jlpt/second.svg') }}" class="absolute w-[60px] left-[calc(50%-30px)] top-[-20px]">
                            <div
                                class="absolute left-[calc(50%-40px)] top-[50px] w-[80px] h-[80px] rounded-full object-contain overflow-hidden border border-[3px] border-white shadow-sm"
                            >
                                <img v-if="!ranking[1]?.userAvatar" :src="url + '/assets/img/default-avatar.jpg'" class="w-full h-full" />
                                <img v-if="ranking[1]?.userAvatar" :src="`{{ config('app.asset_url') }}` + '/cdn/avatar/default/' + ranking[1].userAvatar" class="w-full h-full" />
                            </div>
                            <div class="flex flex-wrap px-2.5 flex-col absolute top-[150px] font-quicksand text-sm font-semibold text-white text-center w-full items-center">
                                <div>@{{ ranking[1]?.userName }}</div>
                                <div class="font-black text-lg">@{{ ranking[1]?.grade_achieved }}</div>
                            </div>
                            <img src="{{ asset('assets/img/jlpt/badge.svg') }}" alt="" class="w-[150px]">
                        </div>
                        <div class="relative mt-[-20px]">
                            <img src="{{ asset('assets/img/jlpt/first.svg') }}" alt="" class="absolute w-[60px] left-[calc(50%-30px)] top-[-20px]">
                            <div
                                class="absolute left-[calc(50%-40px)] top-[50px] w-[80px] h-[80px] rounded-full object-contain overflow-hidden border border-[3px] border-white shadow-sm"
                            >
                                <img v-if="!ranking[0]?.userAvatar" :src="url + '/assets/img/default-avatar.jpg'" class="w-full h-full"/>
                                <img v-if="ranking[0]?.userAvatar" :src="`{{ config('app.asset_url') }}` + '/cdn/avatar/default/' + ranking[0].userAvatar" class="w-full h-full"/>
                            </div>
                            <div class="flex flex-wrap px-2.5 flex-col absolute top-[150px] font-quicksand text-sm font-semibold text-white text-center w-full items-center">
                                <div>@{{ ranking[0]?.userName }}</div>
                                <div class="font-black text-lg">@{{ ranking[0]?.grade_achieved }}</div>
                            </div>
                            <img src="{{ asset('assets/img/jlpt/badge.svg') }}" alt="" class="w-[160px]">
                        </div>
                        <div class="relative">
                            <img src="{{ asset('assets/img/jlpt/third.svg') }}" class="absolute w-[60px] left-[calc(50%-30px)] top-[-20px]">
                            <div
                                class="absolute left-[calc(50%-40px)] top-[50px] w-[80px] h-[80px] rounded-full object-contain overflow-hidden border border-[3px] border-white shadow-sm"
                            >
                                <img v-if="!ranking[2]?.userAvatar" :src="url + '/assets/img/default-avatar.jpg'" class="w-full h-full"/>
                                <img v-if="ranking[2]?.userAvatar" :src="`{{ config('app.asset_url') }}` + '/cdn/avatar/default/' + ranking[2].userAvatar" class="w-full h-full"/>
                            </div>
                            <div class="flex flex-wrap px-2.5 flex-col absolute top-[150px] font-quicksand text-sm font-semibold text-white text-center w-full items-center">
                                <div>@{{ ranking[2]?.userName }}</div>
                                <div class="font-black text-lg">@{{ ranking[2]?.grade_achieved }}</div>
                            </div>
                            <img src="{{ asset('assets/img/jlpt/badge.svg') }}" alt="" class="w-[150px]">
                        </div>
                    </div>
                </div>

                <div v-if="loading == false">
                @if(Auth::check())

                @else
                    <div class="rank-not-auth">
                        <span><i class="zmdi zmdi-info-outline"></i> @lang('jlpt.ranking.login')
                        </span>
                    </div>
                @endif
                </div>


                {{-- bảng danh sách top 100 --}}
                <div class="list-students-container">
                    <div class="notification-empty-container" v-if="loading == true">
                        <img class="loading-icon" style="width: 20px;" :src="url + '/assets/img/loading.gif'"/> &nbsp; @lang('jlpt.ranking.loading')
                    </div>
                    <table class="table" v-if="loading == false">
                        <thead class="thead-dark">
                            <tr>
                                <th class="text-left title-rank" style="text-align: center;">@lang('jlpt.ranking.tbl_top')</th>
                                <th class="text-left title-rank">@lang('jlpt.ranking.tbl_student')</th>
                                <th class="text-center title-rank">@lang('jlpt.ranking.tbl_level')</th>
                                <th class="text-center title-rank">@lang('jlpt.ranking.tbl_point')</th>
                                <template v-if="['N1', 'N2', 'N3'].includes(level)">
                                    <th class="text-center title-rank">@lang('jlpt.ranking.N3.tbl_part_1', ['part' => 1])</th>
                                    <th class="text-center title-rank">@lang('jlpt.ranking.N3.tbl_part_2', ['part' => 2])</th>
                                    <th class="text-center title-rank">@lang('jlpt.ranking.N3.tbl_part_3', ['part' => 3])</th>
                                </template>
                                <template v-else>
                                    <th class="text-center title-rank">@lang('jlpt.ranking.N5.tbl_part_1', ['part' => 1])</th>
                                    <th class="text-center title-rank">@lang('jlpt.ranking.N5.tbl_part_2', ['part' => 2])</th>
                                </template>
                                <th class="text-center title-rank">@lang('jlpt.ranking.tbl_result')</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in ranking" style="height: 42px;" v-on:click="showCertificateMobile(index)"
                                :class="[(index == 0) ? 'top-1' : '', (index == 1) ? 'top-2' : '', (index == 2) ? 'top-3' : '', (index >= 3 && index <= 9) ? 'top-4-9' : '']">
                                <td class="text-left indexRank">
                                    <img v-if="index == 0" src="{{url('/assets/img/jlpt/1.svg')}}" style="width: 35px;">
                                    <img v-else-if="index == 1" src="{{url('/assets/img/jlpt/2.svg')}}" style="width: 35px;">
                                    <img v-else-if="index == 2" src="{{url('/assets/img/jlpt/3.svg')}}" style="width: 35px;">

                                    <span v-if="index > 2 && index < 10" class="number" style="color: #96D962;">@{{ printIndex(index) }}</span>
                                    <span v-if="index > 9" class="number" style="color: #888; font-size: 22px;">@{{ printIndex(index) }}</span>
                                </td>

                                <td class="text-left avatar">
                                    <img v-if="! item.userAvatar" :src="url + '/assets/img/default-avatar.jpg'" style="width: 35px; height: 35px; object-fit: cover"/>
                                    <img v-if="item.userAvatar" :src="`{{ config('app.asset_url') }}` + '/cdn/avatar/small/' + item.userAvatar" style="width: 35px; height: 35px; object-fit: cover"/>
                                    {{-- <img :src="url + '/assets/img/default-avatar.jpg'"> --}}
                                    <span>@{{item.userName}}</span>
                                </td>
                                <td class="text-left detail" style="text-align: center;">@{{item.course_name}}</td>
                                <td class="text-center score"> <span>@{{ printTotal(item.grade_1, item.grade_2, item.grade_3) }}</span> </td>
                                <td class="text-center score"> @{{ item.grade_1}} </td>
                                <td v-if="['N1', 'N2', 'N3'].includes(level)" class="text-center score"> @{{ item.grade_2}} </td>
                                <td class="text-center score"> @{{ item.grade_3}} </td>
                                <td class="text-center passed">
                                    <span v-if="printResult(item.course_name, item.grade_1, item.grade_2, item.grade_3) == true" class="ok">
                                        <i class="glyphicon glyphicon-ok"></i>
                                    </span>
                                    <span v-else class="not-ok"><i class="glyphicon glyphicon-remove"></i></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div v-if="!ranking" class="noResult">
                        <div class="notification-empty-container" v-if="loading == false">
                            @lang('jlpt.ranking.tbl_noContent')
                        </div>
                    </div>
                </div>
                <script type="text/javascript">

                    var lastYear     = "{{ $lastItem->created_at->year }}";
                    var lastMonth    = "{{ $lastItem->created_at->month }}";
                    var lastLevel    = "{{ $lastItem->course }}";
                    var currentUserId = {!! isset(Auth::user()->id) ? Auth::user()->id : 0 !!};
                    var exams = "{{ $lastItem->course }}";
                    var examId = {{ $lastItem->exam_id }};
                </script>
            </div>
        </div>
    </div>
</div>
@stop

@section('footer-js')
    <script>
        $(".thi-thu").addClass("active");
        $(".ranking").addClass("active");
    </script>
    <script src="{{asset('assets/js/jlpt-ranking.js')}}?{{filemtime('assets/js/jlpt-ranking.js')}}"></script>
@stop
