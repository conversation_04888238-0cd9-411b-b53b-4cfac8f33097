@extends('frontend.jlpt._app')

@section('title') Thi thử JLPT online - Dungmori @stop
@section('description')Thi thử JLPT online với Dungmori @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> t<PERSON><PERSON><PERSON> vớ<PERSON>hong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop
<style>
    .remind-btn {
        cursor: pointer;
        padding: 10px 52px; border-radius: 52px; font-weight: 700; font-size: 16px; color: white; background-color: #ADADAD
    }
    .remind-btn:hover {
        background-color: #FF7A00;
    }
    .remind-item {
        display: flex; align-items: center; justify-content: space-between; padding: 27px 0px;
    }
    .remind-item:not(:last-child) {
        border-bottom: 1px solid #ADADAD;
    }
</style>
@section('content')

<div class="main">
    <div class="main-center">
        @include('frontend.jlpt.common-title')
        <div class="text-center font-quicksand font-semibold text-lg -mt-3">Thi thử JLPT online miễn phí</div>
        <div class="main-exam-menu">
            <div class="exam-container-menu">
                <a href="{{url('/thi-thu/ranking')}}" class="item-exam ranking">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_75_3009)"><path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2327 23.0904V11.5452C41.2327 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" fill="#F2F2F2"></path> <path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2328 23.0904V11.5452C41.2328 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7917 46.1807H32.9862" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M26.3889 37.9341V46.1806" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M40.8616 26.3889H42.882C44.6317 26.3889 46.3097 25.6939 47.5469 24.4566C48.7842 23.2194 49.4792 21.5414 49.4792 19.7917V16.4931C49.4792 16.0556 49.3055 15.6361 48.9961 15.3268C48.6868 15.0175 48.2673 14.8438 47.8299 14.8438H41.2327" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M11.9576 26.3889H9.87532C8.12562 26.3889 6.44759 25.6939 5.21036 24.4566C3.97314 23.2194 3.27808 21.5414 3.27808 19.7917V16.4931C3.27808 16.0556 3.45184 15.6361 3.76115 15.3268C4.07045 15.0175 4.48996 14.8438 4.92739 14.8438H11.5246" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_75_3009"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.ranking')</span>
                </a>
                <a href="{{url('/thi-thu')}}" class="item-exam room active">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_77_3339)"><path d="M41.3369 45.5834H11.6493C11.2119 45.5834 10.7924 45.4097 10.4831 45.1004C10.1738 44.7911 10 44.3716 10 43.9341V7.64931C10 7.21189 10.1738 6.79238 10.4831 6.48307C10.7924 6.17377 11.2119 6 11.6493 6H31.441L42.9862 17.5452V43.9341C42.9862 44.3716 42.8124 44.7911 42.5031 45.1004C42.1938 45.4097 41.7743 45.5834 41.3369 45.5834Z" fill="#96D962"></path> <path d="M31.3369 6.59717V18.1423H42.8821L31.3369 6.59717Z" fill="white"></path> <path d="M41.2329 46.1806H11.5453C11.1079 46.1806 10.6884 46.0068 10.3791 45.6975C10.0698 45.3882 9.896 44.9687 9.896 44.5313V8.24648C9.896 7.80905 10.0698 7.38955 10.3791 7.08024C10.6884 6.77093 11.1079 6.59717 11.5453 6.59717H31.337L42.8822 18.1423V44.5313C42.8822 44.9687 42.7084 45.3882 42.3991 45.6975C42.0898 46.0068 41.6703 46.1806 41.2329 46.1806Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M31.3369 6.59717V18.1423H42.8821" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7915 28.0382H32.986" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7915 34.6355H32.986" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_77_3339"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.jlpt')</span>
                </a>
                <a href="{{url('/thi-thu/history')}}" class="item-exam history">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_75_3017)"><path d="M26.3888 44.5313C36.4085 44.5313 44.5312 36.4087 44.5312 26.3889C44.5312 16.3691 36.4085 8.24646 26.3888 8.24646C16.369 8.24646 8.24634 16.3691 8.24634 26.3889C8.24634 36.4087 16.369 44.5313 26.3888 44.5313Z" fill="#F2F2F2"></path> <path d="M26.3889 16.493V26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M34.9653 31.3368L26.3889 26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M14.8027 20.5545H6.55615V12.308" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M13.5657 39.2123C16.1024 41.751 19.335 43.4804 22.8547 44.1816C26.3744 44.8828 30.023 44.5244 33.339 43.1517C36.6549 41.779 39.4892 39.4536 41.4834 36.4698C43.4776 33.486 44.542 29.9777 44.542 26.3889C44.542 22.8 43.4776 19.2918 41.4834 16.308C39.4892 13.3241 36.6549 10.9988 33.339 9.62606C30.023 8.25334 26.3744 7.89492 22.8547 8.59615C19.335 9.29738 16.1024 11.0268 13.5657 13.5655L6.55615 20.5544" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_75_3017"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.my_score')</span>
                </a>
            </div>
            @include('frontend.jlpt._lang')
        </div>

        <div class="main-exam-right" style="opacity: 0">

            @if(!Auth::check())
            <div class="requre-auth">
                <span><i class="zmdi zmdi-info-outline"></i> @lang('jlpt.content.please')
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('login')"
                     style="cursor: pointer;">@lang('jlpt.content.login')  </a> @lang('jlpt.content.or')
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('register')"
                     style="cursor: pointer;">@lang('jlpt.content.register')  </a>
                     @lang('jlpt.content.do_this_exam')
                </span>
            </div>
            @endif

            <p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p>
            {{-- <div class="no-auth-notifi"></div> --}}
            {{-- <div class="no-auth-notifi">
                <span>
                    Thi thử trực tuyến sẽ diễn ra vào thứ 7 hàng tuần với 2 ca thi (<b>giờ Việt Nam</b>)
                    &nbsp; &nbsp; Sáng: <b>9h-11h30</b> &nbsp; &nbsp; Tối: <b>19h-21h30</b>
                </span>
            </div> --}}


            @if(Auth::check())
            <table class="table table-borderless">
                <thead>
                    <tr>
                        <th scope="col" style="text-align: left; width: 90px; padding-left: 0;">@lang('jlpt.exam.level')</th>
                        <th scope="col" class="mobile-hidden">@lang('jlpt.exam.pass_score')</th>
                        <th scope="col" class="mobile-hidden">@lang('jlpt.exam.duration')</th>
                        <th scope="col" width="300px">@lang('jlpt.exam.agenda')</th>
                        <th scope="col" style="text-align: right; padding-right: 35px;">@lang('jlpt.exam.online')</th>
                    </tr>
                </thead>
                <tbody>


                    <?php
                        $passScore = ['80', '90', '95', '90', '100'];
                        $durations = ['90', '125', '140', '155', '170'];
                    ?>
                    @foreach(['N5', 'N4', 'N3', 'N2', 'N1', 'N0'] as $key => $course)
                    {{-- N0 là chuẩn bị tham gia thi => User đang online ở page thi-thu--}}
                    @if ($course != 'N0')
                    <tr>
                        <th class="center" style="padding-left: 0; text-align: left;"><span class="nx {{$course}}">{{$course}}</span></th>
                        <td class="mobile-hidden"> {{$passScore[$key]}}/180</td>
                        <td class="mobile-hidden">{{$durations[$key]}} @lang('jlpt.content.minutes')</td>
                        <td class="action-area">
                            <div v-if="isEnableExam('{{$course}}')">
                                <span v-if="joinBtnState['{{$course}}'] == 0">-</span>
                                <span v-if="joinBtnState['{{$course}}'] == 1" style="line-height: 0.9;">
                                    <span style="font-size: 13px;"><i class="zmdi zmdi-timer"></i> @lang('jlpt.content.start_at') </span><br/>
                                    <b style="color: green; font-size: 14px;">
                                        {{App\Http\ModelsFrontend\ThiThu\Exam::getTimeStartByLevel($exams, $course)}}
                                    </b>
                                </span>
                                <span v-if="joinBtnState['{{$course}}'] == 2" class="btn dmr-btn" style="padding-left: 10px;" v-on:click="joinRoom('{{$course}}')">
                                    @if(App\Http\ModelsFrontend\ThiThu\Exam::getAccessCodeByLevel($exams, $course) == null)
                                        <i class="zmdi zmdi-time zmdi-hc-spin"></i>
                                    @else
                                        <i class="zmdi zmdi-key"></i>
                                    @endif
                                    &nbsp; @lang('jlpt.content.join_room')
                                </span>
                                <span v-if="joinBtnState['{{$course}}'] == 3" style="line-height: 1;">
                                    <span style="font-size: 13px; color: #777; width: 100%;">
                                        <i class="zmdi zmdi-timer-off"></i> @lang('jlpt.content.end_at')
                                    </span>
                                    <span style="color: #555; font-size: 14px;">
                                        {{App\Http\ModelsFrontend\ThiThu\Exam::getTimeEndByLevel($exams, $course)}}
                                    </span>
                                </span>
                            </div>
                            <span v-else style="opacity: 0.6;">
                                {{ isset($schedules[$key]) ? $schedules[$key]->schedule : '
                                —
                            ' }}
                            </span>
                        </td>
                        <td class="center">
                            {{-- nếu đang trong trạng thái join room --}}
                            <span class="online-status-num" v-if="isEnableExam('{{$course}}') && joinBtnState['{{$course}}'] == 2" style="width: 100%; text-align: right; padding-right: 30px;">
                                <div class="top a2"></div>
                                <b v-html="totalOnline('{{$course}}')"></b> @lang('jlpt.content.people')
                            </span>
                            <span v-else style="margin-right: 55px;">-</span>
                        </td>
                    </tr>
                    @else

                    @endif

                    @endforeach

                </tbody>
            </table>
            <div style="width: 100%; font-size: 16px; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
                <div style="text-align: right; padding-right: 35px;" >
                    <i style="font-size: 13px;">@lang('jlpt.content.now_in_vietnam')</i>
                    <div style="background: green; padding-bottom: .2em;" class="label label-primary" v-html="vnTime"></div>
                    <i style="font-size: 13px;">@{{showCurrentDate}}</i><br/>
                </div>
                <div class="online-status-num" style="font-size: 14px; text-align: right; padding-right: 35px;">
                    @lang('jlpt.content.current_online') &nbsp;
                    <div class="top a2"></div>
                    <b v-html="totalOnline('N0')"></b> @lang('jlpt.content.people')
                </div>
            </div>
            @endif

            <div style="border-radius: 18px; overflow: hidden; box-shadow: 0px 4px 13.2px 0px #0000001F; margin-top: 20px; margin-bottom: 30px">
                <div class="green" style="background-color: #57D061; padding: 22px 30px; font-size: 36px; font-family: Montserrat, Arial, sans-serif; font-weight: 700; text-align: center">
                    LỊCH THI THỬ JLPT MIỄN PHÍ <br> TẠI APP/WEBSITE DUNGMORI
                </div>
                <div class="green" style="font-family: Montserrat, Arial, sans-serif; padding: 0 48px;">
                    <div @class([
                            'remind-item',
                        ])>
                        <div style="font-weight: 700; font-size: 20px; color: #57D061"></div>
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 100px;">
                            <div style="text-align: center; width: 110px; color: rgb(87, 208, 97)">
                                <div style="font-weight: 700; font-size: 16px">Bắt Đầu</div>
                            </div>
                            <div style="text-align: center; width: 150px; color: rgb(87, 208, 97)">
                                <div style="font-weight: 700; font-size: 16px">Kết Thúc</div>
                            </div>
                        </div>
                    </div>
                    <div @class([
                            'remind-item',
                            'active' => now()->isBetween(Carbon\Carbon::parse('2024-06-07 00:00:00'), Carbon\Carbon::parse('2024-06-09 23:59:59')),
                            'passed' => now()->gt(Carbon\Carbon::parse('2024-06-09 23:59:59')),
                            'future' => now()->lt(Carbon\Carbon::parse('2024-06-07 00:00:00'))
                        ])>
                        <div style="font-weight: 700; font-size: 20px; color: #57D061">●  LẦN 1</div>
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 100px;">
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">02/06/2025 (T2)</div>
                                <div>07:00</div>
                            </div>
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">08/06/2025 (CN)</div>
                                <div>23:59</div>
                            </div>
                        </div>
                    </div>
                    <div @class([
                            'remind-item',
                            'active' => now()->isBetween(Carbon\Carbon::parse('2024-06-14 00:00:00'), Carbon\Carbon::parse('2024-06-16 23:59:59')),
                            'passed' => now()->gt(Carbon\Carbon::parse('2024-06-16 23:59:59')),
                            'future' => now()->lt(Carbon\Carbon::parse('2024-06-14 00:00:00'))
                        ])>
                        <div style="font-weight: 700; font-size: 20px; color: #57D061">●  LẦN 2</div>
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 100px;">
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">09/06/2025 (T2)</div>
                                <div>07:00</div>
                            </div>
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">15/06/2025 (CN)</div>
                                <div>23:59</div>
                            </div>
                        </div>
                    </div>
                    <div @class([
                            'remind-item',
                            'active' => now()->isBetween(Carbon\Carbon::parse('2024-06-21 00:00:00'), Carbon\Carbon::parse('2024-06-23 23:59:59')),
                            'passed' => now()->gt(Carbon\Carbon::parse('2024-06-23 23:59:59')),
                            'future' => now()->lt(Carbon\Carbon::parse('2024-06-21 00:00:00'))
                        ])>
                        <div style="font-weight: 700; font-size: 20px; color: #57D061">●  LẦN 3</div>
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 100px;">
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">16/06/2025 (T2)</div>
                                <div>07:00</div>
                            </div>
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">22/06/2025 (CN)</div>
                                <div>23:59</div>
                            </div>
                        </div>
                    </div>
                    <div @class([
                            'remind-item',
                            'active' => now()->isBetween(Carbon\Carbon::parse('2024-06-28 00:00:00'), Carbon\Carbon::parse('2024-06-30 23:59:59')),
                            'passed' => now()->gt(Carbon\Carbon::parse('2024-06-30 23:59:59')),
                            'future' => now()->lt(Carbon\Carbon::parse('2024-06-28 00:00:00'))
                        ])>
                        <div style="font-weight: 700; font-size: 20px; color: #57D061">●  LẦN 4</div>
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 100px;">
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">23/06/2025 (T2)</div>
                                <div>07:00</div>
                            </div>
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">29/06/2025 (CN)</div>
                                <div>23:59</div>
                            </div>
                        </div>

                    </div>
                    <div @class([
                            'remind-item',
                            'active' => now()->isBetween(Carbon\Carbon::parse('2024-07-05 00:00:00'), Carbon\Carbon::parse('2024-07-07 23:59:59')),
                            'passed' => now()->gt(Carbon\Carbon::parse('2024-07-07 23:59:59')),
                            'future' => now()->lt(Carbon\Carbon::parse('2024-07-05 00:00:00'))
                        ])>
                        <div style="font-weight: 700; font-size: 20px; color: #57D061">●  LẦN 5</div>
                        <div style="display: flex; justify-content: flex-end; align-items: center; gap: 100px;">
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">30/06/2025 (T2)</div>
                                <div>07:00</div>
                            </div>
                            <div style="text-align: center">
                                <div style="font-weight: 700; font-size: 16px">05/07/2025 (T7)</div>
                                <div>23:59</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="white" style="background-color: white;padding: 20px"></div>
            </div>
            <div class="flex gap-3 mb-10">
                <a href="https://m.me/1595926847401625?ref=zoom" target="_blank"><img src="{{ asset('assets/img/jlpt/jlpt_info_left-2024.png') }}" alt="" class="shadow-md rounded-[15px] overflow-hidden"></a>
                <a href="https://dungmori.com/bai-viet/cm/thong-tin-ki-thi-jlpt" target="_blank"><img src="{{ asset('assets/img/jlpt/jlpt_info_right.png') }}" alt="" class="shadow-md rounded-[15px] overflow-hidden"></a>
            </div>
        </div>

        {{-- <a href="{{url('/khoa-hoc')}}"><img style="margin-top: -30px;" src="{{url('/assets/img/luyendem.png')}}"/></a> --}}
    </div>

</div>

@stop
@section('footer-js')

    <script type="text/javascript">
        $(".thi-thu").addClass("active");
        var enableExams = {!! json_encode($exams) !!};
        var timeStarts = {!! json_encode($timeStarts) !!};
        var timeEnds = {!! json_encode($timeEnds) !!};

        var me = null;
        @if(Auth::check())
            me = { id: "{{Auth::user()->id}}", name: "{{Auth::user()->name}}", avatar: "{{Auth::user()->avatar}}"};
        @endif

        if(performance.navigation.type == 2){
            location.reload(true);
        }
    </script>

    {{-- <script src="{{asset('plugin/socket-io/socket.io-1.7.3.js')}}"></script> --}}
    <script src="{{asset('plugin/crypto-js/crypto-js.min.js')}}"></script>

    <script type="text/javascript">
        var jlptUrl = "http://localhost:3333";
        var jlptSocket = "http://localhost:8008";

        //nếu là deploy trên web thật
        if(window.location.href.indexOf("-test") != -1){
            jlptUrl = "https://jlpt-test.dungmori.com";
            jlptSocket = "https://count-test.dungmori.com";
        }

        //nếu là deploy trên web thật
        if(window.location.href.indexOf("dungmori.com") != -1 && window.location.href.indexOf("web-test") == -1){
            jlptUrl = "https://mjt.dungmori.com";
            jlptSocket = "https://mjt-count.dungmori.com";
        }
    </script>
    <script src="{{asset('assets/js/jlpt-exam.js')}}?{{filemtime('assets/js/jlpt-exam.js')}}"></script>

    {{-- @if(Auth::check())
    <script src="{{asset('plugin/socket/regenerator.min.js')}}"></script>
    <script src="{{asset('plugin/socket/websocket-client.js')}}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.2/rollups/aes.js"></script>
    <script src="{{asset('assets/js/jlpt-exam.js')}}?{{filemtime('assets/js/jlpt-exam.js')}}"></script>
    @endif --}}
@stop
