@extends('frontend.jlpt._app')

@section('title') Dungmori - Lịch sử thi @stop
@section('description')Thi thử JLPT online với Dungmori @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')
<div class="main">
    <div class="main-center">
        @include('frontend.jlpt.common-title')
        <div class="text-center font-quicksand font-semibold text-lg -mt-3">Thi thử JLPT online miễn phí</div>
        <div class="main-history-menu">
            <div class="exam-container-menu">
                <a href="{{url('/thi-thu/ranking')}}" class="item-exam ranking">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_75_3009)"><path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2327 23.0904V11.5452C41.2327 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" fill="#F2F2F2"></path> <path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2328 23.0904V11.5452C41.2328 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7917 46.1807H32.9862" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M26.3889 37.9341V46.1806" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M40.8616 26.3889H42.882C44.6317 26.3889 46.3097 25.6939 47.5469 24.4566C48.7842 23.2194 49.4792 21.5414 49.4792 19.7917V16.4931C49.4792 16.0556 49.3055 15.6361 48.9961 15.3268C48.6868 15.0175 48.2673 14.8438 47.8299 14.8438H41.2327" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M11.9576 26.3889H9.87532C8.12562 26.3889 6.44759 25.6939 5.21036 24.4566C3.97314 23.2194 3.27808 21.5414 3.27808 19.7917V16.4931C3.27808 16.0556 3.45184 15.6361 3.76115 15.3268C4.07045 15.0175 4.48996 14.8438 4.92739 14.8438H11.5246" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_75_3009"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.ranking')</span>
                </a>
                <a href="{{url('/thi-thu')}}" class="item-exam room">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_77_3339)"><path d="M41.3369 45.5834H11.6493C11.2119 45.5834 10.7924 45.4097 10.4831 45.1004C10.1738 44.7911 10 44.3716 10 43.9341V7.64931C10 7.21189 10.1738 6.79238 10.4831 6.48307C10.7924 6.17377 11.2119 6 11.6493 6H31.441L42.9862 17.5452V43.9341C42.9862 44.3716 42.8124 44.7911 42.5031 45.1004C42.1938 45.4097 41.7743 45.5834 41.3369 45.5834Z" fill="#F6F6F6"></path> <path d="M31.3369 6.59717V18.1423H42.8821L31.3369 6.59717Z" fill="white"></path> <path d="M41.2329 46.1806H11.5453C11.1079 46.1806 10.6884 46.0068 10.3791 45.6975C10.0698 45.3882 9.896 44.9687 9.896 44.5313V8.24648C9.896 7.80905 10.0698 7.38955 10.3791 7.08024C10.6884 6.77093 11.1079 6.59717 11.5453 6.59717H31.337L42.8822 18.1423V44.5313C42.8822 44.9687 42.7084 45.3882 42.3991 45.6975C42.0898 46.0068 41.6703 46.1806 41.2329 46.1806Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M31.3369 6.59717V18.1423H42.8821" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7915 28.0382H32.986" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.7915 34.6355H32.986" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_77_3339"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.jlpt')</span>
                </a>
                <a href="{{url('/thi-thu/history')}}" class="item-exam history active">
                    <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_75_3017)"><path d="M26.3888 44.5313C36.4085 44.5313 44.5312 36.4087 44.5312 26.3889C44.5312 16.3691 36.4085 8.24646 26.3888 8.24646C16.369 8.24646 8.24634 16.3691 8.24634 26.3889C8.24634 36.4087 16.369 44.5313 26.3888 44.5313Z" fill="#96D962"></path> <path d="M26.3889 16.493V26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M34.9653 31.3368L26.3889 26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M14.8027 20.5545H6.55615V12.308" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M13.5657 39.2123C16.1024 41.751 19.335 43.4804 22.8547 44.1816C26.3744 44.8828 30.023 44.5244 33.339 43.1517C36.6549 41.779 39.4892 39.4536 41.4834 36.4698C43.4776 33.486 44.542 29.9777 44.542 26.3889C44.542 22.8 43.4776 19.2918 41.4834 16.308C39.4892 13.3241 36.6549 10.9988 33.339 9.62606C30.023 8.25334 26.3744 7.89492 22.8547 8.59615C19.335 9.29738 16.1024 11.0268 13.5657 13.5655L6.55615 20.5544" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"></path></g> <defs><clipPath id="clip0_75_3017"><rect width="52.7779" height="52.7779" fill="white"></rect></clipPath></defs></svg>
                    <br/>
                    <span>@lang('jlpt.menu.my_score')</span>
                </a>
            </div>
            @include('frontend.jlpt._lang')
        </div>
        <div class="main-history-right" id="main-history-right">

            @if(! Auth::check())
                <div class="result-not-auth">
                    <span><i class="zmdi zmdi-info-outline"></i> @lang('jlpt.content.please')
                        <a data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('login')"
                         style="cursor: pointer;">@lang('jlpt.content.login')</a> @lang('jlpt.content.view_result')
                    </span>
                </div>
            @else

                {{-- chứng chỉ kết quả của từng người --}}
                <div class="fancybox-dialog" style="display: none;">
                    <a class="fancybox" data-options='{"src": "#jlpt-certificate", "touch": true}' href="javascript:;"></a>
                    <div id="jlpt-certificate" v-if="certificateUser != null">
                        <div class="jlpt-certificate">
                            <div class="jlpt-cer-title">日本語 能力試験　合否結果通知書</div>
                            <div class="jlpt-cer-title-en">Japanese Language Proficiency Test</div>
                            <div class="jlpt-cer-t-test">Test Result</div>

                            <div class="jlpt-date">受験日: @{{printDate(certificateUser.created_at)}}</div>
                            <div class="jlpt-more-infor">
                                <div class="jlpt-level">受験レベル Level: &nbsp; <b>@{{certificateUser.course_name}}</b></div>
                            </div>
                            <div class="jlpt-more-infor">
                                <div class="jlpt-name">氏名 Name: &nbsp; <b>@{{certificateUser.userName}}</b></div>
                            </div>
                            <div class="jlpt-score">
                                <table class="jlpt-detail-score">
                                    <tbody>
                                        <tr>
                                            <td colspan="3" class="text-center">
                                                <p>得点区分別得点</p>
                                                <p>Scores by Scoring Section</p>
                                            </td>
                                            <td rowspan="2" class="jlpt-total-score" class="text-center">
                                                <p>総合得点</p>
                                                <p>Total Score</p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-center">文字・語彙</td>
                                            <td class="text-center">言語知識・読解</td>
                                            <td class="text-center">聴解</td>
                                        </tr>
                                        <tr style="font-weight: bold;">
                                            <td class="text-center">@{{certificateUser.grade_1}} / 60</td>
                                            <td class="text-center">@{{certificateUser.grade_2}} / 60</td>
                                            <td class="text-center">@{{certificateUser.grade_3}} / 60</td>
                                            <td class="jlpt-total-score-c">@{{certificateUser.grade_achieved}} / 180</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table class="jlpt-conclude">
                                    <tbody>
                                        <tr>
                                            <td class="jlpt-conclude-pass" style="font-weight: bold;">
                                                <div class="jlpt-conclude-pass-c">
                                                    <span v-if="printResult(certificateUser.course_name, certificateUser.grade_1, certificateUser.grade_2, certificateUser.grade_3) == true">合  格  Passed</span>
                                                    <span v-else>不  合  格  Not Passed</span>
                                                </div>
                                            </td>
                                            <td class="stamp">
                                                <img src="/assets/img/logo.png">
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="!items.length" class="notification-empty-container" v-cloak>
                    <i class="fa fa-mortar-board"></i> Không có kết quả thi !
                </div>
                <div v-else class="mt-12 w-full grid grid-cols-3 gap-4" v-cloak>
                    <div v-for="(item, index) in items" class="flex flex-col gap-3 items-center cursor-pointer rounded-lg border border-slate-200 p-4 shadow-sm focus-within:ring-2 focus-within:ring-green-500 focus-within:ring-offset-2 hover:shadow-lg transition-all bg-white">
                        <div class="flex flex-col w-full gap-2 items-center">
                            @if(auth()->check() && auth()->user()->email == '<EMAIL>')
                                <div v-for="(time, index) in JSON.parse(item.join_time)" class="w-full flex justify-between font-quicksand text-black">
                                    <div class="font-bold">Phần @{{ index + 1 }}</div>
                                    <div class="font-semibold" v-cloak>@{{ time ? moment(time).format("DD/MM/YYYY HH:mm:ss") : '--' }}</div>
                                </div>
                            @endif
                            <div class="w-full flex justify-between font-quicksand text-black">
                                <div class="font-bold">@lang('jlpt.content.date')</div>
                                <div class="font-semibold" v-cloak>@{{ item.created_at }}</div>
                            </div>
                            <div class="w-full flex justify-between font-quicksand text-black">
                                <div class="font-bold">@lang('jlpt.exam.level')</div>
                                <div class="font-semibold" v-cloak>@{{item.course_name}}</div>
                            </div>

                            <div class="w-full flex justify-between font-quicksand text-black">
                                <div class="font-bold">
                                    <span v-if="['N1', 'N2', 'N3'].includes(item.course)">@lang('jlpt.ranking.N3.tbl_part_1')</span>
                                    <span v-else>@lang('jlpt.ranking.N5.tbl_part_1')</span>
                                </div>
                                <div class="font-semibold">
                                    <span v-if="['N1', 'N2', 'N3'].includes(item.course)" v-cloak>@{{ item.grade_1 }} / 60</span>
                                    <span v-else v-cloak>@{{ item.grade_1 }} / 120</span>
                                </div>
                            </div>
                            <div v-if="['N1', 'N2', 'N3'].includes(item.course)" class="w-full flex justify-between font-quicksand text-black">
                                <div class="font-bold">
                                    <span v-if="['N1', 'N2', 'N3'].includes(item.course)" v-cloak>@lang('jlpt.ranking.N3.tbl_part_2')</span>
                                    <span v-else v-cloak>@lang('jlpt.ranking.N5.tbl_part_2')</span>
                                </div>
                                <div class="font-semibold" v-cloak>@{{ item.grade_2 }} / 60</div>
                            </div>
                            <div class="w-full flex justify-between font-quicksand text-black">
                                <div class="font-bold">
                                    <span v-if="['N1', 'N2', 'N3'].includes(item.course)" v-cloak>@lang('jlpt.ranking.N3.tbl_part_3')</span>
                                    <span v-else v-cloak>@lang('jlpt.ranking.N5.tbl_part_2')</span>
                                </div>
                                <div class="font-semibold" v-cloak>@{{ item.grade_3 }} / 60</div>
                            </div>
                            <div class="w-full flex justify-between font-quicksand text-black">
                                <div class="font-bold">@lang('jlpt.exam.total')</div>
                                <div class="font-semibold">
                                    <span v-cloak>@{{ item.grade_1 + item.grade_2 + item.grade_3  }}</span>
                                    <span v-if="['N3','N2','N1'].includes(item.course_name) && (item.grade_1 < 19 || item.grade_2 < 19 || item.grade_3 < 19)" v-cloak>不合格</span>
                                    <span v-else-if="['N4','N5'].includes(item.course_name) && (item.grade_1 < 38 || item.grade_3 < 19)" v-cloak>不合格</span>
                                    <span v-else v-cloak>
                                    <template v-if="item.course_name == 'N5' && (item.grade_1 + item.grade_2 + item.grade_3) >= 80 ||
                                        item.course_name == 'N4' && (item.grade_1 + item.grade_2 + item.grade_3) >= 90 ||
                                        item.course_name == 'N3' && (item.grade_1 + item.grade_2 + item.grade_3) >= 95 ||
                                        item.course_name == 'N2' && (item.grade_1 + item.grade_2 + item.grade_3) >= 90 ||
                                        item.course_name == 'N1' && (item.grade_1 + item.grade_2 + item.grade_3) >= 100">合格</template>
                                    <template v-else>不合格</template>
                                </span>
                                    {{--                                <span class="label label-success" v-on:click="showCertificate(index)">@lang('jlpt.content.view')</span>--}}
                                    <a v-if="item.certificate_info" class="label bg-[#96D962] !text-white" :href="'https://mjt.dungmori.com/certificate/' + btoa(item.id)" target="_blank">@lang('jlpt.content.view')</a>
                                </div>
                            </div>

                            <button class="mt-auto w-full rounded-lg bg-[#96D962] text-white px-3 py-1.5 text-sm font-semibold text-white hover:shadow-md focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-lime-600" v-on:click="showAnswer(item.exam_id, item.user_id)">@lang('jlpt.exam.detail')</button>
                        </div>


                </div>
                {{-- <div class="paginate-container">{{ $items->links() }} </div> --}}
                {{-- Nếu danh sách trống --}}

            @endif

            <div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="infoModal" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <form @submit.prevent="submitCertificate">
                        <div class="modal-content">
                            <div class="modal-header">
                            <h3 class="modal-title" id="exampleModalLabel">@lang('jlpt.modal.header')</h3>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center title">@lang('jlpt.modal.title')</div>

                                <div class="form-group">
                                <label for="recipient-name" class="col-form-label">@lang('jlpt.modal.fullname') <span class="required">*</span> <span class="has-error" v-html="validation.fullname"></span></label>
                                <input type="text" class="form-control"  v-model="formData.fullname">
                                </div>
                                <div class="form-group">
                                <label for="recipient-name" class="col-form-label">@lang('jlpt.modal.dob') <span class="required">*</span> <span class="has-error" v-html="validation.dob"></span></label>
                                <input type="date" class="form-control" v-model="formData.dob">
                                </div>
                                <div class="form-group">
                                <label for="recipient-name" class="col-form-label">@lang('jlpt.modal.mobile') <span class="required">*</span> <span class="has-error" v-html="validation.mobile"></span></label>
                                <input type="text" class="form-control" v-model="formData.mobile" maxlength="12">
                                </div>
                                <div class="form-group">
                                <label for="recipient-name" class="col-form-label">@lang('jlpt.modal.email') <span class="required">*</span> <span class="has-error" v-html="validation.email"></span></label>
                                <input type="text" class="form-control" v-model="formData.email">
                                </div>
                                <div class="form-group">
                                <label for="recipient-name" class="col-form-label">@lang('jlpt.modal.address') <span class="required">*</span> <span class="has-error" v-html="validation.msgAddress"></span></label>
                                <div style="margin-bottom: 30px">
                                    <select v-model="formData.country" class="ip-add form-control">
                                        <option value="vi" selected>@lang('jlpt.modal.country_vi')</option>
                                        <option value="jp">@lang('jlpt.modal.country_jp')</option>
                                    </select>
                                    <input type="text" class="form-control ip-add" v-model="formData.postalcode" maxlength="8" placeholder="@lang('jlpt.modal.postalcode_placeholder')">
                                    <a target="_blank" v-if="formData.country == 'vi'" class="ip-add" href="https://buucuc.com/ma-buu-dien-zip-postal-code-viet-nam">@lang('jlpt.modal.postcode', ['country' => __('jlpt.modal.country_vi')])</a>
                                    <a target="_blank" v-if="formData.country == 'jp'" class="ip-add" href="https://japan-postcode.810popo.net/">@lang('jlpt.modal.postcode', ['country' => __('jlpt.modal.country_jp')])</a>
                                </div>
                                <textarea class="w-100 form-control" v-model="formData.address" rows="3" placeholder="@lang('jlpt.modal.address_placeholder')"></textarea>

                                </div>


                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">@lang('jlpt.modal.btn_close')</button>
                                <button type="submit" class="btn btn-success">@lang('jlpt.modal.btn_send')</button>
                            </div>
                        </div>
                    </form>
                  </div>
                </div>
              </div>
            {{-- <a href="{{url('/khoa-hoc')}}"><img style="margin-top: -10px;" src="{{url('/assets/img/luyendem.png')}}"/></a> --}}
        </div>
</div>

<script>
    var items  = [];
    var userName  = null;
    var locale = "{{Lang::locale()}}";
    @if(Auth::check())
        userName = "{{Auth::user()->name}}";
        items  = {!! json_encode($items) !!};
    @endif
    $(".thi-thu").addClass("active");
</script>
@stop

@section('footer-js')
    <script src="{{asset('plugin/lodash/lodash.js')}}"></script>
    <script src="{{asset('plugin/moment/moment.min.js')}}?{{filemtime('plugin/moment/moment.min.js')}}" type="text/javascript">
        moment.locale('vi')
    </script>
    <script src="{{asset('assets/js/jlpt-history.js')}}?{{filemtime('assets/js/jlpt-history.js')}}"></script>
@stop
