<script type="text/javascript">

    //hàm set ngôn ngữ
	function setLang(lang){
	    setCookie("_lang", lang, 100);
	    location.reload();
	}

    //setup ngôn ngữ tự động nếu chưa được set
    $(document).ready(function(){

        if(getCookie("_lang") == ''){
            setLang('vi');
        }
    });
</script>

@if(!isset($_COOKIE['_lang']) || $_COOKIE['_lang'] != 'en')
    <span class="lang-btn" onclick="setLang('en')">
        <img width="18px" src="{{url('/assets/img/lang_en.svg')}}"/> English
    </span>
@else
    <span class="lang-btn" onclick="setLang('vi')">
        <img width="18px" src="{{url('/assets/img/vn.gif')}}"/> Tiếng việt
    </span>
@endif
