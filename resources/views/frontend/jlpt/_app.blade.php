<!DOCTYPE html>
<html lang="vi">
<head>

    <meta charset="UTF-8">
    <title>@yield('title')</title>
    <meta http-equiv="content-language" content="vi"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="format-detection" content="telephone=no">
    <meta name="google-site-verification" content="qXXUA_R1uoDBf-1WE-ghxdEO6pirYuP-yDVB5iqytRg" />
    <meta name="facebook-domain-verification" content="4w08w6rk0e6sojaci5ix41vcdlh97t" />

    <meta name="author" content="@yield('author')"/>
    <meta name="description" content="@yield('description')"/>
    <meta name="robots" content="index,follow"/>

    <link rel="icon" href="{{asset('assets/img/new_home/06-2024/dungmori-fav.png')}}"/>
    <link rel="alternate" href="{{url('')}}" hreflang="vi" />

    <meta property="og:title" content="@yield('title')"/>
    <meta property="og:description" content="@yield('description')"/>
    <meta property="og:image" content="@yield('image')"/>
    <meta property="og:type" content="website"/>
    <meta property="og:site_name" content="DUNGMORI"/>
    <meta property="og:url" content="@yield('url')"/>
    <meta property="og:image:type" content=".png"/>
    <meta property="og:image:width" content="1200"/>
    <meta property="og:image:height" content="628"/>

    <meta property="fb:app_id" content="1768213996826394"/>
    <meta property="fb:admins" content="100004908811327"/>
    <meta property="fb:admins" content="1725907325"/>

    <link href="https://fonts.googleapis.com/css?family=Noto+Sans+JP:700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="preload" href="{{asset('css/base.css')}}?{{filemtime('css/base.css')}}" as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <link rel="preload" href="{{asset('css/plugins.css')}}?{{filemtime('css/plugins.css')}}" as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <link rel="preload" href="{{asset('assets/css/styles.css')}}?{{filemtime('assets/css/styles.css')}}" as="style" onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <link href="{{asset('assets/css/jlpt.css')}}?{{filemtime('assets/css/jlpt.css')}}" rel="stylesheet" type="text/css">

    @yield('header-css')

    <script src="{{asset('assets/js/headlibs.js')}}?{{filemtime('assets/js/headlibs.js')}}"></script>
    <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
    @yield('header-js')

    <?php

    //lưu admin ss vào cookie để giảm đi 1 truy vấn thường suyên
    $adminSession = false;
    if(isset($_COOKIE['admin_ss']) && $_COOKIE['admin_ss'] == 'be972bedb15a'){
        $adminSession = true;
    }else{
        if(Auth::guard('admin')->user()) {
            $adminSession = true;
            setcookie('admin_ss', 'be972bedb15a', time() + (86400 * 30), "/");
        }
    }
    //echo $adminSession;
    ?>
</head>
<body>
    <div id="application">

        {{-- {{Auth::user()}} --}}
        {{-- nav header trên pc --}}
        <div class="hidden lg:flex fixed w-full top-0 py-[15px] left-0 bg-dmr-green h-[76px] z-[100]">
            <div class="container">
                <div class="header-inner">
                    <div class="header-logo">
                        <a href="{{route('home')}}">
                            <img class="lazyload object-cover"
                                 src="{{asset('assets/img/new_home/06-2024/dungmori-logo.svg')}}"
                                 data-src="{{asset('assets/img/new_home/06-2024/dungmori-logo.svg')}}" alt=""/>
                        </a>
                    </div>
                    <div class="header-menu font-beanbag">
                        <div class="header-menu__item text-[0.9em] xlg:text-lg">
                            <a href="{{ url('/ve-dungmori') }}" class="!text-white hover:text-white active:text-white">Về
                                DUNGMORI</a>
                            {{--                    <img class="lazyload object-cover stroke-current text-white fill-current" src="{{ asset('/assets/img/hamburger.svg') }}" />--}}
                            <div class="header-menu__dropdown">
                                <div class="header-menu__dropdown-inner bg-dmr-green text-white">
                                    <a href="{{ url('/ve-dungmori') }}" class="header-menu__dropdown-item">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">VỀ DUNGMORI</div>
                                            <div class="description">Lịch sử hình thành và phát triển</div>
                                        </div>
                                    </a>
                                    <a href="{{ url('https://dungmori.co.jp') }}" class="header-menu__dropdown-item"
                                       target="_blank">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">Dung Mori Japan</div>
                                            <div class="description">Homepage chính thức tại Nhật Bản</div>
                                        </div>
                                    </a>
                                    <div onclick="goTo('{{ route('blog.index') }}')"
                                         class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'blog.index') ? 'active' : '' }}">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">Tin tức</div>
                                            <div class="description">Cập nhật tin tức và kiến thức tiếng Nhật</div>
                                        </div>
                                    </div>
                                    {{-- <div onclick="goTo('{{ url('')}}/bai-viet/cm/cau-chuyen-lop-vip')" class="header-menu__dropdown-item {{ str_contains(Request::path(), 'cm/cau-chuyen-lop-vip') ? 'active' : '' }}">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">Câu chuyện lớp VIP</div>
                                            <div class="description">Chia sẻ những câu chuyện thú vị của lớp VIP Online</div>
                                        </div>
                                    </div> --}}
                                    <div onclick="goTo('{{ route('teacher.index') }}')"
                                         class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'teacher') ? 'active' : '' }}">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">Giáo viên</div>
                                            <div class="description">Đội ngũ giáo viên của Dũng Mori</div>
                                        </div>
                                    </div>
                                    <div onclick="goTo('{{ route('review.index') }}')"
                                         class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'review') ? 'active' : '' }}">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">Review dũng mori</div>
                                            <div class="description">Chia sẻ của học viên về Khóa học</div>
                                        </div>
                                    </div>
                                    <div onclick="goTo('{{ route('recruitment') }}')"
                                         class="header-menu__dropdown-item {{ str_contains(Route::currentRouteName(), 'recruitment') ? 'active' : '' }}">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">Tuyển dụng</div>
                                            <div class="description">Trang tuyển dụng Dũng Mori</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{--                <a href="{{url('/ve-dungmori')}}" class="header-menu__item text-[0.9em] xlg:text-lg">--}}
                        {{--                    <span>Về DUNGMORI</span>--}}
                        {{--                </a>--}}
                        <a href="{{url('/groups')}}" class="header-menu__item text-[0.9em] xlg:text-lg">
                            <span>Cộng đồng</span>
                        </a>
                        <div class="header-menu__item text-[0.9em] xlg:text-lg">
                            <span>JLPT</span>
                            <div class="header-menu__dropdown mega">
                                <div class="header-menu__dropdown-inner flex">
                                    <ul class="menu-column border-right w-0.3 flex-shrink-0" role="tablist">
                                        <li class="header-menu__dropdown-item active">
                                            <a href="#jlpt" class="dropdown-parent header-menu__dropdown-item-inner"
                                               role="presentation" data-toggle="pill">
                                                <div class="flex">
                                                    <div class="">
                                                        <div class="title text-white">Học qua App/Web</div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        <li class="header-menu__dropdown-item">
                                            <a href="#kaiwa" class="dropdown-parent header-menu__dropdown-item-inner"
                                               role="presentation" data-toggle="pill">
                                                <div class="flex">
                                                    <div class="">
                                                        <div class="title text-white"
                                                             onclick="goToWindow('{{ url('https://onlinevip.dungmori.com/') }}')">
                                                            Học qua Zoom
                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        <li class="header-menu__dropdown-item">
                                            <a href="#eju" class="dropdown-parent header-menu__dropdown-item-inner"
                                               role="presentation" data-toggle="pill">
                                                <div class="flex">
                                                    <div class="">
                                                        <div class="title text-white"
                                                             onclick="ga('send', 'event', 'Menu', 'click', 'Offline'); goToWindow('{{ url('https://offline.dungmori.com/tructiep?utm_source=website&utm_medium=menu&utm_campaign=header') }}')">
                                                            Offline
                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="menu-column tab-content flex-grow-1" role="tablist">
                                        <div class="tab-pane active in min-h-full" id="jlpt">
                                            <div class="flex">
                                                <ul>
                                                    <a href="{{url('/khoa-hoc/chuyen-nganh')}}"
                                                       class="header-menu__dropdown-sub-item"
                                                       onclick="ga('send', 'event', 'Menu', 'click', 'Chuyên ngành')">
                                                        <div class="header-menu__dropdown-sub-item-inner">
                                                            <div class="title">Chuyên ngành</div>
                                                        </div>
                                                    </a>
                                                    <a href="{{url('/khoa-hoc/khoa-n5')}}"
                                                       class="header-menu__dropdown-sub-item"
                                                       onclick="ga('send', 'event', 'Menu', 'click', 'N5')">
                                                        <div class="header-menu__dropdown-sub-item-inner">
                                                            <div class="title">Khóa N5 <span
                                                                        style="font-size: 14px; background-color: #FF6531; padding: 5px 10px; border-radius: 15px; color: #fff; margin-left: 5px;">Học thử miễn phí</span>
                                                            </div>
                                                        </div>
                                                    </a>
                                                    <a href="{{url('/khoa-hoc/khoa-n4')}}"
                                                       class="header-menu__dropdown-sub-item"
                                                       onclick="ga('send', 'event', 'Menu', 'click', 'N4')">
                                                        <div class="header-menu__dropdown-sub-item-inner">
                                                            <div class="title">Khóa N4</div>
                                                        </div>
                                                    </a>
                                                    <a href="{{url('/khoa-hoc/jlpt-n3')}}"
                                                       class="header-menu__dropdown-sub-item"
                                                       onclick="ga('send', 'event', 'Menu', 'click', 'N3')">
                                                        <div class="header-menu__dropdown-sub-item-inner">
                                                            <div class="title">Khóa N3</div>
                                                        </div>
                                                    </a>
                                                    <a href="{{url('/khoa-hoc/jlpt-n2')}}"
                                                       class="header-menu__dropdown-sub-item"
                                                       onclick="ga('send', 'event', 'Menu', 'click', 'N2')">
                                                        <div class="header-menu__dropdown-sub-item-inner">
                                                            <div class="title">Khóa N2</div>
                                                        </div>
                                                    </a>
                                                    <a href="{{url('/khoa-hoc/jlpt-n1')}}"
                                                       class="header-menu__dropdown-sub-item"
                                                       onclick="ga('send', 'event', 'Menu', 'click', 'N1')">
                                                        <div class="header-menu__dropdown-sub-item-inner">
                                                            <div class="title">Khóa N1</div>
                                                        </div>
                                                    </a>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="tab-pane min-h-full" id="kaiwa">
                                            <div class="px-4">
                                                <img src="{{url('assets/img/new_home/06-2024/menu_2.png')}}"/>
                                            </div>
                                            {{--                                    <ul>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa sơ cấp')">--}}
                                            {{--                                                <div class="title">Kaiwa Sơ cấp</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa Trung cấp')">--}}
                                            {{--                                                <div class="title">Kaiwa Trung cấp</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                    </ul>--}}
                                        </div>
                                        <div class="tab-pane min-h-full" id="eju">
                                            <div class="px-4">
                                                <img src="{{url('assets/img/new_home/06-2024/menu_1.png')}}"/>
                                            </div>
                                            {{--                                    <ul>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/eju')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'EJU Tiếng Nhật')">--}}
                                            {{--                                                <div class="title">EJU Tiếng Nhật</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/eju-xhth')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'EJU XHTH')">--}}
                                            {{--                                                <div class="title">EJU XHTH</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/eju-toan')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'EJU Toán')">--}}
                                            {{--                                                <div class="title">EJU Toán</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/eju-toan-ii')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'EJU Toán II')">--}}
                                            {{--                                                <div class="title">EJU Toán II</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/eju-ly')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'EJU Lý')">--}}
                                            {{--                                                <div class="title">EJU Lý</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                        <li class="header-menu__dropdown-sub-item">--}}
                                            {{--                                            <a href="{{url('/khoa-hoc/eju-hoa')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'EJU Hoá')">--}}
                                            {{--                                                <div class="title">EJU Hoá</div>--}}
                                            {{--                                            </a>--}}
                                            {{--                                        </li>--}}
                                            {{--                                    </ul>--}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg mega mega">--}}
                        {{--                    <span>JLPT</span>--}}
                        {{--                    <div class="header-menu__dropdown">--}}
                        {{--                        <div class="header-menu__dropdown-inner flex">--}}
                        {{--                            <div class="menu-column tab-content flex-grow-1" role="tablist">--}}
                        {{--                                <div class="tab-pane active" id="luyenThi">--}}
                        {{--                                    <a href="{{url('/khoa-hoc/khoa-n5')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N5')">--}}
                        {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                        {{--                                            <div class="title">Khóa N5 <span style="font-size: 14px; background-color: #FF6531; padding: 5px 10px; border-radius: 15px; color: #fff; margin-left: 5px;">Học thử miễn phí</span></div>--}}
                        {{--                                        </div>--}}
                        {{--                                    </a>--}}
                        {{--                                    <a href="{{url('/khoa-hoc/khoa-n4')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N4')">--}}
                        {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                        {{--                                            <div class="title">Khóa N4</div>--}}
                        {{--                                        </div>--}}
                        {{--                                    </a>--}}
                        {{--                                    <a href="{{url('/khoa-hoc/khoa-n3')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N3')">--}}
                        {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                        {{--                                            <div class="title">Khóa N3</div>--}}
                        {{--                                        </div>--}}
                        {{--                                    </a>--}}
                        {{--                                    <a href="{{url('/khoa-hoc/khoa-n2')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N2')">--}}
                        {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                        {{--                                            <div class="title">Khóa N2</div>--}}
                        {{--                                        </div>--}}
                        {{--                                    </a>--}}
                        {{--                                    <a href="{{url('/khoa-hoc/khoa-n1')}}" class="header-menu__dropdown-sub-item" onclick="ga('send', 'event', 'Menu', 'click', 'N1')">--}}
                        {{--                                        <div class="header-menu__dropdown-sub-item-inner">--}}
                        {{--                                            <div class="title">Khóa N1</div>--}}
                        {{--                                        </div>--}}
                        {{--                                    </a>--}}
                        {{--                                </div>--}}
                        {{--                            </div>--}}
                        {{--                        </div>--}}
                        {{--                    </div>--}}
                        {{--                </div>--}}
                        <div class="header-menu__item text-[0.9em] xlg:text-lg">
                            <span>EJU</span>
                            <div class="header-menu__dropdown">
                                <div class="header-menu__dropdown-inner">
                                    <div class="tab-pane min-h-full" id="eju">
                                        <ul>
                                            <li class="header-menu__dropdown-sub-item">
                                                <a href="{{url('/khoa-hoc/eju')}}" class="header-menu__dropdown-sub-item-inner"
                                                   onclick="ga('send', 'event', 'Menu', 'click', 'EJU Tiếng Nhật')">
                                                    <div class="title">EJU Tiếng Nhật</div>
                                                </a>
                                            </li>
                                            <li class="header-menu__dropdown-sub-item">
                                                <a href="{{url('/khoa-hoc/eju-xhth')}}"
                                                   class="header-menu__dropdown-sub-item-inner"
                                                   onclick="ga('send', 'event', 'Menu', 'click', 'EJU XHTH')">
                                                    <div class="title">EJU XHTH</div>
                                                </a>
                                            </li>
                                            <li class="header-menu__dropdown-sub-item">
                                                <a href="{{url('/khoa-hoc/eju-toan')}}"
                                                   class="header-menu__dropdown-sub-item-inner"
                                                   onclick="ga('send', 'event', 'Menu', 'click', 'EJU Toán')">
                                                    <div class="title">EJU Toán</div>
                                                </a>
                                            </li>
                                            <li class="header-menu__dropdown-sub-item">
                                                <a href="{{url('/khoa-hoc/eju-toan-ii')}}"
                                                   class="header-menu__dropdown-sub-item-inner"
                                                   onclick="ga('send', 'event', 'Menu', 'click', 'EJU Toán II')">
                                                    <div class="title">EJU Toán II</div>
                                                </a>
                                            </li>
                                            <li class="header-menu__dropdown-sub-item">
                                                <a href="{{url('/khoa-hoc/eju-ly')}}"
                                                   class="header-menu__dropdown-sub-item-inner"
                                                   onclick="ga('send', 'event', 'Menu', 'click', 'EJU Lý')">
                                                    <div class="title">EJU Lý</div>
                                                </a>
                                            </li>
                                            <li class="header-menu__dropdown-sub-item">
                                                <a href="{{url('/khoa-hoc/eju-hoa')}}"
                                                   class="header-menu__dropdown-sub-item-inner"
                                                   onclick="ga('send', 'event', 'Menu', 'click', 'EJU Hoá')">
                                                    <div class="title">EJU Hoá</div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="header-menu__item text-[0.9em] xlg:text-lg"
                             onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa'); goToWindow('{{ url('https://kaiwa.dungmori.com/') }}')">
                            <span>Giao tiếp</span>
                            {{--                    <div class="header-menu__dropdown">--}}
                            {{--                        <div class="header-menu__dropdown-inner">--}}
                            {{--                            <div class="tab-pane min-h-full" id="kaiwa">--}}
                            {{--                                <ul>--}}
                            {{--                                    <li class="header-menu__dropdown-sub-item">--}}
                            {{--                                        <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa sơ cấp')">--}}
                            {{--                                            <div class="title">Kaiwa Sơ cấp</div>--}}
                            {{--                                        </a>--}}
                            {{--                                    </li>--}}
                            {{--                                    <li class="header-menu__dropdown-sub-item">--}}
                            {{--                                        <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}" class="header-menu__dropdown-sub-item-inner" onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa Trung cấp')">--}}
                            {{--                                            <div class="title">Kaiwa Trung cấp</div>--}}
                            {{--                                        </a>--}}
                            {{--                                    </li>--}}
                            {{--                                </ul>--}}
                            {{--                            </div>--}}
                            {{--                        </div>--}}
                            {{--                    </div>--}}
                        </div>

                        {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg">--}}

                        {{--                    <span class="text-red-500">MJT テスト--}}
                        {{--                        <img src="{{url('assets/img/onl.gif')}}" />--}}
                        {{--                    </span>--}}
                        {{--                    <div class="header-menu__dropdown">--}}
                        {{--                        <div class="header-menu__dropdown-inner">--}}
                        {{--                            <div onclick="goToWindow('{{ route('thi-thu.index') }}')" class="header-menu__dropdown-item">--}}
                        {{--                                <div class="header-menu__dropdown-item-inner">--}}
                        {{--                                    <div class="title">MJT テスト--}}
                        {{--                                        <img src="{{url('assets/img/onl.gif')}}" />--}}
                        {{--                                    </div>--}}
                        {{--                                    <div class="description">Thi thử JLPT trên Dũng Mori</div>--}}
                        {{--                                </div>--}}
                        {{--                            </div>--}}
                        {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Test online'); goToWindow('{{ route('test-online.index') }}')" class="header-menu__dropdown-item">--}}
                        {{--                                <div class="header-menu__dropdown-item-inner">--}}
                        {{--                                    <div class="title">Test online</div>--}}
                        {{--                                    <div class="description">Làm bài tập đầu giờ các nhóm zoom</div>--}}
                        {{--                                </div>--}}
                        {{--                            </div>--}}
                        {{--                            --}}{{--                                    <div onclick="goToWindow('{{ route('ad-test-fe.index') }}')" class="header-menu__dropdown-item">--}}
                        {{--                            --}}{{--                                        <div class="header-menu__dropdown-item-inner">--}}
                        {{--                            --}}{{--                                            <div class="title">Đồng hồ báo thức mùa thi</div>--}}
                        {{--                            --}}{{--                                            <div class="description"></div>--}}
                        {{--                            --}}{{--                                        </div>--}}
                        {{--                            --}}{{--                                    </div>--}}
                        {{--                        </div>--}}
                        {{--                    </div>--}}
                        {{--                </div>--}}
                        {{-- <div onclick="goTo('{{ route('thi-thu.index') }}')" class="header-menu__item text-[0.9em] xlg:text-lg featured {{ Route::currentRouteNamed('thi-thu') ? 'active' : '' }}">
                            <span>MJT テスト</span>
                        </div> --}}
                        {{-- <div onclick="goTo('{{ route('test-online.index') }}')" class="header-menu__item text-[0.9em] xlg:text-lg {{ Route::currentRouteNamed('test-online.index') ? 'active' : '' }}">
                            <span>Test Online</span>
                        </div> --}}
                        {{--                <a href="{{ url('/groups') }}" class="header-menu__item text-[0.9em] xlg:text-lg" onclick="ga('send', 'event', 'Menu', 'click', 'Community')">--}}
                        {{--                    <span>Cộng đồng</span>--}}
                        {{--                </a>--}}

                        {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg">--}}
                        {{--                    <span>Online Vip</span>--}}
                        {{--                    <div class="header-menu__dropdown">--}}
                        {{--                        <div class="header-menu__dropdown-inner">--}}
                        {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Online VIP'); goToWindow('{{ url('https://onlinevip.dungmori.com') }}')" class="header-menu__dropdown-item">--}}
                        {{--                                <div class="header-menu__dropdown-item-inner">--}}
                        {{--                                    <div class="title">Online VIP</div>--}}
                        {{--                                    <div class="description">Học qua phần mềm Zoom</div>--}}
                        {{--                                </div>--}}
                        {{--                            </div>--}}
                        {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Online Plus'); goToWindow('{{ url('https://onlineplus.dungmori.com') }}')" class="header-menu__dropdown-item">--}}
                        {{--                                <div class="header-menu__dropdown-item-inner">--}}
                        {{--                                    <div class="title">Online Plus</div>--}}
                        {{--                                    <div class="description">Học qua phần mềm Livestream</div>--}}
                        {{--                                </div>--}}
                        {{--                            </div>--}}
                        {{--                            <div onclick="ga('send', 'event', 'Menu', 'click', 'Kaiwa'); goToWindow('{{ url('https://kaiwa.dungmori.com/') }}')" class="header-menu__dropdown-item">--}}
                        {{--                                <div class="header-menu__dropdown-item-inner">--}}
                        {{--                                    <div class="title">Kaiwa</div>--}}
                        {{--                                    <div class="description">Học qua phần mềm Zoom</div>--}}
                        {{--                                </div>--}}
                        {{--                            </div>--}}
                        {{--                        </div>--}}
                        {{--                    </div>--}}
                        {{--                </div>--}}
                        {{--                <div class="header-menu__item text-[0.9em] xlg:text-lg" onclick="ga('send', 'event', 'Menu', 'click', 'Offline'); goToWindow('https://offline.dungmori.com/tructiep?utm_source=website&utm_medium=menu&utm_campaign=header')">--}}
                        {{--                    <span>Offline</span>--}}
                        {{--                </div>--}}
                        <div class="header-menu__item text-[0.9em] xlg:text-lg"
                             onclick="ga('send', 'event', 'Menu', 'click', 'Book'); goToWindow('https://sach.dungmori.com')">
                            <span>Sách</span>
                        </div>
                        <div class="header-menu__item text-[0.9em] xlg:text-lg">
                            <div>B2B</div>
                            <div class="header-menu__dropdown">
                                <div class="header-menu__dropdown-inner">
                                    <div onclick="ga('send', 'event', 'Menu', 'click', 'B2B'); goToWindow('https://online.dungmori.com/b2b')"
                                         class="header-menu__dropdown-item">
                                        <div class="header-menu__dropdown-item-inner">
                                            <div class="title">Khóa doanh nghiệp</div>
                                            <div class="description">Hợp tác cùng với Dũng Mori</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @if (auth()->check())
                        <a href="{{ route('frontend.user.account.rewards') }}"
                           class="border-2 border-white bg-white rounded-full py-2 px-4 text-dmr-green-dark hover:text-dmr-green-dark font-quicksand font-bold flex items-center gap-1 justify-center cursor-pointer hover:bg-[#F7FFF0]"
                           onclick="ga('send', 'event', 'Menu', 'click', 'Thành tích')">
                            <img src="{{ asset('assets/img/gamification/leaf.svg') }}" alt="" class="w-5 shrink-0">
                            {{ auth()->user()->getPoints() }}
                        </a>
                    @else
                        <div class="border border-white bg-white rounded-full py-2 px-4 text-dmr-green-dark font-quicksand font-bold flex items-center gap-1 justify-center">
                            <img src="{{ asset('assets/img/gamification/leaf.svg') }}" alt="" class="w-5 shrink-0">
                            0
                        </div>
                    @endif

                    <div class="flex justify-end items-center gap-3">
                        @if (Auth::check())
                            <div class="tooltip-i">
                                <svg onclick="ga('send', 'event', 'Menu', 'click', 'My courses'); myCourseClick()" width="17"
                                     height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"
                                     class="w-5 cursor-pointer stroke-current text-white hover:text-dmr-green-dark">
                                    <path d="M6.90619 3.5C6.90619 5.08344 5.5237 6.43 3.7381 6.43C1.95249 6.43 0.57 5.08344 0.57 3.5C0.57 1.91656 1.95249 0.57 3.7381 0.57C5.5237 0.57 6.90619 1.91656 6.90619 3.5Z"
                                          stroke-width="2"/>
                                    <path d="M16.1625 3.5C16.1625 5.08344 14.7801 6.43 12.9944 6.43C11.2088 6.43 9.82635 5.08344 9.82635 3.5C9.82635 1.91656 11.2088 0.57 12.9944 0.57C14.7801 0.57 16.1625 1.91656 16.1625 3.5Z"
                                          stroke-width="2"/>
                                    <path d="M6.90619 12.1665C6.90619 13.7499 5.5237 15.0965 3.7381 15.0965C1.95249 15.0965 0.57 13.7499 0.57 12.1665C0.57 10.5831 1.95249 9.2365 3.7381 9.2365C5.5237 9.2365 6.90619 10.5831 6.90619 12.1665Z"
                                          stroke-width="2"/>
                                    <path d="M16.1625 12.1665C16.1625 13.7499 14.7801 15.0965 12.9944 15.0965C11.2088 15.0965 9.82635 13.7499 9.82635 12.1665C9.82635 10.5831 11.2088 9.2365 12.9944 9.2365C14.7801 9.2365 16.1625 10.5831 16.1625 12.1665Z"
                                          stroke-width="2"/>
                                </svg>
                                <span class="tooltiptext">Khóa học của tôi</span>
                            </div>
                            <div id="my-course" style="display: none;">
                                <div class="my-course-main">
                                    <div class="my-course-container">
                                        <div class="title-box">
                                            <span class="title">Khóa học của tôi</span><br>
                                            <span class="description">Các khóa bạn đang sở hữu, học ngay!</span>
                                            <i class="fa fa-window-close" aria-hidden="true" onclick="myCourseClick()"></i>
                                        </div>
                                        <hr>
                                        <div id="my-course-content" class="my-course-content" style="display: none;">
                                            <div v-if="myCourses.length === 0" class="empty-box">
                                                <img class="lazyload object-cover"
                                                     data-src="{{url('assets/img/img_empty.png')}}">
                                                <span>Bạn không sở hữu khóa học nào</span>
                                                <a href="{{url('/bang-gia')}}">Mua ngay</a>
                                            </div>
                                            <a v-for="course in myCourses" :href="'{{url('/khoa-hoc')}}/' + course.SEOurl">
                                                <div class="my-course-item">
                                                    <img :src="'{{asset('cdn/course/small')}}/' + course.avatar_name">
                                                    <span>
                                                <i class="fa fa-circle" aria-hidden="true"></i> <b>@{{ course.name }}</b> <i
                                                                class="fa fa-circle" aria-hidden="true"></i>
                                            </span>
                                                    <span>
                                                <i class="fa fa-circle" aria-hidden="true"></i> Hết hạn:&nbsp;<b>@{{ course.expired_day }}</b> <i
                                                                class="fa fa-circle" aria-hidden="true"></i>
                                            </span>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <script type="text/javascript">
                                function myCourseClick(onlyClose = false) {
                                    var myCourseDiv = document.getElementById("my-course");
                                    if (!onlyClose && myCourseDiv.style.display === 'none') {
                                        $('#my-course').css('display', 'block');
                                        myCourseDiv.classList.remove("my-course-hide");
                                        myCourseDiv.classList.add("my-course-show");
                                        if (myCoursesVue) {
                                            myCoursesVue.getMyCourses();
                                        }
                                    } else {
                                        myCourseDiv.classList.remove("my-course-show");
                                        myCourseDiv.classList.add("my-course-hide");
                                        setTimeout(function () {
                                            $('#my-course').css('display', 'none');
                                        }, 200);
                                    }
                                }

                                var mycourse = document.getElementById('my-course');
                                mycourse.onclick = function (event) {
                                    if (event.target == mycourse) {
                                        myCourseClick();
                                    }
                                }
                            </script>
                        @endif


                        <a href="{{url('/khoa-hoc')}}"
                           class="cursor-pointer text-white stroke-current fill-current hover:text-dmr-green-dark"
                           onclick="ga('send', 'event', 'Menu', 'click', 'Store')">
                            <div class="tooltip-i">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                     class="w-4">
                                    <path d="M1.02191 1.28948H22.6139V20.2245C22.6139 21.6919 21.4243 22.8815 19.957 22.8815H3.67886C2.21146 22.8815 1.02191 21.6919 1.02191 20.2245V1.28948Z"
                                          stroke-width="2.5"/>
                                    <path d="M5.41602 7.4082C7.5498 11.2654 13.0484 16.6656 17.9725 7.4082" stroke-width="2.5"/>
                                </svg>
                                <span class="tooltiptext">Cửa hàng</span>
                            </div>
                        </a>
                        <div id="maintain-container" style="display: none;background: none; text-align: center;">
                            <img class="lazyload object-cover" data-src="{{ asset('assets/img/new_home/maintain-web.png') }}"
                                 alt="" style="max-width: 50%; margin: auto;">
                        </div>
                        <style>
                            #jlpt-gift-container {
                                display: none;
                                background: none;
                                text-align: center;
                            }

                            #jlpt-gift-container .jlpt-gift-content {
                                background: white;
                                border-radius: 15px;
                            }

                            #jlpt-gift-container .jlpt-gift-content > p {
                                font-family: 'Quicksand';
                                font-style: normal;
                                font-weight: 500;
                                font-size: 30px;
                                line-height: 1.61;
                                text-transform: uppercase;
                            }

                            #jlpt-gift-container .jlpt-gift-content > p:nth-child(2) {
                                font-weight: bold;
                                color: #96D962;
                            }

                            #jlpt-gift-container .jlpt-gift-content .quote {
                                width: 388px;
                                font-weight: 600;
                                font-size: 16px;
                                line-height: 1.61;
                                margin-top: 20px;
                            }

                            .jlpt-gift-content .btn {
                                margin-top: 30px;
                                padding: 15px 34px;
                                font-family: 'Montserrat';
                                font-style: normal;
                                font-weight: 700;
                                font-size: 20px;
                                line-height: 1.2;
                                color: #FFFFFF;
                                border-radius: 15px;
                            }

                            .jlpt-gift-content .btn-danger {
                                background-color: #FB6D3A;
                                margin-right: 20px;
                            }

                            .jlpt-gift-content .btn-info {
                                background-color: #1093F1;
                            }

                            .jlpt-gift-policy {
                                margin-left: 24px;
                                max-width: 350px;
                                text-align: left;
                            }

                            .jlpt-gift-policy .uppercase {
                                text-transform: uppercase;
                            }

                            .jlpt-gift-policy .text-red {
                                color: #FB6D3A;
                            }

                            .jlpt-gift-policy .text-blue {
                                color: #0E65E5;
                            }

                            input::-webkit-outer-spin-button,
                            input::-webkit-inner-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                            }

                            .btn-upload {
                                font-family: Quicksand;
                                background: #F7FFF0;
                                border: 1px solid #96D962;
                                border-radius: 10px;
                                display: flex;
                                flex-direction: row;
                                justify-content: center;
                                align-items: center;
                                padding: 8px 50px;
                                gap: 10px;
                                color: green !important;
                                font-size: 16px !important;
                                font-weight: 400 !important;
                                margin-top: 0 !important;
                            }

                            .point-input {
                                position: relative;
                            }

                            .point-label {
                                position: absolute;
                                top: -10px;
                                left: 9px;
                                background: white;
                                color: #BDBDBD !important;
                            }

                            .fancybox-slide:before {
                                height: 0 !important;
                            }

                            @media only screen and (max-width: 992px) {
                                .two-columns {
                                    flex-flow: column;
                                    align-items: center;
                                }

                                .jlpt-gift-content {
                                    /*padding: 40px 5px;*/
                                }

                                .fancybox-slide > * {
                                    padding: 0;
                                }
                            }

                            #point-input-1,
                            #point-input-2,
                            #point-input-3,
                            #point-input-total {
                                font-weight: bold;
                                font-size: 13px;
                                border-bottom: 1px solid #000;
                                width: 5%;
                                text-align: right;
                                line-height: 1;
                            }
                        </style>
                        <div id="jlpt-gift-container" class="bg-gray-50">
                            <div class="jlpt-gift-content px-10 py-10 sm:px-[100px] sm:py-[40px]">
                                <p>Báo thành tích JLPT</p>
                                <p>Rinh quà từ dũng mori</p>
                                <template v-if="isPassed == null">
                                    <img src="{{ asset('assets/img/new_home/jlpt-gift.png') }}" alt=""
                                         style="margin-top: 60px; max-width: 180px; margin: auto;">
                                    <p class="quote">Dù trượt hay đỗ JLPT thì Dũng mori đều có
                                        món quà nhỏ gửi đến các bạn!</p>
                                    <div class="flex justify-center items-center">
                                        <button class="btn btn-danger" @click="isPassed = 'preFailed'">Trượt JLPT</button>
                                        <button class="btn btn-info" @click="isPassed = 'prePassed'">Đỗ JLPT</button>
                                    </div>
                                </template>
                                <template v-else-if="isPassed == 'prePassed'">
                                    <div class="p-5 mt-2 border-2 border-[#96D962] rounded-xl w-[95%] sm:w-[60%] m-auto text-left bg-green-50 leading-10 max-w-[1024px]">
                                        <div class="text-md font-semibold">HỌC VIÊN ĐỖ:</div>
                                        <p class="text-md leading-6">
                                            - Đỗ -> <b>129 điểm: Giảm 200k </b> khi đăng ký học khóa học bất kỳ của Dũng Mori
                                            (được cộng
                                            gộp ưu đãi học viên cũ)
                                            <br>
                                            - <b>130 - 149 điểm: </b> Thưởng <b>200.000</b> tiền mặt
                                            <br>
                                            - <b>150 - 175 điểm: </b> Thưởng <b>500.000 </b> tiền mặt
                                            <br>
                                            - Trên <b>175 điểm: </b> Thưởng <b>1.000.000 </b> tiền mặt
                                        </p>
                                        <div class="text-center">
                                            ---------------------------------------------------------------------------
                                        </div>
                                        <p class="text-md leading-6">
                                            <b>Điều kiện:</b> Báo điểm thông qua link: <a
                                                    href="https://dungmori.com/nhan-qua-jlpt" target="_blank">https://dungmori.com/nhan-qua-jlpt</a>
                                            hoặc ngay tại popup này để tạo ra 1 bằng khen của Dũng Mori, đăng ảnh bằng khen này
                                            lên FB/Tiktok cá nhân kèm bài viết chia sẻ cảm nghĩ về trung tâm và hashtag <span
                                                    class="text-green-500">#hoctiengnhat #dungmori #reviewdungmori</span> sau đó
                                            điền link bài đăng vào form:
                                            <a href="https://bit.ly/baodiemJLPT"
                                               target="_blank">https://bit.ly/baodiemJLPT</a>
                                            <br>
                                            <b>- THỜI GIAN BÁO ĐIỂM HỢP LỆ: Tính từ ngày 26/08 đến hết ngày 01/09/2024 </b><br>
                                            <b>- THỜI GIAN NHẬN THƯỞNG Trong 2 ngày 09 và 10/09/2024</b> <br>
                                            <b>- THỜI GIAN SỬ DỤNG VOUCHER: Đến 03/10/2024</b> <br>
                                        </p>
                                    </div>
                                    <button class="btn bg-[#96D962]" @click="isPassed = true">Tiếp tục</button>
                                </template>
                                <template v-else-if="isPassed == 'preFailed'">
                                    <div class="p-5 mt-2 border-2 border-[#96D962] rounded-xl w-[60%] m-auto text-left leading-5 bg-green-50">
                                        <div class="text-md font-semibold">HỌC VIÊN TRƯỢT</div>
                                        <p class="text-md leading-6">
                                            Được mở thêm 1 tháng khóa online và giảm 10% khi đăng ký lại cùng cấp độ
                                        </p>
                                        ---------------------------------------------
                                        <div class="text-md font-semibold leading-6">Lưu ý:</div>
                                        <p class="text-md">
                                            <b>- Tất cả thông tin trong bảng “BÁO ĐIỂM THI” cần được điền đầy đủ và chính xác.
                                                Nếu thiếu thông tin nào thì tài khoản báo điểm mặc định không được xác minh quà
                                                tặng. </b><br>
                                            <b>- Điểm thi hợp lệ chỉ được tính trong kỳ thi JLPT tháng 7/2024 </b><br>
                                            <b>- THỜI GIAN BÁO ĐIỂM HỢP LỆ: Tính từ ngày 26/08 đến hết ngày 01/09/2024 </b><br>
                                            <b>- THỜI GIAN NHẬN THƯỞNG: Trong 2 ngày 09 và 10/09/2024</b> <br>
                                            <b>- THỜI GIAN SỬ DỤNG VOUCHER: Đến 03/10/2024</b> <br>
                                        </p>
                                    </div>
                                    <button class="btn bg-[#96D962]" @click="isPassed = false">Tiếp tục</button>
                                </template>
                                <template v-else-if="isPassed == true">
                                    @if (!auth()->check() && !isset($achievement))
                                        <div class="flex justify-center items-center" style="min-height: 500px;">
                                            <button class="btn btn-success" data-fancybox data-animation-duration="300"
                                                    data-src="#auth-container" onclick="swichTab('login')">Đăng nhập
                                            </button>
                                        </div>
                                    @else
                                        <div v-if="!success"
                                             class="w-[300px] flex flex-col justify-center items-center mt-5 mx-auto">
                                            <div class="w-full point-input mb-4">
                                                <div class="point-label">Họ và tên</div>
                                                <input v-model="name" type="text"
                                                       style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                            </div>
                                            <div class="w-full point-input mb-4">
                                                <div class="point-label">Trình độ</div>
                                                <select name="" id="" v-model="level"
                                                        style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                                >
                                                    <option value="N1">N1</option>
                                                    <option value="N2">N2</option>
                                                    <option value="N3">N3</option>
                                                    <option value="N4">N4</option>
                                                    <option value="N5">N5</option>
                                                </select>
                                            </div>
                                            <div class="w-full point-input mb-4">
                                                <div class="point-label">Tổng điểm</div>
                                                <input v-model="point" type="text"
                                                       style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                            </div>
                                            <button v-if="!imageName" type="button"
                                                    class="w-full rounded bg-green-50 px-2 py-1 font-semibold text-green-600 shadow-sm hover:bg-green-100 text-lg"
                                                    @click="selectFile">
                                                <i class="fa fa-camera"></i> Tải ảnh
                                            </button>
                                            <div v-else class="w-full flex items-center">
                                                @{{ imageName }}
                                                <i class="fa fa-times ml-2 a-cursor-pointer" @click="removeImage"></i>
                                            </div>
                                            <input id="jlptGiftImage" type="file" style="display: none;" @change="uploadFile"
                                                   accept="image/*"/>
                                            {{--                                    <div class="w-full text-left mt-3 mb-4 font-quicksand font-semibold text-md">3 người bạn mà bạn muốn chia sẻ cùng</div>--}}
                                            {{--                                    <div class="w-full point-input mb-4">--}}
                                            {{--                                        <div class="point-label">Họ tên người bạn thứ nhất</div>--}}
                                            {{--                                        <input v-model="friend1" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />--}}
                                            {{--                                    </div>--}}
                                            {{--                                    <div class="w-full point-input mb-4">--}}
                                            {{--                                        <div class="point-label">Họ tên người bạn thứ hai</div>--}}
                                            {{--                                        <input v-model="friend2" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />--}}
                                            {{--                                    </div>--}}
                                            {{--                                    <div class="w-full point-input mb-4">--}}
                                            {{--                                        <div class="point-label">Họ tên người bạn thứ ba</div>--}}
                                            {{--                                        <input v-model="friend3" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />--}}
                                            {{--                                    </div>--}}
                                        </div>
                                        <div v-if="success && id" class="flex gap-5 flex-col sm:flex-row relative">
                                            <img style="object-fit: contain" :src="url + '/nhan-qua-jlpt/gen-img/' + id" alt=""
                                                 class="shadow-lg border border-t border-gray-900 rounded-md max-w-full md:max-w-[400px] order-2 md:order-1">
                                            <div class="max-w-[500px] font-quicksand text-left text-base mt-3 md:mt-0 leading-8 order-1 md:order-3">
                                                Yay! Bạn đã rất gần với phần quà của chúng mình rồi. <span
                                                        class="text-[#96D962] font-semibold">Dũng Mori</span> cần một vài thông
                                                tin để thuận tiện cho việc gửi phần thưởng đến tận tay học viên. Bạn vui lòng
                                                làm theo các bước sau đây bạn nhé!
                                                <div class="mt-2">
                                                    <b>Bước 1:</b> Tải ảnh bằng khen, đăng CÔNG KHAI lên Facebook/Tiktok cá nhân
                                                    kèm bài viết chia sẻ cảm nghĩ về trung tâm và hashtag <span
                                                            class="text-green-500">#hoctiengnhat #dungmori #reviewdungmori</span>
                                                </div>
                                                <div class="mt-2">
                                                    <b>Bước 2:</b> Truy cập form sau đây: <a href="https://bit.ly/baodiemJLPT"
                                                                                             target="_blank">https://bit.ly/baodiemJLPT</a>
                                                    để điền thông tin nhận thưởng nhé!
                                                </div>
                                                <div class="flex items-center justify-center sm:justify-start mt-3 mb-3">
                                                    <button style="background: #57d061"
                                                            class="justify-center mr-1 w-1/2 flex items-center text-center text-base font-semibold py-2.5 leading-0 rounded-md bg-blue-700 text-white"
                                                            @click="downloadImage()">
                                                        <i class="fa fa-download mr-2"></i>
                                                        Tải ảnh xuống
                                                    </button>
                                                    <button style="background: #0084ff"
                                                            class="justify-center mr-1 w-1/2 flex items-center text-center text-base font-semibold py-2.5 leading-0 rounded-md bg-blue-700 text-white"
                                                            @click="shareContent(url + '/assets/img/jlpt/img-user/' + user_id + '.png')"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" width="16px"
                                                             height="16px">
                                                            <path d="M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z"
                                                                  fill="#ffffff"/>
                                                        </svg>
                                                        Chia sẻ
                                                    </button>
                                                </div>
                                                <button @click="openChatbox" type="button" style="background: rgb(87, 208, 97)"
                                                        class="w-full py-2 px-4 flex justify-center items-center bg-[#0084ff] focus:ring-blue-500 focus:ring-offset-blue-200 text-white transition ease-in duration-200 text-center text-base font-semibold shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-lg">
                                                    <svg width="42" height="38" viewBox="0 0 42 38" fill="none"
                                                         xmlns="http://www.w3.org/2000/svg" class="w-6 lg:w-[18px]">
                                                        <path d="M21.0298 0.00783166C9.64407 0.00783166 0.380859 8.38874 0.380859 18.6888C0.380859 28.9889 9.64407 37.3698 21.0298 37.3698C24.9127 37.3698 28.6641 36.4014 31.9383 34.5527L38.9803 37.2466C39.2107 37.3346 39.441 37.3698 39.6549 37.3698C40.1979 37.3698 40.7408 37.1233 41.1357 36.6831C41.6951 36.0493 41.8432 35.1337 41.5306 34.3414L38.9968 27.9149C40.7737 25.0978 41.6951 21.9285 41.6951 18.6712C41.6951 8.37115 32.4319 -0.00976562 21.0462 -0.00976562L21.0298 0.00783166ZM34.7847 28.4959L35.9694 31.5067L32.4154 30.151C31.856 29.9397 31.2308 29.9925 30.7043 30.327C27.8579 32.1053 24.5014 33.0385 21.0133 33.0385C11.8653 33.0385 4.42838 26.6119 4.42838 18.6888C4.42838 10.7657 11.8653 4.33915 21.0133 4.33915C30.1613 4.33915 37.5982 10.7657 37.5982 18.6888C37.5982 21.3827 36.7098 24.0237 35.048 26.3126C34.5873 26.9289 34.4886 27.774 34.7847 28.4959ZM13.7409 17.157C14.1194 17.562 14.3333 18.1254 14.3333 18.6888C14.3333 18.8297 14.3168 18.9705 14.3004 19.1114C14.2675 19.2522 14.2346 19.3931 14.1852 19.5163C14.1358 19.6396 14.07 19.7629 13.9877 19.8861C13.9219 20.0093 13.8232 20.115 13.7409 20.2206C13.6422 20.3263 13.5435 20.4143 13.4283 20.4847C13.3132 20.5728 13.198 20.6256 13.0828 20.6784C12.9676 20.7312 12.836 20.7841 12.7044 20.8017C12.5728 20.8369 12.4411 20.8369 12.3095 20.8369C11.783 20.8369 11.2565 20.608 10.8781 20.203C10.7794 20.0974 10.6971 19.9918 10.6313 19.8685C10.5655 19.7453 10.4996 19.622 10.4503 19.4987C10.4009 19.3755 10.368 19.2346 10.3351 19.0938C10.3187 18.9529 10.3022 18.8121 10.3022 18.6712C10.3022 18.5304 10.3187 18.3895 10.3351 18.2486C10.368 18.1078 10.4009 17.9669 10.4503 17.8437C10.4996 17.7204 10.5655 17.5972 10.6313 17.474C10.7135 17.3507 10.7958 17.2451 10.8781 17.1394C11.3552 16.6288 12.0463 16.3999 12.7044 16.5408C12.836 16.5584 12.9512 16.6112 13.0828 16.664C13.198 16.7168 13.3296 16.7873 13.4283 16.8577C13.5435 16.9457 13.6422 17.0338 13.7409 17.1218V17.157ZM23.0042 18.2663C23.0206 18.4071 23.0371 18.548 23.0371 18.6888C23.0371 18.8297 23.0371 18.9705 23.0042 19.1114C22.9712 19.2522 22.9383 19.3931 22.889 19.5163C22.8396 19.6396 22.7738 19.7629 22.6915 19.8861C22.6257 20.0093 22.527 20.115 22.4447 20.2206C22.0663 20.6256 21.5398 20.8545 21.0133 20.8545C20.8817 20.8545 20.7501 20.8545 20.6184 20.8193C20.4868 20.8017 20.3716 20.7488 20.24 20.696C20.1248 20.6432 20.0097 20.5728 19.8945 20.5024C19.7793 20.4143 19.6806 20.3439 19.5819 20.2382C19.4832 20.1326 19.4009 20.0269 19.3351 19.9037C19.2693 19.7804 19.2034 19.6572 19.1541 19.534C19.1047 19.4107 19.0718 19.2698 19.0389 19.129C19.0225 18.9881 19.006 18.8473 19.006 18.7064C19.006 18.5656 19.0225 18.4247 19.0389 18.2839C19.0718 18.143 19.1047 18.0022 19.1541 17.8789C19.2034 17.7557 19.2693 17.6324 19.3351 17.5092C19.4173 17.3859 19.4996 17.2803 19.5819 17.1746C19.6806 17.069 19.7793 16.9809 19.8945 16.9105C20.0097 16.8225 20.1248 16.7697 20.24 16.7168C20.3716 16.664 20.4868 16.6112 20.6184 16.5936C21.2766 16.4528 21.9841 16.6816 22.4447 17.1922C22.5435 17.2979 22.6257 17.4035 22.6915 17.5268C22.7738 17.65 22.8232 17.7733 22.889 17.8965C22.9383 18.0198 22.9712 18.1606 23.0042 18.3015V18.2663ZM31.7244 18.2663C31.7409 18.4071 31.7573 18.548 31.7573 18.6888C31.7573 18.8297 31.7573 18.9705 31.7244 19.1114C31.6915 19.2522 31.6586 19.3931 31.6092 19.5163C31.5599 19.6396 31.4941 19.7629 31.4118 19.8861C31.346 20.0093 31.2473 20.115 31.165 20.2206C31.0663 20.3263 30.9676 20.4143 30.8524 20.4847C30.7372 20.5728 30.622 20.6256 30.5069 20.6784C30.3917 20.7312 30.2601 20.7841 30.1284 20.8017C29.9968 20.8369 29.8652 20.8369 29.7336 20.8369C29.2071 20.8369 28.6805 20.608 28.3021 20.203C28.2034 20.0974 28.1211 19.9918 28.0553 19.8685C27.9895 19.7453 27.9237 19.622 27.8743 19.4987C27.825 19.3755 27.7921 19.2346 27.7592 19.0938C27.7427 18.9529 27.7263 18.8121 27.7263 18.6712C27.7263 18.5304 27.7427 18.3895 27.7592 18.2486C27.7921 18.1078 27.825 17.9669 27.8743 17.8437C27.9237 17.7204 27.9895 17.5972 28.0553 17.474C28.1376 17.3507 28.2199 17.2451 28.3021 17.1394C28.7793 16.6288 29.4703 16.3999 30.1284 16.5408C30.2601 16.5584 30.3752 16.6112 30.5069 16.664C30.622 16.7168 30.7537 16.7873 30.8524 16.8577C30.9676 16.9457 31.0663 17.0338 31.165 17.1218C31.2637 17.2274 31.346 17.3331 31.4118 17.4563C31.4941 17.5796 31.5434 17.7028 31.6092 17.8261C31.6586 17.9493 31.6915 18.0902 31.7244 18.231V18.2663Z"
                                                              fill="white"></path>
                                                    </svg>
                                                    <span class="leading-0 ml-1"> Liên hệ nhận quà</span>
                                                </button>

                                            </div>
                                        </div>
                                        <button v-else class="btn bg-[#96D962]" style="margin: 15px 80px;" :disabled="disabled"
                                                @click="upload">
                                            <i class="fa fa-spinner fa-pulse" v-if="loading"></i>
                                            Gửi thông tin
                                        </button>
                                    @endif
                                </template>
                                <template v-else>
                                    @if (!auth()->check() && !isset($achievement))
                                        <div class="flex justify-center items-center" style="min-height: 500px;">
                                            <button class="btn btn-success" data-fancybox data-animation-duration="300"
                                                    data-src="#auth-container" onclick="swichTab('login')">Đăng nhập
                                            </button>
                                        </div>
                                    @else
                                        <div>
                                            <div v-if="!success" class="flex flex-col justify-center items-center mt-5"
                                                 style="margin: 7px;">
                                                <div class="point-input mb-4">
                                                    <div class="point-label">Họ và tên</div>
                                                    <input v-model="name" type="text"
                                                           style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                                </div>
                                                <div class="point-input mb-4">
                                                    <div class="point-label">Trình độ</div>
                                                    <select name="" id="" v-model="level"
                                                            style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                                    >
                                                        <option value="N1">N1</option>
                                                        <option value="N2">N2</option>
                                                        <option value="N3">N3</option>
                                                        <option value="N4">N4</option>
                                                        <option value="N5">N5</option>
                                                    </select>
                                                </div>
                                                <div class="point-input mb-4">
                                                    <div class="point-label">Tổng điểm</div>
                                                    <input v-model="point" type="text"
                                                           style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                                </div>
                                                {{--                                                    <div class="mb-4">--}}
                                                {{--                                                        <div class="text-left mb-3 flex items-center w-[300px]">--}}
                                                {{--                                                            <input type="checkbox" v-model="showCaptionInput" id="post_to_community" class="m-0">--}}
                                                {{--                                                            <label for="post_to_community" class="font-normal m-0 ml-2"> Đăng bài lên cộng dồng Dungmori</label>--}}
                                                {{--                                                        </div>--}}
                                                {{--                                                        <div v-if="showCaptionInput" class="point-input">--}}
                                                {{--                                                            <div class="point-label">Caption đăng bài lên cộng đồng Dungmori</div>--}}
                                                {{--                                                            <textarea v-model="caption" style="width: 300px;height: 100px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;padding:12px;"></textarea>--}}
                                                {{--                                                        </div>--}}
                                                {{--                                                    </div>--}}
                                                <button v-if="!imageName" type="button"
                                                        class="w-[300px] py-3 rounded bg-green-50 px-2 py-1 text-xs font-semibold text-green-600 shadow-sm hover:bg-green-100 text-lg"
                                                        @click="selectFile">
                                                    <i class="fa fa-camera"></i> Tải ảnh
                                                </button>
                                                <div v-else class="flex items-center">
                                                    @{{ imageName }}
                                                    <i class="fa fa-times ml-2 a-cursor-pointer" @click="removeImage"></i>
                                                </div>
                                                <input id="jlptGiftImage" type="file" accept="image/*" style="display: none;"
                                                       @change="uploadFile"/>
                                            </div>
                                            {{--                                    <div class="grid grid-cols-2 gap-5 mt-5">--}}
                                            {{--                                        <div--}}
                                            {{--                                                v-for="gift in failedGift"--}}
                                            {{--                                                class="select-none px-4 py-12 duration-100 hover:shadow-md hover:-translate-y-1 rounded-lg border border-2 border-gray-200 cursor-pointer flex flex-col justify-start items-center"--}}
                                            {{--                                                :class="[--}}
                                            {{--                                                            gift.value == choseGift ? 'bg-[#96D962] border-[#96D962]' : '',--}}
                                            {{--                                                          ]"--}}
                                            {{--                                                @click="chooseGift(gift.value)"--}}
                                            {{--                                        >--}}
                                            {{--                                            <img :src="url + '/assets/img/new_home/12-2021/' + gift.img"  class="max-w-6xl max-h-24"/>--}}
                                            {{--                                            <span class="mt-5 text-md font-semibold italic" :class="[gift.value == choseGift ? 'text-white' : '',]">@{{ gift.label }}</span>--}}
                                            {{--                                        </div>--}}
                                            {{--                                    </div>--}}
                                            <button v-if="!success" class="btn bg-[#96D962]" style="margin: 15px 80px;"
                                                    :disabled="disabled" @click="upload">
                                                <i class="fa fa-spinner fa-pulse" v-if="loading"></i>
                                                Gửi thông tin
                                            </button>
                                        </div>
                                        <div v-if="!is_upload && success">
                                            Bạn đã gửi thông tin trước đó
                                        </div>
                                    @endif
                                </template>
                            </div>
                        </div>
                        @if (!Auth::check())
                            @include('frontend._layouts.auth')
                            <div class="flex items-center gap-1 bg-[#EC6E23] border-2 border-white rounded-[50px] py-2 px-5 text-white">
                                <div data-fancybox data-animation-duration="300" data-src="#auth-container"
                                     class="cursor-pointer no-underline uppercase font-semibold">
                                    <div class="text-white" id="text-login"
                                         onclick="ga('send', 'event', 'Menu', 'click', 'Login'); swichTab('login')">Đăng nhập
                                    </div>
                                </div>
                                /
                                <div data-fancybox data-animation-duration="300" data-src="#auth-container"
                                     class="!text-white cursor-pointer no-underline uppercase font-semibold">
                                    <div class="text-white"
                                         onclick="ga('send', 'event', 'Menu', 'click', 'Register'); swichTab('register')">Đăng
                                        ký
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="text-white hover:text-dmr-green-dark has-badge cursor-pointer"
                                 onclick="ga('send', 'event', 'Menu', 'click', 'Chat'); showChatbox()">
                                <div id="auth-container" v-if="countUnreadMess > 0">
                                    <span class="badge--red countMess" v-html="countUnreadMess"></span>
                                </div>
                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                                     class="w-5 stroke-current">
                                    <path d="M5.43874 14.1944L3.17446 16.0979C3.09246 16.1668 2.99249 16.2109 2.8863 16.2249C2.78011 16.239 2.67211 16.2224 2.57501 16.1772C2.47791 16.132 2.39574 16.06 2.33816 15.9696C2.28059 15.8793 2.25 15.7744 2.25 15.6673V4.5C2.25 4.35082 2.30926 4.20774 2.41475 4.10225C2.52024 3.99676 2.66332 3.9375 2.8125 3.9375H15.1875C15.3367 3.9375 15.4798 3.99676 15.5852 4.10225C15.6907 4.20774 15.75 4.35082 15.75 4.5V13.5C15.75 13.6492 15.6907 13.7923 15.5852 13.8977C15.4798 14.0032 15.3367 14.0625 15.1875 14.0625H5.80069C5.66827 14.0625 5.5401 14.1092 5.43874 14.1944Z"
                                          stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M9 9.84375C9.46599 9.84375 9.84375 9.46599 9.84375 9C9.84375 8.53401 9.46599 8.15625 9 8.15625C8.53401 8.15625 8.15625 8.53401 8.15625 9C8.15625 9.46599 8.53401 9.84375 9 9.84375Z"
                                          fill="currentColor"/>
                                    <path d="M5.625 9.84375C6.09099 9.84375 6.46875 9.46599 6.46875 9C6.46875 8.53401 6.09099 8.15625 5.625 8.15625C5.15901 8.15625 4.78125 8.53401 4.78125 9C4.78125 9.46599 5.15901 9.84375 5.625 9.84375Z"
                                          fill="currentColor"/>
                                    <path d="M12.375 9.84375C12.841 9.84375 13.2188 9.46599 13.2188 9C13.2188 8.53401 12.841 8.15625 12.375 8.15625C11.909 8.15625 11.5312 8.53401 11.5312 9C11.5312 9.46599 11.909 9.84375 12.375 9.84375Z"
                                          fill="currentColor"/>
                                </svg>
                            </div>
                            <div id="announce" class="annouce">
                                <user-annouce></user-annouce>
                            </div>
                            <div class="header-avatar">
                                <div class="dropdown-toggle user-info-box" type="button" data-toggle="dropdown">
                                    <img class="user-avatar-circle"
                                         @if (Auth::user()->avatar == null)
                                             src="{{url('assets/img/default-avatar.jpg')}}"
                                         @else
                                             src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                                            @endif
                                    />
                                </div>
                                <ul class="dropdown-menu user-menu">
                                    <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>
                                    <li><a href="{{url('/account')}}"
                                           onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Profile')"><i
                                                    class="zmdi zmdi-account-box"></i> Thông tin cá nhân</a></li>
                                    <li><a href="{{url('/account/courses')}}"
                                           onclick="ga('send', 'event', 'Avatar Menu', 'click', 'My courses from ava list')"><i
                                                    class="zmdi zmdi-dns"></i> Khóa học của tôi</a></li>
                                    <li><a href="{{url('/account/score')}}"
                                           onclick="ga('send', 'event', 'Avatar Menu', 'click', 'My improvement')"><i
                                                    class="fa fa-trophy trophy-icon"></i>Sự tiến bộ của tôi&nbsp;&nbsp;<div
                                                    class="tooltip-i"><i class="fa fa-info-circle" aria-hidden="true"></i><span
                                                        class="tooltiptext">Theo dõi sự tiến bộ của bạn thông qua 2 bài thi giữa kỳ và cuối kỳ</span>
                                            </div>
                                        </a></li>
                                    <li><a href="{{url('/account/achievement')}}"
                                           onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Thành tích')"><i
                                                    class="fa fa-star"></i>Thành tích&nbsp;&nbsp;<div class="tooltip-i"><i
                                                        class="fa fa-info-circle" aria-hidden="true"></i><span
                                                        class="tooltiptext">Phần thưởng cho sự chăm chỉ của bạn, duy trì và cải thiện thứ hạng trên bảng xếp hạng nhé!</span>
                                            </div>
                                        </a></li>
                                    <li><a href="{{url('/account/billing')}}"
                                           onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Payment history')"><i
                                                    class="zmdi zmdi-card"></i> Lịch sử thanh toán</a></li>
                                    <li><a href="{{url('/ho-tro/chinh-sach-gia-han-khoa-hoc-online')}}"
                                           onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Chính sách gia hạn')"><i
                                                    class="fa fa-shield"></i> Chính sách gia hạn</a></li>
                                    <li><a href="{{url('/account?focus=changePass')}}"
                                           onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Change passwrod')"><i
                                                    class="zmdi zmdi-shield-security"></i> Thay đổi mật khẩu</a></li>
                                    <div class="dropdown-divider"></div>
                                    <li><a onclick="ga('send', 'event', 'Avatar Menu', 'click', 'Logout'); logout()"><i
                                                    class="zmdi zmdi-power"></i> Đăng xuất</a></li>
                                </ul>
                            </div>
                        @endif

                        <script type="text/javascript">
                            function showChatbox() {
                                if (typeof chatbox !== 'undefined' && chatbox) {
                                    chatbox.initChat();
                                    $("#chat-box").css('display', 'flex');
                                    $("#mess-input").focus();
                                    chatbox.goToBottom();
                                }
                            }
                            @if (Auth::check())
                            var chatUser = {{ Auth::user()->id }};
                            var at = "{{ session('_tokenApp') }}";
                            var api = "{{ config('app.api_url') }}";
                            @else
                            var chatUser = localStorage.getItem('incognito_user');
                            if (chatUser) {
                                chatUser = parseInt(chatUser);
                            } else {
                                chatUser = (Math.floor(Math.random() * 1000000) + 1) * -1;
                                localStorage.setItem('incognito_user', chatUser);
                            }
                            @endif
                            if (chatUser) {
                                var socketServer = "{{ config('app.socket_server') }}";

                                var socket = io.connect(socketServer, {
                                    query: {
                                        type: 'user',
                                        from: 'web',
                                        userId: chatUser
                                    }
                                });

                                // Check receive new message
                                @if (Auth::check())
                                socket.on('send_new_message_enc', async function (message) {
                                    if (typeof chatbox !== 'undefined') {
                                        message = JSON.parse(dmsg(message));
                                        chatbox.receiveNewMsg(message);
                                        if (notiCount) {
                                            notiCount.updateMessageCount(message);
                                        }
                                        if (message && parseInt(message.receiverId) == chatUser) {
                                            if (!countAdminMess) {
                                                countAdminMess = 0;
                                            }
                                            countAdminMess = countAdminMess + 1;

                                            if (message.senderType == 'admin' && $('#chat-box').css('display') === 'none') {
                                                showChatbox();
                                                chatbox.goToBottom();
                                            }
                                        }
                                    }
                                });
                                @else
                                socket.on('send_new_message', async function (message) {
                                    if (typeof chatbox !== 'undefined') {
                                        chatbox.receiveNewMsg(message);
                                    }
                                    if (typeof notiCount !== 'undefined') {
                                        notiCount.updateMessageCount(message);
                                    }
                                    if (message && parseInt(message.receiverId) == chatUser) {
                                        if ($('#chat-box').css('display') === 'none') {
                                            showChatbox();
                                            chatbox.goToBottom();
                                        }
                                    }
                                });
                                @endif

                                socket.on('send_new_message_group_enc', async function (message) {
                                    if (typeof chatbox !== 'undefined') {
                                        message = JSON.parse(dmsg(message));
                                        chatbox.receiveNewMsg(message);
                                    }
                                    if (typeof notiCount !== 'undefined') {
                                        notiCount.updateMessageCount(message);
                                    }
                                });
                            }
                        </script>
                    </div>
                </div>
            </div>
        </div>


{{--        <div class="header--pc">--}}
{{--            <div class="container">--}}
{{--                <div class="header-inner">--}}
{{--                    <div class="header-logo">--}}
{{--                        <a href="{{route('home')}}">--}}
{{--                            <img src="{{url('assets/img/new_home/logo.png')}}" alt="Dũng Mori - Website tiếng Nhật hàng đầu Việt Nam"/>--}}
{{--                        </a>--}}
{{--                    </div>--}}
{{--                    <div class="header-menu">--}}
{{--                        <div class="header-menu__item">--}}
{{--                            <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                <g clip-path="url(#clip0_2069_2600)">--}}
{{--                                    <path d="M20.1661 10.8161H0.876789C0.3928 10.8161 0 10.4427 0 9.98275C0 9.52275 0.3928 9.14941 0.876789 9.14941H20.1661C20.6501 9.14941 21.0429 9.52275 21.0429 9.98275C21.0429 10.4427 20.6501 10.8161 20.1661 10.8161Z" fill="black"/>--}}
{{--                                    <path d="M20.1661 4.42643H0.876789C0.3928 4.42643 0 4.0531 0 3.5931C0 3.1331 0.3928 2.75977 0.876789 2.75977H20.1661C20.6501 2.75977 21.0429 3.1331 21.0429 3.5931C21.0429 4.0531 20.6501 4.42643 20.1661 4.42643Z" fill="black"/>--}}
{{--                                    <path d="M20.1661 17.2048H0.876789C0.3928 17.2048 0 16.8314 0 16.3714C0 15.9114 0.3928 15.5381 0.876789 15.5381H20.1661C20.6501 15.5381 21.0429 15.9114 21.0429 16.3714C21.0429 16.8314 20.6501 17.2048 20.1661 17.2048Z" fill="black"/>--}}
{{--                                </g>--}}
{{--                                <defs>--}}
{{--                                    <clipPath id="clip0_2069_2600">--}}
{{--                                        <rect width="21.0429" height="20" fill="white"/>--}}
{{--                                    </clipPath>--}}
{{--                                </defs>--}}
{{--                            </svg>--}}
{{--                            <div class="header-menu__dropdown">--}}
{{--                                <div class="header-menu__dropdown-inner">--}}
{{--                                    <div onclick="goTo('{{url('/trang/ve-dungmori')}}')" class="header-menu__dropdown-item">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Về Dũng Mori</div>--}}
{{--                                            <div class="description">Lịch sử hình thành và phát triển</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <a href="{{ url('https://dungmori.co.jp') }}" class="header-menu__dropdown-item" target="_blank">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Dung Mori Japan</div>--}}
{{--                                            <div class="description">Homepage chính thức tại Nhật Bản</div>--}}
{{--                                        </div>--}}
{{--                                    </a>--}}
{{--                                    <div onclick="goTo('{{ route('blog.index') }}')" class="header-menu__dropdown-item {{ strpos(Route::currentRouteName(), 'blog') !== false ? 'active' : '' }}">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Tin tức</div>--}}
{{--                                            <div class="description">Cập nhật tin tức và kiến thức tiếng Nhật</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div onclick="goTo('{{ route('teacher.index') }}')" class="header-menu__dropdown-item {{ strpos(Route::currentRouteName(), 'teacher') !== false ? 'active' : '' }}">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Giáo viên</div>--}}
{{--                                            <div class="description">Đội ngũ giáo viên của Dũng Mori</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div onclick="goTo('{{ route('review.index') }}')" class="header-menu__dropdown-item {{ strpos(Route::currentRouteName(), 'review') !== false ? 'active' : '' }}">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Review dũng mori</div>--}}
{{--                                            <div class="description">Chia sẻ của học viên về Khóa học</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="header-menu__item">--}}
{{--                            <span>Test Online--}}
{{--                                @if (session('_mjt_is_enable') > 0)--}}
{{--                                    <img src="{{url('assets/img/onl.gif')}}" />--}}
{{--                                @endif--}}
{{--                            </span>--}}
{{--                            <div class="header-menu__dropdown">--}}
{{--                                <div class="header-menu__dropdown-inner">--}}
{{--                                    <div onclick="goToWindow('{{ route('thi-thu.index') }}')" class="header-menu__dropdown-item">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">MJT テスト--}}
{{--                                                @if (session('_mjt_is_enable') > 0)--}}
{{--                                                    <img src="{{url('assets/img/onl.gif')}}" />--}}
{{--                                                @endif</div>--}}
{{--                                            <div class="description">Thi thử JLPT trên Dũng Mori</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div onclick="goToWindow('{{ route('test-online.index') }}')" class="header-menu__dropdown-item">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Test online</div>--}}
{{--                                            <div class="description">Làm bài tập đầu giờ các nhóm zoom</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        --}}{{-- <div onclick="goTo('{{ route('thi-thu.index') }}')" class="header-menu__item featured {{ Route::currentRouteNamed('thi-thu') ? 'active' : '' }}">--}}
{{--                            <span>MJT テスト</span>--}}
{{--                        </div> --}}
{{--                        --}}{{-- <div onclick="goTo('{{ route('test-online.index') }}')" class="header-menu__item {{ Route::currentRouteNamed('test-online.index') ? 'active' : '' }}">--}}
{{--                            <span>Test Online</span>--}}
{{--                        </div> --}}
{{--                        <a href="{{ url('/groups') }}" class="header-menu__item">--}}
{{--                            <span>Cộng đồng</span>--}}
{{--                        </a>--}}
{{--                        <div class="header-menu__item">--}}
{{--                            <span>Online</span>--}}
{{--                            <div class="header-menu__dropdown mega">--}}
{{--                                <div class="header-menu__dropdown-inner flex">--}}
{{--                                    <ul class="menu-column border-right w-0.3" role="tablist">--}}
{{--                                        <li class="header-menu__dropdown-item active">--}}
{{--                                            <a href="#jlpt" class="header-menu__dropdown-item-inner" role="presentation" data-toggle="pill">--}}
{{--                                                <div class="flex">--}}
{{--                                                    <svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                                        <path d="M4.71484 20.7502V8.48974C4.71484 7.98948 4.91357 7.50972 5.26731 7.15598C5.62104 6.80224 6.10081 6.60352 6.60107 6.60352H23.5771C24.0774 6.60352 24.5571 6.80224 24.9109 7.15598C25.2646 7.50972 25.4633 7.98948 25.4633 8.48974V20.7502" stroke="black" stroke-width="1.66835" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M2.82812 20.748H27.3491V22.6343C27.3491 23.1345 27.1504 23.6143 26.7966 23.968C26.4429 24.3218 25.9631 24.5205 25.4629 24.5205H4.71435C4.21409 24.5205 3.73432 24.3218 3.38059 23.968C3.02685 23.6143 2.82813 23.1345 2.82812 22.6343V20.748Z" stroke="black" stroke-width="1.66835" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M16.9756 10.375H13.2031" stroke="black" stroke-width="1.66835" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                    </svg>--}}
{{--                                                    <div class="ml-5">--}}
{{--                                                        <div class="title">JLPT</div>--}}
{{--                                                        <div class="description">Các Khóa luyện thi và luyện đề</div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </a>--}}
{{--                                        </li>--}}
{{--                                        <li class="header-menu__dropdown-item">--}}
{{--                                            <a href="#kaiwa" class="header-menu__dropdown-item-inner" role="presentation" data-toggle="pill">--}}
{{--                                                <div class="flex">--}}
{{--                                                    <svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                                        <path d="M4.01545 16.5944C2.96087 14.8167 2.59155 12.7151 2.97684 10.6843C3.36212 8.65345 4.47551 6.83315 6.10793 5.56516C7.74036 4.29718 9.77952 3.66873 11.8425 3.79782C13.9055 3.92692 15.8504 4.80468 17.312 6.26629C18.7736 7.72789 19.6514 9.6728 19.7805 11.7358C19.9096 13.7988 19.2812 15.8379 18.0132 17.4704C16.7452 19.1028 14.9249 20.2162 12.8941 20.6015C10.8633 20.9868 8.76171 20.6175 6.98394 19.5629L6.98396 19.5628L4.05236 20.4004C3.93107 20.4351 3.80272 20.4367 3.68061 20.405C3.5585 20.3734 3.44707 20.3097 3.35788 20.2205C3.26868 20.1313 3.20496 20.0198 3.17332 19.8977C3.14168 19.7756 3.14327 19.6473 3.17792 19.526L4.01552 16.5944L4.01545 16.5944Z" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M10.8516 20.7375C11.2688 21.9221 11.9436 22.9993 12.8273 23.8917C13.711 24.7841 14.7816 25.4695 15.962 25.8983C17.1424 26.3272 18.4032 26.489 19.6536 26.3719C20.9041 26.2549 22.113 25.862 23.1933 25.2216L23.1933 25.2215L26.1249 26.0591C26.2462 26.0938 26.3745 26.0954 26.4966 26.0637C26.6187 26.0321 26.7302 25.9683 26.8194 25.8792C26.9086 25.79 26.9723 25.6785 27.0039 25.5564C27.0356 25.4343 27.034 25.306 26.9993 25.1847L26.1617 22.2531L26.1618 22.2531C26.906 20.998 27.3142 19.5723 27.347 18.1135C27.3797 16.6547 27.036 15.2121 26.3489 13.9248C25.6618 12.6375 24.6545 11.5491 23.4242 10.7644C22.194 9.97981 20.7823 9.52549 19.3253 9.44531" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                    </svg>--}}
{{--                                                    <div class="ml-5">--}}
{{--                                                        <div class="title">KAIWA</div>--}}
{{--                                                        <div class="description">Khóa học giao tiếp</div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </a>--}}
{{--                                        </li>--}}
{{--                                        <li class="header-menu__dropdown-item">--}}
{{--                                            <a href="#eju" class="header-menu__dropdown-item-inner" role="presentation" data-toggle="pill">--}}
{{--                                                <div class="flex">--}}
{{--                                                    <svg width="31" height="31" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                                        <path d="M22.2773 11.498V22.9951" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M17.2461 11.498V22.9951" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M12.9355 11.498V22.9951" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M7.9043 11.498V22.9951" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M24.4316 22.9922H5.74901" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <path d="M25.8711 22.9922H4.31421" stroke="black" stroke-width="1.41467" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                                        <rect x="4.3125" y="6.4668" width="21.5569" height="5.02994" rx="0.718563" stroke="black" stroke-width="1.41557"/>--}}
{{--                                                    </svg>--}}
{{--                                                    <div class="ml-5">--}}
{{--                                                        <div class="title">EJU</div>--}}
{{--                                                        <div class="description">Khóa luyện thi đại học</div>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </a>--}}
{{--                                        </li>--}}
{{--                                    </ul>--}}
{{--                                    <div class="menu-column tab-content flex-grow-1" role="tablist">--}}
{{--                                        <div class="tab-pane fade active in min-h-full" id="jlpt">--}}
{{--                                            <div class="flex">--}}
{{--                                                <ul class="menu-column border-right">--}}
{{--                                                    <li href="#luyenThi" data-toggle="pill" class="header-menu__dropdown-sub-item active">--}}
{{--                                                        <a class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                            <div class="title">Luyện thi</div>--}}
{{--                                                        </a>--}}
{{--                                                    </li>--}}
{{--                                                    <li href="#luyenDe" data-toggle="pill" class="header-menu__dropdown-sub-item">--}}
{{--                                                        <a class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                            <div class="title">Luyện đề</div>--}}
{{--                                                        </a>--}}
{{--                                                    </li>--}}
{{--                                                </ul>--}}
{{--                                                <div class="menu-column flex-grow-1 tab-content">--}}
{{--                                                    <div class="tab-pane active" id="luyenThi">--}}
{{--                                                        <a href="{{url('/khoa-hoc/chuyen-nganh')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Chuyên ngành</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                        <a href="{{url('/khoa-hoc/khoa-n5')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Khóa N5</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                        <a href="{{url('/khoa-hoc/khoa-n4')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Khóa N4</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                        <a href="{{url('/khoa-hoc/khoa-n3')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Khóa N3</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                        <a href="{{url('/khoa-hoc/khoa-n2')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Khóa N2</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                        <a href="{{url('/khoa-hoc/khoa-n1')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Khóa N1</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                    </div>--}}
{{--                                                    <div class="tab-pane" id="luyenDe">--}}
{{--                                                        <a href="{{url('/khoa-hoc/luyen-de-n3')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Luyện đề N3</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                        <a href="{{url('https://luyende.dungmori.com')}}" class="header-menu__dropdown-sub-item">--}}
{{--                                                            <div class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                                <div class="title">Luyện đề VIP500</div>--}}
{{--                                                            </div>--}}
{{--                                                        </a>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        <div class="tab-pane fade min-h-full" id="kaiwa">--}}
{{--                                            <ul>--}}
{{--                                                <li class="header-menu__dropdown-sub-item">--}}
{{--                                                    <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}" class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                        <div class="title">Kaiwa Sơ cấp</div>--}}
{{--                                                    </a>--}}
{{--                                                </li>--}}
{{--                                                <li class="header-menu__dropdown-sub-item">--}}
{{--                                                    <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}" class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                        <div class="title">Kaiwa Trung cấp</div>--}}
{{--                                                    </a>--}}
{{--                                                </li>--}}
{{--                                                <li class="header-menu__dropdown-sub-item">--}}
{{--                                                    <a href="{{url('/khoa-hoc/kaiwa-nang-cao')}}" class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                        <div class="title">Kaiwa nâng cao</div>--}}
{{--                                                    </a>--}}
{{--                                                </li>--}}
{{--                                            </ul>--}}
{{--                                        </div>--}}
{{--                                        <div class="tab-pane fade min-h-full" id="eju">--}}
{{--                                            <ul>--}}
{{--                                                <li class="header-menu__dropdown-sub-item">--}}
{{--                                                    <a href="{{url('/khoa-hoc/eju')}}" class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                        <div class="title">EJU Tiếng Nhật</div>--}}
{{--                                                    </a>--}}
{{--                                                </li>--}}
{{--                                                <li class="header-menu__dropdown-sub-item">--}}
{{--                                                    <a href="{{url('/khoa-hoc/eju-xhth')}}" class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                        <div class="title">EJU XHTH</div>--}}
{{--                                                    </a>--}}
{{--                                                </li>--}}
{{--                                                <li class="header-menu__dropdown-sub-item">--}}
{{--                                                    <a href="{{url('/khoa-hoc/eju-toan')}}" class="header-menu__dropdown-sub-item-inner">--}}
{{--                                                        <div class="title">EJU Toán</div>--}}
{{--                                                    </a>--}}
{{--                                                </li>--}}
{{--                                            </ul>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="header-menu__item">--}}
{{--                            <span>Online Vip</span>--}}
{{--                            <div class="header-menu__dropdown">--}}
{{--                                <div class="header-menu__dropdown-inner">--}}
{{--                                    <div onclick="goToWindow('{{ url('https://onlinevip.dungmori.com') }}')" class="header-menu__dropdown-item">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Online VIP</div>--}}
{{--                                            <div class="description">Học qua phần mềm Zoom</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div onclick="goToWindow('{{ url('https://onlineplus.dungmori.com') }}')" class="header-menu__dropdown-item">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Online Plus</div>--}}
{{--                                            <div class="description">Học qua phần mềm Livestream</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div onclick="goToWindow('{{ url('https://kaiwa.dungmori.com/') }}')" class="header-menu__dropdown-item">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Kaiwa</div>--}}
{{--                                            <div class="description">Học qua phần mềm Zoom</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="header-menu__item" onclick="goToWindow('https://offline.dungmori.com/tructiep?utm_source=website&utm_medium=menu&utm_campaign=header')">--}}
{{--                            <span>Offline</span>--}}
{{--                        </div>--}}
{{--                        <div class="header-menu__item" onclick="goToWindow('https://sach.dungmori.com')">--}}
{{--                            <span>Book</span>--}}
{{--                        </div>--}}
{{--                        <div class="header-menu__item">--}}
{{--                            <div>B2B</div>--}}
{{--                            <div class="header-menu__dropdown">--}}
{{--                                <div class="header-menu__dropdown-inner">--}}
{{--                                    <div onclick="goToWindow('https://online.dungmori.com/b2b')" class="header-menu__dropdown-item">--}}
{{--                                        <div class="header-menu__dropdown-item-inner">--}}
{{--                                            <div class="title">Khóa doanh nghiệp</div>--}}
{{--                                            <div class="description">Hợp tác cùng với Dũng Mori</div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    <div class="flex justify-end items-center">--}}
{{--                        <a href="{{url('/khoa-hoc')}}" class="mr-4 cursor-pointer hover-green">--}}
{{--                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                <path d="M4.26271 1.4209L2.13086 4.26336V14.212C2.13086 14.5889 2.2806 14.9504 2.54713 15.217C2.81366 15.4835 3.17516 15.6332 3.55209 15.6332H13.5007C13.8777 15.6332 14.2392 15.4835 14.5057 15.217C14.7722 14.9504 14.922 14.5889 14.922 14.212V4.26336L12.7901 1.4209H4.26271Z" stroke="black" stroke-width="1.13699" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                <path d="M2.13086 4.26367H14.922" stroke="black" stroke-width="1.13699" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                <path d="M11.3705 7.10645C11.3705 7.86031 11.071 8.58331 10.5379 9.11637C10.0049 9.64944 9.28188 9.94891 8.52801 9.94891C7.77414 9.94891 7.05115 9.64944 6.51809 9.11637C5.98502 8.58331 5.68555 7.86031 5.68555 7.10645" stroke="black" stroke-width="1.13699" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                            </svg>--}}
{{--                        </a>--}}
{{--                        @if (!Auth::check())--}}
{{--                            @include('frontend._layouts.auth')--}}
{{--                            <div class="login-logout">--}}
{{--                                <a data-fancybox data-animation-duration="300" data-src="#auth-container">--}}
{{--                                    <div class="text-login a__c" id="text-login" onclick="swichTab('login')">Đăng nhập</div>--}}
{{--                                </a>--}}
{{--                                <a data-fancybox data-animation-duration="300" data-src="#auth-container">--}}
{{--                                    <div class="text-register a--cursor-pointer" onclick="swichTab('register')">Đăng ký</div>--}}
{{--                                </a>--}}
{{--                            </div>--}}
{{--                        @else--}}
{{--                            <div class="mr-4 a-cursor-pointer hover-green has-badge" onclick="showChatbox()" >--}}
{{--                                <div id="auth-container" v-if="countUnreadMess > 0">--}}
{{--                                    <span class="badge--red countMess" v-html="countUnreadMess"></span>--}}
{{--                                </div>--}}
{{--                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                    <path d="M5.43874 14.1944L3.17446 16.0979C3.09246 16.1668 2.99249 16.2109 2.8863 16.2249C2.78011 16.239 2.67211 16.2224 2.57501 16.1772C2.47791 16.132 2.39574 16.06 2.33816 15.9696C2.28059 15.8793 2.25 15.7744 2.25 15.6673V4.5C2.25 4.35082 2.30926 4.20774 2.41475 4.10225C2.52024 3.99676 2.66332 3.9375 2.8125 3.9375H15.1875C15.3367 3.9375 15.4798 3.99676 15.5852 4.10225C15.6907 4.20774 15.75 4.35082 15.75 4.5V13.5C15.75 13.6492 15.6907 13.7923 15.5852 13.8977C15.4798 14.0032 15.3367 14.0625 15.1875 14.0625H5.80069C5.66827 14.0625 5.5401 14.1092 5.43874 14.1944Z" stroke="black" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                    <path d="M9 9.84375C9.46599 9.84375 9.84375 9.46599 9.84375 9C9.84375 8.53401 9.46599 8.15625 9 8.15625C8.53401 8.15625 8.15625 8.53401 8.15625 9C8.15625 9.46599 8.53401 9.84375 9 9.84375Z" fill="black"/>--}}
{{--                                    <path d="M5.625 9.84375C6.09099 9.84375 6.46875 9.46599 6.46875 9C6.46875 8.53401 6.09099 8.15625 5.625 8.15625C5.15901 8.15625 4.78125 8.53401 4.78125 9C4.78125 9.46599 5.15901 9.84375 5.625 9.84375Z" fill="black"/>--}}
{{--                                    <path d="M12.375 9.84375C12.841 9.84375 13.2188 9.46599 13.2188 9C13.2188 8.53401 12.841 8.15625 12.375 8.15625C11.909 8.15625 11.5312 8.53401 11.5312 9C11.5312 9.46599 11.909 9.84375 12.375 9.84375Z" fill="black"/>--}}
{{--                                </svg>--}}
{{--                            </div>--}}
{{--                            <div id="announce" class="annouce">--}}
{{--                                <user-annouce></user-annouce>--}}
{{--                            </div>--}}
{{--                            <div class="header-avatar">--}}
{{--                                <div class="dropdown-toggle user-info-box" type="button" data-toggle="dropdown">--}}
{{--                                    <img class="user-avatar-circle"--}}
{{--                                         @if(Auth::user()->avatar == null)--}}
{{--                                         src="{{url('assets/img/default-avatar.jpg')}}"--}}
{{--                                         @else--}}
{{--                                         src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"--}}
{{--                                            @endif--}}
{{--                                    />--}}
{{--                                </div>--}}
{{--                                <ul class="dropdown-menu user-menu">--}}
{{--                                    <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>--}}
{{--                                    <li><a href="{{url('/account')}}"><i class="zmdi zmdi-account-box"></i> Thông tin cá nhân</a></li>--}}
{{--                                    <li><a href="{{url('/account/courses')}}"><i class="zmdi zmdi-dns"></i> Khóa học của tôi</a></li>--}}
{{--                                    <li><a href="{{url('/account/billing')}}"><i class="zmdi zmdi-card"></i> Lịch sử thanh toán</a></li>--}}
{{--                                    <li><a href="{{url('/account/active')}}"><i class="zmdi zmdi-shopping-cart"></i> Nạp mã kích hoạt</a></li>--}}
{{--                                    <li><a href="{{url('/account?focus=changePass')}}"><i class="zmdi zmdi-shield-security"></i> Thay đổi mật khẩu</a></li>--}}
{{--                                    <div class="dropdown-divider"></div>--}}
{{--                                    <li><a onclick="logout()"><i class="zmdi zmdi-power"></i> Đăng xuất</a></li>--}}
{{--                                </ul>--}}
{{--                              <script type="text/javascript">--}}
{{--                                  function showChatbox() {--}}
{{--                                      $("#chat-box").css('display', 'block');--}}
{{--                                      $("#mess-input").focus();--}}
{{--                                      chatbox.goToBottom();--}}
{{--                                  }--}}
{{--                                  @if (Auth::check())--}}
{{--                                  var chatUser = {{ Auth::user()->id }};--}}
{{--                                  @else--}}
{{--                                  var chatUser = localStorage.getItem('incognito_user');--}}
{{--                                  if (chatUser) {--}}
{{--                                      chatUser = parseInt(chatUser);--}}
{{--                                  } else {--}}
{{--                                      chatUser = (Math.floor(Math.random() * 1000000) + 1) * -1;--}}
{{--                                      localStorage.setItem('incognito_user', chatUser);--}}
{{--                                  }--}}
{{--                                  @endif--}}
{{--                                  if (chatUser) {--}}
{{--                                      var socketServer = "{{ config('app.socket_server') }}";--}}
{{--                                        var socket = io.connect(socketServer, {--}}
{{--                                            query: {--}}
{{--                                                type: 'user',--}}
{{--                                                from: 'web',--}}
{{--                                                userId: chatUser--}}
{{--                                            }--}}
{{--                                        });--}}

{{--                                      socket.on('send_new_message', function(message) {--}}
{{--                                          if (message && parseInt(message.receiverId) === chatUser) {--}}
{{--                                              showChatbox();--}}
{{--                                              chatbox.goToBottom();--}}
{{--                                          }--}}
{{--                                      });--}}
{{--                                  }--}}
{{--                              </script>--}}
{{--                            </div>--}}
{{--                        @endif--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
        {{--    <div class="site-header">--}}

        {{--        --}}{{-- cho phép admin xóa cache redis --}}
        @if($adminSession == true)
        <div class="refresh-cache" onclick="refreshCache()">
            <i class="fa fa-refresh" id="refresh-cache"></i>
            <i class="fa fa-check-circle" id="refresh-done" style="display: none; color: green;"></i> cache
        </div>
        <div class="refresh-cache" onclick="refreshMjtCache()" style="top: 230px;">
            <i class="fa fa-refresh" id="refresh-mjt-cache"></i>
            <i class="fa fa-check-circle" id="refresh-mjt-done" style="display: none; color: green;"></i> mjt
        </div>
        @endif

        {{--        --}}{{-- nav header trên cùng --}}
        {{--        <div class="header-top">--}}
        {{--            <ul class="nav-left">--}}
        {{--                --}}{{-- <li> <a href="{{url('/thi-thu')}}" class="mn-item thi-thu" style="font-weight: bold; color: red;">DUNGMORI 日本語テスト <i class="fa fa-spinner fa-spin fa-fw"></i></a></li> --}}
        {{--                <li> <a href="{{url('/thi-thu')}}" class="mn-item thi-thu" style="font-weight: bold; color: red;">Thi thử MJT</a></li>--}}
        {{--                <li> <a href="{{url('/bang-gia')}}" class="mn-item khoa-hoc">Bảng giá</a></li>--}}
        {{--                <li> <a href="{{url('/books')}}" class="mn-item book">Sách</a></li>--}}
        {{--                <li> <a href="{{url('/bai-viet')}}" class="mn-item bai-viet">Bài viết</a></li>--}}
        {{--                <li> <a href="{{url('/ho-tro')}}" class="mn-item ho-tro">Hỗ trợ</a></li>--}}
        {{--                <li> <a href="{{url('/giao-vien')}}" class="mn-item giao-vien">Giáo viên</a></li>--}}
        {{--                <li> <a href="https://sach.dungmori.com" target="_blank" class="mn-item">Sách</a></li>--}}
        {{--                <li> <a href="{{url('/review')}}" class="mn-item mn-feedback">Review DũngMori</a></li>--}}


        {{--                <div class="account-container" id="account-container">--}}
        {{--                @if(!Auth::check())--}}

        {{--                    @include('frontend._layouts.auth')--}}

        {{--                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">--}}
        {{--                        <div class="text-register" onclick="swichTab('register')">Tạo tài khoản</div>--}}
        {{--                    </a>--}}
        {{--                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">--}}
        {{--                        <div class="text-login" id="text-login" onclick="swichTab('login')">Đăng nhập</div>--}}
        {{--                    </a>--}}
        {{--                @else--}}
        {{--                    <div class="dropdown auth-container" id="auth-container">--}}

        {{--                        --}}{{-- bật chat cho tester test trước --}}
        {{--                        --}}{{-- @if(in_array(Auth::user()->id, array(2804 , 7, 138721, 1))) --}}
        {{--                        <script type="text/javascript">--}}
        {{--                            function showChatbox(){--}}

        {{--                                console.log("bấm show chatbox");--}}

        {{--                                $("#chat-box").css('display', 'block');--}}
        {{--                                chatbox.initChat();--}}
        {{--                            }--}}
        {{--                        </script>--}}
        {{--                        <span class="messenger-icon" onclick="showChatbox()">--}}
        {{--                            <img src="{{url('assets/img/messenger.png?')}}" />--}}
        {{--                            <span class="mess-counts" v-show="countUnreadMess != 0" style="display: none;">@{{ countUnreadMess }}</span>--}}
        {{--                        </span>--}}


        {{--                        --}}{{-- @endif --}}

        {{--                        <a href="{{url('/account/notifications')}}">--}}
        {{--                        <span class="svgIcon svgIcon--bell svgIcon--25px">--}}
        {{--                            --}}{{-- <span class="noti-counts" style="">5</span> --}}
        {{--                            <span class="noti-counts" v-show="countNotification != 0" style="display: none;">@{{ countNotification }}</span>--}}
        {{--                            <svg class="svgIcon-use" width="26" height="26" viewBox="-293 409 25 25"><path d="M-273.327 423.67l-1.673-1.52v-3.646a5.5 5.5 0 0 0-6.04-5.474c-2.86.273-4.96 2.838-4.96 5.71v3.41l-1.68 1.553c-.204.19-.32.456-.32.734V427a1 1 0 0 0 1 1h3.49a3.079 3.079 0 0 0 3.01 2.45 3.08 3.08 0 0 0 3.01-2.45h3.49a1 1 0 0 0 1-1v-2.59c0-.28-.12-.55-.327-.74zm-7.173 5.63c-.842 0-1.55-.546-1.812-1.3h3.624a1.92 1.92 0 0 1-1.812 1.3zm6.35-2.45h-12.7v-2.347l1.63-1.51c.236-.216.37-.522.37-.843v-3.41c0-2.35 1.72-4.356 3.92-4.565a4.353 4.353 0 0 1 4.78 4.33v3.645c0 .324.137.633.376.85l1.624 1.477v2.373z"></path></svg>--}}
        {{--                        </span>--}}
        {{--                        </a>--}}

        {{--                        <div class="dropdown-toggle user-info-box" type="button" data-toggle="dropdown">--}}

        {{--                            <img class="user-avatar-circle"--}}
        {{--                            @if(Auth::user()->avatar == null)--}}
        {{--                                src="{{url('assets/img/default-avatar.jpg')}}"--}}
        {{--                            @else--}}
        {{--                                src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"--}}
        {{--                            @endif--}}
        {{--                            />--}}
        {{--                            <span class="user-name">{{ Auth::user()->name }}</span>--}}
        {{--                            <span class="caret"></span>--}}
        {{--                        </div>--}}
        {{--                        <ul class="dropdown-menu user-menu">--}}
        {{--                            <img class="caret-up" src="{{url('assets/img/caret-up.png')}}"/>--}}
        {{--                            <li><a href="{{url('/account')}}"><i class="zmdi zmdi-account-box"></i> Thông tin cá nhân</a></li>--}}
        {{--                            <li><a href="{{url('/account/courses')}}"><i class="zmdi zmdi-dns"></i> Khóa học của tôi</a></li>--}}
        {{--                            <li><a href="{{url('/account/billing')}}"><i class="zmdi zmdi-card"></i> Lịch sử thanh toán</a></li>--}}
        {{--                            <li><a href="{{url('/account/active')}}"><i class="zmdi zmdi-shopping-cart"></i> Nạp mã kích hoạt</a></li>--}}
        {{--                            <li><a href="{{url('/account?focus=changePass')}}"><i class="zmdi zmdi-shield-security"></i> Thay đổi mật khẩu</a></li>--}}
        {{--                            <div class="dropdown-divider"></div>--}}
        {{--                            <li><a onclick="logout()"><i class="zmdi zmdi-power"></i> Đăng xuất</a></li>--}}
        {{--                        </ul>--}}
        {{--                    </div>--}}
        {{--                @endif--}}
        {{--                </div>--}}
        {{--                <span class="shop-icon">--}}
        {{--                    <a href="{{url('/khoa-hoc')}}">--}}
        {{--                    <img src="{{url('assets/img/shop.png?')}}" />--}}
        {{--                    </a>--}}
        {{--                </span>--}}
        {{--            </ul>--}}
        {{--        </div>--}}

        {{--        --}}{{-- header menu --}}
        {{--        <div class="header-content mid-header">--}}
        {{--            <div class="container">--}}
        {{--                <a href="{{url('/')}}" class="logo" id="logo">--}}
        {{--                    <img src="{{url('assets/img/new_home/header_logo.png')}}" alt="dungmori logo">--}}
        {{--                </a>--}}
        {{--                <div class="block-nav-menu">--}}
        {{--                    <ul class="ui-menu">--}}
        {{--                        <li class="nav-item-mn">--}}
        {{--                            <div class="group-name">--}}
        {{--                                --}}{{-- <span class="free" style="margin-left: 30px">miễn phí</span> --}}
        {{--                                Khóa miễn phí <i class="fa fa-caret-down"></i>--}}
        {{--                            </div>--}}
        {{--                            <div class="hover-box">--}}
        {{--                                <a href="{{url('/khoa-hoc/chuyen-nganh')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/khcn.png')}}" />Chuyên ngành--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/khoa-n5')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kn5.png')}}" />Khóa N5--}}
        {{--                                </a>--}}
        {{--                            </div>--}}
        {{--                        </li>--}}
        {{--                        <li class="nav-item-mn">--}}
        {{--                            <div class="group-name">Khóa JLPT <i class="fa fa-caret-down"></i></div>--}}
        {{--                            <div class="hover-box">--}}
        {{--                                <a href="{{url('/khoa-hoc/khoa-n5')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kn5.png')}}" />Khóa N5 <span class="free" style="margin-left: 6px">miễn phí</span>--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/khoa-n4')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kn4.png')}}" />Khóa N4--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/khoa-n3')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kn3.png')}}" />Khóa N3--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/khoa-n2')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kn2.png')}}" />Khóa N2--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/khoa-n1')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kn1.png')}}" />Khóa N1--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/luyen-de-n3')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/khcn.png')}}" />Luyện đề N3 <span class="free" style="margin-left: 6px">mới</span>--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/luyen-de-vip')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/khcn.png')}}" />Luyện đề VIP500 <span class="free" style="margin-left: 6px">mới</span>--}}
        {{--                                </a>--}}
        {{--                                <p style="border-top: 1px solid #EEE; float: left; width: 100%;"></p>--}}
        {{--                                <a href="https://onlinevip.dungmori.com" target="_blank" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/mn.png')}}" />Khóa Online VIP</span>--}}
        {{--                                </a>--}}
        {{--                                <a href="https://offline.dungmori.com" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/mn.png')}}" />Khóa Offline</span>--}}
        {{--                                </a>--}}
        {{--                                <a href="https://onlineplus.dungmori.com" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/mn.png')}}" />Khóa Online Plus</span>--}}
        {{--                                </a>--}}
        {{--                                <a href="https://sach.dungmori.com" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/mn.png')}}" />Sách</span>--}}
        {{--                                </a>--}}
        {{--                            </div>--}}

        {{--                        </li>--}}
        {{--                        <li class="nav-item-mn">--}}
        {{--                            <div class="group-name">Khóa Kaiwa <i class="fa fa-caret-down"></i></div>--}}
        {{--                            <div class="hover-box">--}}
        {{--                                <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kwsc.png')}}" />Kaiwa sơ cấp--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kwtc.png')}}" />Kaiwa trung cấp--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/kaiwa-nang-cao')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/tc2.png')}}" />Kaiwa nâng cao--}}
        {{--                                </a>--}}
        {{--                                --}}{{-- <a href="{{url('/khoa-hoc/kaiwa-trung-cap')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/kwtc.png')}}" />Kaiwa trung cấp--}}
        {{--                                </a> --}}
        {{--                            </div>--}}
        {{--                        </li>--}}
        {{--                        <li class="nav-item-mn">--}}
        {{--                            <div class="group-name">Khóa EJU <i class="fa fa-caret-down"></i></div>--}}
        {{--                            <div class="hover-box">--}}
        {{--                                <a href="{{url('/khoa-hoc/eju')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/eju.png')}}" />EJU Tiếng Nhật--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/eju-xhth')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/ejuxh.png')}}" />EJU XHTH--}}
        {{--                                </a>--}}
        {{--                                <a href="{{url('/khoa-hoc/eju-toan')}}" class="mn-item">--}}
        {{--                                    <img src="{{url('assets/img/mn/ejut.png')}}" />EJU Toán--}}
        {{--                                </a>--}}
        {{--                            </div>--}}
        {{--                        </li>--}}
        {{--                        <a href="{{url('/b2b')}}" style="color: #000; float: left; opacity: 1;">--}}
        {{--                        <li class="nav-item-mn">--}}
        {{--                            <div class="group-name">--}}
        {{--                                <span class="free" style="margin:8px 0 0 60px"><i class="zmdi zmdi-star" style="opacity: 1;"></i> mới</span> Khóa Doanh nghiệp--}}
        {{--                            </div>--}}
        {{--                        </li>--}}
        {{--                        </a>--}}

        {{--                        --}}{{-- <a href="https://vip.dungmori.com" target="_blank" style="color: #000; float: left; opacity: 1;">--}}
        {{--                        <li class="nav-item-mn">--}}

        {{--                            <div class="group-name"><span class="free" style="margin:8px 0 0 60px"><i class="zmdi zmdi-star" style="opacity: 1;"></i> mới</span>Khóa ONLINE VIP</div>--}}
        {{--                        </li>--}}
        {{--                        </a>--}}
        {{--                        <a href="{{url('/trang/hoc-offline')}}" style="color: #000; float: left; opacity: 1;">--}}
        {{--                        <li class="nav-item-mn">--}}
        {{--                            <div class="group-name">Khóa học Offline</div>--}}
        {{--                        </li>--}}
        {{--                        </a>--}}

        {{--                    </ul>--}}
        {{--                </div>--}}
        {{--            </div>--}}
        {{--        </div>--}}

        {{--    </div>--}}
        {{--    --}}{{-- end of pc --}}

        {{--    --}}{{-- mobile --}}
        <div class="mobile-header">

            <div id="nav-icon">
                <span class="nav-span"></span>
                <span class="nav-span"></span>
                <span class="nav-span"></span>
                <ul class="dropdown-menu">

                    @if(Auth::user())
                    <div class="col">
                        <div class="tabs">
                            <div class="tab">
                                <input type="checkbox" id="chck0">
                                <label class="tab-label label-account" for="chck0">
                                    <div class="user-info-box">
                                        <img class="user-avatar-circle"
                                        @if(Auth::user()->avatar == null)
                                            src="{{url('assets/img/default-avatar.jpg')}}"
                                        @else
                                            src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                                        @endif
                                        />
                                        <div style="width: 70%;">
                                            <p class="user-name">{{ Auth::user()->name }}</p>
                                            <i class="zmdi zmdi-settings"></i><span class="user-name ml-2">Xem trang cá nhân</span>
                                        </div>
                                    </div>
                                </label>
                                <div class="tab-content account-expand">
                                    <li>
                                        <a href="{{url('/account/notifications')}}">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9 21H15" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M5.27051 9.74994C5.26926 8.86045 5.44409 7.97951 5.7849 7.1579C6.12571 6.33628 6.62576 5.59025 7.25624 4.9628C7.88672 4.33535 8.63515 3.83889 9.45839 3.50204C10.2816 3.16519 11.1634 2.99461 12.0529 3.00013C15.7644 3.02772 18.7332 6.11276 18.7332 9.83468V10.4999C18.7332 13.8577 19.4357 15.8061 20.0544 16.871C20.1211 16.9848 20.1565 17.1142 20.1572 17.246C20.1579 17.3779 20.1238 17.5076 20.0584 17.6221C19.993 17.7366 19.8985 17.8318 19.7845 17.8982C19.6706 17.9645 19.5411 17.9996 19.4092 17.9999H4.59369C4.4618 17.9996 4.33234 17.9645 4.21835 17.8981C4.10437 17.8318 4.0099 17.7366 3.94448 17.622C3.87905 17.5075 3.84499 17.3778 3.84571 17.2459C3.84644 17.114 3.88194 16.9846 3.94863 16.8709C4.56768 15.8059 5.2705 13.8575 5.2705 10.4999L5.27051 9.74994Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Xem thông báo
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{url('/account')}}">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M11.998 15.75C13.6549 15.75 14.998 14.4069 14.998 12.75C14.998 11.0931 13.6549 9.75 11.998 9.75C10.3412 9.75 8.99805 11.0931 8.99805 12.75C8.99805 14.4069 10.3412 15.75 11.998 15.75Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7.49805 17.9998C8.02208 17.3013 8.70153 16.7343 9.4826 16.3438C10.2637 15.9533 11.1249 15.75 11.9982 15.75C12.8714 15.75 13.7327 15.9532 14.5138 16.3437C15.2949 16.7342 15.9744 17.3011 16.4984 17.9996" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M19.498 20.25V3.75C19.498 3.33579 19.1623 3 18.748 3L5.24805 3C4.83383 3 4.49805 3.33579 4.49805 3.75L4.49805 20.25C4.49805 20.6642 4.83383 21 5.24805 21H18.748C19.1623 21 19.498 20.6642 19.498 20.25Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M8.99805 6H14.998" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Thông tin cá nhân
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{url('/account/courses')}}">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.125 10.5C8.98896 10.5 10.5 8.98896 10.5 7.125C10.5 5.26104 8.98896 3.75 7.125 3.75C5.26104 3.75 3.75 5.26104 3.75 7.125C3.75 8.98896 5.26104 10.5 7.125 10.5Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M16.875 10.5C18.739 10.5 20.25 8.98896 20.25 7.125C20.25 5.26104 18.739 3.75 16.875 3.75C15.011 3.75 13.5 5.26104 13.5 7.125C13.5 8.98896 15.011 10.5 16.875 10.5Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M16.875 20.249C18.739 20.249 20.25 18.738 20.25 16.874C20.25 15.0101 18.739 13.499 16.875 13.499C15.011 13.499 13.5 15.0101 13.5 16.874C13.5 18.738 15.011 20.249 16.875 20.249Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7.125 20.249C8.98896 20.249 10.5 18.738 10.5 16.874C10.5 15.0101 8.98896 13.499 7.125 13.499C5.26104 13.499 3.75 15.0101 3.75 16.874C3.75 18.738 5.26104 20.249 7.125 20.249Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Khóa học của tôi</a></li>
                                    <li><a href="{{url('/account/billing')}}">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M21.752 6H2.25195C1.83774 6 1.50195 6.33579 1.50195 6.75V17.25C1.50195 17.6642 1.83774 18 2.25195 18H21.752C22.1662 18 22.502 17.6642 22.502 17.25V6.75C22.502 6.33579 22.1662 6 21.752 6Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M12.002 15C13.6588 15 15.002 13.6569 15.002 12C15.002 10.3431 13.6588 9 12.002 9C10.3451 9 9.00195 10.3431 9.00195 12C9.00195 13.6569 10.3451 15 12.002 15Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M16.502 6L22.502 11.25" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M16.502 18L22.502 12.75" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7.50195 6L1.50195 11.25" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7.50195 18L1.50195 12.75" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Lịch sử thanh toán</a></li>
                                    <li><a href="{{url('/account/active')}}">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M7.125 10.5C8.98896 10.5 10.5 8.98896 10.5 7.125C10.5 5.26104 8.98896 3.75 7.125 3.75C5.26104 3.75 3.75 5.26104 3.75 7.125C3.75 8.98896 5.26104 10.5 7.125 10.5Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M16.875 10.5C18.739 10.5 20.25 8.98896 20.25 7.125C20.25 5.26104 18.739 3.75 16.875 3.75C15.011 3.75 13.5 5.26104 13.5 7.125C13.5 8.98896 15.011 10.5 16.875 10.5Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7.125 20.25C8.98896 20.25 10.5 18.739 10.5 16.875C10.5 15.011 8.98896 13.5 7.125 13.5C5.26104 13.5 3.75 15.011 3.75 16.875C3.75 18.739 5.26104 20.25 7.125 20.25Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M16.875 14.251V19.501" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M19.5 16.875H14.25" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Nạp mã kích hoạt</a></li>
                                    <li><a href="{{url('/account?focus=changePass')}}">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M3.75 10.75V5.25C3.75 5.05109 3.82902 4.86032 3.96967 4.71967C4.11032 4.57902 4.30109 4.5 4.5 4.5H19.5C19.6989 4.5 19.8897 4.57902 20.0303 4.71967C20.171 4.86032 20.25 5.05109 20.25 5.25V10.75C20.25 18.6264 13.5651 21.236 12.2303 21.6785C12.0811 21.7298 11.9189 21.7298 11.7697 21.6785C10.4349 21.236 3.75 18.6264 3.75 10.75Z" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Thay đổi mật khẩu</a></li>
                                    <div class="dropdown-divider"></div>
                                    <li><a onclick="logout()">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M16.3145 8.0625L20.2509 12L16.3145 15.9375" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M9.75 12H20.2472" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M9.75 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V4.5C3.75 4.30109 3.82902 4.11032 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75H9.75" stroke="black" stroke-width="1.52202" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            Đăng xuất</a></li>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif


{{--                    <li><a href="{{url('')}}">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 14.25H15" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M9 11.25H15" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M15.0002 3.75H18.75C18.9489 3.75 19.1397 3.82902 19.2803 3.96967C19.421 4.11032 19.5 4.30109 19.5 4.5V20.25C19.5 20.4489 19.421 20.6397 19.2803 20.7803C19.1397 20.921 18.9489 21 18.75 21H5.25C5.05109 21 4.86032 20.921 4.71967 20.7803C4.57902 20.6397 4.5 20.4489 4.5 20.25V4.5C4.5 4.30109 4.57902 4.11032 4.71967 3.96967C4.86032 3.82902 5.05109 3.75 5.25 3.75H8.9998" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M8.25 6.75V6C8.25 5.00544 8.64509 4.05161 9.34835 3.34835C10.0516 2.64509 11.0054 2.25 12 2.25C12.9946 2.25 13.9484 2.64509 14.6517 3.34835C15.3549 4.05161 15.75 5.00544 15.75 6V6.75H8.25Z" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p>Trang chủ</p></a></li>--}}
{{--                        <li>--}}
{{--                            <a style="color: red;" href="{{url('thi-thu')}}">--}}
{{--                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                    <path d="M9 14.25H15" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                    <path d="M9 11.25H15" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                    <path d="M15.0002 3.75H18.75C18.9489 3.75 19.1397 3.82902 19.2803 3.96967C19.421 4.11032 19.5 4.30109 19.5 4.5V20.25C19.5 20.4489 19.421 20.6397 19.2803 20.7803C19.1397 20.921 18.9489 21 18.75 21H5.25C5.05109 21 4.86032 20.921 4.71967 20.7803C4.57902 20.6397 4.5 20.4489 4.5 20.25V4.5C4.5 4.30109 4.57902 4.11032 4.71967 3.96967C4.86032 3.82902 5.05109 3.75 5.25 3.75H8.9998" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                    <path d="M8.25 6.75V6C8.25 5.00544 8.64509 4.05161 9.34835 3.34835C10.0516 2.64509 11.0054 2.25 12 2.25C12.9946 2.25 13.9484 2.64509 14.6517 3.34835C15.3549 4.05161 15.75 5.00544 15.75 6V6.75H8.25Z" stroke="#FB6D3A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                                </svg>--}}
{{--                                <p>MJT テスト</p>--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                    <li>--}}
{{--                        <a style="color: red;" href="{{url('thi-thu')}}">--}}
{{--                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">--}}
{{--                                <path fill-rule="evenodd" clip-rule="evenodd" d="M3.25196 10.8116C3.25196 6.48814 6.69339 2.98328 10.9386 2.98328C15.1839 2.98328 18.6253 6.48814 18.6253 10.8116L18.6253 10.8309L18.5543 10.8918C16.7874 12.4032 14.3858 13.9699 12.2657 15.1326C10.1673 16.2833 8.1467 16.9415 6.50471 17.2071C4.53677 15.7894 3.25196 13.4533 3.25196 10.8116ZM5.13072 17.343C4.50929 16.7695 3.97007 16.1053 3.53321 15.3709C3.3673 15.5177 3.22459 15.7209 3.12164 15.9578C2.98537 16.2715 2.94263 16.5896 2.97603 16.8249C3.06343 16.9531 3.28851 17.1208 3.77223 17.2332C4.1338 17.3172 4.59135 17.3577 5.13072 17.343ZM3.07409 14.49C2.69301 14.7628 2.41436 15.1567 2.23909 15.5601C2.02771 16.0466 1.93851 16.6112 2.04526 17.1019C2.05343 17.1395 2.06593 17.176 2.08247 17.2107C2.34046 17.7507 2.92524 18.0445 3.55756 18.1914C4.21299 18.3437 5.04288 18.3692 5.97975 18.2691C6.07635 18.2587 6.17432 18.247 6.2736 18.234C7.61969 19.1133 9.22068 19.6232 10.9386 19.6232C15.5592 19.6232 19.3338 15.9345 19.5782 11.2909C20.3167 10.6261 20.943 9.96067 21.3708 9.33047C21.8537 8.61906 22.1789 7.80955 21.8929 7.07022L21.8925 7.06936L21.8875 7.05619L21.8817 7.04167C21.8732 7.02097 21.8618 6.99588 21.8471 6.96734C21.8177 6.91024 21.7746 6.83917 21.7131 6.76207C21.5885 6.60584 21.3945 6.43293 21.103 6.30098C20.5486 6.05002 19.6981 5.97052 18.386 6.32367C16.8789 3.73524 14.1077 2.00001 10.9386 2.00001C6.16018 2.00001 2.28648 5.9451 2.28648 10.8116C2.28648 12.1246 2.56845 13.3705 3.07409 14.49ZM18.842 7.22055C19.2169 8.07504 19.4621 9.00148 19.5522 9.97391C19.9753 9.55019 20.323 9.14565 20.5769 8.77155C21.0285 8.1063 21.0917 7.67696 20.9939 7.42782L20.9921 7.42314C20.989 7.41735 20.9804 7.40238 20.9641 7.38187C20.9307 7.34 20.8571 7.26529 20.711 7.19915C20.4465 7.0794 19.8949 6.96778 18.842 7.22055ZM18.5053 12.1971C17.863 15.8597 14.7196 18.64 10.9386 18.64C9.81267 18.64 8.74326 18.3934 7.77961 17.9504C9.28799 17.5866 10.9867 16.9503 12.7233 15.998C14.6288 14.953 16.7696 13.5818 18.5053 12.1971ZM9.38213 6.94448C8.14469 6.94448 7.14155 7.96611 7.14155 9.22636C7.14155 10.4866 8.14469 11.5082 9.38213 11.5082C10.6196 11.5082 11.6227 10.4866 11.6227 9.22636C11.6227 7.96611 10.6196 6.94448 9.38213 6.94448ZM6.17645 9.22636C6.17645 7.42327 7.61168 5.96159 9.38213 5.96159C11.1526 5.96159 12.5878 7.42327 12.5878 9.22636C12.5878 11.0294 11.1526 12.4911 9.38213 12.4911C7.61168 12.4911 6.17645 11.0294 6.17645 9.22636ZM18.8115 20.3198C18.8115 19.9348 19.1179 19.6227 19.496 19.6227C19.874 19.6227 20.1805 19.9348 20.1805 20.3198C20.1805 20.7048 19.874 21.0169 19.496 21.0169C19.1179 21.0169 18.8115 20.7048 18.8115 20.3198ZM19.496 18.6398C18.5849 18.6398 17.8463 19.392 17.8463 20.3198C17.8463 21.2476 18.5849 21.9998 19.496 21.9998C20.407 21.9998 21.1456 21.2476 21.1456 20.3198C21.1456 19.392 20.407 18.6398 19.496 18.6398Z" fill="black"/>--}}
{{--                                <path d="M18.6253 10.8116L18.7253 10.8117V10.8116H18.6253ZM18.6253 10.8309L18.6904 10.9067C18.7125 10.8877 18.7252 10.8601 18.7253 10.831L18.6253 10.8309ZM18.5543 10.8918L18.6193 10.9678L18.6195 10.9677L18.5543 10.8918ZM12.2657 15.1326L12.3138 15.2202L12.2657 15.1326ZM6.50471 17.2071L6.44626 17.2882C6.46776 17.3037 6.49452 17.31 6.52068 17.3058L6.50471 17.2071ZM3.53321 15.3709L3.61915 15.3198C3.60392 15.2942 3.57806 15.2767 3.54863 15.2721C3.51919 15.2675 3.48924 15.2763 3.46693 15.296L3.53321 15.3709ZM5.13072 17.343L5.13345 17.443C5.17412 17.4419 5.21006 17.4162 5.22435 17.3781C5.23864 17.3401 5.22844 17.2971 5.19854 17.2695L5.13072 17.343ZM3.12164 15.9578L3.02993 15.918L3.12164 15.9578ZM2.97603 16.8249L2.87702 16.839C2.87917 16.8541 2.88477 16.8686 2.8934 16.8812L2.97603 16.8249ZM3.77223 17.2332L3.7496 17.3306H3.7496L3.77223 17.2332ZM2.23909 15.5601L2.33081 15.5999L2.23909 15.5601ZM3.07409 14.49L3.1323 14.5713C3.17107 14.5435 3.18485 14.4922 3.16522 14.4488L3.07409 14.49ZM2.04526 17.1019L1.94754 17.1232H1.94754L2.04526 17.1019ZM2.08247 17.2107L1.99224 17.2538L1.99224 17.2538L2.08247 17.2107ZM3.55756 18.1914L3.53493 18.2888H3.53493L3.55756 18.1914ZM5.97975 18.2691L5.96912 18.1696L5.97975 18.2691ZM6.2736 18.234L6.32829 18.1503C6.30828 18.1372 6.28425 18.1317 6.26055 18.1348L6.2736 18.234ZM19.5782 11.2909L19.5113 11.2166C19.4916 11.2343 19.4797 11.2592 19.4783 11.2856L19.5782 11.2909ZM21.3708 9.33047L21.2881 9.27431L21.3708 9.33047ZM21.8929 7.07022L21.7987 7.10402L21.7996 7.10631L21.8929 7.07022ZM21.8925 7.06936L21.9867 7.03556L21.986 7.03388L21.8925 7.06936ZM21.8875 7.05619L21.9811 7.02071L21.9803 7.01892L21.8875 7.05619ZM21.8817 7.04167L21.9745 7.00439L21.9741 7.0035L21.8817 7.04167ZM21.8471 6.96734L21.7582 7.01316V7.01316L21.8471 6.96734ZM21.7131 6.76207L21.6349 6.82443L21.6349 6.82443L21.7131 6.76207ZM21.103 6.30098L21.1442 6.20987L21.103 6.30098ZM18.386 6.32367L18.2996 6.37399C18.3223 6.41302 18.3684 6.43197 18.412 6.42023L18.386 6.32367ZM19.5522 9.97391L19.4526 9.98313C19.4562 10.0218 19.4818 10.0548 19.5182 10.068C19.5547 10.0811 19.5955 10.072 19.6229 10.0446L19.5522 9.97391ZM18.842 7.22055L18.8187 7.12331C18.7899 7.13022 18.7656 7.14955 18.7524 7.17607C18.7393 7.2026 18.7385 7.2336 18.7504 7.26072L18.842 7.22055ZM20.5769 8.77155L20.4942 8.71539L20.5769 8.77155ZM20.9939 7.42782L20.9006 7.4639L20.9008 7.46438L20.9939 7.42782ZM20.9921 7.42314L21.086 7.38681L21.0804 7.37627L20.9921 7.42314ZM20.9641 7.38187L20.8859 7.44424L20.8859 7.44424L20.9641 7.38187ZM20.711 7.19915L20.6698 7.29026H20.6698L20.711 7.19915ZM18.5053 12.1971L18.6038 12.2144C18.6109 12.1735 18.592 12.1323 18.5563 12.1111C18.5205 12.0899 18.4754 12.093 18.4429 12.119L18.5053 12.1971ZM7.77961 17.9504L7.75616 17.8532C7.71475 17.8632 7.68421 17.8983 7.68008 17.9407C7.67595 17.9831 7.69914 18.0235 7.73784 18.0413L7.77961 17.9504ZM12.7233 15.998L12.7713 16.0857V16.0857L12.7233 15.998ZM10.9386 2.88328C6.63645 2.88328 3.15196 6.43464 3.15196 10.8116H3.35196C3.35196 6.54164 6.75034 3.08328 10.9386 3.08328V2.88328ZM18.7253 10.8116C18.7253 6.43464 15.2408 2.88328 10.9386 2.88328V3.08328C15.1269 3.08328 18.5253 6.54164 18.5253 10.8116H18.7253ZM18.7253 10.831L18.7253 10.8117L18.5253 10.8115L18.5253 10.8307L18.7253 10.831ZM18.6195 10.9677L18.6904 10.9067L18.5601 10.755L18.4891 10.816L18.6195 10.9677ZM12.3138 15.2202C14.438 14.0554 16.8458 12.4849 18.6193 10.9678L18.4893 10.8158C16.7291 12.3215 14.3337 13.8845 12.2176 15.0449L12.3138 15.2202ZM6.52068 17.3058C8.17481 17.0382 10.2062 16.376 12.3138 15.2202L12.2176 15.0449C10.1284 16.1906 8.11859 16.8447 6.48874 17.1084L6.52068 17.3058ZM3.15196 10.8116C3.15196 13.4864 4.45296 15.8523 6.44626 17.2882L6.56316 17.1259C4.62057 15.7265 3.35196 13.4203 3.35196 10.8116H3.15196ZM3.44726 15.422C3.88906 16.1647 4.43439 16.8365 5.0629 17.4165L5.19854 17.2695C4.5842 16.7026 4.05108 16.0459 3.61915 15.3198L3.44726 15.422ZM3.21336 15.9977C3.31141 15.772 3.44612 15.5815 3.59948 15.4458L3.46693 15.296C3.28848 15.4539 3.13777 15.6698 3.02993 15.918L3.21336 15.9977ZM3.07503 16.8109C3.04467 16.5968 3.08312 16.2974 3.21336 15.9977L3.02993 15.918C2.88762 16.2455 2.8406 16.5823 2.87702 16.839L3.07503 16.8109ZM3.79486 17.1358C3.32213 17.0259 3.1257 16.867 3.05866 16.7686L2.8934 16.8812C3.00115 17.0393 3.2549 17.2156 3.7496 17.3306L3.79486 17.1358ZM5.128 17.243C4.59458 17.2576 4.14594 17.2173 3.79486 17.1358L3.7496 17.3306C4.12165 17.417 4.58812 17.4578 5.13345 17.443L5.128 17.243ZM2.33081 15.5999C2.50057 15.2092 2.76907 14.8313 3.1323 14.5713L3.01587 14.4086C2.61696 14.6942 2.32814 15.1042 2.14737 15.5202L2.33081 15.5999ZM2.14297 17.0807C2.04169 16.6151 2.12568 16.072 2.33081 15.5999L2.14737 15.5202C1.92974 16.0211 1.83533 16.6074 1.94754 17.1232L2.14297 17.0807ZM2.1727 17.1676C2.1595 17.1399 2.14951 17.1108 2.14297 17.0807L1.94754 17.1232C1.95736 17.1683 1.97236 17.2122 1.99224 17.2538L2.1727 17.1676ZM3.58019 18.094C2.95761 17.9494 2.41078 17.6659 2.1727 17.1676L1.99224 17.2538C2.27014 17.8354 2.89286 18.1397 3.53493 18.2888L3.58019 18.094ZM5.96912 18.1696C5.03911 18.2691 4.22122 18.243 3.58019 18.094L3.53493 18.2888C4.20477 18.4445 5.04665 18.4694 5.99038 18.3685L5.96912 18.1696ZM6.26055 18.1348C6.16206 18.1478 6.0649 18.1594 5.96912 18.1696L5.99038 18.3685C6.08781 18.3581 6.18658 18.3463 6.28665 18.3331L6.26055 18.1348ZM10.9386 19.5232C9.24079 19.5232 7.6587 19.0193 6.32829 18.1503L6.21891 18.3177C7.58068 19.2073 9.20058 19.7232 10.9386 19.7232V19.5232ZM19.4783 11.2856C19.2366 15.8781 15.5041 19.5232 10.9386 19.5232V19.7232C15.6142 19.7232 19.4309 15.991 19.678 11.2962L19.4783 11.2856ZM21.2881 9.27431C20.8667 9.89515 20.2468 10.5545 19.5113 11.2166L19.6451 11.3652C20.3865 10.6978 21.0194 10.0262 21.4536 9.38663L21.2881 9.27431ZM21.7996 7.10631C22.0667 7.79649 21.7676 8.56787 21.2881 9.27431L21.4536 9.38663C21.9398 8.67026 22.2912 7.82261 21.9861 7.03413L21.7996 7.10631ZM21.7984 7.10315L21.7987 7.10401L21.987 7.03643L21.9867 7.03557L21.7984 7.10315ZM21.7941 7.09167L21.799 7.10483L21.986 7.03388L21.981 7.02072L21.7941 7.09167ZM21.7889 7.07895L21.7948 7.09347L21.9803 7.01892L21.9745 7.00439L21.7889 7.07895ZM21.7582 7.01316C21.7716 7.03906 21.7817 7.06159 21.7893 7.07983L21.9741 7.0035C21.9646 6.98036 21.9521 6.9527 21.936 6.92152L21.7582 7.01316ZM21.6349 6.82443C21.6919 6.89591 21.7315 6.96134 21.7582 7.01316L21.936 6.92152C21.9039 6.85913 21.8573 6.78244 21.7913 6.6997L21.6349 6.82443ZM21.0618 6.39208C21.3381 6.51715 21.5196 6.67987 21.6349 6.82443L21.7913 6.6997C21.6573 6.53181 21.451 6.34871 21.1442 6.20987L21.0618 6.39208ZM18.412 6.42023C19.7114 6.07049 20.5361 6.15413 21.0618 6.39208L21.1442 6.20987C20.5611 5.94591 19.6847 5.87055 18.36 6.22711L18.412 6.42023ZM10.9386 2.10001C14.0701 2.10001 16.8093 3.81451 18.2996 6.37399L18.4724 6.27335C16.9484 3.65597 14.1454 1.90001 10.9386 1.90001V2.10001ZM2.38648 10.8116C2.38648 5.99859 6.21712 2.10001 10.9386 2.10001V1.90001C6.10323 1.90001 2.18648 5.8916 2.18648 10.8116H2.38648ZM3.16522 14.4488C2.66533 13.342 2.38648 12.1102 2.38648 10.8116H2.18648C2.18648 12.139 2.47157 13.3989 2.98295 14.5311L3.16522 14.4488ZM19.6517 9.96469C19.5607 8.98143 19.3127 8.04455 18.9336 7.18037L18.7504 7.26072C19.1211 8.10554 19.3636 9.02154 19.4526 9.98313L19.6517 9.96469ZM20.4942 8.71539C20.2449 9.08264 19.9018 9.4823 19.4814 9.90325L19.6229 10.0446C20.0489 9.61809 20.4011 9.20867 20.6597 8.82771L20.4942 8.71539ZM20.9008 7.46438C20.9794 7.66448 20.942 8.05561 20.4942 8.71539L20.6597 8.82771C21.115 8.15699 21.2041 7.68943 21.087 7.39126L20.9008 7.46438ZM20.8988 7.45921L20.9006 7.4639L21.0871 7.39175L21.0853 7.38706L20.8988 7.45921ZM20.8859 7.44424C20.8981 7.45949 20.9033 7.46919 20.9037 7.47L21.0804 7.37627C21.0747 7.3655 21.0628 7.34527 21.0422 7.31951L20.8859 7.44424ZM20.6698 7.29026C20.8007 7.34952 20.8618 7.41404 20.8859 7.44424L21.0422 7.31951C20.9995 7.26597 20.9136 7.18107 20.7523 7.10805L20.6698 7.29026ZM18.8654 7.31778C19.9052 7.06815 20.4322 7.18273 20.6698 7.29026L20.7523 7.10805C20.4607 6.97608 19.8846 6.8674 18.8187 7.12331L18.8654 7.31778ZM10.9386 18.74C14.7703 18.74 17.9535 15.9227 18.6038 12.2144L18.4068 12.1799C17.7725 15.7968 14.6689 18.54 10.9386 18.54V18.74ZM7.73784 18.0413C8.7143 18.4901 9.79792 18.74 10.9386 18.74V18.54C9.82742 18.54 8.77223 18.2967 7.82138 17.8596L7.73784 18.0413ZM12.6752 15.9103C10.9461 16.8585 9.25562 17.4915 7.75616 17.8532L7.80306 18.0476C9.32035 17.6817 11.0273 17.042 12.7713 16.0857L12.6752 15.9103ZM18.4429 12.119C16.7126 13.4994 14.5768 14.8675 12.6752 15.9103L12.7713 16.0857C14.6808 15.0385 16.8266 13.6643 18.5676 12.2753L18.4429 12.119ZM7.24155 9.22636C7.24155 8.01961 8.20164 7.04448 9.38213 7.04448V6.84448C8.08775 6.84448 7.04155 7.91261 7.04155 9.22636H7.24155ZM9.38213 11.4082C8.20164 11.4082 7.24155 10.4331 7.24155 9.22636H7.04155C7.04155 10.5401 8.08775 11.6082 9.38213 11.6082V11.4082ZM11.5227 9.22636C11.5227 10.4331 10.5626 11.4082 9.38213 11.4082V11.6082C10.6765 11.6082 11.7227 10.5401 11.7227 9.22636H11.5227ZM9.38213 7.04448C10.5626 7.04448 11.5227 8.01961 11.5227 9.22636H11.7227C11.7227 7.91261 10.6765 6.84448 9.38213 6.84448V7.04448ZM9.38213 5.86159C7.55474 5.86159 6.07645 7.36977 6.07645 9.22636H6.27645C6.27645 7.47677 7.66862 6.06159 9.38213 6.06159V5.86159ZM12.6878 9.22636C12.6878 7.36977 11.2095 5.86159 9.38213 5.86159V6.06159C11.0956 6.06159 12.4878 7.47677 12.4878 9.22636H12.6878ZM9.38213 12.5911C11.2095 12.5911 12.6878 11.0829 12.6878 9.22636H12.4878C12.4878 10.9759 11.0956 12.3911 9.38213 12.3911V12.5911ZM6.07645 9.22636C6.07645 11.0829 7.55474 12.5911 9.38213 12.5911V12.3911C7.66862 12.3911 6.27645 10.9759 6.27645 9.22636H6.07645ZM19.496 19.5227C19.061 19.5227 18.7115 19.8813 18.7115 20.3198H18.9115C18.9115 19.9883 19.1749 19.7227 19.496 19.7227V19.5227ZM20.2805 20.3198C20.2805 19.8813 19.9309 19.5227 19.496 19.5227V19.7227C19.8171 19.7227 20.0805 19.9883 20.0805 20.3198H20.2805ZM19.496 21.1169C19.9309 21.1169 20.2805 20.7583 20.2805 20.3198H20.0805C20.0805 20.6513 19.8171 20.9169 19.496 20.9169V21.1169ZM18.7115 20.3198C18.7115 20.7583 19.061 21.1169 19.496 21.1169V20.9169C19.1749 20.9169 18.9115 20.6513 18.9115 20.3198H18.7115ZM17.9463 20.3198C17.9463 19.4455 18.6418 18.7398 19.496 18.7398V18.5398C18.528 18.5398 17.7463 19.3385 17.7463 20.3198H17.9463ZM19.496 21.8998C18.6418 21.8998 17.9463 21.1941 17.9463 20.3198H17.7463C17.7463 21.3011 18.528 22.0998 19.496 22.0998V21.8998ZM21.0456 20.3198C21.0456 21.1941 20.3501 21.8998 19.496 21.8998V22.0998C20.464 22.0998 21.2456 21.3011 21.2456 20.3198H21.0456ZM19.496 18.7398C20.3501 18.7398 21.0456 19.4455 21.0456 20.3198H21.2456C21.2456 19.3385 20.464 18.5398 19.496 18.5398V18.7398Z" fill="black"/>--}}
{{--                            </svg>--}}
{{--                            <p>Cộng đồng </p>--}}
{{--                        </a>--}}
{{--                    </li>--}}

                    <div class="col">
                        <div class="tabs">
                            <div class="tab">
                                <input type="checkbox" id="chck1">
                                <label class="tab-label" for="chck1">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.75 16.5V6.75C3.75 6.35218 3.90804 5.97064 4.18934 5.68934C4.47064 5.40804 4.85218 5.25 5.25 5.25H18.75C19.1478 5.25 19.5294 5.40804 19.8107 5.68934C20.092 5.97064 20.25 6.35218 20.25 6.75V16.5" stroke="black" stroke-width="1.32673" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M2.25 16.5H21.75V18C21.75 18.3978 21.592 18.7794 21.3107 19.0607C21.0294 19.342 20.6478 19.5 20.25 19.5H3.75C3.35218 19.5 2.97064 19.342 2.68934 19.0607C2.40804 18.7794 2.25 18.3978 2.25 18V16.5Z" stroke="black" stroke-width="1.32673" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M13.5 8.25H10.5" stroke="black" stroke-width="1.32673" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p>ONLINE</p></label>
                                <div class="tab-content">
                                    <div class="tabs">
                                        <div class="tab">
                                            <input type="checkbox" id="chck1_1">
                                            <label class="tab-label child-label" for="chck1_1"><p>JLPT</p></label>
                                            <div class="tab-content">
                                                <a href="{{url('khoa-hoc/chuyen-nganh')}}" class="child-label">
                                                    <div class="content-label">Chuyên ngành
                                                        <div class="free">Miễn phí</div>
                                                    </div>
                                                </a>
                                                <a href="{{url('khoa-hoc/so-cap-n5')}}" class="child-label"><div class="content-label">Sơ cấp N5</div></a>
                                                <a href="{{url('khoa-hoc/so-cap-n4')}}" class="child-label"><div class="content-label">Sơ cấp N4</div></a>
                                                <a href="{{url('khoa-hoc/jlpt-n3')}}" class="child-label"><div class="content-label">N3</div></a>
                                                <a href="{{url('khoa-hoc/jlpt-n2')}}" class="child-label"><div class="content-label">N2</div></a>
                                                <a href="{{url('khoa-hoc/jlpt-n1')}}" class="child-label"><div class="content-label">N1</div></a>
                                                <a href="{{url('/khoa-hoc/luyen-de-n3')}}" class="child-label"><div class="content-label">Luyện đề N3</div></a>
                                                <a href="{{url('https://luyende.dungmori.com')}}" target="_blank" class="child-label"><div class="content-label">Luyện đề VIP500</div></a>
                                            </div>
                                        </div>
{{--                                        <div class="tab">--}}
{{--                                            <input type="checkbox" id="chck1_2">--}}
{{--                                            <label class="tab-label child-label" for="chck1_2"><p>KAIWA</p></label>--}}
{{--                                            <div class="tab-content">--}}
{{--                                                <a href="{{url('khoa-hoc/kaiwa-so-cap')}}" class="child-label"><div class="content-label">Kaiwa sơ cấp</div></a>--}}
{{--                                                <a href="{{url('khoa-hoc/kaiwa-trung-cap-1')}}" class="child-label"><div class="content-label">Kaiwa trung cấp</div></a>--}}
{{--                                                <a href="{{url('khoa-hoc/kaiwa-nang-cao')}}" class="child-label"><div class="content-label">Kaiwa nâng cao</div></a>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
                                        <div class="tab">
                                            <input type="checkbox" id="chck11">
                                            <label class="tab-label child-label" for="chck11"><p>EJU</p></label>
                                            <div class="tab-content">
                                                <a href="{{url('khoa-hoc/eju')}}" class="child-label"><div class="content-label">EJU Tiếng Nhật</div></a>
                                                <a href="{{url('khoa-hoc/eju-xhth')}}" class="child-label"><div class="content-label">EJU XHTH</div></a>
                                                <a href="{{url('khoa-hoc/eju-toan')}}" class="child-label"><div class="content-label">EJU Toán</div></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab">
                                <input type="checkbox" id="chck2">
                                <label class="tab-label" for="chck2">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.75 16.5V6.75C3.75 6.35218 3.90804 5.97064 4.18934 5.68934C4.47064 5.40804 4.85218 5.25 5.25 5.25H18.75C19.1478 5.25 19.5294 5.40804 19.8107 5.68934C20.092 5.97064 20.25 6.35218 20.25 6.75V16.5" stroke="black" stroke-width="1.32673" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M2.25 16.5H21.75V18C21.75 18.3978 21.592 18.7794 21.3107 19.0607C21.0294 19.342 20.6478 19.5 20.25 19.5H3.75C3.35218 19.5 2.97064 19.342 2.68934 19.0607C2.40804 18.7794 2.25 18.3978 2.25 18V16.5Z" stroke="black" stroke-width="1.32673" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M11.9981 12.6212C13.1362 12.6212 14.0587 11.6987 14.0587 10.5606C14.0587 9.42257 13.1362 8.5 11.9981 8.5C10.8601 8.5 9.9375 9.42257 9.9375 10.5606C9.9375 11.6987 10.8601 12.6212 11.9981 12.6212Z" stroke="black" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8.9082 14.1655C9.26815 13.6856 9.73484 13.2962 10.2713 13.028C10.8078 12.7598 11.3994 12.6201 11.9992 12.6201C12.599 12.6201 13.1906 12.7597 13.7271 13.0279C14.2637 13.2961 14.7304 13.6855 15.0903 14.1653" stroke="black" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <p>ONLINE VIP</p></label>
                                <div class="tab-content">
                                    <a href="https://onlinevip.dungmori.com" class="child-label"><div class="content-label">Khóa Online Vip</div></a>
                                    <a href="https://dungmori.com/onlineplus/" class="child-label"><div class="content-label">Khóa Online Plus</div></a>
                                    <a href="https://kaiwa.dungmori.com/" class="child-label"><div class="content-label">Khóa Kaiwa</div></a>
                                </div>
                            </div>
                            <li><a href="{{url('offline')}}">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20.25 4.5H3.75C3.33579 4.5 3 4.83579 3 5.25V16.5C3 16.9142 3.33579 17.25 3.75 17.25H20.25C20.6642 17.25 21 16.9142 21 16.5V5.25C21 4.83579 20.6642 4.5 20.25 4.5Z" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M15 17.25L18 21" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M9 17.25L6 21" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M9 11.25V13.5" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12 9.75V13.5" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M15 8.25V13.5" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12 4.5V2.25" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p>OFFLINE</p></a></li>
                            <li><a href="{{url('offline')}}">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M7.5 3.75H4.5C4.08579 3.75 3.75 4.08579 3.75 4.5V19.5C3.75 19.9142 4.08579 20.25 4.5 20.25H7.5C7.91421 20.25 8.25 19.9142 8.25 19.5V4.5C8.25 4.08579 7.91421 3.75 7.5 3.75Z" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M3.75 7.5H8.25" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12 3.75H9C8.58579 3.75 8.25 4.08579 8.25 4.5V19.5C8.25 19.9142 8.58579 20.25 9 20.25H12C12.4142 20.25 12.75 19.9142 12.75 19.5V4.5C12.75 4.08579 12.4142 3.75 12 3.75Z" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M8.25 16.5H12.75" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M16.3781 3.50989L13.4803 4.28635C13.0802 4.39356 12.8428 4.80481 12.95 5.20492L16.8323 19.6939C16.9395 20.094 17.3507 20.3314 17.7508 20.2242L20.6486 19.4477C21.0487 19.3405 21.2862 18.9293 21.179 18.5292L17.2967 4.04023C17.1895 3.64013 16.7782 3.40269 16.3781 3.50989Z" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M16.0547 16.7965L20.4014 15.6318" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M13.7246 8.10316L18.0713 6.93848" stroke="black" stroke-width="1.32589" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p>BOOK</p></a></li>
                            <li><a href="https://online.dungmori.com/b2b">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M22.5673 11.4172L20.25 12.5759L17.25 6.83067L19.5909 5.66024C19.7667 5.5723 19.9701 5.55695 20.1572 5.61749C20.3443 5.67802 20.5001 5.80961 20.5911 5.9839L22.8967 10.3992C22.9428 10.4875 22.9708 10.5841 22.9792 10.6834C22.9876 10.7826 22.9761 10.8825 22.9454 10.9773C22.9148 11.0721 22.8655 11.1598 22.8006 11.2353C22.7357 11.3108 22.6564 11.3727 22.5673 11.4172V11.4172Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M3.74945 12.4753L1.43217 11.3166C1.34308 11.2721 1.26376 11.2103 1.19883 11.1347C1.13391 11.0592 1.08467 10.9715 1.05401 10.8767C1.02334 10.782 1.01186 10.682 1.02023 10.5828C1.0286 10.4835 1.05666 10.387 1.10276 10.2987L3.40835 5.88332C3.49936 5.70902 3.65518 5.57744 3.84226 5.5169C4.02934 5.45636 4.23271 5.47171 4.40858 5.55965L6.74945 6.73008L3.74945 12.4753Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M20.25 12.5761L18.75 14.3309L15.3003 17.7806C15.2087 17.8722 15.095 17.9385 14.9701 17.9731C14.8453 18.0076 14.7137 18.0093 14.588 17.9779L9.15458 16.6195C9.05266 16.594 8.95724 16.5474 8.87448 16.4827L3.75 12.4756" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M18.7508 14.3311L14.6258 11.3311L13.4258 12.2311C12.9065 12.6205 12.2749 12.8311 11.6258 12.8311C10.9767 12.8311 10.3451 12.6205 9.82577 12.2311L9.31758 11.8499C9.23161 11.7854 9.16051 11.7032 9.10908 11.6089C9.05764 11.5146 9.02708 11.4103 9.01947 11.3031C9.01185 11.1959 9.02735 11.0883 9.06493 10.9877C9.1025 10.887 9.16127 10.7956 9.23725 10.7196L12.9061 7.05072C12.9757 6.98108 13.0584 6.92584 13.1494 6.88814C13.2404 6.85045 13.3379 6.83105 13.4364 6.83105H17.2508" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M6.80469 6.73047L11.6159 5.32783C11.7878 5.27772 11.9719 5.29075 12.135 5.36455L15.3758 6.83095" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M10.5 19.9558L7.67443 19.2494C7.55977 19.2207 7.4535 19.1654 7.3643 19.0878L5.25 17.25" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p>B2B</p></a>
                            </li>

                        </div>
                      </div>
                    <li style="padding-top: 10px; margin-top: 10px; border-top: 2px solid rgba(0,0,0,0.2);"><a href="{{url('bai-viet')}}"><p>Tin tức</p></a></li>
                    <li><a href="{{url('https://dungmori.co.jp')}}"><p>Dung Mori Japan</p></a></li>
                    <li><a href="{{url('giao-vien')}}"><p>Giáo viên</p></a></li>
                    <li><a href="{{url('/review')}}"><p>Review Dũng Mori</p></a></li>
                </ul>
            </div>
            <a href="{{url('/')}}" class="logo" id="logo">
                <img src="{{url('assets/img/new_home/logo.png')}}" alt="Dũng Mori - Website tiếng Nhật hàng đầu Việt Nam"/>
            </a>
            <div class="account-container" id="account-container">
                @if(!Auth::check())

                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                        <div class="text-register" onclick="swichTab('register')">Tạo tài khoản</div>
                    </a>
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                        <div class="text-login" onclick="swichTab('login')">Đăng nhập</div>
                    </a>
                @else

                    <div class="dropdown auth-container" id="auth-container">

                        <script type="text/javascript">
                            function showChatbox(){
                                // console.log("bấm show chatbox");
                                $("#chat-box").css('display', 'block');
                                chatbox.initChat();
                            }
                        </script>
                        <span class="messenger-icon" onclick="showChatbox()">
                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M5.43874 14.1944L3.17446 16.0979C3.09246 16.1668 2.99249 16.2109 2.8863 16.2249C2.78011 16.239 2.67211 16.2224 2.57501 16.1772C2.47791 16.132 2.39574 16.06 2.33816 15.9696C2.28059 15.8793 2.25 15.7744 2.25 15.6673V4.5C2.25 4.35082 2.30926 4.20774 2.41475 4.10225C2.52024 3.99676 2.66332 3.9375 2.8125 3.9375H15.1875C15.3367 3.9375 15.4798 3.99676 15.5852 4.10225C15.6907 4.20774 15.75 4.35082 15.75 4.5V13.5C15.75 13.6492 15.6907 13.7923 15.5852 13.8977C15.4798 14.0032 15.3367 14.0625 15.1875 14.0625H5.80069C5.66827 14.0625 5.5401 14.1092 5.43874 14.1944Z" stroke="black" stroke-width="1.125" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M9 9.84375C9.46599 9.84375 9.84375 9.46599 9.84375 9C9.84375 8.53401 9.46599 8.15625 9 8.15625C8.53401 8.15625 8.15625 8.53401 8.15625 9C8.15625 9.46599 8.53401 9.84375 9 9.84375Z" fill="black"/>
                                <path d="M5.625 9.84375C6.09099 9.84375 6.46875 9.46599 6.46875 9C6.46875 8.53401 6.09099 8.15625 5.625 8.15625C5.15901 8.15625 4.78125 8.53401 4.78125 9C4.78125 9.46599 5.15901 9.84375 5.625 9.84375Z" fill="black"/>
                                <path d="M12.375 9.84375C12.841 9.84375 13.2188 9.46599 13.2188 9C13.2188 8.53401 12.841 8.15625 12.375 8.15625C11.909 8.15625 11.5312 8.53401 11.5312 9C11.5312 9.46599 11.909 9.84375 12.375 9.84375Z" fill="black"/>
                            </svg>
                            <span class="mess-counts" v-show="countUnreadMess != 0" style="display: none;">@{{ countUnreadMess }}</span>
                        </span>

                        <a href="{{url('/account/notifications')}}">
                        <span class="svgIcon svgIcon--bell svgIcon--25px">
{{--                            <span class="noti-counts" v-show="countNotification != 0" style="display: none;">@{{ countNotification }}</span>--}}
                            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.33811 12.65C3.44133 12.4959 3.54684 12.3234 3.65174 12.1311C4.26774 11.0017 4.85 9.2134 4.85 6.5C4.85 5.46566 5.26089 4.47368 5.99228 3.74228C6.72368 3.01089 7.71566 2.6 8.75 2.6C9.78435 2.6 10.7763 3.01089 11.5077 3.74228C12.2391 4.47368 12.65 5.46566 12.65 6.5C12.65 9.2134 13.2323 11.0017 13.8483 12.1311C13.9532 12.3234 14.0587 12.4959 14.1619 12.65H3.33811Z" stroke="black" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M10.2981 15.75C10.1663 15.9773 9.97701 16.166 9.74929 16.2971C9.52158 16.4283 9.26341 16.4973 9.00062 16.4973C8.73784 16.4973 8.47967 16.4283 8.25196 16.2971C8.02424 16.166 7.83498 15.9773 7.70312 15.75" stroke="black" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </span>
                        </a>
                    </div>

                @endif
            </div>

        </div>
        {{-- end of mobile --}}
        {{-- end header --}}

        {{-- load nội dung --}}
        @yield('content')

        {{-- hộp thoại chat --}}
        @if(Auth::check())
            @include('frontend._layouts.chatbox')
        @endif


        {{-- tắt truy cập bằng mobile --}}
        <div class="download-store" id="download-store">
            <h2>Dũng Mori hiện đã có <br/>trên kho ứng dụng</h2>
            <p>Vui lòng tải app và sử dụng<br/>Để có được trải nghiệm tốt nhất</p>
            <div class="mobile-app">
                <a href="https://apps.apple.com/us/app/id1486123836" target="_blank" class="mobile-app-button">
                    <img src="{{asset('assets/img/dungmori_ios.png')}}" alt="img"/>
                </a>
                <a href=" https://play.google.com/store/apps/details?id=com.dungmori.dungmoriapp" target="_blank" class="mobile-app-button">
                    <img src="{{asset('assets/img/dungmori_android.png')}}" alt="img"/>
                </a>
            </div>
        </div>
        {{-- <script type="text/javascript">
            var userAgent = navigator.userAgent || navigator.vendor || window.opera;
            if(window.location.href.indexOf("/bai-viet") == -1){ //bỏ check cho trang bài viết
                if (/android/i.test(userAgent)) document.getElementById("download-store").style.display = "block";
                if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) document.getElementById("download-store").style.display = "block";
            }
        </script> --}}

        {{--chống đăng nhập trên nhiều máy/chỉ gọi khi người dùng đang đăng nhập ko phải admin --}}
        <script type="text/javascript">
            var lastFingerprint = null;
            var embed = "";
        </script>
        <script>
          function goTo(url) {
            window.location.href = url;
          }

          function goToWindow(url) {
            window.open(url);
          }
        </script>
        @if(Auth::check() && $adminSession == false)
        <script type="text/javascript">
            var lastFingerprint = "{{Auth::user()->fingerprint}}";
            var lastBrowser = "{{Auth::user()->last_browser}}";
            var lastOS = "{{Auth::user()->last_os}}";
            var embed = "{{Auth::user()->embed}}";
        </script>
        @endif

        {{-- kích tài khoản đã bị khóa --}}
        @if(Auth::check() && Auth::user()->blocked == 1)
            <script type="text/javascript">logout()</script>
        @endif

        {{-- import default js contain setting  --}}
        <script src="{{asset('plugin/vue/vue.min.js')}}"></script>

        <script src="{{asset('plugin/toastr/toastr.min.js')}}"></script>
        <link rel="stylesheet" href="{{asset('plugin/toastr/toastr.min.css')}}" />

        {{-- lấy ra thông tin Agent của trình duyệt/ check việc chống đăng nhập nhiều máy,
            tracking.js được gộp vào đây dể bảo mật --}}
        <script src="{{asset('assets/js/app.js')}}?{{filemtime('assets/js/app.js')}}"></script>

        {{--
            nếu là admin -> cho xem đầy đủ thông tin học viên/ trong comment component
            biến này được check trong hàm printPrivateEmail và printPrivatePhone
        --}}
        <script type="text/javascript">
            var enableFIV = false;
            @if($adminSession == true) enableFIV = true; @endif
        </script>

        {{-- import các template của vue component để hỗ trợ các version cũ  --}}
        @include('frontend._layouts.template')

        {{-- tắt console log ở mode production --}}
       {{--  @if($_SERVER['SERVER_NAME'] == "dungmori.com")
            <script type="text/javascript">
                console.log = function() {}
            </script>
        @endif --}}

        <script src="{{asset('plugin/driver/driver.min.js')}}?{{filemtime('plugin/driver/driver.min.js')}}"></script>
        <link rel="stylesheet" href="{{asset('plugin/driver/driver.min.css')}}?{{filemtime('plugin/driver/driver.min.css')}}">
        {{-- import vanila emoji picker  --}}
        <script src="{{asset('plugin/vanilla-emoji-picker/emojiPicker.js')}}?{{filemtime('plugin/vanilla-emoji-picker/emojiPicker.js')}}"></script>

        {{-- thư viện moment js --}}
        <script src="{{asset('plugin/moment/moment.min.js')}}?{{filemtime('plugin/moment/moment.min.js')}}"></script>
        <script type="text/javascript"> moment.locale('vi') </script>

        {{-- import all vue components  --}}
        <script src="{{asset('assets/js/components.js')}}?{{filemtime('assets/js/components.js')}}"></script>

        {{-- nếu đã login -> import component thông báo /else/ import auth neu chua login  --}}
        @if(Auth::check())
        <script type="text/javascript">
            var myUserId = "{{Auth::user()->id}}";
            var countMess = "{{ session('_countMess') }}";
            var countAdminMess = "{{ session('_countAdminMess') }}";
            countAdminMess = parseInt(countAdminMess);
            var countNoti = "{{ session('_countNoti') }}";
        </script>
        <script src="{{asset('plugin/socket-io-4.1.2/socket.io.min.js')}}?{{filemtime('plugin/socket-io-4.1.2/socket.io.min.js')}}"></script>
        <script src="{{ asset('assets/js/chatbox.js') }}?{{filemtime('assets/js/chatbox.js')}}"></script>
        <script src="{{asset('assets/js/count_message.js')}}?{{filemtime('assets/js/count_message.js')}}"></script>
        <script src="{{ asset('js/announce.js') }}?{{filemtime('js/announce.js')}}"></script>
        @else
        <script src="{{asset('assets/js/GVuZ3RoKCk.js')}}?{{filemtime('assets/js/GVuZ3RoKCk.js')}}"></script>
        @endif

        @yield('footer-js')
        @yield('bottom-js')
        @yield('lesson-bottom')

        {{-- Global site tag (gtag.js) - Google Analytics  --}}
        {{-- <script async src="https://www.googletagmanager.com/gtag/js?id=UA-131128751-4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'UA-131128751-4');
        </script> --}}

{{--        <script src="{{ asset('assets/js/counter.js') }}?{{filemtime('assets/js/counter.js')}}"></script>--}}
        <script type="text/javascript" src="{{asset('assets/js/modal.js')}}?{{filemtime('assets/js/modal.js')}}"></script>


        {{-- đoạn theo dõi google analytics / chỉ bật trên server thật  --}}
        @if($_SERVER['SERVER_NAME'] == "dungmori.com")
            <script type="text/javascript">
              (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
              (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
              m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
              })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
              ga('create', 'UA-98604763-1', 'auto');
              ga('send', 'pageview');
            </script>

            <!-- Facebook Pixel Code -->
    <script>
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '511629070106054');
      fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=511629070106054&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Facebook Pixel Code -->

            <!-- Hotjar Tracking Code for www.dungmori.com -->
            <script>
                (function(h,o,t,j,a,r){
                    h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                    h._hjSettings={hjid:1928619,hjsv:6};
                    a=o.getElementsByTagName('head')[0];
                    r=o.createElement('script');r.async=1;
                    r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                    a.appendChild(r);
                })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            </script>
        @endif

        {{-- Global site tag (gtag.js) - Google Analytics  --}}
        <script async src="https://www.googletagmanager.com/gtag/js?id=UA-131128751-4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'UA-131128751-4');
        </script>

        <script>
          (function() {
            var ta = document.createElement('script'); ta.type = 'text/javascript'; ta.async = true;
            ta.src = 'https://analytics.tiktok.com/i18n/pixel/sdk.js?sdkid=BPU67QVC2JVVVND7OQEG';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(ta, s);
          })();
        </script>

        @if(!Auth::check())
            <script>
              var search = window.location.search;
              var qs = new URLSearchParams(search);
              if (qs.has("action")) {
                var action = qs.get("action");
                if (action == "login") {
                  var loginBtn = document.getElementById('text-login');
                  loginBtn.click();
                }
              }
            </script>
        @endif
    </div>
</body>
</html>
