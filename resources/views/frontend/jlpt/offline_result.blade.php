@extends('frontend.jlpt._app')

@section('title') <PERSON>ế<PERSON> quả thi thử JLPT @stop
@section('description')Thi thử JLPT offline v<PERSON><PERSON> @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t vớ<PERSON> phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')
<div class="main">
    <div class="p-5" id="offline-data">
        <div class="flex items-center justify-content-start" style="flex-wrap: wrap">
            <div class="mt-2 form-group mr-3 mb-0">
                <input
                    v-model="search"
                    type="email"
                    class="form-control"
                    id="exampleInputEmail1"
                    aria-describedby="emailHelp"
                    placeholder="Tìm kiếm bằng email hoặc SĐT"
                    style="min-width: 400px;"
                    @keyup.enter="initData"
                >
            </div>
            <div class="mt-2 form-group mr-3 mb-0">
                <select
                    v-model="level"
                    class="form-control"
                    style="min-width: 50px;"
                    @change="initData"
                >
                    <option value="N1">N1</option>
                    <option value="N2">N2</option>
                    <option value="N3">N3</option>
                    <option value="N4">N4</option>
                    <option value="N5">N5</option>
                </select>
            </div>
            <div class="mt-2 btn btn-info" @click="initData">Tìm kiếm</div>
        </div>
        <div class="table-responsive mt-2">
            <table class="table mt-5" style="background-color: white">
                <thead>
                <tr>
                    <th scope="col">#</th>
                    <th scope="col">Họ tên</th>
                    <th scope="col">Mail</th>
                    <th scope="col">Cấp độ</th>
                    <th scope="col">Thi tại</th>
                    <th scope="col" class="text-center">
                        <span v-if="['N1', 'N2', 'N3'].includes(level)">Chữ Hán - Từ vựng - Ngữ pháp</span>
                        <span v-if="['N4', 'N5'].includes(level)">Chữ Hán - Từ vựng - Ngữ pháp - Đọc hiểu</span>
                    </th>
                    <th v-if="['N1', 'N2', 'N3'].includes(level)" scope="col" class="text-center">
                        Đọc hiểu
                    </th>
                    <th scope="col" class="text-center">Nghe hiểu</th>
                    <th scope="col" class="text-center">Tổng</th>
                </tr>
                </thead>
                <tbody>
                <tr v-if="loading">
                    <td colspan="12"><div style="width: 100%;text-align: center">Đang tải...</div></td>
                </tr>
                <tr v-else-if="!list.length">
                    <td colspan="12"><div style="width: 100%;text-align: center">Không tìm thấy kết quả trùng khớp</div></td>
                </tr>
                <template v-else>
                    <tr v-for="(item, idx) in list" :style="getColor(idx)">
                        <td scope="row" :style="{fontSize: idx < 3 ? 22 - idx*2 + 'px' : '14px', fontWeight: idx < 3 ? 'bold' : 'normal'}">@{{ idx + 1 }}</td>
                        <td style="font-weight: bold">@{{ item.name }}</td>
                        <td>@{{ item.email }}</td>
                        <td>@{{ item.level }}</td>
                        <td>@{{ item.location }}</td>
                        <td class="text-center">@{{ ['N1', 'N2', 'N3'].includes(level) ? item.mark_1 : item.mark_1 + item.mark_2 }}</td>
                        <td v-if="['N1', 'N2', 'N3'].includes(level)" class="text-center">@{{ item.mark_2 }}</td>
                        <td class="text-center">@{{ item.mark_3 }}</td>
                        <td class="text-center">@{{ item.mark_total }}</td>
                    </tr>
                </template>

                </tbody>
            </table>
        </div>
    </div>
</div>


@stop
@section('footer-js')
    <script>
        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
        });
        new Vue({
            el: '#offline-data',
            data: function() {
                return {
                    list: [],
                    search: '',
                    level: 'N2',
                    loading: false
                }
            },
            computed: {
              searchStr: function() {
                  var search = ''
                  if (this.search && this.level) {
                      search = search + '?search=' + this.search + '&level=' + this.level
                  } else if (this.search) {
                      search = search + '?search=' + this.search
                  } else if (this.level) {
                      search = search + '?level=' + this.level
                  }
                  return search
              }
            },
            methods: {
                initData: function() {
                    this.loading = true
                    var vm = this
                    $.get(window.location.origin + '/ket-qua-thi/list' + vm.searchStr).then(function (res) {
                        vm.list = res.data
                        vm.loading = false
                    });
                },
                handleInput: function() {
                    this.$refs.fileInput.click()
                },
                upload: function(event) {
                    this.loading = true
                    const data = new FormData()
                    data.append('file', event.target.files[0])
                    data.append('_token', $('meta[name="csrf-token"]').attr("content"))
                    $.ajax({
                        url : window.location.origin + '/backend/nhap-diem-offline/import',
                        type: 'POST',
                        data: data,
                        cache : false,
                        processData: false,
                        contentType: false,
                    }).done(function(res) {
                        // console.log(res)
                    });
                },
                getColor: function(idx) {
                    const colors = [
                        'rgb(255, 249, 192)',
                        'rgba(249, 226, 161, 0.9)',
                        'rgba(255, 200, 150, 0.8)',
                        'rgba(237, 255, 223, 0.7)',
                        'rgba(237, 255, 223, 0.6)',
                        'rgba(237, 255, 223, 0.5)',
                        'rgba(237, 255, 223, 0.4)',
                        'rgba(237, 255, 223, 0.3)',
                        'rgba(237, 255, 223, 0.2)',
                        'rgba(237, 255, 223, 0.1)',
                    ]
                    var color = colors[idx] || '#fff'
                    return 'background-color: ' + color
                }
            },
            mounted: function () {
                this.initData()
            },
        })
    </script>
@stop
