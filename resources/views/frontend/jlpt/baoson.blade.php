@extends('frontend.jlpt._app')

@section('title') <PERSON>hi thử JLPT online - Baoson @stop
@section('description')Thi thử JLPT online với Du<PERSON>i @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON> vớ<PERSON> phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<style type="text/css">
    .header-content{display: none;}
    .nav-left {height: 40px;}
    .nav-left .mn-item{display: none;}
</style>

<div class="main">
    <div class="main-center">
        <div class="main-exam-menu" style="text-align: center; border-bottom: none; padding-top: 20px;">
            <img style="display: inline;" src="{{url('/assets/img/logobs.png')}}">
        </div>

        <div class="main-exam-right" style="opacity: 0; border-top: none;">

            @if(!Auth::check())
            <div class="requre-auth">
                <span><i class="zmdi zmdi-info-outline"></i> @lang('jlpt.content.please')
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('login')"
                     style="cursor: pointer;">@lang('jlpt.content.login')  </a> @lang('jlpt.content.or')
                    <a data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('register')"
                     style="cursor: pointer;">@lang('jlpt.content.register')  </a>
                     @lang('jlpt.content.do_this_exam')
                </span>
            </div>
            @endif

            <p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p>
            {{-- <div class="no-auth-notifi"></div> --}}
            {{-- <div class="no-auth-notifi">
                <span>
                    Thi thử trực tuyến sẽ diễn ra vào thứ 7 hàng tuần với 2 ca thi (<b>giờ Việt Nam</b>)
                    &nbsp; &nbsp; Sáng: <b>9h-11h30</b> &nbsp; &nbsp; Tối: <b>19h-21h30</b>
                </span>
            </div> --}}


            @if(Auth::check())
            <table class="table table-borderless">
                <thead>
                    <tr>
                        <th scope="col" style="text-align: center; width: 90px;">@lang('jlpt.exam.level')</th>
                        <th scope="col" class="mobile-hidden">@lang('jlpt.exam.pass_score')</th>
                        <th scope="col" class="mobile-hidden">@lang('jlpt.exam.duration')</th>
                        <th scope="col" width="300px">@lang('jlpt.exam.agenda')</th>
                        <th scope="col" style="text-align: right; padding-right: 35px;">@lang('jlpt.exam.online')</th>
                    </tr>
                </thead>
                <tbody>

                    <?php
                        $passScore = ['80', '90', '95', '90', '100'];
                        $durations = ['105', '125', '140', '155', '170'];
                    ?>
                    @foreach(['N5', 'N4', 'N3', 'N2', 'N1', 'N0'] as $key => $course)
                    {{-- N0 là chuẩn bị tham gia thi => User đang online ở page thi-thu--}}
                    @if ($course != 'N0')
                    <tr>
                        <th class="center"><span class="nx {{$course}}">{{$course}}</span></th>
                        <td class="mobile-hidden"> {{$passScore[$key]}}/180</td>
                        <td class="mobile-hidden">{{$durations[$key]}} @lang('jlpt.content.minutes')</td>
                        <td class="action-area">
                            <div v-if="isEnableExam('{{$course}}')">
                                <span v-if="joinBtnState['{{$course}}'] == 0">-</span>
                                <span v-if="joinBtnState['{{$course}}'] == 1" style="line-height: 0.9;">
                                    <span style="font-size: 13px;"><i class="zmdi zmdi-timer"></i> @lang('jlpt.content.start_at') </span><br/>
                                    <b style="color: green; font-size: 14px;">
                                        {{App\Http\ModelsFrontend\ThiThu\Exam::getTimeStartByLevel($exams, $course)}}
                                    </b>
                                </span>
                                <span v-if="joinBtnState['{{$course}}'] == 2" class="btn dmr-btn" style="padding-left: 10px;" v-on:click="joinRoom('{{$course}}')">
                                    @if(App\Http\ModelsFrontend\ThiThu\Exam::getAccessCodeByLevel($exams, $course) == null)
                                        <i class="zmdi zmdi-time zmdi-hc-spin"></i>
                                    @else
                                        <i class="zmdi zmdi-key"></i>
                                    @endif
                                    &nbsp; @lang('jlpt.content.join_room')
                                </span>
                                <span v-if="joinBtnState['{{$course}}'] == 3" style="line-height: 1;">
                                    <span style="font-size: 13px; color: #777; width: 100%; float: left;">
                                        <i class="zmdi zmdi-timer-off"></i> @lang('jlpt.content.end_at')
                                    </span>
                                    <span style="color: #555; font-size: 14px;">
                                        {{App\Http\ModelsFrontend\ThiThu\Exam::getTimeEndByLevel($exams, $course)}}
                                    </span>
                                </span>
                            </div>
                            <span v-else style="opacity: 0.6;">
                                {{ isset($schedules[$key]) ? $schedules[$key]->schedule : '
                                —
                            ' }}
                            </span>
                        </td>
                        <td class="center">
                            {{-- nếu đang trong trạng thái join room --}}
                            <span v-if="isEnableExam('{{$course}}') && joinBtnState['{{$course}}'] == 2" style="width: 100%; float: right; text-align: right; padding-right: 30px;">
                                <div class="top a2"></div>
                                <b v-html="totalOnline('{{$course}}')"></b> @lang('jlpt.content.people')
                            </span>
                            <span v-else style="float: right; margin-right: 55px;">-</span>
                        </td>
                    </tr>
                    @else

                    @endif

                    @endforeach

                </tbody>
            </table>
            <div style="width: 100%; float: left; font-size: 16px;">
                <div style="width: 75%; float: left; text-align: right; padding-right: 35px;" >
                    <i style="font-size: 13px;">@lang('jlpt.content.now_in_vietnam')</i>
                    <div style="background: green; padding-bottom: .2em;" class="label label-primary" v-html="vnTime"></div>
                    <i style="font-size: 13px;">@{{showCurrentDate}}</i><br/>
                </div>
                <div style="width: 25%; font-size: 14px; float: right; text-align: right; padding-right: 35px;">
                    @lang('jlpt.content.current_online') &nbsp;
                    <div class="top a2"></div>
                    <b v-html="totalOnline('N0')"></b> @lang('jlpt.content.people')
                </div>
            </div>
            @endif

        </div>

        {{-- <a href="{{url('/khoa-hoc')}}"><img style="margin-top: -30px;" src="{{url('/assets/img/luyendem.png')}}"/></a> --}}
    </div>

</div>


@stop
@section('footer-js')

    <script type="text/javascript">
        $(".thi-thu").addClass("active");
        var enableExams = {!! json_encode($exams) !!};
        var timeStarts = {!! json_encode($timeStarts) !!};
        var timeEnds = {!! json_encode($timeEnds) !!};

        var me = null;
        @if(Auth::check())
            me = { id: "{{Auth::user()->id}}", name: "{{Auth::user()->name}}", avatar: "{{Auth::user()->avatar}}"};
        @endif

        if(performance.navigation.type == 2){
            location.reload(true);
        }
    </script>

    <script src="{{asset('plugin/socket-io/socket.io-1.7.3.js')}}"></script>
    <script src="{{asset('plugin/crypto-js/crypto-js.min.js')}}"></script>

    <script type="text/javascript">
        var jlptUrl = "http://localhost:3333";
        var jlptSocket = "http://localhost:8008";

        //nếu là deploy trên web thật
        if(window.location.href.indexOf("dungmori.com") !== -1){
            jlptUrl = "https://baoson.dungmori.com";
            jlptSocket = "https://mjt-count.dungmori.com";
        }
    </script>
    <script src="{{asset('assets/js/jlpt-exam.js')}}?{{filemtime('assets/js/jlpt-exam.js')}}"></script>

    {{-- @if(Auth::check())
    <script src="{{asset('plugin/socket/regenerator.min.js')}}"></script>
    <script src="{{asset('plugin/socket/websocket-client.js')}}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.2/rollups/aes.js"></script>
    <script src="{{asset('assets/js/jlpt-exam.js')}}?{{filemtime('assets/js/jlpt-exam.js')}}"></script>
    @endif --}}
@stop
