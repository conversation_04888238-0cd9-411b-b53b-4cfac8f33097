@extends('frontend._layouts.default')

@section('title') Dungmori - {{ $blog->title }} @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON>y tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('/cdn/blog/default')}}/{{ $blog->image_name }} @stop
@section('author') DUNGMORI @stop

@section('footer-js')
   <script> $(".bai-viet").addClass("active");  </script>
   <script type="text/javascript">
      new Vue({ el: '.fb-comments' }) ;
   </script>
@stop
@section('header-css')
   <style>
      table {
         border-width: 3px !important;
         border-style: double !important;
         border-color: #000 !important;
         padding: 5px;
      }

      table tbody tr td, table thead tr thead {
         border: 1px solid #000 !important;
      }
   </style>
@section('content')

<div id="fb-root"></div>
<script>(function(d, s, id) {
   var js, fjs = d.getElementsByTagName(s)[0];
   js = d.createElement(s); js.id = id;
   if (d.getElementById(id)) return;
   js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
   fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));</script>

<div class="main">
   <div class="main-center">
      <div class="main-left blog-main-left">
         <div class="blog-detail-container">
            <div class="flex flex-row justify-content-between blog-info-box">
               <div class="new-type detail-blog-category">
                  @if (!is_null($thisCategory))
                     <span>{{ $thisCategory->name }}</span>
                  @endif
               </div>
               <span class="blog-detail-info"><i class="fa fa-calendar-check-o"></i>  {{ $blog->getFriendlyTime() }}</span>
            </div>
            <h1 class="blog-detail-title">{{ $blog->title }}</h1>
            <div class="blog-social-like">
              <div class="fb-share-button" data-href="{{url('bai-viet')}}/{{ $blog->id }}-{{ $blog->url }}" data-layout="button_count" data-size="small"><a target="_blank" href="https://www.facebook.com/sharer/sharer.php?u={{url('bai-viet')}}/{{ $blog->id }}-{{ $blog->url }}&amp;src=sdkpreparse" class="fb-xfbml-parse-ignore">Chia sẻ</a></div>
            </div>
            <div class="blog-detail-content">
               @if (!is_null($blog) && $blog->image_name != null && $blog->image_name != "" && $blog->show_image)
                  <img  class="blog-detail-thumb" src="{{url('/cdn/blog/default')}}/{{ $blog->image_name }}" alt="{{ $blog->title }}">
                  <br/><br/>
               @endif
               <div class="main-content">{!! html_entity_decode($blog->content) !!}</div>
            </div>

            <div class="related-blogs-container">
               <div class="blog-heading">Bài viết khác</div>
               <div class="related-blogs-content">
                  @foreach ($listRelatedBlog as $blog)
                     <div class="news-item featured-more">
                        <a href="{{url('')}}/bai-viet/{{ $blog->id }}-{{ $blog->url }}">
                           @if ($blog->image_name)
                              <img class="lazyload" src="{{url('/cdn/blog/small')}}/{{ $blog->image_name }}"/>
                           @else
                              <img class="lazyload" src="{{url('assets/img/default-blog.jpg')}}"/>
                           @endif
                        </a>
                        <div class="new-type">
                           <span>{{ $blog->category_name }}</span>
                        </div>
                        <a href="{{url('')}}/bai-viet/{{ $blog->id }}-{{ $blog->url }}" title="{{ $blog->title }}">
                           <h1 class="title" style="-webkit-box-orient: vertical;">{{ $blog->title }}</h1>
                        </a>
                        <span class="info"><i class="fa fa-calendar-check-o"></i>  {{ $blog->getFriendlyTime() }}</span>
                        <span class="brief"{{ $blog->intro }}</span>
                     </div>
                  @endforeach
               </div>
            </div>

            <div class="comment-container fb-comments">
               <div class="comment-heading">
                  <span>Bình luận</span>
                  <div class="border-comment-heading"></div>
               </div>
               <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
            </div>

            </div>
      </div>
      <div class="main-right blog-main-right">
         <div class="list-group list-category detail-list-category">
            <a class="list-group-item item-heading">Chuyên mục</a>
            @foreach($listCategories as $category)
               <a href="{{url('bai-viet/cm')}}/{{ $category->friendly_url }}" class="list-group-item {{ $category->friendly_url }}">{{ $category->name }}</a>
            @endforeach
         </div>

         <a href="{{url('')}}/bai-viet/cm/bai-viet-facebook"><h3 class="related-title">Bài viết Facebook</h3></a>

         @foreach($facebookBlog as $item)
            <div class="related-news-item">
               <a
                  @if ($item->category_id === 20)
                     target="_blank"
                     href="{{ $item->content }}"
                  @else
                     href="{{url('')}}/bai-viet/{{ $item->id }}-{{ $item->url }}"
                  @endif
                  title="{{ $item->title }}"
               >
                  @if($item->image_name != '')
                     <img class="lazyload" src="{{url('/cdn/blog/small')}}/{{ $item->image_name }}"/>
                  @else
                     <img class="lazyload" src="{{url('assets/img/default-blog.jpg')}}"/>
                  @endif
               </a>
               <a
                  @if ($item->category_id === 20)
                     target="_blank"
                     href="{{ $item->content }}"
                  @else
                     href="{{url('')}}/bai-viet/{{ $item->id }}-{{ $item->url }}"
                  @endif
                  title="{{ $item->title }}"
               >
                  <div class="new-type facebook-new-type">
                     <span>{{ $item->category_name }}</span>
                  </div>
                  <span class="title" style="-webkit-box-orient: vertical;">{{ $item->title }}</span>
                  <span class="info"><i class="fa fa-calendar-check-o"></i>  {{ $item->getFriendlyTime() }}  </span>
               </a>
            </div>
         @endforeach

         <a class="see-more-blog-facebook" href="{{url('')}}/bai-viet/cm/bai-viet-facebook">
            Xem thêm >>
         </a>
      </div>
   </div>
</div>

  @section('footer-js')

   <script>
      $(".bai-viet").addClass("active");
      @if(isset($thisCategory))
         $(".{{$thisCategory->friendly_url}}").addClass("active");
      @endif
   </script>

  @stop
@stop
