@extends('frontend._layouts.default')

@section('title') Dungmori - Trang tin tức @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng Nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
   <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
@stop

@section('content')

<div class="main">
   <div class="main-center">
      <div class="new-blog-container">
         <div class="new-blog-content">
            <h2 class="blog-heading"><PERSON><PERSON><PERSON> vi<PERSON><PERSON> mớ<PERSON> nhất</h2>
            <div class="newest-blog-box">
               <div id=new-blogs>
                  @foreach ($newestBlogs as $blog)
                     <div class="new-blog-item">
                        <a
                           @if ($blog->category_id === 20)
                              target="_blank"
                              href="{{ $blog->content }}"
                           @else
                              href="{{url('')}}/bai-viet/{{ $blog->id }}-{{ $blog->url }}">
                           @endif
                           @if ($blog->image_name)
                              <img class="new-blog-img lazyload" class="" src="{{url('/cdn/blog/default')}}/{{ $blog->image_name }}">
                           @else
                              <img class="new-blog-img lazyload" src="{{url('assets/img/default-blog.jpg')}}"/>
                           @endif
                        </a>
                        <div class="new-blog-info">
                           <span class="new-blog-tag">{{ $blog->category_name }}</span>
                           <a
                              @if ($blog->category_id === 20)
                                 target="_blank"
                                 href="{{ $blog->content }}"
                              @else
                                 href="{{url('')}}/bai-viet/{{ $blog->id }}-{{ $blog->url }}">
                              @endif
                           >
                              <h2 style="-webkit-box-orient: vertical;">{{ $blog->title }}</h2>
                           </a>
                           <span class="blog-time"><i class="fa fa-calendar-check-o"></i>  {{ $blog->getFriendlyTime() }}</span>
                           <p class="blog-intro" style="-webkit-box-orient: vertical;">{{ $blog->intro }}</p>
                        </div>
                     </div>
                  @endforeach
               </div>
            </div>
         </div>
      </div>

      <div class="list-group list-category mobile-ct-group">
         <a class="list-group-item item-heading accordion">Chuyên mục <i class="fa fa-angle-down"></i></a>
         <div class="panel">
            @foreach($listCategories as $category)
               <a href="{{url('')}}/bai-viet/cm/{{ $category->friendly_url }}" class="list-group-item {{ $category->friendly_url }}">{{ $category->name }}<span class="blog-count">{{ $category->count }}</span></a>
            @endforeach
         </div>
         <script>
            var acc = document.getElementsByClassName("accordion");
            var i;
            for (i = 0; i < acc.length; i++) {
               acc[i].addEventListener("click", function() {
                  this.classList.toggle("active");
                  var panel = this.nextElementSibling;
                  if (panel.style.maxHeight) {
                     panel.style.maxHeight = null;
                  } else {
                     panel.style.maxHeight = panel.scrollHeight + "px";
                  }
               });
            }
         </script>
      </div>

      <div id="blog-container" class="main-left blog-main-left">
         @if (count($listBlog) === 0)
            <div class="flex justify-center"><span class="no-more-blog">Không còn bài viết nào</span></div>
         @else
            <div class="blog-hidden-before">
               <h2 class="blog-heading">Bài viết gần đây</h2>
               <div class="blog-content">
                  <div v-for="blog in listBlog" :key="'blog_' + blog.id" class="news-item featured-more">
                     <a
                        :href="blog.category_id === 20 ? blog.content : `{{url('')}}/bai-viet/${blog.id}-${blog.url}`"
                        :target="blog.category_id === 20 ? '_blank' : '_self'"
                     >
                        <img v-if="blog.image_name" class="lazyload" :src="`{{url('/cdn/blog/small')}}/${blog.image_name}`"/>
                        <img v-else class="lazyload" src="{{url('assets/img/default-blog.jpg')}}"/>
                     </a>
                     <div class="new-type">
                        <span>@{{ blog.category_name }}</span>
                     </div>
                     <a
                        :href="blog.category_id === 20 ? blog.content : `{{url('')}}/bai-viet/${blog.id}-${blog.url}`"
                        :title="blog.title"
                        :target="blog.category_id === 20 ? '_blank' : '_self'"
                     >
                        <h1 class="title" style="-webkit-box-orient: vertical;">@{{ blog.title }}</h1>
                     </a>
                     <span class="info"><i class="fa fa-calendar-check-o"></i>  @{{ getFriendlyTime(blog.created_at) }}</span>
                     <span class="brief" style="-webkit-box-orient: vertical;">@{{ blog.intro }}</span>
                  </div>
               </div>

               <div class="flex justify-center load-more-box">
                  <div class="flex items-center justify-center load-more-text" @click="loadMoreBlogs">
                     <span v-if="endOfList">Không còn bài viết nào</span>
                     <span v-else-if="!loading">Bấm để xem thêm</span>
                     <img v-else="loading" :src="`{{url('/assets/img/loading.gif')}}`" />
                  </div>
               </div>
            </div>
         @endif
      </div>
      <div class="main-right blog-main-right">
         <div class="list-group list-category">
            <a class="list-group-item item-heading">Chuyên mục</a>
            @foreach($listCategories as $category)
               <a href="{{url('')}}/bai-viet/cm/{{ $category->friendly_url }}" class="list-group-item {{ $category->friendly_url }}">{{ $category->name }}<span class="blog-count">{{ $category->count }}</span></a>
            @endforeach
         </div>

         <a href="{{url('')}}/bai-viet/cm/bai-viet-facebook">
            <h3 class="related-title">Bài viết Facebook</h3>
         </a>

         @foreach($facebookBlog as $item)
            <div class="related-news-item">
               <a
                  @if ($item->category_id === 20)
                     target="_blank"
                     href="{{ $item->content }}"
                  @else
                     href="{{url('')}}/bai-viet/{{ $item->id }}-{{ $item->url }}"
                  @endif
                  title="{{ $item->title }}"
               >
                  @if($item->image_name != '')
                     <img class="lazyload" src="{{url('/cdn/blog/small')}}/{{ $item->image_name }}"/>
                  @else
                     <img class="lazyload" src="{{url('assets/img/default-blog.jpg')}}"/>
                  @endif
               </a>
               <a
                  @if ($item->category_id === 20)
                     target="_blank"
                     href="{{ $item->content }}"
                  @else
                     href="{{url('')}}/bai-viet/{{ $item->id }}-{{ $item->url }}"
                  @endif
                  title="{{ $item->title }}"
               >
                  <div class="new-type facebook-new-type">
                     <span>{{ $item->category_name }}</span>
                  </div>
                  <span class="title" style="-webkit-box-orient: vertical;">{{ $item->title }}</span>
                  <span class="info"><i class="fa fa-calendar-check-o"></i>  {{ $item->getFriendlyTime() }}  </span>
               </a>
            </div>
         @endforeach

         <a class="see-more-blog-facebook" href="{{url('')}}/bai-viet/cm/bai-viet-facebook">
            Xem thêm >>
         </a>
      </div>
   </div>
</div>

@section('footer-js')
   <script type="text/javascript">
      var listBlog = {!! json_encode($listBlog) !!};

      @if (isset($thisCategory))
         var cId = {!! json_encode($thisCategory->id) !!};
      @else
         var cId = null;
      @endif
   </script>
   <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
   <script src="{{asset('assets/js/blog.js')}}?{{filemtime('assets/js/blog.js')}}"></script>
   <script>
      $(document).ready(function(){
         $('#new-blogs').slick({
            infinite: true,
            slidesToShow: 1,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 2000,
            dots: true,
            customPaging: function(slider, i) {
               return '<i class="fa fa-circle" aria-hidden="true"></i>';
            },
            nextArrow: '<div class="arrow next-arrow"><i class="fa fa-arrow-right" aria-hidden="true"></i></div>',
            prevArrow: '<div class="arrow prev-arrow"><i class="fa fa-arrow-left" aria-hidden="true"></i></div>'
         });
      });

      $(".bai-viet").addClass("active");
      @if(isset($thisCategory))
         $(".{{$thisCategory->friendly_url}}").addClass("active");
      @endif
   </script>
@stop

@stop
