@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON> sách tại dungmori @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('{{asset("assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
@stop
@section('header-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
@stop
@section('content')
<div class="main main-books">
    <div class="slider-book"></div>
    <div class="main-center main-book">
        <div class="new-release">
            <div class="new-release__content">
                <h2 class="book__title"><img src="{{asset("assets/img/book/bi1.svg")}}" alt="Các đầu sách mới phát hành"/>Các đầu sách mới phát hành</h2>
                <div class="new-release__box">
                    <div class="book__slider" id="book-slider-1">
                        @foreach($recentBook as $book)
                            <div class="new-release__item">
                                <a class="new-release__item-inner" href="{{route('frontend.book.detail', ['url' => $book->SEOurl])}}">
                                    <div class="image">
                                        <img
                                            @if ($book->cover_name == null || $book->cover_name == '')
                                            src="{{url('assets/img/icon_backend')}}/no_image.png"
                                            @else
                                            src="{{url('/cdn/book/default/'.$book->cover_name)}}"
                                            @endif
                                            alt="{{ $book->name }}"
                                        />
                                    </div>
                                    <div class="title">{{ $book->name }}</div>
                                    @if ($book->combo)
                                        <div class="price">
                                            Tại Nhật: <strong>
                                            Liên hệ
{{--                                            {{number_format($book->combo->jpy_price)}}¥--}}
                                          </strong>
                                        </div>
                                        <div class="price">
                                            Tại VN: <strong>{{number_format($book->combo->price_at_vn)}}đ </strong>
                                        </div>
{{--                                        <div class="solds">Đã bán {{$book->combo->invoices_count}}</div>--}}
                                    @endif
                                </a>
                            </div>
                        @endforeach
                    </div>
                    <div class="book__list-navigation">
                        <div id="prv_sld_1" class="slide-btn"><img src="{{asset("assets/img/book/prev.svg")}}"/></div>
                        <div id="nxt_sld_1" class="slide-btn"><img src="{{asset("assets/img/book/next.svg")}}"/></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="book__navigation">
        <div class="book__navigation-items">
{{--            <div class="book__navigation-item active" onclick="handleScroll('so-cap')">--}}
{{--                <img src="{{asset("assets/img/book/icon-so-cap.svg")}}" />--}}
{{--                <div>Sơ cấp</div>--}}
{{--            </div>--}}
            <div class="book__navigation-item" onclick="handleScroll('trung-cap')">
                <img src="{{asset("assets/img/book/icon-trung-cap.svg")}}" />
                <div>Trung cấp</div>
            </div>
{{--            <div class="book__navigation-item" onclick="handleScroll('cao-cap')">--}}
{{--                <img src="{{asset("assets/img/book/icon-cao-cap.svg")}}" />--}}
{{--                <div>Cao cấp</div>--}}
{{--            </div>--}}
            <a class="book__navigation-item" href="{{route('frontend.book.audio_book')}}">
                <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                <div>File nghe</div>
            </a>
            <a class="book__navigation-item" href="{{ route('frontend.book.flashcard_book') }}">
              <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
              <div>Flashcard</div>
            </a>
        </div>
    </div>
{{--    <div class="book__category" id="so-cap">--}}
{{--        <div class="book__title flex items-center justify-center">--}}
{{--            <img src="{{asset("assets/img/book/icon-title.svg")}}"/>--}}
{{--            <div>Sách Sơ cấp</div>--}}
{{--        </div>--}}
{{--        <div class="book__description quote">--}}
{{--            <img src="{{asset("assets/img/book/quote-before.svg")}}" class="quote-symbol quote-symbol--before" />--}}
{{--            <div>Giới thiệu sách phù hợp với cấp độ nào? Giúp rèn luyện, cải thiện những kĩ năng .... It is a long established fact that a reader.</div>--}}
{{--            <img src="{{asset("assets/img/book/quote-after.svg")}}" class="quote-symbol quote-symbol--after" />--}}
{{--        </div>--}}
{{--        <div class="book__list">--}}
{{--            <div class="book__slider" id="book-slider-4">--}}
{{--                @foreach($scBook as $book)--}}
{{--                    <div class="book__item">--}}
{{--                        <a class="book__item-inner" href="{{route('frontend.book.detail', ['url' => $book->SEOurl])}}">--}}
{{--                            <div class="image">--}}
{{--                                <img--}}
{{--                                        @if ($book->cover_name == null || $book->cover_name == '')--}}
{{--                                        src="{{url('assets/img/icon_backend')}}/no_image.png"--}}
{{--                                        @else--}}
{{--                                        src="{{url('/cdn/book/default/'.$book->cover_name)}}"--}}
{{--                                        @endif--}}
{{--                                        alt="{{ $book->name }}"--}}
{{--                                />--}}
{{--                            </div>--}}
{{--                            <div class="title">{{$book->name}}</div>--}}
{{--                            @if ($book->combo)--}}
{{--                                <div class="solds">Đã bán {{$book->combo->invoices_count}}</div>--}}
{{--                                <div class="book__price">--}}
{{--                                    <sup>JPY</sup> <strong>{{number_format($book->combo->jpy_price)}}¥</strong> |--}}
{{--                                    <sup>VNĐ</sup> <strong>{{number_format($book->combo->price)}}đ</strong>--}}
{{--                                </div>--}}
{{--                            @endif--}}
{{--                        </a>--}}
{{--                    </div>--}}
{{--                @endforeach--}}
{{--            </div>--}}
{{--            <div class="book__list-navigation">--}}
{{--                <div id="prv_sld_4" class="slide-btn"><img src="{{asset("assets/img/book/prev.svg")}}"/></div>--}}
{{--                <div id="nxt_sld_4" class="slide-btn"><img src="{{asset("assets/img/book/next.svg")}}"/></div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
    <div class="book__category" id="trung-cap">
        <div class="book__title flex items-center justify-center">
            <img src="{{asset("assets/img/book/icon-title.svg")}}"/>
            <div>Sách Trung cấp</div>
        </div>
        <div class="book__description quote">
            <img src="{{asset("assets/img/book/quote-before.svg")}}" class="quote-symbol quote-symbol--before" />
            <div>Bộ sách Kanji của <b>Dũng Mori</b> với phương pháp <b>"Học 1 nhớ 3"</b> giúp tăng gấp 3 lần năng lực ghi nhớ của não bộ, thay vì quên đi 50% kiến thức sau khi học theo cách học truyền thống</div>
            <img src="{{asset("assets/img/book/quote-after.svg")}}" class="quote-symbol quote-symbol--after" />
        </div>
        <div class="book__list">
            <div class="book__slider" id="book-slider-2">
                @foreach($tcBook as $book)
                    <div class="book__item slick-slide">
                        <a class="book__item-inner" href="{{route('frontend.book.detail', ['url' => $book->SEOurl])}}">
                            <div class="image">
                                <img
                                    @if ($book->cover_name == null || $book->cover_name == '')
                                        src="{{url('assets/img/icon_backend')}}/no_image.png"
                                    @else
                                        src="{{url('/cdn/book/default/'.$book->cover_name)}}"
                                    @endif
                                    alt="{{ $book->name }}"
                                />
                            </div>
                            <div class="title">{{$book->name}}</div>
                            @if ($book->combo)
{{--                                <div class="solds">Đã bán {{$book->combo->invoices_count}}</div>--}}
                                <div class="book__price">
                                    <div>
                                        <sup>Tại Nhật</sup> <strong>
                                        Liên hệ
{{--                                        {{number_format($book->combo->jpy_price)}}¥--}}
                                      </strong>
                                    </div>
                                    <div>
                                        <sup>Tại VN</sup> <strong>{{number_format($book->combo->price_at_vn)}}đ</strong>
                                    </div>
                                </div>
                            @endif
                        </a>
                    </div>
                @endforeach
            </div>
            <div class="book__list-navigation">
                <div id="prv_sld_2" class="slide-btn"><img src="{{asset("assets/img/book/prev.svg")}}"/></div>
                <div id="nxt_sld_2" class="slide-btn"><img src="{{asset("assets/img/book/next.svg")}}"/></div>
            </div>
        </div>
    </div>
{{--    <div class="book__category" id="cao-cap">--}}
{{--        <div class="book__title flex items-center justify-center">--}}
{{--            <img src="{{asset("assets/img/book/icon-title.svg")}}"/>--}}
{{--            <div>Sách Cao cấp</div>--}}
{{--        </div>--}}
{{--        <div class="book__description quote">--}}
{{--            <img src="{{asset("assets/img/book/quote-before.svg")}}" class="quote-symbol quote-symbol--before" />--}}
{{--            <div>Giới thiệu sách phù hợp với cấp độ nào? Giúp rèn luyện, cải thiện những kĩ năng .... It is a long established fact that a reader.</div>--}}
{{--            <img src="{{asset("assets/img/book/quote-after.svg")}}" class="quote-symbol quote-symbol--after" />--}}
{{--        </div>--}}
{{--        <div class="book__list">--}}
{{--            <div class="book__slider" id="book-slider-3">--}}
{{--                @foreach($ccBook as $book)--}}
{{--                    <div class="book__item slick-slide">--}}
{{--                        <a class="book__item-inner" href="{{route('frontend.book.detail', ['url' => $book->SEOurl])}}">--}}
{{--                            <div class="image">--}}
{{--                                <img--}}
{{--                                    @if ($book->cover_name == null || $book->cover_name == '')--}}
{{--                                    src="{{url('assets/img/icon_backend')}}/no_image.png"--}}
{{--                                    @else--}}
{{--                                    src="{{url('/cdn/book/default/'.$book->cover_name)}}"--}}
{{--                                    @endif--}}
{{--                                    alt="{{ $book->name }}"--}}
{{--                                />--}}
{{--                            </div>--}}
{{--                            <div class="title">{{$book->name}}</div>--}}
{{--                            @if ($book->combo)--}}
{{--                                <div class="solds">Đã bán {{$book->combo->invoices_count}}</div>--}}
{{--                                <div class="book__price">--}}
{{--                                    <sup>JPY</sup> <strong>{{number_format($book->combo->jpy_price)}}¥</strong> |--}}
{{--                                    <sup>VNĐ</sup> <strong>{{number_format($book->combo->price)}}đ</strong>--}}
{{--                                </div>--}}
{{--                            @endif--}}
{{--                        </a>--}}
{{--                    </div>--}}
{{--                @endforeach--}}
{{--            </div>--}}
{{--            <div class="book__list-navigation">--}}
{{--                <div id="prv_sld_3" class="slide-btn"><img src="{{asset("assets/img/book/prev.svg")}}"/></div>--}}
{{--                <div id="nxt_sld_3" class="slide-btn"><img src="{{asset("assets/img/book/next.svg")}}"/></div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
</div>
@stop

@section('footer-js')
<script type="text/javascript">
    function handleScroll(id) {
      document.getElementById(id).scrollIntoView({
        behavior: 'smooth'
      });
    }
</script>
<script type="text/javascript">
  $(document).ready(function(){
    var sliders = {
      1: {slider : '#book-slider-1', prev: '#prv_sld_1', next: '#nxt_sld_1'},
      2: {slider : '#book-slider-2', prev: '#prv_sld_2', next: '#nxt_sld_2'},
      3: {slider : '#book-slider-3', prev: '#prv_sld_3', next: '#nxt_sld_3'},
      4: {slider : '#book-slider-4', prev: '#prv_sld_4', next: '#nxt_sld_4'}
    };
    $.each(sliders, function() {
      var slider = $(this.slider);
      slider.slick({
        slidesToShow: 1,
        arrows: false,
        speed: 800,
        draggable: false,
        variableWidth: true,
        centerMode: true,
        responsive: [
          {
            breakpoint: 768,
            settings: {
              variableWidth: false,
            }
          },
        ],
      });
      $(this.prev).on("click", function(e) {
        e.preventDefault();
        slider.slick("slickNext")
      });
      $(this.next).on("click", function(e) {
        e.preventDefault();
        slider.slick("slickPrev")
      });
    });
  })
</script>
@stop
