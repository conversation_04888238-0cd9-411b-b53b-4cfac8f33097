@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON> sách tại dungmori @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('{{asset("assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
    <style>
      /* Style the buttons that are used to open and close the accordion panel */
      .fc-accordion {
        transition: 0.4s;
      }

      /* Add a background color to the button if it is clicked on (add the .active class with JS), and when you move the mouse over it (hover) */
      .active, .fc-accordion:hover {
        /*background-color: #ccc;*/
      }

      /* Style the accordion panel. Note: hidden by default */
      .fc-panel {
        /*padding: 0 18px;*/
        /*background-color: white;*/
        display: none;
        overflow: hidden;
      }
    </style>
@stop
@section('header-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
@stop
@section('content')
    <div class="main main-books">
        <div class="slider-book"></div>
        <div class="book__navigation mt-0">
            <div class="book__navigation-items">
{{--                <a class="book__navigation-item" href="{{url('sach/am-thanh#so-cap')}}">--}}
{{--                    <img src="{{asset("assets/img/book/icon-so-cap.svg")}}" />--}}
{{--                    <div>Sơ cấp</div>--}}
{{--                </a>--}}
                <a class="book__navigation-item" href="{{url('sach/am-thanh#trung-cap')}}">
                    <img src="{{asset("assets/img/book/icon-trung-cap.svg")}}" />
                    <div>Trung cấp</div>
                </a>
{{--                <a class="book__navigation-item" href="{{url('sach/am-thanh#cao-cap')}}">--}}
{{--                    <img src="{{asset("assets/img/book/icon-cao-cap.svg")}}" />--}}
{{--                    <div>Cao cấp</div>--}}
{{--                </a>--}}
                <a class="book__navigation-item" href="{{route('frontend.book.audio', ['url' => $url])}}">
                    <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                    <div>File nghe</div>
                </a>
                <a class="book__navigation-item active" href="{{route('frontend.book.flashcards', ['url' => $url])}}">
                    <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                    <div>Flashcard</div>
                </a>
            </div>
        </div>
        <div class="audio__header">
            <div class="book__title flex items-center justify-center">
                <img src="{{asset("assets/img/book/icon-book.svg")}}"/>
                <div>{{$book->name}}</div>
            </div>
{{--            <div class="btn-download-all">--}}
{{--                <div><i class="fa fa-cloud-download"></i> Tải trọn bộ</div>--}}
{{--            </div>--}}
        </div>
        <div class="audio__list">
            <div class="audio__item">
                <div>Mục lục Flashcard</div>
                <div class="audio__item-file"></div>
            </div>
            @if($stacks)
                <div class="container flex flex-col gap-3 flex-wrap mt-3 p-10 shadow-lg rounded-md bg-[#f7fff0]">
                    @foreach($stacks as $key => $value)
                        @include('frontend.books.table_content', ['child' => $value])
                    @endforeach
                </div>
            @endif
        </div>
    </div>
@stop

@section('footer-js')
<script type="text/javascript">
  function handleScroll(id) {
    document.getElementById(id).scrollIntoView({
      behavior: 'smooth'
    });
  }
</script>
<script>
    var acc = document.getElementsByClassName("fc-accordion");
    var i;

    for (i = 0; i < acc.length; i++) {
        acc[i].addEventListener("click", function() {
            /* Toggle between adding and removing the "active" class,
            to highlight the button that controls the panel */
            this.classList.toggle("active");

            /* Toggle between hiding and showing the active panel */
            var panel = this.nextElementSibling;
            if (panel.style.display === "block") {
                panel.style.display = "none";
            } else {
                panel.style.display = "block";
            }
        });
    }
</script>
@stop
