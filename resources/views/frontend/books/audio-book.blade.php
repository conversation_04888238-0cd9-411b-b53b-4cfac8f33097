@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON> sách tại dungmori @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('{{asset("assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
@stop
@section('header-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
@stop
@section('content')
    <div class="main main-books">
        <div class="slider-book"></div>
        <div class="book__navigation mt-0">
            <div class="book__navigation-items">
                <a class="book__navigation-item" href="{{url('sach#so-cap')}}">
                    <img src="{{asset("assets/img/book/icon-so-cap.svg")}}" />
                    <div>Sơ cấp</div>
                </a>
                <a class="book__navigation-item" href="{{url('sach#trung-cap')}}">
                    <img src="{{asset("assets/img/book/icon-trung-cap.svg")}}" />
                    <div>Trung cấp</div>
                </a>
{{--                <a class="book__navigation-item" href="{{url('sach#cao-cap')}}">--}}
{{--                    <img src="{{asset("assets/img/book/icon-cao-cap.svg")}}" />--}}
{{--                    <div>Cao cấp</div>--}}
{{--                </a>--}}
                <a class="book__navigation-item active">
                    <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                    <div>File nghe</div>
                </a>
                <a class="book__navigation-item" href="{{ route('frontend.book.flashcard_book') }}">
                  <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                  <div>Flashcard</div>
                </a>
            </div>
        </div>
        <div class="book__category" id="basic-book">
            <div class="book__title flex items-center justify-center">
                <img src="{{asset("assets/img/book/icon-title.svg")}}"/>
                <div>Sách Sơ cấp</div>
            </div>
            <div class="book__list">
                <div class="book__slider" id="book-slider-1">
                    @foreach($scBook as $book)
                        <div class="book__item slick-slide">
                            <div class="book__item-inner">
                                <div class="image w-80">
                                    <img
                                        @if ($book->cover_name == null || $book->cover_name == '')
                                        src="{{url('assets/img/icon_backend')}}/no_image.png"
                                        @else
                                        src="{{url('/cdn/book/default/'.$book->cover_name)}}"
                                        @endif
                                        alt="{{ $book->name }}"
                                    />
                                </div>
                                <div class="title">{{$book->name}}</div>
                                <div class="btn-book btn-book--green-100">
                                    <div><i class="fa fa-cloud-download"></i> Tải trọn bộ</div>
                                </div>
                                <div class="btn-book btn-book--green-0">
                                    <a href="{{route('frontend.book.audio', ['url' => $book->SEOurl])}}">Xem chi tiết</a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="book__list-navigation">
                    <div id="prv_sld_1" class="slide-btn"><img src="{{asset("assets/img/book/prev.svg")}}"/></div>
                    <div id="nxt_sld_1" class="slide-btn"><img src="{{asset("assets/img/book/next.svg")}}"/></div>
                </div>
            </div>
        </div>
        <div class="book__category" id="intermediate-book">
            <div class="book__title flex items-center justify-center">
                <img src="{{asset("assets/img/book/icon-title.svg")}}"/>
                <div>Sách Trung cấp</div>
            </div>
            <div class="book__list">
                <div class="book__slider" id="book-slider-2">
                    @foreach($tcBook as $book)
                        <div class="book__item slick-slide">
                            <div class="book__item-inner">
                                <div class="image w-80">
                                    <img
                                        @if ($book->cover_name == null || $book->cover_name == '')
                                        src="{{url('assets/img/icon_backend')}}/no_image.png"
                                        @else
                                        src="{{url('/cdn/book/default/'.$book->cover_name)}}"
                                        @endif
                                        alt="{{ $book->name }}"
                                    />
                                </div>
                                <div class="title">{{$book->name}}</div>
{{--                                <div class="btn-book btn-book--green-100">--}}
{{--                                    <div><i class="fa fa-cloud-download"></i> Tải trọn bộ</div>--}}
{{--                                </div>--}}
                                <div class="btn-book btn-book--green-0">
                                    <a href="{{route('frontend.book.audio', ['url' => $book->SEOurl])}}">Xem chi tiết</a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="book__list-navigation">
                    <div id="prv_sld_2" class="slide-btn"><img src="{{asset("assets/img/book/prev.svg")}}"/></div>
                    <div id="nxt_sld_2" class="slide-btn"><img src="{{asset("assets/img/book/next.svg")}}"/></div>
                </div>
            </div>
        </div>
{{--        <div class="book__category" id="advanced-book">--}}
{{--            <div class="book__title flex items-center justify-center">--}}
{{--                <img src="{{asset("assets/img/book/icon-title.svg")}}"/>--}}
{{--                <div>Sách Cao cấp</div>--}}
{{--            </div>--}}
{{--            <div class="book__list">--}}
{{--                <div class="book__slider" id="book-slider-3">--}}
{{--                    @foreach($ccBook as $book)--}}
{{--                        <div class="book__item slick-slide">--}}
{{--                            <div class="book__item-inner">--}}
{{--                                <div class="image w-80">--}}
{{--                                    <img--}}
{{--                                        @if ($book->cover_name == null || $book->cover_name == '')--}}
{{--                                        src="{{url('assets/img/icon_backend')}}/no_image.png"--}}
{{--                                        @else--}}
{{--                                        src="{{url('/cdn/book/default/'.$book->cover_name)}}"--}}
{{--                                        @endif--}}
{{--                                        alt="{{ $book->name }}"--}}
{{--                                    />--}}
{{--                                </div>--}}
{{--                                <div class="title">{{$book->name}}</div>--}}
{{--                                <div class="btn-book btn-book--green-100">--}}
{{--                                    <div><i class="fa fa-cloud-download"></i> Tải trọn bộ</div>--}}
{{--                                </div>--}}
{{--                                <div class="btn-book btn-book--green-0">--}}
{{--                                    <a href="{{route('frontend.book.audio', ['url' => $book->SEOurl])}}">Xem chi tiết</a>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    @endforeach--}}
{{--                </div>--}}
{{--                <div class="book__list-navigation">--}}
{{--                    <div id="prv_sld_3" class="slide-btn"><img src="{{asset("assets/img/book/prev.svg")}}"/></div>--}}
{{--                    <div id="nxt_sld_3" class="slide-btn"><img src="{{asset("assets/img/book/next.svg")}}"/></div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
    </div>
@stop

@section('footer-js')
<script type="text/javascript">
  function handleScroll(id) {
    document.getElementById(id).scrollIntoView({
      behavior: 'smooth'
    });
  }
</script>
<script type="text/javascript">
  $(document).ready(function(){
    var sliders = {
      1: {slider : '#book-slider-1', prev: '#prv_sld_1', next: '#nxt_sld_1'},
      2: {slider : '#book-slider-2', prev: '#prv_sld_2', next: '#nxt_sld_2'},
      3: {slider : '#book-slider-3', prev: '#prv_sld_3', next: '#nxt_sld_3'}
    };
    $.each(sliders, function() {
      var slider = $(this.slider);
      slider.slick({
        slidesToShow: 1,
        arrows: false,
        speed: 800,
        draggable: false,
        variableWidth: true,
        centerMode: true,
        responsive: [{
          breakpoint: 1160,
          settings: {
            variableWidth: false
          }
        }, {
          breakpoint: 768,
          settings: {
            variableWidth: false,
            centerPadding: "22.5px"
          }
        }]
      });
      $(this.prev).on("click", function(e) {
        e.preventDefault();
        slider.slick("slickNext")
      });
      $(this.next).on("click", function(e) {
        e.preventDefault();
        slider.slick("slickPrev")
      });
    });
  })
</script>
@stop
