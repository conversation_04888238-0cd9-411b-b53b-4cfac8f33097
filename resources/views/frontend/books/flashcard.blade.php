@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON> sách tại dungmori @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('{{asset("assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
@stop
@section('header-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
@stop
@section('content')
    <div class="main main-books">
        <div class="slider-book"></div>
        <div class="book__navigation mt-0">
            <div class="book__navigation-items">
{{--                <a class="book__navigation-item" href="{{url('sach/am-thanh#so-cap')}}">--}}
{{--                    <img src="{{asset("assets/img/book/icon-so-cap.svg")}}" />--}}
{{--                    <div>Sơ cấp</div>--}}
{{--                </a>--}}
                <a class="book__navigation-item" href="{{url('sach/am-thanh#trung-cap')}}">
                    <img src="{{asset("assets/img/book/icon-trung-cap.svg")}}" />
                    <div>Trung cấp</div>
                </a>
{{--                <a class="book__navigation-item" href="{{url('sach/am-thanh#cao-cap')}}">--}}
{{--                    <img src="{{asset("assets/img/book/icon-cao-cap.svg")}}" />--}}
{{--                    <div>Cao cấp</div>--}}
{{--                </a>--}}
                <a class="book__navigation-item" href="{{route('frontend.book.audio', ['url' => $url])}}">
                    <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                    <div>File nghe</div>
                </a>
                <a class="book__navigation-item active" href="{{route('frontend.book.flashcards', ['url' => $url])}}">
                    <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                    <div>Flashcard</div>
                </a>
            </div>
        </div>
        <div class="audio__header">
        </div>
        <div class="audio__list">
            <div class="audio__item">
                <div>Flashcard</div>
                <div class="audio__item-file"></div>
            </div>
            <div id="flashcard-wrapper" class="mx-auto w-full max-w-[630px]">
                @include('frontend.course.components.flashcards')
            </div>
        </div>
    </div>
@stop

@section('footer-js')
<script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
<script type="text/javascript">
  function handleScroll(id) {
    document.getElementById(id).scrollIntoView({
      behavior: 'smooth'
    });
  }
</script>
<script type="text/javascript">
    var lesson = {!! json_encode($lesson) !!};
    var meid = null;
    var myAvatar = null;
    @if(Auth::check())
        meid = '{{ Auth::user()->id }}';
    myAvatar = '{{ Auth::user()->avatar }}';
    @endif
</script>
<script type="text/javascript">
    var user = new Vue({
        el: "#flashcard-wrapper",
        data: function() {
            return {
                url: window.location.origin, //đường dẫn host
                tokenPlayMp3: 0,
                mp3: null,
                lesson: lesson,
                playingMp3: false,
                flashcards: [], // mảng flashcard
                cardStats: {}, // chỉ số card đã thuộc/chưa thuộc
                currentCard: 0, // index hiện tại của flashcard
                currentType: "all", // loại hiện tại của stack card (known, unknown, all)
                currentTempCard: 0, // id hiện tại của flashcard mà user học dở (number || "end")
                userSettings: {}, // setting của user sử dụng flashcard
                flashcardMp3: null,
                swipeLoading: false,
                focusFC: false,
                token: document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
                //các biến cmts của riêng flashcards
                showComment: false,
                meid: meid,
                avatar: myAvatar,
                thisFlashcardId: 0,
                listComments: [], //sanh sách các comments
                page: 1, //trang thứ mấy
                numPost: 5,
                showLoading: false, //trạng thái hiển thị button tải thêm
                theEnd: false, //thông báo hết danh sách
                showLoadingNewComment: false,
                likeLoading: false, // trạng thái của nút like (true: không được thao tác)
                prevFlashcard: {},
            }
        },
        mounted: function() {
            this.initFlashcards();
        },
        methods: {
            initFlashcards() {
                const vm = this;
                if (typeof userLoggedIn != "undefined" && userLoggedIn) this.showComment = true;

                this.userSettings = $.extend(
                    {},
                    typeof defaultUserSettings != "undefined" ? defaultUserSettings : null,
                    typeof userSettings != "undefined" ? userSettings : null
                );

                this.cardStats = typeof defaultCardStats != "undefined" ? defaultCardStats : null;

                var params = this.getQueryParameters(window.location.search);
                if (params.hasOwnProperty("focus_fc")) vm.focusFC = true;

                this.getCurrentState();
                this.fetchCards();

                $(".stats").css("opacity", 100);
                $(".empty-cards").css("opacity", 100);
            },
            playCDNAudio(audio) {
                const vm = this;

                // console.log("click play audio", audio);

                if (vm.flashcardMp3 == null)
                    vm.flashcardMp3 = new Audio("https://mp3-v2.dungmori.com/" + audio);
                else {
                    vm.flashcardMp3.pause();
                    vm.flashcardMp3.currentTime = 0;
                    vm.flashcardMp3 = new Audio("https://mp3-v2.dungmori.com/" + audio);
                }
                vm.flashcardMp3.play();
                vm.playingMp3 = true;
            },
            //play audio trong flashcards
            playAudio(audio) {
                // console.log("click play audio", audio);
                const vm = this;

                if (vm.flashcardMp3 == null)
                    vm.flashcardMp3 = new Audio(vm.url + "/cdn/audio/" + audio);
                else {
                    vm.flashcardMp3.pause();
                    vm.flashcardMp3.currentTime = 0;
                    vm.flashcardMp3 = new Audio(vm.url + "/cdn/audio/" + audio);
                }
                vm.flashcardMp3.play();
                vm.playingMp3 = true;
            },
            // pause audio trong flashcards
            pauseAudio() {
                const vm = this;
                vm.flashcardMp3.pause();
                vm.flashcardMp3.currentTime = 0;
                vm.playingMp3 = false;
            },
            // Gửi api thay đổi setting
            onChangeCheckboxSetting(e) {
                const vm = this;

                var data = {};
                data[e.target.name] = e.target.checked ? 1 : 0;
                $.post(
                    window.location.origin + "/api/flashcards/option",
                    data,
                    function (response, status) {
                        $.extend({}, vm.userSettings, data);
                    }
                );
            },
            // Call api sửa user settings
            onChangeSelectSetting(e) {
                const vm = this;

                var data = {};
                data[e.target.name] = e.target.value;
                $.post(
                    window.location.origin + "/api/flashcards/option",
                    data,
                    function (response, status) {
                        $.extend({}, vm.userSettings, data);
                    }
                );
            },
            // Highlight từ vựng trong ví dụ
            highlightVocab(cards) {
                const vm = this;
                cards.map(function (card, index) {
                    var jp = card.value.jp;

                    var replace = new RegExp(jp, "gi");
                    // Regex chuỗi bắt đầu và kết thúc bằng dấu * đồng thời trích ra chuỗi nằm giữa 2 dấu *
                    var manualReplace = new RegExp(/\*+(.+?)\*+/, "gi");

                    var newEx = card.value.ex.replace(
                        replace,
                        '<span class="highlight">' + jp + "</span>"
                    );

                    // Lấy object chứa chuỗi được trích ra (extract capturing group)
                    var manualHighlightWord = manualReplace.exec(newEx);

                    // Nếu tồn tại object thì thay thế chuỗi bằng dom html
                    if (manualHighlightWord) {
                        newEx = newEx.replace(
                            manualHighlightWord[0],
                            '<span class="highlight">' + manualHighlightWord[1] + "</span>"
                        );
                    }
                    while (manualHighlightWord) {
                        manualHighlightWord = manualReplace.exec(newEx);
                        if (manualHighlightWord)
                            newEx = newEx.replace(
                                manualHighlightWord[0],
                                '<span class="highlight">' + manualHighlightWord[1] + "</span>"
                            );
                    }
                    newEx = vm.mp3Ex(newEx);
                    card.value.ex = newEx;
                    return card;
                });
            },
            mp3Ex(ex) {
                // Regex chuỗi bắt đầu và kết thúc bằng dấu * đồng thời trích ra chuỗi nằm giữa 2 dấu *
                var manualReplace = new RegExp(/\{\!(.+?)\!\}/, "gi");

                var newEx = ex;
                // Lấy object chứa chuỗi đầu tiên được trích ra (extract capturing group)
                var manualHighlightWord = manualReplace.exec(newEx);
                if (manualHighlightWord) {
                    var mp3Name = _.trim(manualHighlightWord[1]);
                    newEx = newEx.replace(
                        manualHighlightWord[0],
                        `<span class='text-info noFlip' onclick='user.playCDNAudio("${mp3Name}")'>` +
                        "<i class='fa fa-volume-up noFlip'></i>" +
                        "</span>"
                    );
                }
                // Kiểm tra xem còn chuỗi nào thoả mãn nữa không, nếu có thì chạy vòng lặp
                while (manualHighlightWord) {
                    manualHighlightWord = manualReplace.exec(newEx);
                    if (manualHighlightWord) {
                        var mp3Name = _.trim(manualHighlightWord[1]);
                        newEx = newEx.replace(
                            manualHighlightWord[0],
                            `<span class="text-info" onclick='user.playCDNAudio("${mp3Name}")'>` +
                            "<i class='fa fa-volume-up noFlip'></i>" +
                            "</span>"
                        );
                    }
                }

                return newEx;
            },
            // lấy danh sách flashcards
            fetchCards(type, first) {
                const vm = this;

                var data = {
                    lessonId: vm.lesson.id,
                    type: type ? type : vm.currentType,
                };
                vm.cardLoading = true;
                $.post(
                    window.location.origin + "/api/flashcards",
                    data,
                    function (response, status) {
                        if (first) {
                            vm.firstTime = false;
                        }
                        var params = vm.getQueryParameters(window.location.search);

                        vm.cardStats = response.cardStats;

                        vm.flashcards = response.flashcards;
                        if (vm.userSettings.isShuffle) {
                            vm.flashcards = _.shuffle(response.flashcards);
                        }
                        if (!vm.focusFC) {
                            vm.flashcards = vm.loadRestCards(type, vm.flashcards);
                        }

                        vm.flashcards = vm.flashcards.map(function (card, index) {
                            card.value = JSON.parse(card.value);
                            card.value.jp = card.value.jp.replace(/\n/gi, "<br/>");
                            card.value.vi = card.value.vi.replace(/\n/gi, "<br/>");
                            card.value.ex = card.value.ex.replace(/\n/gi, "<br/>");
                            return card;
                        });

                        // console.log(vm.flashcards);
                        if (params.hasOwnProperty("focus_fc"))
                            vm.flashcards = vm.flashcards.filter(function (card) {
                                return card.id == params.focus_fc;
                            });

                        vm.highlightVocab(vm.flashcards);
                        vm.cardStats = response.cardStats;

                        setTimeout(function () {
                            if (typeof stackedCards != "undefined") stackedCards();
                        }, 100);

                        vm.currentCard = 0;

                        if (type) {
                            vm.currentType = type;
                            vm.currentTempCard = 0;
                        }

                        if (vm.userSettings.autoPlay && vm.currentTempCard != "end") {
                            vm.flashcards.forEach(function (card, index) {
                                if (!vm.isIos) {
                                    // index === vm.currentCard && setTimeout(function(){ vm.playAudio(card.value.audio) }, 300);
                                }
                            });
                        }
                        if (vm.showComment) {
                            if (params.hasOwnProperty("focus_fc") || vm.currentTempCard != "end") {
                                //gán id để call load comments
                                vm.thisFlashcardId = vm.flashcards[0].id;
                                vm.fetchlistComments();
                                // console.log("id thawngf dau tien", vm.thisFlashcardId);
                            }
                        }
                    }
                );
            },

            // event chuyển thẻ
            swipeCard(status) {
                const vm = this;
                var data = {
                    cardId: vm.flashcards[vm.currentCard].id,
                    status: status,
                };

                if (typeof userLoggedIn != "undefined" && userLoggedIn) vm.callApiSwipe(data);
                else vm.guestSwipe(data);
                this.prevFlashcard.prev = { ...this.prevFlashcard };
                this.prevFlashcard.status = status;
            },
            guestSwipe(data) {
                const vm = this;
                vm.saveCurrentFlashcard(data.status);
                vm.currentCard++;

                if (vm.userSettings.autoPlay && vm.currentTempCard != "end") {
                    vm.flashcards.forEach(function (card, index) {
                        if (!vm.isIos) {
                            // index === vm.currentCard && setTimeout(function(){ vm.playAudio(card.value.audio) }, 300);
                        }
                    });
                } else {
                    vm.playingMp3 && vm.pauseAudio();
                }
            },
            callApiSwipe(data) {
                const vm = this;
                // Call api cập nhật số thẻ thuộc/chưa thuộc
                vm.swipeLoading = true;
                setTimeout(function () {
                    $.post(
                        window.location.origin + "/api/flashcards/learn",
                        data,
                        function (response, status) {
                            response.changes.forEach(function (change, index) {
                                vm.cardStats[change.stat] =
                                    vm.cardStats[change.stat] + change.value;
                            });

                            vm.saveCurrentFlashcard(data.status);

                            vm.currentCard++;

                            if (vm.currentTempCard == "end") {
                                vm.saveExamProgress();
                            }
                            if (vm.userSettings.autoPlay) {
                                vm.flashcards.forEach(function (card, index) {
                                    if (!vm.isIos) {
                                        // index === vm.currentCard && setTimeout(function(){ vm.playAudio(card.value.audio) }, 300);
                                    }
                                });
                            } else {
                                vm.playingMp3 && vm.pauseAudio();
                            }
                            vm.swipeLoading = false;
                            if (vm.showComment) {
                                if (vm.currentTempCard != "end") {
                                    //gán id để call load comments
                                    vm.thisFlashcardId = vm.flashcards[vm.currentCard].id;
                                    vm.fetchlistComments();
                                }
                            }
                        }
                    );
                }, 500);
            },
            undo() {
                if (this.currentCard > 0) {
                    if (this.prevFlashcard.status) {
                        this.cardStats.current_total++;
                        if (this.prevFlashcard.status == "known") {
                            this.cardStats.current_known--;
                        } else {
                            this.cardStats.current_unknown--;
                        }
                    }
                    this.prevFlashcard = this.prevFlashcard.prev;
                    this.currentCard--;
                    const vm = this;
                    vm.thisFlashcardId = vm.flashcards[vm.currentCard].id;
                    vm.fetchlistComments();
                    var currentMemo = {
                        lesson: vm.lesson.id,
                        learned: [
                            {
                                id: vm.flashcards[vm.currentCard].id,
                                status: this.prevFlashcard.status,
                            },
                        ],
                        current: vm.flashcards[vm.currentCard].id,
                        type: vm.currentType,
                    };
                    // Nếu không có cookie fc_memo thì set bằng giá trị currentMemo luôn
                    // Nếu không thì check xem bài này có trong cookie không, nếu không có thì nối mảng, nếu có thì cập nhật lại object trong mảng
                    var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
                    var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
                    var fc_memo;
                    if (!lesson) {
                        fc_memo = _.concat(cookie_memo, currentMemo);
                        localStorage.setItem("fc_memo", JSON.stringify(fc_memo));
                    } else {
                        fc_memo = cookie_memo.map(function (memo) {
                            if (memo.lesson == vm.lesson.id) {
                                memo.learned =
                                    vm.currentCard == vm.flashcards.length - 1
                                        ? []
                                        : memo.type == vm.currentType
                                            ? memo.learned.filter(
                                                (t) => t.id != vm.flashcards[vm.currentCard].id
                                            )
                                            : currentMemo.learned;
                                memo.current = currentMemo.current;
                                memo.type = currentMemo.type;
                            }
                            return memo;
                        });
                        localStorage.setItem("fc_memo", JSON.stringify(fc_memo));

                        // Cập nhật lại bộ đếm
                        vm.currentTempCard = currentMemo.current;
                    }
                }
            },
            // Lưu flashcard đang học hiện tại, các flashcard đã học của bài học, type đang học dở
            saveCurrentFlashcard(status) {
                const vm = this;
                // @param lesson: id bài học
                // @param learned: mảng id các flashcard đã thuộc trong bài học
                // @param current: id flashcard lưu vết cuối cùng của bài học
                var currentMemo = {
                    lesson: vm.lesson.id,
                    learned: [
                        {
                            id: vm.flashcards[vm.currentCard].id,
                            status: status,
                        },
                    ],
                    current:
                        vm.currentCard != vm.flashcards.length - 1
                            ? vm.flashcards[vm.currentCard + 1].id
                            : "end",
                    type: vm.currentType,
                };
                // Nếu không có cookie fc_memo thì set bằng giá trị currentMemo luôn
                // Nếu không thì check xem bài này có trong cookie không, nếu không có thì nối mảng, nếu có thì cập nhật lại object trong mảng
                if (!localStorage.getItem("fc_memo")) {
                    localStorage.setItem("fc_memo", JSON.stringify([currentMemo]));
                } else {
                    var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
                    var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
                    var fc_memo;
                    if (!lesson) {
                        fc_memo = _.concat(cookie_memo, currentMemo);
                        localStorage.setItem("fc_memo", JSON.stringify(fc_memo));
                    } else {
                        fc_memo = cookie_memo.map(function (memo) {
                            if (memo.lesson == vm.lesson.id) {
                                memo.learned =
                                    vm.currentCard == vm.flashcards.length - 1
                                        ? []
                                        : memo.type == vm.currentType
                                            ? _.union([], memo.learned, currentMemo.learned)
                                            : currentMemo.learned;
                                memo.current = currentMemo.current;
                                memo.type = currentMemo.type;
                            }
                            return memo;
                        });
                        localStorage.setItem("fc_memo", JSON.stringify(fc_memo));

                        // cập nhật lại bộ đếm
                        vm.currentTempCard = currentMemo.current;
                    }
                }
                vm.cardStats["current_" + status]++;
            },
            // Kiểm tra flashcard có đang học dở không, nếu đang học dở thì load những card còn lại
            // Đang bị duplicate code nhưng nếu viết ra thành function riêng thì không chạy được
            loadRestCards(type, cards) {
                const vm = this;
                if (type) {
                    vm.cardStats.current_total = cards.length;
                    vm.cardStats.current_known = 0;
                    vm.cardStats.current_unknown = 0;
                    return cards;
                }
                if (!localStorage.getItem("fc_memo")) {
                    vm.cardStats.current_total = cards.length;
                    vm.cardStats.current_known = 0;
                    vm.cardStats.current_unknown = 0;
                    return cards;
                } else {
                    var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
                    var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
                    if (!lesson) {
                        vm.cardStats.current_total = cards.length;
                        vm.cardStats.current_known = 0;
                        vm.cardStats.current_unknown = 0;
                        return cards;
                    } else {
                        var fc_memo = cards.filter(function (card) {
                            return !_.some(lesson.learned, ["id", card.id]);
                        });
                        fc_memo.sort(function (x, y) {
                            return x.id == lesson.current ? -1 : y.id == lesson.current ? 1 : 0;
                        });
                        vm.cardStats.current_total = fc_memo.length;

                        // Đếm số thẻ thuộc và chưa thuộc tạm thời (hiển thị trong giao diện stack thẻ)
                        vm.cardStats.current_known =
                            _.countBy(lesson.learned, ["status", "known"]).true || 0;
                        vm.cardStats.current_unknown =
                            _.countBy(lesson.learned, ["status", "unknown"]).true || 0;
                        return fc_memo;
                    }
                }
            },
            // Lấy ra trạng thái đang học dở trong cookie, nếu không thì mặc định tất cả thẻ
            getCurrentState() {
                const vm = this;
                if (!localStorage.getItem("fc_memo")) {
                    vm.currentType = "all";
                } else {
                    console.log(vm.lesson)
                    var cookie_memo = JSON.parse(localStorage.getItem("fc_memo"));
                    var lesson = _.find(cookie_memo, ["lesson", vm.lesson.id]);
                    if (!lesson) {
                        vm.currentType = "all";
                    } else {
                        vm.currentType = lesson.type;
                        vm.currentTempCard = lesson.current;
                    }
                }
            },
            //////////comment flashcards/////
            //in ra thông tin email dạng nửa kín nửa hở
            printPrivateEmail(email) {
                // console.log("Biến permission", enableFIV);

                if (email != "<EMAIL>" && email != "<EMAIL>") {
                    //nếu biến cho phép hiển thị = true
                    if (enableFIV && enableFIV == true) return email;
                    else return "****" + email.slice(4);
                } else return "<EMAIL>";
            },

            //in ra thông tin mobile dạng nửa kín nửa hở
            printPrivatePhone(phone) {
                if (phone != "0969.86.84.85") {
                    //nếu biến cho phép hiển thị = true
                    if (enableFIV && enableFIV == true) return phone;
                    else return "*******" + phone.slice(-5);
                } else return "0969.86.84.85";
            },

            //in ra định dạng ngày giờ đẹp
            prettyDate(t) {
                var d = new Date(t);
                return (
                    d.toLocaleDateString("en-GB", {
                        timeZone: "Asia/Ho_Chi_Minh",
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                    }) +
                    " " +
                    t.substring(11, 16)
                );
            },

            //in ra thông tin có dấu cách
            printInfo(info) {
                var result = _.escape(info);

                result = result.replace("<", "&#60;");
                result = result.replace(">", "&#62;");

                //xử lý xuống dòng
                result = info.replace(new RegExp("\r?\n", "g"), "<br />");

                var re =
                    /(\(.*?)?\b((?:https?|ftp|file):\/\/[-a-z0-9+&@#\/%?=~_()|!:,.;]*[-a-z0-9+&@#\/%=~_()|])/gi;
                return result.replace(re, function (match, lParens, url) {
                    var rParens = "";
                    lParens = lParens || "";

                    // Try to strip the same number of right parens from url
                    // as there are left parens.  Here, lParenCounter must be
                    // a RegExp object.  You cannot use a literal
                    //     while (/\(/g.exec(lParens)) { ... }
                    // because an object is needed to store the lastIndex state.
                    var lParenCounter = /\(/g;
                    while (lParenCounter.exec(lParens)) {
                        var m;
                        // We want m[1] to be greedy, unless a period precedes the
                        // right parenthesis.  These tests cannot be simplified as
                        //     /(.*)(\.?\).*)/.exec(url)
                        // because if (.*) is greedy then \.? never get a chance.
                        if ((m = /(.*)(\.\).*)/.exec(url) || /(.*)(\).*)/.exec(url))) {
                            url = m[1];
                            rParens = m[2] + rParens;
                        }
                    }
                    return (
                        lParens +
                        "<a href='" +
                        url +
                        "' target='_blank'>" +
                        url +
                        "</a>" +
                        rParens
                    );
                });
            },

            //tải về các comments cho lần tải đầu tiên
            fetchlistComments() {
                const vm = this;

                //focus vào comment được đánh dấu
                vm.ref = "notice";

                const data = {
                    id: vm.thisFlashcardId,
                    name: "flashcard",
                    numpost: vm.numPost,
                    ref: vm.ref,
                };

                $.post(
                    window.location.origin + "/api/flashcard/comments-load-first",
                    data,
                    function (response, status) {
                        //console.log(response);

                        vm.listComments = response.comments;
                        //console.log(vm.listComments);

                        //nếu đã hết danh sách
                        if (response.comments.length < vm.numPost) vm.theEnd = true;
                        // Nếu comment có trong danh sách like của user thì liked = 1
                        vm.listComments.map(function (comment) {
                            if (response.listLike.includes(comment.id)) comment.liked = 1;
                            return comment;
                        });
                        //ẩn biểu tượng loading
                        vm.showLoading = false;
                    }
                );
            },

            //tải các phản hồi
            fetchMoreComments() {
                const vm = this;

                //hiện biểu tượng loading
                vm.showLoading = true;

                setTimeout(function () {
                    const data = {
                        id: vm.thisFlashcardId,
                        name: "flashcard",
                        numpost: vm.numPost,
                        page: vm.page++,
                    };
                    //console.log(data);
                    $.post(
                        window.location.origin + "/api/flashcard/comments-load-more",
                        data,
                        function (response, status) {
                            //nối thêm mảng tải thêm
                            vm.listComments = vm.listComments.concat(response.comments);

                            //nếu đã hết danh sách
                            if (response.comments.length < vm.numPost) vm.theEnd = true;
                            vm.listComments.map(function (comment) {
                                if (response.listLike.includes(comment.id)) comment.liked = 1;
                                return comment;
                            });
                            //ẩn biểu tượng loading
                            vm.showLoading = false;
                            console.log(vm.showLoading);
                        }
                    );
                }, 500);

                //console.log('tải thêm các bình luận');
            },

            //đăng bình luận mới
            postNewComment(tbid) {
                const vm = this;

                // bỏ qua comment rỗng
                if (
                    $("#comment-content").val() == null ||
                    $("#comment-content").val() == undefined ||
                    $("#comment-content").val().trim() == ""
                ) {
                    alert("Vui lòng nhập nội dung");
                    return;
                }

                var form_data = new FormData();
                form_data.append("tbid", vm.thisFlashcardId);
                form_data.append("tbname", "flashcard");
                form_data.append("content", $("#comment-content").val());

                vm.showLoadingNewComment = true;

                setTimeout(function () {
                    $.ajax({
                        url: window.location.origin + "/api/comments/add-new-comment",
                        type: "POST",
                        data: form_data,
                        contentType: false,
                        cache: false,
                        processData: false,
                        success(response) {
                            console.log(response);
                            if (response == "imagesize")
                                alert("ảnh vượt quá dung lượng cho phép");
                            else if (response == "type") alert("định dạng ảnh không cho phép");
                            else {
                                var newComment = response;
                                response.liked = 0;
                                response.count_like = 0;

                                vm.listComments.unshift(newComment);
                                $("#comment-content").val("");
                                $("#comment-content").css("height", "70px");
                            }
                            vm.showLoadingNewComment = false;
                        },
                    });
                }, 500);
            },

            // ghim comment len dau
            pinComment(comment) {
                const vm = this;
                var data = {
                    id: comment.id,
                };
                $.post(
                    window.location.origin + "/backend/binhluan/api/pin",
                    data,
                    function (res) {
                        if (res.code === 200) {
                            vm.listComments.forEach(function (comment) {
                                if (comment.id === res.data.id) {
                                    comment.pin = res.data.pin;
                                }
                            });
                        }
                    }
                );
            },

            //xóa comment theo id
            delComment(id) {
                const vm = this;
                setTimeout(function () {
                    $.post(
                        window.location.origin + "/api/comments/delete-comment",
                        { id: id },
                        function (response, status) {
                            if (response == "success") {
                                vm.listComments = vm.listComments.filter(function (comment) {
                                    return comment.id != id;
                                });
                            } else {
                                alert("thao tác không hợp lệ");
                            }
                        }
                    );
                }, 500);
            },
            // like comment
            likeComment(id) {
                const vm = this
                vm.likeLoading = true;
                setTimeout(function () {
                    $.post(
                        window.location.origin + "/api/comments/like-comment",
                        { id: id },
                        function (response, status) {
                            if (response.code == 200) {
                                vm.listComments.map(function (comment) {
                                    if (comment.id == id) {
                                        comment.count_like = response.data.count_like;
                                        comment.liked = response.data.liked;
                                    }
                                    return comment;
                                });
                            } else if (response.code == 404) {
                                alert("Bình luận không tồn tại");
                            } else {
                                alert("Lỗi hệ thống");
                            }
                            vm.likeLoading = false;
                        }
                    );
                }, 500);
            },
            printFirstComment(comment) {
                return _.truncate(comment, { length: 100 });
            },
            getQueryParameters(str) {
                return (str || document.location.search)
                    .replace(/(^\?)/, "")
                    .split("&")
                    .map(
                        function (n) {
                            return (n = n.split("=")), (this[n[0]] = n[1]), this;
                        }.bind({})
                    )[0];
            },
            scrollToComment() {
                $("html, body").animate(
                    {
                        scrollTop: $("#list-comments").offset().top,
                    },
                    600
                );
            },
        },
    })
</script>
@stop
