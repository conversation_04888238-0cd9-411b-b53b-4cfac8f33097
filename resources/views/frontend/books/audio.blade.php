@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON><PERSON> sách tại dungmori @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('{{asset("assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
@stop
@section('header-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
@stop
@section('content')
    <div class="main main-books">
        <div class="slider-book"></div>
        <div class="book__navigation mt-0">
            <div class="book__navigation-items">
{{--                <a class="book__navigation-item" href="{{url('sach/am-thanh#so-cap')}}">--}}
{{--                    <img src="{{asset("assets/img/book/icon-so-cap.svg")}}" />--}}
{{--                    <div>Sơ cấp</div>--}}
{{--                </a>--}}
                <a class="book__navigation-item" href="{{url('sach/am-thanh#trung-cap')}}">
                    <img src="{{asset("assets/img/book/icon-trung-cap.svg")}}" />
                    <div>Trung cấp</div>
                </a>
{{--                <a class="book__navigation-item" href="{{url('sach/am-thanh#cao-cap')}}">--}}
{{--                    <img src="{{asset("assets/img/book/icon-cao-cap.svg")}}" />--}}
{{--                    <div>Cao cấp</div>--}}
{{--                </a>--}}
                <div class="book__navigation-item active">
                    <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                    <div>File nghe</div>
                </div>
                <a class="book__navigation-item active" href="{{route('frontend.book.flashcards', ['url' => $url])}}">
                    <img src="{{asset("assets/img/book/icon-nghe.svg")}}" />
                    <div>Flashcard</div>
                </a>
            </div>
        </div>
        <div class="audio__header">
            <div class="book__title flex items-center justify-center">
                <img src="{{asset("assets/img/book/icon-book.svg")}}"/>
                <div>{{$book->name}}</div>
            </div>
{{--            <div class="btn-download-all">--}}
{{--                <div><i class="fa fa-cloud-download"></i> Tải trọn bộ</div>--}}
{{--            </div>--}}
        </div>
        <div class="audio__list">
            <div class="audio__item">
                <div>File</div>
                <div class="audio__item-file"></div>
                <div></div>
            </div>
            @if($book->audios)
                @foreach(array_reverse(json_decode($book->audios)) as $audio)
                    <div class="audio__item">
                        <div class="audio__item-filename">{{$audio->display_name}}</div>
                        <div class="audio__item-file">
                            <audio src="{{url('cdn/book_audio/' . $audio->file)}}" controls controlsList="nodownload"></audio>
                        </div>
                        <div class="audio__item-action">
                            <a href="{{url('cdn/book_audio/' . $audio->file)}}" class="btn-download-one" download>Tải xuống</a>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
    </div>
@stop

@section('footer-js')
<script type="text/javascript">
  function handleScroll(id) {
    document.getElementById(id).scrollIntoView({
      behavior: 'smooth'
    });
  }
</script>
@stop
