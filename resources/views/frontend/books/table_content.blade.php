@if ($child->lesson_id)
  <a href="{{ route('frontend.book.flashcard', ['url' => request()->url, 'id' => $child->id]) }}" class="block p-3 text-gray-800 font-quicksand font-semibold">
    {{ $child->name }}
  </a>
@elseif ($child->parent_id)
  <div class="fc-accordion bg-[#96D962] w-full shadow-lg py-3 px-4 text-md text-white font-quicksand font-semibold border-t cursor-pointer">{{ $child->name }}</div>
  @if (count($child->children))
    <div class="fc-panel">
    @foreach($child->children as $grandchild)
      @include('frontend.books.table_content', ['child' => $grandchild])
    @endforeach
    </div>
  @endif
@else
  <div class="fc-accordion bg-[#96D962] w-full shadow-lg py-3 px-4 text-md text-white font-quicksand font-semibold border-t cursor-pointer">{{ $child->name }}</div>
  @if (count($child->children))
    <div class="fc-panel">
    @foreach($child->children as $grandchild)
      @include('frontend.books.table_content', ['child' => $grandchild])
    @endforeach
    </div>
  @endif
@endif

