@extends('frontend._layouts.default')

@section('title') Dungmori - Trang tuyển dụng @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
    <link rel="stylesheet" href="{{asset('assets/css/recruitment.css')}}">
@stop

@section('content')
  <div class="main text-lg pb-20" style="font-family: Quicksand">
    <style>
      .navigation-button.active {
        background-color: #FECC31;
      }
      .slick-dots {
        bottom: -50px;
      }
      #slide_slick .slick-dots li.slick-active button:before {
        color: #96d962;
      }
      #slide_slick .slick-dots li button:before {
        font-size: 12px;
      }
    </style>
    <div class="flex sm:h-[850px] sm:p-0 px-3.75 sm:flex-row flex-col h-auto sm:bg-top bg-left bg-no-repeat bg-cover" style="background-image: url({{ asset('assets/img/recruitment/recruitment_bg.png') }})">
      <div class="flex-1"></div>
      <div class="flex-1 sm:mt-45 mt-60 flex flex-col sm:items-start items-center">
        <img src="{{ asset('assets/img/recruitment/dmr.png') }}">
        <div class="mt-4 text-2xl font-bold">Giáo dục thay đổi cuộc sống</div>
        <div class="mt-4">
          Mở rộng tầm nhìn của chúng ta, cùng tạo ra những thay đổi,<br>
          và phấn đấu hướng tới những mục tiêu chung.
        </div>
        <a href="https://m.me/108011780810530" target="_blank">
          <div class="mt-4 text-3xl inline-block bg-white hover:bg-amber-100 rounded-xl border border-black px-10 border-2 text-black py-3 font-semibold cursor-pointer navigation-button">
            ỨNG TUYỂN NGAY
          </div>
        </a>
      </div>
    </div>

    <div class="mt-25 sm:w-[1100px] w-auto mx-auto sm:-mt-[210px] mt-10 relative">
      <img class="absolute top-70 -left-60" src="{{ asset('assets/img/recruitment/trangtri1.png') }}">
      <img class="absolute bottom-10 -right-[230px]" src="{{ asset('assets/img/recruitment/trangtri2.png') }}">
      <div class="sm:p-5 p-3.75 bg-white z-10 relative border rounded-2xl">
        <div class="flex items-center">
          <img class="sm:h-[129px] sm:w-auto w-[300px] h-auto" src="{{ asset('assets/img/recruitment/findjob.png') }}">
          <img class="h-[150px] sm:w-auto w-0" src="{{ asset('assets/img/recruitment/seejob.png') }}">
        </div>
        <div class="mt-6">
          <table class="table">
            <tbody>
              @foreach ($posts as $key => $post)
                <tr>
                  <td class="p-4">
                    <a href="{{ $post->link }}" class="flex items-center cursor-pointer text-black font-semibold underline" target="_blank">
                      {{ $post->title }}
                    </a>
                  </td>
                  <td class="p-4">
                    <svg aria-hidden="true" focusable="false" data-prefix="fal" data-icon="dollar-sign" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" class="w-3 svg-inline--fa fa-dollar-sign fa-w-8 fa-2x"><path fill="currentColor" d="M191.9 259.3L73.7 222.2C49.2 214.5 32 189 32 160.3 32 124.8 57.6 96 89 96h73.8c22.2 0 43.3 8.6 60.1 24.5 3.1 2.9 7.8 3.2 11 .3l11.9-10.8c3.4-3.1 3.6-8.4.4-11.6-22.8-22-52.7-34.5-83.3-34.5H144V8c0-4.4-3.6-8-8-8h-16c-4.4 0-8 3.6-8 8v56H89c-49.1 0-89 43.2-89 96.3 0 42.6 26.4 80.6 64.1 92.4l118.2 37.1c24.6 7.7 41.7 33.2 41.7 61.9 0 35.4-25.6 64.3-57 64.3H93.2c-22.2 0-43.3-8.6-60.1-24.5-3.1-2.9-7.8-3.2-11-.3L10.3 402c-3.3 3-3.6 8.4-.3 11.5 22.8 22 52.7 34.5 83.3 34.5H112v56c0 4.4 3.6 8 8 8h16c4.4 0 8-3.6 8-8v-56h23c49.1 0 89-43.2 89-96.3 0-42.5-26.4-80.5-64.1-92.4z" class=""></path></svg>&nbsp;{{ $post->salary }}
                  </td>
                  <td class="p-4">
                    <svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="map-marker-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" class="w-[15px] svg-inline--fa fa-map-marker-alt fa-w-12 fa-2x"><path fill="currentColor" d="M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z" class=""></path></svg>&nbsp;{{ $post->place }}
                  </td>
                </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        {{-- <div class="mt-5 flex justify-center">
          <a class="underline">Xem danh sách trên Facebook</a>
        </div> --}}
      </div>
    </div>

    <div class="mt-25 sm:p-20 py-20 px-3.75 text-center bg-cover bg-top bg-no-repeat" style="
      background-image: url({{ asset('assets/img/recruitment/bg1.png') }})
    ">
      <div class="sm:text-5xl text-3xl font-bold">VỀ DŨNG MORI</div>
      <div class="flex justify-center"><div class="h-[2px] bg-black mt-5 w-35"></div></div>
      <div class="mt-10 sm:w-[950px] w-auto mx-auto">Công ty cổ phần Dũng Mori đi theo định hướng phát triển trở thành một cơ quan nghiên cứu chuyên sâu và phát triển chuyên nghiệp các sản phẩm giáo dục phục vụ cho đối tượng là người Việt Nam học tiếng Nhật ở trong và ngoài lãnh thổ Việt Nam.<br><br>Với định hướng đó, CTCP Dũng Mori đã trở thành đơn vị tiên phong trong việc xây dựng hệ thống đào tạo tiếng Nhật theo phương pháp Blended Learning - Kết hợp giữa học trực tiếp và học online tại Việt Nam.</div>
      <div class="flex justify-center sm:mt-20 mt-6 font-bold sm:flex-row flex-col">
        <div class="relative sm:block flex flex-col items-center">
          <div class="flex items-center text-white bg-black rounded-full py-3 justify-center w-[380px] sm:absolute relative sm:-right-10 right-0">
            <div class="text-5xl">99%</div>
            <div class="ml-3 text-left">HỌC VIÊN HÀI LÒNG<br>VỚI KHÓA HỌC</div>
          </div>
          <div class="sm:mt-[170px] mt-[5px] flex items-center bg-white border border-2 border-black rounded-full py-3 justify-center w-[380px]">
            <div class="text-5xl">70%</div>
            <div class="ml-3 text-left">HỌC VIÊN<br>ĐỖ JLPT</div>
          </div>
        </div>
        <div class="sm:w-auto w-0">
          <img class="flex-1 m-3 rounded-2xl relative z-10" src="{{ asset('assets/img/recruitment/morisuccess.png') }}">
        </div>
        <div class="sm:block flex flex-col items-center">
          <div class="flex items-center bg-white border border-2 border-black rounded-full py-3 sm:-ml-10 m-[0px] justify-center w-[380px]">
            <div class="text-5xl">{{ number_format($userCount) }}</div>
            <div class="ml-3 text-left">HỌC VIÊN<br>THEO HỌC</div>
          </div>
          <div class="sm:mt-20 mt-[5px] flex items-center text-white bg-black rounded-full py-2 justify-center w-[380px]">
            <div class="text-6xl">#1</div>
            <div class="ml-3 text-left">DÀNH CHO NGƯỜI<br>HỌC TIẾNG NHẬT</div>
          </div>
        </div>
        <div class="sm:w-0 w-auto mt-5">
          <img class="flex-1 m-3 rounded-2xl relative z-10" src="{{ asset('assets/img/recruitment/morisuccess.png') }}">
        </div>
      </div>
    </div>

    <div class="text-center sm:p-20 px-3.75 py-10">
      <div class="inline-block relative">
        <div class="sm:text-4xl text-2xl font-bold">CHÚNG TÔI LUÔN TRÂN TRỌNG TỪNG CÁ NHÂN</div>
        <div class="flex justify-center"><div class="h-[2px] bg-black mt-5 w-35"></div></div>
        <img class="w-[280px] absolute -top-[65px] -right-25" src="{{ asset('assets/img/recruitment/bookblur.png') }}">
      </div>
      <div class="mt-10 sm:w-[1000px] w-auto mx-auto">
        Dungmori luôn quan tâm và đề cao giá trị của từng nhân viên trong tập thể. Chúng tôi luôn hướng tới một môi trường có văn hóa làm việc lành mạnh, nơi mỗi một tài năng đều có thể say mê làm việc và cùng nhau thành công.
      </div>
      <div class="mt-15" id="slide_slick">
        <img
          class="w-[280px] h-[250px] max-h-[250px] object-cover border-solid border-8 border-red-200 mx-3"
          src="{{ asset('assets/img/recruitment/team1.jpg') }}"
          style="min-height: 250px !important"
          >
        <img
          class="w-[280px] h-[250px] max-h-[250px] object-cover border-solid border-8 border-indigo-200 mx-3"
          src="{{ asset('assets/img/recruitment/team2.jpg') }}"
          style="min-height: 250px !important"
        >
        <img
          class="w-[280px] h-[250px] max-h-[250px] object-cover border-solid border-8 border-purple-200 mx-3"
          src="{{ asset('assets/img/recruitment/team3.jpg') }}"
          style="min-height: 250px !important"
        >
        <img
          class="w-[280px] h-[250px] max-h-[250px] object-cover border-solid border-8 border-yellow-200 mx-3"
          src="{{ asset('assets/img/recruitment/team4.jpg') }}"
          style="min-height: 250px !important"
        >
        <img
          class="w-[280px] h-[250px] max-h-[250px] object-cover border-solid border-8 border-fuchsia-200 mx-3"
          src="{{ asset('assets/img/recruitment/team5.jpg') }}"
          style="min-height: 250px !important"
        >
        <img
          class="w-[280px] h-[250px] max-h-[250px] object-cover border-solid border-8 border-green-200 mx-3"
          src="{{ asset('assets/img/recruitment/team6.jpg') }}"
          style="min-height: 250px !important"
        >
        <img
          class="w-[280px] h-[250px] max-h-[250px] object-cover border-solid border-8 border-violet-200 mx-3"
          src="{{ asset('assets/img/recruitment/team7.jpg') }}"
          style="min-height: 250px !important"
        >
      </div>
    </div>

    <div class="sm:p-20 px-3.75 py-10 text-center" style="background-color: #C0E5A1">
      <div class="sm:text-5xl text-3xl font-bold">TẦM NHÌN</div>
      <div class="flex justify-center"><div class="h-[2px] bg-black mt-5 w-35"></div></div>
      <div class="mt-10 sm:w-[950px] w-auto mx-auto">
        Dungmori là một tập thể với rất nhiều những tài năng luôn tràn đầy năng lượng. Chúng tôi hướng tới một môi trường làm việc hiện đại với phương thức làm việc linh hoạt nhằm khuyến khích tính sáng tạo của mỗi cá nhân một cách tối đa.
      </div>
      <div class="mt-15 flex justify-evenly sm:flex-row flex-col items-center">
        <div class="flex flex-col">
          <img class="w-[250px] h-[300px]" src="{{ asset('assets/img/recruitment/view1.png') }}">
          <div class="mt-5">
            Trở thành cơ quan chuyên<br>
            nghiên cứu - phát triển - cung cấp các<br>
            sản phẩm trong lĩnh vực giáo dục<br>
            tiếng Nhật <span class="font-bold">HÀNG ĐẦU</span> tại Việt Nam
          </div>
        </div>
        <div class="flex flex-col sm:mt-0 mt-12">
          <img class="w-[250px] h-[300px]" src="{{ asset('assets/img/recruitment/view2.png') }}">
          <div class="mt-5">
            Xây dựng HỆ SINH THÁI<br>
            học tiếng Nhật <span class="font-bold">TOÀN DIỆN</span> và<br>
            <span class="font-bold">CHUYÊN NGHIỆP</span> cho người Việt.
          </div>
        </div>
        <div class="flex flex-col sm:mt-0 mt-12">
          <img class="w-[250px] h-[300px]" src="{{ asset('assets/img/recruitment/view3.png') }}">
          <div class="mt-5">
            <span class="font-bold">Tâm - Tín - Trí</span><br>
            Làm Giáo dục từ Tâm - Giữ chữ Tín<br>
            với Khách hàng, Đối tác và Nhân viên<br>
            - Không ngừng mài giũa,<br>
            nâng cao năng lực, trí tuệ
          </div>
        </div>
      </div>
    </div>

    <div class="p-15 flex flex-col items-center text-center">
      <div class="sm:text-3xl text-2xl font-bold">
        HÃY LÀ THÀNH VIÊN TIẾP THEO CỦA DŨNG MORI!
      </div>
      <div class="relative mt-8">
        <img class="w-[268px] h-[297px]" src="{{ asset('assets/img/recruitment/morijoin.png') }}">
        <a href="https://m.me/108011780810530" target="_blank">
          <div class="absolute -bottom-3 -left-3 text-3xl inline-block bg-white rounded-xl border-black border-2 text-black py-3 w-[290px] h-[66px] font-semibold cursor-pointer navigation-button hover:bg-amber-100">
            GỬI CV NGAY
          </div>
        </a>
        <img class="absolute -left-[425px]" src="{{ asset('assets/img/recruitment/cv_left.png') }}">
        <img class="absolute -right-[410px]" src="{{ asset('assets/img/recruitment/cv_right.png') }}">
      </div>
    </div>

    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
    <script>
      const buttons = document.querySelectorAll('.navigation-button');
      buttons.forEach((button) => {
        button.addEventListener('mouseup', () => {
          button.classList.remove('active');
        });
        button.addEventListener('mousedown', () => {
          button.classList.add('active')
        });
      });

      var slidesToShow = 4;
      if (screen.width <= 600) {
        slidesToShow = 1;
      }

      $('#slide_slick').slick({
        autoplay: true,
        autoplaySpeed: 3000,
        arrows: false,
        loop: true,
        infinite: true,
        slidesToShow: slidesToShow,
        dots: true,
        arrows: true
      });
    </script>
  </div>
@stop
