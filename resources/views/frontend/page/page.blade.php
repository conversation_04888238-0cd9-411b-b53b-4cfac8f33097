@extends('frontend._layouts.default')

@section('title') Dungmori - {{ $page->title }} @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber dạy tiếng Nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<script type="text/javascript">
    @if( $page->url == 'hoc-offline')
        $(".hoc-offline").addClass("active");
    @endif
</script>

<div id="fb-root"></div>

<div class="main">
   <div class="main-center main-page-center">
      <div class="main-content-center">
         <div class="page-detail-container">
            <h2 class="page-detail-title">{{ $page->title }}</h2>
            <span class="page-detail-info"><i class="fa fa-user"></i> Người đăng : {{ $page->getAuthorName->name }} &nbsp; &nbsp;  <i class="fa fa-calendar-check-o"></i> Đăng ngày : {{ $page->getFriendlyTime() }} </span>
            <div class="page-social-like">
               <div class="fb-like" data-href="{{url('')}}/bai-viet/{{ $page->id }}-{{ $page->url }}" data-layout="button_count" data-action="like" data-size="small" data-show-faces="false" data-share="true"></div>
            </div>
            <div class="page-detail-content">
               <br/><br/>
               <div class="main-content">{!! html_entity_decode($page->content) !!}</div>
            </div>

            {{-- <div class="comment-container">
               <div class="comment-heading">
                  <span>Bình luận của học viên</span>
               </div>
               <div class="fb-comments" data-width="100%" data-href="{{url('')}}/trang/{{ $page->url }}" data-numposts="15"></div>
            </div> --}}

          </div>
      </div>
   </div>
</div>

@stop

@section('footer-js')
  <script>(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) return;
  js = d.createElement(s); js.id = id;
  js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));</script>
@stop
