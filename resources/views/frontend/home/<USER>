@extends('frontend._layouts.default')

@section('title')
    Kết quả JLPT @if(isset($achievement))
        - {{ $achievement->name }}
    @endif
@stop
@section('keywords')
    Dungmori, dạy, tiếng nhật, họ<PERSON>, online, d<PERSON> hiểu nh<PERSON>, <PERSON><PERSON>o, Japanese, miễn phí, n1, n2, n3, n4, n5
@stop
@section('author')
    DUNGMORI
@stop
@if (isset($achievement))
    @section('description')
        Học tiếng Nhật online cùng với các giáo viên uy tín hàng đầu trong lĩnh vực giảng dạy tiếng Nhật tại Việt Nam và các giáo viên bản địa có kinh nghiệm, tham gia khóa học tiếng Nhật cơ bản dành cho người mới bắt đầu HOÀN TOÀN MIỄN PHÍ và các kh<PERSON>a học tiếng Nh<PERSON>t trung cấp, cao cấp, tham gia các kì thi thử tiếng Nhật JLPT miễn phí đư<PERSON> tổ chức thường xuyên giúp bạn nhanh chóng tiếp cận và nắm vững tiếng Nhật.
    @stop
    @section('image')
        https://dungmori.com/nhan-qua-jlpt/gen-img/{{$achievement->id}}
    @stop
    @section('url')
        https://dungmori.com/nhan-qua-jlpt/{{$achievement->id}}
    @stop

@endif
@section('header-css')
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick.css')}}"/>
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/slick/slick-theme.css')}}"/>
@stop

@section('header-js')
    <meta property="og:image:type" content=".png"/>
    <meta property="og:image:width" content="469"/>
    <meta property="og:image:height" content="470"/>
    <script type="application/ld+json">
        {
          "@context": "http://schema.org",
          "@type": "Organization",
          "name": "DUNGMORI",
          "url": "https://dungmori.com",
          "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
        {
           "@context": "http://schema.org",
           "@type": "WebSite",
           "name": "DUNGMORI",
           "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
           "url": "https://dungmori.com"
         }
    </script>
    <style>
        [v-cloak] {
            display: none
        }

        #jlpt-gift-container-2 {
            display: block;
            text-align: center;
            padding: 10px;
        }

        .jlpt-gift-content {
            border-radius: 15px;
            max-width: 1100px;
            margin: auto;
            padding: 100px;
        }

        .jlpt-gift-content > p {
            font-family: 'Quicksand';
            font-style: normal;
            font-weight: 500;
            font-size: 30px;
            line-height: 1.61;
            text-transform: uppercase;
        }

        .jlpt-gift-content > p:nth-child(2) {
            font-weight: bold;
            color: #96D962;
        }

        .jlpt-gift-content .quote {
            width: 100%;
            font-weight: 600;
            font-size: 16px;
            line-height: 1.61;
            margin-top: 20px;
            text-align: center;
        }

        .jlpt-gift-content .btn {
            margin-top: 30px;
            padding: 15px 34px;
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 700;
            font-size: 20px;
            line-height: 1.2;
            color: #FFFFFF;
            border-radius: 15px;
        }

        .jlpt-gift-content .btn-danger {
            background-color: #FB6D3A;
            margin-right: 20px;
        }

        .jlpt-gift-content .btn-info {
            background-color: #1093F1;
        }

        .jlpt-gift-policy {
            margin-left: 24px;
            max-width: 350px;
            text-align: left;
        }

        .jlpt-gift-policy .uppercase {
            text-transform: uppercase;
        }

        .jlpt-gift-policy .text-red {
            color: #FB6D3A;
        }

        .jlpt-gift-policy .text-blue {
            color: #0E65E5;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .btn-upload {
            font-family: Quicksand;
            background: #F7FFF0;
            border: 1px solid #96D962;
            border-radius: 10px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 8px 50px;
            gap: 10px;
            color: green !important;
            font-size: 16px !important;
            font-weight: 400 !important;
            margin-top: 0 !important;
        }

        .point-input {
            position: relative;
        }

        .point-label {
            position: absolute;
            top: -10px;
            left: 9px;
            background: white;
            color: #96D962 !important;
        }

        @media only screen and (max-width: 992px) {
            .two-columns {
                flex-flow: column;
                align-items: center;
            }

            .jlpt-gift-content {
                padding: 40px 5px;
            }
        }

        #point-input-1,
        #point-input-2,
        #point-input-3,
        #point-input-total {
            font-weight: bold;
            font-size: 13px;
            border-bottom: 1px solid #000;
            width: 5%;
            text-align: right;
            line-height: 1;
        }
    </style>
@stop
@section('content')
    <div class="home-wrapper relative">
        <div id="jlpt-gift-container-2" class="bg-gray-50 relative">
            <dotlottie-player v-if="success && id"
                              src="https://lottie.host/e6935e64-5dad-4f38-8d36-b70418ef3e4e/usVveXYkDF.json"
                              background="transparent" speed="1" class="w-full absolute" loop
                              autoplay></dotlottie-player>
            <div class="jlpt-gift-content px-10 py-10 sm:px-[100px] sm:py-[40px]">
                <p>Báo thành tích JLPT</p>
                <p>Rinh quà từ dũng mori</p>
                <template v-if="isPassed == null">
                    <img src="{{ asset('assets/img/new_home/jlpt-gift.png') }}" alt=""
                         style="margin-top: 60px; max-width: 180px; margin: auto;">
                    <p class="quote">Dù trượt hay đỗ JLPT thì Dũng mori đều có
                        món quà nhỏ gửi đến các bạn!</p>
                    <div class="flex justify-center items-center">
                        <button class="btn btn-danger" @click="isPassed = 'preFailed'">Trượt JLPT</button>
                        <button class="btn btn-info" @click="isPassed = 'prePassed'">Đỗ JLPT</button>
                    </div>
                </template>
                <template v-else-if="isPassed == 'prePassed'">
                    <div class="p-5 mt-2 border-2 border-[#96D962] rounded-xl w-[95%] sm:w-[60%] m-auto text-left leading-5 bg-green-50 leading-10 max-w-[400px]">
                        <div class="text-md font-semibold">HỌC VIÊN ĐỖ:</div>
                        <p class="text-md leading-6">
                            - Đỗ → <b>129 điểm: Giảm 200.000 VND</b> khi đăng ký học khóa học bất kỳ của Dũng Mori (được cộng
                            gộp ưu đãi học viên cũ, thời hạn áp dụng 1 tháng)
                            <br>
                            - <b>130 - 149 điểm: </b> Thưởng <b>200.000 VND</b> tiền mặt hoặc Voucher giảm <b>400.000 VND</b> học phí
                            <br>
                            - <b>150 - 175 điểm: </b> Thưởng <b>500.000 VND</b> tiền mặt hoặc Voucher giảm <b>700.000 VND</b> học phí
                            <br>
                            - Trên <b>175 điểm: </b> Thưởng <b>1.000.000 VND</b> tiền mặt hoặc Voucher giảm <b>1.200.000 VND</b> học phí
                        </p>
                        <br>
                        <div class="text-md font-semibold">
                            Đặc biệt:
                        </div>
                        <p class="text-md leading-6">
                            *Học viên trên 160 điểm sẽ được tham gia duet giao lưu trực tiếp với cô Thanh, thầy Dũng
                            <br>
                            *Học viên Offline trên 160 điểm sẽ được tặng thư tay do thầy cô viết và trao trực tiếp tại các cơ sở Dungmori
                            <br>
                            *Học viên báo điểm đỗ giới thiệu bạn bè đăng ký học tặng <b>200.000 VND</b> tiền mặt/ 1 bạn bè đăng ký (có áp dụng cùng ưu đãi học viên cũ)
                        </p>
                        <div class="text-center">
                            ---------------------------------------------------------------------------
                        </div>
                        <p class="text-md leading-6">
                            <b>*Cách thức nhận thưởng cho học viên:</b>
                            <br>
                            - Bước 1: Báo điểm thông qua link: <a href="https://dungmori.com/nhan-qua-jlpt" target="_blank">https://dungmori.com/nhan-qua-jlpt</a>
                            <br>
                            - Bước 2: Đăng ảnh bằng khen lên FB/tiktok cá nhân kèm nội dung tự chọn và hashtag #mình_chọn_dũng_mori, đăng ở chế độ công khai
                            <br>
                            - Bước 3: Điền thông tin vào link <a href="https://forms.gle/mVGfNqBPwxP6wLmj7" target="_blank">form báo điểm</a>
                            <br>
                            <br>
                            <b>*MỘT SỐ LƯU Ý TRONG NỘI DUNG BÁO ĐIỂM</b>
                            <br>
                            - Tất cả thông tin trong bảng “BÁO ĐIỂM THI” cần được điền đầy đủ và chính xác. Nếu thiếu thông tin nào thì tài khoản báo điểm mặc định không được xác minh quà tặng.
                            <br>
                            - Điểm thi hợp lệ chỉ được tính trong kỳ thi JLPT tháng 12/2024
                            <br>
                            - Thời gian báo điểm hợp lệ: tính từ ngày 31/01/2025 - 06/02/2025
                            <br>
                            - Thời gian nhận thưởng: 13 - 14/02/2025
                            <br>
                            - Thời hạn sử dụng Voucher: Tới hết 06/03/2025
{{--                            <a href="https://bit.ly/baodiemJLPT" target="_blank">https://bit.ly/baodiemJLPT</a>--}}
{{--                            <br>--}}
{{--                            <b>- THỜI GIAN BÁO ĐIỂM HỢP LỆ: Tính từ ngày 26/08 đến hết ngày 01/09/2024 </b><br>--}}
{{--                            <b>- THỜI GIAN NHẬN THƯỞNG Trong 2 ngày 09 và 10/09/2024</b> <br>--}}
{{--                            <b>- THỜI GIAN SỬ DỤNG VOUCHER: Đến 03/10/2024</b> <br>--}}
                        </p>
                    </div>
                    <button class="btn bg-[#fe9c9c]" @click="isPassed = null">Trở lại</button>
                    <button class="btn bg-[#96D962]" @click="isPassed = true">Tiếp tục</button>
                </template>
                <template v-else-if="isPassed == 'preFailed'">
                    <div class="p-5 mt-2 border-2 border-[#96D962] rounded-xl w-[95%] sm:w-[60%] m-auto text-left leading-5 bg-green-50">
                        <div class="text-md font-semibold">HỌC VIÊN TRƯỢT</div>
                        <p class="text-md leading-6">
                            Dũng Mori sẽ tặng thêm 1 tháng khóa học online hoặc giảm 10% khi đăng ký lại cấp độ đã học
                        </p>
                        <div class="text-center">
                            ---------------------------------------------------------------------------
                        </div>
                        <div class="text-md font-semibold leading-6">Lưu ý:</div>
                        <p class="text-md leading-6">
                            <b>*Cách thức nhận thưởng cho học viên:</b>
                            <br>
                            - Bước 1: Báo điểm thông qua link: <a href="https://dungmori.com/nhan-qua-jlpt" target="_blank">https://dungmori.com/nhan-qua-jlpt</a>
                            <br>
                            - Bước 2: Đăng ảnh bằng khen lên FB/tiktok cá nhân kèm nội dung tự chọn và hashtag #mình_chọn_dũng_mori, đăng ở chế độ công khai
                            <br>
                            - Bước 3: Điền thông tin vào link <a href="https://forms.gle/mVGfNqBPwxP6wLmj7" target="_blank">form báo điểm</a>
                            <br>
                            <br>
                            <b>*MỘT SỐ LƯU Ý TRONG NỘI DUNG BÁO ĐIỂM</b>
                            <br>
                            - Tất cả thông tin trong bảng “BÁO ĐIỂM THI” cần được điền đầy đủ và chính xác. Nếu thiếu thông tin nào thì tài khoản báo điểm mặc định không được xác minh quà tặng.
                            <br>
                            - Điểm thi hợp lệ chỉ được tính trong kỳ thi JLPT tháng 12/2024
                            <br>
                            - Thời gian báo điểm hợp lệ: tính từ ngày 31/01/2025 - 06/02/2025
                            <br>
                            - Thời gian nhận thưởng: 13 - 14/02/2025
                            <br>
                            - Thời hạn sử dụng Voucher: Tới hết 06/03/2025
                            {{--                            <a href="https://bit.ly/baodiemJLPT" target="_blank">https://bit.ly/baodiemJLPT</a>--}}
                            {{--                            <br>--}}
                            {{--                            <b>- THỜI GIAN BÁO ĐIỂM HỢP LỆ: Tính từ ngày 26/08 đến hết ngày 01/09/2024 </b><br>--}}
                            {{--                            <b>- THỜI GIAN NHẬN THƯỞNG Trong 2 ngày 09 và 10/09/2024</b> <br>--}}
                            {{--                            <b>- THỜI GIAN SỬ DỤNG VOUCHER: Đến 03/10/2024</b> <br>--}}
                        </p>
                    </div>
                    <button class="btn bg-[#fe9c9c]" @click="isPassed = null">Trở lại</button>
                    <button class="btn bg-[#96D962]" @click="isPassed = false">Tiếp tục</button>
                </template>
                <template v-else-if="isPassed == true">
                    @if (!auth()->check() && !isset($achievement))
                        <div class="flex justify-center items-center" style="min-height: 500px;">
                            <button class="btn btn-success" data-fancybox data-animation-duration="300"
                                    data-src="#auth-container" onclick="swichTab('login')">Đăng nhập
                            </button>
                        </div>
                    @else
                        <div v-if="!success" class="w-[300px] flex flex-col justify-center items-center mt-5 mx-auto">
                            <div class="w-full point-input mb-4">
                                <div class="point-label">Họ và tên</div>
                                <input v-model="name" type="text"
                                       style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                            </div>
                            <div class="w-full point-input mb-4">
                                <div class="point-label">Trình độ</div>
                                <select name="" id="" v-model="level"
                                        style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                >
                                    <option value="N1">N1</option>
                                    <option value="N2">N2</option>
                                    <option value="N3">N3</option>
                                    <option value="N4">N4</option>
                                    <option value="N5">N5</option>
                                </select>
                            </div>
                            <div class="w-full point-input mb-4">
                                <div class="point-label">Tổng điểm</div>
                                <input v-model="point" type="text"
                                       style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                            </div>
                            <div class="w-full point-input mb-4" v-if="show_gift_value">
                                <div class="point-label">Chọn quà</div>
                                <select name="" id="" v-model="gift_value"
                                        style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                >
                                    <option value="3">Voucher giảm giá</option>
                                    <option value="2">Thưởng tiền mặt</option>
                                </select>
                            </div>
                            <button v-if="!imageName" type="button"
                                    class="w-full py-3 rounded bg-green-50 px-2 py-1 text-xs font-semibold text-green-600 shadow-sm hover:bg-green-100 text-lg"
                                    @click="selectFile">
                                <i class="fa fa-camera"></i> Tải ảnh
                            </button>
                            <div v-else class="w-full flex items-center">
                                @{{ imageName }}
                                <i class="fa fa-times ml-2 a-cursor-pointer" @click="removeImage"></i>
                            </div>
                            <input id="jlptGiftImage" type="file" style="display: none;" @change="uploadFile"
                                   accept="image/*"/>
                            {{-- <div class="w-full text-left mt-3 mb-4 font-quicksand font-semibold text-md">3 người bạn mà bạn muốn chia sẻ cùng</div>
                            <div class="w-full point-input mb-4">
                              <div class="point-label">Họ tên người bạn thứ nhất</div>
                              <input v-model="friend1" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />
                            </div>
                            <div class="w-full point-input mb-4">
                              <div class="point-label">Họ tên người bạn thứ hai</div>
                              <input v-model="friend2" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />
                            </div>
                            <div class="w-full point-input mb-4">
                              <div class="point-label">Họ tên người bạn thứ ba</div>
                              <input v-model="friend3" type="text" style="width: 100%;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;" />
                            </div> --}}
                        </div>
                        <div v-if="success && id" class="flex gap-5 flex-col sm:flex-row relative p-3 items-center">
                            <img style="object-fit: contain" :src="url + '/nhan-qua-jlpt/gen-img/' + id" alt=""
                                 class="shadow-lg border border-t border-gray-900 rounded-md max-w-full md:max-w-[400px] md:h-full">
                            <div class="max-w-[500px] font-quicksand text-left text-base mt-3 md:mt-0 leading-8">
                                Yay! Bạn đã rất gần với phần quà của chúng mình rồi. <span
                                        class="text-[#96D962] font-semibold">Dũng Mori</span> cần một vài thông tin để
                                thuận tiện cho việc gửi phần thưởng đến tận tay học viên. Bạn vui lòng
                                làm theo các bước sau đây bạn nhé!
                                <div class="mt-2">
                                    <b>Bước 1:</b> Đăng ảnh bằng khen lên FB/tiktok cá nhân kèm nội dung tự chọn và hashtag <span class="text-green-500">#mình_chọn_dũng_mori</span>, đăng ở chế độ công khai.
                                </div>
                                <div class="mt-2">
                                    <b>Bước 2:</b> Truy cập form sau đây: <a href="https://forms.gle/mVGfNqBPwxP6wLmj7"
                                                                             target="_blank">https://bit.ly/baodiemJLPT</a>
                                    để điền thông tin nhận thưởng nhé!
                                </div>
                                <div class="flex items-center justify-center sm:justify-start mt-3 mb-3">
                                    <button style="background: #57d061"
                                            class="justify-center mr-1 w-1/2 flex items-center text-center text-base font-semibold py-2.5 leading-0 rounded-md bg-blue-700 text-white"
                                            @click="downloadImage()">
                                        <i class="fa fa-download mr-2"></i>
                                        Tải ảnh báo điểm JLPT
                                    </button>
                                    <button style="background: #0084ff"
                                            class="justify-center mr-1 w-1/2 flex items-center text-center text-base font-semibold py-2.5 leading-0 rounded-md bg-blue-700 text-white"
                                            @click="shareContent(url + '/assets/img/jlpt/img-user/' + user_id + '.png')"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" width="16px"
                                             height="16px">
                                            <path d="M80 299.3V512H196V299.3h86.5l18-97.8H196V166.9c0-51.7 20.3-71.5 72.7-71.5c16.3 0 29.4 .4 37 1.2V7.9C291.4 4 256.4 0 236.2 0C129.3 0 80 50.5 80 159.4v42.1H14v97.8H80z"
                                                  fill="#ffffff"/>
                                        </svg>
                                        Chia sẻ
                                    </button>
                                </div>
                                <button @click="openChatbox" type="button" style="background: rgb(87, 208, 97)"
                                        class="w-full py-2 px-4 flex justify-center items-center bg-[#0084ff] focus:ring-blue-500 focus:ring-offset-blue-200 text-white transition ease-in duration-200 text-center text-base font-semibold shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-lg">
                                    <svg width="42" height="38" viewBox="0 0 42 38" fill="none"
                                         xmlns="http://www.w3.org/2000/svg" class="w-6 lg:w-[18px]">
                                        <path d="M21.0298 0.00783166C9.64407 0.00783166 0.380859 8.38874 0.380859 18.6888C0.380859 28.9889 9.64407 37.3698 21.0298 37.3698C24.9127 37.3698 28.6641 36.4014 31.9383 34.5527L38.9803 37.2466C39.2107 37.3346 39.441 37.3698 39.6549 37.3698C40.1979 37.3698 40.7408 37.1233 41.1357 36.6831C41.6951 36.0493 41.8432 35.1337 41.5306 34.3414L38.9968 27.9149C40.7737 25.0978 41.6951 21.9285 41.6951 18.6712C41.6951 8.37115 32.4319 -0.00976562 21.0462 -0.00976562L21.0298 0.00783166ZM34.7847 28.4959L35.9694 31.5067L32.4154 30.151C31.856 29.9397 31.2308 29.9925 30.7043 30.327C27.8579 32.1053 24.5014 33.0385 21.0133 33.0385C11.8653 33.0385 4.42838 26.6119 4.42838 18.6888C4.42838 10.7657 11.8653 4.33915 21.0133 4.33915C30.1613 4.33915 37.5982 10.7657 37.5982 18.6888C37.5982 21.3827 36.7098 24.0237 35.048 26.3126C34.5873 26.9289 34.4886 27.774 34.7847 28.4959ZM13.7409 17.157C14.1194 17.562 14.3333 18.1254 14.3333 18.6888C14.3333 18.8297 14.3168 18.9705 14.3004 19.1114C14.2675 19.2522 14.2346 19.3931 14.1852 19.5163C14.1358 19.6396 14.07 19.7629 13.9877 19.8861C13.9219 20.0093 13.8232 20.115 13.7409 20.2206C13.6422 20.3263 13.5435 20.4143 13.4283 20.4847C13.3132 20.5728 13.198 20.6256 13.0828 20.6784C12.9676 20.7312 12.836 20.7841 12.7044 20.8017C12.5728 20.8369 12.4411 20.8369 12.3095 20.8369C11.783 20.8369 11.2565 20.608 10.8781 20.203C10.7794 20.0974 10.6971 19.9918 10.6313 19.8685C10.5655 19.7453 10.4996 19.622 10.4503 19.4987C10.4009 19.3755 10.368 19.2346 10.3351 19.0938C10.3187 18.9529 10.3022 18.8121 10.3022 18.6712C10.3022 18.5304 10.3187 18.3895 10.3351 18.2486C10.368 18.1078 10.4009 17.9669 10.4503 17.8437C10.4996 17.7204 10.5655 17.5972 10.6313 17.474C10.7135 17.3507 10.7958 17.2451 10.8781 17.1394C11.3552 16.6288 12.0463 16.3999 12.7044 16.5408C12.836 16.5584 12.9512 16.6112 13.0828 16.664C13.198 16.7168 13.3296 16.7873 13.4283 16.8577C13.5435 16.9457 13.6422 17.0338 13.7409 17.1218V17.157ZM23.0042 18.2663C23.0206 18.4071 23.0371 18.548 23.0371 18.6888C23.0371 18.8297 23.0371 18.9705 23.0042 19.1114C22.9712 19.2522 22.9383 19.3931 22.889 19.5163C22.8396 19.6396 22.7738 19.7629 22.6915 19.8861C22.6257 20.0093 22.527 20.115 22.4447 20.2206C22.0663 20.6256 21.5398 20.8545 21.0133 20.8545C20.8817 20.8545 20.7501 20.8545 20.6184 20.8193C20.4868 20.8017 20.3716 20.7488 20.24 20.696C20.1248 20.6432 20.0097 20.5728 19.8945 20.5024C19.7793 20.4143 19.6806 20.3439 19.5819 20.2382C19.4832 20.1326 19.4009 20.0269 19.3351 19.9037C19.2693 19.7804 19.2034 19.6572 19.1541 19.534C19.1047 19.4107 19.0718 19.2698 19.0389 19.129C19.0225 18.9881 19.006 18.8473 19.006 18.7064C19.006 18.5656 19.0225 18.4247 19.0389 18.2839C19.0718 18.143 19.1047 18.0022 19.1541 17.8789C19.2034 17.7557 19.2693 17.6324 19.3351 17.5092C19.4173 17.3859 19.4996 17.2803 19.5819 17.1746C19.6806 17.069 19.7793 16.9809 19.8945 16.9105C20.0097 16.8225 20.1248 16.7697 20.24 16.7168C20.3716 16.664 20.4868 16.6112 20.6184 16.5936C21.2766 16.4528 21.9841 16.6816 22.4447 17.1922C22.5435 17.2979 22.6257 17.4035 22.6915 17.5268C22.7738 17.65 22.8232 17.7733 22.889 17.8965C22.9383 18.0198 22.9712 18.1606 23.0042 18.3015V18.2663ZM31.7244 18.2663C31.7409 18.4071 31.7573 18.548 31.7573 18.6888C31.7573 18.8297 31.7573 18.9705 31.7244 19.1114C31.6915 19.2522 31.6586 19.3931 31.6092 19.5163C31.5599 19.6396 31.4941 19.7629 31.4118 19.8861C31.346 20.0093 31.2473 20.115 31.165 20.2206C31.0663 20.3263 30.9676 20.4143 30.8524 20.4847C30.7372 20.5728 30.622 20.6256 30.5069 20.6784C30.3917 20.7312 30.2601 20.7841 30.1284 20.8017C29.9968 20.8369 29.8652 20.8369 29.7336 20.8369C29.2071 20.8369 28.6805 20.608 28.3021 20.203C28.2034 20.0974 28.1211 19.9918 28.0553 19.8685C27.9895 19.7453 27.9237 19.622 27.8743 19.4987C27.825 19.3755 27.7921 19.2346 27.7592 19.0938C27.7427 18.9529 27.7263 18.8121 27.7263 18.6712C27.7263 18.5304 27.7427 18.3895 27.7592 18.2486C27.7921 18.1078 27.825 17.9669 27.8743 17.8437C27.9237 17.7204 27.9895 17.5972 28.0553 17.474C28.1376 17.3507 28.2199 17.2451 28.3021 17.1394C28.7793 16.6288 29.4703 16.3999 30.1284 16.5408C30.2601 16.5584 30.3752 16.6112 30.5069 16.664C30.622 16.7168 30.7537 16.7873 30.8524 16.8577C30.9676 16.9457 31.0663 17.0338 31.165 17.1218C31.2637 17.2274 31.346 17.3331 31.4118 17.4563C31.4941 17.5796 31.5434 17.7028 31.6092 17.8261C31.6586 17.9493 31.6915 18.0902 31.7244 18.231V18.2663Z"
                                              fill="white"></path>
                                    </svg>
                                    <span class="leading-0 ml-1"> Liên hệ nhận quà</span>
                                </button>
                            </div>
                        </div>
                        <button v-if="!success" class="btn bg-[#fe9c9c]" style="margin: 15px 8px;" @click="isPassed = null">Trở lại</button>
                        <button v-if="!success" class="btn bg-[#96D962]" style="margin: 15px 8px;"
                                :disabled="disabled" @click="upload">
                            <i class="fa fa-spinner fa-pulse" v-if="loading"></i>
                            Gửi thông tin
                        </button>
                    @endif
                </template>
                <template v-else>
                    @if (!auth()->check() && !isset($achievement))
                        <div class="flex justify-center items-center" style="min-height: 500px;">
                            <button class="btn btn-success" data-fancybox data-animation-duration="300"
                                    data-src="#auth-container" onclick="swichTab('login')">Đăng nhập
                            </button>
                        </div>
                    @else
                        <div>
                            <div v-if="!success" class="flex flex-col justify-center items-center mt-5 mx-auto">
                                <div class="point-input mb-4">
                                    <div class="point-label">Họ và tên</div>
                                    <input v-model="name" type="text"
                                           style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                </div>
                                <div class="point-input mb-4">
                                    <div class="point-label">Trình độ</div>
                                    <select name="" id="" v-model="level"
                                            style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                    >
                                        <option value="N1">N1</option>
                                        <option value="N2">N2</option>
                                        <option value="N3">N3</option>
                                        <option value="N4">N4</option>
                                        <option value="N5">N5</option>
                                    </select>
                                </div>
                                <div class="point-input mb-4">
                                    <div class="point-label">Tổng điểm</div>
                                    <input v-model="point" type="text"
                                           style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"/>
                                </div>
                                <div class="point-input mb-4">
                                    <div class="point-label">Chọn quà</div>
                                    <select name="" id="" v-model="gift_value"
                                            style="width: 300px;height: 38px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;text-align: center;"
                                    >
                                        <option value="0">Giảm 10% khi đăng lại cấp độ đã học</option>
                                        <option value="1">Tặng thêm 1 tháng khóa học online</option>
                                    </select>
                                </div>
                                {{--                  <div class="mb-4">--}}
                                {{--                    <div class="text-left mb-3 flex items-center w-[300px]">--}}
                                {{--                      <input type="checkbox" v-model="showCaptionInput" id="post_to_community" class="m-0">--}}
                                {{--                      <label for="post_to_community" class="font-normal m-0 ml-2"> Đăng bài lên cộng dồng Dungmori</label>--}}
                                {{--                    </div>--}}
                                {{--                    <div v-if="showCaptionInput" class="point-input">--}}
                                {{--                      <div class="point-label">Caption đăng bài lên cộng đồng Dungmori</div>--}}
                                {{--                      <textarea v-model="caption" style="width: 300px;height: 100px;background: #FFFFFF;border: 1px solid #96D962;border-radius: 5px;padding:12px;"></textarea>--}}
                                {{--                    </div>--}}
                                {{--                  </div>--}}
                                <button v-if="!imageName" type="button"
                                        class="w-[300px] py-3 rounded bg-green-50 px-2 py-1 text-xs font-semibold text-green-600 shadow-sm hover:bg-green-100 text-lg"
                                        @click="selectFile">
                                    <i class="fa fa-camera"></i> Tải ảnh
                                </button>
                                <div v-else class="flex items-center">
                                    @{{ imageName }}
                                    <i class="fa fa-times ml-2 a-cursor-pointer" @click="removeImage"></i>
                                </div>
                                <input id="jlptGiftImage" type="file" accept="image/*" style="display: none;"
                                       @change="uploadFile"/>
                            </div>
                            {{--                            <div class="grid grid-cols-2 gap-5 mt-5">--}}
                            {{--                                <div--}}
                            {{--                                        v-for="gift in failedGift"--}}
                            {{--                                        class="select-none px-4 py-12 duration-100 hover:shadow-md hover:-translate-y-1 rounded-lg border border-2 border-gray-200 cursor-pointer flex flex-col justify-start items-center"--}}
                            {{--                                        :class="[--}}
                            {{--                                                            gift.value == choseGift ? 'bg-[#96D962] border-[#96D962]' : '',--}}
                            {{--                                                          ]"--}}
                            {{--                                        @click="chooseGift(gift.value)"--}}
                            {{--                                >--}}
                            {{--                                    <img :src="url + '/assets/img/new_home/12-2021/' + gift.img"--}}
                            {{--                                         class="max-w-6xl max-h-24"/>--}}
                            {{--                                    <span class="mt-5 text-md font-semibold italic"--}}
                            {{--                                          :class="[gift.value == choseGift ? 'text-white' : '',]">@{{ gift.label }}</span>--}}
                            {{--                                </div>--}}
                            {{--                            </div>--}}
                            <button v-if="!success" class="btn bg-[#fe9c9c]" style="margin: 15px 8px;" @click="isPassed = null">Trở lại</button>
                            <button v-if="!success" class="btn bg-[#96D962]" style="margin: 15px 8px;"
                                    :disabled="disabled" @click="upload">
                                <i class="fa fa-spinner fa-pulse" v-if="loading"></i>
                                Gửi thông tin
                            </button>
                        </div>
                        <div v-if="!is_upload && success">
                            Bạn đã gửi thông tin trước đó
                        </div>
                    @endif
                </template>
            </div>
        </div>
    </div>
@stop
@section('footer-js')
    <script src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs" type="module"></script>
    @if (auth()->check())
        <script>
            var name = '{{ auth()->user()->name }}'
        </script>
    @endif
    <script>
        var jlptGift = new Vue({
            el: "#jlpt-gift-container-2",
            data: {
                url: window.location.origin,
                user_id: null,
                isPassed: null,
                id: null,
                name: name || '',
                level: 'N1',
                point: null,
                gift_value: null,
                image: null,
                imageName: null,
                friend1: '',
                friend2: '',
                friend3: '',
                loading: false,
                success: false,
                show_gift_value: false,
                jlptFormErrors: {},
                achievement: null,
                failedGift: [
                    {value: 1, label: 'Tặng 1 tháng gia hạn khoá học', img: 'reading-mori.svg'},
                    {value: 2, label: 'Giảm 10% khi đăng ký học lại', img: 'teaching-mori.svg'},
                ],
                choseGift: null,
                is_upload: false,
            },
            computed: {
                disabled: function () {
                    return this.point == null || this.image == null || this.loading || this.success;
                },
            },
            watch: {
                point(newVal, oldVal) {
                    this.show_gift_value = parseInt(newVal) > 129
                },
                isPassed(newVal, oldVal) {
                    window.scrollTo(0,0);
                }
            },
            mounted: function () {
                @if(auth()->check())
                setCookie('jlpt_form_timeout_{{auth()->user()->id}}', 1, 1);
                this.user_id = {{auth()->user()->id}};
                    @endif
                    @if(isset($achievement)) {
                    this.success = true
                    var achievement = {!! $achievement !!};
                    if (achievement) {
                        this.user_id = achievement.user_id;
                        this.id = achievement.id;
                        this.name = achievement.name;
                        this.point = achievement.score;
                        this.level = achievement.level;
                        this.isPassed = achievement.is_passed;
                        this.imagePreview = achievement.image;
                        this.choseGift = achievement.gift_value;
                        // this.friend1 = achievement.friends[0];
                        // this.friend2 = achievement.friends[1];
                        // this.friend3 = achievement.friends[2];
                    }
                }
                @elseif (auth()->check() && !is_null(auth()->user()->load(['achievement'])->achievement))
                    this.success = true
                var achievement = {!! auth()->user()->load(['achievement'])->achievement !!};
                if (achievement) {
                    this.user_id = achievement.user_id;
                    this.id = achievement.id;
                    this.name = achievement.name;
                    this.point = achievement.score;
                    this.level = achievement.level;
                    this.isPassed = achievement.is_passed;
                    this.imagePreview = achievement.image;
                    this.choseGift = achievement.gift_value;
                    // this.friend1 = achievement.friends[0];
                    // this.friend2 = achievement.friends[1];
                    // this.friend3 = achievement.friends[2];
                }
                @endif
            },
            methods: {
                openChatbox: function () {
                    showChatbox()
                },
                chooseGift: function (value) {
                    if (!this.success) {
                        this.choseGift = value
                    }
                },
                downloadImage: function () {
                    var link = document.createElement('a');
                    link.href = this.url + '/nhan-qua-jlpt/gen-img/' + this.id;
                    link.setAttribute('download', 'ket-qua.png'); //or any other extension
                    document.body.appendChild(link);
                    link.click();
                },
                shareContent: function (urlU = null) {
                    // let hashtag = '#dungmori #jlpt #hoctiengnhat';
                    let u = urlU == null ? this.url + '/nhan-qua-jlpt/' + this.id : urlU;
                    let url = 'https://www.facebook.com/sharer.php?u=' + u;
                    console.log(url);
                    this.popupCenter(url, 'Share Facebook', '600', '700');
                },
                popupCenter: function (url, title, w, h) {
                    // Fixes dual-screen position                         Most browsers      Firefox
                    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : screen.left;
                    var dualScreenTop = window.screenTop != undefined ? window.screenTop : screen.top;

                    width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
                    height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

                    var left = ((width / 2) - (w / 2)) + dualScreenLeft;
                    var top = ((height / 2) - (h / 2)) + dualScreenTop;
                    var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

                    // Puts focus on the newWindow
                    if (window.focus) {
                        newWindow.focus();
                    }
                },
                downloadCertificate: function () {
                    let vm = this;
                    html2canvas(document.querySelector("#capture-this-pls"), {}).then(vm.saveScreenShot);
                },
                selectFile: function () {
                    document.getElementById("jlptGiftImage").click();
                },
                uploadFile: function (event) {
                    this.imageName = event.target.files[0].name;
                    this.image = event.target.files[0];
                },
                removeImage: function () {
                    this.image = null;
                    this.imageName = null;
                },
                upload: function () {
                    var vm = this;
                    // vm.is_upload = true;
                    if (!this.level) {
                        alert('Vui lòng nhập trình độ');
                        return;
                    }
                    if (!this.point) {
                        alert('Vui lòng nhập điểm!');
                        return;
                    }
                    if (!this.image) {
                        alert('Vui lòng nhập ảnh!');
                        return;
                    }

                    if (!this.gift_value && parseInt(this.point) > 129) {
                        alert('Vui lòng chọn loại quà!');
                        return;
                    }
                    // if (this.choseGift == null && !this.isPassed) {
                    //     alert('Vui lòng chọn một phần quà!');
                    //     return;
                    // }
                    // vm.loading = true;
                    var jlptFormData = new FormData();
                    jlptFormData.append('isPassed', this.isPassed ? 1 : 0);
                    jlptFormData.append('name', this.name);
                    jlptFormData.append('level', this.level);
                    jlptFormData.append('point', this.point);
                    jlptFormData.append('image', this.image);
                    jlptFormData.append('gift_value', this.gift_value);
                    jlptFormData.append('friend1', this.friend1);
                    jlptFormData.append('friend2', this.friend2);
                    jlptFormData.append('friend3', this.friend3);
                    $.ajax({
                        url: vm.url + '/get-jlpt-gift',
                        type: 'post', processData: false, contentType: false, data: jlptFormData,
                        success: function (response) {
                            vm.loading = false;
                            vm.success = true;
                            setCookie('jlpt_board_timeout', 1, 365);
                            vm.id = response.data.id;
                            vm.name = response.data.name;
                            vm.point = response.data.score;
                            vm.level = response.data.level;
                            vm.isPassed = response.data.is_passed;
                            vm.imagePreview = response.data.image;
                            vm.choseGift = response.data.gift_value;
                            vm.friend1 = response.data.friends[0];
                            vm.friend2 = response.data.friends[1];
                            vm.friend3 = response.data.friends[2];
                        },
                        error: function (response) {
                            alert('Đã xảy ra lỗi!')
                        }
                    });
                }
            }
        })
    </script>
@stop
