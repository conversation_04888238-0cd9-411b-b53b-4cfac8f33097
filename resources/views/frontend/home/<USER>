@extends('frontend._layouts.default')

@section('title')
    DUNGMORI - Nền tảng học tiếng Nhật Online Số 1 tại Việt Nam
@stop
@section('description')
    Hơn 300,000 học viên đã tin tưởng theo học khóa tiếng Nhật online và offline của DUNGMORI, chuyên sâu về luyện thi JLPT, Kaiwa và tiếng Nhật doanh nghiệp.
@stop
@section('keywords')
    DUNGMORI, dạy, tiếng nhật, học, online, d<PERSON> hiểu nh<PERSON>t, nihongo, Japanese, miễn phí, n1, n2, n3, n4, n5
@stop
@section('image')
    {{url('assets/img/oglogo.png')}}
@stop
@section('author')
    DUNGMORI
@stop
@section('header-css')
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
@stop
@section('header-js')

    <script type="application/ld+json">
        {
          "@context": "http://schema.org",
          "@type": "Organization",
          "name": "DUNGMORI",
          "url": "https://dungmori.com",
          "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
        {
           "@context": "http://schema.org",
           "@type": "WebSite",
           "name": "DUNGMORI",
           "alternateName": "DUNGMORI - Website học tiếng Nhật online số 1 tại Việt Nam",
           "url": "https://dungmori.com"
         }
    </script>
    <style lang="scss">
        .complex-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 5px 20px;
            color: white;
            text-shadow: 2px 2px rgb(116, 116, 116);
            text-transform: uppercase;
            cursor: pointer;
            border: solid 2px black;
            letter-spacing: 1px;
            font-weight: 600;
            font-size: 17px;
            background-color: #ff0000;
            border-radius: 50px;
            position: relative;
            overflow: hidden;
            transition: all 0.5s ease;
        }

        .complex-button:active {
            transform: scale(0.9);
            transition: all 100ms ease;
        }

        .complex-button img {
            height: 50px;
            transition: all 0.5s ease;
            z-index: 2;
        }

        .play {
            transition: all 0.5s ease;
            transition-delay: 300ms;
        }

        .complex-button:hover img {
            transform: scale(3) translate(100%);
        }

        .now {
            position: absolute;
            left: 0;
            transform: translateX(-100%);
            transition: all 0.5s ease;
            z-index: 2;
            font-size: 30px;
        }

        .complex-button:hover .now {
            font-family: Montserrat, Arial, sans-serif;
            font-weight: bold;
            transform: translateX(30px);
            transition-delay: 300ms;
        }

        .complex-button:hover .play {
            transform: translateX(200%);
            transition-delay: 300ms;
        }

        .wheel {
            background: url("{{ asset('assets/img/new_home/06-2024/pattern.svg') }}") no-repeat center center;
        }

        .wheel-wrapper {
            position: relative;
        }

        .wheel-item {
            width: 120px;

            .wheel-avatar {
                width: 120px;
            }

            .wheel-feedback {
                height: 110%;
                margin-top: -5%;
                width: 400px;
            }

            .wheel-feedback-container {
                background-color: #fff;
                padding-right: 0;
            }

            .wheel-feedback-name {
                color: #57D061;
            }

            .wheel-feedback-job {
                color: #07403F;
            }

            .wheel-feedback-content {
                display: none;
            }

            &.active {
                .wheel-avatar {
                    transform: scale3d(1.33, 1.33, 1.33);
                }

                .wheel-feedback {
                    height: 140%;
                    margin-top: -20%;
                    width: 65vw;
                }

                .wheel-feedback-container {
                    background-color: #57D061;
                    padding-right: 50px;
                }

                .wheel-feedback-content {
                    display: block;
                    color: #fff;
                }

                .wheel-feedback-name {
                    color: #07403F;
                }

                .wheel-feedback-job {
                    color: #fff;
                }
            }

        }

        .wheel-top,
        .wheel-bottom {
            .wheel-feedback {
                height: 80%;
                margin-top: 10%;
            }

            .wheel-avatar {
                transform: scale3d(0.8, 0.8, 0.8);
            }
        }

        .wheel-svg {
            height: calc(80vh);
            overflow: visible;
            width: calc(80vh);
            z-index: -1;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .st0 {
            fill: none;
            stroke: #57D061;
            stroke-width: 2;
            stroke-miterlimit: 1;
        }

        .home-stats {
            background-image: url('/assets/img/new_home/06-2024/stat-bg.jpg');
        }

        .vertical-dots {
            right: 5px;
            list-style: none;
            display: block;
            position: absolute;
            top: 40%;
            margin-top: -10px;
            text-align: right;
        }

        .vertical-dots li {
            position: relative;
            width: 10px;
            height: 20px;
            cursor: pointer;
        }

        .vertical-dots li button {
            font-size: 0;
            line-height: 0;
            display: block;
            width: 20px;
            height: 20px;
            padding: 5px;
            cursor: pointer;
            color: transparent;
            border: 0;
            outline: none;
            background: transparent;
        }

        .vertical-dots li button:hover,
        .vertical-dots li button:focus {
            outline: none;
        }

        .vertical-dots li button:hover:before,
        .vertical-dots li button:focus:before {
            opacity: 1;
        }

        .vertical-dots li button:before {
            font-family: 'slick';
            font-size: 6px;
            line-height: 20px;

            position: absolute;
            top: 0;
            left: 0;

            width: 20px;
            height: 20px;

            content: '•';
            text-align: center;

            opacity: .25;
            color: black;

            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .vertical-dots li.slick-active button:before {
            opacity: .75;
            color: black;
        }

        .logos-slider-wrapper:before, .logos-slider-wrapper:after {
            position: absolute;
            top: 0;
            content: '';
            width: 30%;
            height: 100%;
            z-index: 2;
            filter: grayscale(100%);
        }

        .logos-slider-wrapper:before {
            left: 0;
            background: linear-gradient(to left, rgba(240, 240, 240, 0), rgba(240, 240, 240, 1));
        }

        .logos-slider-wrapper:after {
            right: 0;
            background: linear-gradient(to right, rgba(240, 240, 240, 0), rgb(240, 240, 240));
        }

        #home-header-slider {
            .image-holder {
                background-repeat: no-repeat;
                background-position: center bottom;
                background-size: cover;
            }

            .slick-active .dot {
                transform: translateX(-20px);
                width: 40px;
                height: 10px;
                border-radius: 5px;
                transform-origin: right;
                margin: 0 4px;
            }

            .slick-dots {
                display: flex;
                position: absolute;
                left: -40px;
                top: 50%;
                width: 100px;
                height: 20px;
                transform: rotate(90deg);

                li {
                    margin: 0 5px;
                }
            }

            .dot {
                width: 10px;
                height: 10px;
                background-color: #308560;
                display: block;
                border-radius: 50%;
                transition: 0.15s ease-in-out;
            }
        }

        .logos-slider {
            .slick-track {
                display: flex;
            }

            .slick-track .slick-slide {
                display: flex;
                height: auto;
                align-items: center;
                justify-content: center;
            }

            .slick-active .dot {
                transform: translateX(-20px);
                width: 40px;
                height: 10px;
                border-radius: 5px;
                transform-origin: right;
                margin: 0 4px;
            }

            .slick-dots {
                li {
                    margin: 0 5px;
                }
            }

            .dot {
                width: 10px;
                height: 10px;
                background-color: #57D061;
                display: block;
                border-radius: 50%;
                transition: 0.15s ease-in-out;
            }
        }

        .feedback-slider {
            .slick-initialized .slick-slide {
                display: flex;
            }
        }

        .active > [data-toggle="pill"] {
            color: rgba(53, 53, 53, 1);
        }

        [data-toggle="pill"] {
            color: rgba(53, 53, 53, 0.5);

            &.active {
                color: rgba(53, 53, 53, 1);
            }

            &:active {
                color: rgba(53, 53, 53, 0.5);
            }

            &:hover {
                color: rgba(53, 53, 53, 1)
            }

            &:focus {
                color: rgba(53, 53, 53, 1);
            }
        }

        @media (max-width: 1610px) {
            .tc-container {
                width: 100vw;
            }

            .tc-info {
                width: 280px;
            }

            .tc-saying {
                width: 320px;
            }
        }

        img {
            filter: blur(0);
            transform: translateZ(0);
            image-rendering: auto;
        }

        #application {
            padding-top: 0;
        }

        #news_slick {
            .slick-slide img {
                margin-left: 0;
            }

            .slick-track {
                height: 450px;
            }
        }

    </style>
@stop

@section('content')
    <!-- Modal của Bootstrap -->
    {{--    <div id="modalSelectLevelJapanese" class="modal fade" tabindex="-1" role="dialog">--}}
    {{--        <div class="modal-dialog " role="document" style="width: 600px">--}}
    {{--            <div class="modal-content bg-[#F4F5FA] rounded-[32px]">--}}
    {{--                <div style="border-bottom: unset" class="modal-header">--}}
    {{--                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">--}}
    {{--                        <span aria-hidden="true">&times;</span>--}}
    {{--                    </button>--}}
    {{--                </div>--}}
    {{--                <div class="modal-body px-[64px]">--}}
    {{--                    <div class="grid grid-cols-2 gap-4 justify-center">--}}
    {{--                        <div class="col-span-2 text-center mb-[68px] font-beanbag text-[40px] text-[#07403F]">Trình độ tiếng Nhật của bạn</div>--}}
    {{--                        <div class="text-center mr-[53px] ml-[43px]">--}}
    {{--                            <div class="btn-n5 rounded-full relative px-[22px] py-[6px]">--}}
    {{--                                <p class="mb-0 font-beanbag text-[#07403F] text-[20px]">Mình mới bắt đầu học tiếng Nhật</p>--}}
    {{--                            </div>--}}
    {{--                            <!-- icon arrow -->--}}

    {{--                            <a href="/khoa-hoc/so-cap-n5">--}}
    {{--                                <img class="" src="/images/lessons/logo-redirect-n5.png">--}}
    {{--                            </a>--}}
    {{--                        </div>--}}
    {{--                        <div class="text-center mr-[43px] ml-[53px]">--}}
    {{--                            <div class="btn-n4 rounded-full relative px-[22px] py-[6px]">--}}
    {{--                                <p class="mb-0 font-beanbag text-[#07403F] text-[20px]">Mình mới bắt đầu học tiếng Nhật</p>--}}
    {{--                            </div>--}}
    {{--                            <!-- icon arrow -->--}}

    {{--                            <a href="/khoa-hoc/so-cap-n4">--}}
    {{--                                <img class="" src="/images/lessons/logo-redirect-n4.png">--}}
    {{--                            </a></div>--}}
    {{--                    </div>--}}
    {{--                </div>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}


    <div id="container_home_2024" v-cloak>

        <!-- Popup Modal Coupon Code-->
        <div id="modalEnterCouponCode" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="grid grid-cols-2 gap-4 justify-center">
                            <div>
                                <img src="/images/lessons/img-popup-enter-coupon-code.png">
                            </div>
                            <div class="">
                                <div class="flex justify-end">
                                    <svg class="cursor-pointer" data-dismiss="modal" aria-label="Close" width="14"
                                         height="14" viewBox="0 0 14 14" fill="none"
                                         xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8.1337 7L13.7922 1.34154C13.9305 1.18886 14.0048 0.988831 13.9998 0.782863C13.9947 0.576896 13.9106 0.380767 13.7649 0.235082C13.6192 0.0893969 13.4231 0.00531458 13.2171 0.00024342C13.0112 -0.00482774 12.8111 0.0695006 12.6585 0.207839L7 5.8663L1.34154 0.207839C1.18886 0.0695006 0.988831 -0.00482774 0.782863 0.00024342C0.576896 0.00531458 0.380767 0.0893969 0.235082 0.235082C0.0893969 0.380767 0.00531458 0.576896 0.00024342 0.782863C-0.00482774 0.988831 0.0695006 1.18886 0.207839 1.34154L5.8663 7L0.207839 12.6585C0.0695006 12.8111 -0.00482774 13.0112 0.00024342 13.2171C0.00531458 13.4231 0.0893969 13.6192 0.235082 13.7649C0.380767 13.9106 0.576896 13.9947 0.782863 13.9998C0.988831 14.0048 1.18886 13.9305 1.34154 13.7922L7 8.1337L12.6585 13.7922C12.8111 13.9305 13.0112 14.0048 13.2171 13.9998C13.4231 13.9947 13.6192 13.9106 13.7649 13.7649C13.9106 13.6192 13.9947 13.4231 13.9998 13.2171C14.0048 13.0112 13.9305 12.8111 13.7922 12.6585L8.1337 7Z"
                                              fill="#212121"/>
                                    </svg>
                                </div>
                                <div class="mb-[42px]">
                                    <div class="font-zuume-semibold uppercase text-[38px] text-[#57D061]">
                                        Có mã giới thiệu?
                                    </div>
                                    <div class="text-[14px] text-[#1E1E1E] font-averta-regular">
                                        Mở toàn bộ khóa học
                                    </div>
                                    <div class="text-[14px] text-[#1E1E1E]">
                                        <span class="text-bold">N5-N4 sơ cấp</span> mới trong 24 giờ
                                    </div>
                                </div>

                                <div class="form__group field mb-[42px]">
                                    <input
                                            autocomplete="off"
                                            :disabled="loading_btn_add_code"
                                            type="text" class="form__field" placeholder="Nhập mã"
                                            v-model="add_code_to_user.code_input" name="name" id='name'
                                            required/>
                                    {{--                                    <label for="name" class="form__label font-averta-regular">Nhập mã</label>--}}
                                    <p :class="`text-[14px] ${status_add_code ? 'text-[#C00F0C]' : 'text-[#FFFFFF]'}`">
                                        *Mã không hợp lệ</p>
                                </div>

                                <div>
                                    {{--                                    @if(!\Illuminate\Support\Facades\Auth::check())--}}
                                    {{--                                        <button--}}
                                    {{--                                                @click="showDialogLogin()"--}}
                                    {{--                                                :class="`uppercase font-beanbag text-[12px] w-full rounded-full py-[12px] mb-[12px] bg-[#CEFFD8] text-[#07403F] cursor-pointer'`"--}}
                                    {{--                                        >--}}
                                    {{--                                            Đăng nhập--}}
                                    {{--                                        </button>--}}
                                    {{--                                    @else--}}
                                    <button
                                            @click="addCodeToUser()"
                                            v-loading="loading_btn_add_code"
                                            :disabled="!add_code_to_user.code_input"
                                            :class="`uppercase font-beanbag text-[12px] w-full rounded-full py-[12px] mb-[12px] ${add_code_to_user.code_input ? 'bg-[#CEFFD8] text-[#07403F] cursor-pointer' : 'bg-[#D9D9D9] text-[#B3B3B3]'}`"
                                    >
                                        áp dụng mã
                                    </button>
                                    {{--                                    @endif--}}

                                    <button data-dismiss="modal" aria-label="Close"
                                            class="uppercase font-beanbag text-[12px] text-[#07403F] w-full rounded-full py-[12px] border border-[#D9D9D9]">
                                        Bỏ qua
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full h-[56.25vw] bg-[#F0F0F0] mt-[60px] xl:mt-0">
            <div id="home-header-slider" class="w-full h-[56.25vw] relative">
                @foreach ($advertise as $news)
                    @if(in_array($news->id, [18, 30]))
                        @if( !$courseOwnerPromotion)
                            <div class="slick-slide w-full h-[56.25vw]">
                                <a
                                        onclick="ga('send', 'event', 'hoc_thu_cate', 'click_banner', 'click_banner_label')"
                                        @if(!\Illuminate\Support\Facades\Auth::check())
                                            data-fancybox data-animation-duration="300" data-src="#auth-container"
                                        onclick="swichTab('login')"
                                        @else
                                            data-toggle="modal" data-target="#modalEnterCouponCode"
                                        @endif
                                        class="block w-full h-[56.25vw] image-holder bg-cover bg-center bg-no-repeat cursor-pointer"
                                        style="background-image: url('{{url('/cdn/qc/default/' . $news->image_name)}}')"
                                >
                                </a>
                            </div>
                        @else
                            @continue
                        @endif

                    @else
                        <div class="slick-slide w-full h-[56.25vw]">
                            <a class="block w-full h-[56.25vw] image-holder bg-cover bg-center bg-no-repeat"
                               href="{{$news->link}}"
                               style="background-image: url('{{url('/cdn/qc/default/' . $news->image_name)}}')"></a>
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
        <div class="bg-[#F0F0F0] py-6 xl:py-20 relative floating-container px-2 xl:px-0">
            <img src="{{ asset('assets/img/new_home/06-2024/flower.svg') }}" alt=""
                 class="floating w-10 h-10 parallax-bg absolute top-[500px] left-0 z-[2]" data-speed=".4">
            <img src="{{ asset('assets/img/new_home/06-2024/play.svg') }}" alt="Học qua nền tảng App/Website, Zoom"
                 class="floating w-10 h-10 parallax-bg absolute top-[600px] left-[200px] z-[2]" data-speed=".45">
            <img src="{{ asset('assets/img/new_home/06-2024/flower-outline.svg') }}" alt="Học trực tiếp tại các cơ sở"
                 class="floating w-10 h-10 parallax-bg absolute top-[1000px] right-[100px] z-[2]" data-speed=".25">
            <img src="{{ asset('assets/img/new_home/06-2024/heart.svg') }}"
                 alt="Luyện thi kì thi vào các trường đại học tại Nhật"
                 class="floating w-10 h-10 parallax-bg absolute top-[1500px] left-[300px] xl:left-[30px] z-[2]"
                 data-speed=".35">
            <img src="{{ asset('assets/img/new_home/06-2024/heart.svg') }}"
                 alt="Cung cấp giáo trình luyện JLPT, Giao tiếp"
                 class="floating w-10 h-10 parallax-bg absolute top-[1.2%] lg:top-[2%] left-[calc(70%)] lg:left-[calc(65%)] z-[2]"
                 data-speed=".2">

            <div class="container xl:px-22 font-zuume-semibold xl:font-beanbag text-dmr-green-dark text-[60px] md:text-[50px] capitalize flex items-center justify-start xl:justify-center text-left xl:text-center gap-3 w-full z-[1] relative leading-[1.1em]">
                <span>Sản phẩm <br class="xl:hidden"> độc quyền</span>
            </div>
            <section class="container">
                <div class="w-auto xl:!w-[1300px] flex flex-col md:flex-row items-center md:items-end gap-5 font-beanbag mt-2 xl:mt-5 px-2 lg:px-0">
                    <div class="w-full xl:w-1/3">
                        <div class="pr-3 xl:pr-10">
                            <div class="bg-dmr-green-dark py-2 text-white text-xl xl:text-[32px] rounded-2xl text-center relative test-box">
                                <span class="capitalize">Học thử miễn phí</span>
                                <div class="absolute -top-2 -right-5 md:-right-10 py-1.5 px-3 bg-red-600 text-sm md:text-[14px] font-averta-semibold uppercase text-white  rounded-[20px] leading-none">
                                    Mới
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-auto xl:!w-[1300px] md:grid grid-cols-3 flex flex-col md:flex-row items-center md:items-justify gap-5 font-beanbag mt-2 xl:mt-4 px-2 lg:px-0">
                    <div class="w-full col-span-1">
                        <div class="w-full aspect-square hover:scale-[101%] transition">
                            <a href="{{url('/khoa-hoc/so-cap-n5')}}">
                                <img src="{{ asset('assets/img/new_home/06-2024/jlpt-new-n54-banner.png') }}"
                                     alt="Khóa JLPT N5 & N4" class="w-full object-cover cursor-pointer">
                            </a>
                        </div>
                    </div>
                    <div class="w-full h-full col-span-2 relative rounded-[10px] group hover:scale-[101%] transition mt-5 md:mt-0">
                        <img src="{{ asset('assets/img/new_home/06-2024/so-cap-banner.png') }}"
                             alt="Ra mắt khóa học trước sơ cấp" class="object-cover cursor-pointer rounded-[10px]">

                        <img src="{{ asset('assets/img/new_home/06-2024/dmr-so-cap-banner.png') }}"
                             alt="Ra mắt khóa học trước sơ cấp"
                             class="transition origin-bottom-left group-hover:scale-105 cursor-pointer absolute bottom-0 left-0 h-[110%]">
                        {{--                    <span href="" class="font-averta-bold text-[30px] text-dmr-green-dark hover:text-dmr-green-dark absolute left-[46%] bottom-[20%] md:bottom-[24%] xl:bottom-[28%]">20.05.2024</span>--}}
                        {{--                    <a href="" class="font-beanbag text-base md:text-[20px] px-1 md:px-5 py-1 md:py-2 bg-white rounded-full text-dmr-green hover:text-dmr-green-dark hover:scale-105 transition-transform absolute left-[49%] xl:left-[46%] bottom-[5%] md:bottom-[7%] xl:bottom-[18%] flex items-center">Đăng ký học thử</a>--}}
                    </div>
                </div>
            </section>

            <section class="container w-auto xl:!w-[1300px] mt-5">
                <div class="flex flex-col xl:flex-row font-beanbag gap-1 md:gap-3 px-1 md:px-0 mt-12 md:mt-20">
                    <h1 class="text-gray-700 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Luyện thi JLPT
                        online</h1>
                    <h2 class="text-gray-400 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Học qua nền tảng
                        App/Website, Zoom</h2>
                </div>
                <div class="flex flex-col xl:flex-row px-1 md:px-0 mt-4">
                    <div class="w-full xl:w-1/2 relative mr-6">
                        <div
                                class="h-full w-full bg-white rounded-2xl p-4.5 shadow-lg flex flex-col flex-wrap items-justify xl:items-start justify-center md:justify-between relative">
                            <img src="{{ asset('assets/img/new_home/06-2024/star-bg.svg') }}" alt="Mori Online Basic"
                                 class="absolute bottom-0 right-0">
                            <div class="flex flex-col lg:flex-row gap-3 w-full flex-start">
                                <img src="{{ asset('assets/img/new_home/06-2024/mori-online-basic.png') }}"
                                     alt="Mori Online Basic"
                                     class="cursor-pointer w-full md:w-[50%] aspect-square flex-shrink-0">
                                <div class="w-full xl:w-[50%]">
                                <span class="font-zuume-semibold text-dmr-green text-5xl md:text-[65px] relative bg-gradient-to-r from-[#57D061] to-[#A0EC1D] text-transparent bg-clip-text">
                                    <span class="">Online VIP</span>
                                    <svg width="22" height="21" viewBox="0 0 22 21" fill="none"
                                         xmlns="http://www.w3.org/2000/svg" class="absolute -right-8 top-1">
                                        <g clip-path="url(#clip0_819_201)">
                                            <path d="M17.3334 20.9999C16.9891 20.9999 16.817 20.9999 16.4727 20.8277L10.6203 16.6966L4.7678 20.6556C4.2514 20.9999 3.56288 20.9999 3.04649 20.6556C2.53009 20.3113 2.35796 19.6228 2.53009 18.9343L4.59567 12.2212L0.636649 8.4343C0.120256 8.09004 -0.0518754 7.40151 0.292387 6.88512C0.464518 6.36872 1.15304 6.02446 1.66944 6.02446H6.83337L9.41534 0.860528C9.93173 -0.172259 11.653 -0.172259 12.1694 0.860528L14.7514 6.02446H19.9153C20.6039 6.02446 21.1203 6.36872 21.2924 6.88512C21.4645 7.40151 21.4645 8.09004 20.9481 8.60643L16.817 12.3933L18.8826 19.1064C19.0547 19.795 18.8826 20.3113 18.3662 20.8277C18.0219 20.8277 17.6776 20.9999 17.3334 20.9999ZM10.6203 13.4261C10.9645 13.4261 11.1366 13.4261 11.4809 13.5982L14.7514 15.8359L13.7186 12.3933C13.5465 11.8769 13.7186 11.3605 14.0629 10.8441L15.9563 8.95069H13.7186C13.0301 8.95069 12.5137 8.60643 12.3416 8.09004L10.6203 4.81954L8.89894 8.09004C8.72681 8.60643 8.21042 8.95069 7.69403 8.95069H5.28419L7.17763 10.8441C7.5219 11.1884 7.69403 11.8769 7.5219 12.3933L6.48911 15.8359L9.7596 13.7704C10.1039 13.5982 10.276 13.4261 10.6203 13.4261Z"
                                                  fill="#A0EC1D"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_819_201">
                                                <rect width="21" height="21" fill="white"
                                                      transform="translate(0.121094)"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </span>
                                    <div class="font-averta-bold text-xl mt-3">Học trực tiếp với giáo <br> viên qua
                                        Zoom.
                                    </div>
                                    <div class="font-averta-regular text-base font-normal text-[#888888] mt-8">Lộ trình
                                        khoa học bài bản, có giáo viên hướng dẫn trực tiếp và trợ giảng hỗ trợ ngoài
                                        giờ. Khóa học cam kết cung cấp đầy đủ kiến thức theo từng level khác nhau.
                                    </div>
                                </div>
                            </div>
                            <div class="w-full">
                                <ul class="w-full flex flex-wrap items-center md:justify-start justify-between uppercase font-averta-bold text-xl">
                                    <li>
                                        <a href="https://onlinevip.dungmori.com/"
                                           class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N1</a>
                                    </li>
                                    <li>
                                        <a href="https://onlinevip.dungmori.com/"
                                           class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N2</a>
                                    </li>
                                    <li>
                                        <a href="https://onlinevip.dungmori.com/"
                                           class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N3</a>
                                    </li>
                                    <li>
                                        <a href="https://onlinevip.dungmori.com/"
                                           class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N4</a>
                                    </li>
                                    <li>
                                        <a href="https://onlinevip.dungmori.com/"
                                           class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N5</a>
                                    </li>
                                </ul>
                            </div>

                        </div>
                    </div>
                    <div class="w-full xl:w-1/2 mt-6 xl:mt-0">
                        <div class="relative">
                            <div
                                    class="h-full w-full bg-white border-4 border-white/5 rounded-2xl px-4 py-6 shadow-lg flex xl:flex-row flex-col flex-wrap md:items-start items-center md:justify-between justify-center gap-8">
                                <div>
                                    <div class="w-full flex flex-col md:flex-row flex-cols-1 md:flex-cols-2 gap-2 items-justify md:items-center">
                                        <div class="w-full xl:w-1/2">
                                        <span class="font-zuume-semibold text-dmr-green text-5xl md:text-[65px] leading-none mt-1.5 bg-gradient-to-r from-[#57D061] to-[#A0EC1D] text-transparent bg-clip-text relative">
                                        Online Plus
                                        <svg width="13" height="13" viewBox="0 0 13 13" fill="none"
                                             xmlns="http://www.w3.org/2000/svg" class="absolute -right-4 top-1.5">
                                            <path d="M6.23377 12.4C4.98701 12.4 4.15584 11.4 4.15584 10.4V8.4H2.07792C0.831169 8.4 0 7.4 0 6.4C0 5.4 1.03896 4.4 2.07792 4.4H4.15584V2C4.15584 0.8 5.19481 0 6.23377 0C7.27273 0 8.31169 1 8.31169 2V4H10.3896C11.6364 4 12.4675 5 12.4675 6C12.4675 7 11.4286 8 10.3896 8H8.31169V10C8.51948 11.4 7.48052 12.4 6.23377 12.4Z"
                                                  fill="#A8E81D"/>
                                        </svg>

                                    </span>
                                        </div>
                                        <div class="font-averta-bold text-lg leading-none w-full xl:w-1/2">Học trực tiếp
                                            với giáo viên qua Zoom.
                                        </div>
                                    </div>
                                    <div class="font-averta-regular text-base font-normal text-[#888888] mt-2">Khóa học
                                        cung cấp đầy đủ kiến thức từng level kết hợp luyện đề qua zoom cùng các
                                        giáo viên có nhiều năm kinh nghiệm.
                                    </div>
                                </div>
                                <div class="w-full">
                                    <ul class="w-full flex flex-wrap items-center lg:justify-start justify-start uppercase font-averta-semibold text-xl">
                                        <li>
                                            <a href="https://onlineplus.dungmori.com/"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N1</a>
                                        </li>
                                        <li>
                                            <a href="https://onlineplus.dungmori.com/"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N2</a>
                                        </li>
                                        <li>
                                            <a href="https://onlineplus.dungmori.com/"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[14vw] h-[14vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">N3</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                        </div>
                        <div class="relative mt-6">
                            <div
                                    class="h-full w-full bg-white border-4 border-white/5 rounded-2xl p-4 shadow-lg flex xl:flex-row flex-col flex-wrap md:items-start items-center md:justify-between justify-center">
                                <div>
                                    <div class="w-full flex flex-col md:flex-row flex-cols-2 gap-2 items-center">
                                        <div class="font-zuume-semibold text-dmr-green text-4xl md:text-[60px] w-full xl:w-1/2 leading-none">
                                            Online Basic
                                        </div>
                                        <div class="font-averta-bold text-lg w-full xl:w-1/2 leading-none">Học online
                                            qua video bài giảng, hệ thống bài test.
                                        </div>
                                    </div>
                                </div>
                                <div class="w-full lg:w-[60%] font-averta-regular text-base font-normal text-[#888888] mt-2">
                                    Với lộ trình được cá nhân hóa và hệ thống bài giảng lên tới hàng nghìn video/bài
                                    test.
                                </div>

                                <div class="w-full mt-8">
                                    <ul class="w-full flex flex-wrap items-center md:justify-start justify-between uppercase font-averta-bold text-xl">
                                        <li>
                                            <a href="{{url('/khoa-hoc/jlpt-n1')}}"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[13vw] h-[13vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer relative">
                                               N1
                                               <div class="absolute -top-3 -right-5 md:right-1.75 py-1 px-2.25 bg-red-600 text-sm md:text-[14px] font-averta-semibold uppercase text-white rounded-[20px] leading-none">
                                                    Mới
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="{{url('/khoa-hoc/jlpt-n2')}}"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[13vw] h-[13vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer relative">
                                               N2
                                               <div class="absolute -top-3 -right-5 md:right-1.75 py-1 px-2.25 bg-red-600 text-sm md:text-[14px] font-averta-semibold uppercase text-white rounded-[20px] leading-none">
                                                    Mới
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="{{url('/khoa-hoc/jlpt-n3')}}"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[13vw] h-[13vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer relative">
                                               N3
                                               <div class="absolute -top-3 -right-5 md:right-1.75 py-1 px-2.25 bg-red-600 text-sm md:text-[14px] font-averta-semibold uppercase text-white rounded-[20px] leading-none">
                                                    Mới
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="{{url('/khoa-hoc/so-cap-n4')}}"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[13vw] h-[13vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer relative">
                                                N4
                                            </a>
                                        </li>
                                        <li>
                                            <a href="{{url('/khoa-hoc/so-cap-n5')}}"
                                               class="mr-2 xl:mr-4 mt-1 lg:mt-0 transition-transform hover:scale-105 rounded-full w-[13vw] h-[13vw] lg:h-[66px] lg:w-[66px] aspect-square p-4 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer relative">
                                                N5
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
                <div class="flex flex-col xl:flex-row font-beanbag text-lg md:text-[30px] xl:text-[40px] gap-1 md:gap-3 px-1 mt-16 md:mt-20">
                    <h1 class="text-gray-700 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Luyện thi JLPT
                        offline</h1>
                    <h2 class="text-gray-400 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Học trực tiếp tại các
                        cơ sở</h2>
                </div>
                <div class="relative  mt-4">
                    <div
                            class="h-full w-full bg-white border-4 border-white/5 rounded-2xl py-4.5 px-6 shadow-lg flex md:flex-row flex-col flex-wrap md:items-start items-center md:justify-between justify-center gap-6 space-x-6 ">
                        <div class="flex flex-col xl:flex-row gap-3 xl:space-x-3 w-full justify-between">
                            <div class="w-full xl:w-[50%]">
                                <div class="w-full flex flex-col xl:flex-row items-center md:items-start xl:items-center gap-10 lg:space-x-10 xl:space-x-0">
                                    <div class="font-zuume-semibold text-dmr-green text-5xl md:text-[65px]">Offline
                                    </div>
                                    <div class="font-averta-bold text-lg">Học trực tiếp tại các cơ sở ở Hà Nội và TP.
                                        HCM cùng các giảng viên giàu kinh nghiệm.
                                    </div>
                                </div>

                                <div class="font-averta-regular text-base">Lớp học trực tiếp có giáo viên kèm cặp, cam
                                    kết đảm bảo đầu ra bằng văn bản.
                                </div>
                            </div>
                            <div class="grid grid-cols-2 xl:flex flex-wrap flex-cols-2 items-center justify-center md:justify-start gap-4 uppercase font-averta-bold text-xl mt-5 xl:mt-0">
                                <a href="https://offline.dungmori.com/"
                                   class="transition-transform hover:scale-105 rounded-full w-full xl:w-[66px] py-4 px-12 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">
                                    N2</a>
                                <a href="https://offline.dungmori.com/"
                                   class="transition-transform hover:scale-105 rounded-full w-full xl:w-[66px] py-4 px-12 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">
                                    N3</a>
                                <a href="https://offline.dungmori.com/"
                                   class="transition-transform hover:scale-105 rounded-full w-full xl:w-[66px] py-4 px-12 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">
                                    N4</a>
                                <a href="https://offline.dungmori.com/"
                                   class="transition-transform hover:scale-105 rounded-full w-full xl:w-[66px] py-4 px-12 border-2 border-dmr-green hover:bg-dmr-green text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">
                                    N5</a>
                            </div>
                        </div>

                    </div>
                </div>
                <img src="{{ asset('assets/img/new_home/06-2024/banner-jlpt.png') }}" alt="JLPT offline"
                     class="object-cover w-full mt-5 shadow">

                <div class="flex flex-col xl:flex-row font-beanbag text-lg md:text-[30px] xl:text-[40px] gap-1 md:gap-3 px-1 md:px-0 mt-16 md:mt-20">
                    <h1 class="text-gray-700 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Luyện Giao tiếp</h1>
                    <h2 class="text-gray-400 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Luyện nói tiếng Nhật
                        qua lớp Zoom</h2>
                </div>

                <div class="h-auto xl:h-[245px] w-full flex flex-col xl:flex-row gap-4 mt-4 items-stretch md:items-stretch">
                    <div class="w-full xl:w-[25%] rounded-xl overflow-hidden mx-auto align-self-stretch shadow-lg mt-2">
                        <img src="{{ asset('assets/img/new_home/06-2024/kaiwa-banner.png') }}" alt="Kaiwa"
                             class="object-cover w-full">
                    </div>

                    <div class="w-full xl:w-[40%] relative cols-span-1 md:col-span-2 h-full mt-2">
                        <div
                                class="h-full w-full bg-white border-4 border-white/5 rounded-2xl p-2 lg:p-4.5 shadow-lg
                            md:flex md:flex-row flex-wrap md:items-start items-center md:justify-between justify-center gap-0 ">
                            <div class="flex flex-col items-start xl:items-start justify-start gap-2 relative">
                                <svg width="173" height="173" viewBox="0 0 173 173" fill="none"
                                     xmlns="http://www.w3.org/2000/svg" class="absolute top-6 left-1/2 xl:left-[60%]">
                                    <g opacity="0.08" clip-path="url(#clip0_839_337)">
                                        <path d="M7.20833 144.167C3.2037 144.167 0 140.963 0 136.958V7.20833C0 3.2037 3.2037 0 7.20833 0H136.958C140.963 0 144.167 3.2037 144.167 7.20833V100.917C144.167 104.921 140.963 108.125 136.958 108.125H46.4537L12.8148 141.764C11.213 143.366 8.81018 144.167 7.20833 144.167ZM14.4167 14.4167V119.338L38.4444 95.3102C40.0463 93.7083 41.6481 92.9074 43.25 92.9074H129.75V14.4167H14.4167Z"
                                              fill="#EC6E23"/>
                                        <path d="M165.792 173C164.19 173 161.788 172.199 160.987 170.598L127.348 136.959H57.6673C53.6627 136.959 50.459 133.755 50.459 129.75C50.459 125.746 53.6627 122.542 57.6673 122.542H129.751C131.353 122.542 133.755 123.343 134.556 124.945L158.584 148.973V43.2503C158.584 39.2457 161.788 36.042 165.792 36.042C169.797 36.042 173.001 39.2457 173.001 43.2503V165.792C173.001 168.996 169.797 173 165.792 173Z"
                                              fill="#EC6E23"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_839_337">
                                            <rect width="173" height="173" fill="white"/>
                                        </clipPath>
                                    </defs>
                                </svg>

                                <div class="font-zuume-semibold text-dmr-green text-5xl md:text-[65px] leading-none relative bg-gradient-to-b from-[#EC3B23] to-[#EC6E23] text-transparent bg-clip-text">
                                    KAIWA VIP
                                    <svg width="21" height="21" viewBox="0 0 21 21" fill="none"
                                         xmlns="http://www.w3.org/2000/svg" class="absolute top-2 -right-7">
                                        <g clip-path="url(#clip0_819_238)">
                                            <path d="M17.2123 20.9999C16.868 20.9999 16.6959 20.9999 16.3516 20.8277L10.4992 16.6966L4.6467 20.6556C4.13031 20.9999 3.44179 20.9999 2.92539 20.6556C2.409 20.3113 2.23687 19.6228 2.409 18.9343L4.47457 12.2212L0.515555 8.4343C-0.000838031 8.09004 -0.172969 7.40151 0.171293 6.88512C0.343424 6.36872 1.03195 6.02446 1.54834 6.02446H6.71228L9.29424 0.860528C9.81064 -0.172259 11.5319 -0.172259 12.0483 0.860528L14.6303 6.02446H19.7942C20.4828 6.02446 20.9992 6.36872 21.1713 6.88512C21.3434 7.40151 21.3434 8.09004 20.827 8.60643L16.6959 12.3933L18.7615 19.1064C18.9336 19.795 18.7615 20.3113 18.2451 20.8277C17.9008 20.8277 17.5565 20.9999 17.2123 20.9999ZM10.4992 13.4261C10.8434 13.4261 11.0156 13.4261 11.3598 13.5982L14.6303 15.8359L13.5975 12.3933C13.4254 11.8769 13.5975 11.3605 13.9418 10.8441L15.8352 8.95069H13.5975C12.909 8.95069 12.3926 8.60643 12.2205 8.09004L10.4992 4.81954L8.77785 8.09004C8.60572 8.60643 8.08933 8.95069 7.57293 8.95069H5.1631L7.05654 10.8441C7.4008 11.1884 7.57293 11.8769 7.4008 12.3933L6.36801 15.8359L9.63851 13.7704C9.98277 13.5982 10.1549 13.4261 10.4992 13.4261Z"
                                                  fill="#ED4124"/>
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_819_238">
                                                <rect width="21" height="21" fill="white"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </div>
                                <p class="font-averta-bold text-xl leading-7 w-[80%]">Học Kaiwa trực tiếp với giáo viên
                                    Nhật/Việt qua nền tảng Zoom</p>
                            </div>
                            <ul class="w-full flex xl:flex-row items-center justify-start md:justify-start gap-1 lg:gap-4 xl:gap-2 uppercase font-averta-semibold text-lg mt-2">
                                <li class="w-[25vw] h-[25vw] md:w-1/3 md:h-auto flex-shrink-0 mr-2">
                                    <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}"
                                       class="text-center transition-transform hover:scale-105 w-full aspect-square md:aspect-auto rounded-full p-4 border-2 border-dmr-orange hover:bg-dmr-orange text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">Chi
                                        tiết</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="w-full xl:w-[35%] relative  col-span-1 h-full mt-2">
                        <div class="h-full w-full bg-white border-4 border-white/5 rounded-2xl p-2 lg:p-4.5 shadow-lg flex xl:flex-row flex-col flex-wrap items-start md:justify-between justify-center gap-0 ">
                            <div class="font-zuume-semibold text-[#EC6E23] text-5xl md:text-[65px] leading-none relative">
                                KAIWA BASIC
                            </div>
                            <div class="font-averta-bold text-xl leading-7 pr-3">Tự luyện Kaiwa với ứng dụng DUNGMORI
                            </div>
                            <ul class="w-full flex xl:flex-row items-center justify-start md:justify-start gap-1 lg:gap-4 xl:gap-2 uppercase font-averta-semibold text-lg mt-2">
                                <li class="w-[25vw] h-[25vw] md:w-1/2 md:h-auto flex-shrink-0">
                                    <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}"
                                       class="text-center transition-transform hover:scale-105 w-full aspect-square md:aspect-auto rounded-full p-4 border-2 border-dmr-orange hover:bg-dmr-orange text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">Sơ
                                        cấp</a></li>
                                <li class="w-[25vw] h-[25vw] md:w-1/2 md:h-auto flex-shrink-0">
                                    <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}"
                                       class="text-center transition-transform hover:scale-105 w-full aspect-square md:aspect-auto rounded-full p-4 border-2 border-dmr-orange hover:bg-dmr-orange text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer ">Trung
                                        cấp</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col xl:flex-row font-beanbag text-lg md:text-[30px] xl:text-[40px] gap-1 md:gap-3 px-1 md:px-0 mt-16 md:mt-20">
                    <h1 class="text-gray-700 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Sách</h1>
                    <h2 class="text-gray-400 cursor-pointer text-lg md:text-[30px] xl:text-[40px]">Cung cấp giáo trình
                        luyện JLPT,
                        <br class="xl:hidden"> Giao tiếp</h2>
                </div>
                <div class="relative  col-span-2 mt-6">
                    <div
                            class="relative h-full w-full bg-white border-4 border-white/5 rounded-2xl p-4.5 shadow-lg flex xl:flex-row flex-col flex-wrap xl:items-start items-center md:justify-between justify-center gap-6 ">

                        <div class="w-full xl:w-[70%]">
                            <div class="w-full flex flex-col xl:flex-row items-center xl:items-center gap2 md:gap-10">
                                <div class="font-zuume-semibold text-dmr-cyan text-5xl xl:text-[85px] flex-shrink-0 leading-0">
                                    Sách
                                </div>
                                <ul class="w-full lg:w-2/3 flex xl:flex-row items-center justify-between md:justify-start gap-1 lg:gap-4 xl:gap-2 uppercase font-averta-bold text-lg">
                                    <li class="w-[25vw] h-[25vw] md:w-1/3 md:h-auto flex-shrink-0">
                                        <a href="https://sach.dungmori.com/"
                                           class="transition-transform hover:scale-105 rounded-full w-full h-full text-center aspect-square md:aspect-auto p-4 border-2 border-dmr-cyan hover:bg-dmr-cyan text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer">Sơ
                                            cấp</a></li>
                                    <li class="w-[25vw] h-[25vw] md:w-1/3 md:h-auto flex-shrink-0">
                                        <a href="https://sach.dungmori.com/"
                                           class="transition-transform hover:scale-105 rounded-full w-full h-full text-center aspect-square md:aspect-auto p-4 border-2 border-dmr-cyan hover:bg-dmr-cyan text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer">Trung
                                            cấp</a></li>
                                    <li class="w-[25vw] h-[25vw] md:w-1/3 md:h-auto flex-shrink-0">
                                        <a href="https://sach.dungmori.com/"
                                           class="transition-transform hover:scale-105 rounded-full w-full h-full text-center aspect-square md:aspect-auto p-4 border-2 border-dmr-cyan hover:bg-dmr-cyan text-dmr-green-dark hover:text-white flex justify-center items-center cursor-pointer">Cao
                                            cấp</a></li>
                                </ul>
                            </div>
                            <div class="font-averta-regular text-xl mt-3 xl:mt-0">
                                Học nhanh, nhớ lâu, hiểu sâu với các đầu sách do DUNGMORI nghiên cứu phát
                                triển. <br class="hidden xl:block"> Giúp bạn tăng gấp 3 hiệu quả học tiếng Nhật
                                trong ngắn hạn!
                            </div>
                        </div>
                        <img src="{{ asset('assets/img/new_home/06-2024/book-banner.png') }}" alt="Kaiwa"
                             class="static xl:absolute right-3 bottom-10 xl:h-[90%]">
                    </div>
                </div>
            </section>
            <div class="wheel my-25 xl:mt-40 xl:mb-60 h-auto xl:h-[80vh] w-full relative z-0">
                <div class="container w-auto xl:!w-[1300px] h-full">
                    <div class="h-full w-full flex flex-col justify-between items-center xl:items-end">
                        <div class="flex flex-col items-end text-right relative z-[2]">
                            <img src="{{ asset('assets/img/new_home/06-2024/discussion.svg') }}"
                                 alt="Cung cấp giáo trình luyện JLPT, Giao tiếp"
                                 class="parallax-bg floating w-10 h-10 absolute top-[90%] lg:top-[70%] left-3 lg:left-[calc(50%-100px)] z-10"
                                 data-speed="0.1">
                            <div class="capitalize text-dmr-green-dark text-[3vw] font-beanbag leading-[1.2]">Cảm nhận
                                <br/> từ học viên
                            </div>
                            <div class="font-averta-regular text-sm md:text-[1vw]">
                                Hãy để lại một bức ảnh, 1 lời nhắn hoặc đôi dòng <br/> tâm sự nào đó, để DUNGMORI lưu
                                giữ giùm bạn
                                <br/> một tuổi trẻ đã từng cố gắng hết mình nhé!
                            </div>
                        </div>
                        <div class="block xl:hidden w-full h-full z-[2] mt-10 mb-15">
                            <div id="feedback-slider">
                                @foreach ($feedbacks as $key => $feedback)
                                    <div class="w-screen !flex flex-col items-center justify-center px-1">
                                        <div class="w-[70%] m-auto aspect-square rounded-full bg-dmr-green-dark z-1 flex items-center justify-center">
                                            <img class="h-full w-full lazyload object-cover rounded-full"
                                                 data-src="{{url('/cdn/feedback/default/' . $feedback->image)}}" alt="">
                                        </div>
                                        <div class="font-beanbag text-dmr-green-dark text-xl">{{$feedback->users['name'] ?? ''}}</div>
                                        <div class="font-averta-regular text-dmr-green-dark text-lg">{{$feedback->user_job ?? ''}}</div>
                                        <div class="w-full p-3 text-center bg-[#57D061] rounded-lg text-white mt-3 font-averta-regular text-sm">{{ $feedback->content }}</div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        <div class="relative z-[2] flex flex-col items-end text-right mb-auto xl:-mb-[200px] px-2 xl:px-0">
                            <p class="font-beanbag text-dmr-green-dark xl:text-[1vw] text-md">
                                Bắt đầu bằng việc học tiếng Nhật, <br class="xl:hidden"> tại DUNGMORI <br
                                        class="hidden xl:inline-block">
                                chúng tôi cùng <br class="xl:hidden"> học trò của mình thắp lên và trao giữ <br>
                                lửa học tập, hướng tới hành trình
                            </p>
                            <h2 class="text-[#EC6E23] font-zuume-semibold text-[2.5vw] mt-4 leading-20">"Học để thay đổi
                                - <br class="xl:hidden"> Học để thành công"</h2>
                            <img src="{{ asset('assets/img/new_home/06-2024/mori-3d.png') }}" alt="Kaiwa"
                                 class="mr-10 mt-5"/>
                        </div>
                    </div>
                </div>
                <img src="{{ asset('assets/img/new_home/06-2024/flower.svg') }}" alt=""
                     class="hidden lg:block floating w-10 h-10 absolute top-[1200px] left-2 z-[2] parallax-bg"
                     data-speed="0.3">
                <img src="{{ asset('assets/img/new_home/06-2024/mori-logo-gray.svg') }}"
                     alt="Cung cấp giáo trình luyện JLPT, Giao tiếp" class="h-[70vh] absolute left-0 top-[5vh] z-0">
                <div class="hidden xl:block h-full w-full absolute top-0 left-0">
                    <div class="wheel-container h-full aspect-square absolute -left-[40vh] top-0">
                        <div class="wheel-wrapper h-full w-full">
                            @foreach ($feedbacks as $key => $feedback)
                                <div class="wheel-item aspect-square relative">
                                    <div class="wheel-avatar aspect-square rounded-full bg-dmr-green-dark z-1">
                                        <img class="h-full w-full lazyload object-cover object center object-center rounded-full"
                                             data-src="{{url('/cdn/feedback/default/' . $feedback->image)}}" alt="">
                                    </div>
                                    <div class="wheel-feedback rounded-md absolute left-[50%] top-0 flex items-center -z-[1]">
                                        <div class="wheel-feedback-container shadow-lg h-[100%] w-full flex flex-col justify-center px-[120px] rounded-lg">
                                            <div class="wheel-feedback-name font-beanbag text-dmr-green-dark text-xl">{{$feedback->users['name'] ?? ''}}</div>
                                            <div class="wheel-feedback-job font-averta-regular text-white text-base">{{$feedback->user_job ?? ''}}</div>
                                            <div class="wheel-feedback-content text-white mt-1 font-averta-regular text-[1vw] text-justify lg:text-left">
                                                {{ $feedback->content }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            <svg viewBox="0 0 300 300" class="wheel-svg">
                                <circle id="holder" class="st0" cx="150" cy="150" r="150" stroke="#07403F"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="slider w-[500vw] md:w-screen !h-[280px] lg:!h-[560px]">
                <div class="slide-track flex gap-5 w-[4000vw] md:w-[2000vw] xl:w-[1000vw]">
                    <div class="slide w-[50vw] md:w-[600px] mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-1.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <div class="mb-[1px] text-white font-zuume-semibold text-[3em] lg:text-[6em] leading-[46px] lg:leading-[92px] bg-dmr-purple rounded-xl aspect-[400:350] p-4 lg:p-8 shadow-lg relative">
                            Khoảnh khắc đáng nhớ
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                 xmlns="http://www.w3.org/2000/svg" class="absolute bottom-3 right-3">
                                <path d="M24.9994 0C11.1866 0 0 11.1866 0 24.9994C0 38.8123 11.1866 49.9988 24.9994 49.9988C38.8123 49.9988 49.9988 38.8123 49.9988 24.9994C49.9988 11.1866 38.7981 0 24.9994 0ZM19.0938 9.45463H19.1364C19.5055 9.35526 19.903 9.39785 20.2295 9.5966C21.9189 10.6329 23.5088 11.797 24.9994 13.0747C26.49 11.7828 28.0942 10.6187 29.7693 9.5966C30.0958 9.39785 30.4933 9.34107 30.8624 9.44044H30.905C32.0975 9.76695 32.3388 11.3711 31.2883 12.0099C29.8119 12.9185 28.4065 13.9264 27.0863 15.0479C26.3622 15.6584 25.6666 16.2972 24.9994 16.9644C24.3322 16.2972 23.6366 15.6584 22.9126 15.0479C21.5923 13.9264 20.1869 12.9185 18.7105 12.0099C17.66 11.3711 17.8871 9.75275 19.0938 9.45463ZM12.1519 12.6914C12.1519 12.6914 12.1661 12.6914 12.1803 12.6772C12.5778 12.3932 13.1172 12.3365 13.5715 12.5352C16.2688 13.7135 18.7673 15.2751 20.9961 17.1489C21.7201 17.7594 22.4299 18.4124 23.0971 19.0796C23.7644 19.761 24.4032 20.4708 25.0136 21.209C25.6241 20.4708 26.2629 19.761 26.9301 19.0796C27.5973 18.3982 28.2929 17.7594 29.0311 17.1489C31.2599 15.2751 33.7584 13.7135 36.4557 12.5352C36.91 12.3365 37.4352 12.379 37.8469 12.6772C37.8469 12.6772 37.8611 12.6772 37.8753 12.6914C38.7697 13.3302 38.6277 14.693 37.634 15.1331C35.1497 16.2262 32.8499 17.66 30.8056 19.3919C30.0674 20.0166 29.3718 20.6696 28.7046 21.3652C28.0232 22.075 27.3844 22.8132 26.7739 23.594C26.1493 24.4032 25.5673 25.2408 25.0278 26.1067C24.4884 25.2408 23.9063 24.389 23.2817 23.594C22.6854 22.8132 22.0324 22.075 21.351 21.3652C20.6838 20.6696 19.9882 20.0166 19.25 19.3919C17.2057 17.66 14.906 16.212 12.4216 15.1331C11.4137 14.693 11.2859 13.3302 12.1803 12.6914H12.1519ZM20.0876 40.5868H18.0859C17.8588 40.5868 17.66 40.4022 17.66 40.1609C17.6032 37.7476 17.007 35.462 16.0133 33.4177C15.4454 32.2537 14.7356 31.1605 13.9122 30.1668C11.7544 27.5547 8.81581 25.6382 5.43713 24.7439C4.64214 24.5309 4.18786 23.6934 4.45759 22.9126V22.8842C4.69893 22.2028 5.46552 21.8053 6.16113 21.9898C9.55401 22.8842 12.5778 24.6729 14.9769 27.1004C15.7435 27.8812 16.4533 28.7188 17.078 29.6274C17.8304 30.7205 18.4834 31.8846 19.0086 33.1196C19.9314 35.2916 20.4566 37.6624 20.5134 40.1467C20.5134 40.388 20.3289 40.5726 20.0876 40.5726V40.5868ZM26.6178 40.1751C26.6178 40.4022 26.419 40.601 26.1919 40.601H23.7927C23.5656 40.601 23.3669 40.4164 23.3669 40.1751C23.3243 37.6482 22.8416 35.2206 22.004 32.9635C21.5356 31.6858 20.9393 30.4507 20.2437 29.3009C19.6759 28.3355 19.0228 27.427 18.313 26.5752C17.66 25.7944 16.9644 25.0562 16.212 24.3606C13.8838 22.2312 11.1156 20.5844 8.03502 19.5907C7.12647 19.2926 6.78576 18.1853 7.3536 17.4187V17.3903C7.7369 16.8934 8.37573 16.6947 8.95777 16.8792C12.1803 17.9297 15.1189 19.6191 17.6316 21.7911C18.3698 22.4299 19.0654 23.1255 19.7326 23.8495C20.4283 24.6019 21.0671 25.4111 21.6633 26.2487C22.3163 27.1714 22.8984 28.1226 23.4236 29.1305C24.0483 30.323 24.5735 31.5722 24.9994 32.8783C25.4253 31.5864 25.9506 30.3372 26.5752 29.1305C27.1004 28.1226 27.6825 27.1714 28.3355 26.2487C28.9318 25.4111 29.5706 24.6019 30.2662 23.8495C30.9192 23.1255 31.629 22.4441 32.3672 21.7911C34.8657 19.6191 37.8043 17.9297 41.0411 16.8792C41.6231 16.6947 42.2619 16.8792 42.631 17.3903V17.4187C43.2131 18.1853 42.8724 19.2926 41.9638 19.5907C38.8974 20.5844 36.115 22.2312 33.7868 24.3606C33.0344 25.042 32.3388 25.7802 31.6858 26.5752C30.976 27.427 30.3372 28.3497 29.7551 29.3009C29.0595 30.4649 28.4775 31.6858 27.9948 32.9635C27.1572 35.2206 26.6888 37.6482 26.632 40.1751H26.6178ZM44.5333 24.7581C41.1688 25.6382 38.216 27.5689 36.0582 30.181C35.2348 31.1747 34.525 32.2678 33.9572 33.4319C32.9493 35.4762 32.3672 37.7618 32.3104 40.1751C32.3104 40.4022 32.1117 40.601 31.8846 40.601H29.8829C29.6416 40.601 29.457 40.4022 29.457 40.1751C29.5138 37.6908 30.0391 35.32 30.9618 33.148C31.4871 31.9129 32.1401 30.7489 32.8925 29.6558C33.5171 28.7614 34.2269 27.9096 34.9935 27.1288C37.3785 24.7013 40.4164 22.9126 43.8093 22.0182C44.5049 21.8337 45.2715 22.2312 45.4987 22.9126V22.941C45.7826 23.7218 45.3283 24.5735 44.5333 24.7723V24.7581Z"
                                      fill="white"/>
                            </svg>
                        </div>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-2.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-3.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-4.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-5.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-6.png') }}" alt="Kaiwa"
                         class="mr-[1px] slide h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-7.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <div class="text-white font-zuume-semibold text-[3em] lg:text-[6em] leading-[46px] lg:leading-[92px] bg-dmr-purple rounded-xl aspect-[400:350] p-4 lg:p-8 shadow-lg relative">
                            Khoảnh khắc đáng nhớ
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                 xmlns="http://www.w3.org/2000/svg" class="absolute bottom-3 right-3">
                                <path d="M24.9994 0C11.1866 0 0 11.1866 0 24.9994C0 38.8123 11.1866 49.9988 24.9994 49.9988C38.8123 49.9988 49.9988 38.8123 49.9988 24.9994C49.9988 11.1866 38.7981 0 24.9994 0ZM19.0938 9.45463H19.1364C19.5055 9.35526 19.903 9.39785 20.2295 9.5966C21.9189 10.6329 23.5088 11.797 24.9994 13.0747C26.49 11.7828 28.0942 10.6187 29.7693 9.5966C30.0958 9.39785 30.4933 9.34107 30.8624 9.44044H30.905C32.0975 9.76695 32.3388 11.3711 31.2883 12.0099C29.8119 12.9185 28.4065 13.9264 27.0863 15.0479C26.3622 15.6584 25.6666 16.2972 24.9994 16.9644C24.3322 16.2972 23.6366 15.6584 22.9126 15.0479C21.5923 13.9264 20.1869 12.9185 18.7105 12.0099C17.66 11.3711 17.8871 9.75275 19.0938 9.45463ZM12.1519 12.6914C12.1519 12.6914 12.1661 12.6914 12.1803 12.6772C12.5778 12.3932 13.1172 12.3365 13.5715 12.5352C16.2688 13.7135 18.7673 15.2751 20.9961 17.1489C21.7201 17.7594 22.4299 18.4124 23.0971 19.0796C23.7644 19.761 24.4032 20.4708 25.0136 21.209C25.6241 20.4708 26.2629 19.761 26.9301 19.0796C27.5973 18.3982 28.2929 17.7594 29.0311 17.1489C31.2599 15.2751 33.7584 13.7135 36.4557 12.5352C36.91 12.3365 37.4352 12.379 37.8469 12.6772C37.8469 12.6772 37.8611 12.6772 37.8753 12.6914C38.7697 13.3302 38.6277 14.693 37.634 15.1331C35.1497 16.2262 32.8499 17.66 30.8056 19.3919C30.0674 20.0166 29.3718 20.6696 28.7046 21.3652C28.0232 22.075 27.3844 22.8132 26.7739 23.594C26.1493 24.4032 25.5673 25.2408 25.0278 26.1067C24.4884 25.2408 23.9063 24.389 23.2817 23.594C22.6854 22.8132 22.0324 22.075 21.351 21.3652C20.6838 20.6696 19.9882 20.0166 19.25 19.3919C17.2057 17.66 14.906 16.212 12.4216 15.1331C11.4137 14.693 11.2859 13.3302 12.1803 12.6914H12.1519ZM20.0876 40.5868H18.0859C17.8588 40.5868 17.66 40.4022 17.66 40.1609C17.6032 37.7476 17.007 35.462 16.0133 33.4177C15.4454 32.2537 14.7356 31.1605 13.9122 30.1668C11.7544 27.5547 8.81581 25.6382 5.43713 24.7439C4.64214 24.5309 4.18786 23.6934 4.45759 22.9126V22.8842C4.69893 22.2028 5.46552 21.8053 6.16113 21.9898C9.55401 22.8842 12.5778 24.6729 14.9769 27.1004C15.7435 27.8812 16.4533 28.7188 17.078 29.6274C17.8304 30.7205 18.4834 31.8846 19.0086 33.1196C19.9314 35.2916 20.4566 37.6624 20.5134 40.1467C20.5134 40.388 20.3289 40.5726 20.0876 40.5726V40.5868ZM26.6178 40.1751C26.6178 40.4022 26.419 40.601 26.1919 40.601H23.7927C23.5656 40.601 23.3669 40.4164 23.3669 40.1751C23.3243 37.6482 22.8416 35.2206 22.004 32.9635C21.5356 31.6858 20.9393 30.4507 20.2437 29.3009C19.6759 28.3355 19.0228 27.427 18.313 26.5752C17.66 25.7944 16.9644 25.0562 16.212 24.3606C13.8838 22.2312 11.1156 20.5844 8.03502 19.5907C7.12647 19.2926 6.78576 18.1853 7.3536 17.4187V17.3903C7.7369 16.8934 8.37573 16.6947 8.95777 16.8792C12.1803 17.9297 15.1189 19.6191 17.6316 21.7911C18.3698 22.4299 19.0654 23.1255 19.7326 23.8495C20.4283 24.6019 21.0671 25.4111 21.6633 26.2487C22.3163 27.1714 22.8984 28.1226 23.4236 29.1305C24.0483 30.323 24.5735 31.5722 24.9994 32.8783C25.4253 31.5864 25.9506 30.3372 26.5752 29.1305C27.1004 28.1226 27.6825 27.1714 28.3355 26.2487C28.9318 25.4111 29.5706 24.6019 30.2662 23.8495C30.9192 23.1255 31.629 22.4441 32.3672 21.7911C34.8657 19.6191 37.8043 17.9297 41.0411 16.8792C41.6231 16.6947 42.2619 16.8792 42.631 17.3903V17.4187C43.2131 18.1853 42.8724 19.2926 41.9638 19.5907C38.8974 20.5844 36.115 22.2312 33.7868 24.3606C33.0344 25.042 32.3388 25.7802 31.6858 26.5752C30.976 27.427 30.3372 28.3497 29.7551 29.3009C29.0595 30.4649 28.4775 31.6858 27.9948 32.9635C27.1572 35.2206 26.6888 37.6482 26.632 40.1751H26.6178ZM44.5333 24.7581C41.1688 25.6382 38.216 27.5689 36.0582 30.181C35.2348 31.1747 34.525 32.2678 33.9572 33.4319C32.9493 35.4762 32.3672 37.7618 32.3104 40.1751C32.3104 40.4022 32.1117 40.601 31.8846 40.601H29.8829C29.6416 40.601 29.457 40.4022 29.457 40.1751C29.5138 37.6908 30.0391 35.32 30.9618 33.148C31.4871 31.9129 32.1401 30.7489 32.8925 29.6558C33.5171 28.7614 34.2269 27.9096 34.9935 27.1288C37.3785 24.7013 40.4164 22.9126 43.8093 22.0182C44.5049 21.8337 45.2715 22.2312 45.4987 22.9126V22.941C45.7826 23.7218 45.3283 24.5735 44.5333 24.7723V24.7581Z"
                                      fill="white"/>
                            </svg>
                        </div>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-8.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-9.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-10.png') }}" alt="Kaiwa"
                         class="mr-[1px] slide h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-11.png') }}" alt="Kaiwa"
                         class="mr-[1px] slide h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    <div class="slide w-screen md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <div class="text-white mb-[1px] font-zuume-semibold text-[3em] lg:text-[6em] leading-[46px] lg:leading-[92px] bg-dmr-purple rounded-xl aspect-[400:350] p-4 lg:p-8 shadow-lg relative">
                            Khoảnh khắc đáng nhớ
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                 xmlns="http://www.w3.org/2000/svg" class="absolute bottom-3 right-3">
                                <path d="M24.9994 0C11.1866 0 0 11.1866 0 24.9994C0 38.8123 11.1866 49.9988 24.9994 49.9988C38.8123 49.9988 49.9988 38.8123 49.9988 24.9994C49.9988 11.1866 38.7981 0 24.9994 0ZM19.0938 9.45463H19.1364C19.5055 9.35526 19.903 9.39785 20.2295 9.5966C21.9189 10.6329 23.5088 11.797 24.9994 13.0747C26.49 11.7828 28.0942 10.6187 29.7693 9.5966C30.0958 9.39785 30.4933 9.34107 30.8624 9.44044H30.905C32.0975 9.76695 32.3388 11.3711 31.2883 12.0099C29.8119 12.9185 28.4065 13.9264 27.0863 15.0479C26.3622 15.6584 25.6666 16.2972 24.9994 16.9644C24.3322 16.2972 23.6366 15.6584 22.9126 15.0479C21.5923 13.9264 20.1869 12.9185 18.7105 12.0099C17.66 11.3711 17.8871 9.75275 19.0938 9.45463ZM12.1519 12.6914C12.1519 12.6914 12.1661 12.6914 12.1803 12.6772C12.5778 12.3932 13.1172 12.3365 13.5715 12.5352C16.2688 13.7135 18.7673 15.2751 20.9961 17.1489C21.7201 17.7594 22.4299 18.4124 23.0971 19.0796C23.7644 19.761 24.4032 20.4708 25.0136 21.209C25.6241 20.4708 26.2629 19.761 26.9301 19.0796C27.5973 18.3982 28.2929 17.7594 29.0311 17.1489C31.2599 15.2751 33.7584 13.7135 36.4557 12.5352C36.91 12.3365 37.4352 12.379 37.8469 12.6772C37.8469 12.6772 37.8611 12.6772 37.8753 12.6914C38.7697 13.3302 38.6277 14.693 37.634 15.1331C35.1497 16.2262 32.8499 17.66 30.8056 19.3919C30.0674 20.0166 29.3718 20.6696 28.7046 21.3652C28.0232 22.075 27.3844 22.8132 26.7739 23.594C26.1493 24.4032 25.5673 25.2408 25.0278 26.1067C24.4884 25.2408 23.9063 24.389 23.2817 23.594C22.6854 22.8132 22.0324 22.075 21.351 21.3652C20.6838 20.6696 19.9882 20.0166 19.25 19.3919C17.2057 17.66 14.906 16.212 12.4216 15.1331C11.4137 14.693 11.2859 13.3302 12.1803 12.6914H12.1519ZM20.0876 40.5868H18.0859C17.8588 40.5868 17.66 40.4022 17.66 40.1609C17.6032 37.7476 17.007 35.462 16.0133 33.4177C15.4454 32.2537 14.7356 31.1605 13.9122 30.1668C11.7544 27.5547 8.81581 25.6382 5.43713 24.7439C4.64214 24.5309 4.18786 23.6934 4.45759 22.9126V22.8842C4.69893 22.2028 5.46552 21.8053 6.16113 21.9898C9.55401 22.8842 12.5778 24.6729 14.9769 27.1004C15.7435 27.8812 16.4533 28.7188 17.078 29.6274C17.8304 30.7205 18.4834 31.8846 19.0086 33.1196C19.9314 35.2916 20.4566 37.6624 20.5134 40.1467C20.5134 40.388 20.3289 40.5726 20.0876 40.5726V40.5868ZM26.6178 40.1751C26.6178 40.4022 26.419 40.601 26.1919 40.601H23.7927C23.5656 40.601 23.3669 40.4164 23.3669 40.1751C23.3243 37.6482 22.8416 35.2206 22.004 32.9635C21.5356 31.6858 20.9393 30.4507 20.2437 29.3009C19.6759 28.3355 19.0228 27.427 18.313 26.5752C17.66 25.7944 16.9644 25.0562 16.212 24.3606C13.8838 22.2312 11.1156 20.5844 8.03502 19.5907C7.12647 19.2926 6.78576 18.1853 7.3536 17.4187V17.3903C7.7369 16.8934 8.37573 16.6947 8.95777 16.8792C12.1803 17.9297 15.1189 19.6191 17.6316 21.7911C18.3698 22.4299 19.0654 23.1255 19.7326 23.8495C20.4283 24.6019 21.0671 25.4111 21.6633 26.2487C22.3163 27.1714 22.8984 28.1226 23.4236 29.1305C24.0483 30.323 24.5735 31.5722 24.9994 32.8783C25.4253 31.5864 25.9506 30.3372 26.5752 29.1305C27.1004 28.1226 27.6825 27.1714 28.3355 26.2487C28.9318 25.4111 29.5706 24.6019 30.2662 23.8495C30.9192 23.1255 31.629 22.4441 32.3672 21.7911C34.8657 19.6191 37.8043 17.9297 41.0411 16.8792C41.6231 16.6947 42.2619 16.8792 42.631 17.3903V17.4187C43.2131 18.1853 42.8724 19.2926 41.9638 19.5907C38.8974 20.5844 36.115 22.2312 33.7868 24.3606C33.0344 25.042 32.3388 25.7802 31.6858 26.5752C30.976 27.427 30.3372 28.3497 29.7551 29.3009C29.0595 30.4649 28.4775 31.6858 27.9948 32.9635C27.1572 35.2206 26.6888 37.6482 26.632 40.1751H26.6178ZM44.5333 24.7581C41.1688 25.6382 38.216 27.5689 36.0582 30.181C35.2348 31.1747 34.525 32.2678 33.9572 33.4319C32.9493 35.4762 32.3672 37.7618 32.3104 40.1751C32.3104 40.4022 32.1117 40.601 31.8846 40.601H29.8829C29.6416 40.601 29.457 40.4022 29.457 40.1751C29.5138 37.6908 30.0391 35.32 30.9618 33.148C31.4871 31.9129 32.1401 30.7489 32.8925 29.6558C33.5171 28.7614 34.2269 27.9096 34.9935 27.1288C37.3785 24.7013 40.4164 22.9126 43.8093 22.0182C44.5049 21.8337 45.2715 22.2312 45.4987 22.9126V22.941C45.7826 23.7218 45.3283 24.5735 44.5333 24.7723V24.7581Z"
                                      fill="white"/>
                            </svg>
                        </div>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-12.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-13.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-14.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-15.png') }}" alt="Kaiwa"
                         class="slide mr-[1px] h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    <div class="slide w-[50vw] md:w-[600px] mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-1.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <div class="text-white mb-[1px] font-zuume-semibold text-[3em] lg:text-[6em] leading-[46px] lg:leading-[92px] bg-dmr-purple rounded-xl aspect-[400:350] p-4 lg:p-8 shadow-lg relative">
                            Khoảnh khắc đáng nhớ
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                 xmlns="http://www.w3.org/2000/svg" class="absolute bottom-3 right-3">
                                <path d="M24.9994 0C11.1866 0 0 11.1866 0 24.9994C0 38.8123 11.1866 49.9988 24.9994 49.9988C38.8123 49.9988 49.9988 38.8123 49.9988 24.9994C49.9988 11.1866 38.7981 0 24.9994 0ZM19.0938 9.45463H19.1364C19.5055 9.35526 19.903 9.39785 20.2295 9.5966C21.9189 10.6329 23.5088 11.797 24.9994 13.0747C26.49 11.7828 28.0942 10.6187 29.7693 9.5966C30.0958 9.39785 30.4933 9.34107 30.8624 9.44044H30.905C32.0975 9.76695 32.3388 11.3711 31.2883 12.0099C29.8119 12.9185 28.4065 13.9264 27.0863 15.0479C26.3622 15.6584 25.6666 16.2972 24.9994 16.9644C24.3322 16.2972 23.6366 15.6584 22.9126 15.0479C21.5923 13.9264 20.1869 12.9185 18.7105 12.0099C17.66 11.3711 17.8871 9.75275 19.0938 9.45463ZM12.1519 12.6914C12.1519 12.6914 12.1661 12.6914 12.1803 12.6772C12.5778 12.3932 13.1172 12.3365 13.5715 12.5352C16.2688 13.7135 18.7673 15.2751 20.9961 17.1489C21.7201 17.7594 22.4299 18.4124 23.0971 19.0796C23.7644 19.761 24.4032 20.4708 25.0136 21.209C25.6241 20.4708 26.2629 19.761 26.9301 19.0796C27.5973 18.3982 28.2929 17.7594 29.0311 17.1489C31.2599 15.2751 33.7584 13.7135 36.4557 12.5352C36.91 12.3365 37.4352 12.379 37.8469 12.6772C37.8469 12.6772 37.8611 12.6772 37.8753 12.6914C38.7697 13.3302 38.6277 14.693 37.634 15.1331C35.1497 16.2262 32.8499 17.66 30.8056 19.3919C30.0674 20.0166 29.3718 20.6696 28.7046 21.3652C28.0232 22.075 27.3844 22.8132 26.7739 23.594C26.1493 24.4032 25.5673 25.2408 25.0278 26.1067C24.4884 25.2408 23.9063 24.389 23.2817 23.594C22.6854 22.8132 22.0324 22.075 21.351 21.3652C20.6838 20.6696 19.9882 20.0166 19.25 19.3919C17.2057 17.66 14.906 16.212 12.4216 15.1331C11.4137 14.693 11.2859 13.3302 12.1803 12.6914H12.1519ZM20.0876 40.5868H18.0859C17.8588 40.5868 17.66 40.4022 17.66 40.1609C17.6032 37.7476 17.007 35.462 16.0133 33.4177C15.4454 32.2537 14.7356 31.1605 13.9122 30.1668C11.7544 27.5547 8.81581 25.6382 5.43713 24.7439C4.64214 24.5309 4.18786 23.6934 4.45759 22.9126V22.8842C4.69893 22.2028 5.46552 21.8053 6.16113 21.9898C9.55401 22.8842 12.5778 24.6729 14.9769 27.1004C15.7435 27.8812 16.4533 28.7188 17.078 29.6274C17.8304 30.7205 18.4834 31.8846 19.0086 33.1196C19.9314 35.2916 20.4566 37.6624 20.5134 40.1467C20.5134 40.388 20.3289 40.5726 20.0876 40.5726V40.5868ZM26.6178 40.1751C26.6178 40.4022 26.419 40.601 26.1919 40.601H23.7927C23.5656 40.601 23.3669 40.4164 23.3669 40.1751C23.3243 37.6482 22.8416 35.2206 22.004 32.9635C21.5356 31.6858 20.9393 30.4507 20.2437 29.3009C19.6759 28.3355 19.0228 27.427 18.313 26.5752C17.66 25.7944 16.9644 25.0562 16.212 24.3606C13.8838 22.2312 11.1156 20.5844 8.03502 19.5907C7.12647 19.2926 6.78576 18.1853 7.3536 17.4187V17.3903C7.7369 16.8934 8.37573 16.6947 8.95777 16.8792C12.1803 17.9297 15.1189 19.6191 17.6316 21.7911C18.3698 22.4299 19.0654 23.1255 19.7326 23.8495C20.4283 24.6019 21.0671 25.4111 21.6633 26.2487C22.3163 27.1714 22.8984 28.1226 23.4236 29.1305C24.0483 30.323 24.5735 31.5722 24.9994 32.8783C25.4253 31.5864 25.9506 30.3372 26.5752 29.1305C27.1004 28.1226 27.6825 27.1714 28.3355 26.2487C28.9318 25.4111 29.5706 24.6019 30.2662 23.8495C30.9192 23.1255 31.629 22.4441 32.3672 21.7911C34.8657 19.6191 37.8043 17.9297 41.0411 16.8792C41.6231 16.6947 42.2619 16.8792 42.631 17.3903V17.4187C43.2131 18.1853 42.8724 19.2926 41.9638 19.5907C38.8974 20.5844 36.115 22.2312 33.7868 24.3606C33.0344 25.042 32.3388 25.7802 31.6858 26.5752C30.976 27.427 30.3372 28.3497 29.7551 29.3009C29.0595 30.4649 28.4775 31.6858 27.9948 32.9635C27.1572 35.2206 26.6888 37.6482 26.632 40.1751H26.6178ZM44.5333 24.7581C41.1688 25.6382 38.216 27.5689 36.0582 30.181C35.2348 31.1747 34.525 32.2678 33.9572 33.4319C32.9493 35.4762 32.3672 37.7618 32.3104 40.1751C32.3104 40.4022 32.1117 40.601 31.8846 40.601H29.8829C29.6416 40.601 29.457 40.4022 29.457 40.1751C29.5138 37.6908 30.0391 35.32 30.9618 33.148C31.4871 31.9129 32.1401 30.7489 32.8925 29.6558C33.5171 28.7614 34.2269 27.9096 34.9935 27.1288C37.3785 24.7013 40.4164 22.9126 43.8093 22.0182C44.5049 21.8337 45.2715 22.2312 45.4987 22.9126V22.941C45.7826 23.7218 45.3283 24.5735 44.5333 24.7723V24.7581Z"
                                      fill="white"/>
                            </svg>
                        </div>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-2.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-3.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-4.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-5.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-6.png') }}" alt="Kaiwa"
                         class="slide mr-[1px] h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-7.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <div class="text-white font-zuume-semibold text-[3em] lg:text-[6em] leading-[46px] lg:leading-[92px] bg-dmr-purple rounded-xl aspect-[400:350] p-4 lg:p-8 shadow-lg relative">
                            Khoảnh khắc đáng nhớ
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                 xmlns="http://www.w3.org/2000/svg" class="absolute bottom-3 right-3">
                                <path d="M24.9994 0C11.1866 0 0 11.1866 0 24.9994C0 38.8123 11.1866 49.9988 24.9994 49.9988C38.8123 49.9988 49.9988 38.8123 49.9988 24.9994C49.9988 11.1866 38.7981 0 24.9994 0ZM19.0938 9.45463H19.1364C19.5055 9.35526 19.903 9.39785 20.2295 9.5966C21.9189 10.6329 23.5088 11.797 24.9994 13.0747C26.49 11.7828 28.0942 10.6187 29.7693 9.5966C30.0958 9.39785 30.4933 9.34107 30.8624 9.44044H30.905C32.0975 9.76695 32.3388 11.3711 31.2883 12.0099C29.8119 12.9185 28.4065 13.9264 27.0863 15.0479C26.3622 15.6584 25.6666 16.2972 24.9994 16.9644C24.3322 16.2972 23.6366 15.6584 22.9126 15.0479C21.5923 13.9264 20.1869 12.9185 18.7105 12.0099C17.66 11.3711 17.8871 9.75275 19.0938 9.45463ZM12.1519 12.6914C12.1519 12.6914 12.1661 12.6914 12.1803 12.6772C12.5778 12.3932 13.1172 12.3365 13.5715 12.5352C16.2688 13.7135 18.7673 15.2751 20.9961 17.1489C21.7201 17.7594 22.4299 18.4124 23.0971 19.0796C23.7644 19.761 24.4032 20.4708 25.0136 21.209C25.6241 20.4708 26.2629 19.761 26.9301 19.0796C27.5973 18.3982 28.2929 17.7594 29.0311 17.1489C31.2599 15.2751 33.7584 13.7135 36.4557 12.5352C36.91 12.3365 37.4352 12.379 37.8469 12.6772C37.8469 12.6772 37.8611 12.6772 37.8753 12.6914C38.7697 13.3302 38.6277 14.693 37.634 15.1331C35.1497 16.2262 32.8499 17.66 30.8056 19.3919C30.0674 20.0166 29.3718 20.6696 28.7046 21.3652C28.0232 22.075 27.3844 22.8132 26.7739 23.594C26.1493 24.4032 25.5673 25.2408 25.0278 26.1067C24.4884 25.2408 23.9063 24.389 23.2817 23.594C22.6854 22.8132 22.0324 22.075 21.351 21.3652C20.6838 20.6696 19.9882 20.0166 19.25 19.3919C17.2057 17.66 14.906 16.212 12.4216 15.1331C11.4137 14.693 11.2859 13.3302 12.1803 12.6914H12.1519ZM20.0876 40.5868H18.0859C17.8588 40.5868 17.66 40.4022 17.66 40.1609C17.6032 37.7476 17.007 35.462 16.0133 33.4177C15.4454 32.2537 14.7356 31.1605 13.9122 30.1668C11.7544 27.5547 8.81581 25.6382 5.43713 24.7439C4.64214 24.5309 4.18786 23.6934 4.45759 22.9126V22.8842C4.69893 22.2028 5.46552 21.8053 6.16113 21.9898C9.55401 22.8842 12.5778 24.6729 14.9769 27.1004C15.7435 27.8812 16.4533 28.7188 17.078 29.6274C17.8304 30.7205 18.4834 31.8846 19.0086 33.1196C19.9314 35.2916 20.4566 37.6624 20.5134 40.1467C20.5134 40.388 20.3289 40.5726 20.0876 40.5726V40.5868ZM26.6178 40.1751C26.6178 40.4022 26.419 40.601 26.1919 40.601H23.7927C23.5656 40.601 23.3669 40.4164 23.3669 40.1751C23.3243 37.6482 22.8416 35.2206 22.004 32.9635C21.5356 31.6858 20.9393 30.4507 20.2437 29.3009C19.6759 28.3355 19.0228 27.427 18.313 26.5752C17.66 25.7944 16.9644 25.0562 16.212 24.3606C13.8838 22.2312 11.1156 20.5844 8.03502 19.5907C7.12647 19.2926 6.78576 18.1853 7.3536 17.4187V17.3903C7.7369 16.8934 8.37573 16.6947 8.95777 16.8792C12.1803 17.9297 15.1189 19.6191 17.6316 21.7911C18.3698 22.4299 19.0654 23.1255 19.7326 23.8495C20.4283 24.6019 21.0671 25.4111 21.6633 26.2487C22.3163 27.1714 22.8984 28.1226 23.4236 29.1305C24.0483 30.323 24.5735 31.5722 24.9994 32.8783C25.4253 31.5864 25.9506 30.3372 26.5752 29.1305C27.1004 28.1226 27.6825 27.1714 28.3355 26.2487C28.9318 25.4111 29.5706 24.6019 30.2662 23.8495C30.9192 23.1255 31.629 22.4441 32.3672 21.7911C34.8657 19.6191 37.8043 17.9297 41.0411 16.8792C41.6231 16.6947 42.2619 16.8792 42.631 17.3903V17.4187C43.2131 18.1853 42.8724 19.2926 41.9638 19.5907C38.8974 20.5844 36.115 22.2312 33.7868 24.3606C33.0344 25.042 32.3388 25.7802 31.6858 26.5752C30.976 27.427 30.3372 28.3497 29.7551 29.3009C29.0595 30.4649 28.4775 31.6858 27.9948 32.9635C27.1572 35.2206 26.6888 37.6482 26.632 40.1751H26.6178ZM44.5333 24.7581C41.1688 25.6382 38.216 27.5689 36.0582 30.181C35.2348 31.1747 34.525 32.2678 33.9572 33.4319C32.9493 35.4762 32.3672 37.7618 32.3104 40.1751C32.3104 40.4022 32.1117 40.601 31.8846 40.601H29.8829C29.6416 40.601 29.457 40.4022 29.457 40.1751C29.5138 37.6908 30.0391 35.32 30.9618 33.148C31.4871 31.9129 32.1401 30.7489 32.8925 29.6558C33.5171 28.7614 34.2269 27.9096 34.9935 27.1288C37.3785 24.7013 40.4164 22.9126 43.8093 22.0182C44.5049 21.8337 45.2715 22.2312 45.4987 22.9126V22.941C45.7826 23.7218 45.3283 24.5735 44.5333 24.7723V24.7581Z"
                                      fill="white"/>
                            </svg>
                        </div>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-8.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-9.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-10.png') }}" alt="Kaiwa"
                         class="slide mr-[1px] h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-11.png') }}" alt="Kaiwa"
                         class="slide mr-[1px] h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <div class="text-white mb-[1px] font-zuume-semibold text-[3em] lg:text-[6em] leading-[46px] lg:leading-[92px] bg-dmr-purple rounded-xl aspect-[400:350] p-4 lg:p-8 shadow-lg relative">
                            Khoảnh khắc đáng nhớ
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none"
                                 xmlns="http://www.w3.org/2000/svg" class="absolute bottom-3 right-3">
                                <path d="M24.9994 0C11.1866 0 0 11.1866 0 24.9994C0 38.8123 11.1866 49.9988 24.9994 49.9988C38.8123 49.9988 49.9988 38.8123 49.9988 24.9994C49.9988 11.1866 38.7981 0 24.9994 0ZM19.0938 9.45463H19.1364C19.5055 9.35526 19.903 9.39785 20.2295 9.5966C21.9189 10.6329 23.5088 11.797 24.9994 13.0747C26.49 11.7828 28.0942 10.6187 29.7693 9.5966C30.0958 9.39785 30.4933 9.34107 30.8624 9.44044H30.905C32.0975 9.76695 32.3388 11.3711 31.2883 12.0099C29.8119 12.9185 28.4065 13.9264 27.0863 15.0479C26.3622 15.6584 25.6666 16.2972 24.9994 16.9644C24.3322 16.2972 23.6366 15.6584 22.9126 15.0479C21.5923 13.9264 20.1869 12.9185 18.7105 12.0099C17.66 11.3711 17.8871 9.75275 19.0938 9.45463ZM12.1519 12.6914C12.1519 12.6914 12.1661 12.6914 12.1803 12.6772C12.5778 12.3932 13.1172 12.3365 13.5715 12.5352C16.2688 13.7135 18.7673 15.2751 20.9961 17.1489C21.7201 17.7594 22.4299 18.4124 23.0971 19.0796C23.7644 19.761 24.4032 20.4708 25.0136 21.209C25.6241 20.4708 26.2629 19.761 26.9301 19.0796C27.5973 18.3982 28.2929 17.7594 29.0311 17.1489C31.2599 15.2751 33.7584 13.7135 36.4557 12.5352C36.91 12.3365 37.4352 12.379 37.8469 12.6772C37.8469 12.6772 37.8611 12.6772 37.8753 12.6914C38.7697 13.3302 38.6277 14.693 37.634 15.1331C35.1497 16.2262 32.8499 17.66 30.8056 19.3919C30.0674 20.0166 29.3718 20.6696 28.7046 21.3652C28.0232 22.075 27.3844 22.8132 26.7739 23.594C26.1493 24.4032 25.5673 25.2408 25.0278 26.1067C24.4884 25.2408 23.9063 24.389 23.2817 23.594C22.6854 22.8132 22.0324 22.075 21.351 21.3652C20.6838 20.6696 19.9882 20.0166 19.25 19.3919C17.2057 17.66 14.906 16.212 12.4216 15.1331C11.4137 14.693 11.2859 13.3302 12.1803 12.6914H12.1519ZM20.0876 40.5868H18.0859C17.8588 40.5868 17.66 40.4022 17.66 40.1609C17.6032 37.7476 17.007 35.462 16.0133 33.4177C15.4454 32.2537 14.7356 31.1605 13.9122 30.1668C11.7544 27.5547 8.81581 25.6382 5.43713 24.7439C4.64214 24.5309 4.18786 23.6934 4.45759 22.9126V22.8842C4.69893 22.2028 5.46552 21.8053 6.16113 21.9898C9.55401 22.8842 12.5778 24.6729 14.9769 27.1004C15.7435 27.8812 16.4533 28.7188 17.078 29.6274C17.8304 30.7205 18.4834 31.8846 19.0086 33.1196C19.9314 35.2916 20.4566 37.6624 20.5134 40.1467C20.5134 40.388 20.3289 40.5726 20.0876 40.5726V40.5868ZM26.6178 40.1751C26.6178 40.4022 26.419 40.601 26.1919 40.601H23.7927C23.5656 40.601 23.3669 40.4164 23.3669 40.1751C23.3243 37.6482 22.8416 35.2206 22.004 32.9635C21.5356 31.6858 20.9393 30.4507 20.2437 29.3009C19.6759 28.3355 19.0228 27.427 18.313 26.5752C17.66 25.7944 16.9644 25.0562 16.212 24.3606C13.8838 22.2312 11.1156 20.5844 8.03502 19.5907C7.12647 19.2926 6.78576 18.1853 7.3536 17.4187V17.3903C7.7369 16.8934 8.37573 16.6947 8.95777 16.8792C12.1803 17.9297 15.1189 19.6191 17.6316 21.7911C18.3698 22.4299 19.0654 23.1255 19.7326 23.8495C20.4283 24.6019 21.0671 25.4111 21.6633 26.2487C22.3163 27.1714 22.8984 28.1226 23.4236 29.1305C24.0483 30.323 24.5735 31.5722 24.9994 32.8783C25.4253 31.5864 25.9506 30.3372 26.5752 29.1305C27.1004 28.1226 27.6825 27.1714 28.3355 26.2487C28.9318 25.4111 29.5706 24.6019 30.2662 23.8495C30.9192 23.1255 31.629 22.4441 32.3672 21.7911C34.8657 19.6191 37.8043 17.9297 41.0411 16.8792C41.6231 16.6947 42.2619 16.8792 42.631 17.3903V17.4187C43.2131 18.1853 42.8724 19.2926 41.9638 19.5907C38.8974 20.5844 36.115 22.2312 33.7868 24.3606C33.0344 25.042 32.3388 25.7802 31.6858 26.5752C30.976 27.427 30.3372 28.3497 29.7551 29.3009C29.0595 30.4649 28.4775 31.6858 27.9948 32.9635C27.1572 35.2206 26.6888 37.6482 26.632 40.1751H26.6178ZM44.5333 24.7581C41.1688 25.6382 38.216 27.5689 36.0582 30.181C35.2348 31.1747 34.525 32.2678 33.9572 33.4319C32.9493 35.4762 32.3672 37.7618 32.3104 40.1751C32.3104 40.4022 32.1117 40.601 31.8846 40.601H29.8829C29.6416 40.601 29.457 40.4022 29.457 40.1751C29.5138 37.6908 30.0391 35.32 30.9618 33.148C31.4871 31.9129 32.1401 30.7489 32.8925 29.6558C33.5171 28.7614 34.2269 27.9096 34.9935 27.1288C37.3785 24.7013 40.4164 22.9126 43.8093 22.0182C44.5049 21.8337 45.2715 22.2312 45.4987 22.9126V22.941C45.7826 23.7218 45.3283 24.5735 44.5333 24.7723V24.7581Z"
                                      fill="white"/>
                            </svg>
                        </div>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-12.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <div class="slide w-[50vw] md:w-[400px] flex flex-col gap-4 mr-[1px]">
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-13.png') }}" alt="Kaiwa"
                             class="h-full mb-[1px] object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                        <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-14.png') }}" alt="Kaiwa"
                             class="h-full object-cover rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                    </div>
                    <img src="{{ asset('assets/img/new_home/06-2024/dungmori-carousel-15.png') }}" alt="Kaiwa"
                         class="slide h-full rounded-xl shadow-lg transition hover:shadow-xl transition hover:brightness-105"/>
                </div>
            </div>
            <div class="home-stats w-screen h-auto xl:h-[600px] mt-5 bg-cover bg-center bg-no-repeat flex items-center relative py-20">
                <img src="{{ asset('assets/img/new_home/06-2024/discussion.svg') }}" alt=""
                     class="parallax-bg floating w-10 h-10 absolute top-[770px] xl:top-[800px] left-[10px] lg:left-[250px] z-[2]"
                     data-speed="0.15">
                <img src="{{ asset('assets/img/new_home/06-2024/plane.svg') }}" alt=""
                     class="parallax-bg floating w-10 h-10 absolute top-[600px] xl:top-[500px] left-[120px] xl:left-[480px] z-[2]"
                     data-speed="0.2">
                <img src="{{ asset('assets/img/new_home/06-2024/play.svg') }}" alt=""
                     class="parallax-bg floating w-10 h-10 absolute bottom-[700px] xl:-bottom-[740px] left-[600px] z-[2]"
                     data-speed="0.35">
                <img src="{{ asset('assets/img/new_home/06-2024/bell.svg') }}" alt=""
                     class="parallax-bg floating w-10 h-10 absolute bottom-[0px] right-[100px] xl:right-[300px] z-[2]"
                     data-speed="0.1">
                <img src="{{ asset('assets/img/new_home/06-2024/flower.svg') }}" alt=""
                     class="parallax-bg floating w-10 h-10 absolute right-0 top-[1400px] xl:right-[210px] z-0 xl:z-[2]"
                     data-speed="0.45">

                <div class="w-full h-full xl:w-[75%] m-auto flex flex-col xl:flex-row items-center gap-3 px-3 xl:px-0 stat-wrapper">
                    <div class="w-full h-auto xl:h-full xl:w-1/4 flex flex-row xl:flex-col items-center gap-2 order-2 xl:order-1 -mt-14 xl:mt-0">
                        <div class="w-[40%] xl:w-auto stat-item  h-[50%] font-zuume-semibold text-[20vw] xl:text-[130px] text-dmr-green-dark leading-0 order-2 xl:order-1">
                            <span class="stat-number -ml-0 xl:-ml-12">99</span>%
                        </div>
                        <div class="w-[60%] xl:w-full h-auto xl:h-[50%] relative order-1 xl:order-2">
                            <img src="{{ asset('assets/img/new_home/06-2024/nghia.png') }}" alt="Kaiwa"
                                 class="hidden lg:block stat-item w-full -top-[15%] xl:absolute"/>
                            <img src="{{ asset('assets/img/new_home/06-2024/nghia-mobile.png') }}" alt="Kaiwa"
                                 class="lg:hidden stat-item w-full -top-[15%] xl:absolute"/>
                        </div>
                    </div>
                    <div class="w-full h-auto xl:h-full xl:w-1/4 flex flex-row xl:flex-col items-center text-center order-1 xl:order-2">
                        <div class="w-[46%] xl:w-full text-center stat-item h-auto xl:h-[50%] flex items-center justify-center font-zuume-semibold text-[19vw] xl:text-[125px] text-dmr-green-dark order-1 xl:order-2 stat-number pt-0 xl:pt-16 pl-0 xl:-ml-20">
                            <span>{{ $countStudents > 100000 ? number_format($countStudents, 0, '', '.') : '514.408' }}</span>
                        </div>
                        <div class="w-[54%] xl:w-full h-auto xl:h-[50%] order-2 xl:order-1 relative">
                            <img src="{{ asset('assets/img/new_home/06-2024/thanh.png') }}" alt="Kaiwa"
                                 class="hidden lg:block stat-item w-full static xl:absolute -bottom-[24%] -left-[13%]"/>
                            <img src="{{ asset('assets/img/new_home/06-2024/thanh-mobile.png') }}" alt="Kaiwa"
                                 class="lg:hidden stat-item w-full static xl:absolute -bottom-[24%] -left-[13%]"/>
                        </div>
                    </div>
                    <div class="w-full h-auto xl:h-full xl:w-1/4 flex flex-row xl:flex-col items-center order-4 xl:order-3 -mt-14 z-[2]">
                        <div class="w-[42%] xl:w-full stat-item h-auto xl:h-[50%] xl:mt-0 mt-16 xl:ml-0 -ml-2 font-zuume-semibold text-[17vw] xl:text-[125px] text-dmr-green-dark stat-number pl-0 xl:pl-5 pt-0 xl:pt-6 order-2 xl:order-1 z-[1]">{{ $countStudents > 100000 ? number_format($countStudents * 70 / 100, 0, '', '.') : '359.834' }}</div>
                        <div class="w-[58%] xl:w-full h-auto xl:h-[50%] relative order-1 xl:order-2 z-[2]">
                            <img src="{{ asset('assets/img/new_home/06-2024/passrate.png') }}" alt="Kaiwa"
                                 class="hidden lg:block stat-item w-full xl:w-[130%] max-w-[130%] xl:absolute -top-[20%] -left-[2%] z-[2]"/>
                            <img src="{{ asset('assets/img/new_home/06-2024/passratemobile.png') }}" alt="Kaiwa"
                                 class="lg:hidden stat-item w-full xl:w-[130%] max-w-[130%] xl:absolute -top-[20%] -left-[2%] z-[2]"/>
                        </div>
                    </div>
                    <div class="w-full h-auto xl:h-full xl:w-1/4 flex flex-row xl:flex-col items-center relative order-3 xl:order-4">
                        <img src="{{ asset('assets/img/new_home/06-2024/heart.svg') }}" alt=""
                             class="floating w-10 h-10 absolute top-50 lg:top-6 left-[45%] lg:left-[40%] z-[2]">

                        <div class="w-[42%] xl:w-full stat-item h-auto xl:h-[50%] font-zuume-semibold text-center text-[19vw] xl:text-[130px] text-dmr-green-dark order-1 xl:order-2 pt-0 xl:pt-10 pr-0 xl:pr-10">
                            #1
                        </div>
                        <div class="w-[58%] xl:w-full h-auto xl:h-[50%] relative order-2 xl:order-1 text-center">
                            <img src="{{ asset('assets/img/new_home/06-2024/hst.png') }}" alt="Kaiwa"
                                 class="hidden lg:block stat-item w-[70%] xl:absolute z-[1] right-[20%] top-[19%]"/>
                            <img src="{{ asset('assets/img/new_home/06-2024/hst-mobile.png') }}" alt="Kaiwa"
                                 class="lg:hidden stat-item w-full xl:absolute z-[1] right-[20%] top-[19%]"/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="capitalize font-zuume-semibold xl:font-beanbag text-dmr-green-dark text-[50px] xl:text-[3rem] leading-[1.11] container xs:text-left xl:text-center mt-20 px-3 md:px-0 ">
                <span class="relative">Doanh nghiệp hợp tác cùng DUNGMORI<img
                            src="{{ asset('assets/img/new_home/06-2024/plane.svg') }}" alt=""
                            class="parallax-bg floating w-10 h-10 absolute top-[250px] xl:top-[200px] -right-[10%] z-0"
                            data-speed="0.1"></span>
            </div>
            <div class="logos-slider-wrapper relative">
                <div id="logos-slider" class="logos-slider pt-5 md:pt-20 pb-8 w-screen flex items-center mb-16 md:mb-0">
                    <div class="h-full flex items-center">
                        <img src="{{asset('assets/img/new_home/06-2024/canon.svg')}}" class="max-w-[150px]" alt=""/>
                    </div>
                    <div class="h-full flex items-center"><img
                                src="{{asset('assets/img/new_home/06-2024/hitachi.svg')}}" class="max-w-[150px]"
                                alt=""/></div>
                    <div class="h-full flex items-center"><img src="{{asset('assets/img/new_home/06-2024/fsoft.svg')}}"
                                                               class="max-w-[150px]" alt=""/></div>
                    <div class="h-full flex items-center"><img src="{{asset('assets/img/new_home/06-2024/nissan.svg')}}"
                                                               class="max-w-[150px]" alt=""/></div>
                    <div class="h-full flex items-center"><img
                                src="{{asset('assets/img/new_home/06-2024/maedakousen.svg')}}" class="max-w-[150px]"
                                alt=""/></div>
                    <div class="h-full flex items-center"><img src="{{asset('assets/img/new_home/06-2024/aeon.svg')}}"
                                                               class="max-w-[150px]" alt=""/></div>
                    <div class="h-full flex items-center"><img src="{{asset('assets/img/new_home/06-2024/gmo.svg')}}"
                                                               class="max-w-[150px]" alt=""/></div>
                    <div class="h-full flex items-center"><img src="{{asset('assets/img/new_home/06-2024/rikkei.svg')}}"
                                                               class="max-w-[150px]" alt=""/></div>
                    <div class="h-full flex items-center"><img src="{{asset('assets/img/new_home/06-2024/nitori.svg')}}"
                                                               class="max-w-[150px]" alt=""/></div>
                    <div class="h-full flex items-center"><img
                                src="{{asset('assets/img/new_home/06-2024/bachkhoa.jpg')}}"
                                class="max-w-[100px]" alt=""/></div>
                </div>
            </div>


            <div class="tc-container xl:block hidden font-averta-regular w-[1616px] mt-25 mx-auto relative">
                <img src="{{ asset('assets/img/new_home/06-2024/flower-outline.svg') }}" alt=""
                     class="floating w-10 h-10 absolute top-[1800px] left-[55%] z-[2] parallax-bg" data-speed="0.5">
                <div class="flex flex-row">
                    <div class="flex flex-row">
                        <div class="tc-info w-[320px] mr-4 flex items-center transition-all">
                            <div class="relative">
                                <div class="text-4xl font-averta-bold">NGUYỄN VĂN DŨNG</div>
                                <div class="text-lg mt-4">
                                    <div>• Giám đốc CTCP DUNGMORI</div>
                                    <div>• Cử nhân khoa Giáo dục, Đại học Nhật Bản (日本大学)</div>
                                    <div>• Đạt học bổng Mitsubishi</div>
                                </div>
                                <div class="z-10 tc-saying w-[360px] relative bg-[#EC6E23] mt-12 px-10 py-4 text-white text-4xl rounded-xl text-center mr-[-40px]">
                                    <div class="font-beanbag">TÂM HUYẾT</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tc-box bg-[#D7D6FF] mr-4 hover:bg-[#B7B5F9] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/thaydung.png') }}"
                                    class="tc-img lazyload object-cover h-[500px] w-[440px] rounded-xl transition-all select-none"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                        </div>
                    </div>
                    <div class="flex flex-row">
                        <div class="tc-info w-[320px] mr-4 flex items-center hidden transition-all">
                            <div class="relative">
                                <div class="text-4xl font-averta-bold">PHƯƠNG THỊ THANH</div>
                                <div class="text-lg mt-4">
                                    <div>• Phó giám đốc CTCP DUNGMORI</div>
                                    <div>• Cử nhân khoa Tiếng Nhật, Đại học Hà Nội</div>
                                    <div>• Thạc sĩ chuyên ngành Giáo dục tiếng Nhật, Đại học Ngoại ngữ Tokyo</div>
                                    <div>• Đạt học bổng chính phủ Nhật Bản - MEXT</div>
                                </div>
                                <div class="z-10 tc-saying w-[360px] relative bg-[#EC6E23] mt-10 px-10 py-4 text-white text-4xl rounded-xl text-center mr-[-40px]">
                                    <div class="font-beanbag">SÁNG TẠO</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tc-box bg-[#B2CFDB] mr-4 hover:bg-[#A8D9EE] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/cothanh.png') }}"
                                    class="tc-img lazyload object-cover h-[500px] w-[190px] rounded-xl transition-all"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                        </div>
                    </div>
                    <div class="flex flex-row">
                        <div class="tc-info w-[320px] mr-4 flex items-center hidden transition-all">
                            <div class="relative">
                                <div class="text-4xl font-averta-bold">ĐAN HOÀNG NGHĨA</div>
                                <div class="text-[17px] leading-[25px] mt-4">
                                    <div>• Phó giám đốc CTCP DUNGMORI</div>
                                    <div>• Cử nhân Luật Đại học Aomori Chuo</div>
                                    <div>• Đạt học bổng toàn phần của Tập đoàn Tokyo Marine cho hệ thạc sĩ KHTT Đại học
                                        Quốc gia Nagoya
                                    </div>
                                    <div>• Từng đạt 60/60 Từ vựng Ngữ pháp, 60/60 Đọc hiểu, 60/60 Nghe hiểu trình độ
                                        JLPT N1
                                    </div>
                                </div>
                                <div class="z-10 tc-saying w-[360px] relative bg-[#EC6E23] mt-8 py-3.5 text-white text-4xl rounded-xl text-center mr-[-40px]">
                                    <div class="font-beanbag">ĐA DẠNG</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tc-box bg-[#AFEBB4] mr-4 hover:bg-[#A5F2AB] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/thaynghia.png') }}"
                                    class="tc-img lazyload object-cover h-[500px] w-[190px] rounded-xl transition-all"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                        </div>
                    </div>
                    <div class="flex flex-row">
                        <div class="tc-info w-[320px] mr-4 flex items-center hidden transition-all">
                            <div class="relative">
                                <div class="text-4xl font-averta-bold">ĐINH THỊ HIỀN</div>
                                <div class="text-lg mt-6">
                                    <div>• Trưởng phòng Đào tạo CTCP DUNGMORI</div>
                                    <div>• Thành thạo tiếng Nhật và tiếng Trung</div>
                                    <div>• Đạt chứng chỉ JLPT N1</div>
                                </div>
                                <div class="z-10 tc-saying w-[360px] relative bg-[#EC6E23] mt-12 py-4 text-white text-4xl rounded-xl text-center mr-[-40px]">
                                    <div class="font-beanbag">BỀN BỈ</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tc-box bg-[#F8C8B9] mr-4 hover:bg-[#FFB8A1] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/cohien.png') }}"
                                    class="tc-img lazyload object-cover h-[500px] w-[190px] rounded-xl transition-all"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                        </div>
                    </div>
                    <div class="flex flex-row">
                        <div class="tc-info w-[320px] mr-4 flex items-center hidden transition-all">
                            <div class="relative">
                                <div class="text-4xl font-averta-bold">PHƯƠNG THỊ NHÀN</div>
                                <div class="text-lg mt-4">
                                    <div>• Trưởng phòng Phát triển sản phẩm CTCP DUNGMORI</div>
                                    <div>• Cử nhân Đại học Toyo</div>
                                    <div>• Đạt chứng chỉ N1, điểm đọc 60/60, điểm nghe 60/60</div>
                                    <div>• Đạt chứng chỉ J1+ tiếng Nhật thương mại</div>
                                </div>
                                <div class="z-10 tc-saying w-[360px] relative bg-[#EC6E23] mt-10 py-4 text-white text-4xl rounded-xl text-center mr-[-40px]">
                                    <div class="font-beanbag">KẾT NỐI</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tc-box bg-[#F2E8B2] hover:bg-[#FFEE8F] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/conhan.png') }}"
                                    class="tc-img lazyload object-cover h-[500px] w-[190px] rounded-xl transition-all"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"/>
                        </div>
                    </div>
                </div>
            </div>

            <div class="xl:hidden block mt-30 xl:mt-20 container font-averta-regular px-2 relative">
                <div id="tc_slick">
                    <div class="">
                        <div class="relative bg-[#D7D6FF] hover:bg-[#B7B5F9] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/thaydung.png') }}"
                                    class="lazyload object-cover rounded-xl"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                            <div class="absolute -bottom-8 w-full flex justify-center">
                                <div class="relative bg-[#EC6E23] px-10 py-3 text-white text-4xl rounded-xl text-center">
                                    <div>TÂM HUYẾT</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-15">
                            <div class="text-3xl font-averta-bold">NGUYỄN VĂN DŨNG</div>
                            <div class="text-lg mt-4">
                                <div>• Giám đốc CTCP DUNGMORI</div>
                                <div>• Cử nhân khoa Giáo dục, Đại học Nhật Bản (日本大学)</div>
                                <div>• Đạt học bổng Mitsubishi</div>
                            </div>
                        </div>
                    </div>
                    <div class="">
                        <div class="relative bg-[#B2CFDB] hover:bg-[#A8D9EE] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/cothanh.png') }}"
                                    class="lazyload object-cover rounded-xl"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                            <div class="absolute -bottom-8 w-full flex justify-center">
                                <div class="relative bg-[#EC6E23] px-10 py-3 text-white text-4xl rounded-xl text-center">
                                    <div>SÁNG TẠO</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-15">
                            <div class="text-3xl font-averta-bold">PHƯƠNG THỊ THANH</div>
                            <div class="text-lg mt-4">
                                <div>• Phó giám đốc CTCP DUNGMORI</div>
                                <div>• Cử nhân khoa Tiếng Nhật, Đại học Hà Nội</div>
                                <div>• Thạc sĩ chuyên ngành Giáo dục tiếng Nhật, Đại học Ngoại ngữ Tokyo</div>
                                <div>• Đạt học bổng chính phủ Nhật Bản - MEXT</div>
                            </div>
                        </div>
                    </div>
                    <div class="">
                        <div class="relative bg-[#AFEBB4] hover:bg-[#A5F2AB] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/thaynghia.png') }}"
                                    class="lazyload object-cover rounded-xl"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                            <div class="absolute -bottom-8 w-full flex justify-center">
                                <div class="relative bg-[#EC6E23] px-10 py-3 text-white text-4xl rounded-xl text-center">
                                    <div>ĐA DẠNG</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-15">
                            <div class="text-3xl font-averta-bold">ĐAN HOÀNG NGHĨA</div>
                            <div class="text-lg mt-4">
                                <div>• Phó giám đốc CTCP DUNGMORI</div>
                                <div>• Cử nhân Luật Đại học Aomori Chuo</div>
                                <div>• Đạt học bổng toàn phần của Tập đoàn Tokyo Marine cho hệ thạc sĩ KHTT Đại học Quốc
                                    gia Nagoya
                                </div>
                                <div>• Từng đạt 60/60 Từ vựng Ngữ pháp, 60/60 Đọc hiểu, 60/60 Nghe hiểu trình độ JLPT
                                    N1
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="">
                        <div class="relative bg-[#F8C8B9] hover:bg-[#FFB8A1] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/cohien.png') }}"
                                    class="lazyload object-cover rounded-xl"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                            <div class="absolute -bottom-8 w-full flex justify-center">
                                <div class="relative bg-[#EC6E23] px-10 py-3 text-white text-4xl rounded-xl text-center">
                                    <div>BỀN BỈ</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-15">
                            <div class="text-3xl font-averta-bold">ĐINH THỊ HIỀN</div>
                            <div class="text-lg mt-4">
                                <div>• Trưởng phòng Đào tạo CTCP DUNGMORI</div>
                                <div>• Thành thạo tiếng Nhật và tiếng Trung</div>
                                <div>• Đạt chứng chỉ JLPT N1</div>
                            </div>
                        </div>
                    </div>
                    <div class="">
                        <div class="relative bg-[#F2E8B2] hover:bg-[#FFEE8F] rounded-xl">
                            <img
                                    data-src="{{ asset('assets/img/new_home/06-2024/conhan.png') }}"
                                    class="lazyload object-cover rounded-xl"
                                    style="-webkit-user-drag: none; -webkit-user-select: none"
                            />
                            <div class="absolute -bottom-8 w-full flex justify-center">
                                <div class="relative bg-[#EC6E23] px-10 py-3 text-white text-4xl rounded-xl text-center">
                                    <div>KẾT NỐI</div>
                                    <div class="w-4.5 h-4.5 top-8 -right-2 absolute rotate-45 bg-[#EC6E23]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-15">
                            <div class="text-3xl font-averta-bold">PHƯƠNG THỊ NHÀN</div>
                            <div class="text-lg mt-4">
                                <div>• Trưởng phòng Phát triển sản phẩm CTCP DUNGMORI</div>
                                <div>• Cử nhân Đại học Toyo</div>
                                <div>• Đạt chứng chỉ N1, điểm đọc 60/60, điểm nghe 60/60</div>
                                <div>• Đạt chứng chỉ J1+ tiếng Nhật thương mại</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-20 container font-averta-regular px-2 xl:px-0">
                <div class="text-center font-beanbag md:text-5xl text-4xl text-[#07403F]">Tin tức nổi bật</div>
                <div class="flex mt-5 text-base md:flex-row flex-col relative">
                    <img src="{{ asset('assets/img/new_home/06-2024/flower.svg') }}" alt=""
                         class="floating w-10 h-10 absolute top-[400%] -left-20 z-[2] parallax-bg" data-speed="0.3">
                    <div class="flex-1 flex flex-col md:mr-4 mr-[0px]">
                        <div class="font-averta-bold lg:text-3xl text-2xl border-b-[3px] text-[#07403F] border-[#07403F] pb-3">
                            Kinh nghiệm học tiếng Nhật
                        </div>
                        <a href="https://dungmori.com/bai-viet/1191-lo-trinh-tu-hoc-tieng-nhat-chuan-nhat-cho-nguoi-moi-bat-dau-"
                           target="_blank">
                            <div class="flex flex-row mt-5 pb-4 text-black border-b border-black cursor-pointer hover:underline hover:text-[#4e87ff]">
                                <div class="xl:w-[90px] w-[75px] min-w-[75px]">
                                    <span class="font-averta-bold">28.01.24</span>
                                </div>
                                <div class="md:h-[46px] h-auto">Lộ Trình Học Tiếng Nhật Chuẩn Nhất Cho Người Mới Bắt
                                    Đầu
                                </div>
                            </div>
                        </a>
                        <a href="https://dungmori.com/bai-viet/110-tuyet-chieu-hoc-tieng-nhat-cho-nguoi-moi-bat-dau-de-dang-va-nhanh-chong"
                           target="_blank">
                            <div class="flex flex-row mt-5 pb-4 text-black border-b border-black cursor-pointer hover:underline hover:text-[#4e87ff]">
                                <div class="xl:w-[90px] w-[75px] min-w-[75px]">
                                    <span class="font-averta-bold">05.05.24</span>
                                </div>
                                <div class="md:h-[46px] h-auto">Tuyệt Chiêu Học Tiếng Nhật Cho Người Mới Bắt Đầu Dễ Dàng
                                    Và Nhanh Chóng
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="flex-1 flex flex-col md:ml-4 ml-[0px] md:mt-0 mt-[35px]">
                        <div class="font-averta-bold lg:text-3xl text-2xl border-b-[3px] text-[#07403F] border-[#07403F] pb-3">
                            Thông tin kỳ thi JLPT
                        </div>
                        <a href="https://dungmori.com/bai-viet/1130-cach-tinh-diem-jlpt-tu-n5-den-n1-theo-cau-truc-de-moi-nhat"
                           target="_blank">
                            <div class="flex flex-row mt-5 pb-4 text-black border-b border-black cursor-pointer hover:underline hover:text-[#4e87ff]">
                                <div class="xl:w-[90px] w-[75px] min-w-[75px]">
                                    <span class="font-averta-bold">30.04.24</span><br>
                                </div>
                                <div class="md:h-[46px] h-auto">Cách Tính Điểm JLPT Từ N5 Đến N1 Theo Cấu Trúc Đề Mới
                                    Nhất
                                </div>
                            </div>
                        </a>
                        <a href="https://dungmori.com/bai-viet/1220-tim-hieu-cach-tinh-diem-thi-doc-la-cua-ki-thi-jlpt"
                           target="_blank">
                            <div class="flex flex-row mt-5 pb-4 text-black border-b border-black cursor-pointer hover:underline hover:text-[#4e87ff]">
                                <div class="xl:w-[90px] w-[75px] min-w-[75px]">
                                    <span class="font-averta-bold">05.06.24</span><br>
                                </div>
                                <div class="md:h-[46px] h-auto">Tìm Hiểu Cách Tính Điểm Thi Độc Lạ Của Kì Thi JLPT</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <div class="mt-20">
                {{--            <div class="news_slick md:w-[800px] w-full mx-auto md:px-0 px-[15px]" id="news_slick">--}}
                {{--                <a href="https://vietnamnet.vn/chang-trai-8x-quyet-tam-xay-he-sinh-thai-hoc-tieng-nhat-toan-dien-cho-nguoi-viet-763075.html" target="_blank">--}}
                {{--                    <div class="bg-white md:p-2 p-[8px] rounded-lg shadow-lg">--}}
                {{--                        <div class="flex md:flex-row flex-col">--}}
                {{--                            <img data-src="{{ asset('assets/img/new_home/12-2021/tin-tuc-nguyen-van-dung.jpg') }}" class="lazyload object-cover md:rounded rounded-xl md:w-[134px] min-w-[134px] md:h-[138px] w-full h-[60vw]" />--}}
                {{--                            <div class="flex flex-col md:ml-2.5 ml-[0px] md:mt-0 mt-[10px]">--}}
                {{--                                <img data-src="{{ asset('assets/img/new_home/12-2021/logo-vietnamnet.svg') }}" class="lazyload object-cover md:w-35 w-[90px]" />--}}
                {{--                                <span class="text-[#073A3B] text-xl font-semibold mt-3 font-averta-semibold line-clamp-3 md:h-auto h-[115px] md:pr-3 pr-[0px]">CHÀNG TRAI 8X QUYẾT TÂM XÂY HỆ SINH THÁI HỌC TIẾNG NHẬT TOÀN DIỆN CHO NGƯỜI VIỆT</span>--}}
                {{--                            </div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                </a>--}}
                {{--                <a href="https://vietnamnet.vn/chang-trai-8x-quyet-tam-xay-he-sinh-thai-hoc-tieng-nhat-toan-dien-cho-nguoi-viet-763075.html" target="_blank">--}}
                {{--                    <div class="bg-white md:p-2 p-[8px] rounded-lg shadow-lg">--}}
                {{--                        <div class="flex md:flex-row flex-col">--}}
                {{--                            <img data-src="{{ asset('assets/img/new_home/12-2021/dung-mori-chuyen-nghiep.jpg') }}" class="lazyload object-cover md:rounded rounded-xl md:w-[134px] min-w-[134px] md:h-[138px] w-full h-[60vw]" />--}}
                {{--                            <div class="flex flex-col md:ml-2.5 ml-[0px] md:mt-0 mt-[10px]">--}}
                {{--                                <img data-src="{{ asset('assets/img/new_home/12-2021/logo-svvn.png') }}" class="lazyload object-cover w-35 bg-[#bdbdc9]" />--}}
                {{--                                <span class="text-[#073A3B] text-xl font-semibold mt-3 font-averta-semibold line-clamp-3 md:h-auto h-[115px] md:pr-3 pr-[0px]">SỐNG Ở NHẬT BẢN NHƯNG CHÀNG TRAI VẪN ĐIỀU HÀNH CÔNG TY GIẢNG DẠY TIẾNG NHẬT TẠI VIỆT NAM</span>--}}
                {{--                            </div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                </a>--}}
                {{--                <a href="https://vietnamnet.vn/chang-trai-8x-quyet-tam-xay-he-sinh-thai-hoc-tieng-nhat-toan-dien-cho-nguoi-viet-763075.html" target="_blank">--}}
                {{--                    <div class="bg-white md:p-2 p-[8px] rounded-lg shadow-lg">--}}
                {{--                        <div class="flex md:flex-row flex-col">--}}
                {{--                            <img data-src="{{ asset('assets/img/new_home/12-2021/hop-tac-dao-tao-cao-dang.jpg') }}" class="lazyload object-cover md:rounded rounded-xl md:w-[134px] min-w-[134px] md:h-[138px] w-full h-[60vw]" />--}}
                {{--                            <div class="flex flex-col md:ml-2.5 ml-[0px] md:mt-0 mt-[10px]">--}}
                {{--                                <img data-src="{{ asset('assets/img/new_home/12-2021/dantri.png') }}" class="lazyload object-cover w-36" />--}}
                {{--                                <span class="text-[#073A3B] text-xl font-semibold mt-3 font-averta-semibold line-clamp-3 md:h-auto h-[115px] md:pr-3 pr-[0px]">DUNGMORI VÀ BƯỚC NGOẶT TRỞ THÀNH NHÀ ĐÀO TẠO TIẾNG NHẬT CHUYÊN NGHIỆP</span>--}}
                {{--                            </div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                </a>--}}
                {{--            </div>--}}

                <div class="container relative">
                    <div class="absolute top-[45%] w-full z-[3] flex justify-between">
                        <svg class="prev group cursor-pointer absolute -left-[45px]" width="37" height="37"
                             viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="34.6914" y="34.6924" width="33.192" height="33.192" rx="16.596"
                                  transform="rotate(-180 34.6914 34.6924)" stroke="#57D061" stroke-width="3"
                                  class="transition group-hover:fill-[#57D061]"/>
                            <path d="M18.0957 25.9023L10.2896 18.0962L18.0957 10.2901" stroke="#57D061"
                                  stroke-width="3.45724" stroke-linecap="round" stroke-linejoin="round"
                                  class="transition group-hover:stroke-white"/>
                            <path d="M25.5469 18.0967L10.6443 18.0967" stroke="#57D061" stroke-width="3.45724"
                                  stroke-linecap="round" stroke-linejoin="round"
                                  class="transition group-hover:stroke-white"/>
                        </svg>
                        <svg class="next group cursor-pointer rotate-180 absolute -right-[45px]" width="37" height="37"
                             viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="34.6914" y="34.6924" width="33.192" height="33.192" rx="16.596"
                                  transform="rotate(-180 34.6914 34.6924)" stroke="#57D061" stroke-width="3"
                                  class="transition group-hover:fill-[#57D061]"/>
                            <path d="M18.0957 25.9023L10.2896 18.0962L18.0957 10.2901" stroke="#57D061"
                                  stroke-width="3.45724" stroke-linecap="round" stroke-linejoin="round"
                                  class="transition group-hover:stroke-white"/>
                            <path d="M25.5469 18.0967L10.6443 18.0967" stroke="#57D061" stroke-width="3.45724"
                                  stroke-linecap="round" stroke-linejoin="round"
                                  class="transition group-hover:stroke-white"/>
                        </svg>
                    </div>
                    <div class="container flex flex-col xl:flex-row gap-4 before:hidden h-[500px] z-[0]"
                         id="news_slick">
                        <a href="https://vietnamnet.vn/chang-trai-8x-quyet-tam-xay-he-sinh-thai-hoc-tieng-nhat-toan-dien-cho-nguoi-viet-763075.html"
                           target="_blank"
                           class="h-[500px] mx-2 shadow-lg transition w-full xl:w-1/3 p-4 pb-8 bg-white rounded-lg flex flex-col xl:gap-8 gap-3 items-start">
                            <img data-src="{{ asset('assets/img/new_home/06-2024/vietnamnet-new.png') }}"
                                 class="h-[200px] lazyload object-cover w-full rounded-lg"/>
                            <img data-src="{{ asset('assets/img/new_home/12-2021/logo-vietnamnet.svg') }}"
                                 class="lazyload object-contain !h-16 !mt-10"/>
                            <h3 class="font-averta-bold text-[16px] text-dmr-green-dark uppercase text-left leading-5 mt-4">
                                Chàng trai 8x quyết tâm xây hệ sinh thái học tiếng nhật toàn diện cho người
                                Việt</h3>
                        </a>
                        <a href="https://svvn.tienphong.vn/song-o-nhat-ban-nhung-chang-trai-van-dieu-hanh-cong-ty-giang-day-tieng-nhat-tai-viet-nam-post1368028.tpo"
                           target="_blank"
                           class="h-[500px] mx-2 shadow-lg transition w-full xl:w-1/3 p-4 pb-8 bg-white rounded-lg flex flex-col xl:gap-8 gap-3 items-start">
                            <img data-src="{{ asset('assets/img/new_home/06-2024/sinh-vien-vn-new.png') }}"
                                 class="h-[200px] lazyload object-cover w-full rounded-lg"/>
                            <img data-src="{{ asset('assets/img/new_home/06-2024/logo-svvn.jpg') }}"
                                 class="lazyload object-contain !h-16 !mt-10"/>
                            <h3 class="font-averta-bold text-[16px] text-dmr-green-dark uppercase text-left leading-5 mt-4">
                                Sống ở Nhật Bản nhưng chàng trai vẫn điều hành công ty giảng dạy tiếng nhật tại Việt
                                Nam</h3>
                        </a>
                        <a href="https://dantri.com.vn/giao-duc/dung-mori-va-buoc-ngoat-tro-thanh-nha-dao-tao-tieng-nhat-chuyen-nghiep-20211007170352717.htm"
                           target="_blank"
                           class="h-[500px] mx-2 shadow-lg transition w-full xl:w-1/3 p-4 pb-8 bg-white rounded-lg flex flex-col xl:gap-8 gap-3 items-start">
                            <img data-src="{{ asset('assets/img/new_home/06-2024/dantri-new.png') }}"
                                 class="h-[200px] lazyload object-cover w-full rounded-lg"/>
                            <img data-src="{{ asset('assets/img/new_home/12-2021/dantri.png') }}"
                                 class="lazyload object-contain !h-16 !mt-10"/>
                            <h3 class="font-averta-bold text-[16px] text-dmr-green-dark uppercase text-left leading-5 mt-4">
                                DUNGMORI và bước ngoặt trở thành nhà đào tạo tiếng Nhật chuyên nghiệp</h3>
                        </a>
                        <a href="https://vtv.vn/giao-duc/kham-pha-ngay-dia-diem-sang-xin-min-cho-nguoi-hoc-tieng-nhat-tai-ha-noi-20240726175353664.htm"
                           target="_blank"
                           class="h-[500px] mx-2 shadow-lg transition w-full xl:w-1/3 p-4 pb-8 bg-white rounded-lg flex flex-col xl:gap-8 gap-3 items-start">
                            <img data-src="{{ asset('assets/img/new_home/06-2024/dungmori-galaxy.png') }}"
                                 class="h-[200px] lazyload object-cover w-full rounded-lg"/>
                            <img data-src="{{ asset('assets/img/new_home/06-2024/vtv.webp') }}"
                                 class="lazyload object-contain !h-16 !mt-10"/>
                            <h3 class="font-averta-bold text-[16px] text-dmr-green-dark uppercase text-left leading-5 mt-4">
                                Khám phá ngay địa điểm sang-xịn-mịn cho người học tiếng Nhật tại Hà Nội</h3>
                        </a>
                        <a href="https://dantri.com.vn/giao-duc/hanh-trinh-1-thap-ky-khang-dinh-vi-the-trong-linh-vuc-giao-duc-tieng-nhat-cua-dungmori-20240730181145153.htm"
                           target="_blank"
                           class="h-[500px] mx-2 shadow-lg transition w-full xl:w-1/3 p-4 pb-8 bg-white rounded-lg flex flex-col xl:gap-8 gap-3 items-start">
                            <img data-src="{{ asset('assets/img/new_home/06-2024/dungmori-vanphuc.webp') }}"
                                 class="h-[200px] lazyload object-cover w-full rounded-lg"/>
                            <img data-src="{{ asset('assets/img/new_home/12-2021/dantri.png') }}"
                                 class="lazyload object-contain !h-16 !mt-10"/>
                            <h3 class="font-averta-bold text-[16px] text-dmr-green-dark uppercase text-left leading-5 mt-4">
                                Hành trình 1 thập kỷ khẳng định vị thế trong lĩnh vực giáo dục tiếng Nhật của
                                Dungmori</h3>
                        </a>
                        <a href="https://thitruong.nld.com.vn/trai-nghiem-hoc-tap-va-lam-viec-tai-trung-tam-tieng-nhat-hang-dau-ha-noi-196240809122724878.htm"
                           target="_blank"
                           class="h-[500px] mx-2 shadow-lg transition w-full xl:w-1/3 p-4 pb-8 bg-white rounded-lg flex flex-col xl:gap-8 gap-3 items-start">
                            <img data-src="{{ asset('assets/img/new_home/06-2024/dmrhd.webp') }}"
                                 class="h-[200px] lazyload object-cover w-full rounded-lg"/>
                            <img data-src="{{ asset('assets/img/new_home/06-2024/nldlogo.png') }}"
                                 class="lazyload object-contain !h-16 !mt-10"/>
                            <h3 class="font-averta-bold text-[16px] text-dmr-green-dark uppercase text-left leading-5 mt-4">
                                Trải nghiệm học tập và làm việc tại trung tâm tiếng Nhật hàng đầu Hà Nội</h3>
                        </a>
                    </div>
                </div>
            </div>

            <div class="mt-20">
                <div class="text-center font-beanbag md:text-5xl text-4xl text-[#07403F] relative">
                <span class="relative">
                    DUNGMORI trên SNS
                    <img src="{{ asset('assets/img/new_home/06-2024/play.svg') }}" alt=""
                         class="hidden lg:block floating w-10 h-10 absolute -top-10 right-12 lg:top-[600px] lg:-right-10 z-[2] parallax-bg"
                         data-speed="0.2">
                </span>
                </div>
                <div id="social_slick" class="mt-5 pb-12">
                    {{--                <a href="https://www.tiktok.com/@dungmoriofficial/video/7317916763105152257" target="_blank" class="yt-social-item relative w-[262px]  mx-3" style="min-height: 500px !important">--}}
                    {{--                    <img--}}
                    {{--                            class="w-[830px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"--}}
                    {{--                            src="{{ asset('assets/img/new_home/06-2024/dungmori-hoc-cach-khen-nguoi-nhat-xin-xo-hon.jpeg') }}"--}}
                    {{--                            style="min-height: 467px !important; min-width: 830px"--}}
                    {{--                    >--}}
                    {{--                    <img--}}
                    {{--                            class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"--}}
                    {{--                            src="{{ asset('assets/img/new_home/06-2024/dungmori-hoc-cach-khen-nguoi-nhat-xin-xo-hon.jpeg') }}"--}}
                    {{--                            style="min-height: 467px !important; max-width: 262px;"--}}
                    {{--                    >--}}
                    {{--                    <div class="absolute bottom-[10px] p-[5px] rounded-full left-[391px] bg-white shadow-md">--}}
                    {{--                        <img data-src="{{ asset('assets/img/tiktok_thay_dung.png') }}" class="lazyload object-cover w-8 h-8"/>--}}
                    {{--                    </div>--}}
                    {{--                </a>--}}
                    <a href="https://www.tiktok.com/@dungmoriofficial/video/7317916763105152257" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/dungmori-hoc-cach-khen-nguoi-nhat-xin-xo-hon.jpeg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_thay_dung.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@dungmoriofficial/video/7273072611209268488" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/dungmori-nho-ngay-trong-mot-not-nhac.jpeg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_thay_dung.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@dungmoriofficial/video/7140602858713435394" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/dungmori-nen-o-nhat-hay-ve-bay-gio.jpeg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_thay_dung.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@dungmoriofficial/video/7129456112713207041" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/dungmori-5-tu-vung-n2-n3.jpeg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_thay_dung.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@phuongthanh_dungmori/video/7355745369965251848" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/bakari.jpg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_co_thanh.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@phuongthanh_dungmori/video/7336736990928440594" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/nhomacovo.png') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_co_thanh.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@phuongthanh_dungmori/video/7343900860830747925" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/motlanlanho.jpg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_co_thanh.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@gioitiengnhat.dungmori/video/7388461669367827719" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/dungmori-top-10-cau-de-mat-diem-jlpt-n4.jpeg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_gioi_tu_goc.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@gioitiengnhat.dungmori/video/7344351335166364946" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/dungmori-tieng-nhat-so-ho-mot-ti-la-sai.jpeg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_gioi_tu_goc.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                    <a href="https://www.tiktok.com/@gioitiengnhat.dungmori/video/7375050545133931793" target="_blank"
                       class="relative w-[262px]  mx-3"
                       style="min-height: 500px !important; min-width: 262px; max-width: 262px">
                        <img
                                class="w-[262px] h-[467px] max-h-[467px] object-cover rounded-2xl shadow-md"
                                src="{{ asset('assets/img/new_home/06-2024/dungmori-tiem-cafe-bat-on.jpeg') }}"
                                style="min-height: 467px !important; max-width: 262px;"
                        >
                        <div class="absolute bottom-[10px] p-[8px] rounded-full left-[107px] bg-white shadow-md">
                            <img data-src="{{ asset('assets/img/tiktok_gioi_tu_goc.png') }}"
                                 class="lazyload object-cover w-8 h-8"/>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>


    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
    <script>
        window.fbAsyncInit = function () {
            FB.init({
                appId: '1768213996826394',
                xfbml: true,
                version: 'v16.0'
            });
            FB.AppEvents.logPageView();
        };

        var isMobile = screen.width < 768;

        if (isMobile) {
            $('.yt-social-item').css({display: 'none'});
        }

        (function (d, s, id) {
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) {
                return;
            }
            js = d.createElement(s);
            js.id = id;
            js.src = "https://connect.facebook.net/en_US/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));


    </script>
@stop
@section('fixed-panel')
@stop

@section('footer-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
    <script src="{{ asset('assets/js/course/basic.js') }}"></script>
    <script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script type="text/javascript">
        const apiLesson = axios.create({
            baseURL: "/khoa-hoc",
            headers: {
                "Content-Type": "application/json",
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            withCredentials: true,
        });

        new Vue({
            el: "#container_home_2024",
            data: {
                status_add_code: 0,
                loading_btn_add_code: false,
                add_code_to_user: {
                    code_input: null,
                    course_id: null
                }
            },
            methods: {
                async addCodeToUser() {
                    this.loading_btn_add_code = true
                    await apiLesson.post('/add-code-lesson-to-user', this.add_code_to_user).then(res => {
                        console.log(`res: `, res)
                        if (res.data.error_code) {
                            this.status_add_code = 1
                            return;
                        }
                        console.log('vao day')
                        localStorage.setItem('congratulate_code_try_lesson', '1');
                        window.location.href = '/account/courses'
                    }).catch(err => {
                        console.log(`err: `, err)
                    })
                    this.loading_btn_add_code = false
                },
                showDialogLogin() {
                    $("#modalEnterCouponCode").modal("hide");
                    $("#btn-login").on('click', function (e) {
                        console.log(`vao dc day`)
                    });
                }
            },
            watch: {
                'add_code_to_user.code_input': {
                    handler: function (val, oldVal) {
                        this.status_add_code = 0;
                    },
                    deep: true
                }
            },
            mounted() {

            }
        })
    </script>
    <script>
        // use a script tag or an external JS file
        document.addEventListener("DOMContentLoaded", (event) => {
            // gsap.utils.toArray(".floating").forEach(randomFloat);

            // function randomFloat(element) {
            //     gsap.to(element, {
            //         x: 'random(-50, 20, 5)',
            //         y: 'random(-50, 10, 3)',
            //         ease: "sine.inOut",
            //         duration: gsap.utils.random([2, 4]),
            //         onComplete: () => randomFloat(element)
            //     });
            // }
        });

    </script>
    <script type="text/javascript">
        document.addEventListener("DOMContentLoaded", (event) => {
            gsap.registerPlugin(MotionPathPlugin);
            gsap.registerPlugin(ScrollTrigger);
            gsap.set('.stat-item', {autoAlpha: 0});

            gsap.to(".parallax-bg", {
                scrollTrigger: {
                    scrub: 1
                },
                yPercent: (i, target) => {
                    return -ScrollTrigger.maxScroll(window) * target.dataset.speed
                },
                ease: "none"
            });


            ScrollTrigger.batch(".home-stats", {
                onEnter: batch => {
                    batch.forEach((section, i) => {
                        const items = section.querySelectorAll(".stat-item");
                        gsap.to(items, {
                            autoAlpha: 1,
                            yPercent: 0,
                            duration: 0.4,
                            ease: "power1.inOut",
                            stagger: 0.1,
                            delay: 0.2,
                            scrub: true,
                        });
                        // gsap.from(section.querySelectorAll(".stat-number"), {
                        //     textContent: 0,
                        //     duration: 1,
                        //     ease: "power1.in",
                        //     snap: { textContent: 1 },
                        //     stagger: {
                        //         each: 1.0,
                        //         onUpdate: function() {
                        //             this.targets()[0].innerHTML = numberWithCommas(this.targets()[0].textContent);
                        //         },
                        //     }
                        // });
                    });
                },
                start: "top 50%",
                end: "bottom 5%",
                markers: false,
                scrub: true
            });

            const circlePath = MotionPathPlugin.convertToPath("#holder", false)[0];
            circlePath.id = "circlePath";
            document.querySelector(".wheel-svg").prepend(circlePath);

            let items = gsap.utils.toArray(".wheel-item"),
                numItems = items.length,
                itemStep = 1 / numItems,
                wrapProgress = gsap.utils.wrap(0, 1),
                snap = gsap.utils.snap(itemStep),
                wrapTracker = gsap.utils.wrap(0, numItems),
                tracker = {item: 0};
            items[0].classList.add('active');
            hideItems(0);

            let step = 1 / 8;

            const tl = gsap.timeline({paused: true});
            tl.to('.wheel-wrapper', {
                rotation: 360,
                transformOrigin: 'center',
                duration: 1.5,
                ease: 'none'
            });

            tl.to(items, {
                rotation: "-=360",
                transformOrigin: 'center',
                duration: 1.5,
                ease: 'none'
            }, 0);

            tl.to(tracker, {
                item: numItems,
                duration: 1.5,
                ease: 'none',
                modifiers: {
                    item: value => wrapTracker(numItems - Math.round(value))
                }
            }, 0);
            items.forEach(function (el, i) {
                el.addEventListener("click", function () {
                    var current = tracker.item,
                        activeItem = i;

                    if (i === current) {
                        return;
                    }

                    if (document.querySelector('.wheel-item.active')) {
                        document.querySelector('.wheel-item.active').classList.remove('active');
                    }

                    items[activeItem].classList.add('active');

                    hideItems(activeItem);

                    var diff = current - i;

                    if (Math.abs(diff) < numItems / 2) {
                        moveWheel(diff * itemStep);
                    } else {
                        var amt = numItems - Math.abs(diff);

                        if (current > i) {
                            moveWheel(amt * -itemStep);
                        } else {
                            moveWheel(amt * itemStep);
                        }
                    }
                });
            });

            // const nextTl = gsap.timeline({ paused:true });
            // nextTl.to('.wrapper', {
            //  rotation: "-=360",
            //  transformOrigin: 'center',
            //  duration: 1,
            //  ease: 'none'
            // });
            // nextTl.to(items, {
            //  rotation: 360,
            //  transformOrigin: 'center',
            //  duration: 1,
            //  ease: 'none'
            // });

            function numberWithCommas(x) {
                return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            }

            function hideItems(activeItem) {
                const totalItems = numItems;
                const itemsToShowCount = 5;
                const halfToShow = Math.floor(itemsToShowCount / 2);

                const itemsToShow = [];
                for (let i = -halfToShow; i <= halfToShow; i++) {
                    itemsToShow.push((activeItem + i + totalItems) % totalItems);
                }

                const upperItem = (activeItem - 1 + totalItems) % totalItems
                const lowerItem = (activeItem + 1) % totalItems
                const topItem = (activeItem - 2 + totalItems) % totalItems
                const botItem = (activeItem + 2) % totalItems

                items.forEach((item, index) => {
                    if (itemsToShow.includes(index)) {
                        item.classList.remove('opacity-0');
                    } else {
                        item.classList.add('opacity-0');
                    }

                    if (index === activeItem) {
                        item.classList.remove('wheel-upper');
                    } else if (upperItem === index) {
                        item.classList.add('wheel-upper');
                    } else {
                        item.classList.remove('wheel-upper');
                    }

                    if (index === activeItem) {
                        item.classList.remove('wheel-lower');
                    } else if (lowerItem === index) {
                        item.classList.add('wheel-lower');
                    } else {
                        item.classList.remove('wheel-lower');
                    }

                    if (index === activeItem) {
                        item.classList.remove('wheel-top');
                    } else if (topItem === index) {
                        item.classList.add('wheel-top');
                    } else {
                        item.classList.remove('wheel-top');
                    }

                    if (index === activeItem) {
                        item.classList.remove('wheel-bottom');
                    } else if (botItem === index) {
                        item.classList.add('wheel-bottom');
                    } else {
                        item.classList.remove('wheel-bottom');
                    }

                });
                gsap.set(items, {
                    motionPath: {
                        path: circlePath,
                        align: circlePath,
                        alignOrigin: [0.5, 0.5],
                        end: i => {
                            if (i === upperItem) {
                                return i / numItems + 0.040
                            }
                            if (i === lowerItem) {
                                return i / numItems - 0.040
                            }
                            if (i === topItem) {
                                return i / numItems + 0.050
                            }
                            if (i === botItem) {
                                return i / numItems - 0.050
                            }
                            return i / numItems
                        }
                    },
                });
            }

            function moveWheel(amount, i, index) {

                let progress = tl.progress();
                tl.progress(wrapProgress(snap(tl.progress() + amount)));
                let next = tracker.item;
                tl.progress(progress);

                document.querySelector('.wheel-item.active').classList.remove('active');
                items[next].classList.add('active');

                gsap.to(tl, {
                    progress: snap(tl.progress() + amount),
                    modifiers: {
                        progress: wrapProgress
                    }
                });
            }

            var interval = 0;
            setInterval(() => {
                if (interval === 8) {
                    interval = 0;
                } else {
                    interval++;
                }
                if (!$(".open")[0]) {
                    // items[interval].click()
                }
            }, 5000);
        })
    </script>
    <script type="text/javascript">
        $('#home-header-slider').slick({
            autoplay: true,
            autoplaySpeed: 3000,
            dots: true,
            arrow: false,
            infinite: true,
            speed: 500,
            adaptiveHeight: true,
            fade: true,
            customPaging: function (slider, i) {
                // this example would render "tabs" with titles
                return '<span class="dot"></span>';
            },
        });
        $('#logos-slider').slick({
            centerMode: true,
            centerPadding: '60px',
            speed: 2000,
            autoplay: true,
            autoplaySpeed: 2000,
            slidesToShow: 7,
            slidesToScroll: 3,
            infinite: true,
            focusOnSelect: true,
            pauseOnHover: true,
            dots: true,
            customPaging: function (slider, i) {
                // this example would render "tabs" with titles
                return '<span class="dot"></span>';
            },
            responsive: [
                {
                    breakpoint: 820,
                    settings: {
                        arrows: false,
                        centerMode: true,
                        centerPadding: '40px',
                        slidesToShow: 3
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        arrows: false,
                        centerMode: true,
                        centerPadding: '40px',
                        slidesToShow: 3
                    }
                },
                {
                    breakpoint: 750,
                    settings: {
                        arrows: false,
                        centerMode: true,
                        centerPadding: '40px',
                        slidesToShow: 1
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        arrows: false,
                        centerMode: true,
                        centerPadding: '20px',
                        slidesToShow: 1
                    }
                }
            ]
        });
        $('#feedback-slider').slick({
            speed: 500,
            autoplay: true,
            autoplaySpeed: 2000,
            slidesToShow: 1,
            infinite: true,
        });
        $('#news_slick').slick({
            autoplay: true,
            autoplaySpeed: 3000,
            arrows: true,
            loop: true,
            slidesToShow: 3,
            dots: false,
            prevArrow: $('.prev'),
            nextArrow: $('.next'),
            variableHeight: true,
            responsive: [
                {
                    breakpoint: 820,
                    settings: {
                        slidesToShow: 3
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 3
                    }
                },
                {
                    breakpoint: 750,
                    settings: {
                        centerMode: true,
                        centerPadding: '40px',
                        slidesToShow: 2
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        centerMode: true,
                        centerPadding: '20px',
                        slidesToShow: 1
                    }
                }
            ]
        });
        $('#social_slick').slick({
            centerMode: true,
            autoplay: true,
            autoplaySpeed: 3000,
            arrows: false,
            loop: true,
            infinite: true,
            slidesToShow: isMobile ? 1 : 3,
            dots: false,
            variableWidth: true
        });



        $('#tc_slick').slick({
            autoplay: false,
            autoplaySpeed: 3000,
            arrows: false,
            loop: true,
            slidesToShow: 1,
            dots: true
        });

        var tcInterval = setInterval(() => {
            if ($('.w-\\[440px\\]').parent().parent().next().length === 0) {
                if (!$(".open")[0]) {
                    $(".tc-box").first().click();
                }
            } else {
                if (!$(".open")[0]) {
                    $('.w-\\[440px\\]').parent().parent().next().children(1).eq(1).click();
                }
            }
        }, 5000);

        $(".tc-box").click(function () {
            $('.tc-img').removeClass('w-[440px]');
            $('.tc-img').addClass('w-[190px]');
            $(this).find('img:first').removeClass('w-[190px]');
            $(this).find('img:first').addClass('w-[440px]');
            $(".tc-box").prev().addClass('hidden');
            $(this).prev().removeClass('hidden');
            $(this).prev().addClass('block');

            clearInterval(tcInterval);
            tcInterval = setInterval(() => {
                if ($('.w-\\[440px\\]').parent().parent().next().length === 0) {
                    if (!$(".open")[0]) {
                        $(".tc-box").first().click();
                    }
                } else {
                    if (!$(".open")[0]) {
                        $('.w-\\[440px\\]').parent().parent().next().children(1).eq(1).click();
                    }
                }
            }, 5000);
        });
    </script>
@stop
