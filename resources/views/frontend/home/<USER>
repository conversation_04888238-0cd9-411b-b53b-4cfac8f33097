<div class="modal fade" id="survey_modal" tabindex="-1" aria-labelledby="survey_modal_label" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="survey_modal_label">Khảo sát chất l<PERSON> d<PERSON> h<PERSON></h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="survey_modal_body">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="save_survey_btn"><PERSON><PERSON><PERSON></button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $(document).ready(function () {
        let surveyId = null; // Biến để lưu survey_id
        let showSurveyPopup = {{ session('show_survey_popup') ? 'true' : 'false' }};

        if (showSurveyPopup) {
            $.ajax({
                url: '/survey/detail',
                success: function (res) {
                    if (res.data != null) {
                        let survey = res.data
                        surveyId = survey.id;
                        $("#survey_modal_label").text(survey.title)
                        if (survey.question.length > 0) {
                            renderQuestions(survey.question)
                        }
                    }
                    $('#survey_modal').modal('show');
                }
            })
        }
        $('#survey_modal').on('hidden.bs.modal', function () {
            console.log(`vao day`)
            $.ajax({
                url: '/survey/close-popup',
                success: function (res) {
                }
            })

        });

        $('#save_survey_btn').on('click', function () {
            let surveyResponses = [];

            $('input[type=radio]:checked').each(function () {
                let questionId = $(this).attr('name').split('_')[1]; // Tên input có dạng question_ID
                let answerValue = $(this).val(); // Lấy giá trị của radio button (Có hoặc Không)

                surveyResponses.push({
                    question_id: questionId,
                    answer_id: answerValue
                });
            });

            if (surveyResponses.length === $('.question_item').length) {
                $.ajax({
                    url: '/survey/save',
                    type: 'POST',
                    data: {
                        survey_responses: surveyResponses,
                        survey_id: surveyId,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (res) {
                        if (res.code === 200) {
                            alert('Khảo sát đã được lưu!');
                            $('#survey_modal').modal('hide');
                        } else {
                            alert('Có lỗi xảy ra khi lưu khảo sát.');
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('Error:', error);
                        alert('Có lỗi xảy ra khi gửi dữ liệu.');
                    }
                });
            } else {
                alert('Bạn chưa trả lời hết các câu hỏi.');
            }
        });
    })

    // Hàm render câu hỏi ra modal
    function renderQuestions(questions) {
        $('#survey_modal_body').empty();

        // Duyệt qua từng câu hỏi và thêm vào modal
        questions.forEach(function (question, index) {
            console.log(`question: `, question, index)

            // Khởi tạo phần HTML cho câu hỏi
            let questionHtml = `
                <div class="question_item" id="question_item_${index + 1}">
                    <div>Câu ${index + 1}: ${question.question}</div>
                    <div class="form-group">`;

            // Lặp qua các đáp án của câu hỏi để tạo radio button
            question.answer.forEach(function (answer) {
                questionHtml += `
            <div class="custom-control custom-radio custom-control-inline">
                <input type="radio" id="customRadioInline_${answer.id}" name="question_${question.id}" class="custom-control-input" value="${answer.id}">
                <label class="custom-control-label" for="customRadioInline_${answer.id}">${answer.answer}</label>
            </div>`;
            });

            // Kết thúc phần HTML của câu hỏi
            questionHtml += `</div></div>`;

            $('#survey_modal_body').append(questionHtml);
        });
    }
</script>