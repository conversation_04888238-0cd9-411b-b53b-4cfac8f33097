@extends('frontend._layouts.default')

@section('title') Dungmori - Nền tảng học tiếng Nhật Online Số 1 tại Việt Nam @stop
@section('description') Hơn 300,000 học viên đã tin tưởng theo học khóa tiếng <PERSON>h<PERSON>t online và offline c<PERSON><PERSON>, chuyên sâu về luyện thi JLPT, Kaiwa, EJU và tiếng Nhật doanh nghiệp. @stop
@section('keywords') Dungmori, dạy, tiếng nhật, học, online, d<PERSON> hiểu nh<PERSON>, nihongo, Japanese, miễn phí, n1, n2, n3, n4, n5 @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-js')

    <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "Organization",
      "name": "DUNGMORI",
      "url": "https://dungmori.com",
      "logo": "{{ config('app.asset_url') . '/assets/img/logo.png' }}",
      "sameAs": [
        "https://www.facebook.com/dungmori",
      ]
    }
    </script>
    <script type="application/ld+json">
       {
          "@context": "http://schema.org",
          "@type": "WebSite",
          "name": "DUNGMORI",
          "alternateName": "Dungmori - Website học tiếng Nhật online số 1 tại Việt Nam",
          "url": "https://dungmori.com"
        }
    </script>
    <style>
        .complex-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 5px 20px;
            color: white;
            text-shadow: 2px 2px rgb(116, 116, 116);
            text-transform: uppercase;
            cursor: pointer;
            border: solid 2px black;
            letter-spacing: 1px;
            font-weight: 600;
            font-size: 17px;
            background-color: #ff0000;
            border-radius: 50px;
            position: relative;
            overflow: hidden;
            transition: all 0.5s ease;
        }

        .complex-button:active {
            transform: scale(0.9);
            transition: all 100ms ease;
        }

        .complex-button img {
            height: 50px;
            transition: all 0.5s ease;
            z-index: 2;
        }

        .play {
            transition: all 0.5s ease;
            transition-delay: 300ms;
        }

        .complex-button:hover img {
            transform: scale(3) translate(100%);
        }

        .now {
            position: absolute;
            left: 0;
            transform: translateX(-100%);
            transition: all 0.5s ease;
            z-index: 2;
            font-size: 30px;
        }

        .complex-button:hover .now {
            font-family: Montserrat, Arial, sans-serif;
            font-weight: bold;
            transform: translateX(30px);
            transition-delay: 300ms;
        }

        .complex-button:hover .play {
            transform: translateX(200%);
            transition-delay: 300ms;
        }


    </style>
@stop


@section('content')
    <script>
        window.fbAsyncInit = function() {
            FB.init({
                appId      : '1768213996826394',
                xfbml      : true,
                version    : 'v16.0'
            });
            FB.AppEvents.logPageView();
        };

        (function(d, s, id){
            var js, fjs = d.getElementsByTagName(s)[0];
            if (d.getElementById(id)) {return;}
            js = d.createElement(s); js.id = id;
            js.src = "https://connect.facebook.net/en_US/sdk.js";
            fjs.parentNode.insertBefore(js, fjs);
        }(document, 'script', 'facebook-jssdk'));
    </script>
    <div class="home-wrapper">
        <div class="home-header">
            <div class="home-header__inner">
                <div class="home-header__slider-nav flex justify-end pc">
                    <div class="flex flex-column align-items-flex-end">
                        <img class="lazyload object-cover" data-src="{{ asset('/assets/img/new_home/12-2021/looking-mori.svg') }}" />
                        <div>Sự kiện mới</div>
                    </div>
                    <div class="flex align-items-flex-end ml-5 pb-3">
                        <svg id="prv_sld" class="a-cursor-pointer mr-3" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="19" height="19" rx="2" transform="matrix(-1 0 0 1 19 0)" fill="#DDDDDD"/>
                            <path d="M11.2227 14.4446L6.77821 10.0001L11.2227 5.55566" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <svg id="nxt_sld" class="a-cursor-pointer" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="19" height="19" rx="2" fill="#96D962"/>
                            <path d="M7.77734 14.4446L12.2218 10.0001L7.77734 5.55566" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="home-header__slider-wrapper">
                    <div id="home-header-slider" class="home-header__slider">
                        @foreach ($advertise as $news)
                            <div class="home-header__slide slick-slide">
                                <a class="home-header__slide-inner" href="{{$news->link}}">
                                    <img class="home-header__slide-image" src="{{url('/cdn/qc/small/' . $news->image_name)}}" alt="">
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="home-header__slogan">NỀN TẢNG HỌC TIẾNG NHẬT NHIỀU NGƯỜI THEO HỌC NHẤT VIỆT NAM</div>
            </div>
        </div>

        <div class="home-products">
            @include('frontend.home.components.section_title', ['title' => 'プロダクト', 'description' => 'sản phẩm độc quyền tại dũng mori', 'suffix_icon' => 'looking-mori'])
            <div class="container">
                <div class="row home-product flex">
                    <div class="col-md-6 home-product__info">
                        <div class="home-ribbon">
                            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#96D962" stroke="black"/>
                            </svg>
                            <div class="bookmark-ribbon bookmark-ribbon--green">
                                <strong>Tiếng Nhật ONLINE</strong>
                            </div>
                        </div>
                        <div class="pl-md-5 mt-3">
                            <p class="font-montserrat font-bold text-lg">Học online qua video bài giảng, hệ thống bài test</p>
                            <p class="font-quicksand text-sm">Với lộ trình được cá nhân hóa và hệ thống bài giảng lên tới hàng nghìn video/bài test, khóa học cam kết cung cấp đầy đủ kiến thức theo từng level khác nhau.</p>
                            <ul class="flex home-tabs" role="tablist">
                                <li class="home-tab active">
                                    <a href="#productJlpt" data-target="#productJlpt, #imageJlpt" class="home-tab__inner home-tab__inner--green" role="presentation" data-toggle="pill">JLPT</a>
                                </li>
                                <li class="home-tab">
                                    <a href="#productLD" data-target="#productLD, #imageLD" class="home-tab__inner home-tab__inner--green" role="presentation" data-toggle="pill">JLPT LĐ</a>
                                </li>
                                <li class="home-tab">
                                    <a href="#productKaiwa" data-target="#productKaiwa, #imageKaiwa" class="home-tab__inner home-tab__inner--green" role="presentation" data-toggle="pill">KAIWA</a>
                                </li>
                                <li class="home-tab">
                                    <a href="#productEju" data-target="#productEju, #imageEju" class="home-tab__inner home-tab__inner--green" role="presentation" data-toggle="pill">EJU</a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="productJlpt">
                                    <div class="font-quicksand text-sm mr-4 mt-4 mobile">Luyện thi</div>
                                    <div class="flex items-center flex-wrap mt-5">
                                        <div class="font-quicksand text-sm mr-4 pc">Luyện thi</div>
                                        <a href="{{url('/khoa-hoc/so-cap-n5')}}" class="mt-5 btn home-product-link home-product-link--green">N5</a>
                                        <a href="{{url('/khoa-hoc/so-cap-n4')}}" class="mt-5 btn home-product-link home-product-link--green">N4</a>
                                        <a href="{{url('/khoa-hoc/jlpt-n3')}}" class="mt-5 btn home-product-link home-product-link--green">N3</a>
                                        <a href="{{url('/khoa-hoc/jlpt-n2')}}" class="mt-5 btn home-product-link home-product-link--green">N2</a>
                                        <a href="{{url('/khoa-hoc/jlpt-n1')}}" class="mt-5 btn home-product-link home-product-link--green">N1</a>
                                    </div>
                                </div>
                                <div class="tab-pane" id="productLD">
                                    <div class="font-quicksand text-sm mr-4 mt-4 mobile">Luyện đề</div>
                                    <div class="flex items-center flex-wrap mt-4">
                                        <div class="font-quicksand text-sm mr-4 pc">Luyện đề</div>
                                        <a href="{{url('/khoa-hoc/luyen-de-n1')}}" target="_blank" class="mt-5 btn home-product-link home-product-link--green">N1</a>
                                        <a href="{{url('/khoa-hoc/luyen-de-n2')}}" target="_blank" class="mt-5 btn home-product-link">N2</a>
                                        <a href="{{url('/khoa-hoc/luyen-de-n3')}}" target="_blank" class="mt-5 btn home-product-link">N3</a>
                                    </div>
                                </div>
                                <div class="tab-pane" id="productKaiwa">
                                    <div class="flex items-center flex-wrap mt-5">
                                        <a href="{{url('/khoa-hoc/kaiwa-so-cap')}}" class="btn mt-2 home-product-link home-product-link--green">Kaiwa Sơ cấp</a>
                                        <a href="{{url('/khoa-hoc/kaiwa-trung-cap-1')}}" class="btn mt-2 home-product-link home-product-link--green">Kaiwa Trung cấp</a>
                                        <a href="{{url('/khoa-hoc/kaiwa-nang-cao')}}" class="btn mt-2 home-product-link home-product-link--green">Kaiwa Cao cấp</a>
                                    </div>
                                </div>
                                <div class="tab-pane" id="productEju">
                                    <div class="flex items-center flex-wrap mt-5">
                                        <a href="{{url('/khoa-hoc/eju')}}" class="btn mt-2 home-product-link home-product-link--green">EJU Tiếng Nhật</a>
                                        <a href="{{url('/khoa-hoc/eju-xhth')}}" class="btn mt-2 home-product-link home-product-link--green">EJU XHTH</a>
                                        <a href="{{url('/khoa-hoc/eju-toan')}}" class="btn mt-2 home-product-link home-product-link--green">EJU Toán</a>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-content">
                                <div class="tab-pane active" id="imageJlpt">
                                    <a href="http://online.dungmori.com/" target="_blank" class="home-product__button">Xem chi tiết</a>
                                </div>
                                <div class="tab-pane" id="imageKaiwa">
{{--                                    <a href="https://kaiwa.dungmori.com/" target="_blank" class="home-product__button">Xem chi tiết</a>--}}
                                </div>
                                <div class="tab-pane" id="imageEju">
{{--                                    <a href="" target="_blank" class="home-product__button">Xem thêm</a>--}}
                                </div>
                                <div class="tab-pane" id="imageLD">
                                    <a href="https://luyende.dungmori.com/online" target="_blank" class="home-product__button">Xem chi tiết</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 pc">
                        <div class="home-preview flex">
                            <div class="home-preview__inside flex-fill flex-1 tab-content">
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/jlpt-bg.webp')}}" class="tab-pane active lazyload object-cover" id="imageJlpt" />
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/kaiwa-bg.webp')}}" class="tab-pane lazyload object-cover" id="imageKaiwa" />
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/eju-bg.webp')}}" class="tab-pane lazyload object-cover" id="imageEju" />
                            </div>
                            <div class="home-preview__arrow-left"></div>
                            <div class="home-preview__icon home-preview__icon--right">
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/sitting-mori.svg')}}" class="lazyload object-cover"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-divider"></div>
            <div class="container">
                <div class="row home-product flex">
                    <div class="col-md-6 home-product__info order-2">
                        <div class="home-ribbon">
                            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#F27712" stroke="black"/>
                            </svg>
                            <div class="bookmark-ribbon bookmark-ribbon--yellow">
                                <strong>Tiếng Nhật ONLINE VIP</strong>
                            </div>
                        </div>
                        <div class="pl-md-5 mt-3">
                            <p class="font-montserrat font-bold text-lg">Học trực tiếp với giáo viên qua Zoom, Livestream</p>
                            <p class="font-quicksand text-sm">Là hình thức học kết hợp giữa khóa online, với các buổi học trực tiếp với giáo viên qua Zoom, Livestream, khóa học theo sát quá trình học tập của học viên, đảm bảo kết quả đầu ra.</p>
                            <ul class="flex home-tabs">
                                <li class="home-tab">
                                    <a href="{{url('https://onlinevip.dungmori.com') }}" target="_blank" class="home-tab__inner home-tab__inner--orange">JLPT</a>
                                </li>
                                <li class="home-tab">
                                    <a href="{{url('https://kaiwa.dungmori.com/')}}" target="_blank" class="home-tab__inner home-tab__inner--orange">Kaiwa</a>
                                </li>
                                <li class="home-tab">
                                    <a href="{{url('https://luyende.dungmori.com')}}" target="_blank" class="home-tab__inner home-tab__inner--orange">Luyện đề</a>
                                </li>
                            </ul>
{{--                            <a href="" target="_blank" class="home-product__button">Xem lịch khai giảng gần nhất</a>--}}
                        </div>
                    </div>
                    <div class="col-md-6 pc">
                        <div class="home-preview flex">
                            <div class="home-preview__inside flex-fill flex-1">
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/online-vip-bg.jpeg')}}" class="tab-pane active lazyload object-cover" />
                            </div>
                            <div class="home-preview__arrow-right"></div>
                            <div class="home-preview__icon home-preview__icon--left">
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/teaching-mori.svg')}}" class="lazyload object-cover"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-divider"></div>
            <div class="container">
                <div class="row home-product flex">
                    <div class="col-md-6 home-product__info">
                        <div class="home-ribbon">
                            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#7CA4FF" stroke="black"/>
                            </svg>
                            <div class="bookmark-ribbon bookmark-ribbon--blue">
                                <strong>Tiếng Nhật OFFLINE</strong>
                            </div>
                        </div>
                        <div class="pl-md-5 mt-3">
                            <p class="font-montserrat font-bold text-lg">Học trực tiếp tại các cơ sở ở Hà Nội và TP. HCM cùng các giảng viên giàu kinh nghiệm</p>
                            <p class="font-quicksand text-sm">Lớp học trực tiếp có giáo viên kèm cặp, cam kết đảm bảo đầu ra bằng văn bản</p>
                            <div class="tab-pane active" id="productJlpt">
                                <div class="flex items-center flex-wrap">
                                    <a href="https://offline.dungmori.com/" target="_blank" class="mt-4 btn home-product-link home-product-link--blue">N5</a>
                                    <a href="https://offline.dungmori.com/" target="_blank" class="mt-4 btn home-product-link home-product-link--blue">N4</a>
                                    <a href="https://offline.dungmori.com/" target="_blank" class="mt-4 btn home-product-link home-product-link--blue">N3</a>
                                    <a href="https://offline.dungmori.com/" target="_blank" class="mt-4 btn home-product-link home-product-link--blue">N2</a>
                                </div>
                            </div>
                            <a href="https://offline.dungmori.com/tructiep?utm_source=website&utm_medium=section&utm_campaign=chitiet" target="_blank" class="home-product__button">Xem chi tiết</a>
                        </div>
                    </div>
                    <div class="col-md-6 pc">
                        <div class="home-preview flex">
                            <div class="home-preview__inside flex-fill flex-1">
                                <img alt="" data-src="{{asset('assets/img/new_home/07-2023/offline-bg-2023.png')}}" class="tab-pane active lazyload object-cover" />
                            </div>
                            <div class="home-preview__arrow-left"></div>
                            <div class="home-preview__icon home-preview__icon--right">
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/running-mori.svg')}}" class="lazyload object-cover"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="home-divider"></div>
            <div class="container">
                <div class="row home-product flex">
                    <div class="col-md-6 home-product__info order-2">
                        <div class="home-ribbon">
                            <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#9159FF" stroke="black"/>
                            </svg>
                            <div class="bookmark-ribbon bookmark-ribbon--violet">
                                <strong>Tủ sách Dũng Mori</strong>
                            </div>
                        </div>
                        <div class="pl-md-5 mt-3">
                            <p class="font-montserrat font-bold text-lg">Tủ sách được viết bằng phương pháp nghiên cứu độc quyền từ Dũng Mori</p>
                            <p class="font-quicksand text-sm">Sau gần 10 năm nghiên cứu và phát triển phương pháp dạy chữ Hán, ngữ pháp, Dũng Mori hiện đã cho ra mắt bộ sách để học kèm cùng các khóa học luyện thi JLPT của Dũng Mori, giúp tăng gấp 3 hiệu quả</p>
                            <ul class="flex home-tabs">
                                <li class="home-tab">
                                    <a href="https://sach.dungmori.com/" class="home-tab__inner home-tab__inner--violet">Sơ cấp</a>
                                </li>
                                <li class="home-tab">
                                    <a href="https://sach.dungmori.com/" class="home-tab__inner home-tab__inner--violet">Trung cấp</a>
                                </li>
                                <li class="home-tab">
                                    <a href="https://sach.dungmori.com/" class="home-tab__inner home-tab__inner--violet">Cao cấp</a>
                                </li>
                            </ul>
                            <a href="https://sach.dungmori.com/" target="_blank" class="home-product__button">Xem chi tiết</a>
                        </div>
                    </div>
                    <div class="col-md-6 pc">
                        <div class="home-preview flex">
                            <div class="home-preview__inside flex-fill flex-1 tab-content">
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/home-ts.png')}}" class="tab-pane active lazyload object-cover" />
                            </div>
                            <div class="home-preview__arrow-right"></div>
                            <div class="home-preview__icon home-preview__icon--left">
                                <img alt="" data-src="{{asset('assets/img/new_home/12-2021/reading-mori.svg')}}" class="lazyload object-cover"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="home-philosophy">
            @include('frontend.home.components.section_title',['title' => '人生を変える教育へ', 'description' => 'triết lí giáo dục tại Dũng mori'])
            <div class="home-philosophy__icon container">
                <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/running-mori.svg')}}" style="transform: scale(-1, 1)" />
            </div>
            <div class="container">
                <div class="flex home-philosophy__content">
                    <div class="home-philosophy__text">
                        <div class="text-ghost">
                            Bắt đầu bằng việc <strong>học tiếng Nhật</strong>, tại Dũng Mori <br />
                            chúng tôi cùng học trò của mình thắp lên và trao giữ <strong>lửa học tập </strong><br />
                            hướng tới xây dựng một nền <br />
                            <strong class="text-ghost">"Giáo dục thay đổi cuộc đời"</strong>
                        </div>
                        <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/triet-ly-giao-duc.png')}}" />
                    </div>
                    <div class="home-philosophy__image ml-5 pc">
                        <div class="mr-5">
                            <div class="home-teacher flex flex-column bg-orange-75">
                                <div class="home-teacher__inside">
                                    <img class="lazyload object-cover" data-src="{{ asset('assets/img/new_home/12-2021/co-thanh.png') }}" alt="" style="width: 110%; left: 0; bottom: -17px;">
                                </div>
                                <div class="mt-4">
                                    Thạc sĩ đại học Ngoại ngữ Tokyo, chuyên ngành Giáo dục tiếng Nhật
                                    Học bổng toàn phần Chính phủ
                                    Nhật Bản MEXT
                                </div>
                                <div class="home-teacher-name" id="thanh">
                                    <div class="home-ribbon">
                                        <svg width="17" height="60" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                            <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#F27712" stroke="black"/>
                                        </svg>
                                        <div
                                                class="bookmark-ribbon bookmark-ribbon--yellow"
                                        >
                                            <strong>Cô Thanh</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="home-teacher__arrow-left home-teacher__arrow--orange"></div>
                            </div>
                        </div>
                        <div>
                            <div class="home-teacher flex flex-column bg-green-25" style="width: 220px">
                                <div class="home-teacher__inside">
                                    <img class="lazyload object-cover" data-src="{{ asset('assets/img/new_home/12-2021/thay-dung.png') }}" alt="" style="width: 90%; left: 9px; bottom: 0">
                                </div>
                                <div class="mt-4">
                                    Cử nhân đại học Nihon, khoa Giáo dục
                                    Học bổng Mitsubishi
                                </div>
                                <div class="home-teacher-name" id="dung">
                                    <div class="home-ribbon">
                                        <svg width="17" height="60" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                            <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#96D962" stroke="black"/>
                                        </svg>
                                        <div
                                                class="bookmark-ribbon bookmark-ribbon--green"
                                        >
                                            <strong>Thầy Dũng</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="home-teacher__arrow-left home-teacher__arrow--green"></div>
                            </div>
                            <div class="home-teacher flex flex-column bg-blue-25 mt-5" style="width: 220px">
                                <div class="home-teacher__inside">
                                    <img class="lazyload object-cover" data-src="{{ asset('assets/img/new_home/12-2021/thay-nghia.png') }}" alt="" style="width: 90%; left: 0; bottom: 0">
                                </div>
                                <div class="mt-4">
                                    Cử nhân Luật Đại học Aomori Chuo <br/>
                                    Học bổng toàn phần của Tập đoàn Tokyo Marine, hệ thạc sĩ KHTT <br />Đại học Quốc gia Nagoya
                                </div>
                                <div class="home-teacher-name" id="nghia">
                                    <div class="home-ribbon">
                                        <svg width="17" height="60" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                                            <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#9097AC" stroke="black"/>
                                        </svg>
                                        <div
                                            class="bookmark-ribbon bookmark-ribbon--blue"
                                        >
                                            <strong>Thầy Nghĩa</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="home-teacher__arrow-left home-teacher__arrow--blue"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="home-analysis p-5">
            <div class="home-analysis container">
                <div class="home-analysis__item">
                    <div class="home-analysis__item-icon">
                        <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/hoc-vien-hai-long.svg')}}" alt=""/>
                    </div>
                    <div class="home-analysis__item-main">
                        <span class="counter">99</span>%
                    </div>
                    <div class="home-analysis__item-color bg-green-25"></div>
                    <div class="home-analysis__item-stat">
                        Học viên hài lòng với khóa học
                    </div>
                </div>
                <div class="home-analysis__item">
                    <div class="home-analysis__item-icon">
                        <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/hoc-vien-theo-hoc.svg')}}" alt=""/>
                    </div>
                    <div class="home-analysis__item-main">
                        <span class="counter">{{ number_format($countStudents) }}</span>
                    </div>
                    <div class="home-analysis__item-color bg-orange-75"></div>
                    <div class="home-analysis__item-stat">
                        Học viên theo học
                    </div>
                </div>
                <div class="home-analysis__item">
                    <div class="home-analysis__item-icon">
                        <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/hoc-vien-do.svg')}}" alt=""/>
                    </div>
                    <div class="home-analysis__item-main">
                        <span class="counter">{{ number_format($countStudents * 70 / 100) }}</span>
                    </div>
                    <div class="home-analysis__item-color bg-blue-25"></div>
                    <div class="home-analysis__item-stat">
                        Học viên đỗ JLPT
                    </div>
                </div>
                <div class="home-analysis__item">
                    <div class="home-analysis__item-icon">
                        <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/website-so-mot.svg')}}" alt=""/>
                    </div>
                    <div class="home-analysis__item-main">
                        <span>#1</span>
                    </div>
                    <div class="home-analysis__item-color bg-gray-25"></div>
                    <div class="home-analysis__item-stat">
                        Dành cho người học tiếng Nhật
                    </div>
                </div>
            </div>
        </div>
        <div class="home-product">
            @include('frontend.home.components.section_title',['title' => 'ニュース', 'description' => 'báo chí nói gì về Dũng Mori'])
            <div class="home-news container">
                <div class="home-news__column mr-5">
                    <a class="home-news__item home-news__item-lg" href="https://dantri.com.vn/giao-duc-huong-nghiep/dung-mori-va-buoc-ngoat-tro-thanh-nha-dao-tao-tieng-nhat-chuyen-nghiep-20211007170352717.htm" target="_blank">
                        <div class="home-news__item-corner">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/corner.svg')}}" />
                        </div>
                        <div class="home-news__item-thumbnail">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/hop-tac-dao-tao-cao-dang.jpg')}}" alt=""/>
                        </div>
                        <div class="flex items-center mt-5">
                            <div class="home-news__item-source">
                                <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/logo-dan-tri.svg')}}" alt=""/>
                            </div>
                            <div class="home-news__item-desc ml-5 text-lg">
                                Dũng Mori và bước ngoặt trở thành nhà đào tạo tiếng Nhật chuyên nghiệp
                            </div>
                        </div>
                    </a>
                </div>
                <div class="home-news__column">
                    <a class="home-news__item home-news__item-sm" href="https://vietnamnet.vn/vn/giao-duc/guong-mat-tre/chang-trai-8x-quyet-tam-xay-he-sinh-thai-hoc-tieng-nhat-toan-dien-cho-nguoi-viet-763075.html" target="_blank">
                        <div class="home-news__item-corner">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/corner.svg')}}" />
                        </div>
                        <div class="home-news__item-thumbnail">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/tin-tuc-nguyen-van-dung.jpg')}}" alt=""/>
                        </div>
                        <div>
                            <div class="home-news__item-source">
                                <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/logo-vietnamnet.svg')}}" alt="" />
                            </div>
                            <div class="home-news__item-desc mt-3 text-lg">
                                Chàng trai 8x quyết tâm xây hệ sinh thái học tiếng Nhật toàn diện cho người Việt
                            </div>
                        </div>
                    </a>
                    <a class="home-news__item home-news__item-sm mt-5" href="https://svvn.tienphong.vn/song-o-nhat-ban-nhung-chang-trai-van-dieu-hanh-cong-ty-giang-day-tieng-nhat-tai-viet-nam-post1368028.tpo" target="_blank">
                        <div class="home-news__item-corner">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/corner.svg')}}" />
                        </div>
                        <div class="home-news__item-thumbnail">
                            <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/dung-mori-chuyen-nghiep.jpg')}}" alt=""/>
                        </div>
                        <div>
                            <div class="home-news__item-source bg-gray-25">
                                <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/logo-svvn.png')}}" alt=""/>
                            </div>
                            <div class="home-news__item-desc text-lg">
                                Sống ở Nhật Bản nhưng chàng trai vẫn điều hành công ty giảng dạy tiếng Nhật tại Việt Nam
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
        <div class="home-product">
            @include('frontend.home.components.section_title',['title' => '新着', 'description' => 'Tin tức nổi bật'])
            <div class="home-blog container">
                @foreach ($featureNews as $news)
                    <div class="home-blog__column mt-5 flex">
                        <a class="home-news__item home-news__item-md" href="{{url('/bai-viet')}}/{{ $news['id'] }}-{{ $news['url']}}">
                            <div class="home-news__item-corner">
                                <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/corner.svg')}}" />
                            </div>
                            <div class="home-news__item-thumbnail">
                                <img class="lazyload object-cover" data-src="{{url('/cdn/blog/default')}}/{{ $news['image_name'] }}" alt="" />
                            </div>
                            <div class="flex items-center mt-5">
                                <div class="home-news__item-source">
                                    <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/blog-icon.svg')}}" alt=""/>
                                </div>
                                <div class="home-news__item-desc ml-5 text-md">
                                    {{ $news['title'] }}
                                </div>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
            <div class="container text-center font-quicksand font-600 mt-5 text-lg">
                Xem thêm về <a href="{{route('blog.index')}}">các tin tức khác</a>
            </div>
        </div>
        <div class="home-feedback">
            @include('frontend.home.components.section_title',['title' => '学生たちから', 'description' => 'Học viên của Dũng Mori'])
            <div id="student-feedback-slider" class="home-feedback__slider">
                @foreach ($feedbacks as $key => $feedback)
                    <div class="home-feedback__slide slick-slide">
                        <div class="home-feedback__slide-inner">
                            <div class="home-feedback__slide-image">
                                <img class="lazyload object-cover" data-src="{{url('/cdn/feedback/default/' . $feedback->image)}}" alt="">
                                <div class="home-feedback__slide-content {{ ($key % 2 != 0) ? 'bg-green-75' : array('bg-orange-75', 'bg-blue-50')[array_rand(array('bg-orange-75', 'bg-blue-50'))] }}">
                                    {{ $feedback->content }}
                                    <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.0272 15.461L14.0281 0H0L14.0272 15.461Z" fill="#96D962"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="home-feedback__slide-info text-center">
                                <div class="fb_name">{{$feedback->users['name'] ?? ''}}</div>
                                <span class="fb_job">{{$feedback->user_job ?? ''}}</span>
{{--                                <span class="fb_add">{{$feedback->current_course}}</span>--}}
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="home-partner container">
            @include('frontend.home.components.section_title',['title' => '導入企業', 'description' => 'Doanh nghiệp hợp tác đào tạo'])
            <div class="home-partner__list">
                <img data-src="{{asset('assets/img/new_home/12-2021/canon-logo.svg')}}" class="home-partner__item lazyload object-cover" alt="" />
                <img data-src="{{asset('assets/img/new_home/12-2021/hitachi.svg')}}" class="home-partner__item lazyload object-cover" alt="" />
                <img data-src="{{asset('assets/img/new_home/12-2021/fsoft.svg')}}" class="home-partner__item lazyload object-cover" alt="" />
                <img data-src="{{asset('assets/img/new_home/12-2021/nissan.svg')}}" class="home-partner__item lazyload object-cover" alt="" />
                <img data-src="{{asset('assets/img/new_home/12-2021/maedakosen.svg')}}" class="home-partner__item lazyload object-cover" alt="" />
                <img data-src="{{asset('assets/img/new_home/12-2021/aeon.svg')}}" class="home-partner__item lazyload object-cover" alt="" />
                <img data-src="{{asset('assets/img/new_home/12-2021/gmo.png')}}" class="home-partner__item lazyload object-cover" alt="" />
                <img data-src="{{asset('assets/img/new_home/12-2021/rikkei.png')}}" class="home-partner__item lazyload object-cover" alt="" />
            </div>
        </div>
        <div class="home-divider"></div>
        <div class="home-youtube container">
            <div class="home-news__column mr-5">
                <div class="home-ribbon pc">
                    <svg width="17" height="82" viewBox="0 0 17 82" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.66699 7.04883L10.5573 81.2806" stroke="black" stroke-width="3"/>
                        <circle cx="6.6674" cy="7.04928" r="5.69447" transform="rotate(-3 6.6674 7.04928)" fill="#EC342C" stroke="black"/>
                    </svg>
                    <div class="bookmark-ribbon bookmark-ribbon--gray text-xl">
                        <div class="flex items-center">
                            <svg width="38" height="38" viewBox="0 0 38 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M34.0112 26.8622C33.7025 28.483 32.3904 29.7179 30.7696 29.9494C28.2227 30.3353 23.9779 30.7984 19.1927 30.7984C14.4848 30.7984 10.2399 30.3353 7.61581 29.9494C5.99504 29.7179 4.68299 28.483 4.37427 26.8622C4.06555 25.0871 3.75684 22.463 3.75684 19.2215C3.75684 15.9799 4.06555 13.3558 4.37427 11.5807C4.68299 9.95992 5.99504 8.72504 7.61581 8.49351C10.1627 8.10761 14.4076 7.64453 19.1927 7.64453C23.9779 7.64453 28.1455 8.10761 30.7696 8.49351C32.3904 8.72504 33.7025 9.95992 34.0112 11.5807C34.3199 13.3558 34.7058 15.9799 34.7058 19.2215C34.6286 22.463 34.3199 25.0871 34.0112 26.8622Z" fill="#EC342C"/>
                                <path d="M16.1064 24.6245V13.8193L25.368 19.2219L16.1064 24.6245Z" fill="white"/>
                            </svg>
                            <strong>Youtube - </strong>
                            <div class="ml-3">Dung Mori's channel</div>
                        </div>
                    </div>
                </div>
                <div class="pl-5">
                    <div class="home-youtube__title">XEM VIDEO MỚI NHẤT</div>
                    <div>
                        <div class="flex items-center">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="8" fill="#C4C4C4"/>
                                <path d="M10 15.5V8.5L16 12L10 15.5Z" fill="white"/>
                            </svg>
                            <div class="ml-3"><a href="https://www.youtube.com/watch?v=fVVAKpvMmzA" target="_blank" class="home-link">Làm sao để tăng thêm 20-30 điểm Nghe Hiểu JLPT ??? </a></div>
                        </div>
                        <div class="flex items-center">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="8" fill="#C4C4C4"/>
                                <path d="M10 15.5V8.5L16 12L10 15.5Z" fill="white"/>
                            </svg>
                            <div class="ml-3"><a href="https://www.youtube.com/watch?v=BaIUXiPK8h4" target="_blank" class="home-link">Tổng hợp kính ngữ siêu nhanh gọn trước kì thi JLPT</a></div>
                        </div>
                        <div class="flex items-center">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="8" fill="#C4C4C4"/>
                                <path d="M10 15.5V8.5L16 12L10 15.5Z" fill="white"/>
                            </svg>
                            <div class="ml-3"><a href="https://www.youtube.com/watch?v=ufmhBKGBarU" target="_blank" class="home-link">Series tổng hợp kiến thức N5 - Trợ từ ①</a></div>
                        </div>
                    </div>
                    <div class="">
                        <a href="https://youtube.com/c/dungmori" target="_blank" class="home-youtube__button">+ Đăng ký theo dõi</a>
                    </div>
                </div>
            </div>
            <div class="home-news__column">
                <a class="home-news__item home-news__item-lg home-link" href="https://www.youtube.com/watch?v=4ghxAHr4z2k" target="_blank">
                    <div class="home-news__item-corner">
                        <img class="lazyload object-cover" data-src="{{asset('assets/img/new_home/12-2021/corner-red.png')}}" />
                    </div>
                    <div class="home-news__item-thumbnail">
                        <img class="lazyload object-cover" data-src="https://img.youtube.com/vi/4ghxAHr4z2k/0.jpg" alt="" style="border-radius: 18px;"/>
                    </div>
                    <div class="flex items-center mt-5">
                        <div class="home-news__item-source">
                            <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M44.7904 35.1486C44.3757 37.3259 42.6131 38.9848 40.4358 39.2958C37.0143 39.8142 31.3119 40.4363 24.8837 40.4363C18.5591 40.4363 12.8567 39.8142 9.33151 39.2958C7.15421 38.9848 5.39163 37.3259 4.97691 35.1486C4.56219 32.7639 4.14746 29.2388 4.14746 24.8842C4.14746 20.5296 4.56219 17.0044 4.97691 14.6198C5.39163 12.4425 7.15421 10.7836 9.33151 10.4725C12.753 9.95412 18.4554 9.33203 24.8837 9.33203C31.3119 9.33203 36.9107 9.95412 40.4358 10.4725C42.6131 10.7836 44.3757 12.4425 44.7904 14.6198C45.2051 17.0044 45.7235 20.5296 45.7235 24.8842C45.6199 29.2388 45.2051 32.7639 44.7904 35.1486Z" fill="#EC342C"/>
                                <path d="M20.7363 32.1413V17.626L33.178 24.8836L20.7363 32.1413Z" fill="white"/>
                            </svg>
                        </div>
                        <div class="home-news__item-desc ml-5 text-lg">
                            MUỐN ĐẠT ĐIỂM NGHE TUYỆT ĐỐI - HỌC NGAY MẪU NGỮ PHÁP NÀY
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>

{{--    @if (count($userAchivements))--}}
{{--        <div class="honor-box flex justify-center z-10">--}}
{{--            <div class="flex">--}}
{{--                <img class="lazyload object-cover honer-image" data-src="{{ url('assets/img/gocvinhdanh.png') }}" />--}}
{{--                <div class="slick-box">--}}
{{--                    <div id="honer_slick">--}}
{{--                        @foreach ($userAchivements as $userAchivement)--}}
{{--                            <div style="height: 75px; display: flex">--}}
{{--                                <div class="flex items-center border honer-item">--}}
{{--                                    <div class="relative">--}}
{{--                                        <img class="absolute image-border" src="{{ url('assets/img/rewardava.png') }}" />--}}
{{--                                        @if ($userAchivement->avatar)--}}
{{--                                            <img class="w-11 h-11 rounded-full relative z-10" src="{{ url('cdn/avatar/small') }}/{{ $userAchivement->avatar }}" />--}}
{{--                                        @else--}}
{{--                                            <img class="w-11 h-11 rounded-full relative z-10" src="{{ url('assets/img/default-avatar.jpg') }}" />--}}
{{--                                        @endif--}}
{{--                                    </div>--}}
{{--                                    <div class="flex items-center h-11 flex-1 justify-center honer-text">--}}
{{--                                        <span class="text-[22px] font-bold">{{ $userAchivement->name }}</span>--}}
{{--                                        <span class="text-[17px] font-bold honer-text-after">Đỗ JLPT {{ $userAchivement->level }} - {{ $userAchivement->score }} ĐIỂM</span>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                                <div class="flex items-center justify-center ml-3">--}}
{{--                                    <img class="h-5.5 w-5.5" src="{{ url('assets/img/star4.png') }}" />--}}
{{--                                    <a--}}
{{--                                        data-fancybox="images"--}}
{{--                                        href="{{ url('cdn/achivement/default') }}/{{ $userAchivement->image }}"--}}
{{--                                    >--}}
{{--                                        <div class="w-30 text-base font-bold text-base underline text-white p-2.5 cursor-pointer">--}}
{{--                                            Xem Kết Quả--}}
{{--                                        </div>--}}
{{--                                    </a>--}}
{{--                                    <img class="h-5.5 w-5.5" src="{{ url('assets/img/star4.png') }}" />--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        @endforeach--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    @endif--}}


    {{-- nhúng bộ chat của fb messager vào web --}}
    <div class="fb-customerchat" page_id="1595926847401625" minimized="true"></div>
@stop
@section('fixed-panel')
    {{-- <a href="{{url('/thi-thu')}}">
    <a href="#">
        <div class="thithu-btn" style="background: red; color: #fff;">
            <img class="lazyload object-cover" data-src="{{asset('assets/img/fixed-thithu.png')}}" alt=""/>
            <p>THI NGAY</p>
        </div>
    </a> --}}


@stop

{{-- @if(strpos( $_SERVER['HTTP_HOST'], 'localhost') !== false)
<div id="fb-root"></div>
@endif --}}
@section('footer-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>

    {{-- hiệu ứng --}}
    <link rel="stylesheet" type="text/css" href="{{asset('plugin/animate.css/animate.min.css')}}" media="screen"/>

    <script src="{{asset('plugin/wow/dist/wow.min.js')}}"></script>
    <script>
        new WOW().init();
    </script>
    {{-- tài liệu hiệu ứng
        https://github.com/daneden/animate.css
        https://wowjs.uk
    --}}
    <script defer src="{{asset('plugin/classie/classie.js')}}"></script>
{{--    <script src="{{asset('plugin/lodash/lodash.js')}}"></script>--}}
    <script defer src="{{asset('assets/js/home.js')}}?{{filemtime('assets/js/home.js')}}"></script>

    <script>
        $('#honer_slick').slick({
            autoplay: true,
            autoplaySpeed: 5000,
            arrows: false,
            loop: true,
            vertical: true,
            verticalSwiping: true,
        });
    </script>

    <script type="text/javascript">
                {{--Student feedback slick--}}
        $('#student-feedback-slider').slick({
            slidesToShow: 5,
            slidesToScroll: 3,
            loop: true,
            autoplay: true,
            autoplaySpeed: 2000,
            centerMode: false,
            responsive: [
              {
                breakpoint: 1366,
                settings: {
                  slidesToShow: 2,
                  slidesToScroll: 2,
                  autoplay: true,
                  centerMode: true,
                  centerPadding: '15%',
                }
              },
              {
                breakpoint: 1024,
                settings: {
                  slidesToShow: 2,
                  slidesToScroll: 2,
                  autoplay: true,
                  centerMode: true,
                  centerPadding: '70px',
                }
              },
              {
                breakpoint: 768,
                settings: {
                  slidesToShow: 1,
                  slidesToScroll: 1,
                  centerMode: true,
                  centerPadding: '120px',
                }
              },
              {
                breakpoint: 600,
                settings: {
                  slidesToShow: 1,
                  centerMode: true,
                  centerPadding: '13%',
                }
              },
            ]
        });
        $('#home-header-slider').slick({
            slidesToShow: 2,
            slidesToScroll: 1,
            autoplay: true,
            autoplaySpeed: 2000,
            arrows: false,
            loop: true,
            centerMode: false,
            variableWidth: true,
            responsive: [
              {
                breakpoint: 1024,
                settings: {
                  slidesToShow: 3,
                  slidesToScroll: 2,
                  autoplay: true,
                  centerMode: true,
                  centerPadding: '70px',
                }
              },
                {
                  breakpoint: 768,
                  settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    centerMode: true,
                    centerPadding: '120px',
                  }
                },
              {
                breakpoint: 600,
                settings: {
                  slidesToShow: 1,
                  centerMode: true,
                  centerPadding: '13%',
                }
              },
            ]
        });
        $("#nxt_sld").on("click", function(e) {
            e.preventDefault();
            $("#home-header-slider").slick("slickNext")
        });
        $("#prv_sld").on("click", function(e) {
            e.preventDefault();
            $("#home-header-slider").slick("slickPrev")
        });
    </script>
    <script>(function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1768213996826394';
        fjs.parentNode.insertBefore(js, fjs);
      }(document, 'script', 'facebook-jssdk'));
    </script>
    <script type="text/javascript">
        function goMJT() {
            setTimeout(function() {
                window.location.href = '{{ route('thi-thu.index') }}';
            }, 1000);
        }
    </script>
    {{-- nhúng bộ chat của fb messager vào web --}}
    {{-- <script>
    window.fbAsyncInit = function() {
      FB.init({
        appId            : '1768213996826394',
        autoLogAppEvents : true,
        xfbml            : true,
        version          : 'v2.11'
      });
    };
    (function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "https://connect.facebook.net/en_US/sdk/xfbml.customerchat.js";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
    </script>
   --}}
@stop
