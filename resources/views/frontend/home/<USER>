@extends('frontend._layouts.default')

@section('title') Dungmori - Nền tảng học tiếng Nhật Online Số 1 tại Việt Nam @stop
@section('description') Học tiếng Nhật online cùng với các giáo viên uy tín hàng đầu trong lĩnh vực giảng dạy tiếng Nhật tại Việt Nam và các giáo viên bản địa có kinh nghiệm, tham gia khóa học tiếng Nhật cơ bản dành cho người mới bắt đầu HOÀN TOÀN MIỄN PHÍ và các khóa học tiếng Nh<PERSON> trung cấp, cao cấp, tham gia các kì thi thử tiếng Nhật JLPT miễn phí đư<PERSON> tổ chức thường xuyên giú<PERSON> bạn n<PERSON>h chóng tiếp cận và nắm vững tiếng Nhật. @stop
@section('keywords') <PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON>, ti<PERSON><PERSON>, h<PERSON><PERSON>, online, d<PERSON> hi<PERSON>, ni<PERSON><PERSON>, Japanese, mi<PERSON>n phí, n1, n2, n3, n4, n5 @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('header-css')
<style type="text/css">

    #comment-container{border: 1px solid #ccc; border-radius: 12px; padding: 30px 25px;}
    #comment-content{border-radius: 6px;  }
    .comment-item{padding: 30px 0 !important;}
    .love-btn{margin-right: 10px; cursor: pointer;}
    .list-comments .comment-item .comment-content .name b {color: #404040 !important; }
    .child-comment-item .comment-content .child-name b {color: #404040 !important; }
</style>

@stop

@section('content')

<div class="container feedback-page" style="width: 100%; background: url('assets/img/fback/bg.png'); background-size: 100%;" >
    <div class="row" style="width: 860px; padding-top: 50px; margin: 0 auto;">
        <div class="col-md-8 col-md-push-3" style="left: 17%">

            <div class="section-title text-center" style="margin-bottom: 30px;">
                <h2> <span class="heading-200" style="color: #fff;">Top bài đăng tiêu biểu của học viên</h2>
            </div><!-- .section-title end -->

        </div><!-- .col-md-6 end -->
        <div class="col-md-12">
            <div class="slider single-item">
                <div>
                    <img src="{{asset('assets/img/fback/hinh1.png')}}"/>
                </div>
                <div>
                    <img src="{{asset('assets/img/fback/hinh3.png')}}"/>
                </div>
            </div>

        </div><!-- .col-md-12 end -->
    </div><!-- .row end -->
</div><!-- .container end -->

<div class="container feedback-page pb-5" style="width: 100%;" >
    <div class="row" style="width: 860px; padding-top: 50px; margin: 0 auto;">
        <img class="ftext" style="width: 100%; float: left;" src="{{asset('assets/img/fback/text2.png')}}"/>
        <img class="ftext-mb" style="width: 100%; float: left;" src="{{asset('assets/img/fback/mobilet.png')}}"/>
        <div style="width: 100%; float: left; margin-top: 40px;" class="comment-container" id="comment-container">
            <div class="tab-content">
                <div id="user-comment-content" class="tab-pane active">
                    @if(Auth::check())
                        <comments-feedback meid="{{ Auth::user()->id }}"
                                avatar="{{ Auth::user()->avatar }}"
                                tbid="99999999"
                                tbname="feedback"
                                num-posts="15"
                                background="#fff"
                                ref="comment">
                        </comments-feedback>
                    @else
                        <comments-feedback tbid="99999999"
                                tbname="feedback"
                                num-posts="15"
                                background="#fff"
                                ref="comment">
                        </comments-feedback>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@stop


@section('footer-js')
    <script src="{{asset('plugin/slick/slick.min.js')}}"></script>
    <script src="{{ asset('/plugin/jquery/lodash.min.js') }}"></script>
    <script type="text/javascript">
                $('.single-item').slick({
                  dots: true,
                  infinite: true,
                  speed: 300,
                  slidesToShow: 1,
                  adaptiveHeight: true
                });
            </script>
    <script type="text/javascript"> $(".mn-feedback").addClass("active"); </script>
    <script type="text/javascript"> new Vue({ el: '#comment-container' }); </script>
@stop
