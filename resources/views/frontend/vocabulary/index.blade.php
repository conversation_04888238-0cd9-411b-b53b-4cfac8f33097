<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="icon" href="{{asset('assets/img/new_home/06-2024/dungmori-fav.png')}}"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="preload" href="{{asset('css/base.css')}}?{{filemtime('css/base.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <link rel="preload" href="{{asset('css/plugins.css')}}?{{filemtime('css/plugins.css')}}" as="style"
          onload="this.onload=null;this.rel='stylesheet'" media="screen">
    <link rel="stylesheet" href="{{asset('assets/css/styles.css')}}?{{filemtime('assets/css/styles.css')}}"
          media="screen">

    <link rel="stylesheet" href="{{ asset('css/course/basic.css') }}">
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .pc-header.vocabulary .header-menu {
            display: flex !important;
        }

        .vocabulary .header-menu__item {
            color: #07403F !important;
        }

        .vocabulary .header-menu__item:hover {
            color: white !important;
        }

        .vocabulary-container {
            display: flex;
            min-height: calc(100vh);
        }

        .nav-menu {
            padding-top: 76px;
            width: 280px;
            background-color: #f8f9fa;
            border-right: 1px solid #e9ecef;
            transition: all 0.3s;
        }

        .nav-menu.collapsed {
            width: 60px;
        }

        .nav-menu-header {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-menu-title {
            font-size: 18px;
            font-weight: bold;
            color: #07403F;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .toggle-menu {
            cursor: pointer;
            color: #07403F;
        }

        .nav-menu-items {
            padding: 20px;
        }

        .nav-menu-item {
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s;
            border-radius: 12px;
        }

        .nav-menu-item:hover {
            background-color: #e9ecef;
        }

        .nav-menu-item.active {
            background-color: #CCF8D1;
            color: #07403F;
            font-weight: bold;
        }

        .nav-menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .nav-menu-item-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .nav-submenu {
            padding-left: 30px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }

        .nav-submenu.open {
            max-height: 500px;
        }

        .fa-chevron-down {
            transition: transform 0.3s ease;
            transform: rotate(-90deg); /* Default: arrow points right */
        }

        .fa-chevron-down.open {
            transform: rotate(0deg); /* When open: arrow points down */
        }

        .nav-submenu-item {
            padding: 8px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #6c757d;
            transition: all 0.2s;
            border-radius: 12px;
        }

        .nav-submenu-item:hover {
            background-color: #e9ecef;
        }

        .nav-submenu-item.active {
            color: #07403F;
            font-weight: bold;
        }

        .content-area {
            flex: 1;
            padding: 20px;
            background-color: #F4F5FA;
            background-image: url('/images/vocabulary/vocabulary-pattern.svg');
            background-repeat: no-repeat;
            background-position: top center;
            background-size: auto;
            position: relative;
            padding-top: 76px;
        }

        /* Overlay để làm mờ hình nền và giúp nội dung dễ đọc hơn */
        .content-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0;
        }

        .content-section {
            display: none;
            position: relative;
            z-index: 1;
        }

        /* Card styles */
        .vocabulary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .vocabulary-card {
            width: calc(20% - 16px);
            background-color: #fff;
            border-radius: 24px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .vocabulary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        }

        .vocabulary-card-image {
            width: 100%;
            height: 130px;
            object-fit: cover;
            border-bottom: 1px solid #eee;
        }

        .vocabulary-card-content {
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-left: 20px;
            padding-right: 15px;
        }

        .vocabulary-card-name {
            text-align: right;
            padding: 20px 20px 0 20px;
        }

        .vocabulary-card-name h1 {
            font-size: 110px;
            background: linear-gradient(white, #57D061);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: bold;
        }

        .vocabulary-card + .vocabulary-card-incoming {
            background-image: url('/images/vocabulary/bg-incoming.svg');
        }

        /*khi class vocabulary-card có thêm class card-favorite thi width = 100%*/
        .vocabulary-card.card-favorite {
            width: 100%;
        }

        .vocabulary-card-incoming-text p {
            /*// style cho text  nam duoi cung*/
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            text-align: center;
            font-size: 20px;
            color: #07403F;
            font-weight: bold;
            margin: 0;
            padding: 28px 0;
        }

        .vocabulary-card-title {
            font-size: 16px;
            font-weight: bold;
            color: #07403F;
            /*margin-bottom: 8px;*/
            /*height: 40px;*/
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .vocabulary-card-count {
            font-size: 14px;
            color: #6c757d;
            display: flex;
            align-items: center;
        }

        .vocabulary-card-count i {
            margin-right: 5px;
            font-size: 12px;
        }

        .vocabulary-card-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .badge-vip {
            background-color: #FFD700;
            color: #000;
        }

        .badge-free {
            background-color: #28a745;
            color: #fff;
        }

        .badge-favorite {
            background-color: #dc3545;
            color: #fff;
        }

        .badge-new {
            background-color: #EF6D13;
            color: #fff;
            margin: 12px 0 0 14px;
        }

        .vocabulary-card-status {
            position: absolute;
            top: 0;
            left: 0;
        }

        .vocabulary-line {
            position: absolute;
            left: 10px;
            height: 41px;
            width: 2px;
            /*background: #57D061;*/
            border-radius: 50px;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .vocabulary-card {
                width: calc(25% - 15px);
            }
        }

        @media (max-width: 992px) {
            .vocabulary-card {
                width: calc(33.333% - 14px);
            }
        }

        @media (max-width: 768px) {
            .vocabulary-card {
                width: calc(50% - 10px);
            }
        }

        @media (max-width: 576px) {
            .vocabulary-card {
                width: 100%;
            }
        }

        /* Overview styles */
        .overview-content {
            padding: 0 !important;
        }

        .vocabulary-section {
            margin-bottom: 50px;
            padding: 0 20px;
        }

        .vocabulary-section-title {
            font-size: 24px;
            font-weight: bold;
            color: #07403F;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #CCF8D1;
            display: flex;
            align-items: center;
        }

        .vocabulary-section-title .badge {
            margin-left: 10px;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 20px;
            text-transform: uppercase;
        }

        .view-all-btn {
            text-align: center;
            margin-top: 20px;
        }

        .view-all-btn button {
            background-color: #07403F;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .view-all-btn button:hover {
            background-color: #0a5e5c;
            transform: translateY(-2px);
        }

        .view-all-btn i {
            margin-left: 5px;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #07403F;
        }

        .section-content {
            /*background-color: rgba(255, 255, 255, 0.9);*/
            padding: 25px;
            /*border-radius: 10px;*/
            min-height: 300px;
            /*box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);*/
            /*backdrop-filter: blur(10px);*/
            /*-webkit-backdrop-filter: blur(10px);*/
            /*border: 1px solid rgba(255, 255, 255, 0.5);*/
            transition: all 0.3s ease;
        }

        .section-content:hover {
            /*box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);*/
            /*transform: translateY(-2px);*/
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                height: 100%;
                z-index: 1000;
                left: 0;
                top: 60px;
                transform: translateX(-100%);
            }

            .nav-menu.open {
                transform: translateX(0);
            }

            .content-area {
                width: 100%;
            }
        }
    </style>

    <link rel="stylesheet" href="{{ asset('css/module/flashcard.css') }}">

    <title>Vocabulary</title>
    <script type="text/javascript">
        let list_vocabulary = {
            "jlpt": {
                "type": "vip",
                "data": [
                    {
                        "id": 1,
                        "name": "Từ vựng N5",
                        "level": "N5",
                        "img_thumb": "https://example.com/n5.jpg",
                        "word_count": "1122",
                        "status": "new", // active, inactive, coming_soon
                        "classify": {learned: 83, temporary: 120, memorized: 50, mastered: 30},
                    },
                    {
                        "id": 2,
                        "name": "Từ vựng N4",
                        "level": "N4",
                        "img_thumb": "https://example.com/n4.jpg",
                        "word_count": "1122",
                        "status": "new",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 3,
                        "name": "Từ vựng N3",
                        "level": "N3",
                        "img_thumb": "https://example.com/n3.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 4,
                        "name": "Từ vựng N2",
                        "level": "N2",
                        "img_thumb": "https://example.com/n2.jpg",
                        "word_count": "1122",
                        "status": "incoming",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 5,
                        "name": "Từ vựng N1",
                        "level": "N1",
                        "img_thumb": "https://example.com/n1.jpg",
                        "word_count": "1122",
                        "status": "incoming",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    }
                ],
                "name": "JLPT"
            },
            "specialized": {
                "type": "free",
                "data": [
                    {
                        "id": 1,
                        "name": "Kaigo",
                        "img_thumb": "https://example.com/kaigo.jpg",
                        "word_count": "1122",
                        "status": "hot",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 2,
                        "name": "Du lịch, khách sạn",
                        "img_thumb": "https://example.com/du-lich.jpg",
                        "word_count": "1122",
                        "status": "hot",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 3,
                        "name": "Nông nghiệp",
                        "img_thumb": "https://example.com/nong-nghiep.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 4,
                        "name": "Đời sống",
                        "img_thumb": "https://example.com/doi-song.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 5,
                        "name": "Tokutei",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 6,
                        "name": "Công nghệ thông tin",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 7,
                        "name": "Công nghệ thông tin",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 8,
                        "name": "Gốm sứ",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 9,
                        "name": "Công nghệ thông tin",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 10,
                        "name": "Xây dựng",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 11,
                        "name": "May mặc",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 12,
                        "name": "Nhà hàng",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 13,
                        "name": "Hải sản",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "active",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 14,
                        "name": "Cơ khí",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "incoming",
                        "classify": {learned: 52, temporary: 120, memorized: 88, mastered: 20},
                    },
                    {
                        "id": 15,
                        "name": "Công nghệ thông tin",
                        "img_thumb": "https://example.com/cong-nghe.jpg",
                        "word_count": "1122",
                        "status": "incoming",
                        "classify": {learned: 83, temporary: 120, memorized: 50, mastered: 30}
                    }
                ],
                "name": "Chuyên ngành"
            },
            "favorite": {
                "type": "favorite",
                "data": [
                    {
                        "id": 1,
                        "name": "Từ vựng của tôi",
                        "img_thumb": "https://example.com/favorite.jpg",
                        "word_count": "1122",
                        "status": "active",
                    }
                ],
                "name": "Từ vựng yêu thích"
            }
        }
        let course_id = 39;
    </script>

</head>
<body>
<div id="vocabulary_page">
    @include('frontend._layouts.menu')

    <div id="vocabulary_content">
        <vocabulary :list_vocabulary="list_vocabulary"></vocabulary>
    </div>
</div>

<script>
    // Toggle menu collapse
    function toggleMenu() {
        const navMenu = document.getElementById('navMenu');
        navMenu.classList.toggle('collapsed');
    }

    // Toggle submenu
    function toggleSubmenu(submenuId) {
        const submenu = document.getElementById(submenuId);
        const isOpen = submenu.classList.toggle('open');

        // Toggle arrow direction for the clicked menu item
        const menuItem = event.currentTarget;
        const arrow = menuItem.querySelector('.fa-chevron-down');

        if (isOpen) {
            // Menu is open, arrow points down
            arrow.classList.add('open');
        } else {
            // Menu is closed, arrow points right
            arrow.classList.remove('open');
        }

        // Close other submenus
        const allSubmenus = document.querySelectorAll('.nav-submenu');
        allSubmenus.forEach(menu => {
            if (menu.id !== submenuId && menu.classList.contains('open')) {
                menu.classList.remove('open');

                // Find the parent menu item for this submenu and rotate its arrow
                const submenuId = menu.id;
                const parentMenuItem = document.querySelector(`.nav-menu-item[onclick*="'${submenuId}'"]`);
                if (parentMenuItem) {
                    const arrow = parentMenuItem.querySelector('.fa-chevron-down');
                    if (arrow) {
                        arrow.classList.remove('open');
                    }
                }
            }
        });
    }

    // Show content section

    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function () {
        const mobileMenuToggle = document.querySelector('.toggle-menu');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function () {
                if (window.innerWidth <= 768) {
                    document.getElementById('navMenu').classList.toggle('open');
                }
            });
        }
    });
</script>

<script src="{{asset('plugin/vue/vue.min.js')}}" type="text/javascript"></script>
<script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>


<script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
<script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>


<script src="{{ asset('/assets/js/vocabulary/vocabulary.js') }}?{{ filemtime('assets/js/vocabulary/vocabulary.js') }}"></script>

</body>
</html>