@extends('frontend._layouts.default')

@section('title') Dungmori - Trang hỗ trợ @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON>y tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<input type="hidden" id="csrf_token" value="{{ csrf_token() }}">

<div class="main">
   <div class="main-center">

      <div class="main-support-left">
         <a class="item-question-heading" href="{{url('')}}/ho-tro" style="color: #a51817;">HỖ TRỢ</a>
         @foreach($listPages as $page)
         <a href="{{url('')}}/ho-tro/{{ $page->url }}" class="item-question {{$page->url}}">{{ $page->title }}</a>
         @endforeach
      </div>

      <div class="main-support-right">
          <div class="block-hotro">
              <div class="block-content row">
                  <div class="col-md-7 block-contact">
                      <form class="form-horizontal" id="form-push-user-opinion">
                          <legend>Liên hệ với chúng tôi</legend>
                          <ul class="error-list" id="error-list">
                            <li v-for="error in errors">@{{ error }}</li>
                          </ul>
                          <div class="form-group">
                              <label class="col-md-4 control-label">Họ và tên</label>
                              <div class="col-md-8">
                                  <input type="text" id="fullname" class="form-control" placeholder="">
                              </div>
                          </div>
                          <div class="form-group">
                              <label class="col-md-4 control-label">Email</label>
                              <div class="col-md-8">
                                  <input type="email" id="user-email" class="form-control" placeholder="" required>
                              </div>
                          </div>
                          <div class="form-group">
                              <label class="col-md-4 control-label">Số điện thoại</label>
                              <div class="col-md-8">
                                  <input type="text" id="phone-number" class="form-control" placeholder="">
                              </div>
                          </div>
                          <div class="form-group">
                              <label class="col-md-4 control-label">Thắc mắc về</label>
                              <div class="col-md-8">
                                <select name="student[country]" class="form-control" id="option-type">
                                  <option value="Khóa học online" >Khóa học Online</option>
                                  <option value="Khóa học offline" >Khóa học Offline</option>
                                </select>
                              </div>
                          </div>
                          <div class="form-group">
                              <label class="col-md-4 control-label">Nội dung thắc mắc</label>
                              <div class="col-md-8">
                                  <textarea class="form-control" id="option-content"></textarea>
                              </div>
                          </div>
                          <div class="form-group">
                              <div class="col-md-8 col-md-offset-4" id="btn-submit">
                                  <div class="dmr-btn" v-on:click="sendEmail()">Gửi ý kiến</div>
                              </div>
                          </div>
                      </form>
                  </div>
                  <div class="col-md-5 support-right">
                      <legend>điện thoại</legend>
                      <p>Tư Vấn về khóa học ONLINE :</p>
                      <p><strong>0969-868-485</strong></p>
                      <p>Tư Vấn về khóa học OFFLINE :</p>
                      <p><strong>0969-856-116</strong></p>
                      <br/>
                      <br/>
                      <legend>Địa chỉ</legend>
                      <p>Nhà liền kề số 03 VNT TOWER SỐ 19 Nguyễn Trãi, Thanh Xuân, Hà Nội.</p>
                      <p class="break-line">&nbsp;</p>
                      <p><em>Giờ làm việc *</em></p>
                      <table>
                         <tbody>
                            <tr>
                               <td style="vertical-align:top; width:140px">thứ 2 - thứ 6</td>
                               <td>7h00 - 11h30&nbsp;<br>
                               13h30 - 21h00</td>
                            </tr>
                            <tr>
                               <td style="vertical-align:top">thứ 7</td>
                               <td>8h00 - 12h00&nbsp;<br>
                               13h00 - 15h00</td>
                            </tr>
                         </tbody>
                      </table>
                  </div>
              </div>
          </div>
      </div>
   </div>
   <div class="loader-area" id="loader-send-email">
     <div class="loader"></div>
   </div>
</div></div>

@stop

@section('footer-js')
  <script> $(".ho-tro").addClass("active");  </script>
  <script src="{{asset('assets/js/support.js')}}?{{filemtime('assets/js/support.js')}}"></script>
  <div id="fb-root"></div>
  <script>(function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) return;
    js = d.createElement(s); js.id = id;
    js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
    fjs.parentNode.insertBefore(js, fjs);
  }(document, 'script', 'facebook-jssdk'));</script>

@stop
