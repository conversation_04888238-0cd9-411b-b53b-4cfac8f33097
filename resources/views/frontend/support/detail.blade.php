@extends('frontend._layouts.default')

@section('title') Dungmori - Trang hỗ trợ @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON>y tiếng Nhật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<div id="fb-root"></div>

<div class="main">
   <div class="main-center">

      <div class="main-support-left">
         <a class="item-question-heading" href="{{url('')}}/ho-tro" style="color: #a51817;">HỖ TRỢ</a>
         @foreach($listPages as $page)
            <a href="{{url('')}}/ho-tro/{{ $page->url }}" class="item-question {{$page->url}}">{{ $page->title }}</a>
         @endforeach
      </div>

      <div class="main-support-right">
         <div class="support-detail-container">
            <h2 class="support-detail-title">{{ $currentPages->title }}</h2>
            <span class="support-detail-info"><i class="fa fa-user"></i> Người đăng : {{ $currentPages->getAuthorName->name }} &nbsp; &nbsp;  <i class="fa fa-calendar-check-o"></i> Đăng ngày : {{ $currentPages->getFriendlyTime() }} </span>
            <div class="support-social-like">
              <div class="fb-share-button" data-href="{{url('')}}/bai-viet/{{ $currentPages->id }}-{{ $currentPages->url }}" data-layout="button_count" data-size="small"><a target="_blank" href="https://www.facebook.com/sharer/sharer.php?u={{url('')}}/bai-viet/{{ $currentPages->id }}-{{ $currentPages->url }}&amp;src=sdkpreparse" class="fb-xfbml-parse-ignore">Chia sẻ</a></div>
               {{-- <div class="fb-like" data-href="{{url('')}}/bai-viet/{{ $currentPages->id }}-{{ $currentPages->url }}" data-layout="button_count" data-action="like" data-size="small" data-show-faces="false" data-share="true"></div> --}}
            </div>
            <div class="support-detail-content">
               <br/><br/>
               <div class="main-content">{!! html_entity_decode($currentPages->content) !!}</div>
            </div>

            <div class="comment-container">
               <div class="comment-heading">
                  <span>Ý kiến học viên</span>
               </div>
               <div class="fb-comments">
                 <comment-fb url="http://dungmori.com{{ $_SERVER['REQUEST_URI'] }}"></comment-fb>
               </div>
            </div>

            </div>
      </div>

   </div>
</div>

@stop

@section('footer-js')
  <script> $(".ho-tro").addClass("active"); </script>
  <script> $(".{{$currentPages->url}}").addClass("active"); </script>
  <script type="text/javascript">
    new Vue({ el: '.fb-comments' }) ;
  </script>
  <script>(function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) return;
    js = d.createElement(s); js.id = id;
    js.src = 'https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v2.11&appId=1548366118800829';
    fjs.parentNode.insertBefore(js, fjs);
  }(document, 'script', 'facebook-jssdk'));</script>
@stop
