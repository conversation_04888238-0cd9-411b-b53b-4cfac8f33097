<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta property="og:title" content="Dungmori - Nền tảng học tiếng Nhật Online Số 1 tại Việt Nam"/>
  <meta property="og:description" content="Hơn 300,000 học viên đã tin tưởng theo học khóa tiếng Nhật online và offline của Dũng Mori, chuyên sâu về luyện thi JLPT, Kaiwa, EJU và tiếng Nhật doanh nghiệp."/>
  <meta property="og:image" content="{{url('assets/img/oglogo.png')}}"/>
  <meta property="og:type" content="website"/>
  <meta property="og:site_name" content="DUNGMORI"/>
  <meta property="og:image:type" content=".png"/>
  <meta property="og:image:width" content="1200"/>
  <meta property="og:image:height" content="628"/>
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <link rel="icon" href="{{url('assets/img/new_home/06-2024/dungmori-fav.png')}}">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          spacing: {
            '1': '4px',
            '2': '8px',
            '3': '12px',
            '4': '16px',
            '5': '20px',
            '6': '24px',
            '100': '400px',
            '200': '800px',
          },
          fontSize: {
            'base': '16px',
            'lg': '24px',
          }
        }
      }
    }
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
  <title>Nhận mã giảm giá Dũng Mori</title>
</head>
<body>
<div class="coupon-wrapper h-screen flex justify-center items-center px-3" id="voucher-screen">
  <div class="flex flex-col items-center h-full w-full md:w-200">
    <img class="w-52 mt-8 mb-16 select-none" alt="Dũng Mori Online" src="{{asset('assets/img/new_home/logo.svg')}}" />
    <img class="w-40 mb-12 select-none" alt="Nhận voucher giảm giá" src="{{asset('assets/img/new_home/12-2021/reading-mori.svg')}}" />
    <div class="text-base mb-3 text-center font-bold text-slate-900 select-none">Chúc mừng bạn nhận được ưu đãi giảm giá khi mua khoá học của Dũng Mori. Chỉ còn 1 bước nữa</div>
    <div class="w-full rounded-lg shadow-md bg-white py-8 px-5 flex flex-col coupon-form" id="form1">
      <input
        type="text"
        name="email"
        v-model="email"
        id="email"
        required
        class="select-none text-base focus:ring-green-500 focus:border-green-500 block w-full py-3 px-5 border-gray-300 rounded-md mb-3"
        placeholder="Nhập email của bạn"
      >
      <button
        :disabled="loading"
        @click="submit"
        class="select-none mb-2 text-base w-full cursor-pointer bg-green-500 hover:bg-green-400 shadow-xl px-5 py-3 inline-block text-white hover:text-white rounded"
        :class="[!loading ? 'bg-green-500 hover:bg-green-400' : 'bg-gray-400 hover:bg-gray-400 select-none']"
        v-text="loading ? 'Đang gửi...' : 'Gửi mã giảm giá tới email'"
      >
      </button>
      <div class="text-red-600 text-center" v-cloak>
        <ul>
          <li v-for="(error, key) in errors" v-text="error[0]"></li>
        </ul>
      </div>
    </div>
  </div>
</div>
</body>
<script src="{{ asset('plugin/vue/vue.min.js') }}"></script>
<script src="{{ asset('plugin/jquery/jquery.min.js') }}"></script>
<script type="text/javascript">
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
</script>
<script type="text/javascript">
  var voucherScreen = new Vue({
    el: '#voucher-screen',
    data: {
      csrf: document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
      email: '',
      loading: false,
      errors: {},
    },
    methods: {
      submit: function() {
        if (this.loading) return;
        var vm = this;
        this.loading = true;
        var data = { csrf: vm.csrf, email: vm.email };
        $.post( window.location.origin + "/coupon/submit", data, function( res ) {
          if (res.code == 200) {
            window.location.href = window.location.origin + '/coupon/thank-you';
          }
        })
        .fail(function(err) {
          if (err.status == 422) {
            vm.errors = err.responseJSON.errors;
          }
          vm.loading = false;
        });
      }
    },
  });
</script>
<style>
  [v-cloak] {
    display: none;
  }
  input {
    -webkit-user-select: text; /* Chrome, Opera, Safari */
    -moz-user-select: text; /* Firefox 2+ */
    -ms-user-select: text; /* IE 10+ */
    user-select: text; /* Standard syntax */
  }
  input[type='text'], input[type='email'], input[type='search'],
  input[type='password'], textarea
  {
    -webkit-user-select: text;
  }
  .coupon-wrapper {
    font-family: Quicksand, Arial, sans-serif;
    background-image: url("/assets/img/new_home/clouds.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
  .coupon-form {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border-radius: 12px;
    border: 1px solid rgba(209, 213, 219, 0.3);
  }
</style>
</html>
