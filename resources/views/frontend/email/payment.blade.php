<h3>B<PERSON><PERSON> đã tạo đơn hàng thành công trên website dungmori.com</h3>
----------------------------------------------------------
<p><h4>Thông tin chi tiết đơn hàng : <strong style="color: #e02323;">{{ $invoice->uuid }}</strong></h4></p>
<p>
Tên khách hàng : {{ $customer['name'] }}<br/>
Số điện thoại : {{ $customer['phone'] }}<br/>
Email : <span style="text-decoration: none !important;" >{{ $customer['email'] }}</span><br/>
    @if (isset($customer->address))
        Địa chỉ : {{ $customer->address }}<br/><br/>
    @endif

Mã đơn hàng : <strong>{{ $invoice->id }}</strong><br/>
<PERSON><PERSON><PERSON> tạo : {{ $invoice->getTimeDetail() }}<br/>
Sản phẩm : {{ $invoice->product_type }} {{ $invoice->product_name }}<br/>
Tổng tiền : <span style="color: #e02323;">{{ number_format($invoice->price) }} đ</span>

{{-- nếu là đơn hàng chuyển khoản bên nhật -> in thêm giá yên --}}
{{--@if($invoice->payment_method_id == 5)--}}
{{--	({{ number_format($invoice->getJpyPrice()) }} ¥)--}}
{{--@endif--}}
<br/><br/>

Hình thức thanh toán : {{ $paymentTutorial->payment_method_name }}<br/>

{{-- nếu là chuyển phát nhanh mã thẻ --}}
@if($invoice->payment_method_id == 4)
    Tên người nhận : {{ json_decode($invoice->info_contact)->name }}<br/>
    Số điện thoại người nhận : {{ json_decode($invoice->info_contact)->phone }}<br/>
    Địa chỉ người nhận : {{ json_decode($invoice->info_contact)->address }}<br/>
@endif

</p>
<p>
    <span>Vui lòng kiểm tra trạng thái đơn hàng tại địa chỉ: </span><br/>
    <a style="text-decoration: none;" href="{{$url}}/checkout/{{$invoice->uuid}}" target="_blank">{{$url}}/checkout/{{$invoice->uuid}}</a>
</p>
----------------------------------------------------------

@if($invoice->payment_method_id != 4)
    <h3>Hướng dẫn thanh toán</h3>
    <p>{!! html_entity_decode($paymentTutorial->description) !!}</p>
@endif
