@extends('frontend._layouts.default')

@section('title') Dungmori - <PERSON>àm bài kiểm tra @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>hật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop
@section('content')
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <div class="test-online-page" id="test-online-home">
        <div class="main">
            <div class="main-center">
                <div style="margin-top: 50px">
                    @include('frontend.home.components.section_title',['title' => 'グループテスト', 'description' => 'Test online nhóm học', 'suffix_icon' => 'looking-mori'])
                </div>
                <div class="test-online__navigation">
                    <div class="test-online__navigation-item" :class="{active: tab == 'test'}" @click.stop="tab = 'test'">
                        <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_77_3339)">
                                <path d="M41.3369 45.5834H11.6493C11.2119 45.5834 10.7924 45.4097 10.4831 45.1004C10.1738 44.7911 10 44.3716 10 43.9341V7.64931C10 7.21189 10.1738 6.79238 10.4831 6.48307C10.7924 6.17377 11.2119 6 11.6493 6H31.441L42.9862 17.5452V43.9341C42.9862 44.3716 42.8124 44.7911 42.5031 45.1004C42.1938 45.4097 41.7743 45.5834 41.3369 45.5834Z" fill="#F6F6F6"/>
                                <path d="M31.3369 6.59717V18.1423H42.8821L31.3369 6.59717Z" fill="white"/>
                                <path d="M41.2329 46.1806H11.5453C11.1079 46.1806 10.6884 46.0068 10.3791 45.6975C10.0698 45.3882 9.896 44.9687 9.896 44.5313V8.24648C9.896 7.80905 10.0698 7.38955 10.3791 7.08024C10.6884 6.77093 11.1079 6.59717 11.5453 6.59717H31.337L42.8822 18.1423V44.5313C42.8822 44.9687 42.7084 45.3882 42.3991 45.6975C42.0898 46.0068 41.6703 46.1806 41.2329 46.1806Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M31.3369 6.59717V18.1423H42.8821" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M19.7915 28.0382H32.986"àmtroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M19.7915 34.6355H32.986" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_77_3339">
                                    <rect width="52.7779" height="52.7779" fill="white"/>
                                </clipPath>
                            </defs>
                        </svg>
                        <span>Làm bài</span>
                    </div>
                    @if(auth()->check() && (!in_array(2728, auth()->user()->group()->pluck('community_groups.id')->toArray()) || auth()->user()->is_tester))
                        <div class="test-online__navigation-item" :class="{active: tab == 'rank'}" @click.stop="tab = 'rank'">
                            <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_75_3009)">
                                    <path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2327 23.0904V11.5452C41.2327 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" fill="#F2F2F2"/>
                                    <path d="M11.5452 11.5452V22.9048C11.5452 31.0895 18.1012 37.8723 26.2859 37.9341C28.2438 37.9477 30.1852 37.5738 31.998 36.834C33.8109 36.0941 35.4594 35.0028 36.8487 33.6231C38.238 32.2434 39.3406 30.6024 40.0931 28.7947C40.8455 26.9871 41.2328 25.0484 41.2328 23.0904V11.5452C41.2328 11.1078 41.059 10.6883 40.7497 10.3789C40.4404 10.0696 40.0209 9.89587 39.5834 9.89587H13.1945C12.7571 9.89587 12.3375 10.0696 12.0282 10.3789C11.7189 10.6883 11.5452 11.1078 11.5452 11.5452Z" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M19.7917 46.1807H32.9862" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M26.3889 37.9341V46.1806" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M40.8616 26.3889H42.882C44.6317 26.3889 46.3097 25.6939 47.5469 24.4566C48.7842 23.2194 49.4792 21.5414 49.4792 19.7917V16.4931C49.4792 16.0556 49.3055 15.6361 48.9961 15.3268C48.6868 15.0175 48.2673 14.8438 47.8299 14.8438H41.2327" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M11.9576 26.3889H9.87532C8.12562 26.3889 6.44759 25.6939 5.21036 24.4566C3.97314 23.2194 3.27808 21.5414 3.27808 19.7917V16.4931C3.27808 16.0556 3.45184 15.6361 3.76115 15.3268C4.07045 15.0175 4.48996 14.8438 4.92739 14.8438H11.5246" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_75_3009">
                                        <rect width="52.7779" height="52.7779" fill="white"/>
                                    </clipPath>
                                </defs>
                            </svg>
                            <span>Xếp hạng</span>
                        </div>
                        <div class="test-online__navigation-item" :class="{active: tab == 'history'}" @click.stop="tab = 'history'">
                            <svg width="53" height="53" viewBox="0 0 53 53" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_75_3017)">
                                    <path d="M26.3888 44.5313C36.4085 44.5313 44.5312 36.4087 44.5312 26.3889C44.5312 16.3691 36.4085 8.24646 26.3888 8.24646C16.369 8.24646 8.24634 16.3691 8.24634 26.3889C8.24634 36.4087 16.369 44.5313 26.3888 44.5313Z" fill="#F2F2F2"/>
                                    <path d="M26.3889 16.493V26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M34.9653 31.3368L26.3889 26.3889" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M14.8027 20.5545H6.55615V12.308" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M13.5657 39.2123C16.1024 41.751 19.335 43.4804 22.8547 44.1816C26.3744 44.8828 30.023 44.5244 33.339 43.1517C36.6549 41.779 39.4892 39.4536 41.4834 36.4698C43.4776 33.486 44.542 29.9777 44.542 26.3889C44.542 22.8 43.4776 19.2918 41.4834 16.308C39.4892 13.3241 36.6549 10.9988 33.339 9.62606C30.023 8.25334 26.3744 7.89492 22.8547 8.59615C19.335 9.29738 16.1024 11.0268 13.5657 13.5655L6.55615 20.5544" stroke="black" stroke-width="3.29862" stroke-linecap="round" stroke-linejoin="round"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_75_3017">
                                        <rect width="52.7779" height="52.7779" fill="white"/>
                                    </clipPath>
                                </defs>
                            </svg>
                            <span>Lịch sử thi</span>
                        </div>
                    @endif

                </div>
                <test-online-list ref="test" v-if="tab == 'test'"></test-online-list>
                <test-online-rank v-if="tab == 'rank'"></test-online-rank>
                <test-online-history v-if="tab == 'history'"></test-online-history>
            </div>
        </div>
    </div>
@stop
@section('footer-js')
    <!-- import CSS -->
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>

    <script>
      ELEMENT.locale(ELEMENT.lang.vi)
    </script>
    <script>
      var api = "{{ config('app.api_url') }}";
      var userId = {{ Auth::user()->id }};
      var exam = {{ Auth::user()->id }};
    </script>
    <script src="{{asset('assets/js/test_online.js')}}?{{filemtime('assets/js/test_online.js')}}"></script>
@stop
