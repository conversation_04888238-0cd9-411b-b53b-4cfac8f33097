@extends('frontend._layouts.default')

@section('title') Dungmori - Cộng đồng @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber dạy tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop
@section('header-css')
    <style>
        table, thead, tbody, tr, td {
            border-style: solid;
            border-width: 1px;
            border-color: #000;
        }
    </style>
@stop
@section('content')
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <div class="test-online-page" id="test-online-exam">
        <div class="main">
            <div class="test-online__exam-audio sticky" v-if="mp3">
                <div class="test-online__exam-audio-wrapper">
                    <audio controls="controls" v-cloak>
                        <source :src="'https://mp3-v2.dungmori.com/'+ mp3" type="audio/mpeg">
                        Trình duyệt không hỗ trợ phát âm thanh
                    </audio>
                </div>
            </div>
            <div class="main-center test-online__exam-wrapper" style="margin-top: 20px">
                <div class="test-online__exam" v-cloak>
                    <div class="test-online__exam-header" v-cloak>@{{ examDetail.name }}</div>
                    <div class="test-online__exam-body">
                        <div v-for="(lesson, lIdx) in examDetail.lessons" :key="'lesson-' + lIdx">
                            <div class="test-online__exam-lesson" v-cloak>@{{ lessonTitles[lesson.type - 1] }}</div>
                            <div v-cloak class="test-online__exam-questions">
                                <div v-for="(question, qIdx) in lesson.questions" :key="'question-' + qIdx" class="mt-5">
                                    <div v-html="question.content" class="text-bold"></div>
                                    <div style="display: flex; flex-wrap: wrap" class="test-online__exam-answers mt-3">
                                        <div v-for="(answer, aIdx) in question.answers" :key="'answer-' + aIdx" class="mt-2" v-bind:style="answerLength(answer.content) >= 18 ? 'width:100%;' : 'width: 50%;'">
                                            <label class="flex items-center">
                                                <input class="mt-0" type="radio" :name="'answer-' + question.id" :value="answer.id" @change="selectAnswer(question.id, answer.id)" :checked="answers[question.id] == answer.id">
                                                <span class="ml-2" v-html="answer.content"></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="test-online__exam-side" v-cloak>
                    <div>Đã làm được: <span class="font-semibold">@{{ answerCount }}/@{{ questionCount }}</span> câu</div>
                    <div>Thời gian còn lại: <span class="font-semibold">@{{ timeText.hour | two_digits }}:@{{ timeText.minute | two_digits }}:@{{ timeText.second | two_digits }}</span> </div>
                    <div class="btn submit-exam-btn" @click="confirmSubmit">Nộp bài</div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer-js')
    <!-- import CSS -->
    <link rel="stylesheet" href="{{ asset('/plugin/element-ui/element-ui.min.css') }}">
    <!-- import JavaScript -->
    <script src="{{ asset('/plugin/element-ui/element-ui.min.js') }}"></script>
    <script src="{{ asset('/plugin/element-ui/vi.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>

    <script>
      ELEMENT.locale(ELEMENT.lang.vi);
    </script>
    <script>
      var api = "{{ config('app.api_url') }}";
      var userId = {{ Auth::user()->id }};
      var exam = {!! $exam !!};
      var step = "{!! $step !!}";
    </script>
    <script src="{{asset('assets/js/test_online_room.js')}}?{{filemtime('assets/js/test_online_room.js')}}"></script>
@stop
