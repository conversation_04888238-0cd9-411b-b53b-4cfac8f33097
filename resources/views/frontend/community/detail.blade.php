@extends('frontend._layouts.default')

@section('title') Dungmori - Cộng đồng @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
<link href='https://unpkg.com/boxicons@2.0.9/css/boxicons.min.css' rel='stylesheet'>
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
<link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />
<script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>
<link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}">

<script type="text/javascript">
    $("#application").css('padding-top', '41px');
    $(".header-content").css('display', 'none');
    $(".site-header").css('height', '42px');
</script>
<style type="text/css">
    .footer{display: none;}
</style>

<div id="fb-root"></div>

<?php $me = Auth::user(); ?>


<div class="main" style="background: #F3f3f3;">
   <div class="main-center main-community-center">

        <div class="body-panel">
            <div class="community-left-pannel" style="opacity: 0;">

            </div>

            <div class="community-center-pannel" style="padding-top: 30px;">

                <div class="search-content">

                    @if(isset($_GET['q']))

                    <section class="empty-posts" v-if="listPosts.length == 0" v-cloak style="margin-bottom: 20px;">
                        <i class='bx bx-search'></i><br/>
                        0 kết quả
                    </section>
                    @else

                    <section class="empty-posts" v-if="listPosts.length == 0" v-cloak>
                        <img class="logo" alt="cmntLogo" src="{{url('assets/img/group/public-icon.png')}}"><br/>
                        Chưa có nội dung
                    </section>
                    @endif

                    @include('frontend.community.post_item')

                </div>
            </div>

            <div class="community-right-pannel">

            </div>
        </div>

   </div>
</div>

@stop

@section('footer-js')
    <script type="text/javascript">
        var api = "{{ config('app.api_url') }}";
        var cdn = "{{ config('app.cdn_url') }}";
        var gId = {{ $groupid }};
        var token = "{{ session('_tokenApp') }}";
        var authId = null;
        @if (count($exams) == 0)
            var examList = [];
        @else
            var examList = {!! $exams !!};
        @endif
        @if(Auth::check())
        authId = {{Auth::user()->id}};
        @endif
        var isMod = false;
        @if(Auth::check() && Auth::user()->is_tester)
            isMod = true;
        @endif
    </script>
    {{-- <script src="//code.jquery.com/jquery-1.11.0.min.js"></script> --}}
    {{-- <script src="//code.jquery.com/jquery-migrate-1.2.1.min.js"></script> --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
    <script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/notify/0.4.2/notify.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src='{{ asset('/plugin/v-calendar/v-calendar.min.js') }}'></script>
    <script src="{{asset('assets/js/community.js')}}?{{filemtime('assets/js/community.js')}}"></script>

    <script async defer src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.2"></script>

    <style>
        html {
            background: #F3f3f3;
        }
    </style>
@stop
