
{{-- form đăng bài --}}
<form id="post-form" class="posts-form-new" style="width: 600px; display: none; " v-on:click="postnew_error = ''">
    <h1><PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>ng</h1>
    <fieldset style="border:0;">
        <div class="panel-post">
            <div class="panel-body">
                <img class="user-avatar-circle"
                    @if(Auth::user()->avatar == null)
                        src="{{url('assets/img/default-avatar.jpg')}}"
                    @else
                        src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                    @endif
                />
                <textarea spellcheck="false" id="new-posts-input-area" class="content-input" placeholder="Nhập nội dung..."></textarea>
            </div>
            <div class="media-container">
                <div class="btn-group media-options">
                    <div type="button" class="btn btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span v-if="postnew_type == 0"><i class="zmdi zmdi-image"></i> Kèm ảnh</span>
                        <span v-if="postnew_type == 4 && isMod"><i class="zmdi zmdi-file"></i> Thêm file</span>
                        <span v-if="postnew_type == 1"><i class="zmdi zmdi-youtube-play" style="color: red;"></i> Youtube</span>
                        <span v-if="postnew_type == 3"><i class="zmdi zmdi-facebook-box" style="color: #4267B2;"></i> Fb Video</span>
                        <span v-if="postnew_type == 2"><i class='bx bxl-tiktok'></i> TikTok</span>
                        <i class="zmdi zmdi-caret-down"></i>
                    </div>
                    <div class="dropdown-menu">
                        <a v-on:click="postnew_type = 0" class="dropdown-item"><i class="zmdi zmdi-image"></i> Thêm ảnh</a>
                        <a v-if="isMod" v-on:click="postnew_type = 4" class="dropdown-item"><i class="zmdi zmdi-file"></i> Thêm file</a>
                        <a v-on:click="postnew_type = 1" class="dropdown-item"><i style="color: red;" class="zmdi zmdi-youtube-play"></i> Youtube</a>
                        <a v-on:click="postnew_type = 3" class="dropdown-item"><i style="color: #4267B2;" class="zmdi zmdi-facebook-box"></i> FB Video</a>
                        <a v-on:click="postnew_type = 2" class="dropdown-item"><i class='bx bxl-tiktok'></i> TikTok</a>
                    </div>
                </div>
                <div class="media-pick-container" v-if="postnew_type == 0">
                    <span class="pick-image">
                        <i class="zmdi zmdi-camera"></i> Chọn 1 hoặc nhiều ảnh <small style="color: #999">(không bắt buộc)</small>
                        <small class="clear" id="clear-prv" style="color: red;" v-on:click="clearImgPreview" data-toggle="tooltip" data-placement="top" title="xóa bỏ ảnh">
                            <i class='bx bxs-no-entry' ></i> clear
                        </small>
                    </span>
                    <form class="form-pick-image" id="post-attachment-form">
                        <input type='file' multiple id="postsImagePicked" @change="previewNewPostsImage" name="postsImagePicked" accept=".png, .jpg, .jpeg, .gif" data-toggle="tooltip" data-placement="top" title="Dung lượng ảnh tối đa 3MB, Định dạng cho phép: jpg, png, jpeg, gif" />
                    </form>
                </div>
                <div v-if="postnew_type == 4">
                    <form>
                        <input type="file" multiple id="documentData" accept=".doc, .xsl, .ppt, .pdf, .docx, .pptx, .xlsx" data-toggle="tooltip" data-placement="top" title="Dung lượng file tối đa 3MB."/>
                    </form>
                </div>
                <div class="media-pick-container"  v-if="postnew_type != 0 && postnew_type != 4">
                    <input type="text" class="embed-link" id="embed-url-link" placeholder="nhập link..." />
                </div>
            </div>
            <div class="preview-image" id="preview-image-post"></div>
            <div class="prepare-hashtag" id="pick-hashtag">
                <h4>Chọn hashtag: <small style="color: red;">* bắt buộc</small></h4>
                <select class="select2-hashtag" id="select2-hashtag" name="states[]" multiple="multiple" style="width: 100%; height: 50px; border: 1px solid #EEE;">
                    <option v-for="(tag, index) in listTags" :value="tag.id">#@{{tag.name}}</option>
                </select>
            </div>
            <div class="post-bottom-area">
                @if(Auth::user()->is_tester == 1)
                <div style="width: 100%; float: right; padding-left: 50px; margin-bottom: 10px;">
                    <input type="date" class="datepicker" id="create-date-picker" />
                    <input type="time" class="timepicker" id="create-time-picker" />
                    &nbsp; Lập lịch đăng bài
                </div>
                @endif
                <button v-on:click="addNewPost()" type="button" class="btn post-btn">
                    <span v-show="postnew_btn == 0">Đăng bài</span>
                    <span v-show="postnew_btn == 1">Lưu <img class="posting-icon" :src="url + '/assets/img/loading.gif'"/></span>
                    <span v-show="postnew_btn == 2">Đã đăng bài <i class="zmdi zmdi-check-circle"></i></span>
                </button>
                <button type="button" class="btn post-btn" style="background: #DDD; margin-right: 10px;" onclick="$.fancybox.close()">
                    <span>Hủy bỏ</span>
                </button>
                <span style="color: red; float: right; margin: 4px 10px 0 0;" v-show="postnew_error != ''" v-html="postnew_error"></span>
            </div>
        </div>
    </fieldset>
</form>
