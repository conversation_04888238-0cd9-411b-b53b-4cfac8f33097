@extends('frontend._layouts.default')

@section('title') Dungmori - Cộng đồng @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
<link href='https://unpkg.com/boxicons@2.0.9/css/boxicons.min.css' rel='stylesheet'>
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
<link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />

<script type="text/javascript">
    $("#application").css('padding-top', '41px');
    $(".header-content").css('display', 'none');
    $(".site-header").css('height', '42px');
</script>
<style type="text/css">
    .footer{display: none;}
</style>

<div id="fb-root"></div>

<?php $me = Auth::user(); ?>


<div class="main" style="background: #F3f3f3;">
   <div class="main-center main-community-center" id="main-profile-center">

        <div class="head-panel">
            <div class="cover-left">
                <a href="{{url('/')}}">
                    <img class="logo" alt="cmntLogo" src="{{url('assets/img/group/public-icon.png')}}">
                </a>
                <div class="heading">
                    <h3>Cộng đồng Dũng Mori</h3>
                    <h4>Chia sẻ kiến thức tiếng Nhật.</h3>
                </div>
             </div>
             <div class="search-box">
                <div id="search-submit">&nbsp;</div>
                <form class="header-search">
                    <input type="text" name="q" class="search-input" id="search-input" placeholder="Tìm kiếm..." autocomplete="off"/>
                </form>
             </div>
        </div>
        <div class="body-panel">
            <div class="community-left-pannel">
                <a href="{{url('/groups')}}" class="item-group master">
                    <img class="group-icon" src="{{url('assets/img/group/public-icon.png')}}"> <strong>Bài đăng tổng hợp</strong>
                </a>

                @if(!Auth::check())

                <a class="item-group master " data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('login')">
                    <img class="group-icon" style="width: 18px; height: 18px; margin-left: 3px;" src="{{url('assets/img/group/profile.png')}}"> <strong>Trang cá nhân</strong>
                </a>

                @else
                <a class="item-group master active" href="{{url('/groups/me')}}">
                    <img class="group-icon" style="width: 18px; height: 18px; margin-left: 3px;" src="{{url('assets/img/group/profile.png')}}"> <strong>Trang cá nhân</strong>
                </a>
                <a class="item-group master">
                    <img class="group-icon" src="{{url('assets/img/group/my-group.png')}}"> <strong>Nhóm của tôi <i class='bx bx-caret-down'></i></strong>
                </a>
                @endif

                <div v-cloak style="width: 100%; float: left;background: #fff; overflow-x: hidden; height: 609px;">
                <div v-cloak style="width: 108%; float: left;background: #fff; overflow-y: scroll; height: 609px;">
                    <a v-if="listGroups.length > 0" class="item-group" v-for="group in listGroups" :href="url+ '/groups/'+ group.id +'-'+ group.slug" >
                        <img class="group-icon" :src="'{{url('cdn/community/small')}}/'+ group.avatar"/>
                        <span>@{{group.name}}
                            <p><i class="fa fa-users" style="font-size: 11px;"></i> @{{group.members}} members <small>(@{{ prettyExpired(group.expired_at) }})</small></p>

                        </span>
                    </a>
                    <div class="empty-groups" v-if="listGroups.length == 0">
                        <i class='bx bx-error-circle'></i>
                        Bạn chưa tham gia vào nhóm nào
                    </div>
                </div>
                </div>

            </div>

            <div class="community-center-pannel">

                <div class="avatar-box">
                    <img class="profile-avatar"
                        @if(Auth::user()->avatar == null)
                            src="{{url('assets/img/default-avatar.jpg')}}"
                        @else
                            src="{{url('cdn/avatar/default')}}/{{ Auth::user()->avatar}}"
                        @endif
                    />
                    <h3 class="name"><span>{{ Auth::user()->name }}</span></h3>
                </div>

                @if(isset($_GET['q']))

                    <div class="search-header" v-cloak>
                        <h4><i class='bx bx-search' style="font-size: 25px; vertical-align: bottom; color: green;"></i> Từ khóa:  @{{ params.text }}</h4>
                        <a :href="crrurl"><i class='bx bx-x'></i></a>
                    </div>

                @else

                    @include('frontend.community.form_edit')
                    @include('frontend.community.form_cmt')

                @endif

                <div class="filter-bar">
                    <i class='bx bx-sort-down'></i>
                    @if(isset($_GET['q']))
                        <strong>Kết quả tìm kiếm</strong>
                    @else
                        <strong>Bài đăng của tôi</strong>
                    @endif

                    <strong v-if="params.tagId != null && currTag != null" v-cloak>Chủ đề
                        <span class="filter-tag">#@{{ currTag.name }} </span> <a style="font-weight: 400;">(@{{ currTag.total_tags }} kết quả) </a>
                    </strong>
                    <select class="select-filter" v-model="orderBy">
                        <option value="latest">Bài mới nhất</option>
                        <option value="featured">Nổi bật nhất</option>
                    </select>
                </div>

                <div class="search-content">

                    @if(isset($_GET['q']))

                    <section class="empty-posts" v-if="listPosts.length == 0" v-cloak style="margin-bottom: 20px;">
                        <i class='bx bx-search'></i><br/>
                        0 kết quả
                    </section>
                    @else

                    <section class="empty-posts" v-if="listPosts.length == 0" v-cloak>
                        <img class="logo" alt="cmntLogo" src="{{url('assets/img/group/public-icon.png')}}"><br/>
                        Chưa có nội dung
                    </section>
                    @endif

                    @include('frontend.community.post_item')

                    <!-- hiển thị loading -->
                    <div v-if="theEnd == false" class="load-more-posts">
                        <span v-show="showLoading == false">Tải thêm bình luận</span>Đang tải thêm
                        <img class="loading-icon" v-show="showLoading == true" :src="url + '/assets/img/loading.gif'"/>
                    </div>
                    <div v-if="theEnd == true" class="end-of-list">Hết danh sách</div>

                </div>
            </div>

            <div class="community-right-pannel">
                <div class="file-panel">
                    <h3 class="panel-heading">
                        #File đã chia sẻ
                    </h3>
                    <div class="file-body">
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <a href="#"> <img class="file-item" src="https://dummyimage.com/80/eeeeee/000000.png" /> </a>
                        <div class="expand-tag">Xem đầy đủ <i class='bx bx-caret-down'></i></div>
                    </div>
                </div>

            </div>
        </div>

   </div>
</div>

@stop

@section('footer-js')
    <script type="text/javascript">
        var api = "{{ config('app.api_url') }}";
        var cdn = "{{ config('app.cdn_url') }}";
        var token = "{{ session('_tokenApp') }}";
        var authId = null;
        @if(Auth::check())
        authId = {{Auth::user()->id}};
        @endif
    </script>
    {{-- <script src="//code.jquery.com/jquery-1.11.0.min.js"></script> --}}
    {{-- <script src="//code.jquery.com/jquery-migrate-1.2.1.min.js"></script> --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
    <script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/notify/0.4.2/notify.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script src="{{asset('assets/js/community_me.js')}}?{{filemtime('assets/js/community_me.js')}}"></script>

    <script async defer src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.2"></script>
    <script async src="https://www.tiktok.com/embed.js"></script>

@stop
