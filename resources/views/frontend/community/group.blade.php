@extends('frontend._layouts.default')

@section('title') Dungmori - Cộng đồng @stop
@section('description')Dungmori YouTuber d<PERSON><PERSON> tiế<PERSON>t với phong cách gần gũi dễ hiểu nhất @stop
@section('keywords') Dungmori YouTuber d<PERSON><PERSON> tiếng <PERSON>ật với phong cách gần gũi dễ hiểu nhất @stop
@section('image'){{url('assets/img/oglogo.png')}} @stop
@section('author') DUNGMORI @stop

@section('content')

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
<link href='https://unpkg.com/boxicons@2.0.9/css/boxicons.min.css' rel='stylesheet'>
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
<link href="{{asset('plugin/select2-4.1.0/css/select2.min.css')}}" rel="stylesheet" />

<script type="text/javascript">
    $("#application").css('padding-top', '41px');
    $(".header-content").css('display', 'none');
    $(".site-header").css('height', '42px');
</script>
<style type="text/css">
    .footer{display: none;}
</style>

<div id="fb-root"></div>

<?php $me = Auth::user(); ?>


<div class="main" style="background: #F3f3f3;">
   <div class="main-center main-community-center">

        <div class="head-panel">
            <div class="cover-left">
                <a href="{{url('/')}}">
                    <img class="logo" alt="cmntLogo" src="{{url('assets/img/group/public-icon.png')}}">
                </a>
                <div class="heading">
                    <h3>Cộng đồng Dũng Mori</h3>
                    <h3>Chia sẻ kiến thức tiếng Nhật.</h3>
                </div>
             </div>
             <div class="search-box">
                <div id="search-submit">&nbsp;</div>
                <form class="header-search">
                    <input type="text" name="q" class="search-input" id="search-input" placeholder="Tìm kiếm..." autocomplete="off"/>
                </form>
             </div>
        </div>
        <div class="body-panel">
            <div class="community-left-pannel">
                <a href="{{url('/groups')}}" class="item-group master @if($groupid == 1) active @endif">
                    <img class="group-icon" src="{{url('assets/img/group/public-icon.png')}}"> <strong>Bài đăng tổng hợp</strong>
                </a>

                @if(!Auth::check())

                <a class="item-group master" data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('login')">
                    <img class="group-icon" style="width: 18px; height: 18px; margin-left: 3px;" src="{{url('assets/img/group/profile.png')}}"> <strong>Trang cá nhân</strong>
                </a>
                @else
                <a class="item-group master" href="{{url('/groups/me')}}">
                    <img class="group-icon" style="width: 18px; height: 18px; margin-left: 3px;" src="{{url('assets/img/group/profile.png')}}"> <strong>Trang cá nhân</strong>
                </a>
                <a class="item-group master">
                    <img class="group-icon" src="{{url('assets/img/group/my-group.png')}}"> <strong>Nhóm của tôi <i class='bx bx-caret-down'></i></strong>
                </a>
                @endif

                <div v-cloak style="width: 100%; float: left;background: #fff; overflow-x: hidden; height: 609px;">
                <div v-cloak style="float: left;background: #fff; overflow-y: scroll; height: 609px;" class="list-groups-community">
                    <div class="group-filter-community">
                        <input type="text" placeholder="Tìm kiếm ..." v-model="filterKeywords" v-on:change="filterGroup">
                    </div>
                    <a v-if="listGroups.length > 0" v-bind:class=" groupId != group.id ? 'item-group': 'item-group active'"
                        v-for="group in listGroups" :href="url+ '/groups/'+ group.id +'-'+ group.slug" >
                        <img class="group-icon" :src="'{{url('cdn/community/small')}}/'+ group.avatar"/>
                        <span>@{{group.name}}
                            <p><i class="fa fa-users" style="font-size: 11px;"></i> @{{group.members}} members <small>(@{{ prettyExpired(group.expired_at) }})</small></p>

                        </span>
                    </a>
                    <a class="item-group" style="padding-left: 15px;">
                        <div style="background: green; text-align: center;padding: 10px;cursor: pointer" @click="loadMoreGroup">
                            <div v-show="groupLoading == false" style="color: white">Tải thêm </div>
                            <img class="loading-icon" v-show="groupLoading == true" :src="url + '/assets/img/loading.gif'"/>
                        </div>
                    </a>
                    <div class="empty-groups" v-if="listGroups.length == 0">
                        <i class='bx bx-error-circle'></i>
                        Bạn chưa tham gia vào nhóm nào
                    </div>
                </div>
                </div>

            </div>

            <div class="community-center-pannel">

                @if(isset($_GET['q']))

                    <div class="search-header" v-cloak>
                        <h4><i class='bx bx-search' style="font-size: 25px; vertical-align: bottom; color: green;"></i> Từ khóa:  @{{ params.text }}</h4>
                        <a :href="crrurl"><i class='bx bx-x'></i></a>
                    </div>

                @else


                    @if(Auth::check())
                        @if($groupid != 1)
                        <div class="panel-post">
                            <div class="panel-body">
                                <img class="user-avatar-circle"
                                    @if(Auth::user()->avatar == null)
                                        src="{{url('assets/img/default-avatar.jpg')}}"
                                    @else
                                        src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                                    @endif
                                />
                                <a id="open-create-post" data-fancybox data-animation-duration="300" data-src="#post-form">
                                    <div spellcheck="false" id="new-posts-input" class="content-input">Nhập nội dung...</div>
                                    <button type="button" class="btn post-btn">Đăng bài</button>
                                </a>
                            </div>
                        </div>

                        @include('frontend.community.form_create')
                        @include('frontend.community.form_cmt')
                        @endif
                        @include('frontend.community.form_edit')

                    @else
                    <div class="not-auth-box">
                        <img class="group-icon" src="{{url('assets/img/group/notauth.png')}}"/>
                        <p>Bạn đang thắc mắc điều gì?<br/>
                            <a id="login-text-btn" data-fancybox data-animation-duration="300" data-src="#auth-container" onclick="swichTab('login')" style="color: green; font-weight: bold; cursor: pointer;">Đăng nhập </a>
                            ngay để đặt câu hỏi và tham gia bình luận nhé!
                        </p>
                    </div>
                    @endif

                @endif

                <div class="filter-bar">
                    <i class='bx bx-sort-down'></i>
                    @if(isset($_GET['q']))
                        <strong>Kết quả tìm kiếm</strong>
                    @endif

                    <strong v-if="params.tagId != null && currTag != null" v-cloak>Chủ đề
                        <span class="filter-tag">#@{{ currTag.name }} </span> <a style="font-weight: 400;">(@{{ currTag.total_tags }} kết quả) </a>
                    </strong>
                    <select class="select-filter" v-model="orderBy">
                        <option value="latest">Bài mới nhất</option>
                        <option value="featured">Nổi bật nhất</option>
                    </select>
                </div>

                <div class="search-content">

                    @if(isset($_GET['q']))

                    <section class="empty-posts" v-if="listPosts.length == 0" v-cloak style="margin-bottom: 20px;">
                        <i class='bx bx-search'></i><br/>
                        0 kết quả
                    </section>
                    @else

                    <section class="empty-posts" v-if="listPosts.length == 0" v-cloak>
                        <img class="logo" alt="cmntLogo" src="{{url('assets/img/group/public-icon.png')}}"><br/>
                        Chưa có nội dung
                    </section>
                    @endif

                    @include('frontend.community.post_item')

                    <!-- hiển thị loading -->
                    <div v-if="theEnd == false" class="load-more-posts" v-on:click="loadMorePosts">
                        <span v-show="showLoading == false">Tải thêm </span>
                        <img class="loading-icon" v-show="showLoading == true" :src="url + '/assets/img/loading.gif'"/>
                    </div>
                    <div v-if="theEnd == true" class="end-of-list">Hết danh sách</div>

                </div>
            </div>

            <div class="community-right-pannel">
                @if (Auth::user() && Auth::user()->is_tester && $groupid != 1)
                    <div class="test-panel">
                        <h3 class="panel-heading">
                            Lịch kiểm tra
                        </h3>
                        <div class="test-body">
                            <div>
                                <v-date-picker v-model='selectedDate' :attributes='attributes' :model-config="modelConfig" mode="date" :minute-increment="5" locale="vi"/>
                            </div>
                            <template v-if="selectedDate">
                                <div class="test-list w-full" v-if="selectedDate">
                                    <div v-for="schedule in selectedDateExams" class="test-list-item" :class="{active: schedule.id == currentExam.id}" @click="selectExam(schedule)">@{{ schedule.exam ? schedule.exam.name : 'Không có tiêu đề' }}</div>
                                    <div class="test-list-item text-center" @click="appendSchedule"><i class="fa fa-plus"></i></div>
                                </div>
                                <template v-if="currentExam.hasOwnProperty('id')">
                                    <div class="form-group w-full mt-2">
                                        <label>Thời gian bắt đầu</label>
                                        <v-date-picker v-model="currentExam.time_start" mode="dateTime" is24hr :model-config="modelConfig" :minute-increment="5"></v-date-picker>
                                    </div>
                                    <div class="form-group w-full mt-2">
                                        <label>Thời gian kết thúc</label>
                                        <v-date-picker v-model="currentExam.time_end" mode="dateTime" is24hr :model-config="modelConfig" :minute-increment="5"></v-date-picker>
                                    </div>
                                    <div class="form-group w-full mt-2">
                                        <label for="testSelect">Bài kiểm tra</label>
                                        <v-select :options="examList" label="name" :reduce="function(name) {return name.id}" v-model="currentExam.examId"></v-select>
{{--                                        <select class="form-control" id="testSelect" v-model="currentExam.examId">--}}
{{--                                            @foreach($exams as $exam)--}}
{{--                                                <option value="{{$exam->id}}">{{$exam->name}}</option>--}}
{{--                                            @endforeach--}}
{{--                                        </select>--}}
                                    </div>

                                    <div class="form-group w-full">
                                        <label for="typeSelect">Loại</label>
                                        <select class="form-control" id="typeSelect" v-model="currentExam.type">
                                            <option value="1">Kiểm tra đầu giờ</option>
                                            <option value="2">Bài về nhà</option>
                                            <option value="3">Thi giữa khoá</option>
                                            <option value="4">Thi cuối khoá</option>
                                            <option value="5">Test Offline</option>
                                        </select>
                                    </div>
                                    <div class="form-group w-full">
                                        <label for="typeSelect">Tính điểm</label>
                                        <div>
                                            <input v-model="currentExam.counted" type="radio" name="counted-radio" :value="1" id="counted-radio-1" />
                                            <label for="counted-radio-1" class="mr-2">Có</label>
                                            <input v-model="currentExam.counted" type="radio" name="counted-radio" :value="0" id="counted-radio-0" />
                                            <label for="counted-radio-0">Không</label>
                                        </div>

                                    </div>
                                    <div v-if="currentExam.id" class="btn btn-block btn-danger" @click="deleteExam">
                                        <i class="fa fa-trash"></i> Huỷ
                                    </div>
                                    <div v-if="!currentExam.id" class="btn btn-block btn-success" @click="createExam">
                                        <i class="fa fa-plus"></i> Tạo
                                    </div>
                                    <div v-if="currentExam.id" class="btn btn-block btn-info" @click="updateExam">
                                        <i class="fa fa-floppy-disk"></i> Lưu
                                    </div>
                                </template>
                            </template>
                        </div>
                    </div>
                @endif
                <div class="tag-panel">
                    <h3 class="panel-heading">
                        #Chủ đề phổ biến
                    </h3>
                    <div class="tag-body" v-cloak>
                        <a v-for="(tag, index) in listTags" :href="crrurl + '?tag='+ tag.id ">
                            <span v-if="index < limitTag" class="tag-item">#@{{tag.name}}
                                <small>@{{tag.total_tags}}</small>
                            </span>
                        </a>
                        <div v-if="limitTag <= 15" class="expand-tag" v-on:click="limitTag = listTags.length">
                            Xem tất cả chủ đề <i class='bx bx-caret-down'></i>
                        </div>
                        <div v-if="limitTag > 15" class="expand-tag" v-on:click="limitTag = 15">
                            Thu gọn <i class='bx bx-caret-up'></i>
                        </div>
                    </div>
                </div>
                <div class="file-panel">
                    <h3 class="panel-heading">
                        #File đã chia sẻ
                    </h3>
                    <div class="file-body">
                        <a href="#" v-for="(img, index) in media">
                            <img class="file-item" :src="cdn +'/cdn/community/small/'+ img" />
                        </a>

                        {{-- <div class="expand-tag">Xem thêm <i class='bx bx-caret-down'></i></div> --}}
                    </div>
                </div>

            </div>
        </div>

   </div>
</div>

@stop

@section('footer-js')
    <script type="text/javascript">
        var api = "{{ config('app.api_url') }}";
        var cdn = "{{ config('app.asset_url') }}";
        var gId = {{ $groupid }};
        var token = "{{ session('_tokenApp') }}";
        var authId = null;
        @if (count($exams) == 0)
            var examList = [];
        @else
            var examList = {!! $exams !!};
        @endif
        @if(Auth::check())
            authId = {{Auth::user()->id}};
        @endif
        var isMod = false;
        @if(Auth::check() && Auth::user()->is_tester)
            isMod = true;
        @endif
    </script>
    {{-- <script src="//code.jquery.com/jquery-1.11.0.min.js"></script> --}}
    {{-- <script src="//code.jquery.com/jquery-migrate-1.2.1.min.js"></script> --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
    <script src="{{ asset('/plugin/axios/axios.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/notify/0.4.2/notify.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="{{ asset('/plugin/vue-select/vue-select.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('/plugin/vue-select/vue-select.css') }}">

    <script src="{{asset('plugin/v-calendar/v-calendar.min.js')}}?{{filemtime('plugin/v-calendar/v-calendar.min.js')}}"></script>
    <script src="{{asset('assets/js/community.js')}}?{{filemtime('assets/js/community.js')}}"></script>

    <script async defer src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.2"></script>

    <style>
        html {
            background: #F3f3f3;
        }
        .vs__search {
            font-size: 13px !important;
        }
        .vs__dropdown-option {
            font-size: 12px !important;
            padding: 3px 10px;
        }
        .list-groups-community::-webkit-scrollbar{
            display: none;
        }
        .group-filter-community{
            width: 100%;
            padding: 0 15px;
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
        }
        .group-filter-community input{
            width: 100%;
            height: 45px;
            border-radius: 7px;
            outline: none;
            border: 1px solid #e8dddd;
            padding: 0 10px;
            text-align: left;
        }
    </style>
@stop
