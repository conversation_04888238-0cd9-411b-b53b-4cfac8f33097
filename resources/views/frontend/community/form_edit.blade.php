<form id="post-form-edit" class="posts-form-new" style="width: 600px; display: none; " v-on:click="postnew_error = ''">
    <h1>Sửa bài đăng</h1>
    <fieldset style="border:0;">
        <div class="panel-post">
            <div class="panel-body">
                <img class="user-avatar-circle"
                    @if(Auth::user()->avatar == null)
                        src="{{url('assets/img/default-avatar.jpg')}}"
                    @else
                        src="{{url('cdn/avatar/small')}}/{{ Auth::user()->avatar}}"
                    @endif
                />
                <textarea spellcheck="false" id="edit-posts-input-area" class="content-input" placeholder="Nhập nội dung..." v-if="postEditing != null" v-model="postEditing.content"></textarea>
            </div>
            <div v-if="postEditing != null" class="media-container">
                <div class="btn-group media-options">
                    <span v-if="postEditing.type == 0"><i class="zmdi zmdi-image"></i> Nội dung</span>
                    <span v-if="postEditing.type == 1"><i class="zmdi zmdi-youtube-play"></i> Youtube</span>
                    <span v-if="postEditing.type == 3"><i class="zmdi zmdi-facebook-box"></i> Fb Video</span>
                    <span v-if="postEditing.type == 2"><i class='bx bxl-tiktok'></i> TikTok</span>
                </div>
                <div class="media-pick-container"  v-if="postEditing.type != 0">
                    <input type="text" class="embed-link" id="embed-url-link" :value="postEditing.data.url" disabled />
                </div>
            </div>

            <div class="prepare-hashtag" style="margin-top: 5px;">
                <h4>Sửa hashtag:</h4>
                <select class="select2-hashtag" id="edit-select2-hashtag" name="states[]" multiple="multiple" style="width: 100%; height: 50px; border: 1px solid #EEE;">
                    <option v-for="(tag, index) in listTags" :value="tag.id">#@{{tag.name}}</option>
                </select>
            </div>
            <div class="post-bottom-area">
                @if(Auth::user()->is_tester == 1)
                    <p style="width: 100%; float: right; padding-left: 50px; margin-bottom: 10px;">
                        <input type="date" class="datepicker" id="edit-date-picker" />
                        <input type="time" class="timepicker" id="edit-time-picker" />
                        &nbsp; Lập lịch đăng bài
                    </p>
                @endif
                <button v-on:click="saveEditPosts(postEditing.id)" type="button" class="btn post-btn">
                    <span v-show="postnew_btn == 0">Lưu lại</span>
                    <span v-show="postnew_btn == 1">Đang lưu <img class="posting-icon" :src="url + '/assets/img/loading.gif'"/></span>
                    <span v-show="postnew_btn == 2">Lưu thành công<i class="zmdi zmdi-check-circle"></i></span>
                </button>
                <button type="button" class="btn post-btn" style="background: #DDD; margin-right: 10px;" onclick="$.fancybox.close()">
                    <span>Hủy bỏ</span>
                </button>
                <span style="color: red; float: right; margin: 4px 10px 0 0;" v-show="postnew_error != ''" v-html="postnew_error"></span>
            </div>
        </div>
    </fieldset>
</form>

