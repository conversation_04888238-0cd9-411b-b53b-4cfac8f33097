<section class="posts-item" v-if="listPosts.length > 0" :id="'posts-'+posts.id" v-for="(posts, index) in listPosts" v-cloak
    v-bind:style="(posts.pined_at != null && index > 0) ? 'margin-top: -21px': ''">
    <div class="pin-box" v-if="posts.pined_at != null">
        <b v-if="index == 0">Bài đăng đư<PERSON> ghim</b><b v-if="index != 0">&nbsp;</b>
        <i class='bx bxs-pin' style="float: right; color: green;" ></i>
    </div>
    <div class="panel-heading posts-top">
        <img v-if="posts.avatar != null" class="post-avatar" :src="cdn +'/cdn/avatar/small/'+ posts.avatar" />
        <img v-if="posts.avatar == null" class="post-avatar" :src="cdn +'/assets/img/default-avatar.jpg'" />
        <i v-show="posts.is_tester == 1" class="zmdi zmdi-check-circle admin-tick"></i>
        <div class="post-info">
            <div class="inline-block">
                <div class="flex flex-column justify-content-center justify-content-center" style="height: 40px">
                    <b class="user-name">@{{ posts.name }}</b>
                    <div v-if="posts.group_name" class="flex align-items-center">
                        <i class="fa fa-users text-xs" aria-hidden="true"></i>
                        <span class="ml-2">@{{ posts.group_name }}</span>
                    </div>
                </div>
            </div>
            {{-- @{{ posts.id }}  --}}
            <div v-if="posts.group != null" style="display: inline; color: #000; font-weight: bold;">
                <a v-if="posts.group.id == 1" :href="url+ '/groups'"> <i class='bx bx-caret-right'></i> @{{ posts.group.name }}</a>
                <a v-if="posts.group.id != 1" :href="url+ '/groups/'+ posts.group_id + '-view'"> <i class='bx bx-caret-right'></i> @{{ posts.group.name }}</a>
            </div>

            <div class="dropdown posts-option">
                <div class="dropdown-toggle" type="button" :id="'dropdownMenu' + posts.id" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    <i class='bx bx-dots-horizontal-rounded' style="margin-top: 2px"></i>
                </div>
                @if(Auth::check())
                <ul class="dropdown-menu" :aria-labelledby="'dropdownMenu' + posts.id">
                    <li v-if="posts.user_id == {{ $me->id}}" style="cursor: pointer;" v-on:click="showEditPosts(posts)" data-fancybox data-animation-duration="300" data-src="#post-form-edit">
                        <a><i class='bx bxs-edit' ></i> Sửa nội dung</a>
                    </li>
                    <li  v-if="posts.user_id == {{ $me->id}}" style="cursor: pointer;" v-on:click="deletePosts(posts.id)"><a><i class='bx bxs-trash'></i> Xóa bài đăng</a></li>
                    <li  v-if="posts.user_id != {{ $me->id}}" style="cursor: pointer;" v-on:click="reportPosts(posts.id)"><a><i class='bx bxs-flag-alt' ></i> Báo cáo nội dung</a></li>

                    {{-- chế độ kiểm duyệt dành cho admin --}}
                    @if(Auth::user()->is_tester == 1)
                        <li role="separator" class="divider"></li>
                        <li v-if="posts.pined_at == null" style="cursor: pointer;" v-on:click="pinThisPosts(posts.id)">
                            <a style="cursor: pointer; color: green;"><i class='bx bxs-pin' ></i> Ghim bài đăng</a>
                        </li>
                        <li v-if="posts.pined_at != null" style="cursor: pointer;" v-on:click="unpinThisPosts(posts.id)">
                            <a style="cursor: pointer; color: red;"><i class='bx bxs-pin' ></i> Bỏ Ghim bài đăng</a>
                        </li>
                        <li style="cursor: pointer;" v-on:click="deletePosts(posts.id)">
                            <a style="cursor: pointer; color: red;"><i class='bx bxs-user-detail' ></i> Xóa với quyền admin</a>
                        </li>
                    @endif
                </ul>
                @endif
            </div>

            <span class="date-posted">
                <i class='bx bx-time' v-if="posts.published_at != posts.created_at"></i>
                @{{ prettyTime(posts.published_at) }} @{{ prettyDate(posts.published_at) }}
            </span>
        </div>
    </div>
    <div v-if="[1,2,3].includes(posts.type) || (posts.type == 0 && posts.data != null) || posts.tags.length > 0" class="d-inline-block px-4 mt-3">
        <p class="tags-list">
            <i v-show="posts.type == 0 && posts.data != null" class='bx bx-image' style="color: blue;"></i>
            <i v-show="posts.type == 1" class='bx bxl-youtube' style="color: red;"></i>
            <i v-show="posts.type == 2" class='bx bxl-tiktok' style="color: black;"></i>
            <i v-show="posts.type == 3" class='bx bxl-facebook-circle' style="color: #4080ff;"></i>
            <a v-for="tag in posts.tags" :href="crrurl + '?tag='+ tag.id ">
                <b>#@{{ tag.name }} &nbsp;</b>
            </a>
        </p>
    </div>
    <section v-if="!posts.is_tester" class="panel-body posts-content" :id="'posts-content-'+posts.id" v-text="renderContent(posts.content)" style="white-space: pre-wrap;word-wrap: break-word;"></section>
    <section v-else class="panel-body posts-content" :id="'posts-content-'+posts.id" v-html="renderContent(posts.content)" style="white-space: pre-wrap;word-wrap: break-word;"></section>
    <p class="expand-content" :id="'expand-content-'+posts.id" v-show="posts.content.length > 150" v-on:click="expandContent(posts.id)">...xem thêm</p>
    <div v-if="posts.data && posts.type == 4" class="px-4 pb-2">
        <i class="zmdi zmdi-file mr-1"></i>
        <a :href="cdn + '/cdn/community/files/' + posts.data.name" target="_blank" class="!text-blue-600">@{{ posts.data.name }}</a>
    </div>
    <section v-if="posts.data">

        {{-- album ảnh --}}
        <div class="album popup-gallery" v-if="posts.type == 0 && posts.data && posts.data.length < 3">
            <a v-if="posts.data && posts.data.length == 1" :href="cdn +'/cdn/community/default/'+ posts.data[0]" data-fancybox="images" animationEffect="none">
                <img class="single-image" :src="cdn +'/cdn/community/default/'+ posts.data[0]" />
            </a>
            <a v-if="posts.data && posts.data.length == 2" v-for="img in posts.data" :href="cdn +'/cdn/community/default/'+ img" data-fancybox="images" animationEffect="none" >
                <img class="two-image" :src="cdn +'/cdn/community/default/'+ img" />
            </a>
        </div>

        <div class="slider slick-gallery" :id="'slick-'+ posts.id" v-if="posts.type == 0 && posts.data && posts.data.length >= 3">
            <div v-for="img in posts.data" class="slick-item">
                <a :href="cdn +'/cdn/community/default/'+ img" data-fancybox="images" animationEffect="none" >
                    <img class="multiples-image" :src="cdn +'/cdn/community/default/'+ img" />
                </a>
            </div>
        </div>

        {{-- link youtube --}}
        <iframe v-if="posts.type == 1" width="100%" height="315" :src="'https://www.youtube.com/embed/'+ posts.data.id" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>

        {{-- link facebook --}}
        <div v-show="posts.type == 3" class="fb-video" :data-href="'https://www.facebook.com/facebook/videos/'+ posts.data.id" data-width="550" data-show-text="false">
            <div class="fb-xfbml-parse-ignore">
            </div>
        </div>

        {{-- link tiktok --}}
        <iframe v-if="posts.type == 2" width="100%" height="774" style="background: #fff;" :src="url+ '/groups/tiktok/'+ posts.data.id" title="tiktok video player" frameborder="0" allowfullscreen></iframe>

    </section>
    <div class="posts-bottom-action">

        <span class="like-btn" v-if="posts.is_like == 0" v-on:click="likePost(posts.id)">
            <i class='bx bxs-like'></i>
            <a v-if="posts.total_likes == 0">Like</a>
            <a v-if="posts.total_likes > 0"> @{{ posts.total_likes }} Likes</a>
        </span>
        <span class="like-btn active"  v-if="posts.is_like == 1" v-on:click="dislikePost(posts.id)">
            <i class='bx bxs-like'></i>
            <a v-if="posts.total_likes == 0">Like</a>
            <a v-if="posts.total_likes > 0"> @{{ posts.total_likes }} Likes</a>
        </span>

        <span :id="'cmt-b-'+ posts.id" v-on:click="showCommentBoxOfPosts(posts.id)" v-bind:style="posts.showBoxComment == true ? 'font-weight: 600; color: #111;' : ''">
            <i class='bx bxs-comment-detail'></i> <b :id="'count-cmt-of-'+ posts.id">@{{ posts.total_comments }}</b> Comments
        </span>

        <span class="follow-btn" v-if="posts.is_follow == 0" v-on:click="followPost(posts.id)"><i class='bx bxs-bell'></i> Follow</span>
        <span class="follow-btn active" v-if="posts.is_follow == 1" v-on:click="unfollowPost(posts.id)"><i class='bx bxs-bell'></i> Following</span>
    </div>

    {{-- <div class="panel-body posts-content" :id="'posts-update-'+posts.id" style="display: none;">
        <textarea class="update-posts" spellcheck="false" :id="'update-posts-textarea-'+ posts.id">@{{posts.content}}</textarea>
        <div class="update-or-cancel">
            <button v-on:click="saveUpdatePosts(posts.id);" type="button" class="btn btn-primary btn-sm post-btn">Save</button>
            <button v-on:click="cancelUpdatePosts(posts.id);" type="button" class="btn btn-sm post-btn" style="background: #EEE; color: #444;">Cancel</button>
        </div>
    </div> --}}

    <div :id="'user-comment-content-'+ posts.id" class="comment-box active" v-if="posts.showBoxComment == true">
        @if(Auth::check())
            <comments-community meid="{{ Auth::user()->id }}"
                    avatar="{{ Auth::user()->avatar }}"
                    name="{{ Auth::user()->name }}"
                    :postid="posts.id"
                    numposts="5"
                    background="#fafafa"
                    ref="comment">
            </comments-community>
        @else
            <comments-community
                    :postid="posts.id"
                    numposts="5"
                    background="#fafafa"
                    ref="comment">
            </comments-community>
        @endif
    </div>

</section>

